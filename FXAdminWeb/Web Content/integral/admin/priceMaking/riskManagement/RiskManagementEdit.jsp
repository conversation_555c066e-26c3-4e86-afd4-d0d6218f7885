<%@ page language="java"%>
<%@ page import="com.integral.is.util.CurrencyUtil"%>
<%@ page import="com.integral.admin.utils.marketdefinition.MarketDefinitionUtil"%>
<%@ page import="com.integral.finance.fx.FXRateConvention"%>
<%@ page import="com.integral.finance.currency.Currency"%>
<%@ page import="com.integral.finance.currency.CurrencyFactory"%>
<%@ taglib uri="/tag/idc-html.tld" prefix="idcHtml" %>
<%@ taglib uri="/tag/idc-bean.tld" prefix="idcBean" %>
<%@ taglib uri="/tag/struts-html.tld" prefix="html" %>
<%@ taglib uri="/tag/idc-logic.tld" prefix="idcLogic" %>

<%@ page import="com.integral.admin.utils.GenericAdminUtil" %>
<%@ page import="com.integral.admin.utils.AdminWebServicesMBean" %>
<%@ page import="com.integral.admin.utils.AdminWebServicesMBeanC" %>
<%@ page import="com.integral.admin.utils.organization.OrganizationUtil" %>
<%@ page import="com.integral.broker.config.BrokerProvisionConfigFactory" %>
<%@ page import="com.integral.broker.configuration.PriceConfigurationMBean"%>
<%@ page import="com.integral.broker.configuration.ConfigurationFactory"%>
<%@ page import="com.integral.broker.config.BrokerProvisionConfig" %>
<%@ page import="com.integral.broker.admin.ConfigurationService,com.integral.user.User" %>
<%@ page import="com.integral.broker.admin.OrganizationService" %>
<%@ page import="com.integral.broker.admin.StreamService" %>
<%@ page import="com.integral.broker.model.ExecutionMethod" %>
<%@ page import="com.integral.broker.model.SpreadPriority"%>
<%@ page import="com.integral.broker.model.TenorSpread"%>
<%@ page import="com.integral.broker.model.SpreadBias"%>
<%@ page import="com.integral.broker.model.SpreadType"%>
<%@ page import="com.integral.broker.model.Tier"%>
<%@ page import="com.integral.broker.sort.ProxyPriceSorterC"%>
<%@ page import="com.integral.finance.calculator.Calculator"%>
<%@ page import="com.integral.jsp.framework.table.TableForm"%>
<%@ page import="com.integral.user.Organization"%>
<%@ page import="org.apache.commons.lang.ArrayUtils"%>
<%@ page import="org.apache.struts.action.Action"%>
<%@ page import="org.apache.struts.action.ActionErrors"%>
<%@ page import="org.apache.struts.util.RequestUtils"%>
<%@ page import="org.apache.struts.util.ResponseUtils"%>
<%@ page import="java.io.IOException"%>
<%@ page import="com.integral.admin.utils.web.HTTPWebUtils"%>
<%@ page import="com.integral.broker.model.*,com.integral.system.runtime.RuntimeFactory,com.integral.persistence.cache.ReferenceDataCacheC" %>
<%@ page import="com.integral.admin.utils.pricemaking.PriceMakingUtil" %>
<%@ page import="com.integral.admin.pricemaking.PriceMBeanFactory" %>
<%@ page import="com.integral.admin.pricemaking.PriceMakingMBean" %>
<%@ page import="java.util.*" %>
<%@ page import="com.integral.is.ISCommonConstants" %>
<%@ page import="java.text.DecimalFormat" %>
<%@ page import="com.integral.finance.marketData.fx.FXMarketDataSet" %>
<%@ page import="com.integral.broker.aggregate.*" %>
<%@ page import="com.integral.finance.counterparty.UserCounterpartyGroup" %>
<%@ page import="com.integral.admin.utils.organization.SalesDealerGroupUtil" %>
<%@ page import="com.integral.log.Log" %>
<%@ page import="com.integral.log.LogFactory" %>
<%@ page import="com.integral.admin.utils.organization.UserUtil"%>
<%@ page import="java.text.SimpleDateFormat"%>
<%@ page import="com.integral.riskmanagement.rw.provision.RWProvisionConfig" %>
<%@ page import="com.integral.riskmanagement.rw.provision.RWProvisionConfigManager" %>
<%@ page import="com.integral.is.common.util.YMUtil" %>
<%@ page import="com.integral.finance.counterparty.LegalEntity" %>
<%@ page import="com.integral.finance.dealing.mifid.MiFIDUtils" %>
<%@ page import="com.integral.util.StringUtilC" %>
<%@ page import="com.integral.broker.model.enums.SpreadUnit" %>
<jsp:include page="/integral/core/framework/js/JsInclude.jsp?file=/integral/core/framework/js/combined/FrameworkCommonUtils.js"/>
<jsp:include page="/integral/core/framework/jsp/DateFormat.jsp"/>
<jsp:include page="/integral/core/framework/js/JsInclude.jsp?file=/integral/core/framework/js/html/Calendar.js"/>
<jsp:include page="/integral/core/framework/jsp/DecimalFormat.jsp" />
<jsp:include page="/integral/core/framework/js/JsInclude.jsp?file=/integral/core/framework/js/dealing/NotionalAmountFormat.js" />
<script src="/admin/integral/admin/js/SelectSortFilterUtil.js" type="text/javascript"></script>
<script src="/admin/integral/admin/priceMaking/js/tiers.js"></script>
<script src="/admin/integral/admin/priceMaking/js/tenorSpread.js"></script>
<script src="/admin/integral/core/framework/js/util/StringUtils.js"></script>
<script type="text/javascript" language="javascript" src="/admin/integral/admin/priceMaking/config/PriceMakingConfigurationDetail.js"></script>

<idcBean:define id="displayPreference" name="user"	property="displayPreference" type="com.integral.user.DisplayPreference" />
<idcBean:define id="dateFormatPattern" name="displayPreference"	property="dateFormatPattern" type="java.lang.String" />
<idcBean:define id="user" name="IdcSessionContext" property="user"	type="com.integral.user.User" />
<idcBean:define id="displayPreference" name="user"	property="displayPreference" type="com.integral.user.DisplayPreference" />
<idcBean:define id="timeFormat" name="displayPreference"	property="timeFormat" type="java.text.SimpleDateFormat" />
<idcBean:define id="timeZone" name="displayPreference" 	property="timeZone" type="java.util.TimeZone" />
<idcBean:define id="timeZoneID" name="displayPreference" property="timeZoneID" type="java.lang.String" />
<idcBean:define id="dateTimeFormatPattern" name="displayPreference"  property="dateTimeFormatPattern" type="java.lang.String" />
<idcBean:define id="dateTimeFormat" name="displayPreference"  property="dateTimeFormat" type="java.text.SimpleDateFormat" />
<idcBean:define id="decimalFormat" name="displayPreference" property="decimalFormat" type="java.text.DecimalFormat"/>
<idcBean:define id="loginUser" name="ObjectActionForm" property="user" type="com.integral.user.User" />

<%
    String breadCrumbListLink = "Admin.PriceMaking.RiskManagement.List";
    String fromWhere = HTTPWebUtils.getParameterValue(request, "fromWhere", "PriceMaking");
    String brokerEncryptedId = ResponseUtils.filter(request.getParameter("orgEncryptedId"));
    Organization broker = (Organization) GenericAdminUtil.getEntityById(com.integral.user.OrganizationC.class, TableForm.decryptId(brokerEncryptedId));

    String profileName = HTTPWebUtils.getParameterValue(request,"profileName");
    RiskManagementProfile rmProfile = broker.getBrokerOrganizationFunction().getRiskManagementProfile(profileName);
    Collection<Configuration> configs = rmProfile.getConfigurations();
    Configuration profileConfig = null;
    if (configs != null && !configs.isEmpty()) {
        for (Configuration cpcConfig: configs) {
            profileConfig = cpcConfig;
        }
    }
    String commonBrokerURL = "objectForm.objectType=com.integral.user.OrganizationC&objectForm.encryptedId="+broker.getEncryptedObjectId()+"&encryptedObjectId="+broker.getEncryptedObjectId();
    if (fromWhere.equals("PriceMaking")) {
        breadCrumbListLink = breadCrumbListLink + "&" + commonBrokerURL;
    }

    // CurrencyPairConfiguration
    Boolean marketRangeEnabledForEspExecution = profileConfig.isMarketRangeEnabledForEspExecution();
    Double marketRangeForEspExecution = profileConfig.getMarketRangeForEspExecution();
    Boolean marketRangeEnabledForRfsExecution = profileConfig.isMarketRangeEnabledForRfsExecution();
    Boolean multiFillEnabled = profileConfig.isOriginalMultiFillEnabled();
    Boolean noTimeout = profileConfig.isNoTimeout();
    Boolean overrideESPUpdateInterval = profileConfig.isOriginalOverrideESPUpdateInterval();
    Long espFullUpdateInterval = profileConfig.getOriginalEspFullUpdateInterval();
    Long espBestBidOfferInterval = profileConfig.getOriginalEspBestBidOfferInterval();
    FXMarketDataSet RFSQuoteFilterMarketDataSet = profileConfig.getRFSQuoteFilterMarketDataSet();
    Double allowedDeviationPercent = profileConfig.getAllowedDeviationPercent();
    Double marketRangeForRfsExecution = profileConfig.getMarketRangeForRfsExecution();
    Boolean overrideRFSUpdateInterval = profileConfig.isOriginalOverrideRFSUpdateInterval();
    Long rfsFullUpdateInterval = profileConfig.getOriginalRfsFullUpdateInterval();
    Long rsfBestBidOfferInterval = profileConfig.getOriginalRsfBestBidOfferInterval();
    Boolean overrideRFSParameters = profileConfig.isOriginalOverrideRFSParameters();
    Long requestExpiry = profileConfig.getOriginalRequestExpiry();
    com.integral.finance.trade.Tenor maxTenor = profileConfig.getOriginalMaximumTenor();

    PriceConfigurationMBean pcConfig = ConfigurationFactory.getInstance().getPriceConfigurationMBean();
    boolean isPriceCheckEnabled = pcConfig.isPriceCheckEnabled(broker.getShortName());
    boolean isESPUIOnly = pcConfig.isPriceMakingESPUIOnlyEnabled(broker.getShortName());
    boolean isProviderPointsValidationEnabled = pcConfig.isBrokerRFSProviderPointsValidationEnabled(broker.getShortName());
    boolean isSpotAndSwapEnabled = pcConfig.isPriceSSPUsingSwapEnabled(broker.getShortName())
                                        || pcConfig.isPriceOTUsingSwapEnabled(broker.getShortName())
                                        || pcConfig.isPriceNDFAndNDFSwapUsingSpotAndSwapEnabled(broker.getShortName())
        		                        || pcConfig.isPriceSwapUsingSpotAndSwapEnabled(broker.getShortName());
    boolean isDealerInterventionEnabled = pcConfig.isDealerInterventionEnabled(broker.getShortName());


    BrokerProvisionConfig brokerProvisionConfig = BrokerProvisionConfigFactory.getBrokerProvisionConfig();
    boolean displayCoverOnly = brokerProvisionConfig.isDisplayCoverOnly(broker.getShortName());
    boolean hourglassEnabled = brokerProvisionConfig.isHourglassPricingEnabled(broker.getShortName());
    boolean isNewCopyTradeEnabled = brokerProvisionConfig.isNewCopyTradeEnabled(broker.getShortName());
    boolean isOldCopyTradeEnabled = brokerProvisionConfig.isCopyTradeEnabled(broker.getShortName());
    boolean partialCoverEnabled = brokerProvisionConfig.isPartialCoverEnabled(broker.getShortName());
    String hideHourglassRow = hourglassEnabled ? "" : "style='display:none'";
    String[] availableTimeZoneIDs = TimeZone.getAvailableIDs();
    List<UserCounterpartyGroup> salesDealerGroups = SalesDealerGroupUtil.getSortedSalesDealerGroups(broker);

    PriceMakingMBean pmConf1 = PriceMBeanFactory.getInstance().getPriceMakingMBean() ;
    boolean isSyntheticFullBookEnabled = pmConf1.isSyntheticFullBookEnabled(broker.getShortName()) ;
    boolean isSyntheticFbMtFokEnabled = pmConf1.isSyntheticFullBookMtFokEnabled(broker.getShortName()) ;
    boolean isBenchmarkAggregationEnabled = pmConf1.isBenchmarkAggregationEnabled(broker.getShortName());
    boolean isDisableVWAPIfMultiFillEnabled = pmConf1.isVWAPDisabledIfMultiFillEnabled(broker.getShortName());
    boolean isFaMultiTierEnabled = pmConf1.isFullAmountMultiTierEnabled(broker.getShortName()) ;
    boolean isFaMultiQuoteEnabled = pmConf1.isFullAmountMultiQuoteEnabled(broker.getShortName()) ;

    boolean isMultiTierRfsSpotSpreads = profileConfig.isMultiTierRfsSpotSpreads();
    String showRFS =  "";
    String rfsMultiTierSpreadTableIdStyle = !isMultiTierRfsSpotSpreads ? "style=\'display: none\'" : "";
    String showMiFID = MiFIDUtils.isMiFIDEnabled(broker) && (broker.isBroker() || broker.isPrimeBroker()) ? "" : "none";
    String showSynCrossPointsUI = pcConfig.isSyntheticCrossPointsEnabled(broker.getShortName()) ? "" : "none";

    if (isESPUIOnly == true) {
        isSpotAndSwapEnabled = false;
        showRFS ="none";
        rfsMultiTierSpreadTableIdStyle = "style=\'display: none\'";
        showSynCrossPointsUI ="none";
        showMiFID ="none";
    }

    ConfigurationService configService = com.integral.broker.admin.AdminFactory.getInstance().getConfigurationService();
    CoverExecutionMethod[] espCoverExecutionMethods = configService.getESPCoverExecutionMethods();
    CoverExecutionMethod[] rfsCoverExecutionMethods = configService.getRFSCoverExecutionMethods();
    ExecutionRangeType[] executionRangeType = configService.getExecutionRangeType();
    ExecutionPricingType[] executionPricingType = configService.getExecutionPricingTypes();
    PriceCheckType[] priceCheckType = configService.getPriceCheckTypes();
    ExecutionMethod[] autoCoverExecutionMethods =configService.getAutoCoverExecutionMethods();
    ExecutionRuleConditionType[] autoCoverExecRuleConditionType = configService.getAutoCoverExecutionRuleConditionTypes();

	if (!isOldCopyTradeEnabled) {
		espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, com.integral.broker.model.CoverExecutionMethod.Copy );
	}

    RWProvisionConfig rwProvisionConfig = RWProvisionConfigManager.getInstance().getRwProvisionConfig();
    if (!rwProvisionConfig.getRiskWarehouseOrganizations().contains(broker.getShortName())) {
        espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, CoverExecutionMethod.Warehouse );
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }
    else if (!YMUtil.isWarehouseOptionEnabled(broker.getShortName())) {
        espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, CoverExecutionMethod.Warehouse );
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }
    else if (!rwProvisionConfig.isRFSNetSpotEnabledForRiskWarehouse(broker.getShortName())) {
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }

    if (!ConfigurationFactory.getInstance().getPriceConfigurationMBean().isOrderRejectEnabled(broker.getShortName())) {
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Reject );
    }

    boolean isFFDisabled = false;
    try {
        com.integral.marketmaker.rule.execution.FullFill mmfill = com.integral.maker.service.MarketMakerFactory.getInstance().getExecutionService().queryFullFill(broker.getShortName());
        isFFDisabled = (mmfill.getEnabled() == com.integral.marketmaker.rule.Rule.OFF);
    }
    catch(Exception e) {
        //ignore
    }
%>

<%!
    private String getFormattedNotionalAmountERC(double amount, DecimalFormat format) {
        if (amount == -1) {
            return "";
        } else {
            return format.format(amount);
        }
    }

    /* This function will format the time zone to :[GMT -/+ hh:mm] 3Character ID - DisplayName */
    private String getFormattedTimeZone(Object timezone){
        return timezone.toString();
    }
%>

<jsp:include page="/integral/admin/priceMaking/config/executionRules.jsp" />

<input type="hidden" name="fromWhere" value="<%=fromWhere%>"></input>
<div class="bc">
    <% if(!fromWhere.equalsIgnoreCase("Config")) {
        if(!fromWhere.equals("MDS")) {%>
	        <div class="space"></div>
	        <a class="bcl" href="<%=request.getContextPath() %>/Forward.do?forwardURI=Admin.PriceStream.Status&<%=commonBrokerURL%>">
	            <idcBean:message key="PriceMaking.Config.PriceMaking"/>
	        </a>&nbsp;>&nbsp;&nbsp;
	    <%}%>
        <a class="bcl" href="<%=request.getContextPath() %>/Forward.do?forwardURI=<%=breadCrumbListLink%>">
            <idcBean:message key="PriceMaking.RiskManagement" />
        </a>
	<%} %>
</div>

<div class="space"></div>
<title>RiskManagement Edit</title>
<div class="t">Edit: <%=rmProfile.getLongName()%></div>

<div class="space"></div>
<table id="PriceMakingPanel" cellpadding="0" cellspacing="0" class="outl2" style="width:1540px; display: <%/*none*/%>;">
<tr>
        <td id="espExcnTag" class="f3" colspan="6" nowrap><strong>Execution Method</strong></br>
             <table id="espExecutionConditions"  cellpadding="0" border="0" cellspacing="1" style="width:1530px;background-color: #999;" >
				<!--<tr>
					<td class="stl2" style="text-align:center" colspan="5" nowrap>Matching Criteria</td>
					<td class="stl2" style="text-align:center" colspan="3" nowrap>Execution</td>
				</tr>-->
                  <tr class="lt" style="text-align:center">
                      <td rowspan="2" style="vertical-align:bottom;width: 10px;"><input type="checkbox"></td>
                      <td rowspan="2" class="ac" width="2%">Sort</td>
					  <td >Notional</td>
					  <td>Time</td>
					  <td rowspan="2">Counterparty</td>
                      <td rowspan="2">Execution Rule</td>
                      <% if ( partialCoverEnabled ){ %>
						<td rowspan="2">Partial Cover</td>
                      <% } %>
                      <td colspan="2">Full Fill <%=isFFDisabled?"<img  src='/admin/theme/images/caution.png' title='Full fill feature Disabled globally. Settings will not take effect.' alt='Full fill feature Disabled globally. Settings will not take effect.'>":""%></td>
                      <% if(isPriceCheckEnabled){ %>
                      <td rowspan="2">Price Check</td>
                      <% } %>
                </tr>
                <tr class="lt" style="text-align:center">
                     <td>Low (>)<br>High (<=)</td>
                     <td nowrap><span style="margin-right:20px;">Start</span>Stop</td>
					 <td>Min % Covered</td>
					 <td>Auto Cover</td>
                </tr>
           <%
              ExecutionRule espExecutionRule = profileConfig.getESPExecutionRule();
               Collection<ExecutionRuleCondition> espRuleConditions =
                       (espExecutionRule != null && espExecutionRule.getExecutionRuleConditions() != null &&
                               espExecutionRule.getExecutionRuleConditions().size() != 0) ?
                               espExecutionRule.getExecutionRuleConditions() : Collections.EMPTY_LIST;
               int espCount =0;
               String sdEncId = null;
               for (ExecutionRuleCondition ruleCondition : espRuleConditions ) {
                   String cls = espCount % 2 == 0 ? "tl" : "tlc";
                    UserCounterpartyGroup salesDealerGroup1 = ruleCondition.getSalesDealerGroup();
                    sdEncId = salesDealerGroup1 != null ? salesDealerGroup1.getEncryptedObjectId() : null;
                   out.println("<tr class='" + cls + "' style='text-align:center' rowId = '" + espCount + "'>");
                   out.println("<td  rowspan='2'><input type='checkbox' id='espExecRuleCond_" + espCount + "' name='workflowMessage.espExecRuleCond_" + espCount + "'> </td>");
                   out.println("<td  rowspan='2'><input type='text' id='espSortOrder_" + espCount + "'  name='workflowMessage.espSortOrder_" + espCount + "' class='ftr' size='2' value='" + ruleCondition.getSortOrder() + "' onblur='changeButton()'> </td>");


				   	//move notional
				   out.println( "<td  rowspan='2'><input id='espExecNotLow_"+espCount+"'  name='workflowMessage.espExecNotLow_"+espCount+"' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()' default='Low (>)' value='"+ getFormattedNotionalAmountERC(ruleCondition.getLowAmount(),decimalFormat)+"' data-string='" + ruleCondition.getLowAmount() + "' size='9' ></br>");
				   out.println("<input id='espExecNotHigh_"+espCount+"' name='workflowMessage.espExecNotHigh_"+espCount+"' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()' default='High (<=)' value='"+ getFormattedNotionalAmountERC(ruleCondition.getHighAmount(),decimalFormat) +"' data-string='"+ ruleCondition.getHighAmount() + "' size='9' ></td>");

					//move timezone
                    out.println("<td  rowspan='2' nowrap><input id='espExecTmStrt_"+ espCount +"'  name='workflowMessage.espExecTmStrt_"+ espCount +"' class='ft' type='text' value='"+ (ruleCondition.getStartTime() == null ? "" : ruleCondition.getStartTime()) +"' size='6'  onblur='changeButton()'><span style='width: 0px;'>&nbsp;</span><input id='espExecTmEnd_"+ espCount +"'  name='workflowMessage.espExecTmEnd_"+ espCount +"' class='ft' type='text' value='"+ (ruleCondition.getStopTime() == null ? "" : ruleCondition.getStopTime()) +"' size='6' onblur='changeButton()'></br><select id='espTimeZoneID_"+ espCount +"' name='workflowMessage.espTimeZoneID_"+ espCount +"' class='ft' style='width: 114px;overflow:hidden;' onchange='changeButton()'>");
                    for (int j= 0;  j < availableTimeZoneIDs.length; j++) {
                      String displayFormat = getFormattedTimeZone((availableTimeZoneIDs[j]));
                      if (displayFormat == null) continue;
                      out.println("<option value='"+displayFormat+"' "+((displayFormat.equalsIgnoreCase( ruleCondition.getTimeZone() )) ? " selected " : "")+"  >"+displayFormat+"</option>");
                    }
                    out.println("</select> </td>");

				%>
					<td  rowspan="2" nowrap>
                    <select id="espSalesDealerGroup_<%=espCount%>" name="workflowMessage.espSalesDealerGroup_<%=espCount%>" class="ft" style="width: 90px;overflow:hidden;" onchange="changeButton()">
                     <option value="-1" > &nbsp; </option>
                     <%
                         UserCounterpartyGroup salesDealerGroup = ruleCondition.getSalesDealerGroup();
                         for (UserCounterpartyGroup dealerGroup : salesDealerGroups) {
                             %>
                     <option value="<%=dealerGroup.getObjectID()%>" <%=dealerGroup.equals(salesDealerGroup) ? "selected" : "" %>><%=dealerGroup.getShortName()%></option>
                     <%
                         }
                     %>
                 </select>
                 <%if(sdEncId != null)
                    { %>
                        <br>
                     <a href="javascript:showSalesDealerGroupPage('<%=sdEncId%>', '<%=brokerEncryptedId%>')">
                             view/edit
                         </a>
                    <%
                         }
                     %>
                     </td>
					<%


					StringBuilder executionRuleRow1 = new StringBuilder("<td  rowspan='1' nowrap>");
                  executionRuleRow1.append("<select  class='ft' style='width:85' id='espCoverExecutionMethod_").append(espCount).append("'  name='workflowMessage.espCoverExecutionMethod_").append(espCount)
                           .append("' onchange='validateESPCoverExec(this," + espCount + ")' nowrap>");
                   String selectedValue = "";
                   if(displayCoverOnly){
                       boolean isSelected = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod() != null;
                       if(isSelected) {
                           selectedValue = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().getName();
                           executionRuleRow1.append("<option value='").append(selectedValue).append("' selected >")
                           .append(selectedValue).append("</option>");
                           if(!selectedValue.equals("Cover")){
                               executionRuleRow1.append("<option value='Cover' >Cover</option>");
                           }
                       }else{
                           executionRuleRow1.append("<option value='").append(CoverExecutionMethod.Cover.getName()).append("' selected >")
                           .append(CoverExecutionMethod.Cover.getName()).append("</option>");
                       }
                   }else{
                       for (int j = 0; j < espCoverExecutionMethods.length; j++) {
                           boolean isSelected = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod() == espCoverExecutionMethods[j];
                           if( isSelected ) selectedValue = espCoverExecutionMethods[j].name();
                           executionRuleRow1.append("<option value='").append(espCoverExecutionMethods[j].name()).append("' ");
                           executionRuleRow1.append( isSelected ? "selected" : "");
                           executionRuleRow1.append("   >").append(espCoverExecutionMethods[j].getFullName()).append("</option>");
                       }
                   }
                   executionRuleRow1.append("</select>").append("&nbsp;&nbsp;");
                   executionRuleRow1.append("<script>$(\"espCoverExecutionMethod_"+espCount+"\").prevVal=\""+selectedValue+"\";</script>");

                   executionRuleRow1.append("<input type='checkbox' id='espNoPriceChecked_").append(espCount).append("' name='workflowMessage.espNoPriceChecked_").append(espCount)
                           .append("' ").append(ruleCondition.getExecutionMethodParameters().isNoPriceCheckEnabled() ? "checked" : "").append(" onclick='validateESPNoPriceCheckBox(this," + espCount + ")'")
                           .append(CoverExecutionMethod.NoCover.equals(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod()) ? "" : " disabled").append(" >")
                           .append("<label id='espNoPriceCheckedLabel_").append(espCount).append("'>").append("NPC").append("</label>").append("&nbsp;&nbsp;");


                   executionRuleRow1.append("<input type='radio' id='espTrdTypeMkt_").append(espCount).append("' name='workflowMessage.espTrdType_").append(espCount)
                           .append("' ").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "" : "checked")
                           .append(" onclick='validateESPTradeTypeCombo(this," + espCount + ")'>").append("Market").append("&nbsp;&nbsp;");

                   executionRuleRow1.append("&nbsp;").append("<input type='radio' id='espTrdTypeLmt_").append(espCount).append("' name='workflowMessage.espTrdType_").append(espCount)
                           .append("' ").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "checked" : "")
                           .append(" onclick='validateESPTradeTypeCombo(this," + espCount + ")'>").append("Limit").append("&nbsp;&nbsp;");


                   executionRuleRow1.append("<input id='espRange_").append(espCount).append("'  name='workflowMessage.espRange_").append(espCount)
                           .append("' class='ftr' type='text' onblur='validateESPRange(this," + espCount + ")' value='").append(getFormattedNotionalAmountERC(ruleCondition.getExecutionMethodParameters().getRange(), decimalFormat))
                           .append("' size='3'").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "" : " disabled ").append(">").append("&nbsp;&nbsp;");

                   executionRuleRow1.append("<select  class='ft' style='width:70' id='espRangeType_").append(espCount).append("'  name='workflowMessage.espRangeType_").append(espCount)
                           .append("' onchange='validateESPCoverExec(this," + espCount + ")' nowrap>");
                   for (int j = 0; j < executionRangeType.length; j++) {
                       executionRuleRow1.append("<option value='").append(executionRangeType[j].name()).append("' ");
                       executionRuleRow1.append(ruleCondition.getExecutionMethodParameters().getRangeType() == executionRangeType[j] ? "selected" : "");
                       executionRuleRow1.append("   >").append(executionRangeType[j].getFullName()).append("</option>");
                   }
                   executionRuleRow1.append("</select>").append("&nbsp;&nbsp;");

                   executionRuleRow1.append("<input type='checkbox' class='espVWAP' id='espVWAP_").append(espCount).append("' name='workflowMessage.espVWAP_").append(espCount)
                          .append("' ").append(ruleCondition.getExecutionMethodParameters().isVWAPEnabled() ? "checked" : "").append(" onclick='validateESPVWAPCheckBox(this," + espCount + ")'")
                          .append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Cover) ? "" : " disabled").append(">").append("VWAP").append("&nbsp;&nbsp;");

                  executionRuleRow1.append("<input type='checkbox' id='espFOK_").append(espCount).append("' name='workflowMessage.espFOK_").append(espCount)
                          .append("' ").append(ruleCondition.getExecutionMethodParameters().isFOKEnabled() ? "checked" : "").append(" onclick='validateESPFOKCheckBox(this," + espCount + ")'")
                          .append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Cover) ? "" : " disabled").append(">").append("FOK");


                   if(isNewCopyTradeEnabled){
                	   executionRuleRow1.append("&nbsp;&nbsp;").append("<input type='checkbox' id='espCopyChecked_").append(espCount).append("' name='workflowMessage.espCopyChecked_").append(espCount)
                       .append("' ").append("' onclick='validateESPExecutionRuleCondition(" + espCount + ")'").append(" onchange='changeButton()'").append(ruleCondition.getExecutionMethodParameters().isCopyEnabled() ? "checked" : "").append(">")
                       .append("<label id='espCopyCheckedLabel_").append(espCount).append("'>").append("Copy").append("</label>").append("&nbsp;&nbsp;");

                       executionRuleRow1.append("<input id='espCopyValue_").append(espCount).append("'  name='workflowMessage.espCopyValue_").append(espCount)
                       .append("' class='ftr' type='text' value='").append(decimalFormat.format(ruleCondition.getExecutionMethodParameters().getCopyValue())).append("' size='3'")
                       .append(" onkeyup='changeButton()'").append(" onblur='validateESPCopyValue(this," + espCount + ")'").append(">")
                       .append("<label id='espCopyPercentageLabel_").append(espCount).append("'>").append("%").append("</label>");
                   }

                   executionRuleRow1.append("</td>");
                   out.println(executionRuleRow1.toString());

				if ( partialCoverEnabled )
				{
					out.println("<td rowspan='2'><input type='checkbox' id='espPartialCover_"+espCount+"' name='workflowMessage.espPartialCover_"+espCount+"' " + (ruleCondition.isPartialCoverEnabled() ? "checked" : "") + " onclick='changeButton(),validateESPCoverPercentage("+espCount+")'><span style='width: 10px;'></span><input type='text' id='espCoverPercentageVal_"+espCount+"' name='workflowMessage.espCoverPercentageVal_"+espCount+"' class='ftr' size='2' value='" + decimalFormat.format(ruleCondition.getCoverPercentage()) +"' onblur='return validateESPCoverPercentage("+espCount+")' onkeyup='changeButton()'>% </td>");
				}

				  out.println("<td rowspan='2'><input type='checkbox' id='espFullFill_"+espCount+"' name='workflowMessage.espFullFill_"+espCount+"' " + (ruleCondition.isFullFillEnabled() ? "checked" : "") + " onclick='changeButton()'><span style='width: 10px;'></span><input type='text' id='espFullFillVal_"+espCount+"' name='workflowMessage.espFullFillVal_"+espCount+"' class='ftr' size='2' value='" + decimalFormat.format(ruleCondition.getFullFill()) +"' onblur='return validateESPFFPercentage(this,"+espCount+")' onkeyup='changeButton()'>% </td>");
                  out.println("<td  rowspan='2'><input type='checkbox' id='espAutoCover_"+espCount+"' name='workflowMessage.espAutoCover_"+espCount+"' " + (ruleCondition.isAutoCoverEnabled() ? "checked" : "") + " onclick='changeButton()'> <br><span style='visibility: hidden'  id='espDescFullFill_" + espCount + "'></span></td>");

                  if(isPriceCheckEnabled){
                	  out.println("<td rowspan='2'><input type='checkbox' id='espPriceCheck_"+espCount+"' name='workflowMessage.espPriceCheck_"+espCount+"' " + (ruleCondition.isPriceCheckEnabled() ? "checked" : "") + " onclick='changeButton()'><span style='width: 10px;'></span><input type='text' id='espPriceCheckVal_"+espCount+"' name='workflowMessage.espPriceCheckVal_"+espCount+"' class='ftr' size='3' value='" + decimalFormat.format(ruleCondition.getPriceCheckValue()) +"' onblur='return validateESPPriceCheckValue(this)' onkeyup='changeButton()'> &nbsp;&nbsp;" +
                              "<select class='ft' style='width:70' id='espPriceCheckTypeName_"+espCount+"' name='workflowMessage.espPriceCheckTypeName_"+espCount+"' onclick='changeButton()' nowrap>");
                      for (int j = 0; j < priceCheckType.length; j++) {
                           out.println("<option value='" + priceCheckType[j].name() + "' " + (priceCheckType[j].name().equals(ruleCondition.getPriceCheckTypeName()) ? "selected" : "")  + " >"+ priceCheckType[j].name() + "</option>");
                          }
                      out.println("</select></td>");
                  }


				  out.println("</tr>");
				  out.println("<tr class='" + cls + "' style='text-align:center' rowId = '" + espCount + "'>");
                  out.println("<td id='espDescription_" + espCount + "'></td>");
                  //out.println("<td style='visibility: hidden'  id='espDescFullFill_" + espCount + "'></td>");
				  out.println("</tr>");
                  %>


                 <script>
                     validateESPExecutionRuleCondition(<%=espCount%>);
                 </script>
                 <%
                 espCount++;
             }

           %>
           </table>
           <table id="espExcnAction">
               <tr>
                   <td class="f" colspan="11">
                        <span style="width:25px;">&nbsp;</span>
                        <input type="button" class="b1" value="Delete Rule"  onclick="javascript:deleteExecutionCondition('espExecutionConditions','ESP');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
                        <input type="button" class="b1" value="Add Rule" onclick="javascript:newExecutionCondition('espExecutionConditions','ESP');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
                       <input type="hidden" id='espCOUNTEXECUTIONCOND' name="workflowMessage.espCOUNTEXECUTIONCOND" value="<%=espRuleConditions.size()%>"/>
                   </td>
               </tr>
             </table><br><br>
        </td>
    </tr>
		    <tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr id="espExcnResp">
		<td class="f3"><strong>Execution Response</strong></td>
        <td class="f3" colspan="4" nowrap>
            <input type="radio"  name="workflowMessage.multiFillEnabled" id="multiFillEnabled1" value="false" <%=(multiFillEnabled==null)?"checked":multiFillEnabled ? "" : "checked"%> onclick='changeButton(), handleNoTimeOut(true), handleMultipleFill(false)'/>Aggregated Fill&nbsp;&nbsp;
            <input type="radio" name="workflowMessage.multiFillEnabled" id="multiFillEnabled2" value="true" <%=(multiFillEnabled==null)?"": multiFillEnabled ? "checked" : ""%> onclick='changeButton(), handleNoTimeOut(false), handleMultipleFill(<%=isDisableVWAPIfMultiFillEnabled%>)'/>Multiple Fills
            <input type="checkbox" id="confNoTimeout" name="workflowMessage.noTimeout" <%=noTimeout == null ? "" : noTimeout ? "checked":"" %> value="true" class="ft" onclick='changeButton()'/> No Timeout
	    </td>
        <td class="f3"></td>
        <td class="f3"></td>
    </tr>
		<tr>
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr id="espExcnOvrInt">
		<td class="f3" nowrap><strong>Override Stream Update Interval</strong></td>
		<td class="f3" nowrap style="text-align: bottom"><input type="checkbox" id="overrideESPUpdateInterval" name="workflowMessage.overrideESPUpdateInterval" value="<%=overrideESPUpdateInterval%>" <%=overrideESPUpdateInterval.booleanValue()?"checked":""%> class="ft" onclick="changeButton();updateESPIntervals();"></td>
        <td class="f3" nowrap>Full Stream Update Interval</td>
		<td class="f3" nowrap><input type="text" id = "espFullUpdateInterval" name="workflowMessage.espFullUpdateInterval" size="2" value="<%=(espFullUpdateInterval==null)?"":espFullUpdateInterval.toString()%>"
	        <%=overrideESPUpdateInterval.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>milliseconds&nbsp;&nbsp;(Stream Default:&nbsp;<%= rmProfile.getQuotePublicationInterval() %>&nbsp; milliseconds)</td>
		<td class="f3" nowrap></td>
		<td class="f3" nowrap></td>
    </tr>
    <tr id="espExcnUpdInt">
        <td class="f3" colspan="2" nowrap></td>
        <td class="f3" nowrap>Best Bid/Offer Update Interval</td>
		<td class="f3" nowrap><input type="text" id = "espBestBidOfferInterval" name="workflowMessage.espBestBidOfferInterval" size="2" value="<%=(espBestBidOfferInterval==null)?"":espBestBidOfferInterval.toString()%>"
	        <%=overrideESPUpdateInterval.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>milliseconds&nbsp;&nbsp;(Stream Default:&nbsp;&nbsp;&nbsp;<%= rmProfile.getQuotePublicationCheckInterval() %>&nbsp;milliseconds)</td>
		<td class="f3" nowrap></td>
		<td class="f3" nowrap></td>
    </tr>
	<tr>
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
		<tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr style="display: <%=showRFS %>" id="rfsExcnHeaderTag">
        <td class="f" colspan="6"  style="font-size: 9pt;font-weight: bold;"  nowrap>RFS Rules</td><td class="f" nowrap></td>
    </tr>
     <%
     Collection<String> stdTenors =   BrokerProvisionConfigFactory.getBrokerProvisionConfig().getBrokerStdTenors(broker.getShortName());
        if(!stdTenors.isEmpty()) {
        	List<String> list = new ArrayList<String>(stdTenors);
        	StringBuilder sb = new StringBuilder();
        	sb.append(list.get(0));
        	for(int i=1;i<list.size();i++) {
        		sb.append(", ").append(list.get(i));
        	}
     %>
      <tr style="display: <%=showRFS %>">
        <td class="f3" nowrap colspan="2"><strong>Standard Tenors</strong></td>
        <td class="f" colspan="4" ><p style="display: inline-block; margin: 0px">
        <%= sb.toString() %></p>
        </td>
		<td class="f3" nowrap></td>
      </tr>
     <%
		}
		if ( isProviderPointsValidationEnabled )
		{
	 %>
   <tr style="display: <%=showRFS %>">
     <td class="f" nowrap><strong>RFS Provider Points Validation Market Data Set</strong></td>
     <td class="f3" nowrap>
         <a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/MarketData/MarketDataEdit.html#_top','Admin Portal Help')">
               <img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a>
     </td>
     <td  class="f" colspan="1" nowrap>
             <select id="RFSQuoteFilterMarketDataSet" name="workflowMessage.RFSQuoteFilterMarketDataSet"  class="ft" onchange="changeButton()">
				<option value="" <%=RFSQuoteFilterMarketDataSet != null ? "" : "selected"%>><idcBean:message key="SelectOption1" /></option>
                <idcLogic:iterate id="mds" name="MDSES" type="com.integral.finance.marketData.fx.FXMarketDataSet">
                                <option value="<%=mds.getEncryptedObjectID()%>" <%=(null!=RFSQuoteFilterMarketDataSet && RFSQuoteFilterMarketDataSet.isSameAs(mds))?"selected":""%>><%=mds.getShortName()%></option>
                </idcLogic:iterate>
             </select>
     <%
     	if ( RFSQuoteFilterMarketDataSet != null )
     	{
     %>
         <a href="javascript:openDetailWindow('/admin/Forward.do?forwardURI=Admin.MarketDataSet.Edit&objectForm.objectType=com.integral.finance.marketData.fx.FXMarketDataSetC&objectForm.encryptedId=<%=RFSQuoteFilterMarketDataSet.getEncryptedObjectID()%>&fromWhere=Config', 'Market Data Set')">
             view/edit
         </a>
     <%
     	}
     %></td>
     <td class="f" nowrap><strong> Allowed Deviation (Percent): </strong></td>
     <td class="f" nowrap>
           <input type="text" id='allowedDeviationPercent' name ="workflowMessage.allowedDeviationPercent" value='<%=(allowedDeviationPercent!=null)?decimalFormat.format(allowedDeviationPercent):decimalFormat.format(0)%>'
             onblur="validateAllowedDeviationPercent(this)" onkeyup='changeButton()'>
     </td>
      <td class="f3" nowrap></td>
      <td class="f3" nowrap></td>
  </tr>
	<%
		}
	%>
   <tr>
       <td id="rfsExcnTag" class="f3" colspan="6" nowrap style="display: <%=showRFS %>"><strong>Pricing and Execution Method</strong></br>

            <table id="rfsExecutionConditions"  cellpadding="0" border="0" cellspacing="1" style="width:1530px;background-color: #999;">
               <tr class="lt" style="text-align:center">
                     <td rowspan="2" style="vertical-align:bottom;width: 10px;padding-left: 0px; padding-right: 0px;"><input type="checkbox"></td>
                     <td rowspan="2" class="ac" width="2%" style="padding-left: 0px; padding-right: 0px;">Sort</td>
					 <td nowrap rowspan="2" style="padding-left: 0px; padding-right: 0px;">Trade Type</td>
					 <td nowrap style="padding-left: 0px; padding-right: 0px;">Notional</td>
					 <td nowrap style="padding-left: 0px; padding-right: 0px;">Net Spot Amt</td>
					 <td nowrap style="padding-left: 0px; padding-right: 0px;">Time</td>
					 <td colspan="3" style="padding-left: 0px; padding-right: 0px;">Tenor</td>
                     <td>Spot</td>
					 <td rowspan="2" style="padding-left: 0px; padding-right: 0px;">Counterparty</td>
                     <td rowspan="2" colspan="2" style="padding-left: 0px; padding-right: 0px;">Pricing</td>
                     <% if(isSpotAndSwapEnabled) { %>
                     	<td colspan="2" style="padding-left: 0px; padding-right: 0px; text-align:center !important;"> Execution Rule </td>
                     <% } %>
                     <% if(!isSpotAndSwapEnabled) { %>
                     	<td rowspan="2" style="padding-left: 0px; padding-right: 0px; text-align:center !important;">Execution Rule</td>
                     <% } %>
                     <td colspan="3" style="padding-left: 0px; padding-right: 0px;">Full Fill <%=(isFFDisabled?"<img  src='/admin/theme/images/caution.png' title='Full fill feature Disabled globally. Settings will not take effect.' alt='Full fill feature Disabled globally. Settings will not take effect.'>":"")%></td>
               </tr>
               <tr class="lt" style="text-align:center">
					<td style="padding-left: 0px; padding-right: 0px;">Low (>)<br>High (<=)</td>
					<td style="padding-left: 0px; padding-right: 0px;">Low (>)<br>High (<=)</td>
                    <td nowrap style="padding-left: 0px; padding-right: 0px;"><span style="margin-right:20px;">Start</span>Stop</td>
                    <td style="padding-left: 0px; padding-right: 0px;">Legs</td>
                    <td style="padding-left: 0px; padding-right: 0px;">Category</td>
                    <td style="padding-left: 0px; padding-right: 0px;">Range</br>Min (>)</br>Max(<=)</td>
                    <td style="padding-left: 0px; padding-right: 0px;">Pips Deviation<br>(<=)</td>
                    <% if(isSpotAndSwapEnabled) { %>
	                    <td id="rfsSwapPointsBlank" style="padding-left: 0px; padding-right: 0px;">Spot Risk</td>
	                    <td id="rfsSwapPoints" nowrap style="padding-left: 0px; padding-right: 0px;">Swap Risk</td>
                    <% } %>
                    <td style="padding-left: 0px; padding-right: 0px;">Min %<br>Covered</td>
					<td style="padding-left: 0px; padding-right: 0px;">Auto<br>Cover</td>
               </tr>


 <%
            ExecutionRule rfsExecutionRule = profileConfig.getRFSExecutionRule();
            Collection<ExecutionRuleCondition> rfsRuleConditions =
                    (rfsExecutionRule != null && rfsExecutionRule.getExecutionRuleConditions() != null &&
                            rfsExecutionRule.getExecutionRuleConditions().size() != 0) ?
                            rfsExecutionRule.getExecutionRuleConditions() : Collections.EMPTY_LIST;
            int rfsCount = 0;
            for (ExecutionRuleCondition ruleCondition : rfsRuleConditions) {
                String tradeType = ruleCondition.getTradeType();
                String cls = rfsCount % 2 == 0 ? "tl" : "tlc";
                out.println("<tr class='" + cls + "' style='text-align:center;padding-left: 0px; padding-right: 0px;' rowId = '" + rfsCount + "'>");
                out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input type='checkbox' id='rfsExecRuleCond_" + rfsCount + "' name='workflowMessage.rfsExecRuleCond_" + rfsCount + "'> </td>");
                out.println(" <td rowspan='2' style=padding-left: 0px; padding-right: 0px;'><input type='text' id='rfsSortOrder_" + rfsCount + "'  name='workflowMessage.rfsSortOrder_" + rfsCount + "' class='ftr' size='2' value='" + ruleCondition.getSortOrder() + "' onblur='changeButton()'> </td>");

				//move tradetype
				 out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><select style='width: 90px;overflow:hidden;' id='rfsTradeType_" + rfsCount + "' name='workflowMessage.rfsTradeType_" + rfsCount + "' class='ft' onChange='validateRFSTradeType(this, \"" + rfsCount + "\" );'>");
                    out.println("<option value='" + ISCommonConstants.TRD_CLSF_SP + "' " + ((ISCommonConstants.TRD_CLSF_SP.equals(tradeType)) ? "selected" : "") + ">FX Spot</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_OR + "' " + ((ISCommonConstants.TRD_CLSF_OR.equals(tradeType)) ? "selected" : "") + ">FX Outright</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_SWAP + "' " + ((ISCommonConstants.TRD_CLSF_SWAP.equals(tradeType)) ? "selected" : "") + ">FX Swap</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_FWD + "' " + ((ISCommonConstants.TRD_CLSF_FWD.equals(tradeType)) ? "selected" : "") + ">FX Fwd Fwd</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_FXNDF + "' " + ((ISCommonConstants.TRD_CLSF_FXNDF.equals(tradeType)) ? "selected" : "") + ">FX NDF</option>" +
							"<option value='" + ISCommonConstants.TRD_CLSF_FXFSR + "' " + ((ISCommonConstants.TRD_CLSF_FXFSR.equals(tradeType)) ? "selected" : "") + ">FX FSR</option>" +
							"<option value='" + ISCommonConstants.TRD_CLSF_FXSSP + "' " + ((ISCommonConstants.TRD_CLSF_FXSSP.equals(tradeType)) ? "selected" : "") + ">FX SSP</option>" +
							"<option value='" + ISCommonConstants.TRD_CLSF_FXNDF_SWAP + "' " + ((ISCommonConstants.TRD_CLSF_FXNDF_SWAP.equals(tradeType)) ? "selected" : "") + ">FX NDF Swap</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_FXWindowFWD + "' " + ((ISCommonConstants.TRD_CLSF_FXWindowFWD.equals(tradeType)) ? "selected" : "") + ">FX Window FWD</option>" +

                            "<option value='" + ISCommonConstants.TRD_CLSF_ALL + "' " + ((ISCommonConstants.TRD_CLSF_ALL.equals(tradeType)) ? "selected" : "") + ">All</option> ");
                    out.println("</select>");
                    out.println("<input type='hidden' id='rfsTradeType_hidden_" + rfsCount + "' value='" + (ruleCondition.getTradeType() == null ? ISCommonConstants.TRD_CLSF_ALL : ruleCondition.getTradeType()) + "'>");
                    out.println(" </td>");


					//move notional
					out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input id='rfsExecNotLow_" + rfsCount + "'  name='workflowMessage.rfsExecNotLow_" + rfsCount + "' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()' value='" + getFormattedNotionalAmountERC(ruleCondition.getLowAmount(), decimalFormat) + "' data-string='" + ruleCondition.getLowAmount() + "' size='9' ></br><input id='rfsExecNotHigh_" + rfsCount + "' name='workflowMessage.rfsExecNotHigh_" + rfsCount + "' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()' value='" + getFormattedNotionalAmountERC(ruleCondition.getHighAmount(), decimalFormat) + "' data-string='" + ruleCondition.getHighAmount() + "' size='9' ></td>");


				//move net amount
				out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input id='rfsExecLowNetAmount_" + rfsCount + "'  name='workflowMessage.rfsExecLowNetAmount_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getLowNetAmount() == -1 ? "" : getFormattedNotionalAmountERC(ruleCondition.getLowNetAmount(), decimalFormat)) + "' onblur='convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()' data-string='" + ruleCondition.getLowNetAmount() + "' size='9' ></br><input id='rfsExecHighNetAmount_" + rfsCount + "'  name='workflowMessage.rfsExecHighNetAmount_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getHighNetAmount() == -1 ? "" : getFormattedNotionalAmountERC(ruleCondition.getHighNetAmount(), decimalFormat)) + "' onblur='convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()' data-string='" + ruleCondition.getHighNetAmount() + "' size='9' ></td>");

				//move time zone
				out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap><input id='rfsExecTmStrt_" + rfsCount + "'  name='workflowMessage.rfsExecTmStrt_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getStartTime() == null ? "" : ruleCondition.getStartTime()) + "' onblur='changeButton()' size='6'><span style='width: 0px'>&nbsp;</span><input id='rfsExecTmEnd_" + rfsCount + "'  name='workflowMessage.rfsExecTmEnd_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getStopTime() == null ? "" : ruleCondition.getStopTime()) + "' onblur='changeButton()' size='6'></br><select id='rfsTimeZoneID_" + rfsCount + "' name='workflowMessage.rfsTimeZoneID_" + rfsCount + "' class='ft' style='width: 110px;overflow:hidden;'  onchange='changeButton()'>");
                    for (int j = 0; j < availableTimeZoneIDs.length; j++) {
                        String displayFormat = getFormattedTimeZone((availableTimeZoneIDs[j]));
                        if (displayFormat == null) continue;
                        out.println("<option value='" + availableTimeZoneIDs[j] + "' " + ((availableTimeZoneIDs[j].equalsIgnoreCase(ruleCondition.getTimeZone())) ? " selected " : "") + "  >" + displayFormat + "</option>");
                    }
                    out.println("</select> </td>");

				//move tenor legs
				boolean dateNotDefined = ruleCondition.getMinValueDate() == null && ruleCondition.getMaxValueDate() == null;
 					SimpleDateFormat dateFormat =  loginUser.getDisplayPreference().getDateFormat();
                    {
                         String value = "";
                          boolean anyLeg = ruleCondition.isAnyLeg();
                          if(!dateNotDefined) {
                            value = ruleCondition.getMinValueDate() == null ? "" : dateFormat.format(ruleCondition.getMinValueDate());
                          } else {
                             value = ruleCondition.getMinTenor() == null? "" : ruleCondition.getMinTenor().toString();
                          }
                          String id = "rfsAnyLeg_"+ rfsCount;
                          String name = "workflowMessage."+id;

                          String rid =  "rfsmc_"+ rfsCount; // rfsMatchCriteria
                          String rname = "workflowMessage." + rid;

                          int mc = ruleCondition.getMatchCriteria();
                          String props []= new String[3];
                          for(int i =0 ; i<3;i++) {
                        	  props[i] = mc == i ? "checked=true" : "";
                          }

						String anycp = anyLeg? "checked=true" : "";
						String allcp = anyLeg? "" : "checked=true";

                         String lgroup = "<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap>";
                          lgroup += "<input type='radio' name='"+name+"'  value='true' " + anycp + " onclick='changeButton()'>Any Leg<br>";
                          lgroup += "<input type='radio' name='"+name+"'  value='false' " + allcp + " onclick='changeButton()'>All Legs<br>";
                          lgroup+= "</td>";
						  out.println(lgroup);



				//move tenor category

                          String radioGroup = "<td rowspan='2' nowrap style='text-align:left;padding-left: 0px; padding-right: 0px;'>";
                          radioGroup += "<input type='radio' name='"+rname+"'  value='0' " + props[0]+ " onclick='changeButton()' class='mc' id='rfsMatchingCriteria.0_" +rfsCount+ "'>Default<br>";
                          radioGroup += "<input type='radio' name='"+rname+"'  value='1' " + props[1]+ " onclick='changeButton()' class='mc' id='rfsMatchingCriteria.1_" +rfsCount+ "'>Standard Tenor<br>";
                          radioGroup += "<input type='radio' name='"+rname+"'  value='2' " + props[2]+ " onclick='changeButton()' class='mc' id='rfsMatchingCriteria.2_" +rfsCount+ "'>Non Standard<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tenor<br>";
                          radioGroup+= "</td>";
						  out.println(radioGroup);

				//move tenor range
				String curi = request.getContextPath() + "/integral/core/framework/jsp/Calendar.jsp?hostField=workflowMessage.rfsExecTenMin_" +  rfsCount + "&allowPastTenor=true" ;
                String juri = "javascript:popupCalendar(\""+curi+"\")";

                       out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap><div style='width:90%'><div style='display:inline'><input onblur='changeButton()' style='width:60px;min-width:50px;' type='text' id='rfsExecTenMin_" + rfsCount + "'  name='workflowMessage.rfsExecTenMin_" + rfsCount + "' size='20' value='"+value+"' class='ft' ></div><div style='display:inline'>&nbsp;<a id='cptyChooser' style='vertical-align: bottom;'><a href='"+juri+"' onclick='changeButton()' class='mc"+rfsCount+"' tabindex='-1'><img src='/admin/theme/images/calendar.gif' border='0'></a></div></div>");
						}
						{
        				   String value = "";
                           boolean anyLeg = ruleCondition.isAnyLeg();
                           if(!dateNotDefined) {
                             value = ruleCondition.getMaxValueDate() == null ? "" : dateFormat.format(ruleCondition.getMaxValueDate());
                           } else {
                             value = ruleCondition.getMaxTenor() == null? "" : ruleCondition.getMaxTenor().toString();
                           }
                           String curi = request.getContextPath() + "/integral/core/framework/jsp/Calendar.jsp?hostField=workflowMessage.rfsExecTenMax_" +  rfsCount + "&allowPastTenor=true" ;
                           String juri = "javascript:popupCalendar(\""+curi+"\")";
					  out.println("</br><div style='width:90%'><div style='display:inline'><input onblur='changeButton()' style='width:60px;min-width:50px;' type='text' id='rfsExecTenMax_" + rfsCount + "'  name='workflowMessage.rfsExecTenMax_" + rfsCount + "' size='20' value='"+value+"' class='ft' ></div><div style='display:inline'>&nbsp;<a id='cptyChooser' style='vertical-align: bottom;'><a href='"+juri+"' onclick='changeButton()' tabindex='-1'><img src='/admin/theme/images/calendar.gif' border='0'></a></div></div></td>");
					}
				//move spot pips deviation
				        out.println("<td rowspan='2'><input id='rfsPipsDeviation_" + rfsCount + "'  name='workflowMessage.rfsPipsDeviation_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getPipsDeviation() == -1 ? "" : ruleCondition.getPipsDeviation()) + "' onblur='validateMinimumSpread(this);changeButton()' size='6' ></td>");

				%>
				<!--move counterparty-->
				    <td rowspan="2" style='padding-left: 0px; padding-right: 0px;'>
                    <select id="rfsSalesDealerGroup_<%=rfsCount%>"
                            name="workflowMessage.rfsSalesDealerGroup_<%=rfsCount%>" class="ft"  style='width: 90px;overflow:hidden;'
                            onchange="changeButton()">
                        <option value="-1"> &nbsp; </option>
                        <%
                            UserCounterpartyGroup salesDealerGroup = ruleCondition.getSalesDealerGroup();
                            for (UserCounterpartyGroup dealerGroup : salesDealerGroups) {
                        %>
                        <option value="<%=dealerGroup.getObjectID()%>" <%=dealerGroup.equals(salesDealerGroup) ? "selected" : "" %>><%=dealerGroup.getShortName()%>
                        </option>
                        <%
                            }
                        %>
                    </select>
                </td>

				<%

                out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap>");
                out.println("<span id='rfsRFS_" + rfsCount + "'>");
                out.println("<input type='checkbox' id='rfsIsRFSEnabled_" + rfsCount + "' name='workflowMessage.rfsIsRFSEnabled_" + rfsCount + "' " + (ruleCondition.getExecutionPricingParameters().isRFSEnabled() ? "checked" : "")
                        + " onclick='return validateRFSRFSCheckBox(this,\"" + rfsCount + "\")'>RFS</td>");
                out.println("</span>");
                out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap>");

                out.println("<span id='rfsSpMds_" + rfsCount + "'>");
                out.println("<input type='checkbox' id='rfsIsSpotMDSEnabled_" + rfsCount + "' name='workflowMessage.rfsIsSpotMDSEnabled_" + rfsCount + "' " + (ruleCondition.getExecutionPricingParameters().isSpotAndMDSEnabled() ? "checked" : "") +
                        " onclick='return validateRFSSpotMDSCheckBox(this,\"" + rfsCount + "\")'>Spot & MDS");
                out.println("</span>");
                out.println("<span id='rfsSpSwap_" + rfsCount + "'>");
	            out.println("<input type='checkbox' id='rfsIsSpotSwapEnabled_" + rfsCount + "' name='workflowMessage.rfsIsSpotSwapEnabled_" + rfsCount + "' " + (ruleCondition.getExecutionPricingParameters().isSpotAndSwapEnabled() ? "checked" : "") +
	                        " onclick='return validateRFSSpotSwapCheckBox(this,\"" + rfsCount + "\")'>Spot & Swap");
	            out.println("</span>");
                StringBuilder executionPricingRow1 = new StringBuilder();
                executionPricingRow1.append("<br><select  class='ft' id='rfsPricingType_").append(rfsCount).append("'  name='workflowMessage.rfsPricingType_").append(rfsCount)
                        .append("' onchange='return validateRFSPricingType(this, \""+rfsCount+"\")'").append(ruleCondition.getExecutionPricingParameters().isSpotAndMDSEnabled() ? "" : " disabled ").append("nowrap>");
                for (int j = 0; j < executionPricingType.length; j++) {
                  if(!((!isFaMultiTierEnabled && ExecutionPricingType.FAMultiTier.name().equals(executionPricingType[j].name()))
                        || (!isFaMultiQuoteEnabled && ExecutionPricingType.FAMultiQuote.name().equals(executionPricingType[j].name())))){
                    executionPricingRow1.append("<option value='").append(executionPricingType[j].name()).append("' ");
                    executionPricingRow1.append(ruleCondition.getExecutionPricingParameters().getPricingType() == executionPricingType[j] ? "selected" : "");
                    executionPricingRow1.append("   >").append(executionPricingType[j].getFullName()).append("</option>");
                  }
                }
                executionPricingRow1.append("</select>");
                out.println(executionPricingRow1.toString());

				//move inflate
                out.println("</br> <span style='height: 30px;margin-top: 6px;text-align: center'><span style='font-weight: bold'>Inflate Price</span> <input type='checkbox' id='rfsInflate_" + rfsCount + "' name='workflowMessage.rfsInflate_" + rfsCount + "' " + (ruleCondition.isInflateEnabled() ? "checked" : "") + " onclick='changeButton()'>");

                out.println("<span id='rfsInflateValText_"+rfsCount+"'><input type='text' id='rfsInflateVal_" + rfsCount + "' name='workflowMessage.rfsInflateVal_" + rfsCount + "' class='ftr' size='1' value='" + decimalFormat.format(ruleCondition.getInflateValue()) + "' onblur='return validatePercentage(this)' onkeyup='changeButton()'>%</span></span></td>");


               StringBuilder executionRuleRow1 = new StringBuilder("<td nowrap>");
                executionRuleRow1.append("<select  class='ft' style='width:85' id='rfsCoverExecutionMethod_").append(rfsCount).append("'  name='workflowMessage.rfsCoverExecutionMethod_").append(rfsCount)
                        .append("' onchange='validateRFSCoverExec(this,\"" + rfsCount + "\")'").append(" nowrap>");
                String selectedValue = "";
                if(displayCoverOnly){
                    boolean isSelected = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod() != null;
                    if(isSelected) {
                        selectedValue = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().name();

                        executionRuleRow1.append("<option value='").append(selectedValue).append("' selected >").append(selectedValue).append("</option>");
                        if(!selectedValue.equals("Cover")){
                            executionRuleRow1.append("<option value='Cover' >Cover</option>");
                        }
                    }else{
                        executionRuleRow1.append("<option value='").append(CoverExecutionMethod.Cover.getName()).append("' selected >")
                        .append(CoverExecutionMethod.Cover.getName()).append("</option>");
                    }
                }else{
                    for (int j = 0; j < rfsCoverExecutionMethods.length; j++) {
                        boolean isSelected = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod() == rfsCoverExecutionMethods[j];
                        if( isSelected ) selectedValue = rfsCoverExecutionMethods[j].name();
                        executionRuleRow1.append("<option value='").append(rfsCoverExecutionMethods[j].name()).append("' ");
                        executionRuleRow1.append(isSelected ? "selected" : "");
                        executionRuleRow1.append("   >").append(rfsCoverExecutionMethods[j].getFullName()).append("</option>");
                    }
                }
                executionRuleRow1.append("</select>").append("&nbsp;&nbsp;");
                executionRuleRow1.append("<script>$(\"rfsCoverExecutionMethod_"+rfsCount+"\").prevVal=\""+selectedValue+"\";</script>");
                out.println("<input type='hidden' id='rfsCoverExecutionMethod_hidden_" + rfsCount + "' value='" + (ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().name()) + "'>");

                executionRuleRow1.append("<input type='checkbox' id='rfsNoPriceChecked_").append(rfsCount).append("' name='workflowMessage.rfsNoPriceChecked_").append(rfsCount)
                    .append("' ").append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.NoCover) ? "" : " disabled ")
                    .append(ruleCondition.getExecutionMethodParameters().isNoPriceCheckEnabled() ? "checked" : "").append(" onclick='return validateRFSNoPriceCheckBox(this,\"" + rfsCount + "\")'>")
                    .append("<label id='rfsNoPriceCheckedLabel_").append(rfsCount).append("'>").append("NPC").append("</label>").append("&nbsp;&nbsp;");

				executionRuleRow1.append("<span style='padding-left: 10px;' id='").append("rfsFOKText_").append(rfsCount).append("'><input type='checkbox' id='rfsFOK_").append(rfsCount).append("' name='workflowMessage.rfsFOK_").append(rfsCount).append("' ")
                    .append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Cover) ? "" : " disabled ")
                    .append(ruleCondition.getExecutionMethodParameters().isFOKEnabled() ? "checked" : "").append(" onclick='return validateRFSFOKCheckBox(this,\"" + rfsCount + "\")'>").append("FOK </span>");

				if(isDealerInterventionEnabled){
					executionRuleRow1.append("<span title = 'Dealer Intervention' style='padding-left: 10px;' id='").append("rfsDIText_").append(rfsCount).append("'><input type='checkbox' id='rfsDIChecked_").append(rfsCount).append("' name='workflowMessage.rfsDIChecked_").append(rfsCount).append("' ")
	                .append(ruleCondition.getExecutionMethodParameters().isDealerInterventionEnabled() ? "checked" : "").append(" onclick='return validateRFSDICheckBox(this,\"" + rfsCount + "\")'>").append("DI </span>");
				}

				executionRuleRow1.append("&nbsp;</br>").append("<span id='").append("rfsLimit_").append(rfsCount).append("'><input type='radio' id='rfsTrdTypeLmt_").append(rfsCount).append("' name='workflowMessage.rfsTrdType_").append(rfsCount)
                       .append("' ").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "checked" : "")
                       .append(" onclick='validateRFSTradeTypeCombo(this," + rfsCount + ")'>").append("Limit").append("&nbsp;&nbsp;</span>");

			    executionRuleRow1.append("<span id='").append("rfsMarket_").append(rfsCount).append("'><input type='radio' id='rfsTrdTypeMkt_").append(rfsCount).append("' name='workflowMessage.rfsTrdType_").append(rfsCount)
                       .append("' ").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "" : "checked")
                       .append(" onclick='validateRFSTradeTypeCombo(this," + rfsCount + ")'>").append("Market").append("&nbsp;&nbsp; </span>");

                executionRuleRow1.append("<input id='rfsRange_").append(rfsCount).append("'  name='workflowMessage.rfsRange_").append(rfsCount)
                        .append("' class='ftr' type='text' onblur='validateRFSRange(this," + rfsCount + ")' value='").append(getFormattedNotionalAmountERC(ruleCondition.getExecutionMethodParameters().getRange(), decimalFormat))
                        .append("' size='2'").append(!ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Manual) && ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "" : " disabled ").append(" >").append("&nbsp;&nbsp;");

                executionRuleRow1.append("<select  class='ft' style='width:70' id='rfsRangeType_").append(rfsCount).append("'  name='workflowMessage.rfsRangeType_").append(rfsCount).append("' ")
                        .append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Manual) ? " disabled " : "").append("onchange='return validateRFSSpotMDSCheckBox(this,\"" + rfsCount + "\")' nowrap>");
                for (int j = 0; j < executionRangeType.length; j++) {
                    executionRuleRow1.append("<option value='").append(executionRangeType[j].name()).append("' ");
                    executionRuleRow1.append(ruleCondition.getExecutionMethodParameters().getRangeType() == executionRangeType[j] ? "selected" : "");
                    executionRuleRow1.append("   >").append(executionRangeType[j].getFullName()).append("</option>");
                }
                executionRuleRow1.append("</select>").append("&nbsp;&nbsp;");


                executionRuleRow1.append("</td>");
                out.println(executionRuleRow1.toString());

                if(isSpotAndSwapEnabled) {
                	out.println("<td nowrap rowspan='2' style='padding-left: 5px; padding-right: 5px;'><select  class='ft' style='width:70' id='rfsSpotAndSwapCoverExecutionMethod_" + rfsCount + "' name='workflowMessage.rfsSpotAndSwapCoverExecutionMethod_" + rfsCount + "'"
                            +  " onchange='validateSpotAndSwapCoverExec(this,\"" + rfsCount + "\")' nowrap>"
                            + "<option value='" + CoverExecutionMethod.Cover.name() + "'" + (ruleCondition.getExecutionMethodParameters().getSpotAndSwapCoverExecutionMethod() == CoverExecutionMethod.Cover ? "selected" : "") + ">" + CoverExecutionMethod.Cover.name() + "</option>"
                            + "<option value='" + CoverExecutionMethod.NoCover.name() + "'" + (ruleCondition.getExecutionMethodParameters().getSpotAndSwapCoverExecutionMethod() == CoverExecutionMethod.NoCover ? "selected" : "") + ">" + CoverExecutionMethod.NoCover.name()  + "</option>"
                            + "</select><span id='rfsSpotAndSwapCoverExecutionMethodNA_" + rfsCount + "'>N/A</span></td>");
                }

                out.println("<td nowrap rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input type='checkbox' id='rfsFullFill_" + rfsCount + "' name='workflowMessage.rfsFullFill_" + rfsCount + "' " + (ruleCondition.isFullFillEnabled() ? "checked" : "")
                        + " onclick='return validateRFSFullFillCheckBox(this,\"" + rfsCount + "\")'>&nbsp;&nbsp;<span id='rfsFullFillValText_"+rfsCount+"'><input type='text' id='rfsFullFillVal_" + rfsCount + "' name='workflowMessage.rfsFullFillVal_" + rfsCount + "' class='ftr' size='1' value='" + decimalFormat.format(ruleCondition.getFullFill()) + "' onblur='return validatePercentage(this)' onkeyup='changeButton()'>%</span></td>");
                out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input type='checkbox' id='rfsAutoCover_" + rfsCount + "' name='workflowMessage.rfsAutoCover_" + rfsCount + "' " + (ruleCondition.isAutoCoverEnabled() ? "checked" : "") + " onclick='changeButton()' ><span id='rfsAutoCoverNA_" + rfsCount + "'>N/A</span></td>");




                out.println("</tr>");
				    // Second Row
                    out.println("<tr class='" + cls + "' style='text-align:center;padding-left: 0px; padding-right: 0px;' rowId = '" + rfsCount + "'>");
                    out.println("<td><table><tr class='" + cls + "' style='text-align:center;padding-left: 0px; padding-right: 0px;'><td style='letter-spacing: -0.05em;padding-left: 0px; padding-right: 0px;width: 260px;' id='rfsDescription_" + rfsCount + "' nowrap></td></tr></table></td>");
				//out.println("<td style='letter-spacing: -0.05em;padding-left: 0px; padding-right: 0px;' id='rfsDescription_" + rfsCount + "' nowrap></td>");
				  out.println("</tr>");

            %>

                <script>
                    validateRFSExecutionRuleCondition(<%=rfsCount%>, false); // pass true to turn on onLoad validation alerting & changeButton()
                </script>
                <%
                        rfsCount++;

                    }

           %>

           </table>
           <script>
               updateVWAPCheckbox(<%=multiFillEnabled && isDisableVWAPIfMultiFillEnabled%>);
           </script>
            <table id="rfsExcnAction">
               <tr>
                    <td class="f" colspan="11">
                        <span style="width:25px;">&nbsp;</span>
                        <input type="button" class="b1" value="Delete Rule"  onclick="javascript:deleteExecutionCondition('rfsExecutionConditions','RFS');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
                        <input type="button" class="b1" value="Add Rule" onclick="javascript:newExecutionCondition('rfsExecutionConditions','RFS');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
                        <input type="hidden" id='rfsCOUNTEXECUTIONCOND' name="workflowMessage.rfsCOUNTEXECUTIONCOND" value="<%=rfsRuleConditions.size()%>"/>
                   </td>
               </tr>
             </table><br><br>
        </td>
   </tr>
<%--
    <tr>
         <td class="f3" colspan="2" nowrap>Full Fill Method</td>
         <td class="f" colspan="4" nowrap>
           <input type="radio" name="workflowMessage.rfscustFullFillType" id="rfsCustFullFillType1" value='Always'  checked  disabled>&nbsp;Always&nbsp;
           <input type="radio" name="workflowMessage.rfscustFullFillType" id="rfsCustFullFillType2" value='FillAleastOne'  disabled>&nbsp;Cover has at least One fill&nbsp;
           <input type="radio" name="workflowMessage.rfscustFullFillType" id="rfsCustFullFillType3" value='Custom'    disabled>&nbsp;Custom&nbsp;
           <select name="customConditionForCustomerFullFill" onChange="selectCustFFDescription();changeButton()" disabled="disabled" class="ft" >
                        <option value="TwentyFivePercentage" selected="selected">Cover is Atleast 25% filled</option>
                        <option value="FiftyPercentage">Cover is Atleast 50% filled</option>
                        <option value="SeventyFivePercentage">Cover is Atleast 75% filled</option>
           </select>
         </td>
         <td  class="f">&nbsp;</td>
    </tr>

    <tr>
         <td class="f3" colspan="2" nowrap></td>
         <td class="f" colspan="4" nowrap>&nbsp;Customer trade is filled with the same amount as the cover trade</td>
         <td  class="f" >&nbsp;</td>
    </tr>
--%>
	<tr style="display: <%=showRFS %>" id="rfsExcnOvrInt">
		<td class="f3" nowrap><strong>Override Stream Update Interval</strong></td>
		<td class="f3" nowrap><input type="checkbox" id="overrideRFSUpdateInterval" name="workflowMessage.overrideRFSUpdateInterval" value="<%=overrideRFSUpdateInterval%>" <%=overrideRFSUpdateInterval.booleanValue()?"checked":""%> class="ft" onclick="changeButton();updateRFSIntervals();"></td>
        <td class="f3" nowrap>Full Stream Update Interval</td>
		<td class="f3" nowrap><input type="text" id = "rfsFullUpdateInterval" name="workflowMessage.rfsFullUpdateInterval" size="2" value="<%=(rfsFullUpdateInterval==null)?"":rfsFullUpdateInterval.toString()%>"
	        <%=overrideRFSUpdateInterval.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>milliseconds (Stream Default:&nbsp;&nbsp;<%= rmProfile.getQuotePublicationIntervalRFS() %>&nbsp;milliseconds)</td>
        <td class="f3"></td>
		<td class="f3"></td>
    </tr>
    <tr style="display: <%=showRFS %>" id="rfsExcnUpdInt">
        <td class="f3" colspan="2" nowrap></td>
        <td class="f3" nowrap>Best Bid/Offer Update Interval</td>
		<td class="f3" nowrap><input type="text" id = "rsfBestBidOfferInterval" name="workflowMessage.rsfBestBidOfferInterval" size="2" value="<%=(rsfBestBidOfferInterval==null)?"":rsfBestBidOfferInterval.toString()%>"
	        <%=overrideRFSUpdateInterval.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>milliseconds(Stream Default:&nbsp;<%= rmProfile.getQuotePublicationCheckIntervalRFS() %>&nbsp;milliseconds)</td>
        <td class="f3"></td>
		<td class="f3"></td>
    </tr>
		<tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
		<tr style="display: <%=showRFS %>" id="rfsExcnParamOvrInt">
		<td class="f3" nowrap><strong>Override Stream RFS Parameters</strong></td>
		<td class="f3" nowrap><input type="checkbox" id="overrideRFSParameters" name="workflowMessage.overrideRFSParameters" value="<%=overrideRFSParameters%>" <%=overrideRFSParameters.booleanValue()?"checked":""%> class="ft" onclick="changeButton();updateRFSParameters();"></td>
        <td class="f3" nowrap>Request Expiry</td>
		<td class="f3" nowrap><input type="text" id = "requsetExpiry" name="requsetExpiry" size="2" value="<%=(requestExpiry==null)?"":requestExpiry.toString()%>"
	        <%=overrideRFSParameters.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>seconds (Stream Default:&nbsp;<%= rmProfile.getRequestExpiry() %> &nbsp; seconds)</td>
        <td class="f3"></td>
		<td class="f3"></td>
    </tr>
    <tr style="display: <%=showRFS %>" id="rfsExcnParamUpdInt">
        <td class="f3" colspan="2" nowrap></td>
        <td class="f3" nowrap>Maximum Tenor</td>
		<td class="f3" nowrap><input type="text" id="maximumTenor" name="maximumTenor" size="2" value="<%=maxTenor==null?"":maxTenor.toString()%>" <%=overrideRFSParameters.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='this.value=this.value.toUpperCase();changeButton()'></td>
		<td class="f3" nowrap><span id="maximumTenorDesc"/></span> (Stream Default:&nbsp;&nbsp;<%= profileConfig.getOriginalMaximumTenor() != null ? profileConfig.getOriginalMaximumTenor().getName() : ""%>)</td>
        <td class="f3"></td>
		<td class="f3"></td>
    </tr>

    <tr>
        <td id="autoExcnTag" class="f" colspan="6" style="font-size: 9pt;font-weight: bold;"  nowrap><b>Full-Fill Auto Cover</b></td><td class="f" nowrap></td>
    </tr>
    <tr>
       <td id="autoExcnDesc" class="f3" colspan="6" nowrap>Auto-Cover Execution Rules (If no rule criteria is met, Auto Cover execution will be done at Market)

              <table id="autoExecutionConditions" cellpadding="0" border="0" cellspacing="1" style="width:1530px;background-color: #999;">
           <tr class="lt" style="text-align:center">
                 <td rowspan="2" style="vertical-align:bottom;width: 10px"><input type="checkbox"></td>
                 <td rowspan="2" class="ac" width="2%">Sort</td>
                 <td rowspan="2">Action</td>
				 <td colspan="2">Notional Amount</td>
                 <td rowspan="2">Execution Method</td>
                 <td colspan="2">Limit Order</td>
                 <td colspan="1">Market Order</td>
           </tr>
           <tr class="lt" style="text-align:center">
		        <td>Low (>)</td>
                <td>High (<=)</td>
                <td>TIF(ms)</td>
                <td>Range</td>
                <td>TOB Range</td>
           </tr>

            <%
               ExecutionRule autoExecutionRule = profileConfig.getAutoCoverExecutionRule();
               Collection<ExecutionRuleCondition> autoRuleConditions = (autoExecutionRule !=null && autoExecutionRule.getExecutionRuleConditions() != null && autoExecutionRule.getExecutionRuleConditions().size() !=0) ? autoExecutionRule.getExecutionRuleConditions() : null ;
               if( autoRuleConditions == null ) {
                    autoRuleConditions = new ArrayList<ExecutionRuleCondition>();
                   /*ExecutionRuleCondition autoExecutionRuleCondition = ModelFactory.getInstance().newExecutionRuleCondition();
                   autoExecutionRuleCondition.setFullFillEnabled(true);
                   autoExecutionRuleCondition.setFullFill(100);
                   autoRuleConditions.add(autoExecutionRuleCondition);*/
                }
                int autoCount =0;
                for (ExecutionRuleCondition ruleCondition : autoRuleConditions) {
                    String cls = autoCount % 2 ==0? "tl" : "tlc";
                    out.println("<tr class='"+ cls +"' style='text-align:center' rowId = '" + autoCount + "'>");
                    out.println("<td><input type='checkbox' id='autoExecRuleCond_"+ autoCount +"' name='workflowMessage.autoExecRuleCond_"+ autoCount +"'> </td>");
                    out.println(" <td><input type='text' id='autoSortOrder_"+ autoCount +"'  name='workflowMessage.autoSortOrder_"+ autoCount +"' class='ftr' size='2' value='"+ruleCondition.getSortOrder()+"' onblur='changeButton()'> </td>");

                    out.println("<td nowrap><select  class='ft' style='width:155'  id='autoActionType_"+ autoCount +"'  name='workflowMessage.autoActionType_"+ autoCount +"'  onchange='changeButton()'>");
                    for (int j= 0;  j < autoCoverExecRuleConditionType.length; j++) {
                          out.println("<option value='"+autoCoverExecRuleConditionType[j].name()+"' "+(((ExecutionRuleConditionType)ruleCondition.getConditionType()) == autoCoverExecRuleConditionType[j] ? "selected" : "" )+"   >"+autoCoverExecRuleConditionType[j].getDescription()+"</option>");
                    }
                    out.println( "</select> </td>");

					                    out.println( "<td><input id='autoExecNotLow_"+autoCount+"'  name='workflowMessage.autoExecNotLow_"+autoCount+"' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getLowAmount(),decimalFormat)+"' data-string='" + ruleCondition.getLowAmount() + "' size='9' ></td>");

                    out.println("<td><input id='autoExecNotHigh_"+autoCount+"' name='workflowMessage.autoExecNotHigh_"+autoCount+"' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getHighAmount(),decimalFormat) +"' data-string='" + ruleCondition.getHighAmount() + "' size='9' ></td>");


                    out.println("<td nowrap><select  class='ft' style='width:155'  id='autoExecutionMethod_"+ autoCount +"'  name='workflowMessage.autoExecutionMethod_"+ autoCount +"'  onchange='handleAutoExecutionChange(this," + autoCount + ");changeButton()'>");
                    for (int j= 0;  j < autoCoverExecutionMethods.length; j++) {
                          out.println("<option value='"+autoCoverExecutionMethods[j].name()+"' "+(((ExecutionMethod)ruleCondition.getExecutionMethod()) == autoCoverExecutionMethods[j] ? "selected" : "" )+"   >"+autoCoverExecutionMethods[j].getDescription()+"</option>");
                    }
                    out.println( "</select> </td>");

                    boolean isLimitMarket = false;
                    boolean isMarket = false;

                    if (ruleCondition.getExecutionMethod().name().equals(ExecutionMethod.LimitMarket.name())) {
                        isLimitMarket = true;
                    } else if (ruleCondition.getExecutionMethod().name().equals(ExecutionMethod.TopOfTheBook.name())) {
                        isMarket = true;
                    }
                    out.println( "<td><input id='autoLimitTif_"+ autoCount +"'  name='workflowMessage.autoLimitTif_"+ autoCount + "' class='ftr' type='text' onblur='userDecimalFormat.format(this,0);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getTimeInForceInterval(), decimalFormat) +"' size='9'" + (isMarket ? "disabled" : "") + "></td>");

                    out.println("<td><input id='autoLimitRange_"+ autoCount +"' name='workflowMessage.autoLimitRange_"+ autoCount + "' class='ftr' type='text' onblur='javascript:validateMarketRange(this);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getLimitRange(), decimalFormat) +"' size='9'" + (isMarket ? "disabled" : "") + "></td>");

                    out.println( "<td><input id='autoTOBRange_"+ autoCount +"'  name='workflowMessage.autoTOBRange_"+ autoCount + "' class='ftr' type='text' onblur='javascript:validateMarketRange(this, -1);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getTobRange(), decimalFormat) + "' size='9' ></td>");

                    out.println("</tr>");

                    autoCount++;
                }
               %>
        </table>
        <table>
           <tr>
                <td class="f" colspan="11">
                    <span style="width:25px;">&nbsp;</span>
                    <input type="button" class="b1" value="Delete Rule"  onclick="javascript:deleteExecutionCondition('autoExecutionConditions','Auto');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
                    <input type="button" class="b1" value="Add Rule" onclick="javascript:newAutoExecutionCondition('autoExecutionConditions','Auto');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
                    <input type="hidden" id='autoCOUNTEXECUTIONCOND' name="workflowMessage.autoCOUNTEXECUTIONCOND" value="<%=autoRuleConditions.size()%>"/>
               </td>
           </tr>
         </table><br><br>
    </td>
   </tr>

</table>