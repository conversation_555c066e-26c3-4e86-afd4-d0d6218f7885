<%@ page import="com.integral.broker.admin.ConfigurationService" %>
<%@ page import="com.integral.admin.pricemaking.PriceMBeanFactory" %>
<%@ page import="com.integral.admin.pricemaking.PriceMakingMBean" %>
<%@ page import="com.integral.jsp.framework.table.TableForm" %>
<%@ page import="com.integral.admin.utils.organization.OrganizationUtil" %>
<%@ page import="com.integral.user.Organization" %>
<%@ page import="org.apache.struts.util.ResponseUtils" %>
<%@ page import="java.util.TimeZone" %>
<%@ page import="com.integral.is.ISCommonConstants" %>
<%@ page import="com.integral.broker.model.*" %>
<%@ page import="com.integral.admin.utils.organization.SalesDealerGroupUtil" %>
<%@ page import="com.integral.finance.counterparty.UserCounterpartyGroup" %>
<%@ page import="java.util.List" %>
<%@ page import="com.integral.broker.config.BrokerProvisionConfigFactory" %>
<%@ page import="org.apache.commons.lang.ArrayUtils" %>
<%@ page import="com.integral.riskmanagement.rw.provision.RWProvisionConfig" %>
<%@ page import="com.integral.riskmanagement.rw.provision.RWProvisionConfigManager" %>
<%@ page import="com.integral.is.common.util.YMUtil" %>
<%@page import="com.integral.broker.configuration.ConfigurationFactory"%>
<%@ page import="com.integral.broker.BrokerAdaptorUtil" %>
<%@ taglib uri="/tag/idc-html.tld" prefix="idcHtml" %>
<%@ taglib uri="/tag/idc-bean.tld" prefix="idcBean" %>
<%@ taglib uri="/tag/struts-html.tld" prefix="html" %>
<%@ taglib uri="/tag/idc-logic.tld" prefix="idcLogic" %>


<script type="text/javascript">

<%
    ConfigurationService configService = com.integral.broker.admin.AdminFactory.getInstance().getConfigurationService();
    PriceMakingMBean pmConf = PriceMBeanFactory.getInstance().getPriceMakingMBean() ;
    String orgEncryptedId=ResponseUtils.filter(request.getParameter( "orgEncryptedId" ));
    Organization org=OrganizationUtil.getOrganization(TableForm.decryptId(orgEncryptedId));
    ExecutionMethod[] espExecutionmethods=configService.getEspExecutionMethods();

    CoverExecutionMethod[] espCoverExecutionMethods = configService.getESPCoverExecutionMethods();
    CoverExecutionMethod[] rfsCoverExecutionMethods = configService.getRFSCoverExecutionMethods();
    ExecutionRangeType[] executionRangeType = configService.getExecutionRangeType();
    ExecutionPricingType[] executionPricingType = configService.getExecutionPricingTypes();
    PriceCheckType[] priceCheckType = configService.getPriceCheckTypes();
    boolean isDisableVWAPIfMultiFillEnabled = pmConf.isVWAPDisabledIfMultiFillEnabled(org.getShortName());
    boolean isMatchDelayEnabled = pmConf.isDelayedOrderMatchEnabled(org.getShortName());
    boolean isNewCopyTradeEnabled = BrokerProvisionConfigFactory.getBrokerProvisionConfig().isNewCopyTradeEnabled(org.getShortName());
    boolean isOldCopyTradeEnabled = BrokerProvisionConfigFactory.getBrokerProvisionConfig().isCopyTradeEnabled(org.getShortName());
    boolean partialCoverEnabled = BrokerProvisionConfigFactory.getBrokerProvisionConfig().isPartialCoverEnabled(org.getShortName());

    if( !isOldCopyTradeEnabled) {
        espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, com.integral.broker.model.CoverExecutionMethod.Copy );
    }

    RWProvisionConfig rwProvisionConfig = RWProvisionConfigManager.getInstance().getRwProvisionConfig();
    //initializing supportedBookNames
    List<String> supportedBookNames = rwProvisionConfig.getRiskWarehouseSupportedBookNames(org.getShortName());
    supportedBookNames.remove("A"); //ABook is implicit and not configurable
    boolean isESPMultiBookPropertyEnabled = BrokerAdaptorUtil.getInstance().isMultiBookStampingEnabledForOrg(org.getShortName(),"ESP");
    boolean isRFSMultiBookPropertyEnabled = BrokerAdaptorUtil.getInstance().isMultiBookStampingEnabledForOrg(org.getShortName(),"RFS");

    if( !rwProvisionConfig.getRiskWarehouseOrganizations().contains(org.getShortName())){
        espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, CoverExecutionMethod.Warehouse );
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }
    else if(!YMUtil.isWarehouseOptionEnabled(org.getShortName())){
        espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, CoverExecutionMethod.Warehouse );
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }
    else if(!rwProvisionConfig.isRFSNetSpotEnabledForRiskWarehouse(org.getShortName())){
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }

    if(!ConfigurationFactory.getInstance().getPriceConfigurationMBean().isOrderRejectEnabled(org.getShortName())){
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Reject );
    }

     String espOptions= "var espOptions='";
     for (int i= 0;  i < espExecutionmethods.length; i++) {
          boolean isVwapEnabledForOrg = pmConf.isVwapExecutionEnabled( org.getShortName() ) ;
          if( ! (! isVwapEnabledForOrg && ExecutionMethod.WeightedAverage.equals( espExecutionmethods[i]  ) ) ) {
                espOptions =  espOptions   + "<option value="+espExecutionmethods[i].name()+">"+espExecutionmethods[i].getDescription()+"</option>";
          }
     }
     out.println( espOptions+"';" );

     String espCoverExecOptions = "var espCoverExecOptions='";
     boolean isCoverOnlyEnabled = BrokerProvisionConfigFactory.getBrokerProvisionConfig().isDisplayCoverOnly(org.getShortName());
     if(isCoverOnlyEnabled){
         espCoverExecOptions =  espCoverExecOptions + "<option value=Cover>Cover</option>" ;
     }else{
         for (int i= 0;  i < espCoverExecutionMethods.length; i++) {
                espCoverExecOptions =  espCoverExecOptions   + "<option value="+espCoverExecutionMethods[i].name()+">"+espCoverExecutionMethods[i].getFullName()+"</option>";
         }
     }
     out.println( espCoverExecOptions+"';" );

     String rfsCoverExecOptions = "var rfsCoverExecOptions='";
     if(isCoverOnlyEnabled){
    	 rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.NoCover );
    	 rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Manual );
    	 rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
     }
     for (int i= 0;  i < rfsCoverExecutionMethods.length; i++) {
             rfsCoverExecOptions =  rfsCoverExecOptions   + "<option value="+rfsCoverExecutionMethods[i].name()+">"+rfsCoverExecutionMethods[i].getFullName()+"</option>";
     }
     out.println( rfsCoverExecOptions+"';" );

     String bookNameOptions = "var bookNameOptions='";
     for (String bookName : supportedBookNames) {
        bookNameOptions =  bookNameOptions   + "<option value="+bookName+">"+bookName+"</option>";
     }
     out.println( bookNameOptions+"';" );
     
     String rfsSpotAndSwapCoverExecOptions = "var rfsSpotAndSwapCoverExecOptions='";
     rfsSpotAndSwapCoverExecOptions += "<option value="+CoverExecutionMethod.Cover.name()+">"+CoverExecutionMethod.Cover.name()+"</option>";
     rfsSpotAndSwapCoverExecOptions += "<option value="+CoverExecutionMethod.NoCover.name()+" selected>"+CoverExecutionMethod.NoCover.name()+"</option>";
     out.println( rfsSpotAndSwapCoverExecOptions+"';" );
     
     String execRangeOptions= "var execRangeOptions='";
     for (int i= 0;  i < executionRangeType.length; i++) {
            execRangeOptions =  execRangeOptions   + "<option value="+executionRangeType[i].name()+">"+executionRangeType[i].getFullName()+"</option>";
     }
     out.println( execRangeOptions+"';" );

     String execPriceOptions= "var execPriceOptions='";
     for (int i= 0;  i < executionPricingType.length; i++) {
            execPriceOptions =  execPriceOptions   + "<option value="+executionPricingType[i].name()+">"+executionPricingType[i].getFullName()+"</option>";
     }
     out.println( execPriceOptions+"';" );

     ExecutionMethod[] rfsExecutionmethods=ExecutionMethod.onlyRfsValues();

     String rfsOptions= "var rfsOptions='";
     for (int i= 0;  i < rfsExecutionmethods.length; i++) {
       rfsOptions =  rfsOptions   + "<option value="+rfsExecutionmethods[i].name()+">"+rfsExecutionmethods[i].getDescription()+"</option>";
     }
     out.println( rfsOptions+"';" );
     
     String priceCheckOptions = "var priceCheckOptions='";
     for(int i=0; i < priceCheckType.length; i++){
    	 priceCheckOptions = priceCheckOptions + "<option value="+priceCheckType[i].name()+">"+priceCheckType[i].name()+"</option>";
    	 
     }    
     out.println( priceCheckOptions+"';" );

     String timeZoneOptions=" var timeZoneOptions= '";
     String[] availableTimeZoneIDs = TimeZone.getAvailableIDs();
     for (int j= 0;  j < availableTimeZoneIDs.length; j++) {
        timeZoneOptions = timeZoneOptions + "<option value="+availableTimeZoneIDs[j]+" "+(availableTimeZoneIDs[j].equalsIgnoreCase( "UTC") ? " selected " : "")+"  >"+availableTimeZoneIDs[j]+"</option>";
     }
     out.println( timeZoneOptions+"';" );

     ExecutionMethod[] autoExecutionmethods = ExecutionMethod.autoCoverValues();

     String autoOptions= "var autoOptions='";
     for (int i= 0;  i < autoExecutionmethods.length; i++) {
       autoOptions =  autoOptions   + "<option value="+autoExecutionmethods[i].name() + (autoExecutionmethods[i].name().equals(ExecutionMethod.TopOfTheBook.name()) ? " selected " : "") + ">"+autoExecutionmethods[i].getDescription()+"</option>";
     }
     out.println( autoOptions+"';" );

     ExecutionRuleConditionType[] autoConditionTypes = ExecutionRuleConditionType.autoCoverValues();

     String autoConditionTypesOptions= "var autoConditionTypesOptions='";
     for (int i= 0;  i < autoConditionTypes.length; i++) {
       autoConditionTypesOptions =  autoConditionTypesOptions   + "<option value="+autoConditionTypes[i].name()+">"+autoConditionTypes[i].getDescription()+"</option>";
     }
     out.println( autoConditionTypesOptions+"';" );
     
     String ctxPath = "var contextPath ='"+request.getContextPath()+"';";
     out.println(ctxPath);
     
     boolean isPriceCheckEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceCheckEnabled(org.getShortName());
     boolean isDealerInterventionEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isDealerInterventionEnabled(org.getShortName());
     boolean isSpotAndSwapForSSPEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceSSPUsingSwapEnabled(org.getShortName());
     boolean isSpotAndSwapForOTEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceOTUsingSwapEnabled(org.getShortName());
     boolean isSpotAndSwapForSWEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceSwapUsingSpotAndSwapEnabled(org.getShortName());
     boolean isSpotAndSwapForNDFAndSWEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceNDFAndNDFSwapUsingSpotAndSwapEnabled(org.getShortName());
     boolean isSpotAndSwapEnabled = isSpotAndSwapForSSPEnabled || isSpotAndSwapForOTEnabled || isSpotAndSwapForSWEnabled || isSpotAndSwapForNDFAndSWEnabled;
     boolean isESPUIOnly = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceMakingESPUIOnlyEnabled(org.getShortName());
     if(isESPUIOnly){
         isSpotAndSwapEnabled   = false;
         isSpotAndSwapForSSPEnabled = false;
         isSpotAndSwapForOTEnabled = false;
         isSpotAndSwapForSWEnabled = false;
         isSpotAndSwapForNDFAndSWEnabled = false;
     }

%>

    function reInitializeIdForTable(table_id, offset, continuous_rows) {
        var table_data = window.document.getElementById(table_id);
        var rows_collection  = table_data.rows;
        var last_row = rows_collection.length;
        var count = rows_collection.length - offset;

        var row_count = 0;
        for (var i=offset; i<last_row; i++) {
            var row = rows_collection[i];
            for (var j=0; j<row.cells.length; j++) {
                var col = row.cells[j];
                var input_elements = col.getElementsByTagName('input');
                if (input_elements != null && input_elements.length > 0) {
                    for (var k=0; k<input_elements.length; k++) {
                        var input_element = input_elements[k];
                        const last_index = input_element.id.lastIndexOf('_');
                        if (last_index > 0) {
                            var new_id = input_element.id.slice(0, last_index) + '_' + row_count;
                            input_element.id = new_id;
                            var new_input_element = document.getElementById(new_id);
                        }
                    }
                }
                var select_elements = col.getElementsByTagName('select');
                if (select_elements != null && select_elements.length > 0) {
                    for (var k=0; k<select_elements.length; k++) {
                        var select_element = select_elements[k];
                        const last_index = select_element.id.lastIndexOf('_');
                        if (last_index > 0) {
                            var new_id = select_element.id.slice(0, last_index) + '_' + row_count;
                            select_element.id = new_id;
                            var new_select_element = document.getElementById(new_id);
                        }
                    }
                }
            }
            if (continuous_rows || ((i - offset) % 2 == 0)) {
                row_count++;
            }
        }
    }

    function newExecutionConditionV2(streamMinMatchDelay,streamMaxMatchDelay,tbl,type) {
        var isRFS = (type == 'RFS');
        if (isRFS) {
            newRFSExecutionCondition(tbl);
        } else {
            newESPExecutionCondition(streamMinMatchDelay, streamMaxMatchDelay, tbl);
        }
    }

    function newExecutionCondition(tbl,type) {
        var isRFS = (type == 'RFS');
        if (isRFS) {
            newRFSExecutionCondition(tbl);
        } else {
            newESPExecutionCondition(0,0,tbl);
        }
    }

    function newESPExecutionCondition(streamMinMatchDelay,streamMaxMatchDelay,tbl) {
        var executionConditions = window.document.getElementById(tbl);
        var rowsCollection  = executionConditions.rows;
        var lastRow = rowsCollection.length;
        var count= rowsCollection.length -2;

        var newRow_1 = executionConditions.insertRow(-1);
        newRow_1.className = (lastRow % 2 ? "tl" : "tlc");
        var rowid = 0;
        var sortOrder = 10;
        if (count != 0){
            var prevRow = rowsCollection[count];
            rowid = getAttrValueOr(prevRow, "rowId", 0)-0+1;
//            rowid = prevRow.rowId - 0  + 1;
            sortOrder = prevRow.cells[1].firstChild.value -0 + 10;
        }
        var prefix="esp";

//        newRow_1.rowId = rowid;
        newRow_1.setAttribute('rowId', rowid);
        var cell_1 = newRow_1.insertCell(-1);
        cell_1.className ="ac";
        cell_1.rowSpan = 2;
        cell_1.innerHTML = '<input type="checkbox"  id="'+prefix+'ExecRuleCond_'+rowid+'" name="workflowMessage.'+prefix+'ExecRuleCond_'+rowid+'" >';
        cell_1.noWrap=true;

        var cell_2 = newRow_1.insertCell(-1);
        cell_2.rowSpan = 2;
        cell_2.innerHTML='<input type="text" id="'+prefix+'SortOrder_'+rowid+'" name="workflowMessage.'+prefix+'SortOrder_'+rowid+'" value="'+sortOrder+'"  size="2" class="ftr" >';
        cell_2.className ="ac";
        cell_2.noWrap=true;


		var cell_3 = newRow_1.insertCell(-1);
		cell_3.rowSpan = 2;
		cell_3.className ="ac";
        cell_3.noWrap=true;
        cell_3.innerHTML='<input type="text" id="'+prefix+'ExecNotLow_'+rowid+'" name="workflowMessage.'+prefix+'ExecNotLow_'+rowid+'" value=""  onblur="convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()" size="9" class="ftr"></br><input type="text" id="'+prefix+'ExecNotHigh_'+rowid+'" name="workflowMessage.'+prefix+'ExecNotHigh_'+rowid+'" value="" onblur="convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()" size="9" class="ftr" >';


		var cell_4 = newRow_1.insertCell(-1);
        cell_4.innerHTML='<input type="text" id="'+prefix+'ExecTmStrt_'+rowid+'" name="workflowMessage.'+prefix+'ExecTmStrt_'+rowid+'" value=""  onblur="changeButton()" size="6" class="ft" ><span style="width: 0px">&nbsp;</span><input type="text" id="'+prefix+'ExecTmEnd_'+rowid+'" name="workflowMessage.'+prefix+'ExecTmEnd_'+rowid+'" value=""   onblur="changeButton()" size="6" class="ft" ></br><select id="'+prefix+'TimeZoneID_'+rowid+'" name="workflowMessage.'+prefix+'TimeZoneID_'+rowid+'" onchange="changeButton()" class="ft"  style="width: 114px;overflow:hidden;">'+timeZoneOptions+'</select>';
        cell_4.className ="ac";
		cell_4.rowSpan = 2;
        cell_4.noWrap=true;

		addSalesDealerGroup(newRow_1, rowid, prefix );



        var cell_5 = newRow_1.insertCell(-1);
        cell_5.className ="ac";
		cell_5.rowSpan = 1;
        cell_5.innerHTML= '<select class="ft" style="width:85" id="'+prefix+'CoverExecutionMethod_'+rowid+'"  name="workflowMessage.'+prefix+'CoverExecutionMethod_'+rowid
                +'" onchange="validateESPCoverExec(this,' + rowid + ');updateBookVisibility(this,\'ESP\');" nowrap>'+espCoverExecOptions+'</select>' + '&nbsp;&nbsp;&nbsp;&nbsp;'
            <% if(isESPMultiBookPropertyEnabled){ %>
                + '<select class="ft" id="'+prefix+'CoverExecutionMethod_'+rowid+'_Book"  name="workflowMessage.'+prefix+'CoverExecutionMethod_'+rowid
                    +'_Book" style="display:none;" nowrap>'+bookNameOptions+'</select>' + '&nbsp;&nbsp;'
            <% } %>

            + '<input type="checkbox" id="'+prefix+'NoPriceChecked_'+rowid+'" name="workflowMessage.'+prefix+'NoPriceChecked_'+rowid + '" disabled onclick="validateESPNoPriceCheckBox(this,' + rowid + ')")">'
            + '<label id="'+prefix+'NoPriceCheckedLabel_'+rowid+'">NPC</label>' + '&nbsp;&nbsp'

            + '<input type="radio" id="'+prefix+'TrdTypeMkt_'+rowid+'" name="workflowMessage.'+prefix+'TrdType_'+rowid+ '" onclick="validateESPTradeTypeCombo(this,' + rowid + ')" )">Market' + '&nbsp;&nbsp;'
            + '<input type="radio" id="'+prefix+'TrdTypeLmt_'+rowid+'" name="workflowMessage.'+prefix+'TrdType_'+rowid+ '" checked onclick="validateESPTradeTypeCombo(this,' + rowid + ')" )">Limit' + '&nbsp;&nbsp;'
            //+ '<input type="checkbox" checked id="'+prefix+'RangeEnabled_'+rowid+'" name="workflowMessage.'+prefix+'RangeEnabled_'+rowid+ '" onclick="validateESPRangeCheckBox(this,' + rowid + ')" )">Range' + '&nbsp;&nbsp;'

            + '<input type="text" id="'+prefix+'Range_'+rowid+'" name="workflowMessage.'+prefix+'Range_'+rowid+'" value="0" onblur="validateESPRange(this,' + rowid + ')" size="3" class="ft" >' + '&nbsp;&nbsp;'
            + '<select class="ft" style="width:70" id="'+prefix+'RangeType_'+rowid+'"  name="workflowMessage.'+prefix+'RangeType_'+rowid+'" onchange="validateESPCoverExec(this,' + rowid + ')" nowrap>'+execRangeOptions+'</select>' + '&nbsp;&nbsp;'

            + '<input type="checkbox" class="espVWAP" id="'+prefix+'VWAP_'+rowid+'" name="workflowMessage.'+prefix+'VWAP_'+rowid+ '" onclick="validateESPVWAPCheckBox(this,' + rowid + ')" )">VWAP' + '&nbsp;&nbsp;'
            + '<input type="checkbox"  id="'+prefix+'FOK_'+rowid+'" name="workflowMessage.'+prefix+'FOK_'+rowid+ '" onclick="validateESPFOKCheckBox(this,' + rowid + ')" )">FOK';
        
        var copyTrade = '';
        if(<%=isNewCopyTradeEnabled%>){
        	copyTrade = '&nbsp;&nbsp;' + '<input type="checkbox" id="'+prefix+'CopyChecked_'+rowid+'" name="workflowMessage.'+prefix+'CopyChecked_'+rowid + '" onchange="changeButton()" onclick="validateESPExecutionRuleCondition(' + rowid + ')")">'
           	     		 + '<label id="'+prefix+'CopyCheckedLabel_'+rowid+'">Copy</label>' + '&nbsp;&nbsp'
            			 + '<input type="text" id="'+prefix+'CopyValue_'+rowid+'" name="workflowMessage.'+prefix+'CopyValue_'+rowid+'" value="" size="3" class="ft" onblur="validateESPCopyValue(this,' + rowid + ')">'
            			 + '<label id="'+prefix+'CopyPercentageLabel_'+rowid+'">%</label>';
        }
        cell_5.innerHTML += copyTrade;
        cell_5.noWrap=true;

        if(<%=isMatchDelayEnabled%>){
            var cell_5_1a = newRow_1.insertCell(-1);
            cell_5_1a.rowSpan = 2;
        	var matchDelay = '<label id="espMatchDelayOverrideLabel_' + rowid
        	                 + '">Override</label><input type="checkbox" id="espMatchDelayOverride_' + rowid
        	                 + '" name="workflowMessage.espMatchDelayOverride_' + rowid
        	                 + '" onclick="changeButton(),toggleMatchDelayFields(' + rowid
        	                 + ',0,' + streamMinMatchDelay + ',0,'
        	                 + streamMaxMatchDelay + ')"/><br><label id="espMinMatchDelayLabel_' + rowid
        	                 + '">Min</label>&nbsp;<input type="text" id="espMinMatchDelay_' + rowid
        	                 + '" name="workflowMessage.espMinMatchDelay_' + rowid
        	                 + '" class="ftr" size="2" value="'+ streamMinMatchDelay
        	                 + '" onkeyup="changeButton()" disabled/>&nbsp;&nbsp;<label id="espMaxMatchDelayLabel_' + rowid
        	                 + '">Max</label>&nbsp;<input type="text" id="espMaxMatchDelay_' + rowid
        	                 + '" name="workflowMessage.espMaxMatchDelay_' + rowid
        	                 + '" class="ftr" size="2" value="' + streamMaxMatchDelay + '" onkeyup="changeButton()" disabled/>';
            cell_5_1a.innerHTML = matchDelay;
            cell_5_1a.style="text-align:center";
            cell_5_1a.noWrap=true;
        }


        //var cell_5_1a = newRow_1.insertCell(-1);
        //cell_5_1a.id = prefix + 'DescFullFill_' + rowid;
        //cell_5_1a.className ="ac";
        //cell_5_1a.noWrap=true;
        //cell_5_1a.colSpan=3;


        if( <%=partialCoverEnabled%> ){
	    var cell_5a = newRow_1.insertCell(-1);
        cell_5a.rowSpan = 2;
        cell_5a.className ="ac";
		cell_5a.innerHTML = '<input type="checkbox"  id="'+prefix+'PartialCover_'+rowid+'" '+ ('') +' name="workflowMessage.'+prefix+'PartialCover_'+rowid+'" onclick="changeButton(),validateESPCoverPercentage('+rowid+')" )">&nbsp;&nbsp;<input type="text" ' + ('') + ' id="'+prefix+'CoverPercentageVal_'+rowid+'" name="workflowMessage.'+prefix+'CoverPercentageVal_'+rowid+'" class="ftr" size="2" onblur="return validateESPCoverPercentage('+rowid+')" onkeyup="changeButton()">% </br><span id="'+prefix+'DescPartialCover_'+rowid+'" style="height: 1px;margin-top: 0px;text-align: center;visibility: hidden;"></span>';
		}

		var cell_6 = newRow_1.insertCell(-1);
        cell_6.rowSpan = 2;
        cell_6.className ="ac";
        cell_6.innerHTML = '<input type="checkbox"  id="'+prefix+'FullFill_'+rowid+'" '+ ('') +' name="workflowMessage.'+prefix+'FullFill_'+rowid+'" onclick="changeButton()" )">&nbsp;&nbsp;<input type="text" ' + ('') + ' id="'+prefix+'FullFillVal_'+rowid+'" name="workflowMessage.'+prefix+'FullFillVal_'+rowid+'" class="ftr" size="2" onblur="return validateESPFFPercentage(this, '+rowid+')" onkeyup="changeButton()">% </br><span id="'+prefix+'DescFullFill_'+rowid+'" style="height: 1px;margin-top: 0px;text-align: center;visibility: hidden;"></span>';

        var cell_7 = newRow_1.insertCell(-1);
        cell_7.rowSpan = 2;
        cell_7.className ="ac";
        cell_7.innerHTML = '<input type="checkbox"  id="'+prefix+'AutoCover_'+rowid+'" name="workflowMessage.'+prefix+'AutoCover_'+rowid+ '" onclick="changeButton()" )">';
        
        if(<%=isPriceCheckEnabled%>){
            var cell_8 = newRow_1.insertCell(-1);
            cell_8.rowSpan = 2;
            cell_8.className ="ac";
            cell_8.innerHTML = '<input type="checkbox"  id="'+prefix+'PriceCheck_'+rowid+'" '+ ('') +' name="workflowMessage.'+prefix+'PriceCheck_'+rowid+'" onclick="changeButton()" )"><span style="width: 10px;"></span>'
                  + '<input type="text" ' + ('') + ' id="'+prefix+'PriceCheckVal_'+rowid+'" name="workflowMessage.'+prefix+'PriceCheckVal_'+rowid+'" class="ftr" size="3" value="0" onblur="return validateESPPriceCheckValue(this)" onkeyup="changeButton()">&nbsp;&nbsp;'
                  + '<select class="ft" style="width:70" id="'+prefix+'PriceCheckTypeName_'+rowid+'"  name="workflowMessage.'+prefix+'PriceCheckTypeName_'+rowid+'" nowrap>'+priceCheckOptions+'</select>' + '&nbsp;&nbsp;'
        	
        }
        
        var newRow_2 = executionConditions.insertRow(-1);
        newRow_2.className = (lastRow % 2 ? "tl" : "tlc");


        var cell_5 = newRow_2.insertCell(-1);
        cell_5.id = prefix + 'Description_' + rowid;
        cell_5.className ="ac";
		cell_5.rowSpan = 1;
        cell_5.noWrap=true;


        window.document.getElementById("espCOUNTEXECUTIONCOND").value=rowsCollection.length -2;
        validateESPExecutionRuleCondition(rowid);
    }

    function addSalesDealerGroup(newRow, rowid, prefix ){
        var salesDealerOptions = "<option value='-1' > &nbsp; </option>";
        <%
        List<UserCounterpartyGroup> salesDealerGroups = SalesDealerGroupUtil.getSortedSalesDealerGroups(org);
        for (UserCounterpartyGroup salesDealerGroup : salesDealerGroups) {
          %>
            salesDealerOptions += "<option value='<%=salesDealerGroup.getObjectID()%>' > <%=salesDealerGroup.getShortName()%> </option>";
        <%
        }
        %>
        var new_cell = newRow.insertCell(-1);
        new_cell.rowSpan = 2;
        new_cell.className ="ac";
        new_cell.innerHTML = '<select id="'+prefix+'SalesDealerGroup_'+rowid+'" name="workflowMessage.'+prefix+'SalesDealerGroup_'+rowid+'" onchange="changeButton()" class="ft" style="width: 90px;overflow:hidden;">'+salesDealerOptions+'</select>';
    }

    function newRFSExecutionCondition(tbl) {
    	var executionConditions = window.document.getElementById(tbl);
        var rowsCollection  = executionConditions.rows;
        var lastRow = rowsCollection.length;
        var count= rowsCollection.length -2;

        var newRow_1 = executionConditions.insertRow(-1);
        newRow_1.className = (lastRow % 2 ? "tl" : "tlc");
        var rowid = 0;
        var sortOrder = 10;
        if (count != 0){
            var prevRow = rowsCollection[count];
            rowid = getAttrValueOr(prevRow, "rowId", 0)-0+1;
//            rowid = prevRow.rowId - 0  + 1;
            sortOrder = prevRow.cells[1].firstChild.value -0 + 10;
        }
        var prefix="rfs";
        
//        newRow_1.rowId = rowid;
        newRow_1.setAttribute('rowId', rowid);
        var cell_1 = newRow_1.insertCell(-1);
        cell_1.className ="ac";
        cell_1.rowSpan = 2;
        cell_1.innerHTML = '<input type="checkbox"  id="'+prefix+'ExecRuleCond_'+rowid+'" name="workflowMessage.'+prefix+'ExecRuleCond_'+rowid+'" >';
        cell_1.noWrap=true;

        var cell_2 = newRow_1.insertCell(-1);
        cell_2.rowSpan = 2;
        cell_2.innerHTML='<input type="text" id="'+prefix+'SortOrder_'+rowid+'" name="workflowMessage.'+prefix+'SortOrder_'+rowid+'" value="'+sortOrder+'"  size="2" class="ftr" >';
        cell_2.className ="ac";
        cell_2.noWrap=true;

		var tradeOptions="<option value='<%=ISCommonConstants.TRD_CLSF_SP%>' >FX Spot</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_OR%>' >FX Outright</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_SWAP%>' >FX Swap</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FWD%>'>FX Fwd Fwd</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXNDF%>'>FX NDF</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXFSR%>'>FX FSR</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXSSP%>'>FX SSP</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXNDF_SWAP%>'>FX NDF Swap</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXWindowFWD%>'>FX Window Fwd</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXPRORATAFWD%>'>FX Prorata Fwd</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_ALL%>' >All</option> ";
		var cell_3 = newRow_1.insertCell(-1);
        cell_3.rowSpan = 2;
        cell_3.innerHTML='<select  style="width: 100%" id="'+prefix+'TradeType_'+rowid+'" name="workflowMessage.'+prefix+'TradeType_'+rowid+'" class="ft" '+
                (' onchange="return validateRFSTradeType(this,\'' + rowid + '\')"')+'> '+tradeOptions+'</select>'
                + '<input type="hidden" id="'+prefix+'TradeType_hidden_'+rowid+'" value="ALL">';
        cell_3.className ="ac";
        cell_3.noWrap=true;

		var cell_4 = newRow_1.insertCell(-1);
        cell_4.innerHTML='<input type="text" id="'+prefix+'ExecNotLow_'+rowid+'" name="workflowMessage.'+prefix+'ExecNotLow_'+rowid+'" value=""  onblur="convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()" size="9" class="ftr" ></br><input type="text" id="'+prefix+'ExecNotHigh_'+rowid+'" name="workflowMessage.'+prefix+'ExecNotHigh_'+rowid+'" value="" onblur="convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()" size="9" class="ftr" >';
        cell_4.className ="ac";
		cell_4.rowSpan = 2;
        cell_4.noWrap=true;

		 var cell_5 = newRow_1.insertCell(-1);
         cell_5.innerHTML='<input type="text" id="'+prefix+'ExecLowNetAmount_'+rowid+'" name="workflowMessage.'+prefix+'ExecLowNetAmount_'+rowid+'" value=""  onblur="convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()" size="9" class="ft" ></br><input type="text" id="'+prefix+'ExecHighNetAmount_'+rowid+'" name="workflowMessage.'+prefix+'ExecHighNetAmount_'+rowid+'"'+('value=""')+'  onblur="convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()" size="9" class="ft" >';
         cell_5.className ="ac";
		 cell_5.rowSpan = 2;
         cell_5.noWrap=true;

		var cell_6 = newRow_1.insertCell(-1);
        cell_6.innerHTML='<input type="text" id="'+prefix+'ExecTmStrt_'+rowid+'" name="workflowMessage.'+prefix+'ExecTmStrt_'+rowid+'" value=""  onblur="changeButton()" size="6" class="ft" ><span style="width: 0px">&nbsp;</span><input type="text" id="'+prefix+'ExecTmEnd_'+rowid+'" name="workflowMessage.'+prefix+'ExecTmEnd_'+rowid+'" value=""   onblur="changeButton()" size="6" class="ft" ></br><select id="'+prefix+'TimeZoneID_'+rowid+'" name="workflowMessage.'+prefix+'TimeZoneID_'+rowid+'" onchange="changeButton()" class="ft"  style="width: 108px;overflow:hidden;">'+timeZoneOptions+'</select>';
        cell_6.className ="ac";
		cell_6.rowSpan = 2;
        cell_6.noWrap=true;

		var mcelem =  '<input type="radio" name="workflowMessage.'+prefix+'mc_'+rowid+'" value="0" checked=true class="mc" id="rfsMatchingCriteria.0_'+rowid+'">Default<br>';
        mcelem = mcelem + '<input type="radio" name="workflowMessage.'+prefix+'mc_'+rowid+'" value="1"  class="mc" id="rfsMatchingCriteria.1_'+rowid+'">Standard Tenor<br>';
        mcelem = mcelem + '<input type="radio" name="workflowMessage.'+prefix+'mc_'+rowid+'" value="2"  class="mc" id="rfsMatchingCriteria.2_'+rowid+'">Non Standard<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tenor<br>';
        
        var anyelem =  '<input type="radio" name="workflowMessage.'+prefix+'AnyLeg_'+rowid+'" value="true" >Any Leg<br>';
        anyelem = anyelem + '<input type="radio" name="workflowMessage.'+prefix+'AnyLeg_'+rowid+'" value="false" checked=true>All Legs<br>';
        
        var cell_7 = newRow_1.insertCell(-1);
        cell_7.innerHTML=anyelem;
        cell_7.style.textAlign="left";
        cell_7.style.paddingRight="10px";
        cell_7.noWrap=true;
        cell_7.rowSpan = 2;
        
        var cell_8 = newRow_1.insertCell(-1);
        cell_8.innerHTML=mcelem;
        cell_8.textAlign="left";
        cell_8.style.paddingRight="8px";
        cell_8.noWrap=true;
        cell_8.rowSpan = 2;


		var cell_9 = newRow_1.insertCell(-1);
        var curimin = contextPath + "/integral/core/framework/jsp/Calendar.jsp?hostField=workflowMessage."+prefix+"ExecTenMin_" +  rowid + "&allowPastTenor=true" ;
        var jurimin = "javascript:popupCalendar('"+curimin+"')";

		var curimax = contextPath + "/integral/core/framework/jsp/Calendar.jsp?hostField=workflowMessage."+prefix+"ExecTenMax_" +  rowid + "&allowPastTenor=true" ;
        var jurimax = "javascript:popupCalendar('"+curimax+"')";

        cell_9.innerHTML='<div style="width:90%"><div style="display:inline"><input onblur="changeButton()" style="width:60px;min-width50px;" type="text" id="'+prefix+'ExecTenMin_'+rowid+'" name="workflowMessage.'+prefix+'ExecTenMin_'+rowid+'" '+('value=""')+'   size="30" class="ft" ></div><div style="display:inline"><a id="cptyChooser" style="vertical-align: bottom;"><a href="'+jurimin+'" onclick="changeButton()"  class="mc'+rowid +'"  tabindex="-1"><img src="/admin/theme/images/calendar.gif" border="0"></a></div></div></br><div style="width:90%"><div style="display:inline"><input onblur="changeButton()" style="width:60px;min-width:50px;" type="text" id="'+prefix+'ExecTenMax_'+rowid+'" name="workflowMessage.'+prefix+'ExecTenMax_'+rowid+'" '+('value=""')+'   size="30" class="ft" ></div><div style="display:inline"><a id="cptyChooser" style="vertical-align: bottom;"><a href="'+jurimax+'" onclick="changeButton()" class="mc'+rowid +'" tabindex="-1"><img src="/admin/theme/images/calendar.gif" border="0"></a></div></div>';
        cell_9.className ="ac";
		cell_9.rowSpan = 2;
        cell_9.noWrap=true;

		 var cell_10 = newRow_1.insertCell(-1);
         cell_10.rowSpan = 2;
         cell_10.innerHTML='<input type="text" id="'+prefix+'PipsDeviation_'+rowid+'" name="workflowMessage.'+prefix+'PipsDeviation_'+rowid+'" value=""  onblur="validateMinimumSpread(this);changeButton()" size="6" class="ft" >';
         cell_10.className ="ac";
         cell_10.noWrap=true;

	 addSalesDealerGroup(newRow_1, rowid, prefix )

        var cell_11 = newRow_1.insertCell(-1);
        cell_11.rowSpan = 2;
        cell_11.innerHTML = '<span id="'+prefix+'RFS_'+rowid+'"><input type="checkbox" checked id="'+prefix+'IsRFSEnabled_'+rowid+'" name="workflowMessage.'+prefix+'IsRFSEnabled_'+rowid+ '" checked onclick="return validateRFSRFSCheckBox(this,\'' + rowid + '\')" )">RFS </span>';
        cell_11.className ="ac";
        cell_11.noWrap=true;

		var cell_12 = newRow_1.insertCell(-1);
        cell_12.innerHTML = '<span id="'+prefix+'SpMds_'+rowid+'"><input type="checkbox"  id="'+prefix+'IsSpotMDSEnabled_'+rowid+'" name="workflowMessage.'+prefix+'IsSpotMDSEnabled_'+rowid+ '" onclick="return validateRFSSpotMDSCheckBox(this,\'' + rowid + '\')" )">Spot & MDS</span>' +
        					'<span id="'+prefix+'SpSwap_'+rowid+'"><input type="checkbox"  id="'+prefix+'IsSpotSwapEnabled_'+rowid+'" name="workflowMessage.'+prefix+'IsSpotSwapEnabled_'+rowid+ '" onclick="return validateRFSSpotSwapCheckBox(this,\'' + rowid + '\')" )">Spot & Swap</span>' +
                			'<br> <select class="ft" id="'+prefix+'PricingType_'+rowid+'"  name="workflowMessage.'+prefix+'PricingType_'+rowid+'" disabled onchange="changeButton()" nowrap>'+execPriceOptions+'</select>' + '</br><span style="font-weight: bold">Inflate Price</span> <input type="checkbox"  id="'+prefix+'Inflate_'+rowid+'" name="workflowMessage.'+prefix+'Inflate_'+rowid +'" onclick="changeButton()" )">' + '<span id="'+prefix+'InflateValText_'+rowid+'"><input type="text"  id="'+prefix+'InflateVal_'+rowid+'" name="workflowMessage.'+prefix+'InflateVal_'+rowid+'" size="1" class="ftr" onblur="return validatePercentage(this)" onkeyup="changeButton()">%</span>';
        cell_12.className ="ac";
        cell_12.noWrap=true;
        cell_12.rowSpan = 2;


		var cell_13 = newRow_1.insertCell(-1);
		var dealerIntervention =  '</br>';
		if(<%=isDealerInterventionEnabled%>){
			dealerIntervention = '<span id="'+prefix+'DIText_'+rowid+'" title = "Dealer Intervention" style="padding-left: 10px;"><input type="checkbox" id="'+prefix+'DIChecked_'+rowid+'" name="workflowMessage.'+prefix+'DIChecked_'+rowid+ '" onclick="return validateRFSDICheckBox(this,\'' + rowid + '\')" )"> DI</span></br>';
		}
		
        cell_13.className ="ac";
        cell_13.innerHTML= '<select class="ft" style="width:85" id="'+prefix+'CoverExecutionMethod_'+rowid+'"  name="workflowMessage.'+prefix+'CoverExecutionMethod_'+rowid
                +'" onchange="validateRFSCoverExec(this,\'' + rowid + '\');updateBookVisibility(this,\'RFS\');"  nowrap>'+rfsCoverExecOptions+'</select>'
                + '<input type="hidden" id="'+prefix+'CoverExecutionMethod_hidden_'+rowid+'" value="NoCover">' + '&nbsp;&nbsp;&nbsp;&nbsp;'
                <% if(isRFSMultiBookPropertyEnabled){ %>
                    + '<select class="ft" id="'+prefix+'CoverExecutionMethod_'+rowid+'_Book"  name="workflowMessage.'+prefix+'CoverExecutionMethod_'+rowid
                    +'_Book" style="display:none;" nowrap>'+bookNameOptions+'</select>'
                <% } %>
                + '<input type="checkbox"  id="'+prefix+'NoPriceChecked_'+rowid+'" name="workflowMessage.'+prefix+'RNoPriceChecked_'+rowid+ '"  disabled onclick="return validateRFSNoPriceCheckBox(this,\'' + rowid + '\')" )">'
                + '<label id="'+prefix+'NoPriceCheckedLabel_'+rowid+'">NPC</label>' + '&nbsp;&nbsp'
				
                + '<span id="'+prefix+'FOKText_'+rowid+'" style="padding-left: 10px;"><input type="checkbox" id="'+prefix+'FOK_'+rowid+'" name="workflowMessage.'+prefix+'FOK_'+rowid+ '" onclick="return validateRFSFOKCheckBox(this,\'' + rowid + '\')" )"> FOK</span>'
                
                + dealerIntervention
                
                + '<span id="'+prefix+'Limit_'+rowid+'"><input type="radio" id="'+prefix+'TrdTypeLmt_'+rowid+'" name="workflowMessage.'+prefix+'TrdType_'+rowid+ '" checked onclick="validateRFSTradeTypeCombo(this,' + rowid + ')" )">Limit' + '&nbsp;&nbsp;</span>'

				+ '<span id="'+prefix+'Market_'+rowid+'"><input type="radio" id="'+prefix+'TrdTypeMkt_'+rowid+'" name="workflowMessage.'+prefix+'TrdType_'+rowid+ '" onclick="validateRFSTradeTypeCombo(this,' + rowid + ')" )">Market' + '&nbsp;&nbsp;</span>'

                + '<input type="text" id="'+prefix+'Range_'+rowid+'" name="workflowMessage.'+prefix+'Range_'+rowid+'" value="0" onblur="validateRFSRangeCheckBox(this,\'' + rowid + '\')" size="2 " class="ft" >' + '&nbsp;&nbsp;'
                + '<select class="ft" style="width:70" id="'+prefix+'RangeType_'+rowid+'"  name="workflowMessage.'+prefix+'RangeType_'+rowid+'" onchange="validateRFSCoverExec(this,\'' + rowid + '\')" nowrap>'+execRangeOptions+'</select>' + '&nbsp;&nbsp;';
        cell_13.rowSpan = 1;
        cell_13.noWrap=true;
        
        if(<%=isSpotAndSwapEnabled%>){
        	var cell_14 = newRow_1.insertCell(-1);
            cell_14.rowSpan = 2;
            cell_14.className ="ac";
            cell_14.innerHTML = '<select class="ft" style="width:70" id="'+prefix+'SpotAndSwapCoverExecutionMethod_'+rowid+'"  name="workflowMessage.'+prefix+'SpotAndSwapCoverExecutionMethod_'+rowid
            					+ '" onchange="validateSpotAndSwapCoverExec(this,\'' + rowid + '\')" nowrap>'+ rfsSpotAndSwapCoverExecOptions+'</select><span id="'+prefix+'SpotAndSwapCoverExecutionMethodNA_'+ rowid + '">N/A</span>';
        }
        
		var cell_15 = newRow_1.insertCell(-1);
        cell_15.rowSpan = 2;
        cell_15.className ="ac";
        cell_15.innerHTML = '<input type="checkbox"  id="'+prefix+'FullFill_'+rowid+'" '+ ('checked') +' name="workflowMessage.'+prefix+'FullFill_'+rowid+'" onclick="return validateRFSFullFillCheckBox(this,\'' + rowid + '\')">&nbsp;&nbsp;<span id="'+prefix+'FullFillValText_'+rowid+'"><input type="text" ' + ('value=100') + ' id="'+prefix+'FullFillVal_'+rowid+'" name="workflowMessage.'+prefix+'FullFillVal_'+rowid+'" class="ftr" size="1" onblur="return validatePercentage(this)" onkeyup="changeButton()">%</span>';


        var cell_16 = newRow_1.insertCell(-1);
        cell_16.rowSpan = 2;
        cell_16.className ="ac";
        cell_16.innerHTML = '<input type="checkbox"  id="'+prefix+'AutoCover_'+rowid+'" name="workflowMessage.'+prefix+'AutoCover_'+rowid+ '" onclick="changeButton()" )"><span id="'+prefix+'AutoCoverNA_'+rowid+'">N/A</span>';
       


       var newRow_2 = executionConditions.insertRow(-1);
       newRow_2.className = (lastRow % 2 ? "tl" : "tlc");


        var cell_13 = newRow_2.insertCell(-1);
        cell_13.id = prefix + 'Description_' + rowid;
        cell_13.className ="ac";
		cell_13.rowSpan = 1;
        cell_13.noWrap=true;

       /* var tradeOptions="<option value='<%=ISCommonConstants.TRD_CLSF_SP%>' >FX Spot</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_OR%>' >FX Outright</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_SWAP%>' >FX Swap</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FWD%>'>FX Fwd Fwd</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXNDF%>'>FX NDF</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXFSR%>'>FX FSR</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXSSP%>'>FX SSP</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_FXNDF_SWAP%>'>FX NDF Swap</option>" +
                           "<option value='<%=ISCommonConstants.TRD_CLSF_ALL%>' >All</option> ";
        var cell_15 = newRow_1.insertCell(-1);
        cell_15.rowSpan = 1;
        cell_15.innerHTML='<select  style="width: 100%" id="'+prefix+'TradeType_'+rowid+'" name="workflowMessage.'+prefix+'TradeType_'+rowid+'" class="ft" '+
                (' onchange="return validateRFSTradeType(this,\'' + rowid + '\')"')+'> '+tradeOptions+'</select>'
                + '<input type="hidden" id="'+prefix+'TradeType_hidden_'+rowid+'" value="ALL">';
        cell_15.className ="ac";
        cell_15.noWrap=true;*/


        window.document.getElementById("rfsCOUNTEXECUTIONCOND").value=rowsCollection.length -2;
        validatePercentage(cell_12.children[0]);
        cell_3.children[0].options[10].selected = true;

		validateRFSExecutionRuleCondition(rowid);
        $j('.mc').unbind("click");
        $j('.mc').click(function(elem){
        	tenorValidation(elem.target);
        });
    }

// todo 4.14 put in some common place
function getAttrValueOr( elem, attrName, d) {
    if( elem == undefined || elem == null ) return d;
    var attr = elem.attributes[attrName];
    if( attr == undefined || attr == null ) return d;
    return attr.value;
}
    function newAutoExecutionCondition(tbl,type) {
        var executionConditions = window.document.getElementById(tbl);
        var rowsCollection  = executionConditions.rows;
        var lastRow = rowsCollection.length;
        var count= rowsCollection.length -2;
        var newRow = executionConditions.insertRow(-1);
        newRow.className = (lastRow % 2 ? "tl" : "tlc");
        var rowid = 0;
        var sortOrder = 10;
        if (count != 0){
            var prevRow = rowsCollection[count +1];
            rowid = getAttrValueOr(prevRow, "rowId", 0)-0+1;
            sortOrder = prevRow.cells[1].firstChild.value -0 + 10;
        }
        var prefix="auto";

        newRow.setAttribute('rowId', rowid);
        var cell1 = newRow.insertCell(-1);
        cell1.className ="ac";
        cell1.innerHTML = '<input type="checkbox" id="'+prefix+'ExecRuleCond_'+rowid+'" name="workflowMessage.'+prefix+'ExecRuleCond_'+rowid+'" >';
        cell1.noWrap=true;

        var cell2 = newRow.insertCell(-1);
        cell2.innerHTML='<input type="text" id="'+prefix+'SortOrder_'+rowid+'" name="workflowMessage.'+prefix+'SortOrder_'+rowid+'" value="'+sortOrder+'"  size="2" class="ftr" >';
        cell2.className ="ac";
        cell2.noWrap=true;

        var cell3 = newRow.insertCell(-1);
        cell3.innerHTML= '<select class="ft" style="width:155" id="'+prefix+'ActionType_'+rowid+'"  name="workflowMessage.'+prefix+'ActionType_'+rowid+'">'+autoConditionTypesOptions+'</select>';
        cell3.className ="ac"
        cell3.noWrap=true;

		var cell4 = newRow.insertCell(-1);
        cell4.innerHTML='<input type="text" id="'+prefix+'ExecNotLow_'+rowid+'" name="workflowMessage.'+prefix+'ExecNotLow_'+rowid+'" value=""  onblur="convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()" size="9" class="ftr" >';
        cell4.className ="ac"
        cell4.noWrap=true;

        var cell5 = newRow.insertCell(-1);
        cell5.innerHTML='<input type="text" id="'+prefix+'ExecNotHigh_'+rowid+'" name="workflowMessage.'+prefix+'ExecNotHigh_'+rowid+'" value="" onblur="convertToNotionalFormatNumber(this);parseNotionalFormatNumber(this);changeButton()" size="9" class="ftr" >';
        cell5.className ="ac"
        cell5.noWrap=true;

        var cell6 = newRow.insertCell(-1);
        cell6.innerHTML= '<select  class="ft" style="width:155" id="'+prefix+'ExecutionMethod_'+rowid+'"  name="workflowMessage.'+prefix+'ExecutionMethod_'+rowid+'" onChange="handleAutoExecutionChange(this,' + rowid + ');changeButton()">'+autoOptions+'</select>';
        cell6.className ="ac"
        cell6.noWrap=true;

        var cell7 = newRow.insertCell(-1);
        cell7.innerHTML='<input type="text" id="'+prefix+'LimitTif_'+rowid+'" name="workflowMessage.'+prefix+'LimitTif_'+rowid+'" value="" onblur="userDecimalFormat.format(this,0);changeButton()" size="9" class="ftr" disabled>';
        cell7.className ="ac"
        cell7.noWrap=true;

        var cell8 = newRow.insertCell(-1);
        cell8.innerHTML='<input type="text" id="'+prefix+'LimitRange_'+rowid+'" name="workflowMessage.'+prefix+'LimitRange_'+rowid+'" value="" onblur="validateMarketRange(this);changeButton()" size="9" class="ftr" disabled>';
        cell8.className ="ac"
        cell8.noWrap=true;

        var cell9 = newRow.insertCell(-1);
        cell9.innerHTML='<input type="text" id="'+prefix+'TOBRange_'+rowid+'" name="workflowMessage.'+prefix+'TOBRange_'+rowid+'" value="" onblur="validateMarketRange(this, -1);changeButton()" size="9" class="ftr">';
        cell9.className ="ac"
        cell9.noWrap=true;



        window.document.getElementById("autoCOUNTEXECUTIONCOND").value=rowsCollection.length -2;
    }


    function deleteExecutionCondition(tbl,type) {
    	var startIndex = 2;
        var executionConditions = window.document.getElementById(tbl);
        var delCount=0;
        var rowsCollection  = executionConditions.rows;
        var lastRow = rowsCollection.length;
        for(i=startIndex; i<lastRow; i++)
        {
            var chk = rowsCollection[i-delCount].cells[0].firstChild;
            if((chk != undefined) && (chk != null) && (chk.name != null)
                    && (chk.name != undefined) &&(chk.name.indexOf("ExecRuleCond_") != -1) && chk.checked)
            {
                executionConditions.deleteRow(i-delCount);
                delCount++;

                if (type == 'ESP' || type == 'RFS') {
                    executionConditions.deleteRow((i+1)-delCount);
                    delCount++;
                }
            }
        }
    }

    function getNoFullFillOptions(){
    <%--var noFullFillOptions = new Array(); noFullFillOptions[0] = document.createElement("OPTION");noFullFillOptions[0].value = "<%=PricingStrategy.RFS.name()%>";noFullFillOptions[0].text = "<%=PricingStrategy.RFS.getDescription()%>";--%>
        var noFullFillOptions = new Array(); noFullFillOptions[0] = new Option("<%=PricingStrategy.RFS.getDescription()%>", "<%=PricingStrategy.RFS.name()%>", false, false);
        return noFullFillOptions;
    }


    function getFullfillOptions(tradeType){
        var fullFillOptions = new Array();
    <%
    PricingStrategy[] rfsPricingStrategies = PricingStrategy.getRfsPricngStrategies();
    for(int i = 0; i < rfsPricingStrategies.length;i++) {
      PricingStrategy rfsPricingStrategy = rfsPricingStrategies[i];
    %>
        <%--if (<%= rfsPricingStrategy.isDoRFSSubscription()%> || ("<%=ISCommonConstants.TRD_CLSF_FXNDF%>" != tradeType.value)) {--%>
            fullFillOptions[fullFillOptions.length] = new Option("<%=rfsPricingStrategy.getDescription()%>", "<%=rfsPricingStrategy.name()%>", false, false );
//        }
    <%
    }
    %>
        return fullFillOptions;
    }

    function getAllRFSExecutionMethodOptions(){
        var rfsExecutionMethodOptions = new Array();
        <%
        ExecutionMethod[] executionMethods = ExecutionMethod.rfsValues();
        for(int i = 0; i < executionMethods.length; i++) {
          ExecutionMethod executionMethod = executionMethods[i];
        %>
            rfsExecutionMethodOptions[<%=i%>] = new Option("<%=executionMethod.getDescription()%>", "<%=executionMethod.name()%>", false, false );
        <%
        }
        %>
        return rfsExecutionMethodOptions;
    }

    function getOnlyRFSExecutionMethodOptions(){
        var onlyRfsExecutionMethodOptions = new Array();
        <%
        executionMethods = ExecutionMethod.onlyRfsValues();
        for(int i = 0; i < executionMethods.length; i++) {
          ExecutionMethod executionMethod = executionMethods[i];
        %>
            onlyRfsExecutionMethodOptions[<%=i%>] = new Option("<%=executionMethod.getDescription()%>", "<%=executionMethod.name()%>", false, false );
        <%
        }
        %>
        return onlyRfsExecutionMethodOptions;
    }

    function setOptionTo(obj, val) {
        var ops = obj.options;
        for (var i = 0; i < obj.length; i++) {
            var op = obj[i];
            if (op.value == val) {
                op.selected = true;
            }
        }
    }

    function handleAutoExecutionChange(obj, count) {
        var limitTifComp = document.getElementById("autoLimitTif_" + count);
        var limitRangeComp = document.getElementById("autoLimitRange_" + count);
        var tobRange = document.getElementById("autoTOBRange_" + count);

        if (obj.value == "<%=ExecutionMethod.LimitMarket.name()%>") {
            limitTifComp.disabled = false;
            limitRangeComp.disabled = false;
//            tobRange.disabled = true;
        } else if (obj.value == "<%=ExecutionMethod.TopOfTheBook.name()%>") {
            limitTifComp.disabled = true;
            limitRangeComp.disabled = true;
//            tobRange.disabled = false;
        }
    }

    function updateOptions(oOptions, oSelect){
		while(oSelect.options.length != 0){
			oSelect.options.remove(0);
		}
        for (var i = 0; i < oOptions.length ; i++){
			oSelect.add(oOptions[i]);
		}
	}

    /*function validateRFSPricing(spotMDSEnabled, type, rowId) {
        document.getElementById(type + 'PricingType_' + rowId).disabled = !spotMDSEnabled.checked;
    }*/
    function validateESPCoverExec(component, rowId) {
        // validate excel pricing source compatability
        if($("pricingSource_MDS") != null && $("pricingSource_MDS").checked && component.value != "NoCover") {
			var ri = optionIndexOf( component, component.prevVal );
			ri = (ri == null) ? 1 : ri;
            component.selectedIndex = ri;
			return;
        }

        validateESPExecutionRuleCondition(rowId);
        changeButton();
		component.prevVal = component.options[component.selectedIndex].value; // so we can restore it later if need be
        return true;
    }

    function validateESPNoPriceCheckBox(component, rowId) {
        validateESPExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateESPFOKCheckBox(component, rowId) {
        validateESPExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateESPVWAPCheckBox(component, rowId) {
        validateESPExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateESPRangeCheckBox(component, rowId) {
        validateESPExecutionRuleCondition(rowId);
        changeButton();
        validateRange(component, "esp", rowId);
        return true;
    }

    function validateESPTradeTypeCombo(component, rowId) {
        validateESPExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateESPRange(component, rowId) {
        validateESPExecutionRuleCondition(rowId);
        changeButton();
        validateMarketRange(component);
        return true;
    }

    function numberOfPricingsChecked( type, rowId ) {
        var n = 0;
        if( $(type + 'IsSpotMDSEnabled_' + rowId).checked ) n += 1;
        if( $(type + 'IsRFSEnabled_' + rowId).checked ) n += 1;
        if( $(type + 'IsSpotSwapEnabled_' + rowId).checked ) n += 1;
        return n;
    }
    function validateRFSRFSCheckBox(component, rowId) {
        if( numberOfPricingsChecked( "rfs", rowId ) == 0 ) {
            component.checked = true;
            return;
        }
        
        var tradeType = $('rfs' + 'TradeType_' + rowId).value;
    	if(component.checked && (tradeType == '<%=ISCommonConstants.TRD_CLSF_OR%>' ||  tradeType == '<%=ISCommonConstants.TRD_CLSF_SWAP%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FWD%>')){
    		$('rfs' + 'IsSpotSwapEnabled_' + rowId).checked =false;
    	}
    	
        // mutex RFS and ESPMDS for FSR
        if( $("rfs" + "TradeType_" + rowId).value == '<%=ISCommonConstants.TRD_CLSF_FXFSR%>' && component.checked ) {
            $("rfs" + 'IsSpotMDSEnabled_' + rowId).checked = false;
        }
        validateRFSExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateRFSSpotMDSCheckBox(component, rowId) {
    	if(numberOfPricingsChecked( "rfs", rowId ) == 0 ) {
            component.checked = true;
            return;
        }
    	
    	var tradeType = $('rfs' + 'TradeType_' + rowId).value;
    	if(component.checked && (tradeType == '<%=ISCommonConstants.TRD_CLSF_FXSSP%>' || 
    							 tradeType == '<%=ISCommonConstants.TRD_CLSF_OR%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXWindowFWD%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXPRORATAFWD%>' ||
    							 tradeType == '<%=ISCommonConstants.TRD_CLSF_SWAP%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FWD%>')){
    		$('rfs' + 'IsSpotSwapEnabled_' + rowId).checked =false;
    	}
    	
        // mutex RFS and ESPMDS for FSR
        if( $("rfs" + "TradeType_" + rowId).value == '<%=ISCommonConstants.TRD_CLSF_FXFSR%>' && component.checked ) {
            $("rfs" + 'IsRFSEnabled_' + rowId).checked = false;
        }
        // ensure NoCover and PriceAtSize if source from excel is checked
        if( component.checked && $("pricingSource_MDS") != null && $("pricingSource_MDS").checked ) {
            if( badCover( "rfs", rowId ) || badRFSPricingType( "rfs", rowId ) )
                component.checked = false;
        }

        validateRFSExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }
    
    
    function validateRFSSpotSwapCheckBox(component, rowId){
    	if(numberOfPricingsChecked( "rfs", rowId ) == 0 ) {
            component.checked = true;
            return;
        }
    	
    	var tradeType = $('rfs' + 'TradeType_' + rowId).value;
    	if(component.checked){
    		if(tradeType == '<%=ISCommonConstants.TRD_CLSF_FXSSP%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXWindowFWD%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXPRORATAFWD%>'){
    			$('rfs' + 'IsSpotMDSEnabled_' + rowId).checked =false;
    		}else if(tradeType == '<%=ISCommonConstants.TRD_CLSF_OR%>'  || tradeType == '<%=ISCommonConstants.TRD_CLSF_SWAP%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FWD%>'){
    			$('rfs' + 'IsSpotMDSEnabled_' + rowId).checked =false;
    			$('rfs' + 'IsRFSEnabled_' + rowId).checked = false;
    		}
    	}
    	
    	validateRFSExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateRFSPricingType(component, rowId) {
        // validate excel pricing source compatability
        // it must be in a valid state for AS_MDS to be checked => we know to put back PriceAtSize
        if( $("pricingSource_MDS").checked && component.value != "<%=ExecutionPricingType.PriceAtSize.getName()%>" ) {
            component.selectedIndex = 0; // todo 4.10 njb undo the hack and set P@S properly
        }

        changeButton();
        return true;
    }

    function validateRFSFullFillCheckBox(component, rowId) {
        validateRFSExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

	function optionIndexOf( component, val ) {
		for( var i=0; i<component.options.length; i++ ) if( component.options[i].value == val ) return i;
        return null;
	}
    function validateRFSCoverExec(component, rowId) {
        // validate excel pricing source compatability
        // it must be in a valid state for AS_MDS to be checked => we know to put back NoCover
        if( $("pricingSource_MDS").checked && component.value == "Cover") {
			var ri = optionIndexOf( component, component.prevVal );
			ri = (ri == null) ? 1 : ri;
            component.selectedIndex = ri;
			return;
        }

        validateRFSExecutionRuleCondition(rowId);
        changeButton();
		component.prevVal = component.options[component.selectedIndex].value;
        return true;
    }
    
    function validateSpotAndSwapCoverExec(component, rowId) {
        validateRFSExecutionRuleCondition(rowId);
        changeButton();
		component.prevVal = component.options[component.selectedIndex].value;
        return true;
    }

    function validateRFSNoPriceCheckBox(component, rowId) {
        validateRFSExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateRFSFOKCheckBox(component, rowId) {
        validateRFSExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }
    
    function validateRFSDICheckBox(component, rowId) {
        validateRFSExecutionRuleCondition(rowId);
        if(!$('rfs' + 'DIChecked_' + rowId).checked){
        	var sortOrder = $('rfs' + 'SortOrder_' + rowId).value;
        	alert('DI setting removed from RFS Execution rule with sort order ' +  sortOrder + '. Please review the cover execution settings.');
    	}
        changeButton();
        return true;
    }

    function validateRFSTradeType(component, rowId) {
    	validateRFSExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateRFSRangeCheckBox(component, rowId) {
        validateRFSExecutionRuleCondition(rowId);
        changeButton();
        validateRange(component, "rfs", rowId);
        return true;
    }

    function validateRFSTradeTypeCombo(component, rowId) {
        validateRFSExecutionRuleCondition(rowId);
        changeButton();
        return true;
    }

    function validateRFSRange(component, rowId) {
        validateRFSExecutionRuleCondition(rowId);
        changeButton();
        validateMarketRange(component);
        return true;
    }

    <% // set elem to checked and return if new check state is different = ^isChanged %>
    function alertChanged( rowId, changedMessage ) {
        if( changedMessage ) alert("RFS Execution Rule " + (1.0 + new Number(rowId)) + " validation: " + changedMessage);
        <% // todo 4.10 maybe use sortOrder as the row id? %>
    }
    
    function setElemChecked( elem, checked, rowId, changedMessage ) {
        var oldChecked = elem.checked;
        elem.checked = checked;
        var changed = oldChecked != checked;
        if( changed ) alertChanged( rowId, changedMessage );
        return changed;
    }
    function setIsSpotMDSEnabled( enabled, type, rowId ) {
        $(type + 'IsSpotMDSEnabled_' + rowId).disabled = !enabled;
        $(type + 'SpMds_' + rowId).disabled = !enabled;
        $(type + 'PricingType_' + rowId).disabled = !enabled;
    }
    function setIsSpotMDSEnabledChecked( enabled, checked, type, rowId, changedMessage ) {
        setIsSpotMDSEnabled( enabled, type, rowId );
        var elem = $(type + 'IsSpotMDSEnabled_' + rowId);
        return setElemChecked( elem, checked, rowId, changedMessage );
    }
    function setIsRFSEnabledChecked( enabled, checked, type, rowId, changedMessage ) {
        var elem = $(type + 'IsRFSEnabled_' + rowId);
        $(type + 'IsRFSEnabled_' + rowId).disabled = !enabled;
        $(type + 'RFS_' + rowId).disabled = !enabled;
        return setElemChecked( elem, checked, rowId, changedMessage );
    }
    function setIsSpotSwapEnabledChecked( enabled, checked, type, rowId, changedMessage ) {
    	$(type + 'IsSpotMDSEnabled_' + rowId).disabled = !enabled;
        $(type + 'IsRFSEnabled_' + rowId).disabled = !enabled;
        $(type + 'RFS_' + rowId).disabled = !enabled;
        var elem = $(type + 'IsSpotMDSEnabled_' + rowId);
        if(numberOfPricingsChecked( "rfs", rowId ) == 0 ) {
        	elem.checked = true;
        }
        setElemChecked( elem, elem.checked, rowId, changedMessage );
        elem = $(type + 'IsSpotSwapEnabled_' + rowId);
        return setElemChecked( elem, checked, rowId, changedMessage );
    }
    function setInflateEnabledChecked( enabled, checked, type, rowId, changedMessage ) {
        var elem = $(type + 'Inflate_' + rowId);
        setInflateEnabled( enabled, type, rowId );
        return setElemChecked( elem, checked, rowId, changedMessage );
    }
    function setInflateEnabled( enabled, type, rowId ) {
        $(type + 'Inflate_' + rowId).disabled = !enabled;
        $(type + 'InflateVal_' + rowId).disabled = !enabled;
        $(type + 'InflateValText_' + rowId).disabled = !enabled;
    }
    function setFullFillEnabledChecked( enabled, checked, type, rowId, changedMessage ) {
        var elem = $(type + 'FullFill_' + rowId);
        setFullFillEnabled( enabled, type, rowId );
        return setElemChecked( elem, checked, rowId, changedMessage );
    }
    function setFullFillEnabled( enabled, type, rowId ) {
        $(type + 'FullFill_' + rowId).disabled = !enabled;
        $(type + 'FullFillVal_' + rowId).disabled = !enabled;
        $(type + 'FullFillValText_' + rowId).disabled = !enabled;
    }
    function hideShowSpotAndSwapChkBox(type, rowId, show){
    	var spotAndSwapElem = $(type + 'IsSpotSwapEnabled_' + rowId);
        var spotAndSwapLabel = $(type + 'SpSwap_' + rowId);
        if(show){
        	spotAndSwapElem.show();
            spotAndSwapLabel.show();
        }else{
        	spotAndSwapElem.hide();
            spotAndSwapLabel.hide();
        }
    }
    
    function showSpotAndSwapCoverExecutionMethod(type, rowId, showCoverExecMethod, showNA ){
    	var spotAndSwapCoverExecMethodElem = $(type + 'SpotAndSwapCoverExecutionMethod_' + rowId);
    	var spotAndSwapCoverExecMethodNAElem = $(type + 'SpotAndSwapCoverExecutionMethodNA_' + rowId);
    	if(showCoverExecMethod){
    		spotAndSwapCoverExecMethodElem.show();
    		spotAndSwapCoverExecMethodNAElem.hide();
    	}else{
    		spotAndSwapCoverExecMethodElem.hide();
    		spotAndSwapCoverExecMethodNAElem.show();
    	}
    	
    	if(!showCoverExecMethod && !showNA){
    		spotAndSwapCoverExecMethodElem.hide();
    		spotAndSwapCoverExecMethodNAElem.hide();
    	}
        
    }

// todo 4.10 njb if you select RFS Cover the desc says partial fills not allowed => maybe auto set FF checked & 100% (otoh maybe workflow enforces...)
    function validateRFSExecutionRuleCondition(rowId, alertChanges) {
//            var alertChanges = true; // for testing uncomment to force change alerts on post window load user driven validations
            var changed = false;
            var type = "rfs";
            
            // Initialize; enable everything
            $(type + 'Inflate_' + rowId).disabled = false;
            $(type + 'InflateVal_' + rowId).disabled = false;
            if ( $(type + 'InflateValText_' + rowId)) $(type + 'InflateValText_' + rowId).disabled = false;
            $(type + 'IsRFSEnabled_' + rowId).disabled = false;
            $(type + 'IsSpotMDSEnabled_' + rowId).disabled = false;
            $(type + 'IsSpotSwapEnabled_' + rowId).disabled = false;
            $(type + 'PricingType_' + rowId).disabled = false;
            $(type + "CoverExecutionMethod_" + rowId).firstChild.disabled = false;
            $(type + "FOK_" + rowId).disabled = false;
            $(type + 'FOKText_' + rowId).disabled = false;
            $(type + 'FullFill_' + rowId).disabled = false;
            $(type + 'FullFillVal_' + rowId).disabled = false;
            $(type + 'FullFillValText_' + rowId).disabled = false;
            if( $(type + 'AutoCover_' + rowId) != null){            
            	$(type + 'AutoCover_' + rowId).disabled = false;
            }
            
            $(type + 'ExecLowNetAmount_' + rowId).disabled = false;
            $(type + 'ExecHighNetAmount_' + rowId).disabled = false;
            $(type + 'SpMds_' + rowId).disabled = false;
            $(type + 'RFS_' + rowId).disabled = false;
            
            hideShowSpotAndSwapChkBox(type, rowId, false);
            if(<%=isSpotAndSwapEnabled%>){
            	showSpotAndSwapCoverExecutionMethod(type, rowId, false, true);
            }
            var spotAndSwapElem = $(type + 'IsSpotSwapEnabled_' + rowId);
            var spotAndMdsElem = $(type + 'IsSpotMDSEnabled_' + rowId);
            var isSpotAndMdsChecked = spotAndMdsElem.checked || (<%=isSpotAndSwapEnabled%> && spotAndSwapElem.checked);
            
            var isDIChecked = $(type + 'DIChecked_' + rowId) != null && $(type + 'DIChecked_' + rowId).checked;
            if(<%=isDealerInterventionEnabled%>){
        		showDealerInterventionCheckbox(type, rowId, true);
            }
            
            // trade type implies...
            var tradeType =  $(type + "TradeType_" + rowId).value;

            if (tradeType == '<%=ISCommonConstants.TRD_CLSF_FXNDF%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXNDF_SWAP%>') {
// 59384 Allow ESPMDS Pricing of NDF
//                changed = setIsSpotMDSEnabledChecked( false, false, type, rowId, alertChanges ? "Spot&MDS not supported by <%=ISCommonConstants.TRD_CLSF_FXNDF%>, deselecting.":null ) || changed; // avoid || quick eval shortcut
//                changed = setIsRFSEnabledChecked( true, true, type, rowId, alertChanges ? "Spot&MDS not supported by <%=ISCommonConstants.TRD_CLSF_FXNDF%>, selecting RFS.":null ) || changed;
            	if(<%=isSpotAndSwapForNDFAndSWEnabled%>){
            		hideShowSpotAndSwapChkBox(type, rowId, true);
            		var coverElem = $( type + "CoverExecutionMethod_" + rowId).value;
	            	if(spotAndSwapElem.checked && coverElem != '<%=CoverExecutionMethod.Manual.name()%>'){
	        			showSpotAndSwapCoverExecutionMethod(type, rowId, true, false);
	        		}else{
	        			showSpotAndSwapCoverExecutionMethod(type, rowId, false, true);
	        		}
	            	var rfsElem = $(type + 'IsRFSEnabled_' + rowId);
	            	changed = setIsRFSEnabledChecked( true, rfsElem.checked, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_FXNDF%>, deselecting.":null ) || changed;
	            	changed = setIsSpotSwapEnabledChecked( true, spotAndSwapElem.checked, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_FXNDF%>, selecting Spot&Swap.":null ) || changed;
	            }
            } else if (false && tradeType == '<%=ISCommonConstants.TRD_CLSF_SP%>') { // todo 4.10 njb confirm with Kiran & Dig that it should be removed
                $(type + 'PipsDeviation_' + rowId).disabled = true;
                $(type + 'ExecTmStrt_' + rowId).disabled = true;
                $(type + 'ExecTmEnd_' + rowId).disabled = true;

            } else if (tradeType == '<%=ISCommonConstants.TRD_CLSF_FXFSR%>') {
                if( numberOfPricingsChecked( "rfs", rowId ) == 2 ) 
                    changed = setIsRFSEnabledChecked( false, false, type, rowId, alertChanges ? "MDS & RFS not supported by <%=ISCommonConstants.TRD_CLSF_FXFSR%>, deslecting RFS.":null ) || changed;
                $(type + "PricingType_" + rowId).disabled = true;

                // for MDS no Cover or Price Check
                if( !$(type + 'IsRFSEnabled_' + rowId).checked ) {
                    var coverElem = $( type + "CoverExecutionMethod_" + rowId);
                    coverElem.firstChild.disabled = true;
                    if( coverElem.selectedIndex == 0 ) {
                        coverElem.selectedIndex = 1;
                        alertChanged( rowId, alertChanges ? "Cover of MDS not supported by <%=ISCommonConstants.TRD_CLSF_FXFSR%>, selecting NoCover.":null );
                        changed = true;
                    }
                    if( coverElem.selectedIndex == 1 ) {
                        var npcElem = $(type + 'NoPriceChecked_' + rowId);
                        npcElem.disabled = true;
                        changed = setElemChecked( npcElem, true, rowId, alertChanges ? "Price Check of MDS not supported by <%=ISCommonConstants.TRD_CLSF_FXFSR%>, selecting No Price Check.":null  ) || changed;
                    }
                }

                // auto cover is to auto cover a risk position that might have opened up NA for FSR
                if($(type + 'AutoCover_' + rowId) != null){
                	var autoCoverElem = $(type + 'AutoCover_' + rowId);
                    autoCoverElem.disabled = true;
                    changed = setElemChecked( autoCoverElem, false, rowId, alertChanges ? "AutoCover not supported by <%=ISCommonConstants.TRD_CLSF_FXFSR%>, deslecting AutoCover." : null ) || changed;                	
                }
                
                // FSR are always even swaps
                $(type + 'ExecLowNetAmount_' + rowId).disabled = true;
                $(type + 'ExecHighNetAmount_' + rowId).disabled = true;

            } else if ( tradeType == '<%=ISCommonConstants.TRD_CLSF_FXSSP%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXWindowFWD%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXPRORATAFWD%>') {
            	// In case of SSP, DI checkbox should be disabled and hide
            	if(<%=isDealerInterventionEnabled%> && tradeType == '<%=ISCommonConstants.TRD_CLSF_FXSSP%>'){
            		showDealerInterventionCheckbox(type, rowId, false);
                }
            	if((<%=isSpotAndSwapForSSPEnabled%> && tradeType == '<%=ISCommonConstants.TRD_CLSF_FXSSP%>') || 
            		(<%=isSpotAndSwapForOTEnabled%> && ( tradeType == '<%=ISCommonConstants.TRD_CLSF_FXWindowFWD%>' ) ) ){
            		hideShowSpotAndSwapChkBox(type, rowId, true);
            		var coverElem = $( type + "CoverExecutionMethod_" + rowId).value;
            		if(spotAndSwapElem.checked && coverElem != '<%=CoverExecutionMethod.Manual.name()%>'){
            			showSpotAndSwapCoverExecutionMethod(type, rowId, true, false);
            		}else{
            			showSpotAndSwapCoverExecutionMethod(type, rowId, false, true);
            		}
            		changed = setIsRFSEnabledChecked( false, false, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_FXSSP%>, deselecting.":null ) || changed;
                	changed = setIsSpotSwapEnabledChecked( true, spotAndSwapElem.checked, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_FXSSP%>, selecting Spot&Swap.":null ) || changed;
                }else{
                	changed = setIsRFSEnabledChecked( false, false, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_FXSSP%>, deselecting.":null ) || changed;
                	changed = setIsSpotMDSEnabledChecked( true, true, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_FXSSP%>, selecting Spot&MDS.":null ) || changed;
                }
            } else if (tradeType == '<%=ISCommonConstants.TRD_CLSF_OR%>') {
            	if(<%=isSpotAndSwapForOTEnabled%>){
            		hideShowSpotAndSwapChkBox(type, rowId, true);
            		var coverElem = $( type + "CoverExecutionMethod_" + rowId).value;
	            	if(spotAndSwapElem.checked && coverElem != '<%=CoverExecutionMethod.Manual.name()%>'){
	        			showSpotAndSwapCoverExecutionMethod(type, rowId, true, false);
	        		}else{
	        			showSpotAndSwapCoverExecutionMethod(type, rowId, false, true);
	        		}
	            	var rfsElem = $(type + 'IsRFSEnabled_' + rowId);
	            	changed = setIsRFSEnabledChecked( true, rfsElem.checked, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_OR%>, deselecting.":null ) || changed;
	            	changed = setIsSpotSwapEnabledChecked( true, spotAndSwapElem.checked, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_OR%>, selecting Spot&Swap.":null ) || changed;
	            }
            } else if (tradeType == '<%=ISCommonConstants.TRD_CLSF_SWAP%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FWD%>') {
                if(<%=isSpotAndSwapForSWEnabled%>){
                    hideShowSpotAndSwapChkBox(type, rowId, true);
                    var coverElem = $( type + "CoverExecutionMethod_" + rowId).value;
                    if(spotAndSwapElem.checked && coverElem != '<%=CoverExecutionMethod.Manual.name()%>'){
                        showSpotAndSwapCoverExecutionMethod(type, rowId, true, false);
                    }else{
                        showSpotAndSwapCoverExecutionMethod(type, rowId, false, true);
                    }
                    var rfsElem = $(type + 'IsRFSEnabled_' + rowId);
                    changed = setIsRFSEnabledChecked( true, rfsElem.checked, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_OR%>, deselecting.":null ) || changed;
                    changed = setIsSpotSwapEnabledChecked( true, spotAndSwapElem.checked, type, rowId, alertChanges ? "RFS not supported by <%=ISCommonConstants.TRD_CLSF_OR%>, selecting Spot&Swap.":null ) || changed;
                }
            }
            if( false && numberOfPricingsChecked( "rfs", rowId ) == 0 ) // false && => be conservative, the user has to choose
                changed = setIsSpotMDSEnabledChecked( true, true, type, rowId, alertChanges ? "A Pricing must be selected, selecting Spot&MDS.":null ) || changed; // 'safe' fall back

            validateRFSPricingExecutionRule(rowId,tradeType);

			// FullFill controlls fill Broker to Customer FF => always FF cust regardless of if covered all
            // see previous version for more convoluted and incorrect version
            if(   isSpotAndMdsChecked  ) {
                changed = setFullFillEnabledChecked( true, true, type, rowId, alertChanges ? "Selecting FullFill as Spot & MDS is checked.":null ) || changed;
            } else if( false && $(type + 'IsRFSEnabled_' + rowId).checked ) { // todo checking applicability
                changed = setFullFillEnabledChecked( true, true, type, rowId, alertChanges ? "Selecting Full Fill for RFS.":null ) || changed;
            }

            // Inflate if ESP subscriptioin is made; i.e not FSR
            if( isSpotAndMdsChecked && tradeType != '<%=ISCommonConstants.TRD_CLSF_FXFSR%>' ) {
                setInflateEnabled( true, type, rowId );
            } else {
                changed = setInflateEnabledChecked( false, false, type, rowId, alertChanges ? "Inflate is not supported, deselecting.":null ) || changed;
            }
            
            // copied out of tiers.js ( fix up any enable disablments) and tweaked to change selection also
            if( isBARFSESPSpreading ) {
                var quoteAggregators = document.getElementById('quoteAggregator');
                if (quoteAggregators != null) {
                    quoteAggregator = quoteAggregators.options[quoteAggregators.selectedIndex].value;
                    if (quoteAggregator != null) {
                        var isESPAggMultiTier = multiTier.containsKey( quoteAggregator );
                        var sel;
                        for( var i=0; sel = $("rfsPricingType_"+i); i++ ) { // iterate the RFS exec rules
                            var espSpreadable = isRFSESPSpreadable("rfs", i);
                            sel.options[0].disabled = espSpreadable && !isESPAggMultiTier;
                            sel.options[1].disabled = espSpreadable && !isESPAggMultiTier;
                            sel.options[2].disabled = espSpreadable && isESPAggMultiTier;
                            if( sel.options[ sel.selectedIndex ].disabled ) {
                                for( var i=0; i<sel.options.length; i++ ) // select the first not disabled option
                                if( !sel.options[i].disabled ) {
                                    sel.selectedIndex = i;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
			
			var autoCoverElem = $(type + 'AutoCover_' + rowId); 
			var NAElem = $(type + 'AutoCoverNA_' + rowId);
			var fokChkBx = $(type + 'FOK_' + rowId);
            //If RFS is enabled and SPOT & MDS is not enabled, uncheck and disable Auto Cover
            if($(type + 'IsRFSEnabled_' + rowId).checked && !isSpotAndMdsChecked ){            	  
                  autoCoverElem.hide();
                  fokChkBx.hide();
                  NAElem.show();
            } else {
                  autoCoverElem.show();
                  fokChkBx.show();
                  NAElem.hide();
            }
            if(<%=isDealerInterventionEnabled%>){
            	validateDealerInterventionChkBox(type, rowId);
            }
            
            var isOtAndSwapTradeType = tradeType == '<%=ISCommonConstants.TRD_CLSF_OR%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXSSP%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXWindowFWD%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXPRORATAFWD%>' ||  tradeType == '<%=ISCommonConstants.TRD_CLSF_SWAP%>' ||  tradeType == '<%=ISCommonConstants.TRD_CLSF_FWD%>';
            if(<%=!isSpotAndSwapForOTEnabled%> && (tradeType == '<%=ISCommonConstants.TRD_CLSF_OR%>'  || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXWindowFWD%>' || tradeType == '<%=ISCommonConstants.TRD_CLSF_FXPRORATAFWD%>' )){
        		if(spotAndSwapElem.checked && !spotAndMdsElem.checked){
            		spotAndMdsElem.checked = true;
            	}
            	spotAndSwapElem.checked = false;
        	}else if(<%=!isSpotAndSwapForSSPEnabled%> && tradeType == '<%=ISCommonConstants.TRD_CLSF_FXSSP%>'){
        		if(spotAndSwapElem.checked && !spotAndMdsElem.checked){
            		spotAndMdsElem.checked = true;
            	}
            	spotAndSwapElem.checked = false;
        	}else if(<%=!isSpotAndSwapForSWEnabled%> &&  (tradeType == '<%=ISCommonConstants.TRD_CLSF_SWAP%>' ||  tradeType == '<%=ISCommonConstants.TRD_CLSF_FWD%>')){
                    if(spotAndSwapElem.checked && !spotAndMdsElem.checked){
                        spotAndMdsElem.checked = true;
                    }
                    spotAndSwapElem.checked = false;
            }
        	else if(<%=isSpotAndSwapForOTEnabled%> && <%=isSpotAndSwapForSSPEnabled%> && <%=isSpotAndSwapForSWEnabled%> && !isOtAndSwapTradeType){
        		if(spotAndSwapElem.checked && !spotAndMdsElem.checked){
            		spotAndMdsElem.checked = true;
            	}
            	spotAndSwapElem.checked = false;
        	}else if(<%=isSpotAndSwapForOTEnabled%> && !isOtAndSwapTradeType ){
        		if(spotAndSwapElem.checked && !spotAndMdsElem.checked){
            		spotAndMdsElem.checked = true;
            	}
            	spotAndSwapElem.checked = false;
        	}else if(<%=isSpotAndSwapForSSPEnabled%> && !isOtAndSwapTradeType){
        		if(spotAndSwapElem.checked && !spotAndMdsElem.checked){
            		spotAndMdsElem.checked = true;
            	}
            	spotAndSwapElem.checked = false;
        	}
            
            displayExecutionRuleTextualDescription(rowId, "rfs");

            if( alertChanges && changed ) rfsExecutionRuleValidationChangesOnLoad = true; // calling changeButton() here would get over-ridden by other onLoad processing.
    }

    function validateRFSPricingExecutionRule(rowId,tradeType) {
        var type = "rfs";
        var isRFSPricingChecked = $(type + "IsRFSEnabled_" + rowId).checked;
        var isSpotSwapChecked = <%=isSpotAndSwapEnabled%> && $(type + "IsSpotSwapEnabled_" + rowId).checked;
        var isSpotMDSChecked = $(type + "IsSpotMDSEnabled_" + rowId).checked || isSpotSwapChecked;
        var execMethod = $(type + "CoverExecutionMethod_" + rowId).value;
        var isMarketChecked = $(type + "TrdTypeMkt_" + rowId).checked ? true : false;

        if (!isRFSPricingChecked && !isSpotMDSChecked) {
            $(type + "NoPriceChecked_" + rowId).checked = false;
            $(type + "NoPriceChecked_" + rowId).disabled = true;
            $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
            $(type + "FOK_" + rowId).disabled = true;
            $(type + 'FOKText_' + rowId).disabled = true;
            //$(type + "RangeEnabled_" + rowId).disabled = true;
            $(type + "TrdTypeMkt_" + rowId).disabled = true;
             $(type + 'Market_' + rowId).disabled = true;
            $(type + "TrdTypeLmt_" + rowId).disabled = true;
            $(type + 'Limit_' + rowId).disabled = true;
            $(type + "Range_" + rowId).disabled = true;
            $(type + "RangeType_" + rowId).disabled = true;
        }

        if (isRFSPricingChecked && !isSpotMDSChecked) {
            if (execMethod == '<%=CoverExecutionMethod.Cover.name()%>' || execMethod == '<%=CoverExecutionMethod.Warehouse.name()%>') {
                $(type + "CoverExecutionMethod_" + rowId).disabled = false;
                $(type + "NoPriceChecked_" + rowId).disabled = true;
                $(type + "NoPriceChecked_" + rowId).checked = false;
                $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
                $(type + "FOK_" + rowId).disabled = false;
                //$(type + "FOK_" + rowId).checked = true;
                $(type + 'FOKText_' + rowId).disabled = true;
                //$(type + "RangeEnabled_" + rowId).disabled = true;
                $(type + "TrdTypeMkt_" + rowId).disabled = true;
                $(type + 'Market_' + rowId).disabled = true;
                $(type + "TrdTypeLmt_" + rowId).disabled = true;
                $(type + 'Limit_' + rowId).disabled = true;
                $(type + "Range_" + rowId).disabled = true;
                $(type + "RangeType_" + rowId).disabled = true;
            } else if (execMethod == '<%=CoverExecutionMethod.NoCover.name()%>') {
                $(type + "CoverExecutionMethod_" + rowId).disabled = false;
                $(type + "NoPriceChecked_" + rowId).disabled = true;
                $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
                $(type + "NoPriceChecked_" + rowId).checked = false;
                $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
                $(type + "FOK_" + rowId).disabled = true;
                $(type + 'FOKText_' + rowId).disabled = true;
                //$(type + "RangeEnabled_" + rowId).disabled = true;
                $(type + "TrdTypeMkt_" + rowId).disabled = true;
                $(type + 'Market_' + rowId).disabled = true;
                $(type + "TrdTypeLmt_" + rowId).disabled = true;
                $(type + 'Limit_' + rowId).disabled = true;
                $(type + "Range_" + rowId).disabled = true;
                $(type + "RangeType_" + rowId).disabled = true;
            } else {
                $(type + "CoverExecutionMethod_" + rowId).disabled = false;
                $(type + "NoPriceChecked_" + rowId).disabled = true;
                $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
                $(type + "NoPriceChecked_" + rowId).checked = false;
                $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
                $(type + "FOK_" + rowId).disabled = true;
                $(type + 'FOKText_' + rowId).disabled = true;
                //(type + "RangeEnabled_" + rowId).disabled = true;
                $(type + "TrdTypeMkt_" + rowId).disabled = true;
                $(type + 'Market_' + rowId).disabled = true;
                $(type + "TrdTypeLmt_" + rowId).disabled = true;
                $(type + 'Limit_' + rowId).disabled = true;
                $(type + "Range_" + rowId).disabled = true;
                $(type + "RangeType_" + rowId).disabled = true;
            }
        }

        if (isSpotMDSChecked) {
            if (execMethod == '<%=CoverExecutionMethod.Cover.name()%>' || execMethod == '<%=CoverExecutionMethod.Warehouse.name()%>') {
                $(type + "CoverExecutionMethod_" + rowId).disabled = false;
                if(execMethod == '<%=CoverExecutionMethod.Warehouse.name()%>' && !isRFSPricingChecked){
                    $(type + "NoPriceChecked_" + rowId).disabled = false;
                    $(type + 'NoPriceCheckedLabel_' + rowId).disabled = false
                }
                else{
                    $(type + "NoPriceChecked_" + rowId).disabled = true;
                    $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
                    $(type + "NoPriceChecked_" + rowId).checked = false;
                }
                $(type + "FOK_" + rowId).disabled = false;
                $(type + 'FOKText_' + rowId).disabled = false;
                //$(type + "RangeEnabled_" + rowId).disabled = false;
                $(type + "TrdTypeMkt_" + rowId).disabled = false;
                $(type + "TrdTypeLmt_" + rowId).disabled = false;
                $(type + 'Market_' + rowId).disabled = false;
                $(type + 'Limit_' + rowId).disabled = false;
                $(type + "Range_" + rowId).disabled = false;
                $(type + "RangeType_" + rowId).disabled = false;
            } else if (execMethod == '<%=CoverExecutionMethod.NoCover.name()%>') {
                $(type + "CoverExecutionMethod_" + rowId).disabled = false;
                $(type + "NoPriceChecked_" + rowId).disabled = false;
                $(type + 'NoPriceCheckedLabel_' + rowId).disabled = false;
                $(type + "FOK_" + rowId).disabled = true;
                $(type + 'FOKText_' + rowId).disabled = true;
                //$(type + "RangeEnabled_" + rowId).disabled = false;
                //$(type + 'RangeEnabled_' + rowId).disabled = false;
                if ($(type + 'NoPriceChecked_' + rowId).checked) {
                    $(type + "TrdTypeMkt_" + rowId).disabled = true;
                    $(type + "TrdTypeLmt_" + rowId).disabled = true;
                    $(type + 'Market_' + rowId).disabled = true;
                    $(type + 'Limit_' + rowId).disabled = true;
                    $(type + 'Range_' + rowId).disabled = true;
                    $(type + 'RangeType_' + rowId).disabled = true;
                } else {
                    $(type + "TrdTypeMkt_" + rowId).disabled = true;
                    $(type + "TrdTypeLmt_" + rowId).disabled = false;
                    $(type + 'Market_' + rowId).disabled = true;
                    $(type + 'Limit_' + rowId).disabled = false;
                    $(type + 'Range_' + rowId).disabled = false;
                    $(type + 'RangeType_' + rowId).disabled = false;
                }
            } else {
                $(type + "CoverExecutionMethod_" + rowId).disabled = false;
                $(type + "NoPriceChecked_" + rowId).disabled = true;
                $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
                $(type + "NoPriceChecked_" + rowId).checked = false;
                $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
                $(type + "FOK_" + rowId).disabled = true;
                $(type + 'FOKText_' + rowId).disabled = true;
                //$(type + "RangeEnabled_" + rowId).disabled = true;
                $(type + "TrdTypeMkt_" + rowId).disabled = true;
                $(type + "TrdTypeLmt_" + rowId).disabled = true;
                $(type + 'Market_' + rowId).disabled = true;
                $(type + 'Limit_' + rowId).disabled = true;
                $(type + "Range_" + rowId).disabled = true;
                $(type + "RangeType_" + rowId).disabled = true;
            }
        }

        if (isMarketChecked) {
            $(type + 'Range_' + rowId).disabled = true;
            $(type + 'RangeType_' + rowId).disabled = true;
        }
        displayExecutionRuleTextualDescription(rowId, "rfs");
    }

    function validateESPExecutionRuleCondition(rowId) {
        validateESPExecutionRuleParameters(rowId);
    }

    var do59150 = true;
    function validateESPExecutionRuleParameters(rowId) {
        var type = "esp";
        var val =$(type + "CoverExecutionMethod_" + rowId).value;
        var isVWAPChecked = $(type + "VWAP_" + rowId).checked;
        var isFOKChecked = $(type + "FOK_" + rowId).checked;
        var isMarketChecked = $(type + "TrdTypeMkt_" + rowId).checked ? true : false;
        // FillFill percentage is used for FF and Copy, when CoverExecutionMethod is copy then FFPercentage can be anything but 0.
        var isCopy = val == '<%=CoverExecutionMethod.Copy.name()%>';
        var ffpInput = $("espFullFillVal_"+rowId);
        userDecimalFormat.format( ffpInput );
        var ffp = userDecimalFormat.parse( ffpInput.value );
        $('espFullFill_' + rowId).disabled = false;
        $('espAutoCover_' + rowId).disabled = false;
        if( isCopy ) {
            if( false && ffp == 0 ) { // PM simplifeid spec
                alert("Valid Copy percent values are non zero." );
                ffpInput.value = 100.0;
            }
            $('espFullFill_' + rowId).checked = true;
            $('espFullFill_' + rowId).disabled = true;
            $('espAutoCover_' + rowId).checked = true;
            $('espAutoCover_' + rowId).disabled = true;
        } else if ( !isCopy && (ffp < 0 || ffp > 100) ) {
            alert("Valid FullFill percent values are between 0 to 100." );
            ffpInput.value = 0.0;
        }
        //'input:checkbox[name=checkAll]'
        if (val == '<%=CoverExecutionMethod.Cover.name()%>') {
            //$(type + 'VWAP_' + rowId).disabled = false;
            $(type + 'NoPriceChecked_' + rowId).disabled = true;
            $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
            $(type + 'FOK_' + rowId).disabled = false;
            //$(type + 'RangeEnabled_' + rowId).disabled = false;
            $(type + "TrdTypeMkt_" + rowId).disabled = false;
            $(type + "TrdTypeLmt_" + rowId).disabled = false;
            $(type + 'Range_' + rowId).disabled = false;
            $(type + 'RangeType_' + rowId).disabled = false;
        }
        else if(val == '<%=CoverExecutionMethod.Warehouse.name()%>') {
            //$(type + 'VWAP_' + rowId).disabled = false;
            $(type + 'NoPriceChecked_' + rowId).disabled = false;
            $(type + 'NoPriceCheckedLabel_' + rowId).disabled = false;
            $(type + 'FOK_' + rowId).disabled = false;
            //$(type + 'RangeEnabled_' + rowId).disabled = false;
            $(type + "TrdTypeMkt_" + rowId).disabled = false;
            $(type + "TrdTypeLmt_" + rowId).disabled = false;
            $(type + 'Range_' + rowId).disabled = false;
            $(type + 'RangeType_' + rowId).disabled = false;
        }
        else if (val == '<%=CoverExecutionMethod.NoCover.name()%>' || val == '<%=CoverExecutionMethod.Copy.name()%>' ) {
            //$(type + 'VWAP_' + rowId).disabled = true;
            $(type + 'NoPriceChecked_' + rowId).disabled = false;
            $(type + 'NoPriceCheckedLabel_' + rowId).disabled = false;
            $(type + 'FOK_' + rowId).disabled = true;
            //$(type + 'RangeEnabled_' + rowId).disabled = false;
            if ($(type + 'NoPriceChecked_' + rowId).checked) {
                $(type + "TrdTypeMkt_" + rowId).disabled = true;
                $(type + "TrdTypeLmt_" + rowId).disabled = true;
                $(type + 'Range_' + rowId).disabled = true;
                $(type + 'RangeType_' + rowId).disabled = true;
            } else {
                $(type + "TrdTypeMkt_" + rowId).disabled = true;
                $(type + "TrdTypeLmt_" + rowId).disabled = false;
                $(type + 'Range_' + rowId).disabled = false;
                $(type + 'RangeType_' + rowId).disabled = false;
            }
        } else if (val == '<%=CoverExecutionMethod.Manual.name()%>') {
            $(type + 'NoPriceChecked_' + rowId).disabled = true;
            $(type + 'NoPriceCheckedLabel_' + rowId).disabled = true;
            $(type + 'FOK_' + rowId).disabled = true;
            //$(type + 'RangeEnabled_' + rowId).disabled = true;
            $(type + "TrdTypeMkt_" + rowId).disabled = true;
            $(type + "TrdTypeLmt_" + rowId).disabled = true;
            $(type + 'Range_' + rowId).disabled = true;
            $(type + 'RangeType_' + rowId).disabled = true;
        }
        var multiFillEnabled = $("multiFillEnabled2") != null && $("multiFillEnabled2").checked;
        if(!multiFillEnabled && (val == '<%=CoverExecutionMethod.Cover.name()%>' || val == '<%=CoverExecutionMethod.Warehouse.name()%>')){
            $(type + 'VWAP_' + rowId).disabled = false;
        }else{
            if(<%=isDisableVWAPIfMultiFillEnabled%>) {
                $(type + 'VWAP_' + rowId).disabled = true;
            }
        }
        
	var partialCoverConfigured = $( type + "PartialCover_" + rowId) != null;
	if ( partialCoverConfigured )
	{
		if( val == '<%=CoverExecutionMethod.Cover.name()%>' ){
		    $(type + 'PartialCover_' + rowId).disabled = false;
		    $(type + 'CoverPercentageVal_' + rowId).disabled = false;
		}else{
		    $(type + 'PartialCover_' + rowId).disabled = true;
		    $(type + 'CoverPercentageVal_' + rowId).disabled = true;
		}
	}


        if (isMarketChecked) {
            $(type + 'Range_' + rowId).disabled = true;
            $(type + 'RangeType_' + rowId).disabled = true;
        }

        if (isVWAPChecked) {
            //$(type + 'RangeEnabled_' + rowId).disabled = true;
            //$('input:radio[name=workflowMessage.espRangeEnabled_' + rowId +']').prop('checked');
            if( !do59150 ) {
                $(type + 'Range_' + rowId).disabled = true;
                $(type + 'RangeType_' + rowId).disabled = true;
                $(type + 'Range_' + rowId).value = "0";
            } else {
                $(type + 'Range_' + rowId).disabled = false;
                $(type + 'RangeType_' + rowId).disabled = false;
            }
            $(type + 'FOK_' + rowId).disabled = true;
            $(type + 'TrdTypeLmt_' + rowId).checked = true;
        }

        if (isFOKChecked) {
            $(type + 'VWAP_' + rowId).disabled = true;
        }
        
       if(isCopy && <%=isNewCopyTradeEnabled%>){
    	   $(type + "CopyChecked_" + rowId).hide();
    	   $(type + "CopyCheckedLabel_" + rowId).hide();
    	   $(type + "CopyValue_" + rowId).hide();
    	   $(type + "CopyPercentageLabel_" + rowId).hide();
        }else if(!isCopy && <%=isNewCopyTradeEnabled%>){
        	$(type + "CopyChecked_" + rowId).show();
      	  	$(type + "CopyCheckedLabel_" + rowId).show();
      	   	$(type + "CopyValue_" + rowId).show();
      	  	$(type + "CopyPercentageLabel_" + rowId).show();

        	
        	var coverElem = $(type + "CoverExecutionMethod_" + rowId);
        	
        	for(var i=0; i < coverElem.options.length; i++){
        		if(coverElem.options[i].value == "Copy"){
        			coverElem.remove(i);
        		}
        	}
        }
       
        displayExecutionRuleTextualDescription(rowId, "esp");
    }

    function updateVWAPCheckbox(value){
        if(value){
            $j(".espVWAP").each(function() {
                $j(this).prop("checked", !value);
                $j(this).prop("disabled", value);
            });
        }
    }

    /*function validateRange(rangeEnabled, type, rowId) {
        $(type + 'Range_' + rowId).disabled = !rangeEnabled.checked;
    }*/
    function validateRange(rangeEnabled, type, rowId) {
        $(type + 'Range_' + rowId).disabled = !rangeEnabled.checked;
    }

    function displayExecutionRuleTextualDescription(rowId, type) {
        var val = $(type + "CoverExecutionMethod_" + rowId).value;
        var isNPCChecked = $(type + 'NoPriceChecked_' + rowId).checked;
        var isVWAPChecked = $(type + 'VWAP_' + rowId) != null && $(type + 'VWAP_' + rowId).checked;
        var isFOKChecked = $(type + 'FOK_' + rowId) != null && $(type + 'FOK_' + rowId).checked;
        var isMarketChecked = $(type + 'TrdTypeMkt_' + rowId).checked;
        var isLimitChecked = $(type + 'TrdTypeLmt_' + rowId).checked;
        var range = $(type + 'Range_' + rowId).value;
        var rangType = $(type + 'RangeType_' + rowId).value;
        var isRangeInPips = rangType == "Pips";
        var isDIChecked = $(type + 'DIChecked_' + rowId) != null && $(type + 'DIChecked_' + rowId).checked;

        var description = "";
        if(<%=isDealerInterventionEnabled%> && isDIChecked){
        	description += "Dealer Confirmation Required. ";
        }
        if (val == '<%=CoverExecutionMethod.Cover.name()%>' || val == '<%=CoverExecutionMethod.Warehouse.name()%>') {
            if( val == '<%=CoverExecutionMethod.Warehouse.name()%>'){
                if(isNPCChecked){
                    description += "No price check will be done. "
                }
                else{
                   description += "Price check will be done. "
                }
            }
            if( val == '<%=CoverExecutionMethod.Warehouse.name()%>'){
                description += "If warehouse is OFF "
            }
            if(type != "esp" && $(type + 'IsRFSEnabled_' + rowId).checked && !$(type + 'IsSpotMDSEnabled_' + rowId).checked ){
            	if(isMarketChecked){
            		description += "Fill at market with a single provider. Partial fills are not allowed.";
            	}else{
            		description += "Fill at limit with a range of " + range + (isRangeInPips ? " pips." : " % of spread.") + " Partial fills are not allowed.";            		
            	}            	
            } else if (isVWAPChecked) {
                description += "Fill using VWAP execution";
                description += !do59150 ? "" : " with a range of " + range + (isRangeInPips ? " pips" : " % of spread");
                description += ". Partial fills are allowed.";
            } else if (isFOKChecked) {
                if (isMarketChecked) {
                    description  += "Fill at market with a single provider. Partial fills are not allowed.";
                } else {
                    description  += "Fill at limit with a range of " + range + (isRangeInPips ? " pips." : " % of spread.") + " Partial fills are not allowed.";
                }
            } else {
                if (isMarketChecked) {
                    description  += "Fill at market. Partial fills are allowed.";
                } else {
                    description  += "Fill at limit with a range of " + range + (isRangeInPips ? " pips." : " % of spread.") + " Partial fills are allowed.";
                }
            }            
            
        } else if (val == '<%=CoverExecutionMethod.NoCover.name()%>') {
            if (isNPCChecked) {
                description  += "Do not cover. No price check will be done.";
            } else {
                if (range > 0) {
                    description  += "Do not cover. Price will be checked to ensure it is within the range.";
                } else {
                    description  += "Do not cover. Price will be checked to ensure it is valid.";
                }
            }
        } else if (val == '<%=CoverExecutionMethod.Manual.name()%>') {
            description  += "Send for manual intervention by dealer.";
        } else if (val == '<%=CoverExecutionMethod.Copy.name()%>') {
            if (isNPCChecked) {
                description  += "Copy. No price check will be done.";
            } else {
                if (range > 0) {
                    description  += "Copy. Price will be checked to ensure it is within the range.";
                } else {
                    description  += "Copy. Price will be checked to ensure it is valid.";
                }
            }
        }
        
        $(type + 'Description_' + rowId).innerText = description;
        if( type != "esp" ) return;

        var descFF = "&nbsp;";
        if (val == '<%=CoverExecutionMethod.Copy.name()%>') {
            descFF = "copy %";
        } else {
            descFF = "min % covered";
        }
        $(type + 'DescFullFill_' + rowId).innerText = descFF;
    }
    
    function validateDealerInterventionChkBox(type, rowId){
    	var coverElem = $( type + "CoverExecutionMethod_" + rowId);
    	var isDIChecked = $(type + 'DIChecked_' + rowId);
    	var rfsTradeType = $( type + "TradeType_" + rowId);
    	if(isDIChecked!=null && isDIChecked.checked){
        	
        	var options= coverElem.options;
        	for (var i= 0; i<options.length; i++) {
    	   		if(options[i].value==="Manual"){
    	   			if(options[i].selected){
    	   				coverElem.selectedIndex = 1;
    	   			}
    	   			options.remove(i);
    	   		}
    	   	}
        	
        }else{
        	var isManualExist = false;
        	var options= coverElem.options;
        	for (var i= 0; i<options.length; i++) {
        		if(options[i].value==="Manual"){
        			isManualExist = true;
        		}
        	}
        	if(!isManualExist){
        		var selectedIndex = coverElem.selectedIndex;
        		coverElem.innerHTML = "";
        		<%for (int i= 0;  i < rfsCoverExecutionMethods.length; i++) { %>
        			coverElem.add(new Option("<%=rfsCoverExecutionMethods[i].getFullName()%>","<%=rfsCoverExecutionMethods[i].name()%>"));
        		<%}%>
        		if(selectedIndex == 2)
        			coverElem.selectedIndex = selectedIndex +1;
        		else
        			coverElem.selectedIndex = selectedIndex;
        	}
        	
        	var isSSPTradeTypeExist = false;
        	var rfsTradeTypeOptions= rfsTradeType.options;
        	for (var i= 0; i<rfsTradeTypeOptions.length; i++) {
        		if(rfsTradeTypeOptions[i].value==='<%=ISCommonConstants.TRD_CLSF_FXSSP%>'){
        			isSSPTradeTypeExist = true;
        			break;
        		}
        	}
        	if(!isSSPTradeTypeExist){
        		rfsTradeType.add(new Option("FX SSP", "<%=ISCommonConstants.TRD_CLSF_FXSSP%>"), 6);
        	}
        }   
    }
    
    function showDealerInterventionCheckbox(type, rowId, show){
    	var dealerInterventionElem = $(type + 'DIChecked_' + rowId);
    	var dealerInterventionText = $(type + 'DIText_' + rowId);
    	if(show){
    		dealerInterventionElem.show();
        	dealerInterventionText.show();
    	}else{
    		dealerInterventionElem.checked = show;
    		dealerInterventionElem.hide();
        	dealerInterventionText.hide();
    	}
    }

    function updateBookVisibility(coverExecMethodSelectElement,productType) {

        var isUpdateRequired = false;
        if(productType){
           isUpdateRequired = (productType == "ESP" && <%=isESPMultiBookPropertyEnabled%>) || (productType == "RFS" && <%=isRFSMultiBookPropertyEnabled%>);
        }

        //console.log('coverExecMethodSelectElement='+coverExecMethodSelectElement.id);
        if(coverExecMethodSelectElement && isUpdateRequired ){
            var bookElement = document.getElementById(coverExecMethodSelectElement.id+'_Book');
            if(bookElement){
                const selectedValue = coverExecMethodSelectElement.value;
                console.log('selected value='+selectedValue);
                bookElement.style.display = (selectedValue === "Warehouse") ? "inline" : "none";
            }
        }
    }


</script>