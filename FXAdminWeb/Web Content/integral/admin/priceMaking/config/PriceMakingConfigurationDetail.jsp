<%@page import="com.integral.is.util.CurrencyUtil"%>
<%@page import="com.integral.admin.utils.marketdefinition.MarketDefinitionUtil"%>
<%@page import="com.integral.finance.fx.FXRateConvention"%>
<%@page import="com.integral.finance.currency.Currency"%>
<%@page import="com.integral.finance.currency.CurrencyFactory"%>
<%@ taglib uri="/tag/idc-html.tld" prefix="idcHtml" %>
<%@ taglib uri="/tag/idc-bean.tld" prefix="idcBean" %>
<%@ taglib uri="/tag/struts-html.tld" prefix="html" %>
<%@ taglib uri="/tag/idc-logic.tld" prefix="idcLogic" %>

<%@ page import="com.integral.admin.utils.AdminWebServicesMBean" %>
<%@ page import="com.integral.admin.utils.AdminWebServicesMBeanC" %>
<%@ page import="com.integral.admin.utils.organization.OrganizationUtil" %>
<%@ page import="com.integral.broker.config.BrokerProvisionConfigFactory" %>
<%@ page import="com.integral.broker.configuration.ConfigurationFactory"%>
<%@ page import="com.integral.broker.config.BrokerProvisionConfig" %>
<%@ page import="com.integral.broker.admin.ConfigurationService,com.integral.user.User" %>
<%@ page import="com.integral.broker.admin.OrganizationService" %>
<%@ page import="com.integral.broker.admin.StreamService" %>
<%@ page import="com.integral.broker.model.ExecutionMethod" %>
<%@ page import="com.integral.broker.model.SpreadPriority"%>
<%@ page import="com.integral.broker.model.TenorSpread"%>
<%@ page import="com.integral.broker.model.SpreadBias"%>
<%@ page import="com.integral.broker.model.SpreadType"%>
<%@ page import="com.integral.broker.model.Tier"%>
<%@ page import="com.integral.broker.sort.ProxyPriceSorterC"%>
<%@ page import="com.integral.finance.calculator.Calculator"%>
<%@ page import="com.integral.finance.trade.Tenor"%>
<%@ page import="com.integral.jsp.framework.table.TableForm"%>
<%@ page import="com.integral.user.Organization"%>
<%@ page import="com.integral.user.OrganizationRelationship"%>
<%@ page import="org.apache.commons.lang.ArrayUtils"%>
<%@ page import="org.apache.struts.action.Action"%>
<%@ page import="org.apache.struts.action.ActionErrors"%>
<%@ page import="org.apache.struts.util.RequestUtils"%>
<%@ page import="org.apache.struts.util.ResponseUtils"%>
<%@ page import="java.io.IOException"%>
<%@page import="com.integral.admin.utils.web.HTTPWebUtils"%>
<%@ page import="com.integral.broker.model.*,com.integral.system.runtime.RuntimeFactory,com.integral.persistence.cache.ReferenceDataCacheC" %>
<%@ page import="com.integral.admin.utils.pricemaking.PriceMakingUtil" %>
<%@ page import="com.integral.admin.pricemaking.PriceMBeanFactory" %>
<%@ page import="com.integral.admin.pricemaking.PriceMakingMBean" %>
<%@ page import="java.util.*" %>
<%@ page import="com.integral.is.ISCommonConstants" %>
<%@ page import="java.text.DecimalFormat" %>
<%@ page import="com.integral.finance.marketData.fx.FXMarketDataSet" %>
<%@ page import="com.integral.broker.aggregate.*" %>
<%@ page import="com.integral.finance.counterparty.UserCounterpartyGroup" %>
<%@ page import="com.integral.admin.utils.organization.SalesDealerGroupUtil" %>
<%@ page import="com.integral.log.Log" %>
<%@ page import="com.integral.log.LogFactory" %>
<%@ page import="com.integral.admin.utils.organization.UserUtil"%>
<%@ page import="com.integral.finance.trade.configuration.TradeConfigurationFactory" %>
<%@ page import="com.integral.finance.trade.configuration.TradeConfigurationMBean" %>
<%@ page import="java.text.SimpleDateFormat"%>
<%@ page import="com.integral.riskmanagement.rw.provision.RWProvisionConfig" %>
<%@ page import="com.integral.riskmanagement.rw.provision.RWProvisionConfigManager" %>
<%@ page import="com.integral.is.common.util.YMUtil" %>
<%@ page import="com.integral.finance.counterparty.LegalEntity" %>
<%@ page import="com.integral.finance.dealing.mifid.MiFIDUtils" %>
<%@ page import="com.integral.util.StringUtilC" %>
<%@ page import="com.integral.broker.model.enums.SpreadUnit" %>
<%@ page import="com.integral.pricing.config.PricingConfigurationFactory" %>
<%@ page import="com.integral.broker.BrokerAdaptorUtil" %>

<jsp:include page="/integral/core/framework/js/JsInclude.jsp?file=/integral/core/framework/js/combined/FrameworkCommonUtils.js"/>
<jsp:include page="/integral/core/framework/jsp/DateFormat.jsp"/>
<jsp:include page="/integral/core/framework/js/JsInclude.jsp?file=/integral/core/framework/js/html/Calendar.js"/>
<jsp:include page="/integral/core/framework/jsp/DecimalFormat.jsp" />
<jsp:include page="/integral/core/framework/js/JsInclude.jsp?file=/integral/core/framework/js/dealing/NotionalAmountFormat.js" />
<jsp:include page="/integral/admin/priceMaking/config/executionRules.jsp" />
<script src="/admin/integral/admin/js/SelectSortFilterUtil.js" type="text/javascript"></script>
<script src="/admin/integral/admin/priceMaking/js/tiers.js"></script>
<script src="/admin/integral/admin/priceMaking/js/tenorSpread.js"></script>
<script src="/admin/integral/core/framework/js/util/StringUtils.js"></script>
<script type="text/javascript" language="javascript" src="/admin/integral/admin/priceMaking/config/PriceMakingNSpreadConfig.js"></script>
<script type="text/javascript" language="javascript" src="/admin/integral/admin/priceMaking/config/PriceMakingConfigurationDetail.js"></script>

<script>
    var aggProvInclude = new ArrayList();
    var aggProvExclude = new ArrayList();
    var orderProvInclude = new ArrayList();
    var orderProvExclude = new ArrayList();
    var lgProvidersList = new ArrayList();
    var tiersAdded=new ArrayList();
    var multiTier=new HashMap();
    var espExecutionRulesCondAdded=new ArrayList();
    var rfsExecutionRulesCondAdded=new ArrayList();

    var swapPointProvInclude = new ArrayList();
    var swapPointProvExclude = new ArrayList();
 
</script>


<%!
    Log log = LogFactory.getLog( "com.integral.finance.trade.jsp.PriceMakingConfigurationDetail" );
%>
<%!
    StreamService ss = com.integral.broker.admin.AdminFactory.getInstance().getStreamService();
    ConfigurationService configService = com.integral.broker.admin.AdminFactory.getInstance().getConfigurationService();
    OrganizationService os = com.integral.broker.admin.AdminFactory.getInstance().getOrganizationService();
    private boolean isErrorOnPage(PageContext pageContext) throws JspException 
    {
        ActionErrors actionErrors = RequestUtils.getActionErrors(pageContext, Action.ERROR_KEY);
        return actionErrors.size() > 0;
    }

    private void printTenorSpreads(int counter, TenorSpread tenor, String type, JspWriter out) throws IOException
    {
        printTenorSpreads(    counter, type, out, tenor.getTenor().toString()
                            , String.valueOf( tenor.getBidSpread()), String.valueOf( tenor.getOfferSpread()), String.valueOf( tenor.getOneWaySpread())
                            , String.valueOf( tenor.getPostBidSpread()), String.valueOf( tenor.getPostOfferSpread()), String.valueOf( tenor.getPostOneWaySpread())
                         );
    }
    
    private void printTenorSpreadsWhenErrors(int counter, String type, JspWriter out, HttpServletRequest request)
            throws IOException, JspException 
    {
        // TODO: Make logic better
        String val= HTTPWebUtils.getParameterValue(request,"workflowMessage." + type + "Tenor_" + counter);
        if( null == val ) return;
        printTenorSpreads(
            counter, type, out,
            request.getParameter( "workflowMessage." + type + "Tenor_" + counter ),
            request.getParameter( "workflowMessage." + type + "Bid_" + counter ),
            request.getParameter( "workflowMessage." + type + "Offer_" + counter ),
            request.getParameter( "workflowMessage." + type + "One_" + counter ),
            request.getParameter( "workflowMessage." + type + "BidPost_" + counter ),
            request.getParameter( "workflowMessage." + type + "OfferPost_" + counter ),
            request.getParameter( "workflowMessage." + type + "OnePost_" + counter )
        );
    }

    private static int COLSPERROW = 1;
    private void printTenorSpreads(int counter, String type, JspWriter out, String tenor
                                    , String bid, String offer, String oneWay
                                    , String bidPost, String offerPost, String oneWayPost
                                  ) throws IOException
     {
        
         if (counter % 2== 0) 
         {
             out.println("<tr class='tl' style='text-align:center;'>");
         } 
         else 
         {
             out.println("<tr class='tlc' style='text-align:center;'>");
         }
        out.println("<td nowrap class='ac'><input type=checkbox id='" +type +"_"+counter +"' name='" +type +"_"+counter +"' value='"+type+"_"+counter+"'></td>");
        printTenor( out, type,"workflowMessage." + type + "Tenor_" + counter,tenor );
        printSpread( out, "workflowMessage." + type + "Bid_" + counter, bid, true );
        printSpread( out, "workflowMessage." + type + "Offer_" + counter, offer, true );
        printSpread( out, "workflowMessage." + type + "One_" + counter, oneWay, true );
        printSpread( out, "workflowMessage." + type + "BidPost_" + counter, bidPost, false );
        printSpread( out, "workflowMessage." + type + "OfferPost_" + counter, offerPost, false );
        printSpread( out, "workflowMessage." + type + "OnePost_" + counter, oneWayPost, false );
        out.println("</tr>");
    }

    private void printTierMultipliers(int counter, JspWriter out, RFSTierMultiplier multiplier, DecimalFormat format) throws Exception {
        String klass = counter % 2 == 0 ? "tl" : "tlc";
        String row = "<tr class='FPTierMultiplierRow " + klass + "' style='text-align:center;' name='FPTierMultiplierRow' id='" + counter + "'>";
        out.println(row);
        out.println("<td nowrap class='ac'><input type=checkbox class='FPTierMultiplierCheckBox tierMultiplierElement'></td>");
        out.println("<td nowrap class='ac'>Tier " + (multiplier.getSortOrder() + 1) + "</td>");
        out.println("<td nowrap class='ac'><input type='text' class='ftr tierMultiplierElement' name='rfsTierMultiplierLimit' value='" + format.format(multiplier.getLimit()) + "' onblur='validatePositiveNumber(this, false, 1);changeButton()'></intput></td>");
        out.println("<td nowrap class='ac'><input type='text' class='ftr tierMultiplierElement' name='rfsTierMultiplierValue' value='" + multiplier.getMultiplier() + "' onblur='validatePositiveNumber(this, false, 1);changeButton()'></intput></td>");
    }

    private void printTenor( JspWriter out, String type , String key, String value ) throws IOException
    {
        String classNameTbl = "OTS".equals(type) ? "" : " swapTenorSprdTbl";
        if (value == null || "null".equalsIgnoreCase( value )){
            value = "";
        }
        out.println("\t<td nowrap class='ac'><input type='text' style='background: var(--input-bg-secondary);font-weight:bold' id ='" + key + " 'name ='" + key + "' value='" + value + "' size='10'  class='ftr" + classNameTbl + "' onblur='validateTenor(this,\""+type+"\")' onkeyup='changeButton()'></td>");
    }

    private void printSpread( JspWriter out, String key, String value, boolean isPre ) throws IOException {
        if (value == null || "null".equalsIgnoreCase( value )){
            value = "";
        }
        String className = "";
        boolean isSwap = key.contains("STS");
        if(isSwap) className = isPre ? " swapTenorSprdCellPre" : " swapTenorSprdCellPost";
        out.println("\t<td nowrap class='ac'><input type='text' id ='" + key + "' name ='" + key + "' value='" + value + "' size='10'  class='ftr" + className + "' onblur='validateTenorSpread(this)' onkeyup='changeButton()'></td>");
    }


  
  /* This function will format the time zone to :[GMT -/+ hh:mm] 3Character ID - DisplayName */
	private String getFormatedTimeZone(Object timezone){
	    return timezone.toString();
	}

    private String getFormattedNotionalAmountERC(double amount, DecimalFormat format){
        if (amount == -1 ){
            return "";
        } else {
            return format.format(amount);
        }
    }



%>
<div>

<% // TODO these 4 setup 'owner' details - though stream/owner are kind of synonyms they are not quite, hence the unclarity here %>
<idcBean:define id="configuration" name="ObjectActionForm" property="objectForm.entity" type="com.integral.broker.model.ConfigurationC"/>
<idcBean:define id="ccypairgrp"  name="ObjectActionForm" property="objectForm.currencyPairGroup" />
 <idcBean:define id="crncyGrpObject" name="ObjectActionForm" property="objectForm.object.currencyPairGroup" type="com.integral.finance.currency.CurrencyPairGroup"/>
 <idcBean:define id="quoteAggregator" name="ObjectActionForm" property="objectForm.object.quoteAggregator" />
 <idcBean:define id="pricingSource" name="ObjectActionForm" property="objectForm.object.pricingSource" />
<idcBean:define id="executionMethod" name="ObjectActionForm" property="objectForm.object.espExecutionMethod" />
 <idcBean:define id="espExecutionMethod" name="ObjectActionForm" property="objectForm.object.espExecutionMethod" />
<idcBean:define id="rfsExecutionMethod" name="ObjectActionForm" property="objectForm.object.rfsExecutionMethod" />
<idcBean:define id="spreadPri" name="ObjectActionForm" property="objectForm.object.spreadPriority" />
<idcBean:define id="customerFullFillEnabled" name="ObjectActionForm" property="objectForm.object.customerFullFillEnabled" type="java.lang.Boolean"/>
<idcBean:define id="marketRangeEnabledForEspExecution" name="ObjectActionForm" property="objectForm.object.marketRangeEnabledForEspExecution" type="java.lang.Boolean"/>
<idcBean:define id="marketRangeForEspExecution" name="ObjectActionForm" property="objectForm.marketRangeForEspExecution" type="java.lang.Double"/>
<idcBean:define id="marketRangeEnabledForRfsExecution" name="ObjectActionForm" property="objectForm.object.marketRangeEnabledForRfsExecution" type="java.lang.Boolean"/>
<idcBean:define id="marketRangeForRfsExecution" name="ObjectActionForm" property="objectForm.marketRangeForRfsExecution" type="java.lang.Double"/>
<idcBean:define id="customerFullFillType" name="ObjectActionForm" property="objectForm.object.customerFullFillType" />
<idcBean:define id="customConditionForCustomerFullFill" name="ObjectActionForm" property="objectForm.object.customConditionForCustomerFullFill" />
 <idcBean:define id="executionMethodF" name="ObjectActionForm" property="objectForm.espExecutionMethod" />
 <idcBean:define id="priceSorter" name="ObjectActionForm" property="objectForm.object.priceSorter" />
 <idcBean:define id="spreadBiasForConfig" name="ObjectActionForm" property="objectForm.spreadBias" />
 <idcBean:define id="marketDataSet" name="ObjectActionForm" property="objectForm.object.marketDataSet" type="com.integral.finance.marketData.fx.FXMarketDataSet"/>
 <idcBean:define id="RFSQuoteFilterMarketDataSet" name="ObjectActionForm" property="objectForm.object.RFSQuoteFilterMarketDataSet" type="com.integral.finance.marketData.fx.FXMarketDataSet"/>
 <idcBean:define id="loginUser" name="ObjectActionForm" property="user" type="com.integral.user.User" />
 <idcBean:define id="loginUserOrg" name="ObjectActionForm" property="user.organization" type="com.integral.user.Organization" />
 <idcBean:define id="loginUserOrg" name="ObjectActionForm" property="user.organization" type="com.integral.user.Organization" />
 <idcBean:define id="priceProvidersForConfig" name="ObjectActionForm" property="objectForm.object.priceProviders" type="java.util.Collection" />
 <idcBean:define id="stalenessInterval" name="ObjectActionForm" property="objectForm.stalenessInterval" type="java.lang.Long"/>
 <idcBean:define id="currencyCollectionForm" name="ObjectActionForm"  property="referenceData(com/integral/finance/currency/CurrencyC)"/>
 <idcBean:setProperty name="currencyCollectionForm"    property="sortProperty" value="sortOrder"/>
 <idcBean:define id="currencyCollection" name="currencyCollectionForm"  property="sortedList" type="java.util.Collection"/>
 <idcBean:define id="overrideChannel" name="ObjectActionForm" property="objectForm.channelOverrideEnabled" type="java.lang.Boolean"/>
 <idcBean:define id="isLgOnly" name="ObjectActionForm" property="objectForm.liquidityGroupOnly" type="java.lang.Boolean"/>

 <idcBean:define id="espEnabled" name="ObjectActionForm" property="objectForm.ESPEnabled" type="java.lang.Boolean"/>
 <idcBean:define id="rfsEnabled" name="ObjectActionForm" property="objectForm.RFSEnabled" type="java.lang.Boolean"/>
 <idcBean:define id="isFixSprdEnabled" name="ObjectActionForm" property="objectForm.spreadFixedEnabled" type="java.lang.Boolean"/>
 <idcBean:define id="displayPreference"  name="loginUser" property="displayPreference" type="com.integral.user.DisplayPreferenceC"/>
<idcBean:define id="decimalAmountSeparator" name="displayPreference" property="decimalAmountSeparator" type="java.lang.String"/>
<idcBean:define id="decimalFormat" name="displayPreference" property="decimalFormat" type="java.text.DecimalFormat"/>

 <idcBean:define id="spreadTypeCalculators" name="ObjectActionForm" property="objectForm.referenceData(com/integral/finance/price/fx/FXSpreadPriceCalculatorC).list"/>
 <idcBean:define id="prorataForwardMarketDataSet" name="ObjectActionForm" property="objectForm.object.prorataForwardMarketDataSet" type="com.integral.finance.marketData.fx.FXMarketDataSet"/>

<script>
   var decimalSeparator = '<%=decimalAmountSeparator%>';
   var aggProvExcludeNonDO = [];
   var aggProvExcludeDO = [];
</script>
<%
    Currency tierCurrency = configuration.getOriginalTierCurrency();
    if (tierCurrency!=null){
%>
<script>
  
   selectedTierCurrency='<%=tierCurrency%>';
 </script>
<%
    }
%>

<script>
	selectedVehicleCurrency = '<%=configuration.getVehicleCurrency()!=null ? configuration.getVehicleCurrency().getShortName() : "USD"%>'
</script>

<%
    String orgEncryptedId=ResponseUtils.filter(request.getParameter( "orgEncryptedId" ));
    Organization org=OrganizationUtil.getOrganization(TableForm.decryptId(orgEncryptedId));
    LegalEntity orgDefaultLe = org.getDefaultDealingEntity();
    List<QuoteAggregator> quoteAggregators=new ArrayList(configService.getQuoteAggregators());
    PriceMakingMBean pmConf = PriceMBeanFactory.getInstance().getPriceMakingMBean() ;
    boolean isSyntheticFullBookEnabled = pmConf.isSyntheticFullBookEnabled(org.getShortName()) ;
    boolean isSyntheticFbMtFokEnabled = pmConf.isSyntheticFullBookMtFokEnabled(org.getShortName()) ;
    boolean isFaMultiTierEnabled = pmConf.isFullAmountMultiTierEnabled(org.getShortName()) ;
    boolean isFaMultiQuoteEnabled = pmConf.isFullAmountMultiQuoteEnabled(org.getShortName()) ;
    boolean isBenchmarkAggregationEnabled = pmConf.isBenchmarkAggregationEnabled(org.getShortName());
    boolean isDisableVWAPIfMultiFillEnabled = pmConf.isVWAPDisabledIfMultiFillEnabled(org.getShortName());
    boolean isMatchDelayEnabled = pmConf.isDelayedOrderMatchEnabled(org.getShortName());
    BrokerProvisionConfig brokerProvisionConfig = BrokerProvisionConfigFactory.getBrokerProvisionConfig();
    boolean hourglassEnabled = brokerProvisionConfig.isHourglassPricingEnabled(org.getShortName());
    boolean isNewCopyTradeEnabled = brokerProvisionConfig.isNewCopyTradeEnabled(org.getShortName());
    boolean isOldCopyTradeEnabled = brokerProvisionConfig.isCopyTradeEnabled(org.getShortName());
    boolean partialCoverEnabled = brokerProvisionConfig.isPartialCoverEnabled(org.getShortName());
    String hideHourglassRow = hourglassEnabled ? "" : "style='display:none'";
    Iterator iterator = quoteAggregators.iterator();
    Spread espSpread = configuration.getOriginalESPSpread();
    Spread spotSpread = configuration.getOriginalSpotSpread();
    Spread swapSpread = configuration.getOriginalSwapSpread();
    Boolean multiFillEnabled = configuration.isOriginalMultiFillEnabled();
    Boolean noTimeout = configuration.isOriginalNoTimeout();
    Boolean overrideESPUpdateInterval = configuration.isOriginalOverrideESPUpdateInterval();
    Boolean overrideRFSUpdateInterval = configuration.isOriginalOverrideRFSUpdateInterval();
    Boolean overrideRFSParameters = configuration.isOriginalOverrideRFSParameters();
    Boolean isMinSprdEnabled = configuration.isOriginalSpreadMinimumEnabled();
    Boolean isMaxSprdEnabled = configuration.isOriginalSpreadMaximumEnabled();
    Boolean isSpreadPreserved = configuration.isOriginalRFSSpreadPreserved();
    Boolean isMinRFSSprdEnabled = configuration.isOriginalRFSSpreadMinimumEnabled();
    Boolean isMaxRFSSprdEnabled = configuration.isOriginalRFSSpreadMaximumEnabled();
    Tenor maxTenor = configuration.getOriginalMaximumTenor();
    SpreadBias rfsSpreadMinBiasConfig = configuration.getRFSSpreadMinimumBias();
    SpreadBias rfsSpreadMaxBiasConfig = configuration.getRFSSpreadMaximumBias();
    SpreadBias spreadMinBiasConfig = configuration.getOriginalSpreadBias();
    SpreadBias spreadMaxBiasConfig = configuration.getOriginalSpreadMaximumBias();
    Long requestExpiry = configuration.getOriginalRequestExpiry();
    Long espFullUpdateInterval = configuration.getOriginalEspFullUpdateInterval();
    Long espBestBidOfferInterval = configuration.getOriginalEspBestBidOfferInterval();
    Long rfsFullUpdateInterval = configuration.getOriginalRfsFullUpdateInterval();
    Long rsfBestBidOfferInterval = configuration.getOriginalRsfBestBidOfferInterval();
    Double rfsSpreadMinimum = configuration.getOriginalRFSSpreadMinimum();
    Double rfsSpreadMaximum = configuration.getOriginalRFSSpreadMaximum();
    Double spreadMaximum = configuration.getOriginalSpreadMaximum();
    Double spreadMinimum = configuration.getOriginalSpreadMinimum();
    Double allowedDeviationPercent = configuration.getOriginalAllowedDeviationPercent();
    Collection<Tier> tiers = configuration.getOriginalTiers();
	boolean prorataForwardEnabled = TradeConfigurationFactory.getTradeConfigurationMBean().isProrataForwardSupportEnabled( org );

    while (iterator.hasNext()) {
        Object quoteAgg = iterator.next();
        String shortName = ((QuoteAggregator) quoteAgg).getShortName();
        boolean doRemove = ((!isSyntheticFullBookEnabled
                            && ("FullBookMTAQuoteAggregatorC".equals(shortName)
                                || "FullBookMTWQuoteAggregatorC".equals(shortName)))
                            || (!isBenchmarkAggregationEnabled && "BenchmarkQuoteAggregatorC".equals(shortName))
                            || (!isSyntheticFbMtFokEnabled && "FullBookMTFOKQuoteAggregatorC".equals(shortName))
                            || (!isFaMultiQuoteEnabled && "SyntheticFullAmountFullBookAggregatorC".equals(shortName))
                            || (!isFaMultiTierEnabled && "MultiTierFullAmountAggregatorC".equals(shortName)));
        if (doRemove) {
            iterator.remove();
        }
    }

    List<UserCounterpartyGroup> salesDealerGroups = SalesDealerGroupUtil.getSortedSalesDealerGroups(org);
    Collections.sort(quoteAggregators, new Comparator<QuoteAggregator>() {
        public int compare(QuoteAggregator o1, QuoteAggregator o2) {
            return o1.getLongName().compareTo(o2.getLongName());
        }
    });
    List<QuoteAggregator> quoteAggregatorsCol = new ArrayList<QuoteAggregator>();
    for (QuoteAggregator quoteAggEntry : quoteAggregators)
    {
        String qaShortName = quoteAggEntry.getShortName();
        if (qaShortName.equals("FullBookMTAQuoteAggregatorC")) {
            quoteAggEntry.setLongName("Synthetic Full Book (Weighted Average)");
        }
        else if (qaShortName.equals("FullBookMTWQuoteAggregatorC")) {
            quoteAggEntry.setLongName("Synthetic Full Book (Multi-Price Tiers)");
        }
        else if (qaShortName.equals("FullBookMTFOKQuoteAggregatorC")) {
            quoteAggEntry.setLongName("Synthetic Full Book (Multi-Tier FOK)");
        }
        else if (qaShortName.equals("MultiTierFullAmountAggregatorC")) {
            quoteAggEntry.setLongName("Full Amount Multi-Tier (FA Multi-Tier)");
        }
        else if (qaShortName.equals("SyntheticFullAmountFullBookAggregatorC")) {
            quoteAggEntry.setLongName("Full Amount Multi-Quote (FA Multi-Quote)");
        }
        quoteAggregatorsCol.add(quoteAggEntry);
    }
	
    ExecutionMethod[] espExecutionmethods=configService.getEspExecutionMethods();
    CoverExecutionMethod[] espCoverExecutionMethods = configService.getESPCoverExecutionMethods();
    CoverExecutionMethod[] rfsCoverExecutionMethods = configService.getRFSCoverExecutionMethods();

	// AP-858 Configure Copy trade on a per broker org basis
	if( !isOldCopyTradeEnabled) {
		espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, com.integral.broker.model.CoverExecutionMethod.Copy );
	}

    RWProvisionConfig rwProvisionConfig = RWProvisionConfigManager.getInstance().getRwProvisionConfig();
    if( !rwProvisionConfig.getRiskWarehouseOrganizations().contains(org.getShortName())){
        espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, CoverExecutionMethod.Warehouse );
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }
    else if(!YMUtil.isWarehouseOptionEnabled(org.getShortName())){
        espCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( espCoverExecutionMethods, CoverExecutionMethod.Warehouse );
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }
    else if(!rwProvisionConfig.isRFSNetSpotEnabledForRiskWarehouse(org.getShortName())){
        rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Warehouse );
    }

    if(!ConfigurationFactory.getInstance().getPriceConfigurationMBean().isOrderRejectEnabled(org.getShortName())){
            rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Reject );
    }

    List<String> supportedBookNames = rwProvisionConfig.getRiskWarehouseSupportedBookNames(org.getShortName());
    supportedBookNames.remove("A"); //ABook is implicit and not configurable
    boolean isESPMultiBookPropertyEnabled = BrokerAdaptorUtil.getInstance().isMultiBookStampingEnabledForOrg(org.getShortName(),"ESP");
    boolean isRFSMultiBookPropertyEnabled = BrokerAdaptorUtil.getInstance().isMultiBookStampingEnabledForOrg(org.getShortName(),"RFS");

    ExecutionRangeType[] executionRangeType = configService.getExecutionRangeType();
    ExecutionPricingType[] executionPricingType = configService.getExecutionPricingTypes();
    PriceCheckType[] priceCheckType = configService.getPriceCheckTypes();


    ExecutionMethod[] rfsExecutionmethods=configService.getRfsExecutionMethods();
    ExecutionMethod[] autoCoverExecutionmethods =configService.getAutoCoverExecutionMethods();
    ExecutionRuleConditionType[] autoCoverExecRuleConditionType = configService.getAutoCoverExecutionRuleConditionTypes();

    PricingStrategy[] rfsPricingStrategies = PricingStrategy.getRfsPricngStrategies();
//    CustomConditionForFullFill[] customerConditionsForFullFill=configService.getCustomConditionsForFullFill();
    SpreadBias[] spreadBiases=configService.getSpreadBiases();
    Collection collSpreadBiases=new ArrayList();
    Collection priceSorters=configService.getPriceSorters();
//    Collection quoteFilters=configService.getQuoteFilters();
//    Collection<Organization> providers=configService.getProviders(org);
//	  providers = new TreeSet<Organization>(new com.integral.persistence.ShortNameComparatorC());
    Collection<Organization> providers=new TreeSet<Organization>(new com.integral.persistence.ShortNameComparatorC());
    providers.addAll(configService.getProviders(org));
    Collection<Organization> liqGroups = new TreeSet<Organization>(new com.integral.persistence.ShortNameComparatorC());
	liqGroups.addAll(configService.getLiquidityGroups(org));
    Collection<RiskManagementProfile> riskProfiles = new TreeSet<RiskManagementProfile>(new com.integral.persistence.ShortNameComparatorC());
    Collection<SpreadManagementProfile> spreadProfiles = new TreeSet<SpreadManagementProfile>(new com.integral.persistence.ShortNameComparatorC());
    if (org.getBrokerOrganizationFunction() != null) {
        if (org.getBrokerOrganizationFunction().getRiskManagementProfiles() != null) {
            riskProfiles.addAll(org.getBrokerOrganizationFunction().getRiskManagementProfiles());
        }
        if (org.getBrokerOrganizationFunction().getSpreadManagementProfiles() != null) {
            spreadProfiles.addAll(org.getBrokerOrganizationFunction().getSpreadManagementProfiles());
        }
    }

    Organization liqGroup = null;
    RiskManagementProfile espRmp = null;
    RiskManagementProfile rfsRmp = null;
    SpreadManagementProfile espSmp = null;
    SpreadManagementProfile rfsSmp = null;
    boolean profileUsed = isLgOnly.booleanValue();
	//displaying the broker organization as an LP if it is specified in IDC.IS.Providers.List property
    AdminWebServicesMBean adminConf = AdminWebServicesMBeanC.getInstance();
    String providersList=adminConf.getISProvidersList() ;
    Stream stream= PriceMakingUtil.getStream(HTTPWebUtils.getObjectID(request,"objectForm.stream.encryptedId"));
    if (TradeConfigurationFactory.getTradeConfigurationMBean().isRFQSupported(org) && stream.isRfqEnabled()){
         rfsCoverExecutionMethods = (CoverExecutionMethod[])ArrayUtils.removeElement( rfsCoverExecutionMethods, CoverExecutionMethod.Manual );
    }
    Organization proBrokerOrg = null;
    User sessionUser = UserUtil.getSessionUser();
    boolean isMainOrgUser = sessionUser.getOrganization().getShortName().equals("MAIN");
    
    Collection<String> excludedProvidersFromAdmin=  ConfigurationFactory.getInstance().getConfigurationMBean().getExcludedProvidersFromAdmin(org.getShortName());
    
    
    if(providersList != null)
    {
      StringTokenizer stkPList = new StringTokenizer(providersList,"," );
      while(stkPList.hasMoreElements())
      {
          String brokerOrg = stkPList.nextToken();
          if(org.getShortName().equals(brokerOrg) )
          {
               proBrokerOrg = org;
          }
      }
    }
    boolean doesContain = false;
    if( null != proBrokerOrg )
    {
        for(Organization orgTemp:providers)
        {
            if(orgTemp.isSameAs(proBrokerOrg))
            {
                doesContain = true;
                break;
            }
        }
    }
    if(!doesContain && null != proBrokerOrg)
    {
        providers.add( proBrokerOrg );
    }

    List<Organization> priceProvidersResponse=configuration.getOriginalSortedPriceProviders();
    if (priceProvidersResponse == null) {
        priceProvidersResponse = new ArrayList<Organization>();
    }
    List<Organization> priceProviders = null;
    boolean cfdFailoverEnabled = configuration.isCfdFailoverEnabled();

    try
    {
        priceProviders = new ArrayList<Organization>(priceProvidersResponse);

        Collection<Organization> pOrgList = priceProvidersResponse;
        liqGroup = configuration.getLiquidityGroup();
        espRmp = configuration.getEspRiskManagementProfile();
        rfsRmp = configuration.getRfsRiskManagementProfile();
        espSmp = configuration.getEspSpreadManagementProfile();
        rfsSmp = configuration.getRfsSpreadManagementProfile();
        profileUsed = profileUsed || espRmp != null || rfsRmp != null || espSmp != null || rfsSmp != null;
	}
	catch(Exception e)
    {
        e.printStackTrace();

        if ( log.isInfoEnabled() )
        {
          StringBuilder sb = new StringBuilder();
          Collection<Organization> eProviders =priceProvidersResponse;
          for(Organization provider:eProviders)
          {
               if(provider == null)
               {
                   sb.append( "Provider is null\n");
                   continue;
               }

               if(provider.getShortName() == null)
               {
                   sb.append( "Provider shortname is null\n");
                   continue;
               }

               if(provider.getShortName().equals(""))
               {
                   sb.append( "Provider shortname is Empty\n");
                   continue;
               }

               sb.append("Provider shortname:");
               sb.append(provider.getShortName());
               sb.append("\n");
          }

           log.info(sb.toString());
       }
	}

    Collection orderProviders=configuration.getOriginalOrderProviders();
    for (int count=0;count<spreadBiases.length;count++)
    {
        collSpreadBiases.add(spreadBiases[count]);
    }

    Collection swapPointsProviders=configuration.getSwapProviders();
    boolean useSwapProviders = configuration.getUseSwapProviders();
    boolean sorUsingMDSEnabled = configuration.isSorUsingMDSEnabled();
    String spreadBiasesOptions = "";
    for (int count=0;count<spreadBiases.length;count++)
    {
        SpreadBias spreadBias = spreadBiases[count];
        spreadBiasesOptions += "<option value=\""+spreadBias.name()+"\">"+spreadBias.name()+"</option>";
    }
    
    String excludeProviders=adminConf.getProvidesExcludedList();
    Collection collExcludedProviders=new ArrayList();
    StringTokenizer strTkExcludedProviders=new StringTokenizer(excludeProviders,",");
    while(strTkExcludedProviders.hasMoreElements()){
        collExcludedProviders.add(strTkExcludedProviders.nextToken());
    }
    
    
    String[] availableTimeZoneIDs = TimeZone.getAvailableIDs();
    boolean isFMADisplayEnabled =  com.integral.admin.pricemaking.PriceMBeanFactory.getInstance().getPriceMakingMBean().isFMAAdminEnabled();
    
    boolean hideFMAProviders = adminConf.hideFMAProviders(org.getShortName());
 	Collection tempPriceProviders = priceProviders;
 	if (tempPriceProviders == null) {
 	    tempPriceProviders = new ArrayList<Organization>();
 	}
    Collection tempOrderProviders  = orderProviders;
    Collection tempSwapPointsProviders = swapPointsProviders;
     if((configuration.getShortName()==null||configuration.getShortName().equals("") ) && stream.isIncludeFMAProviders()) {
 		Collection<Organization> tempAllProviders = configService.getProviders(stream.getBrokerOrganizationFunction().getOrganization());
 		List<Organization> finalFMAProviders = new ArrayList<Organization>();
 		for (Organization organization : tempAllProviders)
 		{
 			if (organization.isFMALP())
 			{
 				finalFMAProviders.add(organization);
 			}
 		}
 		tempPriceProviders = finalFMAProviders;
 		tempOrderProviders = finalFMAProviders;
 		tempSwapPointsProviders = finalFMAProviders;
 	}

    boolean showStreamAlias = adminConf.showStreamAliasInCurrencyPairConfigForOrg(org.getShortName());
    boolean showStreamAttributes = adminConf.showStreamAttributesInCurrencyPairConfigForOrg(org.getShortName());
    Map<String, String> lpStreamAliasMap = new HashMap<String, String>();
    if (showStreamAlias) {
        for (Organization organization: providers) {
            OrganizationRelationship organizationRelationship = OrganizationUtil.getOrganizationRelationshipByClassification(org, organization,
                                                                                            OrganizationRelationship.FI_LP_RELATIONSHIP);
            if (organizationRelationship != null
                    && organizationRelationship.getTakerStreamAlias() != null
                    && organizationRelationship.getTakerStreamAlias().trim().length() != 0) {
                lpStreamAliasMap.put(organization.getShortName(), organizationRelationship.getTakerStreamAlias());
            }
        }
    }

     if(!isMainOrgUser){
    	 for(String excludedProviderName:excludedProvidersFromAdmin){
    		 try{
    		 if ( StringUtilC.isNullOrEmpty( excludedProviderName ) )
    		 {
    		 	continue;
    		 }
    		 Organization excludedProvider = ReferenceDataCacheC.getInstance().getOrganization(excludedProviderName);
    		 providers.remove(excludedProvider);
    		 }catch(Exception e){
    			 LogFactory.getApplicationLog().error("Price Making config details: mask LP" + excludedProviderName + ", error : " + e.getMessage());
    				
    		 }
    	 }
    	 
     }
 	boolean displayCoverOnly = BrokerProvisionConfigFactory.getBrokerProvisionConfig().isDisplayCoverOnly(org.getShortName());
 	boolean isDisplayOrderAdminFunctionalityDisabled = RuntimeFactory.getServerRuntimeMBean().isDisplayOrderAdminFunctionalityDisabled();
 	Organization brokerOrg = stream.getBrokerOrganizationFunction().getOrganization();
    List<User> miFIDExecutingUsers  = OrganizationUtil.getMifidExecutingUsers(brokerOrg);
    User mifidExecUser = configuration.getMiFIDExecutingUser();
    List<LegalEntity> miFIDExecutingLEs  = OrganizationUtil.getActiveMiFIDExecutingLEs(brokerOrg);
    List<User> miFIDInvDecisionMkrs  = OrganizationUtil.getActiveMifidInvestmentDecisionMakers(brokerOrg);
    int coverOnMTFValue = configuration.getCoverOnMTF();
    String showMiFID = MiFIDUtils.isMiFIDEnabled(brokerOrg) && (brokerOrg.isBroker() || brokerOrg.isPrimeBroker()) ? "" : "none";
    //String showMiFIDTaker = MiFIDUtils.isMTFTakerOrg(brokerOrg) && (brokerOrg.isBroker() || brokerOrg.isPrimeBroker()) ? "" : "none";
    boolean showMiFIDTaker = MiFIDUtils.isMTFTakerOrg(brokerOrg) && (brokerOrg.isBroker() || brokerOrg.isPrimeBroker());
    
    boolean isPriceCheckEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceCheckEnabled(org.getShortName()); 
    boolean isDealerInterventionEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isDealerInterventionEnabled(org.getShortName());
    String showBps = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceMakingBpsEnabled(org.getShortName()) ? "" : "none";
    showBps="";
	String showSpreadPercent = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceMakingSpreadPercentEnabled(org.getShortName()) ? "" : "none";
    SpreadUnit espSpreadUnit = configuration.getOriginalEspSpreadUnit();
    if(espSpreadUnit == null) espSpreadUnit = SpreadUnit.Pips;
    boolean isBPS = espSpreadUnit == SpreadUnit.BPS;
    boolean isSpreadPercent=espSpreadUnit == SpreadUnit.Spread_Percent;
    SpreadUnit rfsFwdSpreadUnit = configuration.getOriginalRfsForwardSpreadUnit();
    if(rfsFwdSpreadUnit == null) rfsFwdSpreadUnit = SpreadUnit.Pips;
    boolean isRfsFwdPips = rfsFwdSpreadUnit == SpreadUnit.Pips;
    boolean isRfsFwdBPS = rfsFwdSpreadUnit == SpreadUnit.BPS;
    boolean isRfsFwdPercent = rfsFwdSpreadUnit == SpreadUnit.PFP;
    SpreadUnit rfsSpotSpreadUnit = configuration.getOriginalRfsSpotSpreadUnit();
    if(rfsSpotSpreadUnit == null) rfsSpotSpreadUnit = SpreadUnit.Pips;
    boolean isRfsSpotBPS = rfsSpotSpreadUnit == SpreadUnit.BPS;
    String showSynCrossPointsUI = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isSyntheticCrossPointsEnabled(org.getShortName()) ? "" : "none";
    boolean isSpotAndSwapEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceSSPUsingSwapEnabled(org.getShortName())
    		|| ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceOTUsingSwapEnabled(org.getShortName())
    		|| ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceNDFAndNDFSwapUsingSpotAndSwapEnabled(org.getShortName())
    		|| ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceSwapUsingSpotAndSwapEnabled(org.getShortName());
	boolean isProviderPointsValidationEnabled = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isBrokerRFSProviderPointsValidationEnabled ( org.getShortName() );
    boolean isESPUIOnly = ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceMakingESPUIOnlyEnabled(org.getShortName());
    boolean isMultiTierRfsSpotSpreads = configuration.isMultiTierRfsSpotSpreads();
    String showRFS =  "";
    String useProfile = adminConf.isBrokerCurrencyPairConfigurationEnableProfileConfiguration(brokerOrg.getShortName()) ? "" : "none";
    String rfsMultiTierSpreadTableIdStyle = !isMultiTierRfsSpotSpreads ? "style=\'display: none\'" : "";
    String providersLabel ="ESP and RFS Providers";

    if(isESPUIOnly == true){
        isSpotAndSwapEnabled = false;
        showRFS ="none";
        rfsMultiTierSpreadTableIdStyle = "style=\'display: none\'";
        showSynCrossPointsUI ="none";
        showMiFID ="none";
        providersLabel ="ESP Providers";
    }
    boolean mdsOnlyFeatureEnabled = PricingConfigurationFactory.getPricingConfigurationMBean().isPriceFromMDSEnabled(org.getShortName());
    String mdsOnlyRadioType = mdsOnlyFeatureEnabled ? "radio" : "hidden";
    String priceSourceRadioName = mdsOnlyFeatureEnabled ? "pricingSourceRadio" : "pricingSourceHidden";
    int streamMinMatchDelay = stream.getMinimumMatchDelay();
    int streamMaxMatchDelay = stream.getMaximumMatchDelay();
%>
 <idcLogic:iterate id="currProvider" name="ObjectActionForm" type="Organization" collection="<%=liqGroups%>">
 <%
      if(!(collExcludedProviders.contains(currProvider.getShortName())) && UserUtil.isSysAdmin(loginUser)) {
      %>
         <script>lgProvidersList.add( new Option( "<%=((Organization)currProvider).getShortName()%>", "<%=((Organization)currProvider).getEncryptedObjectID()%>"));</script>
     <% }
 %>
 </idcLogic:iterate>

 <idcLogic:iterate id="currProvider" name="ObjectActionForm" type="Organization" collection="<%=providers%>">
 <%
      if(!(collExcludedProviders.contains(currProvider.getShortName()))  &&  (UserUtil.isSysAdmin(loginUser) || !(!isFMADisplayEnabled && ((Organization)currProvider).isFMALP()) ))
      {
        String orgName = ((Organization)currProvider).getShortName();
        String displayName = orgName;
        if (showStreamAlias && lpStreamAliasMap.get(orgName) != null) {
            displayName = lpStreamAliasMap.get(orgName) + " (" + orgName;
            if (showStreamAttributes && orgDefaultLe != null) {
                String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currProvider));
                if (streamAttributes.trim().isEmpty()) {
                    displayName = displayName + ")";
                }
                else {
                    displayName = displayName + "," + streamAttributes + ")";
                }
            }
            else {
                displayName = displayName + ")";
            }
        }
        else if (showStreamAttributes && orgDefaultLe != null) {
            String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currProvider));
            if (!streamAttributes.trim().isEmpty()) {
                displayName = displayName + " (" + streamAttributes + ")";
            }
        }
      %>
         <script>aggProvExclude.add( new Option( "<%=displayName%>","<%=((Organization)currProvider).getEncryptedObjectID()%>"));</script>

 <%
 		
 	if (currProvider.isDisplayOrderProvider()) {
%>
	<script>aggProvExcludeDO.push({"name":"<%=((Organization)currProvider).getShortName()%>","id":"<%=((Organization)currProvider).getEncryptedObjectID()%>"});</script>
<% }
 	else {
 %>
 <script>aggProvExcludeNonDO.push({"name":"<%=((Organization)currProvider).getShortName()%>","id":"<%=((Organization)currProvider).getEncryptedObjectID()%>"});</script>
 <% 
      
 	}  } %>
 </idcLogic:iterate>
 
 <idcLogic:iterate id="currPriceProvider" name="ObjectActionForm" collection="<%=tempPriceProviders%>">
     <%if ( UserUtil.isSysAdmin(loginUser) || providers.contains((Organization)currPriceProvider)){
        if (providers.contains((Organization)currPriceProvider)){
            if (!UserUtil.isSysAdmin(loginUser) && !isFMADisplayEnabled  && ((Organization)currPriceProvider).isFMALP()) { %>
                <input type="hidden" name="selectAggProvIncludeHidden" id="selectAggProvInclude<%=((Organization)currPriceProvider).getShortName()%>" value="<%=((Organization)currPriceProvider).getEncryptedObjectID()%>"/>
                <% }else {
                    String orgName = ((Organization)currPriceProvider).getShortName();
                    String displayName = orgName;
                    if (showStreamAlias && lpStreamAliasMap.get(orgName) != null) {
                        displayName = lpStreamAliasMap.get(orgName) + " (" + orgName;
                        if (showStreamAttributes && orgDefaultLe != null) {
                            String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currPriceProvider));
                            if (streamAttributes.trim().isEmpty()) {
                                displayName = displayName + ")";
                            }
                            else {
                                displayName = displayName + "," + streamAttributes + ")";
                            }
                        }
                        else {
                            displayName = displayName + ")";
                        }
                    }
                    else if (showStreamAttributes && orgDefaultLe != null) {
                        String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currPriceProvider));
                        if (!streamAttributes.trim().isEmpty()) {
                            displayName = displayName + " (" + streamAttributes + ")";
                        }
                    }
                %>
            <script>
                var opt = new Option( "<%=displayName%>","<%=((Organization)currPriceProvider).getEncryptedObjectID()%>");
                aggProvInclude.add(opt);

             </script>
        <%}}}%>
 </idcLogic:iterate>

 <idcLogic:iterate id="currProvider" name="ObjectActionForm" collection="<%=providers%>">
 <%
      if(!(collExcludedProviders.contains(( (Organization)currProvider).getShortName())) &&  (UserUtil.isSysAdmin(loginUser) || !(!isFMADisplayEnabled  && ((Organization)currProvider).isFMALP())))
      {
          String orgName = ((Organization)currProvider).getShortName();
          String displayName = orgName;
          if (showStreamAlias && lpStreamAliasMap.get(orgName) != null) {
              displayName = lpStreamAliasMap.get(orgName) + " (" + orgName;
              if (showStreamAttributes && orgDefaultLe != null) {
                  String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currProvider));
                  if (streamAttributes.trim().isEmpty()) {
                      displayName = displayName + ")";
                  }
                  else {
                      displayName = displayName + "," + streamAttributes + ")";
                  }
              }
              else {
                  displayName = displayName + ")";
              }
          }
          else if (showStreamAttributes && orgDefaultLe != null) {
              String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currProvider));
              if (!streamAttributes.trim().isEmpty()) {
                  displayName = displayName + " (" + streamAttributes + ")";
              }
          }
      %>
         <script>orderProvExclude.add( new Option( "<%=displayName%>","<%=((Organization)currProvider).getEncryptedObjectID()%>"));</script>
 <%   }
  %>
 </idcLogic:iterate>
 <idcLogic:iterate id="currOrderProvider" name="ObjectActionForm" collection="<%=tempOrderProviders%>">
     <%if (UserUtil.isSysAdmin(loginUser) || providers.contains((Organization)currOrderProvider)){
       if (providers.contains((Organization)currOrderProvider)){
          if (!UserUtil.isSysAdmin(loginUser) && !isFMADisplayEnabled  && ((Organization)currOrderProvider).isFMALP()){ %>
            <input type="hidden" name="selectOrderProvIncludeHidden" id="selectOrderProvInclude<%=((Organization)currOrderProvider).getShortName()%>" value="<%=((Organization)currOrderProvider).getEncryptedObjectID()%>"/>
          <% } else {
              String orgName = ((Organization)currOrderProvider).getShortName();
              String displayName = orgName;
              if (showStreamAlias && lpStreamAliasMap.get(orgName) != null) {
                  displayName = lpStreamAliasMap.get(orgName) + " (" + orgName;
                  if (showStreamAttributes && orgDefaultLe != null) {
                      String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currOrderProvider));
                      if (streamAttributes.trim().isEmpty()) {
                          displayName = displayName + ")";
                      }
                      else {
                          displayName = displayName + "," + streamAttributes + ")";
                      }
                  }
                  else {
                      displayName = displayName + ")";
                  }
              }
              else if (showStreamAttributes && orgDefaultLe != null) {
                  String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currOrderProvider));
                  if (!streamAttributes.trim().isEmpty()) {
                      displayName = displayName + " (" + streamAttributes + ")";
                  }
              } %>
            <script>
              var opt =  new Option( "<%=displayName%>","<%=((Organization)currOrderProvider).getEncryptedObjectID()%>");
              orderProvInclude.add(opt);
            </script>
      <%}}}%>
 </idcLogic:iterate>

 <idcLogic:iterate id="currProvider" name="ObjectActionForm" collection="<%=providers%>">
  <%
       if(!(collExcludedProviders.contains((   (Organization)currProvider).getShortName())) &&  (UserUtil.isSysAdmin(loginUser) || !(!isFMADisplayEnabled  && ((Organization)currProvider).isFMALP())))
       {
           String orgName = ((Organization)currProvider).getShortName();
           String displayName = orgName;
           if (showStreamAlias && lpStreamAliasMap.get(orgName) != null) {
               displayName = lpStreamAliasMap.get(orgName) + " (" + orgName;
               if (showStreamAttributes && orgDefaultLe != null) {
                   String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currProvider));
                   if (streamAttributes.trim().isEmpty()) {
                       displayName = displayName + ")";
                   }
                   else {
                       displayName = displayName + "," + streamAttributes + ")";
                   }
               }
               else {
                   displayName = displayName + ")";
               }
           }
           else if (showStreamAttributes && orgDefaultLe != null) {
               String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currProvider));
               if (!streamAttributes.trim().isEmpty()) {
                   displayName = displayName + " (" + streamAttributes + ")";
               }
           }
       %>
          <script>swapPointProvExclude.add( new Option( "<%=displayName%>","<%=((Organization)currProvider).getEncryptedObjectID()%>"));</script>
  <%   }
   %>
  </idcLogic:iterate>
  <idcLogic:iterate id="currSwapPointsProvider" name="ObjectActionForm" collection="<%=tempSwapPointsProviders%>">
      <%if (providers.contains((Organization)currSwapPointsProvider)){%>
       <%
        if (!UserUtil.isSysAdmin(loginUser) && !isFMADisplayEnabled  && ((Organization)currSwapPointsProvider).isFMALP())
        { %>
            	<input type="hidden" name="selectSwapPointsProvIncludeHidden" id="selectSwapPointsProvInclude<%=((Organization)currSwapPointsProvider).getShortName()%>" value="<%=((Organization)currSwapPointsProvider).getEncryptedObjectID()%>"/>
           	<% } else {
           	    String orgName = ((Organization)currSwapPointsProvider).getShortName();
                String displayName = orgName;
                if (showStreamAlias && lpStreamAliasMap.get(orgName) != null) {
                    displayName = lpStreamAliasMap.get(orgName) + " (" + orgName;
                    if (showStreamAttributes && orgDefaultLe != null) {
                        String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currSwapPointsProvider));
                        if (streamAttributes.trim().isEmpty()) {
                            displayName = displayName + ")";
                        }
                        else {
                            displayName = displayName + "," + streamAttributes + ")";
                        }
                    }
                    else {
                        displayName = displayName + ")";
                    }
                }
                else if (showStreamAttributes && orgDefaultLe != null) {
                    String streamAttributes = PriceMakingUtil.getStreamAttributes(orgDefaultLe, ((Organization)currSwapPointsProvider));
                    if (!streamAttributes.trim().isEmpty()) {
                        displayName = displayName + " (" + streamAttributes + ")";
                    }
                }
           	%>
            <script>
            var opt =  new Option( "<%=displayName%>","<%=((Organization)currSwapPointsProvider).getEncryptedObjectID()%>");
            swapPointProvInclude.add(opt);
          </script>
      <%} }%>
  </idcLogic:iterate>
<script>
  <% Iterator qaIt=quoteAggregators.iterator();
    while(qaIt.hasNext()){
        Object quoteAgg=qaIt.next();
        if(quoteAgg instanceof com.integral.broker.aggregate.MultiTierQuoteAggregator){%>
            multiTier.put('<%=((Calculator)quoteAgg).getShortName()%>','<%=quoteAgg.getClass().toString()%>');
      <%}
    }%>
</script>
<style type="text/css">
	.spread	{
		color:#FF0033;
		font-family: tahoma, verdana, arial, sans-serif;
		font-size:8pt;
		font-style:normal;
		font-weight:normal;
	}
</style>

<table style="width:100%" cellpadding="0" cellspacing="0" class="outl">
	<tr>
		<td class="stl2"  nowrap>Price Making and Execution&nbsp;&nbsp;
		    <a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/StreamsEditCcyPairsEditCreate.html#csh_pricemaking','Admin Portal Help')"><img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a>
		</td>
		<td class="stl3" nowrap>&nbsp;</td>
		<td class="stl4" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
		<td class="stl8" nowrap>&nbsp;<span id="scopeIndPME">to Scope</span>&nbsp;&nbsp;</td>
	</tr>
</table>
<table id="PriceMakingPanel" cellpadding="0" cellspacing="0" class="outl2" style="width:1638px; display: <%/*none*/%>;">
<tr  id="PMEFirstRow">
	<td class="f" width="100"></td>
    <td class="f" width="50"></td>
	<td class="f" width="100"></td>
	<td class="f" width="40"></td>
	<td class="f" width="100"></td>
	<td class="f" width="800"></td>
	<td class="f" width="1"></td>
</tr>

<tr style="display: <%=showRFS %>">
    <td  class="f3" nowrap><strong>Override Stream Channels</strong></td>
	<td  class="f3" nowrap><input type="checkbox" name="overrideFlag" id="overrideFlag"  value="<%=overrideChannel%>" <%=overrideChannel.booleanValue()?"checked":""%> class="ft" onclick="changeButton();updateChannels();">
       <input type="hidden" name="workflowMessage.overrideFlag" id="workflowMessage.overrideFlag" value="<%=overrideChannel%>"/>
        <script>registerChange('workflowMessage.overrideFlag','overrideFlag');</script></td>
    <td colspan="3" class="f3" nowrap><input type="checkbox" name="espEnabled"  id="espEnabled" <%=espEnabled.booleanValue()?"checked":""%> class="ft"  <%=overrideChannel.booleanValue()?"":"disabled=true"%> onclick='changeButton();updateChannels();'>ESP
    <input type="hidden" name="workflowMessage.espEnabled" id="workflowMessage.espEnabled"  value="<%=espEnabled%>"/>
        <script>registerChange('workflowMessage.espEnabled','espEnabled');</script>&nbsp;
<input type="checkbox" name="rfsEnabled"  id="rfsEnabled" <%=rfsEnabled.booleanValue()?"checked":""%> class="ft" <%=overrideChannel.booleanValue()?"":"disabled=true"%>  onclick='changeButton();updateChannels();'>RFS
<input type="hidden" name="workflowMessage.rfsEnabled" id="workflowMessage.rfsEnabled" value="<%=rfsEnabled%>"/>
        <script>registerChange('workflowMessage.rfsEnabled','rfsEnabled');</script>&nbsp;
    <span id="channelDesc"></span>&nbsp;&nbsp;(Stream Default:&nbsp;<%
    if(isESPUIOnly){
    out.println("ESP only");
    }
    else if (stream.isESPEnabled() && stream.isRFSEnabled()){
        out.println("ESP and RFS");
    } else if (stream.isESPEnabled()){
        out.println("ESP only");
    } else if (stream.isRFSEnabled()){
        out.println("RFS only");
    } else {
        out.println("None enabled");
    }
%>)
</td>
    <td class="f" nowrap></td>
	<td class="f" ><input type="checkbox" name="workflowMessage.allOverrideStream" onclick="allClick();" desc="Override Stream" scopeIndicator="scopeIndPME"></td>
</tr>

<%
    marketDataSet = marketDataSet == null? OrganizationUtil.getMarketDataSet(org) : marketDataSet;
    Collection<FXMarketDataSet> colMDS = OrganizationUtil.getMarketDataSets( org,'A', true, true, null );
    pageContext.setAttribute("MDSES",colMDS);
%>
    <tr style="display: <%=useProfile%>">
        <td class="f" colspan="6" nowrap>&nbsp;</td>
        <td class="f" nowrap></td>
    </tr>

    <tr id='lgList' style="display: <%=useProfile%>">
        <td class="f3"><strong>Liquidity Group</strong></td>
        <td class="f3" colspan="5" nowrap><idcHtml:select name="ObjectActionForm" property="lgp" styleId="lgp" value='""' onchange="changeButton();onLgChange();">
            <option value="null" "selected">None</option>
            <script>registeredFields.add('lgp')</script>
            <idcLogic:iterate id="lg" collection="<%=liqGroups%>" type="com.integral.user.OrganizationC">
                <option value="<%=lg.getEncryptedObjectID()%>" <%=(liqGroup!= null && lg==liqGroup)?"selected":""%>><%=lg.getLongName()%></option>
            </idcLogic:iterate>
            </idcHtml:select>
        </td>
        <td class="f">
          <input type="checkbox" name="allLiqGrp" onclick="allClick();" desc="Liquidity Groups" scopeIndicator="scopeIndPME" value=>
        </td>
    </tr>

    <tr id='espRmpList' style="display: <%=useProfile%>">
        <td class="f3"><strong>ESP Risk Management Profile</strong></td>
        <td class="f3" colspan="5" nowrap><idcHtml:select name="ObjectActionForm" property="ermp" styleId="ermp" value='""' onchange="changeButton();onEspRmChange();">
            <option value="null" "selected">None</option>
            <script>registeredFields.add('ermp')</script>
            <idcLogic:iterate id="espRp" collection="<%=riskProfiles%>" type="com.integral.broker.model.RiskManagementProfileC">
                <option value="<%=espRp.getEncryptedObjectID()%>" <%=(espRmp!= null && espRp==espRmp)?"selected":""%>><%=espRp.getLongName()%></option>
            </idcLogic:iterate>
            </idcHtml:select>
        </td>
        <td class="f">
          <input type="checkbox" name="allEspRiskMgmtProfile" onclick="allClick();" desc="ESP Risk Management Profiles" scopeIndicator="scopeIndPME">
        </td>
    </tr>

    <tr id='rfsRmpList' style="display: <%=useProfile%>">
        <td class="f3"><strong>RFS Risk Management Profile</strong></td>
        <td class="f3" colspan="5" nowrap><idcHtml:select name="ObjectActionForm" property="rrmp" styleId="rrmp" value='""' onchange="changeButton();onRfsRmChange();">
            <option value="null" "selected">None</option>
            <script>registeredFields.add('rrmp')</script>
            <idcLogic:iterate id="rfsRp" collection="<%=riskProfiles%>" type="com.integral.broker.model.RiskManagementProfileC">
                <option value="<%=rfsRp.getEncryptedObjectID()%>" <%=(rfsRmp!= null && rfsRp==rfsRmp)?"selected":""%>><%=rfsRp.getLongName()%></option>
            </idcLogic:iterate>
            </idcHtml:select>
        </td>
        <td class="f">
          <input type="checkbox" name="allRfsRiskMgmtProfile" onclick="allClick();" desc="RFS Risk Management Profiles" scopeIndicator="scopeIndPME">
        </td>
    </tr>

    <tr id='espSmpList' style="display: <%=useProfile%>">
        <td class="f3"><strong>ESP Spread Management Profile</strong></td>
        <td class="f3" colspan="5" nowrap><idcHtml:select name="ObjectActionForm" property="esmp" styleId="esmp" value='""' onchange="changeButton();onEspSmChange();">
            <option value="null" "selected">None</option>
            <script>registeredFields.add('esmp')</script>
            <idcLogic:iterate id="espSp" collection="<%=spreadProfiles%>" type="com.integral.broker.model.SpreadManagementProfileC">
                <option value="<%=espSp.getEncryptedObjectID()%>" <%=(espSmp!= null && espSp==espSmp)?"selected":""%>><%=espSp.getLongName()%></option>
            </idcLogic:iterate>
            </idcHtml:select>
        </td>
        <td class="f">
          <input type="checkbox" name="allEspSpreadMgmtProfile" onclick="allClick();" desc="ESP Spread Management Profiles" scopeIndicator="scopeIndPME">
        </td>
    </tr>

    <tr id='rfsSmpList' style="display: <%=useProfile%>">
        <td class="f3"><strong>RFS Spread Management Profile</strong></td>
        <td class="f3" colspan="5" nowrap><idcHtml:select name="ObjectActionForm" property="rsmp" styleId="rsmp" value='""' onchange="changeButton();onRfsSmChange();">
            <option value="null" "selected">None</option>
            <script>registeredFields.add('rsmp')</script>
            <idcLogic:iterate id="rfsSp" collection="<%=spreadProfiles%>" type="com.integral.broker.model.SpreadManagementProfileC">
                <option value="<%=rfsSp.getEncryptedObjectID()%>" <%=(rfsSmp!= null && rfsSp==rfsSmp)?"selected":""%>><%=rfsSp.getLongName()%></option>
            </idcLogic:iterate>
            </idcHtml:select>
        </td>
        <td class="f">
          <input type="checkbox" name="allRfsSpreadMgmtProfile" onclick="allClick();" desc="RFS Spread Management Profile" scopeIndicator="scopeIndPME">
        </td>
    </tr>

    <tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
<tr style="display: <%=showRFS %>">
    <td class="f3" nowrap><strong>Market Data Set</strong>&nbsp;&nbsp;
    <a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/MarketData/MarketDataEdit.html#_top','Admin Portal Help')">
          <img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a>
    </td>
    <td  class="f" colspan="4" nowrap>
    
        <select id="marketDataSet" name="workflowMessage.marketDataSet"  class="ft" onchange="changeButton()">
            <idcLogic:iterate id="mds" name="MDSES" type="com.integral.finance.marketData.fx.FXMarketDataSet">
                <option value="<%=mds.getEncryptedObjectID()%>" <%=(null!=marketDataSet && marketDataSet.isSameAs(mds))?"selected":""%>><%=mds.getShortName()%></option>
            </idcLogic:iterate>
        </select>&nbsp;
<%
	if ( marketDataSet != null )
	{
%>
    <a href="javascript:openDetailWindow('/admin/Forward.do?forwardURI=Admin.MarketDataSet.Edit&objectForm.objectType=com.integral.finance.marketData.fx.FXMarketDataSetC&objectForm.encryptedId=<%=marketDataSet.getEncryptedObjectID()%>&fromWhere=Config', 'Market Data Set')">
        view/edit
    </a>
<%
	}
%></td>
    <td class="f"  nowrap></td>
    <td class="f"><input type="checkbox" name="workflowMessage.allMDS" onclick="allClick();" desc="Market Data Set" scopeIndicator="scopeIndPME"></td>
</tr>
    <tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
 <%   
   	if ( RuntimeFactory.getServerRuntimeMBean ().isStreamingNonSpotEnabled ( org ) )
	{
   %>
    <tr style="display: <%=showRFS %>">
	    <td  class="f3" wrap><strong>Use Market Data Set for Streaming Outright points</strong></td>
		<td  class="f3" colspan="4" nowrap>
			<input type="checkbox" name="workflowMessage.sorUsingMDSEnabled"  value="<%=sorUsingMDSEnabled%>" id="sorUsingMDSEnabled" <%=sorUsingMDSEnabled?"checked":""%> onclick='changeButton();updateSorUsingMDSEnabled();'/>
		</td>
	
	    <td class="f" nowrap></td>
		<td class="f" ></td>
	</tr>
    <tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap>
        </td>
    </tr>
    
    <%
	}
		if ( prorataForwardEnabled )
		{
	 %>
   <tr style="display: <%=showRFS %>">
     <td class="f3" nowrap><strong>Prorata Forward Market Data Set</strong></td>
     <td  class="f" nowrap>
             <select id="prorataForwardMarketDataSet" name="workflowMessage.prorataForwardMarketDataSet"  class="ft" onchange="changeButton()">
				<option value="" <%=prorataForwardMarketDataSet != null ? "" : "selected"%>><idcBean:message key="SelectOption1" /></option>
                <idcLogic:iterate id="mds" name="MDSES" type="com.integral.finance.marketData.fx.FXMarketDataSet">
                                <option value="<%=mds.getEncryptedObjectID()%>" <%=(null!=prorataForwardMarketDataSet && prorataForwardMarketDataSet.isSameAs(mds))?"selected":""%>><%=mds.getShortName()%></option>
                </idcLogic:iterate>
             </select>
     <%
     	if ( prorataForwardMarketDataSet != null )
     	{
     %>
         <a href="javascript:openDetailWindow('/admin/Forward.do?forwardURI=Admin.MarketDataSet.Edit&objectForm.objectType=com.integral.finance.marketData.fx.FXMarketDataSetC&objectForm.encryptedId=<%=prorataForwardMarketDataSet.getEncryptedObjectID()%>&fromWhere=Config', 'Market Data Set')">
             view/edit
         </a>
     <%
     	}
     %></td>
        <td class="f" colspan="4" nowrap>&nbsp;</td>
		<td class="f" nowrap>&nbsp;</td>
  </tr>
	<%
		}
	%>
	 <tr style="display: <%= showSynCrossPointsUI %>;">
    <td class="f3" nowrap>Enable Synthetic Cross Points</td>
    <td class="f" nowrap><input type="checkbox" name="workflowMessage.synCrossPoints" id="synCrossPoints" value="<%=configuration.isSyntheticCrossPointsEnabled()%>" <%=configuration.isSyntheticCrossPointsEnabled()?"checked":"" %> onchange="javascript:enableDisableVehicleCurrency();javascript:updateCheckboxValue('synCrossPoints');changeButton()" class="ft"></td>
    <td class="f3" nowrap>Vehicle Currency</td>
    <td  class="f" colspan="2" nowrap>
    
        <select id="vehicleCurrency" name="workflowMessage.vehicleCurrency"  class="ft" onchange="changeButton()">
        </select>
    </td>
    <td class="f"  nowrap></td>
     <td class="f"  nowrap></td>
</tr>

<tr style="display: <%= showSynCrossPointsUI %>;">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>

    <tr>
	<td class="f3"><strong>Price Priority</strong></td>
        <td class="f3" colspan="5" nowrap><idcHtml:select name="ObjectActionForm" property="priceSorter" styleId="priceSorter" value='<%=priceSorter==null?"":((ProxyPriceSorterC)priceSorter).getShortName() %>'>
                   <script>registeredFields.add('priceSorter')</script>
                     <idcLogic:iterate id="priceSort" collection="<%= priceSorters %>" type="com.integral.broker.sort.ProxyPriceSorterC">
                            <idcHtml:option value="<%=priceSort.getShortName()%>">
                                <idcBean:write name="priceSort" property="longName"/>
                        </idcHtml:option>
                    </idcLogic:iterate>
                </idcHtml:select>
		</td>
		<td class="f">
		  <input type="checkbox" name="workflowMessage.allPricePri" onclick="allClick();" desc="Price Priority" scopeIndicator="scopeIndPME">
		</td>
	</tr>
		<tr>
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr>
        <td class="f3"><strong>Allow Negative Spreads</strong></td>
        <td class="f" colspan="4" nowrap>
            <input type="checkbox" name="workflowMessage.isNegativeSpreadEnabled" id="isNegativeSpreadEnabled" value="<%=configuration.isNegativeSpreadEnabled()%>" <%=configuration.isNegativeSpreadEnabled()?"checked":"" %> onclick="changeButton();updateNegativeSpreadEnabled();" class="ft">
        </td>
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr>
	<td class="f3"><strong>Spread Priority</strong></td>
        <td class="f3" colspan="5"><idcHtml:select name="ObjectActionForm" property="spreadPri"  styleId="spreadPri" value='<%=spreadPri==null?"":((SpreadPriority)spreadPri).name() %>' onchange='changeButton()'>
                <script>registeredFields.add('spreadPri')</script>
                  <idcLogic:iterate id="so" collection="<%=SpreadPriority.getSpreadPriorities()%>" type="com.integral.broker.model.SpreadPriority">
                         <idcHtml:option value="<%=so.name()%>">
                             <idcBean:message key="<%=so.getShortName()%>" />
                     </idcHtml:option>
                 </idcLogic:iterate>
            </idcHtml:select>
        </td>
        <td class="f"><input type="checkbox" name="workflowMessage.allSpreadPri" onclick="allClick();" desc="Price Priority" scopeIndicator="scopeIndPME"></td>
    </tr>
	    <tr>
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
		<tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr>
		<td class="f" colspan="6"  style="font-size: 9pt;font-weight: bold;" nowrap>ESP Rules</td><td class="f" nowrap></td>
    </tr>
	<tr>
		<%--<td class="f" nowrap>price sorters in DB: [<%=cs.getQuoteAggregators()%>]</td> --%>
        <td class="f3" nowrap><strong>Price Making Method</strong></td>
		<td class="f3" colspan="5" nowrap>
			<script>
				function validateExecutionRulesForExternalPricingSource() {
                    clickFromPricingSource(true);
                }

                function clickFromPricingSource(fromOnLoad, errArray) {
                    var fromLPs = $("pricingSource_LPs").checked;
                    var fromMDSOnly = $("pricingSource_OnlyMDS").checked;
                    if (!fromLPs && !fromMDSOnly) {
                        // all exec rules must be no cover, RFS must be PriceAtSize
                        var errs = "";
                        var badCoverESPs = collectExecRulesWhere("esp", badCover);
                        if (badCoverESPs.length > 0) errs = "ESP rules must not be 'Cover': " + badCoverESPs +".";

                        var badCopyESPs = collectExecRulesWhere("esp", badCopy);
                        if (badCopyESPs.length > 0) errs += "\nESP rules must not be 'Copy': " + badCopyESPs +".";

                        var badCoverRFSs = collectExecRulesWhere("rfs", badRFSCover);
                        if (badCoverRFSs.length > 0) errs +=  "\nRFS rules must not be 'Cover': " + badCoverRFSs +".";

                        var badPricingTypeRFSs = collectExecRulesWhere("rfs", badRFSPricingType);
                        if (badPricingTypeRFSs.length > 0) errs += "\nRFS rules must be 'Price at Size': " + badPricingTypeRFSs + ".";

                        if (errs != "") {
                            errs = errs + "\n\nPlease adjust the indicated rules before Sourcing from Excel.";
                            if(errArray != undefined) return errArray[errArray.length] = errs; // used by save validation
                            alert(errs);
                            fromLPs = true;
                            $("pricingSource_LPs").checked = true;
                            if (fromOnLoad)
                                changeButton();
                            else
                                return;
                        }
                    }
                    // we didn't bail early so adjust quoteAggregator and pricingSource
                    var qASelect = $("quoteAggregator");
                    for (var oi=0; oi < qASelect.length; oi++) {
                        if (qASelect.options[ oi ].value != "MultiTierWorstQuoteAggregator")
                            qASelect.options[ oi ].disabled = !fromLPs;
                        else if (!fromLPs && qASelect.selectedIndex != oi) {
                            qASelect.selectedIndex = oi;
                            quoteAggregatorChanged();
                        }
                    }
                    $("pricingSource").value = (fromLPs ? "" : (fromMDSOnly ? "MDS":"excel"));
                }

				function collectExecRulesWhere( type, cond ) {
    				var rids = [];
					var rowId = 0;
					var cemElem =$(type + "SortOrder_" + rowId); // existence check
					while( cemElem != null ) {
						if( cond( type, rowId ) ) rids[rids.length] = 1 + new Number(rowId);
						rowId++;
						cemElem =$(type + "SortOrder_" + rowId);
					}
					return rids;
				}

				// conditions that can be passed to collectExecRulesWhere
				function erAttVal( type, rowId, att, reqVal ) {
					var elem = $(type + att + rowId);
					var val = elem.type == "checkbox" ? elem.checked : elem.value;
					return val == reqVal;
				}
				function badCover( type, rowId ) {
					return erAttVal( type, rowId, "CoverExecutionMethod_", "<%=CoverExecutionMethod.Cover.name()%>");
				}
				function badCopy( type, rowId ) {
					return erAttVal( type, rowId, "CoverExecutionMethod_", "<%=CoverExecutionMethod.Copy.name()%>");
				}
				function badRFSCover( type, rowId ) {
					return erAttVal( "rfs", rowId, "IsSpotMDSEnabled_", true ) && badCover( "rfs", rowId);
				}
				function badRFSPricingType( type, rowId ) {
					return erAttVal( "rfs", rowId, "IsSpotMDSEnabled_", true ) && !erAttVal( "rfs", rowId, "PricingType_", "<%=ExecutionPricingType.PriceAtSize.getName()%>" );
				}
				function isRFSESPSpreadable( type, rowId ) {
					return !erAttVal( "rfs", rowId, "IsRFSEnabled_", true ) && erAttVal( "rfs", rowId, "IsSpotMDSEnabled_", true );
				}
				function isRFSAggESPBPFOK( type, rowId ) {
					var v = isRFSESPSpreadable( type, rowId )
							&& erAttVal( "rfs", rowId, "PricingType_", "<%=ExecutionPricingType.BestPriceFOK.getName()%>" );
					return v;
				}
				function isRFSAggESPMT( type, rowId ) {
					var v = isRFSESPSpreadable( type, rowId )
							&& !erAttVal( "rfs", rowId, "PricingType_", "<%=ExecutionPricingType.BestPriceFOK.getName()%>" );
					return v;
				}
			</script>
		<input type="radio" name="pricingSourceRadio" id="pricingSource_LPs" value="true" <%=(null == pricingSource || "".equals(pricingSource)) ? "checked" :""%> onclick="clickFromPricingSource(); changeButton();"/>Aggregate from LPs
		&nbsp;
             <%--<idcHtml:select name="ObjectActionForm" property="objectForm.quoteAggregator" style="ft" >--%>
           <idcHtml:select name="ObjectActionForm" property="quoteAggregator"  styleId="quoteAggregator"  value='<%=quoteAggregator==null?"":((Calculator)quoteAggregator).getShortName() %>' onchange='quoteAggregatorChanged();changeButton()'>
                <script>registeredFields.add('quoteAggregator')</script>
                 <idcLogic:iterate id="quoteAgg" collection="<%= quoteAggregatorsCol %>">
                        <idcHtml:option value="<%= ((Calculator)quoteAgg).getShortName()%>">
                               <%= ((Calculator)quoteAgg).getLongName()%>
                        </idcHtml:option>
				</idcLogic:iterate>
            </idcHtml:select>
		&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
		<input type="radio" name="pricingSourceRadio" id="pricingSource_MDS" value="false" <%=(!(null == pricingSource || "".equals(pricingSource)) && "excel".equals(pricingSource)) ? "checked" :""%> onclick="clickFromPricingSource(); changeButton();"/>Source From Excel (Multi-Price Tiers)
		<input type="<%= mdsOnlyRadioType %>" name="<%= priceSourceRadioName %>" id="pricingSource_OnlyMDS" value="false" <%=(!(null == pricingSource || "".equals(pricingSource)) && "MDS".equals(pricingSource)) ? "checked" :""%> onclick="clickFromPricingSource(); changeButton();"/><% if(mdsOnlyFeatureEnabled){%>Source From MDS<%}%>
		<input type="hidden" id="pricingSource" name="workflowMessage.pricingSource"  class="ft" onchange="changeButton()" value="<%=pricingSource%>">
		&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
		</td>
	<td class="f"><input type="checkbox" name="workflowMessage.allPriceMMeth" onclick="allClick();" desc="Price Making Method" scopeIndicator="scopeIndPME"></td>
    </tr>
	<%
		boolean isFFDisabled= false;
		try
		{
			com.integral.marketmaker.rule.execution.FullFill mmfill = com.integral.maker.service.MarketMakerFactory.getInstance().getExecutionService().queryFullFill(org.getShortName());
			isFFDisabled = (mmfill.getEnabled() == com.integral.marketmaker.rule.Rule.OFF);
		}
		catch(Exception e)
		{
			//ignore
		}

	%>
	<tr>
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap>
        </td>
    </tr>
  
    <tr>
        <td id="espExcnTag" class="f3" colspan="6" nowrap><strong>Execution Method</strong></br>
             <table id="espExecutionConditions"  cellpadding="0" border="0" cellspacing="1" style="width:1636px;background-color: #999;" >
				<!--<tr>
					<td class="stl2" style="text-align:center" colspan="5" nowrap>Matching Criteria</td>
					<td class="stl2" style="text-align:center" colspan="3" nowrap>Execution</td>
				</tr>-->
                  <tr class="lt" style="text-align:center">
                      <td rowspan="2" style="vertical-align:bottom;width: 10px;"><input type="checkbox"></td>
                      <td rowspan="2" class="ac" width="2%">Sort</td>
					  <td >Notional</td>
					  <td>Time</td>
					  <td rowspan="2">Counterparty</td>
                      <td rowspan="2">Execution Rule</td>
                      <% if (isMatchDelayEnabled) { %>
						<td rowspan="2">Match Delay (ms)</td>
                      <% } %>
                      <% if ( partialCoverEnabled ){ %>
						<td rowspan="2">Partial Cover</td>
                      <% } %>
                      <td colspan="2">Full Fill <%=isFFDisabled?"<img  src='/admin/theme/images/caution.png' title='Full fill feature Disabled globally. Settings will not take effect.' alt='Full fill feature Disabled globally. Settings will not take effect.'>":""%></td>
                      <% if(isPriceCheckEnabled){ %>
                      <td rowspan="2">Price Check</td>
                      <% } %>
                </tr>
                <tr class="lt" style="text-align:center">
                     <td>Low (>)<br>High (<=)</td>
                     <td nowrap><span style="margin-right:20px;">Start</span>Stop</td>
					 <td>Min % Covered</td>
					 <td>Auto Cover</td>
                </tr>
           <%
              ExecutionRule espExecutionRule = configuration.getOriginalESPExecutionRule();
               Collection<ExecutionRuleCondition> espRuleConditions =
                       (espExecutionRule != null && espExecutionRule.getExecutionRuleConditions() != null &&
                               espExecutionRule.getExecutionRuleConditions().size() != 0) ?
                               espExecutionRule.getExecutionRuleConditions() : Collections.EMPTY_LIST;
               int espCount =0;
               String sdEncId = null;
               for (ExecutionRuleCondition ruleCondition : espRuleConditions ) {
                   String cls = espCount % 2 == 0 ? "tl" : "tlc";
                    UserCounterpartyGroup salesDealerGroup1 = ruleCondition.getSalesDealerGroup();
                    sdEncId = salesDealerGroup1 != null ? salesDealerGroup1.getEncryptedObjectId() : null;
                   out.println("<tr class='" + cls + "' style='text-align:center' rowId = '" + espCount + "'>");
                   out.println("<td  rowspan='2'><input type='checkbox' id='espExecRuleCond_" + espCount + "' name='workflowMessage.espExecRuleCond_" + espCount + "'> </td>");
                   out.println("<td  rowspan='2'><input type='text' id='espSortOrder_" + espCount + "'  name='workflowMessage.espSortOrder_" + espCount + "' class='ftr' size='2' value='" + ruleCondition.getSortOrder() + "' onblur='changeButton()'> </td>");


				   	//move notional
				   out.println( "<td  rowspan='2'><input id='espExecNotLow_"+espCount+"'  name='workflowMessage.espExecNotLow_"+espCount+"' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);changeButton()' default='Low (>)' value='"+ getFormattedNotionalAmountERC(ruleCondition.getLowAmount(),decimalFormat)+"' size='9' ></br>");
					out.println("<input id='espExecNotHigh_"+espCount+"' name='workflowMessage.espExecNotHigh_"+espCount+"' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);changeButton()' default='High (<=)' value='"+ getFormattedNotionalAmountERC(ruleCondition.getHighAmount(),decimalFormat) +"' size='9' ></td>");

					//move timezone
                    out.println("<td  rowspan='2' nowrap><input id='espExecTmStrt_"+ espCount +"'  name='workflowMessage.espExecTmStrt_"+ espCount +"' class='ft' type='text' value='"+ (ruleCondition.getStartTime() == null ? "" : ruleCondition.getStartTime()) +"' size='6'  onblur='changeButton()'><span style='width: 0px;'>&nbsp;</span><input id='espExecTmEnd_"+ espCount +"'  name='workflowMessage.espExecTmEnd_"+ espCount +"' class='ft' type='text' value='"+ (ruleCondition.getStopTime() == null ? "" : ruleCondition.getStopTime()) +"' size='6' onblur='changeButton()'></br><select id='espTimeZoneID"+ espCount +"' name='workflowMessage.espTimeZoneID_"+ espCount +"' class='ft' style='width: 114px;overflow:hidden;' onchange='changeButton()'>");
                    for (int j= 0;  j < availableTimeZoneIDs.length; j++) {
                      String displayFormat = getFormatedTimeZone((availableTimeZoneIDs[j]));
                      if (displayFormat == null) continue;
                      out.println("<option value='"+displayFormat+"' "+((displayFormat.equalsIgnoreCase( ruleCondition.getTimeZone() )) ? " selected " : "")+"  >"+displayFormat+"</option>");
                    }
                    out.println("</select> </td>");

				%>
					<td  rowspan="2" nowrap>
                    <select id="espSalesDealerGroup_<%=espCount%>" name="workflowMessage.espSalesDealerGroup_<%=espCount%>" class="ft" style="width: 90px;overflow:hidden;" onchange="changeButton()">
                     <option value="-1" > &nbsp; </option>
                     <%
                         UserCounterpartyGroup salesDealerGroup = ruleCondition.getSalesDealerGroup();
                         for (UserCounterpartyGroup dealerGroup : salesDealerGroups) {
                             %>
                     <option value="<%=dealerGroup.getObjectID()%>" <%=dealerGroup.equals(salesDealerGroup) ? "selected" : "" %>><%=dealerGroup.getShortName()%></option>
                     <%
                         }
                     %>
                 </select>
                 <%if(sdEncId != null)
                    { %>
                        <br>
                     <a href="javascript:showSalesDealerGroupPage('<%=sdEncId%>', '<%=orgEncryptedId%>')">
                             view/edit
                         </a>
                    <%
                         }
                     %>
                     </td>
					<%

                    boolean hideESPBookSelectElement = true;
					StringBuilder executionRuleRow1 = new StringBuilder("<td  rowspan='1' nowrap>");
                  executionRuleRow1.append("<select  class='ft' style='width:85' id='espCoverExecutionMethod_").append(espCount).append("'  name='workflowMessage.espCoverExecutionMethod_").append(espCount)
                           .append("' onchange='validateESPCoverExec(this," + espCount + ");updateBookVisibility(this,\"ESP\");' nowrap>");
                   String selectedValue = "";
                   if(displayCoverOnly){
                       boolean isSelected = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod() != null;
                       if(isSelected) {
                           selectedValue = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().getName();
                           executionRuleRow1.append("<option value='").append(selectedValue).append("' selected >")
                           .append(selectedValue).append("</option>");
                           if(!selectedValue.equals("Cover")){
                               executionRuleRow1.append("<option value='Cover' >Cover</option>");
                           }
                       }else{
                           executionRuleRow1.append("<option value='").append(CoverExecutionMethod.Cover.getName()).append("' selected >")
                           .append(CoverExecutionMethod.Cover.getName()).append("</option>");
                       }
                   }else{
                       for (int j = 0; j < espCoverExecutionMethods.length; j++) {
                           boolean isSelected = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod() == espCoverExecutionMethods[j];
                           if( isSelected ) selectedValue = espCoverExecutionMethods[j].name();
                           executionRuleRow1.append("<option value='").append(espCoverExecutionMethods[j].name()).append("' ");
                           executionRuleRow1.append( isSelected ? "selected" : "");
                           executionRuleRow1.append("   >").append(espCoverExecutionMethods[j].getFullName()).append("</option>");
                           if(selectedValue.equals("Warehouse")){
                               hideESPBookSelectElement = false;
                           }
                       }
                   }
                   executionRuleRow1.append("</select>").append("&nbsp;&nbsp;&nbsp;&nbsp;");
                   executionRuleRow1.append("<script>$(\"espCoverExecutionMethod_"+espCount+"\").prevVal=\""+selectedValue+"\";</script>");

                       String bookNameConfiguredValue = ruleCondition.getExecutionMethodParameters().getBookName();
                       boolean isBookNameConfigured = (bookNameConfiguredValue!=null && bookNameConfiguredValue.length()>0);
                         //Adding the bookName Selectbox when either we already have the configured value or have the property enabled
                         if(isESPMultiBookPropertyEnabled || isBookNameConfigured ){
                               boolean isSelected = false;
                               executionRuleRow1.append("<select class='ft' id='espCoverExecutionMethod_").append(espCount).append("_Book' name='workflowMessage.espCoverExecutionMethod_").append(espCount).append("_Book' onchange='changeButton();' ");
                                    executionRuleRow1.append(!isESPMultiBookPropertyEnabled || hideESPBookSelectElement ? "style='display:none;'>" : ">");
                                    for(String bookName : supportedBookNames){
                                        executionRuleRow1.append("<option value='").append(bookName).append("' ");
                                        if(!isSelected && (!isBookNameConfigured || bookName.equals(bookNameConfiguredValue))){
                                            executionRuleRow1.append("selected >");
                                            isSelected = true;
                                        }else{
                                            executionRuleRow1.append(">");
                                        }
                                        executionRuleRow1.append(bookName);
                                        executionRuleRow1.append("</option>");
                                    }

                                    if(supportedBookNames!=null && isBookNameConfigured && !supportedBookNames.contains(bookNameConfiguredValue)){
                                        //Adding the configured bookName option to supported bookname list
                                        executionRuleRow1.append("<option value='").append(bookNameConfiguredValue).append("' selected >").append(bookNameConfiguredValue).append("</option>");
                                    }
                               executionRuleRow1.append("</select>&nbsp;&nbsp;");
                         }

                   executionRuleRow1.append("<input type='checkbox' id='espNoPriceChecked_").append(espCount).append("' name='workflowMessage.espNoPriceChecked_").append(espCount)
                           .append("' ").append(ruleCondition.getExecutionMethodParameters().isNoPriceCheckEnabled() ? "checked" : "").append(" onclick='validateESPNoPriceCheckBox(this," + espCount + ")'")
                           .append(CoverExecutionMethod.NoCover.equals(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod()) ? "" : " disabled").append(" >")
                           .append("<label id='espNoPriceCheckedLabel_").append(espCount).append("'>").append("NPC").append("</label>").append("&nbsp;&nbsp;");


                   executionRuleRow1.append("<input type='radio' id='espTrdTypeMkt_").append(espCount).append("' name='workflowMessage.espTrdType_").append(espCount)
                           .append("' ").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "" : "checked")
                           .append(" onclick='validateESPTradeTypeCombo(this," + espCount + ")'>").append("Market").append("&nbsp;&nbsp;");

                   executionRuleRow1.append("&nbsp;").append("<input type='radio' id='espTrdTypeLmt_").append(espCount).append("' name='workflowMessage.espTrdType_").append(espCount)
                           .append("' ").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "checked" : "")
                           .append(" onclick='validateESPTradeTypeCombo(this," + espCount + ")'>").append("Limit").append("&nbsp;&nbsp;");


                   executionRuleRow1.append("<input id='espRange_").append(espCount).append("'  name='workflowMessage.espRange_").append(espCount)
                           .append("' class='ftr' type='text' onblur='validateESPRange(this," + espCount + ")' value='").append(getFormattedNotionalAmountERC(ruleCondition.getExecutionMethodParameters().getRange(), decimalFormat))
                           .append("' size='3'").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "" : " disabled ").append(">").append("&nbsp;&nbsp;");

                   executionRuleRow1.append("<select  class='ft' style='width:70' id='espRangeType_").append(espCount).append("'  name='workflowMessage.espRangeType_").append(espCount)
                           .append("' onchange='validateESPCoverExec(this," + espCount + ")' nowrap>");
                   for (int j = 0; j < executionRangeType.length; j++) {
                       executionRuleRow1.append("<option value='").append(executionRangeType[j].name()).append("' ");
                       executionRuleRow1.append(ruleCondition.getExecutionMethodParameters().getRangeType() == executionRangeType[j] ? "selected" : "");
                       executionRuleRow1.append("   >").append(executionRangeType[j].getFullName()).append("</option>");
                   }
                   executionRuleRow1.append("</select>").append("&nbsp;&nbsp;");

                   executionRuleRow1.append("<input type='checkbox' class='espVWAP' id='espVWAP_").append(espCount).append("' name='workflowMessage.espVWAP_").append(espCount)
                          .append("' ").append(ruleCondition.getExecutionMethodParameters().isVWAPEnabled() ? "checked" : "").append(" onclick='validateESPVWAPCheckBox(this," + espCount + ")'")
                          .append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Cover) ? "" : " disabled").append(">").append("VWAP").append("&nbsp;&nbsp;");

                  executionRuleRow1.append("<input type='checkbox' id='espFOK_").append(espCount).append("' name='workflowMessage.espFOK_").append(espCount)
                          .append("' ").append(ruleCondition.getExecutionMethodParameters().isFOKEnabled() ? "checked" : "").append(" onclick='validateESPFOKCheckBox(this," + espCount + ")'")
                          .append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Cover) ? "" : " disabled").append(">").append("FOK");
                   
                   
                   if(isNewCopyTradeEnabled){
                	   executionRuleRow1.append("&nbsp;&nbsp;").append("<input type='checkbox' id='espCopyChecked_").append(espCount).append("' name='workflowMessage.espCopyChecked_").append(espCount)
                       .append("' ").append("' onclick='validateESPExecutionRuleCondition(" + espCount + ")'").append(" onchange='changeButton()'").append(ruleCondition.getExecutionMethodParameters().isCopyEnabled() ? "checked" : "").append(">")
                       .append("<label id='espCopyCheckedLabel_").append(espCount).append("'>").append("Copy").append("</label>").append("&nbsp;&nbsp;");
                       
                       executionRuleRow1.append("<input id='espCopyValue_").append(espCount).append("'  name='workflowMessage.espCopyValue_").append(espCount)
                       .append("' class='ftr' type='text' value='").append(decimalFormat.format(ruleCondition.getExecutionMethodParameters().getCopyValue())).append("' size='3'")
                       .append(" onkeyup='changeButton()'").append(" onblur='validateESPCopyValue(this," + espCount + ")'").append(">")
                       .append("<label id='espCopyPercentageLabel_").append(espCount).append("'>").append("%").append("</label>");
                   }
                   
                   executionRuleRow1.append("</td>");
                   out.println(executionRuleRow1.toString());

                if (isMatchDelayEnabled) {
                    boolean overRide = ruleCondition.isOverrideStreamMatchDelay();
                    int ruleMinMatchDelay = ruleCondition.getMinimumMatchDelay();
                    int ruleMaxMatchDelay = ruleCondition.getMaximumMatchDelay();
                    StringBuilder matchDelayRow = new StringBuilder("<td rowspan='2' nowrap><label id='espMatchDelayOverrideLabel_").append(espCount)
                        .append("'>Override</label><input type='checkbox' id='espMatchDelayOverride_").append(espCount)
                        .append("' name='workflowMessage.espMatchDelayOverride_").append(espCount).append("' ")
                        .append(overRide ? "checked" : "").append(" onclick='changeButton(),toggleMatchDelayFields(").append(espCount)
                        .append(",").append(ruleMinMatchDelay).append(",").append(streamMinMatchDelay)
                        .append(",").append(ruleMaxMatchDelay).append(",").append(streamMaxMatchDelay)
                        .append(")'/><br>");

                    matchDelayRow.append("<label id='espMinMatchDelayLabel_").append(espCount)
                        .append("'>Min</label>&nbsp;<input type='text' id='espMinMatchDelay_").append(espCount)
                        .append("' name='workflowMessage.espMinMatchDelay_").append(espCount)
                        .append("' class='ftr' size='2' value='")
                        .append(overRide ? ruleMinMatchDelay : streamMinMatchDelay)
                        .append("' onkeyup='changeButton()' ").append(overRide ? "" : "disabled")
                        .append("/>&nbsp;&nbsp;<label id='espMaxMatchDelayLabel_").append(espCount)
                        .append("'>Max</label>&nbsp;<input type='text' id='espMaxMatchDelay_").append(espCount)
                        .append("' name='workflowMessage.espMaxMatchDelay_").append(espCount)
                        .append("' class='ftr' size='2' value='")
                        .append(overRide ? ruleMaxMatchDelay : streamMaxMatchDelay)
                        .append("' onkeyup='changeButton()' ").append(overRide ? "" : "disabled")
                        .append("/></td>");
                    out.println(matchDelayRow.toString());
                }

				if ( partialCoverEnabled )
				{
					out.println("<td rowspan='2'><input type='checkbox' id='espPartialCover_"+espCount+"' name='workflowMessage.espPartialCover_"+espCount+"' " + (ruleCondition.isPartialCoverEnabled() ? "checked" : "") + " onclick='changeButton(),validateESPCoverPercentage("+espCount+")'><span style='width: 10px;'></span><input type='text' id='espCoverPercentageVal_"+espCount+"' name='workflowMessage.espCoverPercentageVal_"+espCount+"' class='ftr' size='2' value='" + decimalFormat.format(ruleCondition.getCoverPercentage()) +"' onblur='return validateESPCoverPercentage("+espCount+")' onkeyup='changeButton()'>% </td>");
				}

				  out.println("<td rowspan='2'><input type='checkbox' id='espFullFill_"+espCount+"' name='workflowMessage.espFullFill_"+espCount+"' " + (ruleCondition.isFullFillEnabled() ? "checked" : "") + " onclick='changeButton()'><span style='width: 10px;'></span><input type='text' id='espFullFillVal_"+espCount+"' name='workflowMessage.espFullFillVal_"+espCount+"' class='ftr' size='2' value='" + decimalFormat.format(ruleCondition.getFullFill()) +"' onblur='return validateESPFFPercentage(this,"+espCount+")' onkeyup='changeButton()'>% </td>");
                  out.println("<td  rowspan='2'><input type='checkbox' id='espAutoCover_"+espCount+"' name='workflowMessage.espAutoCover_"+espCount+"' " + (ruleCondition.isAutoCoverEnabled() ? "checked" : "") + " onclick='changeButton()'> <br><span style='visibility: hidden'  id='espDescFullFill_" + espCount + "'></span></td>");
                  
                  if(isPriceCheckEnabled){
                	  out.println("<td rowspan='2'><input type='checkbox' id='espPriceCheck_"+espCount+"' name='workflowMessage.espPriceCheck_"+espCount+"' " + (ruleCondition.isPriceCheckEnabled() ? "checked" : "") + " onclick='changeButton()'><span style='width: 10px;'></span><input type='text' id='espPriceCheckVal_"+espCount+"' name='workflowMessage.espPriceCheckVal_"+espCount+"' class='ftr' size='3' value='" + decimalFormat.format(ruleCondition.getPriceCheckValue()) +"' onblur='return validateESPPriceCheckValue(this)' onkeyup='changeButton()'> &nbsp;&nbsp;" +
                              "<select class='ft' style='width:70' id='espPriceCheckTypeName_"+espCount+"' name='workflowMessage.espPriceCheckTypeName_"+espCount+"' onclick='changeButton()' nowrap>");
                      for (int j = 0; j < priceCheckType.length; j++) {
                           out.println("<option value='" + priceCheckType[j].name() + "' " + (priceCheckType[j].name().equals(ruleCondition.getPriceCheckTypeName()) ? "selected" : "")  + " >"+ priceCheckType[j].name() + "</option>");                	 
                          }
                      out.println("</select></td>");
                  }
                  
                  
				  out.println("</tr>");
				  out.println("<tr class='" + cls + "' style='text-align:center' rowId = '" + espCount + "'>");
                  out.println("<td id='espDescription_" + espCount + "'></td>");
                  //out.println("<td style='visibility: hidden'  id='espDescFullFill_" + espCount + "'></td>");
				  out.println("</tr>");
                  %>
				                    

                 <script>
                     validateESPExecutionRuleCondition(<%=espCount%>);
                 </script>
                 <%
                 espCount++;
             }

           %>
           </table>
           <table id="espExcnAction">
               <tr>
                   <td class="f" colspan="11">
                        <span style="width:25px;">&nbsp;</span>
                        <input type="button" class="b1" value="Delete Rule"  onclick="javascript:deleteExecutionCondition('espExecutionConditions','ESP');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
                        <input type="button" class="b1" value="Add Rule" onclick="javascript:newExecutionConditionV2(<%=streamMinMatchDelay%>,<%=streamMaxMatchDelay%>,'espExecutionConditions','ESP');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
                       <input type="hidden" id='espCOUNTEXECUTIONCOND' name="workflowMessage.espCOUNTEXECUTIONCOND" value="<%=espRuleConditions.size()%>"/>
                   </td>
               </tr>
             </table><br><br>
        </td>
        <td class="f" nowrap>
            &nbsp;
            <input type="checkbox" name="workflowMessage.allESPExecMeth" onclick="allClick();" desc="ESP Execution Method" scopeIndicator="scopeIndPME">
            <%
                if(marketRangeEnabledForEspExecution){
            %>
            <input type="hidden" name="workflowMessage.marketRangeEnabledForEspExecution"  value="on">
            <%}%>
            <input type="hidden" name="workflowMessage.marketRangeForEspExecution" value="<%=marketRangeForEspExecution==null?"":decimalFormat.format(marketRangeForEspExecution)%>">
        </td>
    </tr>
		    <tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr id="espExcnResp">
		<td class="f3"><strong>Execution Response</strong></td>
        <td class="f3" colspan="4" nowrap><input type="radio"  name="workflowMessage.multiFillEnabled" id="multiFillEnabled1" value="false" <%=(multiFillEnabled==null)?"checked":multiFillEnabled ? "" : "checked"%> onclick='changeButton(), handleNoTimeOut(true), handleMultipleFill(false)'/>Aggregated Fill&nbsp;&nbsp;<input type="radio" name="workflowMessage.multiFillEnabled" id="multiFillEnabled2" value="true" <%=(multiFillEnabled==null)?"": multiFillEnabled ? "checked" : ""%> onclick='changeButton(), handleNoTimeOut(false), handleMultipleFill(<%=isDisableVWAPIfMultiFillEnabled%>)'/>Multiple Fills
        <input type="checkbox" name="workflowMessage.noTimeout" <%=noTimeout == null ? "" : noTimeout ? "checked":"" %> value="true" class="ft" onclick='changeButton()'/> No Timeout
	    </td>
        <td class="f3"></td>
        <td class="f3"></td>
    </tr>
		<tr>
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr id="espExcnOvrInt">
		<td class="f3" nowrap><strong>Override Stream Update Interval</strong></td>
		<td class="f3" nowrap style="text-align: bottom"><input type="checkbox" id="overrideESPUpdateInterval" name="workflowMessage.overrideESPUpdateInterval" value="<%=overrideESPUpdateInterval%>" <%=overrideESPUpdateInterval.booleanValue()?"checked":""%> class="ft" onclick="changeButton();updateESPIntervals();"></td>
        <td class="f3" nowrap>Full Stream Update Interval</td>
		<td class="f3" nowrap><input type="text" id = "espFullUpdateInterval" name="workflowMessage.espFullUpdateInterval" size="2" value="<%=(espFullUpdateInterval==null)?"":espFullUpdateInterval.toString()%>"
	        <%=overrideESPUpdateInterval.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>milliseconds<script>registeredFields.add('espFullUpdateInterval')</script>&nbsp;&nbsp;(Stream Default:&nbsp;<%= stream.getQuotePublicationInterval() %>&nbsp; milliseconds)</td>
		<td class="f3" nowrap></td>
		<td class="f3" nowrap></td>
    </tr>
    <tr id="espExcnUpdInt">
        <td class="f3" colspan="2" nowrap></td>
        <td class="f3" nowrap>Best Bid/Offer Update Interval</td>
		<td class="f3" nowrap><input type="text" id = "espBestBidOfferInterval" name="workflowMessage.espBestBidOfferInterval" size="2" value="<%=(espBestBidOfferInterval==null)?"":espBestBidOfferInterval.toString()%>"
	        <%=overrideESPUpdateInterval.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>milliseconds<script>registeredFields.add('espBestBidOfferInterval')</script>&nbsp;&nbsp;(Stream Default:&nbsp;&nbsp;&nbsp;<%= stream.getQuotePublicationCheckInterval() %>&nbsp;milliseconds)</td>
		<td class="f3" nowrap></td>
		<td class="f3" nowrap></td>
    </tr>
	<tr>
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
		<tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
    <tr style="display: <%=showRFS %>" id="rfsExcnHeaderTag">
        <td class="f" colspan="6"  style="font-size: 9pt;font-weight: bold;"  nowrap>RFS Rules</td><td class="f" nowrap></td>
    </tr>
     <% 
     Collection<String> stdTenors =   BrokerProvisionConfigFactory.getBrokerProvisionConfig().getBrokerStdTenors(org.getShortName());
        if(!stdTenors.isEmpty()) { 
        	List<String> list = new ArrayList<String>(stdTenors);
        	StringBuilder sb = new StringBuilder();
        	sb.append(list.get(0));
        	for(int i=1;i<list.size();i++) { 
        		sb.append(", ").append(list.get(i));
        	}
     %>
      <tr style="display: <%=showRFS %>">
        <td class="f3" nowrap colspan="2"><strong>Standard Tenors</strong></td>
        <td class="f" colspan="4" ><p style="display: inline-block; margin: 0px">
        <%= sb.toString() %></p>
        </td>
		<td class="f3" nowrap></td>
      </tr>
     <%
		}
		if ( isProviderPointsValidationEnabled )
		{
	 %>
   <tr style="display: <%=showRFS %>">
     <td class="f" nowrap><strong>RFS Provider Points Validation Market Data Set</strong></td>
     <td class="f3" nowrap>
         <a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/MarketData/MarketDataEdit.html#_top','Admin Portal Help')">
               <img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a>
     </td>
     <td  class="f" colspan="1" nowrap>
             <select id="RFSQuoteFilterMarketDataSet" name="workflowMessage.RFSQuoteFilterMarketDataSet"  class="ft" onchange="changeButton()">
				<option value="" <%=RFSQuoteFilterMarketDataSet != null ? "" : "selected"%>><idcBean:message key="SelectOption1" /></option>
                <idcLogic:iterate id="mds" name="MDSES" type="com.integral.finance.marketData.fx.FXMarketDataSet">
                                <option value="<%=mds.getEncryptedObjectID()%>" <%=(null!=RFSQuoteFilterMarketDataSet && RFSQuoteFilterMarketDataSet.isSameAs(mds))?"selected":""%>><%=mds.getShortName()%></option>
                </idcLogic:iterate>
             </select>
     <%
     	if ( RFSQuoteFilterMarketDataSet != null )
     	{
     %>
         <a href="javascript:openDetailWindow('/admin/Forward.do?forwardURI=Admin.MarketDataSet.Edit&objectForm.objectType=com.integral.finance.marketData.fx.FXMarketDataSetC&objectForm.encryptedId=<%=RFSQuoteFilterMarketDataSet.getEncryptedObjectID()%>&fromWhere=Config', 'Market Data Set')">
             view/edit
         </a>
     <%
     	}
     %></td>
     <td class="f" nowrap><strong> Allowed Deviation (Percent): </strong></td>
     <td class="f" nowrap>
           <input type="text" id='allowedDeviationPercent' name ="workflowMessage.allowedDeviationPercent" value='<%=(allowedDeviationPercent!=null)?decimalFormat.format(allowedDeviationPercent):decimalFormat.format(0)%>'
             onblur="validateAllowedDeviationPercent(this)" onkeyup='changeButton()'>
             <script>registeredFields.add('allowedDeviationPercent')</script>
     </td>
      <td class="f3" nowrap></td>
      <td class="f3" nowrap></td>
  </tr>
	<%
		}
	%>
   <tr>
       <td id="rfsExcnTag" class="f3" colspan="6" nowrap style="display: <%=showRFS %>"><strong>Pricing and Execution Method</strong></br>

            <table id="rfsExecutionConditions"  cellpadding="0" border="0" cellspacing="1" style="width:1636px;background-color: #999;">
               <tr class="lt" style="text-align:center">
                     <td rowspan="2" style="vertical-align:bottom;width: 10px;padding-left: 0px; padding-right: 0px;"><input type="checkbox"></td>
                     <td rowspan="2" class="ac" width="2%" style="padding-left: 0px; padding-right: 0px;">Sort</td>
					 <td nowrap rowspan="2" style="padding-left: 0px; padding-right: 0px;">Trade Type</td>
					 <td nowrap style="padding-left: 0px; padding-right: 0px;">Notional</td>
					 <td nowrap style="padding-left: 0px; padding-right: 0px;">Net Spot Amt</td>
					 <td nowrap style="padding-left: 0px; padding-right: 0px;">Time</td>
					 <td colspan="3" style="padding-left: 0px; padding-right: 0px;">Tenor</td>
                     <td>Spot</td>
					 <td rowspan="2" style="padding-left: 0px; padding-right: 0px;">Counterparty</td>
                     <td rowspan="2" colspan="2" style="padding-left: 0px; padding-right: 0px;">Pricing</td>
                     <% if(isSpotAndSwapEnabled) { %>
                     	<td colspan="2" style="padding-left: 0px; padding-right: 0px; text-align:center !important;"> Execution Rule </td>
                     <% } %>
                     <% if(!isSpotAndSwapEnabled) { %>
                     	<td rowspan="2" style="padding-left: 0px; padding-right: 0px; text-align:center !important;">Execution Rule</td>
                     <% } %>
                     <td colspan="3" style="padding-left: 0px; padding-right: 0px;">Full Fill <%=(isFFDisabled?"<img  src='/admin/theme/images/caution.png' title='Full fill feature Disabled globally. Settings will not take effect.' alt='Full fill feature Disabled globally. Settings will not take effect.'>":"")%></td>
               </tr>
               <tr class="lt" style="text-align:center">
					<td style="padding-left: 0px; padding-right: 0px;">Low (>)<br>High (<=)</td>
					<td style="padding-left: 0px; padding-right: 0px;">Low (>)<br>High (<=)</td>
                    <td nowrap style="padding-left: 0px; padding-right: 0px;"><span style="margin-right:20px;">Start</span>Stop</td>
                    <td style="padding-left: 0px; padding-right: 0px;">Legs</td>
                    <td style="padding-left: 0px; padding-right: 0px;">Category</td>
                    <td style="padding-left: 0px; padding-right: 0px;">Range</br>Min (>)</br>Max(<=)</td>
                    <td style="padding-left: 0px; padding-right: 0px;">Pips Deviation<br>(<=)</td>
                    <% if(isSpotAndSwapEnabled) { %>
	                    <td id="rfsSwapPointsBlank" style="padding-left: 0px; padding-right: 0px;">Spot Risk</td>
	                    <td id="rfsSwapPoints" nowrap style="padding-left: 0px; padding-right: 0px;">Swap Risk</td>
                    <% } %>
                    <td style="padding-left: 0px; padding-right: 0px;">Min %<br>Covered</td>
					<td style="padding-left: 0px; padding-right: 0px;">Auto<br>Cover</td>
               </tr>


 <%
            ExecutionRule rfsExecutionRule = configuration.getOriginalRFSExecutionRule();
            Collection<ExecutionRuleCondition> rfsRuleConditions =
                    (rfsExecutionRule != null && rfsExecutionRule.getExecutionRuleConditions() != null &&
                            rfsExecutionRule.getExecutionRuleConditions().size() != 0) ?
                            rfsExecutionRule.getExecutionRuleConditions() : Collections.EMPTY_LIST;
            int rfsCount = 0;
            for (ExecutionRuleCondition ruleCondition : rfsRuleConditions) {
                String tradeType = ruleCondition.getTradeType();
                String cls = rfsCount % 2 == 0 ? "tl" : "tlc";
                out.println("<tr class='" + cls + "' style='text-align:center;padding-left: 0px; padding-right: 0px;' rowId = '" + rfsCount + "'>");
                out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input type='checkbox' id='rfsExecRuleCond_" + rfsCount + "' name='workflowMessage.rfsExecRuleCond_" + rfsCount + "'> </td>");
                out.println(" <td rowspan='2' style=padding-left: 0px; padding-right: 0px;'><input type='text' id='rfsSortOrder_" + rfsCount + "'  name='workflowMessage.rfsSortOrder_" + rfsCount + "' class='ftr' size='2' value='" + ruleCondition.getSortOrder() + "' onblur='changeButton()'> </td>");

				//move tradetype
				 out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><select style='width: 90px;overflow:hidden;' id='rfsTradeType_" + rfsCount + "' name='workflowMessage.rfsTradeType_" + rfsCount + "' class='ft' onChange='validateRFSTradeType(this, \"" + rfsCount + "\" );'>");
                    out.println("<option value='" + ISCommonConstants.TRD_CLSF_SP + "' " + ((ISCommonConstants.TRD_CLSF_SP.equals(tradeType)) ? "selected" : "") + ">FX Spot</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_OR + "' " + ((ISCommonConstants.TRD_CLSF_OR.equals(tradeType)) ? "selected" : "") + ">FX Outright</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_SWAP + "' " + ((ISCommonConstants.TRD_CLSF_SWAP.equals(tradeType)) ? "selected" : "") + ">FX Swap</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_FWD + "' " + ((ISCommonConstants.TRD_CLSF_FWD.equals(tradeType)) ? "selected" : "") + ">FX Fwd Fwd</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_FXNDF + "' " + ((ISCommonConstants.TRD_CLSF_FXNDF.equals(tradeType)) ? "selected" : "") + ">FX NDF</option>" +
							"<option value='" + ISCommonConstants.TRD_CLSF_FXFSR + "' " + ((ISCommonConstants.TRD_CLSF_FXFSR.equals(tradeType)) ? "selected" : "") + ">FX FSR</option>" +
							"<option value='" + ISCommonConstants.TRD_CLSF_FXSSP + "' " + ((ISCommonConstants.TRD_CLSF_FXSSP.equals(tradeType)) ? "selected" : "") + ">FX SSP</option>" +
							"<option value='" + ISCommonConstants.TRD_CLSF_FXNDF_SWAP + "' " + ((ISCommonConstants.TRD_CLSF_FXNDF_SWAP.equals(tradeType)) ? "selected" : "") + ">FX NDF Swap</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_FXWindowFWD + "' " + ((ISCommonConstants.TRD_CLSF_FXWindowFWD.equals(tradeType)) ? "selected" : "") + ">FX Window Fwd</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_FXPRORATAFWD + "' " + ((ISCommonConstants.TRD_CLSF_FXPRORATAFWD.equals(tradeType)) ? "selected" : "") + ">FX Prorata Fwd</option>" +
                            "<option value='" + ISCommonConstants.TRD_CLSF_ALL + "' " + ((ISCommonConstants.TRD_CLSF_ALL.equals(tradeType)) ? "selected" : "") + ">All</option> ");
                    out.println("</select>");
                    out.println("<input type='hidden' id='rfsTradeType_hidden_" + rfsCount + "' value='" + (ruleCondition.getTradeType() == null ? ISCommonConstants.TRD_CLSF_ALL : ruleCondition.getTradeType()) + "'>");
                    out.println(" </td>");


					//move notional
					out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input id='rfsExecNotLow_" + rfsCount + "'  name='workflowMessage.rfsExecNotLow_" + rfsCount + "' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);changeButton()' value='" + getFormattedNotionalAmountERC(ruleCondition.getLowAmount(), decimalFormat) + "' size='9' ></br><input id='rfsExecNotHigh_" + rfsCount + "' name='workflowMessage.rfsExecNotHigh_" + rfsCount + "' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);changeButton()' value='" + getFormattedNotionalAmountERC(ruleCondition.getHighAmount(), decimalFormat) + "' size='9' ></td>");


				//move net amount
				out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input id='rfsExecLowNetAmount_" + rfsCount + "'  name='workflowMessage.rfsExecLowNetAmount_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getLowNetAmount() == -1 ? "" : getFormattedNotionalAmountERC(ruleCondition.getLowNetAmount(), decimalFormat)) + "' onblur='convertToNotionalFormatNumber(this);changeButton()' size='9' ></br><input id='rfsExecHighNetAmount_" + rfsCount + "'  name='workflowMessage.rfsExecHighNetAmount_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getHighNetAmount() == -1 ? "" : getFormattedNotionalAmountERC(ruleCondition.getHighNetAmount(), decimalFormat)) + "' onblur='convertToNotionalFormatNumber(this);changeButton()' size='9' ></td>");

				//move time zone
				out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap><input id='rfsExecTmStrt_" + rfsCount + "'  name='workflowMessage.rfsExecTmStrt_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getStartTime() == null ? "" : ruleCondition.getStartTime()) + "' onblur='changeButton()' size='6'><span style='width: 0px'>&nbsp;</span><input id='rfsExecTmEnd_" + rfsCount + "'  name='workflowMessage.rfsExecTmEnd_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getStopTime() == null ? "" : ruleCondition.getStopTime()) + "' onblur='changeButton()' size='6'></br><select id='rfsTimeZoneID_" + rfsCount + "' name='workflowMessage.rfsTimeZoneID_" + rfsCount + "' class='ft' style='width: 110px;overflow:hidden;'  onchange='changeButton()'>");
                    for (int j = 0; j < availableTimeZoneIDs.length; j++) {
                        String displayFormat = getFormatedTimeZone((availableTimeZoneIDs[j]));
                        if (displayFormat == null) continue;
                        out.println("<option value='" + availableTimeZoneIDs[j] + "' " + ((availableTimeZoneIDs[j].equalsIgnoreCase(ruleCondition.getTimeZone())) ? " selected " : "") + "  >" + displayFormat + "</option>");
                    }
                    out.println("</select> </td>");

				//move tenor legs
				boolean dateNotDefined = ruleCondition.getMinValueDate() == null && ruleCondition.getMaxValueDate() == null;
 					SimpleDateFormat dateFormat =  loginUser.getDisplayPreference().getDateFormat();
                    {
                         String value = "";
                          boolean anyLeg = ruleCondition.isAnyLeg();
                          if(!dateNotDefined) {
                            value = ruleCondition.getMinValueDate() == null ? "" : dateFormat.format(ruleCondition.getMinValueDate());
                          } else {
                             value = ruleCondition.getMinTenor() == null? "" : ruleCondition.getMinTenor().toString();
                          }
                          String id = "rfsAnyLeg_"+ rfsCount;
                          String name = "workflowMessage."+id;

                          String rid =  "rfsmc_"+ rfsCount; // rfsMatchCriteria
                          String rname = "workflowMessage." + rid;

                          int mc = ruleCondition.getMatchCriteria();
                          String props []= new String[3];
                          for(int i =0 ; i<3;i++) {
                        	  props[i] = mc == i ? "checked=true" : "";
                          }

						String anycp = anyLeg? "checked=true" : "";
						String allcp = anyLeg? "" : "checked=true";

                         String lgroup = "<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap>";
                          lgroup += "<input type='radio' name='"+name+"'  value='true' " + anycp + " onclick='changeButton()'>Any Leg<br>";
                          lgroup += "<input type='radio' name='"+name+"'  value='false' " + allcp + " onclick='changeButton()'>All Legs<br>";
                          lgroup+= "</td>";
						  out.println(lgroup);



				//move tenor category

                          String radioGroup = "<td rowspan='2' nowrap style='text-align:left;padding-left: 0px; padding-right: 0px;'>";
                          radioGroup += "<input type='radio' name='"+rname+"'  value='0' " + props[0]+ " onclick='changeButton()' class='mc' id='" +rfsCount+ ".0'>Default<br>";
                          radioGroup += "<input type='radio' name='"+rname+"'  value='1' " + props[1]+ " onclick='changeButton()' class='mc' id='" +rfsCount+ ".1'>Standard Tenor<br>";
                          radioGroup += "<input type='radio' name='"+rname+"'  value='2' " + props[2]+ " onclick='changeButton()' class='mc' id='" +rfsCount+ ".2'>Non Standard<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tenor<br>";
                          radioGroup+= "</td>";
						  out.println(radioGroup);

				//move tenor range
				String curi = request.getContextPath() + "/integral/core/framework/jsp/Calendar.jsp?hostField=workflowMessage.rfsExecTenMin_" +  rfsCount + "&allowPastTenor=true" ;
                String juri = "javascript:popupCalendar(\""+curi+"\")";

                       out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap><div style='width:90%'><div style='display:inline'><input onblur='changeButton()' style='width:60px;min-width:50px;' type='text' id='rfsExecTenMin_" + rfsCount + "'  name='workflowMessage.rfsExecTenMin_" + rfsCount + "' size='20' value='"+value+"' class='ft' ></div><div style='display:inline'>&nbsp;<a id='cptyChooser' style='vertical-align: bottom;'><a href='"+juri+"' onclick='changeButton()' class='mc"+rfsCount+"' tabindex='-1'><img src='/admin/theme/images/calendar.gif' border='0'></a></div></div>");
						}
						{
        				   String value = "";
                           boolean anyLeg = ruleCondition.isAnyLeg();
                           if(!dateNotDefined) {
                             value = ruleCondition.getMaxValueDate() == null ? "" : dateFormat.format(ruleCondition.getMaxValueDate());
                           } else {
                             value = ruleCondition.getMaxTenor() == null? "" : ruleCondition.getMaxTenor().toString();
                           }
                           String curi = request.getContextPath() + "/integral/core/framework/jsp/Calendar.jsp?hostField=workflowMessage.rfsExecTenMax_" +  rfsCount + "&allowPastTenor=true" ;
                           String juri = "javascript:popupCalendar(\""+curi+"\")";
					  out.println("</br><div style='width:90%'><div style='display:inline'><input onblur='changeButton()' style='width:60px;min-width:50px;' type='text' id='rfsExecTenMax_" + rfsCount + "'  name='workflowMessage.rfsExecTenMax_" + rfsCount + "' size='20' value='"+value+"' class='ft' ></div><div style='display:inline'>&nbsp;<a id='cptyChooser' style='vertical-align: bottom;'><a href='"+juri+"' onclick='changeButton()' tabindex='-1'><img src='/admin/theme/images/calendar.gif' border='0'></a></div></div></td>");
					}
				//move spot pips deviation
				        out.println("<td rowspan='2'><input id='rfsPipsDeviation_" + rfsCount + "'  name='workflowMessage.rfsPipsDeviation_" + rfsCount + "' class='ft' type='text' value='" + (ruleCondition.getPipsDeviation() == -1 ? "" : ruleCondition.getPipsDeviation()) + "' onblur='validateMinimumSpread(this);changeButton()' size='6' ></td>");

				%>
				<!--move counterparty-->
				    <td rowspan="2" style='padding-left: 0px; padding-right: 0px;'>
                    <select id="rfsSalesDealerGroup_<%=rfsCount%>"
                            name="workflowMessage.rfsSalesDealerGroup_<%=rfsCount%>" class="ft"  style='width: 90px;overflow:hidden;'
                            onchange="changeButton()">
                        <option value="-1"> &nbsp; </option>
                        <%
                            UserCounterpartyGroup salesDealerGroup = ruleCondition.getSalesDealerGroup();
                            for (UserCounterpartyGroup dealerGroup : salesDealerGroups) {
                        %>
                        <option value="<%=dealerGroup.getObjectID()%>" <%=dealerGroup.equals(salesDealerGroup) ? "selected" : "" %>><%=dealerGroup.getShortName()%>
                        </option>
                        <%
                            }
                        %>
                    </select>
                </td>

				<%

                out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap>");
                out.println("<span id='rfsRFS_" + rfsCount + "'>");
                out.println("<input type='checkbox' id='rfsIsRFSEnabled_" + rfsCount + "' name='workflowMessage.rfsIsRFSEnabled_" + rfsCount + "' " + (ruleCondition.getExecutionPricingParameters().isRFSEnabled() ? "checked" : "")
                        + " onclick='return validateRFSRFSCheckBox(this,\"" + rfsCount + "\")'>RFS</td>");
                out.println("</span>");
                out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;' nowrap>");

                out.println("<span id='rfsSpMds_" + rfsCount + "'>");
                out.println("<input type='checkbox' id='rfsIsSpotMDSEnabled_" + rfsCount + "' name='workflowMessage.rfsIsSpotMDSEnabled_" + rfsCount + "' " + (ruleCondition.getExecutionPricingParameters().isSpotAndMDSEnabled() ? "checked" : "") +
                        " onclick='return validateRFSSpotMDSCheckBox(this,\"" + rfsCount + "\")'>Spot & MDS");
                out.println("</span>");
                out.println("<span id='rfsSpSwap_" + rfsCount + "'>");
	            out.println("<input type='checkbox' id='rfsIsSpotSwapEnabled_" + rfsCount + "' name='workflowMessage.rfsIsSpotSwapEnabled_" + rfsCount + "' " + (ruleCondition.getExecutionPricingParameters().isSpotAndSwapEnabled() ? "checked" : "") +
	                        " onclick='return validateRFSSpotSwapCheckBox(this,\"" + rfsCount + "\")'>Spot & Swap");
	            out.println("</span>");
                StringBuilder executionPricingRow1 = new StringBuilder();
                executionPricingRow1.append("<br><select  class='ft' id='rfsPricingType_").append(rfsCount).append("'  name='workflowMessage.rfsPricingType_").append(rfsCount)
                        .append("' onchange='return validateRFSPricingType(this, \""+rfsCount+"\")'").append(ruleCondition.getExecutionPricingParameters().isSpotAndMDSEnabled() ? "" : " disabled ").append("nowrap>");
                for (int j = 0; j < executionPricingType.length; j++) {
                  if(!((!isFaMultiTierEnabled && ExecutionPricingType.FAMultiTier.name().equals(executionPricingType[j].name()))
                                     || (!isFaMultiQuoteEnabled && ExecutionPricingType.FAMultiQuote.name().equals(executionPricingType[j].name())))){
                    executionPricingRow1.append("<option value='").append(executionPricingType[j].name()).append("' ");
                    executionPricingRow1.append(ruleCondition.getExecutionPricingParameters().getPricingType() == executionPricingType[j] ? "selected" : "");
                    executionPricingRow1.append("   >").append(executionPricingType[j].getFullName()).append("</option>");
                  }
                }
                executionPricingRow1.append("</select>");
                out.println(executionPricingRow1.toString());

				//move inflate
                out.println("</br> <span style='height: 30px;margin-top: 6px;text-align: center'><span style='font-weight: bold'>Inflate Price</span> <input type='checkbox' id='rfsInflate_" + rfsCount + "' name='workflowMessage.rfsInflate_" + rfsCount + "' " + (ruleCondition.isInflateEnabled() ? "checked" : "") + " onclick='changeButton()'>");

                out.println("<span id='rfsInflateValText_"+rfsCount+"'><input type='text' id='rfsInflateVal_" + rfsCount + "' name='workflowMessage.rfsInflateVal_" + rfsCount + "' class='ftr' size='1' value='" + decimalFormat.format(ruleCondition.getInflateValue()) + "' onblur='return validatePercentage(this)' onkeyup='changeButton()'>%</span></span></td>");

                boolean hideRFSBookSelectElement = true;
               StringBuilder executionRuleRow1 = new StringBuilder("<td nowrap>");
                executionRuleRow1.append("<select  class='ft' style='width:85' id='rfsCoverExecutionMethod_").append(rfsCount).append("'  name='workflowMessage.rfsCoverExecutionMethod_").append(rfsCount)
                        .append("' onchange='validateRFSCoverExec(this,\"" + rfsCount + "\");updateBookVisibility(this,\"RFS\");'").append(" nowrap>");
                String selectedValue = "";
                if(displayCoverOnly){
                    boolean isSelected = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod() != null;
                    if(isSelected) {
                        selectedValue = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().name();

                        executionRuleRow1.append("<option value='").append(selectedValue).append("' selected >").append(selectedValue).append("</option>");
                        if(!selectedValue.equals("Cover")){
                            executionRuleRow1.append("<option value='Cover' >Cover</option>");
                        }
                    }else{
                        executionRuleRow1.append("<option value='").append(CoverExecutionMethod.Cover.getName()).append("' selected >")
                        .append(CoverExecutionMethod.Cover.getName()).append("</option>");
                    }
                }else{
                    for (int j = 0; j < rfsCoverExecutionMethods.length; j++) {
                        boolean isSelected = ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod() == rfsCoverExecutionMethods[j];
                        if( isSelected ) selectedValue = rfsCoverExecutionMethods[j].name();
                        executionRuleRow1.append("<option value='").append(rfsCoverExecutionMethods[j].name()).append("' ");
                        executionRuleRow1.append(isSelected ? "selected" : "");
                        executionRuleRow1.append("   >").append(rfsCoverExecutionMethods[j].getFullName()).append("</option>");
                        if(selectedValue.equals("Warehouse")){
                            out.println("selectedValue::"+selectedValue);
                            hideRFSBookSelectElement = false;
                        }
                    }
                }
                executionRuleRow1.append("</select>").append("&nbsp;&nbsp;&nbsp;&nbsp;");
                executionRuleRow1.append("<script>$(\"rfsCoverExecutionMethod_"+rfsCount+"\").prevVal=\""+selectedValue+"\";</script>");
                out.println("<input type='hidden' id='rfsCoverExecutionMethod_hidden_" + rfsCount + "' value='" + (ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().name()) + "'>");

                        String bookNameConfiguredValue = ruleCondition.getExecutionMethodParameters().getBookName();
                        boolean isBookNameConfigured = (bookNameConfiguredValue!=null && bookNameConfiguredValue.length()>0);
                        if(isRFSMultiBookPropertyEnabled || isBookNameConfigured ){
                            boolean isSelected = false;
                            executionRuleRow1.append("<select class='ft' id='rfsCoverExecutionMethod_").append(rfsCount).append("_Book' name='workflowMessage.rfsCoverExecutionMethod_").append(rfsCount).append("_Book' onchange='changeButton();' ");
                            executionRuleRow1.append(!isRFSMultiBookPropertyEnabled || hideRFSBookSelectElement ? "style='display:none;'>" : ">");
                            for(String bookName : supportedBookNames){
                                executionRuleRow1.append("<option value='").append(bookName).append("' ");
                                if(!isSelected && (!isBookNameConfigured || bookName.equals(bookNameConfiguredValue))){
                                    executionRuleRow1.append("selected >");
                                    isSelected = true;
                                }else{
                                    executionRuleRow1.append(">");
                                }
                                executionRuleRow1.append(bookName);
                                executionRuleRow1.append("</option>");
                            }
                            executionRuleRow1.append("</select>&nbsp;&nbsp;");
                        }

                executionRuleRow1.append("<input type='checkbox' id='rfsNoPriceChecked_").append(rfsCount).append("' name='workflowMessage.rfsNoPriceChecked_").append(rfsCount)
                    .append("' ").append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.NoCover) ? "" : " disabled ")
                    .append(ruleCondition.getExecutionMethodParameters().isNoPriceCheckEnabled() ? "checked" : "").append(" onclick='return validateRFSNoPriceCheckBox(this,\"" + rfsCount + "\")'>")
                    .append("<label id='rfsNoPriceCheckedLabel_").append(rfsCount).append("'>").append("NPC").append("</label>").append("&nbsp;&nbsp;");

				executionRuleRow1.append("<span style='padding-left: 10px;' id='").append("rfsFOKText_").append(rfsCount).append("'><input type='checkbox' id='rfsFOK_").append(rfsCount).append("' name='workflowMessage.rfsFOK_").append(rfsCount).append("' ")
                    .append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Cover) ? "" : " disabled ")
                    .append(ruleCondition.getExecutionMethodParameters().isFOKEnabled() ? "checked" : "").append(" onclick='return validateRFSFOKCheckBox(this,\"" + rfsCount + "\")'>").append("FOK </span>");

				if(isDealerInterventionEnabled){
					executionRuleRow1.append("<span title = 'Dealer Intervention' style='padding-left: 10px;' id='").append("rfsDIText_").append(rfsCount).append("'><input type='checkbox' id='rfsDIChecked_").append(rfsCount).append("' name='workflowMessage.rfsDIChecked_").append(rfsCount).append("' ")
	                .append(ruleCondition.getExecutionMethodParameters().isDealerInterventionEnabled() ? "checked" : "").append(" onclick='return validateRFSDICheckBox(this,\"" + rfsCount + "\")'>").append("DI </span>");
				}

				executionRuleRow1.append("&nbsp;</br>").append("<span id='").append("rfsLimit_").append(rfsCount).append("'><input type='radio' id='rfsTrdTypeLmt_").append(rfsCount).append("' name='workflowMessage.rfsTrdType_").append(rfsCount)
                       .append("' ").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "checked" : "")
                       .append(" onclick='validateRFSTradeTypeCombo(this," + rfsCount + ")'>").append("Limit").append("&nbsp;&nbsp;</span>");

			    executionRuleRow1.append("<span id='").append("rfsMarket_").append(rfsCount).append("'><input type='radio' id='rfsTrdTypeMkt_").append(rfsCount).append("' name='workflowMessage.rfsTrdType_").append(rfsCount)
                       .append("' ").append(ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "" : "checked")
                       .append(" onclick='validateRFSTradeTypeCombo(this," + rfsCount + ")'>").append("Market").append("&nbsp;&nbsp; </span>");

                executionRuleRow1.append("<input id='rfsRange_").append(rfsCount).append("'  name='workflowMessage.rfsRange_").append(rfsCount)
                        .append("' class='ftr' type='text' onblur='validateRFSRange(this," + rfsCount + ")' value='").append(getFormattedNotionalAmountERC(ruleCondition.getExecutionMethodParameters().getRange(), decimalFormat))
                        .append("' size='2'").append(!ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Manual) && ruleCondition.getExecutionMethodParameters().isRangeEnabled() ? "" : " disabled ").append(" >").append("&nbsp;&nbsp;");

                executionRuleRow1.append("<select  class='ft' style='width:70' id='rfsRangeType_").append(rfsCount).append("'  name='workflowMessage.rfsRangeType_").append(rfsCount).append("' ")
                        .append(ruleCondition.getExecutionMethodParameters().getCoverExecutionMethod().equals(CoverExecutionMethod.Manual) ? " disabled " : "").append("onchange='return validateRFSSpotMDSCheckBox(this,\"" + rfsCount + "\")' nowrap>");
                for (int j = 0; j < executionRangeType.length; j++) {
                    executionRuleRow1.append("<option value='").append(executionRangeType[j].name()).append("' ");
                    executionRuleRow1.append(ruleCondition.getExecutionMethodParameters().getRangeType() == executionRangeType[j] ? "selected" : "");
                    executionRuleRow1.append("   >").append(executionRangeType[j].getFullName()).append("</option>");
                }
                executionRuleRow1.append("</select>").append("&nbsp;&nbsp;");


                executionRuleRow1.append("</td>");
                out.println(executionRuleRow1.toString());

                if(isSpotAndSwapEnabled) {
                	out.println("<td nowrap rowspan='2' style='padding-left: 5px; padding-right: 5px;'><select  class='ft' style='width:70' id='rfsSpotAndSwapCoverExecutionMethod_" + rfsCount + "' name='workflowMessage.rfsSpotAndSwapCoverExecutionMethod_" + rfsCount + "'"
                            +  " onchange='validateSpotAndSwapCoverExec(this,\"" + rfsCount + "\")' nowrap>"
                            + "<option value='" + CoverExecutionMethod.Cover.name() + "'" + (ruleCondition.getExecutionMethodParameters().getSpotAndSwapCoverExecutionMethod() == CoverExecutionMethod.Cover ? "selected" : "") + ">" + CoverExecutionMethod.Cover.name() + "</option>"
                            + "<option value='" + CoverExecutionMethod.NoCover.name() + "'" + (ruleCondition.getExecutionMethodParameters().getSpotAndSwapCoverExecutionMethod() == CoverExecutionMethod.NoCover ? "selected" : "") + ">" + CoverExecutionMethod.NoCover.name()  + "</option>"
                            + "</select><span id='rfsSpotAndSwapCoverExecutionMethodNA_" + rfsCount + "'>N/A</span></td>");
                }

                out.println("<td nowrap rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input type='checkbox' id='rfsFullFill_" + rfsCount + "' name='workflowMessage.rfsFullFill_" + rfsCount + "' " + (ruleCondition.isFullFillEnabled() ? "checked" : "")
                        + " onclick='return validateRFSFullFillCheckBox(this,\"" + rfsCount + "\")'>&nbsp;&nbsp;<span id='rfsFullFillValText_"+rfsCount+"'><input type='text' id='rfsFullFillVal_" + rfsCount + "' name='workflowMessage.rfsFullFillVal_" + rfsCount + "' class='ftr' size='1' value='" + decimalFormat.format(ruleCondition.getFullFill()) + "' onblur='return validatePercentage(this)' onkeyup='changeButton()'>%</span></td>");
                out.println("<td rowspan='2' style='padding-left: 0px; padding-right: 0px;'><input type='checkbox' id='rfsAutoCover_" + rfsCount + "' name='workflowMessage.rfsAutoCover_" + rfsCount + "' " + (ruleCondition.isAutoCoverEnabled() ? "checked" : "") + " onclick='changeButton()' ><span id='rfsAutoCoverNA_" + rfsCount + "'>N/A</span></td>");




                out.println("</tr>");
				    // Second Row
                    out.println("<tr class='" + cls + "' style='text-align:center;padding-left: 0px; padding-right: 0px;' rowId = '" + rfsCount + "'>");
                    out.println("<td><table><tr class='" + cls + "' style='text-align:center;padding-left: 0px; padding-right: 0px;'><td style='letter-spacing: -0.05em;padding-left: 0px; padding-right: 0px;width: 260px;' id='rfsDescription_" + rfsCount + "' nowrap></td></tr></table></td>");
				//out.println("<td style='letter-spacing: -0.05em;padding-left: 0px; padding-right: 0px;' id='rfsDescription_" + rfsCount + "' nowrap></td>");
				  out.println("</tr>");

            %>

                <script>
                    validateRFSExecutionRuleCondition(<%=rfsCount%>, false); // pass true to turn on onLoad validation alerting & changeButton()
                </script>
                <%
                        rfsCount++;

                    }

           %>

           </table>
           <script>
               updateVWAPCheckbox(<%=multiFillEnabled && isDisableVWAPIfMultiFillEnabled%>);
           </script>
            <table id="rfsExcnAction">
               <tr>
                    <td class="f" colspan="11">
                        <span style="width:25px;">&nbsp;</span>
                        <input type="button" class="b1" value="Delete Rule"  onclick="javascript:deleteExecutionCondition('rfsExecutionConditions','RFS');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
                        <input type="button" class="b1" value="Add Rule" onclick="javascript:newExecutionCondition('rfsExecutionConditions','RFS');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
                        <input type="hidden" id='rfsCOUNTEXECUTIONCOND' name="workflowMessage.rfsCOUNTEXECUTIONCOND" value="<%=rfsRuleConditions.size()%>"/>
                   </td>
               </tr>
             </table><br><br>
        </td>
        <td class="f" nowrap style="display: <%=showRFS %>">
            &nbsp;
            <input type="checkbox" name="workflowMessage.allRFSExecMeth" onclick='allClick();' desc="Execution Method" scopeIndicator="scopeIndPME">
            <%
                if(marketRangeEnabledForRfsExecution){
            %>
            <input type="hidden" name="workflowMessage.marketRangeEnabledForRfsExecution"  value='on'>
            <%}%>
            <input type="hidden" name="workflowMessage.marketRangeForRfsExecution" value="<%=marketRangeForRfsExecution==null?"":decimalFormat.format(marketRangeForRfsExecution)%>">
        </td>
   </tr>
<%--
    <tr>
         <td class="f3" colspan="2" nowrap>Full Fill Method</td>
         <td class="f" colspan="4" nowrap>
           <input type="radio" name="workflowMessage.rfscustFullFillType" id="rfsCustFullFillType1" value='Always'  checked  disabled>&nbsp;Always&nbsp;
           <input type="radio" name="workflowMessage.rfscustFullFillType" id="rfsCustFullFillType2" value='FillAleastOne'  disabled>&nbsp;Cover has at least One fill&nbsp;
           <input type="radio" name="workflowMessage.rfscustFullFillType" id="rfsCustFullFillType3" value='Custom'    disabled>&nbsp;Custom&nbsp;
           <select name="customConditionForCustomerFullFill" onChange="selectCustFFDescription();changeButton()" disabled="disabled" class="ft" >
                        <option value="TwentyFivePercentage" selected="selected">Cover is Atleast 25% filled</option>
                        <option value="FiftyPercentage">Cover is Atleast 50% filled</option>
                        <option value="SeventyFivePercentage">Cover is Atleast 75% filled</option>
           </select>
         </td>
         <td  class="f">&nbsp;</td>
    </tr>

    <tr>
         <td class="f3" colspan="2" nowrap></td>
         <td class="f" colspan="4" nowrap>&nbsp;Customer trade is filled with the same amount as the cover trade</td>
         <td  class="f" >&nbsp;</td>
    </tr>
--%>
	<tr style="display: <%=showRFS %>" id="rfsExcnOvrInt">
		<td class="f3" nowrap><strong>Override Stream Update Interval</strong></td>
		<td class="f3" nowrap><input type="checkbox" id="overrideRFSUpdateInterval" name="workflowMessage.overrideRFSUpdateInterval" value="<%=overrideRFSUpdateInterval%>" <%=overrideRFSUpdateInterval.booleanValue()?"checked":""%> class="ft" onclick="changeButton();updateRFSIntervals();"></td>
        <td class="f3" nowrap>Full Stream Update Interval</td>
		<td class="f3" nowrap><input type="text" id = "rfsFullUpdateInterval" name="workflowMessage.rfsFullUpdateInterval" size="2" value="<%=(rfsFullUpdateInterval==null)?"":rfsFullUpdateInterval.toString()%>"
	        <%=overrideRFSUpdateInterval.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>milliseconds<script>registeredFields.add('rfsFullUpdateInterval')</script>(Stream Default:&nbsp;&nbsp;<%= stream.getQuotePublicationIntervalRFS() %>&nbsp;milliseconds)</td>
        <td class="f3"></td>
		<td class="f3"></td>
    </tr>
    <tr style="display: <%=showRFS %>" id="rfsExcnUpdInt">
        <td class="f3" colspan="2" nowrap></td>
        <td class="f3" nowrap>Best Bid/Offer Update Interval</td>
		<td class="f3" nowrap><input type="text" id = "rsfBestBidOfferInterval" name="workflowMessage.rsfBestBidOfferInterval" size="2" value="<%=(rsfBestBidOfferInterval==null)?"":rsfBestBidOfferInterval.toString()%>"
	        <%=overrideRFSUpdateInterval.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>milliseconds<script>registeredFields.add('rsfBestBidOfferInterval')</script>(Stream Default:&nbsp;<%= stream.getQuotePublicationCheckIntervalRFS() %>&nbsp;milliseconds)</td>
        <td class="f3"></td>
		<td class="f3"></td>
    </tr>
		<tr style="display: <%=showRFS %>">
        <td class="f" colspan="6"   nowrap>&nbsp;</td><td class="f" nowrap></td>
    </tr>
		<tr style="display: <%=showRFS %>" id="rfsExcnParamOvrInt">
		<td class="f3" nowrap><strong>Override Stream RFS Parameters</strong></td>
		<td class="f3" nowrap><input type="checkbox" id="overrideRFSParameters" name="workflowMessage.overrideRFSParameters" value="<%=overrideRFSParameters%>" <%=overrideRFSParameters.booleanValue()?"checked":""%> class="ft" onclick="changeButton();updateRFSParameters();"></td>
        <td class="f3" nowrap>Request Expiry</td>
		<td class="f3" nowrap><input type="text" id = "requsetExpiry" name="requsetExpiry" size="2" value="<%=(requestExpiry==null)?"":requestExpiry.toString()%>"
	        <%=overrideRFSParameters.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='changeButton()'></td>
		<td class="f3" nowrap>seconds <script>registeredFields.add('requsetExpiry')</script>(Stream Default:&nbsp;<%= stream.getRequestExpiry() %> &nbsp; seconds)</td>
        <td class="f3"></td>
		<td class="f3"></td>
    </tr>
    <tr style="display: <%=showRFS %>" id="rfsExcnParamUpdInt">
        <td class="f3" colspan="2" nowrap></td>
        <td class="f3" nowrap>Maximum Tenor</td>
		<td class="f3" nowrap><input type="text" id="maximumTenor" name="maximumTenor" size="2" value="<%=maxTenor==null?"":maxTenor.toString()%>" <%=overrideRFSParameters.booleanValue()?"":"disabled=true"%> class="ftr" onkeyup='this.value=this.value.toUpperCase();changeButton()'>
           <script>registeredFields.add('maximumTenor')</script> </td>
		<td class="f3" nowrap><span id="maximumTenorDesc"/></span> (Stream Default:&nbsp;&nbsp;<%= stream.getMaximumTenor().getName() %>)</td>
        <td class="f3"></td>
		<td class="f3"></td>
    </tr>

    <tr>
        <td id="autoExcnTag" class="f" colspan="6" style="font-size: 9pt;font-weight: bold;"  nowrap><b>Full-Fill Auto Cover</b></td><td class="f" nowrap></td>
    </tr>
    <tr>
       <td id="autoExcnDesc" class="f3" colspan="6" nowrap>Auto-Cover Execution Rules (If no rule criteria is met, Auto Cover execution will be done at Market)

              <table id="autoExecutionConditions" cellpadding="0" border="0" cellspacing="1" style="width:1636px;background-color: #999;">
           <tr class="lt" style="text-align:center">
                 <td rowspan="2" style="vertical-align:bottom;width: 10px"><input type="checkbox"></td>
                 <td rowspan="2" class="ac" width="2%">Sort</td>
                 <td rowspan="2">Action</td>
				 <td colspan="2">Notional Amount</td>
                 <td rowspan="2">Execution Method</td>
                 <td colspan="2">Limit Order</td>
                 <td colspan="1">Market Order</td>
           </tr>
           <tr class="lt" style="text-align:center">
		        <td>Low (>)</td>
                <td>High (<=)</td>
                <td>TIF(ms)</td>
                <td>Range</td>
                <td>TOB Range</td>
           </tr>

            <%
               ExecutionRule autoExecutionRule = configuration.getAutoCoverExecutionRule();
               Collection<ExecutionRuleCondition> autoRuleConditions = (autoExecutionRule !=null && autoExecutionRule.getExecutionRuleConditions() != null && autoExecutionRule.getExecutionRuleConditions().size() !=0) ? autoExecutionRule.getExecutionRuleConditions() : null ;
               if( autoRuleConditions == null ) {
                    autoRuleConditions = new ArrayList<ExecutionRuleCondition>();
                   /*ExecutionRuleCondition autoExecutionRuleCondition = ModelFactory.getInstance().newExecutionRuleCondition();
                   autoExecutionRuleCondition.setFullFillEnabled(true);
                   autoExecutionRuleCondition.setFullFill(100);
                   autoRuleConditions.add(autoExecutionRuleCondition);*/
                }
                int autoCount =0;
                for (ExecutionRuleCondition ruleCondition : autoRuleConditions) {
                    String cls = autoCount % 2 ==0? "tl" : "tlc";
                    out.println("<tr class='"+ cls +"' style='text-align:center' rowId = '" + autoCount + "'>");
                    out.println("<td><input type='checkbox' id='autoExecRuleCond_"+ autoCount +"' name='workflowMessage.autoExecRuleCond_"+ autoCount +"'> </td>");
                    out.println(" <td><input type='text' id='autoSortOrder_"+ autoCount +"'  name='workflowMessage.autoSortOrder_"+ autoCount +"' class='ftr' size='2' value='"+ruleCondition.getSortOrder()+"' onblur='changeButton()'> </td>");

                    out.println("<td nowrap><select  class='ft' style='width:155'  id='autoActionType_"+ autoCount +"'  name='workflowMessage.autoActionType_"+ autoCount +"'  onchange='changeButton()'>");
                    for (int j= 0;  j < autoCoverExecRuleConditionType.length; j++) {
                          out.println("<option value='"+autoCoverExecRuleConditionType[j].name()+"' "+(((ExecutionRuleConditionType)ruleCondition.getConditionType()) == autoCoverExecRuleConditionType[j] ? "selected" : "" )+"   >"+autoCoverExecRuleConditionType[j].getDescription()+"</option>");
                    }
                    out.println( "</select> </td>");

					                    out.println( "<td><input id='autoExecNotLow_"+autoCount+"'  name='workflowMessage.autoExecNotLow_"+autoCount+"' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getLowAmount(),decimalFormat)+"' size='9' ></td>");

                    out.println("<td><input id='autoExecNotHigh_"+autoCount+"' name='workflowMessage.autoExecNotHigh_"+autoCount+"' class='ftr' type='text' onblur='convertToNotionalFormatNumber(this);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getHighAmount(),decimalFormat) +"' size='9' ></td>");


                    out.println("<td nowrap><select  class='ft' style='width:155'  id='auotoExecutionMethod_"+ autoCount +"'  name='workflowMessage.autoExecutionMethod_"+ autoCount +"'  onchange='handleAutoExecutionChange(this," + autoCount + ");changeButton()'>");
                    for (int j= 0;  j < autoCoverExecutionmethods.length; j++) {
                          out.println("<option value='"+autoCoverExecutionmethods[j].name()+"' "+(((ExecutionMethod)ruleCondition.getExecutionMethod()) == autoCoverExecutionmethods[j] ? "selected" : "" )+"   >"+autoCoverExecutionmethods[j].getDescription()+"</option>");
                    }
                    out.println( "</select> </td>");

                    boolean isLimitMarket = false;
                    boolean isMarket = false;

                    if (ruleCondition.getExecutionMethod().name().equals(ExecutionMethod.LimitMarket.name())) {
                        isLimitMarket = true;
                    } else if (ruleCondition.getExecutionMethod().name().equals(ExecutionMethod.TopOfTheBook.name())) {
                        isMarket = true;
                    }
                    out.println( "<td><input id='autoLimitTif_"+ autoCount +"'  name='workflowMessage.autoLimitTif_"+ autoCount + "' class='ftr' type='text' onblur='userDecimalFormat.format(this,0);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getTimeInForceInterval(), decimalFormat) +"' size='9'" + (isMarket ? "disabled" : "") + "></td>");

                    out.println("<td><input id='autoLimitRange_"+ autoCount +"' name='workflowMessage.autoLimitRange_"+ autoCount + "' class='ftr' type='text' onblur='javascript:validateMarketRange(this);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getLimitRange(), decimalFormat) +"' size='9'" + (isMarket ? "disabled" : "") + "></td>");

                    out.println( "<td><input id='autoTOBRange_"+ autoCount +"'  name='workflowMessage.autoTOBRange_"+ autoCount + "' class='ftr' type='text' onblur='javascript:validateMarketRange(this, -1);changeButton()' value='"+ getFormattedNotionalAmountERC(ruleCondition.getTobRange(), decimalFormat) + "' size='9' ></td>");

                    out.println("</tr>");

                    autoCount++;
                }
               %>
        </table>
        <table>
           <tr>
                <td class="f" colspan="11">
                    <span style="width:25px;">&nbsp;</span>
                    <input type="button" class="b1" value="Delete Rule"  onclick="javascript:deleteExecutionCondition('autoExecutionConditions','Auto');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
                    <input type="button" class="b1" value="Add Rule" onclick="javascript:newAutoExecutionCondition('autoExecutionConditions','Auto');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
                    <input type="hidden" id='autoCOUNTEXECUTIONCOND' name="workflowMessage.autoCOUNTEXECUTIONCOND" value="<%=autoRuleConditions.size()%>"/>
               </td>
           </tr>
         </table><br><br>
    </td>
    <td class="f" nowrap>
        &nbsp;
        <input type="checkbox" name="workflowMessage.allAutoExecMeth" onclick='allClick();' desc="Execution Method" scopeIndicator="scopeIndPME">
   </td>
   </tr>

</table>
<div class="space"></div>
<table id="provConfigPanel" width="100%" cellpadding="0" cellspacing="0" class="outl">
	<tr>
		<td class="stl2" nowrap> <%=providersLabel %>&nbsp;&nbsp;
		 </td>
		<td class="stl3" nowrap>&nbsp;</td>
		<td class="stl4" nowrap>&nbsp;
		 
		  </td>
		<td class="stl8" nowrap>&nbsp;</td>
	</tr>
	<tr class="f3">
        <td nowrap class="f3v">
          <b>  Aggregation: </b>
        </td>
        <td class="f3" colspan="3" style="padding-right:5px">
            <span id=aggProviderNames></span>

        </td>
    </tr>
    <tr class="f3">
        <td nowrap class="f3v">
          <b>  Order Execution: </b>
        </td>
        <td class="f3" colspan="3" style="padding-right:5px">
            <span id=exeProviderNames></span>

        </td>
    </tr>
	 <tr class="f3" >
            <td colspan="3">&nbsp;</td>
            <td style="text-align:right;">Configure Providers <img width="16px" height="15px" id="providerAdvSettingsImage" src="<%=request.getContextPath()%>/theme/images/collapse_icon_button.gif" onclick="javascript:setProviderConfigVisibility('<%=request.getContextPath()%>')" alt="Collapse">&nbsp;</td>
     </tr>
</table>
<table id='AggregationPanelHeader' width="100%" cellpadding="0" cellspacing="0" class="outl">

	<tr class="f stl2">
        <td colspan="3">Aggregation &nbsp;&nbsp;
                                   		 <a class="bl" name="refLink1" href="javascript:helpPopup('Cpty/PriceMaking/StreamsEditCcyPairsEditCreate.html#csh_aggregation','Admin Portal Help')"><img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a>
                                   		</td>
        <td style="text-align: right; font-weight:normal" nowrap>&nbsp;<span id="scopeIndAgg">to Scope</span> <input type="checkbox" name="workflowMessage.allAggLPs" onclick="allClick();" desc="Aggregation LPs" scopeIndicator="scopeIndAgg">
        		</td>
    </tr>
</table>
<table  id='AggregationPanel' width="100%" cellpadding="0" cellspacing="0"  class="outl2" style="display: <%/*none*/%>;">
         	<tr>
                <td nowrap class="f3v" width="20%">Providers
                    <br><br><img src="<%=request.getContextPath()%>/theme/images/collapse_icon_button.gif" onclick='pando(["selectAggProvExclude","selectAggProvInclude"], false)'> Show Fewer.
                    <br><img src="<%=request.getContextPath()%>/theme/images/expand_icon_button.gif" onclick='pando(["selectAggProvExclude","selectAggProvInclude"], true)'> Show More.
                    <br><input type="checkbox" name="hideDOfromAggr" id="hideDOfromAggr" style="margin-left:-2px;padding-left:0;" onclick="javascript:toggleDOProviders('hideDOfromAggr', 'selectAggProvExclude', 'selectAggProvInclude');" <% if (isDisplayOrderAdminFunctionalityDisabled) {%> disabled="disabled" <%} %>/> Hide Displayed Order Providers
	
                </td>
                <td class="f3" width="20%" nowrap>
                    <%--<idcBean:message key="available" />--%>
                    Available<br>
                    <select class="ftms3" name="selectAggProvExclude" id="selectAggProvExclude" size="10" multiple style="width:300px;"></select>
                </td>
                <td class="f3" width="10%" nowrap style="text-align:center;">
					<input class="b1" onmouseover="this.className='b1over';" onmouseout="this.className='b1';" type="button" value=">>" onclick="doActivateSaveButton('selectAggProvExclude');doMergeToSelected('selectAggProvExclude', 'selectAggProvInclude'); doRemoveSelected('selectAggProvExclude')">
					<br><br><input class="b1" onmouseover="this.className='b1over';" onmouseout="this.className='b1';" type="button" value="<<" onclick="doActivateSaveButton('selectAggProvInclude');doMergeToSelected('selectAggProvInclude', 'selectAggProvExclude'); doRemoveSelected('selectAggProvInclude')">
                </td>
                <td class="f3" width="20%" nowrap>Selected<br>
                   <select class="ftms3" name="selectAggProvInclude" id="selectAggProvInclude" size="10" multiple style="width:300px;"></select>
                </td>

                <%--<td nowrap class="f3" width="20%">&nbsp;</td>--%>
                <td nowrap class="f3" width="20%" style="vertical-align:middle;">
                    <% if(ConfigurationFactory.getInstance().getConfigurationMBean().isCfdFailoverEnabled(org.getShortName())){ %>
                        <span id="cfdFailoverEnabledLabel">Enable Single provider Failover</span>
                        <input type="checkbox" id="cfdFailoverEnabled" name="cfdFailoverEnabled" <%=cfdFailoverEnabled ? "checked" : "" %> onclick="changeCfdFailover();" desc="Enable Single Provider Failover"><br>
                        <input type="hidden" name="workflowMessage.cfdFailoverEnabled" id="workflowMessage.cfdFailoverEnabled" value="<%= cfdFailoverEnabled %>">
                        <br>
                        &nbsp;<img src="<%=request.getContextPath()%>/theme/images/up_icon.gif" style="visibility: visible;" onclick=moveUp(document.ObjectActionForm.selectAggProvInclude)><br><img src="<%=request.getContextPath()%>/theme/images/down_icon.gif" style="visibility: visible;" onclick=moveDown(document.ObjectActionForm.selectAggProvInclude)>
                    <% } %>
                </td>
        	</tr>

            <tr>
<script>

$j('.mc').click(function(elem){
	//var id = $j(elem.target).prop('id');
	tenorValidation(elem.target);
});

$j(document).ready(function(){
	$j('.mc:checked').each(function(){
		//var id = $j(this).prop('id');
		tenorValidation(this);
	});

});



<% // todo put these two in common js? %>
// All Providers - ReadOnly
var allProviders = [];

// DO providers
//aggProvExcludeDO - ReadOnly
// Non DO providers
//aggProvExcludeNonDO  - ReadOnly

// Current Selected DO 
var hiddenDOSel = [];
// Current Excluded DO
hiddenDOItems = [];


    function doAggFromExec() {
        var aggOut = document.getElementById("selectAggProvExclude");
        var aggIn = document.getElementById("selectAggProvInclude");
        var exeIn = document.getElementById("selectOrderProvInclude");
        var aggAll = [];
        aggAll = merged(aggIn, aggOut);
        var aggNewIn = [];
        var aggNewOut = [];
        for (var i =0; i<aggAll.length; i++) {
            if (containsOption(exeIn.options, aggAll[i].text))
                aggNewIn[aggNewIn.length] = aggAll[i];
            else
                aggNewOut[aggNewOut.length] = aggAll[i];
        }
        aggOut.innerHTML = "";
        for (var i=0; i<aggNewOut.length; i++)
            aggOut.options[aggOut.options.length] = new Option(aggNewOut[i].text, aggNewOut[i].value);
        aggIn.innerHTML = "";
        for (var i=0; i<aggNewIn.length; i++)
            aggIn.options[aggIn.options.length] = aggNewIn[i];
        changeButton();
    }

    function copyProvidersFromAggToExecSimple()
    {
        let aggSelectElement = document.getElementById("selectAggProvInclude");
        let aggOptions = aggSelectElement.options;
        let execSelectElement = document.getElementById("selectOrderProvInclude");
        let execOptions = execSelectElement.options;
        execSelectElement.innerHTML = "";
        //copy all options from agg to exec
        for (var i = 0; i < aggOptions.length; i++) {
            let option = new Option(aggOptions[i].text, aggOptions[i].value);
            execSelectElement.add(option);
        }
        changeButton();
    }

	function doExecFromAgg()
	{
        var providerFailoverEnabledElement = document.getElementById("cfdFailoverEnabled");
        let providerFailoverEnabled = providerFailoverEnabledElement ? providerFailoverEnabledElement.checked : false;
        if(providerFailoverEnabled) {
            copyProvidersFromAggToExecSimple();
            return;
        }
<% /* todo tidy this up, I'm sure it can be smaller and neater */ %>
		var newExeInOptions = [];
        var newHiddenExInOptions = [];

		var exeIn = document.getElementById("selectOrderProvInclude");
		var exeInOptions = [];
		copyOptionsFromTo(exeIn, exeInOptions);

		var aggIn = document.getElementById("selectAggProvInclude");
		var aggToAdd = [];
		copyOptionsFromTo(aggIn, aggToAdd);

        // merge hidden and selected displayed order providerz
        var hiddenDOAggBackup = hiddenDOItems['selectAggProvInclude'];
        for( var i =0; i < hiddenDOAggBackup.length; i++) {
            aggToAdd[aggToAdd.length] = new Option(hiddenDOAggBackup[i].name, hiddenDOAggBackup[i].id);
        }

        var hiddenDOExecBackup = hiddenDOItems['selectOrderProvInclude'];
        var hideDOfromOEChecked= jQuery("#hideDOfromOE").prop("checked")?true:false;
        if (hiddenDOExecBackup.length == 0 && !hideDOfromOEChecked ){
            // iterate execLPs and keep if in the agg LPs
            for(var i = 0; i<exeInOptions.length; i++)
                if( containsOption(aggToAdd, exeInOptions[i].text) )
                    newExeInOptions[newExeInOptions.length] = exeInOptions[i];

            // iterate and take remaining aggLPs in agg order
            for(var i=0; i<aggToAdd.length; i++)
                if( !containsOption(newExeInOptions, aggToAdd[i].text) )
                    newExeInOptions[newExeInOptions.length] = aggToAdd[i];
        }else{
            // traverse hidden first
            for( var i =0; i < hiddenDOExecBackup.length; i++) {
                if (containsOption(aggToAdd, hiddenDOExecBackup[i].name)){
                    newHiddenExInOptions.push({"id":hiddenDOExecBackup[i].id, "name":hiddenDOExecBackup[i].name, "pos":i});
                }
            }
            // iterate execLPs and keep if in the agg LPs
            for(var i = 0; i<exeInOptions.length; i++)
                if( containsOption(aggToAdd, exeInOptions[i].text) )
                    newExeInOptions[newExeInOptions.length] = exeInOptions[i];

            // iterate and take remaining aggLPs in agg order
            var lengthOfNewHiddenDO = newExeInOptions.length;
            for(var i=0; i<aggToAdd.length; i++)   {
                if  (inObjArray( aggProvExcludeDO, "id", aggToAdd[i].value)){
                    if (!containsOptionInHidden (newHiddenExInOptions, aggToAdd[i].text)){
                        newHiddenExInOptions.push({"id":aggToAdd[i].value, "name":aggToAdd[i].text, "pos":lengthOfNewHiddenDO+i});
                    }
                }else{
                    if( !containsOption(newExeInOptions, aggToAdd[i].text) )
                        newExeInOptions[newExeInOptions.length] = aggToAdd[i];
                }
            }
        }
        // update execution provider display order hidden list
        hiddenDOItems['selectOrderProvInclude'] =  newHiddenExInOptions;

		// make a list of excluded exec LPs
		var exeOut = document.getElementById("selectOrderProvExclude");
		var newExeOutOptions = [];
		var exeAll = [];
		copyOptionsFromTo(exeIn, exeAll);
		copyOptionsFromTo(exeOut, exeAll);
		for(var i = 0; i < exeAll.length; i++)
			if(containsOption(newExeInOptions, exeAll[i].text)) exeAll[i] = null;

		// adjust the select contents
		exeOut.innerHTML = "";
		for(var i=0; i<exeAll.length; i++)
			if(exeAll[i] != null) exeOut.options[exeOut.options.length] = new Option(exeAll[i].text, exeAll[i].value);
		exeIn.innerHTML = "";
		for(var i=0; i<newExeInOptions.length; i++) exeIn.options[exeIn.options.length] = new Option(newExeInOptions[i].text, newExeInOptions[i].value);
        changeButton();
	}
</script>
		        <td colspan="3" class="f3" style="height: 5px;"></td>
		        <td class="f3" style="height: 5px;" align=center>
<% if( false ) {%>
					<img src="<%=request.getContextPath()%>/theme/images/up_icon.gif" onclick="doAggFromExec()"> Copy providers.
<% } else if( true) {%>
					<img src="<%=request.getContextPath()%>/theme/images/down_icon.gif" onclick="doExecFromAgg()"> Copy providers.
<% } %>
				</td>
		        <td class="f3" style="height: 5px;" align=center></td>

	        </tr>

  </table>


<table id="ExecutionPanelHeader" width="100%" cellpadding="0" cellspacing="0" class="outl">
	<tr class="f stl2">
		<td   nowrap>Order Execution&nbsp;&nbsp;<a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/StreamsEditCcyPairsEditCreate.html#csh_orderexecution','Admin Portal Help')"><img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a></td>
		<td  nowrap>&nbsp;</td>
		<td  nowrap>&nbsp;</td>
		<td style="text-align: right; font-weight:normal" nowrap>&nbsp;<span id="scopeIndExec">to Scope</span> <input type="checkbox" name="workflowMessage.allExecLPs" onclick="allClick();" desc="Execution LPs" scopeIndicator="scopeIndExec">
		</td>
	</tr>

</table>

<table id="ExecutionPanel" width="100%" cellpadding="0" cellspacing="0"  class="outl2" style="display: <%/*none*/%>;">
         	<tr>
                <td nowrap class="f3v" width="20%">Providers
					<br><br><img src="<%=request.getContextPath()%>/theme/images/collapse_icon_button.gif" onclick='pando(["selectOrderProvExclude","selectOrderProvInclude"], false)'> Show Fewer.
					<br><img src="<%=request.getContextPath()%>/theme/images/expand_icon_button.gif" onclick='pando(["selectOrderProvExclude","selectOrderProvInclude"], true)'> Show More.					 
					<br><input type="checkbox" name="hideDOfromOE" id="hideDOfromOE" style="margin-left:-2px;padding-left:0;" onclick="javascript:toggleDOProviders('hideDOfromOE', 'selectOrderProvExclude', 'selectOrderProvInclude' );" <% if (isDisplayOrderAdminFunctionalityDisabled) {%> disabled="disabled" <%} %>/> Hide Displayed Order Providers

				</td>
                <td class="f3" width="20%" nowrap>Available<br>
                    <select class="ftms3" name="selectOrderProvExclude" id="selectOrderProvExclude"  size="10" multiple style="width:300px;"></select>
                </td>
                <td class="f3" width="10%" nowrap style="text-align:center;">
                    <input class="b1" onmouseover="this.className='b1over';" onmouseout="this.className='b1';" type="button" value=">>" onclick="doActivateSaveButton('selectOrderProvExclude');javascript:doMoveSelected( 'selectOrderProvExclude', 'selectOrderProvInclude' )">
					<br><br><input value="<<" onclick="doActivateSaveButton('selectOrderProvInclude');javascript:doMergeToSelected('selectOrderProvInclude', 'selectOrderProvExclude' ); doRemoveSelected('selectOrderProvInclude');" class="b1" onmouseover="this.className='b1over';" onmouseout="this.className='b1';" type="button" >
                </td>
                <td class="f3" width="20%" nowrap>Selected<br>
                    <select class="ftms3" name="selectOrderProvInclude"  id="selectOrderProvInclude" size="10" multiple style="width:300px;"></select>
                </td>
                <td nowrap class="f3" width="20%" style="vertical-align:middle;">&nbsp;<img src="<%=request.getContextPath()%>/theme/images/up_icon.gif" onclick=moveUp(document.ObjectActionForm.selectOrderProvInclude)><br><img src="<%=request.getContextPath()%>/theme/images/down_icon.gif" onclick=moveDown(document.ObjectActionForm.selectOrderProvInclude)></td>
        	</tr>
            <tr>
		        <td colspan="5" class="f3" style="height: 5px;"></td>
	        </tr>
  </table>
<% if (isSpotAndSwapEnabled) {%>
<div class="space"></div>

<table width="100%" cellpadding="0" cellspacing="0" class="outl">
	<tr>
		<td class="stl2" nowrap>Swap Point Providers &nbsp;&nbsp;<a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/StreamsEditCcyPairsEditCreate.html#csh_orderexecution','Admin Portal Help')"><img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a></td>
		<td class="stl3" nowrap>&nbsp;</td>
		<td class="stl4" nowrap>&nbsp;</td>
		<td class="stl8" nowrap>&nbsp;</td>
	</tr>
</table>

<table width="100%" cellpadding="0" cellspacing="0" class="outl">
	 <tr class="f stl2">
            <td colspan="3">Aggregation and Order Execution</td>
            <td style="text-align: right; font-weight:normal" nowrap>&nbsp;<span id="scopeIndSwapPointExec">to Scope</span> <input type="checkbox" name="workflowMessage.allSwapLPs" onclick="allClick();" desc="Swap Point LPs" scopeIndicator="scopeIndSwapPointExec">
     </tr>
</table>
<table id="SwapPointProvidersPanel" width="100%" cellpadding="0" cellspacing="0"  class="outl2" style="display: <%/*none*/%>;">
         	<tr>
                <td nowrap class="f3v" width="20%">
                 Providers
                <br><br>
                &nbsp;&nbsp;&nbsp;<input type='radio' id='useAggProv' name='workflowMessage.useSwapProv'  value="AGG" onclick='changeUseSwapPointProviders("AGG");changeButton()' <%=!useSwapProviders?"checked":""%> />Use ESP and RFS Providers <br>
                &nbsp;&nbsp;&nbsp;<input type='radio' id='useSwapProv' name='workflowMessage.useSwapProv'  value="SWAP" onclick='changeUseSwapPointProviders("SWAP"); changeButton();' <%=useSwapProviders?"checked":""%> />Use Swap point Providers<br><br>

					<br><br><img src="<%=request.getContextPath()%>/theme/images/collapse_icon_button.gif" onclick='pando(["selectSwapPointProvExclude","selectSwapPointProvInclude"], false)'> Show Fewer.
					<br><img src="<%=request.getContextPath()%>/theme/images/expand_icon_button.gif" onclick='pando(["selectSwapPointProvExclude","selectSwapPointProvInclude"], true)'> Show More.
                    <br><input type="checkbox" name="hideDOfromSwapProv" id="hideDOfromSwapProv" style="visibility: hidden;margin-left:-2px;padding-left:0;" onclick="javascript:toggleDOProviders('hideDOfromSwapProv', 'selectSwapPointProvExclude', 'selectSwapPointProvInclude' );" <% if (isDisplayOrderAdminFunctionalityDisabled) {%> disabled="disabled" <%} %>/>

				</td>
                <td class="f3" width="20%" nowrap>Available<br>
                    <select class="ftms3" name="selectSwapPointProvExclude" id="selectSwapPointProvExclude"  size="10" multiple style="width:300px;"></select>
                </td>
                <td class="f3" width="10%" nowrap style="text-align:center;">
                    <input id='btnSwapProvIncl' class="b1" onmouseover="this.className='b1over';" onmouseout="this.className='b1';" type="button" value=">>" onclick="doActivateSaveButton('selectSwapPointProvExclude');javascript:doMoveSelected( 'selectSwapPointProvExclude', 'selectSwapPointProvInclude' )">
					<br><br><input id='btnSwapProvExcl' value="<<" onclick="doActivateSaveButton('selectSwapPointProvInclude');javascript:doMergeToSelected('selectSwapPointProvInclude', 'selectSwapPointProvExclude' ); doRemoveSelected('selectSwapPointProvInclude');" class="b1" onmouseover="this.className='b1over';" onmouseout="this.className='b1';" type="button" >
                </td>
                <td class="f3" width="20%" nowrap>Selected<br>
                    <select class="ftms3" name="selectSwapPointProvInclude"  id="selectSwapPointProvInclude" size="10" multiple style="width:300px;"></select>
                </td>
                <td nowrap class="f3" width="20%" style="vertical-align:middle;">&nbsp;<img src="<%=request.getContextPath()%>/theme/images/up_icon.gif" style="visibility: hidden;" onclick=moveUp(document.ObjectActionForm.selectSwapPointProvInclude)><br><img src="<%=request.getContextPath()%>/theme/images/down_icon.gif" style="visibility: hidden;" onclick=moveDown(document.ObjectActionForm.selectSwapPointProvInclude)></td>
        	</tr>
            <tr>
		        <td colspan="5" class="f3" style="height: 5px;"></td>
	        </tr>
  </table>

<div class="space"></div>
 <%} %>
<input type="hidden" name="workflowMessage.spreadMinimumEnabled"  id="workflowMessage.spreadMinimumEnabled" value="<%=isMinSprdEnabled%>"/>
<input type="hidden" name="workflowMessage.spreadMaximumEnabled"  id="workflowMessage.spreadMaximumEnabled" value="<%=isMaxSprdEnabled%>"/>
<input type="hidden" name="workflowMessage.singleTier" id="workflowMessage.singleTier"  value=""/>

<div class="space"></div>
<div id='tiersDivHeader' style="display:'none';">
    <table width="100%" cellpadding="0" cellspacing="0" class="outl">
		<tr>
			<td class="stl2" nowrap>ESP Tiers and Spreads&nbsp;&nbsp;
                <a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/StreamsEditCcyPairsEditCreate.html#csh_tiers','Admin Portal Help')"><img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a>
            </td>
			<td class="stl3" nowrap>&nbsp;</td> <td class="stl4" nowrap>&nbsp;
			 
			 </td>
			<td class="stl8" nowrap>&nbsp;
                <%--<a id="Tier" class="lnuh" target="body" href="/admin/Forward.do?forwardURI=Admin.Configuration.Detail&objectForm.objectType=com.integral.broker.model.ConfigurationC&objectForm.encryptedId=<%=conf.getEncryptedObjectId()%>&newTier=true&encryptedObjectId="<%=organization.getEncryptedObjectId() %>>New</a>&nbsp;--%>
			</td>
		</tr>
    </table>
</div>

<div id="espSpreadsHeader">
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
        <tr>
			<td class="stl2" nowrap>ESP Spreads&nbsp;&nbsp;
                <a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/StreamsEditCcyPairsEditCreate.html#csh_esp','Admin Portal Help')"><img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a>
            </td>
            <td class="stl3" nowrap>&nbsp;</td> <td class="stl4" nowrap>&nbsp;
			</td>
			<td class="stl8" nowrap>&nbsp;
			</td>
		</tr>
</table>
</div>
<div id="espSpreads">
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr class="f" >
        <td>Spread Unit</td>
        <td><input type="radio" name="workflowMessage.espSpreadUnit" id="espSpreadUnitPips" value="Pips" onclick="changeEspSpreadUnit('Pips');changeButton();" <%=(!isBPS&&!isSpreadPercent)?"checked":""%>></td>
        <td>Pips</td>
        <td style="display: <%= showBps %>"><input type="radio" name="workflowMessage.espSpreadUnit" id="espSpreadUnitBasisPoint" value="BPS" onclick="changeEspSpreadUnit('BPS');changeButton();" <%=(isBPS&&!isSpreadPercent)?"checked":""%>></td>
        <td style="display: <%= showBps %>">BPS</td>
        <% if(showBps!=null&&showBps.equals("none")){ %>
		<td style="display: <%= ""%>"><td>
		<% }%>
        <td style="display: <%= showSpreadPercent %>"><input type="radio" name="workflowMessage.espSpreadUnit" id="espSpreadUnitSpreadPercent" value="Spread_Percent" onclick="changeEspSpreadUnit('Spread_Percent');changeButton();" <%=isSpreadPercent?"checked":""%>>&nbsp;Spread Percent
        <td style="display: <%= showSpreadPercent.equals("none")?"":"'none';"%>"> <td>
		</td>
    </tr>
	<tr class=f>
		<td>Minimum Spread</td>
		<td>
			<input type="checkbox" name="minSprdEnabledCB" id="minSprdEnabledCB" <%=isMinSprdEnabled.booleanValue()?"checked":""%> class="ft" onclick='changeButton()'>
			<input type="hidden" name="workflowMessage.spreadMinimumEnabled"  id="workflowMessage.spreadMinimumEnabled" value="<%=isMinSprdEnabled%>"/>
			<script>registerChange('workflowMessage.spreadMinimumEnabled','minSprdEnabledCB');</script>
		</td>
		<td>
			<div id="espSpreadsMin">
				<%
					String spreadMin = "0";
					try { spreadMin = decimalFormat.format(((Double)spreadMinimum).doubleValue()); } catch(Exception e) {}
				%>
				<idcHtml:text name="ObjectActionForm" styleClass="ftr" property="spreadMinimum" styleId="spreadMinimum"  size="6" value="<%=spreadMin%>" onblur="validateMinimumSpread(this);" onkeyup='changeButton()'/>
            	<script>registeredFields.add('spreadMinimum')</script>
				&nbsp;<label class="espSpreadUnitLbl"><%=(espSpreadUnit.name().equals("Spread_Percent")?"Pips":espSpreadUnit.name())%></label>
			</div>
		</td>
		<td>Bias</td>
		<td>
			<idcHtml:select name="ObjectActionForm" property="spreadMinimumBias"  styleId="spreadMinimumBias" value='<%=spreadMinBiasConfig==null?"":((SpreadBias)spreadMinBiasConfig).name() %>' onchange='changeButton()'>
				<idcLogic:iterate id="sprdBias" collection="<%= collSpreadBiases %>" type="SpreadBias">
					<idcHtml:option value="<%= sprdBias.name()%>"> <%= sprdBias.name()%> </idcHtml:option>
				</idcLogic:iterate>
			</idcHtml:select>
			<script>registeredFields.add('spreadMinimumBias')</script>
			<% // todo JSP level validation changes for new names ... %>
			<input type="hidden" name="workflowMessage.spreadBias" value="Bid"/>
			<script>registerChange('workflowMessage.spreadBias','spreadMinimumBias');</script>
		</td>
		<td style="width: 70%"></td>
	</tr>
	<tr class=f>
		<td>Maximum Spread</td>
		<td>
			<input type="checkbox" name="maxSprdEnabledCB" id="maxSprdEnabledCB" <%=isMaxSprdEnabled.booleanValue()?"checked":""%> class="ft" onclick='changeButton()'>
			<input type="hidden" name="workflowMessage.spreadMaximumEnabled" id="workflowMessage.spreadMaximumEnabled" value="<%=isMaxSprdEnabled%>"/>
			<script>registerChange('workflowMessage.spreadMaximumEnabled','maxSprdEnabledCB');</script>
		</td>
		<td>
			<div id="espSpreadsMax">
				<%
					String spreadMax = "0";
					try { spreadMax = decimalFormat.format(((Double)spreadMaximum).doubleValue()); } catch(Exception e) {}
				%>
				<% // todo validation? %>
				<idcHtml:text name="ObjectActionForm"  styleClass="ftr" property="spreadMaximum" styleId="spreadMaximum" size="6" value="<%=spreadMax%>" onblur="validateMinimumSpread(this);" onkeyup='changeButton()'/>
            	<script>registeredFields.add('spreadMaximum')</script>
				&nbsp;<label class="espSpreadUnitLbl"><%=(espSpreadUnit.name().equals("Spread_Percent")?"Pips":espSpreadUnit.name())%></label>
			</div>
		</td>
		<td>Bias</td>
		<td>
			<idcHtml:select name="ObjectActionForm" property="spreadMaximumBias" styleId="spreadMaximumBias"  value='<%=spreadMaxBiasConfig==null?"":((SpreadBias)spreadMaxBiasConfig).name() %>' onchange='changeButton()'>
				<idcLogic:iterate id="sprdBias" collection="<%= collSpreadBiases %>"  type="SpreadBias">
					<idcHtml:option value="<%= sprdBias.name()%>"> <%= sprdBias.name()%> </idcHtml:option>
				</idcLogic:iterate>
			</idcHtml:select>
			<script>registeredFields.add('spreadMaximumBias')</script>
		</td>
		<td style="width: 70%"></td>
	</tr>
	<script>
		function spreadTypeCBClicked(cb) {document.getElementById("workflowMessage."+cb.name).value = cb.checked ? '<%=SpreadType.BIDOFFER.getId()%>' : '<%=SpreadType.DISABLED.getId()%>';}
	</script>
	<tr class=f>
            <td>Pre-Trade Fixed Spread</td>
            <td>
				<% // todo better handling for old 'mid' data or will migration take care of it?
					SpreadType iESPType = configuration.getOriginalESPSpread() == null ? SpreadType.DISABLED : configuration.getOriginalESPSpread().getType();
					iESPType = iESPType == SpreadType.MIDONLY ? SpreadType.DISABLED : iESPType;
					boolean isESPFixedEnabled = iESPType != SpreadType.DISABLED;
				%>
                <input type="checkbox" name="espSpreadType"  id="espSpreadType" <%=isESPFixedEnabled?"checked":""%> class="ft" onclick='spreadTypeCBClicked(this); changeButton()'>
                <input type="hidden" name="workflowMessage.espSpreadType"  id="workflowMessage.espSpreadType" value="<%=iESPType.getId()%>"/>
            </td>
				<td>Preserve</td>
				<td>
                <% boolean isPreserveSpreadESP = espSpread != null ? espSpread.isSpreadPreserved() : true; %>
                <input type="checkbox" name="espPreserveSpread" id="espPreserveSpread" <%=isPreserveSpreadESP?"checked":""%> class="ft" onclick='changeButton()'>
                <input type="hidden" name="workflowMessage.spreadPreservedESP"  id="workflowMessage.spreadPreservedESP" value="<%=isPreserveSpreadESP%>"/>
                <script>registerChange('workflowMessage.spreadPreservedESP','espPreserveSpread');</script></td>
		<td colspan="2" style="width: 70%"></td>
	</tr>
	<tr class=f>
            <td>Post-Trade Fixed Spread</td>
            <td>
				<%
					SpreadType iESPTypePost = configuration.getOriginalESPSpread() == null ? SpreadType.DISABLED : configuration.getOriginalESPSpread().getTypePost();
					iESPType = iESPTypePost == SpreadType.MIDONLY ? SpreadType.DISABLED : iESPTypePost;
					boolean isESPFixedPostEnabled = iESPTypePost != SpreadType.DISABLED;
                    boolean isEspHourglass = configuration.isHourglassPricingSupported();
				%>
                <input type="checkbox" name="espSpreadTypePost" id="espSpreadTypePost" <%=isESPFixedPostEnabled?"checked":""%> class="ft" onclick='spreadTypeCBClicked(this); changeButton()'>
                <input type="hidden" name="workflowMessage.espSpreadTypePost"  id="workflowMessage.espSpreadTypePost"  value="<%=iESPTypePost.getId()%>"/>
            </td>
            <td>
                <span id="useOrderAmountToDerivePostTradeSpreadLabel">Use Order Amount</span>
            </td>
            <td>
                <input type="checkbox" name="setUseOrderAmountToDerivePostTradeSpread" id="setUseOrderAmountToDerivePostTradeSpread" <%=configuration.getUseOrderAmountToDerivePostTradeSpread()?"checked":""%> class="ft" onclick='spreadTypeCBClicked(this); changeButton()'>
                <input type="hidden" name="workflowMessage.setUseOrderAmountToDerivePostTradeSpread" id="workflowMessage.setUseOrderAmountToDerivePostTradeSpread" value="<%=configuration.getUseOrderAmountToDerivePostTradeSpread()%>"/>
                <script>registerChange('workflowMessage.setUseOrderAmountToDerivePostTradeSpread','setUseOrderAmountToDerivePostTradeSpread');</script></td>
		<td colspan="2" style="width: 70%"></td>
	</tr>
    <tr class="f" <%=hideHourglassRow %>>
        <td>Enable Hourglass Pricing</td>
        <td colspan="5">
            <input type="checkbox" name="espHourglass" id="espHourglass" <%=isEspHourglass ? "checked" : "" %> class="ft" onclick="changeHourglass()">
            <input type="hidden" name="workflowMessage.espHourglass" id="workflowMessage.espHourglass" value="<%= isEspHourglass %>">
        </td>
    </tr>

	<tr id="espSpreadsFixedHeader" class="f stl2">
		<td colspan="6" nowrap>Fixed ESP Spread</td>
	</tr>
</table>
</div>

<div id='tiersDiv' style="display:'none';">
    <div id='otherCcy' style="display:'none';">
    <table width="100%" cellpadding="0" cellspacing="1" class="outl3">
        <tr class="f3">
            <td class="f3" nowrap>
                <input type="radio" name="currencyType" value="Base" <%=(tierCurrency==null)?"checked":""%> onclick= 'changeButton()'>&nbsp;Base Currency&nbsp;
                <input type="radio" name="currencyType" value="Other" <%=(tierCurrency!=null)?"checked":""%> onclick= 'changeButton()'>&nbsp;Other&nbsp;
                <script>registeredFields.add('currencyType')</script>
                <select name="tierCurrency" id="tierCurrency"  class="ft" onchange='changeButton()' ></select>
                <script>registeredFields.add('tierCurrency')</script>
            </td>
            <td nowrap class="f3" width="100%">
            </td>
        </tr>
    </table>
    </div>
    <div id='BaseCcy' style="display:'';">
       <table width="100%" cellpadding="0" cellspacing="1" class="outl3">
          <tr class="f3">
            <td class="f3" nowrap width="100%">
                <input type="hidden" name="currencyType"  id="currencyType" value="Base">&nbsp;Base Currency&nbsp;
                <script>registeredFields.add('currencyType')</script>
            </td>
          </tr>
       </table>
    </div>
   <%
       int lastTierSortOrder=-1;
   %>
<% /*  note: we hide tier max/min bias from 3.4.8 use config level bias but avoid un-hooking everything for a while. todo later unhook and remove tier bias */ %>
<table id="tiersHolder" width="100%" border="0" cellpadding="0" cellspacing="1" bgcolor="999999">
    <tr class="lt" style="text-align:center;">
        <td rowspan=2 width="2%"><input class="ft" type="checkbox" id="checkAll" name="checkAll" onclick='doCheckOrUncheckAll()'></td>
        <td rowspan=2 nowrap class="ac" width="10%">Tier</td>
        <td rowspan=2 nowrap width="20%">Bid Limit</td>
        <td rowspan=2 nowrap width="20%">Offer Limit</td>
        <td rowspan=2 width="20%">Minimum Spread (<label id="minSpread" class="espSpreadUnitLbl"><%=(espSpreadUnit.name().equals("Spread_Percent"))?"Pips":espSpreadUnit.name()%></label>)</td>
        <td rowspan=2 width="20%">Maximum Spread (<label id="maxSpread" class="espSpreadUnitLbl"><%=(espSpreadUnit.name().equals("Spread_Percent")?"Pips":espSpreadUnit.name())%></label>)</td>
        <td colspan=2 nowrap width="20%">Pre-Trade Fixed Spread (<label class="espSpreadUnitLbl"><%=(espSpreadUnit.name().equals("Spread_Percent")?"Spread Percent":espSpreadUnit.name())%></label>)</td>
        <td colspan=2 nowrap width="20%">Post-Trade Fixed Spread (<label class="espSpreadUnitLbl"><%=(espSpreadUnit.name().equals("Spread_Percent")?"Spread Percent":espSpreadUnit.name())%></label>)</td>
    </tr>
    <tr class="lt" style="text-align:center;">
        <td nowrap width="20%" style="display: none;">Bias</td>
        <td nowrap width="20%" style="display: none;">Bias</td>
        <td nowrap width="20%">Bid</td>
        <td nowrap width="20%">Offer</td>
        <td nowrap width="20%">Bid</td>
        <td nowrap width="20%">Offer</td>
    </tr>

<% String trc = "tl"; %>
<idcLogic:iterate id="aTier" collection="<%=tiers%>" type="TierC" >
	<% String atpp = "objectForm.tiers.entityForm(" + aTier.getEncryptedObjectId() + ")"; %>
    <%
        Tier currTier=(Tier)aTier;
        int tierSortOrder=currTier.getSortOrder();
        lastTierSortOrder=tierSortOrder;
        String currSpreadBias=currTier.getSpreadBias().name();
        String currSpreadBiasMax = currTier.getMaxSpreadBias().name();
    %>
    <script type="text/javascript">
        tiersAdded.add(<%=tierSortOrder%>);
    </script>
    <tr class="<%=trc%>" style="text-align:center;">
		<td nowrap><input type="checkbox" name="selectedTiers" value='<%=tierSortOrder%>'></td>
		<td nowrap class="ac">Tier <%=tierSortOrder+1%></td>
		<td nowrap class="ar">
            <input type="text" id='<%="Bid_"+tierSortOrder%>' name ='<%="workflowMessage.Bid_"+tierSortOrder%>' value='<%=currTier.getBidLimit()==null?"+":getFormattedNotionalAmountERC(currTier.getBidLimit(),decimalFormat)%>' onblur="convertTierLimitToNotionalFormatNumber(this);" onkeyup='changeButton()' size="14" class="ftr" >
            <script type="text/javascript">
                var currentBidHTMLObject=document.getElementById('<%="Bid_"+tierSortOrder%>');
                if (!(currentBidHTMLObject.value=='+'))
                    notionalAmountFormat.format(currentBidHTMLObject);
            </script>
        </td>
		<td nowrap class="ar">
            <input type="text" id='<%="Offer_"+tierSortOrder%>' name ='<%="workflowMessage.Offer_"+tierSortOrder%>' value='<%=currTier.getOfferLimit()==null?"+":getFormattedNotionalAmountERC(currTier.getOfferLimit(),decimalFormat)%>' onblur="convertTierLimitToNotionalFormatNumber(this);" onkeyup='changeButton()' size="14" class="ftr" >
            <script type="text/javascript">
                var currentOfferHTMLObject=document.getElementById('<%="Offer_"+tierSortOrder%>');
                if (!(currentOfferHTMLObject.value=='+'))
                    notionalAmountFormat.format(currentOfferHTMLObject);
            </script>
        </td>
		<td nowrap class="ar">
            <input type="text" id='<%="Spread_"+tierSortOrder%>' name ='<%="workflowMessage.Spread_"+tierSortOrder%>' value='<%=currTier.getSpreadMinimum()%>' onblur="validateMinimumSpread(this);" onkeyup='changeButton()' size="6" class="ftr" >
        </td>
		<td nowrap class="ar" style="display: none;">
			<select id='<%="Bias_"+tierSortOrder%>' name ='<%="workflowMessage.Bias_"+tierSortOrder%>' class="ft" onchange='changeButton()' >
        <%
            for (int count=0;count<spreadBiases.length;count++)
            {
            SpreadBias spreadBias=spreadBiases[count];
        %>
               <option value="<%=spreadBias.name()%>" <%=spreadBias.name().equalsIgnoreCase(currSpreadBias) ? "selected" : ""%>><%=spreadBias.name()%></option>

        <%
            } // njb todo can this be factored to some basisOptions[0] ? say
        %>
            </select>
        </td>
        <td nowrap class="ar">
            <input type="text" id='<%="SpreadMax_"+tierSortOrder%>' name ='<%="workflowMessage.SpreadMax_"+tierSortOrder%>' value='<%=currTier.getSpreadMaximum()%>' onblur="validateTierMaximumSpread(this);" onkeyup='changeButton()' size="6" class="ftr" >
        </td>
        <td nowrap class="ar" style="display: none;">
            <select id='<%="BiasMax_"+tierSortOrder%>' name ='<%="workflowMessage.BiasMax_"+tierSortOrder%>' class="ft" onchange='changeButton()' >
            <%
                for(int count = 0; count < spreadBiases.length; count++)
                {
                    SpreadBias spreadBias = spreadBiases[count];
            %>
                    <option value="<%=spreadBias.name()%>" <%=spreadBias.name().equalsIgnoreCase(currSpreadBiasMax) ? "selected" : ""%>><%=spreadBias.name()%></option>
            <%
                }
            %>
            </select>
        </td>
        <td nowrap class="ar">
            <input type="text" id='<%="SpreadFixedBid_"+tierSortOrder%>' name ='<%="workflowMessage.SpreadFixedBid_"+tierSortOrder%>' value='<%=currTier.getSpreadFixedBid()%>' onblur="validateTierFixedBidSpread(this);" onkeyup='changeButton()' size="6" class="ftr" >
        </td>
        <td nowrap class="ar">
            <input type="text" id='<%="SpreadFixedOffer_"+tierSortOrder%>' name ='<%="workflowMessage.SpreadFixedOffer_"+tierSortOrder%>' value='<%=currTier.getSpreadFixedOffer()%>' onblur="validateTierFixedOfferSpread(this);" onkeyup='changeButton()' size="6" class="ftr" >
        </td>
        <td nowrap class="ar">
            <input type="text" id='<%="SpreadFixedPostBid_"+tierSortOrder%>' name ='<%="workflowMessage.SpreadFixedPostBid_"+tierSortOrder%>' value='<%=currTier.getSpreadFixedPostBid()%>' onblur="validateTierFixedBidPostSpread(this);" onkeyup='changeButton()' size="6" class="ftr" >
        </td>
        <td nowrap class="ar">
            <input type="text" id='<%="SpreadFixedPostOffer_"+tierSortOrder%>' name ='<%="workflowMessage.SpreadFixedPostOffer_"+tierSortOrder%>' value='<%=currTier.getSpreadFixedPostOffer()%>' onblur="validateTierFixedOfferPostSpread(this);" onkeyup='changeButton()' size="6" class="ftr" >
        </td>
    </tr>
	<% trc = trc.equals("tl") ? "tlc" : "tl"; %>
</idcLogic:iterate>
     <script>
         tierSortOrder=<%=lastTierSortOrder%>;
         lastTierSuffix=<%=lastTierSortOrder%>;
         spreadBiasesOptions = '<%=spreadBiasesOptions%>';         
     </script>
</table>

<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
	<tr>
		<td class="f3" nowrap width="25%"><input type="button" name="Add" value="Add" class="b1" onclick="addNewTier();changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
		<input type="button" name="Remove" value="Remove" class="b1" onclick="removeTiers();" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
		</td>
	</tr>
</table>
</div>

<div id="espSpreadsFixed">
<table  width="100%" border="0" cellpadding="0" cellspacing="1" class="outl2">
    <tr class="lt" style="text-align:center;">
        <td width="5%"></td>
        <td nowrap colspan="2" width="30%">Pre-Trade Fixed Spreads (<label class="espSpreadUnitLbl"><%=(espSpreadUnit.name().equals("Spread_Percent")?"Spread Percent":espSpreadUnit.name())%></label>)</td>
        <td nowrap colspan="2" width="30%">Post-Trade Fixed Spreads (<label class="espSpreadUnitLbl"><%=(espSpreadUnit.name().equals("Spread_Percent")?"Spread Percent":espSpreadUnit.name())%></label>)</td>
    </tr>
    <tr class="lt" style="text-align:center;">
       <td nowrap>&nbsp;</td>
       <td nowrap>Bid</td>
       <td nowrap>Offer</td>
       <td nowrap>Bid</td>
       <td nowrap>Offer</td>
    </tr>
    <% SpreadType sprdType = espSpread != null ? espSpread.getType() : null; %>
    <tr class="f3">
            <td class="f3" nowrap>SPOT</td>
            <input type="hidden" name="espSpreadType" value='<%=(sprdType!=null)?sprdType.getId():"0"%>'/>
            <script>registeredFields.add('espSpreadType');</script>
            <td nowrap class="ac">
                <input type="text"  name ="workflowMessage.ESPBidSprd" value='<%=(espSpread!=null)?decimalFormat.format(espSpread.getBidSpread()):decimalFormat.format(0)%>'
                       size="10" class="ftr" onblur="validateESPSpread(this,true)" onkeyup='changeButton()'/>
            </td>
            <td nowrap class="ac">
                <input type="text"  name ="workflowMessage.ESPOfferSprd" value='<%=(espSpread!=null)?decimalFormat.format(espSpread.getOfferSpread()):decimalFormat.format(0)%>'
                       size="10" class="ftr" onblur="validateESPSpread(this,true)" onkeyup='changeButton()'/>
            </td>

            <td nowrap class="ac">
                <input type="text"  name ="workflowMessage.ESPBidPostSprd" value='<%=(espSpread!=null)?decimalFormat.format(espSpread.getBidPostSpread()):decimalFormat.format(0)%>'
                       size="10" class="ftr" onblur="validateESPSpread(this)" onkeyup='changeButton()'/>
            </td>
            <td nowrap class="ac">
                <input type="text"  name ="workflowMessage.ESPOfferPostSprd" value='<%=(espSpread!=null)?decimalFormat.format(espSpread.getOfferPostSpread()):decimalFormat.format(0)%>'
                       size="10" class="ftr" onblur="validateESPSpread(this)" onkeyup='changeButton()'/>
            </td>
    </tr>
</table>
</div>

<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl" style="display: <%=showRFS %>" id="rfsSpreadHeader">
        <tr>
			<td class="stl2" nowrap>RFS Spreads&nbsp;&nbsp;<a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/StreamsEditCcyPairsEditCreate.html#csh_rfs','Admin Portal Help')"><img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a></td>
			<td class="stl3" nowrap>&nbsp;</td> <td class="stl4" nowrap>&nbsp;
			</td>
			<td class="stl8" nowrap>&nbsp;
			</td>
		</tr>
</table>



<table width="100%" cellpadding="0" cellspacing="1" class="outl3" style="display: <%=showRFS %>" id="rfsSpreadTab1">
<tr class=f>
        <td>Preserve</td>
        <td>
        <%
// todo move this to the spreads? c.spotSpread.preserve =  & c.swap.... ??
            boolean isPreserveSpreadRFS = (null==configuration || configuration.getObjectId()==-1||configuration.getObjectId()==0)?true:configuration.isOriginalRFSSpreadPreserved();
        %>
            <input type="checkbox" name="rfsPreserveSpread" id="rfsPreserveSpread" <%=isPreserveSpreadRFS?"checked":""%> class="ft" onclick='changeButton()'>
            <input type="hidden" name="workflowMessage.spreadPreservedRFS" id="workflowMessage.spreadPreservedRFS" value="<%=isPreserveSpreadRFS%>"/>
            <script>registerChange('workflowMessage.spreadPreservedRFS','rfsPreserveSpread');</script>&nbsp;
        </td>
        <td colspan="4" style="width: 70%"></td>
	</tr>
    <tr class="f stl2">
        <td colspan="6">Fixed RFS Spread</td>
    </tr>
    <tr class="f" style="display: <%= showBps %>">
        <td>Spread Unit</td>
        <td>
        	<input type="radio" name="workflowMessage.rfsSpotSpreadUnit" id="rfsSpotSpreadUnitPips" value="Pips" onclick="changeRfsSpotSpreadUnit('Pips');changeButton();" <%=!isRfsSpotBPS?"checked":""%>>&nbsp;Pips&nbsp;&nbsp;&nbsp;
        	<input type="radio" name="workflowMessage.rfsSpotSpreadUnit" id="rfsSpotSpreadUnitBPS" value="BPS" onclick="changeRfsSpotSpreadUnit('BPS');changeButton();" <%=isRfsSpotBPS?"checked":""%>>&nbsp;BPS
        </td>
        <td colspan="4"  style="width: 70%"></td>
    </tr>
    <tr class=f>
        <td>Minimum Spread</td>
        <td>
			<input type="checkbox" name="minRFSSprdEnabledCB" id="minRFSSprdEnabledCB" <%=isMinRFSSprdEnabled.booleanValue()?"checked":""%> class="ft" onclick='changeButton()'>
    		<input type="hidden" name="workflowMessage.rfsSpreadMinimumEnabled"  id="workflowMessage.rfsSpreadMinimumEnabled" value="<%=isMinRFSSprdEnabled%>"/>
			<script>registerChange('workflowMessage.rfsSpreadMinimumEnabled','minRFSSprdEnabledCB');</script>
        </td>
        <td>
			<%
				String rfsSpreadMin = "0";
				try { rfsSpreadMin = decimalFormat.format(((Double)rfsSpreadMinimum).doubleValue()); } catch(Exception e) {}
			%>
			<% // todo validation? %>
            <div id="rfsSpreadMinDiv" <%=isMultiTierRfsSpotSpreads ? "style='display: none'" : ""%> >
			<idcHtml:text name="ObjectActionForm"  styleClass="ftr" property="rfsSpreadMinimum" id="rfsSpreadMinimum" size="6" value="<%=rfsSpreadMin%>" onblur="validateMinimumSpread(this);" onkeyup='changeButton()'/>&nbsp;<label class="rfsSpotSpreadUnitLbl"><%=rfsSpotSpreadUnit.name()%></label>
            </div>
            <script>registeredFields.add('rfsSpreadMinimum')</script>
        </td>
        <td>Bias</td>
        <td>
			<idcHtml:select name="ObjectActionForm" property="rfsSpreadMinimumBias" styleId="rfsSpreadMinimumBias" value='<%=rfsSpreadMinBiasConfig==null?"":((SpreadBias)rfsSpreadMinBiasConfig).name() %>' onchange='changeButton()'>
				<idcLogic:iterate id="sprdBias" collection="<%= collSpreadBiases %>" type="SpreadBias">
					<idcHtml:option value="<%= sprdBias.name()%>"> <%= sprdBias.name()%> </idcHtml:option>
				</idcLogic:iterate>
			</idcHtml:select>
			<script>registeredFields.add('rfsSpreadMinimumBias')</script>
        </td>
		<td style="width: 70%"></td>
    </tr>
    <tr class=f>
        <td>Maximum Spread</td>
        <td>
            <input type="checkbox" name="maxRFSSprdEnabledCB" id="maxRFSSprdEnabledCB" <%=isMaxRFSSprdEnabled.booleanValue()?"checked":""%> class="ft" onclick='changeButton()'>
            <input type="hidden" name="workflowMessage.rfsSpreadMaximumEnabled" id="workflowMessage.rfsSpreadMaximumEnabled" value="<%=isMaxRFSSprdEnabled%>"/>
            <script>registerChange('workflowMessage.rfsSpreadMaximumEnabled','maxRFSSprdEnabledCB');</script>
        </td>
        <td>
            <%
                String rfsSpreadMax = "0";
                try { rfsSpreadMax = decimalFormat.format(((Double)rfsSpreadMaximum).doubleValue()); } catch(Exception e) {}
            %>
            <% // todo validation? %>
            <div id="rfsSpreadMaxDiv" <%=isMultiTierRfsSpotSpreads ? "style='display: none'" : ""%> >
            <idcHtml:text name="ObjectActionForm"  styleClass="ftr" property="rfsSpreadMaximum" id="rfsSpreadMaximum" size="6" value="<%=rfsSpreadMax%>" onblur="validateMinimumSpread(this);" onkeyup='changeButton()'/>&nbsp;<label class="rfsSpotSpreadUnitLbl"><%=rfsSpotSpreadUnit.name()%></label>
            </div>
            <script>registeredFields.add('rfsSpreadMaximum')</script>
        </td>
        <td>Bias</td>
        <td>
            <idcHtml:select name="ObjectActionForm" property="rfsSpreadMaximumBias" styleId="rfsSpreadMaximumBias" value='<%=rfsSpreadMaxBiasConfig==null?"":((SpreadBias)rfsSpreadMaxBiasConfig).name() %>' onchange='changeButton()'>
                <idcLogic:iterate id="sprdBias" collection="<%= collSpreadBiases %>" type="SpreadBias">
                    <idcHtml:option value="<%= sprdBias.name()%>"> <%= sprdBias.name()%> </idcHtml:option>
                </idcLogic:iterate>
            </idcHtml:select>
            <script>registeredFields.add('rfsSpreadMaximumBias')</script>
        </td>
		<td style="width: 70%"></td>
	</tr>
	<tr class=f>
        <td>Pre-Trade Fixed Spread</td>
        <td>
            <%
                SpreadType iSpotType = (null !=  configuration.getOriginalSpotSpread()) ?configuration.getOriginalSpotSpread().getType():SpreadType.DISABLED;
                SpreadType iSwapType = (null !=  configuration.getOriginalSwapSpread()) ?configuration.getOriginalSwapSpread().getType():SpreadType.DISABLED;
                boolean isSpotFixedEnabled = iSpotType != SpreadType.DISABLED;
                boolean isSwapFixedEnabled = iSwapType != SpreadType.DISABLED;
            %>
            <input type="checkbox" name="spotSpreadType" id="spotSpreadType" <%=isSpotFixedEnabled?"checked":""%> class="ft" onclick='spreadTypeCBClicked(this); changeButton()'>
            <input type="hidden" name="workflowMessage.spotSpreadType"  id="workflowMessage.spotSpreadType"  value="<%=iSpotType.getId()%>"/>
            SPOT/OTR &nbsp;
            <input type="checkbox" name="swapSpreadType"  id="swapSpreadType" <%=isSwapFixedEnabled?"checked":""%> class="ft" onclick='spreadTypeCBClicked(this); changeButton()'>
            <input type="hidden" name="workflowMessage.swapSpreadType" id="workflowMessage.swapSpreadType"  value="<%=iSwapType.getId()%>"/>
            SWAP
        </td>
		<td colspan="4" style="width: 70%"></td>
	</tr>
    <tr class=f>
        <td>Post-Trade Fixed Spread</td>
        <td>
            <%
                SpreadType iSpotTypePost = (null !=  configuration.getOriginalSpotSpread()) ?configuration.getOriginalSpotSpread().getTypePost():SpreadType.DISABLED;
                SpreadType iSwapTypePost = (null !=  configuration.getOriginalSwapSpread()) ?configuration.getOriginalSwapSpread().getTypePost():SpreadType.DISABLED;
                boolean isSpotFixedEnabledPost = iSpotTypePost != SpreadType.DISABLED;
                boolean isSwapFixedEnabledPost = iSwapTypePost != SpreadType.DISABLED;
            %>
            <input type="checkbox" name="spotSpreadTypePost" id="spotSpreadTypePost"  <%=isSpotFixedEnabledPost?"checked":""%> class="ft" onclick='spreadTypeCBClicked(this); changeButton()'>
            <input type="hidden" name="workflowMessage.spotSpreadTypePost"  id="workflowMessage.spotSpreadTypePost" value="<%=iSpotTypePost.getId()%>"/>
            SPOT/OTR &nbsp;
            <input type="checkbox" name="swapSpreadTypePost"  id="swapSpreadTypePost" <%=isSwapFixedEnabledPost?"checked":""%> class="ft" onclick='spreadTypeCBClicked(this); changeButton()'>
            <input type="hidden" name="workflowMessage.swapSpreadTypePost"  id="workflowMessage.swapSpreadTypePost" value="<%=iSwapTypePost.getId()%>"/>
            SWAP
        </td>
		<td colspan="4" style="width: 70%"></td>
	</tr>
	    <tr class=f>
        <td>Use Tier Based Spreads</td>
        <td><input type="checkbox" name="workflowMessage.useMultiTierRfsSpreads" <%=configuration.isMultiTierRfsSpotSpreads()? "checked" : ""%> id="useMultiTierRfsSpreads" onclick='changeButton(); toggleRfsSpreads(this)'>&nbsp;&nbsp;SPOT/OTR&nbsp;&nbsp;&nbsp;<input type="checkbox" disabled> &nbsp;SWAP</td>
		<td colspan="4" style="width: 70%"></td>
	</tr>
</table>

    <input type="hidden" id="rfsSpreadTiersHidden" name="workflowMessage.rfsSpreadTiers" value="[]">
<table id="rfsMultiTierSpreadTableId" <%=rfsMultiTierSpreadTableIdStyle%> width="100%" border="0" cellpadding="0" = cellspacing="1" class="outl3">

    <tr class="lt" style="text-align:center;">
        <td class="f3" rowspan="999" width="120px">SPOT/OTR</td>
        <td rowspan="2"><input type="checkbox" id="rfsSpreadTierMasterCheckbox" onclick="checkUncheckRfsSpreadTiers(this)"></td>
        <td rowspan="2">Tier</td>
        <td rowspan=2>Limit</td>
        <td rowspan=2>Minimum Spread (<label class="rfsSpotSpreadUnitLbl"><%=rfsSpotSpreadUnit.name()%></label>)</td>
        <td rowspan=2>Maximum Spread (<label class="rfsSpotSpreadUnitLbl"><%=rfsSpotSpreadUnit.name()%></label>)</td>
        <td colspan=3>Pre-Trade Fixed Spread (<label class="rfsSpotSpreadUnitLbl"><%=rfsSpotSpreadUnit.name()%></label>)</td>
        <td colspan=3>Post-Trade Fixed Spread (<label class="rfsSpotSpreadUnitLbl"><%=rfsSpotSpreadUnit.name()%></label>)</td>
    </tr>
    <tr class="lt" style="text-align:center;">
        <td>Bid</td>
        <td>Offer</td>
        <td>1-Way</td>
        <td>Bid</td>
        <td>Offer</td>
        <td>1-Way</td>
    </tr>

    <%
        String tierRow = "<tr id='_x_' class='rfsTierRow _row_color_'>" +
                "<td nowrap class='al'><input type='checkbox' class='ftr rfsTierCheckbox'></td>" +
                "<td nowrap class='al'><div name='rfsTierName'>Tier&nbsp;_x_</div></td>" +
                "<td nowrap class='al'><input type='text'  name='rfsTierLimit' value='_limit_' class='ftr' onblur='convertTierLimitToNotionalFormatNumber1(this); changeButton()'></td>" +
                "<td nowrap class='al'><input type='text' name='rfsTierMin' value='_min_' class='ftr' onblur='changeButton()'></td>" +
                "<td nowrap class='al'><input type='text' name='rfsTierMax' value='_max_' class='ftr' onblur='changeButton()'></td>" +
                "<td nowrap class='al'><input type='text'  name ='rfsTierPreBid' value='_preBid_' class='ftr' onblur='changeButton()'></td>" +
                "<td nowrap class='al'><input type='text'  name ='rfsTierPreOffer' value='_preOffer_' class='ftr' onblur='changeButton()'></td>" +
                "<td nowrap class='al'><input type='text'  name ='rfsTierPreOneway' value='_preOneway_' class='ftr' onblur='changeButton()'></td>" +
                "<td nowrap class='al'><input type='text'  name ='rfsTierPostBid' value='_postBid_' class='ftr' onblur='changeButton()'></td>" +
                "<td nowrap class='al'><input type='text'  name ='rfsTierPostOffer' value='_postOffer_' class='ftr' onblur='changeButton()'></td>" +
                "<td nowrap class='al'><input type='text'  name ='rfsTierPostOneway' value='_postOneway_' class='ftr' onblur='changeButton()'></td>" +
                "</tr>";
        Collection<RFSTier> raw = configuration.getOriginalRFSTiers();
        ArrayList<RFSTier> rfsTiers = (raw == null ? new ArrayList<RFSTier>() : new ArrayList<RFSTier>(raw));
        Collections.sort(rfsTiers);
        for(RFSTier tier : rfsTiers){
            String currentRow = tierRow.replaceAll("_x_", String.valueOf(tier.getSortOrder()));
            currentRow = currentRow.replaceAll("_row_color_", tier.getSortOrder()%2 == 0 ? "tlc" : "tl");
            String limitStr = null;
            if(tier.getBidLimit() == null) limitStr = "+";
            else limitStr = String.valueOf(getFormattedNotionalAmountERC(tier.getBidLimit(), decimalFormat));
            currentRow = currentRow.replaceAll("_limit_", limitStr);
            currentRow = currentRow.replaceAll("_min_", String.valueOf(tier.getSpreadMinimum()));
            currentRow = currentRow.replaceAll("_max_", String.valueOf(tier.getSpreadMaximum()));
            currentRow = currentRow.replaceAll("_preBid_", String.valueOf(tier.getSpreadFixedBid()));
            currentRow = currentRow.replaceAll("_preOffer_", String.valueOf(tier.getSpreadFixedOffer()));
            currentRow = currentRow.replaceAll("_preOneway_", String.valueOf(tier.getSpreadFixedOneway()));
            currentRow = currentRow.replaceAll("_postBid_", String.valueOf(tier.getSpreadFixedPostBid()));
            currentRow = currentRow.replaceAll("_postOffer_", String.valueOf(tier.getSpreadFixedPostOffer()));
            currentRow = currentRow.replaceAll("_postOneway_", String.valueOf(tier.getSpreadFixedPostOneway()));
            out.println(currentRow);
        }
    %>

    <tr class="f3" id="rfsTiersMarker">
        <td class="f3" nowrap="" colspan="11" style="padding-bottom: 10px">
            <input type="button" name="Add" value="Add" class="b1" onclick="javascript:addNewRfsTier();changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
            <input type="button" name="Remove" value="Remove" class="b1" onclick="removeRfsSpreadTier();changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
        </td>
    </tr>
</table>

    <script type="text/javascript">
        var rfsTierLimits = $j("input[name='rfsTierLimit']");
        $j.each(rfsTierLimits, function(i, val){
            val = $j(val);
            if(val.val() != '+'){
                notionalAmountFormat.format(val[0]);
            }
        });
    </script>

<table width="100%" border="0" cellpadding="0" cellspacing="1" class="outl"  style="display: <%=showRFS %>" id="rfsSpreadTab2">
    <tr class="lt" style="text-align:center;">
        <td width="120px">&nbsp;</td>
        <td colspan=4>Pre-Trade</td>
        <td colspan=4>Post-Trade</td>
    </tr>
    <tr class="lt" style="text-align:center;" >
        <td nowrap width="120px">Trade Type</td>
        <td nowrap>Bid</td>
        <td nowrap>Offer</td>
        <td nowrap>1-Way</td>
        <td nowrap>Calculation Basis</td>
        <td nowrap>Bid</td>
        <td nowrap>Offer</td>
        <td nowrap>1-Way</td>
        <td nowrap>Calculation Basis</td>
    </tr>
     <%
         SpreadType sptSprdType = spotSpread!=null ? spotSpread.getType() : null;
         SpreadType swpSprdType = swapSpread!=null ? swapSpread.getType() : null;
     %>
    <tr id="rfsSingleTierSpreadRowId" <%=isMultiTierRfsSpotSpreads ? "style='display: none'" : ""%> class="f3">
        <td class="f3" nowrap width="120px">SPOT/OTR</td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SPBidSprd" value='<%=(spotSpread!=null)?decimalFormat.format(spotSpread.getBidSpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SPOfferSprd" value='<%=(spotSpread!=null)?decimalFormat.format(spotSpread.getOfferSpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SPOneWaySprd" value='<%=(spotSpread!=null)?decimalFormat.format(spotSpread.getOneWaySpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><label class="rfsSpotSpreadUnitLbl"><%=rfsSpotSpreadUnit.name()%></label></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SPBidPostSprd" value='<%=(spotSpread!=null)?decimalFormat.format(spotSpread.getBidPostSpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SPOfferPostSprd" value='<%=(spotSpread!=null)?decimalFormat.format(spotSpread.getOfferPostSpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SPOneWayPostSprd" value='<%=(spotSpread!=null)?decimalFormat.format(spotSpread.getOneWayPostSpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><label class="rfsSpotSpreadUnitLbl"><%=rfsSpotSpreadUnit.name()%></label></td>
    </tr>
    <idcBean:define id="spreadTypeCalculators" name="ObjectActionForm" property="objectForm.referenceData(com/integral/finance/price/fx/FXSpreadPriceCalculatorC).list"/>
    <tr class="f3">
        <td class="f3" nowrap width="120px">SWAP</td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SwapBidSprd" value='<%=(swapSpread!=null)?decimalFormat.format(swapSpread.getBidSpread()):decimalFormat.format(0)%>' size="10"  class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SwapOfferSprd" value='<%=(swapSpread!=null)?decimalFormat.format(swapSpread.getOfferSpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SwapOneWaySprd" value='<%=(swapSpread!=null)?decimalFormat.format(swapSpread.getOneWaySpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al">
            <select name ='caliculationBasis' id='caliculationBasis' class="ft" onchange='changeButton()' >
                <option value="0" <%=(swapSpread!=null && swapSpread.getCalculationBasis().getId()==0)?"selected":""%>>Pips</option>
                <option value="1" <%=(swapSpread!=null && swapSpread.getCalculationBasis().getId()==1)?"selected":""%>>% of Swap Points</option>
                <option value="3" <%=(swapSpread!=null && swapSpread.getCalculationBasis().getId()==3)?"selected":""%>>BPS</option>
            </select>
            <script>registeredFields.add('caliculationBasis')</script>
        </td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SwapBidPostSprd" value='<%=(swapSpread!=null)?decimalFormat.format(swapSpread.getBidPostSpread()):decimalFormat.format(0)%>' size="10"  class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SwapOfferPostSprd" value='<%=(swapSpread!=null)?decimalFormat.format(swapSpread.getOfferPostSpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al"><input type="text"  name ="workflowMessage.SwapOneWayPostSprd" value='<%=(swapSpread!=null)?decimalFormat.format(swapSpread.getOneWayPostSpread()):decimalFormat.format(0)%>' size="10" class="ftr" onblur="validateRFSSpread(this)" onkeyup='changeButton()'></td>
        <td nowrap class="al">
            <select name ='caliculationPostBasis' id='caliculationPostBasis' class="ft" onchange='changeButton()' >
                <option value="0" <%=(swapSpread!=null && swapSpread.getCalculationPostBasis().getId()==0)?"selected":""%>>Pips</option>
                <option value="1" <%=(swapSpread!=null && swapSpread.getCalculationPostBasis().getId()==1)?"selected":""%>>% of Swap Points</option>
                <option value="3" <%=(swapSpread!=null && swapSpread.getCalculationBasis().getId()==3)?"selected":""%>>BPS</option>
            </select>
            <script>registeredFields.add('caliculationPostBasis')</script>
        </td>
    </tr>
</table>
<input type="hidden" name=""></input>
<table width="100%" border="0" cellpadding="0" cellspacing="1" class="outl2" style="display: <%=showRFS %>" id="rfsSpreadTab3">
		<tr class="stl2" style="text-align:left;">
			<th colspan=8 nowrap>Tenor Based Spreads</th>
		</tr>
        <%
            boolean errorOnPage = isErrorOnPage( pageContext );
            SpreadType tenorBasedSpreadType = errorOnPage ? SpreadType.valueOf(Integer.parseInt( request.getParameter( "workflowMessage.tenorBasedSpreadType" ))) :configuration.getOriginalTenorSpreadType();
        %>
				  <%
            boolean swapTenorSpreadOverride = errorOnPage ? Boolean.parseBoolean( request.getParameter( "workflowMessage.swapTenorSpreadOverride" )) : configuration.isOriginalSwapTenorSpreadOverride();
        %>
        <tr class="f" style="display: <%= showBps %>">
        	<td>Spread Unit</td>
        	<td><input type="radio" id="rfsForwardSpreadUnitPips" name="workflowMessage.rfsForwardSpreadUnit" value="Pips" onclick="changeRfsForwardSpreadUnit('Pips');changeButton();" <%=isRfsFwdPips?"checked":""%>></td>
        	<td>Pips</td>
        	<td><input type="radio" id="rfsForwardSpreadUnitBPS" name="workflowMessage.rfsForwardSpreadUnit" value="BPS" onclick="changeRfsForwardSpreadUnit('BPS');changeButton();" <%=isRfsFwdBPS?"checked":""%>></td>
        	<td>BPS</td>
            <!-- <td><input type="radio" id="rfsForwardSpreadUnitPercent" name="workflowMessage.rfsForwardSpreadUnit" value="PFP" onclick="changeRfsForwardSpreadUnit('PFP');changeButton();" <%=isRfsFwdPercent?"checked":""%>></td>
            <td>Percent Of Forward Points (PFP)</td> -->
            <td colspan="1">&nbsp;</td>
        </tr>
		<tr class="f">
			<td nowrap> Use Tenor Based Spreads</td>
                <%
                    boolean useTBS = !(tenorBasedSpreadType == null || tenorBasedSpreadType == SpreadType.DISABLED);
                    int iTBS = useTBS ? 2 : 0;
                    String useFPTierMultiplier = configuration.isForwardPointMultiplierEnabled() ? "checked" : "";
                %>
             <td> <input type="checkbox" name="tenorBasedSpreadType" id="tenorBasedSpreadType" <%=useTBS?"checked":""%> class="ft" onclick='spreadTypeCBClicked(this); changeButton()'>
                <input type="hidden" name="workflowMessage.tenorBasedSpreadType" id="workflowMessage.tenorBasedSpreadType" value="<%=iTBS%>"/></td>
			<td nowrap> Use Outright Spreads for Swaps</td>
			<td><input type="checkbox" id="swapTenorSpreadOverride" name="workflowMessage.swapTenorSpreadOverride" value="true" <%=swapTenorSpreadOverride ? "checked" : "" %>  onclick= "enableDisableSwap();changeButton();"> </td>
			<td colspan="4" style="width: 78%"></td>
		</tr>
		<tr class="f">
			<td nowrap>Unmatched Tenors</td>
			<td>&nbsp;</td>
			<td nowrap>Use Following Tenor</td>
			<td><input type="radio" name="workflowMessage.tenorSpreadInterpolationEnabled" value="false" <%= !configuration.isOriginalTenorSpreadInterpolationEnabled() ? "checked" : "" %> onclick= 'changeButton();'> </td>
			<td style="width: 5%">Interpolate</td> 
			<td><input type="radio" name="workflowMessage.tenorSpreadInterpolationEnabled" value="true" <%= configuration.isOriginalTenorSpreadInterpolationEnabled() ? "checked" : "" %> onclick= 'changeButton();'></td>
			<td colspan="2" style="width: 78%"></td>
		</tr>
        <tr class="f">
            <td nowrap>Use Tier Multipliers for Swaps</td>
            <td><input type="checkbox" name="workflowMessage.forwardPointTierMultiplierEnabled" id="forwardPointTierMultiplierEnabled" <%=useFPTierMultiplier%> class="ft" onclick="enableDisableTierMultipliers();changeButton()"></td>
            <td colspan="6">&nbsp;</td>
        </tr>
	</table>
<table id="otrTenorSpreadTbl" width="100%" border="0" cellpadding="0" cellspacing="1" class="outl2" style="display: <%=showRFS %>">
	<tr class="lt" style="text-align:left;">
			<th colspan="4"><b>OTR</b> <span style="font-weight:normal;">- Applied to Forward Points</span>
            </th>
			<th colspan="4" style="width: 50%"></th>
			</tr>
        <tr class="lt" style="text-align:center;">
                <th nowrap colspan=2>&nbsp;</th>
                <th nowrap colspan=3>Pre-Trade (<label class="rfsForwardSpreadUnitLbl"><%=rfsFwdSpreadUnit.name()%></label>)</th>
                <th nowrap colspan=3>Post-Trade (<label class="rfsForwardSpreadUnitLbl"><%=rfsFwdSpreadUnit.name()%></label>)</th>
        </tr>
        <tr class="lt" style="text-align:center;">
                <th style="width: 1%">
                    <input type="checkbox" id="otrAllTenorSpread" onClick="javascript:toggleTenorSpreadSelection(this,'otrTenorSpreadTbl','OTS')"></input>
                </th>
                <th nowrap>Tenor</th>
                <th nowrap>Bid</th>
                <th nowrap>Offer</th>
                <th nowrap>1-Way</th>
                <th nowrap>Bid</th>
                <th nowrap>Offer</th>
                <th nowrap>1-Way</th>
        </tr>
    <%
        int totalCountOTS = Integer.parseInt(HTTPWebUtils.getParameterValue(request,"workflowMessage.COUNTTENORSPREADOTS","0"));
        int j = 0;
        if ( !errorOnPage) 
        {
            Collection<TenorSpread> otss = configuration.getOriginalOutrightTenorSpreads();
            totalCountOTS = otss.size();
            j = 0;
            for ( TenorSpread ots : otss ) 
            {
                printTenorSpreads(j, ots, "OTS", out);
                j++;
            }
        } 
        else 
        {
            for (int i = 0; i < totalCountOTS; i++)
            {
                printTenorSpreadsWhenErrors( i, "OTS", out, request );
            }
        }
    %>
    <input type="hidden" id="workflowMessage.COUNTTENORSPREADOTS" name="workflowMessage.COUNTTENORSPREADOTS" value="<%=totalCountOTS%>"></input>
	</table>
	<table width="100%" cellpadding="0" cellspacing="1" class="outl2" style="display: <%=showRFS %>" id="rfsSpreadTab4">
	    <tr>
	        <td class="f3" nowrap width="25%">
	            <input type="button" name="Add" value="Add" class="b1" onclick="javascript:addNewTenorSpread('otrTenorSpreadTbl','OTS');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">&nbsp;
	            <input type="button" name="Remove" value="Remove" class="b1" onclick="javascript:removeTenorSpread('otrTenorSpreadTbl','OTS');" onmouseover="this.className='b1over';" onmouseout="this.className='b1';">
	        </td>
	    </tr>
	</table>
	<table  id="swapTenorSpreadTbl" width="100%" border="0" cellpadding="0" cellspacing="1" class="outl2" style="display: <%=showRFS %>">
        <tr class="lt" style="text-align:left;">
			<th colspan="4">&nbsp;SWAP <span style="font-weight:normal;">- Applied to Far leg Forward Points</span></th>
			<th colspan="4" style="width: 50%"></th>
			</tr>
			
        <tr class="lt" style="text-align:center;">
                <th nowrap colspan=2>&nbsp;</th>
                <th nowrap colspan=3 style="text-align:center;">Pre-Trade
                    <select name ='calculationBasisTenorSwap' id='calculationBasisTenorSwap' class="ft" onchange='changeButton();enableSwapTenorSprd();' >
                        <option value="0" <%=(configuration.getOriginalCalculationBasisTenorSwap().getId()==0)?"selected":""%>>Pips</option>
                        <option value="1" <%=(configuration.getOriginalCalculationBasisTenorSwap().getId()==1)?"selected":""%>>% of Swap Points</option>
                        <option value="2" <%=(configuration.getOriginalCalculationBasisTenorSwap().getId()==2)?"selected":""%>>% of Swap Points Spread</option>
                    </select>
                    <input type="text" name="swapSpdSpread" id="swapSpdSpread" value="<%=configuration.getSwapTenorSpread()%>" onchange="validatePositiveNumber(this, true, 0);changeButton()" ></input>
                    <script>registeredFields.add('calculationBasisTenorSwap');registeredFields.add('swapSpdSpread')</script>
                </th>
                <th nowrap colspan=3 style="text-align:center;">Post-Trade
                    <select name ='postCalculationBasisTenorSwap'  id='postCalculationBasisTenorSwap' class="ft" onchange='changeButton();enableSwapTenorSprd();' >
                        <option value="0" <%=(configuration.getOriginalPostCalculationBasisTenorSwap().getId()==0)?"selected":""%>>Pips</option>
                        <option value="1" <%=(configuration.getOriginalPostCalculationBasisTenorSwap().getId()==1)?"selected":""%>>% of Swap Points</option>
                        <option value="2" <%=(configuration.getOriginalPostCalculationBasisTenorSwap().getId()==2)?"selected":""%>>% of Swap Points Spread</option>
                    </select>
                    <input type="text" name="swapSpdPostSpread" id="swapSpdPostSpread" value="<%=configuration.getSwapTenorPostSpread()%>" onchange="validatePositiveNumber(this, true, 0);changeButton()" ></input>
                    <script>registeredFields.add('postCalculationBasisTenorSwap');registeredFields.add('swapSpdPostSpread')</script>
                </th>
        </tr>
        <tr class="lt" style="text-align:center;">
                <th nowrap style="width: 1%">
                    <input type="checkbox" id="swapAllTenorSpread" onClick="javascript:toggleTenorSpreadSelection(this,'swapTenorSpreadTbl','STS')"></input>
                </th>
                <th nowrap>Tenor</th>
                <th nowrap>Bid</th>
                <th nowrap>Offer</th>
                <th nowrap>1-Way</th>
                <th nowrap>Bid</th>
                <th nowrap>Offer</th>
                <th nowrap>1-Way</th>
        </tr>
    <%
        int totalCountSTS = Integer.parseInt(HTTPWebUtils.getParameterValue(request,"workflowMessage.COUNTTENORSPREADSTS","0"));
        if ( !errorOnPage ) {
            Collection<TenorSpread> stss = configuration.getOriginalSwapTenorSpreads();
            totalCountSTS = stss.size();
            j = 0;
            for ( TenorSpread sts : stss ) {
                printTenorSpreads(j, sts, "STS", out);
                j++;
            }
        }  else {
            for (int i = 0; i < totalCountSTS; i++){
                printTenorSpreadsWhenErrors( i, "STS", out, request );
            }
        }
    %>
    <input type="hidden" id="workflowMessage.COUNTTENORSPREADSTS" name="workflowMessage.COUNTTENORSPREADSTS" value="<%=totalCountSTS%>"></input>
	</table>
	    <table width="100%" cellpadding="0" cellspacing="1" class="outl2" style="display: <%=showRFS %>" id="rfsSpreadTab5">
	    <tr>
	        <td class="f3" nowrap width="25%">
	            <input type="button" name="Add" id="addSwapTenor" value="Add" class="b1 swapTenorSprdTbl" onclick="javascript:addNewTenorSpread('swapTenorSpreadTbl','STS');changeButton()" onmouseover="this.className='b1over';" onmouseout="this.className='b1 swapTenorSprdTbl';">&nbsp;
	            <input type="button" name="Remove" id="removeSwapTenor" value="Remove" class="b1 swapTenorSprdTbl" onclick="javascript:removeTenorSpread('swapTenorSpreadTbl','STS');" onmouseover="this.className='b1over';" onmouseout="this.className='b1 swapTenorSprdTbl';">
	        </td>
	    </tr>
	</table>
    <table  id="FPTierMultiplierTbl" width="100%" border="0" cellpadding="0" cellspacing="1" class="outl2" style="display: <%=showRFS %>">
        <tr class="lt" style="text-align:left;">
            <th colspan="5">&nbsp;Tier Multiplier</th>
        </tr>

        <tr class="lt" style="text-align:center;">
            <th nowrap style="width: 1%">
                <input type="checkbox" id="swapTenorMultiplierAll" class="tierMultiplierElement" onClick="javascript:toggleTenorSpreadSelection(this,'FPTierMultiplierTbl','STS')">
            </th>
            <th nowrap>Tier</th>
            <th nowrap>Limit</th>
            <th nowrap>Multiplier</th>
			<th style="width: 40%"nowrap>&nbsp;</th>
        </tr>
        <%
            Collection<RFSTierMultiplier> multipliers = configuration.getOriginalRFSTierMultipliers();
            int count = multipliers.size();
            Iterator<RFSTierMultiplier> multiplierIterator = multipliers.iterator();
            for (int i=0;i<count;i++) {
                printTierMultipliers(i, out, multiplierIterator.next(), decimalFormat);
            }
        %>
		</table>
	    <table id="FPTierMultiplierBtnTbl" width="100%" cellpadding="0" cellspacing="1" class="outl2" style="display: <%=showRFS %>">
	    <tr>
        <tr id="FPTierMultiplierMarker">
            <td class="f3" nowrap width="25%">
                <input type="button" name="Add" value="Add" class="b1 tierMultiplierElement" onclick="javascript:addNewFPTierMultiplier();changeButton()">&nbsp;
                <input type="button" name="Remove" value="Remove" class="b1 tierMultiplierElement" onclick="removeFPTierMultiplier();changeButton()">
            </td>
        </tr>
    </table>
    <input type="hidden" id="FPTierMultiplierJson" name="workflowMessage.FPTierMultipliers" value="[]">
    <div class="space"></div>
    <table id="MiFIDAttributesTableTitle" width="100%" cellpadding="0" cellspacing="0" class="outl" style="display: <%= showMiFID %>" >
        <tr>
            <td class="stl2" nowrap><idcBean:message key="label.MIFID.Parameters" />&nbsp;&nbsp;<a class="bl" name="refLink" href="javascript:helpPopup('Cpty/PriceMaking/StreamsEditCcyPairsEditCreate.html#miFID','Admin Portal Help')"><img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img></a></td>
            <td class="stl3" nowrap>&nbsp;</td> <td class="stl4" nowrap>&nbsp;</td>
            <td class="stl8" nowrap>&nbsp;</td>
        </tr>
    </table>
    <table id="MiFIDAttributesTable" width="100%" border="0" cellpadding="0" cellspacing="1" class="outl2" style="display: <%= showMiFID %>" >
    	<%
		   Boolean overrideMifid = false;
		   if(configuration.getOverrideStreamMifidParams()!=null && configuration.getOverrideStreamMifidParams())
			   overrideMifid = configuration.getOverrideStreamMifidParams();
		%>
		<tr class="f">
			<td><idcBean:message key="label.MIFID.OverrideStreamParameters" /></td>
			<td class="f3" nowrap style="text-align: bottom" colspan="3"><input type="checkbox" id="overrideStreamMifidParams" name="workflowMessage.overrideStreamMifidParams" value="<%=overrideMifid%>" <%=overrideMifid?"checked":""%> class="ft" onclick="changeButton();updateMifidParameters();"></td>
		</tr>
		<tr>
           	<td class="stl2" colspan="2" width="50%">Maker</td>
           	<td class="stl2" colspan="2" width="50%">Taker</td>
       	</tr>
		<tr class="f" style="display: none">
		    <td  width="25%"><idcBean:message key="label.MIFID.ExecutingFirm" /></td>
		    <td width="25%">
		        <select id="miFIDExecutingFirm" name="workflowMessage.miFIDExecutingFirm" class="ft" onchange="changeButton();" >
		            <option value="None"><idcBean:message key="SelectOption1"/></option>
		            <idcLogic:iterate id="miFIDExecutingLE" collection="<%= miFIDExecutingLEs %>" type="com.integral.finance.counterparty.LegalEntity" >
		                <option value="<%=miFIDExecutingLE.getEncryptedObjectId()%>" <%=miFIDExecutingLE.isSameAs(configuration.getMiFIDExecutingFirm())?"selected":""%>><%=miFIDExecutingLE.getShortName()%></option>
		            </idcLogic:iterate>
		        </select>
		    </td>
		    <td width="25%"><idcBean:message key="label.MIFID.ExecutingFirm" /></td>
		    <td width="25%">
		        <select id="miFIDTakerExecutingFirm" name="workflowMessage.miFIDTakerExecutingFirm" class="ft" onchange="changeButton();" >
		            <option value="None"><idcBean:message key="SelectOption1"/></option>
		            <idcLogic:iterate id="miFIDTakerExecutingLE" collection="<%= miFIDExecutingLEs %>" type="com.integral.finance.counterparty.LegalEntity" >
		                <option value="<%=miFIDTakerExecutingLE.getEncryptedObjectId()%>" <%=miFIDTakerExecutingLE.isSameAs(configuration.getMiFIDTakerExecutingFirm())?"selected":""%>><%=miFIDTakerExecutingLE.getShortName()%></option>
		            </idcLogic:iterate>
		        </select>
		    </td>
		</tr>
		<tr class="f" style="display: none">
		    <td><idcBean:message key="label.MIFID.ExecutingUser" /></td>
		    <td>
		        <select id="miFIDExecutingUser" name="workflowMessage.miFIDExecutingUser" class="ft" onchange="changeButton();" >
		            <option value="None"><idcBean:message key="SelectOption1"/></option>
		            <idcLogic:iterate id="miFIDExecutingUser" collection="<%= miFIDExecutingUsers %>" type="com.integral.user.User" >
		                <option value="<%=miFIDExecutingUser.getEncryptedObjectId()%>" <%=miFIDExecutingUser.isSameAs(mifidExecUser)?"selected":""%>><%=miFIDExecutingUser.getShortName()%></option>
		            </idcLogic:iterate>
		        </select>
		    </td>
		    <td><idcBean:message key="label.MIFID.ExecutingUser" /></td>
		    <td>
		        <select id="miFIDTakerExecutingUser" name="workflowMessage.miFIDTakerExecutingUser" class="ft" onchange="changeButton();" >
		            <option value="None"><idcBean:message key="SelectOption1"/></option>
		            <idcLogic:iterate id="miFIDTakerExecutingUser" collection="<%= miFIDExecutingUsers %>" type="com.integral.user.User" >
		                <option value="<%=miFIDTakerExecutingUser.getEncryptedObjectId()%>"
		                        <%=miFIDTakerExecutingUser.isSameAs(configuration.getMiFIDTakerExecutingUser())?"selected":""%>><%=miFIDTakerExecutingUser.getShortName()%></option>
		            </idcLogic:iterate>
		        </select>
		    </td>
		</tr>
		<tr class="f">
        <td width="25%"><idcBean:message key="label.MIFID.InvestmentDecisionMaker" /></td>
        <td class="f3" width="25%">
            <select id="miFIDInvDecisionMaker" name="workflowMessage.miFIDInvDecisionMaker" class="ft" onchange="changeButton();" <%=overrideMifid ? "" : "style='display: none'"%>>
                <option value="None"><idcBean:message key="SelectOption1"/></option>
                <idcLogic:iterate id="miFIDInvDecisionMaker" collection="<%= miFIDInvDecisionMkrs %>" type="com.integral.user.User" >
                    <option value="<%=miFIDInvDecisionMaker.getEncryptedObjectId()%>" <%=miFIDInvDecisionMaker.isSameAs(configuration.getMiFIDInvDecisionMaker())?"selected":""%>><%=miFIDInvDecisionMaker.getShortName()%></option>
                </idcLogic:iterate>
            </select>
            <span id="miFIDInvDecisionMakerS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.getMiFIDInvDecisionMaker() == null ? "None" : stream.getMiFIDInvDecisionMaker().getShortName() %></span>
        </td>
        <td width="25%"><idcBean:message key="label.MIFID.InvestmentDecisionMaker" /></td>
        <td class="f3" width="25%">
            <select id="miFIDTakerInvDecisionMaker" name="workflowMessage.miFIDTakerInvDecisionMaker" class="ft" onchange="changeButton();" <%=overrideMifid ? "" : "style='display: none'"%> <%=showMiFIDTaker ? "" : "disabled='disabled'"%>>
                <option value="None"><idcBean:message key="SelectOption1"/></option>
                <idcLogic:iterate id="miFIDTakerInvDecisionMaker" collection="<%= miFIDInvDecisionMkrs %>" type="com.integral.user.User" >
                    <option value="<%=miFIDTakerInvDecisionMaker.getEncryptedObjectId()%>" <%=miFIDTakerInvDecisionMaker.isSameAs(configuration.getMiFIDTakerInvDecisionMaker())?"selected":""%>><%=miFIDTakerInvDecisionMaker.getShortName()%></option>
                </idcLogic:iterate>
            </select>
            <span id="miFIDTakerInvDecisionMakerS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.getMiFIDTakerInvDecisionMaker() == null ? "None" : stream.getMiFIDTakerInvDecisionMaker().getShortName() %></span></td>
        </td>
	</tr>
	<tr class="f">
        <td><idcBean:message key="PriceMaking.TradingCapacity" /></td>
        <td class="f3">
            <select name="workflowMessage.tradingCapacity" id="tradingCapacity" class="ft" onchange="changeButton();" <%=overrideMifid ? "" : "style='display: none'"%>>
                <option value="None"><idcBean:message key="SelectOption1"/></option>
                <option value="<%=StreamC.TRADING_CAPACITY_DEAL%>" <%=StreamC.TRADING_CAPACITY_DEAL.equals(configuration.getTradingCapacity())?"selected":""%>><idcBean:message key="TradingCapacity.DEAL" /></option>
                <option value="<%=StreamC.TRADING_CAPACITY_MTCH%>" <%=StreamC.TRADING_CAPACITY_MTCH.equals(configuration.getTradingCapacity())?"selected":""%>><idcBean:message key="TradingCapacity.MTCH" /></option>
                <option value="<%=StreamC.TRADING_CAPACITY_AOTC%>" <%=StreamC.TRADING_CAPACITY_AOTC.equals(configuration.getTradingCapacity())?"selected":""%>><idcBean:message key="TradingCapacity.AOTC" /></option>
            </select>
            <span id="tradingCapacityS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.getTradingCapacity() == null ? "None" : stream.getTradingCapacity()%></span>
        </td>
        <td><idcBean:message key="PriceMaking.TradingCapacity" /></td>
        <td class="f3">
            <select name="workflowMessage.takerTradingCapacity" id="takerTradingCapacity" class="ft" onchange="changeButton();" <%=overrideMifid ? "" : "style='display: none'"%> <%=showMiFIDTaker ? "" : "disabled='disabled'"%>>
                <option value="None"><idcBean:message key="SelectOption1"/></option>
                <option value="<%=StreamC.TRADING_CAPACITY_DEAL%>" <%=StreamC.TRADING_CAPACITY_DEAL.equals(configuration.getTakerTradingCapacity())?"selected":""%>><idcBean:message
                        key="TradingCapacity.DEAL" /></option>
                <option value="<%=StreamC.TRADING_CAPACITY_MTCH%>" <%=StreamC.TRADING_CAPACITY_MTCH.equals(configuration.getTakerTradingCapacity())?"selected":""%>><idcBean:message
                        key="TradingCapacity.MTCH" /></option>
                <option value="<%=StreamC.TRADING_CAPACITY_AOTC%>" <%=StreamC.TRADING_CAPACITY_AOTC.equals(configuration.getTakerTradingCapacity())?"selected":""%>><idcBean:message
                        key="TradingCapacity.AOTC" /></option>
            </select>
            <span id="takerTradingCapacityS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.getTakerTradingCapacity() == null ? "None" : stream.getTakerTradingCapacity()%></span>
        </td>
	</tr>
	<tr class="f">
        <td><idcBean:message key="label.PriceMaking.Maker.SIExecution" /></td>
        <td class="f3">
            <%
                Boolean siExe = configuration.getSiExecution();
                if(siExe ==null){
                	siExe = false;
                }
            %>
            <input type="checkbox" id="makerSiExecution" name="workflowMessage.makerSiExecution" value="<%=siExe%>" <%=siExe ? "checked" : "" %> onchange="changeButton(); javascript:updateCheckboxValue('makerSiExecution');" <%=overrideMifid ? "" : "style='display: none'"%>/>
            <span id="makerSiExecutionS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.isSiExecution()%></span>                
        </td>
        <td><idcBean:message key="label.PriceMaking.Taker.SecuritiesFinancing" /></td>
        <td class="f3">
        	<%
                Boolean secFin = configuration.getSecuritiesFinancing();
                if(secFin == null){
                	secFin = false;
            	}
            %>
            <input type="checkbox" id="takerSecuritiesFinancing" name="workflowMessage.takerSecuritiesFinancing" value="<%=secFin%>" <%=secFin ? "checked" : "" %> onchange="changeButton(); javascript:updateCheckboxValue('takerSecuritiesFinancing');" <%=overrideMifid ? "" : "style='display: none'"%> <%=showMiFIDTaker ? "" : "disabled='disabled'"%>/>
            <span id="takerSecuritiesFinancingS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.isSecuritiesFinancing()%></span>
        </td>
	</tr>
	<tr class="f">
        <td><idcBean:message key="label.PriceMaking.Maker.SIVenue" /></td>
        <td class="f3"><input type="text" class="ft" id="makerSIVenue" name="workflowMessage.makerSIVenue" size="20" value="<%= configuration.getSiVenue() == null ? "" : configuration.getSiVenue() %>" onchange="changeButton();" <%=overrideMifid ? "" : "style='display: none'"%>/>
        <span id="makerSIVenueS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.getSiVenue() == null ? "" : stream.getSiVenue()%></span></td>
        <td><idcBean:message key="label.PriceMaking.Taker.NonPriceForming" /></td>
        <td class="f3">
            <%
                Boolean npft = configuration.getNonPriceForming();
                if(npft == null){
                	npft = false;
            	}
            %>
            <input type="checkbox" id="takerNonPriceForming" name="workflowMessage.takerNonPriceForming" value="<%=npft%>" <%=npft ? "checked" : "" %> onchange="changeButton(); javascript:updateCheckboxValue('takerNonPriceForming');" <%=overrideMifid ? "" : "style='display: none'"%> <%=showMiFIDTaker ? "" : "disabled='disabled'"%>/>
            <span id="takerNonPriceFormingS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.isNonPriceForming()%></span>
        </td>            
	</tr>
	<tr class="f">
        <td><idcBean:message key="PriceMaking.CoverOnMTF" /></td>
        <td class="f3">
            <%
                Boolean coverOnMTF= false;
                if(coverOnMTFValue == 1)
                	coverOnMTF = true;
                
            %>
           <input type="checkbox" id="coverOnMTF" name="workflowMessage.coverOnMTF" value="<%=coverOnMTF%>" <%=coverOnMTF ? "checked" : "" %> onchange="changeButton(); javascript:updateCheckboxValue('coverOnMTF');" <%=overrideMifid ? "" : "style='display: none'"%> <%=showMiFIDTaker ? "" : "disabled='disabled'"%>/>
           <span id="coverOnMTFS" <%=overrideMifid ? "style='display: none'" : "" %>><%=stream.isCoverOnMTF()%></span>               
        </td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
	<tr>
 </table>

<div class="space"></div>
</div>

<script type="text/javascript">

var rfsSprdUnit= (<%=isRfsFwdBPS%>)?"BPS":"Pips";
var tempUseSwapProvider= (<%=useSwapProviders%>)?"SWAP":"AGG";
changeUseSwapPointProviders(tempUseSwapProvider);

idcWindow.registerOnLoadScript("initNewConfiguration()");
idcWindow.registerOnLoadScript("validateExecutionRulesForExternalPricingSource()");
idcWindow.registerOnLoadScript("enableSwapTenorSprd()");
idcWindow.registerOnLoadScript("enableDisableTierMultipliers()");
idcWindow.registerOnLoadScript("enableDisableSwap()");
idcWindow.registerOnLoadScript("changeRfsForwardSpreadUnit(rfsSprdUnit)");
idcWindow.registerOnLoadScript("enableDisableVehicleCurrency()");
idcWindow.registerOnLoadScript("updateNegativeSpreadEnabled()");

</script>

