README
---------
Ensure that 
1) the system has maven installed and mvn is in the classpath.
2) there is a settings.xml in the folder .m2 in user home directory with the following contents

<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                      http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <localRepository/>
  <interactiveMode/>
  <usePluginRegistry/>
  <offline/>
  <pluginGroups/>
  <servers>
    <server>
      <id>internal</id>
      <username>username</username>
      <password>password</password>
    </server>
  </servers>
  <mirrors/>
  <proxies/>
  <profiles/>
  <activeProfiles/>
</settings>


Usage "ant build-model"
	=> This will compile the fxiapi code and make a jar fxiapi-model-{date}.jar. 
	=> Upload the generated jar to http://svn:8080/archiva/browse/com.integral/fxiapi-model/{date} 
