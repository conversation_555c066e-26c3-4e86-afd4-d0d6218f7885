--- fxiapi/build.gradle
+++ fxiapi/build.gradle
@@ -9,21 +13,21 @@
 apply plugin: 'groovy'
 apply plugin: "jaco<PERSON>"
 
-buildscript {
-    repositories {
-       maven {
-            url "http://nexus.integral.com:9081/repository/local-jcenter"
-       }
-    }
+// buildscript {
+//     repositories {
+//        maven {
+//             url "http://nexus.integral.com:9081/repository/local-jcenter"
+//        }
+//     }
 
-    dependencies {
-        classpath 'com.eriwen:gradle-css-plugin:1.11.1'
-        classpath 'com.eriwen:gradle-js-plugin:1.12.1'
-    }
-}
+//     dependencies {
+//         classpath 'com.eriwen:gradle-css-plugin:1.11.1'
+//         classpath 'com.eriwen:gradle-js-plugin:1.12.1'
+//     }
+// }
 
 // Invoke the css minify plugin
-apply plugin: 'css'
+//apply plugin: 'css'
 
 css.source{
 	dev{
