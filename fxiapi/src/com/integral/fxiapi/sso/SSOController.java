package com.integral.fxiapi.sso;

import com.integral.cache.distributed.Message;
import com.integral.cas.*;
import com.integral.cas.config.CASMBean;
import com.integral.cas.config.CASMBeanC;
import com.integral.darwin.auth.jwt.JwtSessionUtil;
import com.integral.darwin.auth.jwt.JwtTokenUtil.JwtUserProfile;
import com.integral.darwin.auth.model.JWTTokenInfo;
import com.integral.fxiapi.BaseController;
import com.integral.fxiapi.BaseResponse;
import com.integral.fxiapi.FXIConstants;
import com.integral.fxiapi.Status;
import com.integral.fxiapi.auth.AuthController;
import com.integral.fxiapi.auth.LoginService;
import com.integral.fxiapi.auth.LogoutResponse;
import com.integral.fxiapi.handler.AuthTokenCacheMessageHandler;
import com.integral.fxiapi.messages.AuthTokenMessage;
import com.integral.fxiapi.sso.external.OIDCResponseHandler;
import com.integral.is.common.cache.DistributedCacheFactory;
import com.integral.jmsproxy.protocol.Packet;
import com.integral.persistence.cache.ReferenceDataCache;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.security.CryptC;
import com.integral.security.SecurityConstant;
import com.integral.security.SecurityFactory;
import com.integral.security.jwt.JwtTokenUtil;
import com.integral.security.jwt.PaymentsPermissionsSupplier;
import com.integral.security.util.AuthenticatorUtilC;
import com.integral.security.util.CookieUtils;
import com.integral.session.UserServiceManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.VersionMBean;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.StringUtilC;
import com.integral.util.Tuple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.io.IOException;
import java.util.*;

import static com.integral.security.SecurityConstant.SSO_COOKIE;

/**
 * Created by IntelliJ IDEA.
 * User: poddars
 *
 * SSO APIs for single-sign-on, single-sign-out and token management
 * Please refer to:
 * http://wiki.integral.com/wiki/index.php/FXIServiceAPI#SSO_logout_.28Single_Sign-Out.29
 *
 */

@Controller
@RequestMapping( value = "/sso" )
public class SSOController extends BaseController
{
    private static final CommonAuthenticationService cas = CommonAuthenticationServiceC.getInstance();

    @Autowired
    LoginService loginService;

    @Autowired
    private AuthController authController;

    @Autowired
    public SSOController( Validator validator )
    {
        super( validator );
        DistributedCacheFactory.getInstance().getDistributedCache().registerMessageHandler(new AuthTokenCacheMessageHandler());
    }

    @RequestMapping(value="/version", method=RequestMethod.GET)
    public
    @ResponseBody
    BaseResponse getVersionInfo(HttpServletRequest request, HttpServletResponse response) {
        try {
            final VersionMBean versionMbeanData = ConfigurationFactory.getVersionMBean();
            final Map<String, String> versionData = new LinkedHashMap<String, String>();
            String shortName = versionMbeanData.getShortName();
            String[] buildInfo = shortName.split("-");
            if (buildInfo.length < 2) {
                versionData.put("buildName", versionMbeanData.getShortName());
                versionData.put("buildVersion", versionMbeanData.getShortName());
            } else {
                versionData.put("buildName", buildInfo[0].trim());
                versionData.put("buildVersion", buildInfo[1].trim());
            }
            versionData.put("buildNumber", versionMbeanData.getBuildNumber());
            versionData.put("buildDateTime", versionMbeanData.getBuildDateTime().toString());
            versionData.put("longName", versionMbeanData.getLongName());
            versionData.put("description", versionMbeanData.getDescription());
            versionData.put("timeInitialized", versionMbeanData.getTimeInitialized().toString());
            versionData.put("dateTimeCreated", versionMbeanData.getDateTimeCreated().toString());
            return new BaseResponse(Status.OK) {
                public Map<String, String> getVersionData() {
                    return versionData;
                }
            };
        } catch (Exception e) {
            log.error("Exception while fetching FXI Version Information", e);
        }
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR);
    }

    private String getVirtualServer(String org, HttpServletRequest request) {
        String virtualServer = null;
        try {
            Cookie[] allCookies = request.getCookies();
            if (allCookies != null) {
                for (int i = 0; i < allCookies.length; i++) {
                    if ((OIDCResponseHandler.ORG_SERVICE_GROUP + org).equals(allCookies[i].getName())) {
                        virtualServer = allCookies[i].getValue().trim();
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception while fetching virtualServer: ", e);
        }
        return virtualServer;
    }


    private String getSsoExpiry(HttpServletRequest request) {
        String ssoExpiry = null;
        try {
            Cookie[] allCookies = request.getCookies();
            if (allCookies != null) {
                for (int i = 0; i < allCookies.length; i++) {
                    if (OIDCResponseHandler.SSO_EXPIRY .equals(allCookies[i].getName())) {
                        ssoExpiry = allCookies[i].getValue().trim();
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception while fetching ssoExpiry: ", e);
        }
        return ssoExpiry;
    }


    @RequestMapping(value="/ssoDomain", method=RequestMethod.GET)
    public
    @ResponseBody
    String getSsoDomain(HttpServletRequest request, HttpServletResponse response) {
        return getSsoDomain(request);
    }

    @RequestMapping(value="/ssoToken", method=RequestMethod.GET)
    public
    @ResponseBody
    String getSsoToken(HttpServletRequest request, HttpServletResponse response) {
        return AuthController.getSSOToken(request);
    }

    private String getSsoDomain(HttpServletRequest request) {
        String ssoDomain = null;
        try {
            Cookie[] allCookies = request.getCookies();
            if (allCookies != null) {
                for (int i = 0; i < allCookies.length; i++) {
                    if (OIDCResponseHandler.DOMAIN_COOKIE .equals(allCookies[i].getName())) {
                        ssoDomain = allCookies[i].getValue().trim();
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception while fetching ssoDomain: ", e);
        }
        return ssoDomain;
    }

    private String getExternalSSOAuth(HttpServletRequest request) {
        String externalSSOAuth = null;
        try {
            Cookie[] allCookies = request.getCookies();
            if (allCookies != null) {
                for (int i = 0; i < allCookies.length; i++) {
                    if (OIDCResponseHandler.EXTERNAL_SSO_COOKIE .equals(allCookies[i].getName())) {
                        externalSSOAuth = allCookies[i].getValue().trim();
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception while fetching externalSSOAuth: ", e);
        }
        return externalSSOAuth;
    }

    @RequestMapping( value = "/login", method = RequestMethod.POST )
    public
    @ResponseBody
    SSOLoginResponse login( @RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response )
    {
        SSOLoginResponse loginResponse = null;
        try
        {
            //Do all the validations
            Set failures = validator.validate( loginData );
            if ( !failures.isEmpty() )
            {
                loginResponse = new SSOLoginResponse( Status.ERROR, response );
                loginResponse.setErrorCode( validationMessages( failures ) );
                return loginResponse;
            }
			 if (loginData.getApiVersion() == null || loginData.getApiVersion().trim().length() == 0){
                loginData.setApiVersion(UserServiceManager.DEFAULT_API_VERSION);
            }

			Float apiVersion = Float.parseFloat(loginData.getApiVersion());
            String uId =null;
            User user =null;

			if ("1.21".equals(loginData.getApiVersion())
					|| Math.round(apiVersion * 100) / 100f > 1.41) {

		            ReferenceDataCache referenceDataCache = ReferenceDataCacheC.getInstance();
		            Organization orgRef =referenceDataCache.getOrganization(loginData.getOrg());


		            if ( orgRef == null )
		            {
						loginResponse = new SSOLoginResponse(Status.ERROR);
		                loginResponse.setErrorCode( "Invalid organization supplied" );
		                log.warn( "SSOController.login. organization doesn't exists with id " + loginData.getOrg() );
		                return loginResponse;
		            }


		             uId = loginData.getUser() + '@' + loginData.getOrg();
		             user = UserFactory.getUser( uId );

		            if (user == null)
		            {
						loginResponse = new SSOLoginResponse(Status.ERROR);
						loginResponse.setErrorCode("Invalid user supplied");
		                log.warn( "SSOController.login. user doesn't exists with id " + uId );
		                return loginResponse;
		            }
            }else{
            	  uId = loginData.getUser() + '@' + loginData.getOrg();
            	  user = UserFactory.getUser( uId );

                 if (user == null)

                 {
                     loginResponse = new SSOLoginResponse( Status.ERROR );
                     int httpStatus = Math.round(apiVersion * 100) / 100f > 1.42 ? HttpServletResponse.SC_OK : HttpServletResponse.SC_UNAUTHORIZED;
                     response.setStatus(httpStatus);
                     loginResponse.setErrorCode( "Invalid user/organization supplied" );
                     log.warn( "SSOController.login. user doesn't exists with id " + uId );
                     return loginResponse;
                 }
            }

            if (!CASMBeanC.getInstance().isCasClientSSOEnabled()) {
                loginResponse = new SSOLoginResponse(Status.ERROR);
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                loginResponse.setErrorCode("SSO is not enabled on server");
                log.warn( "SSOController.login. SSO not enabled");
                return loginResponse;
            }

            log.info( new StringBuilder( 200 ).append("SC.doLogin: Login request received for user:").append(uId).toString());

            //password validation for static/otp/2fa
            Tuple<Boolean, String> authResult = loginService.authenticateUser(user, loginData.getOtp(), loginData.getPass(), request);

            if (!authResult.first) {
                loginResponse = new SSOLoginResponse( Status.ERROR );
                loginResponse.setErrorCode( authResult.second );
                int httpStatus = Math.round(apiVersion * 100) / 100f > 1.42 ? HttpServletResponse.SC_OK : HttpServletResponse.SC_UNAUTHORIZED;
                response.setStatus(httpStatus);
                log.warn("SSOController.login. Authentication failed for userid " + uId);
                SecurityFactory.getSecurityService().auditLoginFailed( user, true );
                return loginResponse;
            }

            boolean isLoginEnabled = loginService.isLoginEnabled(user);
            if ( !isLoginEnabled )
            {
                loginResponse = new SSOLoginResponse( Status.ERROR );
                loginResponse.setErrorCode(FXIConstants.ORG_LOGIN_DISABLED);
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return loginResponse;
            }

            boolean isClientIPAllowed = loginService.isClientIPAllowed( user, request );
            if ( !isClientIPAllowed )
            {
                loginResponse = new SSOLoginResponse( Status.ERROR );
                loginResponse.setErrorCode( FXIConstants.INVALID_IP );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                return loginResponse;
            }

            //Set the SSO_TOKEN to allow client to allow any service
            String authToken = cas.generateAuthToken(uId, loginData.getApiVersion(), request);
            loginResponse = new SSOLoginResponse( Status.OK );
            String loginType = AuthController.getCookieValue(request, SecurityConstant.LOGIN_TYPE);
            AuthenticatorUtilC.getInstance().addSSOCookiesToResponse(response, authToken,loginType);
            //add jwt token header
            if(log.isDebugEnabled()){
                boolean maker = user.hasPermission(PaymentsPermissionsSupplier.PaymentMaker);
                boolean approver = user.hasPermission(PaymentsPermissionsSupplier.PaymentApprover);
                boolean admin = user.hasPermission(PaymentsPermissionsSupplier.PaymentFIAdmin);
                boolean sysAdmin = user.hasPermission(PaymentsPermissionsSupplier.PaymentSysAdmin);
                log.debug("Payment Roles: maker=" + maker + ", approver=" + approver + ", admin=" + admin + ", sys=" + sysAdmin);
            }
            LoginService.addLoginTimeCookieToResponse(response);
            loginResponse.setExpiryTime(LoginService.getExpiryTime(authToken));
            loginResponse.setServerUTCTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());
            log.info("SC.SSOlogin. done successfully for " + uId);
            SecurityFactory.getSecurityService().auditLoginSuccess( user, true );
            //set redirect cookie based on user's org virtual server
            authController.setRedirectionCookieFor( user.getOrganization().getShortName(), response);
            //Extract loadBalancer IP Address from the cookie and to track singleURL login through A10.
            //loginService.setSingleURLTrackCookie(request, response);

        }
        catch ( Exception e )
        {
            log.error( "SC.SSOlogin. Exception during login for user: " + loginData.getUser() + " ,org: " + loginData.getOrg(), e );
            sendError( response );
        }

        return loginResponse;
    }

    /**
     * This method is the wrapper to the loginMFA method, this with disable the SSO token generation and enables the JWT token based
     * multi-stpe authentication
     * @param loginData
     * @param request
     * @param response
     * @return
     */
    @RequestMapping( value = "/login/token/mfa", method = RequestMethod.POST )
    public
    @ResponseBody
    MFALoginResponse loginMFAJWT(@RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response ){
        return loginMFA(loginData, request,response,false);
    }

    /**
     * This method is the wrapper to the loginMFA method, this with disable the SSO token generation and enables the JWT token based
     * multi-stpe authentication
     * @param loginData
     * @param request
     * @param response
     * @return
     */
    @RequestMapping( value = "/login/mfa", method = RequestMethod.POST )
    public
    @ResponseBody
    MFALoginResponse loginMFASSO(@RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response ){
        return loginMFA(loginData, request,response,true);
    }

    /**
     * This method supports multi-step MFA authentication. User can start with id and password, if MFA is enabled this method will issue a verification
     * token. Which can be used to validate next step's authentication e.g. Google Auth TOTP token. Finally when all steps are authenticated then SSO_TOKEN
     * will be issued.
     * @param loginData
     * @param request
     * @param response
     * @return
     */
    MFALoginResponse loginMFA(@RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response, boolean isSSOEnable )
    {
        long st = System.currentTimeMillis();
        MFALoginResponse loginResponse = null;
        try
        {
            String uId = loginData.getUser() + '@' + loginData.getOrg();

            Tuple<MFALoginResponse,User> validationResponse = validateRequest(loginData,request,response);
            //Do all the validations
            if( validationResponse.first != null ){
                return validationResponse.first; //validation failed.
            }

            User user = validationResponse.second;

            log.info( new StringBuilder( 200 ).append("SC.loginMFA: Login request received for user:").append(uId).toString());

            //password validation for static/otp/2fa
            Tuple<Boolean, String> authResult = loginService.authenticate2FAUser(user, loginData.getOtp(), loginData.getPass(), request);

            if (!authResult.first) {
                loginResponse = new MFALoginResponse( Status.ERROR );
                loginResponse.setErrorCode( authResult.second );
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                log.warn("SSOController.loginMFA. Authentication failed for userid " + uId);
                SecurityFactory.getSecurityService().auditLoginFailed( user, true );
                return loginResponse;
            }

            if (JwtSessionUtil.isJwtRequest(request, CASMBeanC.getInstance().isJWTAuthenticationEnable())) {
                String token = com.integral.darwin.auth.jwt.JwtTokenUtil.getCurrentToken(request);
                String userIndex = request.getHeader("X-User-Index");
                if (userIndex == null || userIndex.isEmpty()) {
                    userIndex = "0";
                }
                int index = Integer.parseInt(userIndex);
                if (log.isDebugEnabled()) {
                    log.debug("loginMFA: checking if the token is valid for multiuser, token="+token);
                    log.debug("loginMFA: userIndex found in header, index="+index);
                }
                if (token != null && !token.isEmpty()) {
                    boolean isValid = CASClient.getInstance().isJwtTokenValid(token, index, null);
                    if (!isValid) {
                        loginResponse = new MFALoginResponse( Status.ERROR );
                        loginResponse.setErrorCode( "INVALID_JWT_TOKEN" );
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        log.warn("SSOController.loginMFA. Authentication failed for userid " + uId);
                        SecurityFactory.getSecurityService().auditLoginFailed( user, true );
                        return loginResponse;
                    }
                }
            }

            //If 2FA is not required issue sso token
            if(!user.isExternalAuth()){
                return loginService.onLogonSuccess(uId,loginData,user,request,response, isSSOEnable);
            }else if ("INFO_2FA_SETUP_INCOMPLETE".equals(authResult.second)){
                loginResponse = new MFALoginResponse(Status.OK);
                String verificationToken = cas.generateProvisionalToken(uId, request, ProvisionalTokenTypes.Generate2FASecret);
                loginResponse.setVerificationToken(verificationToken);
                loginResponse.setExpiryTime(LoginService.getExpiryTime(verificationToken));
                loginResponse.setServerUTCTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());
                loginResponse.setErrorCode(authResult.second);
                log.info("SC.loginMFA. 2FA setup incomplete. Step one auth successful, for " + uId + " tt=" + (System.currentTimeMillis()-st));
            }else {
                //If 2FA is required then issue verification token.
                String verificationToken = cas.generateProvisionalToken(uId, request, ProvisionalTokenTypes.MFAVerification);
                if( loginData.getOtp() != null && !loginData.getOtp().trim().isEmpty()){
                    loginData.setVerificationToken(verificationToken);
                    return verify2FA(loginData,request,response,isSSOEnable);
                }
                loginResponse = new MFALoginResponse(Status.OK);
                loginResponse.setVerificationToken(verificationToken);
                loginResponse.setExpiryTime(LoginService.getExpiryTime(verificationToken));
                loginResponse.setServerUTCTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());
                loginResponse.setErrorCode("INFO_2FA_CODE_REQUIRED");
                log.info("SC.loginMFA. done successfully for " + uId + " tt=" + (System.currentTimeMillis()-st));
            }

        } catch ( Exception e ) {
            log.error( "SC.loginMFA. Exception during login for user: " + loginData.getUser() + " ,org: " + loginData.getOrg(), e );
            sendError( response );
        }
        return loginResponse;
    }

    /**
     * Second step of 2FA login. One time token like TOTP token is validated here and generate JWT token on successful authentication.
     * @param loginData
     * @param request
     * @param response
     * @return
     */
    @RequestMapping( value = "/verify/token/MFAToken", method = RequestMethod.POST )
    public
    @ResponseBody
    MFALoginResponse verifyJWT2FA(@RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response )
    {
        return verify2FA(loginData, request, response, false);
    }

    /**
     * Second step of 2FA login. One time token like TOTP token is validated here and generate SSO token on successful authentication.
     * @param loginData
     * @param request
     * @param response
     * @return
     */
    @RequestMapping( value = "/verify/MFAToken", method = RequestMethod.POST )
    public
    @ResponseBody
    MFALoginResponse verifySSO2FA(@RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response )
    {
        return verify2FA(loginData, request, response, true);
    }

    /**
     * Second step of 2FA login. One time token like TOTP token is validated here.
     * @param loginData
     * @param request
     * @param response
     * @return
     */
    MFALoginResponse verify2FA(@RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response, boolean isSSOEnable)
    {
        MFALoginResponse loginResponse = null;
        try {
            String uId = loginData.getUser() + '@' + loginData.getOrg();
            log.info( new StringBuilder( 200 ).append("SC.verify2FA: 2FA token verification request received for user:").append(uId).toString());

            Tuple<MFALoginResponse,User> validationResponse = validateRequest(loginData,request,response);
            //Do all the validations
            if( validationResponse.first != null ){
                return validationResponse.first; //validation failed.
            }

            User user = validationResponse.second;

            Tuple<Boolean, String> authResult =loginService.validate2FAToken(user,loginData.getOtp(),loginData.getVerificationToken());

            if(!authResult.first){
                loginResponse = new MFALoginResponse( Status.ERROR );
                loginResponse.setErrorCode( authResult.second );
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                log.warn("SSOController.verify2FA. Second factor (2FA token) authentication failed for userid " + uId);
                SecurityFactory.getSecurityService().auditLoginFailed( user, true );
                return loginResponse;
            }else{
                return loginService.onLogonSuccess(uId,loginData,user,request,response,isSSOEnable);
            }

        }catch ( Exception e )
        {
            log.error( "SC.verify2FA. Exception during login for user: " + loginData.getUser() + " ,org: " + loginData.getOrg(), e );
            sendError( response );
        }
        return loginResponse;
    }


    protected Tuple<MFALoginResponse,User> validateRequest(SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response){
        Set failures = validator.validate( loginData );
        if ( !failures.isEmpty() )
        {
            MFALoginResponse loginResponse = new MFALoginResponse( Status.ERROR, response );
            loginResponse.setErrorCode( validationMessages( failures ) );
            return new Tuple<MFALoginResponse, User>(loginResponse,null);
        }

        String uId = loginData.getUser() + '@' + loginData.getOrg();
        User user = UserFactory.getUser( uId );
        if (user == null)
        {
            MFALoginResponse loginResponse = new MFALoginResponse( Status.ERROR );
            int httpStatus = HttpServletResponse.SC_UNAUTHORIZED;
            response.setStatus(httpStatus);
            loginResponse.setErrorCode( "Invalid user/organization supplied" );
            log.warn( "SSOController.validateRequest. user doesn't exists with id " + uId );
            return new Tuple<MFALoginResponse, User>(loginResponse,null);
        }

        boolean isLoginEnabled = loginService.isLoginEnabled(user);
        if ( !isLoginEnabled )
        {
            MFALoginResponse loginResponse = new MFALoginResponse( Status.ERROR );
            loginResponse.setErrorCode(FXIConstants.ORG_LOGIN_DISABLED);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            //logs are in login service
            return new Tuple<MFALoginResponse, User>(loginResponse,user);
        }

        boolean isClientIPAllowed = loginService.isClientIPAllowed( user, request );
        if ( !isClientIPAllowed )
        {
            MFALoginResponse loginResponse = new MFALoginResponse( Status.ERROR );
            loginResponse.setErrorCode( FXIConstants.INVALID_IP );
            response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
            //logs are in login service
            return new Tuple<MFALoginResponse, User>(loginResponse,user);
        }

        return new Tuple<MFALoginResponse, User>(null,user);
    }


    @RequestMapping( value = "/logout", method = RequestMethod.POST )
    public
    @ResponseBody
    LogoutResponse logout( HttpServletRequest request, HttpServletResponse response )
    {
        LogoutResponse logoutResponse = new LogoutResponse( Status.OK );
        String authToken = AuthController.getSSOToken( request );
        User user = null;

        if ( authToken != null ) {
            try {
                String decryptedToken = CryptC.decryptURLSafeString(authToken);
                boolean isValid = cas.isTokenValidInSpaces(decryptedToken);
                if (isValid) {
                    user = CASClient.AuthToken.parseUser(decryptedToken);
                }
            } catch(Exception ex){
                log.warn("SSO.Logout: Unable to get user");
            }
            //Add the invalid token in spaces and reset SSO cookie
            //audit in fxi6/fxi7 client is done by OA server.
            cas.logoutAndInvalidateToken( authToken ,false);

            if (response != null)
            {
                // Delete all the cookies
                AuthenticatorUtilC.getInstance().addSSOCookiesToResponse(response, null, 0); //expiry 0 will delete the cookie from the browser

                Cookie sessionCookie = CookieUtils.getInstance().createCookie(Packet.SESSIONID, null, null, "/", 0);
                response.addCookie(sessionCookie);

                Cookie secSessionCookie = CookieUtils.getInstance().createCookie(Packet.SEC_SESSIONID, null, null, "/", 0);
                response.addCookie(secSessionCookie);

                Cookie authCookie = CookieUtils.getInstance().createCookie(FXIConstants.AUTH_TOKEN, null, null, "/", 0);
                response.addCookie(authCookie);

            }
        }
        return logoutResponse;
    }

    @RequestMapping( value = "/loginV2", method = RequestMethod.POST )
    public
    @ResponseBody
    SSOLoginResponse loginV2( @RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response )
    {
        SSOLoginResponse loginResponse = null;
        try
        {
            //Do all the validations
            Set failures = validator.validate( loginData );
            if ( !failures.isEmpty() )
            {
                loginResponse = new SSOLoginResponse( Status.ERROR, response );
                loginResponse.setErrorCode( validationMessages( failures ) );
                return loginResponse;
            }
            if (loginData.getApiVersion() == null || loginData.getApiVersion().trim().length() == 0){
                loginData.setApiVersion(UserServiceManager.DEFAULT_API_VERSION);
            }
            Float apiVersion = Float.parseFloat(loginData.getApiVersion());
            String uId =null;
            User user =null;
            if ("1.21".equals(loginData.getApiVersion())
                    || Math.round(apiVersion * 100) / 100f > 1.41) {
                ReferenceDataCache referenceDataCache = ReferenceDataCacheC.getInstance();
                Organization orgRef =referenceDataCache.getOrganization(loginData.getOrg());
                if ( orgRef == null )
                {
                    loginResponse = new SSOLoginResponse(Status.ERROR);
                    loginResponse.setErrorCode( "Invalid organization supplied" );
                    log.warn( "SSOController.login. organization doesn't exists with id " + loginData.getOrg() );
                    return loginResponse;
                }


                uId = loginData.getUser() + '@' + loginData.getOrg();
                user = UserFactory.getUser( uId );

                if (user == null)
                {
                    loginResponse = new SSOLoginResponse(Status.ERROR);
                    loginResponse.setErrorCode("Invalid user supplied");
                    log.warn( "SSOController.login. user doesn't exists with id " + uId );
                    return loginResponse;
                }
            }else{
                uId = loginData.getUser() + '@' + loginData.getOrg();
                user = UserFactory.getUser( uId );

                if (user == null)

                {
                    loginResponse = new SSOLoginResponse( Status.ERROR );
                    int httpStatus = Math.round(apiVersion * 100) / 100f > 1.42 ? HttpServletResponse.SC_OK : HttpServletResponse.SC_UNAUTHORIZED;
                    response.setStatus(httpStatus);
                    loginResponse.setErrorCode( "Invalid user/organization supplied" );
                    log.warn( "SSOController.login. user doesn't exists with id " + uId );
                    return loginResponse;
                }
            }
            if (!CASMBeanC.getInstance().isCasClientSSOEnabled()) {
                loginResponse = new SSOLoginResponse(Status.ERROR);
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                loginResponse.setErrorCode("SSO is not enabled on server");
                log.warn( "SSOController.login. SSO not enabled");
                return loginResponse;
            }
            log.info( new StringBuilder( 200 ).append("SC.doLogin: Login request received for user:").append(uId).toString());
            //password validation for static/otp/2fa
            Tuple<Boolean, String> authResult = loginService.authenticateUser(user, loginData.getOtp(), loginData.getPass(), request);
            if (!authResult.first) {
                loginResponse = new SSOLoginResponse( Status.ERROR );
                loginResponse.setErrorCode( authResult.second );
                int httpStatus = Math.round(apiVersion * 100) / 100f > 1.42 ? HttpServletResponse.SC_OK : HttpServletResponse.SC_UNAUTHORIZED;
                response.setStatus(httpStatus);
                log.warn("SSOController.login. Authentication failed for userid " + uId);
                SecurityFactory.getSecurityService().auditLoginFailed( user, true );
                return loginResponse;
            }

            boolean isLoginEnabled = loginService.isLoginEnabled(user);
            if ( !isLoginEnabled )
            {
                loginResponse = new SSOLoginResponse( Status.ERROR );
                loginResponse.setErrorCode(FXIConstants.ORG_LOGIN_DISABLED);
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return loginResponse;
            }

            boolean isClientIPAllowed = loginService.isClientIPAllowed( user, request );
            if ( !isClientIPAllowed )
            {
                loginResponse = new SSOLoginResponse( Status.ERROR );
                loginResponse.setErrorCode( FXIConstants.INVALID_IP );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                return loginResponse;
            }

            //Set the SSO_TOKEN to allow client to allow any service
//            String authToken = cas.generateAuthToken(uId, loginData.getApiVersion(), request);
//
//            loginResponse = new SSOLoginResponse( Status.OK );
//
//            String loginType = AuthController.getCookieValue(request, SecurityConstant.LOGIN_TYPE);
//            AuthenticatorUtilC.getInstance().addSSOCookiesToResponse(response, authToken,loginType);
            //add jwt token header
            if(log.isDebugEnabled()){
                boolean maker = user.hasPermission(PaymentsPermissionsSupplier.PaymentMaker);
                boolean approver = user.hasPermission(PaymentsPermissionsSupplier.PaymentApprover);
                boolean admin = user.hasPermission(PaymentsPermissionsSupplier.PaymentFIAdmin);
                boolean sysAdmin = user.hasPermission(PaymentsPermissionsSupplier.PaymentSysAdmin);
                log.debug("Payment Roles: maker=" + maker + ", approver=" + approver + ", admin=" + admin + ", sys=" + sysAdmin);
            }
            com.integral.darwin.auth.jwt.JwtTokenUtil.getInstance().addJwtTokenHeaderV2(request,response,user);
            LoginService.addLoginTimeCookieToResponse(response);
//            loginResponse.setExpiryTime(getExpiryTime(authToken));
//            loginResponse.setServerUTCTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());
            log.info("SC.SSOlogin. done successfully for " + uId);
            SecurityFactory.getSecurityService().auditLoginSuccess( user, true );
            //set redirect cookie based on user's org virtual server
            authController.setRedirectionCookieFor( user.getOrganization().getShortName(), response);
            //Extract loadBalancer IP Address from the cookie and to track singleURL login through A10.
            loginService.setSingleURLTrackCookie(request, response);
        }
        catch ( Exception e )
        {
            log.error( "SC.SSOlogin. Exception during login for user: " + loginData.getUser() + " ,org: " + loginData.getOrg(), e );
            sendError( response );
        }

        return loginResponse;
    }


    @RequestMapping( value = "/logoutV2", method = RequestMethod.POST )
    public
    @ResponseBody
    LogoutResponse logoutV2( HttpServletRequest request, HttpServletResponse response )
    {
        LogoutResponse logoutResponse = new LogoutResponse( Status.OK );
        String authToken = AuthController.getSSOToken( request );
        User user = null;

        if ( authToken != null ) {
            try {
                String decryptedToken = CryptC.decryptURLSafeString(authToken);
                boolean isValid = cas.isTokenValidInSpaces(decryptedToken);
                if (isValid) {
                    user = CASClient.AuthToken.parseUser(decryptedToken);
                }
            } catch(Exception ex){
                log.warn("SSO.Logout: Unable to get user");
            }
            //Add the invalid token in spaces and reset SSO cookie
            if(! StringUtilC.isNullOrEmpty( authToken ) ) {
                cas.logoutAndInvalidateToken(authToken,true);
                AuthTokenMessage authTokenMessage = new AuthTokenMessage(Message.MESSAGE_TYPE_SESSION_LOGOUT);
                authTokenMessage.getValues().put(AuthTokenMessage.IS_JWT_TOKEN, String.valueOf(false));
                authTokenMessage.getValues().put(AuthTokenMessage.MESSAGE_ID, authToken);
                log.info("tokenLogout: successfully logout, sending message to DistributedCache="+authTokenMessage);
                DistributedCacheFactory.getInstance().getDistributedCache().sendMessage( authTokenMessage );
            }

            if (response != null)
            {
                if (JwtSessionUtil.isJwtRequest(request, CASMBeanC.getInstance().isJWTAuthenticationEnable())){
                    com.integral.darwin.auth.jwt.JwtTokenUtil.getInstance().handleLogOut(request,response);
                }
                // Delete all the cookies
                AuthenticatorUtilC.getInstance().addSSOCookiesToResponse(response, null, 0); //expiry 0 will delete the cookie from the browser

                Cookie sessionCookie = CookieUtils.getInstance().createCookie(Packet.SESSIONID, null, null, "/", 0);
                response.addCookie(sessionCookie);

                Cookie secSessionCookie = CookieUtils.getInstance().createCookie(Packet.SEC_SESSIONID, null, null, "/", 0);
                response.addCookie(secSessionCookie);

                Cookie authCookie = CookieUtils.getInstance().createCookie(FXIConstants.AUTH_TOKEN, null, null, "/", 0);
                response.addCookie(authCookie);
            }
        }

        return logoutResponse;
    }


    @RequestMapping( value = "/token/getInfo", method = RequestMethod.GET )
    public
    @ResponseBody
    SSOToken getTokenInfo( HttpServletRequest request, HttpServletResponse response )
    {
        SSOToken ssoToken;
        String authToken = AuthController.getSSOToken(request);

        if (authToken != null)
        {
            ssoToken = new SSOToken(Status.OK);
            ssoToken.setValid(cas.isTokenValid(authToken));
            String decryptedToken = CryptC.decryptURLSafeString(authToken);
            long expiryTime = CASClient.AuthToken.parseExpiryTime(decryptedToken);
            ssoToken.setExpiryTime(expiryTime);
            ssoToken.setExpired(CASClient.AuthToken.isExpiredToken(expiryTime));
            ssoToken.setApiVersion(CASClient.AuthToken.parseApiVersion(decryptedToken));
        }
        else
        {
            ssoToken = new SSOToken( Status.ERROR );
            ssoToken.setErrorCode("SSO Token not found");
        }

        return ssoToken;
    }

    @RequestMapping( value = "/token/getInfo/v3", method = RequestMethod.GET )
    public
    @ResponseBody
    ResponseEntity<SSOToken> getTokenInfoV3(HttpServletRequest request, HttpServletResponse response, @RequestParam(required = false,value = "o") String org) {
        ResponseEntity<SSOToken> responseEntity = getTokenInfoV2(request,response);
        SSOToken token = responseEntity.getBody();
        if( token == null || token.isExpired() || !token.isValid() || token.getStatus() == Status.ERROR){
            return new ResponseEntity<SSOToken>(token,HttpStatus.UNAUTHORIZED);
        }
        if( org != null ){
            org = org.trim();
            if( token instanceof SSOTokenV2 ) {
                if(!((SSOTokenV2) token).getUserOrgName().equals(org)){
                    return new ResponseEntity<SSOToken>(token,HttpStatus.UNAUTHORIZED);
                }
            }
            else if( token instanceof JWTTokenInfo){
                boolean orgUserFound = false;
                for(JwtUserProfile jwtUserProfile : ((JWTTokenInfo)token).getUsers()){
                    String u = jwtUserProfile.getU();
                    int idx = u.lastIndexOf('@');
                    if(idx > 0 ){
                        if( org.equals(u.substring(idx+1))){
                            orgUserFound = true;
                            break;
                        }
                    }
                }
                if( !orgUserFound){
                    return new ResponseEntity<SSOToken>(token,HttpStatus.UNAUTHORIZED);
                }
            }
            else{
                return new ResponseEntity<SSOToken>(token, HttpStatus.UNAUTHORIZED);
            }
        }
        return new ResponseEntity<SSOToken>(token,HttpStatus.OK);
    }

    @RequestMapping( value = "/token/getInfo/v2", method = RequestMethod.GET )
    public
    @ResponseBody
    ResponseEntity<SSOToken> getTokenInfoV2(HttpServletRequest request, HttpServletResponse response )
    {
        SSOTokenV2 ssoToken;
        String authToken = AuthController.getSSOToken(request);
        boolean isJWTAuthenticationEnable = CASMBeanC.getInstance().isJWTAuthenticationEnable();
        String token = request.getHeader(com.integral.darwin.auth.jwt.JwtTokenUtil.HEADER);
        if (JwtSessionUtil.isJwtRequest(request,isJWTAuthenticationEnable) && !token.contains("Bearer")){
            JWTTokenInfo jwtToken = new JWTTokenInfo(Status.OK);
            token = com.integral.darwin.auth.jwt.JwtTokenUtil.getCurrentToken(request);
            List<JwtUserProfile> jwtUserProfiles = com.integral.darwin.auth.jwt.JwtTokenUtil.getAllUsersFromToken(token);
            if (!jwtUserProfiles.isEmpty()) {
                List<String> externalSSOUsers = new ArrayList<String>();
                String checkTokenType = request.getParameter("checkTokenType");
                if("Y".equalsIgnoreCase(checkTokenType) || "true".equalsIgnoreCase(checkTokenType)){
                    for(JwtUserProfile userProfile : jwtUserProfiles){
                        if( CASMBeanC.getInstance().getAuthTokenType(UserFactory.getUser(userProfile.getU())) != CASMBean.AuthToken_Type_JWT){
                            ssoToken = new SSOTokenV2( Status.ERROR );
                            ssoToken.setErrorCode("TokenTypeValidationFailed");
                            return new ResponseEntity<SSOToken>(ssoToken,HttpStatus.UNAUTHORIZED);
                        }
                        if (userProfile.isExternalAuth()) {
                            externalSSOUsers.add(userProfile.getU());
                        }
                    }
                }
                for(JwtUserProfile userProfile : jwtUserProfiles){
                    if (userProfile.isExternalAuth()) {
                        externalSSOUsers.add(userProfile.getU());
                    }
                }
                com.integral.darwin.auth.jwt.JwtTokenUtil.setTokenInHeaderAndCookie(request,response,token);
                jwtToken.setExpiryTime(com.integral.darwin.auth.jwt.JwtTokenUtil.getAllClaimsFromToken(token).getExpiration().getTime());
                jwtToken.setValid( true );
                jwtToken.setExpired( false );
                jwtToken.setUsers(jwtUserProfiles);
                jwtToken.setExternalSSOUsers(externalSSOUsers);

            } else {
                jwtToken.setValid(false);
                jwtToken.setExpired(true);
            }
            return new ResponseEntity<SSOToken>(jwtToken,HttpStatus.OK);
        } else if (authToken != null && !authToken.isEmpty())
        {
            List<String> externalSSOUsers = new ArrayList<String>();
            ssoToken = new SSOTokenV2(Status.OK);
            ssoToken.setValid(cas.isTokenValid(authToken));
            String decryptedToken = CryptC.decryptURLSafeString(authToken);
            if(ssoToken.isValid()){
                String userId = decryptedToken.substring( 0, decryptedToken.indexOf( CommonAuthenticationService.AUTH_TOKEN_SEPARATOR ) );
                User user = UserFactory.getUser( userId );
                if( user != null ) {
                    String checkTokenType = request.getParameter("checkTokenType");
                    if("Y".equalsIgnoreCase(checkTokenType) || "true".equalsIgnoreCase(checkTokenType)){
                        if(CASMBeanC.getInstance().getAuthTokenType(user) != CASMBean.AuthToken_Type_SSO_COOKIE){
                            ssoToken = new SSOTokenV2( Status.ERROR );
                            ssoToken.setErrorCode("TokenTypeValidationFailed");
                            return new ResponseEntity<SSOToken>(ssoToken,HttpStatus.UNAUTHORIZED);
                        }
                    }
                    if (getExternalSSOAuth(request) != null) {
                        externalSSOUsers.add(userId);
                        ssoToken.setExternalSSOUsers(externalSSOUsers);
                    }
                    ssoToken.setUserName(user.getShortName());
                    ssoToken.setUserOrgName(user.getOrganization().getShortName());
                }
            }
            long expiryTime = CASClient.AuthToken.parseExpiryTime(decryptedToken);
            ssoToken.setExpiryTime(expiryTime);
            ssoToken.setExpired(CASClient.AuthToken.isExpiredToken(expiryTime));
            ssoToken.setApiVersion(CASClient.AuthToken.parseApiVersion(decryptedToken));
            ssoToken.setLoginType(AuthController.getCookieValue(request, SecurityConstant.LOGIN_TYPE));
            ssoToken.setSsoDomain(getSsoDomain(request));
            ssoToken.setExternalSSOAuth(getExternalSSOAuth(request));
            ssoToken.setVirtualServer(getVirtualServer(ssoToken.getUserOrgName(), request));
            ssoToken.setSsoExpiry(getSsoExpiry(request));
        } else {
            ssoToken = new SSOTokenV2( Status.ERROR );
            ssoToken.setErrorCode("SSO Token not found");
        }
        return new ResponseEntity<SSOToken>(ssoToken,HttpStatus.OK);
    }

    @RequestMapping( value = "/token/renew", method = RequestMethod.GET )
    public
    @ResponseBody
    SSOToken renewToken( HttpServletRequest request, HttpServletResponse response )
    {
        log.info("SSOC.renewToken - Checking token for renewal.");
        boolean isValid = false;
        String decryptedToken = null;
        SSOToken ssoToken = null;
        try{
            boolean isJWTAuthenticationEnable = CASMBeanC.getInstance().isJWTAuthenticationEnable();
            if (JwtSessionUtil.isJwtRequest(request,isJWTAuthenticationEnable)) {
                String token = com.integral.darwin.auth.jwt.JwtTokenUtil.getCurrentToken(request);
                isValid = com.integral.darwin.auth.jwt.JwtTokenUtil.isValidToken(token);
                if (isValid) {
                    User user = JwtSessionUtil.getJWTSessionUser(request);
                    if (user == null) {
                        isValid = false;
                        log.info("JWT.renewToken - Unable to find user from existing token " + token);
                    } else {
                        String newToken = com.integral.darwin.auth.jwt.JwtTokenUtil.getInstance().renewJwtToken(request,response,token,user);
                        ssoToken = new SSOToken( Status.OK );
                        ssoToken.setExpiryTime(com.integral.darwin.auth.jwt.JwtTokenUtil.getAllClaimsFromToken(newToken).getExpiration().getTime());
                        ssoToken.setValid( true );
                        ssoToken.setExpired( false );

                        AuthTokenMessage authTokenMessage = new AuthTokenMessage(Message.MESSAGE_TYPE_SESSION_RENEW);
                        authTokenMessage.getValues().put(AuthTokenMessage.IS_JWT_TOKEN, String.valueOf(true));
                        authTokenMessage.getValues().put(AuthTokenMessage.MESSAGE_ID, newToken);
                        log.info("renewToken: successfully renewToken, sending message to DistributedCache="+authTokenMessage);
                        DistributedCacheFactory.getInstance().getDistributedCache().sendMessage( authTokenMessage );
                    }
                }
            } else {
                String authToken  = AuthController.getSSOToken(request);
                if (authToken != null || !authToken.isEmpty()) {
                    decryptedToken = CryptC.decryptURLSafeString(authToken);
                    isValid = cas.isTokenValidInSpaces(decryptedToken);
                }
                if (isValid) {
                    String apiVersion = CASClient.AuthToken.parseApiVersion(decryptedToken);
                    User user = CASClient.AuthToken.parseUser(decryptedToken);
                    if(user == null)
                    {
                        isValid = false;
                        log.info("SSOC.renewToken - Unable to find user from existing token " + decryptedToken);
                    } else
                    {
                        String newAuthToken = cas.generateAuthToken( user.getFullyQualifiedName(), apiVersion, request );
                        String loginType = AuthController.getCookieValue(request, SecurityConstant.LOGIN_TYPE);
                        //reset the cookie with new token
                        AuthenticatorUtilC.getInstance().addSSOCookiesToResponse( response, newAuthToken, loginType );
                        JwtTokenUtil.getInstance().addJwtTokenHeader(response, user);
                        String newDecryptedToken = CryptC.decryptURLSafeString( newAuthToken );
                        ssoToken = new SSOToken( Status.OK );
                        ssoToken.setExpiryTime( CASClient.AuthToken.parseExpiryTime( newDecryptedToken ) );
                        ssoToken.setApiVersion( CASClient.AuthToken.parseApiVersion( newDecryptedToken ) );
                        ssoToken.setValid( true );
                        ssoToken.setExpired( false );
                    }
                }
            }
        } catch (Exception e) {
            isValid = false;
        }
        if(!isValid)
        {
            log.info("SSOC.renewToken - Invalid Token " + decryptedToken + " , can not be renewed.");
            ssoToken = new SSOToken(Status.ERROR);
            ssoToken.setErrorCode("Invalid token, can not be renewed");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        }
        return ssoToken;
    }

    @RequestMapping( value = "/token/jwt/getInfoV2", method = RequestMethod.GET )
    public
    @ResponseBody
    List<JwtUserProfile> getJwtTokenInfoV2(HttpServletRequest request, HttpServletResponse response )
    {
        String token = com.integral.darwin.auth.jwt.JwtTokenUtil.getCurrentToken(request);
        List<JwtUserProfile> user = com.integral.darwin.auth.jwt.JwtTokenUtil.getAllUsersFromToken(token);
        com.integral.darwin.auth.jwt.JwtTokenUtil.setTokenInHeaderAndCookie(request,response,token);
        return user;
    }



/**
 *  Take the tokenName, user and userIndex, reads the value of the provided tokenName from the cookie and
 *  Sets it to the response header with the key as "Authorization".
 *  Validate the user and userIndex.
 *  @param authType {@link ValidateAuthType}
*
* */
    @RequestMapping(value = "/token/validate",method = RequestMethod.POST)
    public ResponseEntity<Object> validateToken(@RequestBody(required = false) Map<String, String> headerInfo,
                                                @RequestParam(value = "at", required = false) Integer authType,
                                                HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            if (headerInfo == null) {
                headerInfo = new HashMap<String, String>();
            }
            String tokenName = null;
            String tokenValue = null;
            if (headerInfo.containsKey("tokenName")) {
                tokenName = headerInfo.get("tokenName");
                tokenValue = AuthController.getCookieValue(request, tokenName);
            } else if (headerInfo.containsKey("jtcn")) {
                tokenName = headerInfo.get("jtcn");
                tokenValue = AuthController.getCookieValue(request, tokenName);
            }
            else{
                tokenValue = request.getHeader(com.integral.darwin.auth.jwt.JwtTokenUtil.HEADER);
            }
            if (tokenValue != null && !tokenValue.isEmpty() && !tokenValue.equals("null")) {
                if (com.integral.darwin.auth.jwt.JwtTokenUtil.isValidToken(tokenValue)) {
                    if (authType == null){
                        authType = 1;
                    }
                    populateAuthenticationToken(authType,tokenValue,null, request, response);
                    if (headerInfo.containsKey("userIndex")) {
                        response.setHeader(com.integral.darwin.auth.jwt.JwtTokenUtil.USER_INDEX, headerInfo.get("userIndex"));
                    }
                    List<JwtUserProfile> users = com.integral.darwin.auth.jwt.JwtTokenUtil.getAllUsersFromToken(tokenValue);
                    List<String> userNames = new ArrayList<String>();
                    if (headerInfo.containsKey("user")) {
                        for (JwtUserProfile user : users) {
                            if (user.getU().equals(headerInfo.get("user"))) {
                                return new ResponseEntity<Object>(userNames, HttpStatus.OK);
                            }
                        }
                        return new ResponseEntity<Object>(HttpStatus.UNAUTHORIZED);
                    } else {
                        for (JwtUserProfile user : users) {
                            userNames.add(user.getU());
                        }
                        return new ResponseEntity<Object>(userNames, HttpStatus.OK);
                    }
                } else {
                    return new ResponseEntity<Object>(HttpStatus.UNAUTHORIZED);
                }
            }
            tokenValue = request.getHeader(com.integral.darwin.auth.jwt.JwtTokenUtil.HEADER);
            if (tokenValue == null) {
                return new ResponseEntity<Object>(HttpStatus.UNAUTHORIZED);
            } else {
                return new ResponseEntity<Object>(HttpStatus.BAD_REQUEST);
            }
        } catch (Exception ex){
            log.warn("validateToken : failed to populate the jwt token ex:"+ ex);
            return new ResponseEntity<Object>(HttpStatus.BAD_REQUEST);
        }
    }

    private void populateAuthenticationToken(int authTypeIndex,String token,String apiVersion, HttpServletRequest request, HttpServletResponse response){
        String appName = request.getHeader("X-App-Name");
        StringBuilder path = new StringBuilder();
        String uId = null;
        path.append("/");
        if (appName != null && !"null".equals(appName)){
            path.append(appName);
        }
        ValidateAuthType authType = ValidateAuthType.getAuthTypeFromIndex(authTypeIndex);
        switch (authType){
            case RANDOM_COOKIE_TO_JWT_COOKIE:
            case JWT_HEADER_TO_JWT_COOKIE:
                // adds a cookie to the response with the token, will be used for next requests
                String loginType = AuthController.getCookieValue(request, SecurityConstant.LOGIN_TYPE);
                AuthenticatorUtilC.getInstance().addCookieToResponse(response, com.integral.darwin.auth.jwt.JwtTokenUtil.TOKEN_TYPE, token, -1, loginType, path.toString());
                break;
            case RANDOM_COOKIE_TO_SSO_COOKIE:
                String userIndexHeader = request.getHeader("X-User-Index");
                int userIndex = (userIndexHeader != null && !userIndexHeader.isEmpty()) ? Integer.parseInt(userIndexHeader) : 0;
                List<JwtUserProfile> users = com.integral.darwin.auth.jwt.JwtTokenUtil.getAllUsersFromToken(token);
                if (userIndex < users.size()) {
                    uId = users.get(userIndex).getU();
                    String authToken = cas.generateAuthToken(uId, apiVersion, request);
                    loginType = AuthController.getCookieValue(request, SecurityConstant.LOGIN_TYPE);
                    AuthenticatorUtilC.getInstance().addCookieToResponse(response, SSO_COOKIE, authToken, -1, loginType, path.toString());
                } else {
                    log.warn("No user found for the specified index in the token");
                }
                break;
            case RANDOM_COOKIE_TO_JWT_HEADER:
                response.setHeader(com.integral.darwin.auth.jwt.JwtTokenUtil.HEADER, token);
                break;
            case JWT_HEADER_FOR_SINGLE_USER:
            default:
                // takes the user index from the header and adds the corresponding user to the response as a JWT header
                userIndexHeader = request.getHeader("X-User-Index");
                userIndex = (userIndexHeader != null && !userIndexHeader.isEmpty()) ? Integer.parseInt(userIndexHeader) : 0;
                users = com.integral.darwin.auth.jwt.JwtTokenUtil.getAllUsersFromToken(token);
                if (users.size() > Integer.valueOf(userIndex)) {
                    com.integral.darwin.auth.jwt.JwtTokenUtil.JwtUserProfile user = com.integral.darwin.auth.jwt.JwtTokenUtil.getAllUsersFromToken(token).get(userIndex);
                    uId = user.getU();
                    String originId = user.getOriginId();
                    com.integral.darwin.auth.jwt.JwtTokenUtil.getInstance().addJwtTokenHeaderWithSingleUser(request, response, UserFactory.getUser(uId), originId);
                }
                break;
        }
    }

    /**
     * This method is the wrapper to the loginMFA method, this will let the user login with either SSO_TOKEN based of JWT based authentication based on a org/user level property
     * multi-stpe authentication
     * @param loginData
     * @param request
     * @param response
     * @return
     */
    @RequestMapping( value = "/login/token", method = RequestMethod.POST )
    public
    @ResponseBody
    MFALoginResponse loginJWT(@RequestBody SSOLoginRequest loginData, HttpServletRequest request, HttpServletResponse response ){
        String uId = loginData.getUser() + '@' + loginData.getOrg();
        if (CASMBeanC.getInstance().isJWTAuthenticationEnable() && CASMBeanC.getInstance().getAuthTokenType(UserFactory.getUser(uId)) == 1) {
            return loginMFA(loginData, request,response,false);
        } else {
            return loginMFA(loginData, request,response,true);
        }
    }

    @RequestMapping(value = "/logout/token", method = RequestMethod.POST)
    public @ResponseBody LogoutResponse tokenLogout(HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (JwtSessionUtil.isJwtRequest(request, CASMBeanC.getInstance().isJWTAuthenticationEnable())) {
            LogoutResponse logoutResponse = new LogoutResponse( Status.OK );
            String token = com.integral.darwin.auth.jwt.JwtTokenUtil.getCurrentToken(request);
            if (log.isDebugEnabled()) {
                log.debug("tokenLogout: logging out token: token="+token);
            }
            if (token != null && !token.isEmpty()) {
                int userIndex = JwtSessionUtil.getUserIndex(request);
                AuthenticationToken authenticationToken = cas.logoutAndInvalidateJwtToken(userIndex, token);
                if (authenticationToken != null) {
                    AuthTokenMessage authTokenMessage = new AuthTokenMessage(Message.MESSAGE_TYPE_SESSION_LOGOUT);
                    authTokenMessage.getValues().put(AuthTokenMessage.IS_JWT_TOKEN, String.valueOf(true));
                    authTokenMessage.getValues().put(AuthTokenMessage.MESSAGE_ID, authenticationToken.get_id());
                    log.info("tokenLogout: successfully logout, sending message to DistributedCache="+authTokenMessage);
                    DistributedCacheFactory.getInstance().getDistributedCache().sendMessage( authTokenMessage );
                }
            }
            if (response != null)
            {
                // Delete all the cookies
                AuthenticatorUtilC.getInstance().addSSOCookiesToResponse(response, null, 0); //expiry 0 will delete the cookie from the browser

                Cookie sessionCookie = CookieUtils.getInstance().createCookie(Packet.SESSIONID, null, null, "/", 0);
                response.addCookie(sessionCookie);

                Cookie secSessionCookie = CookieUtils.getInstance().createCookie(Packet.SEC_SESSIONID, null, null, "/", 0);
                response.addCookie(secSessionCookie);

                Cookie authCookie = CookieUtils.getInstance().createCookie(FXIConstants.AUTH_TOKEN, null, null, "/", 0);
                response.addCookie(authCookie);
            }

            return logoutResponse;
        }
        else {
            return logoutV2(request, response);
        }

    }
}
