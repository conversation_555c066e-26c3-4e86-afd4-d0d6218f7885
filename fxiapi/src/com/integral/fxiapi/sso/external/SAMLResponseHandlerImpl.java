package com.integral.fxiapi.sso.external;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.TimeZone;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.integral.cas.config.CASMBeanC;
import com.integral.darwin.auth.jwt.JwtTokenUtil;
import com.integral.security.SecurityConstant;
import com.integral.security.util.AuthenticatorUtilC;
import com.integral.security.util.CookieUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.integral.analytics.Record;
import com.integral.analytics.ga.report.Records;
import com.integral.analytics.impl.CollectorFactory;
import com.integral.cas.CASClient;
import com.integral.cas.CommonAuthenticationService;
import com.integral.fxiapi.FXIConstants;
import com.integral.fxiapi.Status;
import com.integral.fxiapi.sso.SSOLoginResponse;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.security.CryptC;
import com.integral.security.SecurityServiceC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;

public class SAMLResponseHandlerImpl implements SAMLResponseHandler {

	private static final String GLOBAL_PATH = "/";
	private static final String BASE_PATH = "/fxi/web";

	protected Log log = LogFactory.getLog( this.getClass() );
	
	private String apiVersion;
	private String successUrl;
	private String failureUrl;
	private CommonAuthenticationService cas;
	private final ConfigPropertyFacade configProperty;
	
	@Autowired
	BrandingResolver brandingResolver;

	public SAMLResponseHandlerImpl(ConfigPropertyFacade configProperty) {
		this.configProperty = configProperty;
	}

	@Override
	public void onSuccess(HttpServletRequest request, HttpServletResponse response, String userMapping, String idpEntityId) {
		User user = UserFactory.getInternalMappedUser(userMapping);
		if(user == null || user.getOrganization() == null) {
			onFailure(request, response, "User Not Found", idpEntityId);
			log.warn("SAML ResponseHandler :  User Not Found For External SSO ID : " + userMapping);
			recordExternalSSOFailure("FAILURE", userMapping);
			return;
		}
		String uId = user.getFullyQualifiedName();
		log.warn("SAML ResponseHandler :  External SSO ID - " + userMapping + " is mapped to User - " + uId);
		String configuredApiVersion = configProperty.getSAMLResource("apiversion", "integral");
		if(StringUtils.isNotBlank(configuredApiVersion)) {
			apiVersion = configuredApiVersion;
		}
		String brand = configProperty.getSAMLBrandName(idpEntityId);
		boolean isDebug = log.isDebugEnabled();
		int ssoAuthType = CASMBeanC.getInstance().getAuthTokenType(user);
		if (isDebug) {
			log.debug("onSuccess: brand="+brand+" customerLogin="+true+" ssoAuthType="+ssoAuthType);
		}
		try {
			if (ssoAuthType == 1) {
				List<User> users = new ArrayList<User>();
				users.add(user);
				String jwtToken = JwtTokenUtil.getInstance().generateTokenForUsers(users, true);
				if (isDebug) {
					log.debug("onSuccess: brand="+brand+" customerLogin="+true+" generatedToken="+jwtToken);
				}
				if (jwtToken != null && !jwtToken.isEmpty()) {
					String temporaryCookieName =  JwtTokenUtil.getInstance().addJwtTokenAsTemporaryCookie(response, jwtToken, user);
					if (isDebug) {
						log.debug("onSuccess: brand="+brand+" temporaryCookieName="+temporaryCookieName+" generatedToken="+jwtToken);
					}
					addLoginTimeCookieToResponse(response);
					log.info("SAML ResponseHandler : JWT token added to temporary cookie, Internal Login done successfully for " + uId + " with External SSO ID : " + userMapping);
					addSAMLBrandCookieToResponse( brand, response );
					redirectWithTemporaryCookie(request, response, brand, true, null, user, temporaryCookieName);
					recordExternalSSOLogin("SUCCESS", user.getOrganization().getShortName(), user.getShortName());
					SecurityServiceC.getInstance().auditExternalLoginSuccess(user);
				}
				return;
			}

			String authToken = cas.generateAuthToken(uId, apiVersion, request);
			SSOLoginResponse loginResponse = new SSOLoginResponse(Status.OK);
			String errorMesg = addSSOCookiesToResponse(response, authToken, uId, user);
			if(StringUtils.isNotBlank(errorMesg)) {
				SecurityServiceC.getInstance().auditExternalLoginFailed(user);
				onFailure(request, response, errorMesg, idpEntityId);
				return;
			}
			addLoginTimeCookieToResponse(response);
			loginResponse.setExpiryTime(getExpiryTime(authToken));
			loginResponse.setExpiryTime(getExpiryTime(authToken));
			loginResponse.setServerUTCTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());
			log.warn("SAML ResponseHandler :  Internal Login done successfully for " + uId + " with External SSO ID : " + userMapping);
			//Add SAML branding cookie -- Only for logout path
			addSAMLBrandCookieToResponse( brand, response );
			redirect(request, response,brand, true, null, user);
			recordExternalSSOLogin("SUCCESS", user.getOrganization().getShortName(), user.getShortName());
		} catch (Exception e) {
			SecurityServiceC.getInstance().auditExternalLoginFailed(user);
			log.error("SAML ResponseHandler : Error While Redirecting to success Url",e);
		}
		SecurityServiceC.getInstance().auditExternalLoginSuccess(user);
	}
	
	public void recordExternalSSOFailure(String action, String userMapping) {
		String userOrg = "-NA-";
		Record r =  Records.newRecordSansUser("ExternalSSO", action);
		r.setDimensionValue(Records.DIM_FEATURE,  "Login");
		r.setDimensionValue(Records.DIM_USER,  userMapping+"@" + userOrg);
		r.setDimensionValue(Records.DIM_USER_ORG, userOrg);
		r.setMetricValue(Records.METRIC_CLICK, 1);
		CollectorFactory.getCollector().collect(r);
	}
	
	public void recordExternalSSOLogin(String action, String userOrg, String user) {
		Record r =  Records.newRecordSansUser("ExternalSSO", action);
		r.setDimensionValue(Records.DIM_FEATURE,  "Login");
		r.setDimensionValue(Records.DIM_USER, user+"@" + userOrg);	
		r.setDimensionValue(Records.DIM_USER_ORG, userOrg);
		r.setMetricValue(Records.METRIC_CLICK, 1);
		CollectorFactory.getCollector().collect(r);
	}

	@Override
	public void onFailure(HttpServletRequest request, HttpServletResponse response, String errorMesg, String idpEntityId) {
		try {
			String brand = configProperty.getSAMLBrandName(idpEntityId);
			redirect(request, response,brand, false, errorMesg, null);
			recordExternalSSOFailure("FAILURE", idpEntityId);
		} catch (IOException e) {
			log.error("SAML ResponseHandler OnFailure : Error while redirecting  ",e);
		}
	}

	private void redirect(HttpServletRequest request, HttpServletResponse response,String brand, boolean success, String errorMesg, User user) throws IOException {
		redirectWithTemporaryCookie(request, response, brand, success, errorMesg, user, null);
	}

	private void redirectWithTemporaryCookie(HttpServletRequest request, HttpServletResponse response,String brand, boolean success, String errorMesg, User user, String temporaryCookieName) throws IOException {
		if(success) {
			String appName = configProperty.getSSOClientAppName ( user );
			String configuredSuccessUrl;
			if ( user != null && appName != null && configProperty.getSSOClientAppSuccessURL( appName ) != null )
			{
				configuredSuccessUrl = configProperty.getSSOClientAppSuccessURL( appName );
				if ( configuredSuccessUrl != null && StringUtils.isNotBlank ( brand  ) )
				{
					configuredSuccessUrl += configuredSuccessUrl.endsWith ( "/" ) ? brand : ( "/" + brand );
				}
			}
			else
			{
				configuredSuccessUrl = configProperty.getSAMLResource("successurl", brand);
			}
			if(StringUtils.isNotBlank(configuredSuccessUrl)) {
				if (temporaryCookieName != null && !temporaryCookieName.isEmpty()) {
					configuredSuccessUrl += "?" + "jtcn=" + temporaryCookieName + "&ea=1";
				}
				if(configuredSuccessUrl.startsWith("/")) {
					response.sendRedirect(request.getContextPath() + configuredSuccessUrl);
				}else {
					response.sendRedirect(configuredSuccessUrl);
				}
			}else {
				response.sendRedirect(request.getContextPath() + successUrl);
			}
		}else {
			String configuredFailUrl = configProperty.getSAMLResource("failureurl", brand);
			if(StringUtils.isNotBlank(configuredFailUrl)) {
				if(StringUtils.isNotBlank(errorMesg)) {
					configuredFailUrl=configuredFailUrl+"?error="+errorMesg;
				}
				if(configuredFailUrl.startsWith("/")) {
					response.sendRedirect(request.getContextPath() + configuredFailUrl);
				}else {
					response.sendRedirect(configuredFailUrl);
				}
			}else {
				String landingUrl = this.failureUrl;
				if(StringUtils.isNotBlank(errorMesg)) {
					landingUrl=landingUrl+"?error="+errorMesg;
				}
				response.sendRedirect(request.getContextPath() + failureUrl);
			}
		}
	}



	private void addLoginTimeCookieToResponse(HttpServletResponse response) {
		Cookie loginTimeCookie = new Cookie( FXIConstants.LOGIN_TIME, Long.toString( System.currentTimeMillis() ) );
		loginTimeCookie.setPath( GLOBAL_PATH );
		response.addCookie( loginTimeCookie );
	}

	private void addSAMLBrandCookieToResponse(String brand, HttpServletResponse response) {
		Cookie samlBranding = CookieUtils.getInstance().createCookie(SAML_BRAND_COOKIE, brand, null, BASE_PATH, -1);
		response.addCookie(samlBranding);
	}

	private String addSSOCookiesToResponse(HttpServletResponse response, String authToken, String uId, User user) throws Exception {
		AuthenticatorUtilC.getInstance().addSSOCookiesToResponse(response, authToken);
		
		Cookie externalSSODomainCookie  = CookieUtils.getInstance().createCookie(EXTERNAL_SSO_COOKIE, uId, INTEGRAL_DOMAIN, GLOBAL_PATH, -1);
		response.addCookie(externalSSODomainCookie);		
		externalSSODomainCookie = CookieUtils.getInstance().createCookie(EXTERNAL_SSO_COOKIE, uId, BETA_INTEGRAL_DOMAIN, GLOBAL_PATH, -1);
		response.addCookie(externalSSODomainCookie);
		
		if(configProperty.getCasMBean().isOIDCServiceLocalhost())
		{
			Cookie ssoLocalhostCookie = CookieUtils.getInstance().createCookie(SecurityConstant.SSO_COOKIE, authToken, null, GLOBAL_PATH, -1);
			response.addCookie(ssoLocalhostCookie);
			Cookie externalSSOLocalhostCookie = CookieUtils.getInstance().createCookie(EXTERNAL_SSO_COOKIE, uId, null, GLOBAL_PATH, -1);
			response.addCookie(externalSSOLocalhostCookie);
		}
				
		Cookie integralDomain  = CookieUtils.getInstance().createCookie(DOMAIN_COOKIE, INTEGRAL_DOMAIN, INTEGRAL_DOMAIN, GLOBAL_PATH, -1);
		response.addCookie(integralDomain);		
		Cookie betaDomain = CookieUtils.getInstance().createCookie(DOMAIN_COOKIE, BETA_INTEGRAL_DOMAIN, BETA_INTEGRAL_DOMAIN, GLOBAL_PATH, -1);
		response.addCookie(betaDomain);

		return setRedirectionCookieFor(user.getOrganization(), response);
	}
	
	
	public static void removeExternalCookies(HttpServletResponse response) {
		Cookie externalSSODomainCookie  = CookieUtils.getInstance().createCookie(EXTERNAL_SSO_COOKIE, null, INTEGRAL_DOMAIN, GLOBAL_PATH, -1);
		response.addCookie(externalSSODomainCookie);
		externalSSODomainCookie = CookieUtils.getInstance().createCookie(EXTERNAL_SSO_COOKIE, null, null, GLOBAL_PATH, -1);
		response.addCookie(externalSSODomainCookie);
		externalSSODomainCookie = CookieUtils.getInstance().createCookie(EXTERNAL_SSO_COOKIE, null, BETA_INTEGRAL_DOMAIN, GLOBAL_PATH, -1);
		response.addCookie(externalSSODomainCookie);
		return;
	}
	
	private String setRedirectionCookieFor(Organization org, HttpServletResponse response) throws Exception {
		String errorMsg = null;
		if (org.getVirtualServer() != null) {
			String virtualServerName = org.getVirtualServer().getShortName();
			Cookie orgVirtualServerCookie = new Cookie(ORG_SERVICE_GROUP, virtualServerName);
			orgVirtualServerCookie.setPath("/");
			orgVirtualServerCookie.setMaxAge(-1);
			response.addCookie(orgVirtualServerCookie);
			String msg = "SAML ResponseHandler : Sending Service Group " + virtualServerName + " for org " + org.getShortName();
			log.warn(msg);
		} else {
			errorMsg = "Organization " + org.getShortName() + " does not have a virtual server assigned to it";
			log.warn("SAML ResponseHandler  : " + errorMsg);
		}
		return errorMsg;
	}
	
	private long getExpiryTime(String encToken) {
		String decryptedToken = CryptC.decryptURLSafeString(encToken);
		return CASClient.AuthToken.parseExpiryTime(decryptedToken);
	}
	
	
	@Override
	public void setApiVersion(String apiVersion) {
		this.apiVersion = apiVersion;
	}

	@Override
	public String getApiVersion() {
		return apiVersion;
	}

	@Override
	public CommonAuthenticationService getCas() {
		return cas;
	}

	@Override
	public void setCas(CommonAuthenticationService cas) {
		this.cas = cas;
	}

	@Override
	public String getSuccessUrl() {
		return successUrl;
	}

	@Override
	public void setSuccessUrl(String successUrl) {
		this.successUrl = successUrl;
	}

	@Override
	public String getFailureUrl() {
		return failureUrl;
	}

	@Override
	public void setFailureUrl(String failureUrl) {
		this.failureUrl = failureUrl;
	}

}
