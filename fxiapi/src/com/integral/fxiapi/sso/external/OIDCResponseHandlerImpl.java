package com.integral.fxiapi.sso.external;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.util.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.integral.cas.config.CASMBeanC;
import com.integral.darwin.auth.jwt.JwtSessionUtil;
import com.integral.darwin.auth.jwt.JwtTokenUtil;
import com.integral.security.SecurityConstant;
import com.integral.security.util.CookieUtils;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;

import com.integral.analytics.Record;
import com.integral.analytics.ga.report.Records;
import com.integral.analytics.impl.CollectorFactory;
import com.integral.cas.CommonAuthenticationService;
import com.integral.cas.config.CASMBean;
import com.integral.fxiapi.FXIConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.security.SecurityServiceC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;

public class OIDCResponseHandlerImpl implements OIDCResponseHandler {

	private static final String GLOBAL_PATH = "/";

	protected Log log = LogFactory.getLog( this.getClass() );
	
	private String apiVersion;
	private String successUrl;
	private String failureUrl;
	private CommonAuthenticationService cas;
	private final ConfigPropertyFacade configProperty;
	private final CASMBean casMBean;

	public OIDCResponseHandlerImpl(ConfigPropertyFacade configProperty) {
		this.configProperty = configProperty;
		casMBean = configProperty.getCasMBean();
	}
	/*
	This is authentication call(1st call).
	 */
	@Override
	public void redirectToExternal(String brand, HttpServletRequest request, HttpServletResponse response) {	
		try{
			String oidcServiceProviderAuthenticationURL = casMBean.getOIDCServiceProviderAuthenticationURL(brand);
			String oidcServiceProviderRedirectURL = casMBean.getOIDCServiceProviderRedirectURL(brand);
			String clientId = casMBean.getOIDCServiceProviderClientId(brand);
			String codeVerifier = casMBean.getOIDCServiceProviderCodeVerifier(brand);
			if(oidcServiceProviderAuthenticationURL == null || oidcServiceProviderRedirectURL == null)
			{
				log.warn("Either brandName in url or brand property is missing");
				onFailure(request, response, "Either brandName in url or brand property is missing", brand);
				return;
			}
			StringBuilder https_url = new StringBuilder(300);
			https_url.append(oidcServiceProviderAuthenticationURL);
			https_url.append("?client_id=").append(clientId).append("&response_type=code&scope=profile%20openid");
			https_url.append("&redirect_uri=");
			https_url.append(oidcServiceProviderRedirectURL);
			Boolean generateCodeChallengeEnabled = casMBean.isOidcServiceProviderGenerateCodeChallengeEnabled(brand);
			if(null != generateCodeChallengeEnabled && generateCodeChallengeEnabled){
				https_url.append("&code_challenge=").append(generateCodeChallenge(codeVerifier));
			}else {
				https_url.append("&code_challenge=").append(codeVerifier);
			}
			Map<String, String> additionalParams = casMBean.getOidcServiceProviderAuthenticationAdditionalParams(brand);
			if (additionalParams != null) {
				for (Map.Entry<String, String> entry : additionalParams.entrySet()) {
					https_url.append("&").append(entry.getKey()).append("=").append(entry.getValue());
				}
			}
			log.info("Brand: " + brand + ", Authentication url: " + https_url.toString());
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			log.info("ServiceProviderController.externalAuthenticate PATH: " + request.getRequestURI());
			response.sendRedirect(https_url.toString());

	} catch (Exception e) {
		StringBuilder builder = new StringBuilder("Error Code: ");
		String errorMsg = builder.append(e.getMessage()).toString();
		log.error(errorMsg, e);
		onFailure(request, response, errorMsg, "");
	}
	}

	public String generateCodeChallenge(String verifier) {
		String challenge = verifier;
		try {
			byte[] bytes = verifier.getBytes("US-ASCII");
			MessageDigest md = MessageDigest.getInstance("SHA-256");
			md.update(bytes, 0, bytes.length);
			byte[] digest = md.digest();
			challenge = new Base64().encodeBase64URLSafeString(digest);
		} catch (Exception e) {
			log.warn("OIDCResponseHandlerImpl.generateCodeChallenge(): some error while generating code Challenge");
		}
		return challenge;
	}

	/*
	This is Authorization call(2nd call).
	 */
	@Override
	public void postToExternal(String brand, HttpServletRequest request, HttpServletResponse response, String code) {	
		try
		{
			String oidcServiceProviderAuthorizationURL = casMBean.getOIDCServiceProviderAuthorizationURL(brand);
			String oidcServiceProviderRedirectURL = casMBean.getOIDCServiceProviderRedirectURL(brand);
			String clientId = casMBean.getOIDCServiceProviderClientId(brand);
			String clientSecret = casMBean.getOIDCServiceProviderClientSecret(brand);
			String codeVerifier = casMBean.getOIDCServiceProviderCodeVerifier(brand);
			if(oidcServiceProviderAuthorizationURL == null || oidcServiceProviderRedirectURL == null)
			{
				log.warn("Either brandName in url or brand property is missing");
				onFailure(request, response, "Either brandName in url or brand property is missing", brand);
				return;
			}
			log.info("ServiceProviderController.ssoAuthenticate PATH: "+request.getRequestURI());
			log.info("Code Received="+code); 
			log.info("URL 1: " + casMBean.getOIDCServiceProviderAuthenticationURL(brand));
			log.info("URL 2: "+oidcServiceProviderAuthorizationURL);
			log.info("Redirect url : "+oidcServiceProviderRedirectURL);
			log.info("ClientId: "+clientId);
			log.info("ClientSecret: "+clientSecret);
			URL url = new URL(oidcServiceProviderAuthorizationURL);
			StringBuilder params = new StringBuilder(300);
			params.append("&grant_type=authorization_code");
			params.append("&redirect_uri=").append(oidcServiceProviderRedirectURL);
			params.append("&code=").append(code);
			params.append("&code_verifier=").append(codeVerifier);
			params.append("&client_id=").append(clientId);
			if(clientSecret != null){
				params.append("&client_secret=").append(clientSecret);
			}
			Map<String, String> additionalParams = casMBean.getOidcServiceProviderAuthorizationAdditionalParams(brand);
			if (additionalParams != null) {
				for (Map.Entry<String, String> entry : additionalParams.entrySet()) {
					params.append("&").append(entry.getKey()).append("=").append(entry.getValue());
				}
			}
			log.info("Brand: " + brand + ", Authorization params: " + params.toString());
			HttpURLConnection con = (HttpURLConnection) url.openConnection();
			BufferedReader in;
			// add request header
			con.setRequestMethod("POST");
			con.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
			
			// Send post request
			con.setDoOutput(true);
			DataOutputStream wr = new DataOutputStream(con.getOutputStream());
			wr.writeBytes(params.toString());
			wr.flush();
			wr.close();

			int responseCode = con.getResponseCode();
			log.info("Response Code : " + responseCode);	
			if (responseCode >= 400)
				in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
			else 
				in = new BufferedReader(new InputStreamReader(con.getInputStream()));

			String inputLine;
			StringBuilder responseData = new StringBuilder();
			while ((inputLine = in.readLine()) != null) {
				responseData.append(inputLine);
			}
			in.close();
			log.info("Incoming Data on Response Buffer:"+responseData);
			JSONObject jSONObject = new JSONObject(responseData.toString());
			Object idTokenFull = jSONObject.get("id_token");
			if(idTokenFull != null){
				String accessTokenData = idTokenFull.toString();
				log.info("accessTokenData: "+ accessTokenData);
				String idToken = getIdToken(accessTokenData);
				log.info("ID-Token: "+idToken);
				onSuccess(request, response, idToken, brand);
			}
			else{
				String errorMsg = "Access token has not come in response";
				log.error(errorMsg);
				onFailure(request, response,  errorMsg, "");
			}

	} catch (Exception e) {
		StringBuilder builder = new StringBuilder("Error Code: ");
		String errorMsg = builder.append(e.getMessage()).toString();
		log.error(errorMsg, e);
		onFailure(request, response, errorMsg, "");
	}
		
	}

	public static String getIdToken(String accessTokenData) {
		String idToken = accessTokenData;
		if( accessTokenData.contains ( "." ) ) {
			idToken = accessTokenData.substring(accessTokenData.indexOf(".") + 1, accessTokenData.lastIndexOf("."));
		}
		return idToken;
	}

	@Override
	public void onSuccess(HttpServletRequest request, HttpServletResponse response, String xIdToken, String brand) {
		String userMapping = null;
		int cookieValidity;
		Map<String, String> longOrgAndShortName = new TreeMap<String, String>();
		try {
			brand = brand == null ? "" : brand;
			byte[] base64Decoded = new Base64().decode(xIdToken);
			String decodedJson = new String(base64Decoded);
			if(!decodedJson.endsWith("}")) {
				if (!decodedJson.endsWith("\"")) {
					decodedJson = decodedJson + "\"";
				}
				if (!decodedJson.endsWith("}")) {
					decodedJson = decodedJson + "}";
				}
			}
			JSONObject jSONObject = new JSONObject(decodedJson);
			String ssoIdField = casMBean.getOIDCServiceProviderSSOIdField(brand);
			userMapping = jSONObject.get(ssoIdField).toString();
			String expiryTimeString = jSONObject.get("exp").toString();
			long expiryTime = Long.parseLong(expiryTimeString);
			if(expiryTimeString.length() < 13){
				// this time is in sec, convert to millis
				expiryTime = expiryTime *1000;
			}
			cookieValidity = (int)((expiryTime - System.currentTimeMillis())/1000);
			log.info("Decoded id token: " + decodedJson+" userMapping: "+userMapping+" expiryTime: "+expiryTime+" cookieValidity(sec): "+cookieValidity);
			if(new Date(expiryTime).before(new Date())){
				onFailure(request, response, "Expiry time has reached", brand);
				log.warn("Expiry time has reached, expiryTime: "+ expiryTime);
				recordExternalSSOFailure("FAILURE", userMapping);
				return;
			}
		} catch (Exception e) {
			onFailure(request, response, "Error while getting userMapping", brand);
			log.warn("Error while getting userMapping",e);
			recordExternalSSOFailure("FAILURE", userMapping);
			return;
		}
		String configuredApiVersion = configProperty.getSAMLResource("apiversion", "integral");
		if(StringUtils.isNotBlank(configuredApiVersion)) {
			apiVersion = configuredApiVersion;
		}
		if(casMBean.isCustomerLogin(brand))
		{
			User user = UserFactory.getInternalMappedUser(userMapping);
			if(user == null || user.getOrganization() == null) {
				onFailure(request, response, "User Not Found", brand);
				log.warn("OIDC ResponseHandler :  User Not Found For External SSO ID : " + userMapping);
				recordExternalSSOFailure("FAILURE", userMapping);
				return;
			}
			String uId = user.getFullyQualifiedName();
			log.info("OIDC ResponseHandler : CustomerLogin: External SSO ID - " + userMapping + " is mapped to User - " + uId);
			try {
				boolean isDebug = log.isDebugEnabled();
				int ssoAuthType = CASMBeanC.getInstance().getAuthTokenType(user);
				if (isDebug) {
					log.debug("onSuccess: brand="+brand+" customerLogin="+true+" ssoAuthType="+ssoAuthType);
				}
				if (ssoAuthType == 1) {
					List<User> users = new ArrayList<User>();
					users.add(user);
					String jwtToken = JwtTokenUtil.getInstance().generateTokenForUsers(users, true);
					if (isDebug) {
						log.debug("onSuccess: brand="+brand+" customerLogin="+true+" generatedToken="+jwtToken);
					}
					if (jwtToken != null && !jwtToken.isEmpty()) {
						String temporaryCookieName =  JwtTokenUtil.getInstance().addJwtTokenAsTemporaryCookie(response, jwtToken, user);
						if (isDebug) {
							log.debug("onSuccess: brand="+brand+" temporaryCookieName="+temporaryCookieName+" generatedToken="+jwtToken);
						}
						addLoginTimeCookieToResponse(response);
						log.info("OIDC ResponseHandler : JWT token added to temporary cookie, Internal Login done successfully for " + uId + " with External SSO ID : " + userMapping);
						addOIDCBrandCookieToResponse( brand, response );
						redirectWithTemporaryCookie(request, response, brand, true, null, user, temporaryCookieName);
						recordExternalSSOLogin("SUCCESS", user.getOrganization().getShortName(), user.getShortName());
						SecurityServiceC.getInstance().auditExternalLoginSuccess(user);
					}
				} else {
					String authToken = cas.generateAuthToken(uId, apiVersion, request);
					String errorMesg = addCustomerLoginSSOCookiesToResponse(response, authToken, uId, user, cookieValidity);
					if(StringUtils.isNotBlank(errorMesg)) {
						SecurityServiceC.getInstance().auditExternalLoginFailed(user);
						onFailure(request, response, errorMesg, "");
						return;
					}
					addLoginTimeCookieToResponse(response);
					log.info("OIDC ResponseHandler :  Internal Login done successfully for " + uId + " with External SSO ID : " + userMapping);
					addOIDCBrandCookieToResponse( brand, response );
					redirect(request, response, brand, true, null, user);
					recordExternalSSOLogin("SUCCESS", user.getOrganization().getShortName(), user.getShortName());
					SecurityServiceC.getInstance().auditExternalLoginSuccess(user);
				}
			} catch (Exception e) {
				SecurityServiceC.getInstance().auditExternalLoginFailed(user);
				log.error("OIDC ResponseHandler : Error While Redirecting to success Url",e);
			}

		}
		else {
			try {
				Collection<User> users = UserFactory.getMultiIdentitySSOTokenMappedUsers(userMapping);
				if (users == null || users.isEmpty()) {
					onFailure(request, response, "Users Not Found", "");
					log.warn("OIDC ResponseHandler :  Users Not Found For External SSO ID : " + userMapping);
					recordExternalSSOFailure("FAILURE", userMapping);
					return;
				}
				List<User> userList = new ArrayList<User>(users);
				int ssoAuthType = CASMBeanC.getInstance().getAuthTokenType(userList.get(0));
				if (log.isDebugEnabled()) {
					log.debug("onSuccess: brand="+brand+" customerLogin="+false+" ssoAuthType="+ssoAuthType);
				}
				if (ssoAuthType == 1) {
					String tokenWithMultiPleUser = JwtTokenUtil.getInstance().generateTokenForUsers(userList, true);
					String temporaryCookieName = JwtTokenUtil.getInstance().addJwtTokenAsTemporaryCookie(response,tokenWithMultiPleUser, userList.get(0));
					if (log.isDebugEnabled()) {
						log.debug("onSuccess: brand="+brand+" tokenWithMultiPleUser="+tokenWithMultiPleUser+" temporaryCookieName="+temporaryCookieName);
					}
					addLoginTimeCookieToResponse(response);
					log.info("OIDC ResponseHandler : JWT token added to temporary cookie, Internal Login done successfully, External SSO ID : " + userMapping);
					addOIDCBrandCookieToResponse( brand, response );
					redirectWithTemporaryCookie(request, response, brand, true, null, null, temporaryCookieName);
					recordExternalSSOMultiuserLogin("SUCCESS", userMapping);
				} else {
					Iterator<User> userIterator = users.iterator();
					StringBuilder uIds = new StringBuilder();
					StringBuilder authTokens = new StringBuilder();
					while (userIterator.hasNext()) {
						User user = userIterator.next();
						if (user.getOrganization() == null) {
							onFailure(request, response, "User Organization Not Found", "");
							log.warn("OIDC ResponseHandler :  User Organization Not Found For External SSO ID : " + userMapping);
							recordExternalSSOFailure("FAILURE", userMapping);
							return;
						}
						String uId = user.getFullyQualifiedName();
						log.info("OIDC ResponseHandler :  DeskLogin External SSO ID - " + userMapping + " is mapped to User - " + uId);
						String authToken = cas.generateAuthToken(uId, apiVersion, request);
						String errorMesg = addRedirectionCookiesToResponse(response, user);
						if (StringUtils.isNotBlank(errorMesg)) {
							SecurityServiceC.getInstance().auditExternalLoginFailed(user);
							onFailure(request, response, errorMesg, "");
							return;
						}
						longOrgAndShortName.put(user.getOrganization().getLongName() + "-" + user.getShortName(), uId+","+authToken);
						SecurityServiceC.getInstance().auditExternalLoginSuccess(user);
					}
					for (String uIdAndAuthTokenAndAuthTokenAdmin : longOrgAndShortName.values()) {
						String[] array = uIdAndAuthTokenAndAuthTokenAdmin.split(",");
						uIds.append(array[0]).append(",");
						authTokens.append(array[1]).append(",");
					}
					addUserListToResponse(request, response, uIds.toString());
					addAuthTokenCookiesToResponse(response, authTokens.toString());
					addDomainNameAndCookieExpiryToResponse(response, cookieValidity);
					addLoginTimeCookieToResponse(response);
					addOIDCBrandCookieToResponse(brand, response);
					recordExternalSSOMultiuserLogin("SUCCESS", userMapping);
					redirect(request, response, brand, true, null, null);
				}
			} catch (Exception e) {
				log.error("OIDC ResponseHandler : Error While Redirecting to success Url", e);
			}
		}
	}


	private String addCustomerLoginSSOCookiesToResponse(HttpServletResponse response, String authToken, String uId, User user, int cookieValidity) throws Exception {

		List<String> domainNames = casMBean.getOidcServiceCookiesDomainName(user);

		for(String domainName : domainNames) {
			Cookie ssoTokenCookie = CookieUtils.getInstance().createCookie(SecurityConstant.SSO_COOKIE, authToken, domainName, GLOBAL_PATH, -1);
			response.addCookie(ssoTokenCookie);
			Cookie externalSSODomainCookie = CookieUtils.getInstance().createCookie(EXTERNAL_SSO_COOKIE, uId, domainName, GLOBAL_PATH, cookieValidity);
			response.addCookie(externalSSODomainCookie);
			Cookie integralDomain = CookieUtils.getInstance().createCookie(DOMAIN_COOKIE, domainName, domainName, GLOBAL_PATH, -1);
			response.addCookie(integralDomain);
		}

		return setRedirectionCookieFor(user.getOrganization(), response, true);
	}
	
	public void recordExternalSSOFailure(String action, String userMapping) {
		String userOrg = "-NA-";
		Record r =  Records.newRecordSansUser("ExternalSSO_OIDC", action);
		r.setDimensionValue(Records.DIM_FEATURE,  "Login");
		r.setDimensionValue(Records.DIM_USER,  userMapping+"@" + userOrg);
		r.setDimensionValue(Records.DIM_USER_ORG, userOrg);
		r.setMetricValue(Records.METRIC_CLICK, 1);
		CollectorFactory.getCollector().collect(r);
	}
	
	public void recordExternalSSOLogin(String action, String userOrg, String user) {
		Record r =  Records.newRecordSansUser("ExternalSSO_OIDC_SingleUser", action);
		r.setDimensionValue(Records.DIM_FEATURE,  "Login");
		r.setDimensionValue(Records.DIM_USER, user+"@" + userOrg);	
		r.setDimensionValue(Records.DIM_USER_ORG, userOrg);
		r.setMetricValue(Records.METRIC_CLICK, 1);
		CollectorFactory.getCollector().collect(r);
	}

	public void recordExternalSSOMultiuserLogin(String action, String userMapping) {
		String userOrg = "-NA-";
		Record r =  Records.newRecordSansUser("ExternalSSO_OIDC_MultiUser", action);
		r.setDimensionValue(Records.DIM_FEATURE,  "Login");
		r.setDimensionValue(Records.DIM_USER, userMapping);
		r.setDimensionValue(Records.DIM_USER_ORG, userOrg);
		r.setMetricValue(Records.METRIC_CLICK, 1);
		CollectorFactory.getCollector().collect(r);
	}

	@Override
	public void onFailure(HttpServletRequest request, HttpServletResponse response, String errorMesg, String brand) {
		try {
			redirect(request, response,brand, false, errorMesg, null);
			recordExternalSSOFailure("FAILURE", brand);
		} catch (IOException e) {
			log.error("OIDC ResponseHandler OnFailure : Error while redirecting  ",e);
		}
	}

	private void redirect(HttpServletRequest request, HttpServletResponse response,String brand, boolean success, String errorMesg, User user) throws IOException {
			redirectWithTemporaryCookie(request, response, brand, success, errorMesg, user, null);
	}

	private void redirectWithTemporaryCookie(HttpServletRequest request, HttpServletResponse response,String brand, boolean success, String errorMesg, User user, String temporaryCookieName) throws IOException {
		if(success) {
			if(casMBean.isCustomerLogin(brand))
			{
				String appName = casMBean.getSSOClientAppName ( user );
				String configuredSuccessUrl;
				if ( user != null && appName != null && casMBean.getSSOClientAppSuccessURL ( appName ) != null )
				{
					configuredSuccessUrl = casMBean.getSSOClientAppSuccessURL( appName );
					if ( configuredSuccessUrl != null && StringUtils.isNotBlank ( brand  ) )
					{
						configuredSuccessUrl += configuredSuccessUrl.endsWith ( "/" ) ? brand : ( "/" + brand );
					}
				}
				else
				{
					configuredSuccessUrl = casMBean.getOIDCResource("successurl", brand);
				}
				if(StringUtils.isNotBlank(configuredSuccessUrl)) {
					if (temporaryCookieName != null && !temporaryCookieName.isEmpty()) {
						configuredSuccessUrl += "?" + "jtcn=" + temporaryCookieName + "&ea=1";
					}
					if(configuredSuccessUrl.startsWith("/")) {
						response.sendRedirect(request.getContextPath() + configuredSuccessUrl);
					}else {
						response.sendRedirect(configuredSuccessUrl);
					}
				}else {
					response.sendRedirect(request.getContextPath() + successUrl);
				}
			}else {
				response.sendRedirect("/fxi/integral/sso/external/ssoLoginDashboard.jsp");
			}
		}
		else {
			String configuredFailUrl = configProperty.getSAMLResource("failureurl", brand);
			if(StringUtils.isNotBlank(configuredFailUrl)) {
				if(StringUtils.isNotBlank(errorMesg)) {
					configuredFailUrl=configuredFailUrl+"?error="+errorMesg;
				}
				if(configuredFailUrl.startsWith("/")) {
					response.sendRedirect(request.getContextPath() + configuredFailUrl);
				}else {
					response.sendRedirect(configuredFailUrl);
				}
			}else {
				String landingUrl = this.failureUrl;
				if(StringUtils.isNotBlank(errorMesg)) {
					landingUrl=landingUrl+"?error="+errorMesg;
				}
				response.sendRedirect(request.getContextPath() + failureUrl);
			}
		}
	}

	private void addLoginTimeCookieToResponse(HttpServletResponse response) {
		Cookie loginTimeCookie = new Cookie( FXIConstants.LOGIN_TIME, Long.toString( System.currentTimeMillis() ) );
		loginTimeCookie.setPath( GLOBAL_PATH );
		response.addCookie( loginTimeCookie );
	}
	
	private void addUserListToResponse(HttpServletRequest request, HttpServletResponse response, String users) {
		users = users.substring(0, users.lastIndexOf(','));
		Cookie userListCookie = CookieUtils.getInstance().createCookie( "USERS", users, casMBean.getOidcServiceCookiesDomainName(), GLOBAL_PATH, -1);
		response.addCookie( userListCookie );
	}
	
	private void addDomainNameAndCookieExpiryToResponse(HttpServletResponse response, int cookieValidity) {
		if(casMBean.getOidcServiceCookiesDomainName() != null) {
			Cookie integralDomain = CookieUtils.getInstance().createCookie(DOMAIN_COOKIE, casMBean.getOidcServiceCookiesDomainName(), casMBean.getOidcServiceCookiesDomainName(), GLOBAL_PATH, -1);
			response.addCookie(integralDomain);
		}
		Cookie expiryCookie = CookieUtils.getInstance().createCookie(SSO_EXPIRY, cookieValidity+"", casMBean.getOidcServiceCookiesDomainName(), GLOBAL_PATH, cookieValidity);
		response.addCookie(expiryCookie);
	}

	private void addOIDCBrandCookieToResponse(String brand, HttpServletResponse response) {
		Cookie OIDCBranding = CookieUtils.getInstance().createCookie(OIDC_BRAND_COOKIE, brand, casMBean.getOidcServiceCookiesDomainName(), GLOBAL_PATH, -1);
		response.addCookie(OIDCBranding);
	}

	private String addRedirectionCookiesToResponse(HttpServletResponse response, User user) throws Exception {
		return setRedirectionCookieFor(user.getOrganization(), response, false);
	}

	private void addAuthTokenCookiesToResponse(HttpServletResponse response, String authTokens) {
		authTokens = authTokens.substring(0, authTokens.lastIndexOf(','));
		Cookie ssoDomainCookie = CookieUtils.getInstance().createCookie(SSO_TOKEN_TRADING, authTokens, casMBean.getOidcServiceCookiesDomainName(), GLOBAL_PATH, -1);
		response.addCookie(ssoDomainCookie);
	}
		

	private String setRedirectionCookieFor(Organization org, HttpServletResponse response, boolean customerLogin) throws Exception {
		String errorMsg = null;
		if (org.getVirtualServer() != null) {
			String virtualServerName = org.getVirtualServer().getShortName();
			Cookie orgVirtualServerCookie;
			if(customerLogin){
				orgVirtualServerCookie = new Cookie("orgServiceGroup", virtualServerName);
			}else {
				orgVirtualServerCookie = new Cookie(ORG_SERVICE_GROUP + org.getShortName(), virtualServerName);
			}
			orgVirtualServerCookie.setPath("/");
			orgVirtualServerCookie.setMaxAge(-1);
			if(casMBean.getOidcServiceCookiesDomainName() != null) {
				orgVirtualServerCookie.setDomain(casMBean.getOidcServiceCookiesDomainName());
			}
			response.addCookie(orgVirtualServerCookie);
			String msg = "OIDC ResponseHandler : Sending Service Group " + virtualServerName + " for org " + org.getShortName();
			log.warn(msg);
		} else {
			errorMsg = "Organization " + org.getShortName() + " does not have a virtual server assigned to it";
			log.warn("OIDC ResponseHandler  : " + errorMsg);
		}
		return errorMsg;
	}
	

	@Override
	public void setApiVersion(String apiVersion) {
		this.apiVersion = apiVersion;
	}

	@Override
	public String getApiVersion() {
		return apiVersion;
	}

	@Override
	public CommonAuthenticationService getCas() {
		return cas;
	}

	@Override
	public void setCas(CommonAuthenticationService cas) {
		this.cas = cas;
	}

	@Override
	public String getSuccessUrl() {
		return successUrl;
	}

	@Override
	public void setSuccessUrl(String successUrl) {
		this.successUrl = successUrl;
	}

	@Override
	public String getFailureUrl() {
		return failureUrl;
	}

	@Override
	public void setFailureUrl(String failureUrl) {
		this.failureUrl = failureUrl;
	}

	@Override
	public CASMBean getCasMBean() {
		return casMBean;
	}

}
