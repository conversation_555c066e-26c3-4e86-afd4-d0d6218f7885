package com.integral.fxiapi.auth;

import com.integral.SEF.SEFUtilC;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.admin.utils.organization.SalesDealerGroupUtil;
import com.integral.broker.config.BrokerProvisionConfig;
import com.integral.broker.config.BrokerProvisionConfigFactory;
import com.integral.cas.CASClient;
import com.integral.cas.CommonAuthenticationService;
import com.integral.cas.CommonAuthenticationServiceC;
import com.integral.cas.ProvisionalTokenTypes;
import com.integral.cas.config.CASMBeanC;
import com.integral.client.util.LegalAgreementHelperC;
import com.integral.client.util.VersionCheckC;
import com.integral.finance.account.configuration.AccountManagementConfigurationFactory;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.*;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.finance.dealing.mifid.MiFIDUtils;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.order.configuration.OrderServiceMBeanC;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.fxiapi.BaseService;
import com.integral.fxiapi.FXIConstants;
import com.integral.fxiapi.Status;
import com.integral.fxiapi.auth.forcedlogout.ForceLogoutMessageGenerator;
import com.integral.fxiapi.auth.forcedlogout.ForceLogoutMessageGeneratorC;
import com.integral.fxiapi.brand.BrandData;
import com.integral.fxiapi.brand.BrandRequest;
import com.integral.fxiapi.brand.BrandService;
import com.integral.fxiapi.messages.APIStreamConfig;
import com.integral.fxiapi.model.LoginRequest;
import com.integral.fxiapi.sso.MFALoginResponse;
import com.integral.fxiapi.sso.SSOLoginRequest;
import com.integral.fxiapi.trade.CustomParameter;
import com.integral.fxiapi.trade.CustomParameters;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.email.TradeEmailMBean;
import com.integral.is.common.mbean.ClientConfMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.management.monitor.UserRuntimeMonitor;
import com.integral.jmsproxy.fxiapi.ApiHTTPRequestPacketC;
import com.integral.jmsproxy.protocol.http11.HTTPParametrizedResponseC;
import com.integral.jmsproxy.server.ConnectionManager;
import com.integral.jmsproxy.server.configuration.JMSProxyConfigurationFactory;
import com.integral.jmsproxy.server.jmsproxy.Connection;
import com.integral.jmsproxy.server.jmsproxy.ConnectionUtil;
import com.integral.jmsproxy.server.stream.Stream;
import com.integral.jmsproxy.server.stream.StreamElement;
import com.integral.jmsproxy.server.stream.StreamElementC;
import com.integral.jmsx.JMSMBeanC;
import com.integral.jmsx.JMSXMLMBeanC;
import com.integral.maker.service.PermissionConstants;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.config.MarketMakerConfigMBean;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.ExternalSystemId;
import com.integral.security.*;
import com.integral.security.jwt.PaymentsPermissionsSupplier;
import com.integral.security.util.AuthenticatorUtilC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.UserService;
import com.integral.sso.SSOMBeanC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.mail.SendEmailC;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.system.server.VirtualServer;
import com.integral.time.IdcDate;
import com.integral.user.*;
import com.integral.user.config.UserMBean;
import com.integral.util.IdcUtilC;
import com.integral.util.MathUtilC;
import com.integral.util.Tuple;
import com.integral.xml.mapping.XMLException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> Development Corporation.
 */

@Service
public class LoginService extends BaseService
{

    public static final String WEB_APP_ACCESS = "WebAppAccess";
    public static final String FXI_MOBILE_APP_ACCESS = "FXIMobileAppAccess";


    @Autowired
    BrandService brandService;

    protected static ConnectionManager connectionManager = ConnectionUtil.getInstance().getConnectionManager();
    /*TODO: Commented code for refactoring authentication and service init
    protected static CommonAuthenticationServiceC casService = CommonAuthenticationServiceC.getInstance();
    protected final static String ORG_LOGIN_DISABLED = "Idc.Org.Logins.Disabled";
    protected final static String INVALID_IP = "user.login.invalidIP";
    protected String API_CONTEXT_URI = "/fxiapi";*/

    private final ForceLogoutMessageGenerator forceLogoutMessageGenerator = new ForceLogoutMessageGeneratorC();

    private static final TradeConfigurationMBean tradeConfig = TradeConfigurationFactory.getTradeConfigurationMBean ();
    private static final ClientConfMBean clientconfMBean = ISFactory.getInstance().getClientConfMBean();
    private static final OrderConfigurationMBean orderConfigurationMBean = OrderConfiguration.getInstance();
    private static final ISMBean isMbean  = ISFactory.getInstance().getISMBean();
    private static final JMSMBeanC jmsbean = JMSXMLMBeanC.getInstance();
    private static final TradeEmailMBean tradeEmailMBean = ISFactory.getInstance().getTradeEmailMBean();
    protected Map<Organization, FXRateConvention> fxRateConventionMap = new ConcurrentHashMap<Organization, FXRateConvention>();

    private static final String dateTimeFormatStr = "yyyy-MM-dd HH:mm:ss z";
    private static final char COMMA = ',';
    private static final String HEARBEAT_REQ_TIMEOUT = "HeartbeatRequestTimeout";
    private static final String LOAD_BALANCER_IP_ADDRESS_COOKIE = "loadBalancerIPAddress";

    protected static final int priority = 10;
    
    //@Autowired
    //private PortalBrandIntegrationService portalBrandService;

    public WorkflowMessage createLoginMessage( User user )
    {
        WorkflowMessage loginMsg = MessageFactory.newWorkflowMessage();
        loginMsg.setEventName( ISCommonConstants.MSG_EVENT_LOGIN );
        loginMsg.setTopic( ISCommonConstants.MSG_TOPIC_SESSION );
        loginMsg.setSender( user );
        loginMsg.setStatus( MessageStatus.SUCCESS );
        return loginMsg;
    }

    public WorkflowMessage createLogoutMessage( User user, HttpSession session, boolean userInitiatedLogout )
    {
        WorkflowMessage logoutMsg = MessageFactory.newWorkflowMessage();
        logoutMsg.setEventName( ISCommonConstants.MSG_EVENT_LOGOUT );
        if ( !userInitiatedLogout )
        {
            logoutMsg.setObject( session );
        }
        logoutMsg.setTopic( ISCommonConstants.MSG_TOPIC_SESSION );
        logoutMsg.setSender( user );
        logoutMsg.setParameterValue( ISCommonConstants.LOGOUT_REASON, UserService.LOGGED_OUT );
        logoutMsg.setStatus( MessageStatus.SUCCESS );
        return logoutMsg;
    }

    public WorkflowMessage createPasswordValidationMessage( PasswordChangeRequest resetPswdRequest, User user )
    {
        WorkflowMessage pswdValidationMsg = MessageFactory.newWorkflowMessage();
        pswdValidationMsg.setProperty( "user", user );
        pswdValidationMsg.setProperty( "userName", user.getShortName() );
        pswdValidationMsg.setProperty( "newPassword1", resetPswdRequest.getNewPassword() );
        return pswdValidationMsg;
    }

    public WorkflowMessage createResetPasswordMessage( PasswordChangeRequest resetPswdRequest, User user )
    {
        WorkflowMessage resetPswdMsg = MessageFactory.newWorkflowMessage();
        resetPswdMsg.setProperty( "user", user );
        resetPswdMsg.setProperty( "newPassword1", resetPswdRequest.getNewPassword() );
        resetPswdMsg.setProperty( "emailAddress", "" );   //this is done since null email overrides existing value to null
        resetPswdMsg.setProperty( "isForgetPasswordRequest", false );
        return resetPswdMsg;
    }

    public WorkflowMessage createForgotPasswordMessage( String newPswd, String email, User user )
    {
        WorkflowMessage forgotPswdMsg = MessageFactory.newWorkflowMessage();
        forgotPswdMsg.setProperty( "user", user );
        forgotPswdMsg.setProperty( "newPassword1", newPswd );
        forgotPswdMsg.setProperty( "emailAddress", email );
        forgotPswdMsg.setProperty( "isForgetPasswordRequest", true );
        return forgotPswdMsg;
    }

    public boolean authenticateTransparentLogin( String pass, String expected )
    {
        boolean isValid = false;
        String md5Str = CryptC.md5encrypt( expected );   //generate md5 checksum
        if ( md5Str.equals( pass ) )
        {
            isValid = true;
        }
        return isValid;
    }

    /* TODO: Commented code for refactoring authentication and service init
    public LoginResponse processLogin( LoginRequest loginData, HttpServletRequest request, HttpServletResponse response)
    {
        return performLoginValidationAndAuthentication( loginData, request, response , true);
    }


    public LoginResponse performLoginValidationAndAuthentication( LoginRequest loginData, HttpServletRequest request, HttpServletResponse response , boolean checkAuth)
    {
        LoginResponse loginResponse = null;
        try
        {
            // TODO: This validation should be done by the calling API.
            Set failures = validator.validate( loginData );
            if ( !failures.isEmpty() )
            {
                loginResponse = new LoginResponse( Status.ERROR, response );
                loginResponse.setErrorCode( validationMessages( failures ) );
                return loginResponse;
            }

            String uId = loginData.getUser() + '@' + loginData.getOrg();
            User user = UserFactory.getUser( uId );

            if ( user == null )
            {
                loginResponse = new LoginResponse( Status.ERROR );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                loginResponse.setErrorCode( "Invalid user/organization supplied" );
                log.error( "AuthController.login. user doesn't exists with id " + uId );
                return loginResponse;
            }

            log.info( new StringBuilder( 200 ).append( "AC.doLogin: Login request received for user:" ).append( uId ).append( ", clientName: " ).append( loginData.getClientName() )
                    .append( ", clientVersion" ).append( loginData.getClientVersion() ).append( ", apiVersion:" ).append( loginData.getApiVersion() ).toString());


            //password validation for static/otp/2fa
            Tuple<Boolean, String> authResult;

            if ( checkAuth )
            {
                authResult = authenticateUser( user, loginData, request );
            }
            else
            {
                log.info( "AuthController.login - Skipping authentication. Relogin request has valid logged in user." );
                authResult = new Tuple<Boolean, String>( true, "" );
            }

            if ( authResult.first == Boolean.TRUE )
            {
                //check for permission
                if ( hasLoginPermission( user, loginData.getClientName() ) )
                {
                    loginResponse = new LoginResponse( Status.OK );
                }
                else
                {
                    loginResponse = new LoginResponse( Status.ERROR );
                    loginResponse.setErrorCode( "Login Failed: User does not have permission to access application." );
                    response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                    log.error( "AuthController.login. Authentication failed for userid " + uId + " . User doesn't have Login Permission." );
                    SecurityFactory.getSecurityService().auditLoginFailed( user , true );
                    return loginResponse;
                }
            }
            else
            {
                loginResponse = new LoginResponse( Status.ERROR );
                loginResponse.setErrorCode( authResult.second );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                log.error( "AuthController.login. Authentication failed for userid " + uId );
                SecurityFactory.getSecurityService().auditLoginFailed( user , true );
                return loginResponse;
            }

            //For older clients, there will be no API version supplied in login request, set to default 1.0.
            if ( loginData.getApiVersion() == null || loginData.getApiVersion().trim().length() == 0 )
            {
                loginData.setApiVersion( UserServiceManager.DEFAULT_API_VERSION );
            }
            String error = checkAPIVersion( loginData.getApiVersion() );
            if ( error != null )
            {
                loginResponse = new LoginResponse( Status.ERROR );
                loginResponse.setErrorCode( error );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                return loginResponse;
            }

            boolean isLoginEnabled = isLoginEnabled( user );
            if ( !isLoginEnabled )
            {
                loginResponse = new LoginResponse( Status.ERROR );
                loginResponse.setErrorCode( ORG_LOGIN_DISABLED );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                return loginResponse;
            }

            boolean isClientIPAllowed = isClientIPAllowed( user, request );
            if ( !isClientIPAllowed )
            {
                loginResponse = new LoginResponse( Status.ERROR );
                loginResponse.setErrorCode( INVALID_IP );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                return loginResponse;
            }

            //login to user service manager to allow force logout from admin
            //TODO SSO: Revisit this for SSO, if on CAS server same user re-logsin the previous user should be logged out??
            HttpSession httpSession = request.getSession( false );
            if ( httpSession == null )
            {
                httpSession = request.getSession( true );
            }

            String webapp = API_CONTEXT_URI;
            UserServiceManager.getInstance().setSingleLogin( webapp, true );

            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext( user );
            httpSession.setAttribute( IdcSessionContext.USER_KEY, user );
            httpSession.setAttribute( IdcSessionContext.USER_NAME, user.getShortName() );
            httpSession.setAttribute( IdcSessionContext.SESSION_CONTEXT, ctxt );
            httpSession.setAttribute( "authController", this );
            IdcUserSession userSession = IdcUserSessionManagerC.getInstance().getUserSession( httpSession );

            //this takes care of logging out this user across different webapps with single-login-criteria
            //TODO SSO: Revisit the right place to do user service registration. It should be the CAS server where all logged-in users in system should be registered.
            UserServiceManager.getInstance().addUserSession( webapp, user, userSession, true );

            //If due to previous user logout session was invalidated - create a new session
            if ( request.getSession( false ) == null )
            {
                httpSession = request.getSession( true );
            }


            //.NET client specific code - verifies version and sets additional required information
            if ( loginData.getLoginParams() != null && loginData.getClientName() != null && FXIConstants.FXINSIDE_CLIENT.equals( loginData.getClientName() ) )
            {
                loginResponse = checkVersion( user, loginData);
                if ( loginResponse.getStatus() == Status.ERROR )
                {
                    response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                    return loginResponse;
                }
            }
            setAdditionalLoginInfo( user, loginResponse );

            if ( loginData.getLoginParams() != null )
            {
                extractLoginDetails( user, loginData, request );
            }

            String authToken = casService.generateAuthToken( uId, request );
            userServiceManager.addUserInfo( user, authToken, loginData.getChannel(), loginData.getApiVersion(), loginData.isFixedFormatting() );
            httpSession.setAttribute( "apiVersion", loginData.getApiVersion() );
            // TODO: This should only set the AUTH_TOEKN cookie
            // createAndAddCookiesToResponse( response, sessionId, authToken );
            //loginResponse.setAsynChannel( jmsproxyResponse.getParameterValue( "protocol" ) );
            log.warn( "AuthController.login. done successfully for " + uId );


            //Should be done on service login workflow
            //setSessionContextOnLogon( user, loginData.getClientName() );

            //this is added since UMO requires last login date to be persisted for user
            FixUtilC.getInstance().updateUserOnLogin( user );

            //set required properties into login response
            setLoginProperties( user, loginData, loginResponse );
            SecurityFactory.getSecurityService().auditLoginSuccess( user , true );
        }
        catch ( Exception e )
        {
            log.error( "AuthController.login. Exception during login for user: " + loginData.getUser() + " ,org: " + loginData.getOrg(), e );
            if ( response != null )
            {
                response.setStatus( HttpServletResponse.SC_INTERNAL_SERVER_ERROR );
            }
        }

        return loginResponse;
    }*/

    public Tuple<Boolean, String> authenticateUser( User user, LoginRequest loginRequest, HttpServletRequest request )
    {
         return authenticateUser( user, loginRequest.getOtp(), loginRequest.getPass(), request );
    }

    /**
     * This authenticates user credentials and supports static, OTP and 2FA authentication mechanism
     *
     * @param user         user
     * @param otp          otp
     * @param password     password
     * @param request      http request
     * @return tuple representing authentication success and error in case it failed.
     */
    public Tuple<Boolean, String> authenticateUser( User user, String otp, String password, HttpServletRequest request )
    {
        Organization org = user.getOrganization();
        String errors = null;
        updateSession( request, user );

        int authType = org.getExternalAuth();
        boolean isAuthSuccess = false;
        boolean result = false;
        boolean checkStaticPasswd = false;
        String wlUser = user.getShortName() + '@' + user.getOrganization().getNamespace().getShortName();

        if ( user.isExternalAuth() && authType > 0 )
        {

            //If user is enabled for externalAuthentication then validate on the basis of external authentication server response
            log.info( "LS.authenticateUser: User : " + user.getShortName() + " using External Authentication." );
            //check what request parameters passed
            boolean isPreAuthCheckSuccess = true;
            if ( authType == 2 )
            {
                if ( otp == null || password == null )
                {
                    log.error( "LS.authenticateUser: Client / server configuration error. Details - AuthType : " + authType + " User Enabled for OTP : " + user.isExternalAuth() + " OTP : " + otp + " password : " + password );
                    isPreAuthCheckSuccess = false;
                    errors = "ERR_2FA_CREDENTIALS_NOT_RECEIVED";
                }
            }
            else if ( authType == 1 )
            {
                if ( otp == null )
                {
                    log.error( "LS.authenticateUser: Client / server configuration error. Details - AuthType : " + authType + " User Enabled for OTP : " + user.isExternalAuth() + " OTP : " + otp + " password : " + password );
                    isPreAuthCheckSuccess = false;
                    errors = "ERR_OTP_NOT_RECEIVED";
                }
            }

            if ( isPreAuthCheckSuccess )
            {
                if(authType == 3)
                {
                    //GoogleAuthentication
                    //check if secret is associated with user
                    //if not then allow login without OTP
                    Boolean isAssigned = GoogleExternalAuthenticationServiceC.getInstance().isSecretAssociated( wlUser );
                    if(isAssigned != null && isAssigned)
                    {
                        isAuthSuccess = GoogleExternalAuthenticationServiceC.getInstance().validateOTPForUser( wlUser, otp );
                    }
                    else
                    {
                        log.warn( "LS.authenticateUser: GoogleAuth skipped OTP validation. It will always be skipped if user has not secret association. User : " + wlUser );
                        isAuthSuccess = true;
                    }

                    checkStaticPasswd = true;
                }
                else
                {

                    Tuple<String, String> authResponse = VascoExternalAuthenticationServiceC.getInstance().sendAunthenticationRequest( wlUser, otp, "", "vdsTokenBlob1" );
                    if ( VascoExternalAuthenticationConstantsC.RESPONSE_OK.equals( authResponse.first ) )
                    {
                        if ( authType == 2 )
                        {
                            checkStaticPasswd = true;
                        }
                        else
                        {
                            result = true;
                        }
                        isAuthSuccess = true;
                    }
                    else
                    {
                        log.warn( "LS.authenticateUser: Failed to validate OTP. Details: user : " + wlUser + " otp : " + otp + " response : " + authResponse.first + " Orig Response : " + authResponse.second );
                        if ( VascoExternalAuthenticationConstantsC.RESPONSE_OTP_INVALID.equals( authResponse.first ) )
                        {
                            if ( authType == 2 )
                            {
                                errors = "ERR_INVALID_OTP_FOR_2FA";
                            }
                            else if ( authType == 1 )
                            {
                                errors = "ERR_INVALID_OTP";
                            }
                        }
                        else
                        {
                            errors = "ERR_OTP_INTERNAL_SERVER_ERROR_" + authResponse.second;
                        }
                        isAuthSuccess = false;
                    }

                    //Send mail to support
                    if ( VascoExternalAuthenticationConstantsC.RESPONSE_UNKNOWN.equals( authResponse.first ) )
                    {
                        UserMBean mbean = UserFactory.getUserMBean();
                        String to = mbean.getFXIBrandContactEMail( user.getOrganization().getAdminBrandName() );
                        if ( to == null )
                        {
                            to = mbean.getFXIContactEMail();
                        }

                        if ( to == null )
                        {
                            log.info( "LS.authenticateUser - Unable to send mail. Mail TO address is not configured." );
                        }
                        else
                        {
                            log.info( "LS.authenticateUser - Sending mail to : " + to );
                            try
                            {
                                SendEmailC.sendEmail( to, mbean.getExternalAuthFailureEmailSubject( authResponse.second ), mbean.getExternalAuthFailureEmailContent( authResponse.second, wlUser ) );
                            }
                            catch ( Exception e )
                            {
                                log.error( "LS.authenticateUser: Error in sending email for user: " + user.getFullyQualifiedName() );
                            }
                        }
                    }
                }

            }
            else    //PreAuthCheck failed
            {
                return new Tuple<Boolean, String>( result, errors );
            }

        }
        //this mode only static passwords are accepted
        else
        {
            if ( otp == null )
            {
                isAuthSuccess = true;
                checkStaticPasswd = true;
            }
            else
            {
                log.warn( "LS.authenticateUser : Client / server configuration error. Details - AuthType : " + authType + " User Enabled for OTP : " + user.isExternalAuth() + " OTP : " + otp + " password : " + password );
                errors = "ERR_OTP_DISABLED";
            }
        }


        if ( isAuthSuccess && checkStaticPasswd )
        {
            boolean pwdVerificationFailed = false;

            //check if user allowed to login through this path
            Organization brokerOrg = org.getBrokerOrganization();
            boolean isLoginAllowed = true;

            if(brokerOrg != null)
            {
                isLoginAllowed = !(CASMBeanC.getInstance().isOnlySAMLLoginAllowed(brokerOrg.getShortName() , org.getShortName() , user.getShortName())||
                        CASMBeanC.getInstance().isOnlyOIDCLoginAllowedForBrokerCustomer(brokerOrg.getShortName() , org.getShortName() , user.getShortName()));
            }else{
                isLoginAllowed = !CASMBeanC.getInstance().isOnlyOIDCLoginAllowed(org.getShortName() , user.getShortName());
            }

            if(isLoginAllowed)
            {
                if ( password == null || password.trim().isEmpty() )
                {
                    pwdVerificationFailed = true;
                }
                else
                {
                    //initialize result based on result of verifyPassword
                    try
                    {
                        user.verifyMD5Password( password );
                        result = true;
                    }
                    catch ( UserAuthenticationException ue )
                    {
                        pwdVerificationFailed = true;
                    }
                }

                if ( pwdVerificationFailed )
                {
                    result = false;
                    errors = "Invalid password";
                    log.warn( "LS.authenticateUser. login authentication failed for " + user.getFullyQualifiedName() );
                    SecurityServiceC.getInstance().handlerAuthenticatioFailed( user );
                }
            }
            else
            {
                result = false;
                errors = "SAML SSO Logins only allowed";
                log.warn( "LS.authenticateUser - User " + user.getFullName() + " only allowed to login through SAML protocol." );
                SecurityServiceC.getInstance().handlerAuthenticatioFailed( user );
            }
        }

        if ( result )
        {
            log.info( new StringBuilder( 350 ).append( "LS.authenticateUser: login succeeded for user " ).append( wlUser ).toString() );
        }
        else
        {
            log.warn( new StringBuilder( 350 ).append( "LS.authenticateUser: login failed for user " ).append( wlUser ).toString() );
        }

        //After a successful login attempt check if status is password change then send passwordChange status
        // else if account is disabled then send error to user

        //Password validation check is not required in case of OTP
        boolean isPasswordValidationCheckReq = !( user.isExternalAuth() && user.getOrganization().getExternalAuth() == 1 );
        if ( isPasswordValidationCheckReq && result )
        {
            if ( User.ACCOUNT_STATUS_PASSWORD_MUST_CHANGE_AT_NEXT_LOGON == user.getAccountStatus() )
            {
            	log.info( "LS.authenticateUser: Password needs to be changed for user: " + user.getShortName() );
                // check if max number of forget request exceeded 
                UserMBean userMbean = UserFactory.getUserMBean();
                long passwordExpireTime = -1;
                if (userMbean.sourceUserPasswordPolicyFromBrand()) {
                    String brandName = user.getOrganization().getShortName();
                    Organization brokerOrg = user.getOrganization().getBrokerOrganization();
                    if (brokerOrg != null) {
                    	brandName = brokerOrg.getShortName();
                    }
                    log.info("LS.authenticateUser:BrandName:" + brandName);
                    //long passwordExpireTime = 5*60*1000;
                    /*
                    try {
                    	ApplicationBrandInfo appBrandInfo = portalBrandService.getApplicationByBrandAndAppName(brandName, "PasswordPolicy");
                 		if (appBrandInfo != null) {
                 			Map<String, Object> appSettings = appBrandInfo.getAppSettings();
                 			if (appSettings != null) {
                 				Object passwordExpireTimeObj = appSettings.get("passwordExpiryTime");
                 				if (passwordExpireTimeObj instanceof Long) {
                 					passwordExpireTime = (Long)passwordExpireTimeObj;
                 				} else if (passwordExpireTimeObj instanceof String) {
                 					try {
                 						passwordExpireTime = Long.parseLong((String)passwordExpireTimeObj);
                 					} catch (Throwable t) {
                 						passwordExpireTime = -1;
                 					}
                 				}
                 			}
                 		}
                    	
                    } catch (Throwable t) {
                    	log.error("LS.authenticateUser:Problem with getting BrandInfo from PortalBrandService.", t);
                    }
                    */
                } else{
                	passwordExpireTime = userMbean.getNewPasswordExpiryTime(user);
                }

                log.info("LS.authenticateUser:passwordExpiryTime in milis:" + passwordExpireTime);
                UserSession us = UserSessionQueryService.getUserSession(user);
                if (us != null && passwordExpireTime != -1) {
                	// check automated password expiry time only if it's automatically generated and supposed to be changed on next logon
                	String pfrtsStr = us.getPasswordForgotRequestTimes();
                	//log.info("LS.authenticateUser:pfrtsStr:" + pfrtsStr);
                	Long lastForgotPassowrdRequestTime = getLastEventTime(pfrtsStr);
                	log.info("LS.authenticateUser:Last forgot password event time:" + lastForgotPassowrdRequestTime);
                	if (lastForgotPassowrdRequestTime != null && (System.currentTimeMillis() - lastForgotPassowrdRequestTime) > passwordExpireTime) {
                		//SecurityFactory.getSecurityService().handlePasswordResetRequestFailed(user); 
                        String subject = userMbean.getUserResetPasswordEmailSubject(user);
                        String fromEmailAddress = userMbean.getUserResetPasswordFromEmailId(user);
                        String emailContent = userMbean.getUserPasswordExpireContent(user);
                		PasswordValidation.sendEmail(user, fromEmailAddress, subject, emailContent);
                		result = false;
                		//errors = SecurityServiceC.PASSWORD_EXPIRED;
                		errors = FXIConstants.ERROR_CODE_PASSWORD_EXPIRED;
                        log.warn("LS.authenticateUser. User password expired. for user with id: " + user);
                	}
                }
            }
            else if ( User.ACCOUNT_STATUS_ACCOUNT_DISABLED == user.getAccountStatus() ) //this may not happen, as user authentication will fail for disabled user
            {
                log.info( "LS.authenticateUser: Password has been disabled for user: " + user.getShortName() );
                result = false;
                errors = SecurityServiceC.PASSWORD_FAILED_ATTEMPTS_EXCEEDED_ERROR;
            }
        }

        // if the user is disabled already, password validation would fail and in that case, set the error code.
        if ( !result && User.ACCOUNT_STATUS_ACCOUNT_DISABLED == user.getAccountStatus() )
        {
            log.info( "LS.authenticateUser: Password has been disabled for user: " + user.getShortName() );
            errors = SecurityServiceC.PASSWORD_FAILED_ATTEMPTS_EXCEEDED_ERROR;
        }

        return new Tuple<Boolean, String>( result, errors );
    }
    
    private Long getLastEventTime(final String eventTimes) {
    	if (eventTimes == null || "".equals(eventTimes.trim())) {
    		return null;
    	}
    	String[] splits = eventTimes.split(",");
    	String lastEventTimeStr = splits[splits.length -1];
    	if (lastEventTimeStr != null) {
    		lastEventTimeStr = lastEventTimeStr.trim();
    	}
    	Long lastEventTime = null;
    	try {
    		lastEventTime = Long.parseLong(lastEventTimeStr);
    	} catch (Throwable t) {
    		lastEventTime = null;
    	}
    	
		return lastEventTime;
    }

    protected int getMaxAllowedFailedAttempts()
    {
        return UserFactory.getUserMBean().getMaxUserLoginPasswordFailedAttempts();
    }

    protected void updateSession( HttpServletRequest request, User user )
    {
        HttpSession session = request.getSession( true );
        if ( !session.isNew() )
        {
            session.invalidate();
        }
        session = request.getSession( true );
    }


    public void setLoginProperties( User user, LoginRequest loginData, LoginResponse loginResponse )
    {
        BrandRequest brandRequest = new BrandRequest();
        brandRequest.setClientType( loginData.getClientType() );
        brandRequest.setClientVersion( loginData.getClientVersion() );
        BrandData brandData = brandService.getBrandData( user, user.getOrganization(), brandRequest );
        loginResponse.setBrandData( brandData );
        loginResponse.setVirtualServer( ConfigurationFactory.getServerMBean().getVirtualServerName() );
        loginResponse.setPermissions( permissionsMBean.getUserPermissions( user ) );

        if ( User.ACCOUNT_STATUS_PASSWORD_MUST_CHANGE_AT_NEXT_LOGON == user.getAccountStatus() )
        {
            loginResponse.setChangePassword( true );
        }
        else
        {
            loginResponse.setChangePassword( false );
        }

        //specific to google auth
        if(user.getOrganization().getExternalAuth() == 3 && user.isExternalAuth())
        {
            Boolean isAssigned = GoogleExternalAuthenticationServiceC.getInstance().isSecretAssociated( user.getFullyQualifiedName() );
            loginResponse.setGenerateSecret( isAssigned != null && !isAssigned );
        }

        loginResponse.setLegalAgreementStatus( LegalAgreementHelperC.getInstance().getLegalAgreementStatus( user ) );
        loginResponse.setTenors( clientconfMBean.getSupportedTenors() );
        loginResponse.setTenorSuffix( clientconfMBean.getSupportedTenorSuffixes());
        loginResponse.setDefaultTwapSize(Double.toString(orderConfigurationMBean.getTwapRegularSize(user.getOrganization().getShortName())));

        loginResponse.setClientSideTrdBlotter( apimBean.isTradeBlotterClientSide() );
        //it should be called after request service.process(LOGIN) call is completed.
        loginResponse.additionalInfo.setExecutionEnabled( VersionCheckC.getExecutionEnabledOrgs( user ) );

        // Set these additional fields for HTML client so that they can access admin/mis urls.
        SSOMBeanC ssoMbean = SSOMBeanC.getInstance();
        loginResponse.additionalInfo.setEncVsName(ssoMbean.getEncryptedVSName());
        loginResponse.additionalInfo.setEncOrgName(user.getOrganization().getEncryptedObjectID());
        String userOrg =  user.getOrganization().getShortName();

        String adminUrl = ssoMbean.getHtmlClientAdminServerURL(userOrg);
        if (StringUtils.isBlank(adminUrl)) {
            adminUrl = ssoMbean.getAdminServerURL();
        }
        loginResponse.additionalInfo.setAdminUrl(adminUrl);

        String misUrl = ssoMbean.getHtmlClientMISServerURL(userOrg);
        if (StringUtils.isBlank(misUrl)) {
            misUrl = ssoMbean.getMISPortalServerURL();
        }
        loginResponse.additionalInfo.setMisUrl(misUrl);
        loginResponse.additionalInfo.setHourglass(checkHourglassProperty(user.getOrganization()));
        loginResponse.additionalInfo.setBamlVenueName(clientconfMBean.getBamlVenueName(user.getOrganization()));
        loginResponse.additionalInfo.setMarketDepthMaxTiers(clientconfMBean.getMarketDepthMaxTiers(user.getOrganization()));
        loginResponse.additionalInfo.setFullBookMaxTiers(clientconfMBean.getFullBookMaxTiers(user.getOrganization()));

        // set credit status, type properties if broker for customer is set.
        final Organization brokerOrg = user.getOrganization ().getBrokerOrganization ();
        if ( brokerOrg != null )
        {
            LegalEntity userOrgDefaultLe = user.getOrganization ().getDefaultDealingEntity ();
            TradingParty tpForUserOrgDefaultLe;
            if ( userOrgDefaultLe != null && ( tpForUserOrgDefaultLe = userOrgDefaultLe.getTradingParty ( brokerOrg ) ) != null )
            {
                loginResponse.additionalInfo.setCreditEnabled ( CreditUtilC.isCreditActive ( brokerOrg, tpForUserOrgDefaultLe ) );
                loginResponse.additionalInfo.setCreditType ( CreditUtilC.getCreditType ( brokerOrg, tpForUserOrgDefaultLe ) );
            }
            else
            {
                log.info ( "LS.setLoginProperties : default legal entity is null or trading party is null. user=" + userOrg + ",brokerOrg=" + brokerOrg + ",defaultLE=" + userOrgDefaultLe );
            }
        }
    }

    private boolean checkHourglassProperty(Organization org){
        Organization broker = org.getBrokerOrganization();
        if(broker == null) return false;
        BrokerProvisionConfig brokerAdaptorMBean = BrokerProvisionConfigFactory.getBrokerProvisionConfig();
        return brokerAdaptorMBean.isHourglassPricingEnabled(broker.getShortName());
    }

    /**
     * Add force logout message to user's stream - this is done to reduce jms hop via user's temporary queue and add to user's jmsproxy stream directly
     * only .NET client uses this message.
     *
     * @param user user
     */
    //todo: custom api-based json force-logout control msg can be added once old clients are migrated to use new message  UserServiceManager.getInstance().getApiVersion( user );
    public void addForceLogoutMessage( User user )
    {
        Connection connection = apiUtil.getConnectionCache().getConnection( user );
        if ( connection != null )
        {
            Stream stream = connection.getStream();
            if ( stream != null )
            {
                WorkflowMessage wfMsg = MessageFactory.newWorkflowMessage();
                wfMsg.setTopic( ISConstantsC.MSG_TOPIC_SESSION );
                wfMsg.setEvent( ISConstantsC.MSG_EVT_LOGOUT );
                wfMsg.setStatus( MessageStatus.SUCCESS );
                wfMsg.setSender( user );

                String strMessage = forceLogoutMessageGenerator.getForcedLogoutMessage(user, wfMsg);
                Map map = new HashMap();
                map.put( "UserName", user.getShortName() );

                String key = forceLogoutMessageGenerator.getForcedLogoutMessageKey(user);
                boolean isAckNeeded = forceLogoutMessageGenerator.isAckNeededForForcedLogout(user);
                String handlerID = forceLogoutMessageGenerator.getHandlerIDForForcedLogout(user);
                Map data = new HashMap();
                data.put( "data", strMessage.replaceAll( "\"", "'" ).replaceAll( "\n", "" ) );
                StreamElement se = new StreamElementC( key, data, priority, map, isAckNeeded, handlerID, 0, 0, 0, StreamElement.STRING_MSG_TYPE, null, -1, -1 );
                stream.add( se );
            }
        }
        else
        {
            log.info( "LS.addForceLogoutMessage: Connection null for user: " + user );
        }
    }

    private String getMessage( com.integral.message.Message message )
    {
        StringWriter sw = new StringWriter();
        try
        {
            ISUtilImpl.xmlConvertor.convertToXML( sw, message, "IntegrationServer" );
        }
        catch ( XMLException e )
        {
            log.error( "LS:getMessage XMLException ", e );
        }
        return sw.toString();
    }

    /**
     * This handles the case where old user session needs to be logged out due to duplicate login. It relies on force logout msg.
     * Adds a delay of configured interval and periodically checks whether message with highest priority exists in user's stream.
     * Waits for maximum configured time and prints a warning if message was still not delivered.
     *
     * @param user user
     * @param oldUserConnection connection
     */
    public void addDelayToDeliverForceLogoutMsg( User user, Connection oldUserConnection )
    {
        //if way to find if previous user was logged into .NET client only then it is important as only it relies on force-logout message (use login-channel?).
        boolean msgDelivered = false;
        long forceLogoutTotalWaitTime = apimBean.getForceLogoutTotalWaitTimeInMills();
        long forceLogoutWaitTimeUnit = apimBean.getForceLogoutWaitTimeUnitInMills();
        if ( log.isDebugEnabled() )
        {
            log.debug( "LS.addDelayToDeliverForceLogoutMsg: forceLogoutTotalWaitTime configured: " + forceLogoutTotalWaitTime + " ,forceLogoutWaitTimeUnit: " + forceLogoutWaitTimeUnit );
        }

        long totalTimeWaited = 0;
        try
        {
            while ( forceLogoutTotalWaitTime > 0 )
            {
                if ( oldUserConnection.getStream().getMsgSizeWithMaxPriority() == 0 )
                {
                    msgDelivered = true;
                    break;
                }
                Thread.sleep( forceLogoutWaitTimeUnit );
                totalTimeWaited += forceLogoutWaitTimeUnit;
                forceLogoutTotalWaitTime = forceLogoutTotalWaitTime - forceLogoutWaitTimeUnit;
            }
        }
        catch ( Exception e )
        {
            log.error( "LS.addDelayToDeliverForceLogoutMsg. Error while wait: ", e );
        }

        if ( totalTimeWaited > 0 )
        {
            log.info( "LS.addDelayToDeliverForceLogoutMsg: Total wait time(ms) for force logout msg delivery: " + totalTimeWaited );
        }
        if ( !msgDelivered )
        {
            log.warn( "LS.addDelayToDeliverForceLogoutMsg: Wait time elapsed but msg not delivered to user. User: " + user );
        }

    }

    /**
     * Verifies whether the supplied version is correct client and brand version
     *
     * @param user         user
     * @param loginRequest login request
     * @return login response
     */
    public String checkVersion( User user, LoginRequest loginRequest )
    {
        String error = null;
        String clientName = loginRequest.getClientName();
        String clientVersion = loginRequest.getClientVersion();

        if ( !isValidClientVersion( user, clientName, clientVersion ) )
        {
            log.warn( "LS.checkVersion UNSUPPORTED_CLIENT_VERSION " + clientVersion );
            return "UNSUPPORTED_CLIENT_VERSION";
        }
        return error;
    }

    /**
     * Validates whether login request has valid Grid API version.
     *
     * @param apiVersion api Version
     * @return login response
     */
    public String checkAPIVersion( String apiVersion )
    {
        String errorCode = null;
        String supportedAPIVersionList = apimBean.getSupportedAPIVersions();
        if ( supportedAPIVersionList != null )
        {
            boolean isValid = ( supportedAPIVersionList.indexOf( apiVersion.trim() ) >= 0 );
            if ( !isValid )
            {
                log.warn( "LS.checkAPIVersion: Unsupported API version: " + apiVersion );
                errorCode = "UNSUPPORTED_API_VERSION";
            }
        }
        else
        {
            log.warn( "LS.checkAPIVersion: Null supported list. Unsupported API version: " + apiVersion );
            errorCode = "UNSUPPORTED_API_VERSION";
        }

        return errorCode;
    }

    /**
     * Validates whether login is enabled for given organization.
     *
     * @param user user
     * @return boolean indicating whether login is enabled.
     */
    public boolean isLoginEnabled( User user )
    {
        ServerRuntimeMBean sMbean = RuntimeFactory.getServerRuntimeMBean();
        boolean isLoginEnabled = true;
        try
        {
            if ( !sMbean.isLoginsEnabled() )
            {
                log.info( "LS.isLoginEnabled : Login disallowed as system is set in admin only mode. user=" + user );
                isLoginEnabled = false;
            }
            if ( !sMbean.getDatabaseState() )
            {
                log.info( "LS.isLoginEnabled : Login disallowed as database is down, user=" + user );
                isLoginEnabled = false;
            }
            if ( !LoginMBeanC.getInstance().isUserOrganizationLoginEnabled( user ) )
            {
                log.warn( "LS.isLoginEnabled : Login is disallowed in this server for the user organization. user=" + user );
                isLoginEnabled = false;
            }
        }
        catch ( IllegalArgumentException e )
        {
            log.error( "LS.isLoginEnabled : Exception while checking the org level login restrictions for the user=" + user );
        }

        return isLoginEnabled;
    }

    /**
     * Validates whether client ip is allowed for given request.
     *
     * @param user    user
     * @param request http request
     * @return boolean indicating whether login is enabled.
     */
    public boolean isClientIPAllowed( User user, HttpServletRequest request )
    {
        boolean isClientIPAllowed = true;
        try
        {
            String clientIP = request.getHeader( "X-Forwarded-For" );
            if ( ( clientIP != null ) && ( clientIP.length() > 1 ) )
            {
                final String xForwardHeader = clientIP;
                if ( clientIP.contains( "," ) )
                {
                    clientIP = clientIP.substring( 0, clientIP.indexOf( "," ) );
                    log.info( new StringBuilder( 200 ).append( "LS.isClientIPAllowed : X-Forwarded-For header value=" )
                            .append( xForwardHeader ).append( ",extractedClientIp=" ).append( clientIP ).append( ",remoteAddress=" )
                            .append( request.getRemoteAddr() ).toString() );
                    clientIP = clientIP != null && clientIP.trim().length() > 0 ? clientIP.trim() : request.getRemoteAddr();
                }
                else
                {
                    clientIP = clientIP.trim();
                }
            }
            else
            {
                clientIP = request.getRemoteAddr();
            }

            if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( user, clientIP ) )
            {
                String brokerStr = "";
                Organization brokerOrg = user.getOrganization ().getBrokerOrganization ();
                if ( brokerOrg != null )
                {
                    brokerStr = " / Idc.BrokerCustomer.Org.RestrictIPMask." + brokerOrg.getShortName () + " : "
                            + LoginMBeanC.getInstance().getBrokerOrgIPRestrictMaskEnabled ( brokerOrg );
                }
                log.warn( "LS.isClientIPAllowed: Login FAILED. User : " + user.getFullName() + " tried to login from IP : "
                        + clientIP + ", is not allowed based on loginIP restriction rules mentioned in properties Idc.Org.RestrictIPMask : "
                        + LoginMBeanC.getInstance().getRestrictedIPMask() + " OR Idc.Org." + user.getOrganization().getShortName()
                        + ".RestrictIPMask : " + LoginMBeanC.getInstance().getRestrictIPMaskForOrg( user.getOrganization() ) + brokerStr );
                isClientIPAllowed = false;
            }
            if ( log.isDebugEnabled() )
            {
                Enumeration headers = request.getHeaders( "X-Forwarded-For" );
                if ( headers != null )
                {
                    while ( headers.hasMoreElements() )
                    {
                        log.debug( new StringBuilder( 200 ).append( "LS.isClientIPAllowed : X-Forwarded-For header value=" ).append( headers.nextElement() ).toString() );
                    }
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            log.error( "LS.isClientIPAllowed. Error: LoginIP Masking rules specified in Idc.Org.RestrictIPMask : " + LoginMBeanC.getInstance().getRestrictedIPMask() + " OR Idc.Org." + user.getOrganization().getShortName() + ".RestrictIPMask : " + LoginMBeanC.getInstance().getRestrictIPMaskForOrg( user.getOrganization() ) + " are not properly specified. Ignoring restricted IP masking for this Org.", e );
        }
        return isClientIPAllowed;
    }


    private boolean isValidClientVersion( User user, String clientName, String currentClientVersion )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "LS.isValidClientVersion clientName = " + clientName + " currentClientVersion = " + currentClientVersion );
        }
        if ( FXIConstants.FXINSIDE_CLIENT.equals( clientName ) )
        {
            String supportedClientVersionList = clientconfMBean.getSupportedFXIVersions( user );
            return ( supportedClientVersionList.indexOf( currentClientVersion.trim() ) >= 0 );
        }
        return false;
    }

    private String getBrand( User user )
    {
        String brand = user.getOrganization().getAdminBrandName();
        return FXIConstants.MAIN.equals( brand ) ? null : brand;
    }

    /**
     * This API is invoked prior to calling login API in CAS server (pre-login API).
     * loadBalancerIPAddress cookie is extracted from the request and is set in response to track the singleURL login through A10.
     *
     * @param request http request
     * @param response http response
     */
    public void setSingleURLTrackCookie( HttpServletRequest request, HttpServletResponse response )
    {
    	try
    	{
        	Cookie[] allCookies = request.getCookies();
        	boolean cookieFound = false;

            	if( allCookies != null )
            	{
            		for( Cookie cookie : allCookies )
            		{
            			if( LOAD_BALANCER_IP_ADDRESS_COOKIE.equals( cookie.getName() ) )
            			{
                            String loadBalancerIPAddress = cookie.getValue();
                            Cookie loadBalancerIPAddressCookie = new Cookie( LOAD_BALANCER_IP_ADDRESS_COOKIE, loadBalancerIPAddress );
                            loadBalancerIPAddressCookie.setPath( "/" );
                            loadBalancerIPAddressCookie.setMaxAge( -1 );
                            response.addCookie( loadBalancerIPAddressCookie );
            				cookieFound = true;
            				log.info("Setting SingleURLTrack cookie in response with IP="+cookie.getValue());
            				break;
            			}
            		}
            	}


        	if( !cookieFound )
        	{
        		log.warn("SingleURLTrack cookie NOT found in login request ");

        	}
    	}
    	catch (Exception exp )
    	{
    		log.error("Exception occured while setting SingleURLTrack cookie during login request in CAS");
    	}

    }

    /**
     * This API is invoked during initialize login API through dealing service (initialize login request).
     * loadBalancerIPAddress cookie is extracted from the request and is set in user property to track the singleURL login through A10.
     *
     * @param user  User
     * @param request http request
     * @param response http response
     */
    public void setSingleURLTrackCookie( User user, HttpServletRequest request, HttpServletResponse response )
    {
    	try
    	{
        	Cookie[] allCookies = request.getCookies();
        	boolean cookieFound = false;
        	if( allCookies != null )
        	{
        		user.removeProperty( LOAD_BALANCER_IP_ADDRESS_COOKIE );
        		for( Cookie cookie : allCookies )
        		{
        			if( LOAD_BALANCER_IP_ADDRESS_COOKIE.equals( cookie.getName() ) )
        			{
        				String loadBalancerIPAddress = cookie.getValue();
        				user.setProperty( LOAD_BALANCER_IP_ADDRESS_COOKIE, loadBalancerIPAddress );

        				Cookie loadBalancerIPAddressCookie = new Cookie( LOAD_BALANCER_IP_ADDRESS_COOKIE, loadBalancerIPAddress );
                        loadBalancerIPAddressCookie.setPath( "/" );
                        loadBalancerIPAddressCookie.setMaxAge( 0 );
                        response.addCookie( loadBalancerIPAddressCookie );

        				log.info("Expiring singleURL cookie and setting the value in user session with IP="+loadBalancerIPAddress);
        				cookieFound = true;
        				break;
        			}
        		}
        	}

        	if( !cookieFound )
        	{
        		log.warn("SingleURLTrack cookie NOT found in dealing service initialize request");

        	}
    	}
    	catch( Exception exp )
    	{
    		log.error("Exception occured while setting SingleURLTrack cookie during initialize request");
    	}


    }

    /**
     * extract login information sent by the client to publish it to GM
     *
     * @param user         user
     * @param loginRequest login request
     * @param request      http request
     */
    public void extractLoginDetails( User user, LoginRequest loginRequest, HttpServletRequest request, HttpServletResponse response )
    {

        LoginRequest.LoginParams loginParams = loginRequest.getLoginParams();

        String protocol = request.isSecure() ? "HTTPS" : "HTTP";
        String ipAddress;
		String fwdHost = request.getHeader("X-Forwarded-For");
		String cfFwdHost = request.getHeader("CF-Connecting-IP"); // CloudFlare original customer IP.
        if( cfFwdHost != null && (cfFwdHost.length() > 1)) {
            fwdHost = cfFwdHost;
        }
		if ((fwdHost != null) && (fwdHost.length() > 1)) {
			ipAddress = fwdHost;
		} else {
			ipAddress = request.getRemoteAddr();
		}

        String clientVersion = loginRequest.getClientVersion();
        String clientBrandVersion = loginParams.getBrandVersion();
        String clientBrand = loginParams.getBrand();
        String clientName = loginRequest.getClientName();

        setSingleURLTrackCookie(user, request, response);

        // sdkVersion - not required

        log.info( new StringBuilder( 200 ).append( "LS.extractLoginDetails: " ).append( user )
        		.append(", GUID[").append(user.getGUID()).append("]")
                .append( ", protocol[" ).append( protocol ).append( "], ipAddress[" ).append( ipAddress )
                .append( "], clientVersion[" ).append( clientVersion ).append( "], clientName[" ).append( clientName )
                .append( "]" ).toString() );

        String brandName = getBrand( user );
        user.setProperty( "protocol", protocol );

        if ( ipAddress != null )
        {
            user.setProperty( "ipAddress", ipAddress );
        }

        if ( clientVersion != null )
        {
            user.setProperty( "clientVersion", clientVersion );
        }
       /* if ( brandName != null )
        {
            user.setProperty( ISCommonConstants.BrandName, brandName );
            if ( clientBrandVersion != null )
            {
                user.setProperty( ISCommonConstants.BrandVersion, clientBrandVersion );
            }
        }*/

        if(clientBrand != null)
        {
            user.setProperty( ISCommonConstants.BrandName, clientBrand );
        }

        if ( clientBrandVersion != null )
        {
            user.setProperty( ISCommonConstants.BrandVersion, clientBrandVersion );
        }

        if ( clientName != null )
        {
            user.setProperty( "clientName", clientName );
        }

        Object cpuType = loginParams.getCpuType();
        Object cpuNumber = loginParams.getCpuNumber();
        Object cpuSpeed = loginParams.getCpuSpeed();
        Object memory = loginParams.getMemory();

        if ( cpuType != null )
        {
            user.setProperty( "cpuType", cpuType );
        }
        if ( cpuNumber != null )
        {
            user.setProperty( "cpuNumber", cpuNumber );
        }
        if ( cpuSpeed != null )
        {
            user.setProperty( "cpuSpeed", cpuSpeed );
        }
        if ( memory != null )
        {
            user.setProperty( "memory", memory );
        }

        //set that the user is FXI Client user  session
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( user );
        ctx.setAttribute( ISConstantsC.CTX_ATTR_CLIENT_VERSION, clientVersion );
        ctx.setAttribute( ISConstantsC.CTX_ATTR_CLIENT_NAME, clientName );
        ctx.setAttribute( ISConstantsC.CLIENT_CONNECTION_TYPE, protocol );
        ctx.setAttribute( ISCommonConstants.CTX_ATTR_CLIENT_IP, ipAddress );
        ISUtilImpl isUtil = ISUtilImpl.getInstance();
        //set compaction type based on login parameters
        if ( null == clientName )
        {
            if ( "1.2.2".equals( clientVersion ) )
            {
                ctx.setAttribute( ISConstantsC.CLIENT_VERSION, ISConstantsC.API_CLIENT_VERSION_1_2_2 );
                ctx.setLoginChannel( LoginChannel.CLIENT_SDK_JAVA );
            }
        }
        else
        {
            if ( FXIConstants.FXINSIDE_CLIENT.equals( clientName ) )
            {

                if ( loginRequest.isClientMidMarkEnabled() )
                {
                    ctx.setAttribute( ISConstantsC.CLIENT_VERSION, ISConstantsC.FXI_CLIENT_VERSION_WITH_MID );
                }
                else if ( isUtil.isCurrentVersionGreaterThanEqualTo( clientVersion, "2.5" ) )
                {
                    ctx.setAttribute( ISConstantsC.CLIENT_VERSION, ISConstantsC.FXI_CLIENT_VERSION_2_5 );
                }
                else
                {
                    ctx.setAttribute( ISConstantsC.CLIENT_VERSION, ISConstantsC.FXI_CLIENT_VERSION_2_0 );
                }
                ctx.setAttribute( "isFXISession", Boolean.TRUE );

                //ctx.setAttribute( "isClientSession", Boolean.TRUE );
                //ctx.setLoginChannel( LoginChannel.FXINSIDE_DOTNET );   //set in common place for all clients
            }
        }

        log.info( "LS.extractLoginDetails  compactionType of user " + user.getShortName() + " is " + ctx.getAttribute( ISConstantsC.CLIENT_VERSION ) );

        //Should be last statement - notify GM with user details
        UserRuntimeMonitor.getInstance().notifyLogin( user );
    }

    public void setAdditionalLoginInfo( User user, LoginResponse loginResponse,String clientName )
    {
        Organization userOrg = user.getOrganization();
        LoginResponse.AdditionalLoginInfo additionalInfo = new LoginResponse.AdditionalLoginInfo();
        loginResponse.setAdditionalInfo( additionalInfo );

        String brokerURL;
        String jmsInitQueue;
        String tradingEnabled = "true";
        brokerURL = clientconfMBean.getJMSURL();
        jmsInitQueue = jmsbean.getDestination( "ISClient" ).getJmsName();
        additionalInfo.setJmsBrokerURL( brokerURL );
        additionalInfo.setJmsInitQueue( jmsInitQueue );
        additionalInfo.setConnectionStatusTopic( "IDC.IS.PROVIDER.HEARTBEAT.DESTINATION" );
        additionalInfo.setMinimumHeartbeatInterval( clientconfMBean.getMinHeartBeatIinterval() );
        additionalInfo.setHeartBeatInterval( clientconfMBean.getHeartBeatIinterval() );
        additionalInfo.setOrderMatchingInterval( clientconfMBean.getOrderMatchingInterval() );
        additionalInfo.setServerName( isMbean.getServerName() );
        additionalInfo.setMiFidEnabled(userOrg.isMiFID());
        additionalInfo.setMtfTakerOrg(MiFIDUtils.isMTFTakerOrg(userOrg));
        if ( !isTradingEnabled( user ) )
        {
            tradingEnabled = "false";
        }

        if(userOrg.getDefaultSEFDealingEntity()!=null)
        {
        	additionalInfo.setOrgSEFDefaultLegalEntity(userOrg.getDefaultSEFDealingEntity().getShortName());

        }else{

        	additionalInfo.setOrgSEFDefaultLegalEntity( "" );
        }

        addUserPermissions( user, additionalInfo );
        addUserLEInfo( additionalInfo, user );

        /* Eventually, legalEntitiesToTrade would replace legalEntitiesForTrading */
        addLegalEntitiesAvailableForTrading( additionalInfo, user );
        addLegalEntitiesToTrade( additionalInfo, user );

        additionalInfo.setUserOrgIndex( userOrg.getIndex() );

        VirtualServer vs = userOrg.getVirtualServer();
        if ( vs != null )
        {
            additionalInfo.setVirtualServer( vs.getName() );
        }
        else
        {
            additionalInfo.setVirtualServer( "" );
        }
        IdcSessionManager.getInstance().getSessionContext( user ).setAttribute( ISConstantsC.TRADING_ENABLED, tradingEnabled );
        additionalInfo.setTradingEnabled( tradingEnabled );
        additionalInfo.setShowNDFButton( ConfigurationFactory.getSefMBean().displayNDFButton( userOrg.getShortName() ) );
        additionalInfo.setIsUSRestricted( ConfigurationFactory.getSefMBean().isUSRestricted() );
        String orgModifiedTime = new SimpleDateFormat( dateTimeFormatStr ).format( new Date( System.currentTimeMillis() ) );
        additionalInfo.setProviderOrgWithOrderMatchQTS( orgModifiedTime );
        additionalInfo.setProviderOrgWithoutOrderMatchQTS( orgModifiedTime );
        additionalInfo.setSupportedCurrencyPairQTS( orgModifiedTime );
        additionalInfo.setFxRateBasisQTS( getQuoteConvetionModifiedDateTime( userOrg ) );
        additionalInfo.setStgrdPollSwitch( clientconfMBean.getStaggeredPollSwitch( user ) );
        additionalInfo.setAcptCompactLevel( clientconfMBean.getAcceptCompactionLevel() );
        additionalInfo.setQtCompactLevel( clientconfMBean.getQuoteCompactionLevel() );
        additionalInfo.setOrgNameMap( clientconfMBean.getOrgNameMapping() );
        additionalInfo.setMultitierOrgs( isMbean.getMultiTierOrgList() );
        additionalInfo.setPriceAdjustmentThreshold( clientconfMBean.getPriceAdjustmentThreshold() );

        IdcDate rollDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
        additionalInfo.setBusinessDate( rollDate.getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN ) );
        additionalInfo.setClientBasedOrderMatching( isMbean.isClientCrossingEnabled( userOrg ) );
        additionalInfo.setOrdersAdaptorURLPrefix( clientconfMBean.getOrdersAdaptorURLPrefix( userOrg.getShortName() ) );

        //RFS properties
        String oaURL = clientconfMBean.getRFSServerURLPrefix( userOrg.getShortName() );
        additionalInfo.setRfsServerURLPrefix( oaURL );
        boolean f = clientconfMBean.useESPServerIfRFSServerNotWorking( userOrg.getShortName() );
        additionalInfo.setRfsFallbackToESPServer( f );
        additionalInfo.setRfsMismatchSwapConfig( ISUtilImpl.getInstance().getRFSMismatchSwapConfiguration( userOrg ) );
        additionalInfo.setRfsExpiryTime ( ISFactory.getInstance ().getISMBean ().getRFSRequestExpiryTime ( userOrg ) );


        //Multi-fill properties
        additionalInfo.setMultiFillConfig( ISUtilImpl.getInstance().getMultiFillConfiguration( user.getOrganization() ) );
        additionalInfo.setMarketRangeOrderProviders( ISUtilImpl.getInstance().getRelatedMarketRangeOrderProviders( user.getOrganization() ) );

        addJMSProxyProperties( additionalInfo, user, clientName );
        addMarketMakerProperties( additionalInfo, user );

        //Login Failure paramaters
        additionalInfo.setDealRequestTimeout( clientconfMBean.getDealRequestTimeOut() );
        ExternalSystemId heartBeatReqTimeOutId = userOrg.getExternalSystemId( HEARBEAT_REQ_TIMEOUT );
        if ( heartBeatReqTimeOutId != null )
        {
            additionalInfo.setHeartbeatRequestTimeout( heartBeatReqTimeOutId.getName() );
        }
        else
        {
            additionalInfo.setHeartbeatRequestTimeout( clientconfMBean.getHeartBeatRequestTimeOut() );
        }
        ExternalSystemId minTimeElapsedToDetectNWFailure = userOrg.getExternalSystemId( ISConstantsC.TIME_TO_DETECT_NETWORK_FAILURE );
        if ( minTimeElapsedToDetectNWFailure != null )
        {
            additionalInfo.setMinElapsedTimeToDetectNetworkFailure( minTimeElapsedToDetectNWFailure.getName() );
        }
        else
        {
            additionalInfo.setMinElapsedTimeToDetectNetworkFailure( clientconfMBean.getMinTimeElapsedTotoDetectNWFailure() );
        }

        additionalInfo.setMissedHeartbeatsRetries( clientconfMBean.getMissedHeartBeatRetries() );
        additionalInfo.setMissedPollingRequestsRetries( clientconfMBean.getMissedPollingRetries() );
        additionalInfo.setWaitTimeToDisplayPendingAlert( clientconfMBean.getWaitTimeToDisplayPendingAlert( userOrg ) );
        additionalInfo.setEuropeContactNos( tradeEmailMBean.getEuropeContactNo() );
        additionalInfo.setNorthAmericaContactNos( tradeEmailMBean.getNorthAmericaContactNo() );

        String helpURL;
        String clientBrand = getBrand( user );

        if ( clientBrand != null )
        {
            //Brandname is already set as part of BrandData (MAIN is set as MAIN and not null) - not duplicated in additionalInfo
            additionalInfo.setContactNumber( clientconfMBean.getBrandContactNumber( clientBrand ) );
            additionalInfo.setContactEMail( clientconfMBean.getBrandContactEMail( clientBrand ) );
            String adminUrl = isMbean.getAdminPortalUrlForOrg( userOrg.getShortName() );
            if ( adminUrl == null || adminUrl.trim().equals( "" ) )
            {
                adminUrl = isMbean.getAdminPortalUrlForBrand( clientBrand );
                if ( adminUrl == null )
                {
                    adminUrl = "";
                }
            }
            additionalInfo.setAdminPortalUrl( adminUrl.trim() );
        }
        else
        {
            additionalInfo.setContactNumber( clientconfMBean.getContactNumber() );
            additionalInfo.setContactEMail( clientconfMBean.getContactEMail() );

            String adminUrl = isMbean.getAdminPortalUrlForOrg( userOrg.getShortName() );
            if ( adminUrl == null )
            {
                adminUrl = "";
            }
            additionalInfo.setAdminPortalUrl( adminUrl.trim() );
        }

        String shouldClientUpdate;
        if ( clientBrand != null )
        {
            shouldClientUpdate = clientconfMBean.getUpdateClientBrandFlag( userOrg.getShortName(), clientBrand );
        }
        else
        {
            shouldClientUpdate = clientconfMBean.getUpdateClientFlagForOrg( userOrg.getShortName() );
        }
        additionalInfo.setUpgradeAlert( shouldClientUpdate != null ? shouldClientUpdate : "0" );


        additionalInfo.setSingleLPEnabled( clientconfMBean.isSingleLPModeEnabled( user, clientBrand ) );
        if ( clientconfMBean.isFXIPrimeEnabled( user, clientBrand ) )
        {
            additionalInfo.setFXIPrimeEnabled( true );
        }
        additionalInfo.setFXINewProEnabled( clientconfMBean.isFXINewProEnabled ( user, clientBrand ) );
        additionalInfo.setForceCptyChngEnabled( clientconfMBean.isForceCptyChangeEnabled( user, clientBrand ) );
        additionalInfo.setForceCptyChangeList( clientconfMBean.getForceCptyChangeList( userOrg ) );
        helpURL = clientconfMBean.getClientHelpURL();
        additionalInfo.setClientHelpURL( helpURL );

        String clientMaxPeriod = clientconfMBean.getClientMaxPeriod( userOrg.getShortName() );
        additionalInfo.setClientMaxPeriod( clientMaxPeriod );

        additionalInfo.setShowOrderPrice( ISUtilImpl.getInstance().isShowLimitAsMatchedPrice( userOrg ) );
        if ( userOrg.getBrokerOrganization() != null )
        {
            additionalInfo.setBrokerOrg( userOrg.getBrokerOrganization().getShortName() );
        }
        else
        {
            additionalInfo.setBrokerOrg( "NULL" );
        }
        additionalInfo.setPriceTierProvider( clientconfMBean.getPriceTierProvider( userOrg ) );

        String minTradeSizeConfig = null, maxOrderSizeConfig = null;
        if ( isMbean.isLiquidityProvisioningEnabled( userOrg.getShortName() ) )
        {
            Collection<LiquidityProvision> liquidityRules = ISUtilImpl.getLiquidityProvisions( userOrg );
            if ( liquidityRules != null && !liquidityRules.isEmpty() )
            {
                LiquidityProvision defaultRule = liquidityRules.iterator().next();
                if ( defaultRule != null )
                {
                    minTradeSizeConfig = defaultRule.getMinMatchSize() != null ? defaultRule.getMinMatchSize().toString() : null;
                    maxOrderSizeConfig = defaultRule.getMaxOrderSize() != null ? defaultRule.getMaxOrderSize().toString() : null;
                }
            }
        }
        else
        {
            minTradeSizeConfig = isMbean.getMinimumTradeSizeConfigurationForOrg( userOrg );
            maxOrderSizeConfig = isMbean.getMaximumOrderSizeConfigurationForOrg( userOrg );
        }
        if ( minTradeSizeConfig != null )
        {
            additionalInfo.setMinTradeAmount( minTradeSizeConfig );
        }
        if ( maxOrderSizeConfig != null )
        {
            additionalInfo.setMaxOrderAmount( maxOrderSizeConfig );
        }
        additionalInfo.setPersistentOrdersEnabled( OrderServiceMBeanC.getInstance().isPersistentOrderEnabled() );
        additionalInfo.setCancelOnDisconnect( OrderServiceMBeanC.getInstance().isPersistentOrderCancellationOnDisconnectEnabled( userOrg ) );
        additionalInfo.setBroker( clientconfMBean.isCustomerIsBroker( userOrg ) );
        additionalInfo.setPrimeBroker( clientconfMBean.isCustomerIsPrimeBroker( userOrg ) );
        additionalInfo.setNRH2Enabled( clientconfMBean.isNRH2HandlerEnabled() );
        if ( clientconfMBean.isAdminLegalLinkEnabled(user.getOrganization()) )
        {
            additionalInfo.setLegalURL(clientconfMBean.getAdminLegalLink(user.getOrganization()));
        }
        String customersBrokerOrg = (userOrg.getBrokerOrganization() != null) ? userOrg.getBrokerOrganization().getShortName() : null;
		additionalInfo.setMetUrl(isMbean.getMETPublicURL(customersBrokerOrg) );
        additionalInfo.setMetUrlSEF( isMbean.getMETPublicURLSEF(customersBrokerOrg) );

        // For now the dotnet client does not have anything to do with FSR.
        additionalInfo.setServerMidMarkEnabled((isMbean.isMidRateSendCustomerEnabled(userOrg.getShortName())));

        additionalInfo.setServerUTCTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());

        // Adding the client session timeout information
        additionalInfo.setClientSessionTimeOut( clientconfMBean.getIdcClientSessionTimeOut(userOrg , getBrand( user )) );
        additionalInfo.setClientConfirmationSessionTimeout( clientconfMBean.getIdcClientConfirmationSessionTimeout(userOrg , getBrand( user )) );

        // Adding the orderSync interval for the client
        additionalInfo.setOrderSyncInterval( clientconfMBean.getIdcIsOrderSyncInterval( userOrg.getShortName() ) );
        additionalInfo.setDisableTradingDuration(clientconfMBean.getIdcClientDisableTradingDuration(userOrg.getShortName()));

        // Add unity server enabled/disabled info
        additionalInfo.setUnityStatus( APIStreamConfig.getInstance().isAPIStreamEnabled(userOrg.getShortName()) );

        // use RFS aggregation work flow for RFS top of the book and full book
        additionalInfo.setUseRFSAggregation(clientconfMBean.useRFSAggregationWorkflow(userOrg.getShortName()));

        //Additional info - Google Auth brandName
        additionalInfo.setGoogleAuthBrandName(UserFactory.getUserMBean().getGoogleExternalAuthBrandName(userOrg.getShortName()));

        if( ISFactory.getInstance().getPositionServiceConfig().isNewPositionServiceEnabled(userOrg.getShortName()) )
        {
        	additionalInfo.setNewPS(true);
        }

        if(OrderConfiguration.getInstance().getSwitchAlgoPercentageAggrressionInterval(user.getOrganization())!=null) {
        	double passiveTimeFactor = MathUtilC.subtract(1.0, OrderConfiguration.getInstance().getSwitchAlgoPercentageAggrressionInterval(user.getOrganization()));
        	additionalInfo.setPassiveTimeFactor(String.valueOf(passiveTimeFactor));
        }

        if( user.getOrganization().isMarketDataFeedServerEnabled() ) {
            additionalInfo.setMDFEnabled(true);
        }
        additionalInfo.setMt300Field72(clientconfMBean.getMt300Field72Values(userOrg.getShortName(),additionalInfo.getUserDefaultLegalEntity()));
        MarketMakerConfig marketMakerConfig = MarketMakerConfig.getInstance();
        String orgName = userOrg.getShortName();
        additionalInfo.setMarketPulseEnabled(marketMakerConfig.isMarketPulseEnabled(orgName));
        additionalInfo.setMarketMakerDynamicAggStrategyEnabled(marketMakerConfig.isDynamicAggStrategyEnabled(orgName));
        additionalInfo.setMarketMakerVolatilityServiceEnabled(marketMakerConfig.isVolatilityServiceEnabled(orgName));

        List<String> volatilityServiceEnabledOrgs = com.integral.broker.configuration.ConfigurationFactory.getInstance().getVolatilityServiceMBean().getEnabledOrgs();
        if(volatilityServiceEnabledOrgs.contains(userOrg.getShortName())){
            additionalInfo.setVolatilityServiceEnabled(true);
        }

        addTradeCustomParameters( additionalInfo, user );
        additionalInfo.setCryptoExchangeNames ( AccountManagementConfigurationFactory.getAccountManagementConfigurationMBean ().getCryptoExchangeNames () );
    }

    protected void addMarketMakerProperties(LoginResponse.AdditionalLoginInfo additionalInfo, User user)
    {
        MarketMakerConfigMBean marketMakerConfigMBean = MarketMakerConfig.getInstance();
        additionalInfo.setMarketMakerResourceURL(marketMakerConfigMBean.getImageLocation());
        MarketMakerConfigMBean.YMMode ymMode = marketMakerConfigMBean.getYMMode(user.getOrganization().getShortName());
        if(ymMode != null) {
            additionalInfo.setMarketMakerYMMode(ymMode.name());
            additionalInfo.setMarketMakerCoverExecutionControlEnabled(marketMakerConfigMBean.isCoverStrategyControlEnabled(user.getOrganization().getShortName()));
        }
        boolean isMasterControlUIEnabled = false;
        boolean tempIsMasterControlUIEnabled  =  marketMakerConfigMBean.isMasterControlUIEnabled(user.getOrganization().getShortName());
        if(tempIsMasterControlUIEnabled == true && user.hasPermission(PermissionConstants.PRICEMAKING_UPDATE) &&
                (UserUtilC.isSysAdmin(user) || UserUtilC.isSysAdminMaker(user) || UserUtilC.isSysAdminMakerChecker(user)||
                        UserUtilC.isSysAdminChecker(user) || UserUtilC.isMarketMaker(user) || UserUtilC.isFIAdminChecker(user) ||
                        UserUtilC.isFIAdminMaker(user) || UserUtilC.isFIAdminMaker(user))){
            isMasterControlUIEnabled = true;

        }
        additionalInfo.setMarketMakerMasterControlUIEnabled(isMasterControlUIEnabled);
        additionalInfo.setMMSpreadControlIncrementByPips(com.integral.broker.configuration.ConfigurationFactory.
                getInstance().getConfigurationMBean().isMarketMakerControlIncrementByPips(user.getOrganization().getShortName()));
    }

    protected void addJMSProxyProperties( LoginResponse.AdditionalLoginInfo additionalInfo, User user ,String clientName)
    {
        additionalInfo.setProxyURL( clientconfMBean.getProxyServerURL() );
        additionalInfo.setProxySecureURL( clientconfMBean.getProxyServerSecureURL() );
        additionalInfo.setProxyPollingInterval( clientconfMBean.getProxyPollingInterval( user, clientName ) );
        additionalInfo.setProxyMaxMsgCount( clientconfMBean.getProxyMaxMsgCount() );
        additionalInfo.setProxyMainURL( clientconfMBean.getProxyMainURL() );
        additionalInfo.setProxyBrokerID( clientconfMBean.getProxyBrokerID() );
        if ( JMSProxyConfigurationFactory.getJMSProxyMBean().getPollOrStreamForUser( user ) == ServerMBean.STREAMING_MODE_POLL )
        {
            additionalInfo.setProxyMode( "Poll" );
        }
        else
        {
            additionalInfo.setProxyMode( "Stream" );
        }
        additionalInfo.setProxyStreamRequestInterval( "" + JMSProxyConfigurationFactory.getJMSProxyMBean().getStreamRequestIntervalForUser( user ) );
        additionalInfo.setProxySecondaryPollingInterval( clientconfMBean.getProxySecondaryPollingInterval( user, clientName ) );
    }

    /**
     * All JMSProxy connection related APIs to create and remove JMSProxy connection.
     * Login to JMSProxy server - creates a jmsproxy connection per user.
     *
     * @param user      User
     * @param request   request
     * @return response from JMSProxy
     */
    public HTTPParametrizedResponseC createJmsproxyConnection( User user, HttpServletRequest request )
    {
        return createJmsproxyConnection(user, request, true);
    }

    /**
     * All JMSProxy connection related APIs to create and remove JMSProxy connection.
     * Login to JMSProxy server - creates a jmsproxy connection per user.
     *
     * @param user      User
     * @param request   request
     * @param isPrimary True, if create JMS proxy connection for dealing server. False, if creating on secondary server.
     * @return response from JMSProxy
     */
    public HTTPParametrizedResponseC createJmsproxyConnection(User user, HttpServletRequest request, boolean isPrimary)
    {
        String remoteHost = request.getRemoteHost();
        remoteHost = remoteHost.replace( ':', ';' );    //this is needed to translate ipv6 address for mbean registry
        String channelId = new StringBuilder( 50 ).append( "host=" ).append( remoteHost ).append( ", port=" ).append( request.getRemotePort() ).toString();

        Connection connection = connectionManager.createNewConnection();
        connection.setUser(user);
        connection.setPrimaryConnection(isPrimary);

        HTTPParametrizedResponseC response = null;
        try
        {
            long reconnectIntervalInMills = apimBean.getReconnectInterval() * 1000;
            ApiHTTPRequestPacketC requestPacket = new ApiHTTPRequestPacketC( reconnectIntervalInMills );
            //This is to prevent the race condition when 2 concurrent logins from same user are done.
            //Fix for bug 56188 - This ensures there is only one jmsproxy connection per user in connection cache.
            synchronized ( user )
            {
                response = connection.login( requestPacket, channelId, null );
            }
        }
        catch ( Exception e )
        {
            log.error( "AuthController.createJmsProxyConnection.Error exception user:" + user, e );
        }
        return response;
    }

    public boolean removeJmsproxyConnection( User user )
    {
        Connection connection = apiUtil.getConnectionCache().getConnection( user );
        boolean isSuccess;
        try
        {

            if ( connection != null )
            {
                ApiHTTPRequestPacketC requestPacket = new ApiHTTPRequestPacketC();
                HTTPParametrizedResponseC response = connection.logout( requestPacket, null );
                if ( response != null && FXIConstants.JMSPROXY_SUCCESS_STATUS.equals( response.getParameterValue( FXIConstants.JMSPROXY_STATUS ) ) )
                {
                    isSuccess = true;
                }
                else
                {
                    isSuccess = false;
                    log.warn( "AuthController.removeJmsProxyConnection : result of jmsproxy logout response is: " + ( response == null ? "null" : response.getParameterValue( FXIConstants.JMSPROXY_STATUS ) ) );
                }

            }
            else
            {
                isSuccess = false;
                log.warn( "AuthController.removeJmsProxyConnection : connection is null for user : " + user );
            }
        }
        catch ( Exception e )
        {
            isSuccess = false;
            log.error( "AuthController.removeJmsProxyConnection.Error exception is:", e );
        }

        return isSuccess;
    }

    public void setSessionContextOnLogon( User user, String clientName )
    {
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( user );
        IdcSessionManager.getInstance().setSessionContext( ctx );
        ctx.setAttribute( "isClientSession", Boolean.TRUE );
        ctx.setAttribute( ISCommonConstants.FXI_API_SESSION, Boolean.TRUE);
        if ( FXIConstants.FXINSIDE_CLIENT.equals( clientName ) )
        {
            ctx.setLoginChannel( LoginChannel.FXINSIDE_DOTNET );
        }
        else
        {
            //todo: may consider adding separate channel for HTML/IOS/Android - although for all source of rates is API based JSON messages
            ctx.setLoginChannel( LoginChannel.FXI_API );
        }
    }

    /**
     * Query FXRateBasis for getting max(modifiedDate).
     *
     * @param org organization
     * @return modified date
     */
    protected String getQuoteConvetionModifiedDateTime( Organization org )
    {
        FXRateConvention oldFXRateConvention = null;
        Date fxLastRateConvModifiedDate = new Date( System.currentTimeMillis() );
        if ( fxRateConventionMap.containsKey( org ) )
        {
            oldFXRateConvention = fxRateConventionMap.get( org );
        }
        if ( oldFXRateConvention != null )
        {
            fxLastRateConvModifiedDate = oldFXRateConvention.getModifiedDate();
        }

        FXRateConvention rateConv = QuoteConventionUtilC.getInstance().getFXRateConvention( org );
        SimpleDateFormat sdf = new SimpleDateFormat( dateTimeFormatStr );
        if ( fxLastRateConvModifiedDate.compareTo( rateConv.getModifiedDate() ) != 0 )
        {
            fxRateConventionMap.put( org, rateConv );
            return sdf.format( rateConv.getModifiedDate() );
        }
        else
        {
            return sdf.format( fxLastRateConvModifiedDate );
        }

    }

    private void addUserLEInfo( LoginResponse.AdditionalLoginInfo additionalInfo, User user )
    {
        CounterpartyGroup existingCptyGroup = ( CounterpartyGroup ) user.getCustomFieldValue( "DirectFX_AssociatedLegalEntities" );
        StringBuilder sb = new StringBuilder( 20 );
        if ( existingCptyGroup != null )
        {
            Collection associatedLEs = existingCptyGroup.getCounterparties();
            boolean addComma = false;
            for ( Object associatedLE : associatedLEs )
            {
                LegalEntity le = ( LegalEntity ) associatedLE;
                if ( addComma )
                {
                    sb.append( "," );
                }
                if ( le.isActive() )
                {
                    sb.append( le.getShortName() );
                    addComma = true;
                }
            }
        }
        additionalInfo.setUserAssociatedLegalEntities( sb.toString() );
        LegalEntity defaultLe = user.getDefaultDealingEntity();
        if ( defaultLe == null )
        {
            additionalInfo.setUserDefaultLegalEntity( "" );
        }
        else
        {
            additionalInfo.setUserDefaultLegalEntity( defaultLe.getShortName() );
        }
    }

    private void addLegalEntitiesAvailableForTrading( LoginResponse.AdditionalLoginInfo additionalInfo, User user )
    {
        StringBuilder sb = new StringBuilder( 200 );
        StringBuilder orgDetails = new StringBuilder( 256 );
        Set<String> orgsToadd = new HashSet<String>();
        if ( isMbean.isOBOWorkflowEnabled( user ) )
        {
            Collection<TradingParty> tradingParties = SalesDealerGroupUtil.getCounterpartyLegalEntitiesForAssociatedSDG( user );
            for ( TradingParty tp : tradingParties )
            {
                if ( tp.isActive() && tp.getLegalEntity().isActive()
                     && !tp.getLegalEntityOrganization().getShortName().equals( "FXI" ) )
                {
                    String shortName = tp.getLegalEntityOrganization().getShortName();
                    sb.append( '{' ).append( tp.getShortName() )
                            .append( '@' ).append( shortName )
                            .append( ':' ).append( tp.getLegalEntity().getObjectID() ).append( '}' );
                    if(!orgsToadd.contains(shortName))
                    {
                        orgDetails.append("{").append( shortName)
                        .append( ':' ).append( tp.getLegalEntityOrganization().getLongName()).append( '}' );
                        orgsToadd.add(shortName);
                    }
                }
            }
        }
        CounterpartyGroup existingCptyGroup = ( CounterpartyGroup ) user.getCustomFieldValue( "DirectFX_AssociatedLegalEntities" );
        if ( existingCptyGroup != null )
        {
            Collection associatedLEs = existingCptyGroup.getCounterparties();
            for ( Object associatedLE : associatedLEs )
            {
                LegalEntity le = ( LegalEntity ) associatedLE;
                if ( le.isActive() )
                {
                    String shortName =le.getOrganization().getShortName() ;
                    sb.append( '{' ).append( le.getShortName() ).append( '@' ).append(shortName )
                            .append( ':' ).append( le.getObjectID() ).append( '}' );
                    if(!orgsToadd.contains(shortName))
                    {
                        orgDetails.append("{").append( shortName).append(":")
                                .append(  le.getOrganization().getLongName()).append( '}' );
                        orgsToadd.add(shortName);
                    }
                }
            }
        }
        additionalInfo.setLegalEntitiesForTrading( sb.toString() );
        additionalInfo.setOrganizationDetails(orgDetails.toString());
    }

    private void addLegalEntitiesToTrade( LoginResponse.AdditionalLoginInfo additionalInfo, User user )
    {
        additionalInfo.setAnySEFMemberLE( false );

        StringBuilder sb = new StringBuilder( 200 );
        StringBuilder le2CHMapString = new StringBuilder (300);
        StringBuilder clearingExceptionList = new StringBuilder (300);
        if ( isMbean.isOBOWorkflowEnabled( user ) )
        {
            Collection<TradingParty> tradingParties = SalesDealerGroupUtil.getCounterpartyLegalEntitiesForAssociatedSDG( user );
            for ( TradingParty tp : tradingParties )
            {
                if ( tp.isActive() && tp.getLegalEntity().isActive() && !tp.getLegalEntityOrganization().getShortName().equals( "FXI" ) )
                {

                    if (CounterpartyUtilC.isSEFCpty(tp.getLegalEntity()))
					{
						additionalInfo.setAnySEFMemberLE(true);
						if ( SEFUtilC.isClearingExempted(tp.getLegalEntity()) )
						{
							clearingExceptionList.append(tp.getLegalEntityOrganization().getShortName()).append(",");
						}
						updateLE2CHlist(tp, le2CHMapString);
					}
                    sb.append( '{' ).append( tp.getShortName() ).append( '@' ).append( tp.getLegalEntityOrganization().getShortName() ).append( ':' ).append( tp.getLegalEntity().getObjectID() ).append( '-' ).append( tp.getLegalEntity().getSEFTypeStr() ).append( '}' );

                }
            }
        }
        CounterpartyGroup existingCptyGroup = ( CounterpartyGroup ) user.getCustomFieldValue( "DirectFX_AssociatedLegalEntities" );
        if ( existingCptyGroup != null )
        {
            Collection associatedLEs = existingCptyGroup.getCounterparties();
            for ( Object associatedLE : associatedLEs )
            {
                LegalEntity le = ( LegalEntity ) associatedLE;
                if ( le.isActive() )
                {
                    sb.append( '{' ).append( le.getShortName() ).append( '@' ).append( le.getOrganization().getShortName() ).append( ':' ).append( le.getObjectID() ).append( '-' ).append( le.getSEFTypeStr() ).append( '}' );
                    if (CounterpartyUtilC.isSEFCpty(le))
                    {
                        additionalInfo.setAnySEFMemberLE( true );
                        updateLE2CHlist(le, le2CHMapString);
            			if ( SEFUtilC.isClearingExempted(le) )
						{
							clearingExceptionList.append(le.getShortName()).append(",");
						}
                    }
                }
            }
        }
        additionalInfo.setLegalEntitiesToTrade(sb.toString());
        additionalInfo.setLegalEntitiesToClearingHouse(le2CHMapString.toString());
        additionalInfo.setClearingExemptList(clearingExceptionList.toString());
    }

    private void updateLE2CHlist(Counterparty cpty , StringBuilder le2CHMapString)
	{
    	//TODO : IF cpty is permissive SEF, return broker hub that this cpty is connected to
		StringBuilder chListStr = new StringBuilder();
		LegalEntity le = CounterpartyUtilC.getLegalEntity(cpty);
		Set<Organization> chSet = CounterpartyUtilC.getAllClearingHousesForLE(le);

		if ( chSet != null && chSet.size() > 0 )
		{
			Iterator<Organization> chIterator = chSet.iterator();
			while ( chIterator.hasNext() )
			{
				Organization ch = chIterator.next();
				chListStr.append(ch.getShortName());
				if ( chIterator.hasNext() )
				{
					chListStr.append(",");
				}
			}
			le2CHMapString.append('{').append(cpty.getShortName()).append('@').append(le.getOrganization().getShortName()).append(':').append(chListStr).append('}');

		}
	}
    /**
     * @param user user
     * @return comma separated permissions to be sent to client
     */
    private String getUserPermissions( User user )
    {
        Map<String, String> permMapping = clientconfMBean.getClientPermissionMapping();
        StringBuilder sb = new StringBuilder( 100 );
        boolean appendComma = false;
        for ( Map.Entry<String, String> perm : permMapping.entrySet() )
        {
            if ( user.hasPermission( perm.getKey() ) )
            {
                if ( appendComma )
                {
                    sb.append( COMMA );
                }
                else
                {
                    appendComma = true;
                }
                sb.append(perm.getValue());
            }
        }
        //To handle RFS Related
        if ( !user.hasPermission( ISConstantsC.RFSTRADING_PERM ) )
        {
            if ( permMapping.containsKey( ISConstantsC.TRADE_TYPE_RFS_SPOT_PERM ) && user.hasPermission( ISConstantsC.TRADE_TYPE_RFS_SPOT_PERM ) ||
                    permMapping.containsKey( ISConstantsC.TRADE_TYPE_RFS_OUTRIGHT_PERM ) && user.hasPermission( ISConstantsC.TRADE_TYPE_RFS_OUTRIGHT_PERM ) ||
                    permMapping.containsKey( ISConstantsC.TRADE_TYPE_RFS_SWAP_PERM ) && user.hasPermission( ISConstantsC.TRADE_TYPE_RFS_SWAP_PERM ) )
            {
                if ( permMapping.containsKey( ISConstantsC.RFSTRADING_PERM ) )
                {
                    if ( appendComma )
                    {
                        sb.append( COMMA );
                    }
                    sb.append( permMapping.get( ISConstantsC.RFSTRADING_PERM ) );
                }
            }
        }
        return sb.toString();
    }

    private void addUserPermissions( User user, LoginResponse.AdditionalLoginInfo additionalInfo )
    {
        String userPermissions = getUserPermissions( user );
        additionalInfo.setUserPerm( userPermissions );
        // For Bug # 22267 , new properties to send roles to the FXI client to disable/enable features on the client.
        if ( UserUtilC.isMarketMaker( user ) )
        {
            additionalInfo.setUserRole( "MM" );
            additionalInfo.setFxiUserRole( "MM" );
            if ( user.hasPermission( ISConstantsC.CHIEFDEALER_DBVIEW_PERM ) || user.hasPermission( ISConstantsC.CHIEFDEALER_MPVIEW_PERM ) )
            {
                additionalInfo.setUserRole( "CD" );
            }
            // for compatability keep sending CD_PERM.
            additionalInfo.setChiefDealerPerm( userPermissions );
        }
        else if ( UserUtilC.isSalesAdministrator( user ) )
        {
            additionalInfo.setUserRole( "SA" );
            additionalInfo.setFxiUserRole( "SA" );
        }
        else
        {
            additionalInfo.setUserRole( "MAS" );
            additionalInfo.setFxiUserRole( "MAS" );
        }
    }

    private boolean isTradingEnabled( User user )
    {
        return ISUtilImpl.isOrderExecutionEnabledForTakerOrg( user.getOrganization());
    }

    public boolean hasLoginPermission( User user, String clientName )
    {
        if ( FXIConstants.HTML_CLIENT.equals( clientName ) )
        {
            return user.hasPermission( WEB_APP_ACCESS );
        }
        else if (FXIConstants.IOS_FX_TRADE_CLIENT_NAME.equals(clientName))
        {
            return user.hasPermission(FXI_MOBILE_APP_ACCESS);
        }
        else
        {
            return true;
        }
    }

    protected void addTradeCustomParameters( LoginResponse.AdditionalLoginInfo additionalLoginInfo, User user )
    {
        try
        {
            final Organization org = user.getOrganization ();
            Collection<String> customParametersList = tradeConfig.getTradeCustomParameters ( org );
            if ( customParametersList != null )
            {
                CustomParameters customParameters = new CustomParameters ();
                for ( String param : customParametersList )
                {
                    CustomParameter customParameter = new CustomParameter ();
                    customParameter.setDefaultValue ( tradeConfig.getTradeCustomParameterInputValue ( org, param, TradeConfigurationMBean.CustomParameterInputAspect.Default ) );
                    String mandatoryStr = tradeConfig.getTradeCustomParameterInputValue ( org, param, TradeConfigurationMBean.CustomParameterInputAspect.Mandatory );
                    if ( IdcUtilC.YES.equalsIgnoreCase ( mandatoryStr ) || Boolean.TRUE.toString ().equalsIgnoreCase ( mandatoryStr ) )
                    {
                        customParameter.setMandatory ( true );
                    }
                    customParameter.setListOfValues ( tradeConfig.getTradeCustomParameterInputValue ( org, param, TradeConfigurationMBean.CustomParameterInputAspect.ListOfValues ) );
                    customParameter.setDisplay ( tradeConfig.getTradeCustomParameterInputValue ( org, param, TradeConfigurationMBean.CustomParameterInputAspect.Display ) );
                    customParameter.setSymbols ( tradeConfig.getTradeCustomParameterInputValue ( org, param, TradeConfigurationMBean.CustomParameterInputAspect.Symbols ) );
                    customParameter.setType ( tradeConfig.getTradeCustomParameterInputValue ( org, param, TradeConfigurationMBean.CustomParameterInputAspect.Type ) );
                    customParameter.setTradeTypes ( tradeConfig.getTradeCustomParameterInputValue( org, param, TradeConfigurationMBean.CustomParameterInputAspect.TradeTypes ) );
                    customParameter.setVisibility ( tradeConfig.getTradeCustomParameterInputValue( org, param, TradeConfigurationMBean.CustomParameterInputAspect.Visibility ) );
                    customParameters.addCustomParameter ( param, customParameter );
                }
                additionalLoginInfo.setTradeCustomParameters ( customParameters );
                log.info ( "LS.addTradeCustomParameters - added custom parameters=" + additionalLoginInfo.getTradeCustomParameters () );
            }
        }
        catch ( Exception e )
        {
            log.error ( "LS.addTradeCustomParameters - Exception while adding trade custom parameters to the login response. user=" + user.getFullyQualifiedName (), e );
        }
    }

    public Tuple<Boolean, String> authenticate2FAUser(User user, String otp, String password, HttpServletRequest request) {

        Organization org = user.getOrganization();
        String wlUser = user.getShortName() + '@' + user.getOrganization().getNamespace().getShortName();

        String errors = null;
        boolean result = false;

        updateSession( request, user );

//        if(!user.isExternalAuth()){
//            log.warn("LS.authenticate2FAUser: User : " + wlUser + " using External Authentication. Auth failed 2FA is not enabled. This is setup issue between server and UI." );
//            errors = "ERR_2FA_NOT_ENABLED";
//            return new Tuple<Boolean, String>( false, errors );
//        }else
        if( user.isExternalAuth() && org.getExternalAuth() != 3  ) { //Only google auth is supported
            log.warn("LS.authenticate2FAUser: User : " + wlUser + " using External Authentication. Only google authenticator is supported as 2FA. Please check admin settings related to this user." );
            errors = "ERR_INVALID_2FA_METHOD";
            return new Tuple<Boolean, String>( false, errors );
        } else if ( User.ACCOUNT_STATUS_ACCOUNT_DISABLED == user.getAccountStatus() ) {
            //this may not happen, as user authentication will fail for disabled user
            log.info( "LS.authenticate2FAUser: Password has been disabled for user: " + wlUser );
            errors = SecurityServiceC.PASSWORD_FAILED_ATTEMPTS_EXCEEDED_ERROR;
            return new Tuple<Boolean, String>( false, errors );
        } else if (password == null || password.trim().isEmpty() ){
            errors = "ERR_INVALID_PWD";
            log.warn( "LS.authenticate2FAUser. login authentication failed for " + user.getFullyQualifiedName() );
            SecurityServiceC.getInstance().handlerAuthenticatioFailed( user );
            return new Tuple<Boolean, String>( false, errors );
        }

        //Check if direct logins are allowed for External SSO customers
        Organization brokerOrg = org.getBrokerOrganization();
        boolean isLoginAllowed = true;
        if(brokerOrg != null) {
            isLoginAllowed = !(CASMBeanC.getInstance().isOnlySAMLLoginAllowed(brokerOrg.getShortName() , org.getShortName() , user.getShortName())||
                    CASMBeanC.getInstance().isOnlyOIDCLoginAllowedForBrokerCustomer(brokerOrg.getShortName() , org.getShortName() , user.getShortName()));
        }else{
            isLoginAllowed = !CASMBeanC.getInstance().isOnlyOIDCLoginAllowed(org.getShortName() , user.getShortName());
        }

        if(!isLoginAllowed) {
            errors = "ERR_EXT_SSO_ONLY";
            log.warn("LS.authenticate2FAUser - User " + user.getFullName() + " only allowed to login through OIDC/SAML protocol.");
            SecurityServiceC.getInstance().handlerAuthenticatioFailed(user);
            return new Tuple<Boolean, String>( false, errors );
        } else {
	        	// check if password is already expired 
				if (User.ACCOUNT_STATUS_PASSWORD_MUST_CHANGE_AT_NEXT_LOGON == user.getAccountStatus()) {
					log.info("LS.authenticate2FAUser: Password needs to be changed for user: " + user.getShortName());
					// check if max number of forget request exceeded
					UserMBean userMbean = UserFactory.getUserMBean();
					long passwordExpireTime = -1;
					if (userMbean.sourceUserPasswordPolicyFromBrand()) {
						String brandName = user.getOrganization().getShortName();
						Organization borg = user.getOrganization().getBrokerOrganization();
						if (brokerOrg != null) {
							brandName = brokerOrg.getShortName();
						}
						log.info("LS.authenticate2FAUser:BrandName:" + brandName);
						// long passwordExpireTime = 5*60*1000;
						/*
						 * try { ApplicationBrandInfo appBrandInfo =
						 * portalBrandService.getApplicationByBrandAndAppName(brandName,
						 * "PasswordPolicy"); if (appBrandInfo != null) { Map<String, Object>
						 * appSettings = appBrandInfo.getAppSettings(); if (appSettings != null) {
						 * Object passwordExpireTimeObj = appSettings.get("passwordExpiryTime"); if
						 * (passwordExpireTimeObj instanceof Long) { passwordExpireTime =
						 * (Long)passwordExpireTimeObj; } else if (passwordExpireTimeObj instanceof
						 * String) { try { passwordExpireTime =
						 * Long.parseLong((String)passwordExpireTimeObj); } catch (Throwable t) {
						 * passwordExpireTime = -1; } } } }
						 * 
						 * } catch (Throwable t) { log.
						 * error("LS.authenticateUser:Problem with getting BrandInfo from PortalBrandService."
						 * , t); }
						 */
					} else {
						passwordExpireTime = userMbean.getNewPasswordExpiryTime(user);
					}

					log.info("LS.authenticate2FAUser:passwordExpiryTime in milis:" + passwordExpireTime);
					UserSession us = UserSessionQueryService.getUserSession(user);
					if (us != null && passwordExpireTime != -1) {
						// check automated password expiry time only if it's automatically generated and
						// supposed to be changed on next logon
						String pfrtsStr = us.getPasswordForgotRequestTimes();
						// log.info("LS.authenticateUser:pfrtsStr:" + pfrtsStr);
						Long lastForgotPassowrdRequestTime = getLastEventTime(pfrtsStr);
						log.info(
								"LS.authenticate2FAUser:Last forgot password event time:" + lastForgotPassowrdRequestTime);
						if (lastForgotPassowrdRequestTime != null
								&& (System.currentTimeMillis() - lastForgotPassowrdRequestTime) > passwordExpireTime) {
							// SecurityFactory.getSecurityService().handlePasswordResetRequestFailed(user);
							String subject = userMbean.getUserResetPasswordEmailSubject(user);
							String fromEmailAddress = userMbean.getUserResetPasswordFromEmailId(user);
							String emailContent = userMbean.getUserPasswordExpireContent(user);
							PasswordValidation.sendEmail(user, fromEmailAddress, subject, emailContent);
							result = false;
							//errors = SecurityServiceC.PASSWORD_EXPIRED;
							errors = FXIConstants.ERROR_CODE_PASSWORD_EXPIRED;
							log.warn("LS.authenticate2FAUser. User password expired. for user with id: " + user);
							return new Tuple<Boolean, String>( false, errors );
						}
					}
				}
        	
            try {
            	user.verifyMD5Password( password );
            	result = true;
            } catch ( UserAuthenticationException ue ) {
                errors = "ERR_INVALID_PWD";
                log.warn( "LS.authenticate2FAUser. Invalid password - login authentication failed for " + user.getFullyQualifiedName() );
                SecurityServiceC.getInstance().handlerAuthenticatioFailed( user );
                return new Tuple<Boolean, String>( false, errors );
            }
        }

        if(result){
            Boolean isAssigned = GoogleExternalAuthenticationServiceC.getInstance().isSecretAssociated( wlUser );
            if(isAssigned==null || !isAssigned){
                errors = "INFO_2FA_SETUP_INCOMPLETE";
                log.info( "LS.authenticate2FAUser. login authentication failed for " + user.getFullyQualifiedName() );
                return new Tuple<Boolean, String>( true, errors );
            }

            log.info( new StringBuilder( 350 ).append( "LS.authenticate2FAUser: login succeeded for user " ).append( wlUser ).toString() );
        } else {
            log.warn( new StringBuilder( 350 ).append( "LS.authenticateUser: login failed for user " ).append( wlUser ).toString() );
        }

        return new Tuple<Boolean, String>( result, errors );
    }


    public Tuple<Boolean, String> validate2FAToken(User user, String otp,String verificationToken) {
        String errors = null;
        boolean result;
        String wlUser = user.getShortName() + '@' + user.getOrganization().getNamespace().getShortName();

        if(verificationToken==null||verificationToken.isEmpty()){
            log.warn("LS.validate2FAToken: User : " + user.getFullName() + ". Invalid verification token (null or empty) ." );
            errors = "ERR_INVALID_VTOKEN";
            return new Tuple<Boolean, String>( false, errors );
        } else {
            Tuple<Boolean,User> validationResult = CommonAuthenticationServiceC.getInstance().
                    validateProvisionalToken(verificationToken, ProvisionalTokenTypes.MFAVerification);
            if (!validationResult.first) {
                log.warn("LS.validate2FAToken: User : " + user.getFullName() + ". Invalid verification token." );
                errors = "ERR_INVALID_VTOKEN";
                return new Tuple<Boolean, String>( false, errors );
            }else {
                if(GoogleExternalAuthenticationServiceC.getInstance().validateOTPForUser( wlUser, otp )){
                    result = true;
                }else{
                    log.warn("LS.validate2FAToken: User : " + user.getFullName() + ". ." );
                    errors = "ERR_INVALID_2FATOKEN";
                    return new Tuple<Boolean, String>( false, errors );
                }
            }
        }
        return new Tuple<Boolean, String>( result, errors );
    }

    private static final CommonAuthenticationService cas = CommonAuthenticationServiceC.getInstance();
    public MFALoginResponse onLogonSuccess(String uId, SSOLoginRequest loginData, User user,
                                           HttpServletRequest request, HttpServletResponse response, boolean isSSOEnable) throws Exception {

        MFALoginResponse loginResponse = new MFALoginResponse( Status.OK );

        if ( User.ACCOUNT_STATUS_PASSWORD_MUST_CHANGE_AT_NEXT_LOGON == user.getAccountStatus() ) {
            loginResponse.setServerUTCTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());
            loginResponse.setErrorCode("INFO_CHANGE_PASSWORD");
            loginResponse.setPasswordResetToken(cas.generateProvisionalToken(uId,request,ProvisionalTokenTypes.ResetPassword));
            log.info("onLogonSuccess . User needs to change password. uId=" + uId);
            return loginResponse;
        }

        //Set the SSO_TOKEN to allow client to allow any service
        String ssoToken = cas.generateAuthToken(uId, loginData.getApiVersion(), request);

        if (isSSOEnable) {
            String loginType = AuthController.getCookieValue(request, SecurityConstant.LOGIN_TYPE);
            AuthenticatorUtilC.getInstance().addSSOCookiesToResponse(response, ssoToken,loginType);
        } else {
            if (CASMBeanC.getInstance().isJWTAuthenticationEnable()) {
                com.integral.darwin.auth.jwt.JwtTokenUtil.getInstance().addJwtTokenHeaderV2(request,response,user);
            }
        }
        //add jwt token header
        if(log.isDebugEnabled()){
            boolean maker = user.hasPermission(PaymentsPermissionsSupplier.PaymentMaker);
            boolean approver = user.hasPermission(PaymentsPermissionsSupplier.PaymentApprover);
            boolean admin = user.hasPermission(PaymentsPermissionsSupplier.PaymentFIAdmin);
            boolean sysAdmin = user.hasPermission(PaymentsPermissionsSupplier.PaymentSysAdmin);
            log.debug("onLogonSuccess. Payment Roles: maker=" + maker + ", approver=" + approver + ", admin=" + admin + ", sys=" + sysAdmin);
        }
        addLoginTimeCookieToResponse(response);
        loginResponse.setExpiryTime(getExpiryTime(ssoToken));
        loginResponse.setServerUTCTime(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());
        log.info("SC.onLogonSuccess. done successfully for " + uId);
        SecurityFactory.getSecurityService().auditLoginSuccess( user, true );
        //set redirect cookie based on user's org virtual server
        AuthController.getInstance().setRedirectionCookieFor( user.getOrganization().getShortName(), response);
        //Extract loadBalancer IP Address from the cookie and to track singleURL login through A10.
        //loginService.setSingleURLTrackCookie(request, response);
        return loginResponse;
    }

    public static void addLoginTimeCookieToResponse(HttpServletResponse response)
    {
        Cookie loginTimeCookie = new Cookie( FXIConstants.LOGIN_TIME, Long.toString( System.currentTimeMillis() ) );
        loginTimeCookie.setPath( "/" );
        response.addCookie( loginTimeCookie );
    }

    public static long getExpiryTime(String encToken)
    {
        String decryptedToken = CryptC.decryptURLSafeString(encToken);
        return CASClient.AuthToken.parseExpiryTime(decryptedToken);
    }



}
