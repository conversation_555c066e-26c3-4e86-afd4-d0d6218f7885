package com.integral.fxiapi.order;

import com.integral.businessCenter.BusinessCenter;
import com.integral.finance.businessCenter.EndOfDayServerC;
import com.integral.finance.businessCenter.EndOfDayService;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.ContingencyType;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.ExecutionFlags;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.facade.RequestStateFacade;
import com.integral.finance.dealing.facade.RequestStateFacadeC;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.*;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.fix.client.FixConstants;
import com.integral.fxiapi.model.*;
import com.integral.fxiapi.model.reference.ExecutionState;
import com.integral.fxiapi.trade.TradeTranslator;
import com.integral.fxiapi.trade.spaces.SingleLegTradeService;
import com.integral.fxiapi.util.FXIApiUtil;
import com.integral.fxiapi.util.FormatUtils;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.oms.OrderConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.user.User;

import java.sql.Time;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> Development Corporation.
 * <AUTHOR>
 */
public class OrderTranslator extends BaseOrderTranslator implements FXIOrderTranslator
{
    private static final Log log = LogFactory.getLog( OrderTranslator.class );

    private static class OrderTranslatorHolder
    {
        private static final OrderTranslator INSTANCE = new OrderTranslator();
    }

    public static OrderTranslator getInstance()
    {
        return OrderTranslatorHolder.INSTANCE;
    }

    public static FXIOrderTranslator getFXIOrderTranslator()
    {
        return OrderTranslatorHolder.INSTANCE;
    }

    @Override
    public Order getAcceptanceMessage( WorkflowMessage wfMsg, User user )
    {

        Order orderResponse = new Order();
        Object msgObject = wfMsg.getObject();
        FXSingleLeg fxTrade = msgObject instanceof Request ? ( FXSingleLeg ) ( ( ( Request ) msgObject ).getTrade() ) : ( FXSingleLeg ) wfMsg.getObject();
        Request request = fxTrade.getRequest();
        Request parentRequest = request.getParentRequest();
        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) parentRequest.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        Currency dealtCcy = dealingPrice.getDealtCurrency();
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat( user );
        DecimalFormat dealtCcyFormat = dealtCcy.getDecimalFormat( userDecFormat.clone() );
        DecimalFormat settledCcyFormat = dealingPrice.getSettledCurrency().getDecimalFormat( userDecFormat.clone() );
        DecimalFormat rateFormat = FXIApiUtil.getInstance().getRateFormat(dealingPrice.getFXRate(), userDecFormat);
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat( user );
        SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateFormat( user );

        copyParentOrderAttributes( parentRequest, orderResponse, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false );

        double avgFilledPrice = ( Double ) wfMsg.getParameterValue( ISCommonConstants.ORDER_AVG_FILL_PRICE );
        double unfilledAmount = ( Double ) wfMsg.getParameterValue( ISCommonConstants.ORDER_UNFILLED_AMT );
        double totalFilledAmt = ( Double ) wfMsg.getParameterValue( ISCommonConstants.ORDER_FILLED_AMT );

        //ocoorderid, positionID ?
        orderResponse.setOrderId( request.getOrderId() );
        orderResponse.setTransactionId( parentRequest.getTransactionID() );
        orderResponse.setParentOrderId( parentRequest.getOrderId() );
        orderResponse.setFillRate( rateFormat.format( avgFilledPrice ) );
        orderResponse.setFilledAmount( dealtCcyFormat.format( totalFilledAmt ) );
        orderResponse.setUnfilledAmount( dealtCcyFormat.format( unfilledAmount ) );


        RequestStateFacade facade = ( ( RequestStateFacade ) request.getFacade( RequestStateFacade.REQUEST_STATE_FACADE ) );
        if ( facade.isDeclined() )
        {
            //todo: check this scenario may not occur since order can expire but may not reject
            orderResponse.setOrderStatus( OrderStatusType.Rejected );
        }
        else
        {
            if ( unfilledAmount != 0 )
            {
                orderResponse.setOrderStatus( OrderStatusType.Partial );
            }
            else
            {
                orderResponse.setOrderStatus( OrderStatusType.Filled );
            }
        }

        Trade trade = TradeTranslator.getFXITradeTranslator().generateTrade(fxTrade, user);
        if(wfMsg.getParameterValue(ISCommonConstants.DEAL_NET) != null){
            trade.setIsnet(true);
        }
        orderResponse.setAcceptedTrade( trade );
        updateTradeParamsfromWFMsg(trade,wfMsg);

        OrderMessage message = getOrderMessage( parentRequest, dateTimeFormat );
        message.setEventName(TRADE_VERIFIED_EVENT);
        message.setEventDetails( getTradeMessageDetails( fxTrade, dealtCcyFormat, settledCcyFormat, dateFormat, true ) );
        message.setTransactionId( fxTrade.getTransactionID() );
        orderResponse.setOrderMessage( message );

        return orderResponse;
    }

    @Override
    public Order getPreRateAcceptanceMessage( WorkflowMessage wfMsg, User user )
    {
        return null;
    }

    private void updateTradeParamsfromWFMsg( Trade trade, WorkflowMessage wfMsg )
	{
		if ( wfMsg.getParameterValue(ISCommonConstants.ISMAKER_FLAG) != null && (Boolean) wfMsg.getParameterValue(ISCommonConstants.ISMAKER_FLAG) )
		{
			trade.setMaker(true);
		}
	}

    @Override
    public Order getCancellationMessage(WorkflowMessage wfMsg, User user)
    {
        Order orderResponse = new Order();
        Object msgObject = wfMsg.getObject();
        FXSingleLeg fxTrade = msgObject instanceof Request ? ( FXSingleLeg ) ( ( ( Request ) msgObject ).getTrade() ) : ( FXSingleLeg ) wfMsg.getObject();
        Request request = fxTrade.getRequest();
        Request parentRequest = request.getParentRequest();
        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) parentRequest.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        Currency dealtCcy = dealingPrice.getDealtCurrency();
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat( user );
        DecimalFormat dealtCcyFormat = dealtCcy.getDecimalFormat( userDecFormat.clone() );
        DecimalFormat settledCcyFormat = dealingPrice.getSettledCurrency().getDecimalFormat( userDecFormat.clone() );
        DecimalFormat rateFormat = FXIApiUtil.getInstance().getRateFormat(dealingPrice.getFXRate(), userDecFormat);
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat( user );
        SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateFormat( user );

        copyParentOrderAttributes( parentRequest, orderResponse, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false );

        orderResponse.setOrderId( request.getOrderId() );
        orderResponse.setTransactionId( parentRequest.getTransactionID() );
        orderResponse.setParentOrderId( parentRequest.getOrderId() );

        Trade trade = TradeTranslator.getFXITradeTranslator().generateTrade(fxTrade, user);
        updateTradeParamsfromWFMsg(trade,wfMsg);
        orderResponse.setCancelledTrade( trade );

        OrderMessage message = getOrderMessage( parentRequest, dateTimeFormat );
        message.setEventName(TRADE_CANCELLED_EVENT);
        message.setEventDetails( getTradeMessageDetails( fxTrade, dealtCcyFormat, settledCcyFormat, dateFormat, false ) );
        message.setTransactionId( fxTrade.getTransactionID() );
        orderResponse.setOrderMessage( message );

        return orderResponse;

    }

    @Override
    public Order getPreAggregatedFill(WorkflowMessage wfMsg, User user) {
        return null;
    }

    @Override
    public Order getOrderExpirationMessage( WorkflowMessage wfMsg, User user )
    {
        Order orderResponse = new Order();
        Object msgObject = wfMsg.getObject();
        Request request = msgObject instanceof Request ? ( Request ) wfMsg.getObject() : ( ( FXSingleLeg ) msgObject ).getRequest() ;

//        Request request = ( Request ) wfMsg.getObject();
        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) request.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        Currency dealtCcy = dealingPrice.getDealtCurrency();
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
        DecimalFormat dealtCcyFormat = dealtCcy.getDecimalFormat( userDecFormat.clone() );
        DecimalFormat rateFormat = FXIApiUtil.getInstance().getRateFormat(dealingPrice.getFXRate(), userDecFormat);
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);

        String orderId = request.getOrderId();
        double avgFilledPrice = ( Double ) wfMsg.getParameterValue( FixConstants.AVG_FILL_PRICE );
        double unfilledAmount = ( Double ) wfMsg.getParameterValue( FixConstants.UNFILLED_AMT );
        double totalFilledAmt = ( Double ) wfMsg.getParameterValue( FixConstants.FILLED_AMT );
        orderResponse.setOrderId( orderId );
        orderResponse.setTransactionId( request.getTransactionID() );
        copyParentOrderAttributes( request, orderResponse, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false );
        orderResponse.setFillRate( rateFormat.format( avgFilledPrice ) );
        orderResponse.setFilledAmount( dealtCcyFormat.format( totalFilledAmt ) );
        orderResponse.setUnfilledAmount( dealtCcyFormat.format( unfilledAmount ) );
        orderResponse.setOrderStatus(getOrderStatus(request) );
        if( request.getRequestAttributes().isExexcutionSuspended() )
        {
        	orderResponse.setExecutionState(ExecutionState.STOPPED);
        }

        if( request.getTradeClassification() != null )
		{
        	orderResponse.setTradeClassification( request.getTradeClassification().getShortName() );
		}

		if( wfMsg.getParameterValue( ISCommonConstants.Key_ExpiryByScheduler ) != null )
		{
			orderResponse.setCancelBySystem( true );
		}
        else {
            orderResponse.setCancelBySystem( false );
        }

		if( request.getCancelledBy() != null )
		{
			orderResponse.setCancelledBy( request.getCancelledBy() );
		}

        OrderMessage message = getOrderMessage( request, dateTimeFormat );
        message.setEventName(ORDER_CANCELED_EVENT);
        message.setEventDetails(FXIApiUtil.getInstance().getOrderCancellationMessage(orderResponse));
        orderResponse.setOrderMessage( message );

        return orderResponse;
    }

    @Override
    public Order getOrderPendingMessage( WorkflowMessage wfMsg, User user )
    {
        Order orderResponse = new Order();
        Object msgObject = wfMsg.getObject();
        Request request = msgObject instanceof Request ? ( Request ) wfMsg.getObject() : ( ( FXSingleLeg ) msgObject ).getRequest() ;

//        Request request = ( Request ) wfMsg.getObject();
        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) request.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        Currency dealtCcy = dealingPrice.getDealtCurrency();
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
        DecimalFormat dealtCcyFormat = dealtCcy.getDecimalFormat( userDecFormat.clone() );
        DecimalFormat rateFormat = FXIApiUtil.getInstance().getRateFormat(dealingPrice.getFXRate(), userDecFormat);
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);

        String orderId = request.getOrderId();

        double avgFilledPrice = ( Double ) wfMsg.getParameterValue( ISConstantsC.ORDER_AVG_FILL_PRICE );
        double unfilledAmount = ( Double ) wfMsg.getParameterValue( ISConstantsC.ORDER_UNFILLED_AMT ); // For bug 59405
        double totalFilledAmt = ( Double ) wfMsg.getParameterValue( ISConstantsC.ORDER_FILLED_AMT ); // For bug 59405
        double pendingAmt = ( Double ) wfMsg.getParameterValue( OrderConstants.ORDER_PENDING_AMOUNT );

        orderResponse.setOrderId( orderId );
        orderResponse.setTransactionId( request.getTransactionID() );
        copyParentOrderAttributes( request, orderResponse, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false );
        orderResponse.setFillRate( rateFormat.format( avgFilledPrice ) );
        orderResponse.setFilledAmount( dealtCcyFormat.format( totalFilledAmt ) );
        orderResponse.setUnfilledAmount( dealtCcyFormat.format( unfilledAmount ) );
        orderResponse.setPendingAmount( dealtCcyFormat.format(pendingAmt) );
        orderResponse.setOrderStatus( OrderStatusType.NotifPending );

        orderResponse.setExecutionState(ExecutionState.ACTIVE);

        if( request.getTradeClassification() != null )
		{
        	orderResponse.setTradeClassification( request.getTradeClassification().getShortName() );
		}

		if( wfMsg.getParameterValue( ISCommonConstants.Key_ExpiryByScheduler ) != null )
		{
			orderResponse.setCancelBySystem( true );
		}
        else
        {
            orderResponse.setCancelBySystem( false );
        }

		if( request.getCancelledBy() != null )
		{
			orderResponse.setCancelledBy( request.getCancelledBy() );
		}

        OrderMessage message = getOrderMessage( request, dateTimeFormat );
        message.setEventName(ORDER_PENDING_EVENT);
        message.setEventDetails( FXIApiUtil.getInstance().getOrderPendingMessage(orderResponse) );
        orderResponse.setOrderMessage( message );

        return orderResponse;
    }

	@Override
    public Order getOrderRejectedMessage( WorkflowMessage wfMsg, User user, String eventName )
    {
        Request request = ( Request ) wfMsg.getObject();
        Order orderResponse = generateOrder( request, user );
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat( user );

        OrderMessage message = getOrderMessage( request, dateTimeFormat );
        message.setEventName( eventName );
        message.setEventDetails(SingleLegTradeService.getFXITradeService().getOrderRejectionMessage(orderResponse, eventName));
        orderResponse.setOrderMessage( message );

        return orderResponse;
    }

    @Override
    public Order getOrderSubmittedMessage( WorkflowMessage wfMsg, User user )
    {
        Object msgObj = wfMsg.getObject();
        Request orderRequest = msgObj instanceof Request ? (Request) msgObj : ( ( FXSingleLegC ) msgObj ).getRequest();
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat( user );

        Order order = generateOrder(orderRequest, user);
        OrderMessage message = getOrderMessage( orderRequest, dateTimeFormat );
        message.setEventName(ORDER_SUBMITTED_EVENT);
        message.setEventDetails(getOrderSubmissionMessage(orderRequest, order));
        order.setOrderMessage( message );
        return order;
    }

    @Override
    public Order generateOrder( Object obj, User user )
    {
        if ( !( obj instanceof Request ) )
        {
            log.error("Wrong dependency injection", new IllegalAccessException(this.getClass().getSimpleName()));
            return null;
        }
        return generateOrder( ( Request ) obj, user, false );
    }

    @Override
    public Order generateOrder( Object obj, User user, boolean enrichTrades )
    {
        if ( !( obj instanceof Request ) )
        {
            log.error("Wrong dependency injection", new IllegalAccessException(this.getClass().getSimpleName()));
            return null;
        }
        return generateOrder( ( Request ) obj, user, false );
    }

    /**
     * Creates the order message by parsing order request
     *
     * @param request order request
     * @param user    user
     * @return order response
     */
    public Order generateOrder( Request request, User user, boolean queryTradeIds)
    {
        Order order = new Order();
        String orderId = request.getOrderId();
        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) request.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat( user );
        DecimalFormat dealtCcyFormat = dealingPrice.getDealtCurrency().getDecimalFormat( userDecFormat.clone() );
        DecimalFormat rateFormat = FXIApiUtil.getInstance().getRateFormat(dealingPrice.getFXRate(), userDecFormat);
        SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);

        copyParentOrderAttributes( request, order, userDecFormat, dealtCcyFormat, rateFormat, dateFormat, queryTradeIds );

        double filledPrice = dealingPrice.getAverageRate() != null ? dealingPrice.getAverageRate() : 0.0;
        order.setFillRate( rateFormat.format( filledPrice ) );
        order.setOrderStatus(getOrderStatus(request));

        double filledAmt = dealingPrice.getVerifiedDealtCurrencyAmount();
        RequestStateFacadeC rsf = ( RequestStateFacadeC ) request.getFacade( RequestStateFacade.REQUEST_STATE_FACADE );
        if(request.isNettingEnabled() && !rsf.isInFinalState() && !request.isShowFills()) {
            if(request.getRequestAttributes().getNettedAmount() <= filledAmt) {
                filledAmt = filledAmt - request.getRequestAttributes().getNettedAmount();
            }
            if(filledAmt == 0) {
                //override average rate and order status
                order.setFillRate(rateFormat.format(0));
                order.setOrderStatus(OrderStatusType.Pending);
            }
        }

        double unFilledAmt = dealingPrice.getDealtAmount() - filledAmt;

        order.setOrderId( orderId );
        order.setTransactionId( request.getTransactionID() );
        order.setFilledAmount(dealtCcyFormat.format(filledAmt));
        order.setUnfilledAmount(dealtCcyFormat.format(unFilledAmt));
        double pendingAmt = dealingPrice.getPendingVerificationAmount();
        if (pendingAmt > 0 && request.isCancelReceived()) {
            // If order is cancelled by user but trade response is pending, we set 'Cancelling' status
            order.setPendingAmount(dealtCcyFormat.format(pendingAmt));
            order.setOrderStatus(OrderStatusType.Cancelling);
        }

        //order.positionId
        order.setParentOrderId( request.getParentRequest() != null ? request.getParentRequest().getOrderId() : null );

//        order.ocoOrderId

        if( request.getRequestAttributes().isOrder() )
        {
        	order.setTradeDate(request.getCreatedBusinessDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN));

        	//ExecutionState INACTIVE has higher precedence then STOPPED. An order may be in both state...
        	if ( request.isOTOInactiveOrder() )
            {
            	order.setExecutionState(ExecutionState.INACTIVE);
            }
        	else if( request.getRequestAttributes().isExexcutionSuspended() )
            {
            	order.setExecutionState(ExecutionState.STOPPED);
            }

        	if( request.getOrderExecutionStrategyName() != null )
    		{
    			order.setExecutionStrategyName( request.getOrderExecutionStrategyName() );
    		}

    		if ( request.getOrderExecutionStartTime() != null )
    		{
    			order.setExecutionStartTime(new SimpleDateFormat(ISCommonConstants.TWAP_TIME_FORMAT_MILLIS).format(request.getOrderExecutionStartTime()));
    		}

    		if ( request.getOrderExecutionEndTime() != null )
    		{
    			order.setExecutionEndTime(new SimpleDateFormat(ISCommonConstants.TWAP_TIME_FORMAT_MILLIS).format(request.getOrderExecutionEndTime()));
    		}

    		if ( request.getActionOnOrderExpitation() != -1 )
    		{
    			order.setActionOnExpiry(request.getActionOnOrderExpitation());
    		}

    		if( request.getPassiveTime() != 0.0 )
    		{
    			order.setPegTime(request.getPassiveTime());
    		}

    		if( request.getTradeClassification() != null )
    		{
    			order.setTradeClassification( request.getTradeClassification().getShortName() );
    		}

    		Boolean isUnsolicitedCancelIation = (Boolean) request.getRequestAttributes().isUnsolicitedCancel();
    		if( isUnsolicitedCancelIation != null && isUnsolicitedCancelIation )
    		{
    			order.setCancelBySystem( isUnsolicitedCancelIation );
    		}

    		if( request.getCancelledBy() != null )
    		{
    			order.setCancelledBy( request.getCancelledBy() );
    		}

    		// Set execution flags
    		order.setExecFlags( getExecutionStrategies( request, dealingPrice ) );

    		populateContingencyParams(order, request);
        }

        // populating order submission field in header object here.
        // TODO: remove header fields from Order objects
        if (StringUtils.isNotBlank(order.getOrderSubmissionTime())) {
            try {
                order.getHeader().setSendingTime(dateFormat.parse(order.getOrderSubmissionTime()).getTime());
            } catch (ParseException e) {
                log.error("Error occurred in parsing OrderSubmissionTime " + e);
            }
        }

        return order;
    }


    private void populateContingencyParams(Order order, Request request)
    {
    	if( request.getContingencyParameters().isEmpty() )
    	{
    		return;
    	}
    	List<ContingencyParameter> cParams= new ArrayList<ContingencyParameter>();
    	for( com.integral.finance.dealing.ContingencyParameter cp : request.getContingencyParameters() )
    	{
	    	ContingencyParameter contParam = new ContingencyParameter();
	    	contParam.setType( cp.getType() );
	    	if( cp.getLinkedOrderIds() != null && !cp.getLinkedOrderIds().isEmpty() )
	    	{
	    		contParam.setLinkedOrderIds( cp.getLinkedOrderIds() );
	    	}
	    	else //hack for .NET client to work
	    	{
	    		contParam.setLinkedOrderIds( new ArrayList<String>(1) );
	    	}

	    	if ( request.getParentOCORequestGroup() != null && cp.getType() != ContingencyType.OTO )
			{
	    		contParam.setGroupId( request.getParentOCORequestGroup().getGroupId() );
			}
	    	else
	    	{
	    		contParam.setGroupId( cp.getGroupId() );
	    	}
	    	cParams.add(contParam);
    	}
    	order.setContingencyParameters(cParams);
    }

//    public Order getOrderResponse(WorkflowMessage wfm, User user)
//    {
//    	Order orderResponse = new Order();
//    	Request request = (Request) wfm.getObject();
//    	if( request == null )
//    	{
//    		log.warn("OrderTranslator.getOrderResponse: Request is found null in workflow msg");
//    		return null;
//    	}
//
//    	if ( request.getRequestAttributes().isOrder() )
//    	{
//    		List<ContingencyParameter> cps = new ArrayList<ContingencyParameter>();
//    		if ( request.getParentOCORequestGroup() != null )
//    		{
//    			ContingencyParameter cp = new ContingencyParameter();
//
//    			cp.setType((String) wfm.getParameterValue(ISCommonConstants.WF_PARAM_CONTINGENCY_TYPE));
//
//    			List<String> lOids = new ArrayList<String>(); // LinkedOrderIds
//    			lOids.add((String) wfm.getParameterValue(ISCommonConstants.WF_PARAM_LINKED_ISORDERID));
//    			cp.setLinkedOrderIds(lOids);
//
//    			cp.setGroupId((String) wfm.getParameterValue(ISCommonConstants.WF_PARAM_CONTINGENT_REQUEST_GROUPID));
//    		}
//
//    		orderResponse.setContigencyParameters(cps);
//
//    		addStrategyWFParameters( wfm, orderResponse );
//
//    	}
//    	return orderResponse;
//    }
//
//    private void addStrategyWFParameters( WorkflowMessage wfm, Order oResp )
//    {
//    	oResp.setExecutionStrategyName(
//    			(String) wfm.getParameterValue(ISCommonConstants.TWAP_STRATEGYNAME));
//    	oResp.setExecutionStartTime(
//    			(String) wfm.getParameterValue(ISCommonConstants.TWAP_STARTTIME_ABSOLUTE));
//    	oResp.setExecutionEndTime(
//    			(String) wfm.getParameterValue(ISCommonConstants.TWAP_ENDTIME_ABSOLUTE));
//    	oResp.setActionOnExpiry(
//    			Integer.valueOf((String) wfm.getParameterValue(ISCommonConstants.TWAP_ACTIONORDEREXPIRY)));
//    	oResp.setPegTime(
//    			Double.val);
//    }

    private void copyParentOrderAttributes( Request orderRequest, Order orderResponse, DecimalFormat userDecFormat, DecimalFormat dealtCcyFormat, DecimalFormat rateFormat, SimpleDateFormat dateFormat, boolean queryTradeIds )
    {
        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) orderRequest.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        boolean isBid = dealingPrice.getBidOfferMode() == FXLegDealingPrice.BID;
        FXPrice fxPrice = ( ( FXDealingPriceElement ) dealingPrice.getPriceElement() ).getFXPrice();
        FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
        FXRateBasis fxRateBasis = fxRate != null ? fxRate.getFXRateBasis() : null;

        double orderAmount = dealingPrice.getDealtAmount();

        long orderSubmissionTime = orderRequest.getSubmissionTime().getTime();
        long expiryTime = (orderRequest.getExpiryTime() != null) ?
            (orderRequest.getExpiryTime().getTime() - orderSubmissionTime) : 0;

        if ( expiryTime > 0 ) {
            //
            // For strategy order with delayed execution, the expiration time is set as expiryTime + executionStartTime
            // Hence while returning back to client, removing the executionStartTime from totalExpirationTime to
            // provide only expiryTime, as submitted by the client.
            //
            long execStartTime = ( orderRequest.getOrderExecutionStartTime() != null ) ?
                                    orderRequest.getOrderExecutionStartTime().getTime() : 0;
            int execFlags = orderRequest.getExecutionFlags();
            if ( ( execFlags & ExecutionFlags.STRATEGY ) == ExecutionFlags.STRATEGY && execStartTime > 0 ) {
                expiryTime = ( orderRequest.getExpiryTime() != null ) ?
                                ( orderRequest.getExpiryTime().getTime() - execStartTime ) : 0;
            }

            orderResponse.setExpiryTime( expiryTime / 1000 );
        }
        else if (getTimeInForce(orderRequest) == OrderExpiryType.DO && orderRequest.getOrganization() != null) {
            // populate expiry time for day order as requested as it is required for HTML client team. We don't
            // use this expiry time internally to expire day orders.
            BusinessCenter businessCenter = orderRequest.getOrganization().getBusinessCenter(EndOfDayServerC.ROLL_TIME_BUSINESS_CENTER_NAME);
            if (businessCenter == null) {
                final EndOfDayService dayService = EndOfDayServiceFactory.getEndOfDayService();
                businessCenter = dayService.getRollTimeBusinessCenter();
            }

            if (businessCenter != null) {
                Time rollTime = businessCenter.getRollTime();
                TimeZone tz = businessCenter.getTimeZone();
                IdcDateTime currentRollTime = DateTimeFactory.newDateTime(DateTimeFactory.newDate(tz), rollTime, tz);
                expiryTime = currentRollTime.asJdkDate().getTime();
                if (expiryTime <= orderSubmissionTime) {
                    currentRollTime = DateTimeFactory.newDateTime(DateTimeFactory.newDate(tz).addDays(1), rollTime, tz);
                    expiryTime = currentRollTime.asJdkDate().getTime();
                }

                if (expiryTime > 0) {
                    expiryTime -= orderSubmissionTime;
                    orderResponse.setExpiryTime(expiryTime / 1000);
                }
            }
        }


        orderResponse.setCustomerId( orderRequest.getUser().getShortName() );
        orderResponse.setCustomerOrder( orderRequest.getExternalRequestId() );
        orderResponse.setAmount( dealtCcyFormat.format( orderAmount ) );

        //get original ccy pair , tenor , settlment type
        FXIApiUtil.getInstance().updateInstrumentDetailsWithOriginal( fxPrice.getBaseCurrency() , fxPrice.getVariableCurrency() , orderResponse,
                dealingPrice.getDealtCurrency() , dealingPrice.getSettledCurrency(), fxRateBasis );

        orderResponse.setTimeInForce( getTimeInForce( orderRequest ) );
        OrderType orderType = getOrderType(orderRequest.getRequestClassification().getShortName());
        orderResponse.setOrderType( orderType );
        if( orderType == OrderType.Market && (orderRequest.getPegType() != Character.MIN_VALUE ) ){
            orderResponse.setLimitRate(null);
        } else {
            orderResponse.setLimitRate( rateFormat.format( fxRate.getRate() ) );
        }
        orderResponse.setExecFlags( getExecutionStrategies( orderRequest, dealingPrice ) );
        orderResponse.setOrderTime( dateFormat.format( orderRequest.getExecutionDateTime() ) );
        orderResponse.setOrderSubmissionTime( dateFormat.format( orderRequest.getSubmissionTime() ) );
        orderResponse.setOrderTimeInMillis( orderRequest.getExecutionDateTime().getTime() );
        orderResponse.setOrderSubmissionTimeInMillis( orderRequest.getSubmissionTime().getTime() );
        orderResponse.setCustomerOrg( orderRequest.getOrganization().getShortName() );
        orderResponse.setCustomerAccount( orderRequest.getCounterparty().getShortName() );
        orderResponse.setExternalRequestID( orderRequest.getExternalRequestId() );

        //order response has buy/sell based on base ccy
        orderResponse.setOrderSide( isBid ? OrderSideType.Buy : OrderSideType.Sell );
        orderResponse.setTradeChannel( orderRequest.getTradeChannel() );

        if ( dealingPrice.getMaxShowAmount() != null )
        {
            orderResponse.setMaxShow( dealtCcyFormat.format( dealingPrice.getMaxShowAmount() ) );
        }
        if ( dealingPrice.getMinDealtAmount() != null )
        {
            orderResponse.setMinQuantity( dealtCcyFormat.format( dealingPrice.getMinDealtAmount() ) );
        }
        if ( dealingPrice.getMarketRange() != null && dealingPrice.getMarketRange() >= 0 )
        {
            orderResponse.setRange( userDecFormat.format( dealingPrice.getMarketRange() * fxRate.getFXRateBasis().getPipsFactor() ) );
        }

        if ( dealingPrice.getStopPrice() != null && dealingPrice.getStopPrice() > 0 )
        {
            orderResponse.setStopPrice( rateFormat.format( dealingPrice.getStopPrice() ) );
        }

        if ( dealingPrice.isStopLossTriggered() )
        {
            orderResponse.setTriggerReached( true );
        }

        List<String> tradeIds = new ArrayList<String>();
        if ( queryTradeIds )
        {
            Collection<Request> tradeRequests = orderRequest.getChildrenRequests();
            for ( Request tradeRequest : tradeRequests )
            {
                Collection<com.integral.finance.trade.Trade> tradeList = tradeRequest.getTrades();
                for ( com.integral.finance.trade.Trade trade : tradeList )
                {
                    TradeStateFacade facade = (( TradeStateFacade ) trade.getFacade( TradeStateFacade.TRADE_STATE_FACADE ));
                    if (facade.isNet()) {
                        tradeIds.add(trade.getTransactionID());
                    }
                    else if (!tradeRequest.isNettingEnabled()) {
                        if (facade.isConfirmed() || facade.isVerified()) {
                            tradeIds.add(trade.getTransactionID());
                        }
                    }
                }
            }
        }
        else if( orderRequest.getRequestAttributes().getVerifiedTradeIds() != null )
        {
        	String[] tids = orderRequest.getRequestAttributes().getVerifiedTradeIds().split(",");
        	tradeIds = Arrays.asList(tids);
        }
        orderResponse.setTradeIds( tradeIds );   //list of done trades transactionID
        orderResponse.setTradeDate(orderRequest.getCreatedBusinessDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN));

        // Outright Order Fields
       	orderResponse.setSpotRate(fxRate.getSpotRate() +"");
       	orderResponse.setForwardPoints(fxRate.getForwardPoints() +"");

        if(dealingPrice.getTenor() !=null && orderResponse.getTenor() == null)
        {
        	orderResponse.setTenor(dealingPrice.getTenor().getName());
        }

        if(dealingPrice.getFixingTenor() !=null && orderResponse.getFixingTenor() == null)
        {
        	orderResponse.setFixingTenor(dealingPrice.getFixingTenor().getName());
        }

        if(dealingPrice.getValueDate() !=null)
        {
        	orderResponse.setValueDate(dealingPrice.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN));
        }
        if(dealingPrice.getFixingDate() !=null)
        {
        	orderResponse.setFixingDate((dealingPrice.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN)));
        }

        if ( orderRequest.isOTOInactiveOrder() )
        {
        	orderResponse.setExecutionState(ExecutionState.INACTIVE);
        }

        orderResponse.setOrderMetaData( orderRequest.getOrderMetaData() );
        orderResponse.setExecutionStrategyName(orderRequest.getOrderExecutionStrategyName());
    }


    @Override
    public Order getOrderMessageForEvent( String event, WorkflowMessage wfMsg, User user , OrderStatusType status )
    {
        Order orderResponse = new Order();
        Object msgObject = wfMsg.getObject();
        Request request = msgObject instanceof Request ? ( Request ) wfMsg.getObject() : ( ( FXSingleLeg ) msgObject ).getRequest() ;

//        Request request = ( Request ) wfMsg.getObject();
        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) request.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        Currency dealtCcy = dealingPrice.getDealtCurrency();
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat( user );
        DecimalFormat dealtCcyFormat = dealtCcy.getDecimalFormat( userDecFormat.clone() );
        DecimalFormat rateFormat = FXIApiUtil.getInstance().getRateFormat(dealingPrice.getFXRate(), userDecFormat);
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat( user );

        String orderId = request.getOrderId();
        Double avgFilledPrice = ( Double ) wfMsg.getParameterValue( FixConstants.AVG_FILL_PRICE );
        Double unfilledAmount = ( Double ) wfMsg.getParameterValue( FixConstants.UNFILLED_AMT );
        Double totalFilledAmt = ( Double ) wfMsg.getParameterValue( FixConstants.FILLED_AMT );
        orderResponse.setOrderId( orderId );
        orderResponse.setTransactionId( request.getTransactionID() );
        copyParentOrderAttributes( request, orderResponse, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false );
        if( avgFilledPrice != null )
        {
        	orderResponse.setFillRate( rateFormat.format( avgFilledPrice ) );
        }
        if( totalFilledAmt != null )
        {
        	orderResponse.setFilledAmount( dealtCcyFormat.format( totalFilledAmt ) );
        }
        if( unfilledAmount != null )
        {
        	orderResponse.setUnfilledAmount( dealtCcyFormat.format( unfilledAmount ) );
        }

        orderResponse.setOrderStatus( status );

        if( status.name().equalsIgnoreCase(OrderStatusType.Amended.name()) || status.name().equalsIgnoreCase(OrderStatusType.Restated.name()) )
        {
        	if( dealingPrice.getOriginalOrderAmount() > 0.0  )
        	{
        		orderResponse.setOrigOrderAmount( userDecFormat.format( dealingPrice.getOriginalOrderAmount() ) );
        	}
        	if( dealingPrice.getOriginalOrderRate() > 0.0  )
        	{
        		orderResponse.setOrigOrderRate( rateFormat.format( dealingPrice.getOriginalOrderRate() ) );
        	}

        	orderResponse.setEventSeqId( (Long) wfMsg.getParameterValue(ISCommonConstants.EVENT_SEQ_ID) );
        	//orderResponse.setLstOrdAmt( (String) wfMsg.getParameterValue(ISCommonConstants.LAST_ORDER_AMOUNT) );
        	//orderResponse.setAvailableAmount( )
        }

        if( request.getRequestAttributes().isExexcutionSuspended() )
        {
        	orderResponse.setExecutionState(ExecutionState.STOPPED);
        }

        OrderMessage message = getOrderMessage( request, dateTimeFormat );
        message.setEventName( event );
        message.setEventDetails( getEventDetails( orderResponse , event) );
        orderResponse.setOrderMessage( message );

        return orderResponse;
    }


    private OrderMessage getOrderMessage( Request orderRequest, SimpleDateFormat dateTimeFormat )
    {
        OrderMessage message = new OrderMessage();
        message.setEventTime( dateTimeFormat.format( orderRequest.getExecutionDateTime() ) );
        message.setOrderId(orderRequest.getOrderId());
        message.setTransactionId(orderRequest.getTransactionID());
        return message;
    }

    private String getOrderSubmissionMessage( Request orderRequest, Order orderResponse )
    {
        String eventDetails = "";

        String bidOffer;
        OrderSideType orderSide;

        // if base ccy is not dealt ccy then flip the buy/sell sides
		String ccyPair = orderResponse.getInstrument();
		if ( !orderResponse.getCurrency().equals( CurrencyFactory.getBaseCurrency(ccyPair) ) )
        {
            bidOffer = orderResponse.getOrderSide() == OrderSideType.Buy ? "Offer" : "Bid";
            orderSide = orderResponse.getOrderSide() == OrderSideType.Buy ? OrderSideType.Sell : OrderSideType.Buy;
        }
        else
        {
            bidOffer = orderResponse.getOrderSide() == OrderSideType.Buy ? "Bid" : "Offer";
            orderSide = orderResponse.getOrderSide();
        }


        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) orderRequest.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        String valueDate = dealingPrice.getValueDate() != null ? dealingPrice.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN) : "";
		String fixingDate = dealingPrice.getFixingDate() != null ? dealingPrice.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN) : "";
		String fixingTenor = dealingPrice.getFixingTenor() != null ? dealingPrice.getFixingTenor().getName() : "";
		String tenor = dealingPrice.getTenor() != null ? dealingPrice.getTenor().getName() : "";

        switch ( orderResponse.getOrderType() )
        {
            case Limit:
                eventDetails = new StringBuilder( 200 ).append( "Submitted " ).append( orderResponse.getLimitRate() ).append( " " ).append( bidOffer )
                        .append( " to " ).append( orderSide ).append( ' ' ).append( orderResponse.getAmount() ).append( ' ' )
                        .append( orderResponse.getCurrency() ).append( " vs. " ).append( orderResponse.getSettledCurrency() )
                        .append( ". Value Date: " ).append(valueDate).append( ". Fixing Date: " ).append(fixingDate)
                        .append( ". Tenor: " ).append(tenor).append( ". FixingTenor: " ).append(fixingTenor).toString();
                //todo: how to find tenor?
                break;
            case Market:
                eventDetails = new StringBuilder( 200 ).append( "Submitted " ).append( orderResponse.getLimitRate() ).append( " " ).append( bidOffer )
                        .append( " to " ).append( orderSide ).append( ' ' ).append( orderResponse.getAmount() ).append( ' ' )
                        .append( orderResponse.getCurrency() ).append( " vs. " ).append( orderResponse.getSettledCurrency() )
                        .append( ". Value Date: " ).append(valueDate).append( ". Fixing Date: " ).append(fixingDate)
                        .append( ". Tenor: " ).append(tenor).append( ". FixingTenor: " ).append(fixingTenor)
                        .append( " Market Range:" ).append( orderResponse.getRange() ).toString();
                break;
            case Stop:
                String triggerType = "";
                switch ( dealingPrice.getTriggerPriceType() )
                {
                    case DealingPrice.BID:
                        triggerType = "Bid";
                        break;
                    case DealingPrice.OFFER:
                        triggerType = "Offer";
                        break;
                    case DealingPrice.MID:
                        triggerType = "Mid";
                        break;
                }

                eventDetails = new StringBuilder( 200 ).append( "Submitted " ).append( orderResponse.getStopPrice() ).append( " Stop MKT " ).append( bidOffer ).append( " to " )
                        .append( orderSide ).append( ' ' ).append( orderResponse.getAmount() ).append( " " ).append( orderResponse.getCurrency() )
                        .append( " vs. " ).append( orderResponse.getSettledCurrency() )
                        .append( ". Value Date: " ).append(valueDate).append( ". Fixing Date: " ).append(fixingDate)
                        .append( ". Tenor: " ).append(tenor).append( ". FixingTenor: " ).append(fixingTenor)
                        .append( " Trigger side: " ).append( triggerType )
                        .toString();
                //todo: how to find tenor?
                break;
            case StopLimit:
                break;
        }

        return eventDetails;
    }

    private String getTradeMessageDetails( FXSingleLeg trade, DecimalFormat dealtCcyFormat, DecimalFormat settledCcyFormat, SimpleDateFormat dateFormat, boolean isVerification )
    {
        Request request = trade.getRequest();
        Request parentRequest = trade.getRequest().getParentRequest();

        FXLegDealingPrice parentDealingPrice = ( FXLegDealingPrice ) parentRequest.getRequestPrice( ISCommonConstants.SINGLE_LEG );
        double dealtAmt = parentDealingPrice.getOriginalOrderAmount();

        FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) request.getRequestPrice(ISCommonConstants.SINGLE_LEG);
        FXLegDealingPrice fxLegAcceptedDealingPrice = ( FXLegDealingPrice ) dealingPrice.getAcceptedDealingPrice();
        int bidOfferMode = dealingPrice.getAcceptedPriceBidOfferMode();
        boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
        String provider = trade.getCounterpartyB().getOrganization().getShortName();
        String providerCpty = trade.getCounterpartyB().getShortName();
        FXPaymentParameters fxPayment = trade.getFXLeg().getFXPayment();
        boolean isDealtCcy1 = fxLegAcceptedDealingPrice.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
        double filledAmt = isDealtCcy1 ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount();
        double settledAmt = isDealtCcy1 ? fxPayment.getCurrency2Amount() : fxPayment.getCurrency1Amount();
        String dealtCcy = isDealtCcy1 ? fxPayment.getCurrency1().getShortName() : fxPayment.getCurrency2().getShortName();
        String settledCcy = isDealtCcy1 ? fxPayment.getCurrency2().getShortName() : fxPayment.getCurrency1().getShortName();
        String valueDate = dateFormat.format( dealingPrice.getValueDate().asJdkDate() );
        String buySell;
        // if base ccy is not dealt ccy then flip the buy/sell sides
        if ( !isDealtCcy1 )
        {
            buySell = isBid ? "Buy" : "Sell";
        }
        else
        {
            buySell = isBid ? "Sell" : "Buy";
        }

        StringBuilder eventDetails = new StringBuilder( 200 ).append( buySell ).append( ' ' ).append( dealtCcyFormat.format( filledAmt ) ).append( " of " ).append( dealtCcyFormat.format( dealtAmt ) )
                .append( ' ' ).append( dealtCcy ).append( " vs. " ).append( settledCcyFormat.format( settledAmt ) ).append( ' ' ).append( settledCcy ).append( " against " )
                .append( parentRequest.getTransactionID() ).append( ". Value Date: " ).append( valueDate ).append( ". From: " ).append( provider ).append( ". Counterparty " )
                .append( providerCpty );
        if(isVerification)
        {
            eventDetails.append( ". External ID: " ).append( trade.getRequest().getExternalRequestId()).append( " Price verified." ).toString();
        }
        //todo: need to verify external trade/request id?
        return eventDetails.toString();
    }

    private OrderExpiryType getTimeInForce( Request orderRequest )
    {
        int tif = orderRequest.getTimeInForce();
        switch ( tif )
        {
            case com.integral.finance.dealing.TimeInForce.IOC:
                return OrderExpiryType.IOC;
            case com.integral.finance.dealing.TimeInForce.FOK:
                return OrderExpiryType.FOK;
            case com.integral.finance.dealing.TimeInForce.GTC:
                return OrderExpiryType.GTC;
            case com.integral.finance.dealing.TimeInForce.GTD:
                return OrderExpiryType.GTD;
            case com.integral.finance.dealing.TimeInForce.DAY:
                return OrderExpiryType.DO;
            default:
                break;
        }
        return null;
    }

    private OrderType getOrderType( String requestClassification )
    {

        if ( ISCommonConstants.MAKEPRICE_CREATE_TYPE.equalsIgnoreCase( requestClassification ) )
        {
            return OrderType.Limit;
        }
        else if ( ISCommonConstants.MARKET.equalsIgnoreCase( requestClassification ) )
        {
            return OrderType.Market;
        }
        else if ( ISCommonConstants.STOP_TYPE.equalsIgnoreCase( requestClassification ) )
        {
            return OrderType.Stop;
        }
        else if ( ISCommonConstants.STOPLIMIT_TYPE.equalsIgnoreCase( requestClassification ) )
        {
            return OrderType.StopLimit;
        }

        return null;
    }

    private ArrayList<ExecutionStrategyType> getExecutionStrategies( Request request, FXLegDealingPrice requestDealingPrice )
    {
        short execInstrs = request.getExecutionFlags();
        ArrayList<ExecutionStrategyType> execStrategies = new ArrayList<ExecutionStrategyType>();

        if ( ( execInstrs & ExecutionFlags.ALLOW_QUOTE_CROSS ) > 0 )
        {
            execStrategies.add( ExecutionStrategyType.OK_TO_CROSS );
        }

        if ( ( execInstrs & ExecutionFlags.VWAP ) > 0 )
        {
            execStrategies.add( ExecutionStrategyType.PegToVWAP );
        }

        if ( ( execInstrs & ExecutionFlags.TRAILING ) > 0 )
        {
            execStrategies.add( ExecutionStrategyType.TrailingStopPeg );
        }

        if ( ( execInstrs & ExecutionFlags.BEST_PRICE ) > 0 )
        {
            execStrategies.add( ExecutionStrategyType.BestPrice );
        }

        if ( ( execInstrs & ExecutionFlags.SWEEP ) > 0 )
        {
            execStrategies.add( ExecutionStrategyType.Sweep );
        }

        if ( ( execInstrs & ExecutionFlags.TWAP ) > 0 )
        {
            execStrategies.add( ExecutionStrategyType.TWAP );
        }
        if((execInstrs & ExecutionFlags.ONLY_FA_STREAMS) > 0)
        {
            execStrategies.add(ExecutionStrategyType.FAStreamsOnly);
        }


        String spp = request.getSecondarySortPriority();
        if ( spp != null )
        {
            if ( FixConstants.PROVIDER_PRIORITY.equals( spp ) )
            {
                execStrategies.add( ExecutionStrategyType.ProviderPriority );
            }
            else if ( FixConstants.SIZE_PRIORITY.equals( spp ) )
            {
                execStrategies.add( ExecutionStrategyType.SizePriority );
            }
            else if ( FixConstants.TIME_PRIORITY.equals( spp ) )
            {
                execStrategies.add( ExecutionStrategyType.TimePriority );
            }
        }
        String clsf = request.getRequestClassification().getShortName();
        if ( ISCommonConstants.STOP_TYPE.equals( clsf ) || ISCommonConstants.STOPLIMIT_TYPE.equals( clsf ) )
        {
            if ( ( execInstrs & ExecutionFlags.AT_RATE ) > 0 )
            {
                execStrategies.add( ExecutionStrategyType.PrimaryPeg );
            }

            switch ( requestDealingPrice.getTriggerPriceType() )
            {
                case DealingPrice.BID:
                    execStrategies.add( ExecutionStrategyType.StayOnBidSide );
                    break;
                case DealingPrice.OFFER:
                    execStrategies.add( ExecutionStrategyType.StayOnOfferSide );
                    break;
                case DealingPrice.MID:
                    execStrategies.add( ExecutionStrategyType.MidPrice );
                    break;
            }
        }
        return execStrategies;
    }

    private OrderStatusType getOrderStatus( Request request )
    {
        RequestStateFacade facade = (( RequestStateFacade ) request.getFacade( RequestStateFacade.REQUEST_STATE_FACADE ));

        if ( facade != null )
        {
            if ( facade.isInitial() || facade.isPending() )
            {
                return OrderStatusType.Pending;
            }
            else if ( facade.isExpired() )
            {
                return OrderStatusType.Expired;
            }
            else if ( facade.isAccepted() )
            {
                return OrderStatusType.Accepted;
            }
            else if ( facade.isWithdrawn() || facade.isCancelled() )
            {
                return OrderStatusType.Cancelled;
            }
            else if ( facade.isDeclined() )
            {
                return OrderStatusType.Rejected;
            }
            else if ( facade.isAcceptVerified() )
            {
                return OrderStatusType.Filled;
            }
            else if ( facade.isPartial() )
            {
                return OrderStatusType.Partial;
            }
            else
            {
                log.error( "OrderTranslator.getOrderStatus. Unknown order status found for order : " + request.getOrderId() );
                return null;
            }
        }
        else
        {
            log.info( "No state facade set for the order id - " + request.getOrderId() );
            return OrderStatusType.Pending;
        }
    }
}
