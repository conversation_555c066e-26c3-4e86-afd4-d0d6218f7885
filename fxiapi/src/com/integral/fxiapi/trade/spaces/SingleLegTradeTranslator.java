package com.integral.fxiapi.trade.spaces;

import com.integral.compaction.model.MessageType;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.DealRequest;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.facade.DealStateFacade;
import com.integral.finance.dealing.fx.*;
import com.integral.finance.dealing.mifid.MiFIDUtils;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.trade.Tenor;
import com.integral.fxiapi.model.OrderSideType;
import com.integral.fxiapi.model.Trade;
import com.integral.fxiapi.model.APITrade;
import com.integral.fxiapi.model.TradeStatusType;
import com.integral.fxiapi.model.TradeType;
import com.integral.fxiapi.trade.FXITradeTranslator;
import com.integral.fxiapi.util.FXIApiUtil;
import com.integral.fxiapi.util.FormatUtils;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.service.RateRoundingService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.*;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.StringUtilC;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
public class SingleLegTradeTranslator implements FXITradeTranslator {
    private static final Log log = LogFactory.getLog(SingleLegTradeTranslator.class);

    private SingleLegTradeTranslator() {
    }

    private static class FXISingleLegTradeTranslatorHolder {
        private static final SingleLegTradeTranslator INSTANCE = new SingleLegTradeTranslator();
    }

    public static SingleLegTradeTranslator getInstance() {
        return FXISingleLegTradeTranslatorHolder.INSTANCE;
    }

    public static FXITradeTranslator getFXITradeTranslator() {
        return FXISingleLegTradeTranslatorHolder.INSTANCE;
    }

    @Override
    public Trade generateTrade(Object obj, User user) {
        try {
            if (obj == null)
                throw new NullPointerException("obj");

            if (obj instanceof SingleLegTrade)
                return generateTrade((SingleLegTrade)obj, user);

            if (obj instanceof Deal)
                return generateTrade((Deal)obj, user);

            throw new IllegalArgumentException("Unsupported: " + this.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("Failed to generate trade, ", e);
        }

        return null;
    }

	@Override
	public APITrade generateApiTrade(FXTrade fxTrade, User user) {
		throw new UnsupportedOperationException("Not relevant for SingleLegTrade");
	}

	/**
     * Transforms the SingleLegTrade to Api Trade.
     *
     * @param singleLegTrade
     * @param user
     * @return Api Trade
     * @throws NullPointerException
     */
    private Trade generateTrade(SingleLegTrade singleLegTrade, User user) throws NullPointerException {
        SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateFormat(user);
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);

        Trade tradeResponse = new Trade();

        tradeResponse.setCounterpartyALEI(singleLegTrade.getCounterpartyALEI());
        tradeResponse.setCounterpartyBLEI(singleLegTrade.getCounterpartyBLEI());
        tradeResponse.setUSI(singleLegTrade.getUSI());
        tradeResponse.setUPI(singleLegTrade.getUPI());
        tradeResponse.setSEF(singleLegTrade.isSEF());
        tradeResponse.setTradeId( getTradeTransactionId( singleLegTrade ) );
        tradeResponse.setRateid( singleLegTrade.getRefRateId() );
        tradeResponse.setMidMarket( singleLegTrade.getOrderRequest().isRiskNetDirectedOrder() && singleLegTrade.getMatchEvent().isDirectedOrderVenueMatch());
        tradeResponse.setMaker( singleLegTrade.isMaker() );
        String tradeType = ISConstantsC.TRD_CLSF_SP;
        if ( !StringUtilC.isNullOrEmpty(singleLegTrade.getClassification()) )
        {
            tradeType = singleLegTrade.getClassification();
        }
        tradeResponse.setTradeType(translateTradeType(tradeType));
        tradeResponse.setTradeDate(dateFormat.format(singleLegTrade.getTradeDate()));
        tradeResponse.setCoverTradeIds(getCoverTradeIds(singleLegTrade));

        long executionTime = singleLegTrade.getExecutionTime();
        tradeResponse.setExecutionTime(dateTimeFormat.format(executionTime));
        tradeResponse.setExecTimeValue(new Date(executionTime));

        String namespace = user.getNamespace().getShortName();
        if (namespace == null)
            throw new NullPointerException("namespace");

        SingleLegOrder singleLegOrder = singleLegTrade.getOrderRequest();
        if (singleLegOrder == null) {
            DealingModelRef<SingleLegOrder> singleLegOrderRef = singleLegTrade.getOrderRequestRef();
            if (singleLegOrderRef == null)
                throw new NullPointerException("dealingModelRef");

            String orderId = singleLegOrderRef.getUid();
            if (orderId == null)
                throw new NullPointerException("orderId");

            singleLegOrder = SingleLegTradeService.getInstance().completeSingleLegTradeWithOrder(singleLegTrade, orderId, namespace);
        }

        tradeResponse.setOrderId(singleLegOrder.get_id());

        SingleLegOrderMatch matchEvent = singleLegTrade.getMatchEvent();
        if (matchEvent == null) {
            DealingModelRef<SingleLegOrderMatch> singleLegOrderMatchRef = singleLegTrade.getMatchEventRef();
            if (singleLegOrderMatchRef == null)
                throw new NullPointerException("singleLegOrderMatchRef");

            String matchEventId = singleLegOrderMatchRef.getUid();
            if (matchEventId == null)
                throw new NullPointerException("matchEventId");

            matchEvent = SingleLegTradeService.getInstance().completeSingleLegTradeWithMatchEvent(singleLegTrade, matchEventId, namespace);
        }

        if(matchEvent.isNettingRequired() && !singleLegOrder.isShowFills()) {
            return null;
        }
        // populate bench mark rate and market snap shot form order match event
        double benchMarkRate = matchEvent.getGridMidRate();
        tradeResponse.setBenchMarkRate(benchMarkRate);
        if( singleLegTrade.getTradingParty() != null && MiFIDUtils.shouldCaptureBenchmarkMid(singleLegTrade.getLegalEntity(), singleLegTrade.getTradingParty().getLegalEntity())){
            tradeResponse.setMifidBenchmarkRate( MiFIDUtils.getFormattedBenchmarkRate( benchMarkRate ) );
        }
        String mktSnapshot = matchEvent.getMarketSnapshot();
        tradeResponse.setMktSnapshot(mktSnapshot);
        MatchEvent.MatchEventLeg matchEventLeg = matchEvent.getMatchEventLeg();
        if (matchEventLeg == null)
            throw new NullPointerException("matchEventLeg");

        TradeLeg tradeLeg = singleLegTrade.getTradeLeg();
        if (tradeLeg == null)
            throw new NullPointerException("tradeLeg");

        long valueDate = tradeLeg.getValueDate();
        tradeResponse.setValueDate(dateFormat.format(valueDate));
        tradeResponse.setvDate(DateTimeFactory.newDate(new Date(valueDate)));
        tradeResponse.setUTI(tradeLeg.getUTI());

        // if tenor is null then set it from trade leg
        if(tradeResponse.getTenor() == null)
        {
            String tenor = matchEventLeg.getTenor();
            if (tenor == null) {
                tenor = tradeLeg.getTenor();
            }
            tradeResponse.setTenor(tenor);
        }

        // add fixing date
        long fixingDateLong = tradeLeg.getFixingDate();
        if (fixingDateLong > 0) {
            String fixingDate = dateFormat.format(fixingDateLong);
            tradeResponse.setFixingDate(fixingDate);
            if(tradeResponse.getOutright() != null){
                tradeResponse.getOutright().setFixingDate(fixingDate);
            }
        }
        String fixingTenor = tradeLeg.getFixingTenor();
        if (fixingTenor != null) {
            tradeResponse.setFixingTenor(fixingTenor);
        }

        CurrencyPair currencyPair = singleLegTrade.getCurrencyPair();
        if (currencyPair == null)
            throw new NullPointerException("currencyPair");

        Currency dealtCurrency = singleLegTrade.getDealtCurrency();
        if (dealtCurrency == null)
            throw new NullPointerException("dealtCurrency");

        Currency baseCurrency = currencyPair.getBaseCurrency();
        if (baseCurrency == null)
            throw new NullPointerException("baseCurrency");

        Currency settledCurrency = (baseCurrency.getShortName().equals(dealtCurrency.getShortName())) ? currencyPair.getVariableCurrency() : currencyPair.getBaseCurrency();
        if (settledCurrency == null)
            throw new NullPointerException("settledCurrency");

        Currency variableCurrency = currencyPair.getVariableCurrency();
        if (variableCurrency == null)
            throw new NullPointerException("variableCurrency");

        FXRateBasis fxRateBasis = singleLegTrade.getFxRateBasis();
        if (fxRateBasis == null)
            throw new NullPointerException("fxRateBasis");

        //get original ccy pair , tenor , settlment type
        FXIApiUtil.getInstance().updateInstrumentDetailsWithOriginal( currencyPair.getBaseCurrency() , currencyPair.getVariableCurrency(),
                tradeResponse, dealtCurrency, fxRateBasis );

        DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());
        DecimalFormat settledCcyFormat = settledCurrency.getDecimalFormat(userDecFormat.clone());
        String dealtAmtFmt = dealtCcyFormat.format(singleLegTrade.getDealtAmount());
        String settledAmtFmt = settledCcyFormat.format(singleLegTrade.getSettledAmount());
        tradeResponse.setDealtAmount(dealtAmtFmt);
        tradeResponse.setSettledAmount(settledAmtFmt);
        tradeResponse.setFees(settledCcyFormat.format(singleLegTrade.getFees()));
        tradeResponse.setInitialSettledAmount(settledCcyFormat.format(singleLegTrade.getInitialSettledAmount()));
        tradeResponse.setBaseAmount(dealtCurrency.isSameAs(baseCurrency) ? dealtAmtFmt : settledAmtFmt);
        tradeResponse.setTermAmount(dealtCurrency.isSameAs(baseCurrency) ? settledAmtFmt : dealtAmtFmt);
        tradeResponse.setdAmt(singleLegTrade.getDealtAmount());
        tradeResponse.setsAmt(singleLegTrade.getSettledAmount());
        tradeResponse.setbAmt(dealtCurrency.isSameAs(baseCurrency) ? singleLegTrade.getDealtAmount() : singleLegTrade.getSettledAmount());
        tradeResponse.settAmt(dealtCurrency.isSameAs(baseCurrency) ? singleLegTrade.getSettledAmount() : singleLegTrade.getDealtAmount());

        boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, variableCurrency);
        DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

        double spotRate = tradeLeg.getSpotRate();
        double rate = tradeLeg.getRate();
        double forwardPoints = tradeLeg.getForwardPoints();
        tradeResponse.setSpotRate(rateFormat.format(spotRate));
        tradeResponse.setdRate( spotRate );
        tradeResponse.setForwardPoints(rateFormat.format(forwardPoints));
        tradeResponse.setRate(rateFormat.format(rate));

        tradeResponse.setCustomerOrg(singleLegOrder.getOrgShortName());
        tradeResponse.setCustomerAccount(singleLegOrder.getLegalEntityName());
        tradeResponse.setTrader(singleLegOrder.getUser().getShortName());

        LegalEntity counterpartyLegalEntity = DealingModelUtil.getCounterPartyLegalEntity( singleLegTrade );
        if (counterpartyLegalEntity == null)
            throw new NullPointerException("counterpartyLegalEntity");

        Organization tradingPartyOrg = counterpartyLegalEntity.getOrganization();
        if (tradingPartyOrg == null)
            throw new NullPointerException("tradingPartyOrg");

        tradeResponse.setCounterParty( tradingPartyOrg.getShortName() );                      // LP/Broker short name
        tradeResponse.setCounterPartyAccount(counterpartyLegalEntity.getShortName());
        tradeResponse.setCptyLongName(tradingPartyOrg.getLongName());
        tradeResponse.setPortfolioId(singleLegTrade.getPortfolioId());
        tradeResponse.setMt300Field72(singleLegTrade.getMt300Field72());
        if ( null != singleLegTrade.getPnlCurrency() && 0.0 != singleLegTrade.getPnlAmount() ){
            tradeResponse.setPnlCurrency(singleLegTrade.getPnlCurrency());
            tradeResponse.setHomeCurrencySpotRate(singleLegTrade.getHomeCurrencySpotRate());
            tradeResponse.setPnlAmount(singleLegTrade.getPnlAmount());
            tradeResponse.setBaseRate(singleLegTrade.getBaseRate());
            tradeResponse.setBaseSwapPoints(singleLegTrade.getBaseSwapPoints());
        }
        // Bug fix 59193 (Do not send cover trade id to customer)
        String cptyTradeId = "";
        if( !counterpartyLegalEntity.getOrganization().isBroker() )
        {
            cptyTradeId = singleLegTrade.getExternalReferenceId();
        }
        tradeResponse.setCptyTradeId( cptyTradeId );
        tradeResponse.setExternalRequestId(singleLegTrade.getOrderRequestClientReferenceId());

        OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
        if (requestLeg == null)
            throw new NullPointerException("requestLeg");

        OrderRequest.RequestLeg.BuySellMode buySellMode = requestLeg.getBuySellMode();
        if (buySellMode == null)
            throw new NullPointerException("buySellMode");

        boolean isBuyMode = (buySellMode == OrderRequest.RequestLeg.BuySellMode.BUY);
        tradeResponse.setOrderSide(isBuyMode ? OrderSideType.Buy : OrderSideType.Sell); // buy/sell is always set in terms of base ccy

        if (matchEvent.isDirectedOrder()) {
            tradeResponse.setMaskedLP(ISCommonConstants.VENUE_DEAL_EXECUTION_LP);
        }
        else {
            MatchEventPQ matchEventPQ = matchEvent.getMatchEventPQ();
            MatchEventPQ.QuoteDescriptor quoteDescriptor = matchEventPQ.getQuoteForTradeRequest();
            if (quoteDescriptor == null)
                throw new NullPointerException("quoteDescriptor");

            tradeResponse.setMaskedLP(quoteDescriptor.getMaskedName()); // if no masked LP used it'll be the same as realLpName
        }

        switch (singleLegTrade.getState().getName()) {
            case TSREJECTED:
                tradeResponse.setStatus(TradeStatusType.Rejected);
                tradeResponse.setRejectReason(singleLegTrade.getRejectionReason());
                break;

            case TSCANCELLED:
            case TSNETTED:
                tradeResponse.setStatus( TradeStatusType.Cancelled );
                break;
            case TSPRERATEVERIFIED:
                tradeResponse.setStatus(TradeStatusType.PreRateVerified);
                break;
            default:
                tradeResponse.setStatus(TradeStatusType.Verified);
                break;
        }

        if(tradeResponse.getStatus() == TradeStatusType.Cancelled){
            try {
                long modifiedTime = singleLegTrade.getModifiedTime();
                if (log.isDebugEnabled()) {
                    log.debug("SingleLegTradeTranslator.generateTrade: Updating Execution Time with  " + tradeResponse.getTradeId() + "|" + dateTimeFormat.format(modifiedTime));
                }
                tradeResponse.setExecutionTime(dateTimeFormat.format(modifiedTime));
                tradeResponse.setExecTimeValue(new Date(modifiedTime));
            }catch (Exception ex){
                log.error("SingleLegTradeTranslator.generateTrade: error while updating execution time with modified date Time", ex);
            }
        }
        // populating order submission field in header object here.
        // TODO: remove header fields from Order objects
        if (StringUtils.isNotBlank(tradeResponse.getExecutionTime())) {
            try {
                tradeResponse.getHeader().setSendingTime(dateFormat.parse(tradeResponse.getExecutionTime()).getTime());
            } catch (ParseException e) {
                log.error("Failed to generate order, ", e);
            }
        }

       if(singleLegTrade.getCoveredTrade() != null){
           if(singleLegTrade.getCoveredTrade().getUser() != null) {
               tradeResponse.getBrokerCDQ().setCoveredDealUser(singleLegTrade.getCoveredTrade().getUser().getShortName());
           }
            tradeResponse.getBrokerCDQ().setCoveredDealOrderId(singleLegTrade.getCoveredTrade().getOrderId());
           tradeResponse.getBrokerCDQ().setCoveredDealId(singleLegTrade.getCoveredTrade().getTradeId());
       }
        return tradeResponse;
    }

    private String getTradeTransactionId( SingleLegTrade singleLegTrade ) {
        if( singleLegTrade.isMaker() ){
            return singleLegTrade.getExternalReferenceId();
        }
        return singleLegTrade.get_id();
    }

    private void updateFarLegData(FXSwapDeal deal, double nearLegForwardPoints, Trade tradeResponse, User user)
    {
    	FXDealLeg fxDealLeg = ((FXSwapDeal)deal).getFarDealLeg();
        tradeResponse.setFarTenor(fxDealLeg.getTenor());
        Tenor tenor = fxDealLeg.getFixingTenor();
        if(null != tenor) {
            tradeResponse.setFarFixingTenor(tenor.getName());
        }
        tradeResponse.setFarUSI(fxDealLeg.getUSI());
        tradeResponse.setFarUTI(fxDealLeg.getUTI());
        tradeResponse.setFarLegISIN( fxDealLeg.getISIN() );
        tradeResponse.setISINLinkId(deal.getISINLinkId());

        tradeResponse.getMifidFields().setIsinFar( fxDealLeg.getISIN() );

        tradeResponse.getBrokerCDQ().setFarMarketSpotRate(fxDealLeg.getMarketSpotRate());
        tradeResponse.getBrokerCDQ().setFarMarketRate(fxDealLeg.getMarketRate());
        tradeResponse.getBrokerCDQ().setFarMarketForwardPoints(fxDealLeg.getMarketForwardPoints());

        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);

        FXRateConvention fxRateConvention = fxDealLeg.getFxRateConvention();
        if (fxRateConvention == null)
        {
            throw new NullPointerException("fxRateConvention; tid="+deal.getTransactionId());
        }
        FXRateBasis fxRateBasis = fxRateConvention.getFXRateBasis(fxDealLeg.getCurrencyPair());
        if (fxRateBasis == null)
        {
            throw new NullPointerException("fxRateBasis");
        }
        Currency variableCurrency = fxDealLeg.getVariableCurrency();
        if (variableCurrency == null)
        {
            throw new NullPointerException("variableCurrency");
        }
        Currency dealtCurrency = fxDealLeg.getDealtCurrency();
        if (dealtCurrency == null)
        {
            throw new NullPointerException("dealtCurrency");
        }
        Currency baseCurrency = fxDealLeg.getBaseCurrency();
        if (baseCurrency == null)
        {
            throw new NullPointerException("baseCurrency");
        }
        Currency settledCurrency = fxDealLeg.getSettledCurrency();
        if (settledCurrency == null)
        {
            throw new NullPointerException("settledCurrency");
        }

        boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, variableCurrency);
        DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);
        double spotRate = fxDealLeg.getSpotRate();
        double forwardPoints = fxDealLeg.getForwardPoints();
        tradeResponse.setFarForwardPoints(rateFormat.format(forwardPoints));
        tradeResponse.setFarRate(rateFormat.format(spotRate + forwardPoints));
        tradeResponse.setfRate( spotRate+ forwardPoints );

        boolean  isBaseDealt = fxDealLeg.isDealtCurrency1();
        DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());
        DecimalFormat settledCcyFormat = settledCurrency.getDecimalFormat(userDecFormat.clone());

        if(!isBaseDealt){
            dealtCcyFormat = settledCurrency.getDecimalFormat(userDecFormat.clone());
            settledCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());
        }

        String baseAmt = dealtCcyFormat.format(fxDealLeg.getBaseAmount());
        String varAmt  = dealtCcyFormat.format(fxDealLeg.getVariableAmount());

        tradeResponse.setFarDealtAmount(isBaseDealt? baseAmt:varAmt);
        tradeResponse.setFarSettledAmount(isBaseDealt? varAmt:baseAmt);
        tradeResponse.setFarBaseAmount(baseAmt);
        tradeResponse.setFarTermAmount(varAmt);
        tradeResponse.setfDAmt( isBaseDealt? fxDealLeg.getBaseAmount():fxDealLeg.getVariableAmount() );
        tradeResponse.setfSAmt( isBaseDealt? fxDealLeg.getVariableAmount():fxDealLeg.getBaseAmount() );
        tradeResponse.setfBAmt( fxDealLeg.getBaseAmount() );
        tradeResponse.setfTAmt( fxDealLeg.getVariableAmount() );

        SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateFormat(user);
        IdcDate valueDate = fxDealLeg.getValueDate();
        if (valueDate == null)
        {
            throw new NullPointerException("valueDate");
        }
        tradeResponse.setFarValueDate(dateFormat.format(valueDate.asJdkDate().getTime()));
        tradeResponse.setFarVDate( valueDate );
        IdcDate fixingDate = fxDealLeg.getFixingDate();
        if(fixingDate != null){
            tradeResponse.setFarFixingDate(dateFormat.format(fixingDate.asJdkDate().getTime()));
        }
        boolean isBuyMode = (fxDealLeg.getAcceptedBidOfferMode() == DealingPrice.OFFER);
        tradeResponse.setFarSide(isBuyMode ? OrderSideType.Buy : OrderSideType.Sell);
        tradeResponse.setSwapPoints(rateFormat.format(forwardPoints - nearLegForwardPoints));
    }

    /**
     * Transforms the CDQ deal to Api Trade.
     *
     * @param deal
     * @param user
     * @return Api Trade
     * @throws NullPointerException
     */
    private Trade generateTrade(Deal deal, User user) throws NullPointerException {
        if ( deal.isOrderBasedNettingEnabled() && !deal.isShowPreNettingFills() ) {
            return null;
        }
        Trade tradeResponse = new Trade();
        FXDealLeg fxDealLeg;
        if(deal instanceof FXSingleLegDeal)
        {
        	fxDealLeg = ((FXSingleLegDeal)deal).getFXDealLeg();
        }
        else //Assert (deal instanceof FXSwapDeal)
        {
        	fxDealLeg = ((FXSwapDeal)deal).getNearDealLeg();
        	updateFarLegData((FXSwapDeal)deal, fxDealLeg.getForwardPoints(),tradeResponse, user);
        }
        if (fxDealLeg == null)
            throw new NullPointerException("fxDealLeg");

        SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateFormat(user);
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);

        tradeResponse.setCounterpartyALEI(deal.getCounterpartyALEI());
        tradeResponse.setCounterpartyBLEI( deal.getCounterpartyBLEI() );
        tradeResponse.setUSI( fxDealLeg.getUSI() );
        tradeResponse.setUTI( fxDealLeg.getUTI() );
        tradeResponse.setNearLegISIN( fxDealLeg.getISIN() );
        tradeResponse.setUPI(deal.getUPI());
        tradeResponse.setSEF(deal.isSEF());
        tradeResponse.setMifidBenchmarkRate( deal.getBenchmarkRate() );
        tradeResponse.setRegulatory( deal.getRegulatory() );

        tradeResponse.getSefFields().setUpi(deal.getUPI());
        tradeResponse.getSefFields().setUsi(fxDealLeg.getUSI());
        tradeResponse.getSefFields().setCptyALEI(deal.getCounterpartyALEI());
        tradeResponse.getSefFields().setCptyBLEI(deal.getCounterpartyBLEI());
        tradeResponse.getMifidFields().setIsin( fxDealLeg.getISIN() );
        tradeResponse.getMifidFields().setRegulatoryFields( deal.getRegulatory() );
        if ( null != deal.getPnlCurrency() && 0.0 != deal.getPnlAmount()){
            tradeResponse.setPnlCurrency(deal.getPnlCurrency());
            tradeResponse.setHomeCurrencySpotRate(deal.getHomeCurrencySpotRate());
            tradeResponse.setPnlAmount(deal.getPnlAmount());
            tradeResponse.setBaseRate(deal.getBaseRate());
            tradeResponse.setBaseSwapPoints(deal.getBaseSwapPoints());
        }

        tradeResponse.setTradeId(deal.getTransactionId());
        tradeResponse.setMaker( !deal.isTaker() );
        tradeResponse.setTradeType(translateTradeType(deal.getTradeClassification().getShortName()));
        tradeResponse.setTradeDate(dateFormat.format(deal.getTradeDate().asJdkDate().getTime()));
        tradeResponse.setCoverTradeIds(getCoverTradeIds(deal));

        Date executionDate = deal.getExecutionDate();
        if (executionDate == null)
            throw new NullPointerException("executionDate");

        tradeResponse.setExecTimeValue(executionDate);
        tradeResponse.setExecutionTime(dateTimeFormat.format(executionDate.getTime()));

        String namespace = user.getNamespace().getShortName();
        if (namespace == null)
            throw new NullPointerException("namespace");

        tradeResponse.setOrderId(deal.getOrderId());
        tradeResponse.setTenor(fxDealLeg.getTenor());
        if(fxDealLeg.getVariableTenor() != null){
        	tradeResponse.setVariableTenor(fxDealLeg.getVariableTenor());
        }

        IdcDate valueDate = fxDealLeg.getValueDate();
        if (valueDate == null)
            throw new NullPointerException("valueDate");

        tradeResponse.setValueDate(dateFormat.format(valueDate.asJdkDate().getTime()));
        tradeResponse.setvDate( valueDate );
        
        if(fxDealLeg.getVariableValueDate() != null){
        	 tradeResponse.setVariableValueDate(dateFormat.format(fxDealLeg.getVariableValueDate().asJdkDate().getTime()));
             tradeResponse.setvVDate( fxDealLeg.getVariableValueDate() );
             tradeResponse.setSubTradeType(fxDealLeg.getSubTradeType());
        }

        Currency dealtCurrency = fxDealLeg.getDealtCurrency();
        if (dealtCurrency == null)
            throw new NullPointerException("dealtCurrency");

        Currency baseCurrency = fxDealLeg.getBaseCurrency();
        if (baseCurrency == null)
            throw new NullPointerException("baseCurrency");

        Currency settledCurrency = fxDealLeg.getSettledCurrency();
        if (settledCurrency == null)
            throw new NullPointerException("settledCurrency");

        Currency variableCurrency = fxDealLeg.getVariableCurrency();
        if (variableCurrency == null)
            throw new NullPointerException("variableCurrency");

        FXRateConvention fxRateConvention = fxDealLeg.getFxRateConvention();
        if (fxRateConvention == null)
            throw new NullPointerException("fxRateConvention; tid="+deal.getTransactionId());

        FXRateBasis fxRateBasis = fxRateConvention.getFXRateBasis(fxDealLeg.getCurrencyPair());
        if (fxRateBasis == null)
            throw new NullPointerException("fxRateBasis");

        //get original ccy pair , tenor , settlment type
        FXIApiUtil.getInstance().updateInstrumentDetailsWithOriginal( baseCurrency , variableCurrency,
                tradeResponse, dealtCurrency, fxRateBasis );

        DecimalFormat baseCcyFormat  = baseCurrency.getDecimalFormat(userDecFormat.clone());
        DecimalFormat varCcyFormat = variableCurrency.getDecimalFormat(userDecFormat.clone());

        String baseAmt = baseCcyFormat.format(fxDealLeg.getBaseAmount());
        String varAmt  = varCcyFormat.format(fxDealLeg.getVariableAmount());
        boolean  isBaseDealt = fxDealLeg.isDealtCurrency1();
        tradeResponse.setDealtAmount(isBaseDealt? baseAmt:varAmt);
        tradeResponse.setSettledAmount(isBaseDealt? varAmt:baseAmt);
        tradeResponse.setBaseAmount(baseAmt);
        tradeResponse.setTermAmount(varAmt);
        tradeResponse.setdAmt(fxDealLeg.getDealtAmount());
        tradeResponse.setsAmt(fxDealLeg.getSettledAmount());
        tradeResponse.setbAmt(dealtCurrency.isSameAs(baseCurrency) ? fxDealLeg.getDealtAmount() : fxDealLeg.getSettledAmount());
        tradeResponse.settAmt(dealtCurrency.isSameAs(baseCurrency) ? fxDealLeg.getSettledAmount() : fxDealLeg.getDealtAmount());
        tradeResponse.setChannel(deal.getTradeChannel());

        boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, variableCurrency);
        DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

        double spotRate = fxDealLeg.getSpotRate();
        double forwardPoints = fxDealLeg.getForwardPoints();
        tradeResponse.setSpotRate(rateFormat.format(spotRate));
        tradeResponse.setdRate( spotRate );
        tradeResponse.setForwardPoints(rateFormat.format(forwardPoints));
        tradeResponse.setRate(rateFormat.format(spotRate + forwardPoints));

        Organization cptyAOrg = deal.getCounterpartyAOrg();
        tradeResponse.setCustomerOrg(cptyAOrg.getShortName());
        tradeResponse.setCustomerAccount(deal.getCounterpartyA().getShortName());
        tradeResponse.setFees(varCcyFormat.format(fxDealLeg.getFees()));
        tradeResponse.setInitialSettledAmount(varCcyFormat.format(fxDealLeg.getInitialSettledAmount()));

        if(deal.getUser() != null)
            tradeResponse.setTrader(deal.getUser().getShortName());
        else
            tradeResponse.setTrader("");

        if (valueDate != null) {
			if(fxRateBasis.isNonDeliverable()) {
                IdcDate fixingDate = fxDealLeg.getFixingDate();
                if(fixingDate == null)
                {
                    fixingDate = fxRateBasis.getFixingDate( valueDate );
                }

				if (fixingDate != null) {
                    String fixingDateStr = dateFormat.format(fixingDate.asJdkDate().getTime());
                    tradeResponse.setFixingDate(fixingDateStr);
					if(tradeResponse.getOutright() != null){
					    tradeResponse.getOutright().setFixingDate(fixingDateStr);
                    }
				}
			}
        }

        LegalEntity tradingPartyLe = deal.getCounterpartyB();
        if (tradingPartyLe == null)
            throw new NullPointerException("tradingPartyLe");

        Organization tradingPartyOrg = tradingPartyLe.getOrganization();
        if (tradingPartyOrg == null)
            throw new NullPointerException("tradingPartyOrg");

        tradeResponse.setCounterParty(tradingPartyOrg.getShortName());                      // LP/Broker short name
        tradeResponse.setCounterPartyAccount(tradingPartyLe.getShortName());
        tradeResponse.setCptyLongName(tradingPartyOrg.getLongName());

        tradeResponse.getCptyB().setCptyOrg(tradingPartyOrg.getShortName());
        tradeResponse.getCptyB().setCptyAccount(tradingPartyLe.getShortName());
        tradeResponse.getCptyB().setMaskedLP(deal.getMaskedLP());

        //Broker CDQ Fields
        tradeResponse.getBrokerCDQ().setMarketSpotRate(fxDealLeg.getMarketSpotRate());
        tradeResponse.getBrokerCDQ().setMarketRate(fxDealLeg.getMarketRate());
        tradeResponse.getBrokerCDQ().setMarketForwardPoints(String.valueOf(fxDealLeg.getMarketForwardPoints()));

        if(StringUtils.isNotEmpty(deal.getCoveredDealId())) {
            tradeResponse.getBrokerCDQ().setCoveredDealId(deal.getCoveredDealId());
        }

        if(deal.getCoveredDealUser() != null){
            tradeResponse.getBrokerCDQ().setCoveredDealUser(deal.getCoveredDealUser().getShortName());
        }

        if(StringUtils.isNotEmpty( deal.getCoveredDealOrderId()) ){
            tradeResponse.getBrokerCDQ().setCoveredDealOrderId(deal.getCoveredDealOrderId());
        }

        // Setting Outright fields for CDQ Data

        tradeResponse.getOutright().setTenor(tradeResponse.getTenor());

        tradeResponse.getOutright().setForwardPoints(tradeResponse.getForwardPoints());
        tradeResponse.getOutright().setSpotRate(tradeResponse.getSpotRate());
        DealRequest dealRequest = deal.getDealRequest();

        if (dealRequest == null)
            throw new NullPointerException("dealRequest");

        // Bug fix 59193 (Do not send cover trade id to customer)
        String cptyTradeId = "";
        if(!tradingPartyOrg.isBroker()) {
            cptyTradeId = deal.getCounterpartyBTradeId();
        }
        tradeResponse.setCptyTradeId(cptyTradeId);
        tradeResponse.setExternalRequestId(dealRequest.getExternalRequestId());

        boolean isBuyMode = (fxDealLeg.getAcceptedBidOfferMode() == DealingPrice.OFFER);
        tradeResponse.setOrderSide(isBuyMode ? OrderSideType.Buy : OrderSideType.Sell);     // buy/sell is always set in terms of base ccy

        tradeResponse.setMaskedLP(deal.getMaskedLP());                         // if no masked LP used it'll be the same as realLpName
        setTradeStatus(tradeResponse, deal);

        if(tradeResponse.getStatus() == TradeStatusType.Cancelled) {
            try {
                IdcDateTime modifiedDateTime = DateTimeFactory.newDateTime(deal.getModifiedTime());

                if(log.isDebugEnabled()){
                    log.debug("SingleLegTradeTranslator.generateTrade: updating execution time with modified timestamp "+tradeResponse.getTradeId() +"|"+  dateTimeFormat.format(modifiedDateTime));
                }
                tradeResponse.setExecTimeValue(modifiedDateTime.asJdkDate());
                tradeResponse.setExecutionTime(dateTimeFormat.format(modifiedDateTime.asJdkDate ()));
            }catch (Exception ex1)
            {
                log.error("SingleLegTradeTranslator.generateTrade: Error occurred while updating execution time with modifiedDateTime. Txn_id: "+ deal.getTransactionId() + ", modifiedTime: "+ deal.getModifiedTime() , ex1);
            }
        }

        if(log.isDebugEnabled())
        {
            log.debug("deal.get_id()  " + deal.get_id() + " deal.getStatus() = " + deal.getStatus() + "deal.getState() ==" + deal.getState());
        }
        MessageType msgType = getMesgTypeFromTradeStatus(tradeResponse.getStatus());
        if(msgType == null)
        {
            log.warn("CDQ : This trade "  + tradeResponse.getTradeId() +  " didnt have status in the database ");
        }
        tradeResponse.getHeader().setMsgType(msgType);
        if (StringUtils.isNotBlank(tradeResponse.getExecutionTime()))
        {
            try
            {
                tradeResponse.getHeader().setSendingTime(dateTimeFormat.parse(tradeResponse.getExecutionTime()).getTime());
            }
            catch (ParseException e)
            {
                log.error("Failed to generate order, ", e);
            }
        }

        tradeResponse.setPricingType(deal.getPricingType());
        tradeResponse.setNote(deal.getNote());
        tradeResponse.setPortfolioId(deal.getPortfolioId());
        tradeResponse.setMt300Field72(deal.getMt300Field72());
        if ( !StringUtilC.isNullOrEmpty(deal.getPnlCurrency()) && 0.0 != deal.getPnlAmount() ){
            tradeResponse.setPnlCurrency(deal.getPnlCurrency());
            tradeResponse.setHomeCurrencySpotRate(deal.getHomeCurrencySpotRate());
            tradeResponse.setPnlAmount(deal.getPnlAmount());
            tradeResponse.setBaseRate(deal.getBaseRate());
            tradeResponse.setBaseSwapPoints(deal.getBaseSwapPoints());
        }

        return tradeResponse;
    }

    private MessageType getMesgTypeFromTradeStatus(TradeStatusType tradeStatusType)
    {
        if(tradeStatusType == null)
        {
            log.warn("CDQ : the deal is not qualified to have a msgType in GroupHeader");
            return null;
        }
        switch(tradeStatusType)
        {
            case Verified:
                return MessageType.TRADE_VERIFIED;
            case Rejected:
            case Cancelled:
                return MessageType.TRADE_CANCELLED;
        }

        return MessageType.TRADE_VERIFIED;
    }

    private void setTradeStatus(Trade tradeResponse, Deal deal) throws NullPointerException {
        DealStateFacade dealStateFacade = (DealStateFacade)deal.getFacade(DealStateFacade.FACADE_NAME);
        if (dealStateFacade == null)
            throw new NullPointerException("dealStateFacade");

        if (dealStateFacade.isVerified()) {
            tradeResponse.setStatus(TradeStatusType.Verified);
            return;
        }

        if (dealStateFacade.isNet()) {
            tradeResponse.setStatus(TradeStatusType.Verified);
            return;
        }

        if (dealStateFacade.isCancelled()) {
            tradeResponse.setStatus(TradeStatusType.Cancelled);
            return;
        }

        if (dealStateFacade.isFilled()) {
            tradeResponse.setStatus(TradeStatusType.Verified);          // at least verified before filled
            return;
        }

        if (dealStateFacade.isDelayedVerification()) {
            tradeResponse.setStatus(TradeStatusType.Verified);          // finally got verified after the delay
            return;
        }

        if (dealStateFacade.isRejected()) {
            tradeResponse.setRejectReason(deal.getRejectReason());
            tradeResponse.setStatus(TradeStatusType.Rejected);
            return;
        }

        if (dealStateFacade.isSubmitError() || dealStateFacade.isSubmitFailure()) {
            tradeResponse.setRejectReason(ISCommonConstants.DEAL_STATE_SUBMITFAILURE);
            tradeResponse.setStatus(TradeStatusType.Rejected);
            return;
        }

        // TODO no corresponding TradeStatusType for the following:
//        if (dealStateFacade.isCreated()) {
//            tradeResponse.setStatus(TradeStatusType.?);
//            return;
//        }
//
//        if (dealStateFacade.isSubmitSuccess()) {
//            tradeResponse.setStatus(TradeStatusType.?);
//            return;
//        }
//
//        if (dealStateFacade.isPartialFilled()) {
//            tradeResponse.setStatus(TradeStatusType.?);
//            return;
//        }
//
//        if (dealStateFacade.isPendingVerification()) {
//            tradeResponse.setStatus(TradeStatusType.?);
//            return;
//        }
    }

    private TradeType translateTradeType(String classification) {

        if ( ISConstantsC.TRD_CLSF_SP.equals( classification ) )
        {
            return TradeType.Spot;
        }
        else if ( ISConstantsC.TRD_CLSF_OR.equals( classification ) )
        {
            return TradeType.Outright;
        }
        else if ( ISConstantsC.TRD_CLSF_FXNDF.equals( classification ) )
        {
            return TradeType.NDF;
        }
        else if ( ISConstantsC.TRD_CLSF_SWAP.equals( classification ) || classification.equals("FXSwap"))
        {
            return TradeType.Swap;
        }
        else if ( ISConstantsC.TRD_CLSF_FWD.equals( classification ) )
        {
            return TradeType.FwdFwd;
        }
        else if ( ISConstantsC.TRD_CLSF_FXNDF_SWAP.equals( classification ))
        {
            return TradeType.NDFSwap;
        }
        return null;

    }

    private ArrayList<String> getCoverTradeIds(SingleLegTrade singleLegTrade) {
        ArrayList<String> coverTrdIds = new ArrayList<String>();
        if (singleLegTrade == null)
            return coverTrdIds;

        String coverTradeIds = singleLegTrade.getCoverTradeIds();
        if (coverTradeIds == null || coverTradeIds.equals(""))
            return coverTrdIds;

        String[] tokenizedIds = coverTradeIds.split("" + ISCommonConstants.COVER_TRADE_ID_SEPARATOR);
        for (String id : tokenizedIds) {
            coverTrdIds.add(id);
        }

        return coverTrdIds;
    }

    private ArrayList<String> getCoverTradeIds(Deal deal) {
        ArrayList<String> coverTrdIds = new ArrayList<String>();
        if (deal == null)
            return coverTrdIds;

        String coverTradeIds = deal.getCoverTradeIds();
        if (coverTradeIds == null || coverTradeIds.equals(""))
            return coverTrdIds;

        String[] tokenizedIds = coverTradeIds.split("" + ISCommonConstants.COVER_TRADE_ID_SEPARATOR);
        for (String id : tokenizedIds) {
            coverTrdIds.add(id);
        }
        return coverTrdIds;
    }
}
