package com.integral.admin.api.legalentity;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.InstanceCreator;
import com.google.gson.reflect.TypeToken;
import com.integral.admin.CommonErrorCodes;
import com.integral.admin.api.model.referencedata.externalsystem.ExternalSystemModel;
import com.integral.admin.api.model.referencedata.legalentity.LegalEntityModel;
import com.integral.admin.auth.ApiAuthorization;
import com.integral.admin.constants.CommonConstants;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.admin.utils.organization.UserUtil;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.fxiapi.BaseResponse;
import com.integral.fxiapi.Status;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.user.Organization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Controller
@RequestMapping(value = "/legalentities")
public class LegalEntityControllerC {

    private Log log = LogFactory.getLog(LegalEntityControllerC.class);

    @Autowired
    private LegalEntityService service;

    /**
     * @param orgName Organization
     * @param httpResponse HttpServletResponse object
     * @return Get all LegalEntities of the Organization
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker, 
            CommonConstants.SYSADMINMakerChecker},
        permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getLegalEntityForOrganization(@PathVariable("orgName") String orgName,
                                               final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                log.warn("Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            final List<LegalEntityModel> legalEntityData = service.getLegalEntities(org);
            return new BaseResponse(Status.OK) {
                public List<LegalEntityModel> getLegalEntityData() { return legalEntityData;}
            };
        } catch (Throwable th) {
            log.warn("getLegalEntityForOrganization: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * @param orgName Organization
     * @param inputJson Json input with only those fields that need to be modified
     * @param httpResponse HttpServletResponse object
     * @return The LegalEntity Details for the given orgName
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker, 
            CommonConstants.SYSADMINMakerChecker},
        permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}", method = RequestMethod.POST)
    public @ResponseBody
    BaseResponse createLegalEntityForOrganization(@PathVariable("orgName") String orgName,
                                                  @RequestBody String inputJson,
                                                  final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                log.warn("Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            Gson gson = new Gson();
            final LegalEntityModel leData = gson.fromJson(inputJson, LegalEntityModel.class);
            String leName = leData.getShortName();
            LegalEntity existingLe = org.getLegalEntity(leName);
            if (existingLe != null) {
                if (existingLe.isActive()) {
                    log.warn("LegalEntity " + leName + " in Organization " + orgName + " already exists");
                    httpResponse.setStatus(HttpServletResponse.SC_CONFLICT);
                    return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_EXISTS);
                }
                LegalEntityModel existingLeModel = service.getLegalEntity(orgName, leData.getShortName());
                InstanceCreator<LegalEntityModel> creator = service.createInstanceCreatorForUpdate(existingLeModel);
                Gson gsonUpdater = new GsonBuilder().registerTypeAdapter(LegalEntityModel.class, creator).create();
                LegalEntityModel entityForUpdate = gsonUpdater.fromJson(inputJson, LegalEntityModel.class);
                entityForUpdate.setStatus(Entity.ACTIVE_STATUS);

                if (service.updateLegalEntity(orgName, leName, entityForUpdate)) {
                    final LegalEntityModel latest = service.getLegalEntity(orgName, leName);
                    return new BaseResponse(Status.OK) {
                        public LegalEntityModel getLegalEntityData() {
                            return latest;
                        }
                    };
                }
            }
            if (service.createLegalEntity(orgName, leData)) {
                final LegalEntityModel latest = service.getLegalEntity(orgName, leData.getShortName());
                httpResponse.setStatus(HttpServletResponse.SC_CREATED);
                return new BaseResponse(Status.OK) {
                    public LegalEntityModel getLegalEntityData() {
                        return latest;
                    }
                };
            }
        } catch (Throwable th) {
            log.warn("createLegalEntityForOrganization: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * @param orgName Organization
     * @param leName LegalEntity
     * @param httpResponse HttpServletResponse object
     * @return The LegalEntity Details for the given orgName
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker,
            CommonConstants.SYSADMINMakerChecker},
        permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{leName}", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getLegalEntityForOrganization(@PathVariable("orgName") String orgName,
                                               @PathVariable("leName") String leName,
                                               final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                log.warn("Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            final LegalEntityModel legalEntityData = service.getLegalEntity(org, leName);
            if (legalEntityData == null) {
                log.warn("LegalEntity " + leName + " in Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.LE_NOT_FOUND);
            }
            return new BaseResponse(Status.OK) {
                public LegalEntityModel getLegalEntityData() { return legalEntityData;}
            };
        } catch (Throwable th) {
            log.warn("getLegalEntityForOrganization: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * @param orgName Organization
     * @param leName LegalEntity
     * @param httpResponse HttpServletResponse object
     * @return The LegalEntity Details for the given orgName
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker,
            CommonConstants.SYSADMINMakerChecker},
        permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{leName}", method = RequestMethod.PUT)
    public @ResponseBody
    BaseResponse updateLegalEntityForOrganization(@PathVariable("orgName") String orgName,
                                                  @PathVariable("leName") String leName,
                                                  @RequestBody String inputJson,
                                                  final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                log.warn("Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }

            LegalEntityModel existingLeModel = service.getLegalEntity(org, leName);
            if (existingLeModel == null) {
                log.warn("LegalEntity " + leName + " in Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.LE_NOT_FOUND);
            }
            InstanceCreator<LegalEntityModel> creator = service.createInstanceCreatorForUpdate(existingLeModel);
            Gson gson = new GsonBuilder().registerTypeAdapter(LegalEntityModel.class, creator).create();
            LegalEntityModel entityForUpdate = gson.fromJson(inputJson, LegalEntityModel.class);

            if (service.updateLegalEntity(orgName, leName, entityForUpdate)) {
                final LegalEntityModel latest = service.getLegalEntity(orgName, entityForUpdate.getShortName());
                return new BaseResponse(Status.OK) {
                    public LegalEntityModel getLegalEntityData() {
                        return latest;
                    }
                };
            }
        } catch (Throwable th) {
            log.warn("updateLegalEntityForOrganization: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * @param orgName Organization
     * @param leName LegalEntity
     * @param httpResponse HttpServletResponse object
     * @return HttpResponse showing the result of inactivation
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker,
            CommonConstants.SYSADMINMakerChecker},
        permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{leName}", method = RequestMethod.DELETE)
    public @ResponseBody
    BaseResponse deleteLegalEntityForOrganization(@PathVariable("orgName") String orgName,
                                                  @PathVariable("leName") String leName,
                                                  final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                log.warn("Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            LegalEntityModel leModel = service.getLegalEntity(org, leName);
            if (leModel == null) {
                log.warn("LegalEntity " + leName + " in Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.LE_NOT_FOUND);
            }
            leModel.setStatus(Entity.PASSIVE_STATUS);
            if (!service.updateLegalEntity(org, leName, leModel)) {
                log.warn("Inactivating LegalEntity " + leName + " of Organization " + orgName + " Failed");
                httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
            }
            httpResponse.setStatus(HttpServletResponse.SC_NO_CONTENT);
            return new BaseResponse(Status.OK);
        } catch (Throwable th) {
            log.warn("deleteLegalEntityForOrganization: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * @param orgName Organization
     * @param leName LegalEntity
     * @param httpResponse HttpServletResponse object
     * @return Get ExternalSystem Details of LegalEntity
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker,
            CommonConstants.SYSADMINMakerChecker},
            permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{leName}/ExternalSystemIds", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getExternalSystemForLegalEntity(@PathVariable("orgName") String orgName,
                                                 @PathVariable("leName") String leName,
                                                 final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                log.warn("Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            LegalEntity orgLe = org.getLegalEntity(leName);
            if (orgLe == null) {
                log.warn("LegalEntity " + leName + " in Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.LE_NOT_FOUND);
            }
            final List<ExternalSystemModel> externalSystemData = service.getExternalSystemIds(orgLe, null);
            return new BaseResponse(Status.OK) {
                public List<ExternalSystemModel> getExternalSystemData() {
                    return externalSystemData;
                }
            };
        } catch (Throwable th) {
            log.warn("getExternalSystemForLegalEntity: Exception ", th);
        }

        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * @param orgName Organization
     * @param leName LegalEntity
     * @param inputJson Json input with only those fields that need to be modified
     * @param httpResponse HttpServletResponse object
     * @return Updated ExternalSystem Details of LegalEntity
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker,
            CommonConstants.SYSADMINMakerChecker},
            permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{leName}/ExternalSystemIds", method = RequestMethod.PUT)
    public @ResponseBody
    BaseResponse updateExternalSystemForLegalEntity(@PathVariable("orgName") String orgName,
                                                    @PathVariable("leName") String leName,
                                                    @RequestBody String inputJson,
                                                    final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                log.warn("Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
           LegalEntity orgLe = org.getLegalEntity(leName);
            if (orgLe == null) {
                log.warn("LegalEntity " + leName + " in Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.LE_NOT_FOUND);
            }

            Gson gson = new Gson();
            Type collectionType = new TypeToken<Collection<ExternalSystemModel>>(){}.getType();
            List<ExternalSystemModel> newExtSystems = gson.fromJson(inputJson,collectionType);

            if (service.updateExternalSystemIds(orgLe, newExtSystems, null)) {
                return new BaseResponse(Status.OK);
            }
        } catch (Throwable th) {
            log.warn("updateExternalSystemForLegalEntity: Exception ", th);
        }

        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * @param orgName Organization
     * @param leName LegalEntity
     * @param extSystemId ExternalSystem
     * @param httpResponse HttpServletResponse object
     * @return Value of given ExternalSystem
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker,
            CommonConstants.SYSADMINMakerChecker},
            permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{leName}/ExternalSystemIds/{extSystemId}", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getExternalSystemForLegalEntity(@PathVariable("orgName") String orgName,
                                                 @PathVariable("leName") String leName,
                                                 @PathVariable("extSystemId") String extSystemId,
                                                 final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            LegalEntity orgLe = org.getLegalEntity(leName);
            if (orgLe == null) {
                log.warn("LegalEntity " + leName + " in Organization " + orgName + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.LE_NOT_FOUND);
            }
            final List<ExternalSystemModel> externalSystemData = service.getExternalSystemIds(orgLe, extSystemId);
            return new BaseResponse(Status.OK) {
                public List<ExternalSystemModel> getExternalSystemData() {
                    return externalSystemData;
                }
            };
        } catch (Throwable th) {
            log.warn("getExternalSystemForLegalEntity: Exception ", th);
        }

        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * @param orgName Organization
     * @param leName LegalEntity
     * @param extSystemId ExternalSystem
     * @param extSystemValue String input with the value for the ExtSystem
     * @param httpResponse HttpServletResponse object
     * @return HttpResponse showing the result of updation
     */
    @ApiAuthorization(roles = {CommonConstants.SYSADMIN, CommonConstants.SYSADMINMaker, CommonConstants.SYSADMINChecker,
            CommonConstants.SYSADMINMakerChecker},
            permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{leName}/ExternalSystemIds/{extSystemId}", method = RequestMethod.PUT)
    public @ResponseBody
    BaseResponse updateExternalSystemForLegalEntity(@PathVariable("orgName") String orgName,
                                                    @PathVariable("leName") String leName,
                                                    @PathVariable("extSystemId") String extSystemId,
                                                    @RequestBody String extSystemValue,
                                                    final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            LegalEntity orgLe = OrganizationUtil.getLegalEntity(org, leName);
            if (orgLe == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.LE_NOT_FOUND);
            }
            final List<ExternalSystemModel> extSystems = Collections.singletonList(new ExternalSystemModel(extSystemId, extSystemValue));
            if (service.updateExternalSystemIds(orgLe, extSystems, extSystemId)){
                return new BaseResponse(Status.OK);
            }
        } catch (Throwable th) {
            log.warn("updateExternalSystemForLegalEntity: Exception ", th);
        }

        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }
}