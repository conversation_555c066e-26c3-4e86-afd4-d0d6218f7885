package com.integral.admin.api.model.referencedata;

import com.integral.finance.fx.FXRateBasis;
import org.codehaus.jackson.annotate.JsonProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * This is the value object of the Currency Pair.
 * 
 * <AUTHOR>
 * 
 */
public class RateBasis implements Serializable {
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 2640568119018517357L;
	
	
	// ** The order of the fields are important ** 

	// All the getters and setters methods 

	@NotNull
	public String getBaseCurrency() {
		return baseCurrency;
	}




	public void setBaseCurrency(String baseCurrency) {
		this.baseCurrency = baseCurrency;
	}



	@NotNull
	public String getTermCurrency() {
		return termCurrency;
	}




	public void setTermCurrency(String termCurrency) {
		this.termCurrency = termCurrency;
	}



	@NotNull
	@Size(min=1,message="cannot be empty")
	public String getName() {
		return name;
	}




	public void setName(String name) {
		this.name = name;
	}



	@Min(0)
	public double getBaseQuoteFactor() {
		return baseQuoteFactor;
	}




	public void setBaseQuoteFactor(double baseQuoteFactor) {
		this.baseQuoteFactor = baseQuoteFactor;
	}



	@Min(0)
	public double getTermQuoteFactor() {
		return termQuoteFactor;
	}




	public void setTermQuoteFactor(double termQuoteFactor) {
		this.termQuoteFactor = termQuoteFactor;
	}



	@Size(min=1,message="cannot be empty")
	public String getCrossCurrency() {
		return crossCurrency;
	}




	public void setCrossCurrency(String crossCurrency) {
		this.crossCurrency = crossCurrency;
	}




	public boolean isDeliverable() {
		return deliverable;
	}




	public void setDeliverable(boolean deliverable) {
		this.deliverable = deliverable;
	}



	public RateBasisCalendars getFixingCalendar() {
		return fixingCalendar;
	}




	public void setFixingCalendar(RateBasisCalendars fixingCalendar) {
		this.fixingCalendar = fixingCalendar;
	}



	public RateBasisCalendars getSettlementCalendarOverride() {
		return settlementCalendarOverride;
	}




	public void setSettlementCalendarOverride(RateBasisCalendars settlementCalendarOverride) {
		this.settlementCalendarOverride = settlementCalendarOverride;
	}


	@Min(0)
	public int getSpotPrecision() {
		return spotPrecision;
	}




	public void setSpotPrecision(int spotPrecision) {
		this.spotPrecision = spotPrecision;
	}



	@Min(0)
	public int getDisplayPointsOffset() {
		return displayPointsOffset;
	}




	public void setDisplayPointsOffset(int displayPointsOffset) {
		this.displayPointsOffset = displayPointsOffset;
	}



	@Min(0)
	public int getForwardPointsPrecision() {
		return forwardPointsPrecision;
	}




	public void setForwardPointsPrecision(int forwardPointsPrecision) {
		this.forwardPointsPrecision = forwardPointsPrecision;
	}



	@Min(0)
	public double getPipsFactor() {
		return pipsFactor;
	}




	public void setPipsFactor(double pipsFactor) {
		this.pipsFactor = pipsFactor;
	}




	public boolean isAllowInverse() {
		return allowInverse;
	}




	public void setAllowInverse(boolean allowInverse) {
		this.allowInverse = allowInverse;
	}



	@Min(0)
	public int getInverseSpotPrecision() {
		return inverseSpotPrecision;
	}




	public void setInverseSpotPrecision(int inverseSpotPrecision) {
		this.inverseSpotPrecision = inverseSpotPrecision;
	}



	@Min(0)
	public int getInverseDisplayPointsOffset() {
		return inverseDisplayPointsOffset;
	}




	public void setInverseDisplayPointsOffset(int inverseDisplayPointsOffset) {
		this.inverseDisplayPointsOffset = inverseDisplayPointsOffset;
	}



	@Min(0)
	public int getInverseForwardPointsPrecision() {
		return inverseForwardPointsPrecision;
	}




	public void setInverseForwardPointsPrecision(int inverseForwardPointsPrecision) {
		this.inverseForwardPointsPrecision = inverseForwardPointsPrecision;
	}



	@Min(0)
	public double getInversePipsFactor() {
		return inversePipsFactor;
	}




	public void setInversePipsFactor(double inversePipsFactor) {
		this.inversePipsFactor = inversePipsFactor;
	}

	public Double getContractMultiplier() { return contractMultiplier; }

	public void setContractMultiplier(Double contractMultiplier) { this.contractMultiplier = contractMultiplier; }

	@JsonProperty
	private String baseCurrency;

	@JsonProperty
	private String termCurrency;
	
	@JsonProperty
	private String name;
	
	@JsonProperty
	private double baseQuoteFactor;

	@JsonProperty
	private double termQuoteFactor;

	@JsonProperty
	private String crossCurrency;

	@JsonProperty
	private boolean deliverable;

	@JsonProperty
	private RateBasisCalendars fixingCalendar;

	@JsonProperty
	private RateBasisCalendars settlementCalendarOverride;
	

	@JsonProperty
	private int spotPrecision;
	
	@JsonProperty
	private int displayPointsOffset;
	
	@JsonProperty
	private int forwardPointsPrecision;

	@JsonProperty
	private double pipsFactor;
	
	
	@JsonProperty
	private boolean allowInverse;
	
	
	@JsonProperty
	private int inverseSpotPrecision;
	
	@JsonProperty
	private int inverseDisplayPointsOffset;

	@JsonProperty
	private int inverseForwardPointsPrecision;

	@JsonProperty
	private double inversePipsFactor;

	@JsonProperty
	private Double contractMultiplier;


	public RateBasis() {

		// Define all the defaults here
		spotPrecision = FXRateBasis.DEFAULT_PRECISION;
		inverseSpotPrecision = FXRateBasis.DEFAULT_PRECISION;
		forwardPointsPrecision = 2;
		inverseForwardPointsPrecision = 2;
		pipsFactor = FXRateBasis.DEFAULT_PIPS_VALUE;
		inversePipsFactor = FXRateBasis.DEFAULT_PIPS_VALUE;
		displayPointsOffset = 2;
		inverseDisplayPointsOffset = 2;
		baseQuoteFactor = 1.0;
		termQuoteFactor = 1.0;
		deliverable = true;
		allowInverse = true;
		contractMultiplier = null;
		fixingCalendar = null;
		settlementCalendarOverride = null;
	}
	
	
	public static class RateBasisCalendars implements Serializable{
		
		/**
		 * 
		 */
		private static final long serialVersionUID = 6385034355638486888L;

		@Min(0)
		public int getSpotLag() {
			return spotLag;
		}

		public void setSpotLag(int spotLag) {
			this.spotLag = spotLag;
		}


		public List<String> getHolidayCalendars() {
			return holidayCalendars;
		}


		public void setHolidayCalendars(List<String> holidayCalendars) {
			this.holidayCalendars = holidayCalendars;
		}
		

		public String getNoLagHolidayCalendar1() {
			return noLagHolidayCalendar1;
		}

		public void setNoLagHolidayCalendar1(String noLagHolidayCalendar1) {
			this.noLagHolidayCalendar1 = noLagHolidayCalendar1;
		}
		
		public String getNoLagHolidayCalendar2() {
			return noLagHolidayCalendar2;
		}

		public void setNoLagHolidayCalendar2(String noLagHolidayCalendar2) {
			this.noLagHolidayCalendar2 = noLagHolidayCalendar2;
		}
		
		public String getIgnLagHolidayCalendar1() {
			return ignLagHolidayCalendar1;
		}

		public void setIgnLagHolidayCalendar1(String ignLagHolidayCalendar1) {
			this.ignLagHolidayCalendar1 = ignLagHolidayCalendar1;
		}
		
		public String getIgnLagHolidayCalendar2() {
			return ignLagHolidayCalendar2;
		}

		public void setIgnLagHolidayCalendar2(String ignLagHolidayCalendar2) {
			this.ignLagHolidayCalendar2 = ignLagHolidayCalendar2;
		}
		public String getRejectTODHolidayCalendar1()
		{
			return rejectTODHolidayCalendar1;
		}

		public void setRejectTODHolidayCalendar1(String rejectTODHolidayCalendar1)
		{
			this.rejectTODHolidayCalendar1 = rejectTODHolidayCalendar1;
		}

		public String getRejectTODHolidayCalendar2()
		{
			return rejectTODHolidayCalendar2;
		}

		public void setRejectTODHolidayCalendar2(String rejectTODHolidayCalendar2)
		{
			this.rejectTODHolidayCalendar2 = rejectTODHolidayCalendar2;
		}

		@JsonProperty
		private int spotLag;

		@JsonProperty
		private List<String> holidayCalendars;

		@JsonProperty
		private String noLagHolidayCalendar1;
		
		@JsonProperty
		private String noLagHolidayCalendar2;
		
		@JsonProperty
		private String ignLagHolidayCalendar1;
		
		@JsonProperty
		private String ignLagHolidayCalendar2;

		@JsonProperty
		private String rejectTODHolidayCalendar1;

		@JsonProperty
		private String rejectTODHolidayCalendar2;

		public RateBasisCalendars(){}
	}


	
	
}
