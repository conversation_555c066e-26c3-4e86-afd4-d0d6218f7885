package com.integral.admin.api.provisioning.referencedata;

import com.integral.admin.api.AdminValidationException;
import com.integral.admin.api.ApiTransaction;
import com.integral.admin.api.BaseController;
import com.integral.admin.api.model.AdminApiResponse;
import com.integral.admin.api.model.Response;
import com.integral.admin.api.model.referencedata.QuoteConvention;
import com.integral.admin.api.model.referencedata.RateBasis;
import com.integral.admin.api.model.referencedata.RateBasis.RateBasisCalendars;
import com.integral.admin.auth.ApiAuthorization;
import com.integral.admin.services.AdminServiceProvider;
import com.integral.admin.services.marketdefinition.MarketDefinitionConstant;
import com.integral.admin.services.marketdefinition.MarketDefinitionService;
import com.integral.admin.services.marketdefinition.impl.MarketDefinitionServiceC;
import com.integral.admin.utils.GenericAdminUtil;
import com.integral.admin.utils.ObjectUtils;
import com.integral.admin.utils.marketdefinition.MarketDefinitionUtil;
import com.integral.admin.utils.organization.UserUtil;
import com.integral.admin.utils.transaction.AdminTransactionUtil;
import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.finance.currency.Currency;
import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.dateGeneration.HolidayCalendarC;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.fxiapi.BadRequestException;
import com.integral.fxiapi.Status;
import com.integral.is.util.CurrencyUtil;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.StringUtilC;
import org.apache.struts.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.*;


@Controller
@RequestMapping( value = "/MarketDefinition/QuoteConvention" )
public class QuoteConventionController extends BaseController
{

    @Autowired
    RateBasisValidator rateBasisValidator;

    public QuoteConventionController ( )
    {
    }

    @RequestMapping( value = "/save", method = RequestMethod.POST, produces = "application/json" )
    public @ResponseBody
    Response save ( @RequestBody QuoteConvention data )
    {
        String id = data.getId ();
        Response response2;
        User user = UserUtil.getSessionUser ();
        if ( ObjectUtils.isNull ( user ) )
        {
            response2 = handleError ( "Invalid Session, Please re-login." );
            return response2;
        }
        else if ( !UserUtil.isSysAdmin ( user ) )
        {
            response2 = handleError ( "Permission denied, only Sysadmin' can modify Quote Convention." );
            return response2;
        }
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( id );
        if ( ObjectUtils.isNull ( convention ) )
        {
            response2 = handleError ( "Quote Convention " + id + " not found." );
            return response2;
        }
        else
        {
            try
            {
                String name = data.getName ();
                String parent = data.getParent ();
                String oldname = convention.getLongName ();
                FXRateConvention oldParent = convention.getParent ();
                Collection<Class> clazz = new ArrayList<Class> ();
                clazz.add ( com.integral.finance.fx.FXRateConventionC.class );
                clazz.add ( com.integral.audit.AuditEventC.class );
                startTX ( clazz );
                convention = AdminTransactionUtil.register ( convention );
                convention.setLongName ( name );
                if ( !parent.isEmpty () )
                {
                    FXRateConvention pConvention = MarketDefinitionUtil.getQuoteConvention ( parent );
                    pConvention = AdminTransactionUtil.register ( pConvention );
                    convention.setParent ( pConvention );
                }
                else
                {
                    convention.setParent ( null );
                    parent = "None";
                }
                if ( ObjectUtils.isNotNull ( convention ) )
                {
                    if ( !name.equals ( oldname ) )
                    {
                        AuditEvent nameAudit = initializeAudit ( convention, MarketDefinitionConstant.AUDIT_ACTION_UPDATEQUOTECONV );
                        nameAudit.setStringArg2 ( name );
                        nameAudit.setStringArg3 ( oldname );
                        MarketDefinitionServiceC.persistToMongo ( nameAudit );
                    }

                    String oldPName = "None";
                    if ( oldParent != null )
                    {
                        oldPName = oldParent.getShortName ();
                    }

                    if ( !parent.equals ( oldPName ) )
                    {
                        AuditEvent parentAudit = initializeAudit ( convention, MarketDefinitionConstant.AUDIT_ACTION_UPDATEPARENTQUOTECONV );
                        parentAudit.setStringArg2 ( parent );
                        parentAudit.setStringArg3 ( oldPName );
                        MarketDefinitionServiceC.persistToMongo ( parentAudit );
                    }
                }

                endTX ();
                response2 = handleSuccess ();
            }
            catch ( Exception e )
            {
                log.error ( "[Admin-Error] Error Occurred while updating quote convention longname" + id, e );
                response2 = handleError ( e );
            }
            finally
            {
                releaseTX ();
            }
        }
        return response2;
    }

    @RequestMapping( value = "/create", method = RequestMethod.POST, produces = "application/json" )
    public @ResponseBody
    Response create ( @RequestBody QuoteConvention data )
    {
        Response response2;
        User user = UserUtil.getSessionUser ();
        if ( ObjectUtils.isNull ( user ) )
        {
            response2 = handleError ( "Invalid Session, Please re-login." );
            return response2;
        }
        else if ( !UserUtil.isSysAdmin ( user ) )
        {
            response2 = handleError ( "Permission denied, only Sysadmin' can create Quote Convention." );
            return response2;
        }
        String id = data.getId ();
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( id );
        if ( ObjectUtils.isNotNull ( convention ) )
        {
            response2 = handleError ( "Quote Convention " + id + " already exists." );
            return response2;
        }
        try
        {
            String name = data.getName ();
            Collection<Class> clazz = new ArrayList<Class> ();
            clazz.add ( com.integral.finance.fx.FXRateConventionC.class );
            clazz.add ( com.integral.audit.AuditEventC.class );
            clazz.add ( com.integral.rule.RuleSetC.class );
            startTX ( clazz );
            MarketDefinitionService definitionService = AdminServiceProvider.getInstance ().getMarketDefinitionService ();
            definitionService.createQuoteConvention ( id, name );
            AdminTransactionUtil.commitTransaction ();
            endTX ();
            response2 = handleSuccess ();
        }
        catch ( Exception e )
        {
            log.error ( "[Admin-Error] Error Occurred while creating quote convention longname" + id, e );
            response2 = handleError ( e );
        }
        finally
        {
            releaseTX ();
        }
        return response2;
    }

    @ApiAuthorization( roles = {"SYSADMINMaker", "SYSADMINChecker", "SYSADMINMakerChecker", "SYSADMIN"}, permissions = {} )
    @RequestMapping( value = "/{quoteConvention}/CurrencyPair/{baseCurrency}", method = RequestMethod.GET, produces = "application/json" )
    @ApiTransaction
    public @ResponseBody
    AdminApiResponse<List<RateBasis>> get ( @PathVariable( "quoteConvention" ) String quoteConvention, @PathVariable( "baseCurrency" ) String baseCurrency )
    {

        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        List<RateBasis> bases = new ArrayList<RateBasis> ();

        //TODO: This iteration can be avoided if we add an API to the FXRateConventionC (FXRateConvention implementation)
        for ( FXRateBasis fxRateBasis : convention.getFXRateBasis () )
        {
            if ( baseCurrency.equalsIgnoreCase ( fxRateBasis.getBaseCurrency ().getShortName () ) )
            {

                RateBasis basis = populate ( null, fxRateBasis );

                bases.add ( basis );
            }
        }

        if ( bases.isEmpty () )
        {

            // We see this as a not found request
            String message = String.format ( DATA_NOT_FOUND, baseCurrency );
            throw new AdminValidationException ( true, message );

        }

        return new AdminApiResponse<List<RateBasis>> ( Status.OK, bases );
    }

    @RequestMapping( value = "/getCurrencyPairs/{quoteConvention}", method = RequestMethod.GET )
    public @ResponseBody
    List<String> getCurrencyPairs ( @PathVariable( "quoteConvention" ) String quoteConvention )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        List<String> currencyPairs = new ArrayList<String> ();
        for ( FXRateBasis fxRateBasis : convention.getFXRateBasis () )
        {
            currencyPairs.add ( fxRateBasis.getCurrencyPair ().toString () );
        }
        Collections.sort ( currencyPairs );

        return currencyPairs;
    }

    @RequestMapping( value = "/getOrgCurrencyPairs/{quoteConvention}/{orgName}", method = RequestMethod.GET )
    public @ResponseBody
    List<String> getOrgCurrencyPairs ( @PathVariable( "quoteConvention" ) String quoteConvention, @PathVariable( "orgName" ) String orgName )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        Organization org = StringUtilC.isNullOrEmpty ( orgName ) ? null : ReferenceDataCacheC.getInstance ().getOrganization ( orgName );
        Collection<FXRateBasis> fxRateBases = org != null ? CurrencyUtil.getFXRateBases ( convention, org ) : convention.getFXRateBasis ();

        List<String> currencyPairs = new ArrayList<String> ();
        for ( FXRateBasis fxRateBasis : fxRateBases )
        {
            currencyPairs.add ( fxRateBasis.getCurrencyPair ().toString () );
        }
        Collections.sort ( currencyPairs );

        return currencyPairs;
    }


    @RequestMapping( value = "/getCurrencyPairs/{quoteConvention}/{vehicleCcy}", method = RequestMethod.GET )
    public @ResponseBody
    List<String> getCrossCurrencyPairs ( @PathVariable( "quoteConvention" ) String quoteConvention,
                                         @PathVariable( "vehicleCcy" ) String vehicleCcy )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        List<String> currencyPairs = new ArrayList<String> ();
        for ( FXRateBasis fxRateBasis : convention.getFXRateBasis () )
        {
            if ( fxRateBasis.getBaseCurrency ().getName ().equalsIgnoreCase ( vehicleCcy )
                    || fxRateBasis.getVariableCurrency ().getName ().equalsIgnoreCase ( vehicleCcy ) )
            {
                currencyPairs.add ( fxRateBasis.getCurrencyPair ().toString () );
            }
        }
        Collections.sort ( currencyPairs );

        return currencyPairs;
    }

    @RequestMapping( value = "/getOrgCurrencyPairs/{quoteConvention}/{orgName}/{vehicleCcy}", method = RequestMethod.GET )
    public @ResponseBody
    List<String> getOrgCrossCurrencyPairs ( @PathVariable( "quoteConvention" ) String quoteConvention,
                                            @PathVariable( "orgName" ) String orgName,
                                            @PathVariable( "vehicleCcy" ) String vehicleCcy )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        Organization org = StringUtilC.isNullOrEmpty ( orgName ) ? null : ReferenceDataCacheC.getInstance ().getOrganization ( orgName );
        Collection<FXRateBasis> fxRateBases = org != null ? CurrencyUtil.getEffectiveFXRateBasis ( convention, org ) : convention.getEffectiveFXRateBasis ();

        List<String> currencyPairs = new ArrayList<String> ();
        for ( FXRateBasis fxRateBasis : fxRateBases )
        {
            if ( fxRateBasis.getBaseCurrency ().isSpotSettlementType ()
                    && fxRateBasis.getVariableCurrency ().isSpotSettlementType ()
                    && (fxRateBasis.getBaseCurrency ().getName ().equalsIgnoreCase ( vehicleCcy )
                    || fxRateBasis.getVariableCurrency ().getName ().equalsIgnoreCase ( vehicleCcy )) )
            {
                currencyPairs.add ( fxRateBasis.getCurrencyPair ().getName () );
            }
        }
        Collections.sort ( currencyPairs );

        return currencyPairs;
    }

    @RequestMapping( value = "/getCurrencies/{quoteConvention}", method = RequestMethod.GET )
    public @ResponseBody
    List<String> getCurrencies ( @PathVariable( "quoteConvention" ) String quoteConvention )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        Set<String> currencies = new HashSet<String> ();
        for ( FXRateBasis fxRateBasis : convention.getFXRateBasis () )
        {
            currencies.add ( fxRateBasis.getCurrencyPair ().getBaseCurrency ().toString () );
            currencies.add ( fxRateBasis.getCurrencyPair ().getVariableCurrency ().toString () );
        }
        List<String> cpList = new ArrayList<String> ( currencies );
        Collections.sort ( cpList );
        return cpList;
    }

    @RequestMapping( value = "/getOrgCurrencies/{quoteConvention}/{orgName}", method = RequestMethod.GET )
    public @ResponseBody
    List<String> getCurrencies ( @PathVariable( "quoteConvention" ) String quoteConvention,
                                 @PathVariable( "orgName" ) String orgName )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        Organization org = StringUtilC.isNullOrEmpty ( orgName ) ? null : ReferenceDataCacheC.getInstance ().getOrganization ( orgName );
        Collection<FXRateBasis> fxRateBases = org != null ? CurrencyUtil.getEffectiveFXRateBasis ( convention, org ) : convention.getEffectiveFXRateBasis ();

        Set<String> currencies = new HashSet<String> ();
        for ( FXRateBasis fxRateBasis : fxRateBases )
        {
            if ( fxRateBasis.getBaseCurrency ().isSpotSettlementType () )
            {
                currencies.add ( fxRateBasis.getBaseCurrency ().getShortName () );
            }
            if ( fxRateBasis.getVariableCurrency ().isSpotSettlementType () )
            {
                currencies.add ( fxRateBasis.getVariableCurrency ().getShortName () );
            }
        }
        List<String> cpList = new ArrayList<String> ( currencies );
        Collections.sort ( cpList );

        return cpList;
    }

    @ApiAuthorization( roles = {"SYSADMINMaker", "SYSADMINChecker", "SYSADMINMakerChecker", "SYSADMIN"}, permissions = {} )
    @RequestMapping( value = "/{quoteConvention}/CurrencyPair/{baseCurrency}/{variableCurrency}", method = RequestMethod.GET, produces = "application/json" )
    @ApiTransaction
    public @ResponseBody
    AdminApiResponse<RateBasis> get ( @PathVariable( "quoteConvention" ) String quoteConvention, @PathVariable( "baseCurrency" ) String baseCurrency,
                                      @PathVariable( "variableCurrency" ) String variableCurrency )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        FXRateBasis fxRateBasis = convention.getFXRateBasis ( baseCurrency, variableCurrency );

        RateBasis basis = populate ( null, fxRateBasis );

        if ( basis == null )
        {

            // We see this as a not found request
            String message = String.format ( DATA_NOT_FOUND, baseCurrency + "/" + variableCurrency );
            throw new AdminValidationException ( true, message );
        }


        return new AdminApiResponse<RateBasis> ( Status.OK, basis );
    }

    @ApiAuthorization( roles = {"SYSADMINMaker", "SYSADMINChecker", "SYSADMINMakerChecker", "SYSADMIN"}, permissions = {} )
    @RequestMapping( value = "/{quoteConvention}/CurrencyPair/add", method = RequestMethod.POST, produces = "application/json" )
    @ApiTransaction( writeable = {
            com.integral.finance.fx.FXRateBasisC.class,
            com.integral.finance.fx.FXRateConventionC.class,
            com.integral.finance.dateGeneration.HolidayCalendarC.class,
            com.integral.persistence.NamespaceC.class,
            com.integral.audit.AuditEventC.class,
            com.integral.finance.businessCalendar.BusinessCalendarC.class,
            com.integral.finance.dateGeneration.HolidayCalendarCollectionC.class
    } )
    public @ResponseBody
    AdminApiResponse<Void> add ( @PathVariable( "quoteConvention" ) String quoteConvention, @RequestBody RateBasis basis )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );


        validate ( basis );

        Currency tCurrency = MarketDefinitionUtil.getCurrency ( basis.getTermCurrency () );
        Currency bCurrency = MarketDefinitionUtil.getCurrency ( basis.getBaseCurrency () );
        Currency cCurrency = null;

		if ( basis.getCrossCurrency () != null )
		{
			cCurrency = MarketDefinitionUtil.getCurrency ( basis.getCrossCurrency () );
		}


        Collection<HolidayCalendar> settlementOverrideHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> noLagSettlementOverrideHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> ignLagSettlementOverrideHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> rejectTODSettlementOverrideHolidayCalendars = new ArrayList<HolidayCalendar> ();
        int settlementOverrideSpotLag = 0;

        if ( basis.getSettlementCalendarOverride () != null )
        {
            if ( basis.getSettlementCalendarOverride ().getHolidayCalendars () != null )
            {
                for ( String cal : basis.getSettlementCalendarOverride ().getHolidayCalendars () )
                {
                    settlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, cal ) );
                }
            }

            if ( basis.getSettlementCalendarOverride ().getNoLagHolidayCalendar1 () != null )
            {
                noLagSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getNoLagHolidayCalendar1 () ) );
            }
            if ( basis.getSettlementCalendarOverride ().getNoLagHolidayCalendar2 () != null )
            {
                noLagSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getNoLagHolidayCalendar2 () ) );
            }

            if ( basis.getSettlementCalendarOverride ().getIgnLagHolidayCalendar1 () != null )
            {
                ignLagSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getIgnLagHolidayCalendar1 () ) );
            }
            if ( basis.getSettlementCalendarOverride ().getIgnLagHolidayCalendar2 () != null )
            {
                ignLagSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getIgnLagHolidayCalendar2 () ) );
            }
            if ( basis.getSettlementCalendarOverride ().getRejectTODHolidayCalendar1 () != null )
            {
                rejectTODSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getRejectTODHolidayCalendar1 () ) );
            }
            if ( basis.getSettlementCalendarOverride ().getRejectTODHolidayCalendar2 () != null )
            {
                rejectTODSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getRejectTODHolidayCalendar2 () ) );
            }

            settlementOverrideSpotLag = basis.getSettlementCalendarOverride ().getSpotLag ();
        }

        Collection<HolidayCalendar> fixingHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> noLagFixingHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> ignLagFixingHolidayCalendars = new ArrayList<HolidayCalendar> ();
        int fixingCalendarSpotLag = 0;

        if ( basis.getFixingCalendar () != null )
        {
            if ( basis.getFixingCalendar ().getHolidayCalendars () != null )
            {
                for ( String cal : basis.getFixingCalendar ().getHolidayCalendars () )
                {
                    fixingHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, cal ) );
                }
            }

            if ( basis.getFixingCalendar ().getNoLagHolidayCalendar1 () != null )
            {
                noLagFixingHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getFixingCalendar ().getNoLagHolidayCalendar1 () ) );
            }

            if ( basis.getFixingCalendar ().getNoLagHolidayCalendar2 () != null )
            {
                noLagFixingHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getFixingCalendar ().getNoLagHolidayCalendar2 () ) );
            }

            if ( basis.getFixingCalendar ().getIgnLagHolidayCalendar1 () != null )
            {
                ignLagFixingHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getFixingCalendar ().getIgnLagHolidayCalendar1 () ) );
            }

            if ( basis.getFixingCalendar ().getIgnLagHolidayCalendar2 () != null )
            {
                ignLagFixingHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getFixingCalendar ().getIgnLagHolidayCalendar2 () ) );
            }

            fixingCalendarSpotLag = basis.getFixingCalendar ().getSpotLag ();
        }

        boolean isSettlementCalendarUseDefault = basis.getSettlementCalendarOverride () == null;

        //TODO: Typically we should let spring inject dependencies.
        AdminServiceProvider adminServices = AdminServiceProvider.getInstance ();

        FXRateBasis fxRateBasis = convention.getFXRateBasis ( basis.getBaseCurrency (), basis.getTermCurrency () );

        notExists ( fxRateBasis, basis.getName () );

        adminServices.getMarketDefinitionService ().createFXRateBasis (
                convention,
                basis.getName (),
                bCurrency,
                tCurrency,
                basis.isAllowInverse (),
                basis.getBaseQuoteFactor (),
                basis.getTermQuoteFactor (),
                basis.getSpotPrecision (),
                basis.getDisplayPointsOffset (),
                basis.getForwardPointsPrecision (),
                basis.getPipsFactor (),
                basis.getInverseSpotPrecision (),
                basis.getInverseDisplayPointsOffset (),
                basis.getInverseForwardPointsPrecision (),
                basis.getInversePipsFactor (),
                cCurrency,
                isSettlementCalendarUseDefault,
                settlementOverrideSpotLag,
                settlementOverrideHolidayCalendars,
                noLagSettlementOverrideHolidayCalendars,
                ignLagSettlementOverrideHolidayCalendars,
                basis.isDeliverable (),
                fixingCalendarSpotLag,
                fixingHolidayCalendars,
                noLagFixingHolidayCalendars,
                ignLagFixingHolidayCalendars,
                basis.getContractMultiplier (),
                true, false, rejectTODSettlementOverrideHolidayCalendars, true, true
        );


        return handleSuccess ();
    }

    @ApiAuthorization( roles = {"SYSADMINMaker", "SYSADMINChecker", "SYSADMINMakerChecker", "SYSADMIN"}, permissions = {} )
    @RequestMapping( value = "/{quoteConvention}/CurrencyPair/update", method = RequestMethod.POST, produces = "application/json" )
    @ApiTransaction( writeable = {
            com.integral.finance.fx.FXRateBasisC.class,
            com.integral.finance.fx.FXRateConventionC.class,
            com.integral.finance.dateGeneration.HolidayCalendarC.class,
            com.integral.persistence.NamespaceC.class,
            com.integral.audit.AuditEventC.class,
            com.integral.finance.businessCalendar.BusinessCalendarC.class,
            com.integral.finance.dateGeneration.HolidayCalendarCollectionC.class
    } )
    public @ResponseBody
    AdminApiResponse<Void> update ( @PathVariable( "quoteConvention" ) String quoteConvention, @RequestBody RateBasis basis )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );


        validate ( basis );

        FXRateBasis fxRateBasis = convention.getFXRateBasis ( basis.getBaseCurrency (), basis.getTermCurrency (), false );
        exists ( fxRateBasis, basis.getBaseCurrency () + "/" + basis.getTermCurrency () );

        Currency tCurrency = MarketDefinitionUtil.getCurrency ( basis.getTermCurrency () );
        Currency bCurrency = MarketDefinitionUtil.getCurrency ( basis.getBaseCurrency () );
        Currency cCurrency = null;

		if ( basis.getCrossCurrency () != null )
		{
			cCurrency = MarketDefinitionUtil.getCurrency ( basis.getCrossCurrency () );
		}


        Collection<HolidayCalendar> settlementOverrideHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> noLagSettlementOverrideHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> ignLagSettlementOverrideHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> rejectTODSettlementOverrideHolidayCalendars = new ArrayList<HolidayCalendar> ();
        int settlementOverrideSpotLag = 0;

        if ( basis.getSettlementCalendarOverride () != null )
        {
            if ( basis.getSettlementCalendarOverride ().getHolidayCalendars () != null )
            {
                for ( String cal : basis.getSettlementCalendarOverride ().getHolidayCalendars () )
                {
                    settlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, cal ) );
                }
            }

            if ( basis.getSettlementCalendarOverride ().getNoLagHolidayCalendar1 () != null )
            {
                noLagSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getNoLagHolidayCalendar1 () ) );
            }
            if ( basis.getSettlementCalendarOverride ().getNoLagHolidayCalendar2 () != null )
            {
                noLagSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getNoLagHolidayCalendar2 () ) );
            }

            if ( basis.getSettlementCalendarOverride ().getIgnLagHolidayCalendar1 () != null )
            {
                ignLagSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getIgnLagHolidayCalendar1 () ) );
            }
            if ( basis.getSettlementCalendarOverride ().getIgnLagHolidayCalendar2 () != null )
            {
                ignLagSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getIgnLagHolidayCalendar2 () ) );
            }

            if ( basis.getSettlementCalendarOverride ().getRejectTODHolidayCalendar1() != null )
            {
                rejectTODSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getRejectTODHolidayCalendar1 () ) );
            }
            if ( basis.getSettlementCalendarOverride ().getRejectTODHolidayCalendar2 () != null )
            {
                rejectTODSettlementOverrideHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getSettlementCalendarOverride ().getRejectTODHolidayCalendar2 () ) );
            }

            settlementOverrideSpotLag = basis.getSettlementCalendarOverride ().getSpotLag ();
        }

        Collection<HolidayCalendar> fixingHolidayCalendars = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> noLagFixingHolidayCalendar = new ArrayList<HolidayCalendar> ();
        Collection<HolidayCalendar> ignLagFixingHolidayCalendar = new ArrayList<HolidayCalendar> ();
        int fixingCalendarSpotLag = 0;

        if ( basis.getFixingCalendar () != null )
        {
            if ( basis.getFixingCalendar ().getHolidayCalendars () != null )
            {
                for ( String cal : basis.getFixingCalendar ().getHolidayCalendars () )
                {
                    fixingHolidayCalendars.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, cal ) );
                }
            }

            if ( basis.getFixingCalendar ().getNoLagHolidayCalendar1 () != null )
            {
                noLagFixingHolidayCalendar.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getFixingCalendar ().getNoLagHolidayCalendar1 () ) );
            }
            if ( basis.getFixingCalendar ().getNoLagHolidayCalendar2 () != null )
            {
                noLagFixingHolidayCalendar.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getFixingCalendar ().getNoLagHolidayCalendar2 () ) );
            }

            if ( basis.getFixingCalendar ().getIgnLagHolidayCalendar1 () != null )
            {
                ignLagFixingHolidayCalendar.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getFixingCalendar ().getIgnLagHolidayCalendar1 () ) );
            }
            if ( basis.getFixingCalendar ().getIgnLagHolidayCalendar2 () != null )
            {
                ignLagFixingHolidayCalendar.add ( GenericAdminUtil.getNamedEntityByShortName ( HolidayCalendarC.class, basis.getFixingCalendar ().getIgnLagHolidayCalendar2 () ) );
            }

            fixingCalendarSpotLag = basis.getFixingCalendar ().getSpotLag ();
        }

        boolean isSettlementCalendarUseDefault = basis.getSettlementCalendarOverride () == null;


        //TODO: Typically we should let spring inject dependencies.
        AdminServiceProvider adminServices = AdminServiceProvider.getInstance ();


        adminServices.getMarketDefinitionService ().updateFXRateBasis (
                fxRateBasis,
                basis.getName (),
                bCurrency,
                tCurrency,
                basis.isAllowInverse (),
                basis.getBaseQuoteFactor (),
                basis.getTermQuoteFactor (),
                basis.getSpotPrecision (),
                basis.getDisplayPointsOffset (),
                basis.getForwardPointsPrecision (),
                basis.getPipsFactor (),
                basis.getInverseSpotPrecision (),
                basis.getInverseDisplayPointsOffset (),
                basis.getInverseForwardPointsPrecision (),
                basis.getInversePipsFactor (),
                cCurrency,
                basis.getContractMultiplier (),
                true, fxRateBasis.isDeliverableNDF ()
        );

        adminServices.getMarketDefinitionService ().updateFXRateBasis (
                fxRateBasis,
                isSettlementCalendarUseDefault,
                settlementOverrideSpotLag,
                settlementOverrideHolidayCalendars,
                noLagSettlementOverrideHolidayCalendars,
                ignLagSettlementOverrideHolidayCalendars, rejectTODSettlementOverrideHolidayCalendars, true, true
        );

        adminServices.getMarketDefinitionService ().updateNDF (
                fxRateBasis,
                basis.isDeliverable (),
                fixingCalendarSpotLag,
                fixingHolidayCalendars,
                noLagFixingHolidayCalendar,
                ignLagFixingHolidayCalendar
        );

        return handleSuccess ();
    }

    @ApiAuthorization( roles = {"SYSADMINMaker", "SYSADMINChecker", "SYSADMINMakerChecker", "SYSADMIN"}, permissions = {} )
    @RequestMapping( value = "/{quoteConvention}/CurrencyPair/remove/{baseCurrency}/{variableCurrency}", method = RequestMethod.GET, produces = "application/json" )
    @ApiTransaction( writeable = {
            com.integral.user.OrganizationC.class,
            com.integral.persistence.NamespaceC.class,
            com.integral.user.UserC.class,
            com.integral.finance.fx.FXRateConventionC.class,
            com.integral.finance.fx.FXRateBasisC.class
    } )
    public @ResponseBody
    AdminApiResponse<Void> remove ( @PathVariable( "quoteConvention" ) String quoteConvention, @PathVariable( "baseCurrency" ) String baseCurrency,
                                    @PathVariable( "variableCurrency" ) String variableCurrency )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        //TODO: Typically we should let spring inject dependencies.
        AdminServiceProvider adminServices = AdminServiceProvider.getInstance ();

        FXRateBasis fxRateBasis = convention.getFXRateBasis ( baseCurrency, variableCurrency );

        if ( fxRateBasis != null )
        {

            adminServices.getMarketDefinitionService ().removeCurrencyPairFromQuoteConvention ( convention, fxRateBasis );

        }
        else
        {

            // We see this as a not found request
            String message = String.format ( DATA_NOT_FOUND, baseCurrency + "/" + variableCurrency );
            throw new AdminValidationException ( true, message );
        }

        return handleSuccess ();
    }

    @ApiAuthorization( roles = {"SYSADMINMaker", "SYSADMINChecker", "SYSADMINMakerChecker", "SYSADMIN"}, permissions = {} )
    @RequestMapping( value = "/{quoteConvention}/CurrencyPair/remove/{baseCurrency}", method = RequestMethod.GET, produces = "application/json" )
    @ApiTransaction( writeable = {
            com.integral.user.OrganizationC.class,
            com.integral.persistence.NamespaceC.class,
            com.integral.user.UserC.class,
            com.integral.finance.fx.FXRateConventionC.class,
            com.integral.finance.fx.FXRateBasisC.class
    } )
    public @ResponseBody
    AdminApiResponse<Void> remove ( @PathVariable( "quoteConvention" ) String quoteConvention, @PathVariable( "baseCurrency" ) String baseCurrency )
    {
        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConvention );
        exists ( convention, quoteConvention );

        //TODO: Typically we should let spring inject dependencies.
        AdminServiceProvider adminServices = AdminServiceProvider.getInstance ();

        boolean removed = false;

        //TODO: This iteration can be avoided if we add an API to the FXRateConventionC (FXRateConvention implementation)
        for ( FXRateBasis fxRateBasis : convention.getFXRateBasis () )
        {
            if ( baseCurrency.equalsIgnoreCase ( fxRateBasis.getBaseCurrency ().getShortName () ) )
            {

                adminServices.getMarketDefinitionService ().removeCurrencyPairFromQuoteConvention ( convention, fxRateBasis );
                removed = true;
            }
        }

        if ( !removed )
        {
            // We see this as a not found request
            String message = String.format ( DATA_NOT_FOUND, baseCurrency );
            throw new AdminValidationException ( true, message );
        }


        return handleSuccess ();
    }

    private static AuditEvent initializeAudit ( FXRateConvention convention, String action )
    {
        User loginUser = GenericAdminUtil.getContextUser ();
        loginUser = AdminTransactionUtil.register ( loginUser );
        AuditEventC audit = new AuditEventC ();
        audit.setUser ( loginUser );
        audit.setOrganization ( loginUser.getOrganization () );
        audit.setComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        audit.setAction ( action );
        if ( ObjectUtils.isNotNull ( convention ) )
        {
            audit.setEntity1 ( convention );
            audit.setStringArg1 ( convention.getShortName () );
            audit.setNamespace ( convention.getNamespace () );
        }
        return audit;
    }


    protected void validate ( RateBasis rateBasis )
    {
        List<RateBasis> list = new ArrayList<RateBasis> ();
        list.add ( rateBasis );

        Map<Integer, List<String>> errors = new HashMap<Integer, List<String>> ();

        boolean badRequest = rateBasisValidator.validateAllFields ( list, errors, null );

        if ( !errors.isEmpty () )
        {
            AdminValidationException e = new AdminValidationException ();
            for ( Map.Entry<Integer, List<String>> item : errors.entrySet () )
            {
                // Don't want the element index because we have just one object
                e.addAllErrors ( item.getValue () );
            }
            if ( badRequest )
            {
                e.setBadRequest ();
            }
            throw e;
        }
    }


    protected void validate ( List<RateBasis> list )
    {

        Map<Integer, List<String>> errors = new HashMap<Integer, List<String>> ();

        boolean badRequest = rateBasisValidator.validateAllFields ( list, errors, null );

        if ( !errors.isEmpty () )
        {
            AdminValidationException e = new AdminValidationException ();
            for ( Map.Entry<Integer, List<String>> item : errors.entrySet () )
            {
                e.addAllErrors ( item.getKey (), item.getValue () );
            }
            if ( badRequest )
            {
                e.setBadRequest ();
            }
            throw e;
        }
    }


    private void exists ( Object obj, String name )
    {
        if ( obj == null )
        {
            // Nothing can be done here; this is a bad request
            String message = String.format ( DATA_NOT_FOUND, name );
            throw new AdminValidationException ( true, message );
        }
    }

    private void notExists ( Object obj, String name )
    {
        if ( obj != null )
        {
            // Nothing can be done here; this is a bad request
            String message = String.format ( DATA_ALREADY_EXISTS, name );
            throw new AdminValidationException ( false, message );
        }
    }


    protected RateBasis populate ( RateBasis rb, FXRateBasis fxrb )
    {

		if ( fxrb == null )
		{
			return null;
		}
        RateBasis basis = rb;
		if ( basis == null )
		{
			basis = new RateBasis ();
		}

        basis.setName ( fxrb.getName () );
        basis.setBaseCurrency ( fxrb.getBaseCurrency ().getName () );
        basis.setTermCurrency ( fxrb.getVariableCurrency ().getName () );
        basis.setBaseQuoteFactor ( fxrb.getBaseCurrencyFactor () );
        basis.setTermQuoteFactor ( fxrb.getVariableCurrencyFactor () );

		if ( fxrb.getCrossCurrency () != null )
		{
			basis.setCrossCurrency ( fxrb.getCrossCurrency ().getName () );
		}

        basis.setDeliverable ( !fxrb.isNonDeliverable () );


        if ( fxrb.getFixingBusinessCalendar () != null )
        {

            RateBasisCalendars fc = new RateBasisCalendars ();

            fc.setSpotLag ( fxrb.getFixingBusinessCalendar ().getLag () );

            // The returned collection in getFXBusinessCalendar().getHolidayCalendars() should have been strictly typed
            Collection<HolidayCalendar> hcc = fxrb.getFixingBusinessCalendar ().getHolidayCalendars ();
            if ( hcc != null )
            {
                List<String> hccList = new ArrayList<String> ();

                for ( HolidayCalendar holidayCalendar : hcc )
                {
                    hccList.add ( holidayCalendar.getShortName () );
                }

                fc.setHolidayCalendars ( hccList );
            }


            HolidayCalendar hc1 = fxrb.getFixingBusinessCalendar ().getNoLagHolidayCalendar1 ();
			if ( hc1 != null )
			{
				fc.setNoLagHolidayCalendar1 ( hc1.getShortName () );
			}
            HolidayCalendar hc2 = fxrb.getFixingBusinessCalendar ().getNoLagHolidayCalendar2 ();
			if ( hc2 != null )
			{
				fc.setNoLagHolidayCalendar2 ( hc2.getShortName () );
			}

            HolidayCalendar ignhc1 = fxrb.getFixingBusinessCalendar ().getIgnoreLagHolidayCalendar1 ();
			if ( ignhc1 != null )
			{
				fc.setIgnLagHolidayCalendar1 ( ignhc1.getShortName () );
			}
            HolidayCalendar ignhc2 = fxrb.getFixingBusinessCalendar ().getIgnoreLagHolidayCalendar2 ();
			if ( ignhc2 != null )
			{
				fc.setIgnLagHolidayCalendar2 ( ignhc2.getShortName () );
			}

            basis.setFixingCalendar ( fc );
        }
        else
        {
            basis.setFixingCalendar ( null );
        }


        if ( fxrb.getFXBusinessCalendar () != null )
        {

            RateBasisCalendars so = new RateBasisCalendars ();

            so.setSpotLag ( fxrb.getFXBusinessCalendar ().getLag () );


            // The returned collection in getFXBusinessCalendar().getHolidayCalendars() should have been strictly typed
            Collection<HolidayCalendar> hcc = fxrb.getFXBusinessCalendar ().getHolidayCalendars ();
            if ( hcc != null )
            {
                List<String> hccList = new ArrayList<String> ();

                for ( HolidayCalendar holidayCalendar : hcc )
                {
                    hccList.add ( holidayCalendar.getShortName () );
                }

                so.setHolidayCalendars ( hccList );
            }


            HolidayCalendar hc1 = fxrb.getFXBusinessCalendar ().getNoLagHolidayCalendar1 ();
			if ( hc1 != null )
			{
				so.setNoLagHolidayCalendar1 ( hc1.getShortName () );
			}
            HolidayCalendar hc2 = fxrb.getFXBusinessCalendar ().getNoLagHolidayCalendar2 ();
			if ( hc2 != null )
			{
				so.setNoLagHolidayCalendar2 ( hc2.getShortName () );
			}

            HolidayCalendar ignhc1 = fxrb.getFXBusinessCalendar ().getIgnoreLagHolidayCalendar1 ();
			if ( ignhc1 != null )
			{
				so.setIgnLagHolidayCalendar1 ( ignhc1.getShortName () );
			}
            HolidayCalendar ignhc2 = fxrb.getFXBusinessCalendar ().getIgnoreLagHolidayCalendar2 ();
			if ( ignhc2 != null )
			{
				so.setIgnLagHolidayCalendar2 ( ignhc2.getShortName () );
			}

            basis.setSettlementCalendarOverride ( so );

        }
        else
        {
            basis.setSettlementCalendarOverride ( null );
        }

        basis.setSpotPrecision ( fxrb.getSpotPrecision () );
        basis.setInverseSpotPrecision ( fxrb.getInverseSpotPrecision () );
        basis.setForwardPointsPrecision ( fxrb.getForwardPointsPrecision () );
        basis.setInverseForwardPointsPrecision ( fxrb.getInverseForwardPointsPrecision () );
        basis.setPipsFactor ( fxrb.getPipsFactor () );
        basis.setInversePipsFactor ( fxrb.getInversePipsFactor () );
        basis.setDisplayPointsOffset ( fxrb.getSpotPointsPrecision () );
        basis.setInverseDisplayPointsOffset ( fxrb.getInverseSpotPointsPrecision () );
        basis.setAllowInverse ( fxrb.isAllowInverse () );

        return basis;

    }

    @ApiAuthorization( roles = {"SYSADMINMaker", "SYSADMINChecker", "SYSADMINMakerChecker", "SYSADMIN"}, permissions = {} )
    @RequestMapping( value = "/effectiveRateBases/{quoteConvention}", method = RequestMethod.GET, produces = "application/json" )
    @ApiTransaction
    public @ResponseBody List<RateBasis> getEffectiveRateBases ( @PathVariable( "quoteConvention" ) String quoteConvention )
    {
        return getRateBases ( quoteConvention, true );
    }

    @ApiAuthorization( roles = {"SYSADMINMaker", "SYSADMINChecker", "SYSADMINMakerChecker", "SYSADMIN"}, permissions = {} )
    @RequestMapping( value = "/rateBases/{quoteConvention}", method = RequestMethod.GET, produces = "application/json" )
    @ApiTransaction
    public @ResponseBody List<RateBasis> getRateBases ( @PathVariable( "quoteConvention" ) String quoteConvention )
    {
        return getRateBases ( quoteConvention, false );
    }

    private List<RateBasis> getRateBases ( String quoteConventionName, boolean includeParent )
    {
        if ( StringUtilC.isNullOrEmpty ( ResponseUtils.filter ( quoteConventionName ) ) )
        {
            log.info ( "QCC.getRateBases - invalid quote convention=" + quoteConventionName );
            throw new BadRequestException ( "Invalid Quote Convention" );
        }

        FXRateConvention convention = MarketDefinitionUtil.getQuoteConvention ( quoteConventionName );
        if ( convention == null )
        {
            log.info ( "QCC.getRateBases - invalid quote convention=" + quoteConventionName );
            throw new BadRequestException ( "Invalid Quote Convention" );
        }

        List<RateBasis> rateBases = new ArrayList<RateBasis> ();
        Collection<FXRateBasis> rateBasisCollection = includeParent ? convention.getEffectiveFXRateBasis () : convention.getFXRateBasis ();
        for ( FXRateBasis fxRateBasis : rateBasisCollection )
        {
            RateBasis rb = populate ( null, fxRateBasis );
            rateBases.add ( rb );
        }
        return rateBases;
    }
}
