package com.integral.admin.api.organization;

import com.google.gson.Gson;
import com.google.gson.InstanceCreator;
import com.integral.admin.CommonErrorCodes;
import com.integral.admin.api.model.referencedata.currencypair.*;
import com.integral.admin.api.model.referencedata.organization.TakerLiquidityBasicModel;
import com.integral.admin.api.model.referencedata.organization.TakerLiquidityDetailModel;
import com.integral.admin.audit.organization.OrganizationAuditConstants;
import com.integral.admin.services.AdminServiceProvider;
import com.integral.admin.services.org.OrganizationService;
import com.integral.admin.utils.ComparatorUtilities;
import com.integral.admin.utils.GenericAdminUtil;
import com.integral.admin.utils.StringUtils;
import com.integral.admin.utils.marketdefinition.MarketDefinitionUtil;
import com.integral.admin.utils.notification.NotificationUtil;
import com.integral.admin.utils.organization.LiquidityRulesAndProvisioningUtil;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.admin.utils.pricemaking.PriceMakingUtil;
import com.integral.admin.utils.transaction.AdminTransactionUtil;
import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.audit.AuditManager;
import com.integral.broker.model.Stream;
import com.integral.commons.Tuple;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.is.ISCommonConstants;
import com.integral.is.functor.TakerCurrencyPairUpdateFunctorC;
import com.integral.is.util.StreamUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.util.*;

@Service
public class CustomerOrgService {
    private final Log log = LogFactory.getLog(this.getClass());

    @Autowired
    OrganizationsService organizationsService;
    public DefaultCurrencyPairResponse getTakerDefaultCcyPairConfig(Organization taker) {
        TakerOrganizationFunction takerOrgFunc = taker.getTakerOrganizationFunction();
        if (takerOrgFunc == null) {
            log.info("Customer " + taker + " has no TakerOrganizationFunction");
            return null;
        }
        return getTakerDefaultCcyPairConfig(takerOrgFunc);
    }

    public TakerLiquidityBasicModel getLiquidityBasicModel(Organization taker, Organization counterparty,
                                                           OrganizationRelationship rel, String clientTag) {
        boolean isDefault = false;
        CurrencyPairGroup cpg = rel.getOneClickCurrencyPairGroup();
        if (cpg == null) {
            cpg = taker.getTakerOrganizationFunction() == null ? null : taker.getTakerOrganizationFunction().getOneClickCurrencyPairGroup();
            isDefault = true;
        }
        if (cpg == null) {
            return null;
        }
        RelationCurrencyPairResponse cpData = getRelationCurrencyPairData(cpg, isDefault);
        String streamAttribute = PriceMakingUtil.getStreamAttributes(taker.getDefaultDealingEntity(), counterparty);
        if (streamAttribute.contains("ESP")) {
            streamAttribute = streamAttribute.replace("ESP", "ESP|SW");
        }
        String stream = organizationsService.getStreamAlias(taker, counterparty, rel);

        TakerLiquidityBasicModel entry = new TakerLiquidityBasicModel();
        entry.setCustomer(taker.getShortName());
        entry.setProvider(counterparty.isMasked() ? counterparty.getRealLP().getShortName() : counterparty.getShortName());
        entry.setClientTag(clientTag);
        entry.setStream(stream);
        entry.setStreamId(counterparty.getShortName());
        entry.setStreamAttributes(streamAttribute);
        entry.setEnabled(rel.isCounterpartyEnabled());
        entry.setCurrencyPairData(cpData);
        return entry;
    }

    public List<TakerLiquidityDetailModel> getLiquidityDetailModel(Organization taker, Organization counterparty,
                                                                   OrganizationRelationship rel, String clientTag) {
        CurrencyPairGroup cpg = rel.getOneClickCurrencyPairGroup();
        if (cpg == null) {
            cpg = taker.getTakerOrganizationFunction() == null ? null : taker.getTakerOrganizationFunction().getOneClickCurrencyPairGroup();
        }
        if (cpg == null) {
            return null;
        }

        if (cpg.getCurrencyPairs() == null || cpg.getCurrencyPairs().isEmpty()) {
            return Collections.emptyList();
        }
        String streamAttribute = PriceMakingUtil.getStreamAttributes(taker.getDefaultDealingEntity(), counterparty);
        if (streamAttribute.contains("ESP")) {
            streamAttribute = streamAttribute.replace("ESP", "ESP|SW");
        }
        String stream = organizationsService.getStreamAlias(taker, counterparty, rel);

        TakerLiquidityDetailModel model = new TakerLiquidityDetailModel();
        model.setCustomer(taker.getShortName());
        model.setProvider(counterparty.isMasked() ? counterparty.getRealLP().getShortName() : counterparty.getShortName());
        model.setClientTag(clientTag);
        model.setStream(stream);
        model.setStreamId(counterparty.getShortName());
        model.setStreamAttributes(streamAttribute);

        Map<String, String> cpToCpgMap = getCurrencyPairToGroupMap(cpg.getCurrencyPairGroups());
        List<TakerLiquidityDetailModel> result = new ArrayList<TakerLiquidityDetailModel>(cpg.getCurrencyPairs().size());
        boolean isFirst = true;
        for (CurrencyPair cp: cpg.getCurrencyPairs()) {
            String classification = MarketDefinitionUtil.getClassification(cp);
            TakerLiquidityDetailModel entry = null;
            if (isFirst) {
                entry = model;
                isFirst = false;
            }
            else {
                entry = model.duplicate();
            }
            entry.setCurrencyPair(cp.getName());
            entry.setCurrencyPairGroup(cpToCpgMap.get(cp.getName()));
            entry.setClassification(classification);
            entry.setEnabled(true);
            result.add(entry);
        }

        Collection<FXRateBasis> excludedCps = cpg.getExcludedFXRateBasis();
        if (excludedCps != null && !excludedCps.isEmpty()) {
            for (FXRateBasis excludedCp: excludedCps) {
                String classification = MarketDefinitionUtil.getClassification(excludedCp.getCurrencyPair());
                TakerLiquidityDetailModel entry = model.duplicate();
                entry.setCurrencyPair(excludedCp.getName());
                entry.setCurrencyPairGroup(cpToCpgMap.get(excludedCp.getName()));
                entry.setClassification(classification);
                entry.setEnabled(false);
                result.add(entry);
            }
        }
        return result;
    }

    public DefaultCurrencyPairResponse getTakerDefaultCcyPairConfig(TakerOrganizationFunction takerOrgFunction) {
        CurrencyPairGroup takerDefaultCpg = takerOrgFunction.getOneClickCurrencyPairGroup();
        if (takerDefaultCpg == null) {
            log.info("Customer " + takerOrgFunction.getOrganization().getShortName() + " has no Default CurrencyPairConfig");
            return null;
        }
        return getDefaultCurrencyPairData(takerDefaultCpg);
    }

    public boolean createTakerOrganizationFunction(Organization taker) {
        try {
            OrganizationService organizationService = AdminServiceProvider.getInstance().getOrganizationService();
            Collection<Class> writeClasses = new ArrayList<Class>();
            writeClasses.add(CurrencyPairGroupC.class);
            writeClasses.add(OrganizationC.class);
            writeClasses.add(com.integral.persistence.NamespaceC.class);
            writeClasses.add(com.integral.taker.TakerOrganizationFunctionC.class);
            AdminTransactionUtil.startTransaction();
            AdminTransactionUtil.addDefaultReadOnlyClasses();
            AdminTransactionUtil.addWriteableClass(writeClasses);
            organizationService.createTakerOrgFunction(taker);
            AdminTransactionUtil.commitTransaction();
            return true;
        }
        catch (Exception e) {
            log.warn("createTakerOrganizationFunction: Exception occurred while creating TakerOrganizationFunction for "
                    + taker.getShortName(), e);
            return false;
        }
        finally {
            AdminTransactionUtil.releaseTransaction();
        }
    }

    public Tuple<Integer, String> setTakerDefaultCcyPairConfig(TakerOrganizationFunction takerOrgFunction,
                                                               CurrencyPairGroup takerDefaultCpg,
                                                               DefaultCurrencyPairRequest existingDefCpg,
                                                               DefaultCurrencyPairRequest newDefCpg) {
        Tuple<Integer, String> result;
        Organization taker = takerOrgFunction.getOrganization();

        // Validate Quote Convention
        FXRateConvention qc = MarketDefinitionUtil.getQuoteConvention(newDefCpg.getQuoteConvention());
        if (qc == null) {
            log.warn("QuoteConvention " + newDefCpg.getQuoteConvention() + " not found");
            result = new Tuple<Integer, String>();
            result.first = HttpServletResponse.SC_NOT_FOUND;
            result.second = CommonErrorCodes.QUOTE_CONVENTION_NOT_FOUND;
            return result;
        }

        try {
            Collection<CurrencyPairGroup> cpgs = new ArrayList<CurrencyPairGroup>();
            Collection<FXRateBasis> includedCps = new ArrayList<FXRateBasis>();
            Collection<FXRateBasis> excludedCps = new ArrayList<FXRateBasis>();
            StringBuilder cpBuilderOld = new StringBuilder();
            StringBuilder cpBuilderNew = new StringBuilder();
            result = validateAndPopulateInputCPG(taker, qc, takerDefaultCpg, newDefCpg, cpgs, includedCps, excludedCps, cpBuilderOld, cpBuilderNew);
            if (result.first != HttpServletResponse.SC_OK) {
                return result;
            }

            Collection<Class> writeClasses = new ArrayList<Class>();
            writeClasses.add(CurrencyPairGroupC.class);
            writeClasses.add(OrganizationC.class);
            writeClasses.add(com.integral.taker.TakerOrganizationFunctionC.class);
            AdminTransactionUtil.startTransaction();
            AdminTransactionUtil.addDefaultReadOnlyClasses();
            AdminTransactionUtil.addWriteableClass(writeClasses);

            if (takerDefaultCpg == null) {
                takerDefaultCpg = CurrencyFactory.newCurrencyPairGroup();
                takerDefaultCpg = (CurrencyPairGroup)takerDefaultCpg.getRegisteredObject();
                String shortName = Long.toString(taker.getObjectID())+"TDCPG";
                String longName = taker.getShortName() + " Default Taker CurrencyPairGroup";
                takerDefaultCpg.setShortName(shortName);
                takerDefaultCpg.setLongName(longName);
                takerDefaultCpg.setNamespace((Namespace)taker.getNamespace().getRegisteredObject());
            }
            else {
                takerDefaultCpg = (CurrencyPairGroup) takerDefaultCpg.getRegisteredObject();
            }
            takerDefaultCpg.setFXRateConvention(AdminTransactionUtil.register(qc));
            takerDefaultCpg.setCurrencyPairGroups(AdminTransactionUtil.register(cpgs));
            takerDefaultCpg.setIncludedFXRateBasis(AdminTransactionUtil.register(includedCps));
            takerDefaultCpg.setExcludedFXRateBasis(AdminTransactionUtil.register(excludedCps));
            takerOrgFunction = (TakerOrganizationFunction) takerOrgFunction.getRegisteredObject();
            takerOrgFunction.setOneClickCurrencyPairGroup(takerDefaultCpg);
            AdminTransactionUtil.commitTransaction();

            if (cpBuilderOld.length() > 0 && cpBuilderNew.length() > 0) {
                AuditEvent auditEvent = getGenericAuditData(taker);
                auditEvent.setComponent(OrganizationAuditConstants.TAKER_CCYPAIR);
                auditEvent.setAction("DEFAULT_CP_UPDATE");
                auditEvent.setOrganization(taker);
                auditEvent.setStringArg3(taker.getShortName());
                auditEvent.setStringArg9(cpBuilderOld.toString());
                auditEvent.setStringArg18(cpBuilderNew.toString());
                auditAction(OrganizationAuditConstants.TAKER_CCYPAIR, auditEvent);
            }

            result = new Tuple<Integer, String>();
            result.first = HttpServletResponse.SC_OK;
            return result;
        }
        catch (Exception e) {
            log.warn("setTakerDefaultCcyPairConfig: Exception occurred while setting default TakerCcyPairConfig for "
                    + taker.getShortName(), e);
        }
        finally {
            AdminTransactionUtil.releaseTransaction();
        }
        result = new Tuple<Integer, String>();
        result.first = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
        result.second = CommonErrorCodes.INTERNAL_SERVER_ERROR;
        return result;
    }

    public Tuple<Integer, String> enableCcyPairInTakerDefaultCcyPairConfig(TakerOrganizationFunction takerOrgFunction,
                                                                           CurrencyPairGroup takerDefaultCpg,
                                                                           FXRateBasis ccyPair) {
        Tuple<Integer, String> result;
        Organization taker = takerOrgFunction.getOrganization();

        try {
            if (takerDefaultCpg != null) {
                if (takerDefaultCpg.getCurrencyPairs().contains(ccyPair.getCurrencyPair())) {
                    log.info("CurrencyPair " + ccyPair.getName() + " is already enabled in Taker Default CcyPairConfig for "
                            + taker.getShortName());
                }
                else {
                    Collection<FXRateBasis> includedCps = new ArrayList<FXRateBasis>();
                    if (takerDefaultCpg.getIncludedFXRateBasis() != null) {
                        includedCps.addAll(takerDefaultCpg.getIncludedFXRateBasis());
                    }
                    if (!includedCps.contains(ccyPair)) {
                        includedCps.add(ccyPair);
                    }

                    Collection<FXRateBasis> excludedCps = new ArrayList<FXRateBasis>();
                    if (takerDefaultCpg.getExcludedFXRateBasis() != null) {
                        excludedCps.addAll(takerDefaultCpg.getExcludedFXRateBasis());
                        excludedCps.remove(ccyPair);
                    }

                    Collection<Class> writeClasses = new ArrayList<Class>();
                    writeClasses.add(CurrencyPairGroupC.class);
                    writeClasses.add(OrganizationC.class);
                    writeClasses.add(com.integral.taker.TakerOrganizationFunctionC.class);
                    AdminTransactionUtil.startTransaction();
                    AdminTransactionUtil.addDefaultReadOnlyClasses();
                    AdminTransactionUtil.addWriteableClass(writeClasses);

                    takerDefaultCpg = (CurrencyPairGroup) takerDefaultCpg.getRegisteredObject();
                    takerDefaultCpg.setIncludedFXRateBasis(includedCps);
                    takerDefaultCpg.setExcludedFXRateBasis(excludedCps);
                    takerOrgFunction = (TakerOrganizationFunction) takerOrgFunction.getRegisteredObject();
                    takerOrgFunction.setOneClickCurrencyPairGroup(takerDefaultCpg);

                    AuditEvent auditEvent = getGenericAuditData(taker);
                    auditEvent.setComponent(OrganizationAuditConstants.TAKER_CCYPAIR);
                    auditEvent.setAction("DEFAULT_CP_ENABLE");
                    auditEvent.setOrganization(taker);
                    auditEvent.setStringArg3(taker.getShortName());
                    auditEvent.setStringArg9(ccyPair.getName());
                    auditAction(OrganizationAuditConstants.TAKER_CCYPAIR, auditEvent);

                    // Notification
                    HashMap<String, String> notificationMAP = new HashMap<String, String>();
                    notificationMAP.put(ISCommonConstants.TAKER_KEY, taker.getShortName());
                    notificationMAP.put(TakerCurrencyPairUpdateFunctorC.QUOTE_CONVENTION,
                            (takerDefaultCpg.getFXRateConvention() == null ? ISCommonConstants.QUOTE_CONVENTION : takerDefaultCpg.getFXRateConvention().getShortName()));
                    notificationMAP.put(ISCommonConstants.CURRENCY_PAIR, ccyPair.getCurrencyPair().getName());
                    notificationMAP.put(ISCommonConstants.MAKER_KEY, "ALL");
                    notificationMAP.put(ISCommonConstants.ACTION_KEY, ISCommonConstants.ENABLED_KEY);
                    NotificationUtil.notifyFunctors(notificationMAP, "FUNCTORS.TAKER_CCYPAIR.ENABLEDISABLE");

                    AdminTransactionUtil.commitTransaction();
                }

                Collection<Organization> lps = taker.getRelatedActiveOrganizations(OrganizationRelationship.FI_LP_RELATIONSHIP);
                for (Organization lp : lps) {
                    OrganizationRelationship orgRel = OrganizationUtil.getOrganizationRelationshipByClassification(taker,
                            lp, OrganizationRelationship.FI_LP_RELATIONSHIP);
                    if (orgRel != null && orgRel.getOneClickCurrencyPairGroup() != null) {
                        // If orgRel already has a CPG, then enable the CPG for this ccyPair
                        enableCcyPairInTakerCcyPairConfig(taker, lp, orgRel, ccyPair, false);
                    }
                }
            }

            result = new Tuple<Integer, String>();
            result.first = HttpServletResponse.SC_OK;
            return result;
        }
        catch (Exception e) {
            log.warn("enableCcyPairInTakerDefaultCcyPairConfig: Exception occurred while setting default TakerCcyPairConfig for "
                    + taker.getShortName(), e);
        }
        finally {
            AdminTransactionUtil.releaseTransaction();
        }
        result = new Tuple<Integer, String>();
        result.first = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
        result.second = CommonErrorCodes.INTERNAL_SERVER_ERROR;
        return result;
    }

    public Tuple<Integer, String> disableCcyPairInTakerDefaultCcyPairConfig(TakerOrganizationFunction takerOrgFunction,
                                                                            CurrencyPairGroup takerDefaultCpg,
                                                                            FXRateBasis ccyPair) {
        Tuple<Integer, String> result;
        Organization taker = takerOrgFunction.getOrganization();

        try {
            Collection<Class> writeClasses = new ArrayList<Class>();
            writeClasses.add(CurrencyPairGroupC.class);
            writeClasses.add(OrganizationC.class);
            writeClasses.add(com.integral.taker.TakerOrganizationFunctionC.class);
            AdminTransactionUtil.startTransaction();
            AdminTransactionUtil.addDefaultReadOnlyClasses();
            AdminTransactionUtil.addWriteableClass(writeClasses);

            if (takerDefaultCpg != null) {
                if (!takerDefaultCpg.getCurrencyPairs().contains(ccyPair.getCurrencyPair())) {
                    log.info("CurrencyPair " + ccyPair.getName() + " is already disabled in Taker Default CcyPairConfig for "
                            + taker.getShortName());
                }
                else {
                    Collection<FXRateBasis> excludedCps = new ArrayList<FXRateBasis>();
                    if (takerDefaultCpg.getExcludedFXRateBasis() != null) {
                        excludedCps.addAll(takerDefaultCpg.getExcludedFXRateBasis());
                    }
                    excludedCps.add(ccyPair);

                    takerDefaultCpg = (CurrencyPairGroup) takerDefaultCpg.getRegisteredObject();
                    takerDefaultCpg.setExcludedFXRateBasis(excludedCps);
                    takerOrgFunction = (TakerOrganizationFunction) takerOrgFunction.getRegisteredObject();
                    takerOrgFunction.setOneClickCurrencyPairGroup(takerDefaultCpg);

                    AuditEvent auditEvent = getGenericAuditData(taker);
                    auditEvent.setComponent(OrganizationAuditConstants.TAKER_CCYPAIR);
                    auditEvent.setAction("DEFAULT_CP_DISABLE");
                    auditEvent.setOrganization(taker);
                    auditEvent.setStringArg3(taker.getShortName());
                    auditEvent.setStringArg9(ccyPair.getName());
                    auditAction(OrganizationAuditConstants.TAKER_CCYPAIR, auditEvent);

                    // Notification
                    HashMap<String, String> notificationMAP = new HashMap<String, String>();
                    notificationMAP.put(ISCommonConstants.TAKER_KEY, taker.getShortName());
                    notificationMAP.put(TakerCurrencyPairUpdateFunctorC.QUOTE_CONVENTION,
                            (takerDefaultCpg.getFXRateConvention() == null ? ISCommonConstants.QUOTE_CONVENTION : takerDefaultCpg.getFXRateConvention().getShortName()));
                    notificationMAP.put(ISCommonConstants.CURRENCY_PAIR, ccyPair.getCurrencyPair().getName());
                    notificationMAP.put(ISCommonConstants.MAKER_KEY, "ALL");
                    notificationMAP.put(ISCommonConstants.ACTION_KEY, ISCommonConstants.DISABLED_KEY);
                    NotificationUtil.notifyFunctors(notificationMAP, "FUNCTORS.TAKER_CCYPAIR.ENABLEDISABLE");

                    AdminTransactionUtil.commitTransaction();
                }

                Collection<Organization> lps = taker.getRelatedActiveOrganizations(OrganizationRelationship.FI_LP_RELATIONSHIP);
                for (Organization lp : lps) {
                    OrganizationRelationship orgRel = OrganizationUtil.getOrganizationRelationshipByClassification(taker,
                            lp, OrganizationRelationship.FI_LP_RELATIONSHIP);
                    if (orgRel != null) {
                        if (orgRel.getOneClickCurrencyPairGroup() != null) {
                            // If orgRel already has a CPG, then disable the CPG for this ccyPair
                            disableCcyPairInTakerCcyPairConfig(taker, lp, orgRel, ccyPair, false);
                        }
                    }
                }
            }

            result = new Tuple<Integer, String>();
            result.first = HttpServletResponse.SC_OK;
            return result;
        }
        catch (Exception e) {
            log.warn("disableCcyPairInTakerDefaultCcyPairConfig: Exception occurred while setting default TakerCcyPairConfig for "
                    + taker.getShortName(), e);
        }
        finally {
            AdminTransactionUtil.releaseTransaction();
        }
        result = new Tuple<Integer, String>();
        result.first = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
        result.second = CommonErrorCodes.INTERNAL_SERVER_ERROR;
        return result;
    }

    public Tuple<Integer, String> enableCcyPairInTakerCcyPairConfig(Organization taker, Organization counterparty,
                                                                    OrganizationRelationship orgRel, FXRateBasis ccyPair,
                                                                    boolean sendNotification) {
        Tuple<Integer, String> result;
        CurrencyPairGroup relCpg = orgRel.getOneClickCurrencyPairGroup();
        boolean existingIsDefault = relCpg == null;
        CurrencyPairGroup takerDefaultCpg = taker.getTakerOrganizationFunction().getOneClickCurrencyPairGroup();

        try {
            Collection<Class> writeClasses = new ArrayList<Class>();
            writeClasses.add(CurrencyPairGroupC.class);
            writeClasses.add(FXRateConventionC.class);
            writeClasses.add(OrganizationRelationshipC.class);
            AdminTransactionUtil.startTransaction();
            AdminTransactionUtil.addDefaultReadOnlyClasses();
            AdminTransactionUtil.addWriteableClass(writeClasses);

            if (existingIsDefault) {
                if (takerDefaultCpg == null) {
                    log.warn("Taker Default CcyPairConfig is not set for " + taker.getShortName());
                    result = new Tuple<Integer, String>();
                    result.first = HttpServletResponse.SC_NOT_FOUND;
                    result.second = CommonErrorCodes.CCY_PAIR_CONFIGURATION_NOT_FOUND;
                    return result;
                }
                // Create a new CPG & associate on orgRel
                relCpg = CurrencyFactory.newCurrencyPairGroup();
                relCpg = (CurrencyPairGroup) relCpg.getRegisteredObject();
                String shortName = Long.toString(taker.getObjectID()) + Long.toString(counterparty.getObjectID()) + "TOCPG";
                String longName = taker.getShortName() + "_" + counterparty.getShortName() + " Taker CPG";
                relCpg.setShortName(shortName);
                relCpg.setLongName(longName);
                relCpg.setNamespace((Namespace) taker.getNamespace().getRegisteredObject());
            }
            else {
                //Update the associated CPG on orgRel
                relCpg = (CurrencyPairGroup) relCpg.getRegisteredObject();
            }
            if (relCpg.getCurrencyPairs().contains(ccyPair.getCurrencyPair())) {
                log.info("CurrencyPair " + ccyPair.getName() + " is already enabled in Taker CcyPairConfig for "
                        + counterparty.getShortName());
                result = new Tuple<Integer, String>();
                result.first = HttpServletResponse.SC_OK;
                return result;
            }
            Collection<FXRateBasis> includedCps = new ArrayList<FXRateBasis>();
            if (relCpg.getIncludedFXRateBasis() != null) {
                includedCps.addAll(relCpg.getIncludedFXRateBasis());
            }
            if (!includedCps.contains(ccyPair)) {
                includedCps.add(ccyPair);
            }
            relCpg.setIncludedFXRateBasis(includedCps);

            Collection<FXRateBasis> excludedCps = new ArrayList<FXRateBasis>();
            if (relCpg.getExcludedFXRateBasis() != null) {
                excludedCps.addAll(relCpg.getExcludedFXRateBasis());
                excludedCps.remove(ccyPair);
            }
            relCpg.setExcludedFXRateBasis(excludedCps);

            AuditEvent auditEvent = getGenericAuditData(orgRel);
            auditEvent.setComponent(OrganizationAuditConstants.TAKER_CCYPAIR);
            auditEvent.setAction("CP_ENABLE");
            auditEvent.setOrganization((Organization) orgRel.getOwner());
            auditEvent.setStringArg1(counterparty.getShortName());
            auditEvent.setStringArg3(taker.getShortName());
            auditEvent.setStringArg9(ccyPair.getName());
            auditAction(OrganizationAuditConstants.TAKER_CCYPAIR, auditEvent);

            if (sendNotification) {
                Stream stream = StreamUtil.getStream(taker.getDefaultDealingEntity(), counterparty);
                if (stream != null) {
                    // Notification
                    HashMap<String, String> notificationMAP = new HashMap<String, String>();
                    notificationMAP.put(ISCommonConstants.TAKER_KEY, taker.getShortName());
                    notificationMAP.put(TakerCurrencyPairUpdateFunctorC.QUOTE_CONVENTION,
                            (takerDefaultCpg.getFXRateConvention() == null ? ISCommonConstants.QUOTE_CONVENTION : takerDefaultCpg.getFXRateConvention().getShortName()));
                    notificationMAP.put(ISCommonConstants.CURRENCY_PAIR, ccyPair.getCurrencyPair().getName());
                    notificationMAP.put(ISCommonConstants.MAKER_KEY, counterparty.getShortName());
                    notificationMAP.put(ISCommonConstants.STREAM, stream.getShortName());
                    notificationMAP.put(ISCommonConstants.ACTION_KEY, ISCommonConstants.ENABLED_KEY);
                    NotificationUtil.notifyFunctors(notificationMAP, "FUNCTORS.TAKER_CCYPAIR.ENABLEDISABLE");
                }
            }
            AdminTransactionUtil.commitTransaction();

            result = new Tuple<Integer, String>();
            result.first = HttpServletResponse.SC_OK;
            return result;
        }
        catch (Exception e) {
            log.warn("enableCcyPairInTakerCcyPairConfig: Exception occurred while enabling " + ccyPair.getName()
                    + " for " + counterparty.getShortName() + " in TakerCcyPairConfig of " + taker.getShortName(), e);
        }
        finally {
            AdminTransactionUtil.releaseTransaction();
        }
        result = new Tuple<Integer, String>();
        result.first = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
        result.second = CommonErrorCodes.INTERNAL_SERVER_ERROR;
        return result;
    }

    public Tuple<Integer, String> disableCcyPairInTakerCcyPairConfig(Organization taker, Organization counterparty,
                                                                     OrganizationRelationship orgRel, FXRateBasis ccyPair,
                                                                     boolean sendNotification) {
        Tuple<Integer, String> result;
        CurrencyPairGroup relCpg = orgRel.getOneClickCurrencyPairGroup();
        boolean existingIsDefault = relCpg == null;
        CurrencyPairGroup takerDefaultCpg = taker.getTakerOrganizationFunction().getOneClickCurrencyPairGroup();

        try {
            Collection<Class> writeClasses = new ArrayList<Class>();
            writeClasses.add(CurrencyPairGroupC.class);
            writeClasses.add(FXRateConventionC.class);
            writeClasses.add(OrganizationRelationshipC.class);
            AdminTransactionUtil.startTransaction();
            AdminTransactionUtil.addDefaultReadOnlyClasses();
            AdminTransactionUtil.addWriteableClass(writeClasses);

            if (existingIsDefault) {
                if (takerDefaultCpg == null) {
                    log.warn("Taker Default CcyPairConfig is not set for " + taker.getShortName());
                    result = new Tuple<Integer, String>();
                    result.first = HttpServletResponse.SC_NOT_FOUND;
                    result.second = CommonErrorCodes.CCY_PAIR_CONFIGURATION_NOT_FOUND;
                    return result;
                }
                // Create a new CPG & associate on orgRel
                relCpg = CurrencyFactory.newCurrencyPairGroup();
                relCpg = (CurrencyPairGroup) relCpg.getRegisteredObject();
                String shortName = Long.toString(taker.getObjectID()) + Long.toString(counterparty.getObjectID()) + "TOCPG";
                String longName = taker.getShortName() + "_" + counterparty.getShortName() + " Taker CPG";
                relCpg.setShortName(shortName);
                relCpg.setLongName(longName);
                relCpg.setNamespace((Namespace) taker.getNamespace().getRegisteredObject());
            }
            else {
                //Update the associated CPG on orgRel
                relCpg = (CurrencyPairGroup) relCpg.getRegisteredObject();
            }
            if (!relCpg.getCurrencyPairs().contains(ccyPair.getCurrencyPair())) {
                log.info("CurrencyPair " + ccyPair.getName() + " is already disabled in Taker CcyPairConfig for "
                        + counterparty.getShortName());
                result = new Tuple<Integer, String>();
                result.first = HttpServletResponse.SC_OK;
                return result;
            }
            Collection<FXRateBasis> excludedCps = new ArrayList<FXRateBasis>();
            if (relCpg.getExcludedFXRateBasis() != null) {
                excludedCps.addAll(relCpg.getExcludedFXRateBasis());
            }
            excludedCps.add(ccyPair);

            relCpg.setExcludedFXRateBasis(excludedCps);

            AuditEvent auditEvent = getGenericAuditData(orgRel);
            auditEvent.setComponent(OrganizationAuditConstants.TAKER_CCYPAIR);
            auditEvent.setAction("CP_DISABLE");
            auditEvent.setOrganization((Organization) orgRel.getOwner());
            auditEvent.setStringArg1(counterparty.getShortName());
            auditEvent.setStringArg3(taker.getShortName());
            auditEvent.setStringArg9(ccyPair.getName());
            auditAction(OrganizationAuditConstants.TAKER_CCYPAIR, auditEvent);

            if (sendNotification) {
                Stream stream = StreamUtil.getStream(taker.getDefaultDealingEntity(), counterparty);
                if (stream != null) {
                    // Notification
                    HashMap<String, String> notificationMAP = new HashMap<String, String>();
                    notificationMAP.put(ISCommonConstants.TAKER_KEY, taker.getShortName());
                    notificationMAP.put(TakerCurrencyPairUpdateFunctorC.QUOTE_CONVENTION,
                            (takerDefaultCpg.getFXRateConvention() == null ? ISCommonConstants.QUOTE_CONVENTION : takerDefaultCpg.getFXRateConvention().getShortName()));
                    notificationMAP.put(ISCommonConstants.CURRENCY_PAIR, ccyPair.getCurrencyPair().getName());
                    notificationMAP.put(ISCommonConstants.MAKER_KEY, counterparty.getShortName());
                    notificationMAP.put(ISCommonConstants.STREAM, stream.getShortName());
                    notificationMAP.put(ISCommonConstants.ACTION_KEY, ISCommonConstants.DISABLED_KEY);
                    NotificationUtil.notifyFunctors(notificationMAP, "FUNCTORS.TAKER_CCYPAIR.ENABLEDISABLE");
                }
            }
            AdminTransactionUtil.commitTransaction();

            result = new Tuple<Integer, String>();
            result.first = HttpServletResponse.SC_OK;
            return result;
        }
        catch (Exception e) {
            log.warn("disableCcyPairInTakerCcyPairConfig: Exception occurred while disabling " + ccyPair.getName()
                    + " for " + counterparty.getShortName() + " in TakerCcyPairConfig of " + taker.getShortName(), e);
        }
        finally {
            AdminTransactionUtil.releaseTransaction();
        }
        result = new Tuple<Integer, String>();
        result.first = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
        result.second = CommonErrorCodes.INTERNAL_SERVER_ERROR;
        return result;
    }

    public Tuple<Integer, String> setTakerCounterpartyCcyPairConfig(Organization taker, Organization counterparty,
                                                                    OrganizationRelationship orgRel,
                                                                    RelationCurrencyPairRequest inputRelCcyPairReq) {
        Tuple<Integer, String> result = new Tuple<Integer, String>();
        CurrencyPairGroup relCpg = orgRel.getOneClickCurrencyPairGroup();
        boolean existingIsDefault = relCpg == null;
        boolean inputIsDefault = inputRelCcyPairReq.getUseDefault() != null && inputRelCcyPairReq.getUseDefault();
        if (inputIsDefault) {
            result = new Tuple<Integer, String>();
            if (existingIsDefault) {
                log.info("Taker CounterpartyCpConfig for " + counterparty.getShortName()
                        + " in " + taker.getShortName() + " is already set to true");
                result.first = HttpServletResponse.SC_OK;
            }
            else {
                if (removeRelationCpg(orgRel, relCpg)) {
                    result.first = HttpServletResponse.SC_OK;
                }
                else {
                    result.first = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
                    result.second = CommonErrorCodes.INTERNAL_SERVER_ERROR;
                }
            }
            return result;
        }
        else {
            // Validate Quote Convention
            String inputQcString = inputRelCcyPairReq.getQuoteConvention();
            FXRateConvention qc = existingIsDefault ? null : relCpg.getFXRateConvention();
            if (!StringUtils.isNullOrEmptyString(inputQcString)) {
                if (existingIsDefault || relCpg.getFXRateConvention().getShortName().equals(inputQcString)) {
                    qc = MarketDefinitionUtil.getQuoteConvention(inputQcString);
                    if (qc == null) {
                        log.warn("QuoteConvention " + inputQcString + " not found");
                        result = new Tuple<Integer, String>();
                        result.first = HttpServletResponse.SC_NOT_FOUND;
                        result.second = CommonErrorCodes.QUOTE_CONVENTION_NOT_FOUND;
                        return result;
                    }
                }
            }
            if (qc == null) {
                log.warn("QuoteConvention not present in Input & also not defined in existing CurrencyPairConfig");
                result = new Tuple<Integer, String>();
                result.first = HttpServletResponse.SC_BAD_REQUEST;
                result.second = CommonErrorCodes.BAD_INPUT_REQUEST;
                return result;
            }

            Collection<CurrencyPairGroup> cpgs = new ArrayList<CurrencyPairGroup>();
            Collection<FXRateBasis> includedCps = new ArrayList<FXRateBasis>();
            Collection<FXRateBasis> excludedCps = new ArrayList<FXRateBasis>();
            StringBuilder cpBuilderOld = new StringBuilder();
            StringBuilder cpBuilderNew = new StringBuilder();
            result = validateAndPopulateInputCPG(taker, qc, relCpg, inputRelCcyPairReq, cpgs, includedCps, excludedCps, cpBuilderOld, cpBuilderNew);
            if (result.first != HttpServletResponse.SC_OK) {
                return result;
            }

            try {
                Collection<Class> writeClasses = new ArrayList<Class>();
                writeClasses.add(CurrencyPairGroupC.class);
                writeClasses.add(FXRateConventionC.class);
                writeClasses.add(OrganizationRelationshipC.class);
                AdminTransactionUtil.startTransaction();
                AdminTransactionUtil.addDefaultReadOnlyClasses();
                AdminTransactionUtil.addWriteableClass(writeClasses);

                if (existingIsDefault) {
                    // Create a new CPG & associate on orgRel
                    relCpg = CurrencyFactory.newCurrencyPairGroup();
                    relCpg = (CurrencyPairGroup) relCpg.getRegisteredObject();
                    String shortName = Long.toString(taker.getObjectID()) + Long.toString(counterparty.getObjectID()) + "TOCPG";
                    String longName = taker.getShortName() + "_" + counterparty.getShortName() + " Taker CPG";
                    relCpg.setShortName(shortName);
                    relCpg.setLongName(longName);
                    relCpg.setNamespace((Namespace) taker.getNamespace().getRegisteredObject());
                } else {
                    //Update the associated CPG on orgRel
                    relCpg = (CurrencyPairGroup) relCpg.getRegisteredObject();
                }
                relCpg.setFXRateConvention(AdminTransactionUtil.register(qc));
                relCpg.setCurrencyPairGroups(AdminTransactionUtil.register(cpgs));
                relCpg.setIncludedFXRateBasis(AdminTransactionUtil.register(includedCps));
                relCpg.setExcludedFXRateBasis(AdminTransactionUtil.register(excludedCps));
                orgRel = (OrganizationRelationship) orgRel.getRegisteredObject();
                orgRel.setOneClickCurrencyPairGroup(relCpg);
                AdminTransactionUtil.commitTransaction();

                //Create Audit Update Event
                if (cpBuilderOld.length() > 0 && cpBuilderNew.length() > 0) {
                    AuditEvent auditEvent = getGenericAuditData(orgRel);
                    auditEvent.setComponent(OrganizationAuditConstants.TAKER_CCYPAIR);
                    auditEvent.setAction("CP_UPDATE");
                    auditEvent.setOrganization((Organization) orgRel.getOwner());
                    auditEvent.setStringArg1(orgRel.getRelatedOrganization() == null ? null : orgRel.getRelatedOrganization().getShortName());
                    auditEvent.setStringArg3(((Organization) orgRel.getOwner()).getShortName());
                    auditEvent.setStringArg9(cpBuilderOld.toString());
                    auditEvent.setStringArg18(cpBuilderNew.toString());
                    auditAction(OrganizationAuditConstants.TAKER_CCYPAIR, auditEvent);
                }

                result.first = HttpServletResponse.SC_OK;
                return result;
            }
            catch (Exception e) {
                log.warn("setTakerCounterpartyCcyPairConfig: Exception occurred while setting TakerCcyPairConfig for "
                        + counterparty.getShortName() + " in " + taker.getShortName(), e);
            }
            finally {
                AdminTransactionUtil.releaseTransaction();
            }
        }
        result.first = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
        result.second = CommonErrorCodes.INTERNAL_SERVER_ERROR;
        return result;
    }

    public DefaultCurrencyPairResponse getDefaultCurrencyPairData(CurrencyPairGroup defaultCpg) {
        DefaultCurrencyPairResponse result = new DefaultCurrencyPairResponse();
        if (defaultCpg != null) {
            populateDefaultCurrencyPairData(defaultCpg, result);
        }
        return result;
    }

    public RelationCurrencyPairResponse getRelationCurrencyPairData(CurrencyPairGroup relationCpg, boolean isDefault) {
        RelationCurrencyPairResponse result = new RelationCurrencyPairResponse();
        if (relationCpg != null) {
            result.setUseDefault(isDefault);
            populateDefaultCurrencyPairData(relationCpg, result);
        }
        return result;
    }

    public InstanceCreator<DefaultCurrencyPairRequest> createInstanceCreatorForUpdate(final DefaultCurrencyPairRequest entity) {
        return new InstanceCreator<DefaultCurrencyPairRequest>() {
            @Override
            public DefaultCurrencyPairRequest createInstance(Type type) {
                Gson gson = new Gson();
                String json = gson.toJson(entity);
                return gson.fromJson(json, DefaultCurrencyPairRequest.class);
            }
        };
    }


    private AuditEvent getGenericAuditData(Entity entity) {
        User loginUser = GenericAdminUtil.getContextUser();
        AuditEventC auditEvent = new AuditEventC();
        auditEvent.setUser(loginUser);
        auditEvent.setNamespace(entity.getNamespace());
        auditEvent.setEntity1(entity);
        auditEvent.setModifiedNamespace(entity.getNamespace() == null ? null : entity.getNamespace().getShortName());
        auditEvent.setModifiedByUser(loginUser.getShortName());
        auditEvent.setModifiedByNamespace(loginUser.getNamespace().getShortName());
        return auditEvent;
    }

    private void auditAction(String component, AuditEvent auditEvent) {
        AuditManager.audit(component, auditEvent);
    }

    private Map<String, String> getCurrencyPairToGroupMap(Collection<CurrencyPairGroup> cpgCollection) {
        if (cpgCollection == null || cpgCollection.isEmpty()) {
            return Collections.emptyMap();
        }
        List<CurrencyPairGroup> cpgList = new ArrayList<CurrencyPairGroup>(cpgCollection);
        Collections.sort(cpgList, ComparatorUtilities.SHORT_NAME_COMPARATOR);

        Map<String, String> cpToCpgMap = new HashMap<String, String>();
        for (CurrencyPairGroup cpgEntry: cpgList) {
            Collection<CurrencyPair> cpCollection = cpgEntry.getCurrencyPairs();
            if (cpCollection != null && !cpCollection.isEmpty()) {
                for (CurrencyPair cp: cpCollection) {
                    if (!cpToCpgMap.containsKey(cp.getName())) {
                        cpToCpgMap.put(cp.getName(), cpgEntry.getShortName());
                    }
                }
            }
        }
        return cpToCpgMap;
    }

    private void populateDefaultCurrencyPairData(CurrencyPairGroup defaultCpg, DefaultCurrencyPairResponse result) {
        // Quote Convention
        String qc = defaultCpg.getFXRateConvention() == null ? "" : defaultCpg.getFXRateConvention().getShortName();
        result.setQuoteConvention(qc);

        // CurrencyPairGroups
        List<String> cpGroups = new ArrayList<String>();
        if (defaultCpg.getCurrencyPairGroups() != null && !defaultCpg.getCurrencyPairGroups().isEmpty()) {
            for (Object cpg : defaultCpg.getCurrencyPairGroups()) {
                cpGroups.add(((CurrencyPairGroup) cpg).getName());
            }
        }
        result.setCurrencyPairGroups(cpGroups);

        // IncludedCurrencyPairs
        List<String> includedCp = new ArrayList<String>();
        if (defaultCpg.getFXRateBasisReferences() != null && !defaultCpg.getFXRateBasisReferences().isEmpty()) {
            for (Object cp : defaultCpg.getFXRateBasisReferences()) {
                includedCp.add(((FXRateBasis) cp).getName());
            }
        }
        result.setIncludedCurrencyPairs(includedCp);

        // ExcludedCurrencyPairs
        Map<String, String> cpToCpgMap = getCurrencyPairToGroupMap(defaultCpg.getCurrencyPairGroups());
        List<CurrencyPairMapModel> currencyPairs = new ArrayList<CurrencyPairMapModel>();
        List<String> excludedCp = new ArrayList<String>();
        if (defaultCpg.getExcludedFXRateBasis() != null && !defaultCpg.getExcludedFXRateBasis().isEmpty()) {
            for (Object cp : defaultCpg.getExcludedFXRateBasis()) {
                CurrencyPair ccyPair = ((FXRateBasis) cp).getCurrencyPair();
                excludedCp.add(ccyPair.getName());
                String classification = MarketDefinitionUtil.getClassification(ccyPair);
                CurrencyPairMapModel entry = new CurrencyPairMapModel();
                entry.setCurrencyPair(ccyPair.getName());
                entry.setCurrencyPairGroup(cpToCpgMap.get(ccyPair.getName()));
                entry.setClassification(classification);
                entry.setEnabled(false);
                currencyPairs.add(entry);
            }
        }
        result.setExcludedCurrencyPairs(excludedCp);

        // CurrencyPairs
        if (defaultCpg.getCurrencyPairs() != null && !defaultCpg.getCurrencyPairs().isEmpty()) {
            for (CurrencyPair pair : defaultCpg.getCurrencyPairs()) {
                String classification = MarketDefinitionUtil.getClassification(pair);
                CurrencyPairMapModel entry = new CurrencyPairMapModel();
                entry.setCurrencyPair(pair.getName());
                entry.setCurrencyPairGroup(cpToCpgMap.get(pair.getName()));
                entry.setClassification(classification);
                entry.setEnabled(true);
                currencyPairs.add(entry);
            }
        }
        result.setCurrencyPairs(currencyPairs);
    }

    private boolean removeRelationCpg(OrganizationRelationship orgRel, CurrencyPairGroup relCpg) {
        try {
            Collection<Class> writableClass = new ArrayList<Class>();
            writableClass.add(com.integral.finance.currency.CurrencyPairGroupC.class);
            writableClass.add(com.integral.user.OrganizationRelationshipC.class);
            writableClass.add(com.integral.user.OrganizationC.class);
            AdminTransactionUtil.startTransaction();
            AdminTransactionUtil.addDefaultReadOnlyClasses();
            AdminTransactionUtil.addWriteableClass(writableClass);

            relCpg = (CurrencyPairGroup) relCpg.getRegisteredObject();
            orgRel = AdminTransactionUtil.register(orgRel);
            orgRel.setOneClickCurrencyPairGroup(null);
            AdminTransactionUtil.deleteObject(relCpg);
            AdminTransactionUtil.commitTransaction();

            AuditEvent auditEvent = getGenericAuditData(orgRel);
            auditEvent.setComponent(OrganizationAuditConstants.TAKER_CCYPAIR);
            auditEvent.setAction("CP_UPDATE");
            auditEvent.setOrganization((Organization) orgRel.getOwner());
            auditEvent.setStringArg1(orgRel.getRelatedOrganization() == null ? null : orgRel.getRelatedOrganization().getShortName());
            auditEvent.setStringArg3(((Organization) orgRel.getOwner()).getShortName());
            auditEvent.setStringArg9("UseDefault: false");
            auditEvent.setStringArg18("UseDefault: true");
            auditAction(OrganizationAuditConstants.TAKER_CCYPAIR, auditEvent);

            return true;
        }
        catch (Exception e) {
            log.warn("removeRelationCpg: Exception ", e);
        }
        finally {
            AdminTransactionUtil.releaseTransaction();
        }
        return false;
    }

    private Tuple<Integer, String> validateAndPopulateInputCPG(Organization taker, FXRateConvention qc,
                                                               CurrencyPairGroup existingCpg,
                                                               DefaultCurrencyPairRequest inputCpgRequest,
                                                               Collection<CurrencyPairGroup> cpgs, Collection<FXRateBasis> includedCps,
                                                               Collection<FXRateBasis> excludedCps,
                                                               StringBuilder cpBuilderOld, StringBuilder cpBuilderNew) {
        Tuple<Integer, String> result = new Tuple<Integer, String>();
        List<String> cpgList = inputCpgRequest.getCurrencyPairGroups();
        List<String> includedCpList = inputCpgRequest.getIncludedCurrencyPairs();
        List<String> excludedCpList = inputCpgRequest.getExcludedCurrencyPairs();
        boolean atleastOne = false;
        boolean cpgUpdated = false;
        boolean includedCpUpdated = false;
        boolean excludedCpUpdated = false;

        // Validate that all CurrencyPairGroup are valid
        if (cpgList != null) {
            for (String cpgName: cpgList) {
                CurrencyPairGroup cpg = MarketDefinitionUtil.getCurrencyPairGroup(taker.getNamespace(), cpgName);
                if (cpg == null) {
                    cpg = MarketDefinitionUtil.getCurrencyPairGroup(GenericAdminUtil.getMainNamespace(), cpgName);
                }
                if (cpg == null) {
                    log.warn("CurrencyPairGroup " + cpgName + " does not exist");
                    result.first = HttpServletResponse.SC_BAD_REQUEST;
                    result.second = CommonErrorCodes.CCY_PAIR_GROUP_NOT_FOUND;
                    return result;
                }
                cpgs.add(cpg);
                atleastOne = true;
                if (!cpgUpdated) {
                    if (existingCpg == null || existingCpg.getCurrencyPairGroups() == null
                            || !existingCpg.getCurrencyPairGroups().contains(cpg)) {
                        cpgUpdated = true;
                    }
                }
            }
        }
        else if (existingCpg != null
                && existingCpg.getCurrencyPairGroups() != null
                && !existingCpg.getCurrencyPairGroups().isEmpty()) {
            atleastOne = true;
            cpgs.addAll(existingCpg.getCurrencyPairGroups());
        }
        if (cpgUpdated) {
            cpBuilderOld.append("CPGroups:")
                    .append((existingCpg != null && existingCpg.getCurrencyPairGroups() != null) ? StringUtils.getCSV(existingCpg.getCurrencyPairGroups()) : "");
            cpBuilderNew.append("CPGroups:").append(StringUtils.getCSVFromCollection(cpgList));
        }

        // Validate that all IncludedCurrencyPair are valid
        if (includedCpList != null) {
            for (String includedCpName: includedCpList) {
                FXRateBasis includedCp = MarketDefinitionUtil.getFXRateBasis(qc, includedCpName);
                if (includedCp == null) {
                    log.warn("IncludedCurrencyPair " + includedCpName + " does not exist");
                    result.first = HttpServletResponse.SC_BAD_REQUEST;
                    result.second = CommonErrorCodes.CCY_PAIR_NOT_FOUND;
                    return result;
                }
                includedCps.add(includedCp);
                atleastOne = true;
                if (!includedCpUpdated) {
                    if (existingCpg == null || existingCpg.getIncludedFXRateBasis() == null
                            || !existingCpg.getIncludedFXRateBasis().contains(includedCp)) {
                        includedCpUpdated = true;
                    }
                }
            }
        }
        else if (existingCpg != null
                && existingCpg.getIncludedFXRateBasis() != null
                && !existingCpg.getIncludedFXRateBasis().isEmpty()) {
            atleastOne = true;
            includedCps.addAll(existingCpg.getIncludedFXRateBasis());
        }
        if (includedCpUpdated) {
            if (cpBuilderOld.length() != 0) {
                cpBuilderOld.append(", ");
            }
            if (cpBuilderNew.length() != 0) {
                cpBuilderNew.append(", ");
            }
            cpBuilderOld.append("Included CP:")
                    .append((existingCpg != null && existingCpg.getIncludedFXRateBasis() != null) ? LiquidityRulesAndProvisioningUtil.getCSV(existingCpg.getIncludedFXRateBasis()) : "");
            cpBuilderNew.append("Included CP:").append(StringUtils.getCSVFromCollection(includedCpList));
        }

        // Validate that atleast one CurrencyPair / CurrencyPairGroup is present
        if (!atleastOne) {
            log.warn("CurrencyPairConfig should have atleast one CurrencyPairGroup or IncludedCurrencyPair");
            result.first = HttpServletResponse.SC_BAD_REQUEST;
            result.second = CommonErrorCodes.BAD_INPUT_REQUEST;
            return result;
        }

        // Validate that all ExcludedCurrencyPair are valid
        if (excludedCpList != null) {
            for (String excludedCpName: excludedCpList) {
                FXRateBasis excludedCp = MarketDefinitionUtil.getFXRateBasis(qc, excludedCpName);
                if (excludedCp == null) {
                    log.warn("ExcludedCurrencyPair " + excludedCpName + " does not exist");
                    result.first = HttpServletResponse.SC_BAD_REQUEST;
                    result.second = CommonErrorCodes.CCY_PAIR_NOT_FOUND;
                    return result;
                }
                excludedCps.add(excludedCp);
                if (!excludedCpUpdated) {
                    if (existingCpg == null || existingCpg.getExcludedFXRateBasis() == null
                            || !existingCpg.getExcludedFXRateBasis().contains(excludedCp)) {
                        excludedCpUpdated = true;
                    }
                }
            }
        }
        else if (existingCpg != null
                && existingCpg.getExcludedFXRateBasis() != null
                && !existingCpg.getExcludedFXRateBasis().isEmpty()) {
            excludedCps.addAll(existingCpg.getExcludedFXRateBasis());
        }
        if (excludedCpUpdated) {
            if (cpBuilderOld.length() != 0) {
                cpBuilderOld.append(", ");
            }
            if (cpBuilderNew.length() != 0) {
                cpBuilderNew.append(", ");
            }
            cpBuilderOld.append("Excluded CP:")
                    .append((existingCpg != null && existingCpg.getExcludedFXRateBasis() != null) ? LiquidityRulesAndProvisioningUtil.getCSV(existingCpg.getExcludedFXRateBasis()) : "");
            cpBuilderNew.append("Excluded CP:").append(StringUtils.getCSVFromCollection(excludedCpList));
        }

        result.first = HttpServletResponse.SC_OK;
        return result;
    }
}
