package com.integral.admin.api.organization;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.InstanceCreator;
import com.integral.admin.CommonErrorCodes;
import com.integral.admin.api.model.referencedata.currencypair.DefaultCurrencyPairRequest;
import com.integral.admin.api.model.referencedata.currencypair.DefaultCurrencyPairResponse;
import com.integral.admin.api.model.referencedata.currencypair.RelationCurrencyPairRequest;
import com.integral.admin.api.model.referencedata.currencypair.RelationCurrencyPairResponse;
import com.integral.admin.api.model.referencedata.organization.TakerLiquidityBasicModel;
import com.integral.admin.api.model.referencedata.organization.TakerLiquidityDetailModel;
import com.integral.admin.auth.ApiAuthorization;
import com.integral.admin.utils.StringUtils;
import com.integral.admin.utils.marketdefinition.MarketDefinitionUtil;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.admin.utils.organization.UserUtil;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.fxiapi.BaseResponse;
import com.integral.fxiapi.Status;
import com.integral.commons.Tuple;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.validator.AccountIDValidator;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Controller
@RequestMapping(value = "/customers")
public class CustomerOrgController {
    private Log log = LogFactory.getLog(CustomerOrgController.class);

    @Autowired
    private CustomerOrgService service;

    /**
     * Get Basic Liquidity Configuration of Customer
     *
     * @param customer     Organization for which Liquidity Config needs to be fetched
     * @param cptys        Counterparties with which the config needs to be fetched
     * @param httpResponse HttpServletResponse object
     * @return Basic Liquidity Config for the given orgName
     */
    @ApiAuthorization(permissions = {"OrgDetailMU", "OrgDetailRO"})
    @RequestMapping(value = "/liquidity/basic/{customer}", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getTakerBasicLiquidityConfig(@PathVariable("customer") String customer,
                                              @RequestParam(value="cptys", required=false) String cptys,
                                              final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(customer)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + customer);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization taker = OrganizationUtil.getOrganization(customer);
            if (taker == null) {
                log.warn("Customer " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            if (!(OrganizationUtil.manageCurrencyPairConfig(taker))) {
                log.warn("Customer " + customer + " is not enabled to Manage CurrencyPairConfig");
                return new BaseResponse(Status.OK) {
                    public List<TakerLiquidityBasicModel> getCustomerData() {
                        return null;
                    }
                };
            }

            TakerOrganizationFunction takerOrgFunc = taker.getTakerOrganizationFunction();
            if (takerOrgFunc == null) {
                log.warn("Customer " + customer + " has no TakerOrganizationFunction");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.OK) {
                    public List<TakerLiquidityBasicModel> getCustomerData() {
                        return null;
                    }
                };
            }
            Collection<Organization> cptyOrgList;
            if (StringUtils.isNullOrEmptyString(cptys)) {
                cptyOrgList = taker.getRelatedActiveOrganizations(OrganizationRelationship.FI_LP_RELATIONSHIP);
            }
            else {
                String[] cptyList = cptys.split(",");
                cptyOrgList = new ArrayList<Organization>();
                for (String cpty: cptyList) {
                    Organization cptyOrg = OrganizationUtil.getOrganization(cpty);
                    if (cptyOrg == null) {
                        log.warn("Counterparty " + cpty + " not found");
                        httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
                    }
                    cptyOrgList.add(cptyOrg);
                }
            }

            final List<TakerLiquidityBasicModel> result = new ArrayList<TakerLiquidityBasicModel>();
            String clientTag = taker.getExternalSystemId(AccountIDValidator.CLIENT_TAG_ID) == null
                    ? null : taker.getExternalSystemId(AccountIDValidator.CLIENT_TAG_ID).getSystemId();
            for (Organization cptyOrg: cptyOrgList) {
                if (OrganizationUtil.isValidProvider(cptyOrg)) {
                    OrganizationRelationship rel = OrganizationUtil.getOrganizationRelationshipByClassification(taker,
                            cptyOrg, OrganizationRelationship.FI_LP_RELATIONSHIP);
                    if (rel == null) {
                        log.warn("Relationship of " + customer + " with " + cptyOrg.getShortName() + " not found");
                        httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        return new BaseResponse(Status.ERROR, CommonErrorCodes.RELATIONSHIP_NOT_FOUND);
                    }

                    TakerLiquidityBasicModel entry = service.getLiquidityBasicModel(taker, cptyOrg, rel, clientTag);
                    if (entry == null) {
                        log.warn("Taker Counterparty CurrencyPairConfig for " + cptyOrg.getShortName()
                                + " in " + customer + " not found");
                        httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
                    }
                    result.add(entry);
                }
            }

            return new BaseResponse(Status.OK) {
                public List<TakerLiquidityBasicModel> getCustomerData() {
                    return result;
                }
            };
        }
        catch (Throwable th) {
            log.warn("getTakerBasicLiquidityConfig: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Get Detail Liquidity Configuration of Customer
     *
     * @param customer     Organization for which Liquidity Config needs to be fetched
     * @param cptys        Counterparties with which the config needs to be fetched
     * @param httpResponse HttpServletResponse object
     * @return Detail Liquidity Config for the given orgName
     */
    @ApiAuthorization(permissions = {"OrgDetailMU", "OrgDetailRO"})
    @RequestMapping(value = "/liquidity/detail/{customer}", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getTakerDetailLiquidityConfig(@PathVariable("customer") String customer,
                                               @RequestParam(value="cptys", required=false) String cptys,
                                               final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(customer)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + customer);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization taker = OrganizationUtil.getOrganization(customer);
            if (taker == null) {
                log.warn("Customer " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            if (!(OrganizationUtil.manageCurrencyPairConfig(taker))) {
                log.warn("Customer " + customer + " is not enabled to Manage CurrencyPairConfig");
                return new BaseResponse(Status.OK) {
                    public List<TakerLiquidityBasicModel> getCustomerData() {
                        return null;
                    }
                };
            }

            TakerOrganizationFunction takerOrgFunc = taker.getTakerOrganizationFunction();
            if (takerOrgFunc == null) {
                log.warn("Customer " + customer + " has no TakerOrganizationFunction");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.OK) {
                    public List<TakerLiquidityBasicModel> getCustomerData() {
                        return null;
                    }
                };
            }
            Collection<Organization> cptyOrgList;
            if (StringUtils.isNullOrEmptyString(cptys)) {
                cptyOrgList = taker.getRelatedActiveOrganizations(OrganizationRelationship.FI_LP_RELATIONSHIP);
            }
            else {
                String[] cptyList = cptys.split(",");
                cptyOrgList = new ArrayList<Organization>();
                for (String cpty: cptyList) {
                    Organization cptyOrg = OrganizationUtil.getOrganization(cpty);
                    if (cptyOrg == null) {
                        log.warn("Counterparty " + cpty + " not found");
                        httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
                    }
                    cptyOrgList.add(cptyOrg);
                }
            }

            final List<TakerLiquidityDetailModel> result = new ArrayList<TakerLiquidityDetailModel>();
            String clientTag = taker.getExternalSystemId(AccountIDValidator.CLIENT_TAG_ID) == null
                    ? null : taker.getExternalSystemId(AccountIDValidator.CLIENT_TAG_ID).getSystemId();
            for (Organization cptyOrg: cptyOrgList) {
                if (OrganizationUtil.isValidProvider(cptyOrg)) {
                    OrganizationRelationship rel = OrganizationUtil.getOrganizationRelationshipByClassification(taker,
                            cptyOrg, OrganizationRelationship.FI_LP_RELATIONSHIP);
                    if (rel == null) {
                        log.warn("Relationship of " + customer + " with " + cptyOrg.getShortName() + " not found");
                        httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        return new BaseResponse(Status.ERROR, CommonErrorCodes.RELATIONSHIP_NOT_FOUND);
                    }

                    List<TakerLiquidityDetailModel> entry = service.getLiquidityDetailModel(taker, cptyOrg, rel, clientTag);
                    if (entry == null) {
                        log.warn("Taker Counterparty CurrencyPairConfig for " + cptyOrg.getShortName()
                                + " in " + customer + " not found");
                        httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
                    }
                    result.addAll(entry);
                }
            }

            return new BaseResponse(Status.OK) {
                public List<TakerLiquidityDetailModel> getCustomerData() {
                    return result;
                }
            };
        }
        catch (Throwable th) {
            log.warn("getTakerDetailLiquidityConfig: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Get Taker Default CurrencyPair Configuration
     *
     * @param customer     Organization for which Taker Default CurrencyPair Config needs to be fetched
     * @param httpResponse HttpServletResponse object
     * @return Taker Default CurrencyPair Config for the given orgName
     */
    @ApiAuthorization(permissions = {"OrgDetailMU", "OrgDetailRO"})
    @RequestMapping(value = "/currencypairs/{customer}", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getTakerDefaultCcyPairConfig(@PathVariable("customer") String customer,
                                              final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(customer)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + customer);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization taker = OrganizationUtil.getOrganization(customer);
            if (taker == null) {
                log.warn("Customer " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }

            if (!(OrganizationUtil.manageCurrencyPairConfig(taker))) {
                log.warn("Customer " + customer + " is not enabled to Manage CurrencyPairConfig");
                return new BaseResponse(Status.OK) {
                    public DefaultCurrencyPairResponse getCustomerData() {
                        return null;
                    }
                };
            }

            TakerOrganizationFunction takerOrgFunc = taker.getTakerOrganizationFunction();
            if (takerOrgFunc == null) {
                log.warn("Customer " + customer + " has no TakerOrganizationFunction");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.OK) {
                    public DefaultCurrencyPairResponse getCustomerData() {
                        return null;
                    }
                };
            }
            final DefaultCurrencyPairResponse defaultCcyPairConfig = service.getTakerDefaultCcyPairConfig(takerOrgFunc);
            if (defaultCcyPairConfig == null) {
                log.info("Customer " + customer + " does not have Taker Default CurrencyPairConfig");
            }
            return new BaseResponse(Status.OK) {
                public DefaultCurrencyPairResponse getCustomerData() {
                    return defaultCcyPairConfig;
                }
            };
        }
        catch (Throwable th) {
            log.warn("getTakerDefaultCcyPairConfig: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Update Taker Default CurrencyPair Configuration
     *
     * @param customer     Organization for which Taker Default CurrencyPair Config needs to be updated
     * @param httpResponse HttpServletResponse object
     * @return Updated Taker Default CurrencyPair Config
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/currencypairs/{customer}", method = RequestMethod.PUT)
    public @ResponseBody
    BaseResponse updateTakerDefaultCcyPairConfig(@PathVariable("customer") String customer,
                                                 @RequestBody String inputJson,
                                                 final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(customer)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + customer);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization taker = OrganizationUtil.getOrganization(customer);
            if (taker == null) {
                log.warn("Customer " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }

            if (!(OrganizationUtil.manageCurrencyPairConfig(taker))) {
                log.warn("Customer " + customer + " is not enabled to Manage CurrencyPairConfig");
                httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ACTION_NOT_SUPPORTED);
            }

            TakerOrganizationFunction takerOrgFunc = taker.getTakerOrganizationFunction();
            if (takerOrgFunc == null) {
                if (!service.createTakerOrganizationFunction(taker)) {
                    log.warn("Creating TakerOrganizationFunction for " + customer + " Failed");
                    httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
                }
                takerOrgFunc = taker.getTakerOrganizationFunction();
            }

            CurrencyPairGroup takerDefaultCpg = takerOrgFunc.getOneClickCurrencyPairGroup();
            DefaultCurrencyPairRequest existingData = service.getDefaultCurrencyPairData(takerDefaultCpg);
            InstanceCreator<DefaultCurrencyPairRequest> creator = service.createInstanceCreatorForUpdate(existingData);
            Gson gson = new GsonBuilder().serializeNulls().registerTypeAdapter(DefaultCurrencyPairRequest.class, creator).create();
            DefaultCurrencyPairRequest entityForUpdate = gson.fromJson(inputJson, DefaultCurrencyPairRequest.class);

            Tuple<Integer, String> result = service.setTakerDefaultCcyPairConfig(takerOrgFunc, takerDefaultCpg, existingData, entityForUpdate);
            if (result.first != HttpServletResponse.SC_OK) {
                httpResponse.setStatus(result.first);
                return new BaseResponse(Status.ERROR, result.second);
            }
            final DefaultCurrencyPairResponse defaultCcyPairConfig = service.getTakerDefaultCcyPairConfig(takerOrgFunc);
            if (defaultCcyPairConfig == null) {
                log.warn("Taker Default CurrencyPairConfig for " + customer + " not found post update");
                httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
            }
            return new BaseResponse(Status.OK) {
                public DefaultCurrencyPairResponse getCustomerData() {
                    return defaultCcyPairConfig;
                }
            };
        }
        catch (Throwable th) {
            log.warn("updateTakerDefaultCcyPairConfig: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Enable/Disable given CurrencyPair across all the LPs in Taker CcyPairConfi
     * @param customer      Organization for which Taker Default CurrencyPair Config needs to be fetched
     * @param qc            QuoteConvetion
     * @param ccyPair       CurrencyPair to be enabled/disabled
     * @param enable        Enable/Disable flag. Valid Values=Y(Enable),N(Disable)
     * @param lp            Non-mandatory param. Update for a specified Provider
     * @param httpResponse  HttpServletResponse object
     * @return
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/currencypair/{customer}", method = RequestMethod.PUT)
    public @ResponseBody
    BaseResponse globalTakerCcyPairConfigUpdate(@PathVariable("customer") String customer,
                                                @RequestParam(value="qc") String qc,
                                                @RequestParam(value="cp") String ccyPair,
                                                @RequestParam(value = "enable") String enable,
                                                @RequestParam(value = "lp", required = false) String lp,
                                                final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(customer)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + customer);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization taker = OrganizationUtil.getOrganization(customer);
            if (taker == null) {
                log.warn("Customer " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }

            if (!(OrganizationUtil.manageCurrencyPairConfig(taker))) {
                log.warn("Customer " + customer + " is not enabled to Manage CurrencyPairConfig");
                httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ACTION_NOT_SUPPORTED);
            }

            TakerOrganizationFunction takerOrgFunc = taker.getTakerOrganizationFunction();
            if (takerOrgFunc == null) {
                if (!service.createTakerOrganizationFunction(taker)) {
                    log.warn("Creating TakerOrganizationFunction for " + customer + " Failed");
                    httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
                }
                takerOrgFunc = taker.getTakerOrganizationFunction();
            }

            CurrencyPairGroup takerDefaultCpg = takerOrgFunc.getOneClickCurrencyPairGroup();
            if (takerDefaultCpg == null) {
                log.warn("Default CurrencyPairConfig for " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.CCY_PAIR_CONFIGURATION_NOT_FOUND);
            }

            FXRateConvention quoteConvention = MarketDefinitionUtil.getQuoteConvention(qc);
            FXRateBasis rateBasis = quoteConvention.getFXRateBasis(ccyPair, false);
            if (rateBasis == null) {
                log.warn("CurrencyPair " + ccyPair + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.CCY_PAIR_NOT_FOUND);
            }

            if (enable == null || (!enable.equalsIgnoreCase("Y") && !enable.equalsIgnoreCase("N"))) {
                log.warn("Invalid enable parameter: " + enable);
                httpResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.BAD_INPUT_REQUEST);
            }
            boolean shouldEnable = enable.equalsIgnoreCase("Y");
            Organization lpOrg = null;
            if (!StringUtils.isNullOrEmptyString(lp)) {
                lpOrg = OrganizationUtil.getOrganization(lp);
                if (lpOrg == null) {
                    log.warn("Provider " + lp + " not found");
                    httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                    return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
                }
                OrganizationRelationship rel = OrganizationUtil.getOrganizationRelationshipByClassification(taker,
                        lpOrg, OrganizationRelationship.FI_LP_RELATIONSHIP);
                if (rel == null) {
                    log.warn("Relationship of " + customer + " with " + lp + " not found");
                    httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                    return new BaseResponse(Status.ERROR, CommonErrorCodes.RELATIONSHIP_NOT_FOUND);
                }
                Tuple<Integer, String> result;
                if (shouldEnable) {
                    result = service.enableCcyPairInTakerCcyPairConfig(taker, lpOrg, rel, rateBasis, true);
                }
                else {
                    result = service.disableCcyPairInTakerCcyPairConfig(taker, lpOrg, rel, rateBasis, true);
                }
                if (result.first != HttpServletResponse.SC_OK) {
                    httpResponse.setStatus(result.first);
                    return new BaseResponse(Status.ERROR, result.second);
                }
            }
            else {
                Tuple<Integer, String> result;
                if (shouldEnable) {
                    result = service.enableCcyPairInTakerDefaultCcyPairConfig(takerOrgFunc, takerDefaultCpg, rateBasis);
                }
                else {
                    result = service.disableCcyPairInTakerDefaultCcyPairConfig(takerOrgFunc, takerDefaultCpg, rateBasis);
                }
                if (result.first != HttpServletResponse.SC_OK) {
                    httpResponse.setStatus(result.first);
                    return new BaseResponse(Status.ERROR, result.second);
                }
            }
            return new BaseResponse(Status.OK);
        }
        catch (Throwable th) {
            log.warn("globalTakerCcyPairConfigUpdate: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Get Taker CurrencyPair Configuration with Counterparty
     *
     * @param customer     Organization
     * @param counterparty Counterparty for which CurrencyPair Configuration with needs to be fetched
     * @param httpResponse HttpServletResponse object
     * @return Taker CurrencyPair Configuration with Counterparty
     */
    @ApiAuthorization(permissions = {"OrgDetailMU", "OrgDetailRO"})
    @RequestMapping(value = "/currencypairs/{customer}/{counterparty}", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getTakerCounterpartyCcyPairConfig(@PathVariable("customer") String customer,
                                                   @PathVariable("counterparty") String counterparty,
                                                   final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(customer)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + customer);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization taker = OrganizationUtil.getOrganization(customer);
            if (taker == null) {
                log.warn("Customer " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }

            if (!(OrganizationUtil.manageCurrencyPairConfig(taker))) {
                log.warn("Customer " + customer + " is not enabled to Manage CurrencyPairConfig");
                httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ACTION_NOT_SUPPORTED);
            }

            Organization counterpartyOrg = OrganizationUtil.getOrganization(counterparty);
            if (counterpartyOrg == null) {
                log.warn("Counterparty " + counterparty + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            OrganizationRelationship rel = OrganizationUtil.getOrganizationRelationshipByClassification(taker,
                    counterpartyOrg, OrganizationRelationship.FI_LP_RELATIONSHIP);
            if (rel == null) {
                log.warn("Relationship of " + customer + " with " + counterparty + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.RELATIONSHIP_NOT_FOUND);
            }
            boolean isDefault = false;
            CurrencyPairGroup cpg = rel.getOneClickCurrencyPairGroup();
            if (cpg == null) {
                cpg = taker.getTakerOrganizationFunction() == null ? null: taker.getTakerOrganizationFunction().getOneClickCurrencyPairGroup();
                isDefault = true;
            }
            if (cpg == null) {
                log.warn("Taker Counterparty CurrencyPairConfig for " + counterparty
                        + " in " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            final RelationCurrencyPairResponse result = service.getRelationCurrencyPairData(cpg, isDefault);
            return new BaseResponse(Status.OK) {
                public RelationCurrencyPairResponse getCustomerData() {
                    return result;
                }
            };
        }
        catch (Throwable th) {
            log.warn("getTakerCounterpartyCcyPairConfig: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }


    /**
     * Update Taker CurrencyPair Configuration with Counterparty
     *
     * @param customer     Organization
     * @param counterparty Counterparty for which CurrencyPair Configuration with needs to be updated
     * @param httpResponse HttpServletResponse object
     * @return Updated Taker CurrencyPair Configuration with Counterparty
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/currencypairs/{customer}/{counterparty}", method = RequestMethod.PUT)
    public @ResponseBody
    BaseResponse updateTakerCounterpartyCcyPairConfig(@PathVariable("customer") String customer,
                                                      @PathVariable("counterparty") String counterparty,
                                                      @RequestBody String inputJson,
                                                      final HttpServletResponse httpResponse) {
        try {
            if (!UserUtil.isUserAuthorized(customer)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + customer);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization taker = OrganizationUtil.getOrganization(customer);
            if (taker == null) {
                log.warn("Customer " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }

            if (!(OrganizationUtil.manageCurrencyPairConfig(taker))) {
                log.warn("Customer " + customer + " is not enabled to Manage CurrencyPairConfig");
                httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ACTION_NOT_SUPPORTED);
            }

            Organization counterpartyOrg = OrganizationUtil.getOrganization(counterparty);
            if (counterpartyOrg == null) {
                log.warn("Counterparty " + counterparty + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            OrganizationRelationship rel = OrganizationUtil.getOrganizationRelationshipByClassification(taker,
                    counterpartyOrg, OrganizationRelationship.FI_LP_RELATIONSHIP);
            if (rel == null) {
                log.warn("Relationship of " + customer + " with " + counterparty + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.RELATIONSHIP_NOT_FOUND);
            }
            RelationCurrencyPairRequest inputRelCcyPairReq;
            try {
                Gson gson = new Gson();
                inputRelCcyPairReq = gson.fromJson(inputJson, RelationCurrencyPairRequest.class);
            }
            catch (Throwable th) {
                log.warn("updateTakerCounterpartyCcyPairConfig: Input Format Incorrect", th);
                httpResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.BAD_INPUT_REQUEST);
            }

            Tuple<Integer, String> result = service.setTakerCounterpartyCcyPairConfig(taker, counterpartyOrg, rel, inputRelCcyPairReq);
            if (result.first != HttpServletResponse.SC_OK) {
                httpResponse.setStatus(result.first);
                return new BaseResponse(Status.ERROR, result.second);
            }

            boolean isDefault = false;
            CurrencyPairGroup cpg = rel.getOneClickCurrencyPairGroup();
            if (cpg == null) {
                cpg = taker.getTakerOrganizationFunction() == null ? null: taker.getTakerOrganizationFunction().getOneClickCurrencyPairGroup();
                isDefault = true;
            }
            if (cpg == null) {
                log.warn("Taker Counterparty CurrencyPairConfig for " + counterparty
                        + " in " + customer + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            final RelationCurrencyPairResponse updatedRelCpg = service.getRelationCurrencyPairData(cpg, isDefault);
            return new BaseResponse(Status.OK) {
                public RelationCurrencyPairResponse getCustomerData() {
                    return updatedRelCpg;
                }
            };
        }
        catch (Throwable th) {
            log.warn("updateTakerCounterpartyCcyPairConfig: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }
}
