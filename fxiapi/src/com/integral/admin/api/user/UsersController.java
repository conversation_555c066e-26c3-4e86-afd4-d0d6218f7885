package com.integral.admin.api.user;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.InstanceCreator;
import com.integral.admin.CommonErrorCodes;
import com.integral.admin.api.model.referencedata.user.*;
import com.integral.admin.auth.ApiAuthorization;
import com.integral.admin.constants.CommonConstants;
import com.integral.admin.utils.GenericAdminUtil;
import com.integral.admin.utils.StringUtils;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.admin.utils.organization.UserUtil;
import com.integral.commons.Tuple;
import com.integral.fxiapi.BaseResponse;
import com.integral.fxiapi.Status;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Controller
@RequestMapping(value = "/users")
public class UsersController {

    private Log log = LogFactory.getLog(UsersController.class);
    private Gson gson = new Gson();
    @Autowired
    private UsersService service;

    /**
     * Get details of all the Users for the given organization
     * @param orgName       Organization ShortName for which the user details needs to be fetched
     * @param httpResponse  HttpServletResponse object
     * @return The list of all the Users' details for the given orgName
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}", method = RequestMethod.GET)
    public @ResponseBody
    BaseResponse getUsers(@PathVariable("orgName") String orgName, HttpServletResponse httpResponse) {
        try{
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            boolean isMainUser = UserUtil.isUserInMAINNamespace();
            if (isMainUser) {
                final List<UserIdModel> userList = service.getUsersWithId(org);
                return new BaseResponse(Status.OK){
                    public List<UserIdModel> getUserData(){
                        return userList;
                    }
                };
            } else {
                final List<UserModel> userList = service.getUsers(org);
                return new BaseResponse(Status.OK){
                    public List<UserModel> getUserData(){
                        return userList;
                    }
                };
            }
        }
        catch (Throwable th) {
            log.warn("getUsers: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Get the details of the specified UserId
     * @param userId        UserId whose details needs to be fetched
     * @param httpResponse  HttpServletResponse object
     * @return The details of the specified User
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/id/{userId}", method = RequestMethod.GET)
    public @ResponseBody BaseResponse getUser(@PathVariable("userId") String userId, HttpServletResponse httpResponse) {
        try{
            Long userIndex = null;
            try {
                userIndex = Long.parseLong(userId);
            } catch (NumberFormatException e) {
                log.warn("getUser: userId " + userId + " is not a valid index value");
                httpResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.BAD_INPUT_REQUEST);
            }
            boolean isMainUser = UserUtil.isUserInMAINNamespace();
            if (!isMainUser) {
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            User user = GenericAdminUtil.getEntityById(UserC.class, userIndex);
            if (user == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_NOT_FOUND);
            }
            final UserModel userModel = service.getUserModel(user);
            return new BaseResponse(Status.OK) {
                public UserModel getUserData(){
                    return userModel;
                }
            };
        }
        catch (Throwable th) {
            log.warn("getUser: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Get the details of the specified User of the given organization
     * @param orgName       Organization ShortName for which the user detail needs to be fetched
     * @param userShortName User whose details needs to be fetched
     * @param httpResponse  HttpServletResponse object
     * @return The details of the specified User for the given orgName
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{userShortName}", method = RequestMethod.GET)
    public @ResponseBody BaseResponse getUser(@PathVariable("orgName") String orgName,
                                              @PathVariable( "userShortName" ) String userShortName,
                                              HttpServletResponse httpResponse) {
        try{
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            boolean isMainUser = false;
            if (UserUtil.isUserInMAINNamespace()) {
                isMainUser = true;
            }
            if (isMainUser) {
                final UserIdModel userModel = service.getUserWithId(org, userShortName);
                if (userModel == null) {
                    httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                    return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_NOT_FOUND);
                }

                return new BaseResponse(Status.OK){
                    public UserIdModel getUserData(){
                        return userModel;
                    }
                };
            } else {
                final UserModel userModel = service.getUser(org,userShortName);
                if (userModel == null) {
                    httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                    return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_NOT_FOUND);
                }

                return new BaseResponse(Status.OK){
                    public UserModel getUserData(){
                        return userModel;
                    }
                };
            }
        }
        catch (Throwable th) {
            log.warn("getUser: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Get the details of the Current Logged-In User
     * @param httpResponse  HttpServletResponse object
     * @return The details of the Logged-In User
     */
    @RequestMapping(value = "/current", method = RequestMethod.GET)
    public @ResponseBody BaseResponse getCurrentUser(HttpServletResponse httpResponse) {
        try{
            User user = UserUtil.getSessionUser();
            if (user == null) {
                log.warn("Session for the logged-in User not found");
                httpResponse.setStatus(HttpServletResponse.SC_PROXY_AUTHENTICATION_REQUIRED);
                return new BaseResponse(Status.ERROR, "Session User Not Found");
            }
            Organization org = user.getOrganization();
            if (org == null) {
                log.warn("Organization of User " + user.getShortName() + " not found");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, "Session Organization Not Found");
            }

            if (!org.isActive()) {
                log.warn("Organization of User " + user.getShortName() + " is Inactive");
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, "Session Organization Not Found");
            }

            final UserProfileModel userProfileModel = service.getUserProfileModel(user);
            if (userProfileModel == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_NOT_FOUND);
            }

            return new BaseResponse(Status.OK){
                public UserProfileModel getUserData(){
                    return userProfileModel;
                }
            };
        }
        catch (Throwable th) {
            log.warn("getCurrentUser: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Create User for the given organization
     * @param orgName       Organization ShortName for which the user needs to be created
     * @param userData {@link UserCreateModel}
     * @param httpResponse  HttpServletResponse object
     * @return The details of the created User for the given orgName
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}", method = RequestMethod.POST)
    public @ResponseBody BaseResponse createUser(@PathVariable( "orgName" ) String orgName,
                                                 @RequestBody UserCreateModel userData,
                                                 final HttpServletResponse httpResponse) {
        try{
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            if (userData == null || StringUtils.isNullOrEmptyString(userData.getShortName())) {
                httpResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.BAD_INPUT_REQUEST);
            }
            User user = GenericAdminUtil.getNamedEntityByShortNameAndNamespace(org.getNamespace(), com.integral.user.UserC.class, userData.getShortName());
            if (user != null) {
                httpResponse.setStatus(HttpServletResponse.SC_CONFLICT);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_EXISTS);
            }
            Tuple<Integer, String> result = service.createUser(org, userData);
            if (result.first != HttpServletResponse.SC_OK) {
                log.warn("Creating User " + userData.getShortName() + " in " + orgName + " Failed");
                httpResponse.setStatus(result.first);
                return new BaseResponse(Status.ERROR, result.second);
            }
            final UserModel userModel = service.getUser(org, userData.getShortName());
            if (userModel != null) {
                httpResponse.setStatus(HttpServletResponse.SC_CREATED);
                return new BaseResponse(Status.OK) {
                    public UserModel getUserData() {
                        return userModel;
                    }
                };
            }
        } catch (Throwable th) {
            log.error("createUser: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Update User for the given organization
     * @param orgName       Organization ShortName for which the user details needs to be updated
     * @param userData {@link UserUpdateModel}
     * @param httpResponse  HttpServletResponse object
     * @return The details of the updated User for the given orgName
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}", method = RequestMethod.PUT)
    public @ResponseBody BaseResponse updateUser(@PathVariable("orgName") String orgName,
                                                 @RequestBody UserUpdateModel userData,
                                                 HttpServletResponse httpResponse){
        try{
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            Organization org = OrganizationUtil.getOrganization(orgName);
            if (org == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.ORG_NOT_FOUND);
            }
            if (userData == null || StringUtils.isNullOrEmptyString(userData.getShortName())) {
                httpResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.BAD_INPUT_REQUEST);
            }
            UserUpdateModel existingData = service.getUserUpdateModel(org, userData.getShortName());
            if (existingData == null) {
                httpResponse.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_NOT_FOUND);
            }
            InstanceCreator<UserUpdateModel> creator = service.createInstanceCreatorForUpdate(existingData);
            Gson gson = new GsonBuilder().serializeNulls().registerTypeAdapter(UserUpdateModel.class, creator).create();
            UserUpdateModel entityForUpdate = gson.fromJson(gson.toJson(userData), UserUpdateModel.class);

            Tuple<Integer, String> result = service.updateUser(org, entityForUpdate);
            if (result.first != HttpServletResponse.SC_OK) {
                log.warn("Updating User " + userData.getShortName() + " in " + orgName + " Failed");
                httpResponse.setStatus(result.first);
                return new BaseResponse(Status.ERROR, result.second);
            }
            final UserModel latest = service.getUser(org, userData.getShortName());
            if (latest != null) {
                return new BaseResponse(Status.OK) {
                    public UserModel getUserData() {
                        return latest;
                    }
                };
            }
        }
        catch (Throwable th) {
            log.warn("updateUser: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }

    /**
     * Inactivate User for the given organization
     * @param orgName       Organization ShortName for which the user details needs to be inactivated
     * @param userShortName User whose details needs to be inactivated
     * @param httpResponse  HttpServletResponse object
     * @return HttpResponse showing the result of inactivation
     */
    @ApiAuthorization(permissions = {"OrgDetailMU"})
    @RequestMapping(value = "/{orgName}/{userShortName}", method = RequestMethod.DELETE)
    public @ResponseBody BaseResponse deleteUser(@PathVariable("orgName") String orgName,
                                                 @PathVariable( "userShortName" ) String userShortName,
                                                 HttpServletResponse httpResponse) {
        try{
            if (!UserUtil.isUserAuthorized(orgName)) {
                log.warn("UserSession not present or User is not authorized to fetch details of " + orgName);
                httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return new BaseResponse(Status.ERROR, CommonErrorCodes.USER_UNAUTHORIZED);
            }
            httpResponse.setStatus(HttpServletResponse.SC_NOT_IMPLEMENTED);
            return new BaseResponse(Status.ERROR, CommonErrorCodes.NOT_IMPLEMENTED);
        }
        catch (Throwable th) {
            log.warn("deleteUser: Exception ", th);
        }
        httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        return new BaseResponse(Status.ERROR, CommonErrorCodes.INTERNAL_SERVER_ERROR);
    }
}