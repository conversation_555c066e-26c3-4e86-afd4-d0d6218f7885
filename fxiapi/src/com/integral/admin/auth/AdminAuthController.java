package com.integral.admin.auth;

import com.integral.admin.api.AdminValidationException;
import com.integral.admin.api.BaseController;
import com.integral.admin.externalpricing.ExcelPricingController;
import com.integral.admin.util.FXIAdminApiUtil;
import com.integral.admin.utils.organization.UserUtil;
import com.integral.audit.AuditInfo;
import com.integral.fxiapi.FXIConstants;
import com.integral.fxiapi.Status;
import com.integral.fxiapi.auth.LoginService;
import com.integral.fxiapi.auth.LogoutResponse;
import com.integral.fxiapi.util.FXIApiUtil;
import com.integral.security.CryptC;
import com.integral.security.SecurityFactory;
import com.integral.session.*;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.Tuple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Validator;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Controller
@Scope( "session" )
@RequestMapping( value = "/auth" )
public class AdminAuthController extends BaseController
{

    protected String API_CONTEXT_URI = "/admin";

    @Autowired
    LoginService loginService;

    User user;
    @Autowired
    ExcelPricingController excelPricingController;
    protected final static String ORG_LOGIN_DISABLED = "Idc.Org.Logins.Disabled";
    protected final static String INVALID_IP = "user.login.invalidIP";

    @Autowired
    public AdminAuthController( Validator validator )
    {
        super( validator );
    }

    @RequestMapping( value = "/login", method = RequestMethod.POST )
    public
    @ResponseBody
    LoginResponse login( @RequestBody LoginRequest loginData, HttpServletRequest request, HttpServletResponse response )
    {
        LoginResponse loginResponse = null;
        try
        {
            Set failures = validator.validate( loginData );
            if ( !failures.isEmpty() )
            {
                loginResponse = new LoginResponse( Status.ERROR, response );
                loginResponse.setErrorCode( validationMessages( failures ) );
                return loginResponse;
            }

            String uId = loginData.getUser() + '@' + loginData.getOrg();
            User user = UserFactory.getUser( uId );

            if ( user == null )
            {
                loginResponse = new LoginResponse( Status.ERROR );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                loginResponse.setErrorCode( "Invalid user/organization supplied" );
                log.warn( "AdminAuthController.login. user doesn't exists with id " + uId );
                return loginResponse;
            }

            log.info( new StringBuilder( 200 ).append( "AC.doLogin: Login request received for user:" ).append( uId ).toString() );

            if (User.ACCOUNT_STATUS_PASSWORD_MUST_CHANGE_AT_NEXT_LOGON == user.getAccountStatus()) {
                loginResponse = new LoginResponse( Status.ERROR );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                loginResponse.setErrorCode( "Password needs to be changed for user" );
                log.error( "AdminAuthController.login - Password needs to be changed for user: " + user.getShortName());
                return loginResponse;
            }

            //password validation for static/otp/2fa
            Tuple<Boolean, String> authResult;
            authResult = loginService.authenticateUser( user, null, loginData.getPass(), request );

            if ( authResult.first == Boolean.TRUE )
            {
                loginResponse = new LoginResponse( Status.OK );
            }
            else
            {
                loginResponse = new LoginResponse( Status.ERROR );
                loginResponse.setErrorCode( authResult.second );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                log.error( "AdminAuthController.login. Authentication failed for userid " + uId );
                SecurityFactory.getSecurityService().auditLoginFailed( user, true );
                return loginResponse;
            }

            boolean isLoginEnabled = loginService.isLoginEnabled( user );
            if ( !isLoginEnabled )
            {
                loginResponse = new LoginResponse( Status.ERROR );
                loginResponse.setErrorCode( ORG_LOGIN_DISABLED );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                return loginResponse;
            }

            boolean isClientIPAllowed = loginService.isClientIPAllowed( user, request );
            if ( !isClientIPAllowed )
            {
                loginResponse = new LoginResponse( Status.ERROR );
                loginResponse.setErrorCode( INVALID_IP );
                response.setStatus( HttpServletResponse.SC_UNAUTHORIZED );
                return loginResponse;
            }

            //login to user service manager to allow force logout from admin
            HttpSession httpSession = request.getSession( false );
            if ( httpSession == null )
            {
                httpSession = request.getSession( true );
            }

            String webapp = API_CONTEXT_URI;
            UserServiceManager.getInstance().setSingleLogin( webapp, false );

            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext( user );
            httpSession.setAttribute( IdcSessionContext.USER_KEY, user );
            httpSession.setAttribute( IdcSessionContext.USER_NAME, user.getShortName() );
            httpSession.setAttribute( IdcSessionContext.SESSION_CONTEXT, ctxt );
            String fwdHost = request.getHeader( "X-Forwarded-For" );
            if ( ( fwdHost == null ) )
            {
                fwdHost = request.getRemoteAddr();
            }
            AuditInfo auditInfo = new AuditInfo();
            auditInfo.setIpAddress(fwdHost);

            String vsMachineName="";
            try
            {
                vsMachineName= InetAddress.getLocalHost().getHostName();
            }
            catch (UnknownHostException e)
            {
                //leave it vsMachineName blank
                log.info("Unable to fetch the localhost machine name, setting sourceVirtualServerMachineName for AuditEvent as blank");
            }
            ServerMBean serverMBean = ConfigurationFactory.getServerMBean();
            auditInfo.setSourceVirtualServer(serverMBean.getVirtualServerName()) ;
            auditInfo.setSourceVirtualServerMachineName(vsMachineName);
            ctxt.setAttribute("audit-info", auditInfo);

            IdcUserSession userSession = IdcUserSessionManagerC.getInstance().getUserSession( httpSession );
             //this takes care of logging out this user across different webapps with single-login-criteria
            UserServiceManager.getInstance().addUserSession( webapp, user, userSession, false );

            //If due to previous user logout session was invalidated - create a new session
            if ( request.getSession( false ) == null )
            {
                httpSession = request.getSession( true );
            }
            com.integral.security.util.UserCookieHandler.initializeOnUserLogin(user,request,response);
            this.user = user;
            //this is added since UMO requires last login date to be persisted for user
            //FixUtilC.getInstance().updateUserOnLogin( user );
            httpSession.setAttribute("webapp", "admin");

            SecurityFactory.getSecurityService().auditLoginSuccess( user, true );
        }
        catch ( Exception e )
        {
            log.error( "AdminAuthController.login. Exception during login for user: " + loginData.getUser() + " ,org: " + loginData.getOrg(), e );
            sendError( response );
        }

        return loginResponse;
    }


    @RequestMapping( value = "/logout", method = RequestMethod.POST )
    public
    @ResponseBody
    LogoutResponse logout( HttpServletResponse response, HttpSession httpSession )
    {
        User user = ( User ) IdcSessionManager.getInstance().getSessionContext().getUser();
        LogoutResponse logoutResponse = null;

        String reasonForLogout = "";
        if ( httpSession != null )
        {
            reasonForLogout = ( String ) httpSession.getAttribute( "REASON" );
        }
        //If logout initiated from session destroyed event, httpresponse is null
        boolean userInitiatedLogout = (response != null);

        log.info( "AC.logout. Reason for logout is:" + reasonForLogout + " ,userInitiated:" + userInitiatedLogout + " invoked for user: " + user );

        try
        {
            logoutResponse = new LogoutResponse( Status.OK );
            //remove the AuthToken and ProxySessionID cookies and clear cache
            //response can be null if session invalidated by admin or timeout
            if ( response != null )
            {
                com.integral.security.util.UserCookieHandler.clearOnUserLogout(response);
                //in case of logout invoked by user set the attribute
                httpSession.setAttribute( "USER_LOGOUT", true );
            }

            if( null != excelPricingController )
            {
                if(!UserUtil.isUserInMAINNamespace(user))
                {
                    log.info("ExcelPricing::Stop rates for provider : "+user.getOrganization().getShortName());
                	excelPricingController.stopUserPublish(user, ExcelPricingController.StopScenario.logout);
                }
            }
            //logout from user service manager - request can be null if session invalidated by admin or timeout
            String webapp = API_CONTEXT_URI;
            UserServiceManager.getInstance().removeUserSession( webapp, user, IdcUserSessionManagerC.getInstance().getUserSession( httpSession ), UserService.LOGGED_OUT );
            //SecurityFactory.getSecurityService().auditLogout( user, false );
        }
        catch ( Exception e )
        {
            if( e instanceof AdminValidationException )
            {
                log.warn("AAC.logout : admin validation ex while logout for user" + user + " ,Cause : " + ((AdminValidationException)e).getErrorCode());
            }
            else
            {
                if( null!= user)
                {
                    log.warn("AAC.logout : Failed for user" + user + " ,Cause : " + e.getMessage());
                }
            }
            // if a logout why send this, commentting it unless someone says otherwise
            //sendError( response );
        }

        return logoutResponse;
    }


    protected String getAuthToken( String uId, User user, HttpServletRequest request )
    {
        String authToken = getFXIApiUtil().generateAuthToken( uId, request );
        return authToken;
    }

    protected FXIApiUtil getFXIApiUtil()
    {
        return FXIAdminApiUtil.getInstance();
    }
}