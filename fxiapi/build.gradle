plugins {
  id "com.eriwen.gradle.js" version "2.14.1"
  id "com.eriwen.gradle.css" version "2.14.0"
}


def repoPath = "$repoSrc"
def runtimePath = "$repoSrc/../runtime/usrv/conf"

description = "This is Integral fxiapi module"

version = USRV_VERSION

apply plugin: 'groovy'
apply plugin: "jacoco"

/*
buildscript {
    repositories {
       maven {
            url "http://nexus.sca.dc.integral.net:8081/repository/local-jcenter"
       }
    }

    dependencies {
        classpath 'com.eriwen:gradle-css-plugin:1.11.1'
        classpath 'com.eriwen:gradle-js-plugin:1.12.1'
    }
}
*/

// Invoke the css minify plugin
//apply plugin: 'css'

css.source{
	dev{
		css{
			srcDir "Web Content/integral/fx/css"
			include "fxi_prime.css"
		}
	}
}

minifyCss {
    source = css.source.dev.css.files
    dest = file('Web Content/integral/fx/css/fxi_prime-min.css')
    yuicompressor { // Optional
        lineBreakPos = -1
    }
}

// Invoke the js minify plugin
apply plugin: 'com.eriwen.gradle.js'

// java script sources to be combined
javascript.source {
    login {
        js {
            srcDir "Web Content/integral/fx"
            include "1.1/js/integral.js"
            include "1.1/js/integral.ui.js"
			include "js/integral.fxlogin.js"
			include "1.1/js/integral.common.js"
			include "js/integral.licenseagreement.js"
			include "js/integral.browsersupport.js"
        }
    }
	loginJquery {
		js {
			srcDir "Web Content/integral/fx/js"
			include "../../core/login/js/md5.js"
            include "jquery/jquery-1.7.1.min.js"
			include "jquery/jquery-ui-1.8.6.custom.min.js"
			include "jquery/jquery.PrintArea.js"
			include "jquery/jquery.printElement.min.js"
			include "jquery/jqModal.js"
			include "jquery/jquery.cookie.js"
			include "jquery/jquery.cooquery.js"
			include "jquery/jquery.dataTables.min.js"
			include "json/json2.js"
			include "jquery.i18n.properties-1.0.9.js"
		}
	}
	mainJquery {
		js {
			srcDir "Web Content/integral/fx/js"
			include "../../core/login/js/md5.js"
            include "jquery/jquery-1.7.1.min.js"
			include "jquery/jquery-ui-1.9.2.custom.min.js"
			include "jquery/jquery.PrintArea.js"
			include "jquery/jquery.printElement.min.js"
			include "jquery/jqModal.js"
			include "jquery/jquery.cookie.js"
			include "jquery/jquery.cooquery.js"
			include "jquery/jquery.dataTables.min.js"
			include "jquery/jquery.dataTables.fnGetColumnData.js"
			include "jquery/ColReorder.js"
			include "jquery/tabletools/media/js/TableTools.js"
			include "jquery/tabletools/media/js/ZeroClipboard.js"
			include "jquery/jquery.okNotify.min.js"
			include "jquery/jquery.idTabs.min.js"
			include "jquery/jquery.datePicker.js"
			include "jquery/date.js"
			include "json/json2.js"
			include "jquery/jquery.simpleCombo.js"
			include "jquery/ui.spinner.js"
			include "jquery.i18n.properties-1.0.9.js"
			include "jquery/jquery.mCustomScrollbar.min.js"
			include "jquery/jquery.mousewheel.min.js"
		}
	}
	mainCombined{
		js {
			srcDir "Web Content/integral/fx"
			include "js/ie.js-comp.js"
            include "js/integral.fxlogin.js"
			include "js/integral.licenseagreement.js"
			include "js/integral.browsersupport.js"
			include "1.1/js/integral.js"
			include "1.1/js/integral.common.js"
			include "1.1/js/integral.ui.js"
			include "js/integral.util.js"
			include "1.1/js/integral.ui.qtpanel.js"
			include "1.1/js/integral.simple.fxboard.js"
			include "1.1/js/integral.fxladder.js"
			include "1.1/js/integral.fxrelations.js"
			include "1.1/js/integral.rfs.js"
			include "1.1/js/integral.fxalgoorders.js"
			include "1.1/js/integral.fxbasicorders.js"
			include "1.1/js/integral.fxtwaporders.js"
			include "1.1/js/integral.fxorders.js"
			include "1.1/js/integral.fxpegorderpanel.js"
			include "1.1/js/integral.fxorderstatuspanel.js"
			include "1.1/js/integral.fxcharts.js"
			include "js/integral.fxorderblotter.js"
			include "js/integral.fxtradeblotter.js"
			include "js/integral.fxpositionblotter.js"
			include "js/integral.fxorderticket.js"
			include "js/integral.fxdealticket.js"
			include "1.1/js/integral.fxprimeheader.js"
			include "1.1/js/integral.fxfullbook.js"
			include "js/integral.fxmessagespanel.js"
			include "1.1/js/integral.settings.js"
			include "js/integral.editableselect.js"
			include "js/integral.dateutils.js"
			include "1.1/js/integral.systemalert.js"
			include "1.1/js/integral.fxticker.js"
			include "1.1/js/highstock/highstock.js"
			include "1.1/js/integral.rfsfullbook.js"
			include "1.1/js/integral.fxrisknetorders.js"
		}
	}
}

task jsLogin(type: com.eriwen.gradle.js.tasks.CombineJsTask) {
    source = javascript.source.login.js.files
    dest = file('Web Content/integral/fx/js/combined/login-combined.js')
}

task jsLoginJQuery(type: com.eriwen.gradle.js.tasks.CombineJsTask) {
    source = javascript.source.loginJquery.js.files
    dest = file('Web Content/integral/fx/js/combined/login-jquery-combined.js')
}

task jsMainJQuery(type: com.eriwen.gradle.js.tasks.CombineJsTask) {
    source = javascript.source.mainJquery.js.files
    dest = file('Web Content/integral/fx/js/combined/main-jquery-combined.js')
}

task jsMainCombined(type: com.eriwen.gradle.js.tasks.CombineJsTask) {
    source = javascript.source.mainCombined.js.files
    dest = file('Web Content/integral/fx/js/combined/main-combined.js')
}

task minifyLogin (type : com.eriwen.gradle.js.tasks.MinifyJsTask) {
    source = file('Web Content/integral/fx/js/combined/login-combined.js')
    dest = file('Web Content/integral/fx/js/combined/login-combined-min.js')
	closure {
        warningLevel = 'QUIET'
		compilationLevel = 'WHITESPACE_ONLY'
    }
}

task minifyLoginJQuery (type : com.eriwen.gradle.js.tasks.MinifyJsTask) {
    source = file('Web Content/integral/fx/js/combined/login-jquery-combined.js')
    dest = file('Web Content/integral/fx/js/combined/login-jquery-combined-min.js')
	closure {
        warningLevel = 'QUIET'
		compilationLevel = 'WHITESPACE_ONLY'
    }
}

task minifyMainJQueryJs (type : com.eriwen.gradle.js.tasks.MinifyJsTask) {
    source = file('Web Content/integral/fx/js/combined/main-jquery-combined.js')
    dest = file('Web Content/integral/fx/js/combined/main-jquery-combined-min.js')
	closure {
        warningLevel = 'QUIET'
		compilationLevel = 'WHITESPACE_ONLY'
    }
}

task minifyMainCombinedJs (type : com.eriwen.gradle.js.tasks.MinifyJsTask) {
    source = file('Web Content/integral/fx/js/combined/main-combined.js')
    dest = file('Web Content/integral/fx/js/combined/main-combined-min.js')
	closure {
        warningLevel = 'QUIET'
		compilationLevel = 'WHITESPACE_ONLY'
    }
}

sourceSets {
    main {
       java {
          srcDirs 'src'
       }
       resources {
          srcDir 'src'
          include '**/*.xml'
       }
   }
   unitTest {
        java {
            compileClasspath += main.output + test.output
            runtimeClasspath += main.output + test.output
            srcDir file('unit')
        }
		groovy {
            compileClasspath += main.output + test.output
            runtimeClasspath += main.output + test.output
            srcDir file('unit')
        }
		resources{
			srcDir file('unit/resources')
			srcDir file("${runtimePath}")
			srcDir file("${repoPath}/properties")
            srcDir file("unit/com/integral")
		}
   }
   functionalTest {
        java {
            compileClasspath += main.output + test.output
            runtimeClasspath += main.output + test.output
            srcDir file('functional')
        }
		groovy {
            compileClasspath += main.output + test.output
            runtimeClasspath += main.output + test.output
            srcDir file('functional')
        }
		resources{
			srcDir file('functional/resources')
			srcDir file("${runtimePath}")
			srcDir file("${repoPath}/properties")
            srcDir file("functional/com/integral")
		}
   }
}

configurations {
    unitTestCompile.extendsFrom testCompile
	unitTestCompile.extendsFrom groovyTestCompile
	unitTestRuntime.extendsFrom testRuntime
	functionalTestCompile.extendsFrom unitTestCompile
	functionalTestRuntime.extendsFrom unitTestRuntime
}

dependencies {
        unitTestCompile "org.javassist:javassist:3.18.1-GA"
        unitTestCompile "org.mockito:mockito-all:2.0.2-beta"
        unitTestCompile "org.powermock:powermock-mockito:1.6.6-full"
        unitTestCompile "org.springframework:spring-mock:2.0.8"

       compile project(':IntegralConfigWeb')
       compile project(':ISDomain')
       compile fileTree(dir: repoPath, include: 'EMSClient.jar')
       compile fileTree(dir: repoPath, include: 'IntegralCoreApps.jar')

       compile group: "com.integral", name: "oracle-persistence", version: ORACLE_PERSISTENCE_VERSION
	   compile group: "com.integral", name: "monitor-core", version: MONITOR_CORE_VERSION
       compile group: "com.integral", name:"rds",version: RDS_VERSION
       compile group: "com.integral", name:"services",version: MODULES_SERVICES
       compile group: "com.integral", name:"xems-api", version:XEMS_API_VERSION
       compile group: "com.integral", name:"portal-api", version:PORTAL_API_VERSION
       compile group: "com.integral", name:"yieldmanager", version:DARWIN_YIELDMANAGER_VERSION
       compile "javax.validation:validation-api:1.0.0.GA"
       compile "commons-io:commons-io:1.1"
       compile "org.restlet.jee:org.restlet:1.0.0"
       compile "org.restlet.jee:org.restlet.ext.json:1.0.0"
       compile group: "com.integral", name:"darwin-auth", version:DARWIN_AUTH_VERSION
       compile "org.springframework:spring-context:3.1.1.RELEASE"
       compile "org.springframework:spring-webmvc:3.1.1.RELEASE"
       compile "org.springframework:spring-beans:3.1.1.RELEASE"
       compile "org.springframework:spring-web:3.1.1.RELEASE"

       // The following is a snapshot and not able to upload to release repos
//       compile fileTree(dir: repoPath, include: 'lib/spring/spring-security-saml2-core-1.0.0-RC2-20130307.070016-9.jar')
       compile "org.springframework.security.extensions:spring-security-saml2-core:1.0.0.RC2"
       compile "com.ning:async-http-client:1.6.2"

       compile "org.opensaml:opensaml:2.5.3"
       compile "org.opensaml:xmltooling:1.3.4"
       compile "org.opensaml:openws:1.4.4"
       compile "org.springframework:spring-test:3.1.1.RELEASE"

       compile "aopalliance:aopalliance:1.0"
       compile "aspectj:aspectjweaver:1.6.12"
       compile group: 'com.fasterxml.jackson.dataformat', name: 'jackson-dataformat-csv', version: '2.4.3'

       runtime "ca.juliusdavies:not-yet-commons-ssl:0.3.9"

       unitTestCompile "com.google.guava:guava:15.0"
       unitTestCompile project(':testframework')
         unitTestCompile "org.springframework:spring-expression:3.1.1.RELEASE"
         unitTestCompile "org.springframework.security:spring-security-config:3.1.2.RELEASE"
         unitTestCompile "org.springframework.security:spring-security-core:3.1.2.RELEASE"
         unitTestCompile "org.springframework.security:spring-security-web:3.1.2.RELEASE"
         unitTestCompile "org.springframework:spring-context:3.1.1.RELEASE"
         unitTestCompile "org.springframework:spring-core:3.1.1.RELEASE"
         unitTestCompile "org.springframework:spring-webmvc:3.1.1.RELEASE"
         unitTestCompile "org.springframework:spring-beans:3.1.1.RELEASE"
         unitTestCompile "org.springframework:spring-aop:3.1.1.RELEASE"
         unitTestCompile "org.springframework:spring-asm:3.1.1.RELEASE"
         unitTestCompile "org.springframework:spring-expression:3.1.1.RELEASE"
         unitTestCompile "org.springframework:spring-test:3.1.1.RELEASE"
         unitTestCompile "org.springframework:spring-web:3.1.1.RELEASE"
         unitTestCompile "org.apache.velocity:velocity:1.5"
         unitTestCompile "org.apache.maven:maven-artifact:3.0.3"
         unitTestCompile "org.testng:testng:6.7"
        unitTestCompile "org.eclipse.jetty.aggregate:jetty-all-server:8.1.15.v20140411"
        unitTestCompile "org.eclipse.jetty.orbit:javax.servlet:3.0.0.v201112011016"

      unitTestCompile project(':testframework')

         //this need to be clean up
	  unitTestCompile fileTree(dir: repoPath, include: '*.jar')

          unitTestCompile "com.integral:log:1.0.2"
          unitTestCompile group: "com.integral", name:"spaces",version: MODULES_SPACES
          unitTestCompile "com.integral:messaging:1.0.1"
    unitTestCompile group: "com.integral", name: "model", version: MODEL_VERSION

	  unitTestRuntime fileTree(dir: repoPath, include: 'XMLMapping/**')
	  unitTestRuntime fileTree(dir: repoPath, include: 'XML/**')
	  unitTestRuntime fileTree(dir: repoPath, include: 'fxiapi/functional/**')
	  unitTestRuntime "org.hibernate:hibernate-validator:4.1.0.Final"

}

clean{
	delete "Web Content/integral/fx/css/fxi_prime-min.css"
	delete "Web Content/integral/fx/js/combined"
        delete "../FXIAPI.jar"
}

jar {
    jar.archiveName = "FXIAPI.jar"
    destinationDir = file("$repoPath")

    includeEmptyDirs = false

     manifest {
        attributes 'Implementation-Title': project.name,
                   'Implementation-Version': project.version,
                   'Implementation-Description': project.description,
                   'Built-By': System.getProperty('user.name'),
                   'Built-JDK': System.getProperty('java.version'),
                   'Built-Hostname': hostname(),
                   'Build-Time': new Date().format("yyyy-MMM-dd HH:mm '(GMT)'"),
                   'Build-Version': svnversion()
    }
}

uploadArchives {
    repositories {
        if ("$PUBLISH_TO_NEXUS" == "true") {
            mavenDeployer {
                repository(url: "$NEXUS_REPO_URL") {
                    authentication(userName: nexus_user, password: nexus_password)
                }
                pom.groupId = "$GROUP"
                pom.version = "$version"
            }
        } else {
            mavenDeployer {
                repository(url: mavenLocal().url)
                pom.groupId = "$GROUP"
                pom.version = "$version"
            }
        }
    }
}

task unitTest(type: Test) {
    forkEvery = 1
    jvmArgs = ['-Djava.net.preferIPv4Stack=true']
    testClassesDir = sourceSets.unitTest.output.classesDir
    classpath = sourceSets.unitTest.runtimeClasspath
    include '**/*TestC.class'
	include '**/*Test.class'
    outputs.upToDateWhen { false }
    ignoreFailures = true
    finalizedBy jacocoTestReport
}

task functionalTest(type: Test) {
    forkEvery = 1
    jvmArgs = ['-Djava.net.preferIPv4Stack=true']
    testClassesDir = sourceSets.functionalTest.output.classesDir
    classpath = sourceSets.functionalTest.runtimeClasspath
    include '**/*TestC.class'
	include '**/*Test.class'
    outputs.upToDateWhen { false }
    ignoreFailures = true
    finalizedBy jacocoTestReport
}

jacocoTestReport() {
    group = "Reporting"
    description = "Generate Jacoco coverage reports after running tests."
    executionData = files('build/jacoco/unitTest.exec')
	executionData = files('build/jacoco/functionalTest.exec')
    additionalSourceDirs = files(sourceSets.main.java.srcDirs)
	// get all projects we have a (compile) dependency on
    def projs = configurations.compile.getAllDependencies().withType(ProjectDependency).collect{it.getDependencyProject()}
    projs.each {
        additionalSourceDirs files(it.sourceSets.main.java.srcDirs)
        additionalClassDirs files(it.sourceSets.main.output)
    }
	reports {
        xml.enabled=false
        csv.enabled=false
        html.destination="${buildDir}/reports/jacoco/html"
    }
}

tasks.withType(Test) {
    reports.html.destination = file("${reporting.baseDir}/${name}")
}

// Temporarily using Ant task to combine and minify js since the gradle plugin has bugs
ant.importBuild 'antMinifyJS.xml'

jar.dependsOn tasks.jsCompile

//jar.dependsOn tasks.minifyCss
//jar.dependsOn tasks.minifyLogin
//jar.dependsOn tasks.minifyLoginJQuery
//jar.dependsOn tasks.minifyMainJQueryJs
//jar.dependsOn tasks.minifyMainCombinedJs


tasks.minifyLogin.dependsOn tasks.jsLogin
tasks.minifyLoginJQuery.dependsOn tasks.jsLoginJQuery
tasks.minifyMainJQueryJs.dependsOn tasks.jsMainJQuery
tasks.minifyMainCombinedJs.dependsOn tasks.jsMainCombined

task compileTests(dependsOn: 'compileUnitTestGroovy'){
}

compileUnitTestGroovy.finalizedBy compileFunctionalTestGroovy
