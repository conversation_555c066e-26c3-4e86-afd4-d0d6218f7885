<?xml version="1.0" encoding="iso-8859-1"?>

<!DOCTYPE project [
  <!ENTITY build-common SYSTEM "file:../build-common.xml">
]>

<project name="FXIAPI" default="all" basedir=".">
    <property file="../../build.properties"/>
    <property file="../build.properties"/>
    <property name="component.name" value="FXIAPI"/>
    <property name="webapp.name" value="${component.name}"/>
 
  
    <target name="jsCompile">
        <taskdef name="yui-compressor" classname="net.noha.tools.ant.yuicompressor.tasks.YuiCompressorTask">
            <classpath>
                <path>
                    <pathelement location="${basedir}/../lib/yui/yuicompressor-2.4.6.jar" />
                    <pathelement location="${basedir}/../lib/yui/yui-compressor-ant-task-0.5.jar" />
                </path>
            </classpath>
        </taskdef>
        <echo message="Building global javascript" />
        <delete dir="${basedir}/Web Content/integral/fx/js/combined"/>
        <concat destfile="${basedir}/Web Content/integral/fx/js/combined/login/login-combined.js">
            <filelist dir="${basedir}/Web Content/integral/fx">
                <file name="js/login/ie.js-comp.js"/>
                <file name="js/clientapi/integral.js"/>
                <file name="js/login/integral.fxlogin.js"/>
                <file name="js/login/integral.common.js"/>
                <file name="js/login/integral.licenseagreement.js"/>
                <file name="js/login/integral.browsersupport.js"/>
            </filelist>
        </concat>
        <concat destfile="${basedir}/Web Content/integral/fx/js/combined/lib/login-jquery-combined.js">
            <filelist dir="${basedir}/Web Content/integral/fx/js/">
                <file name="../../core/login/js/md5.js"/>
                <file name="jquery/jquery-1.7.1.min.js"/>
                <file name="jquery/jquery-ui-1.8.6.custom.min.js"/>
                <file name="jquery/jquery.PrintArea.js"/>
                <file name="jquery/jquery.printElement.min.js"/>
                <file name="jquery/jqModal.js"/>
                <file name="jquery/jquery.cookie.js"/>
                <file name="jquery/jquery.cooquery.js"/>
                <file name="jquery/jquery.dataTables.min.js"/>
                <file name="json/json2.js"/>
                <file name="jquery.i18n.properties-1.0.9.js"/>
            </filelist>
        </concat>
        <concat destfile="${basedir}/Web Content/integral/fx/js/combined/lib/main-jquery-combined.js">
            <filelist dir="${basedir}/Web Content/integral/fx/js/">
                <file name="../../core/login/js/md5.js"/>
                <file name="jquery/jquery-1.7.1.min.js"/>
                <file name="jquery/jquery-ui-1.9.2.custom.min.js"/>
                <file name="jquery/jquery.PrintArea.js"/>
                <file name="jquery/jquery.printElement.min.js"/>
                <file name="jquery/jqModal.js"/>
                <file name="jquery/jquery.cookie.js"/>
                <file name="jquery/jquery.cooquery.js"/>
                <file name="jquery/jquery.dataTables.min.js"/>
                <file name="jquery/jquery.dataTables.fnGetColumnData.js"/>
                <file name="jquery/ColReorder.js"/>
                <file name="jquery/tabletools/media/js/TableTools.js"/>
                <file name="jquery/tabletools/media/js/ZeroClipboard.js"/>
                <file name="jquery/jquery.okNotify.min.js"/>
                <file name="jquery/jquery.idTabs.min.js"/>
                <file name="jquery/jquery.datePicker.js"/>
                <file name="jquery/date.js"/>
                <file name="json/json2.js"/>
                <file name="jquery/jquery.simpleCombo.js"/>
                <file name="jquery/ui.spinner.js"/>
                <file name="jquery.i18n.properties-1.0.9.js"/>
                <file name="jquery/jquery.mCustomScrollbar.min.js"/>
                <file name="jquery/jquery.mousewheel.min.js"/>
            </filelist>
        </concat>
        <concat destfile="${basedir}/Web Content/integral/fx/js/combined/main/main-combined.js">
            <filelist dir="${basedir}/Web Content/integral/fx">
                <file name="js/main/integral.ui.js"/>
                <file name="js/main/integral.util.js"/>
                <file name="js/main/integral.ui.qtpanel.js"/>
                <file name="js/main/integral.simple.fxboard.js"/>
                <file name="js/main/integral.fxladder.js"/>
                <file name="js/main/integral.fxrelations.js"/>
                <file name="js/main/integral.rfs.js"/>
                <file name="js/main/integral.fxalgoorders.js"/>
                <file name="js/main/integral.fxbasicorders.js"/>
                <file name="js/main/integral.fxtwaporders.js"/>
                <file name="js/main/integral.fxorders.js"/>
                <file name="js/main/integral.fxpegorderpanel.js"/>
                <file name="js/main/integral.fxorderstatuspanel.js"/>
                <file name="js/main/integral.fxcharts.js"/>
                <file name="js/main/integral.fxorderblotter.js"/>
                <file name="js/main/integral.fxtradeblotter.js"/>
                <file name="js/main/integral.fxpositionblotter.js"/>
            	<file name="js/main/integral.fxadvpositionblotter.js"/>
                <file name="js/main/integral.fxorderticket.js"/>
                <file name="js/main/integral.fxdealticket.js"/>
                <file name="js/main/integral.fxprimeheader.js"/>
                <file name="js/main/integral.fxfullbook.js"/>
                <file name="js/main/integral.fxmessagespanel.js"/>
                <file name="js/main/integral.settings.js"/>
                <file name="js/main/integral.editableselect.js"/>
                <file name="js/main/integral.dateutils.js"/>
                <file name="js/main/integral.systemalert.js"/>
                <file name="js/highstock/highstock.js"/>
                <file name="js/main/integral.fxticker.js"/>
                <file name="js/main/integral.rfsfullbook.js"/>
                <file name="js/main/integral.fxrisknetorders.js"/>
            	<file name="js/main/integral.swapladder.js"/>
            </filelist>
        </concat>
        <yui-compressor preserveallsemicolons="true"  warn="false" charset="UTF-8"
                        fromdir="${basedir}/Web Content/integral/fx/js/combined/login"
                        todir="${basedir}/Web Content/integral/fx/js/combined/login">
            <include name="login-combined.js" />
        </yui-compressor>
        <yui-compressor preserveallsemicolons="true"  warn="false" charset="UTF-8"
                        fromdir="${basedir}/Web Content/integral/fx/js/combined/lib"
                        todir="${basedir}/Web Content/integral/fx/js/combined/lib">
            <include name="login-jquery-combined.js" />
        </yui-compressor>
        <yui-compressor preserveallsemicolons="true" warn="false" charset="UTF-8"
                        fromdir="${basedir}/Web Content/integral/fx/js/combined/main"
                        todir="${basedir}/Web Content/integral/fx/js/combined/main">
            <include name="main-combined.js" />
        </yui-compressor>
        <yui-compressor preserveallsemicolons="true"  warn="false" charset="UTF-8"
                        fromdir="${basedir}/Web Content/integral/fx/js/combined/lib"
                        todir="${basedir}/Web Content/integral/fx/js/combined/lib">
            <include name="main-jquery-combined.js" />
        </yui-compressor>

        <yui-compressor preserveallsemicolons="true" warn="false" charset="UTF-8"
                        fromdir="${basedir}/Web Content/integral/fx/css/"
                        todir="${basedir}/Web Content/integral/fx/css/">
            <include name="fxi_prime.css" />
        </yui-compressor>
        <apply executable="java" parallel="false" verbose="true" dest="${basedir}/Web Content/integral/fx/js/combined/login">
            <fileset dir="${basedir}/Web Content/integral/fx/js/combined/login">
                <include name="login-combined.js" />
            </fileset>
            <arg line="-jar" />
            <arg path="${basedir}/../lib/yui/yuicompressor-2.4.6.jar" />
            <arg value="--charset" />
            <arg value="ANSI" />
            <arg value="-o" />
            <targetfile />
            <mapper type="glob" from="login-combined.js" to="login-combined-minified.js" />
        </apply>
        <apply executable="java" parallel="false" verbose="true" dest="${basedir}/Web Content/integral/fx/js/combined/main">
            <fileset dir="${basedir}/Web Content/integral/fx/js/combined/main">
                <include name="main-combined.js" />
            </fileset>
            <arg line="-jar" />
            <arg path="${basedir}/../lib/yui/yuicompressor-2.4.6.jar" />
            <arg value="--charset" />
            <arg value="ANSI" />
            <arg value="-o" />
            <targetfile />
            <mapper type="glob" from="main-combined.js" to="main-combined-minified.js" />
        </apply>
    </target>
</project>
