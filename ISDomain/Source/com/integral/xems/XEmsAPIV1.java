package com.integral.xems;

import com.integral.adaptor.AdaptorConstantC;
import com.integral.finance.dealing.Quote;
import com.integral.fix.client.FixConstants;
import com.integral.fix.client.ServerLevelSessionID;
import com.integral.fix.client.cache.ClientCache;
import com.integral.fix.client.handler.FixReceiver;
import com.integral.fix.client.util.FixUtilC;
import com.integral.imtp.message.IMTPApplicationMessage;
import com.integral.imtp.message.PoolableMessage;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.admin.ManualDirectedOrderRequestUpdateHandler;
import com.integral.is.common.comm.tradingvenue.DirectedOrderResponseListener;
import com.integral.is.common.listener.DirectedOrderMessageListener;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.is.message.BrokerOrderResponse;

import com.integral.is.oms.Order;
import com.integral.is.oms.OrderAdaptorFactory;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.oms.OrderConstants;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.is.spaces.engine.EMSWorkflowEngine;
import com.integral.is.spaces.engine.EMSWorkflowEngineConfigC;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.listener.*;
import com.integral.is.spaces.fx.service.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.user.User;
import com.integral.xems.api.XEmsAPI;
import com.integral.xems.api.ems.OrderExecutionTask;
import com.integral.xems.service.XEmsService;
import com.integral.xems.service.reliability.orders.ManualOrderStatusRequest;
import com.integral.xems.service.reliability.orders.ManualOrderStatusRequestC;
import com.integral.xems.service.reliability.orders.ManualOrderStatusResponse;
import com.integral.xems.service.reliability.orders.ManualOrderStatusResponseC;
import quickfix.Message;
import quickfix.Session;
import quickfix.SessionID;
import quickfix.field.MsgType;

import java.util.concurrent.ThreadPoolExecutor;

public class XEmsAPIV1 implements XEmsAPI<Order> {
    private Log log = LogFactory.getLog(this.getClass());
    private final ThreadPoolExecutor executor = ThreadPoolFactory
            .getInstance().getTradeListenerThreadPool();

    @Override
    public WorkflowMessage processWorkflowMessage(WorkflowMessage wfMsg, XEmsService xEmsService) {
        return xEmsService.processWorkflowMessage(wfMsg);
    }

    @Override
    public void processOrderExecutionTask(OrderExecutionTask<Order> orderExecutionTask) {
        try {
            orderExecutionTask.execute();
        }
        catch (Throwable th){
            log.error("processOrderExecutionTask : Failed for orderExecutionTask="+orderExecutionTask);
        }
    }

    @Override
    public void processMessageFromGateway(Message message, SessionID sessionID) {
        try {
            String type = message.getHeader().getString(MsgType.FIELD);
            if (message.isSetField(FixConstants.FIX_FIELD_WARMUP)) {
                new MessageWorkerC(message, sessionID).run();
                return;
            }
            if (MsgType.ORDER_SINGLE.equals(type)
                    && EMSWorkflowEngineConfigC.getInstance()
                    .isEngineProcessingEnabled()) {
                // create a order id if not exist (UIG sends orderID) so that we can distribute it to one
                // of the disruptors
				String orderId = null;
				if(message.isSetField(FixConstants.FIX_FIELD_ORDER_ID)) {
					orderId = message.getString(FixConstants.FIX_FIELD_ORDER_ID);
				}
				if(orderId == null) {
					orderId = DealingModelUtil.generateOrderId();
					message.setString(FixReceiver.ORDER_ID_FIELD, orderId);
				}
                EMSWorkflowEngine.getInstance().addPrimaryEvent(orderId,
                        new MessageWorkerC(message, sessionID));
            } else {
                ThreadPoolExecutor executor = ClientCache.getInstance().getUserMessageThreadPool(sessionID);
                executor.execute(new MessageWorkerC(message, sessionID));
            }
        } catch (Throwable th) {
            log.warn("process : Failed to process message=" + message + ", sessionID=" + sessionID);
            throw new RuntimeException(th);
        }
    }

    @Override
    public void sendMessageToGateway(Message message, SessionID sessionID) {
        try {
            if (!message.isSetField(FixConstants.FIX_FIELD_WARMUP)) {
                if( sessionID instanceof ServerLevelSessionID ){
                    ServerLevelSessionID serverLevelSessionID = ((ServerLevelSessionID)sessionID);
                    FixUtilC.getInstance().addUserDetails(message,serverLevelSessionID);
                    Session.sendToTarget(message, serverLevelSessionID.getParent());
                }
                else{
                    Session.sendToTarget(message, sessionID);
                }
            } else {
                log.info("sendMessageToGateway : warmup message not sent to session. Id=" + sessionID + ", m=" + message);
            }
        } catch (Throwable th) {
            log.warn("sendMessageToGateway : Failed to send message to gateway. message=" + message + ", sessionID=" + sessionID);
            throw new RuntimeException(th);
        }
    }

    @Override
    public WorkflowMessage submitOrder(SingleLegOrder orderRequest, boolean isPartOfBatch) {
        return OrderCalculatorFactory.getInstance().getOrderRouter().route(orderRequest, isPartOfBatch, false);
    }

    @Override
    public WorkflowMessage cancelOrder(WorkflowMessage wfMsg) {
        SingleLegOrder orderRequest = ( SingleLegOrder ) wfMsg.getObject();
        orderRequest.setCancelReceived( true );
        // check if the request is getting cancelled during startup when persistent order is enabled.
        if ( orderRequest.getOrderRequestAttributes().isCancellationOnStartup() ) {
            WorkflowMessage workflowMessage = OrderCalculatorFactory.getInstance().getOrderRouter().cancelOnStartup( wfMsg );
            if ( log.isDebugEnabled() ) {
                log.debug( new StringBuilder( 200 ).append( "ORS.processWithdrawRequest.DEBUG : Processed cancellation of order request on startup. orderId=" )
                        .append( orderRequest.get_id() ).append( ",responseMessage=" ).append( workflowMessage ).toString() );
            }
            return workflowMessage;
        }
        else {
            WorkflowMessage responseMessage = OrderCalculatorFactory.getInstance().getOrderRouter().cancel(wfMsg);
            if ( responseMessage != null && responseMessage.getReplyMessage() != null ) {
                WorkflowMessage replyMessage = ( WorkflowMessage ) responseMessage.getReplyMessage();
                if ( replyMessage.getParameterValue( OrderConstants.Key_HoldOrderCancelReplaceRequest ) != null ) {
                    replyMessage.setEventName( ISCommonConstants.MSG_TOPIC_PENDING );
                }
            }
            return responseMessage;
        }
    }

    @Override
    public void expireOrder(Order order) {
        OrderCalculatorFactory.getInstance().getOrderRouter().expire(order);
    }

    @Override
    public void processQuote(Quote quote) {
        OrderBookCacheC.getInstance().updateRates(quote);
    }

    public WorkflowMessage amendOrder(SingleLegOrder singleLegOrder,WorkflowMessage wfMsg){
       if(singleLegOrder.isExternalVenueDirectedOrder()){
           return OrderCalculatorFactory.getInstance().getOrderRouter().amendExternalVenueDirectedOrder(singleLegOrder,wfMsg);
       }
       return null;
    }


    @Override
    public boolean getOrderStatus(ManualOrderStatusRequest manualOrderStatusRequest) {
        return OrderCalculatorFactory.getInstance().getOrderRouter().getManualOrderStatusForExternalVenueDirectedOrder(manualOrderStatusRequest);

    }

    @Override
    public WorkflowMessage processOrderStatusResponse(ManualOrderStatusResponse manualOrderStatusResponse) {
        ManualDirectedOrderRequestUpdateHandler.getInstance().updateManualOrderResponse(manualOrderStatusResponse);

        return null;
    }



    @Override
    public com.integral.message.Message processOrderExecutionResponse(WorkflowMessage wfMsg) {
        return OrderAdaptorFactory.getOrderResponseService().process(wfMsg);
    }

    @Override
    public void processTradeAcceptance(WorkflowMessage acceptanceMessage) {
        ServiceFactory.getFxisRequestService().process(acceptanceMessage);
    }

    @Override
    public void processTradingVenueResponseMessage(MessageChannel messageChannel, Object message, byte version) {
        switch (messageChannel) {
            case IMTP:
                processTradingVenueResponseMessage((IMTPApplicationMessage)message,version);
                break;
            default:
                throw new IllegalArgumentException("Unsupported messageChannel=" + messageChannel.name());
        }
    }

    private void processTradingVenueResponseMessage(IMTPApplicationMessage applicationMessage, byte version) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("processTradingVenueResponseMessage: Received IMTP message" + applicationMessage.getApplicationData());
            }

            try {
                long receivedTime = System.currentTimeMillis();
                String reqReferId = applicationMessage.getProperty(AdaptorConstantC.REQUEST_REFERENCE_ID);

                if (log.isDebugEnabled()) {
                    log.debug("processTradingVenueResponseMessage: Received message over IMTP with request reference id:" + reqReferId + " message"
                            + applicationMessage.getApplicationData());
                }

                // Increment the reference.
                ((PoolableMessage) applicationMessage).incrementReference();

                if (reqReferId != null) {
                    com.integral.is.common.comm.tradingvenue.SerialTradeMessageProcessorManager.addSerialTradeMessageProcessor(reqReferId, applicationMessage, receivedTime, version);
                } else {
                    executor.execute(new DirectedOrderResponseListener(applicationMessage, version, receivedTime));
                }
            } catch (Exception ex) {
                log.error("processTradingVenueResponseMessage : Exception while handling IMTPMessage: ", ex);
            }
        } catch (Exception e) {
            log.error("processTradingVenueResponseMessage : Exception while handling IMTPMessage: ", e);
        }
    }

    @Override
    public void processTradeResponseMessage(MessageChannel messageChannel, Object message) {
        switch (messageChannel) {
            case IMTP:
                processTradeResponseMessage((IMTPApplicationMessage) message);
                break;
            case JMS:
                processTradeResponseMessage((javax.jms.Message) message);
                break;
            case LOCAL:
                processTradeResponseMessage((BrokerOrderResponse) message);
                break;
            default:
                throw new IllegalArgumentException("Unsupported messageChannel=" + messageChannel.name());
        }
    }

    private void processTradeResponseMessage(javax.jms.Message message) {
        try {
            if (message
                    .getStringProperty(ISCommonConstants.DIRECTED_ORDER_MESSAGE) != null) {
                DirectedOrderMessageListener.getInstance().onMessage(message);
            } else if (message
                    .getStringProperty(ISCommonConstants.FIXING_RATE_UPDATE_MESSAGE) != null) {
                FixingRateUpdateListener.getInstance().onMessage(message);
            } else {
                long receivedTime = System.currentTimeMillis();
                String reqReferId = message
                        .getStringProperty(AdaptorConstantC.REQUEST_REFERENCE_ID);
                if (log.isDebugEnabled()) {
                    log.debug("onMessage : Received message with request id: "
                            + reqReferId + " over JMS.");
                }
                if (reqReferId != null) {
                    SerialTradeMessageProcessorManager.addSerialTradeMessageProcessor(reqReferId, true, message,
                            receivedTime);
                } else {
                    TradeListener tradeListener = new TradeListener(message,
                            receivedTime);
                    executor.execute(tradeListener);
                }
            }
        } catch (Exception ex) {
            log.error("onMessage : Exception while handling JMSMessage: ", ex);
        }

    }
    private void processTradeResponseMessage(BrokerOrderResponse brokerOrderResponse) {

        try {
            long receivedTime = System.currentTimeMillis();
            String reqReferId = brokerOrderResponse.getTradeId();

            if (log.isDebugEnabled()) {
                log.debug("messageReceived: Received message over IMTP with request reference id:"
                        + reqReferId
                        + " message"
                        + brokerOrderResponse);
            }


            String tradeId = brokerOrderResponse.getTradeId();
            SingleLegTrade trade = FXESPWorkflowCache.getTrade(tradeId);
            // Fall back to the old way if we cannot find a trade object
            // Ex:For RFS subscription response, there will be no trade;
            if (EMSWorkflowEngineConfigC.getInstance()
                    .isEngineProcessingEnabled() && trade != null) {
                LocalTradeListener tradeListener;
                if (reqReferId != null) {
                    tradeListener = createLocalTradeListener(reqReferId,brokerOrderResponse, receivedTime);
                } else {
                    tradeListener = new LocalTradeListener(brokerOrderResponse,
                            receivedTime);
                }

                EMSWorkflowEngine.getInstance().addPrimaryEvent(
                        trade.getOrderRequestId(), tradeListener);
                if (log.isDebugEnabled()) {
                    log.debug("Successfully added message:" + tradeId
                            + ":with CorrelationId:"
                            + trade.getOrderRequestId());
                }
            } else {
                if (reqReferId != null) {
                    LocalSerialTradeMessageProcessorManager.addSerialTradeMessageProcessor(reqReferId,
                            brokerOrderResponse, receivedTime);
                } else {
                    LocalTradeListener tradeListener = new LocalTradeListener(
                            brokerOrderResponse, receivedTime);
                    executor.execute(tradeListener);
                }
            }
        } catch (Exception ex) {
            log.error(
                    "messageReceived : Exception while handling IMTPMessage: ",
                    ex);
        }
    }
    private void processTradeResponseMessage(IMTPApplicationMessage applicationMessage) {
        // Increment the reference as we are delegating the actual process to
        // some other thread.
        ((PoolableMessage) applicationMessage).incrementReference();

        try {
            long receivedTime = System.currentTimeMillis();
            String reqReferId = applicationMessage
                    .getProperty(AdaptorConstantC.REQUEST_REFERENCE_ID);

            if (log.isDebugEnabled()) {
                log.debug("messageReceived: Received message over IMTP with request reference id:"
                        + reqReferId
                        + " message"
                        + applicationMessage.getApplicationData());
            }


            String tradeId = applicationMessage.getAppId();
            SingleLegTrade trade = FXESPWorkflowCache.getTrade(tradeId);
            // Fall back to the old way if we cannot find a trade object
            // Ex:For RFS subscription response, there will be no trade;
            if (EMSWorkflowEngineConfigC.getInstance()
                    .isEngineProcessingEnabled() && trade != null) {
                TradeListener tradeListener;
                if (reqReferId != null) {
                    tradeListener = createTradeListener(reqReferId,
                            applicationMessage, receivedTime);
                } else {
                    tradeListener = new TradeListener(applicationMessage,
                            receivedTime);
                }

                EMSWorkflowEngine.getInstance().addPrimaryEvent(
                        trade.getOrderRequestId(), tradeListener);
                if (log.isDebugEnabled()) {
                    log.debug("Successfully added message:" + tradeId
                            + ":with CorrelationId:"
                            + trade.getOrderRequestId());
                }
            } else {
                if (reqReferId != null) {
                    SerialTradeMessageProcessorManager.addSerialTradeMessageProcessor(reqReferId, false,
                            applicationMessage, receivedTime);
                } else {
                    TradeListener tradeListener = new TradeListener(
                            applicationMessage, receivedTime);
                    executor.execute(tradeListener);
                }
            }
        } catch (Exception ex) {
            log.error(
                    "messageReceived : Exception while handling IMTPMessage: ",
                    ex);
        }
    }

    @Override
    public String setMarketData(SingleLegOrder orderRequest) {
        return OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().setMarketData(orderRequest);
    }

    @Override
    public void processOrderExecutionTask(String correlationId, Runnable task) {
        if (EMSWorkflowEngineConfigC.getInstance().isEngineProcessingEnabled()) {
            EMSWorkflowEngine.getInstance().addPrimaryEvent(correlationId, task);
        } else {
            OrderAdaptorFactory.getInstance().getOrderExecutor().execute(task);
        }
    }

    private TradeListener createTradeListener(String queueId, Object message,
                                              long receivedTime) {
        TradeListener tradeListener = new TradeListener(
                (IMTPApplicationMessage) message, receivedTime);
        tradeListener.setSerialTradeMessageListenerKey(queueId);
        return tradeListener;
    }

    private LocalTradeListener createLocalTradeListener(String queueId, BrokerOrderResponse brokerOrderResponse,
                                              long receivedTime) {
        LocalTradeListener tradeListener = new LocalTradeListener(
                brokerOrderResponse, receivedTime);
        tradeListener.setSerialTradeMessageListenerKey(queueId);
        return tradeListener;
    }

    private class MessageWorkerC implements Runnable {
        private Message message;
        private SessionID sessionID;

        public MessageWorkerC(Message message, SessionID sessionID) {
            this.message = message;
            this.sessionID = sessionID;
        }

        public void run() {
            try {
                User user = FixUtilC.getInstance().getUser(message,sessionID);
                if( user != null ){
                    FixUtilC.getInstance().setSessionContext(user);
                }
                FixUtilC.getInstance().getMessageCracker(sessionID).crack(message, sessionID);
            } catch (Exception e) {
                log.error("XEmsAPIV1.MessageWorkerC.run : Failed to process message=" + message, e);
            }
        }
    }
}
