package com.integral.fix.client.translator;

import com.integral.aggregation.price.FXPriceBook;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Order;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.facade.RequestPriceFacade;
import com.integral.finance.dealing.facade.RequestStateFacade;
import com.integral.finance.dealing.facade.RequestStateFacadeC;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.marketData.MarketTierPrice;
import com.integral.finance.marketData.MarketTierPriceChangeSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.fix.client.CurrencyPairDetails;
import com.integral.fix.client.FixConfiguration;
import com.integral.fix.client.FixConstants;
import com.integral.fix.client.mbean.FIXClientConfig;
import com.integral.fix.client.message.*;
import com.integral.fix.client.message.fix43.*;
import com.integral.fix.client.message.fix43.grp.MDIRNoMDEntries43;
import com.integral.fix.client.message.grp.IRNoMDEntries;
import com.integral.fix.client.mifid.MiFIDHandlerFactory;
import com.integral.fix.client.query.FIXClientQueryService;
import com.integral.fix.client.subscription.FixSubscription;
import com.integral.fix.client.util.FIXSEFUtilC;
import com.integral.fix.client.util.FixUtilC;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.spaces.fx.client.fix.ApplicationCommonTranslator;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.*;
import com.integral.netting.model.NettingPortfolio;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.SingleLegOrderQueryService;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.MathUtilC;
import quickfix.Message;
import quickfix.*;
import quickfix.field.Price;
import quickfix.field.*;
import quickfix.fix44.NewOrderMultileg;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class Translator43 extends ApplicationCommonTranslator
{

	public Translator43( FixConfiguration configuration )
	{
		this.configuration = configuration;
	}

	public MarketDataSnapshotFullRefresh getQuote( Quote quote, FixSubscription subscriptionData )
	{
		MarketDataSnapshotFullRefresh mdResponse = new MarketDataSnapshotFullRefresh43();
		boolean isBreakIfNoEntry = false;
		if ( configMbean.isUseExternalSortingForMDEntries() )
		{
			return getQuoteV2(quote, subscriptionData, mdResponse, isBreakIfNoEntry);
		}

		mdResponse.set(FixConstants.PRODUCT);
		mdResponse.set(subscriptionData.getSymbol());
		boolean isTradable = FixConstants.QUOTE_TRADABLE_TYPE.equals(quote.getQuoteClassification().getShortName());
		boolean isMultiTierQuote = (quote.getPriceType() == Quote.PRICE_TYPE_MULTI_TIER);
		int marketDepth2 = FixUtilC.getInstance().getAllowedMarketDepth(subscriptionData.getOrganization(), quote.getOrganization());
		int marketDepth = subscriptionData.getMarketDepth();
		if ( marketDepth2 > 0 )
		{
			if ( marketDepth == 0 ) //Customer asked for full book.
			{
				marketDepth = marketDepth2;
			}
			else
			{
				if ( marketDepth2 < marketDepth )
				{
					marketDepth = marketDepth2;
				}
			}
		}

		Quote rateEvent = (Quote) quote;
		MDEntryOriginator mdEntryOriginator = subscriptionData.getMaskedProviderName();
		IdcDate valueDate = null;
		int bidSize = 0;
		int offerSize = 0;
		String seqNum = Long.toHexString(quote.getSequenceId());
		Iterator prices = quote.getQuotePrices().iterator();
		List<Group> mdEntries = new ArrayList<Group>();
		while ( prices.hasNext() )
		{
			com.integral.fix.client.message.grp.NoMDEntries noMDEntries = mdResponse.createNoMDEntries();
			FXLegDealingPrice dp = (FXLegDealingPrice) prices.next();

			if ( !dp.isActive() )
			{
				continue;
			}

			FXLegDealingPrice tick = (FXLegDealingPrice) dp;
			//			FXRate fxRate = dp.getFXRate();

			if ( valueDate == null )
			{
				valueDate = rateEvent.getValueDate();
			}

			noMDEntries.set(FixConstants.MD_POSITION_NO_ZERO);
			if ( dp.getBidOfferMode() == DealingPrice.BID )
			{
				if ( bidSize >= marketDepth && marketDepth != 0 )
				{
					continue;
				}
				noMDEntries.set(FixConstants.BID);
				bidSize++;
				if ( isMultiTierQuote )
				{
					noMDEntries.set(getMDEntryPositionNo(bidSize));
				}
			}
			else
			{
				if ( offerSize >= marketDepth && marketDepth != 0 )
				{
					continue;
				}
				noMDEntries.set(FixConstants.OFFER);
				offerSize++;
				if ( isMultiTierQuote )
				{
					noMDEntries.set(getMDEntryPositionNo(offerSize));
				}
			}

			noMDEntries.set(new MDEntryPx(tick.getRate()));
			double mDEntrySize = tick.getDealtAmount();
			noMDEntries.set(new MDEntrySize(mDEntrySize));
			if ( isTradable && tick.getRate() > 0 && mDEntrySize > 0 )
			{
				noMDEntries.set(FixConstants.QUOTE_CONDITION_ACTIVE);
			}
			else
			{
				if ( isBreakIfNoEntry )
				{
					mdEntries = new ArrayList<Group>();
					break;
				}
				noMDEntries.set(FixConstants.QUOTE_CONDITION_INACTIVE);
			}
			switch (subscriptionData.getMdEntryIdMode()){
				case FixConstants.MD_ENTRY_ID_MODE_SEQUENCE_NUMBER:
					noMDEntries.set(new QuoteEntryID(new StringBuilder(20).append(seqNum).append('_').append(tick.getName()).toString()));
					break;
				case FixConstants.MD_ENTRY_ID_MODE_TICK_GUID:
					noMDEntries.set(new QuoteEntryID(tick.getGUID()));
					break;
				case FixConstants.MD_ENTRY_ID_MODE_PROVIDER_QUOTE_ID:
					noMDEntries.set(new QuoteEntryID(new StringBuilder(20).append(quote.getExternalQuoteId()).append('_').append(tick.getName()).toString()));
					break;
				default:
					noMDEntries.set(new QuoteEntryID(new StringBuilder(20).append(seqNum).append('_').append(tick.getName()).toString()));
					break;
			}
			noMDEntries.set(subscriptionData.getBaseCurrency());
			noMDEntries.set(mdEntryOriginator);
			if ( quote.getMinimumSize() != null && quote.getMinimumSize() > 0 )
			{
				noMDEntries.set(new MinQty(quote.getMinimumSize()));
			}
			mdEntries.add(noMDEntries.getGroup());
		}
		if ( null != valueDate )
		{
			mdResponse.set(new MaturityDate(valueDate.getFormattedDate(IdcDate.YYYY_MM_DD)));
		}
		mdResponse.setGroups(NoMDEntries.FIELD, mdEntries);
		mdResponse.set(new MDReqID(subscriptionData.getMdRequestID()));
		mdResponse.getHeader().setField(subscriptionData.getTargetSubID());
		return mdResponse;
	}

	public MarketDataSnapshotFullRefresh getQuoteV2( Quote quote, FixSubscription subscriptionData, MarketDataSnapshotFullRefresh mdResponse, boolean isBreakIfNoEntry )
	{
		mdResponse.set(FixConstants.PRODUCT);
		mdResponse.set(subscriptionData.getSymbol());
		boolean isTradable = FixConstants.QUOTE_TRADABLE_TYPE.equals(quote.getQuoteClassification().getShortName());
		boolean isMultiTierQuote = (quote.getPriceType() == Quote.PRICE_TYPE_MULTI_TIER);
		int marketDepth2 = FixUtilC.getInstance().getAllowedMarketDepth(subscriptionData.getOrganization(), quote.getOrganization());
		int marketDepth = subscriptionData.getMarketDepth();
		if ( marketDepth2 > 0 )
		{
			if ( marketDepth == 0 ) //Customer asked for full book.
			{
				marketDepth = marketDepth2;
			}
			else
			{
				if ( marketDepth2 < marketDepth )
				{
					marketDepth = marketDepth2;
				}
			}
		}

		Quote rateEvent = (Quote) quote;
		MDEntryOriginator mdEntryOriginator = subscriptionData.getMaskedProviderName();
		IdcDate valueDate = null;
		int bidSize = 0;
		int offerSize = 0;
		String seqNum = Long.toHexString(quote.getSequenceId());
		Iterator prices = quote.getQuotePrices().iterator();
		boolean isZeroSizeCheckEnabled = ISFactory.getInstance().getISMBean().isZeroQuoteSizeActive();
		while ( prices.hasNext() )
		{
			ExternallySortedGroup mDEntry = new ExternallySortedGroup(268, 269, mdentryFieldOrder);
			FXLegDealingPrice dp = (FXLegDealingPrice) prices.next();

			if ( !dp.isActive() && isTradable )
			{
				continue;
			}

			//			FXRate fxRate = dp.getFXRate();
			FXLegDealingPrice tick = (FXLegDealingPrice) dp;

			if ( valueDate == null )
			{
				valueDate = rateEvent.getValueDate();
			}

			if ( dp.getBidOfferMode() == DealingPrice.BID )
			{
				if ( bidSize >= marketDepth && marketDepth != 0 )
				{
					continue;
				}
				bidSize++;
				mDEntry.setChar(MDEntryType.FIELD, MDEntryType.BID);
			}
			else
			{
				if ( offerSize >= marketDepth && marketDepth != 0 )
				{
					continue;
				}
				offerSize++;
				mDEntry.setChar(MDEntryType.FIELD, MDEntryType.OFFER);
			}

			mDEntry.setDouble(MDEntryPx.FIELD, tick.getRate());
			mDEntry.setField(subscriptionData.getBaseCurrency());
			double mDEntrySize = tick.getDealtAmount();
			mDEntry.setDouble(MDEntrySize.FIELD, mDEntrySize);

			isTradable = isTradable && (isZeroSizeCheckEnabled ? (tick.getRate() > 0) : (tick.getRate() > 0 && mDEntrySize > 0));
			if ( isTradable )
			{
				mDEntry.setField(FixConstants.QUOTE_CONDITION_ACTIVE);
			}
			else
			{
				mDEntry.setField(FixConstants.QUOTE_CONDITION_INACTIVE);
			}
			mDEntry.setField(mdEntryOriginator);
			if ( quote.getMinimumSize() != null && quote.getMinimumSize() > 0 )
			{
				mDEntry.setDouble(MinQty.FIELD, quote.getMinimumSize());
			}
			switch (subscriptionData.getMdEntryIdMode()){
				case FixConstants.MD_ENTRY_ID_MODE_SEQUENCE_NUMBER:
					mDEntry.setField(new QuoteEntryID(new StringBuilder(20).append(seqNum).append('_').append(tick.getName()).toString()));
					break;
				case FixConstants.MD_ENTRY_ID_MODE_TICK_GUID:
					mDEntry.setField(new QuoteEntryID(tick.getGUID()));
					break;
				case FixConstants.MD_ENTRY_ID_MODE_PROVIDER_QUOTE_ID:
					mDEntry.setField(new QuoteEntryID(new StringBuilder(20).append(quote.getExternalQuoteId()).append('_').append(tick.getName()).toString()));
					break;
				default:
					mDEntry.setField(new QuoteEntryID(new StringBuilder(20).append(seqNum).append('_').append(tick.getName()).toString()));
					break;
			}

			if ( isMultiTierQuote )
			{
				if ( dp.getBidOfferMode() == DealingPrice.BID )
				{
					mDEntry.setInt(MDEntryPositionNo.FIELD, getMDEntryPositionNo(bidSize).getValue());
				}
				else
				{
					mDEntry.setInt(MDEntryPositionNo.FIELD, getMDEntryPositionNo(offerSize).getValue());
				}
			}
			else
			{
				mDEntry.setInt(MDEntryPositionNo.FIELD, FixConstants.MD_POSITION_NO_ZERO.getValue());
			}

			mdResponse.addGroup(mDEntry);
		}
		if ( null != valueDate )
		{
			mdResponse.set(new MaturityDate(valueDate.getFormattedDate(IdcDate.YYYY_MM_DD)));
		}
		mdResponse.set(new MDReqID(subscriptionData.getMdRequestID()));
		mdResponse.getHeader().setField(subscriptionData.getTargetSubID());
		return mdResponse;
	}

	@Override
	protected MarketDataSnapshotFullRefresh getInactiveQuote( String lpName, String ccyPair, String mdRequestID, String senderSubID, String quoteID, Organization custOrg, SessionID sessionID )
	{
		MarketDataSnapshotFullRefresh mdResponse = new MarketDataSnapshotFullRefresh43();
		mdRequestID = getMDRequestID(mdRequestID);
		com.integral.fix.client.message.grp.NoMDEntries noMDBidEntries = mdResponse.createNoMDEntries();
		lpName = FixUtilC.getInstance().getMaskedName(custOrg.getShortName(), lpName);
		MDEntryOriginator mdEntryOriginator = new MDEntryOriginator(lpName);

		mdResponse.set(FixConstants.PRODUCT);
		mdResponse.set(new Symbol(ccyPair));
		noMDBidEntries.set(FixConstants.BID);
		noMDBidEntries.set(FixConstants.MD_POSITION_NO_ZERO);
		noMDBidEntries.set(FixConstants.MD_ENTRY_PX_ZERO);
		noMDBidEntries.set(FixConstants.MD_ENTRY_SIZE_ZERO);
		noMDBidEntries.set(new QuoteEntryID(new StringBuilder(20).append(quoteID).append( '_' ).append("BID").toString()));
		String baseCcyStr = CurrencyFactory.getBaseCurrency(ccyPair);
		noMDBidEntries.set(new Currency(baseCcyStr));
		noMDBidEntries.set(mdEntryOriginator);
		noMDBidEntries.set(FixConstants.QUOTE_CONDITION_INACTIVE);
		noMDBidEntries.set(mdEntryOriginator);
		mdResponse.addGroup(noMDBidEntries);

		com.integral.fix.client.message.grp.NoMDEntries noMDOfferEntries = mdResponse.createNoMDEntries();
		noMDOfferEntries.set(FixConstants.OFFER);
		noMDOfferEntries.set(FixConstants.MD_POSITION_NO_ZERO);
		noMDOfferEntries.set(FixConstants.MD_ENTRY_PX_ZERO);
		noMDOfferEntries.set(FixConstants.MD_ENTRY_SIZE_ZERO);
		noMDOfferEntries.set(new QuoteEntryID(new StringBuilder(20).append(quoteID).append( '_' ).append("OFFER").toString()));
		noMDOfferEntries.set(new Currency(baseCcyStr));
		noMDOfferEntries.set(FixConstants.QUOTE_CONDITION_INACTIVE);
		noMDOfferEntries.set(mdEntryOriginator);
		mdResponse.addGroup(noMDOfferEntries);
		mdResponse.set(new MDReqID(mdRequestID));
		mdResponse.getHeader().setField(new TargetSubID(senderSubID));

		return mdResponse;

	}

	@Override
	protected void setAccount( ExecutionReport report, NewOrderSingle newOrderSingle, Request request, FXSingleLeg trade )
	{
		// No implementation needed for FIX 43
	}

    @Override
    protected void setBelowMinimumField( ExecutionReport report, double minQtyDouble )
    {
		// No implementation needed for FIX 43
	}

	public MarketDataIncrementalRefresh getRateUpdateMessage( MarketTierPriceChangeSet changeSet, FixSubscription subscription, boolean isFXPriceBookupdate )
	{
		MarketDataIncrementalRefresh message = new MarketDataIncrementalRefresh43();
		message.setMDReqID(new MDReqID(subscription.getMdRequestID()));

		String ccyPair = subscription.getCurrencyPair().getName();
		List<Group> mdEntries = new ArrayList<Group>();

		if ( changeSet.getMarketTierPrices() == null || changeSet.getMarketTierPrices().isEmpty() )
		{
			if ( log.isDebugEnabled() )
			{
				log.debug("ApplicationTranslator43Impl.getRateUpdateMesage() MarketTierPriceChangeSet contains no market tier prices");
			}
			return null;
		}

		boolean isAggregatedBook = subscription.isAggregatedBook();
		String fiOrg = subscription.getOrganization().getShortName();
		MDEntryOriginator mdEntryOriginator = null;

		for ( MarketTierPrice price : changeSet.getMarketTierPrices() )
		{
			IRNoMDEntries mdEntry = new MDIRNoMDEntries43();
			mdEntry.set(new MDEntryID(price.getPriceId()));

			if ( isFXPriceBookupdate )
			{
				if ( configMbean.isAggregationProviderNameSupportEnabled(fiOrg) || (!isAggregatedBook && !subscription.isVWAPAggregation()) )
				{
					mdEntryOriginator = new MDEntryOriginator(price.getProvider());
				}
				else
				{
					String providerName = configMbean.getAggregatedBookProviderName(fiOrg);
					mdEntryOriginator = new MDEntryOriginator(providerName);
				}
			}
			else
			{
				if ( !isAggregatedBook && !subscription.isVWAPAggregation() )
				{
					mdEntryOriginator = new MDEntryOriginator(price.getProvider());
				}
				else
				{
					if ( mdEntryOriginator == null )
					{
						String providerName = configMbean.getAggregatedBookProviderName(fiOrg);
						mdEntryOriginator = new MDEntryOriginator(providerName);
					}
				}
			}

			switch ( price.getAction() )
			{
			case MarketTierPrice.NEW_ACTION :
				mdEntry.set(price.getSide() == DealingPrice.BID ? FixConstants.BID : FixConstants.OFFER);
				mdEntry.set(new Symbol(ccyPair));
				mdEntry.set(new MDEntryPx(price.getRate()));
				mdEntry.set(new MDEntrySize(price.getLimit()));
				mdEntry.set(new MDUpdateAction(MDUpdateAction.NEW));
				mdEntry.set(mdEntryOriginator);
				break;
			case MarketTierPrice.UPDATE_ACTION :
				if ( price.getRate() != -1 )
				{
					mdEntry.set(new MDEntryPx(price.getRate()));
				}
				if ( price.getLimit() != -1 )
				{
					mdEntry.set(new MDEntrySize(price.getLimit()));
				}
				mdEntry.set(new MDUpdateAction(MDUpdateAction.CHANGE));

				//TODO -check with PM if provider should be sent
				if ( price.getProvider() != null )
				{
					mdEntry.set(mdEntryOriginator);
				}
				break;
			case MarketTierPrice.DELETE_ACTION :
				mdEntry.set(new MDUpdateAction(MDUpdateAction.DELETE));
				break;
			default :
				break;
			}
			mdEntries.add(mdEntry.getGroup());
		}
		message.setGroups(NoMDEntries.FIELD, mdEntries);

		return message;
	}

	@Override
	protected MarketDataSnapshotFullRefresh getActiveQuote( FXPriceBook book, Organization organization, String ccyPair, String baseCcy, int marketDepth, FixSubscription subscription )
	{
		MarketDataSnapshotFullRefresh mdResponse = new MarketDataSnapshotFullRefresh43();
		mdResponse.set(FixConstants.PRODUCT);
		String mdRequestID = subscription.getMdRequestID();
		mdResponse.set(subscription.getSymbol());
		addFxMDEntry(book, book.getBids(), FixConstants.BID, mdResponse, organization, marketDepth, subscription);
		addFxMDEntry(book, book.getOffers(), FixConstants.OFFER, mdResponse, organization, marketDepth, subscription);
		//SD org or FI org
		String valueDate = book.getValueDate();
		IdcDate idcValueDate = null;
		if (valueDate != null && !"".equals(valueDate.trim()))
		{
			try 
			{
				idcValueDate = DateTimeFactory.newDate(valueDate, dateFormater.get());
			}
			catch(Exception e)
			{
				log.info("Translator43.getActiveQuote:Problem with parsing value date:" + valueDate);
				idcValueDate = null;
			}			
		}		
	   	if (com.integral.finance.currency.Currency.SettlementType.FORWARD == subscription.getSettlementType())
    	{
    		IdcDate fixingDate = subscription.getFixingDate(idcValueDate);    		
    		if (fixingDate != null)
    		{
    			String maturityDate = fixingDate.getFormattedDate(IdcDate.YYYY_MM_DD);
    			mdResponse.setString(MaturityDate.FIELD, maturityDate);    			
    		}    		
    		if (valueDate != null)
    		{
    			mdResponse.setString(FutSettDate.FIELD, valueDate);
    		}
    		else
    		{
    			// populate tenor
    			Tenor tenor = subscription.getTenor();
        		if (tenor != null)
        		{
        			mdResponse.setString(FutSettDate.FIELD, tenor.getName());
        		}
    		}
    		
    	}
    	else
    	{
    		if( FIXClientConfig.getInstance().isSendBrokenValueDate(organization) )
    		{               
                //If broken date is available, set that and if not available set it to SPOT.
                if ( valueDate != null )
                {
                    mdResponse.set( new MaturityDate( valueDate ) );
                }
                else
                {                	
                	mdResponse.set( FixConstants.SPOT_MATURITYDATE );                    
                }
    		} 
    		else
    		{
    			mdResponse.set( FixConstants.SPOT_MATURITYDATE );  
    		}
    	} 
		mdResponse.set(new MDReqID(mdRequestID));
		mdResponse.getHeader().setField(subscription.getTargetSubID());
		return mdResponse;
	}

	public WorkflowMessage getLoginMessage( Logon logon, SessionID sessionID ) throws FieldNotFound
	{
		String userName = logon.getUsername().getValue();
		String password = logon.getPassword().getValue();
		String senderCompID = logon.getHeader().getString(SenderCompID.FIELD);
		int startIndex = senderCompID.indexOf('.');
		int endIndex = senderCompID.indexOf('.', startIndex + 1);
		String userOrgName;
		if ( endIndex < 0 )
		{
			userOrgName = senderCompID.substring(startIndex + 1);
		}
		else
		{
			userOrgName = senderCompID.substring(startIndex + 1, endIndex);
		}

		log.warn(new StringBuilder("FixTranslator43Impl.login userName ").append(userName).append(" FI ").append(userOrgName).toString());
		User user = UserFactory.getUser(new StringBuilder(userName).append(FixConstants.DELIMITER_USER_ORGANIZATION).append(userOrgName).toString());
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setTopic(FixConstants.MSG_TOPIC_SESSION);
		msg.setSender(user);
		msg.setPassword(password);
		msg.setEvent(FixConstants.MESSAGE_EVENT_LOGIN);
		msg.setObject(fixUtil.getExternalSystem(FixConstants.CHANNEL));
		return msg;
	}

	public MarketDataRequestReject getSubscriptionReject( MarketDataRequest marketDataRequest, String errorReason ) throws FieldNotFound
	{
		MarketDataRequestReject marketDataRequestReject = new MarketDataRequestReject43();
		marketDataRequestReject.set(marketDataRequest.getMDReqID());
		marketDataRequestReject.set(new Text(errorReason));
		if ( errorReason != null && !errorReason.equals(FixConstants.INTERNAL_SERVER_ERROR) )
		{
			marketDataRequestReject.set(FixConstants.MD_REQ_REJ_REASON_INSUFFICIENT_PERMISSIONS);
		}
		return marketDataRequestReject;
	}

	/*
	 * (non-Javadoc) - Outright reject due to validation error
	 * @see com.integral.fix.client.translator.FixTranslator43#getExecutionReportReject(quickfix.fix43.NewOrderSingle, java.lang.String)
	 */
	public ExecutionReport getExecutionReportReject( NewOrderSingle newOrderSingle, String errorCode, SessionID sessionID ) throws FieldNotFound
	{
		boolean isSwap = false;
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(newOrderSingle.getClOrdID());
		executionReport.set(FixConstants.EXEC_TYPE_REJECTED);
		executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(newOrderSingle.getSide());
		executionReport.set(newOrderSingle.getOrderQty());
		executionReport.set(FixConstants.CUM_QTY_ZERO);
		executionReport.set(FixConstants.LEAVES_QTY_ZERO);
		executionReport.set(new LastQty(0));
		executionReport.set(new LastPx(0));
		executionReport.set(new LastSpotRate(0));
		executionReport.set(new LastForwardPoints(0));
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(newOrderSingle.getCurrency());
		executionReport.set(new AvgPx(0));
		executionReport.set(newOrderSingle.getSymbol());
		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		if ( newOrderSingle.isSetPrice() )
		{
			executionReport.set(newOrderSingle.getPrice());
		}
		if ( newOrderSingle.isSetPrice2() )
		{
			executionReport.setField(newOrderSingle.getPrice2());
			isSwap = true;
		}
		if ( newOrderSingle.isSetOrderQty2() )
		{
			executionReport.setField(newOrderSingle.getOrderQty2());
			isSwap = true;
		}
		if ( newOrderSingle.isSetPegDifference() )
		{
			executionReport.set(newOrderSingle.getPegDifference());
		}
		if(newOrderSingle.isSetField(FutSettDate.FIELD))
		{
			executionReport.set(new FutSettDate(newOrderSingle.getString(FutSettDate.FIELD)));
		}
		if ( isSwap )
		{
			if ( newOrderSingle.isSetField(FutSettDate2.FIELD) )
			{
				executionReport.set(newOrderSingle.getFutSettDate2());
			}
			executionReport.setField(new DoubleField(7542, 0));//LastspotRate2
			executionReport.setField(new DoubleField(641, 0));//LastForwardPoints2
			executionReport.setField(new DoubleField(7541, 0));//lastPx2
			executionReport.setField(new DoubleField(7543, 0));//leavesQty2
			executionReport.setField(new DoubleField(7544, 0));//CumQty2
		}

		if ( newOrderSingle.isSetMaturityDate() )
		{
			executionReport.set(newOrderSingle.getMaturityDate());
		}

		if ( newOrderSingle.isSetTimeInForce() )
		{
			executionReport.set(newOrderSingle.getTimeInForce());
		}
		if ( newOrderSingle.isSetExpireTime() )
		{
			executionReport.set(newOrderSingle.getExpireTime());
		}
		if ( newOrderSingle.isSetMaxShow() )
		{
			executionReport.set(newOrderSingle.getMaxShow());
		}
		if ( newOrderSingle.isSetMinQty() )
		{
			executionReport.set(newOrderSingle.getMinQty());
		}
		if ( newOrderSingle.isSetExecInst() )
		{
			executionReport.set(newOrderSingle.getExecInst());
		}
		if ( newOrderSingle.isSetStopPx() )
		{
			executionReport.set(newOrderSingle.getStopPx());
		}
		executionReport.set(newOrderSingle.getOrdType());
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		executionReport.set(FixConstants.EXEC_ID_ZERO);
		User user = clientCache.getUser(sessionID);
		boolean isPopulateOrderID;
		isPopulateOrderID = (user == null ? FixUtilC.getInstance().getFIXClientConfig().isPopulateOrderIDOnValidationFailure() : FixUtilC.getInstance().getFIXClientConfig().isPopulateOrderIDOnValidationFailure(user.getOrganization()));
		String orderID = newOrderSingle.getOrderID();
		if(orderID != null)
		{
			executionReport.set(new OrderID(orderID));
		}
		else if ( isPopulateOrderID )
		{
			ClOrdID clOrdID = newOrderSingle.getClOrdID();
			executionReport.set(new OrderID(clOrdID == null ? "0" : clOrdID.getValue()));
		}
		else
		{
			executionReport.set(FixConstants.ORDER_ID_ZERO);
		}
		//executionReport.set(FixConstants.FUT_SETT_DATE_REJECT);
		if ( newOrderSingle.isSetHandlInst() && HandlInst.MANUAL_ORDER == newOrderSingle.getHandlInst().getValue())
		{
			String execId = null;
			if (newOrderSingle.isSetClOrdID()) 
			{
				execId = newOrderSingle.getClOrdID().getValue() + "R";
			}
			else
			{
				execId = System.currentTimeMillis() + "R";
			}				
			executionReport.set(new ExecID(execId));
			fixUtil.setRejectReasonForManualOrder(executionReport, errorCode);
		}
		else
		{
			fixUtil.setRejectReason(executionReport, errorCode);
		}		
		setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

        if( newOrderSingle.isWarmup() )
        	executionReport.setWarmup(newOrderSingle.isWarmup());

		return executionReport;
	}

    @Override
    public ExecutionReport getExecutionReportReject(NewOrderMultileg newOrderMultileg, String portfolioId, String errorCode, SessionID sessionID) throws FieldNotFound
    {
        return null;
    }

    /*
     * (non-Javadoc) Generates NEW and PENDIND NEW report.
     * @see com.integral.fix.client.translator.FixTranslator43#getOrderSubmissionExecutionReport(quickfix.fix43.NewOrderSingle, com.integral.finance.dealing.Request, java.lang.String)
     */
	public Message getOrderSubmissionExecutionReport( NewOrderSingle newOrderSingle, Request request, String execType ) throws FieldNotFound
	{
		boolean isSwap = false;
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(newOrderSingle.getClOrdID());
		ExecType exType = FixConstants.EXEC_TYPE_NEW;
		OrdStatus ordStatus = FixConstants.ORDER_STATUS_NEW;
		if ( "NEW".equalsIgnoreCase(execType) )
		{

		}
		else if ( "STOPPED".equalsIgnoreCase(execType) )
		{
			exType = FixConstants.EXEC_TYPE_STOPPED;
			ordStatus = FixConstants.ORDER_STATUS_STOPPED;
		}
		else
		{
			exType = FixConstants.EXEC_TYPE_PENDING_NEW;
			ordStatus = FixConstants.ORDER_STATUS_PENDING_NEW;
		}

		if ( "NEW".equalsIgnoreCase(execType) || "STOPPED".equalsIgnoreCase(execType) )
		{
			executionReport.set(new OrderID(request.getOrderId()));
			if ( request != null && request.getRequestAttributes().isNDF() )
			{
				// for RFQ, MaturityDate(tage 541) to be given as actual date
				FXPaymentParameters fxPayment = ((FXSingleLeg) request.getTrade()).getFXLeg().getFXPayment();
				executionReport.set(new MaturityDate(fxPayment.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
			}
		}
		else
		{
			if ( newOrderSingle.isSetMaturityDate() )// Expected to be present in RFQ
			{
				executionReport.set(newOrderSingle.getMaturityDate()); // copy the value (date/tenor) from origianl order
			}

			executionReport.set(new OrderID("NONE"));
		}
		executionReport.set(exType);
		executionReport.set(ordStatus);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(newOrderSingle.getSide());
		executionReport.set(newOrderSingle.getOrderQty());
		executionReport.set(FixConstants.CUM_QTY_ZERO);
		executionReport.set(new LeavesQty(newOrderSingle.getOrderQty().getValue()));
		executionReport.set(newOrderSingle.getCurrency());
		executionReport.set(new LastQty(0));
		executionReport.set(new LastPx(0));
		executionReport.set(new LastSpotRate(0));
		executionReport.set(new LastForwardPoints(0));
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new AvgPx(0));
		executionReport.set(newOrderSingle.getOrdType());
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		executionReport.set(new ExecID("NONE"));
		//executionReport.set(FixConstants.FUT_SETT_DATE_REJECT);
		executionReport.set(newOrderSingle.getSymbol());
		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		if ( newOrderSingle.isSetPrice() )
		{
			executionReport.set(newOrderSingle.getPrice());
		}
		if ( newOrderSingle.isSetTimeInForce() )
		{
			executionReport.set(newOrderSingle.getTimeInForce());
		}
		if ( newOrderSingle.isSetExpireTime() )
		{
			executionReport.set(newOrderSingle.getExpireTime());
		}
		if ( newOrderSingle.isSetMaxShow() )
		{
			executionReport.set(newOrderSingle.getMaxShow());
		}
		if ( newOrderSingle.isSetMinQty() )
		{
			executionReport.set(newOrderSingle.getMinQty());
		}
		if ( newOrderSingle.isSetPrice2() )
		{
			executionReport.setField(newOrderSingle.getPrice2());
			isSwap = true;
		}
		if ( newOrderSingle.isSetOrderQty2() )
		{
			executionReport.setField(newOrderSingle.getOrderQty2());
			isSwap = true;
		}
		if ( newOrderSingle.isSetExecInst() )
		{
			executionReport.set(newOrderSingle.getExecInst());
		}
		if ( newOrderSingle.isSetPegDifference() )
		{
			executionReport.set(newOrderSingle.getPegDifference());
		}
		if ( newOrderSingle.isSetStopPx() )
		{
			executionReport.set(newOrderSingle.getStopPx());
		}
		if(newOrderSingle.isSetField(FutSettDate.FIELD))
		{
			executionReport.set(new FutSettDate(newOrderSingle.getString(FutSettDate.FIELD)));
		}
		if ( isSwap )
		{
			if ( newOrderSingle.isSetField(FutSettDate2.FIELD) )
			{
				executionReport.set(newOrderSingle.getFutSettDate2());
			}
			//        	executionReport.setField( new DoubleField( 7542,0 ) );//LastspotRate2
			executionReport.setField(new DoubleField(641, 0));//LastForwardPoints2
			executionReport.setField(new DoubleField(7541, 0));//lastPx2
			executionReport.setField(new DoubleField(7543, 0));//leavesQty2
			executionReport.setField(new DoubleField(7544, 0));//CumQty2
		}
		if ( newOrderSingle.isSetField(OrigClOrdID.FIELD) )
		{
			executionReport.set(new OrigClOrdID(newOrderSingle.getString(OrigClOrdID.FIELD)));
		}

		FIXSEFUtilC.setSEFFields( executionReport, request );
		MiFIDHandlerFactory.getInstance().getMiFIDHandler().populateExecutionReport(executionReport,request);
		setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

        if( newOrderSingle.isWarmup() )
        	executionReport.setWarmup( newOrderSingle.isWarmup());

		return executionReport.getMessage();
	}

	public Message getOrderSubmissionPendingExecutionReport( NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		boolean isSwap = false;
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(newOrderSingle.getClOrdID());
		ExecType exType = FixConstants.EXEC_TYPE_PENDING_NEW;
		OrdStatus ordStatus = FixConstants.ORDER_STATUS_PENDING_NEW;

		if (newOrderSingle.isSetMaturityDate())// Expected to be present in RFQ
		{
			executionReport.set(newOrderSingle.getMaturityDate()); // copy the value (date/tenor) from origianl order
		}

		executionReport.set(new OrderID("NONE"));
		executionReport.set(exType);
		executionReport.set(ordStatus);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(newOrderSingle.getSide());
		executionReport.set(newOrderSingle.getOrderQty());
		executionReport.set(FixConstants.CUM_QTY_ZERO);
		executionReport.set(new LeavesQty(newOrderSingle.getOrderQty().getValue()));
		executionReport.set(newOrderSingle.getCurrency());
		executionReport.set(new LastQty(0));
		executionReport.set(new LastPx(0));
		executionReport.set(new LastSpotRate(0));
		executionReport.set(new LastForwardPoints(0));
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new AvgPx(0));
		executionReport.set(newOrderSingle.getOrdType());
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		executionReport.set(new ExecID("NONE"));
		//executionReport.set(FixConstants.FUT_SETT_DATE_REJECT);
		executionReport.set(newOrderSingle.getSymbol());
		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		if ( newOrderSingle.isSetPrice() )
		{
			executionReport.set(newOrderSingle.getPrice());
		}
		if ( newOrderSingle.isSetTimeInForce() )
		{
			executionReport.set(newOrderSingle.getTimeInForce());
		}
		if ( newOrderSingle.isSetExpireTime() )
		{
			executionReport.set(newOrderSingle.getExpireTime());
		}
		if ( newOrderSingle.isSetMaxShow() )
		{
			executionReport.set(newOrderSingle.getMaxShow());
		}
		if ( newOrderSingle.isSetMinQty() )
		{
			executionReport.set(newOrderSingle.getMinQty());
		}
		if ( newOrderSingle.isSetPrice2() )
		{
			executionReport.setField(newOrderSingle.getPrice2());
			isSwap = true;
		}
		if ( newOrderSingle.isSetOrderQty2() )
		{
			executionReport.setField(newOrderSingle.getOrderQty2());
			isSwap = true;
		}
		if ( newOrderSingle.isSetExecInst() )
		{
			executionReport.set(newOrderSingle.getExecInst());
		}
		if ( newOrderSingle.isSetPegDifference() )
		{
			executionReport.set(newOrderSingle.getPegDifference());
		}
		if ( newOrderSingle.isSetStopPx() )
		{
			executionReport.set(newOrderSingle.getStopPx());
		}
		if(newOrderSingle.isSetField(FutSettDate.FIELD))
		{
			executionReport.set(new FutSettDate(newOrderSingle.getString(FutSettDate.FIELD)));
		}
		if ( isSwap )
		{
			if ( newOrderSingle.isSetField(FutSettDate2.FIELD) )
			{
				executionReport.set(newOrderSingle.getFutSettDate2());
			}
			//        	executionReport.setField( new DoubleField( 7542,0 ) );//LastspotRate2
			executionReport.setField(new DoubleField(641, 0));//LastForwardPoints2
			executionReport.setField(new DoubleField(7541, 0));//lastPx2
			executionReport.setField(new DoubleField(7543, 0));//leavesQty2
			executionReport.setField(new DoubleField(7544, 0));//CumQty2
		}
		if ( newOrderSingle.isSetField(OrigClOrdID.FIELD) )
		{
			executionReport.set(new OrigClOrdID(newOrderSingle.getString(OrigClOrdID.FIELD)));
		}
		setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		if( newOrderSingle.isWarmup() )
			executionReport.setWarmup( newOrderSingle.isWarmup());
		return executionReport.getMessage();
	}

	public ExecutionReport getExecutionReportDelayed( OrderStatusRequest orderStatusRequest, NewOrderSingle newOrderSingle, Request request ) throws FieldNotFound
	{
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(newOrderSingle.getClOrdID());
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(FixConstants.ORDER_STATUS_NEW);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(newOrderSingle.getSide());
		executionReport.set(newOrderSingle.getSymbol());
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		if (newOrderSingle.isSetField(FixConstants.FIX_FIELD_Tenor))
		{
			String valueDate = newOrderSingle.getString(FixConstants.FIX_FIELD_Tenor);
			executionReport.setString(FixConstants.FIX_FIELD_Tenor, valueDate);
		}
		executionReport.set(new LeavesQty(newOrderSingle.getOrderQty().getValue()));
		executionReport.set(newOrderSingle.getCurrency());
		executionReport.set(newOrderSingle.getOrderQty());
		executionReport.set(FixConstants.CUM_QTY_ZERO);
		executionReport.set(FixConstants.EXEC_ID_ZERO);
		executionReport.set(FixConstants.ORDER_ID_ZERO);
		if ( request != null && request.getOrderId() != null )
		{
			executionReport.set(new OrderID(request.getOrderId()));
		}
		//executionReport.set(FixConstants.FUT_SETT_DATE_REJECT);
		if ( orderStatusRequest.isSetClOrdID() )
		{
			executionReport.setString(790, orderStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
		}
		else
		{
			executionReport.setString(790, orderStatusRequest.getOrderID().getValue());//orderstatusreqiid.
		}
		executionReport.set(newOrderSingle.getOrdType());
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new AvgPx(0));
		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		if ( newOrderSingle.isSetPrice() )
		{
			executionReport.set(newOrderSingle.getPrice());
		}
		if ( newOrderSingle.isSetTimeInForce() )
		{
			executionReport.set(newOrderSingle.getTimeInForce());
		}
		if ( newOrderSingle.isSetStopPx() )
		{
			executionReport.set(newOrderSingle.getStopPx());
		}
		newOrderSingle.set(newOrderSingle.getOrdType());
		setHeader(orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		return executionReport;
	}

	public ExecutionReport getRFSExecutionReportDelayed( OrderStatusRequest orderStatusRequest, NewOrderSingle newOrderSingle, Request request ) throws FieldNotFound
	{
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(newOrderSingle.getClOrdID());
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(FixConstants.ORDER_STATUS_NEW);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(newOrderSingle.getSide());
		executionReport.set(newOrderSingle.getSymbol());
		executionReport.set(newOrderSingle.getPrice());
		executionReport.set(new AvgPx(0.0));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		executionReport.set(new LeavesQty(newOrderSingle.getOrderQty().getValue()));
		executionReport.set(newOrderSingle.getCurrency());
		executionReport.set(newOrderSingle.getOrderQty());
		executionReport.set(FixConstants.CUM_QTY_ZERO);
		executionReport.set(FixConstants.EXEC_ID_ZERO);
		executionReport.set(FixConstants.ORDER_ID_ZERO);
		if ( request != null && request.getOrderId() != null )
		{
			executionReport.set(new OrderID(request.getOrderId()));
		}
		//executionReport.set(FixConstants.FUT_SETT_DATE_REJECT);
		if ( orderStatusRequest.isSetClOrdID() )
		{
			executionReport.setString(790, orderStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
		}
		else
		{
			executionReport.setString(790, orderStatusRequest.getOrderID().getValue());//orderstatusreqiid.
		}
		executionReport.set(newOrderSingle.getOrdType());
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(FixConstants.AVG_PX_REJECT);
		executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.FILL_OR_KILL));
		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		if ( newOrderSingle.isSetPrice2() )
		{
			executionReport.setField(newOrderSingle.getPrice2());
			executionReport.set(newOrderSingle.getOrderQty2());
			executionReport.set(FixConstants.FUT_SETT_DATE_2_REJECT);
			executionReport.setField(new DoubleField(7543, newOrderSingle.getOrderQty2().getValue()));//leavesQty2
			executionReport.setField(new DoubleField(7544, 0));//CumQty2
		}

		if ( newOrderSingle.isSetMaturityDate() )
		{
			executionReport.set(newOrderSingle.getMaturityDate());
		}
		setHeader(orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

		return executionReport;
	}

	public ExecutionReport getExecutionReportReject( OrderStatusRequest orderStatusRequest, String errorCode ) throws FieldNotFound
	{
		ExecutionReport executionReport = new ExecutionReport43();

		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.BUY);
		if ( orderStatusRequest.isSetSide() )
		{
			executionReport.set(orderStatusRequest.getSide());
		}
		if ( orderStatusRequest.isSetSymbol() )
		{
			executionReport.set(orderStatusRequest.getSymbol());
		}
		executionReport.set(FixConstants.PRICE_REJECT);
		executionReport.set(FixConstants.AVG_PX_REJECT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
		executionReport.set(FixConstants.ORDER_QTY_ZERO);
		executionReport.set(FixConstants.LEAVES_QTY_ZERO);
		executionReport.set(FixConstants.CUM_QTY_ZERO);
		executionReport.set(FixConstants.EXEC_ID_ZERO);
		executionReport.set(FixConstants.ORDER_ID_ZERO);
		if ( orderStatusRequest.isSetClOrdID() )
		{
			executionReport.set(orderStatusRequest.getClOrdID());
			executionReport.setString(790, orderStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
		}
		else
		{
			String orderID = orderStatusRequest.getOrderID().getValue();
			executionReport.set(new ClOrdID(orderID));
			executionReport.setString(790, orderID);//orderstatusreqiid.
		}
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		//executionReport.set(FixConstants.FUT_SETT_DATE_REJECT);
		executionReport.set(FixConstants.ORD_REJ_REASON_UNKNOWN_ORDER);
		executionReport.set(new Text(errorCode));

		setHeader(orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

		return executionReport;
	}

	public ExecutionReport getExecutionReport( Trade trade, OrderStatusRequest orderStatusRequest ) throws FieldNotFound
	{
		Request request = trade.getRequest();
		RequestPriceFacade rpf = (RequestPriceFacade) request.getRequestPrice(FixConstants.SINGLE_LEG).getFacade(RequestPriceFacade.FACADE_NAME);
		RequestStateFacade rsf = (RequestStateFacade) request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE);

		FXPaymentParameters fxPayment = ((FXSingleLeg) trade).getFXLeg().getFXPayment();
		double orderAmount = rpf.getFilledAmount() + rpf.getUnfilledAmount();
		double executedAmount = rpf.getVerifiedAmount();
		com.integral.finance.currency.Currency dealtCcyObj = fxPayment.isDealtCurrency1() ? fxPayment.getCurrency1() : fxPayment.getCurrency2();
		String dealtCurrency = dealtCcyObj.getShortName();
		double lastDealtAmount = fxPayment.isDealtCurrency1() ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount();
		Side side = dealtCcyObj.isSameAs(fxPayment.getCurrency1()) ? (fxPayment.isBuyingCurrency1() ? FixConstants.BUY : FixConstants.SELL) : (fxPayment.isBuyingCurrency1() ? FixConstants.SELL : FixConstants.BUY);
		boolean isPartial = ((orderAmount - executedAmount) > 0);
		String extReqID = request.getExternalRequestId();
		if ( extReqID == null )
		{
			extReqID = "NONE";
		}
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(new ClOrdID(extReqID));
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(side);
		executionReport.set(new Price(fxPayment.getFXRate().getSpotRate()));
		executionReport.set(new Symbol(fixUtil.getCurrencyPair(request).getName()));
		executionReport.set(FixConstants.ORD_TYPE_PREVIOUSLY_QUOTED);
		executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.IMMEDIATE_OR_CANCEL));//defualt IOC
		executionReport.setUtcTimeStamp(TransactTime.FIELD, trade.getCreatedDate(), true);
		if ( rsf.isAcceptVerified() || rsf.isPartial() )
		{
			if ( isPartial )
			{
				executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
			}
			else
			{
				executionReport.set(FixConstants.ORDER_STATUS_FILLED);
			}
			executionReport.set(new LastQty(lastDealtAmount));
			executionReport.set(new AvgPx(rpf.getAverageFillPrice()));
			executionReport.set(new CumQty(executedAmount));
			executionReport.set(new LeavesQty(orderAmount - executedAmount));
			executionReport.set(new FutSettDate(fxPayment.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		}
		else
		{
			executionReport.set(FixConstants.ORDER_STATUS_NEW);
			if ( rsf.isDeclined() )
			{
				executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
				executionReport.set(new Text(trade.getWorkflowStateMap().getWorkflowCodeArgument()));
			}
			//executionReport.set( FixConstants.LAST_QTY_ZERO );
			executionReport.set(FixConstants.AVG_PX_REJECT);
			executionReport.set(FixConstants.CUM_QTY_ZERO);
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
		}

		if ( fxPayment.getFixingDate() != null )
		{
			executionReport.set(new MaturityDate(fxPayment.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		}

		executionReport.set(new Currency(dealtCurrency));
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new ExecID("0"));
		executionReport.set(new OrderID(request.getOrderId()));
		if ( orderStatusRequest.isSetClOrdID() )
		{
			executionReport.setString(790, orderStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
		}
		else
		{
			executionReport.setString(790, orderStatusRequest.getOrderID().getValue());//orderstatusreqiid.
		}
		setHeader(orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		if ( trade.getCounterpartyB() != null )
			setHeader(executionReport, SenderSubID.FIELD, trade.getCounterpartyB().getShortName());
		return executionReport;
	}

	public ExecutionReport getRFSExecutionReport( Trade tradeObj, OrderStatusRequest orderStatusRequest ) throws FieldNotFound
	{
		Request request = null;
		FXLegDealingPrice dealingPrice = null;
		FXLegDealingPrice dealingPrice_far_leg = null;
		FXPaymentParameters fxPayment = null;
		Date createdDate = null;
		boolean isSwap = false;
		if ( tradeObj instanceof FXSingleLeg )
		{
			FXSingleLeg trade = (FXSingleLeg) tradeObj;
			request = trade.getRequest();
			dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
			fxPayment = trade.getFXLeg().getFXPayment();
			createdDate = trade.getCreatedDate();
		}
		else if ( tradeObj instanceof FXSwap )
		{
			isSwap = true;
			FXSwap trade = (FXSwap) tradeObj;
			request = trade.getRequest();
			dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.NEAR_LEG);
			dealingPrice_far_leg = (FXLegDealingPrice) request.getRequestPrice(FixConstants.FAR_LEG);
			fxPayment = trade.getFarLeg().getFXPayment();
			createdDate = trade.getCreatedDate();
		}

		FXLegDealingPrice fxLegAcceptedDealingPrice = (FXLegDealingPrice) dealingPrice.getAcceptedDealingPrice();
		int bidOfferMode = dealingPrice.getAcceptedPriceBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) fxLegAcceptedDealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		boolean isDealtCcy1 = fxLegAcceptedDealingPrice.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
		double filledPrice = fxRate.getRate();
		double spotRate = fxRate.getSpotRate();
		double spotForwardPoints = fxRate.getForwardPoints();
		double filledAmt = isDealtCcy1 ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount();
		double orderAmount = dealingPrice.getDealtAmount();
		double filledPrice2 = 0.0;
		double spotRate2 = 0.0;
		double spotForwardPoints2 = 0.0;
		double filledAmt2 = 0.0;
		double orderAmount2 = 0.0;
		Side side = null;
		if ( isSwap )
		{
			FXLegDealingPrice fxLegAcceptedDealingPrice2 = (FXLegDealingPrice) dealingPrice_far_leg.getAcceptedDealingPrice();
			int bidOfferMode2 = dealingPrice_far_leg.getAcceptedPriceBidOfferMode();
			boolean isBid2 = bidOfferMode2 == FXLegDealingPrice.BID;
			FXPrice fxPrice2 = ((FXDealingPriceElement) fxLegAcceptedDealingPrice2.getPriceElement()).getFXPrice();
			FXRate fxRate2 = isBid2 ? fxPrice2.getBidFXRate() : fxPrice2.getOfferFXRate();
			isDealtCcy1 = fxLegAcceptedDealingPrice2.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
			filledPrice2 = fxRate2.getRate();
			spotRate2 = fxRate2.getSpotRate();
			spotForwardPoints2 = fxRate2.getForwardPoints();
			filledAmt2 = isDealtCcy1 ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount();
			orderAmount2 = dealingPrice_far_leg.getDealtAmount();
			if ( dealingPrice_far_leg.getDealtCurrency().isSameAs(fxRate2.getCurrencyPair().getBaseCurrency()) )
			{
				side = isBid2 ? FixConstants.SELL : FixConstants.BUY;
			}
			else
			{
				side = isBid2 ? FixConstants.BUY : FixConstants.SELL;
			}

		}

		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.ORD_TYPE_PREVIOUSLY_QUOTED);
		executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.FILL_OR_KILL));
		executionReport.set(new ClOrdID(request.getExternalRequestId()));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID(("0")));
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.set(new FutSettDate(dealingPrice.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, createdDate, true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new Price(filledPrice));
		if ( isSwap )
		{
			executionReport.set(side);
			executionReport.setField(new Price2(filledPrice2));
			executionReport.set(new OrderQty2(orderAmount2));
			executionReport.set(new FutSettDate2(dealingPrice_far_leg.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		}
		else
		{
			if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
			{
				executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
			}
			else
			{
				executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
			}
		}

		if ( fxPayment.getFixingDate() != null )
		{
			executionReport.set(new MaturityDate(fxPayment.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		}

		RequestStateFacade facade = ((RequestStateFacade) request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE));

		if ( facade.isDeclined() || facade.isPending() )
		{
			executionReport.set(FixConstants.ORDER_STATUS_NEW);
			if ( facade.isDeclined() )
			{
				String rejectReason = (tradeObj).getWorkflowStateMap().getWorkflowCodeArgument();
				FixUtilC.getInstance().setRejectReason(executionReport, rejectReason);//sets error code and error reason
				executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
			}
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(FixConstants.CUM_QTY_ZERO);
			executionReport.set(FixConstants.AVG_PX_REJECT);
			executionReport.set(new LastSpotRate(0));
			executionReport.set(new LastForwardPoints(0));
			executionReport.set(new LastPx(0));
			if ( isSwap )
			{
				executionReport.setField(new DoubleField(7542, 0));//LastspotRate2
				executionReport.setField(new DoubleField(641, 0));//LastForwardPoints2
				executionReport.setField(new DoubleField(7541, 0));//lastPx2
				executionReport.setField(new DoubleField(7543, 0));//leavesQty2
				executionReport.setField(new DoubleField(7544, 0));//CumQty2
			}
		}
		else
		{
			//Set other quantities and prices
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(new CumQty(filledAmt));
			executionReport.set(new LastQty(filledAmt));
			executionReport.set(new LastSpotRate(spotRate));
			executionReport.set(new LastForwardPoints(spotForwardPoints));
			executionReport.set(new LastPx(filledPrice));
			if ( isSwap )
			{
				executionReport.setField(new DoubleField(7542, spotRate2));//LastspotRate2
				executionReport.setField(new DoubleField(641, spotForwardPoints2));//LastForwardPoints2
				executionReport.setField(new DoubleField(7541, filledPrice2));//lastPx2
				executionReport.setField(new DoubleField(7543, 0));//leavesQty2
				executionReport.setField(new DoubleField(7544, filledAmt2));//CumQty2
			}
			executionReport.set(new AvgPx(filledPrice));
			if ( facade.isPartial() )
			{
				executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
			}
			else if ( facade.isAcceptVerified() )
			{
				executionReport.set(FixConstants.ORDER_STATUS_FILLED);
			}
		}
		if ( orderStatusRequest.isSetClOrdID() )
		{
			executionReport.setString(790, orderStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
		}
		else
		{
			executionReport.setString(790, orderStatusRequest.getOrderID().getValue());//orderstatusreqiid.
		}
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);

		setHeader(orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		if ( tradeObj.getCounterpartyB() != null )
		{
			setHeader(executionReport, SenderSubID.FIELD, tradeObj.getCounterpartyB().getShortName());
		}
		return executionReport;
	}

	/* (non-Javadoc)
	  * @see com.integral.fix.client.translator.FixTranslator43#getExecutionReportOrderCancelPending(quickfix.fix43.OrderCancelRequest)
	  */
	public Message getExecutionReportOrderCancelPending( OrderCancelRequest orderCancelRequest, NewOrderSingle newOrderSingle, Request request ) throws FieldNotFound
	{
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(orderCancelRequest.getClOrdID());
		executionReport.set(orderCancelRequest.getOrigClOrdID());
		executionReport.set(new OrderID("NONE"));
		executionReport.set(FixConstants.EXEC_TYPE_PENDING_CANCEL);
		executionReport.set(FixConstants.ORDER_STATUS_PENDING_CANCEL);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new CumQty(request.getRequestAttributes().getOrderFilledAmt()));
		executionReport.set(new LeavesQty(request.getRequestAttributes().getOrderUnfilledAmt()));
		executionReport.set(new AvgPx(0.0));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		executionReport.set(FixConstants.EXEC_ID_ZERO);
		if ( newOrderSingle != null )
		{
			copyOrderAttributes(newOrderSingle, executionReport);
			setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
			if( newOrderSingle.isWarmup() )
				executionReport.setWarmup(newOrderSingle.isWarmup());
		}
		else
		{
			copyOrderAttributesFromDealingPrice(request, executionReport, true);
			setHeader(executionReport, TargetSubID.FIELD, request.getCounterparty().getShortName());
		}
		
        return executionReport.getMessage();
	}

	/* (non-Javadoc)
	  * @see com.integral.fix.client.translator.FixTranslator43#getExecutionReportOrderCancelReplacePending(quickfix.fix43.OrderCancelReplaceRequest)
	  */
	public Message getExecutionReportOrderCancelReplacePending( OrderCancelReplaceRequest orderCancelReplaceRequest, NewOrderSingle newOrderSingle, Request request ) throws FieldNotFound
	{
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(orderCancelReplaceRequest.getClOrdID());
		executionReport.set(new OrderID("NONE"));
		executionReport.set(FixConstants.EXEC_TYPE_PENDING_REPLACE);
		executionReport.set(FixConstants.ORDER_STATUS_PENDING_REPLACE);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new CumQty(request.getRequestAttributes().getOrderFilledAmt()));
		executionReport.set(new LeavesQty(request.getRequestAttributes().getOrderUnfilledAmt()));
		executionReport.set(new AvgPx(0.0));
		executionReport.set(orderCancelReplaceRequest.getOrigClOrdID());
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		executionReport.set(FixConstants.EXEC_ID_ZERO);
		if ( newOrderSingle != null )
		{
			copyOrderAttributes(newOrderSingle, executionReport);
			setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		}
		else
		{
			copyOrderAttributesFromDealingPrice(request, executionReport, true);
			setHeader(executionReport, TargetSubID.FIELD, request.getCounterparty().getShortName());
		}
		return executionReport.getMessage();
	}

	/* (non-Javadoc)
	  * @see com.integral.fix.client.translator.FixTranslator43#getOrderMassCancelReject(quickfix.fix43.OrderMassCancelRequest)
	  */
	public Message getOrderMassCancelReject( OrderMassCancelRequest orderMassCancelRequest, String errorCode ) throws FieldNotFound
	{
		OrderMassCancelReport orderMassCancelReport = new OrderMassCancelReport43();
		orderMassCancelReport.set(new OrderID("NONE"));
		orderMassCancelReport.set(orderMassCancelRequest.getMassCancelRequestType());
		orderMassCancelReport.set(orderMassCancelRequest.getClOrdID());
		orderMassCancelReport.set(new MassCancelResponse(MassCancelResponse.CANCEL_REQUEST_REJECTED));
		orderMassCancelReport.set(new MassCancelRejectReason(MassCancelRejectReason.MASS_CANCEL_NOT_SUPPORTED));
		orderMassCancelReport.set(new Text(errorCode));
		orderMassCancelReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		setHeader(orderMassCancelRequest, orderMassCancelReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderMassCancelRequest, orderMassCancelReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderMassCancelRequest, orderMassCancelReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderMassCancelRequest, orderMassCancelReport, SenderSubID.FIELD, TargetSubID.FIELD);
		return orderMassCancelReport.getMessage();
	}

	/* (non-Javadoc)
	  * @see com.integral.fix.client.translator.FixTranslator43#getOrderMassCancelReport(quickfix.fix43.OrderMassCancelRequest, com.integral.message.WorkflowMessage)
	  */
	public Message getOrderMassCancelReport( OrderMassCancelRequest orderMassCancelRequest, WorkflowMessage response ) throws FieldNotFound
	{
		OrderMassCancelReport orderMassCancelReport = new OrderMassCancelReport43();
		orderMassCancelReport.set(orderMassCancelRequest.getClOrdID());
		orderMassCancelReport.set(new MassCancelResponse(MassCancelResponse.CANCEL_ALL_ORDERS));
		orderMassCancelReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		orderMassCancelReport.set(new MassCancelRequestType(MassCancelRequestType.CANCEL_ALL_ORDERS));
		orderMassCancelReport.set(new OrderID("NONE"));
		List cancelledOrders = (List) response.getParameterValue("cancelledOrders");
		List cancellationFailedOrders = (List) response.getParameterValue("cancellationFailedOrders");
		if ( cancelledOrders == null && cancellationFailedOrders == null )
		{
			orderMassCancelReport.set(new NoAffectedOrders(0));
			orderMassCancelReport.set(new TotalAffectedOrders(0));
		}
		else
		{
			orderMassCancelReport.set(new NoAffectedOrders(cancelledOrders.size()));
			orderMassCancelReport.set(new TotalAffectedOrders(cancelledOrders.size()));
			for ( Object object : cancelledOrders )
			{
				com.integral.fix.client.message.grp.OMCRNoAffectedOrders order = orderMassCancelReport.createNoAffectedOrders();
				order.set(new OrigClOrdID((String) object));
				orderMassCancelReport.addGroup(order);
			}
		}
		setHeader(orderMassCancelRequest, orderMassCancelReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderMassCancelRequest, orderMassCancelReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderMassCancelRequest, orderMassCancelReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderMassCancelRequest, orderMassCancelReport, SenderSubID.FIELD, TargetSubID.FIELD);
		return orderMassCancelReport.getMessage();
	}

	public NewOrderSingle getNewOrderSingle( OrderCancelReplaceRequest orderCancelReplaceRequest ) throws FieldNotFound
	{

		NewOrderSingle newOrderSingle = new NewOrderSingle43();		
		newOrderSingle.setCurrencyPairDetails(orderCancelReplaceRequest.getCurrencyPairDetails());
		newOrderSingle.set(orderCancelReplaceRequest.getClOrdID());
		newOrderSingle.set(orderCancelReplaceRequest.getSymbol());
		if ( orderCancelReplaceRequest.isSetMinQty() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getMinQty());
		}
		if ( orderCancelReplaceRequest.isSetMaxShow() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getMaxShow());
		}
		if ( orderCancelReplaceRequest.isSetTimeInForce() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getTimeInForce());
		}
		if ( orderCancelReplaceRequest.isSetExpireDate() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getExpireDate());
		}
		if ( orderCancelReplaceRequest.isSetExpireTime() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getExpireTime());
		}
		if ( orderCancelReplaceRequest.isSetPrice() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getPrice());
		}
		if ( orderCancelReplaceRequest.isSetStopPx() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getStopPx());
		}
		//todo add support for parties
		//newOrderSingle.set(orderCancelReplaceRequest.getParties());
		newOrderSingle.set(orderCancelReplaceRequest.getSide());
		newOrderSingle.set(orderCancelReplaceRequest.getOrderQty());
		newOrderSingle.set(orderCancelReplaceRequest.getTransactTime());
		newOrderSingle.set(orderCancelReplaceRequest.getCurrency());
		newOrderSingle.set(orderCancelReplaceRequest.getHandlInst());
		newOrderSingle.set(orderCancelReplaceRequest.getProduct());
		newOrderSingle.set(orderCancelReplaceRequest.getOrdType());
		if ( orderCancelReplaceRequest.getHeader().isSetField(OnBehalfOfCompID.FIELD) )
		{
			newOrderSingle.getHeader().setField(new OnBehalfOfCompID(orderCancelReplaceRequest.getHeader().getString(OnBehalfOfCompID.FIELD)));
		}
		if ( orderCancelReplaceRequest.getHeader().isSetField(SenderSubID.FIELD) )
		{
			newOrderSingle.getHeader().setField(new SenderSubID(orderCancelReplaceRequest.getHeader().getString(SenderSubID.FIELD)));
		}

		if ( orderCancelReplaceRequest.getHeader().isSetField(OnBehalfOfSubID.FIELD) )
		{
			newOrderSingle.getHeader().setField(new OnBehalfOfSubID(orderCancelReplaceRequest.getHeader().getString(OnBehalfOfSubID.FIELD)));
		}

		if ( orderCancelReplaceRequest.getHeader().isSetField(DeliverToCompID.FIELD) )
		{
			newOrderSingle.getHeader().setField(new DeliverToCompID(orderCancelReplaceRequest.getHeader().getString(DeliverToCompID.FIELD)));
		}

		if ( orderCancelReplaceRequest.getHeader().isSetField(PossDupFlag.FIELD) )
		{
			newOrderSingle.getHeader().setField(new PossDupFlag(orderCancelReplaceRequest.getHeader().getBoolean(PossDupFlag.FIELD)));
		}

		if ( orderCancelReplaceRequest.getHeader().isSetField(PossResend.FIELD) )
		{
			newOrderSingle.getHeader().setField(new PossResend(orderCancelReplaceRequest.getHeader().getBoolean(PossResend.FIELD)));
		}
		if ( orderCancelReplaceRequest.getHeader().isSetField(SendingTime.FIELD) )
		{
			newOrderSingle.getHeader().setField(orderCancelReplaceRequest.getHeader().getField(new SendingTime()));
		}
		if ( orderCancelReplaceRequest.isSetAccount() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getAccount());
		}
		if ( orderCancelReplaceRequest.isSetExecInst() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getExecInst());
		}
		if ( orderCancelReplaceRequest.isSetPegDifference() )
		{
			newOrderSingle.set(orderCancelReplaceRequest.getPegDifference());
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_PEGGED_PRICE) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_PEGGED_PRICE, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_PEGGED_PRICE));
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_USER_DEFINED_SPREAD) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_USER_DEFINED_SPREAD, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_USER_DEFINED_SPREAD));
		}
		if ( orderCancelReplaceRequest.isSetOrigClOrdID() )
		{
			newOrderSingle.setString(OrigClOrdID.FIELD, orderCancelReplaceRequest.getOrigClOrdID().getValue());
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_ADDL_EXEC_INSTR) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_ADDL_EXEC_INSTR, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_ADDL_EXEC_INSTR));
		}
		//Strategy parameters
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_STRATEGY_ST_ABS) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_STRATEGY_ST_ABS, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_STRATEGY_ST_ABS));
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_STRATEGY_ST_PERIOD) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_STRATEGY_ST_PERIOD, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_STRATEGY_ST_PERIOD));
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_STRATEGY_ET_ABS) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_STRATEGY_ET_ABS, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_STRATEGY_ET_ABS));
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_STRATEGY_ET_PERIOD) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_STRATEGY_ET_PERIOD, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_STRATEGY_ET_PERIOD));
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_STRATEGY) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_STRATEGY, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_STRATEGY));
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_STRATEGY_NAME) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_STRATEGY_NAME, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_STRATEGY_NAME));
		}
		if ( orderCancelReplaceRequest.isSetField(ClOrdLinkID.FIELD) )
		{
			newOrderSingle.setString(ClOrdLinkID.FIELD, orderCancelReplaceRequest.getString(ClOrdLinkID.FIELD));
		}
		if ( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_CONTINGENCY_TYPE) )
		{
			newOrderSingle.setString(FixConstants.FIX_FIELD_CONTINGENCY_TYPE, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_CONTINGENCY_TYPE));
		}
        if( orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_ORDER_NOTES))
        {
            newOrderSingle.setString(FixConstants.FIX_FIELD_ORDER_NOTES, orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_ORDER_NOTES));
        }
        if( orderCancelReplaceRequest.isSetField(FixConstants.DIRECTED_ORDER_PARAMETERS_FIELD))
        {
            newOrderSingle.setString(FixConstants.DIRECTED_ORDER_PARAMETERS_FIELD, orderCancelReplaceRequest.getString(FixConstants.DIRECTED_ORDER_PARAMETERS_FIELD));
        }
        if( orderCancelReplaceRequest.isSetTargetStrategy() ){
            newOrderSingle.setTargetStrategy( orderCancelReplaceRequest.getTargetStrategy() );
        }
        if( orderCancelReplaceRequest.isSetActionOnExpiry() ){
            newOrderSingle.setActionOnExpiry( orderCancelReplaceRequest.getActionOnExpiry() );
        }
        if( orderCancelReplaceRequest.isSetExpireMilliseconds() ){
            newOrderSingle.setExpireMilliseconds( orderCancelReplaceRequest.getExpireMilliseconds() );
        }
        if( orderCancelReplaceRequest.isSetFixingSource() ){
            newOrderSingle.setFixingSource( orderCancelReplaceRequest.getFixingSource() );
        }
        if( orderCancelReplaceRequest.isSetMinClipSize() ){
            newOrderSingle.setMinClipSize( orderCancelReplaceRequest.getMinClipSize() );
        }
        if( orderCancelReplaceRequest.isSetMaxClipSize() ){
            newOrderSingle.setMaxClipSize( orderCancelReplaceRequest.getMaxClipSize() );
        }
        if (orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_Settlement_Type))
        {        
        	char settlementType = orderCancelReplaceRequest.getChar(FixConstants.FIX_FIELD_Settlement_Type);
        	newOrderSingle.getMessage().setChar(FixConstants.FIX_FIELD_Settlement_Type, settlementType);
        }
        if (orderCancelReplaceRequest.isSetField(FixConstants.FIX_FIELD_Tenor))
        {
        	String tenor = orderCancelReplaceRequest.getString(FixConstants.FIX_FIELD_Tenor);
        	newOrderSingle.getMessage().setString(FixConstants.FIX_FIELD_Tenor, tenor);
        }
		if( orderCancelReplaceRequest.getExDestination() != null ){
			newOrderSingle.setField(new ExDestination(orderCancelReplaceRequest.getExDestination().getValue()));
		}
		return newOrderSingle;
	}

	public Message getQuoteRequestReject( QuoteRequest quoteRequest, String errorCode )
	{
		QuoteRequestReject quoteRequestReject = new QuoteRequestReject43();
		try
		{
			quoteRequestReject.set(new QuoteReqID(quoteRequest.getQuoteReqID().getValue()));
		}
		catch ( FieldNotFound fieldNotFound )
		{
			log.error("FixTranslator43Impl.getQuoteRequestReject: QuoteReqID field not Found");
		}
		quoteRequestReject.set(fixUtil.getQuoteRequestRejectReason(errorCode));
		com.integral.fix.client.message.grp.NoRelatedSym noRelatedSym = quoteRequestReject.createNoRelatedSym();
		noRelatedSym.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		Symbol ccypair = getSymbol(quoteRequest);
		if ( ccypair != null )
		{
			noRelatedSym.set(ccypair);
		}
		quoteRequestReject.addGroup(noRelatedSym);
		quoteRequestReject.set(new Text(errorCode));
		setHeader(quoteRequest, quoteRequestReject, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(quoteRequest, quoteRequestReject, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(quoteRequest, quoteRequestReject, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(quoteRequest, quoteRequestReject, SenderSubID.FIELD, TargetSubID.FIELD);
		return quoteRequestReject.getMessage();
	}

	public Message getQuoteRequestReject( String quoteReqId, String errorCode, String senderSubID, String onBehalfOfCompID, String onBehalfOfSubID, String deliverToSubID, String ccyPair )
	{
		QuoteRequestReject quoteRequestReject = new QuoteRequestReject43();
		quoteRequestReject.set(new QuoteReqID(quoteReqId));
		quoteRequestReject.set(fixUtil.getQuoteRequestRejectReason(errorCode));
		com.integral.fix.client.message.grp.NoRelatedSym noRelatedSym = quoteRequestReject.createNoRelatedSym();
		noRelatedSym.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		noRelatedSym.set(new Symbol(ccyPair));
		quoteRequestReject.addGroup(noRelatedSym);
		quoteRequestReject.set(new Text(errorCode));
		if ( onBehalfOfCompID != null && !onBehalfOfCompID.trim().equals("") )
		{
			quoteRequestReject.getHeader().setField(new DeliverToCompID(onBehalfOfCompID));
		}
		if ( onBehalfOfSubID != null && !onBehalfOfSubID.trim().equals("") )
		{
			quoteRequestReject.getHeader().setField(new DeliverToSubID(onBehalfOfSubID));
		}
		if ( senderSubID != null && !senderSubID.trim().equals("") )
		{
			quoteRequestReject.getHeader().setField(new TargetSubID(senderSubID));
		}
		if ( deliverToSubID != null && !deliverToSubID.trim().equals("") )
		{
			quoteRequestReject.getHeader().setField(new OnBehalfOfCompID(deliverToSubID));
		}
		return quoteRequestReject.getMessage();
	}

	//Used only for order request.
	public Message getExecutionReport( OrderStatusRequest orderStatusRequest, Request request ) throws FieldNotFound
	{
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set(fixUtil.getOrdStatus(request));
        String requestClassfication = request.getRequestClassification().getShortName();
        RequestStateFacadeC rsf = ( RequestStateFacadeC ) request.getFacade( RequestStateFacade.REQUEST_STATE_FACADE );
        FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
        double avgPx = dp.getAverageRate() == null ? 0.0 : dp.getAverageRate();
        double orderAmount = dp.getDealtAmount();
		double filledAmount = dp.getFilledAmount() != null ? dp.getFilledAmount() : 0.0d;
        if(request.isNettingEnabled() && rsf.isPartial()) {
            Double nettedAmount = ServiceFactory.getNettingService().getNettedAmount(request.getOrderId());
            if(nettedAmount != null && nettedAmount <= filledAmount) {
                filledAmount = filledAmount - nettedAmount;
                if(filledAmount == 0) {
                    avgPx = 0.0;
                    executionReport.set(FixConstants.ORDER_STATUS_NEW);
                }
            }
        }
		double leavesAmount = orderAmount - filledAmount;

		boolean isBid = dp.getBidOfferMode() == FXLegDealingPrice.BID;
		Side side = null;

		if ( isBid )
		{
			if ( FXLegDealingPrice.CCY1.equals(dp.getDealtCurrencyProperty()) )
			{
				side = FixConstants.BUY;
			}
			else
			{
				side = FixConstants.SELL;
			}
		}
		else
		{
			if ( FXLegDealingPrice.CCY1.equals(dp.getDealtCurrencyProperty()) )
			{
				side = FixConstants.SELL;
			}
			else
			{
				side = FixConstants.BUY;
			}
		}

		com.integral.finance.currency.Currency dealtCcyObj = dp.getDealtCurrency();
		String dealtCcy = dealtCcyObj.getRealCurrency().getShortName();
		String settlCcy = dp.getSettledCurrency().getRealCurrency().getShortName();
		String ccyPair = null;
		if ( FXLegDealingPrice.CCY1.equalsIgnoreCase(dp.getDealtCurrencyProperty()) )
		{
			ccyPair = dealtCcy + '/' + settlCcy;
		}
		else
		{
			ccyPair = settlCcy + '/' + dealtCcy;
		}

		String extReqId = request.getExternalRequestId();
		if ( extReqId == null )
		{
			extReqId = "NONE";
		}
		executionReport.set(new ClOrdID(extReqId));
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(side);
        executionReport.set(new AvgPx(avgPx));
		executionReport.set(new Symbol(ccyPair));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, request.getCreatedDate(), true);
		populateValueDate(request, executionReport);
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new Currency(dealtCcy));

		FXPrice fxPrice = ((FXDealingPriceElement) dp.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		if ( FixConstants.LIMIT.equals(requestClassfication) )
		{
			executionReport.set(new Price(fxRate.getSpotRate()));
			executionReport.set(new OrdType(OrdType.LIMIT));
		}
		else if ( FixConstants.MARKET.equals(requestClassfication) )
		{
			executionReport.set(new OrdType(OrdType.MARKET));
			Double pegDiff = dp.getMarketRange();
			if ( pegDiff != null && pegDiff != -1 )
			{
				executionReport.set(new Price(fxRate.getSpotRate()));
				executionReport.set(new PegDifference(Double.valueOf(pegDiff)));
			}
		}
		else if ( FixConstants.STOP.equals(requestClassfication) )
		{
			executionReport.set(new OrdType(OrdType.STOP));
			executionReport.set(new StopPx(dp.getStopPrice()));
			Double pegDiff = dp.getMarketRange();
			if ( pegDiff != null && pegDiff != -1 )
			{
				executionReport.set(new Price(fxRate.getSpotRate()));
				executionReport.set(new PegDifference(Double.valueOf(pegDiff)));
			}
		}
		else if ( FixConstants.STOPLIMIT.equals(requestClassfication) )
		{
			executionReport.set(new Price(fxRate.getSpotRate()));
			executionReport.set(new OrdType(OrdType.STOP_LIMIT));
			executionReport.set(new StopPx(dp.getStopPrice()));
		}
		quickfix.field.TimeInForce tif = fixUtil.getTimeInForce(request);
		if ( tif != null )
		{
			executionReport.set(tif);
		}
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(new CumQty(filledAmount));
		executionReport.set(new LeavesQty(dealtCcyObj.round(leavesAmount)));
		executionReport.set(new LastPx(0));
		executionReport.set(new ExecID("0"));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		if ( orderStatusRequest.isSetClOrdID() )
		{
			executionReport.setString(790, orderStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
		}
		else
		{
			executionReport.setString(790, orderStatusRequest.getOrderID().getValue());//orderstatusreqiid.
		}
		setHeader(orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

		if ( request.getOrderCancelReason() != null && !request.getOrderCancelReason().equals("") )
		{
			executionReport.setString(Text.FIELD, request.getOrderCancelReason());
		}

		return executionReport.getMessage();
	}

	public Message getQuoteCancelReject( QuoteCancel quoteCancel, String errorCode ) throws FieldNotFound
	{
		BusinessMessageReject quoteCancRej = new BusinessMessageReject43();
		quoteCancRej.set(new RefMsgType("Z"));
		quoteCancRej.set(new BusinessRejectRefID(quoteCancel.getQuoteReqID().getValue()));
		quoteCancRej.set(new BusinessRejectReason(BusinessRejectReason.UNKOWN_ID));
		quoteCancRej.set(new Text(errorCode));
		return quoteCancRej.getMessage();
	}

	public Message getTradeCaptureReportRequestReject( TradeCaptureReportRequest message, String errorCode ) throws FieldNotFound
	{
		BusinessMessageReject rejectMessage = new BusinessMessageReject43();
		if ( message.isSetTradeRequestID() )
		{
			rejectMessage.set(new BusinessRejectRefID(message.getTradeRequestID().getValue()));
		}
		rejectMessage.set(new RefMsgType(TradeCaptureReportRequest.MSGTYPE));
		if ( "UnknownID".equalsIgnoreCase(errorCode) )
		{
			rejectMessage.set(new BusinessRejectReason(BusinessRejectReason.UNKOWN_ID));
		}
		else
		{
			rejectMessage.set(new BusinessRejectReason(BusinessRejectReason.OTHER));
		}
		rejectMessage.set(new Text(errorCode));
		return rejectMessage.getMessage();
	}

	//Mass status response to ESP Trade.
	public Message getOrderMassStatusExecutionReport( Request request, OrderMassStatusRequest orderMassStatusRequest ) throws FieldNotFound
	{
		Trade trade = request.getTrade();
		RequestPriceFacade rpf = (RequestPriceFacade) request.getRequestPrice(FixConstants.SINGLE_LEG).getFacade(RequestPriceFacade.FACADE_NAME);
		RequestStateFacade rsf = (RequestStateFacade) request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE);

		FXPaymentParameters fxPayment = ((FXSingleLeg) trade).getFXLeg().getFXPayment();
		double orderAmount = rpf.getFilledAmount() + rpf.getUnfilledAmount();
		double executedAmount = rpf.getVerifiedAmount();
		com.integral.finance.currency.Currency dealtCurrencyObj = fxPayment.isDealtCurrency1() ? fxPayment.getCurrency1() : fxPayment.getCurrency2();
		String dealtCurrency = dealtCurrencyObj.getShortName();
		double lastDealtAmount = fxPayment.isDealtCurrency1() ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount();
		char side = dealtCurrencyObj.isSameAs(fxPayment.getCurrency1()) ? (fxPayment.isBuyingCurrency1() ? Side.BUY : Side.SELL) : (fxPayment.isBuyingCurrency1() ? Side.SELL : Side.BUY);
		double leavesQty = dealtCurrencyObj.round((orderAmount - executedAmount));

		boolean isPartial = (leavesQty > 0);
		String externalReqID = request.getExternalRequestId();
		if ( externalReqID == null )
		{
			externalReqID = "NONE";
		}
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(new ClOrdID(externalReqID));
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new Side(side));
		executionReport.set(new Price(fxPayment.getFXRate().getSpotRate()));
		executionReport.set(new Symbol(fixUtil.getCurrencyPair(request).getName()));
		executionReport.set(FixConstants.ORD_TYPE_PREVIOUSLY_QUOTED);
		executionReport.set(FixConstants.IOC);

		executionReport.setUtcTimeStamp(TransactTime.FIELD, trade.getCreatedDate(), true);
		if ( rsf.isAcceptVerified() || rsf.isPartial() )
		{
			if ( isPartial )
			{
				executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
			}
			else
			{
				executionReport.set(FixConstants.ORDER_STATUS_FILLED);
			}
			executionReport.set(new LastQty(lastDealtAmount));
			executionReport.set(new AvgPx(rpf.getAverageFillPrice()));
			executionReport.set(new CumQty(executedAmount));
			executionReport.set(new LeavesQty(leavesQty));
			executionReport.set(new FutSettDate(fxPayment.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		}
		else if ( rsf.isDeclined() )
		{
			executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
			executionReport.set(FixConstants.AVG_PX_REJECT);
			executionReport.set(FixConstants.CUM_QTY_ZERO);
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(new Text(trade.getWorkflowStateMap().getWorkflowCodeArgument()));
		}
		executionReport.set(new Currency(dealtCurrency));
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new ExecID("0"));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.setString(584, orderMassStatusRequest.getMassStatusReqID().getValue());//ordermassstatusreqiid.

		if ( fxPayment.getFixingDate() != null )
		{
			executionReport.set(new MaturityDate(fxPayment.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		}

		executionReport.getHeader().setString(OnBehalfOfCompID.FIELD, trade.getCounterpartyB().getOrganization().getShortName());
		//setHeader( orderMassStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
		setHeader(orderMassStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		setHeader(executionReport, SenderSubID.FIELD, trade.getCounterpartyB().getShortName());
		return executionReport.getMessage();
	}

	//Mass status response to RFS Trade.
	public Message getRFSOrderMassStatusExecutionReport( Request request, OrderMassStatusRequest orderMassStatusRequest ) throws FieldNotFound
	{
		ExecutionReport executionReport = new ExecutionReport43();
		Trade trade = request.getTrade();

		FXPaymentParameters nearLeg = getFXPayment(trade, true);
		FXPaymentParameters farLeg = getFXPayment(trade, false);

		com.integral.finance.currency.Currency settledCcyFarLeg = farLeg.isDealtCurrency1() ? farLeg.getCurrency2() : farLeg.getCurrency1();
		double settlCurrAmtFarLeg = settledCcyFarLeg.isSameAs(farLeg.getCurrency1()) ? farLeg.getCurrency1Amount() : farLeg.getCurrency2Amount();

		com.integral.finance.currency.Currency settledCcyNearLeg = nearLeg.isDealtCurrency1() ? nearLeg.getCurrency2() : nearLeg.getCurrency1();
		double settlCurrAmtNearLeg = settledCcyNearLeg.isSameAs(nearLeg.getCurrency1()) ? nearLeg.getCurrency1Amount() : nearLeg.getCurrency2Amount();

		com.integral.finance.currency.Currency dealtCcy = nearLeg.isDealtCurrency1() ? nearLeg.getCurrency1() : nearLeg.getCurrency2();
		double ordQty = nearLeg.isDealtCurrency1() ? nearLeg.getCurrency1Amount() : nearLeg.getCurrency2Amount();
		double lastPx = nearLeg.getFXRate().getRate();
		double orderQty2 = farLeg.isDealtCurrency1() ? farLeg.getCurrency1Amount() : farLeg.getCurrency2Amount();

		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.ORD_TYPE_PREVIOUSLY_QUOTED);
		executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.FILL_OR_KILL));
		executionReport.set(new ClOrdID(request.getExternalRequestId()));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID(("0")));
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, trade.getCreatedDate(), true);
		executionReport.set(new Currency(dealtCcy.getShortName()));
		executionReport.set(new OrderQty(ordQty));
		executionReport.set(new Price(lastPx));
		executionReport.set(new Side(dealtCcy.isSameAs(farLeg.getCurrency1()) ? (farLeg.isBuyingCurrency1() ? Side.BUY : Side.SELL) : (farLeg.isBuyingCurrency1() ? Side.SELL : Side.BUY)));

		if ( nearLeg != farLeg )
		{
			executionReport.setField(new Price2(farLeg.getFXRate().getRate()));
			executionReport.set(new OrderQty2(orderQty2));
		}

		RequestStateFacade facade = ((RequestStateFacade) request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE));

		if ( facade.isDeclined() )
		{
			String rejectReason = trade.getWorkflowStateMap().getWorkflowCodeArgument();
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
			executionReport.set(FixConstants.CUM_QTY_ZERO);
			executionReport.set(FixConstants.AVG_PX_REJECT);
			executionReport.set(new LastSpotRate(0));
			executionReport.set(new LastForwardPoints(0));
			executionReport.set(new LastPx(0));
			executionReport.set(new SettlCurrAmt(0.0));
			executionReport.set(new SettlCurrency(settledCcyNearLeg.getShortName()));
			FixUtilC.getInstance().setRejectReason(executionReport, rejectReason);//sets error code and error reason
			if ( nearLeg != farLeg )
			{
				executionReport.set(new SettlCurrency(settledCcyFarLeg.getShortName()));//for swap it should be far legs
				executionReport.setField(new DoubleField(641, 0));//LastForwardPoints2
				executionReport.setField(new DoubleField(7541, 0));//lastPx2
				executionReport.setField(new DoubleField(7543, 0));//leavesQty2
				executionReport.setField(new DoubleField(7544, 0));//CumQty2
				executionReport.setField(new DoubleField(7545, 0.0));//SettlCurrAmt
			}
		}
		else
		{
			//Set other quantities and prices
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(new CumQty(ordQty));
			executionReport.set(new LastQty(ordQty));
			executionReport.set(new LastSpotRate(nearLeg.getFXRate().getSpotRate()));
			executionReport.set(new LastForwardPoints(nearLeg.getFXRate().getForwardPoints()));
			executionReport.set(new LastPx(lastPx));
			executionReport.set(new AvgPx(lastPx));
			executionReport.set(new SettlCurrAmt(settlCurrAmtNearLeg));
			executionReport.set(new SettlCurrency(settledCcyNearLeg.getShortName()));
			executionReport.set(new FutSettDate(nearLeg.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));

			if ( nearLeg != farLeg )
			{
				executionReport.set(new LastQty(orderQty2));//for swap it should be far legs
				executionReport.set(new SettlCurrency(settledCcyFarLeg.getShortName()));//for swap it should be far legs
				executionReport.setField(new DoubleField(7545, settlCurrAmtFarLeg));//SettlCurrAmt
				executionReport.setField(new DoubleField(641, farLeg.getFXRate().getForwardPoints()));//LastForwardPoints2
				executionReport.setField(new DoubleField(7541, farLeg.getFXRate().getRate()));//lastPx2
				executionReport.setField(new DoubleField(7543, 0));//leavesQty2
				executionReport.setField(new DoubleField(7544, orderQty2));//CumQty2
				executionReport.set(new FutSettDate2(farLeg.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
			}
			if ( facade.isPartial() )
			{
				executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
			}
			else if ( facade.isAcceptVerified() )
			{
				executionReport.set(FixConstants.ORDER_STATUS_FILLED);
			}
		}
		executionReport.setString(584, orderMassStatusRequest.getMassStatusReqID().getValue());//ordermassstatusreqiid.

		if ( nearLeg.getFixingDate() != null )
		{
			executionReport.set(new MaturityDate(nearLeg.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		}

		executionReport.getHeader().setString(OnBehalfOfCompID.FIELD, trade.getCounterpartyB().getOrganization().getShortName());
		setHeader(orderMassStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		setHeader(executionReport, SenderSubID.FIELD, trade.getCounterpartyB().getShortName());
		return executionReport.getMessage();

	}

	public Message getLimitOrderStatusExecutionReport( WorkflowMessage wfMsg, NewOrderSingle newOrderSingle, Request request, OrderStatusRequest orderStatusRequest ) throws FieldNotFound
	{
		FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		com.integral.finance.currency.Currency dealtCurrency = dp.getDealtCurrency();
		RequestPriceFacade rpf = (RequestPriceFacade) dp.getFacade(RequestPriceFacade.FACADE_NAME);
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(newOrderSingle.getClOrdID());
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(fixUtil.getOrdStatus(request));
		Order order = null;
		String orderId = request.getOrderId();
		if ( orderId != null )
		{
			executionReport.set(new OrderID(request.getOrderId()));
		}
		else
		{
			executionReport.set(new OrderID("NONE"));
		}

		double orderAmount = dp.getDealtAmount();
		double filledAmount = rpf.getFilledAmount();
		double leavesAmount = rpf.getUnfilledAmount();
		double avgFillPrice = 0.0;

		if ( wfMsg != null )
		{
			avgFillPrice = (Double) wfMsg.getParameterValue(FixConstants.AVG_FILL_PRICE);
			leavesAmount = (Double) wfMsg.getParameterValue(FixConstants.UNFILLED_AMT);
			filledAmount = (Double) wfMsg.getParameterValue(FixConstants.FILLED_AMT);
			log.warn("FixTranslator43impl.getLimitOrderStatusExecutionReport - OrderId -" + orderId + " AvgFillPrice-" + avgFillPrice + " UnFilled Amount - " + leavesAmount + " FilledAmount-" + filledAmount);
			if ( leavesAmount == 0 )
			{
				executionReport.set(FixConstants.ORDER_STATUS_FILLED);
			}
			else if ( filledAmount > 0 )
			{
				executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
			}
			else if ( request.getRequestAttributes().isExexcutionSuspended() )
			{
				executionReport.set(FixConstants.ORDER_STATUS_STOPPED);
			}
			else
			{
				executionReport.set(FixConstants.ORDER_STATUS_NEW);
			}

		}
		else
		{
			if ( orderId != null )
			{
				order = FIXClientQueryService.findOrder(orderId);
				if ( order != null )
				{
					orderAmount = order.getAmount();
					filledAmount = order.getFilledAmount();
					leavesAmount = dealtCurrency.round(orderAmount - filledAmount);
					avgFillPrice = order.getAverageRate();
				}
			}
		}
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(newOrderSingle.getSide());
		executionReport.set(newOrderSingle.getCurrency());
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(newOrderSingle.getSymbol());
		executionReport.set(newOrderSingle.getOrdType());
		executionReport.set(new ExecID("0"));
		Date effectiveDate = request.getEffectiveDate();
		if ( effectiveDate == null )
		{
			effectiveDate = request.getCreatedDate();
		}

		executionReport.set(new FutSettDate(DateTimeFactory.newDate(effectiveDate).getFormattedDate(IdcDate.YYYY_MM_DD)));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, request.getCreatedDate(), true);

		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		if ( newOrderSingle.isSetPrice() )
		{
			if ( request.getPegType() != Character.MIN_VALUE )
			{
				String curPegRate = (String) wfMsg.getParameterValue(ISCommonConstants.WF_PARAM_CUR_PEG_RATE);
				if ( curPegRate != null )
				{
					executionReport.setString(Price.FIELD, curPegRate);
				}
				else
				{
					executionReport.set(newOrderSingle.getPrice());
				}
			}
			else
			{
				executionReport.set(newOrderSingle.getPrice());
			}
		}
		if ( newOrderSingle.isSetTimeInForce() )
		{
			executionReport.set(newOrderSingle.getTimeInForce());
		}
		if ( newOrderSingle.isSetExpireTime() )
		{
			executionReport.set(newOrderSingle.getExpireTime());
		}
		if ( newOrderSingle.isSetMaxShow() )
		{
			executionReport.set(newOrderSingle.getMaxShow());
		}
		if ( newOrderSingle.isSetMinQty() )
		{
			executionReport.set(newOrderSingle.getMinQty());
		}
		if ( newOrderSingle.isSetPegDifference() )
		{
			executionReport.set(newOrderSingle.getPegDifference());
		}
		if ( newOrderSingle.isSetExecInst() )
		{
			executionReport.set(newOrderSingle.getExecInst());
		}
		if ( newOrderSingle.isSetStopPx() )
		{
			executionReport.set(newOrderSingle.getStopPx());
		}
		executionReport.set(new LeavesQty(leavesAmount));
		executionReport.set(new CumQty(filledAmount));
		executionReport.set(new AvgPx(avgFillPrice));

		if ( orderStatusRequest.isSetClOrdID() )
		{
			executionReport.setString(790, orderStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
		}
		else
		{
			executionReport.setString(790, orderStatusRequest.getOrderID().getValue());//orderstatusreqiid.
		}
		setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

		if ( request.getOrderCancelReason() != null && !request.getOrderCancelReason().equals("") )
		{
			executionReport.setString(Text.FIELD, request.getOrderCancelReason());
		}

		return executionReport.getMessage();
	}

	public Message getLimitOrderMassStatusExecutionReport( Request request, OrderMassStatusRequest orderMassStatusRequest, boolean queryOrder ) throws FieldNotFound
	{
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set(fixUtil.getOrdStatus(request));

        FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		com.integral.finance.currency.Currency dealtCurrency = dp.getDealtCurrency();
		Side side = null;
		if ( (FXLegDealingPrice.CCY1.equals(dp.getDealtCurrencyProperty()) && dp.getBidOfferMode() == FXLegDealingPrice.BID) || (FXLegDealingPrice.CCY2.equals(dp.getDealtCurrencyProperty()) && dp.getBidOfferMode() == FXLegDealingPrice.OFFER) )
		{
			side = FixConstants.BUY;
		}
		else
		{
			side = FixConstants.SELL;
		}

		double price = dp.getFXRate().getRate();
		double avgFillPrice = dp.getAverageRate() != null ? dp.getAverageRate() : 0.0;
		String dealtCcy = dealtCurrency.getRealCurrency().getShortName();
		String settlCcy = dp.getSettledCurrency().getRealCurrency().getShortName();
		String ccyPair = null;
		if ( FXLegDealingPrice.CCY1.equalsIgnoreCase(dp.getDealtCurrencyProperty()) )
		{
			ccyPair = dealtCcy + '/' + settlCcy;
		}
		else
		{
			ccyPair = settlCcy + '/' + dealtCcy;
		}
		RequestPriceFacade rpf = (RequestPriceFacade) dp.getFacade(RequestPriceFacade.FACADE_NAME);
		double orderAmount = dp.getDealtAmount();
		double filledAmount = rpf.getFilledAmount();
		double leavesAmount = rpf.getUnfilledAmount();
		if ( queryOrder )
		{
			Order order = FIXClientQueryService.findOrder(request.getOrderId());
			if ( order != null )
			{
				orderAmount = order.getAmount();
				filledAmount = order.getFilledAmount();
				leavesAmount = dealtCurrency.round(orderAmount - filledAmount);
				avgFillPrice = order.getAverageRate();
			}
		}
        RequestStateFacade rsf = (RequestStateFacade)request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE);
        if(request.isNettingEnabled() && rsf.isPartial()) {
            Double nettedAmount = ServiceFactory.getNettingService().getNettedAmount(request.getOrderId());
            if(nettedAmount != null && nettedAmount <= filledAmount) {
                filledAmount = filledAmount - nettedAmount;
                if(filledAmount == 0) {
                    avgFillPrice = 0.0;
                    executionReport.set(FixConstants.ORDER_STATUS_NEW);
                }
            }
        }

		if ( request.getExternalRequestId() != null && !request.getExternalRequestId().equals("") )
		{
			executionReport.set(new ClOrdID(request.getExternalRequestId()));
		}
		else
		{
			executionReport.set(new ClOrdID(request.getOrderId()));
		}

		executionReport.set(FixConstants.PRODUCT);
        executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(side);
		executionReport.setUtcTimeStamp(TransactTime.FIELD, request.getCreatedDate(), true);
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new Currency(dealtCcy));
		executionReport.set(new Symbol(ccyPair));
		executionReport.set(new AvgPx(avgFillPrice));

		String requestClassification = request.getRequestClassification().getShortName();
		if ( FixConstants.LIMIT.equalsIgnoreCase(requestClassification) )
		{
			executionReport.set(new OrdType(OrdType.LIMIT));
			executionReport.set(new Price(price));
		}
		else if ( FixConstants.MARKET.equalsIgnoreCase(requestClassification) )
		{
			executionReport.set(new OrdType(OrdType.MARKET));
			if ( dp.getMarketRange() != null && dp.getMarketRange() != -1 )
			{
				executionReport.set(new Price(price));
				double marketRange = dp.getMarketRange() * dp.getFXRate().getFXRateBasis().getPipsFactor();
				executionReport.set(new PegDifference(marketRange));
			}
		}
		else if ( FixConstants.STOPLIMIT.equalsIgnoreCase(requestClassification) )
		{
			executionReport.set(new OrdType(OrdType.STOP_LIMIT));
			executionReport.set(new Price(price));
			executionReport.set(new StopPx(dp.getStopPrice()));
		}
		else if ( FixConstants.STOP.equalsIgnoreCase(requestClassification) )
		{
			executionReport.set(new OrdType(OrdType.STOP));
			if ( dp.getMarketRange() != null && dp.getMarketRange() != -1 )
			{
				executionReport.set(new Price(price));
				double marketRange = dp.getMarketRange() * dp.getFXRate().getFXRateBasis().getPipsFactor();
				executionReport.set(new PegDifference(marketRange));
			}
		}
		quickfix.field.TimeInForce tif = fixUtil.getTimeInForce(request);
		if ( tif != null )
		{
			executionReport.set(tif);
		}
		executionReport.setString(584, orderMassStatusRequest.getMassStatusReqID().getValue());//ordermassstatusreqiid.

		Date effectiveDate = request.getEffectiveDate();
		if ( effectiveDate == null )
		{
			effectiveDate = request.getCreatedDate();
		}

		if ( filledAmount > 0.0 )
		{
			executionReport.set(new FutSettDate(DateTimeFactory.newDate(effectiveDate).getFormattedDate(IdcDate.YYYY_MM_DD)));
		}

		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		executionReport.set(new CumQty(filledAmount));
		executionReport.set(new LeavesQty(leavesAmount));

		executionReport.set(new LastPx(0));
		executionReport.set(new ExecID("0"));
		executionReport.set(new OrderID(request.getOrderId()));

		setHeader(orderMassStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

		if ( request.getOrderCancelReason() != null && !request.getOrderCancelReason().equals("") )
		{
			executionReport.setString(Text.FIELD, request.getOrderCancelReason());
		}

		return executionReport.getMessage();
	}

	/**
	 * 
	 */
	public Message getLimitOrderMassStatusExecutionReport( WorkflowMessage wfMsg, NewOrderSingle newOrderSingle, Request request, OrderMassStatusRequest orderMassStatusRequest ) throws FieldNotFound
	{
		ExecutionReport executionReport = new ExecutionReport43();
		executionReport.set(newOrderSingle.getClOrdID());
		executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
		String orderId = request.getOrderId();
		if ( orderId != null )
		{
			executionReport.set(new OrderID(request.getOrderId()));
		}
		double filledAmount = (Double) wfMsg.getParameterValue(FixConstants.FILLED_AMT);
		double leavesAmount = (Double) wfMsg.getParameterValue(FixConstants.UNFILLED_AMT);
		double avgFillPrice = (Double) wfMsg.getParameterValue(FixConstants.AVG_FILL_PRICE);
		FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		double orderAmount = dp.getDealtAmount();

		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(newOrderSingle.getSide());
		executionReport.set(newOrderSingle.getCurrency());
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(newOrderSingle.getSymbol());
		executionReport.set(newOrderSingle.getOrdType());
		executionReport.set(new ExecID("0"));
		Date effectiveDate = request.getEffectiveDate();
		if ( effectiveDate == null )
		{
			effectiveDate = request.getCreatedDate();
		}

		executionReport.set(new FutSettDate(DateTimeFactory.newDate(effectiveDate).getFormattedDate(IdcDate.YYYY_MM_DD)));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, request.getCreatedDate(), true);
		if ( leavesAmount == 0 )
		{
			executionReport.set(FixConstants.ORDER_STATUS_FILLED);
		}
		else if ( filledAmount > 0 )
		{
			executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
		}
		else if ( request.getRequestAttributes().isExexcutionSuspended() )
		{
			executionReport.set(FixConstants.ORDER_STATUS_STOPPED);
		}
		else
		{
			executionReport.set(FixConstants.ORDER_STATUS_NEW);
		}

		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		if ( newOrderSingle.isSetPrice() )
		{
			executionReport.set(newOrderSingle.getPrice());
		}
		if ( newOrderSingle.isSetTimeInForce() )
		{
			executionReport.set(newOrderSingle.getTimeInForce());
		}
		if ( newOrderSingle.isSetExpireTime() )
		{
			executionReport.set(newOrderSingle.getExpireTime());
		}
		if ( newOrderSingle.isSetMaxShow() )
		{
			executionReport.set(newOrderSingle.getMaxShow());
		}
		if ( newOrderSingle.isSetMinQty() )
		{
			executionReport.set(newOrderSingle.getMinQty());
		}
		if ( newOrderSingle.isSetPegDifference() )
		{
			executionReport.set(newOrderSingle.getPegDifference());
		}
		if ( newOrderSingle.isSetExecInst() )
		{
			executionReport.set(newOrderSingle.getExecInst());
		}
		if ( newOrderSingle.isSetStopPx() )
		{
			executionReport.set(newOrderSingle.getStopPx());
		}
		executionReport.set(new LeavesQty(leavesAmount));
		executionReport.set(new CumQty(filledAmount));
		executionReport.set(new AvgPx(avgFillPrice));
		executionReport.setString(584, orderMassStatusRequest.getMassStatusReqID().getValue());//ordermassstatusreqiid.

		setHeader(orderMassStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(orderMassStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		if ( request.getOrderCancelReason() != null && !request.getOrderCancelReason().equals("") )
		{
			executionReport.setString(Text.FIELD, request.getOrderCancelReason());
		}
		return executionReport.getMessage();
	}

	public Message getOrderMassStatusReject( OrderMassStatusRequest orderMassStatusRequest, String errorCode ) throws FieldNotFound
	{
		BusinessMessageReject rejectMessage = new BusinessMessageReject43();
		if ( orderMassStatusRequest.isSetMassStatusReqID() )
		{
			rejectMessage.set(new BusinessRejectRefID(orderMassStatusRequest.getMassStatusReqID().getValue()));
		}
		rejectMessage.set(new RefMsgType("H"));
		if ( "UnknownID".equalsIgnoreCase(errorCode) )
		{
			rejectMessage.set(new BusinessRejectReason(BusinessRejectReason.UNKOWN_ID));
		}
		else
		{
			rejectMessage.set(new BusinessRejectReason(BusinessRejectReason.CONDITIONALLY_REQUIRED_FIELD_MISSING));
		}

		rejectMessage.set(new Text(errorCode));
		return rejectMessage.getMessage();
	}

	public OrderCancelReject getOrderCancelReject( Request request, String cancelRequestID, boolean isOrderCancelReplace, SessionID sessionID, OrdStatus status )
	{
		if ( status == null )
		{
			status = fixUtil.getOrdStatus(request);
		}
		return getOrderCancelReject(cancelRequestID, isOrderCancelReplace, status, request.getOrderId(), request.getExternalRequestId());
	}

	protected OrderCancelReject getOrderCancelReject( String cancelRequestID, boolean isOrderCancelReplace, OrdStatus status, String orderId, String externalRequestId )
	{
		OrderCancelReject orderCancelReject = new OrderCancelReject43();
		orderCancelReject.set(new ClOrdID(cancelRequestID));
		orderCancelReject.set(new OrigClOrdID(externalRequestId));
		if ( isOrderCancelReplace )
		{
			orderCancelReject.set(new CxlRejResponseTo(CxlRejResponseTo.ORDER_CANCEL_REPLACE_REQUEST));
		}
		else
		{
			orderCancelReject.set(new CxlRejResponseTo(CxlRejResponseTo.ORDER_CANCEL_REQUEST));
		}
		orderCancelReject.set(new Text(FixConstants.ORDER_CANCEL_TO_LATE_TO_CANCEL));
		orderCancelReject.set(new OrderID("NONE"));
		//orderCancelReject.set(new OrdStatus(OrdStatus.NEW));
		CxlRejReason rejReason = fixUtil.getCxlRejReason(FixConstants.ORDER_CANCEL_TO_LATE_TO_CANCEL);
		if ( rejReason != null )
		{
			orderCancelReject.set(rejReason);
		}
		orderCancelReject.set(new OrderID(orderId));
		orderCancelReject.set(status);
		return orderCancelReject;
	}

	public Message getListStatusMessage( List requests, OrderStatusRequest orderStatusRequest ) throws FieldNotFound
	{
		//this is implemented only for FIX 42 implementation
		return null;
	}

	public WorkflowMessage getGeneratedMessage( Trade trade, TradeCaptureReportRequest message, SessionID sessionID )
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		try
		{
			msg.setStatus(MessageStatus.SUCCESS);
			msg.setEventName(TradeService.GENERATE_DOWNLOAD_XML);
			msg.setTopic(FixConstants.MSG_TOPIC_TRADE);
			User user = clientCache.getUser(sessionID);

			if(user == null)
			{
				msg.addError( FixConstants.INTERNAL_SERVER_ERROR );
				log.info( new StringBuilder( 200 ).append( "T43.getGeneratedMessage : User not logged in for session ").append( sessionID ).toString());
				return msg;
			}

			msg.setSender( user );

			for ( CptyTrade cptyTradee : trade.getCptyTrades() )
			{
				if ( cptyTradee.getNamespace().getShortName().equalsIgnoreCase(msg.getSender().getOrganization().getShortName()) )
				{
					trade.setCptyTrade(cptyTradee);
					break;
				}
			}

            // if a cptyTrade is not found, then reject the message.
            if ( trade.getCptyTrade() == null )
            {
                log.info( new StringBuilder( 200 ).append( "T43.getGeneratedMessage : cptyTrade not found in trade.tid=" )
                        .append( trade.getTransactionID() ).append( ",for org=" ).append( msg.getSender().getOrganization() )
                        .append( ",user=" ).append( msg.getSender() ).toString() );
                msg.addError( FixConstants.INTERNAL_SERVER_ERROR );
            }

            msg.setObject(trade);
			msg.setProperty(FixConstants.WF_PARAM_ORGNAIZATION, msg.getSender().getOrganization());
			if ( message.isSetTradeRequestID() )
			{
				msg.setParameterValue(FixConstants.WF_PARAM_TRADE_REQUEST_ID, message.getTradeRequestID().getValue());
			}
			msg.setParameterValue(TradeServiceConstants.DOWNLOAD_FORMAT_KEY, "FIX");
			msg.setProperty(TradeServiceConstants.RESEND_ORGANIZATION_TO_KEY, msg.getSender().getOrganization());
		}
		catch ( FieldNotFound e )
		{
			log.error("FixTranslator43Impl.getSTPResendRequest:Exception while forming resend request:", e);
			msg.addError(FixConstants.INTERNAL_SERVER_ERROR);
		}
		return msg;
	}

    @Override
    public Message getOrderSubmissionExecutionReport(NewOrderMultileg newOrderMultileg, String portfolioID, String pending, List<Trade> allocatedTrades)
    {
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }

	/* (non-Javadoc)
	 * @see com.integral.fix.client.translator.Translator#getExecutionReport(com.integral.fix.client.message.OrderStatusRequest, com.integral.netting.model.NettingPortfolio)
	 */
	@Override
	public Message getExecutionReport( OrderStatusRequest osr, NettingPortfolio portfolio )
	{
		// TODO Auto-generated method stub
		return null;
	}

    @Override
    protected void setAccount( ExecutionReport report, NewOrderSingle newOrderSingle, SingleLegTrade trade )
    {

    }

    @Override
    protected void setAccount( ExecutionReport report, NewOrderSingle newOrderSingle, SingleLegOrder order )
    {

    }


	@Override
	public Message getOrderFilledExecutionReport(NewOrderMultileg newOrderMultileg, NettingPortfolio portfolio, String execType, List<Trade> allocatedTrades)
	{
		// TODO Auto-generated method stub
		return null;
	}
	
    @Override
    public Message getFXOrderSubmissionExecutionReport(NewOrderSingle newOrderSingle, SingleLegOrder orderRequest, String execType, SessionID sessionID) throws FieldNotFound {
        boolean isSwap = false;
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set( newOrderSingle.getClOrdID() );
        ExecType exType = FixConstants.EXEC_TYPE_NEW;
        OrdStatus ordStatus = FixConstants.ORDER_STATUS_NEW;
        if ( "NEW".equalsIgnoreCase( execType ) ) {

        }
        else if ( "STOPPED".equalsIgnoreCase( execType ) ) {
            exType = FixConstants.EXEC_TYPE_STOPPED;
            ordStatus = FixConstants.ORDER_STATUS_STOPPED;
        }
        else {
            exType = FixConstants.EXEC_TYPE_PENDING_NEW;
            ordStatus = FixConstants.ORDER_STATUS_PENDING_NEW;
        }

        if ( "NEW".equalsIgnoreCase( execType ) || "STOPPED".equalsIgnoreCase( execType ) ) {
            executionReport.set( new OrderID( orderRequest.get_id() ) );
            //todo no ndf support for now
            /*if ( request != null && request.getRequestAttributes().isNDF() )
               {
                   // for RFQ, MaturityDate(tage 541) to be given as actual date
                   FXPaymentParameters fxPayment = ((FXSingleLeg ) request.getTrade()).getFXLeg().getFXPayment();
                   executionReport.set(new MaturityDate(fxPayment.getFixingDate().getFormattedDate( IdcDate.YYYY_MM_DD)));
               }*/
        }
        else {
            if ( newOrderSingle.isSetMaturityDate() )// Expected to be present in RFQ
            {
                executionReport.set( newOrderSingle.getMaturityDate() ); // copy the value (date/tenor) from origianl order
            }

            executionReport.set( new OrderID( "NONE" ) );
        }
        // copy value date from new order single to ExecutionReport
        CurrencyPairDetails cpds = newOrderSingle.getCurrencyPairDetails();
        if (cpds != null && 
        		com.integral.finance.currency.Currency.SettlementType.SPOT != cpds.getSettlementType()
        		    && newOrderSingle.isSetField(FutSettDate.FIELD))
        {
        	String settleDate = newOrderSingle.getString(FutSettDate.FIELD);
        	executionReport.setString(FutSettDate.FIELD, settleDate);
        }
        executionReport.set( exType );
        executionReport.set( ordStatus );
        executionReport.set( FixConstants.PRODUCT );
        executionReport.set( newOrderSingle.getSide() );
        executionReport.set( newOrderSingle.getOrderQty() );
        executionReport.set( FixConstants.CUM_QTY_ZERO );
        executionReport.set( new LeavesQty( newOrderSingle.getOrderQty().getValue() ) );
        executionReport.set( newOrderSingle.getCurrency() );
        executionReport.set( new LastQty( 0 ) );
        executionReport.set( new LastPx( 0 ) );
        executionReport.set( new LastSpotRate( 0 ) );
        executionReport.set( new LastForwardPoints( 0 ) );
        executionReport.set( FixConstants.FOREIGN_EXCHANGE_CONTRACT );
        executionReport.set( new AvgPx( 0 ) );
        executionReport.set( newOrderSingle.getOrdType() );
        executionReport.setUtcTimeStamp( TransactTime.FIELD, new Date(), true );
        executionReport.set( new ExecID( "NONE" ) );
        //executionReport.set(FixConstants.FUT_SETT_DATE_REJECT);
        executionReport.set( newOrderSingle.getSymbol() );
        if ( newOrderSingle.isSetAccount() ) {
            executionReport.set( newOrderSingle.getAccount() );
        }
        if ( newOrderSingle.isSetPrice() ) {
            executionReport.set( newOrderSingle.getPrice() );
        }
        if ( newOrderSingle.isSetTimeInForce() ) {
            executionReport.set( newOrderSingle.getTimeInForce() );
        }
        if ( newOrderSingle.isSetExpireTime() ) {
            executionReport.set( newOrderSingle.getExpireTime() );
        }
        if ( newOrderSingle.isSetMaxShow() ) {
            executionReport.set( newOrderSingle.getMaxShow() );
        }
        if ( newOrderSingle.isSetMinQty() ) {
            executionReport.set( newOrderSingle.getMinQty() );
        }
        if ( newOrderSingle.isSetPrice2() ) {
            executionReport.setField( newOrderSingle.getPrice2() );
            isSwap = true;
        }
        if ( newOrderSingle.isSetOrderQty2() ) {
            executionReport.setField( newOrderSingle.getOrderQty2() );
            isSwap = true;
        }
        if ( newOrderSingle.isSetExecInst() ) {
            executionReport.set( newOrderSingle.getExecInst() );
        }
        if ( newOrderSingle.isSetPegDifference() ) {
            executionReport.set( newOrderSingle.getPegDifference() );
        }
        if ( newOrderSingle.isSetStopPx() ) {
            executionReport.set( newOrderSingle.getStopPx() );
        }
        if ( isSwap ) {
            if ( newOrderSingle.isSetField( FutSettDate2.FIELD ) ) {
                executionReport.set( newOrderSingle.getFutSettDate2() );
            }
            //        	executionReport.setField( new DoubleField( 7542,0 ) );//LastspotRate2
            executionReport.setField( new DoubleField( 641, 0 ) );//LastForwardPoints2
            executionReport.setField( new DoubleField( 7541, 0 ) );//lastPx2
            executionReport.setField( new DoubleField( 7543, 0 ) );//leavesQty2
            executionReport.setField( new DoubleField( 7544, 0 ) );//CumQty2
        }
        if ( newOrderSingle.isSetField( OrigClOrdID.FIELD ) ) {
            executionReport.set( new OrigClOrdID( newOrderSingle.getString( OrigClOrdID.FIELD ) ) );
        }

        setHeader( newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
        setHeader( newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD );
        setHeader( newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD );
        setHeader( newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD );

        return executionReport.getMessage();
    }

    public Message getLimitOrderStatusExecutionReport( WorkflowMessage wfMsg, NewOrderSingle newOrderSingle, SingleLegOrder orderRequest, OrderStatusRequest orderStatusRequest ) throws FieldNotFound {
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set( newOrderSingle.getClOrdID() );
        executionReport.set( FixConstants.EXEC_TYPE_ORDER_STATUS );
        executionReport.set( fixUtil.getOrdStatus( orderRequest ) );
        String orderId = orderRequest.get_id();
        if ( orderId != null ) {
            executionReport.set( new OrderID( orderId ) );
        }
        else {
            executionReport.set( new OrderID( "NONE" ) );
        }
        double orderAmount = orderRequest.getOrderAmount();
		OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
        State state = orderRequest.getState();
        boolean isFixingPreRateState = false;
        if ( state.getName() != null ) {
            switch ( state.getName() ) {
                case RSPRERATECANCELLED:
                case RSPRERATEEXPIRED:
                case RSPRERATECOMPLETE:
                case RSPRERATEPARTIAL:
                    isFixingPreRateState = true;
                    break;
            }
        }
        double filledAmount = isFixingPreRateState ? requestLeg.getTotalfilledAmount() : requestLeg.getFilledAmount();
		double leavesAmount = orderRequest.getDealtCurrency().round( orderAmount - filledAmount );
        double avgFillPrice = 0.0;
        if ( wfMsg != null ) {
            avgFillPrice = ( Double ) wfMsg.getParameterValue( FixConstants.AVG_FILL_PRICE );
            leavesAmount = ( Double ) wfMsg.getParameterValue( FixConstants.UNFILLED_AMT );
			filledAmount = ( Double ) wfMsg.getParameterValue( FixConstants.FILLED_AMT );
            if( isFixingPreRateState ){
                filledAmount = ( Double ) wfMsg.getParameterValue( FixConstants.TOTAL_FILLED_AMT );
            }
            if(orderRequest.isNettingEnabled() && State.Name.RSPARTIAL.equals(orderRequest.getState().getName())){
                filledAmount = requestLeg.getFilledAmount() - requestLeg.getNettedAmount();
                leavesAmount = orderRequest.getDealtCurrency().round( orderAmount - filledAmount );
                if(filledAmount == 0) {
                    avgFillPrice = 0.0;
                }
            }
            log.warn( "FixTranslator43impl.getLimitOrderStatusExecutionReport - OrderId -" + orderId + " AvgFillPrice-" + avgFillPrice + " UnFilled Amount - " + leavesAmount + " FilledAmount-" + filledAmount );
            if ( leavesAmount == 0 ) {
                executionReport.set( FixConstants.ORDER_STATUS_FILLED );
            }
            else if ( filledAmount > 0 ) {
                executionReport.set( FixConstants.ORDER_STATUS_PARTIALLY_FILLED );
            }
			else if ( orderRequest.isExecutionSuspended() || orderRequest.isOrderMigrationInProgress())
			{
				executionReport.set(FixConstants.ORDER_STATUS_STOPPED);
			}
			else
			{
                executionReport.set( FixConstants.ORDER_STATUS_NEW );
            }
        }
		if( orderRequest.isCfdTakerAmountAdjustmentDone() ){
			double cfdTakerMultiplier = MathUtilC.correctFloatingPointsCalculationPrecision(1.0D/orderRequest.getClientDescriptor().getCfdMultiplicationFactor());
			orderAmount = MathUtilC.multiply(cfdTakerMultiplier,orderAmount);
			filledAmount = MathUtilC.multiply(cfdTakerMultiplier,filledAmount);
			leavesAmount = MathUtilC.multiply(cfdTakerMultiplier,leavesAmount);
		}
        executionReport.set( FixConstants.FOREIGN_EXCHANGE_CONTRACT );
        executionReport.set( FixConstants.PRODUCT );
        executionReport.set( newOrderSingle.getSide() );
        executionReport.set( newOrderSingle.getCurrency() );
        executionReport.set( new OrderQty( orderAmount ) );
        executionReport.set( newOrderSingle.getSymbol() );
        executionReport.set( newOrderSingle.getOrdType() );
        executionReport.set( new ExecID( "0" ) );        
        populateValueDate(newOrderSingle, orderRequest, executionReport);
        
        executionReport.setUtcTimeStamp( TransactTime.FIELD, new Date( orderRequest.getCreatedTime() ), true );
        if ( newOrderSingle.isSetAccount() ) {
            executionReport.set( newOrderSingle.getAccount() );
        }
        if ( newOrderSingle.isSetPrice() ) {
            OrderStrategy orderStrategy = orderRequest.getOrderStrategy();
            if ( orderStrategy != null ) {
                if ( orderStrategy.getPegOffsetType() != null ) {
                    String curPegRate = ( String ) wfMsg.getParameterValue( ISCommonConstants.WF_PARAM_CUR_PEG_RATE );
                    if ( curPegRate != null ) {
                        executionReport.setString( Price.FIELD, curPegRate );
                    }
                    else {
                        executionReport.set( newOrderSingle.getPrice() );
                    }
                }
                else {
                    executionReport.set( newOrderSingle.getPrice() );
                }
            }
            else {
                executionReport.set( newOrderSingle.getPrice() );
            }
        }
        if ( newOrderSingle.isSetTimeInForce() ) {
            executionReport.set( newOrderSingle.getTimeInForce() );
        }
        if ( newOrderSingle.isSetExpireTime() ) {
            executionReport.set( newOrderSingle.getExpireTime() );
        }
        if ( newOrderSingle.isSetMaxShow() ) {
            executionReport.set( newOrderSingle.getMaxShow() );
        }
        if ( newOrderSingle.isSetMinQty() ) {
            executionReport.set( newOrderSingle.getMinQty() );
        }
        if ( newOrderSingle.isSetPegDifference() ) {
            executionReport.set( newOrderSingle.getPegDifference() );
        }
        if ( newOrderSingle.isSetExecInst() ) {
            executionReport.set( newOrderSingle.getExecInst() );
        }
		if( orderRequest.isTrailingStop() ){
			/*
				For trailing stop orders the stop price should be taker from the OrderRequest.
			 */
			executionReport.set(new StopPx(orderRequest.getOrderTrigger().getTriggerRate()));
		}
		else {
			if (newOrderSingle.isSetStopPx()) {
				executionReport.set(newOrderSingle.getStopPx());
			}
		}
        executionReport.set( new LeavesQty( leavesAmount ) );
        executionReport.set( new CumQty( filledAmount ) );
        executionReport.set( new AvgPx( avgFillPrice ) );

        if ( orderStatusRequest.isSetClOrdID() ) {
            executionReport.setString( 790, orderStatusRequest.getClOrdID().getValue() );//orderstatusreqiid.
        }
        else {
            executionReport.setString( 790, orderStatusRequest.getOrderID().getValue() );//orderstatusreqiid.
        }
        setHeader( newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
        setHeader( newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD );
        setHeader( newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD );
        setHeader( newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD );

        //todo
        /*if ( orderRequest.getCanrequest.getOrderCancelReason() != null && !request.getOrderCancelReason().equals("") )
        {
            executionReport.setString(Text.FIELD, request.getOrderCancelReason());
        }*/

        return executionReport.getMessage();
    }

    //Used only for order request.
    public Message getExecutionReport( OrderStatusRequest orderStatusRequest, SingleLegOrder orderRequest ) throws FieldNotFound {
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set( fixUtil.getOrdStatus( orderRequest ) );

        OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
        double orderAmount = requestLeg.getAmount();
        double filledAmount = requestLeg.getFilledAmount();
        double leavesAmount = orderRequest.getDealtCurrency().round( orderAmount - filledAmount );
        double avgFillPrice = requestLeg.getAverageRate();
        if (orderRequest.isNettingEnabled() && State.Name.RSPARTIAL.equals(orderRequest.getState().getName())) {
            filledAmount = requestLeg.getFilledAmount() - requestLeg.getNettedAmount();
            leavesAmount = orderRequest.getDealtCurrency().round(orderAmount - filledAmount);
            if (filledAmount == 0) {
                avgFillPrice = 0.0;
                executionReport.set( FixConstants.ORDER_STATUS_NEW );
            }
        }

        String extReqId = orderRequest.getClientReferenceId();
        if ( extReqId == null ) {
            extReqId = "NONE";
        }
        executionReport.set( new ClOrdID( extReqId ) );
        executionReport.set( FixConstants.PRODUCT );
        setBuySell( executionReport, orderRequest, requestLeg );
        executionReport.set( new AvgPx(avgFillPrice) );
        CurrencyPair currPair = CurrencyFactory.getOriginalCurrencyPair(orderRequest.getCurrencyPair());
        executionReport.set( new Symbol( currPair.getName() ) );
        executionReport.setUtcTimeStamp( TransactTime.FIELD, new Date( orderRequest.getCreatedTime() ), true );
        populateValueDate(orderRequest, executionReport);
        executionReport.set( new Currency( orderRequest.getDealtCurrency().getRealCurrency().getShortName() ) );


        switch ( orderRequest.getType() ) {
            case LIMIT:
                executionReport.set( new Price( orderRequest.getOrderSpotRate() ) );
                if(orderRequest.isPQOrder()) {
                    executionReport.set( new OrdType( OrdType.PREVIOUSLY_QUOTED ) );
                } else {
                    executionReport.set( new OrdType( OrdType.LIMIT ) );
                }
                break;
            case MARKET:
                executionReport.set( new OrdType( OrdType.MARKET ) );
                Double pegDiff = orderRequest.getMarketRange();
                if ( pegDiff > 0 ) {
                    executionReport.set( new Price( orderRequest.getOrderSpotRate() ) );
                    executionReport.set( new PegDifference( orderRequest.getMarketRange() ) );
                }
                break;
            case ALGO:
                executionReport.set( new OrdType( OrdType.PREVIOUSLY_INDICATED ) );
                break;
            case STOP:
                executionReport.set( new OrdType( OrdType.STOP ) );
                if ( orderRequest.isMarketRangeOrder() ) {
                    executionReport.set( new Price( orderRequest.getOrderSpotRate() ) );
                    executionReport.set( new PegDifference( orderRequest.getMarketRange() ) );
                    executionReport.set( new StopPx( orderRequest.getOrderTrigger().getTriggerRate() ) );
                }
                else {
                    executionReport.set( new StopPx( orderRequest.getOrderTrigger().getTriggerRate() ) );
                }
                break;
            case STOPLIMIT:
                executionReport.set( new Price( orderRequest.getOrderSpotRate() ) );
                executionReport.set( new OrdType( OrdType.STOP_LIMIT ) );
                executionReport.set( new StopPx( orderRequest.getOrderTrigger().getTriggerRate() ) );
                break;
        }

        quickfix.field.TimeInForce tif = getTimeInForce( orderRequest );
        if ( tif != null ) {
            executionReport.set( tif );
        }

		if( orderRequest.isCfdTakerAmountAdjustmentDone() ){
			double cfdTakerMultiplier = MathUtilC.correctFloatingPointsCalculationPrecision(1.0D/orderRequest.getClientDescriptor().getCfdMultiplicationFactor());
			orderAmount = MathUtilC.multiply(cfdTakerMultiplier,orderAmount);
			filledAmount = MathUtilC.multiply(cfdTakerMultiplier,filledAmount);
			leavesAmount = MathUtilC.multiply(cfdTakerMultiplier,leavesAmount);
		}
        executionReport.set( FixConstants.EXEC_TYPE_ORDER_STATUS );
		executionReport.set( new OrderQty( orderAmount ) );
		executionReport.set( new CumQty( filledAmount ) );
        executionReport.set( new LeavesQty( leavesAmount ) );
        executionReport.set( new LastPx( 0 ) );
        executionReport.set( new ExecID( "0" ) );
        executionReport.set( new OrderID( orderRequest.get_id() ) );
        executionReport.set( FixConstants.FOREIGN_EXCHANGE_CONTRACT );
        if ( orderStatusRequest.isSetClOrdID() ) {
            executionReport.setString( 790, orderStatusRequest.getClOrdID().getValue() );//orderstatusreqiid.
        }
        else {
            executionReport.setString( 790, orderStatusRequest.getOrderID().getValue() );//orderstatusreqiid.
        }
        setHeader( orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
        setHeader( orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD );
        setHeader( orderStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD );
        setHeader( orderStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD );
        //todo
        /*if ( request.getOrderCancelReason() != null && !request.getOrderCancelReason().equals("") )
        {
            executionReport.setString(Text.FIELD, request.getOrderCancelReason());
        }*/

        return executionReport.getMessage();
    }

    public Message getExecutionReportOrderCancelPending( OrderCancelRequest orderCancelRequest, NewOrderSingle newOrderSingle, SingleLegOrder orderRequest, SessionID sessionID ) throws FieldNotFound {
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set( orderCancelRequest.getClOrdID() );
        executionReport.set( orderCancelRequest.getOrigClOrdID() );
        executionReport.set( new OrderID( "NONE" ) );
        executionReport.set( FixConstants.EXEC_TYPE_PENDING_CANCEL );
        executionReport.set( FixConstants.ORDER_STATUS_PENDING_CANCEL );
        executionReport.set( FixConstants.PRODUCT );
        executionReport.set( FixConstants.FOREIGN_EXCHANGE_CONTRACT );
        OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
        double orderAmount = requestLeg.getAmount();
        double filledAmount = requestLeg.getFilledAmount();
        double leavesAmount = orderRequest.getDealtCurrency().round( orderAmount - filledAmount );
		if( orderRequest.isCfdTakerAmountAdjustmentDone() ){
			double cfdTakerMultiplier = MathUtilC.correctFloatingPointsCalculationPrecision(1.0D/orderRequest.getClientDescriptor().getCfdMultiplicationFactor());
			orderAmount = MathUtilC.multiply(cfdTakerMultiplier,orderAmount);
			filledAmount = MathUtilC.multiply(cfdTakerMultiplier,filledAmount);
			leavesAmount = MathUtilC.multiply(cfdTakerMultiplier,leavesAmount);
		}
		executionReport.set( new CumQty( filledAmount ) );
        executionReport.set( new LeavesQty( leavesAmount ) );
        executionReport.set( new AvgPx( 0.0 ) );
        executionReport.setUtcTimeStamp( TransactTime.FIELD, new Date(), true );
        executionReport.set( FixConstants.EXEC_ID_ZERO );
        if ( newOrderSingle != null ) {
            copyOrderAttributes( newOrderSingle, executionReport );
            setHeader( newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
            setHeader( newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD );
            setHeader( newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD );
            setHeader( newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD );
            if( newOrderSingle.isWarmup() )
            	executionReport.setWarmup(newOrderSingle.isWarmup());
        }
        else {
            copyOrderAttributesFromOrderRequest( orderRequest, executionReport );
            CurrencyPair currPair = CurrencyFactory.getOriginalCurrencyPair(orderRequest.getCurrencyPair());
            executionReport.set( new Symbol( currPair.getName() ) );
            executionReport.set( new Currency( orderRequest.getDealtCurrency().getRealCurrency().getShortName() ) );
            setExecInst( orderRequest, executionReport );
            executionReport.set( new OrderQty( orderAmount ) );
            setBuySell( executionReport, orderRequest, requestLeg );
            setHeader( executionReport, TargetSubID.FIELD, orderRequest.getLegalEntity().getShortName() );
        }
		if(FixUtilC.getInstance().isServer2ServerSession(sessionID)){
			executionReport.set(new OrderID(orderRequest.get_id()));
		}
        return executionReport.getMessage();
    }

    public OrderCancelReject getOrderCancelReject( SingleLegOrder orderRequest, String cancelRequestID, boolean isOrderCancelReplace, SessionID sessionID, OrdStatus orderStatus ) {
        String externalRequestId = orderRequest.getClientReferenceId();
        if ( orderStatus == null ) {
            orderStatus = fixUtil.getOrdStatus( orderRequest );
        }
        return getOrderCancelReject( cancelRequestID, isOrderCancelReplace, orderStatus, orderRequest.get_id(), externalRequestId );
    }

    public Message getLimitOrderMassStatusExecutionReport( SingleLegOrder request, OrderMassStatusRequest orderMassStatusRequest, boolean queryOrder ) throws FieldNotFound {
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set( fixUtil.getOrdStatus( request ) );

        OrderRequest.RequestLeg singleLeg = request.getRequestLeg();
        Side side = null;
        switch ( singleLeg.getBuySellMode() ) {
            case BUY:
                side = FixConstants.BUY;
                break;
            case SELL:
                side = FixConstants.SELL;
                break;
        }

        double avgFillPrice = singleLeg.getAverageRate();
        CurrencyPair currPair = CurrencyFactory.getOriginalCurrencyPair(request.getCurrencyPair());
        String ccyPair = currPair.getName();
        double orderAmount = singleLeg.getAmount();
        double filledAmount = singleLeg.getFilledAmount();
        double leavesAmount = orderAmount - filledAmount;
        if ( queryOrder ) {
            SingleLegOrder order = SingleLegOrderQueryService.findOrder( request.get_id(), request.getNamespace().getShortName() );
            if ( order != null ) {
                orderAmount = order.getRequestLeg().getAmount();
                filledAmount = order.getRequestLeg().getFilledAmount();
                leavesAmount = orderAmount - filledAmount;
                avgFillPrice = order.getRequestLeg().getAverageRate();
            }
        }
        if(request.isNettingEnabled() && State.Name.RSPARTIAL.equals(request.getState().getName())){
            if(filledAmount >= request.getRequestLeg().getNettedAmount()) { //pre-caution
                filledAmount = filledAmount - request.getRequestLeg().getNettedAmount();
                leavesAmount = request.getDealtCurrency().round( orderAmount - filledAmount );
                if(filledAmount == 0) {
                    avgFillPrice = 0.0;
                    executionReport.set(FixConstants.ORDER_STATUS_NEW);
                }
            }
        }

        if ( request.getClientReferenceId() != null && !request.getClientReferenceId().equals( "" ) ) {
            executionReport.set( new ClOrdID( request.getClientReferenceId() ) );
        }
        else {
            executionReport.set( new ClOrdID( request.get_id() ) );
        }

        executionReport.set( FixConstants.PRODUCT );
        executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
        executionReport.set( side );
        executionReport.setUtcTimeStamp( TransactTime.FIELD, new Date( request.getCreatedTime() ), true );
        populateValueDate(request, executionReport);
        executionReport.set( new OrderQty( orderAmount ) );
        executionReport.set( new Currency( request.getDealtCurrency().getRealCurrency().getShortName() ) );
        executionReport.set( new Symbol( ccyPair ) );
        executionReport.set( new AvgPx( avgFillPrice ) );

        switch ( request.getType() ) {

            case LIMIT:
                executionReport.set( new Price( singleLeg.getSpotRate() ) );
                executionReport.set( new OrdType( OrdType.LIMIT ) );
                break;
            case MARKET:
                executionReport.set( new OrdType( OrdType.MARKET ) );
                //executionReport.set(new Price(singleLeg.getSpotRate()));
                if ( request.isMarketRangeOrder() ) {
                    executionReport.set( new Price( singleLeg.getSpotRate() ) );
                    executionReport.set( new PegDifference( request.getMarketRange() ) );
                }
                break;
            case STOP:
                executionReport.set( new OrdType( OrdType.STOP ) );
                if ( request.isMarketRangeOrder() ) {
                    executionReport.set( new Price( singleLeg.getSpotRate() ) );
                    executionReport.set( new PegDifference( request.getMarketRange() ) );
                    executionReport.set( new StopPx( request.getOrderTrigger().getTriggerRate() ) );
                }
                else {
                    executionReport.set( new StopPx( request.getOrderTrigger().getTriggerRate() ) );
                }
                break;
            case STOPLIMIT:
                executionReport.set( new Price( singleLeg.getSpotRate() ) );
                executionReport.set( new OrdType( OrdType.STOP_LIMIT ) );
                executionReport.set( new StopPx( request.getOrderTrigger().getTriggerRate() ) );
                break;

        }

        quickfix.field.TimeInForce tif = getTimeInForce( request );
        if ( tif != null ) {
            executionReport.set( tif );
        }
        executionReport.setString( 584, orderMassStatusRequest.getMassStatusReqID().getValue() );//ordermassstatusreqiid.

        Date effectiveDate = new Date( request.getCreatedTime() );


        if ( filledAmount > 0.0 ) {
            executionReport.set( new FutSettDate( DateTimeFactory.newDate( effectiveDate ).getFormattedDate( IdcDate.YYYY_MM_DD ) ) );
        }

        executionReport.set( FixConstants.EXEC_TYPE_ORDER_STATUS );
        executionReport.set( new CumQty( filledAmount ) );
        executionReport.set( new LeavesQty( leavesAmount ) );

        executionReport.set( new LastPx( 0 ) );
        executionReport.set( new ExecID( "0" ) );
        executionReport.set( new OrderID( request.get_id() ) );

        setHeader( orderMassStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD );

        //TODO: fix this when cancelreason is added
        /*if ( request.getOrderCancelReason() != null && !request.getOrderCancelReason().equals("") )
        {
            executionReport.setString(Text.FIELD, request.getOrderCancelReason());
        }*/

        return executionReport.getMessage();
    }

    public Message getLimitOrderMassStatusExecutionReport( WorkflowMessage wfMsg, NewOrderSingle newOrderSingle, SingleLegOrder request, OrderMassStatusRequest orderMassStatusRequest ) throws FieldNotFound {
        OrderRequest.RequestLeg singleLeg = request.getRequestLeg();
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set( newOrderSingle.getClOrdID() );
        executionReport.set( FixConstants.EXEC_TYPE_ORDER_STATUS );
        String orderId = request.get_id();
        if ( orderId != null ) {
            executionReport.set( new OrderID( request.get_id() ) );
        }
        double filledAmount = ( Double ) wfMsg.getParameterValue( FixConstants.FILLED_AMT );
        double leavesAmount = ( Double ) wfMsg.getParameterValue( FixConstants.UNFILLED_AMT );
        double avgFillPrice = ( Double ) wfMsg.getParameterValue( FixConstants.AVG_FILL_PRICE );
        double orderAmount = singleLeg.getAmount();
        if(request.isNettingEnabled() && State.Name.RSPARTIAL.equals(request.getState().getName())){
            filledAmount = request.getRequestLeg().getFilledAmount() - request.getRequestLeg().getNettedAmount();
            leavesAmount = request.getDealtCurrency().round( orderAmount - filledAmount );
            if(filledAmount == 0) {
                avgFillPrice = 0.0;
            }
        }


        executionReport.set( FixConstants.FOREIGN_EXCHANGE_CONTRACT );
        executionReport.set( FixConstants.PRODUCT );
        executionReport.set( newOrderSingle.getSide() );
        executionReport.set( newOrderSingle.getCurrency() );
        executionReport.set( new OrderQty( orderAmount ) );
        executionReport.set( newOrderSingle.getSymbol() );
        executionReport.set( newOrderSingle.getOrdType() );
        executionReport.set( new ExecID( "0" ) );
        populateValueDate(newOrderSingle, request, executionReport);
        executionReport.setUtcTimeStamp( TransactTime.FIELD, new Date( request.getCreatedTime() ), true );
        if ( leavesAmount == 0 ) {
            executionReport.set( FixConstants.ORDER_STATUS_FILLED );
        }
        else if ( filledAmount > 0 ) {
            executionReport.set( FixConstants.ORDER_STATUS_PARTIALLY_FILLED );
        }
        //TODO: fix this
        /*else if ( request.getRequestAttributes().isExexcutionSuspended() )
        {
            executionReport.set(FixConstants.ORDER_STATUS_STOPPED);
        }*/
        else {
            executionReport.set( FixConstants.ORDER_STATUS_NEW );
        }

        if ( newOrderSingle.isSetAccount() ) {
            executionReport.set( newOrderSingle.getAccount() );
        }
        if ( newOrderSingle.isSetPrice() ) {
            executionReport.set( newOrderSingle.getPrice() );
        }
        if ( newOrderSingle.isSetTimeInForce() ) {
            executionReport.set( newOrderSingle.getTimeInForce() );
        }
        if ( newOrderSingle.isSetExpireTime() ) {
            executionReport.set( newOrderSingle.getExpireTime() );
        }
        if ( newOrderSingle.isSetMaxShow() ) {
            executionReport.set( newOrderSingle.getMaxShow() );
        }
        if ( newOrderSingle.isSetMinQty() ) {
            executionReport.set( newOrderSingle.getMinQty() );
        }
        if ( newOrderSingle.isSetPegDifference() ) {
            executionReport.set( newOrderSingle.getPegDifference() );
        }
        if ( newOrderSingle.isSetExecInst() ) {
            executionReport.set( newOrderSingle.getExecInst() );
        }
        if ( newOrderSingle.isSetStopPx() ) {
            executionReport.set( newOrderSingle.getStopPx() );
        }
        executionReport.set( new LeavesQty( leavesAmount ) );
        executionReport.set( new CumQty( filledAmount ) );
        executionReport.set( new AvgPx( avgFillPrice ) );
        executionReport.setString( 584, orderMassStatusRequest.getMassStatusReqID().getValue() );//ordermassstatusreqiid.

        setHeader( orderMassStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD );
        //TODO: add this when the cancelReason is added to request
        /*if ( request.getOrderCancelReason() != null && !request.getOrderCancelReason().equals("") )
        {
            executionReport.setString(Text.FIELD, request.getOrderCancelReason());
        }*/
        return executionReport.getMessage();
    }

    //Mass status response to ESP Trade.
    public Message getOrderMassStatusExecutionReport( SingleLegOrder request, OrderMassStatusRequest orderMassStatusRequest ) throws FieldNotFound {

        OrderRequest.RequestLeg singleLeg = request.getRequestLeg();
        double orderAmount = singleLeg.getAmount();
        double executedAmount = singleLeg.getFilledAmount();

        //TODO: get it reviewed
        double lastDealtAmount = singleLeg.getFilledAmount();


        char side = Side.FIELD;
        switch ( singleLeg.getBuySellMode() ) {
            case BUY:
                side = Side.BUY;
                break;
            case SELL:
                side = Side.SELL;
                break;
        }

        double leavesQty = orderAmount - executedAmount;

        String externalReqID = request.getClientReferenceId();
        if ( externalReqID == null ) {
            externalReqID = "NONE";
        }
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set( new ClOrdID( externalReqID ) );
        executionReport.set( FixConstants.PRODUCT );
        executionReport.set( FixConstants.EXEC_TYPE_ORDER_STATUS );
        executionReport.set( FixConstants.FOREIGN_EXCHANGE_CONTRACT );
        executionReport.set( new Side( side ) );
        executionReport.set( new Price( singleLeg.getSpotRate() ) );
        CurrencyPair currPair = CurrencyFactory.getOriginalCurrencyPair(request.getCurrencyPair());
        executionReport.set( new Symbol( currPair.getName() ) );
        executionReport.set( FixConstants.ORD_TYPE_PREVIOUSLY_QUOTED );
        executionReport.set( FixConstants.IOC );

        executionReport.setUtcTimeStamp( TransactTime.FIELD, new Date( request.getCreatedTime() ), true );
        if ( State.Name.RSEXECUTED.equals( request.getState().getName() ) || State.Name.RSPARTIAL.equals( request.getState().getName() ) ) {
            if ( State.Name.RSPARTIAL.equals( request.getState().getName() ) ) {
                executionReport.set( FixConstants.ORDER_STATUS_PARTIALLY_FILLED );
            }
            else {
                executionReport.set( FixConstants.ORDER_STATUS_FILLED );
            }
            executionReport.set( new LastQty( lastDealtAmount ) );
            executionReport.set( new AvgPx( singleLeg.getAverageRate() ) );
            executionReport.set( new CumQty( executedAmount ) );
            executionReport.set( new LeavesQty( leavesQty ) );
            IdcDate valueDate = DateTimeFactory.newDate( new Date( singleLeg.getValueDate() ) );
            executionReport.set( new FutSettDate( valueDate.getFormattedDate( IdcDate.YYYY_MM_DD ) ) );
        }
        else if ( State.Name.RSDECLINED.equals( request.getState().getName() ) ) {
            executionReport.set( FixConstants.ORDER_STATUS_REJECTED );
            executionReport.set( FixConstants.AVG_PX_REJECT );
            executionReport.set( FixConstants.CUM_QTY_ZERO );
            executionReport.set( FixConstants.LEAVES_QTY_ZERO );
            //TODO: set the declined message here
            //executionReport.set(new Text(trade.getRejectionReason()));
        }
        executionReport.set( new Currency( request.getDealtCurrency().getRealCurrency().getShortName() ) );
        executionReport.set( new OrderQty( orderAmount ) );
        executionReport.set( new ExecID( "0" ) );
        executionReport.set( new OrderID( request.get_id() ) );
        executionReport.setString( 584, orderMassStatusRequest.getMassStatusReqID().getValue() );//ordermassstatusreqiid.
        //TODO: add this when suported
        /*if ( fxPayment.getFixingDate() != null )
        {
            executionReport.set(new MaturityDate(fxPayment.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
        }*/

        executionReport.getHeader().setString( OnBehalfOfCompID.FIELD, request.getOrganization().getShortName() );
        //setHeader( orderMassStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD );
        setHeader( orderMassStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD );
        setHeader( executionReport, SenderSubID.FIELD, request.getLegalEntity().getShortName() );
        return executionReport.getMessage();
    }

    @Override
    public Message getExecutionReportOrderCancelReplacePending( OrderCancelReplaceRequest orderCancelReplaceRequest, NewOrderSingle newOrderSingle, SingleLegOrder request ) throws FieldNotFound {
        OrderRequest.RequestLeg singleLeg = request.getRequestLeg();
        double orderAmount = singleLeg.getAmount();
        double executedAmount = singleLeg.getFilledAmount();
        double leavesQty = orderAmount - executedAmount;
        ExecutionReport executionReport = new ExecutionReport43();
        executionReport.set( orderCancelReplaceRequest.getClOrdID() );
        executionReport.set( new OrderID( "NONE" ) );
        executionReport.set( FixConstants.EXEC_TYPE_PENDING_REPLACE );
        executionReport.set( FixConstants.ORDER_STATUS_PENDING_REPLACE );
        executionReport.set( FixConstants.PRODUCT );
        executionReport.set( FixConstants.FOREIGN_EXCHANGE_CONTRACT );
        executionReport.set( new CumQty( executedAmount ) );
        executionReport.set( new LeavesQty( leavesQty ) );
        executionReport.set( new AvgPx( 0.0 ) );
        executionReport.set( orderCancelReplaceRequest.getOrigClOrdID() );
        executionReport.setUtcTimeStamp( TransactTime.FIELD, new Date(), true );
        executionReport.set( FixConstants.EXEC_ID_ZERO );

        if ( newOrderSingle != null ) {
            copyOrderAttributes( newOrderSingle, executionReport );
            setHeader( newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
            setHeader( newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD );
            setHeader( newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD );
            setHeader( newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD );
        }
        else {
            char side = Side.FIELD;
            switch ( singleLeg.getBuySellMode() ) {
                case BUY:
                    side = Side.BUY;
                    break;
                case SELL:
                    side = Side.SELL;
                    break;
            }
            CurrencyPair currPair = CurrencyFactory.getOriginalCurrencyPair(request.getCurrencyPair());
            executionReport.set( new Symbol( currPair.getName() ) );
            executionReport.set( new Currency( request.getDealtCurrency().getRealCurrency().getShortName() ) );
            setExecInst( request, executionReport );
            executionReport.set( new OrderQty( request.getOrderAmount() ) );
            executionReport.set( new Side( side ) );
            copyOrderAttributesFromOrderRequest( request, executionReport );
            setHeader( executionReport, TargetSubID.FIELD, request.getLegalEntity().getShortName() );
        }
        return executionReport.getMessage();
    }

    public OrderCancelReject getOrderCancelReject( OrderCancelRequest orderCancelRequest, String errorCode, SessionID sessionID ) throws FieldNotFound {
        OrderCancelReject orderCancelReject = new OrderCancelReject43();
        orderCancelReject.set( orderCancelRequest.getClOrdID() );
        orderCancelReject.set( orderCancelRequest.getOrigClOrdID() );
        orderCancelReject.set( new CxlRejResponseTo( CxlRejResponseTo.ORDER_CANCEL_REQUEST ) );
        orderCancelReject.set( new Text( errorCode ) );
        orderCancelReject.set( new OrderID( "NONE" ) );
        Organization org = fixUtil.getCustomerOrg( orderCancelRequest.getMessage(), sessionID );
        LegalEntity customerLe = fixUtil.getLegalEntity( org, orderCancelRequest.getMessage() );
        User user = fixUtil.getUser( orderCancelRequest.getMessage(),sessionID );
        if ( customerLe != null ) {
            SingleLegOrder singleLegOrder = null;
            if ( orderCancelRequest.isSetOrderID() ) {
                singleLegOrder = FXESPWorkflowCache.getOrderRequest( orderCancelRequest.getOrderID().getValue() );
                if ( singleLegOrder == null ) {
                    SpacesQueryService.QueryResult<SingleLegOrder> queryResult = SingleLegOrderQueryService.queryOrderRequestById( orderCancelRequest.getOrderID().getValue(), customerLe.getNamespace().getShortName() );
                    switch ( queryResult.getStatus() ) {
                        case SUCCESS:
                            singleLegOrder = queryResult.getResult();
                    }
                }
            }
            else {
                if ( user != null ) {
                    singleLegOrder = FXESPWorkflowCache.getOrderRequestByClientReferenceId( orderCancelRequest.getOrigClOrdID().getValue(), user.getShortName(), user.getOrganization().getShortName() );
                }
                if ( singleLegOrder == null ) {
                    SpacesQueryService.QueryResult<SingleLegOrder> queryResult = SingleLegOrderQueryService.queryOrderRequestByClientReferenceId( orderCancelRequest.getOrigClOrdID().getValue(), customerLe.getNamespace().getShortName() );
                    switch ( queryResult.getStatus() ) {
                        case SUCCESS:
                            singleLegOrder = queryResult.getResult();
                            if( singleLegOrder != null ){
                                SingleLegOrder cachedRequest = FXESPWorkflowCache.getOrderRequest( singleLegOrder.get_id() );
                                if( cachedRequest != null ){
                                    singleLegOrder = cachedRequest;
                                }
                            }
                    }
                }
            }
            if ( singleLegOrder != null ) {
                orderCancelReject.set( new OrderID( singleLegOrder.get_id() ) );
                orderCancelReject.set( fixUtil.getOrdStatus( singleLegOrder ) );
                //Customer is trying to replace old order.Its not un-known order, correct the rejection reason.
                if ( FixConstants.ORDER_CANCEL_UNKNOWN_ORDER.equals( errorCode ) ) {
                    errorCode = FixConstants.ORDER_CANCEL_TO_LATE_TO_CANCEL;
                }
            }
            else {
                orderCancelReject.set( new OrdStatus( OrdStatus.NEW ) );
                errorCode = FixConstants.ORDER_CANCEL_UNKNOWN_ORDER;
            }
        }
        else {
            orderCancelReject.set( new OrdStatus( OrdStatus.NEW ) );
            errorCode = FixConstants.ORDER_CANCEL_UNKNOWN_ORDER;
        }
        CxlRejReason rejReason = fixUtil.getCxlRejReason( errorCode );
        if ( rejReason != null ) {
            orderCancelReject.set( rejReason );
        }
        return orderCancelReject;
    }

    /* (non-Javadoc)
      * @see com.integral.fix.client.translator.FixTranslator43#getExecutionReportCancelReject(quickfix.fix43.OrderCancelReplaceRequest, java.lang.String)
      */
    public OrderCancelReject getOrderCancelReject( OrderCancelReplaceRequest orderCancelReplaceRequest, String errorCode, SessionID sessionID ) throws FieldNotFound {
        OrderCancelReject orderCancelReject = new OrderCancelReject43();
        orderCancelReject.set( orderCancelReplaceRequest.getClOrdID() );
        orderCancelReject.set( orderCancelReplaceRequest.getOrigClOrdID() );
        orderCancelReject.set( new CxlRejResponseTo( CxlRejResponseTo.ORDER_CANCEL_REPLACE_REQUEST ) );
        orderCancelReject.set( new Text( errorCode ) );
        orderCancelReject.set( new OrderID( "NONE" ) );
        Organization org = fixUtil.getCustomerOrg( orderCancelReplaceRequest.getMessage(), sessionID );
        LegalEntity customerLe = fixUtil.getLegalEntity( org, orderCancelReplaceRequest.getMessage() );
        User user = fixUtil.getUser( orderCancelReplaceRequest.getMessage(),sessionID );
        if ( customerLe != null ) {
            SingleLegOrder singleLegOrder = null;
            if ( user != null ) {
                singleLegOrder = FXESPWorkflowCache.getOrderRequestByClientReferenceId( orderCancelReplaceRequest.getOrigClOrdID().getValue(), user.getShortName(), user.getOrganization().getShortName() );
            }
            if ( singleLegOrder == null ) {
                SpacesQueryService.QueryResult<SingleLegOrder> queryResult = SingleLegOrderQueryService.queryOrderRequestByClientReferenceId( orderCancelReplaceRequest.getOrigClOrdID().getValue(), customerLe.getNamespace().getShortName() );
                switch ( queryResult.getStatus() ) {
                    case SUCCESS:
                        singleLegOrder = queryResult.getResult();
                        if( singleLegOrder != null ){
                            SingleLegOrder cachedRequest = FXESPWorkflowCache.getOrderRequest( singleLegOrder.get_id() );
                            if( cachedRequest != null ){
                                singleLegOrder = cachedRequest;
                            }
                        }
                }
            }
            if ( singleLegOrder != null ) {
                orderCancelReject.set( new OrderID( singleLegOrder.get_id() ) );
                orderCancelReject.set( fixUtil.getOrdStatus( singleLegOrder ) );
                if ( FixConstants.ORDER_CANCEL_UNKNOWN_ORDER.equals( errorCode ) ) {
                    errorCode = FixConstants.ORDER_CANCEL_TO_LATE_TO_CANCEL;
                    orderCancelReject.set( new Text( errorCode ) );
                }
            }
            else {
                orderCancelReject.set( new OrdStatus( OrdStatus.NEW ) );
                errorCode = FixConstants.ORDER_CANCEL_UNKNOWN_ORDER;
            }
        }
        else {
            orderCancelReject.set( new OrdStatus( OrdStatus.NEW ) );
            errorCode = FixConstants.ORDER_CANCEL_UNKNOWN_ORDER;
        }

        CxlRejReason rejReason = fixUtil.getCxlRejReason( errorCode );
        if ( rejReason != null ) {
            orderCancelReject.set( rejReason );
        }
        if( FixConstants.ORDER_AMEND_IN_DIRECTED_ORDER_WORKFLOW_NOT_SUPPORTED.equals(errorCode)) {
            orderCancelReject.set(new OrdStatus(OrdStatus.REJECTED));
        }
        return orderCancelReject;
    }

    @Override
	protected void populateSalesSpreadDetails(com.integral.fix.client.message.Quote fixQuote, Quote quote)
	{
		// not populating for FIX 4.3
	}
}
