package com.integral.fix.client.translator;

import com.integral.SEF.SEFConstants;
import com.integral.SEF.SEFUtilC;
import com.integral.admin.utils.StringUtils;
import com.integral.aggregation.AggregationMethod;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.price.SwapFXPriceBook;
import com.integral.deposits.DepositsParam;
import com.integral.exception.IdcIllegalArgumentException;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.TimeInForce;
import com.integral.finance.dealing.*;
import com.integral.finance.dealing.facade.DealStateFacade;
import com.integral.finance.dealing.facade.RequestPriceFacade;
import com.integral.finance.dealing.facade.RequestStateFacade;
import com.integral.finance.dealing.fx.*;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.finance.dealing.mifid.MiFIDUtils;
import com.integral.finance.fx.*;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.fix.client.FIXHandlerFactory;
import com.integral.fix.client.FixConfiguration;
import com.integral.fix.client.FixConstants;
import com.integral.fix.client.cache.ClientCache;
import com.integral.fix.client.factory.FIXMessageFactory;
import com.integral.fix.client.mbean.FIXClientConfigMBean;
import com.integral.fix.client.message.*;
import com.integral.fix.client.message.fix44.grp.NewOrderMultilegNoLegsNoNestedPartyIDs44;
import com.integral.fix.client.message.grp.NoLegs;
import com.integral.fix.client.message.grp.NoNestedPartyIDs;
import com.integral.fix.client.message.grp.NoRelatedSym;
import com.integral.fix.client.message.grp.*;
import com.integral.fix.client.mifid.MiFIDHandlerFactory;
import com.integral.fix.client.subscription.AggregatedViewSubscription;
import com.integral.fix.client.subscription.FixSubscription;
import com.integral.fix.client.subscription.ProviderSubscription;
import com.integral.fix.client.subscription.SubscriptionManager;
import com.integral.fix.client.util.FIXSEFUtilC;
import com.integral.fix.client.util.FixUtilC;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.HandlerCacheC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.finance.trade.calculator.RFSFXTradeCalculatorC;
import com.integral.is.fix.client.handler.AggregatedRFSRateHandler;
import com.integral.is.fix.client.handler.RFSHandler;
import com.integral.is.oms.OrderConstants;
import com.integral.is.pq.PQServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.message.MessageFactory;
import com.integral.message.*;
import com.integral.message.dealing.AcceptedDealingPriceReference;
import com.integral.message.dealing.AcceptedQuoteReference;
import com.integral.message.dealing.DealingMessageFactory;
import com.integral.mifid.MiFIDMBean;
import com.integral.mifid.MiFIDMBeanC;
import com.integral.mifid.TradingCapacity;
import com.integral.mifid.TrdRegPublicationReason;
import com.integral.model.dealing.OrderRequest.Type;
import com.integral.model.dealing.TargetStrategy;
import com.integral.netting.NettingMBeanC;
import com.integral.netting.model.*;
import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.pms.cache.PortfolioServiceCache;
import com.integral.pms.price.PortfolioPriceCalculator;
import com.integral.scheduler.Expiration;
import com.integral.scheduler.ExpirationC;
import com.integral.sef.SefWorkFlow;
import com.integral.staging.Order;
import com.integral.staging.*;
import com.integral.staging.Order.TIF;
import com.integral.staging.config.StagingServiceConfig;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserContact;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;
import com.integral.util.Tuple;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadObjectQuery;
import quickfix.Group;
import quickfix.Message;
import quickfix.*;
import quickfix.Message.Header;
import quickfix.field.Currency;
import quickfix.field.NoMDEntries;
import quickfix.field.*;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.integral.SEF.RTNService.FIX_FIELD_RTN;
import static com.integral.staging.StagingAreaConstants.*;

public abstract class CommonTranslator implements Translator
{

	protected FIXClientConfigMBean configMbean = FixUtilC.getInstance().getFIXClientConfig();
	protected Log log = LogFactory.getLog(this.getClass());
    private static final String SETTLEMENT_CODE = "SettlementCode";
    private static final char EMAIL_ADDRESS_AT = '@';

	protected static int[] mdentryFieldOrder = new int[] { 269, 270, 15, 271, 1026 , 1027, 272, 273, 274, 275, 336, 625, 276, 277, 282, 283, 284, 286, 59, 432, 126, 110, 18, 287, 37, 299, 288, 289, 346, 290, 546, 58, 354, 355, 0 };

	protected ClientCache clientCache = ClientCache.getInstance();
	protected FixUtilC fixUtil = FixUtilC.getInstance();

	/**
	 * fix configuration reference
	 */
	protected FixConfiguration configuration;

	//public static FXRateConvention rateConv = (FXRateConvention) new ReadNamedEntityC().execute(FXRateConvention.class, "STDQOTCNV");
	public static FXRateConvention rateConv = (FXRateConvention) ReferenceDataCacheC.getInstance().getEntityByShortName("STDQOTCNV", FXRateConvention.class, null, null);

	public static final String DATE_FORMAT_UTC = "yyyyMMdd";
	// simpledateformat is not thread safe
	protected ThreadLocal<SimpleDateFormat> dateFormater = new ThreadLocal<SimpleDateFormat>() {
		@Override
		protected SimpleDateFormat initialValue() {
			return new SimpleDateFormat(DATE_FORMAT_UTC);
		}
	};

	public MarketDataSnapshotFullRefresh getInactiveQuote( String lpName, String ccyPair, String mdRequestID, String senderSubID, Organization org, SessionID sessionID )
	{
		String quoteID = FixUtilC.getInstance().newProviderQuoteId(lpName, System.currentTimeMillis());
		MarketDataSnapshotFullRefresh mdResponse = getInactiveQuote(lpName, ccyPair, mdRequestID, senderSubID, quoteID, org, sessionID);
		return mdResponse;
	}

	protected abstract MarketDataSnapshotFullRefresh getInactiveQuote( String lpName, String ccyPair, String mdRequestID, String senderSubID, String quoteID, Organization custOrg, SessionID sessionID );

	protected void addMDEntry( Collection<com.integral.broker.price.Price> prices, MDEntryType mdEntryType, MarketDataSnapshotFullRefresh mdResponse, Organization organization, Currency ccy, String guid, int marketDepth, boolean isAggregatedBook, boolean isVWAP, boolean breakIfNoEntry )
	{
		com.integral.fix.client.message.grp.NoMDEntries noMDEntries = null;

		int size = 0;
		MDEntryOriginator mdEntryOriginator = null;

		int priceType = mdEntryType.equals(FixConstants.BID) ? 0 : 1;

		for ( com.integral.broker.price.Price price : prices )
		{
			noMDEntries = mdResponse.createNoMDEntries();
			if ( size >= marketDepth && marketDepth != 0 )
			{
				break;
			}
			size++;

			if ( !isAggregatedBook && !isVWAP )
			{
				mdEntryOriginator = new MDEntryOriginator(price.getOrganization().getShortName());
			}
			else
			{
				if ( mdEntryOriginator == null )
				{
					String providerName = configMbean.getAggregatedBookProviderName(organization.getShortName());
					mdEntryOriginator = new MDEntryOriginator(providerName);
				}
			}

			noMDEntries.set(FixConstants.MD_POSITION_NO_ZERO);
			noMDEntries.set(mdEntryType);
			noMDEntries.set(new MDEntryPx(price.getRate()));
			noMDEntries.set(new MDEntrySize(price.getAmount()));
			if ( price.getRate() > 0 && price.getAmount() > 0 )
			{
				noMDEntries.set(FixConstants.QUOTE_CONDITION_ACTIVE);
			}
			else
			{
				if ( isVWAP )
				{
					mdResponse.setGroups(NoMDEntries.FIELD, new ArrayList<quickfix.Group>());
					break;
				}
				noMDEntries.set(FixConstants.QUOTE_CONDITION_INACTIVE);
			}
			noMDEntries.set(new QuoteEntryID(new StringBuffer(guid).append(priceType).append(size).toString()));
			noMDEntries.set(ccy);
			noMDEntries.set(mdEntryOriginator);
			mdResponse.addGroup(noMDEntries);
		}
	}

	/**
	 * RFS Trade verification/rejection notification.
	 */
	public ExecutionReport getRFSExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		Object obj = wf.getObject();
		Request request = null;
		FXLegDealingPrice dealingPrice = null;
		FXLegDealingPrice dealingPrice_far_leg = null;
		FXPaymentParameters fxPayment = null;
		FXPaymentParameters fxPayment_Far = null;
		String transactionID = null;
		boolean isSwap = false;
		double filledPrice = 0.0;
		double spotRate = 0.0;
		double spotForwardPoints = 0.0;
		String counterPartyBOrgName = null;
		String counterPartyB = null;
		String maskLP = null;
		Trade trd = null;
		if ( obj instanceof FXSingleLeg )
		{
			FXSingleLeg trade = (FXSingleLeg) obj;
			trd = trade;
			request = trade.getRequest();
			dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
			fxPayment = trade.getFXLeg().getFXPayment();
			transactionID = trade.getTransactionID();
			FXRate fxRate = fxPayment.getFXRate();
			filledPrice = fxRate.getRate();
			spotRate = fxRate.getSpotRate();
			spotForwardPoints = fxRate.getForwardPoints();
			counterPartyBOrgName = trade.getCounterpartyB().getOrganization().getShortName();
			counterPartyB = trade.getCounterpartyB().getShortName();
			maskLP = trade.getMaskedLP();
		}
		else if ( obj instanceof FXSwap )
		{
			isSwap = true;
			FXSwap trade = (FXSwap) obj;
			trd = trade;
			request = trade.getRequest();
			dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.NEAR_LEG);
			dealingPrice_far_leg = (FXLegDealingPrice) request.getRequestPrice(FixConstants.FAR_LEG);
			fxPayment = trade.getNearLeg().getFXPayment();
			fxPayment_Far = trade.getFarLeg().getFXPayment();
			transactionID = trade.getTransactionID();
			FXRate fxRate = fxPayment.getFXRate();
			filledPrice = fxRate.getRate();
			spotRate = fxRate.getSpotRate();
			spotForwardPoints = fxRate.getForwardPoints();
			counterPartyBOrgName = trade.getCounterpartyB().getOrganization().getShortName();
			counterPartyB = trade.getCounterpartyB().getShortName();
			maskLP = trade.getMaskedLP();
		}

		FXLegDealingPrice fxLegAcceptedDealingPrice = (FXLegDealingPrice) dealingPrice.getAcceptedDealingPrice();
		int bidOfferMode = dealingPrice.getAcceptedPriceBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) fxLegAcceptedDealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		boolean isDealtCcy1 = fxLegAcceptedDealingPrice.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
		double filledAmt = isDealtCcy1 ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount();
		double settledAmount = isDealtCcy1 ? fxPayment.getCurrency2Amount() : fxPayment.getCurrency1Amount();
		String settledCcy = isDealtCcy1 ? fxPayment.getCurrency2().getISOName() : fxPayment.getCurrency1().getISOName();
		double orderAmount = dealingPrice.getDealtAmount();
		double filledPrice2 = 0.0;
		//double spotRate2 = 0.0;
		double spotForwardPoints2 = 0.0;
		double filledAmt2 = 0.0;
		double orderAmount2 = 0.0;
		double settledAmount2 = 0.0;
		Side side = null;
		if ( isSwap )
		{
			FXLegDealingPrice fxLegAcceptedDealingPrice2 = (FXLegDealingPrice) dealingPrice_far_leg.getAcceptedDealingPrice();

			int bidOfferMode2 = dealingPrice_far_leg.getAcceptedPriceBidOfferMode();
			boolean isBid2 = bidOfferMode2 == FXLegDealingPrice.BID;
			FXPrice fxPrice2 = ((FXDealingPriceElement) fxLegAcceptedDealingPrice2.getPriceElement()).getFXPrice();
			FXRate fxRate2 = isBid2 ? fxPrice2.getBidFXRate() : fxPrice2.getOfferFXRate();
			isDealtCcy1 = fxLegAcceptedDealingPrice2.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
			FXRate fxrate_fxpayment = fxPayment_Far.getFXRate();
			filledPrice2 = fxrate_fxpayment.getRate();
			//spotRate2 = fxrate_fxpayment.getSpotRate();
			spotForwardPoints2 = fxrate_fxpayment.getForwardPoints();
			filledAmt2 = isDealtCcy1 ? fxPayment_Far.getCurrency1Amount() : fxPayment_Far.getCurrency2Amount();
			settledAmount2 = isDealtCcy1 ? fxPayment_Far.getCurrency2Amount() : fxPayment_Far.getCurrency1Amount();
			orderAmount2 = dealingPrice_far_leg.getDealtAmount();
			if ( dealingPrice_far_leg.getDealtCurrency().isSameAs(fxRate2.getCurrencyPair().getBaseCurrency()) )
			{
				side = isBid2 ? FixConstants.SELL : FixConstants.BUY;
			}
			else
			{
				side = isBid2 ? FixConstants.BUY : FixConstants.SELL;
			}
		}

		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.ORD_TYPE_PREVIOUSLY_QUOTED);
		executionReport.set(newOrderSingle.getClOrdID());
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID((transactionID)));
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.FILL_OR_KILL));
		executionReport.set(new TradeDate(((Trade) obj).getTradeDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
		{
			executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
		}
		else
		{
			executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
		}
		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.set(new FutSettDate(dealingPrice.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		if ( dealingPrice.getFixingDate() != null )
		{
			executionReport.set(new MaturityDate(dealingPrice.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		}
		executionReport.setUtcTimeStamp(TransactTime.FIELD, getTransactTimeForExecutionReport(trd), true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new Price(filledPrice));
		if ( isSwap )
		{
			executionReport.set(side);
			executionReport.setField(new Price2(filledPrice2));
			executionReport.set(new OrderQty2(orderAmount2));
			executionReport.set(new FutSettDate2(dealingPrice_far_leg.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
			if ( dealingPrice_far_leg.getFixingDate() != null )
			{
				executionReport.setString(SEFConstants.FIX_FIELD_FAR_FIXING_DATE, dealingPrice_far_leg.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD));
			}
			String subTradeType = request.getSubTradeType();
			if(subTradeType != null && subTradeType.equals(ISCommonConstants.DEPOSITS)){
				executionReport.set(new SecurityType(SecurityType.TIME_DEPOSIT));
			}
		}
		RequestStateFacade facade = ((RequestStateFacade) request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE));

		if ( facade.isDeclined() )
		{
			String rejectReason = ((Trade) obj).getWorkflowStateMap().getWorkflowCodeArgument();
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(FixConstants.EXEC_TYPE_REJECTED);
			executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
			executionReport.set(FixConstants.CUM_QTY_ZERO);
			executionReport.set(FixConstants.AVG_PX_REJECT);
			executionReport.set(new LastSpotRate(0));
			executionReport.set(new LastForwardPoints(0));
			executionReport.set(new LastPx(0));
			executionReport.set(new SettlCurrAmt(0.0));
			executionReport.set(new SettlCurrency(settledCcy));
			FixUtilC.getInstance().setRejectReason(executionReport, rejectReason);//sets error code and error reason
			if ( isSwap )
			{
				executionReport.setField(new DoubleField(641, 0));//LastForwardPoints2
				executionReport.setField(new DoubleField(7541, 0));//lastPx2
				executionReport.setField(new DoubleField(7543, 0));//leavesQty2
				executionReport.setField(new DoubleField(7544, 0));//CumQty2
				executionReport.setField(new DoubleField(7545, 0.0));//SettlCurrAmt
			}
		}
		else
		{
			//Set other quantities and prices
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(new CumQty(filledAmt));
			executionReport.set(new LastQty(filledAmt));
			executionReport.set(new LastSpotRate(spotRate));
			executionReport.set(new LastForwardPoints(spotForwardPoints));
			executionReport.set(new LastPx(filledPrice));
			executionReport.set(new SettlCurrAmt(settledAmount));
			executionReport.set(new SettlCurrency(settledCcy));
			if ( isSwap )
			{
				executionReport.set(new LastQty(filledAmt2));//for swap it should be far legs
				executionReport.setField(new DoubleField(7545, settledAmount2));//SettlCurrAmt
				executionReport.setField(new DoubleField(641, spotForwardPoints2));//LastForwardPoints2
				executionReport.setField(new DoubleField(7541, filledPrice2));//lastPx2
				executionReport.setField(new DoubleField(7543, 0));//leavesQty2
				executionReport.setField(new DoubleField(7544, filledAmt2));//CumQty2

			}
			executionReport.set(new AvgPx(filledPrice));
			executionReport.set(FixConstants.EXEC_TYPE_TRADE);
			if ( facade.isPartial() )
			{
				executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
			}
			else if ( facade.isAcceptVerified() )
			{
				executionReport.set(FixConstants.ORDER_STATUS_FILLED);
			}

			FIXSEFUtilC.setSEFFields(executionReport, request);
			UTIWorkFlowHelper.updateExecutionReport(executionReport, request);
			UPIWorkFlowHelper.updateExecutionReport(executionReport, request);
			RTNWorkFlowHelper.updateExecutionReport(executionReport, request);
			MiFIDHandlerFactory.getInstance().getMiFIDHandler().populateExecutionReport(executionReport,trd);
		}

		//setHeader( newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
		executionReport.getHeader().setString(OnBehalfOfCompID.FIELD, counterPartyBOrgName);
		setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
		setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
		setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		setHeader(executionReport, SenderSubID.FIELD, counterPartyB);
		if ( maskLP != null && configMbean.isSendMaskLP(request.getCounterparty().getOrganization().getShortName()) )
		{
			setHeader(executionReport, OnBehalfOfSubID.FIELD, maskLP);
		}
		if(trd != null && configMbean.isSendSettlementInstructionInQRAndER(request.getOrganization().getShortName()) && !StringUtils.isNullOrEmptyString(trd.getNote()))
		{
			executionReport.setSettlementInst(trd.getNote());
		}
		if(trd.getDepositsParam() != null){
			DepositsParam dp = trd.getDepositsParam();
			if(dp.getAnnualInterestRate() != null) executionReport.getMessage().setDouble(FixConstants.FIELD_DEP_ANN_INT_RATE, dp.getAnnualInterestRate());
			if(dp.getInterest() != null) executionReport.getMessage().setDouble(FixConstants.FIELD_DEP_ACT_INT, dp.getInterest());
			if(dp.getNumberOfDays() != null) executionReport.getMessage().setInt(FixConstants.FIELD_DEP_NUM_DAYS, dp.getNumberOfDays());
		}
		return executionReport;
	}

	/**
	 * Trade verification/rejection notification for Limit/Market/Stop/Stop limit orders.
	 */
	public ExecutionReport getLimitOrderExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		FXSingleLeg trade = (FXSingleLeg) wf.getObject();
		Request request = trade.getRequest();
		FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		FXPaymentParameters fxPayment = trade.getFXLeg().getFXPayment();
		FXLegDealingPrice fxLegAcceptedDealingPrice = (FXLegDealingPrice) dealingPrice.getAcceptedDealingPrice();
		int bidOfferMode = dealingPrice.getAcceptedPriceBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) fxLegAcceptedDealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		boolean isDealtCcy1 = fxLegAcceptedDealingPrice.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
		double filledPrice = fxPayment.getFXRate().getRate();
		double filledAmt = isDealtCcy1 ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount();
		double settledAmt = isDealtCcy1 ? fxPayment.getCurrency2Amount() : fxPayment.getCurrency1Amount();
		String settledCcy = isDealtCcy1 ? fxPayment.getCurrency2().getISOName() : fxPayment.getCurrency1().getISOName();
		FXLegDealingPrice dealingPrice_LimitRequest = (FXLegDealingPrice) request.getOriginalRequest().getRequestPrice(FixConstants.SINGLE_LEG);
		double avgFilledPrice = (Double) wf.getParameterValue(FixConstants.AVG_FILL_PRICE);
		double unfilledAmount = (Double) wf.getParameterValue(FixConstants.UNFILLED_AMT);
		double totalFilledAmt = (Double) wf.getParameterValue(FixConstants.FILLED_AMT);
		//Populate exec report
		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new ClOrdID(request.getExternalRequestId()));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID((trade.getTransactionID())));
		if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
		{
			executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
		}
		else
		{
			executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
		}
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.set(new FutSettDate(dealingPrice.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		executionReport.set(new TradeDate(trade.getTradeDate().getFormattedDate(IdcDate.YYYY_MM_DD)));

		executionReport.setUtcTimeStamp(TransactTime.FIELD, getTransactTimeForExecutionReport(trade), true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new OrderQty(dealingPrice_LimitRequest.getDealtAmount()));
		if ( newOrderSingle != null )
		{
			executionReport.set(newOrderSingle.getOrdType(), newOrderSingle.getOrigOrdType());
			copyOrderAttributes(newOrderSingle, executionReport);
		}
		else
		{
			copyOrderAttributesFromDealingPrice(request.getOriginalRequest(), executionReport, false);
		}
		RequestStateFacade facade = ((RequestStateFacade) request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE));

		if ( facade.isDeclined() )
		{
			String rejectReason = trade.getWorkflowStateMap().getWorkflowCodeArgument();
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(FixConstants.EXEC_TYPE_REJECTED);
			executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
			executionReport.set(FixConstants.CUM_QTY_ZERO);
			executionReport.set(FixConstants.AVG_PX_REJECT);
			executionReport.set(new LastPx(0));
			FixUtilC.getInstance().setRejectReason(executionReport, rejectReason);//sets error code and error reason
		}
		else
		{
			//Set other quantities and prices
			int version = FixUtilC.getInstance().getVersion(sessionID);
			executionReport.set(new LeavesQty((unfilledAmount)));
			executionReport.set(new CumQty(totalFilledAmt));
			executionReport.set(new LastQty(filledAmt));
			executionReport.set(new AvgPx(avgFilledPrice));
			executionReport.set(new SettlCurrAmt(settledAmt));
			executionReport.set(new SettlCurrency(settledCcy));
			executionReport.set(new LastPx(filledPrice));
			setExecTypeAndOrderStatus(executionReport, version, unfilledAmount);
		}

		executionReport.getHeader().setString(OnBehalfOfCompID.FIELD, trade.getCounterpartyB().getOrganization().getShortName());
		setAccount(executionReport, newOrderSingle, request, trade);

		if ( newOrderSingle != null )
		{
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		}
		else
		{
			boolean isSalesDealerTrade = !request.getOriginalRequest().getUser().getOrganization().isSameAs(request.getOriginalRequest().getCounterparty().getOrganization());
			if ( !isSalesDealerTrade )
			{
				setHeader(executionReport, TargetSubID.FIELD, trade.getCounterpartyA().getShortName());
			}
			else
			{
				setHeader(executionReport, DeliverToSubID.FIELD, trade.getCounterpartyA().getShortName());
				setHeader(executionReport, DeliverToCompID.FIELD, trade.getCounterpartyA().getOrganization().getShortName());
			}
		}
		setHeader(executionReport, SenderSubID.FIELD, trade.getCounterpartyB().getShortName());

		if ( trade.getMaskedLP() != null && configMbean.isSendMaskLP(trade.getCounterpartyA().getOrganization().getShortName()) )
		{
			setHeader(executionReport, OnBehalfOfSubID.FIELD, trade.getMaskedLP());
		}
		if ( request.isOutrightLimitOrder() )
		{
			executionReport.set(new LastSpotRate(fxRate.getSpotRate()));
			executionReport.set(new LastForwardPoints(fxRate.getForwardPoints()));
			if ( fxLegAcceptedDealingPrice.getFixingDate() != null )
			{
				executionReport.set(new MaturityDate(fxLegAcceptedDealingPrice.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
			}
		}
		UTIWorkFlowHelper.updateExecutionReport(executionReport, request);
		UPIWorkFlowHelper.updateExecutionReport(executionReport, request);
		RTNWorkFlowHelper.updateExecutionReport(executionReport, request);
		return executionReport;
	}

	protected void setExecTypeAndOrderStatus( ExecutionReport executionReport, int version, double unfilledAmount )
	{
		if ( unfilledAmount != 0 )
		{
			if ( version == 42 )
			{
				executionReport.set(FixConstants.EXEC_TYPE_PARTIAL);
			}
			else
			{
				executionReport.set(FixConstants.EXEC_TYPE_TRADE);
			}
			executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
		}
		else if ( unfilledAmount == 0 )
		{
			if ( version == 42 )
			{
				executionReport.set(FixConstants.EXEC_TYPE_FILL);
			}
			else
			{
				executionReport.set(FixConstants.EXEC_TYPE_TRADE);
			}
			executionReport.set(FixConstants.ORDER_STATUS_FILLED);
		}
	}

	/**
	 * Creates report for WITHDRAW REQUEST notification. Order cancellation requested by user or server initiated both
	 * will notified through this report.
	 */
	public ExecutionReport getOrderCancelExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		Object object = wf.getObject();
		Request request = null;
		if ( object instanceof Trade )
		{
			request = ((Trade) object).getRequest();
		}
		else
		{
			request = (Request) object;
		}
		FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		int bidOfferMode = dealingPrice.getBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) dealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		double orderAmount = dealingPrice.getDealtAmount();
		double avgFilledPrice = (Double) wf.getParameterValue(FixConstants.AVG_FILL_PRICE);
		double unfilledAmount = (Double) wf.getParameterValue(FixConstants.UNFILLED_AMT);
		double totalFilledAmt = (Double) wf.getParameterValue(FixConstants.FILLED_AMT);
		String COrderId = (String) wf.getParameterValue(FixConstants.ORDER_CANCEL_REQUEST_ID);
		if ( COrderId == null )
		{
			COrderId = request.getExternalRequestId();
		}
		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new OrigClOrdID(request.getExternalRequestId()));
		executionReport.set(new ClOrdID(COrderId));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID(request.getTransactionID()));
		if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
		{
			executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
		}
		else
		{
			executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
		}
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, request.getCreatedDate(), true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new LeavesQty(unfilledAmount));
		executionReport.set(new LastPx(0));
		executionReport.set(FixConstants.EXEC_TYPE_CANCELED);
		executionReport.set(FixConstants.ORDER_STATUS_CANCELED);
		executionReport.set(new CumQty(totalFilledAmt));
		executionReport.set(new AvgPx(avgFilledPrice));
		if ( newOrderSingle != null )
		{
			executionReport.set(newOrderSingle.getOrdType(), newOrderSingle.getOrigOrdType());
			copyOrderAttributes(newOrderSingle, executionReport);
			setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		}
		else
		{
			copyOrderAttributesFromDealingPrice(request, executionReport, false);
			setHeader(executionReport, TargetSubID.FIELD, request.getCounterparty().getShortName());
		}
		setAccount(executionReport, newOrderSingle, request, null);

        double minQtyDouble = 0;
        if ( dealingPrice.getMinDealtAmount() != null && dealingPrice.getMinDealtAmount() > 0 )
        {
            minQtyDouble = dealingPrice.getMinDealtAmount();
        }

        setBelowMinimumField(executionReport, minQtyDouble);
		String cancelReason = request.getOrderCancelReason();
		if ( cancelReason != null && !cancelReason.equals("") )
		{
			executionReport.setString(Text.FIELD, cancelReason);
		}
		UTIWorkFlowHelper.updateExecutionReport(executionReport, request);
		UPIWorkFlowHelper.updateExecutionReport(executionReport, request);
		RTNWorkFlowHelper.updateExecutionReport(executionReport, request);
		return executionReport;
	}

	/**
	 * Order replaced successfully notification.
	 */
	public ExecutionReport getOrderCancelReplaceExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		Object object = wf.getObject();
		Request request = null;
		if ( object instanceof Trade )
		{
			request = ((Trade) object).getRequest();
		}
		else
		{
			request = (Request) object;
		}
		FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		int bidOfferMode = dealingPrice.getBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) dealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		double orderAmount = dealingPrice.getDealtAmount();
		double avgFilledPrice = (Double) wf.getParameterValue(FixConstants.AVG_FILL_PRICE);
		double unfilledAmount = (Double) wf.getParameterValue(FixConstants.UNFILLED_AMT);
		double totalFilledAmt = (Double) wf.getParameterValue(FixConstants.FILLED_AMT);
		String COrderId = (String) wf.getParameterValue(FixConstants.ORDER_CANCEL_REPLACE_REQUEST_ID);
		if ( COrderId == null )
		{
			COrderId = (String) wf.getParameterValue(FixConstants.ORDER_CANCEL_REQUEST_ID);
		}
		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new ClOrdID(COrderId));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID(request.getTransactionID()));

		String origCustomerOrderId = (String) wf.getParameterValue(FixConstants.ORIG_CUSTOMER_ORDER_ID);
		if ( origCustomerOrderId == null )
		{
			origCustomerOrderId = request.getExternalRequestId();
		}
		executionReport.set(new OrigClOrdID(origCustomerOrderId));

		if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
		{
			executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
		}
		else
		{
			executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
		}
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new LeavesQty(unfilledAmount));
		executionReport.set(FixConstants.EXEC_TYPE_REPLACE);
		executionReport.set(FixConstants.ORDER_STATUS_REPLACED);
		executionReport.set(new LastPx(0));
		executionReport.set(new CumQty(totalFilledAmt));
		executionReport.set(new AvgPx(avgFilledPrice));
		if ( newOrderSingle != null )
		{
			executionReport.set(newOrderSingle.getOrdType(), newOrderSingle.getOrigOrdType());
			copyOrderAttributes(newOrderSingle, executionReport);
			setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
			//Add account info only if newOrderSingle is not null
			setAccount(executionReport, newOrderSingle, request, null);
		}
		else
		{
			copyOrderAttributesFromDealingPrice(request, executionReport, false);
			setHeader(executionReport, TargetSubID.FIELD, request.getCounterparty().getShortName());
		}

		if ( dealingPrice.isStopLossTriggered() )
		{
			executionReport.set(new WorkingIndicator(true));
		}
		UTIWorkFlowHelper.updateExecutionReport(executionReport, request);
		UPIWorkFlowHelper.updateExecutionReport(executionReport, request);
		RTNWorkFlowHelper.updateExecutionReport(executionReport, request);
		return executionReport;
	}

	/**
	 * GTD Order expired notification.
	 */
	public ExecutionReport getOrderExpiryExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		Object object = wf.getObject();
		Request request = null;
		if ( object instanceof Trade )
		{
			request = ((Trade) object).getRequest();
		}
		else
		{
			request = (Request) object;
		}
		FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		int bidOfferMode = dealingPrice.getBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) dealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		double orderAmount = dealingPrice.getDealtAmount();
		double avgFilledPrice = (Double) wf.getParameterValue(FixConstants.AVG_FILL_PRICE);
		double unfilledAmount = (Double) wf.getParameterValue(FixConstants.UNFILLED_AMT);
		double totalFilledAmt = (Double) wf.getParameterValue(FixConstants.FILLED_AMT);
		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new ClOrdID(request.getExternalRequestId()));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID(request.getTransactionID()));
		executionReport.set(FixUtilC.getInstance().getOrdType(request));
		executionReport.set(new LastPx(0));
		if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
		{
			executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
		}
		else
		{
			executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
		}
		if ( newOrderSingle != null )
		{
			copyOrderAttributes(newOrderSingle, executionReport);
		}
		else
		{
			copyOrderAttributesFromDealingPrice(request, executionReport, false);
		}
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, request.getCreatedDate(), true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new LeavesQty(unfilledAmount));
		executionReport.set(FixConstants.EXEC_TYPE_EXPIRED);
		executionReport.set(FixConstants.ORDER_STATUS_EXPIRED);
		executionReport.set(new CumQty(totalFilledAmt));
		executionReport.set(new AvgPx(avgFilledPrice));
		if ( newOrderSingle != null )
		{
			setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
			//Add account info only if newOrderSingle is not null
			setAccount(executionReport, newOrderSingle, request, null);
		}
		UTIWorkFlowHelper.updateExecutionReport(executionReport, request);
		UPIWorkFlowHelper.updateExecutionReport(executionReport, request);
		RTNWorkFlowHelper.updateExecutionReport(executionReport, request);
		return executionReport;
	}

	/**
	 * ESP Trade verification/rejection notification for PQ orders
	 */
	public ExecutionReport getExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{

		FXSingleLeg trade = (FXSingleLeg) wf.getObject();
		Request request = trade.getRequest();
		String counterPartyBOrg = trade.getCounterpartyB().getOrganization().getShortName();
		FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		FXPaymentParameters fxPayment = trade.getFXLeg().getFXPayment();
		FXLegDealingPrice fxLegAcceptedDealingPrice = (FXLegDealingPrice) dealingPrice.getAcceptedDealingPrice();
		RequestPriceFacade rpf = (RequestPriceFacade) dealingPrice.getFacade(RequestPriceFacade.FACADE_NAME);
		int bidOfferMode = dealingPrice.getAcceptedPriceBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXRate fxRate = fxPayment.getFXRate();
		boolean isDealtCcy1 = fxLegAcceptedDealingPrice.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
		double filledPrice = fxRate.getRate();
		double spotRate = fxRate.getSpotRate();
		double spotForwardPoints = fxRate.getForwardPoints();
		double filledAmt = isDealtCcy1 ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount();
		double orderAmount = dealingPrice.getDealtAmount();
		double totalFilledAmt = rpf.getVerifiedAmount();
		double avgFilledPrice = rpf.getAverageFillPrice();
		double settledAmt = isDealtCcy1 ? fxPayment.getCurrency2Amount() : fxPayment.getCurrency1Amount();
		String settledCcy = isDealtCcy1 ? fxPayment.getCurrency2().getISOName() : fxPayment.getCurrency1().getISOName();
		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		if ( newOrderSingle != null )
		{
			executionReport.set(newOrderSingle.getOrdType(), newOrderSingle.getOrigOrdType());
			if ( newOrderSingle.isSetAccount() )
			{
				executionReport.set(newOrderSingle.getAccount());
			}
			if ( newOrderSingle.isSetTimeInForce() )
			{
				executionReport.set(newOrderSingle.getTimeInForce());
			}
			else
			{
				executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.IMMEDIATE_OR_CANCEL));
			}
		}
		else
		{
			executionReport.set(FixConstants.ORD_TYPE_PREVIOUSLY_QUOTED);
			executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.IMMEDIATE_OR_CANCEL));
		}

		executionReport.set(new ClOrdID(request.getExternalRequestId()));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID((trade.getTransactionID())));
		if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
		{
			executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
		}
		else
		{
			executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
		}
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		//executionReport.set( new FutSettDate( new SimpleDateFormat( FixConstants.DATE_TIME_FORMAT ).format( dealingPrice.getValueDate().asJdkDate() ) ) );
		//executionReport.set( new TradeDate( new SimpleDateFormat( FixConstants.DATE_TIME_FORMAT ).format( trade.getTradeDate().asJdkDate() ) ) );
		executionReport.set(new FutSettDate(dealingPrice.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
		executionReport.set(new TradeDate(trade.getTradeDate().getFormattedDate(IdcDate.YYYY_MM_DD)));

		executionReport.setUtcTimeStamp(TransactTime.FIELD, getTransactTimeForExecutionReport(trade), true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new OrderQty(orderAmount));
		executionReport.set(new Price(filledPrice));

		RequestStateFacade facade = ((RequestStateFacade) request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE));

		if ( facade.isDeclined() )
		{
			String rejectReason = trade.getWorkflowStateMap().getWorkflowCodeArgument();
			executionReport.set(FixConstants.LEAVES_QTY_ZERO);
			executionReport.set(FixConstants.EXEC_TYPE_REJECTED);
			executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
			executionReport.set(FixConstants.CUM_QTY_ZERO);
			executionReport.set(FixConstants.AVG_PX_REJECT);
			executionReport.set(new LastSpotRate(0));
			executionReport.set(new LastForwardPoints(0));
			executionReport.set(new LastPx(0));
			FixUtilC.getInstance().setRejectReason(executionReport, rejectReason);//sets error code and error reason
		}
		else
		{
			//Set other quantities and prices
			int version = FixUtilC.getInstance().getVersion(sessionID);
			executionReport.set(new LeavesQty((orderAmount - totalFilledAmt)));
			executionReport.set(new CumQty(totalFilledAmt));
			executionReport.set(new LastQty(filledAmt));
			executionReport.set(new AvgPx(avgFilledPrice));
			executionReport.set(FixConstants.EXEC_TYPE_TRADE);
			executionReport.set(new LastSpotRate(spotRate));
			executionReport.set(new LastForwardPoints(spotForwardPoints));
			executionReport.set(new LastPx(filledPrice));
			executionReport.set(new SettlCurrAmt(settledAmt));
			executionReport.set(new SettlCurrency(settledCcy));
			if ( facade.isPartial() )
			{
				if ( version == 42 )
				{
					executionReport.set(FixConstants.EXEC_TYPE_PARTIAL);
				}
				else
				{
					executionReport.set(FixConstants.EXEC_TYPE_TRADE);
				}
				executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
			}
			else if ( facade.isAcceptVerified() )
			{
				if ( version == 42 )
				{
					executionReport.set(FixConstants.EXEC_TYPE_FILL);
				}
				else
				{
					executionReport.set(FixConstants.EXEC_TYPE_TRADE);
				}
				executionReport.set(FixConstants.ORDER_STATUS_FILLED);
			}
		}

		executionReport.getHeader().setString(OnBehalfOfCompID.FIELD, counterPartyBOrg);

		setAccount(executionReport, newOrderSingle, request, trade);

		if ( newOrderSingle != null )
		{
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		}
		else
		{
			setHeader(executionReport, TargetSubID.FIELD, trade.getCounterpartyA().getShortName());
		}

		setHeader(executionReport, SenderSubID.FIELD, trade.getCounterpartyB().getShortName());

		if ( trade.getMaskedLP() != null && configMbean.isSendMaskLP(trade.getCounterpartyA().getOrganization().getShortName()) )
		{
			setHeader(executionReport, OnBehalfOfSubID.FIELD, trade.getMaskedLP());
		}
		UTIWorkFlowHelper.updateExecutionReport(executionReport, request);
		UPIWorkFlowHelper.updateExecutionReport(executionReport, request);
		RTNWorkFlowHelper.updateExecutionReport(executionReport, request);
		
		return executionReport;
	}

	public com.integral.fix.client.message.Quote getRFSQuote( Quote quote, String quoteRequestID, String senderSubID, String onBehalfOfCompID, String OnBehalfOfSenderSubID, SessionID sessionID )
	{
		com.integral.fix.client.message.Quote fixQuote = FIXMessageFactory.getInstance().newQuote(sessionID);

		fixQuote.set(new SecurityType(SecurityType.FOREIGN_EXCHANGE_CONTRACT));
		fixQuote.set(new QuoteID(quote.getGUID()));
		fixQuote.set(new QuoteReqID(quoteRequestID));

		SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss.SSS");
		Expiration expirationObj = quote.getExpiration();
		long milliSeconds = expirationObj.getSeconds() * 1000;
		String expiryTime = formatter.format(new Date(milliSeconds));
		StringBuilder stbr = new StringBuilder(20);
		stbr.append("00000000-").append(expiryTime);
		fixQuote.setString(ValidUntilTime.FIELD, stbr.toString());
		fixQuote.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		Request request = quote.getRequest();
		if(ISCommonConstants.DEPOSITS.equals(request.getSubTradeType())){
			fixQuote.set(FixConstants.DEPOSIT);
		}
		boolean isTradable = FixConstants.RFS_QUOTE_TYPE_TRADABLE.equals(quote.getQuoteClassification().getShortName());
		if ( isTradable )
		{
			fixQuote.set(new QuoteType(QuoteType.TRADEABLE));
		}
		else
		{
			fixQuote.set(new QuoteType(QuoteType.INDICATIVE));
		}

		Iterator prices = quote.getQuotePrices().iterator();
		Currency ccy = null;
		String baseCcy = null;
		String termCcy = null;
		String dealtCcy = null;
		while ( prices.hasNext() )
		{

			FXLegDealingPrice dp = (FXLegDealingPrice) prices.next();
			FXDealingPriceElement dpe = (FXDealingPriceElement) dp.getPriceElement();
			FXPrice price = dpe.getFXPrice();
			FXRate fxRate = dp.getFXRate();
			FXLegDealingPrice request_dp = null;

			if ( ccy == null && fxRate != null )
			{
				baseCcy = fxRate.getBaseCurrency().getShortName();
				termCcy = dp.getFXRate().getVariableCurrency().getShortName();
				fixQuote.set(new Symbol(new StringBuilder(baseCcy).append('/').append(termCcy).toString()));
				dealtCcy = dp.getDealtCurrency().getShortName();
				ccy = new Currency(dealtCcy);
				fixQuote.set(ccy);
			}else if(ccy == null){
				fixQuote.set(new Symbol(quote.getCurrencyPair().getName()));
				dealtCcy = dp.getDealtCurrency().getShortName();
				ccy = new Currency(dealtCcy);
				fixQuote.set(ccy);
			}

			if ( dp.getName().equals(FixConstants.SINGLE_LEG) || dp.getName().equals(FixConstants.NEAR_LEG) )
			{
				request_dp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
				if ( request_dp == null )
				{
					request_dp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.NEAR_LEG);
				}
				fixQuote.setSettlDate(dp.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD));
				if ( dp.getFixingDate() != null )
				{
					fixQuote.set(new MaturityDate(dp.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
				}
				fixQuote.set(new OrdType(OrdType.PREVIOUSLY_QUOTED));
				if ( request_dp.getBidOfferMode() == DealingPrice.TWO_WAY )
				{
					FXRate bid = price.getBidFXRate();
					FXRate offer = price.getOfferFXRate();
					if(bid != null) {
						fixQuote.set(new BidSpotRate(bid.getSpotRate()));
						fixQuote.set(new BidForwardPoints(bid.getForwardPoints()));
						fixQuote.set(new BidPx(bid.getRate()));
					}
					if(offer != null) {
						fixQuote.set(new OfferSpotRate(offer.getSpotRate()));
						fixQuote.set(new OfferForwardPoints(offer.getForwardPoints()));
						fixQuote.set(new OfferPx(offer.getRate()));
					}
					fixQuote.set(new BidSize(dp.getDealtAmount()));
					fixQuote.set(new OfferSize(dp.getDealtAmount()));
					fixQuote.setField(new CharField(54));
				}
				else if ( request_dp.getBidOfferMode() == DealingPrice.BID )
				{
					FXRate bid = price.getBidFXRate();
					fixQuote.set(new BidSpotRate(bid.getSpotRate()));
					fixQuote.set(new BidForwardPoints(bid.getForwardPoints()));
					fixQuote.set(new BidPx(bid.getRate()));
					fixQuote.set(new BidSize(dp.getDealtAmount()));
					if ( baseCcy.equals(dealtCcy) )
					{
						fixQuote.setField(new CharField(54, Side.SELL));
					}
					else
					{
						fixQuote.setField(new CharField(54, Side.BUY));
					}
				}
				else if ( request_dp.getBidOfferMode() == DealingPrice.OFFER )
				{
					FXRate offer = price.getOfferFXRate();
					fixQuote.set(new OfferSpotRate(offer.getSpotRate()));
					fixQuote.set(new OfferForwardPoints(offer.getForwardPoints()));
					fixQuote.set(new OfferPx(offer.getRate()));
					fixQuote.set(new OfferSize(dp.getDealtAmount()));
					if ( baseCcy.equals(dealtCcy) )
					{
						fixQuote.setField(new CharField(54, Side.BUY));
					}
					else
					{
						fixQuote.setField(new CharField(54, Side.SELL));
					}
				}

				FXRate mid = price.getMidFXRate();
				if ( mid != null )
				{
					fixQuote.setDouble(631, mid.getRate());
				}

			}
			else
			{
				request_dp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.FAR_LEG);
				fixQuote.setSettlDate2(dp.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD));
				if (dp.getFixingDate() != null) 
				{
					fixQuote.setString(8541, dp.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD));
				}
				fixQuote.set(new OrdType(OrdType.FOREX_SWAP));
				if ( request_dp.getBidOfferMode() == DealingPrice.TWO_WAY )
				{
					FXRate bid = price.getBidFXRate();
					FXRate offer = price.getOfferFXRate();
					fixQuote.set(new BidForwardPoints2(bid.getForwardPoints()));
					fixQuote.set(new OfferForwardPoints2(offer.getForwardPoints()));
					fixQuote.setDouble(7551, dp.getDealtAmount());//bidsize2
					fixQuote.setDouble(7552, dp.getDealtAmount());//offersize2
					fixQuote.setField(new CharField(54));

				}
				else if ( request_dp.getBidOfferMode() == DealingPrice.BID )
				{
					FXRate bid = price.getBidFXRate();
					fixQuote.set(new BidForwardPoints2(bid.getForwardPoints()));
					fixQuote.setDouble(7551, dp.getDealtAmount());//bidsize2
					if ( baseCcy.equals(dealtCcy) )
					{
						fixQuote.setField(new CharField(54, Side.SELL));
					}
					else
					{
						fixQuote.setField(new CharField(54, Side.BUY));
					}
				}
				else if ( request_dp.getBidOfferMode() == DealingPrice.OFFER )
				{
					FXRate offer = price.getOfferFXRate();
					fixQuote.set(new OfferForwardPoints2(offer.getForwardPoints()));
					fixQuote.setDouble(7552, dp.getDealtAmount());//offersize2
					if ( baseCcy.equals(dealtCcy) )
					{
						fixQuote.setField(new CharField(54, Side.BUY));
					}
					else
					{
						fixQuote.setField(new CharField(54, Side.SELL));
					}
				}

				FXRate mid = price.getMidFXRate();
				if ( mid != null )
				{
					fixQuote.setDouble(7631, mid.getRate());
				}

			}
		}
		if ( senderSubID != null )
		{
			fixQuote.getHeader().setField(new TargetSubID(senderSubID));
		}
		else
		{
			if ( OnBehalfOfSenderSubID != null )
			{
				fixQuote.getHeader().setField(new DeliverToSubID(OnBehalfOfSenderSubID));
			}
			if ( onBehalfOfCompID != null )
			{
				fixQuote.getHeader().setField(new DeliverToCompID(onBehalfOfCompID));
			}
		}
		String orgName = quote.getOrganization().getShortName();
		String maskedName = FixUtilC.getInstance().getMaskedName(request.getOrganization().getShortName(), orgName);
		FIXHandlerFactory.getMifidHandler().populateQuote(fixQuote,quote);
		populateSalesSpreadDetails(fixQuote, quote);
		fixQuote.getHeader().setField(new OnBehalfOfCompID(maskedName));
		if(configMbean.isSendSettlementInstructionInQRAndER(request.getOrganization().getShortName()) && !StringUtils.isNullOrEmptyString(quote.getNotes()))
		{
			fixQuote.setSettlementInst(quote.getNotes());
		}
		if(quote.getDepositsParam() != null){
			DepositsParam dp = quote.getDepositsParam();
			if(dp.getAnnualInterestRate() != null) fixQuote.setDouble(FixConstants.FIELD_DEP_ANN_INT_RATE, dp.getAnnualInterestRate());
			if(dp.getInterest() != null) fixQuote.setDouble(FixConstants.FIELD_DEP_ACT_INT, dp.getInterest());
			if(dp.getNumberOfDays() != null) fixQuote.getMessage().setInt(FixConstants.FIELD_DEP_NUM_DAYS, dp.getNumberOfDays());
		}
		return fixQuote;
	}
	
	
	
	public com.integral.fix.client.message.Quote getRFSQuote(Request request, FXPriceBook priceBook, String quoteRequestID, String senderSubID, String onBehalfOfCompID, String OnBehalfOfSenderSubID, SessionID sessionID )
	{
		Collection<com.integral.aggregation.price.FXPrice>  bids = priceBook.getBids();
		Collection<com.integral.aggregation.price.FXPrice>  offers =  priceBook.getOffers();
		com.integral.fix.client.message.Quote fixQuote = FIXMessageFactory.getInstance().newQuote(sessionID);
		if(ISCommonConstants.DEPOSITS.equals(request.getSubTradeType())){
			fixQuote.set(FixConstants.DEPOSIT);
		}else {
			fixQuote.set(new SecurityType(SecurityType.FOREIGN_EXCHANGE_CONTRACT));
		}
		fixQuote.set(new QuoteID(priceBook.getBookId()));
		fixQuote.set(new QuoteReqID(quoteRequestID));
		int timeToLiveInSeconds = priceBook.getExpiryTime();
		Expiration expirationObj = new ExpirationC();
		expirationObj.setSeconds(timeToLiveInSeconds);
		SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss.SSS");
		//Expiration expirationObj = quote.getExpiration();
		long milliSeconds = expirationObj.getSeconds() * 1000;
		String expiryTime = formatter.format(new Date(milliSeconds));
		StringBuilder stbr = new StringBuilder(20);
		stbr.append("00000000-").append(expiryTime);
		fixQuote.setString(ValidUntilTime.FIELD, stbr.toString());
		fixQuote.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		boolean isTradable = !priceBook.isStale();
		if ( isTradable )
		{
			fixQuote.set(new QuoteType(QuoteType.TRADEABLE));
		}
		else
		{
			fixQuote.set(new QuoteType(QuoteType.INDICATIVE));
		}
		
		CurrencyPair ccyPair = priceBook.getCurrencyPair();
		com.integral.finance.currency.Currency baseCurrency = ccyPair.getBaseCurrency();
		com.integral.finance.currency.Currency variableCurrency = ccyPair.getVariableCurrency();
		String ccypairstr = new StringBuilder(baseCurrency.getShortName()).
				append('/').append(variableCurrency.getShortName()).toString();
		fixQuote.set(new Symbol(ccypairstr));
		FXLegDealingPrice fdp = RFSFXTradeCalculatorC.getRequestDealingPrice(request, true);
		com.integral.finance.currency.Currency dealtCurrency = fdp.getDealtCurrency();
		String dealtCurrencyStr = dealtCurrency.getShortName();
		fixQuote.set(new Currency(dealtCurrencyStr));
		boolean swap = request.getRequestAttributes().isSwap();
		if (swap) {
			fixQuote.set(new OrdType(OrdType.FOREX_SWAP));
			Collection<com.integral.aggregation.price.FXPrice> farBids = ((SwapFXPriceBook)priceBook).getFarBids();
			Collection<com.integral.aggregation.price.FXPrice> farOffers = ((SwapFXPriceBook)priceBook).getFarOffers();
			FXLegDealingPrice neardp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.NEAR_LEG);
			FXLegDealingPrice fardp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.FAR_LEG);
			if (neardp != null && fardp != null) {
				boolean baseSameAsDealt = true;
				if (FXLegDealingPrice.TWO_WAY == fardp.getBidOfferMode()) {
					// client side Two Way
					fixQuote.setCharField(new CharField(Side.FIELD));
				} else if (FXLegDealingPrice.BID == fardp.getBidOfferMode()) {
					// client side SELL
					if (baseCurrency.isSameAs(dealtCurrency)) {
						fixQuote.setCharField(new CharField(Side.FIELD, Side.SELL));
					} else {
						fixQuote.setCharField(new CharField(Side.FIELD, Side.BUY));
						baseSameAsDealt = false;
					}
				} else if (FXLegDealingPrice.OFFER == fardp.getBidOfferMode()) {
					// client side BUY
					if (baseCurrency.isSameAs(dealtCurrency)) {
						fixQuote.setCharField(new CharField(Side.FIELD, Side.BUY));
					} else {
						fixQuote.setCharField(new CharField(Side.FIELD, Side.SELL));
						baseSameAsDealt = false;
					}
				}
				String nearSettleDateStr = neardp.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD);
				String farSettleDateStr = fardp.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD);
				fixQuote.setSettlDate(nearSettleDateStr);
				fixQuote.setSettlDate2(farSettleDateStr);
				if (neardp.getFixingDate() != null) {
					fixQuote.set(new MaturityDate(neardp.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
				}
				if (fardp.getFixingDate() != null) {
					fixQuote.setString(8541, fardp.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD));
				}
				int nearBidSize = 0;
				int farOfferSize = 0;
				int nearOfferSize = 0;
				int farBidSize = 0;
				Iterator<com.integral.aggregation.price.FXPrice> nearBidIetrator = null;
				Iterator<com.integral.aggregation.price.FXPrice> farOfferIetrator = null;
				Iterator<com.integral.aggregation.price.FXPrice> nearOfferIetrator = null;
				Iterator<com.integral.aggregation.price.FXPrice> farBidIetrator = null;
				if (bids != null && bids.size() > 0) {
					nearBidSize = bids.size();
					nearBidIetrator = bids.iterator();
				}
				if (farOffers != null && farOffers.size() > 0) {
					farOfferSize = farOffers.size();
					farOfferIetrator = farOffers.iterator();
				}
				
				if (offers != null && offers.size() > 0) {
					nearOfferSize = offers.size();
					nearOfferIetrator = offers.iterator();
				}
				if (farBids != null && farBids.size() > 0) {
					farBidSize = farBids.size();
					farBidIetrator = farBids.iterator();
				}
				int noOfLegs = Math.max(nearBidSize + farOfferSize, nearOfferSize + farBidSize);
				int noOfBidOfferLps = Math.min(nearBidSize, farOfferSize);
				
				Map<Integer, Tuple<QuoteNoLegs, QuoteNoLegs>> legMap = new HashMap<Integer, Tuple<QuoteNoLegs, QuoteNoLegs>>();
				for (int i = 0; i < noOfBidOfferLps; ++i) {
					QuoteNoLegs nearLeg = fixQuote.createNoLegs();
					QuoteNoLegs farLeg = fixQuote.createNoLegs();
					fixQuote.addGroup(nearLeg);
					fixQuote.addGroup(farLeg);
					Tuple<QuoteNoLegs, QuoteNoLegs> legTuple = new Tuple<QuoteNoLegs, QuoteNoLegs>(nearLeg, farLeg);
					legMap.put(i, legTuple);
					com.integral.aggregation.price.FXPrice nearBidPrice = nearBidIetrator.next();
					com.integral.aggregation.price.FXPrice farOfferPrice = farOfferIetrator.next();
					// near leg Bid price
					String nearBidQuoteId = nearBidPrice.getQuoteId();
					double nearBidRate = nearBidPrice.getRate();
					double nearBidSpotRate = nearBidPrice.getSpotRate();
					double nearBidDealtAmount = nearBidPrice.getLimit();
					String nearBidLP = nearBidPrice.getLPName();
					double nearMidRate = nearBidPrice.getMidRate();
					double nearBidForwardPoint = nearBidPrice.getForwardPoint();
					nearLeg.setString(11016, nearBidQuoteId);
					nearLeg.setLegQty(new LegQty(nearBidDealtAmount));
					nearLeg.set(new LegBidPx(nearBidRate));
					nearLeg.setDouble(7914, nearBidSpotRate);
					//nearLeg.setLegPrice(new LegPrice(nearBidRate));
					nearLeg.set(new LegSymbol(ccypairstr));
					nearLeg.set(new LegSettlDate(nearSettleDateStr));
					//nearLeg.set(new LegSide('0'));
					nearLeg.set(new LegCurrency(dealtCurrencyStr));
					nearLeg.setDouble(1067, nearBidForwardPoint);
					nearLeg.setDouble(11020, nearMidRate);
					nearLeg.setString(11018, nearBidLP);
					// Far Leg Offer price
					String farOfferQuoteId = farOfferPrice.getQuoteId();
					double farOfferRate = farOfferPrice.getRate();
					double farOfferSpotRate = farOfferPrice.getSpotRate();
					double farOfferDealtAmount = farOfferPrice.getLimit();
					String farOfferLP = farOfferPrice.getLPName();
					double farMidRate = farOfferPrice.getMidRate();
					double farOfferForwardPoint = farOfferPrice.getForwardPoint();
					farLeg.setString(11017, farOfferQuoteId);
					farLeg.setLegQty(new LegQty(farOfferDealtAmount));
					farLeg.set(new LegOfferPx(farOfferRate));
					farLeg.setDouble(7915, farOfferSpotRate);
					//farLeg.setLegPrice(new LegPrice(farOfferRate));
					farLeg.set(new LegSymbol(ccypairstr));
					farLeg.set(new LegSettlDate(farSettleDateStr));
					//nearLeg.set(new LegSide('0'));
					farLeg.set(new LegCurrency(dealtCurrencyStr));
					farLeg.setDouble(1068, farOfferForwardPoint);
					farLeg.setDouble(11021, farMidRate);
					farLeg.setString(11019, farOfferLP);
					if (nearLeg.isSetLegBidPx() && farLeg.isSetLegOfferPx() && 
							nearLeg.isSetLegOfferPx() && farLeg.isSetLegBidPx()) {
						nearLeg.set(new LegSide());
						farLeg.set(new LegSide());
						
					} else if (nearLeg.isSetLegBidPx() && farLeg.isSetLegOfferPx()) {
						// side is from client
						if (baseSameAsDealt) {
							nearLeg.set(new LegSide('2'));
							farLeg.set(new LegSide('1'));
						} else {
							nearLeg.set(new LegSide('1'));
							farLeg.set(new LegSide('2'));
						}
						
					} else if (nearLeg.isSetLegOfferPx() && farLeg.isSetLegBidPx()) {
						// side is from client
						if (baseSameAsDealt) {
							nearLeg.set(new LegSide('1'));
							farLeg.set(new LegSide('2'));
						} else {
							nearLeg.set(new LegSide('2'));
							farLeg.set(new LegSide('1'));
						}
					}
				}
				
				int noOfOfferBidLps = Math.min(nearOfferSize, farBidSize);
				for (int i = 0; i < noOfOfferBidLps; ++i) {
					QuoteNoLegs nearLeg = null;
					QuoteNoLegs farLeg = null;
					Tuple<QuoteNoLegs, QuoteNoLegs> legTuple = legMap.get(i);
					if (legTuple == null) {
						nearLeg = fixQuote.createNoLegs();
						farLeg = fixQuote.createNoLegs();
						fixQuote.addGroup(nearLeg);
						fixQuote.addGroup(farLeg);
					} else {
						nearLeg = legTuple.first;
						farLeg = legTuple.second;
					}
					com.integral.aggregation.price.FXPrice nearOfferPrice = nearOfferIetrator.next();
					com.integral.aggregation.price.FXPrice farBidPrice = farBidIetrator.next();
					// near leg offer price
					String nearOfferQuoteId = nearOfferPrice.getQuoteId();
					double nearOfferRate = nearOfferPrice.getRate();
					double nearOfferSpotRate = nearOfferPrice.getSpotRate();
					double nearOfferDealtAmount = nearOfferPrice.getLimit();
					String nearOfferLP = nearOfferPrice.getLPName();
					double nearOfferForwardPoint = nearOfferPrice.getForwardPoint();
					double nearLegMidRate = nearOfferPrice.getMidRate();
					nearLeg.setString(11017, nearOfferQuoteId);
					if (!nearLeg.isSetLegQty()) {
						nearLeg.setLegQty(new LegQty(nearOfferDealtAmount));
					}
					nearLeg.set(new LegOfferPx(nearOfferRate));
					nearLeg.setDouble(7915, nearOfferSpotRate);
					
					//nearLeg.setLegPrice(new LegPrice(nearOfferRate));
					if (!nearLeg.isSetLegSymbol()) {
						nearLeg.set(new LegSymbol(ccypairstr));
					}
					if (!nearLeg.isSetLegSettlDate()) {
						nearLeg.set(new LegSettlDate(nearSettleDateStr));
					}
					//nearLeg.set(new LegSide('0'));
					if (!nearLeg.isSetLegCurrency()) {
						nearLeg.set(new LegCurrency(dealtCurrencyStr));
					}
					nearLeg.setDouble(1068, nearOfferForwardPoint);
					nearLeg.setDouble(11021, nearLegMidRate);
					nearLeg.setString(11019, nearOfferLP);
					
					// Far Leg Bid price
					String farBidQuoteId = farBidPrice.getQuoteId();
					double farBidRate = farBidPrice.getRate();
					double farBidSpotRate = farBidPrice.getSpotRate();
					double farBidDealtAmount = farBidPrice.getLimit();
					String farBidLP = farBidPrice.getLPName();
					double farBidForwardPoint = farBidPrice.getForwardPoint();
					double farLegMidRate = farBidPrice.getMidRate();
					farLeg.setString(11016, farBidQuoteId);
					if (!farLeg.isSetLegQty()) {
						farLeg.setLegQty(new LegQty(farBidDealtAmount));
					}
					
					farLeg.set(new LegBidPx(farBidRate));
					farLeg.setDouble(7914, farBidSpotRate);
					//farLeg.setLegPrice(new LegPrice(farBidRate));
					if (!farLeg.isSetLegSymbol()) {
						farLeg.set(new LegSymbol(ccypairstr));
					}
					if (!farLeg.isSetLegSettlDate()) {
						farLeg.set(new LegSettlDate(farSettleDateStr));
					}
					if (!farLeg.isSetLegCurrency()) {
						farLeg.set(new LegCurrency(dealtCurrencyStr));
					};
					farLeg.setDouble(11020, farLegMidRate);
					farLeg.setDouble(1067, farBidForwardPoint);
					farLeg.setString(11018, farBidLP);
					
					if (nearLeg.isSetLegBidPx() && farLeg.isSetLegOfferPx() && 
							nearLeg.isSetLegOfferPx() && farLeg.isSetLegBidPx()) {
						nearLeg.set(new LegSide());
						farLeg.set(new LegSide());
						
					} else if (nearLeg.isSetLegBidPx() && farLeg.isSetLegOfferPx()) {
						// side is from client
						if (baseSameAsDealt) {
							nearLeg.set(new LegSide('2'));
							farLeg.set(new LegSide('1'));
						} else {
							nearLeg.set(new LegSide('1'));
							farLeg.set(new LegSide('2'));
						}
						
					} else if (nearLeg.isSetLegOfferPx() && farLeg.isSetLegBidPx()) {
						if (baseSameAsDealt) {
							nearLeg.set(new LegSide('1'));
							farLeg.set(new LegSide('2'));
						} else {
							nearLeg.set(new LegSide('2'));
							farLeg.set(new LegSide('1'));
						}
					}
				}
				
			}
			
		} else {
			// single leg
			fixQuote.set(new OrdType(OrdType.PREVIOUSLY_QUOTED));
			FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
			if (dp != null) {
				boolean baseSameAsDealt = true;
				if (FXLegDealingPrice.TWO_WAY == dp.getBidOfferMode()) {
					// client side Two Way
					fixQuote.setCharField(new CharField(Side.FIELD));
				} else if (FXLegDealingPrice.BID == dp.getBidOfferMode()) {
					// client side SELL
					if (baseCurrency.isSameAs(dealtCurrency)) {
						fixQuote.setCharField(new CharField(Side.FIELD, Side.SELL));
					} else {
						fixQuote.setCharField(new CharField(Side.FIELD, Side.BUY));
						baseSameAsDealt = false;
					}
				} else if (FXLegDealingPrice.OFFER == dp.getBidOfferMode()) {
					// client side BUY
					if (baseCurrency.isSameAs(dealtCurrency)) {
						fixQuote.setCharField(new CharField(Side.FIELD, Side.BUY));
					} else {
						fixQuote.setCharField(new CharField(Side.FIELD, Side.SELL));
						baseSameAsDealt = false;
					}
				}
				String settleDateStr = dp.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD);
				fixQuote.setSettlDate(settleDateStr);
				if ( dp.getFixingDate() != null ) {
					fixQuote.set(new MaturityDate(dp.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
				}
				Iterator<com.integral.aggregation.price.FXPrice> bidIterator = null;
				Iterator<com.integral.aggregation.price.FXPrice> offerIterator = null;
				int bidSize = 0;
				int offerSize = 0;
				if (bids != null && bids.size() > 0) {
					bidIterator = bids.iterator();
					bidSize = bids.size();
				}
				if (offers != null && offers.size() > 0) {
					offerIterator = offers.iterator();
					offerSize = offers.size();
				}
				int noOfLegs = Math.max(bidSize, offerSize);
				for (int i = 0; i < noOfLegs; ++i) {
					QuoteNoLegs qLeg = fixQuote.createNoLegs();
					fixQuote.addGroup(qLeg);
					if (bidSize > i && bidIterator != null) {
						com.integral.aggregation.price.FXPrice bidPrice = bidIterator.next();
						double bidDealtAmount = bidPrice.getLimit();
						String lp = bidPrice.getLPName();
						String quoteId = bidPrice.getQuoteId();
						double bidForwardPoint = bidPrice.getForwardPoint();
						double midRate = bidPrice.getMidRate();
						double settleAmount = bidPrice.getSettleAmount();
						double bidRate = bidPrice.getRate();
						double bidSpotRate = bidPrice.getSpotRate();
						int tier = bidPrice.getTier();
						qLeg.setString(11016, quoteId);
						qLeg.setLegQty(new LegQty(bidDealtAmount));
						qLeg.set(new LegBidPx(bidRate));
						qLeg.setDouble(7914, bidSpotRate);
						//qLeg.setLegPrice(new LegPrice(bidRate));
						qLeg.set(new LegSymbol(ccypairstr));
						qLeg.set(new LegSettlDate(settleDateStr));
						//qLeg.set(new LegSide('0'));
						qLeg.set(new LegCurrency(dealtCurrencyStr));
						qLeg.setDouble(1067, bidForwardPoint);
						qLeg.setDouble(11020, midRate);
						qLeg.setString(11018, lp);
					}
					if (offerSize > i && offerIterator != null) {
						com.integral.aggregation.price.FXPrice offerPrice = offerIterator.next();
						double offerDealtAmount = offerPrice.getLimit();
						String lp = offerPrice.getLPName();
						int lpTier = offerPrice.getLPTier();
						String quoteId = offerPrice.getQuoteId();
						double offerRate = offerPrice.getRate();
						double offerForwardPoint = offerPrice.getForwardPoint();
						double midRate = offerPrice.getMidRate();
						double settleAmount = offerPrice.getSettleAmount();
						double offerSpotRate = offerPrice.getSpotRate();
						qLeg.setString(11017, quoteId);
						if (!qLeg.isSetLegQty()) {
							qLeg.setLegQty(new LegQty(offerDealtAmount));
						}
						qLeg.set(new LegOfferPx(offerRate));
						qLeg.setDouble(7915, offerSpotRate);
						//qLeg.setLegPrice(new LegPrice(offerRate));
						if (!qLeg.isSetLegSymbol()) {
							qLeg.set(new LegSymbol(ccypairstr));
						}
						if (!qLeg.isSetLegSettlDate()) {
							qLeg.set(new LegSettlDate(settleDateStr));
						}
						
						if (!qLeg.isSetLegCurrency()) {
							qLeg.set(new LegCurrency(dealtCurrencyStr));
						}
						
						qLeg.setDouble(1068, offerForwardPoint);
						qLeg.setDouble(11021, midRate);
						qLeg.setString(11019, lp);
					}
					if (qLeg.isSetLegBidPx() && qLeg.isSetLegOfferPx()) {
						qLeg.set(new LegSide());
					} else if (qLeg.isSetLegBidPx()) {
						if (baseSameAsDealt) {
							qLeg.set(new LegSide('2'));
						} else {
							qLeg.set(new LegSide('1'));
						}
						
					} else if (qLeg.isSetLegOfferPx()) {
						if (baseSameAsDealt) {
							qLeg.set(new LegSide('1'));
						} else {
							qLeg.set(new LegSide('2'));
						}
					}
				}
			}
		}
		

		if ( senderSubID != null )
		{
			fixQuote.getHeader().setField(new TargetSubID(senderSubID));
		}
		else
		{
			if ( OnBehalfOfSenderSubID != null )
			{
				fixQuote.getHeader().setField(new DeliverToSubID(OnBehalfOfSenderSubID));
			}
			if ( onBehalfOfCompID != null )
			{
				fixQuote.getHeader().setField(new DeliverToCompID(onBehalfOfCompID));
			}
		}
		//String orgName = quote.getOrganization().getShortName();
		//String maskedName = FixUtilC.getInstance().getMaskedName(request.getOrganization().getShortName(), orgName);
		//FIXHandlerFactory.getMifidHandler().populateQuote(fixQuote,quote);
		//populateSalesSpreadDetails(fixQuote, quote);
		//fixQuote.getHeader().setField(new OnBehalfOfCompID(maskedName));
		//if(configMbean.isSendSettlementInstructionInQRAndER(request.getOrganization().getShortName()) && !StringUtils.isNullOrEmptyString(quote.getNotes()))
		//{
			//fixQuote.setSettlementInst(quote.getNotes());
		//}
		return fixQuote;
	}

	

	protected void populateSalesSpreadDetails(com.integral.fix.client.message.Quote fixQuote, Quote quote)
	{
		FIXHandlerFactory.getSalesSpreadHandler().populateQuote(fixQuote,quote);
	}

	public QuoteCancel getQuoteWithdraw( String quoteRequestID, String quoteID, QuoteCancelType quoteCancelType, String senderSubID, String onBehalfOfCompID, String onBehalfOfSenderSubID, String deliverToCompID, SessionID sessionID )
	{
		QuoteCancel quoteCancel = FIXMessageFactory.getInstance().newQuoteCancel(sessionID);
		quoteCancel.set(new QuoteReqID(quoteRequestID));
		quoteCancel.set(new QuoteID(quoteID));
		quoteCancel.set(quoteCancelType);
		quoteCancel.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		if ( onBehalfOfCompID != null && !onBehalfOfCompID.trim().equals("") )
		{
			quoteCancel.getHeader().setField(new DeliverToCompID(onBehalfOfCompID));
		}
		if ( onBehalfOfSenderSubID != null && !onBehalfOfSenderSubID.trim().equals("") )
		{
			quoteCancel.getHeader().setField(new DeliverToSubID(onBehalfOfSenderSubID));
		}
		if ( senderSubID != null && !senderSubID.trim().equals("") )
		{
			quoteCancel.getHeader().setField(new TargetSubID(senderSubID));
		}
		if ( deliverToCompID != null && !deliverToCompID.trim().equals("") )
		{
			quoteCancel.getHeader().setField(new OnBehalfOfCompID(deliverToCompID));
		}
		return quoteCancel;
	}

	/**
	 * Stop order triggered notification
	 */
	public quickfix.Message getOrderTriggeredExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		Request request = (Request) wf.getObject();
		FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		int bidOfferMode = dealingPrice.getBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) dealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new ClOrdID(request.getExternalRequestId()));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID("NONE"));
		if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
		{
			executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
		}
		else
		{
			executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
		}
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, request.getCreatedDate(), true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new OrderQty(dealingPrice.getDealtAmount()));
		executionReport.set(new LeavesQty(dealingPrice.getDealtAmount()));
		executionReport.set(new LastPx(0));
		executionReport.set(FixConstants.LAST_QTY_ZERO);
		executionReport.set(FixConstants.EXEC_TYPE_NEW);
		executionReport.set(FixConstants.ORDER_STATUS_NEW);
		executionReport.set(new WorkingIndicator(true));
		executionReport.set(FixConstants.CUM_QTY_ZERO);
		executionReport.set(new AvgPx(0.0));
		if ( newOrderSingle != null )
		{
			executionReport.set(newOrderSingle.getOrdType(), newOrderSingle.getOrigOrdType());
			copyOrderAttributes(newOrderSingle, executionReport);
			setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		}
		else
		{
			copyOrderAttributesFromDealingPrice(request, executionReport, false);
			setHeader(executionReport, TargetSubID.FIELD, request.getCounterparty().getShortName());
		}
		return executionReport.getMessage();
	}

	/**
	 * Stop order triggered notification
	 */
	public quickfix.Message getOrderRestatedExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		Object object = wf.getObject();
		Request request = null;
		if ( object instanceof Trade )
		{
			request = ((Trade) object).getRequest();
		}
		else
		{
			request = (Request) object;
		}
		FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
		int bidOfferMode = dealingPrice.getBidOfferMode();
		boolean isBid = bidOfferMode == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) dealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set(new ClOrdID(request.getExternalRequestId()));
		executionReport.set(new OrderID(request.getOrderId()));
		executionReport.set(new ExecID("NONE"));
		if ( dealingPrice.getDealtCurrency().isSameAs(fxRate.getCurrencyPair().getBaseCurrency()) )
		{
			executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
		}
		else
		{
			executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
		}
		executionReport.set(new Symbol(FixUtilC.getInstance().getCurrencyPair(request).toString()));
		executionReport.setUtcTimeStamp(TransactTime.FIELD, request.getCreatedDate(), true);
		executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
		executionReport.set(new LeavesQty(0));
		executionReport.set(new LastPx(0));
		executionReport.set(FixConstants.LAST_QTY_ZERO);
		executionReport.set(FixConstants.EXEC_TYPE_RESTATED);
		if ( request.getRequestAttributes().getOrderFilledAmt() == 0 )
		{
			executionReport.set(FixConstants.ORDER_STATUS_NEW);
		}
		else
		{
			executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
		}

		executionReport.set(new WorkingIndicator(true));
		executionReport.set(FixConstants.CUM_QTY_ZERO);
		executionReport.set(new AvgPx(0.0));
		if ( newOrderSingle != null )
		{
			executionReport.set(newOrderSingle.getOrdType(), newOrderSingle.getOrigOrdType());
			copyOrderAttributes(newOrderSingle, executionReport);
			setHeader(newOrderSingle, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
			setHeader(newOrderSingle, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
			setHeader(newOrderSingle, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
		}
		else
		{
			copyOrderAttributesFromDealingPrice(request, executionReport, false);
			setHeader(executionReport, TargetSubID.FIELD, request.getCounterparty().getShortName());
		}
		//override OrderQty set by copyOrderAttributes to restated amount.
		String avblAmt = (String) wf.getParameterValue("AvailableAmount");
		if ( avblAmt != null )
		{
			executionReport.set(new OrderQty(Double.parseDouble(avblAmt)));
		}
		else
		{
			executionReport.set(new OrderQty(dealingPrice.getDealtAmount()));
		}
		return executionReport.getMessage();
	}

	protected void setHeader( com.integral.fix.client.message.Message message, ExecutionReport executionReport, int field1, int field2 )
	{
		if ( message.getHeader().isSetField(field1) )
		{
			try
			{
				executionReport.getHeader().setString(field2, message.getHeader().getString(field1));
			}
			catch ( FieldNotFound fieldNotFound )
			{
				fieldNotFound.printStackTrace();
			}
		}
	}

	protected void setHeader( Message message, ExecutionReport executionReport, int from, int to )
	{
		if ( message.getHeader().isSetField(from) )
		{
			try
			{
				executionReport.getHeader().setString(to, message.getHeader().getString(from));
			}
			catch ( FieldNotFound fieldNotFound )
			{
				fieldNotFound.printStackTrace();
			}
		}
	}

	protected MDEntryPositionNo getMDEntryPositionNo( int size )
	{
		return FixConstants.MDENTRY_POSITIONS[size];
//		switch ( size )
//		{
//		case 1 :
//			return FixConstants.MDENTRY_POSITION_NO_1;
//		case 2 :
//			return FixConstants.MDENTRY_POSITION_NO_2;
//		case 3 :
//			return FixConstants.MDENTRY_POSITION_NO_3;
//		case 4 :
//			return FixConstants.MDENTRY_POSITION_NO_4;
//		case 5 :
//			return FixConstants.MDENTRY_POSITION_NO_5;
//		case 6 :
//			return FixConstants.MDENTRY_POSITION_NO_6;
//		default :
//			return new MDEntryPositionNo(size);
//		}
	}

	protected String getMDRequestID( String mdRequestID )
	{
		if ( mdRequestID.indexOf('@') != -1 )
		{
			return mdRequestID.substring(0, mdRequestID.indexOf('@'));
		}
		return mdRequestID;
	}



	protected void copyOrderAttributes( NewOrderSingle newOrderSingle, ExecutionReport executionReport ) throws FieldNotFound
	{
		if ( newOrderSingle.isSetAccount() )
		{
			executionReport.set(newOrderSingle.getAccount());
		}
		if ( newOrderSingle.isSetPrice() )
		{
			executionReport.set(newOrderSingle.getPrice());
		}
		if ( newOrderSingle.isSetTimeInForce() )
		{
			executionReport.set(newOrderSingle.getTimeInForce());
		}
        else
        {
            if(newOrderSingle.getOrdType().getValue() == OrdType.PREVIOUSLY_QUOTED)
            {
                executionReport.set( new quickfix.field.TimeInForce(quickfix.field.TimeInForce.IMMEDIATE_OR_CANCEL) );
            }
        }
		if ( newOrderSingle.isSetExpireTime() )
		{
			executionReport.set(newOrderSingle.getExpireTime());
		}
		if ( newOrderSingle.isSetMaxShow() )
		{
			executionReport.set(newOrderSingle.getMaxShow());
		}
		if ( newOrderSingle.isSetMinQty() )
		{
			executionReport.set(newOrderSingle.getMinQty());
		}
		if ( newOrderSingle.isSetPegDifference() )
		{
			executionReport.set(newOrderSingle.getPegDifference());
		}
        if(newOrderSingle.getOrdType().getValue() != OrdType.PREVIOUSLY_QUOTED)
        {
            if ( newOrderSingle.isSetExecInst() )
            {
                executionReport.set(newOrderSingle.getExecInst());
            }
        }
		if ( newOrderSingle.isSetStopPx() )
		{
			executionReport.set(newOrderSingle.getStopPx());
		}
		if( newOrderSingle.isSetTrailingDistance()){
			executionReport.setTrailingDistance(newOrderSingle.getTrailingDistance());
		}
		if ( newOrderSingle.isSetClientID() )
		{
			executionReport.set(newOrderSingle.getClientID());
		}
		if ( newOrderSingle.isSetSymbol() )
		{
			executionReport.set(newOrderSingle.getSymbol());
		}
		executionReport.set(newOrderSingle.getSide());
		executionReport.set(newOrderSingle.getOrderQty());
		executionReport.set(newOrderSingle.getCurrency());
		executionReport.set(newOrderSingle.getOrdType());
	}

	public MarketDataSnapshotFullRefresh getQuote( FXPriceBook book, FixSubscription subscription )
	{
		boolean isTradable = countTiers(book) > 0;
		User user = subscription.getUser();
		Organization organization = user.getOrganization();
		String ccyPair = subscription.getCurrencyPair().getName();
		String baseCcy = subscription.getCurrencyPair().getBaseCurrency().getName();
		String senderSubID = subscription.getSenderSubID();
		String mdRequestID = subscription.getMdRequestID();
		int marketDepth = getMarketDepth(subscription);
		MarketDataSnapshotFullRefresh mdResponse;
		if ( isTradable )
		{
			mdResponse = getActiveQuote(book, organization, ccyPair, baseCcy, marketDepth, subscription);
		}
		else
		{
			mdResponse = getInactiveQuote(organization.getShortName(), ccyPair, mdRequestID, senderSubID, String.valueOf(book.getSequenceNo()), user.getOrganization(), subscription.getSessionID());
		}
		return mdResponse;
	}

	private int getMarketDepth( FixSubscription subscription )
	{
		int marketDepth2 = FixUtilC.getInstance().getAllowedMarketDepth(subscription.getOrganization(), null);
		int marketDepth = subscription.getMarketDepth();
		if ( marketDepth2 > 0 )
		{
			if ( marketDepth == 0 ) //Customer asked for full book.
			{
				marketDepth = marketDepth2;
			}
			else
			{
				if ( marketDepth2 < marketDepth )
				{
					marketDepth = marketDepth2;
				}
			}
		}

		if ( marketDepth <= 0 )
		{
			return 0;
		}

		return marketDepth;
	}

	protected int countTiers( FXPriceBook prices )
	{
		final int tiers;
		if ( prices == null )
		{
			tiers = 0;
		}
		else
		{
			int numBids = prices.getBids().size();
			int numOffers = prices.getOffers().size();
			tiers = Math.max(numBids, numOffers);
		}
		return tiers;
	}

	/*
	 * protected static int[] mdentryFieldOrder = new int[]{
	        269, 270, 15, 271, 272, 273, 274, 275, 336, 625, 276, 277,
	        282, 283, 284, 286, 59, 432, 126, 110, 18, 287, 37, 299, 288,
	        289, 346, 290, 546, 58, 354, 355, 0

	        mDEntry.setChar(MDEntryType.FIELD, MDEntryType.OFFER);
	mDEntry.setDouble(MDEntryPx.FIELD, tick.getRate());
	mDEntry.setField(subscriptionData.getBaseCurrency());
	mDEntry.setDouble(MDEntrySize.FIELD, mDEntrySize);
	mDEntry.setField(FixConstants.QUOTE_CONDITION_ACTIVE);
	mDEntry.setField(FixConstants.QUOTE_CONDITION_INACTIVE);
	mDEntry.setField(mdEntryOriginator);
	mDEntry.setDouble(MinQty.FIELD, quote.getMinimumSize());
	mDEntry.setField(new QuoteEntryID(dp.getGUID()));
	mDEntry.setInt(MDEntryPositionNo.FIELD, FixConstants.MD_POSITION_NO_ZERO.getValue());

	};
	 */

	private void addInactiveFxMDEntry( MDEntryType mdEntryType, MarketDataSnapshotFullRefresh mdResponse, Currency dealtCcy, String seqNum, Organization organization )
	{
		ExternallySortedGroup mdEntry = new ExternallySortedGroup(268, 269, mdentryFieldOrder);
		//For externally sorted group, all the entries should be in order
		//1. MDEntryType
		mdEntry.setField(mdEntryType);
		//2. MDEntryPx
		mdEntry.setDouble(MDEntryPx.FIELD, 0.0);
		//3. Currency
		mdEntry.setField(dealtCcy);
		//4.MDEntrySize
		mdEntry.setDouble(MDEntrySize.FIELD, 0.0);
		//5.QuoteCondition
		mdEntry.setField(FixConstants.QUOTE_CONDITION_INACTIVE);

		//6. MDEntryOriginator
		String providerName = configMbean.getAggregatedBookProviderName(organization.getShortName());
		MDEntryOriginator mdEntryOriginator = new MDEntryOriginator(providerName);
		mdEntry.setField(mdEntryOriginator);
		String priceType = mdEntryType.equals(FixConstants.BID) ? "BID" : "OFFER";
		//7. QuoteEntryID
		mdEntry.setField(new QuoteEntryID(new StringBuffer().append(seqNum).append('_').append(priceType).toString()));
		//8. PositionNo
		mdEntry.setField(FixConstants.MD_POSITION_NO_ZERO);

		mdResponse.addGroup(mdEntry);
	}

	protected void addFxMDEntry( FXPriceBook book, Collection<com.integral.aggregation.price.FXPrice> prices, MDEntryType mdEntryType, MarketDataSnapshotFullRefresh mdResponse, Organization organization, int marketDepth, FixSubscription subscription )
	{
		boolean isAggregatedBook = subscription.isAggregatedBook(), isVWAP = subscription.isVWAPAggregation();
		boolean isFBOrRB = subscription.getAggregationMethod() == AggregationMethod.FULL_BOOK || subscription.getAggregationMethod() == AggregationMethod.RAW_BOOK;
		ExternallySortedGroup mdEntry = null;
		int size = 0;
		MDEntryOriginator mdEntryOriginator = null;
		String priceType = mdEntryType.equals(FixConstants.BID) ? "BID" : "OFFER";
		String seqNum = Long.toHexString(book.getSequenceNo());
		Currency dealtCcy = (subscription.getDealtCurrency() == null) ? subscription.getBaseCurrency() : subscription.getDealtCurrency();
		//56739 - For one-sided book, add inactive bid or offer MDEntry when only one side is available
		if ( prices == null || prices.size() == 0 )
		{
			addInactiveFxMDEntry(mdEntryType, mdResponse, dealtCcy, seqNum, organization);
		}

		for ( com.integral.aggregation.price.FXPrice price : prices )
		{
			mdEntry = new ExternallySortedGroup(268, 269, mdentryFieldOrder);
			if ( size >= marketDepth && marketDepth != 0 )
			{
				break;
			}

			//1. MDEntryType
			mdEntry.setField(mdEntryType);
			//2. MDEntryPx
			mdEntry.setDouble(MDEntryPx.FIELD, price.getRate());

			if(subscription.getSettlementType() == com.integral.finance.currency.Currency.SettlementType.FORWARD)
            {
                mdEntry.setDouble( FixConstants.FIX_FIELD_MDEntrySpotRate, price.getRate() );
                mdEntry.setDouble( FixConstants.FIX_FIELD_MDEntryForwardPoints, price.getForwardPoint () );
            }

            //3. Currency
			mdEntry.setField(dealtCcy);
			//4.MDEntrySize
			mdEntry.setDouble(MDEntrySize.FIELD, price.getLimit());
			//5.QuoteCondition
            if(subscription.isClientTagSubscription()){
                if (price.getRate() > 0 )
                {
                    mdEntry.setField(FixConstants.QUOTE_CONDITION_ACTIVE);
                }
            }else if (price.getRate() > 0 && price.getLimit() > 0 )
			{
				mdEntry.setField(FixConstants.QUOTE_CONDITION_ACTIVE);
			}
			else
			{
				if ( isVWAP )
				{
					mdResponse.setGroups(NoMDEntries.FIELD, new ArrayList<quickfix.Group>());
					break;
				}
				mdEntry.setField(FixConstants.QUOTE_CONDITION_INACTIVE);
			}
			//6. MDEntryOriginator
			if ( configMbean.isAggregationProviderNameSupportEnabled(organization.getShortName()) || (!isAggregatedBook && isFBOrRB) )
			{
				mdEntryOriginator = new MDEntryOriginator(price.getLPName());
			}
			else
			{
				String providerName = configMbean.getAggregatedBookProviderName(organization.getShortName());
				mdEntryOriginator = new MDEntryOriginator(providerName);
			}
			mdEntry.setField(mdEntryOriginator);

			mdEntry.setField(new QuoteEntryID(new StringBuffer().append(seqNum).append('_').append(priceType).append(size > 0 ? size : "").toString()));

            if(book.isMultiTier())
            {
                 mdEntry.setInt(MDEntryPositionNo.FIELD, getMDEntryPositionNo(size+1).getValue());
            }
            else
            {
                mdEntry.setField(FixConstants.MD_POSITION_NO_ZERO);
            }

			mdResponse.addGroup(mdEntry);
			size++;
		}
	}

	private Date getTransactTimeForExecutionReport( Trade trade )
	{
		final Organization org = trade.getRequest() != null && trade.getRequest().getUser() != null ? trade.getRequest().getUser().getOrganization() : ((TradingParty) trade.getCounterpartyA()).getLegalEntityOrganization();
		if ( TradeConfigurationFactory.getTradeConfigurationMBean().isUseBusinessExecDateForTransactTime(org) )
		{
			if ( trade.getBusinessExecutionDate() != null )
			{
				return trade.getBusinessExecutionDate();
			}
			else
			{
				log.info("ACT.getTransactTimeForExecutionReport : business execution date is null for trade=" + trade.getTransactionID());
			}
		}
		return trade.getCreatedDate();
	}

	protected abstract void setAccount( ExecutionReport report, NewOrderSingle newOrderSingle, Request request, FXSingleLeg trade );

    protected void setAccount( ExecutionReport report, TradingParty fiTpOfPBLE, LegalEntity pbLE )
    {
        ExternalSystemId extSysID = fiTpOfPBLE.getExternalSystemId(SETTLEMENT_CODE);

        if ( extSysID == null || extSysID.getSystemId() == null || extSysID.getSystemId().trim().length() == 0 )
        {
            //get PB LE's settlement code
            ExternalSystemId pbLEExtSysID = pbLE.getExternalSystemId(SETTLEMENT_CODE);
            if ( pbLEExtSysID == null || pbLEExtSysID.getSystemId() == null || pbLEExtSysID.getSystemId().trim().length() == 0 )
            {
                // set pbLE
                report.set(new Account(pbLE.getShortName()));
            }
            else
            {
                report.set(new Account(pbLEExtSysID.getSystemId()));
            }
        }

        else
        {
            report.set(new Account(extSysID.getSystemId()));
        }
    }



    protected abstract void setBelowMinimumField( ExecutionReport report, double minQtyDouble );

	protected abstract MarketDataSnapshotFullRefresh getActiveQuote( FXPriceBook book, Organization organization, String ccyPair, String baseCcy, int marketDepth, FixSubscription subscription );

	public WorkflowMessage getLogoutMessage( Logout logout, SessionID sessionID ) throws FieldNotFound
	{
		return null;
	}

	public WorkflowMessage getSubscriptionRequest( MarketDataRequest marketDataRequest, SessionID sessionID, boolean isMultiProviderView, boolean isCheckForMaskedName, String originalMDReqID, boolean useAggregationService )
	{
		return createSubscriptionMessage(marketDataRequest, sessionID, isMultiProviderView, isCheckForMaskedName, originalMDReqID, useAggregationService);
	}

	public WorkflowMessage getTradeRequest( NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		Message.Header header = newOrderSingle.getHeader();
		if ( newOrderSingle.isSetHandlInst() && newOrderSingle.getHandlInst().getValue() == HandlInst.MANUAL_ORDER )
		{
			WorkflowMessage wfm = getStagedOrder(newOrderSingle, sessionID, MessageEvent.CREATE);
			StagingAreaUtils.processAlertConditionOnOrderSubmission(wfm, newOrderSingle.toString());
			return wfm;
		}
		Organization customerOrg = fixUtil.getCustomerOrg(newOrderSingle.getMessage(), sessionID);
		LegalEntity customerLe = fixUtil.getLegalEntity(customerOrg, newOrderSingle.getMessage());

		Organization providerOrg = null;
		if ( newOrderSingle.getOrdType().getValue() == OrdType.PREVIOUSLY_QUOTED )
		{
			if ( !header.isSetField(DeliverToCompID.FIELD) && customerOrg != null )
			{

				if (newOrderSingle.isSetQuoteID()) {
					String quoteId = newOrderSingle.getQuoteID().getValue();
					Organization pqSourceOrg = PQServiceFactory.getPQWorkflowService().getPQSourceOrganization(customerOrg,quoteId);
					if (pqSourceOrg != null) {
						boolean pqDirectedOrderSupported = PQServiceFactory.getPQWorkflowService().isPQDirectedOrderSupported(pqSourceOrg, customerOrg, quoteId);
						if( pqDirectedOrderSupported ) {
							providerOrg = pqSourceOrg;
						}
					}
				}
			}
			else {
				String maskedName = header.getString(DeliverToCompID.FIELD);
				String fiName = customerLe.getOrganization().getShortName();
				providerOrg = fixUtil.getOrganization(fixUtil.getRealName(fiName, maskedName));
			}
		}

		if ( newOrderSingle.getOrdType().getValue() == OrdType.PREVIOUSLY_QUOTED )
		{
			Object obj = null;
			try
			{
				String quoteId = newOrderSingle.getQuoteID().getValue();
				boolean isRFS = fixUtil.isRFS(quoteId);
				if ( !isRFS )
				{
					//convert PQ into order. Since spaces.
					return createOrderMessage(customerOrg, customerLe, newOrderSingle, sessionID);
				}
				else
				{
					obj = fixUtil.lookupEntity(quoteId, providerOrg.getShortName(), isRFS);
				}
			}
			catch ( Exception e )
			{
				log.error("FixCommonTranslator.getTradeRequest exception while lookup ", e);
			}

			Quote dbQuote;
			if ( obj == null )
			{
				dbQuote = getQuote(newOrderSingle.getQuoteID().getValue());
				if(!dbQuote.isRfq()){
					log.info("FixCommonTranslator.getTradeRequest quoteId: "+newOrderSingle.getQuoteID().getValue());
					return getFIXValidatonFailureMessage(FixConstants.ACCEPTANCE_REJECTION_REASON_QUOTE_NOT_FOUND);
				}
				if(dbQuote.isRfq()){
					String onBehalfOfCompID = null;
					boolean isSetOnBehalfOfCompID = header.isSetField( OnBehalfOfCompID.FIELD ) ? ( onBehalfOfCompID = header.getString( OnBehalfOfCompID.FIELD ).trim() ).length() > 0 : false;
					String orgExtName = null;
					if ( isSetOnBehalfOfCompID ) {
						int orgIndex = onBehalfOfCompID.indexOf(FixConstants.DELIMITER_USER_ORGANIZATION);
						orgExtName = onBehalfOfCompID.substring(orgIndex + 1);
					}
					String onBehalfOfSubID = null;
					if(header.isSetField( OnBehalfOfSubID.FIELD ) ){
						onBehalfOfSubID = header.getString( OnBehalfOfSubID.FIELD ).trim();
					}
					MessageHandler handler = configuration.getRFSHandler(IdcUtilC.getSessionContextUser(), newOrderSingle.getClOrdID().getValue(), customerLe.getShortName(), sessionID, orgExtName, onBehalfOfSubID);
					HandlerCacheC.getHandlerCache().setHandler(dbQuote.getRequest().getTransactionID(), handler, dbQuote.getRequest());
				}
			}else {
				dbQuote =(Quote) obj;
			}
			return getRFSAcceptanceRequest(providerOrg, customerOrg, customerLe, dbQuote, newOrderSingle, sessionID);

		}
		else
		{
			return createOrderMessage(customerOrg, customerLe, newOrderSingle, sessionID);
		}
	}

	private Quote getQuote(String quid) {
		Quote quote = null;
		try {
			ExpressionBuilder eb = new ExpressionBuilder();
			Expression expr = eb.get("guid").equal(quid);
			ReadObjectQuery raq = new ReadObjectQuery();
			raq.setReferenceClass(Quote.class);
			raq.setSelectionCriteria(expr);
			raq.refreshIdentityMapResult();

			quote = (Quote) PersistenceFactory.newSession().executeQuery(raq);

			if (quote == null) {
				log.warn("getQuote : Quote " + quid + " not found in database");
				return null;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return quote;
	}

	/**
	 * @param customerOrg
	 * @param customerLe
	 * @param newOrderSingle
	 * @param sessionID
	 * @return
	 */
	protected abstract WorkflowMessage createOrderMessage( Organization customerOrg, LegalEntity customerLe, NewOrderSingle newOrderSingle, SessionID sessionID ) throws quickfix.FieldNotFound;


	public WorkflowMessage getFIXValidatonFailureMessage( String fixErrorCode )
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		ErrorMessage err = MessageFactory.newErrorMessage();
		err.setCode(fixErrorCode);
		msg.addError(err);
		msg.setStatus(MessageStatus.FAILURE);
		return msg;
	}

	protected WorkflowMessage createSubscriptionMessage( MarketDataRequest marketDataRequest, SessionID sessionID, boolean isMultiProviderView, boolean isCheckForMaskedName, String originalMDReqID, boolean useAggregationService )
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		try
		{
			User user = clientCache.getUser(sessionID);
			msg.setMessageId(0);
			msg.setSender(user);
			msg.setEvent(MessageEvent.CREATE);
			msg.setTopic(FixConstants.MSG_TOPIC_REQUEST);
			msg.setParameterValue(FixConstants.MSG_REC_AT_WORKFLOW_TS, String.valueOf(System.currentTimeMillis()));
			msg.setParameterValue(FixConstants.WF_PARAM_EXECUTION_TYPE, "S");
			Request request = createSubscriptionRequest(marketDataRequest, sessionID, msg, isCheckForMaskedName);
			if ( request == null )
			{
				return msg;
			}
			SubscriptionManager rateHandler = (SubscriptionManager) configuration.getRateHandler();

			FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
			CurrencyPair ccyPair = CurrencyFactory.newCurrencyPair(dealingPrice.getDealtCurrency(), dealingPrice.getSettledCurrency());
			FixSubscription fixSubscription = createFixSubscription(marketDataRequest, originalMDReqID, sessionID, ccyPair, isMultiProviderView, useAggregationService);

			ProviderSubscription providerSubscription;

			if ( isMultiProviderView )
			{
				providerSubscription = configuration.newMultiProviderSubscription(fixSubscription);
				rateHandler.setMultiProviderSubscription(providerSubscription);
			}
			else
			{
				providerSubscription = configuration.newSingleProviderSubscription(fixSubscription);
				rateHandler.setSingleProviderSubscription(providerSubscription);
			}

			request.setProperty(FixConstants.RATE_HANDLER_KEY, rateHandler);
			msg.setParameterValue(FixConstants.MESSAGE_HANDLER, rateHandler);
			msg.setObject(request);
		}
		catch ( Exception e )
		{
			log.error("FixCommonTranslator.createSubscriptionMessage: Error while creating subscription message-", e);
			msg.addError(FixConstants.INTERNAL_SERVER_ERROR);
		}
		return msg;
	}

	protected Request createSubscriptionRequest( MarketDataRequest marketDataRequest, SessionID sessionID, WorkflowMessage msg, boolean isCheckForMaskedName )
	{
		try
		{
			Organization org = fixUtil.getCustomerOrg(marketDataRequest.getMessage(), sessionID);
			Message.Header header = marketDataRequest.getHeader();
			LegalEntity fiLE;
            // overwritting with default LE for IS subscription - ESF-280
            fiLE = org.getDefaultDealingEntity();

			String fiName = fiLE.getOrganization().getShortName();
			String providerOrgName = header.getString(DeliverToCompID.FIELD);
			if ( isCheckForMaskedName )
			{
				providerOrgName = fixUtil.getRealName(fiName, providerOrgName);
			}
			Organization providerOrg = fixUtil.getOrganization(providerOrgName);

            String ccyPair = marketDataRequest.getCurrencyPairDetails().getDerivedCurrencyPair().getName();

            com.integral.finance.currency.Currency dealtCcy = null;
			com.integral.finance.currency.Currency settledCcy = null;
			CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
			if(currencyPair != null){
				dealtCcy = currencyPair.getBaseCurrency();
				settledCcy = currencyPair.getVariableCurrency();
			}

			// Create FXLegDealingPrice
			FXLegDealingPrice fxLegDealingPrice = FXDealingFactory.newFXLegDealingPrice();
			fxLegDealingPrice.setMinDealtAmount(marketDataRequest.getMinQty().getValue());
			fxLegDealingPrice.setBidOfferMode(DealingPrice.TWO_WAY);

			if ( marketDataRequest.isSetField(MaturityDate.FIELD) )
			{
				StringField tenor = marketDataRequest.getField(new MaturityDate());
				//TODO VALIDATE TENOR
				//TOD0 HANDLE BROKEN DATES
				fxLegDealingPrice.setTenor(new Tenor(tenor.getValue()));
			}
			else
			{
				fxLegDealingPrice.setTenor(FixConstants.SPOT_TENOR);
			}

			fxLegDealingPrice.setDealtCurrency(dealtCcy);
			fxLegDealingPrice.setSettledCurrency(settledCcy);
			fxLegDealingPrice.setDealtAmount(100000000);//set to any value this is ignored for QT subscription

			// Create Request
			Request request = DealingFactory.newRequest();
			request.setRequestClassification(fixUtil.getRequestClassification(FixConstants.CREATE_TYPE));
			request.setChannel(fixUtil.getRequestChannel(marketDataRequest.getMessage()));
			request.setCounterparty(fiLE);
			request.putCustomField(FixConstants.CURRENCY_PAIR, ccyPair);
			if ( providerOrg != null )
			{
				List<Organization> toOrgs = new ArrayList<Organization>(1);
				toOrgs.add(providerOrg);
				request.setToOrganizations(toOrgs);
			}
			request.setRequestPrice(FixConstants.SINGLE_LEG, fxLegDealingPrice);
			request.setExternalRequestId(marketDataRequest.getMDReqID().getValue());
			return request;
		}
		catch ( Exception e )
		{
			log.error("FixCommonTranslator.createSubscriptionRequest: Error while creating subscription request-", e);
			msg.addError(FixConstants.INTERNAL_SERVER_ERROR);
			return null;
		}
	}

    protected WorkflowMessage createTradeMessage( Organization providerOrg, Organization customerOrg, LegalEntity customerLe, FXLegDealingPrice dealingPrice, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		String clientOrderID = newOrderSingle.getClOrdID().getValue();
		User user = clientCache.getUser(sessionID);
		// create message
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setMessageId(0);
		msg.setSender(user);
		msg.setEvent(MessageEvent.CREATE);
		msg.setTopic(FixConstants.MSG_TOPIC_REQUEST);
		MessageHandler handler = configuration.getTradeHandler(clientCache.getUser(sessionID), clientOrderID, sessionID);
		if ( handler == null )
		{
			ErrorMessage err = MessageFactory.newErrorMessage();
			err.setCode(FixConstants.INTERNAL_SERVER_ERROR);
			msg.addError(err);
			msg.setStatus(MessageStatus.FAILURE);
			return msg;
		}
		msg.setParameterValue(FixConstants.MESSAGE_HANDLER, handler);
		msg.setParameterValue(FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_ESP);
		setParametersOnRequest(msg, newOrderSingle, user);
		String tradeChannel = setTradeSubChannel(msg, user, FixConstants.TRADE_CHANNEL);

		Request request = createTradeRequest(providerOrg, customerOrg, customerLe, newOrderSingle, sessionID);

		request.setTradeChannel(tradeChannel);
		long nanoTime = System.nanoTime();
		request.setNotes(new StringBuilder(25).append(nanoTime).append("##").append(request.getUser().getFullName()).toString());
		msg.setParameterValue(FixConstants.MESSAGE_ID, nanoTime);
		msg.setObject(request);
		if ( dealingPrice.getBidOfferMode() == DealingPrice.OFFER )
		{
			if ( !(dealingPrice instanceof FXLegDealingPrice) )
			{
				msg.setParameterValue(FixConstants.OFFER_RATE_COMPACT, String.valueOf(((FXRate) dealingPrice.getPriceElement().getPrice().getOffer()).getRate()));
			}
			else
			{
				msg.setParameterValue(FixConstants.OFFER_RATE_COMPACT, String.valueOf(dealingPrice.getRate()));
			}
		}
		else
		{
			if ( !(dealingPrice instanceof FXLegDealingPrice) )
			{
				msg.setParameterValue(FixConstants.BID_RATE_COMPACT, String.valueOf(((FXRate) dealingPrice.getPriceElement().getPrice().getBid()).getRate()));
			}
			else
			{
				msg.setParameterValue(FixConstants.BID_RATE_COMPACT, String.valueOf(dealingPrice.getRate()));
			}
		}

		if ( newOrderSingle.isSetTimeInForce() )
		{
			char tif = newOrderSingle.getTimeInForce().getValue();
			if ( tif == quickfix.field.TimeInForce.IMMEDIATE_OR_CANCEL )
			{
				request.setTimeInForce(TimeInForce.IOC);
			}
			else if ( tif == quickfix.field.TimeInForce.FILL_OR_KILL )
			{
				request.setTimeInForce(TimeInForce.FOK);
			}
		}

		msg.setProperty(FixConstants.ACCEPTED_QUOTE_REFERENCE_PROPERTY, createAcceptedQuoteReference(dealingPrice, request));
		SefWorkFlow.acceptsParameters(request, newOrderSingle.getMessage(), SefWorkFlow.State.CREATED_TRADE_MESSAGE);
		request.getRequestAttributes().setSessionId(sessionID.toString());
		return msg;
	}

	protected Request createTradeRequest( Organization providerOrg, Organization customerOrg, LegalEntity customerLE, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		// Create FXLegDealingPrice
		String ccyPair = newOrderSingle.getSymbol().getValue();
		String dealtCcy = newOrderSingle.getCurrency().getValue();
		CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
		com.integral.finance.currency.Currency dealtCurrency = CurrencyFactory.getCurrency(dealtCcy);
		FXLegDealingPrice fxLegDealingPrice = FXDealingFactory.newFXLegDealingPrice();

		if ( isDealingBaseCurrency(currencyPair, dealtCurrency) )
		{
			if ( newOrderSingle.getSide().getValue() == Side.BUY )
			{
				fxLegDealingPrice.setBidOfferMode(DealingPrice.OFFER);
			}
			else
			{
				fxLegDealingPrice.setBidOfferMode(DealingPrice.BID);
			}
		}
		else
		{
			if ( newOrderSingle.getSide().getValue() == Side.SELL )
			{
				fxLegDealingPrice.setBidOfferMode(DealingPrice.OFFER);
			}
			else
			{
				fxLegDealingPrice.setBidOfferMode(DealingPrice.BID);
			}
		}

		fxLegDealingPrice.setTenor(FixConstants.SPOT_TENOR);
		fxLegDealingPrice.setDealtCurrencyProperty(getDealtCurrencyProperty(currencyPair, dealtCurrency));
		fxLegDealingPrice.setDealtCurrency(dealtCurrency);
		fxLegDealingPrice.setSettledCurrency(getSettledCurrency(currencyPair, dealtCurrency));
		fxLegDealingPrice.setDealtAmount(newOrderSingle.getOrderQty().getValue());

		// Create Request
		Request request = DealingFactory.newRequest();
		request.setRequestClassification(fixUtil.getRequestClassification(FixConstants.ACCEPT_TYPE));
		request.setChannel(fixUtil.getRequestChannel(newOrderSingle.getMessage()));
		request.setExternalRequestId(newOrderSingle.getClOrdID().getValue());
		if ( customerOrg != null )
		{
			request.setOrganization(customerOrg);
		}
		request.setCounterparty(customerLE);

		List<Organization> toOrgs = new ArrayList<Organization>(1);
		toOrgs.add(providerOrg);
		request.setToOrganizations(toOrgs);

		request.setUser(clientCache.getUser(sessionID));
		request.setRequestPrice(FixConstants.SINGLE_LEG, fxLegDealingPrice);

		if ( log.isDebugEnabled() )
		{
			log.debug("FixCommonTranslator.createTradeRequest request counterparty " + request.getCounterparty().getShortName());
			log.debug("FixCommonTranslator.createTradeRequest request organization " + (request.getOrganization() == null ? null : request.getOrganization().getShortName()));
			log.debug("FixCommonTranslator.createTradeRequest request toorganization " + (request.getToOrganizations().iterator().next()).getShortName());
		}

		return request;

	}

	protected void updateWFMsgWithOutrightParams( NewOrderSingle newOrderSingle, WorkflowMessage wfm )
	{
		try
		{
			if ( newOrderSingle.isSetField(FixConstants.FIX_FIELD_OUTRIGHT_SPOT_RATE) )
			{
				wfm.setParameterValue(ISCommonConstants.WF_PARAM_OUTRIGHT_SPOT_RATE, newOrderSingle.getString(FixConstants.FIX_FIELD_OUTRIGHT_SPOT_RATE));

			}
			if ( newOrderSingle.isSetField(FixConstants.FIX_FIELD_OUTRIGHT_FWD_POINTS) )
			{
				wfm.setParameterValue(ISCommonConstants.WF_PARAM_OUTRIGHT_FWD_POINTS, newOrderSingle.getString(FixConstants.FIX_FIELD_OUTRIGHT_FWD_POINTS));
			}
			DateFormat sdf = new SimpleDateFormat(FixConstants.DATE_TIME_FORMAT);
			setOutrightFixingDate(newOrderSingle, wfm, sdf);
			setOutrightTenor(newOrderSingle, wfm, sdf);
		}
		catch ( FieldNotFound e )
		{
			// Dont do anything. Its not a mandatory field
		}
	}

	/**
	 * public static final String TWAP_STARTTIME_ABSOLUTE = "TWAPSTABS";
	 * public static final String TWAP_STARTTIME_DELAY = "TWAPSTDly";
	 * public static final String TWAP_ENDIME = "TWAPET";
	 * public static final String TWAP_ENDIME_ABSOLUTE = "TWAPETABS";
	 * public static final String TWAP_SLICEINERVAL = "TWAPSI";
	 * public static final String TWAP_SLICEINERVAL_RANGE_FUCTION = "TWAPSIRF";
	 * public static final String TWAP_SLICESIZE = "TWAPSS";
	 * public static final String TWAP_SLICESIZE_RANGE_FUCTION = "TWAPSSRF";
	 * public static final String TWAP_ACTIONORDEREXPIRY = "TWAPAOE";
	 * <p/>
	 * <field number="7555" name="TwapOrderExecutionStartTime" type="UTCTIMESTAMP" />
	 * <field number="7556" name="TwapOrderExecutionEndTime" type="UTCTIMESTAMP" />
	 * <field number="7557" name="TwapSliceInterval" type="INT" />
	 * <field number="7558" name="TwapSliceIntervalRandomizationFactor" type="PRICE" />
	 * <field number="7559" name="TwapSliceSize" type="QTY" />
	 * <field number="7560" name="TwapSliceSizeRandomizationFactor" type="PRICE" />
	 * <field number="7561" name="ActionOnOrderExpitation" type="STRING" />
	 * <p/>
	 * int FIX_FIELD_TWAP_ST_ABS = 7555;
	 * int FIX_FIELD_TWAP_ST_PERIOD = 7562;
	 * int FIX_FIELD_TWAP_ET_ABS = 7556;
	 * int FIX_FIELD_TWAP_ET_RERIOD = 7563;
	 * int FIX_FIELD_TWAP_SI = 7557;
	 * int FIX_FIELD_TWAP_SI_RF = 7558;
	 * int FIX_FIELD_TWAP_SS = 7559;
	 * int FIX_FIELD_TWAP_SS_RF = 7560;
	 * int FIX_FIELD_TWAP_AOE = 7561;
	 *
	 *
	 * @param newOrderSingle new order single
	 * @param msg            message
	 * @throws FieldNotFound field not found
	 */
	protected void setStrategyParameters( NewOrderSingle newOrderSingle, WorkflowMessage msg ) throws FieldNotFound
	{
		//boolean isStrategyOrder = newOrderSingle.isSetField(FixConstants.FIX_FIELD_STRATEGY);
		//boolean isTWAPOrder = newOrderSingle.isSetExecInst() && newOrderSingle.getExecInst().getValue().indexOf("ST") != -1;

		//if(!isStrategyOrder)
		//{
		//	return;
		//}

		if ( newOrderSingle.isSetField(FixConstants.FIX_FIELD_STRATEGY_ST_ABS) )
		{
			String s = newOrderSingle.getString(FixConstants.FIX_FIELD_STRATEGY_ST_ABS);
			msg.setParameterValue(ISCommonConstants.TWAP_STARTTIME_ABSOLUTE, s);
		}
		if ( newOrderSingle.isSetField(FixConstants.FIX_FIELD_STRATEGY_ST_PERIOD) )
		{
			String s = newOrderSingle.getString(FixConstants.FIX_FIELD_STRATEGY_ST_PERIOD);
			msg.setParameterValue(ISCommonConstants.TWAP_STARTTIME_DELAY_MILLIS, String.valueOf(getMillis(s)));
		}
		if ( newOrderSingle.isSetField(FixConstants.FIX_FIELD_STRATEGY_ET_ABS) )
		{
			String s = newOrderSingle.getString(FixConstants.FIX_FIELD_STRATEGY_ET_ABS);
			msg.setParameterValue(ISCommonConstants.TWAP_ENDTIME_ABSOLUTE, s);
			//request.setTimeInForce(TimeInForce.GTD);
		}

		if ( newOrderSingle.isSetField(FixConstants.FIX_FIELD_STRATEGY_ET_PERIOD) )
		{
			String s = newOrderSingle.getString(FixConstants.FIX_FIELD_STRATEGY_ET_PERIOD);
			msg.setParameterValue(ISCommonConstants.TWAP_ENDTIME_DELAY_MILLIS, String.valueOf(getMillis(s)));
			//request.setTimeInForce(TimeInForce.GTD);
		}

		if ( newOrderSingle.isSetField(FixConstants.FIX_FIELD_STRATEGY_NAME) )
		{
			String s = newOrderSingle.getString(FixConstants.FIX_FIELD_STRATEGY_NAME);
			msg.setParameterValue(ISCommonConstants.TWAP_STRATEGYNAME, s);
		}

		if ( newOrderSingle.isSetField(FixConstants.FIX_FIELD_STRATEGY) )
		{
			String strategyParams = newOrderSingle.getString(FixConstants.FIX_FIELD_STRATEGY);
			for ( String paramString : strategyParams.split(" ") )
			{
				String[] param = paramString.split("~");
				if ( param == null || param[0] == null || param[0].trim().equals("") )
				{
					continue;
				}

				if ( FixConstants.TWAP_Strategy_SI.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_SLICEINERVAL_MILLIS, String.valueOf(getMillis(param[1])));
				}
				else if ( FixConstants.TWAP_Strategy_SMI.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_MINSLICEINERVAL_MILLIS, String.valueOf(getMillis(param[1])));
				}
				else if ( FixConstants.TWAP_Strategy_SIR.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_SLICEINERVAL_RANGE_FUCTION, param[1]);
				}
				else if ( FixConstants.TWAP_Strategy_SS.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_SLICESIZE, param[1]);
				}
                else if ( FixConstants.TWAP_Strategy_MSS.equals(param[0]) )
                {
                    msg.setParameterValue(ISCommonConstants.TWAP_MINSLICESIZE, param[1]);
                }
				else if ( FixConstants.TWAP_Strategy_SSR.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_SLICESIZE_RANGE_FUCTION, param[1]);
				}
				else if ( FixConstants.TWAP_Strategy_AE.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_ACTIONORDEREXPIRY, param[1]);
				}
				else if ( FixConstants.TWAP_Strategy_PT.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_PASSIVETIME, param[1]);
				}
				else if ( FixConstants.TWAP_Strategy_SRS.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_SLICEREGSIZE, param[1]);
				}
				else if ( FixConstants.TWAP_Strategy_TOBP.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_SLICETOBPERCENT, param[1]);
				}
                else if ( FixConstants.TWAP_Strategy_TOBR.equals(param[0]))
                {
                    msg.setParameterValue(ISCommonConstants.TWAP_TOB_RANGE, param[1]);
                }
                else if ( FixConstants.TWAP_Strategy_SFOK.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.TWAP_FOK_SLICE, param[1]);
				}
				//Peg Orders
				else if ( FixConstants.Peg_Strategy_POT.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.PEGTYPE, param[1]);
				}
				else if ( FixConstants.Peg_Strategy_PO.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.PEGOFFSET, param[1]);
				}
				else if ( FixConstants.Peg_Strategy_POI.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.PEGOFFSETINCR, param[1]);
				}
				else if ( FixConstants.Peg_Strategy_PII.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.PEGOFFSETINCRINTRVL, param[1]);
				}
				else if ( FixConstants.Peg_Strategy_PIR.equals(param[0]) )
				{
					msg.setParameterValue(ISCommonConstants.PEGOFFSETINCRRNDFLG, param[1]);
				}
			}
		}

		//New fields - overrides above fields
		if( newOrderSingle.getOrdType().getValue() == OrdType.PEGGED && newOrderSingle.isSetField(FixConstants.FIX_FIELD_PEG_TYPE) ){
			msg.setParameterValue(ISCommonConstants.PEGTYPE, String.valueOf(PegTypes.MID));
			//assumption - this is always in pips. validation of value type is done by FIX engine.
			if( newOrderSingle.isSetPegDifference() )
				msg.setParameterValue(ISCommonConstants.PEGOFFSET, String.valueOf(newOrderSingle.getPegDifference().getValue()));
		}

		if( newOrderSingle.getOrdType().getValue() == OrdType.PREVIOUSLY_INDICATED ){
			if( newOrderSingle.isSetField(FixConstants.TARGET_STRATEGY_FIELD) ){

				if ( TargetStrategy.isPresent(newOrderSingle.getTargetStrategy()) )
				{
					TargetStrategy targetStrategy = TargetStrategy.fromCode(newOrderSingle.getTargetStrategy());
					msg.setParameterValue(ISCommonConstants.STRATEGY_TYPE, targetStrategy);

					if( TargetStrategy.TWAP == targetStrategy ||  TargetStrategy.LIT_SWITCH == targetStrategy ||TargetStrategy.DARK_SWITCH == targetStrategy){
						if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_SliceSize) )
							msg.setParameterValue(ISCommonConstants.TWAP_SLICESIZE, newOrderSingle.getString(9051));
						if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_SliceSizeRandomizer) )
							msg.setParameterValue(ISCommonConstants.TWAP_SLICESIZE_RANGE_FUCTION, newOrderSingle.getString(9052));
						if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_SliceInterval) )
							msg.setParameterValue(ISCommonConstants.TWAP_SLICEINERVAL_MILLIS, newOrderSingle.getString(9053) );
						if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_SliceIntrvRandomizer) )
							msg.setParameterValue(ISCommonConstants.TWAP_SLICEINERVAL_RANGE_FUCTION, newOrderSingle.getString(9054) );
						if( newOrderSingle.isSetField(FixConstants.ACTION_ON_EXPIRY_FIELD ) ){
							if( newOrderSingle.getActionOnExpiry() == ISCommonConstants.TWAP_ACTIONORDEREXPIRY_FillAtMarket ){
								msg.setParameterValue(ISCommonConstants.TWAP_ACTIONORDEREXPIRY, String.valueOf( ISCommonConstants.TWAP_ACTIONORDEREXPIRY_FillAtMarket ));
							}
						}
						if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_SliceTOBRange) ){
							msg.setParameterValue(ISCommonConstants.TWAP_TOB_RANGE, newOrderSingle.getString(FixConstants.FIX_FIELD_SliceTOBRange));
						}
						if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_PassiveTime) ) {
							msg.setParameterValue(ISCommonConstants.SWITCH_PASSIVE_TIME, newOrderSingle.getString(FixConstants.FIX_FIELD_PassiveTime) );
						}
						if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_PassiveTimeFactor) ) {
							msg.setParameterValue(ISCommonConstants.SWITCH_PASSIVE_TIME_FACTOR, newOrderSingle.getString(FixConstants.FIX_FIELD_PassiveTimeFactor) );
						}
					}
					else if ( TargetStrategy.FIXING_TWAP == targetStrategy ) {
						//Set FIX specific parameters here
						if ( newOrderSingle.isSetMinClipSize() ) {
							msg.setParameterValue(ISCommonConstants.FIXING_TWAP_MIN_CLIP_SIZE, String.valueOf(newOrderSingle.getMinClipSize()));
						}
						if ( newOrderSingle.isSetMaxClipSize() ) {
							msg.setParameterValue(ISCommonConstants.FIXING_TWAP_MAX_CLIP_SIZE, String.valueOf(newOrderSingle.getMaxClipSize()));
						}
					}
				}

			}
		}


	}

	//s has format 'HH:mm:ss' or 'HH:mm:ss.SSS'
	protected long getMillis( String s )
	{
		long millis = 0;
		int idx1 = s.indexOf('.');
		if ( idx1 == -1 ) //it is in hh:mm:ss
		{
			idx1 = s.indexOf(':');
			int hours = Integer.parseInt(s.substring(0, idx1));
			int mins = Integer.parseInt(s.substring(idx1 + 1, s.lastIndexOf(':')));
			int secs = Integer.parseInt(s.substring(s.lastIndexOf(':') + 1));
			millis = (hours * 60 * 60 + mins * 60 + secs) * 1000;
		}
		else
		{
			idx1 = s.indexOf(':');
			int hours = Integer.parseInt(s.substring(0, idx1));
			int mins = Integer.parseInt(s.substring(idx1 + 1, s.lastIndexOf(':')));
			int secs = Integer.parseInt(s.substring(s.lastIndexOf(':') + 1, s.lastIndexOf('.')));
			millis = Integer.parseInt(s.substring(s.lastIndexOf('.') + 1));
			secs = hours * 60 * 60 + mins * 60 + secs;

			millis = (secs) * 1000 + millis;
		}
		return millis;
	}

	protected AcceptedQuoteReference createRFSAcceptedQuoteReference( Quote quote, NewOrderSingle newOrderSingle ) throws FieldNotFound
	{

		// create accepted quote price reference
		Request request = quote.getRequest();
		request.setExternalRequestId(newOrderSingle.getClOrdID().getValue());
		AcceptedQuoteReference acceptedQuoteRef = DealingMessageFactory.newAcceptedQuoteReference();

		// link accepted quote reference and dealing price reference
		List<AcceptedDealingPriceReference> acceptedDealingPriceRefs = new ArrayList<AcceptedDealingPriceReference>();

		for ( Object obj : quote.getQuotePrices() )
		{
			FXLegDealingPrice fx_LegDealingPrice = (FXLegDealingPrice) obj;
			AcceptedDealingPriceReference acceptedDealingPriceRef = DealingMessageFactory.newAcceptedDealingPriceReference();
			EntityReference acceptedQuotePriceRef = MessageFactory.newEntityReference();
			acceptedQuotePriceRef.setEntity(fx_LegDealingPrice);
			acceptedDealingPriceRef.setAcceptedQuotePriceReference(acceptedQuotePriceRef);
			int acceptanceSide = getAcceptanceBidOfferMode(fx_LegDealingPrice, newOrderSingle);
			acceptedDealingPriceRef.setAcceptedPriceBidOfferMode(acceptanceSide);
			acceptedDealingPriceRef.setEntity(fx_LegDealingPrice);
			acceptedDealingPriceRefs.add(acceptedDealingPriceRef);

		}
		acceptedQuoteRef.setAcceptedDealingPriceReferences(acceptedDealingPriceRefs);
		acceptedQuoteRef.setQuote(quote);
		SefWorkFlow.acceptsParameters(request, newOrderSingle.getMessage(), SefWorkFlow.State.CREATE_RFS_ACCEPTANCE_QUOTE_REQUEST);
		// set request reference
		acceptedQuoteRef.getRequestReference().setEntity(request);

		return acceptedQuoteRef;
	}

	protected int getAcceptanceBidOfferMode( FXLegDealingPrice fx_LegDealingPrice, NewOrderSingle newOrderSingle ) throws FieldNotFound
	{
		try
		{
			char side = newOrderSingle.getSide().getValue();
			String ccyPair = newOrderSingle.getSymbol().getValue();
			String dealtCcy = newOrderSingle.getCurrency().getValue();
			CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
			com.integral.finance.currency.Currency dealtCurrency = CurrencyFactory.getCurrency(dealtCcy);
			//this is done as side comes for FAR Leg.
			if ( FixConstants.NEAR_LEG.equals(fx_LegDealingPrice.getName()) )
			{
				side = (side == Side.BUY) ? Side.SELL : Side.BUY;
			}

			//IF not "TWO WAY" then bidoffermode is already defined in request so just send the requested side.
			if ( fx_LegDealingPrice.getBidOfferMode() == DealingPrice.TWO_WAY )
			{
				if ( isDealingBaseCurrency(currencyPair, dealtCurrency) )
				{
					if ( side == Side.BUY )
					{
						return DealingPrice.OFFER;
					}
					else
					{
						return DealingPrice.BID;
					}
				}
				else
				{
					if ( side == Side.SELL )
					{
						return DealingPrice.OFFER;
					}
					else
					{
						return DealingPrice.BID;
					}
				}
			}
			else
			{
				return fx_LegDealingPrice.getBidOfferMode();
			}
		}

		catch ( FieldNotFound e )
		{
			log.error("FixCommonTranslator.getAcceptanceBidOfferMode:Error while finding bidofferMode:", e);
			throw e;
		}
	}

	/* (non-Javadoc)
	  * @see com.integral.fix.client.translator.FixTranslator42#getWithdrawRequest(quickfix.fix42.OrderCancelRequest, quickfix.SessionID)
	  */
	public WorkflowMessage getWithdrawRequest( OrderCancelRequest orderCancelRequest, SessionID sessionID, Request requestFromORS ) throws FieldNotFound
	{
		WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
		wfm.setStatus(MessageStatus.SUCCESS);
		wfm.setTopic(FixConstants.MSG_TOPIC_REQUEST);
		wfm.setEvent(FixConstants.MSG_EVENT_WITHDRAW);
		wfm.setParameterValue(FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_LIMIT_ORDERS);
		Request request = clientCache.getDealingRequest(sessionID, orderCancelRequest.getOrigClOrdID().getValue());
		if ( request != null ) //request will be null for reloaded orders.
		{
			wfm.setObject(request);
		}
		else
		{
			wfm.setObject(requestFromORS);
		}
		wfm.setSender(clientCache.getUser(sessionID));
		wfm.setParameterValue(FixConstants.ORDER_CANCEL_REQUEST_ID, orderCancelRequest.getClOrdID().getValue());
		return wfm;
	}

	/* (non-Javadoc)
	  * @see com.integral.fix.client.translator.FixTranslator42#getWithdrawRequest(quickfix.fix42.OrderCancelRequest, quickfix.SessionID)
	  */
	public WorkflowMessage getWithdrawRequest( OrderMassCancelRequest orderMassCancelRequest, SessionID sessionID ) throws FieldNotFound
	{
		WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
		wfm.setStatus(MessageStatus.SUCCESS);
		wfm.setTopic(FixConstants.MSG_TOPIC_REQUEST);
		wfm.setEvent(FixConstants.MSG_EVENT_WITHDRAW_ALL);
		wfm.setParameterValue(FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_LIMIT_ORDERS);
		wfm.setParameterValue("RequestClassification", FixConstants.LIMIT);
		wfm.setSender(clientCache.getUser(sessionID));
		Message.Header header = orderMassCancelRequest.getHeader();
		boolean isSetOnBehalfOfCompID = header.isSetField(OnBehalfOfCompID.FIELD);
		if ( isSetOnBehalfOfCompID )
		{
			Organization org = fixUtil.getCustomerOrg(orderMassCancelRequest.getMessage(), sessionID);
			if ( header.isSetField(OnBehalfOfSubID.FIELD) )
			{
				TradingParty tp = fixUtil.getTradingPartyFromCache(FixConstants.ON_BEHALF_OF_SUB_ID_EXTERNAL_SYSTEM, header.getString(OnBehalfOfSubID.FIELD), org.getShortName());
				if ( tp != null )
				{
					wfm.setObject(tp.getLegalEntity());
				}
				else
				{
					LegalEntity le = fixUtil.getCounterparty(header.getString(OnBehalfOfSubID.FIELD), org);
					wfm.setObject(le);
				}
			}
			else
			{
				if( FixUtilC.getInstance().isServer2ServerSession(sessionID)){
					/*
						ERM-835
						For ServerToServer session if only organization is set then cancel all orders of the organization.
					 */
					wfm.setObject(org);
				}
				else {
					/*
						ERM-835
						Non server to server session. Use DefaultDealingEntity. Pre-Existing behavior
					 */
					wfm.setObject(org.getDefaultDealingEntity());
				}
			}
		}
		else
		{
			wfm.setObject(clientCache.getUser(sessionID));
		}
		return wfm;
	}

	/* (non-Javadoc)
	  * @see com.integral.fix.client.translator.FixTranslator42#getWithdrawRequest(quickfix.fix42.OrderCancelReplaceRequest, quickfix.SessionID)
	  */
	public WorkflowMessage getWithdrawRequest( OrderCancelReplaceRequest orderCancelReplaceRequest, SessionID sessionID, Request requestFromORS ) throws FieldNotFound
	{
		WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
		wfm.setStatus(MessageStatus.SUCCESS);
		wfm.setTopic(FixConstants.MSG_TOPIC_REQUEST);
		wfm.setEvent(FixConstants.MSG_EVENT_WITHDRAW);
		wfm.setParameterValue(FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_LIMIT_ORDERS);
		Request request = clientCache.getDealingRequest(sessionID, orderCancelReplaceRequest.getOrigClOrdID().getValue());
		if ( request != null )
		{
			wfm.setObject(request);
		}
		else
		{
			wfm.setObject(requestFromORS);
		}
		wfm.setSender(clientCache.getUser(sessionID));
		wfm.setParameterValue(FixConstants.Key_ReplaceRequest, true);
		wfm.setParameterValue(FixConstants.ORDER_CANCEL_REPLACE_REQUEST_ID, orderCancelReplaceRequest.getClOrdID().getValue());
		return wfm;
	}

	protected WorkflowMessage getRFSAcceptanceRequest( Organization providerOrg, Organization customerOrg, LegalEntity customerLe, Quote quote, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound
	{
		// create message
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setStatus(MessageStatus.SUCCESS);
		msg.setEvent(MessageEvent.ACCEPT);
		msg.setTopic(FixConstants.MSG_TOPIC_QUOTE);
		msg.setMessageId(0);
		User user = clientCache.getUser(sessionID);
		msg.setSender(user);
		setParametersOnRequest(msg, newOrderSingle, user);
		msg.setParameterValue(FixConstants.MESSAGE_ID, System.nanoTime());
		if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_CLIENT_CHANNEL)){
			setTradeSubChannel(msg,user,newOrderSingle.getString(FixConstants.FIX_FIELD_CLIENT_CHANNEL));
		}
		else {
			setTradeSubChannel(msg, user, FixConstants.RFS_TRADE_CHANNEL);
		}
		msg.setObjectReference(createRFSAcceptedQuoteReference(quote, newOrderSingle));
		return msg;
	}

	protected void setParametersOnRequest( WorkflowMessage msg, NewOrderSingle newOrderSingle, User user ) throws FieldNotFound
	{
		// get current timestamp
		String now = String.valueOf(System.currentTimeMillis());
		//      String clientTransactTime = String.valueOf(newOrderSingle.getTransactTime().getValue().getTime());
		String clientOrderID = newOrderSingle.getClOrdID().getValue();
		msg.setParameterValue(FixConstants.ISMAKER_FLAG_COMPACT, FixConstants.FALSE_STR);
		msg.setParameterValue(FixConstants.CLIENT_ORDER_ID_COMPACT, clientOrderID);
		msg.setParameterValue(FixConstants.EVENT_TIME_JMS_SENT_COMPACT, now);
		msg.setParameterValue(FixConstants.BOOK_SNAPSHOT_COMPACT, "");
		msg.setParameterValue(FixConstants.BATCH_SEQUENCE_NO, null);
		msg.setParameterValue(FixConstants.IS_ORDER_ID, new StringBuilder(FixConstants.ORDERID_PREFIX).append(clientOrderID).toString());
		msg.setParameterValue(FixConstants.CPU_USAGE_COMPACT, FixConstants.NA);
		msg.setParameterValue(FixConstants.LAST_POLL_LATANCY_COMPACT, FixConstants.NA);
		msg.setParameterValue(FixConstants.ATTEMPTS_COMPACT, FixConstants.NA);
		msg.setParameterValue(FixConstants.MEMORY_USAGE_COMPACT, FixConstants.NA);
		msg.setParameterValue(FixConstants.EVENT_TIME_JMS_REC_RATE_COMPACT, now);//added to fifo
		msg.setParameterValue(FixConstants.EVENT_TIME_CLIENT_QUERIED_RATE_COPMACT, now);//client queries polls rate
		msg.setParameterValue(FixConstants.EVENT_TIME_CLIENT_REC_RATE_COMPACT, now);//client received rate
		msg.setParameterValue(FixConstants.EVENT_TIME_CLIENT_DISP_RATE_COMPACT, now);//client sees rate
		msg.setParameterValue(FixConstants.EVENT_TIME_CLIENT_ACC_USER_COMPACT, now);//client accepted rate
		msg.setParameterValue(FixConstants.EVENT_TIME_CLIENT_SENT_ACC_COMPACT, now);//client sends acceptance
		msg.setParameterValue(FixConstants.MSG_REC_AT_WORKFLOW_TS, now);//acceptance received at server
		if( newOrderSingle.isSetField(FixConstants.FIX_FIELD_BOOK_NAME)){
			msg.setParameterValue(ISCommonConstants.WF_PARAM_YM_BOOK_NAME,newOrderSingle.getString(FixConstants.FIX_FIELD_BOOK_NAME));
		}
		// SEF fields
		setSEFParameters(msg, newOrderSingle);
		UTIWorkFlowHelper.updateWorkFlowMsg(msg, newOrderSingle);
		UPIWorkFlowHelper.updateWorkFlowMsg(msg, newOrderSingle);
		RTNWorkFlowHelper.updateWorkFlowMsg(msg, newOrderSingle);
		setSettlementInstructions(msg, newOrderSingle);
	}

	private void setSEFParameters( WorkflowMessage msg, NewOrderSingle newOrderSingle ) throws FieldNotFound
	{
		String USIPrefix = newOrderSingle.isSetUSIPrefix() ? newOrderSingle.getUSIPrefix() : null;
		String USI = newOrderSingle.isSetUSI() ? newOrderSingle.getUSI() : null;
		String USIPrefixFar = newOrderSingle.isSetUSIPrefixFar() ? newOrderSingle.getUSIPrefixFar() : null;
		String USIFar = newOrderSingle.isSetUSIFar() ? newOrderSingle.getUSIFar() : null;

		if ( USIPrefix != null )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_USI_PREFIX_PARAM, USIPrefix);
		}
		if ( USI != null )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_USI_PARAM, USI);
		}

		if ( USIPrefixFar != null )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_FAR_USI_PREFIX_PARAM, USIPrefixFar);
		}

		if ( USIFar != null )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_FAR_USI_PARAM, USIFar);
		}

		if ( newOrderSingle.isSetMidRate() )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_MID_RATE_PARAM, newOrderSingle.getMidRate());
		}

		if ( newOrderSingle.isSetMidRateFar() )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_FAR_MID_RATE_PARAM, newOrderSingle.getFarMidRate());
		}
	}

	private void setSEFParametersOnQuoteRequest( WorkflowMessage msg, com.integral.fix.client.message.grp.NoRelatedSym group ) throws FieldNotFound
	{
		String USIPrefix = group.isSetUSIPrefix() ? group.getUSIPrefix() : null;
		if ( USIPrefix != null )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_USI_PREFIX_PARAM, USIPrefix);
		}

		String USIPrefixFar = group.isSetUSIPrefixFar() ? group.getUSIPrefixFar() : null;
		if ( USIPrefixFar != null )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_FAR_USI_PREFIX_PARAM, USIPrefixFar);
		}

		String USI = group.isSetUSI() ? group.getUSI() : null;
		if ( USI != null )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_USI_PARAM, USI);
		}

		String USIFar = group.isSetUSIFar() ? group.getUSIFar() : null;
		if ( USIFar != null )
		{
			msg.setParameterValue(SEFUtilC.WF_FIX_FAR_USI_PARAM, USIFar);
		}
		Integer quoteRequestType = group.isSetQuoteRequestType() ? group.getQuoteRequestType() : null;
		if ( quoteRequestType != null && quoteRequestType == 101 )
		{
			msg.setParameterValue(SEFUtilC.WF_BLOCK_TRADE_IDENTIFIER, true);
		}
		Integer rfqBroadcast = group.isSetBroadcastRFQ() ? group.getBroadcastRFQ() : null;
		if ( rfqBroadcast != null && rfqBroadcast == 1)
		{
			msg.setParameterValue(SEFUtilC.WF_RFS_BROADCAST, true);
		}
	}

	protected void setHeader( Message messageFrom, Message messageTo, int fieldFrom, int fieldTo )
	{
		if ( messageFrom.getHeader().isSetField(fieldFrom) )
		{
			try
			{
				messageTo.getHeader().setString(fieldTo, messageFrom.getHeader().getString(fieldFrom));
			}
			catch ( FieldNotFound fieldNotFound )
			{
				fieldNotFound.printStackTrace();
			}
		}
	}

	protected void setHeader( Message message, int field, String value )
	{
		try
		{
			message.getHeader().setString(field, value);
		}
		catch ( Exception e )
		{

		}
	}

	protected AcceptedQuoteReference createAcceptedQuoteReference( FXLegDealingPrice quotePrice, Request request )
	{
		// create accepted quote price reference
		EntityReference acceptedQuotePriceRef = MessageFactory.newEntityReference();
		acceptedQuotePriceRef.setEntity(quotePrice);

		// create accepted dealing price reference
		AcceptedDealingPriceReference acceptedDealingPriceRef = DealingMessageFactory.newAcceptedDealingPriceReference();
		acceptedDealingPriceRef.setAcceptedQuotePriceReference(acceptedQuotePriceRef);
		acceptedDealingPriceRef.setAcceptedPriceBidOfferMode(quotePrice.getBidOfferMode());

		//new for CSDK
		acceptedDealingPriceRef.setEntity(quotePrice);

		// create accepted quote reference
		AcceptedQuoteReference acceptedQuoteRef = DealingMessageFactory.newAcceptedQuoteReference();
		acceptedQuoteRef.setQuote(quotePrice.getQuote());

		// link accepted quote reference and dealing price reference
		List acceptedDealingPriceRefs = new ArrayList(1);
		acceptedDealingPriceRefs.add(acceptedDealingPriceRef);
		acceptedQuoteRef.setAcceptedDealingPriceReferences(acceptedDealingPriceRefs);

		// set request reference
		acceptedQuoteRef.getRequestReference().setEntity(request);

		return acceptedQuoteRef;
	}

	protected String getDealtCurrencyProperty( CurrencyPair ccyPair, com.integral.finance.currency.Currency dealtCcy )
	{
		if ( isDealingBaseCurrency(ccyPair, dealtCcy) )
		{
			return FXLegDealingPrice.CCY1;
		}
		else
		{
			return FXLegDealingPrice.CCY2;
		}
	}

	protected boolean isDealingBaseCurrency(CurrencyPair ccyPair, com.integral.finance.currency.Currency dealtCcy)
	{
		return ccyPair.getBaseCurrency().isSameAs(dealtCcy);
	}

	protected com.integral.finance.currency.Currency getSettledCurrency(CurrencyPair ccyPair, com.integral.finance.currency.Currency dealtCcy)
	{
		return isDealingBaseCurrency(ccyPair, dealtCcy) ? ccyPair.getVariableCurrency() : ccyPair.getBaseCurrency();
	}

	protected FXPaymentParameters getFXPayment( Trade trade, boolean nearLeg )
	{
		if ( trade instanceof FXSingleLeg )
		{
			return ((FXSingleLeg) trade).getFXLeg().getFXPayment();
		}
		else if ( nearLeg )
		{
			return ((FXSwap) trade).getNearLeg().getFXPayment();
		}
		else
		{
			return ((FXSwap) trade).getFarLeg().getFXPayment();
		}
	}

	public WorkflowMessage getOrderStatusRequest( Request request, User user )
	{
		// create message
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setStatus(MessageStatus.SUCCESS);
		msg.setEvent(FixConstants.MSG_EVENT_STATUS);
		msg.setTopic(FixConstants.MSG_TOPIC_ORDER);
		msg.setSender(user);
		msg.setObject(request);
		msg.setParameterValue(FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_LIMIT_ORDERS);
		return msg;
	}

	protected String setTradeSubChannel( WorkflowMessage msg, User user, String tradeChannel )
	{
		String tradeSubChannel = fixUtil.getFIXClientConfig().getFIXSubChannel(user);
		if ( tradeSubChannel == null || "".equals(tradeSubChannel.trim()) )
		{
			msg.setParameterValue(FixConstants.WF_PARAM_TRADE_CHANNEL, tradeChannel);
			return tradeChannel;
		}
		else
		{
			msg.setParameterValue(FixConstants.WF_PARAM_TRADE_CHANNEL, tradeChannel + '/' + tradeSubChannel);//Trade Channel
			return tradeChannel + '/' + tradeSubChannel;
		}
	}

	protected void setHeader( com.integral.fix.client.message.Message messageFrom, com.integral.fix.client.message.Message messageTo, int fieldFrom, int fieldTo )
	{
		if ( messageFrom.getHeader().isSetField(fieldFrom) )
		{
			try
			{
				messageTo.getHeader().setString(fieldTo, messageFrom.getHeader().getString(fieldFrom));
			}
			catch ( FieldNotFound fieldNotFound )
			{
				fieldNotFound.printStackTrace();
			}
		}
	}

	protected void setHeader( com.integral.fix.client.message.Message message, int field, String value )
	{
		try
		{
			message.getHeader().setString(field, value);
		}
		catch ( Exception e )
		{

		}
	}

	public ProviderSubscription getSingleProviderSubscription( MarketDataRequest marketDataRequest, String originalMDReqID, CurrencyPair ccyPair, SessionID sessionID, boolean useAggregationService )
	{
		try
		{
			FixSubscription subscription = createFixSubscription(marketDataRequest, originalMDReqID, sessionID, ccyPair, false, useAggregationService);
			return configuration.newSingleProviderSubscription(subscription);
		}
		catch ( Exception e )
		{
			log.error("FixCommonTranslator.getSingleProviderSubscription: Error while creating SingleProviderSubscription object -", e);
			return null;
		}
	}

	public ProviderSubscription getMultiProviderSubscription( MarketDataRequest marketDataRequest, String originalMDReqID, CurrencyPair ccyPair, SessionID sessionID, boolean useAggregationService )
	{
		try
		{
			FixSubscription subscription = createFixSubscription(marketDataRequest, originalMDReqID, sessionID, ccyPair, true, useAggregationService);
			return configuration.newMultiProviderSubscription(subscription);
		}
		catch ( Exception e )
		{
			log.error("FixCommonTranslator.getMultiProviderSubscription: Error while creating MultiProviderSubscription object -", e);
			return null;
		}
	}

	public FixSubscription createFixSubscription( MarketDataRequest marketDataRequest, String originalMDReqID, SessionID sessionID, CurrencyPair ccyPair, boolean isMultiProviderView, boolean useAggregationService ) throws Exception
	{
		User user = clientCache.getUser(sessionID);
		Session session = Session.getSessions().get(sessionID);
		String senderSubID = marketDataRequest.getHeader().getString(SenderSubID.FIELD);
		int marketDepth = marketDataRequest.getMarketDepth().getValue();

		boolean isSetAggregatedBook = marketDataRequest.isSetAggregatedBook();
		boolean isAggregatedBook = isSetAggregatedBook ? marketDataRequest.getAggregatedBook().getValue() : true;
		if(marketDataRequest.isMatchingVenueDirectSubscription()) {
			isAggregatedBook = false;
		}
		String givenLPName = marketDataRequest.getHeader().getString(DeliverToCompID.FIELD);
		String fiName = fixUtil.getCustomerOrg(marketDataRequest.getMessage(), sessionID).getShortName();
		String maskedName = fixUtil.getMaskedName(fiName, givenLPName);
		boolean isIncrementalUpdateRequested = marketDataRequest.getMDUpdateType().getValue() == MDUpdateType.INCREMENTAL_REFRESH;
		boolean isMultiPriceAggregation = marketDataRequest.getAggregationType() != null && marketDataRequest.getAggregationType().equals(FixConstants.MULTI_TIER_PRICES_AGGREGATION_TYPE);
		//boolean useAggregationService = fixClient
		return new FixSubscription(user, marketDataRequest.getCurrencyPairDetails(), originalMDReqID, senderSubID, marketDepth, marketDataRequest.getRequestedSize(), isMultiProviderView, session.isFlowControlEnabled(), maskedName, isAggregatedBook, marketDataRequest.isVWAPAggregationType(), isMultiPriceAggregation, isIncrementalUpdateRequested, marketDataRequest.getTiers(), marketDataRequest.getProviderOrgs(), sessionID, useAggregationService);

	}

	public WorkflowMessage getAggregationSubscriptionRequest( MarketDataRequest mdr, SessionID sessionID )
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		try
		{
			User user = clientCache.getUser(sessionID);
			msg.setMessageId(0);
			msg.setSender(user);
			msg.setEvent(MessageEvent.CREATE);
			msg.setTopic(FixConstants.MSG_TOPIC_REQUEST);
			msg.setParameterValue(FixConstants.MSG_REC_AT_WORKFLOW_TS, String.valueOf(System.currentTimeMillis()));
			msg.setParameterValue(FixConstants.WF_PARAM_EXECUTION_TYPE, "S");
			Request request = createSubscriptionRequest(mdr, sessionID, msg, false);
			if ( request == null )
			{
				return msg;
			}
			request.setOrganization(request.getCounterparty().getOrganization());
			request.setIsAggregationRequest(true);
			request.setToOrganizations(mdr.getProviderOrgs());
            request.getRequestAttributes().setIsSingleLPFromClient( mdr.isSingleLPFromClient() );

			FXLegDealingPrice dealingPrice = (FXLegDealingPrice) request.getRequestPrice(FixConstants.SINGLE_LEG);
			CurrencyPair ccyPair = CurrencyFactory.newCurrencyPair(dealingPrice.getDealtCurrency(), dealingPrice.getSettledCurrency());

			Currency dealtCurrency = null;
			dealingPrice.setDealtCurrencyProperty(FXLegDealingPrice.CCY1);
            com.integral.finance.currency.Currency dealtCcy = mdr.getCurrencyPairDetails().getDerivedDealtCurrency();
            if (dealtCcy != null)
            {
            	dealtCurrency = new Currency(dealtCcy.getShortName());
            }
            dealingPrice.setDealtCurrencyProperty(getDealtCurrencyProperty(ccyPair, dealtCcy));

			FixSubscription fixSubscription = createFixSubscription(mdr, mdr.getMDReqID().getValue(), sessionID, ccyPair, true, true);

			//String aggregationType = mdr.isSetField(FixConstants.FIX_FIELD_AGGREGATION_TYPE) ? mdr.getString(FixConstants.FIX_FIELD_AGGREGATION_TYPE) : null;
			String aggregationType = mdr.getAggregationType();
			AggregationMethod agm = AggregationMethod.FULL_BOOK;
			if ( aggregationType == null || aggregationType.equals(FixConstants.FULL_BOOK_AGGREGATION_TYPE) || aggregationType.equals(FixConstants.BEST_PRICE_AGGREGATION_TYPE) )
				agm = fixSubscription.isAggregatedBook() ? AggregationMethod.FULL_BOOK : AggregationMethod.RAW_BOOK;
			else if ( aggregationType.equals(FixConstants.VWAP_AGGREGATION_TYPE) )
				agm = fixSubscription.isAggregatedBook() ? AggregationMethod.WEIGHTED_AVERAGE : AggregationMethod.WEIGHTED_AVERAGE;
			else if ( aggregationType.equals(FixConstants.MULTI_TIER_MARKET_AGGREGATION_TYPE) )
				agm = AggregationMethod.MULTI_PRICE_MARKET;
			else if ( aggregationType.equals(FixConstants.MULTI_TIER_PRICES_AGGREGATION_TYPE) )
				agm = AggregationMethod.MULTI_PRICE_TIERS;
			else if ( aggregationType.equals(FixConstants.SYNTHETIC_FULL_BOOK_MULTI_TIER_AGGREGATION_TYPE) )
				agm = AggregationMethod.FB_MULTI_PRICE_TIERS;
			else if ( aggregationType.equals(FixConstants.SYNTHETIC_FULL_BOOK_VWAP_AGGREGATION_TYPE) )
				agm = AggregationMethod.FB_WEIGHTED_AVERAGE;
            else if ( aggregationType.equals(FixConstants.MULTI_TIER_FOK_AGGREGATION_TYPE) )
                agm = AggregationMethod.MULTI_TIER_FOK;
			else if ( aggregationType.equals(FixConstants.SYNTHETIC_FULL_BOOK_MULTI_TIER_FOK_AGGREGATION_TYPE))
				agm = AggregationMethod.FB_MULTI_TIER_FOK;
			else if ( aggregationType.equals(FixConstants.FULL_AMOUNT_MULTI_TIER_AGGREGATION_TYPE))
				agm = AggregationMethod.FA_MULTI_TIER;
			else if ( aggregationType.equals(FixConstants.FULL_AMOUNT_MULTI_QUOTE_AGGREGATION_TYPE))
				agm = AggregationMethod.FA_MULTI_QUOTE;

			request.setAggregationMethod(agm);
            if(mdr.getClientTagId() != null){
                request.setProperty(ISCommonConstants.CLIENT_TAG_FOR_SUBSCRIPTION,mdr.getClientTagId() );
                fixSubscription.setClientTagSubscription( true );
            }
			fixSubscription.setAggregationMethod(agm);
			fixSubscription.setDealtCurrency(dealtCurrency);
			if ( mdr.getTiers() != null && !mdr.getTiers().isEmpty() )
			{
				request.setAggregationMultiTierSizes(mdr.getTiers());
			}
			request.setAggregationRequestSize(mdr.getRequestedSize());
            if(agm.equals(AggregationMethod.MULTI_TIER_FOK) && configMbean.isOnlyFAStreamsInAggregation(request.getCounterparty().getOrganization().getShortName())){
            	request.setDropFXIDirectStreamPrices(false);
				request.setFullAmount(true);
			}else {
            	switch (agm){
					case MULTI_TIER_FOK:
					case FB_MULTI_TIER_FOK:
						boolean faAggEnabled = AggregationServiceFactory.getInstance().getAggregationMBean()
								.isFullAmountAggregationEnabled(request.getCounterparty().getOrganization().getShortName());
						request.setDropFXIDirectStreamPrices(!faAggEnabled);
						break;
					default:
						request.setDropFXIDirectStreamPrices(true);
				}
			}

			SubscriptionManager rateHandler = (SubscriptionManager) configuration.getRateHandler();

			ProviderSubscription providerSubscription = new AggregatedViewSubscription(configuration, fixSubscription);
			rateHandler.setMultiProviderSubscription(providerSubscription);

			request.setProperty(FixConstants.RATE_HANDLER_KEY, rateHandler);
			msg.setParameterValue(FixConstants.MESSAGE_HANDLER, rateHandler);
			msg.setObject(request);
		}
		catch ( Exception e )
		{
			log.error("FixCommonTranslator.createSubscriptionMessage: Error while creating subscription message-", e);
			msg.addError(FixConstants.INTERNAL_SERVER_ERROR);
		}
		return msg;
	}

	protected void setExecInst( final Request orderRequest, final FXLegDealingPrice orderRequestDealingPrice, ExecutionReport executionReport )
	{
		String execInst = "";
		short insts = orderRequest.getExecutionFlags();
		if ( (insts & ExecutionFlags.ALLOW_QUOTE_CROSS) > 0 )
		{
			execInst = execInst + ' ' + ExecInst.OK_TO_CROSS;
		}
		if ( (insts & ExecutionFlags.BEST_PRICE) > 0 )
		{
			execInst = execInst + ' ' + ExecInst.MARKET_PEG;
		}
		if ( (insts & ExecutionFlags.VWAP) > 0 )
		{
			execInst = execInst + ' ' + ExecInst.PEG_TO_VWAP;
		}

		String spp = orderRequest.getSecondarySortPriority();
		if ( spp != null )
		{
			if ( FixConstants.PROVIDER_PRIORITY.equals(spp) )
			{
				execInst = execInst + ' ' + FixConstants.EXECINST_PROVIDER_PRIORITY;
			}
			else if ( FixConstants.SIZE_PRIORITY.equals(spp) )
			{
				execInst = execInst + ' ' + FixConstants.EXECINST_SIZE_PRIORITY;
			}
			else if ( FixConstants.TIME_PRIORITY.equals(spp) )
			{
				execInst = execInst + ' ' + FixConstants.EXECINST_TIME_PRIORITY;
			}
		}

		String clsf = orderRequest.getRequestClassification().getShortName();
		if ( FixConstants.STOP.equals(clsf) || FixConstants.STOPLIMIT.equals(clsf) )
		{
			if ( (insts & ExecutionFlags.AT_RATE) > 0 )
			{
				execInst = execInst + ' ' + ExecInst.PRIMARY_PEG;
			}
			switch ( orderRequestDealingPrice.getTriggerPriceType() )
			{
			case DealingPrice.BID :
				execInst = execInst + ' ' + ExecInst.STAY_ON_BIDSIDE;
				break;
			case DealingPrice.OFFER :
				execInst = execInst + ' ' + ExecInst.STAY_ON_OFFERSIDE;
				break;
			case DealingPrice.MID :
				execInst = execInst + ' ' + ExecInst.MID_PRICE;
				break;
			}
		}
		executionReport.set(new ExecInst(execInst.trim()));
	}

	protected void setSide( final FXLegDealingPrice orderRequestDealingPrice, ExecutionReport executionReport )
	{
		boolean isBid = orderRequestDealingPrice.getBidOfferMode() == FXLegDealingPrice.BID;
		if ( isBid )
		{
			if ( FXLegDealingPrice.CCY1.equals(orderRequestDealingPrice.getDealtCurrencyProperty()) )
			{
				executionReport.set(FixConstants.BUY);
			}
			else
			{
				executionReport.set(FixConstants.SELL);
			}
		}
		else
		{
			if ( FXLegDealingPrice.CCY1.equals(orderRequestDealingPrice.getDealtCurrencyProperty()) )
			{
				executionReport.set(FixConstants.SELL);
			}
			else
			{
				executionReport.set(FixConstants.BUY);
			}
		}
	}

	protected void copyOrderAttributesFromDealingPrice( Request orderRequest, ExecutionReport executionReport, boolean isOrderCancel ) throws FieldNotFound
	{
		FXLegDealingPrice dealingPrice = (FXLegDealingPrice) orderRequest.getRequestPrice(FixConstants.SINGLE_LEG);
		boolean isBid = dealingPrice.getBidOfferMode() == FXLegDealingPrice.BID;
		FXPrice fxPrice = ((FXDealingPriceElement) dealingPrice.getPriceElement()).getFXPrice();
		FXRate fxRate = isBid ? fxPrice.getBidFXRate() : fxPrice.getOfferFXRate();
		executionReport.set(new Price(fxRate.getRate()));
		quickfix.field.TimeInForce tif = fixUtil.getTimeInForce(orderRequest);
		if ( tif != null )
		{
			executionReport.set(tif);
		}
		executionReport.set(fixUtil.getOrdType(orderRequest));
		if ( dealingPrice.getMaxShowAmount() != null && dealingPrice.getMaxShowAmount() > 0 )
		{
			executionReport.set(new MaxShow(dealingPrice.getMaxShowAmount()));
		}
		if ( dealingPrice.getMinDealtAmount() != null && dealingPrice.getMinDealtAmount() > 0 )
		{
			executionReport.set(new MinQty(dealingPrice.getMinDealtAmount()));
		}
		if ( dealingPrice.getMarketRange() != null && dealingPrice.getMarketRange() > 0 )
		{
			executionReport.set(new PegDifference(dealingPrice.getMarketRange()));
		}
		if ( dealingPrice.getStopPrice() != null && dealingPrice.getStopPrice() > 0 )
		{
			executionReport.set(new StopPx(dealingPrice.getStopPrice()));
		}
		if ( isOrderCancel )
		{
			String ccyPair = dealingPrice.getDealtCurrency().getShortName() + '/' + dealingPrice.getSettledCurrency().getShortName();
			executionReport.set(new Symbol(ccyPair));
			executionReport.set(new Currency(dealingPrice.getDealtCurrency().getShortName()));
			setExecInst(orderRequest, dealingPrice, executionReport);
			executionReport.set(new OrderQty(dealingPrice.getDealtAmount()));
			setSide(dealingPrice, executionReport);
		}
	}

	protected Symbol getSymbol( QuoteRequest quoteRequest )
	{
		try
		{
			com.integral.fix.client.message.grp.NoRelatedSym symbolGroup = quoteRequest.createNoRelatedSym();
			quoteRequest.getGroup(1, symbolGroup);
			if ( symbolGroup.isSetSymbol() )
			{
				return symbolGroup.getSymbol();
			}
		}
		catch ( Exception e )
		{
			log.error("FixCommonTranslator.getSymbol(QuoteRequest) Error - Symbol not set. Message - " + quoteRequest);
		}
		return null;
	}

	public WorkflowMessage getNettingPortfolioRequest( QuoteRequest quoteRequest, SessionID sessionID ) throws FieldNotFound
	{
		WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
		wfm.setStatus(MessageStatus.SUCCESS);
		wfm.setEvent(MessageEvent.PRICE);
		wfm.setTopic(ISConstantsC.MSG_TOPIC_PORTFOLIO);
		User user = clientCache.getUser(sessionID);
		wfm.setSender(user);

		Message.Header header = quoteRequest.getHeader();
		boolean isSetOnBehalfOfCompID = header.isSetField(OnBehalfOfCompID.FIELD);
		boolean isSetOnBehalfOfSubID = header.isSetField(OnBehalfOfSubID.FIELD);
		boolean isSetSenderSubID = header.isSetField(SenderSubID.FIELD);
		String senderSubID = null;
		String onBehalfOfCompID = null;
		String onBehalfOfSubID = null;
		Organization customerOrg = fixUtil.getCustomerOrg(quoteRequest.getMessage(), sessionID);
		if ( isSetOnBehalfOfCompID )
		{
			onBehalfOfCompID = header.getString(OnBehalfOfCompID.FIELD);
		}

		if ( isSetSenderSubID )
		{
			senderSubID = header.getString(SenderSubID.FIELD);
		}

		if ( isSetOnBehalfOfSubID )
		{
			onBehalfOfSubID = header.getString(OnBehalfOfSubID.FIELD);
		}
		else
		{
			onBehalfOfSubID = customerOrg.getDefaultDealingEntity().getShortName();
		}
		NettingPortfolio portfolioObj = new NettingPortfolioC();// Ideally it should come from some factory class

		Collection<Organization> toOrgs = new ArrayList<Organization>();

		Organization providerOrg = ReferenceDataCacheC.getInstance().getOrganization(quoteRequest.getHeader().getString(FixConstants.FIX_FIELD_DELIVER_TO_COMPID));
		toOrgs.add(providerOrg);
		portfolioObj.setOrganization(customerOrg);
		portfolioObj.setUser(user);
		portfolioObj.setExternalRequestId(quoteRequest.getQuoteReqID().getValue());
		portfolioObj.setToOrganizations(toOrgs);
		portfolioObj.setNamespace(customerOrg.getNamespace());

		if ( quoteRequest.isSetField(SEFConstants.FIX_FIELD_IS_ALLOCATION) && quoteRequest.isAllocation() )
		{
			portfolioObj.setAllocation(true);
			portfolioObj.setSEF(true);
		}
		// set the provider Org.
		//		portfolioObj.set // Link to child obj ???

		if ( quoteRequest.getNoRelatedSym().getValue() > 1 )
		{
			log.warn("CommonTranslator.getNettingPortfolioRequest - Method not supported for multiple symbols in " +
					"batch quote request. Continuing default behaviour");
		}

		NoRelatedSym noRelatedSym = quoteRequest.createNoRelatedSym();
		quoteRequest.getGroup(1, noRelatedSym);
		if ( noRelatedSym.isSetField(quickfix.field.ExpireTime.FIELD) )
		{
			Date expiryTime = noRelatedSym.getExpireTime().getValue();
			Calendar cal = Calendar.getInstance();
			cal.setTime(expiryTime);
			int min = cal.get(Calendar.MINUTE);
			int seconds = cal.get(Calendar.SECOND);
			int expiryTimeinSec = (min * 60) + seconds;//as of now ignore milliseconds.
			Expiration expiryObj = new ExpirationC();
			expiryObj.setSeconds(expiryTimeinSec);
			portfolioObj.setExpiration(expiryObj);
		}
		else
		{
			// If ExpireTime field is not set then set what ever is set at IS level. Default value is 120 seconds
	        int defaultExpirationTime = ISFactory.getInstance().getISMBean().getRFSRequestExpiryTime( customerOrg );
			Expiration expiryObj = new ExpirationC();
			expiryObj.setSeconds(defaultExpirationTime);
			portfolioObj.setExpiration(expiryObj);
		}

		if( noRelatedSym.isSetOrderQty() )
		{
			portfolioObj.setNetSpotAmount(noRelatedSym.getOrderQty().getValue());
		}

		String ccypair = noRelatedSym.getSymbol().getValue();
		CurrencyPair ccyp = CurrencyFactory.getCurrencyPairFromString(ccypair);

		HashMap<String, NettingTradeRequest> requests = new HashMap<String, NettingTradeRequest>();
		int requestSeqId = 0;
		FXRateConvention conv = QuoteConventionUtilC.getInstance().getFXRateConvention(customerOrg);
		int legCount = 0;
		double singleLegDealtAmount = 0.0;
		for ( int counter = 1 ; counter <= noRelatedSym.getNoLegs().getValue() ; counter++ )
		{
			NoLegs noLegs = noRelatedSym.createNoLegs();
			Group group = noRelatedSym.getGroup(counter, noLegs);
			String legRefId = noLegs.getString(FixConstants.FIX_FIELD_LEG_REF_ID);
			NettingTradeRequest request = requests.get(legRefId);
			if ( request == null ){
				request = new NettingTradeRequestC();
				request.setExternalRequestId(legRefId);
				request.setSequenceNumber(legRefId);
				request.setSequenceId(++requestSeqId);
				request.setCcyPair(ccyp);

				requests.put(legRefId, request);
			}
			TradeRequestLeg leg = getTradeRequestLeg(noLegs, request);
			leg.setFXRateConvention(conv);
			request.addTradeLeg(leg);
			legCount++;
			singleLegDealtAmount = leg.getDealtAmount ();
			if( leg.getSide() == TradeRequestLeg.UNDISCLOSED ){
				portfolioObj.setUndisclosedRequest(true);
			}
			//Fix for Backward Compatibility - MTF-635
			//Nested ID changes at Leg Level for MIFID
			NoNestedPartyIDs noNestedPartyIDs = noLegs.createNoNestedPartyIDs();
			noLegs.getGroup(1, noNestedPartyIDs);
			List<Group> legGroups = group.getGroups(quickfix.field.NoNestedPartyIDs.FIELD);
			String leShortName=null;
			if(legGroups.size()>1) {
				for (Group individualLeg : legGroups) {
					String party = individualLeg.getString(NestedPartyID.FIELD);
					if (individualLeg.isSetField(NestedPartyRole.FIELD)) {
                        if (individualLeg.isSetField(NestedPartyIDSource.FIELD)) {
                            char aChar = individualLeg.getChar(NestedPartyIDSource.FIELD);
                            if (aChar == 'D') leShortName = party;
                            else continue;
                        }
                    }else{
                        leShortName=party;
                    }
				}
			}else {
				leShortName = noNestedPartyIDs.getNestedPartyID().getValue();
			}
			LegalEntity le = (LegalEntity) ReferenceDataCacheC.getInstance().getEntityByShortName(leShortName, LegalEntity.class, customerOrg.getNamespace(), Entity.ACTIVE_STATUS);
			request.setFund(le);
			request.setNettingPortfolio(portfolioObj);
		}
		portfolioObj.setInputRequestPojos(new ArrayList<NettingTradeRequest>(requests.values()));
		portfolioObj.setFxRateConv(conv);

		// if it is a single leg, set the net spot amount same as leg dealt amount.
		if ( portfolioObj.isUndisclosedRequest () && legCount == 1 && ( !noRelatedSym.isSetOrderQty() || portfolioObj.getNetSpotAmount () == 0.0 ) )
		{
			log.info ( "CT.getNettingPortfolioRequest - setting net spot amount for single leg and no orderQty or orderQty is 0. noRelatedSym="
					+ noRelatedSym + ",singleLegDealtAmount=" + singleLegDealtAmount + ",portfolioId=" + portfolioObj.getPortfolioID () );
			portfolioObj.setNetSpotAmount ( singleLegDealtAmount );
		}

		// set the portfolio handler
		clientCache.storePortfolioQuoteRequest(sessionID, quoteRequest.getQuoteReqID().getValue(), portfolioObj);
		MessageHandler handler = configuration.getPortfolioHandler(user, quoteRequest.getQuoteReqID().getValue(), senderSubID, sessionID, onBehalfOfCompID, onBehalfOfSubID);
		wfm.setParameterValue(FixConstants.MESSAGE_HANDLER, handler);
		wfm.setObject(portfolioObj);
		String tradeChannel = setTradeSubChannel(wfm, user, FixConstants.RFS_TRADE_CHANNEL);
		portfolioObj.setChannel( FixConstants.CHANNEL );
		portfolioObj.setTradeChannel( tradeChannel );
		MiFIDHandlerFactory.getInstance().getMiFIDHandler().processQuoteRequest(quoteRequest,portfolioObj);
		return wfm;
	}

	private TradeRequestLeg getTradeRequestLeg( NoLegs noLegs, NettingTradeRequest request ) throws FieldNotFound
	{
		TradeRequestLeg leg = new TradeRequestLegC();
		leg.setTradeRequest(request);
		CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(noLegs.getLegSymbol().getValue());
		leg.setBaseCurrency(currencyPair.getBaseCurrency());
		leg.setTermCurrency(currencyPair.getVariableCurrency());
		leg.setDealtCurrency(CurrencyFactory.getCurrency(noLegs.getLegCurrency().getValue()));

		CurrencyPair ccyp = CurrencyFactory.getCurrencyPair(leg.getBaseCurrency(), leg.getTermCurrency());
		leg.setCurrencyPair(noLegs.getLegSymbol().getValue());

		DateFormat sdf = new SimpleDateFormat(FixConstants.DATE_TIME_FORMAT);
		setLegValueDate(noLegs, leg, null, sdf);
		setLegFixingDate(noLegs, leg, null, sdf);

		String legRefId = noLegs.getString(FixConstants.FIX_FIELD_LEG_REF_ID);
		leg.setExternalLegId(legRefId);

		if ( leg.getBaseCurrency().isSameAs(leg.getDealtCurrency()) )
		{
			// Base is Dealt Currency
			if( noLegs.getLegSide().getValue() == Side.UNDISCLOSED )
			{
				leg.setSide(TradeRequestLeg.UNDISCLOSED);
			}
			else if ( noLegs.getLegSide().getValue() == Side.BUY )
			{
				// Buying base (or dealt) currency
				leg.setBuyingBase(true);
				leg.setBuyingDealt(true);
				leg.setSide(TradeRequestLeg.BUY);
			}
			else
			{
				// Selling base (or dealt) currency
				leg.setBuyingBase(false);
				leg.setBuyingDealt(false);
				leg.setSide(TradeRequestLeg.SELL);
			}
			leg.setBaseAmount(noLegs.getLegQty().getValue());
			leg.setDealtAmount(noLegs.getLegQty().getValue());
			leg.setTermAmount(0);
		}
		else
		{
			// Term is Dealt Currency

			if( noLegs.getLegSide().getValue() == Side.UNDISCLOSED )
			{
				leg.setSide(TradeRequestLeg.UNDISCLOSED);
			}
			else if ( noLegs.getLegSide().getValue() == Side.BUY )
			{
				// Buying term (or dealt) currency
				leg.setBuyingBase(false);
				leg.setBuyingDealt(true);
				leg.setSide(TradeRequestLeg.BUY);
			}
			else
			{
				// Selling term (or dealt) currency
				leg.setBuyingBase(true);
				leg.setBuyingDealt(false);
				leg.setSide(TradeRequestLeg.SELL);
			}
			leg.setTermAmount(noLegs.getLegQty().getValue());
			leg.setDealtAmount(noLegs.getLegQty().getValue());
			leg.setBaseAmount(0);
		}
		MiFIDHandlerFactory.getInstance().getMiFIDHandler().populateISINOnPortfolioQuoteRequest(noLegs,leg);
		UTIWorkFlowHelper.updateTradeRequestLegs(leg , noLegs );
		UPIWorkFlowHelper.updateTradeRequestLegs(leg , noLegs );
        RTNWorkFlowHelper.updateTradeRequestLegs(leg , noLegs );
		return leg;
	}

	public WorkflowMessage getRFSRequest( QuoteRequest quoteRequest, SessionID sessionID ) throws FieldNotFound
	{
		WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
		wfm.setStatus(MessageStatus.SUCCESS);
		wfm.setEvent(MessageEvent.CREATE);
		wfm.setTopic(FixConstants.MSG_TOPIC_REQUEST);
		User user = clientCache.getUser(sessionID);
		wfm.setSender(user);

		Organization customerOrg = fixUtil.getCustomerOrg(quoteRequest.getMessage(), sessionID);
		LegalEntity customerLe = fixUtil.getLegalEntity(customerOrg, quoteRequest.getMessage());
		Message.Header header = quoteRequest.getHeader();

		boolean isSetOnBehalfOfCompID = header.isSetField(OnBehalfOfCompID.FIELD);
		boolean isSetOnBehalfOfSubID = header.isSetField(OnBehalfOfSubID.FIELD);
		boolean isSetSenderSubID = header.isSetField(SenderSubID.FIELD);
		String senderSubID = null;
		String onBehalfOfCompID = null;
		String onBehalfOfSubID = null;

		if ( isSetOnBehalfOfCompID )
		{
			onBehalfOfCompID = header.getString(OnBehalfOfCompID.FIELD);
		}

		if ( isSetSenderSubID )
		{
			senderSubID = header.getString(SenderSubID.FIELD);
		}

		if ( isSetOnBehalfOfSubID )
		{
			onBehalfOfSubID = header.getString(OnBehalfOfSubID.FIELD);
		}
		else
		{
			onBehalfOfSubID = customerOrg.getDefaultDealingEntity().getShortName();
		}

		
		com.integral.fix.client.message.grp.NoRelatedSym group = quoteRequest.createNoRelatedSym();
		quoteRequest.getGroup(1, group);//As of now done as its always NoOfRelatedSyms is always one.
		boolean isSwap = (group.getOrdType().getValue() == OrdType.FOREX_SWAP);
		Request request = DealingFactory.newRequest();
		request.setRequestClassification(fixUtil.getRequestClassification(FixConstants.RFQ_CREATE_TYPE));
		if ( customerOrg != null )
		{
			request.setOrganization(customerOrg);
		}
		else
		{
			request.setOrganization(user.getOrganization());
		}
		request.setUser(user);
		request.setCounterparty(customerLe);
		request.setOriginatingOrg ( request.getOrganization ().getShortName () );
		boolean longLivedRFQ = RFQParamHelper.updateWorkFlowMsg(wfm, group, customerOrg);
		Date expiryTime = group.getExpireTime().getValue();
		Calendar cal = Calendar.getInstance();
		cal.setTime(expiryTime);
		int min = cal.get(Calendar.MINUTE);
		int seconds = cal.get(Calendar.SECOND);
		int expiryTimeinSec = (min * 60) + seconds;//as of now ignore milliseconds.
		if (longLivedRFQ) {
			int hour = cal.get(Calendar.HOUR_OF_DAY);
			expiryTimeinSec += (hour * 3600);
			log.info("Price request is RFQ, hour will also be considered in expiry, expiry(in sec): " + expiryTimeinSec);
		}
		Expiration expiryObj = new ExpirationC();
		expiryObj.setSeconds(expiryTimeinSec);
		request.setExpiration(expiryObj);
		//Collection<Organization> toOrg = new ArrayList<Organization>(1);
		//toOrg.add(providerOrg);

		request.setChannel(fixUtil.getRequestChannel( quoteRequest.getMessage() ));
		request.setExternalTradeId(quoteRequest.getQuoteReqID().getValue());
		String ccyPair = group.getSymbol().getValue();
		String dealtCcy = group.getCurrency().getValue();
		com.integral.finance.currency.Currency dealtCurrency = CurrencyFactory.getCurrency(dealtCcy);
		CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
		com.integral.finance.currency.Currency settledCurrency = getSettledCurrency(currencyPair, dealtCurrency);
		request.putCustomField(FixConstants.CURRENCY_PAIR, ccyPair);
		request.setExternalRequestId ( quoteRequest.getQuoteReqID ().getValue () );
		if(group.getSecurityType() != null && group.getSecurityType().getValue().equals(SecurityType.TIME_DEPOSIT)){
			request.setSubTradeType(ISCommonConstants.DEPOSITS);
		}
		String fiName = customerOrg.getShortName();
		boolean aggRequest = false;
		List<Organization> providerOrgs = null;
		String maskedLPName = header.getString(DeliverToCompID.FIELD);
		if ("ALL".equalsIgnoreCase(maskedLPName)) {
			aggRequest = true;
			providerOrgs = getProviderList(customerOrg, null, currencyPair);
		} else {
			providerOrgs = new ArrayList<Organization>();
			String[] provs = maskedLPName.split(",");
			if (provs != null) {
				if (provs.length > 1) {
					aggRequest = true;
				}
				for (String prov : provs) {
					Organization providerOrg = fixUtil.getOrganization(fixUtil.getRealName(fiName, prov.trim()));
					if (providerOrg != null) {
						providerOrgs.add(providerOrg);
					}
				}
			}
		}
		request.setToOrganizations(providerOrgs);
		if (aggRequest) {
			request.setIsAggregationRequest(true);
			int depth = quoteRequest.isSetMarketDepth() ? quoteRequest.getMarketDepth() : -1;
			AggregationMethod aggregationMethod = AggregationMethod.RFS_FULL_BOOK;
            if (1 == depth) {
                if (isSwap) {
                	aggregationMethod = AggregationMethod.RFS_SWAP_BEST_PRICE;
                } else {
                	aggregationMethod = AggregationMethod.RFS_BEST_PRICE;
                } 
            } else {
            	if (isSwap) {
            		aggregationMethod = AggregationMethod.RFS_SWAP_FULL_BOOK;
            	}
            }
        	request.setAggregationMethod(aggregationMethod);
		}

		FXLegDealingPrice fxLegDealingPrice = FXDealingFactory.newFXLegDealingPrice();
		FXLegDealingPrice fxLegDealingPrice_far = null;

		char side = group.getSide().getValue();

		// this is done as the side comes for the far leg in case of FXSwap.
		if ( isSwap && side != ' ' )
		{
			side = (side == Side.BUY) ? Side.SELL : Side.BUY;
		}

		int bidOfferMode_far = DealingPrice.BID;

		if ( side == ' ' )//if blank then two way trade.
		{
			fxLegDealingPrice.setBidOfferMode(DealingPrice.TWO_WAY);
			bidOfferMode_far = DealingPrice.TWO_WAY;
		}
		else
		{
			if ( isDealingBaseCurrency(currencyPair, dealtCurrency) )
			{
				if ( side == Side.BUY )
				{
					fxLegDealingPrice.setBidOfferMode(DealingPrice.OFFER);
					bidOfferMode_far = DealingPrice.BID;
				}
				else
				{
					fxLegDealingPrice.setBidOfferMode(DealingPrice.BID);
					bidOfferMode_far = DealingPrice.OFFER;
				}
			}
			else
			{
				if ( side == Side.SELL )
				{
					fxLegDealingPrice.setBidOfferMode(DealingPrice.OFFER);
					bidOfferMode_far = DealingPrice.BID;
				}
				else
				{
					fxLegDealingPrice.setBidOfferMode(DealingPrice.BID);
					bidOfferMode_far = DealingPrice.OFFER;
				}
			}
		}
		DateFormat sdf = new SimpleDateFormat(FixConstants.DATE_TIME_FORMAT);
		setNearValueDate(group, fxLegDealingPrice, request.getExternalTradeId(), sdf);
		if ( group.isSetMaturityDate() )
		{
			setFixingDateOrTenor(group.getMaturityDate().getValue(), fxLegDealingPrice, sdf);
		}
		fxLegDealingPrice.setDealtCurrencyProperty(getDealtCurrencyProperty(currencyPair, dealtCurrency));
		fxLegDealingPrice.setDealtCurrency(dealtCurrency);
		fxLegDealingPrice.setSettledCurrency(settledCurrency);
		fxLegDealingPrice.setDealtAmount(group.getOrderQty().getValue());

		if ( isSwap )
		{
			//Set Far Leg
			fxLegDealingPrice_far = FXDealingFactory.newFXLegDealingPrice();
			fxLegDealingPrice_far.setBidOfferMode(bidOfferMode_far);
			setFarValueDate(group, fxLegDealingPrice_far, sdf);
			if ( group.isSetFarMaturityDate() )
			{
				setFixingDateOrTenor(group.getFarMaturityDate(), fxLegDealingPrice_far, sdf);

			}
			if ( fxLegDealingPrice.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1) )
			{
				fxLegDealingPrice_far.setDealtCurrencyProperty(FXLegDealingPrice.CCY1);
			}
			else
			{
				fxLegDealingPrice_far.setDealtCurrencyProperty(FXLegDealingPrice.CCY2);
			}
			fxLegDealingPrice_far.setDealtCurrency(dealtCurrency);
			fxLegDealingPrice_far.setSettledCurrency(settledCurrency);
			fxLegDealingPrice_far.setDealtAmount(group.getOrderQty2().getValue());
			//Build Request prices
			fxLegDealingPrice.setName(FixConstants.NEAR_LEG);
			fxLegDealingPrice_far.setName(FixConstants.FAR_LEG);

			if ( group.isSetPrice() || group.isSetPrice2() )
			{
				String baseccy = CurrencyFactory.getBaseCurrency(ccyPair);
				com.integral.finance.currency.Currency bc = ccyPair.startsWith(dealtCcy) ? dealtCurrency : settledCurrency;
				com.integral.finance.currency.Currency tc = ccyPair.startsWith(dealtCcy) ? settledCurrency : dealtCurrency;

				request.setFSRRequest(true);
				FXPrice price = FXPriceFactory.newFXPrice();
				// if Price (tag 44) is set then fix near leg price
				if ( group.isSetPrice() )
				{
					FXRate bid = FXFactory.newFXRate(bc, tc, QuoteConventionUtilC.getInstance().getFXRateConvention(customerOrg));
					double price1 = group.getPrice().getValue();
					// setting un-rounded price with Request so that it can be rounded as per the quote convention set
					// between real lp --> PB relationship
					request.setFixedBidSpotRate(price1);
					request.setFixedOfferSpotRate(price1);
					bid.setSpotRate(price1);
					price.setBid(bid);
					FXRate offer = FXFactory.newFXRate(bc, tc, QuoteConventionUtilC.getInstance().getFXRateConvention(customerOrg));
					offer.setSpotRate(price1);
					price.setOffer(offer);
					FXDealingPriceElement priceElement = FXDealingFactory.newFXDealingPriceElementDependent();
					priceElement.setName("SpotPrice");
					priceElement.setPrice(price);
					fxLegDealingPrice.setPriceElement(priceElement);
				}
				//// if Price2 (tag 640) is set then fix Far leg price
				else if ( group.isSetPrice2() )
				{
					FXRate bid = FXFactory.newFXRate(bc, tc, QuoteConventionUtilC.getInstance().getFXRateConvention(customerOrg));
					double price2 = group.getPrice2().getValue();
					// setting un-rounded price with Request so that it can be rounded as per the quote convention set
					// between real lp --> PB relationship
					request.setFixedBidSpotRate(price2);
					request.setFixedOfferSpotRate(price2);
					request.setFixedFarLegFSRRequest(true);
					bid.setSpotRate(price2);
					bid.setRate(price2);
					price.setBid(bid);
					FXRate offer = FXFactory.newFXRate(bc, tc, QuoteConventionUtilC.getInstance().getFXRateConvention(customerOrg));
					offer.setSpotRate(price2);
					offer.setRate(price2);
					price.setOffer(offer);
					FXDealingPriceElement priceElement = FXDealingFactory.newFXDealingPriceElementDependent();
					priceElement.setName("SpotPrice");
					priceElement.setPrice(price);
					fxLegDealingPrice_far.setPriceElement(priceElement);
				}

			}

			request.setRequestPrice(FixConstants.NEAR_LEG, fxLegDealingPrice);
			request.setRequestPrice(FixConstants.FAR_LEG, fxLegDealingPrice_far);
		}
		else
		{
			fxLegDealingPrice.setName(FixConstants.SINGLE_LEG);
			request.setRequestPrice(FixConstants.SINGLE_LEG, fxLegDealingPrice);
		}
		if ( log.isDebugEnabled() )
		{
			log.debug("FixCommonTranslator.createTradeRequest request counterparty " + request.getCounterparty().getShortName());
			log.debug("FixCommonTranslator.createTradeRequest request organization " + request.getOrganization().getShortName());
			log.debug("FixCommonTranslator.createTradeRequest request toorganization " + (request.getToOrganizations().iterator().next()).getShortName());
		}
		wfm.setObject(request);
		clientCache.storeRFS(sessionID, request);
		MessageHandler handler = null;
		if (aggRequest) {
			handler = configuration.getAggregatedRFSHandler(user, quoteRequest.getQuoteReqID().getValue(), senderSubID, sessionID, onBehalfOfCompID, onBehalfOfSubID);
		} else {
			handler = configuration.getRFSHandler(user, quoteRequest.getQuoteReqID().getValue(), senderSubID, sessionID, onBehalfOfCompID, onBehalfOfSubID);
		}
		if ( handler == null )
		{
			ErrorMessage err = MessageFactory.newErrorMessage();
			err.setCode(FixConstants.INTERNAL_SERVER_ERROR);
			wfm.addError(err);
			wfm.setStatus(MessageStatus.FAILURE);
			return wfm;
		}
		if (handler instanceof AggregatedRFSRateHandler) {
			((AggregatedRFSRateHandler)handler).setRequest(request);
		}
		Collection<Organization> toOrgs = request.getToOrganizations();
		if (toOrgs != null) {
			Set<String> provShortNames = new HashSet<String>();
			for (Organization toOrg : toOrgs) {
				if (toOrg != null) {
					provShortNames.add(toOrg.getShortName());
				}
			}
			((RFSHandler)handler).setProviderOrgs(provShortNames);
		}
		
		wfm.setParameterValue(FixConstants.MESSAGE_HANDLER, handler);
		wfm.setParameterValue(FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_RFS);
		setTradeSubChannel(wfm, user, FixConstants.RFS_TRADE_CHANNEL);
		request.getRequestAttributes().setSessionId(sessionID.toString());
		SefWorkFlow.acceptsParameters(request, group, SefWorkFlow.State.GET_RFS_REQUEST);
		setSEFParametersOnQuoteRequest(wfm, group);
		UTIWorkFlowHelper.updateWorkFlowMsg(wfm, group);
		UPIWorkFlowHelper.updateWorkFlowMsg(wfm, group);
		RTNWorkFlowHelper.updateWorkFlowMsg(wfm, quoteRequest);
		MiFIDHandlerFactory.getInstance().getMiFIDHandler().processQuoteRequest(quoteRequest,request);
		setSettlementInstructions(wfm, group);
		setCustomParameters ( quoteRequest, request );
		return wfm;
	}
	
    //Returns provider list if specified, and related LP orgs if not specified in the request
    private List<Organization> getProviderList( Organization customerOrg, List<String> providers, CurrencyPair ccyPair )
    {
        List<Organization> providerOrgs;
        if ( providers == null || providers.size() == 0 )
        {
            providerOrgs = getRelatedLPOrgs( customerOrg );
        }
        else
        {
            providerOrgs = new ArrayList<Organization>();
            Organization provider;
            if (providers.contains(ISCommonConstants.FMA_LP_ID)) {
                providerOrgs.addAll(getExecutionProviders(customerOrg, ccyPair));
            }
            else {
                for ( String org : providers )
                {
                    provider = ReferenceDataCacheC.getInstance().getOrganization( org );
                    if ( provider != null )
                    {
                        providerOrgs.add( provider );
                    }
                    else
                    {
                        log.warn( "getProviderList: Invalid provider name " + org );
                    }
                }
            }
        }
        return providerOrgs;
    }
    
    private List<Organization> getExecutionProviders(Organization customerOrg, CurrencyPair ccyPair)
    {
        List<Organization> providerList = new ArrayList<Organization>();

        LiquidityProvision liquidityRule = ISUtilImpl.getInstance().getLiquidityProvision(null, customerOrg, ccyPair);
        if ( liquidityRule != null )
        {
            Collection<Organization> lpOrgs = liquidityRule.getExecutionProviders();
            for ( Organization lpOrg : lpOrgs ) {
                if (lpOrg.isFMALP()) {
                    providerList.add(lpOrg);
                }
            }
        }

        return providerList;
    }
    
    private List<Organization> getRelatedLPOrgs( Organization customerOrg )
    {
        Collection<Organization> relatedOrgs = customerOrg.getRelatedOrganizations( ISCommonConstants.FI_ORG_RELATIONSHIP );
        List<Organization> lpOrgs = new ArrayList<Organization>();
        lpOrgs.addAll( relatedOrgs );

        if ( relatedOrgs != null )
        {
            for ( Organization lpOrg : relatedOrgs )
            {
                // Removing system level FXI orgs and inactive orgs
                if ( (lpOrg.getShortName().equalsIgnoreCase( "FXI" )) )
                {
                    lpOrgs.remove( lpOrg );
                }
                if ( !lpOrg.isActive() )
                {
                    lpOrgs.remove( lpOrg );
                }
            }
        }
        return lpOrgs;
    }

	private void setSettlementInstructions(WorkflowMessage wfm, NoRelatedSym group)  throws FieldNotFound {
		String stlmtInst = group.isSetSettlementInst() ? group.getSettlementInst() : null;
		wfm.setParameterValue(FixConstants.SETTLEMENT_INSTRUCTIONS, stlmtInst);
	}
	
	private void setSettlementInstructions(WorkflowMessage wfm, NewOrderSingle newOrderSingle )   throws FieldNotFound{
		String stlmtInst = newOrderSingle.isSetSettlementInst() ? newOrderSingle.getSettlementInst() : null;
		wfm.setParameterValue(FixConstants.SETTLEMENT_INSTRUCTIONS, stlmtInst);
	}

	public WorkflowMessage getRFSQuoteWithdrawRequest( Request request, SessionID sessionID )
	{
		WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
		wfm.setStatus(MessageStatus.SUCCESS);
		wfm.setEvent(MessageEvent.WITHDRAW);
		wfm.setTopic(FixConstants.MSG_TOPIC_REQUEST);
		wfm.setSender(clientCache.getUser(sessionID));
		wfm.setParameterValue(FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_RFS);
		wfm.setObject(request);
		return wfm;
	}

	protected void setNearValueDate( com.integral.fix.client.message.grp.NoRelatedSym group, FXLegDealingPrice fxLegDealingPrice, String clOrdId, DateFormat sdf ) throws FieldNotFound
	{
		if ( group.isSetField(FutSettDate.FIELD) )
		{
			IdcDate date = null;
			String futSettDate = group.getFutSettDate().getValue();
			if ( "".equals(futSettDate) && !group.isSetField(MaturityDate.FIELD) )
			{
				fxLegDealingPrice.setTenor(FixConstants.SPOT_TENOR);
			}
			else
			{
				try
				{
					date = DateTimeFactory.newDate(sdf.parse(futSettDate));
					fxLegDealingPrice.setValueDate(date);
				}
				catch ( ParseException e )
				{
					try
					{
						fxLegDealingPrice.setTenor(new Tenor(futSettDate));
					}
					catch ( IdcIllegalArgumentException ex )
					{
						log.error("FixCommonTranslator.setNearValueDate: FutSettDate given in QuoteRequest is nethier a proper Date or proper Tenor Value.", ex);
					}
				}
			}
		}
		else if ( !group.isSetField(MaturityDate.FIELD) )
		{
			fxLegDealingPrice.setTenor(FixConstants.SPOT_TENOR);
			log.warn("FixCommonTranslator.setNearValueDate - FutSettDate not set. ClOrdId - " + clOrdId + "  Using default -" + Tenor.SPOT);
		}
	}

	protected void setOutrightTenor( NewOrderSingle newOrderSingle, WorkflowMessage wfm, DateFormat sdf ) throws FieldNotFound
	{
		if ( newOrderSingle.isSetField(FutSettDate.FIELD) )
		{
			String futSettDate = newOrderSingle.getString(FutSettDate.FIELD);
			if ( futSettDate != null && !"".equals(futSettDate) )
			{
				try
				{
					wfm.setParameterValue(ISCommonConstants.WF_PARAM_OUTRIGHT_VALUE_DATE, sdf.parse(futSettDate));
				}
				catch ( ParseException e )
				{
					try
					{
						wfm.setParameterValue(ISCommonConstants.WF_PARAM_OUTRIGHT_TENOR, futSettDate);
					}
					catch ( IdcIllegalArgumentException ex )
					{
						log.error("FixCommonTranslator.setOutrightTenor: FutSettDate given in NewOrderSIngle is nethier a proper Date or proper Tenor Value.", ex);
					}
				}
			}
		}
	}

	protected void setOutrightFixingDate( NewOrderSingle newOrderSingle, WorkflowMessage wfm, DateFormat sdf ) throws FieldNotFound
	{
		if ( newOrderSingle.isSetMaturityDate() )
		{
			String fixingDate = newOrderSingle.getMaturityDate().getValue();
			fixingDate = fixingDate == null ? "" : fixingDate.trim();
			if ( fixingDate.length() > 0 )
			{
				try
				{
					wfm.setParameterValue(ISCommonConstants.WF_PARAM_OUTRIGHT_FIXING_DATE, sdf.parse(fixingDate));
				}
				catch ( ParseException e )
				{
					try
					{
						wfm.setParameterValue(ISCommonConstants.WF_PARAM_OUTRIGHT_FIXING_TENOR, fixingDate);
					}
					catch ( IdcIllegalArgumentException ex )
					{
						log.error("FixCommonTranslator.setOutrightFixingDate: FixingDate given in NewOrderSingle is neither a proper Date or proper Tenor Value.", ex);
					}
				}
			}
		}
	}

	protected void setLegValueDate( com.integral.fix.client.message.grp.Group groupWrapper, TradeRequestLeg tradeRequestLeg, String clOrdId, DateFormat sdf ) throws FieldNotFound
	{
		Group group = groupWrapper.getGroup();
		if ( group.isSetField(LegSettlDate.FIELD) )
		{
			IdcDate date = null;
			String legSettDate = group.getField(new LegSettlDate()).getValue();
			if ( "".equals(legSettDate) && !group.isSetField(MaturityDate.FIELD) )
			{
				tradeRequestLeg.setTenor(FixConstants.SPOT_TENOR.getName());
			}
			else
			{
				try
				{
					date = DateTimeFactory.newDate(sdf.parse(legSettDate));
					tradeRequestLeg.setValueDate(date);
				}
				catch ( ParseException e )
				{
					try
					{
						tradeRequestLeg.setTenor(new Tenor(legSettDate).getName());
					}
					catch ( IdcIllegalArgumentException ex )
					{
						log.error("FCT.setLegValueDate: LegSettDate given in NoLeg is nethier a proper Date or proper Tenor Value.", ex);
					}
				}
			}
		}
		else if ( !group.isSetField(MaturityDate.FIELD) )
		{
			tradeRequestLeg.setTenor(FixConstants.SPOT_TENOR.getName());
			log.warn("FCT.setLegValueDate - LegSettDate not set. ClOrdId - " + clOrdId + "  Using default -" + Tenor.SPOT);
		}
	}
	
	protected void setLegFixingDate(com.integral.fix.client.message.grp.Group groupWrapper, TradeRequestLeg tradeRequestLeg, String clOrdId, DateFormat sdf) throws FieldNotFound
	{
		Group group = groupWrapper.getGroup();
		if (group.isSetField(MaturityDate.FIELD))
		{
			IdcDate date = null;
			String legFixingDate = group.getField(new MaturityDate()).getValue();
			if (!StringUtils.isNullOrEmptyString(legFixingDate))
			{
				try
				{
					date = DateTimeFactory.newDate(sdf.parse(legFixingDate));
					tradeRequestLeg.setFixingDate(date);
				}
				catch (ParseException e)
				{
					try
					{
						tradeRequestLeg.setFixingTenor(new Tenor(legFixingDate).getName());
					}
					catch (IdcIllegalArgumentException ex)
					{
						log.error("FCT.setLegFixingDate: legFixingDate given in NoLeg is nethier a proper Date or proper Tenor Value.", ex);
					}
				}
			}
		}
	}

	protected void setFixingDateOrTenor(String fixingDateOrTenor ,FXLegDealingPrice fxLegDealingPrice, DateFormat sdf)
	{
		IdcDate date;
		fixingDateOrTenor = fixingDateOrTenor == null ? "" : fixingDateOrTenor.trim();
		if ( fixingDateOrTenor.length() > 0 )
		{
			try
			{
				date = DateTimeFactory.newDate(sdf.parse(fixingDateOrTenor));
				fxLegDealingPrice.setFixingDate(date);
			}
			catch ( ParseException e )
			{
				try
				{
					fxLegDealingPrice.setFixingTenor(new Tenor(fixingDateOrTenor));
				}
				catch ( IdcIllegalArgumentException ex )
				{
					log.error("FixCommonTranslator.setFixingDate: FixingDate given in QuoteRequest is neither a proper Date or proper Tenor Value.", ex);
				}
			}
		}
	}


	protected void setFarValueDate( com.integral.fix.client.message.grp.NoRelatedSym group, FXLegDealingPrice fxLegDealingPrice, DateFormat sdf ) throws FieldNotFound
	{
		if ( group.isSetField(FutSettDate2.FIELD) )
		{
			IdcDate date = null;
			String futSettDate2 = group.getFutSettDate2().getValue();
			if ( "".equals(futSettDate2) && !group.isSetFarMaturityDate() )
			{
				fxLegDealingPrice.setTenor(FixConstants.SPOT_TENOR);
			}
			else
			{
				try
				{
					date = DateTimeFactory.newDate(sdf.parse(futSettDate2));
					fxLegDealingPrice.setValueDate(date);
				}
				catch ( ParseException e )
				{
					try
					{
						fxLegDealingPrice.setTenor(new Tenor(futSettDate2));
					}
					catch ( IdcIllegalArgumentException ex )
					{
						log.error("FixCommonTranslator.setFarValueDate: FutSettDate2 given in QuoteRequest is nethier a proper Date or proper Tenor Value.", ex);
					}
				}
			}
		}
		else if(!group.isSetFarMaturityDate())
		{
			fxLegDealingPrice.setTenor(FixConstants.SPOT_TENOR);
		}
	}

	protected boolean setOutrightTenor( NewOrderSingle newOrderSingle, Order order, DateFormat sdf ) throws FieldNotFound
	{
        String futSettDate = "";
		if ( newOrderSingle.isSetField( FutSettDate.FIELD ) )
		{
			futSettDate = newOrderSingle.getString( FutSettDate.FIELD );
		}

        boolean isValueDateBlankAllowed = NettingMBeanC.getInstance().isValueDateBlankAllowed();
        User user = order.getUser();
		if ( isValueDateBlankAllowed || ( !"".equals( futSettDate ) ) )
		{
			if ( user != null && user.getOrganization() != null )
			{
				IdcDate valueDate = StagingAreaUtils.getValueDateFromDateString( null, futSettDate,
						user.getOrganization(), order.getCcyPair(), sdf );
				if ( valueDate != null )
				{
					order.setValueDate( valueDate.asJdkDate().getTime() );
					return true;
				}
			}
		}
		return false;
	}


	public OrderCancelReject getOrderCancelReject( Request request, String cancelRequestID, boolean isOrderCancelReplace, SessionID sessionID )
	{
		return getOrderCancelReject(request, cancelRequestID, isOrderCancelReplace, sessionID, null);
	}

	public Double roundSpotRate( FXRateBasis rb, double rate, com.integral.finance.instrument.Instrument varCurrency )
	{
		double formattedRate = 0.0;
		if ( rb != null )
		{
			if ( isRateInverted(rb, varCurrency) )
			{

				formattedRate = MathUtil.round(rate, rb.getInverseForwardRatePrecision(), rb.getRoundingType());
			}
			else
			{

				formattedRate = MathUtil.round(rate, rb.getForwardRatePrecision(), rb.getRoundingType());
			}

		}
		return formattedRate;
	}

	public boolean isRateInverted( FXRateBasis r, com.integral.finance.instrument.Instrument varCurrency )
	{
		if ( r == null || r.getVariableCurrency() == null || varCurrency == null )
		{
			return false;
		}
		return r.getVariableCurrency().getObjectID() != varCurrency.getObjectID();
	}

	public Double roundSpotRate( FXRateBasis rb, double rate, com.integral.finance.instrument.Instrument varCurrency, DecimalFormat df )
	{
		if ( rb != null )
		{
			if ( isRateInverted(rb, varCurrency) )
			{
				df.setMaximumFractionDigits(rb.getInverseForwardRatePrecision());
				df.setMinimumFractionDigits(rb.getInverseSpotPrecision());
				rate = MathUtil.round(rate, rb.getInverseForwardRatePrecision(), rb.getRoundingType());
			}
			else
			{
				df.setMaximumFractionDigits(rb.getForwardRatePrecision());
				df.setMinimumFractionDigits(rb.getSpotPrecision());
				rate = MathUtil.round(rate, rb.getForwardRatePrecision(), rb.getRoundingType());
			}
			//return  rate ;
		}
		return rate;
	}

	/* (non-Javadoc)
	  * @see com.integral.fix.client.translator.FixTranslator#getOrderAmendMessage(com.integral.fix.client.message.OrderCancelReplaceRequest, com.integral.finance.dealing.Request)
	  */
	public WorkflowMessage getOrderAmendMessage( OrderCancelReplaceRequest ocr, Request request ) throws FieldNotFound
	{
		// create message
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setMessageId(0);
		msg.setSender(request.getUser());
		msg.setEvent(MessageEvent.AMEND);
		msg.setTopic(FixConstants.MSG_TOPIC_REQUEST);

		msg.setObject(request);

		msg.setParameterValue(OrderConstants.WF_Param_ClOrdId, ocr.getClOrdID().getValue());
		msg.setParameterValue(ISCommonConstants.WF_PARAM_NEW_ORDER_AMOUNT, ocr.getOrderQty().getValue());

		if ( ocr.isSetPrice() )
		{
			msg.setParameterValue(ISCommonConstants.WF_PARAM_NEW_ORDER_RATE, ocr.getPrice().getValue());
		}

		//if true then max show is not in NOS as well
		if ( !ocr.isSetMaxShow() )//Max show is needs to be reset to order amount.
		{
			msg.setParameterValue(ISCommonConstants.WF_PARAM_RESET_MAX_SHOW, Boolean.TRUE);
		}

		return msg;
	}

	public Message getAllocationPortfolioQuote( Quote quote, String quoteRequestID, String senderSubID,
                                                String onBehalfOfCompID, String onBehalfOfSenderSubID,
                                                SessionID sessionId, String quoteId, String providerName,
                                                String quoteType )
	{
		String portfolioId = quote.getRequest().getExternalRequestId();
		NettingPortfolio portfolio = PortfolioServiceCache.getInstance().retrieve(portfolioId);
		PortfolioPriceCalculator.pricePortfolio(portfolio, quote);
		com.integral.fix.client.message.Quote fixQuote = getRFSQuote(quote, quoteRequestID,
                                                        senderSubID, onBehalfOfCompID, onBehalfOfSenderSubID, sessionId);

		Collection<NettingTradeRequest> nettingTradeRequests = portfolio.getInputRequestPojos();
		for ( NettingTradeRequest ntr : nettingTradeRequests )
		{
			if ( !fixQuote.isSetField(Symbol.FIELD))
			{
				fixQuote.set(new Symbol(ntr.getCcyPair().getName()));
			}
			for ( TradeRequestLeg trdleg : ntr.getTradeLegs() )
			{
                QuoteNoLegs noLegs = fixQuote.createNoLegs();
				double spotRate = trdleg.getMarketSpotRate();
				double mrktFwdPoints = trdleg.getMarketForwardPoints();

				char type = '0';
				if ( trdleg.getBaseCurrency().isSameAs(trdleg.getDealtCurrency()) )
				{
					if ( trdleg.isBuyingBase() )
					{
						type = '1';
					}
					else

					{
						type = '2';
					}
				}
				else
				{
					if ( trdleg.isBuyingBase() )
					{
						type = '2'; //side is w.r.t to term currency not base.
					}
					else
					{
						type = '1'; //side is w.r.t to term currency not base.
					}
				}
				noLegs.setLegSymbol(new LegSymbol(ntr.getCcyPair().getName()));
				noLegs.setLegRefID(new LegRefID(trdleg.getExternalLegId()));
				noLegs.setLegSettlDate(new LegSettlDate(trdleg.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
				noLegs.setLegSide(new LegSide(type));
				noLegs.setLegCurrency(new LegCurrency(trdleg.getDealtCurrency().getShortName()));
				// TODO LegQty no optional here .... ??
				// noLegs.set(new LegQty());
				if ( !trdleg.isBuyingBase() )
				{
					noLegs.setLegBidPx(new LegBidPx(spotRate));
					noLegs.setDouble(1067, mrktFwdPoints);

				}
				else
				{
					noLegs.setLegOfferPx(new LegOfferPx(spotRate));
					noLegs.setDouble(1068, mrktFwdPoints);
				}
				fixQuote.addGroup(noLegs);
			}
		}

		if ( senderSubID != null )
		{
			fixQuote.getHeader().setField(new TargetSubID(senderSubID));
		}
		else
		{
			if ( onBehalfOfSenderSubID != null )
			{
				fixQuote.getHeader().setField(new DeliverToSubID(onBehalfOfSenderSubID));
			}
			if ( onBehalfOfCompID != null )
			{
				fixQuote.getHeader().setField(new DeliverToCompID(onBehalfOfCompID));
			}
		}
		//String maskedName = FixUtilC.getInstance().getMaskedName(request.getOrganization().getShortName(), orgName);
		fixQuote.getHeader().setField(new OnBehalfOfCompID(providerName));
		return fixQuote.getMessage();
	}

	public Message getPortfolioQuote( NettingPortfolio quote, String quoteRequestID, String senderSubID,
                                      String onBehalfOfCompID, String onBehalfOfSenderSubID,
                                      SessionID sessionId, String quoteId, String providerName, String quoteType )
	{
        com.integral.fix.client.message.Quote fixQuote = FIXMessageFactory.getInstance().newQuote(sessionId);
		fixQuote.set(new QuoteID(quoteId));
		fixQuote.set(new QuoteReqID(quoteRequestID));
		boolean isTradable = quoteType != null && ISCommonConstants.QTE_CLSF_TRADEABLE.equals(quoteType);
		if ( isTradable ){
			fixQuote.set(new QuoteType(QuoteType.TRADEABLE));
		}
		else{
			fixQuote.set(new QuoteType(QuoteType.INDICATIVE));
		}
		Collection<NettingTradeRequest> nettingTradeRequests = quote.getInputRequestPojos();
		boolean shouldSetMidSpotRate = true;
		for ( NettingTradeRequest ntr : nettingTradeRequests ){
			if ( !fixQuote.isSetSymbol() ){
				fixQuote.set(new Symbol(ntr.getCcyPair().getName()));
			}
			for ( TradeRequestLeg trdleg : ntr.getTradeLegs() ){
                QuoteNoLegs noLegs = fixQuote.createNoLegs();
				double spotRate = trdleg.getMarketSpotRate();
				double mrktFwdPoints = trdleg.getMarketForwardPoints();

				char type = '0';
				if ( trdleg.getBaseCurrency().isSameAs(trdleg.getDealtCurrency()) ){
					if ( trdleg.isBuyingBase() ){
						type = '1';
					}
					else{
						type = '2';
					}
				}
				else{
					if ( trdleg.isBuyingBase() ){
						type = '2'; //side is w.r.t to term currency not base.
					}
					else{
						type = '1'; //side is w.r.t to term currency not base.
					}
				}
				int FIX_FIELD_LEG_REF_ID = 654;
				noLegs.set(new LegSymbol(ntr.getCcyPair().getName()));
				noLegs.setString(FIX_FIELD_LEG_REF_ID, trdleg.getExternalLegId());
				noLegs.set(new LegSettlDate(trdleg.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
				noLegs.set(new LegSide(type));
				noLegs.set(new LegCurrency(trdleg.getDealtCurrency().getShortName()));
				// TODO LegQty no optional here .... ??
				// noLegs.set(new LegQty());
				if( quote.isUndisclosedRequest() ){
                    boolean isSSPtoSWAP = TradeConfigurationFactory.getTradeConfigurationMBean().isSSPTwoUndisclosedLegsAsSwap(quote.getUser().getOrganization());

					MarketPrice price = trdleg.getMarketPrice();

					if( !fixQuote.isSetBidSpotRate() ){
						fixQuote.set(new BidSpotRate(price.getBidSpotRate()));
					}

					if( !fixQuote.isSetOfferSpotRate() ){
						fixQuote.set(new OfferSpotRate(price.getOfferSpotRate()));
					}

					LegBidPx bidSpot = isSSPtoSWAP? new LegBidPx(price.getBidSpotRate()): new LegBidPx();
					noLegs.set(bidSpot);
					noLegs.setDouble(1067, price.getBidForwardPoints());

					LegOfferPx offerSpot = isSSPtoSWAP? new LegOfferPx(price.getOfferSpotRate()) : new LegOfferPx();
					noLegs.set(offerSpot);
					noLegs.setDouble(1068, price.getOfferForwardPoints());
					noLegs.set(new LegSide(Side.UNDISCLOSED));
				}
				else
				{
					if ( !trdleg.isBuyingBase() ){
						LegBidPx bidSpot = new LegBidPx(spotRate);
						noLegs.set(bidSpot);
						noLegs.setDouble(1067, mrktFwdPoints);

					}
					else{
						LegOfferPx offerSpot = new LegOfferPx(spotRate);
						noLegs.set(offerSpot);
						noLegs.setDouble(1068, mrktFwdPoints);
					}
				}
				MarketPrice pr = trdleg.getMarketPrice();
				if(	   SEFUtilC.hasMidRateInSSPQuotes(quote.getOrganization(), quote.getUser().getDefaultDealingEntity())
					&& pr != null) {
					noLegs.setDouble(7632, pr.getMidRate());
				}

				// set the mid spot rate based on configuration.
				if( 	shouldSetMidSpotRate
					&& SEFUtilC.hasMidSpotRateInSSPQuotes( quote.getOrganization(), quote.getUser().getDefaultDealingEntity() )
					&& pr != null ){
					log.info ( "CT.getPortfolioQuote : midSpotRate=" + pr.getMidSpotRate()
								+ ",midRate=" + pr.getMidRate () + ",portfolio id=" + quote.getPortfolioID () );
					fixQuote.setDouble(631, pr.getMidSpotRate());
					shouldSetMidSpotRate = false;
				}
				MiFIDHandlerFactory.getInstance().getMiFIDHandler().populateISINQuote(noLegs,trdleg);
				fixQuote.addGroup(noLegs);
			}
		}
		if ( senderSubID != null ){
			fixQuote.getHeader().setField(new TargetSubID(senderSubID));
		}
		else{
			if ( onBehalfOfSenderSubID != null ){
				fixQuote.getHeader().setField(new DeliverToSubID(onBehalfOfSenderSubID));
			}
			if ( onBehalfOfCompID != null ){
				fixQuote.getHeader().setField(new DeliverToCompID(onBehalfOfCompID));
			}
		}
		//String maskedName = FixUtilC.getInstance().getMaskedName(request.getOrganization().getShortName(), orgName);
		fixQuote.getHeader().setField(new OnBehalfOfCompID(providerName));
		MiFIDHandlerFactory.getInstance().getMiFIDHandler().populateQuote(fixQuote,quote);
		return fixQuote.getMessage();
	}

	private Set<User> getUserFromEmailAddress(final Organization org, final String emailAddress)
	{
		Set<User> foundUsers = new HashSet<User>();
		Collection<User> users = org.getUsers();
		if (users == null || users.size() == 0)
		{
			return null;
		}
		for (User user : users)
		{
			if (user == null)
			{
				continue;
			}
			UserContact userContact = user.getContact();
			if(userContact == null)
			{
				continue;
			}
			String userEmailAddress = userContact.getEmailAddress();
			if (emailAddress.equalsIgnoreCase(userEmailAddress))
			{
				foundUsers.add(user);
			}
		}
		return foundUsers;
	}

	public WorkflowMessage getStagedOrder(NewOrderSingle newOrderSingle, SessionID sessionID, MessageEvent messageEvent) throws FieldNotFound
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setMessageId(0);
		msg.setEvent(messageEvent);
		msg.setTopic(FixConstants.MSG_TOPIC_ORDER_BATCH);
		User user = clientCache.getUser(sessionID);
		User tradingUser = null;
		Order order = new Order();
		msg.setObject(order);
		order.setSource(StagingAreaConstants.STAGING_ORDER_SOURCE_FIX);
		Tuple<Integer, String> tradingUserTuple = FixUtilC.getTradingUser(newOrderSingle.getMessage());
		if(tradingUserTuple == null)
		{
			msg.addError("ORDER_ORIGINATING_USER_NOT_PRESENT_IN_ORDER");
			return msg;
		}
		else
		{
			int tagNo = tradingUserTuple.first;
			String tradingUserStr = tradingUserTuple.second;
			Organization sessionUserOrg = user.getOrganization();
			String userOrgName = sessionUserOrg.getShortName();
			if(tradingUserStr != null)
			{
				// if it's email address then retrieve user from that
				boolean useEmailAddress = StagingServiceConfig.getInstance().useEmailAddressForUser(userOrgName);
				if (useEmailAddress && tradingUserStr.indexOf(EMAIL_ADDRESS_AT) != -1)
				{
					// get user
					Set<User> tradingUsers = getUserFromEmailAddress(sessionUserOrg, tradingUserStr);
					if (tradingUsers != null && tradingUsers.size() > 0)
					{
						if (tradingUsers.size() > 1)
						{
							msg.addError("SAME_EMAIL_ADDRESS_FOR_MULTIPLE_USERS");
							return msg;
						}
						else
						{
							tradingUser = tradingUsers.iterator().next();
						}
					}
				}
				else
				{

					tradingUser = UserFactory.getUser(new StringBuilder(tradingUserStr).
							append(FixConstants.DELIMITER_USER_ORGANIZATION).append(userOrgName).toString());
				}

				if(tradingUser == null)
				{
					msg.addError("ORDER_ORIGINATING_USER_NOT_PRESENT_IN_ORDER");
					return msg;
				}
				else
				{
					order.setTraderUserTagNumber(tagNo);
				}
			}
		}

		DateFormat sdf = new SimpleDateFormat(FixConstants.DATE_TIME_FORMAT);
		sdf.setLenient(false);
		msg.setSender(tradingUser);
		order.set_id(StageOrderIdProvider.getInstance().nextId());
		order.setClientOrderId(newOrderSingle.getClOrdID().getValue());
		String dealtCurrency = newOrderSingle.getCurrency().getValue();
		order.setDealtCcy(dealtCurrency);

		//validate allocation Details
		List<AllocationDetail> allocationDetails = getAllocationDetails(newOrderSingle, msg, user, order.getDealtCcy());
		if(allocationDetails == null)
		{
			return msg;
		}

		String origAccount = FixUtilC.getTag1Account(newOrderSingle.getMessage(), allocationDetails.get( 0 ));
		order.setOrigAccount( origAccount );
		order.setAllocationDetails(FixUtilC.getProcessedAllocationDetails(allocationDetails, user));
		order.setAccount( FixUtilC.getProcessedAccount( user, origAccount ) );
		order.setNamespace(user.getNamespace());
		order.setNamespaceName(user.getNamespace().getShortName());
		order.setDealtAmt(newOrderSingle.getOrderQty().getValue());
		String currencyPair = newOrderSingle.getSymbol().getValue();
		order.setCcyPair(currencyPair);
		boolean buyDealt = newOrderSingle.getSide().getValue() == Side.BUY;
		order.setBuy(buyDealt);
		String baseCurrency = CurrencyFactory.getBaseCurrency(currencyPair);
		if (dealtCurrency.equals(baseCurrency))
		{
			order.setBuyBase(buyDealt);
		}
		else
		{
			order.setBuyBase(!buyDealt);
		}
		// fix session user which is used to retrieve the client session
		order.setSessionUser(user);
		order.setUser(tradingUser);
		order.setState(OrderStatusType.INTIAL.getCode());
		if(newOrderSingle.isSetPrice())
		{
			order.setPrice(newOrderSingle.getPrice().getValue());
		}
		if(newOrderSingle.getOrdType().getValue() == OrdType.LIMIT || newOrderSingle.getOrdType().getValue() == OrdType.FOREX_LIMIT)
		{
			order.setType(Type.LIMIT);
		}
		else
		{
			order.setType(Type.MARKET);
		}

		Tuple<Integer, String> ccInfoTuple = FixUtilC.getCCTuple(newOrderSingle.getMessage());
		if(ccInfoTuple != null)
		{
			int tagNo = ccInfoTuple.first;
			String maskedName = ccInfoTuple.second;
			//String fiName = user.getOrganization().getShortName();
			Set<String> ccSet = new LinkedHashSet<String>();
			if(maskedName != null)
			{
				String[] ccs = maskedName.split("[;,]");
				for(String cc : ccs)
				{
					ccSet.add(cc.trim());
				}
			}
			order.setInputCC(maskedName);
			order.setCcSet(ccSet);
			order.setCcTagNumber(tagNo);
		}
		// set TIF with order as came with NewOrderSingle
		if(newOrderSingle.isSetTimeInForce())
		{
			// setting by default as GTC for staging order as other TIFs will be rejected anyway
			order.setTif( TIF.GTC);
		}

		if(newOrderSingle.isSetField(OrdType.FIELD))
		{
			OrdType ordType = newOrderSingle.getOrdType();
			char ordTypeVal = ordType.getValue();
			order.setOrdType(ordTypeVal);
		}

		//set value date and check for swaps
        if(!msg.getEvent().equals(MessageEvent.REPLACE)  )
        {
            //validate fields
            String futSettDate = "";
            if ( newOrderSingle.isSetField( FutSettDate.FIELD ) )
            {
                futSettDate = newOrderSingle.getString( FutSettDate.FIELD );
            }

            boolean isValueDateBlankAllowed = NettingMBeanC.getInstance().isValueDateBlankAllowed();
            if ( isValueDateBlankAllowed || ( !"".equals( futSettDate ) ) )
            {
                if ( user.getOrganization() != null )
                {
                    IdcDate valueDate = StagingAreaUtils.getValueDateFromDateString( null, futSettDate,
                            user.getOrganization(), order.getCcyPair(), sdf );
                    if ( valueDate != null )
                    {
                        order.setValueDate( valueDate.asJdkDate().getTime() );

                        // check for swaps
                        if(newOrderSingle.isSetOrderQty2() && newOrderSingle.isSetField( SettlDate2.FIELD ))
                        {
                            String futSettDate2 = newOrderSingle.getString( SettlDate2.FIELD );
                            if("".equals(futSettDate2.trim()))
                            {
                                msg.addError("INVALID_VALUE_DATE_OR_TENOR");
                            }
                            else
                            {
                                IdcDate valueDate2 = StagingAreaUtils.getValueDateFromDateString( null, futSettDate2,
                                        user.getOrganization(), order.getCcyPair(), sdf );


                                if(valueDate2 != null)
                                {
                                    if (valueDate2.isEarlierThanOrEqualTo(valueDate))
                                    {
                                    	msg.addError("FAR_DATE_LESS_THAN_EQUAL_TO_NEAR_DATE");
                                    }
                                    List<Leg> legs = new ArrayList<Leg>();
                                    Leg nearLeg = new Leg( order.getDealtAmt(), order.getValueDate(), order.getTenor(), !order.isBuy(), order.getAccount() );
                                    nearLeg.set_id( StageOrderIdProvider.getInstance().nextId() );
                                    nearLeg.setOrderId( order.getId() );
                                    legs.add( nearLeg );

                                    Leg farLeg = new Leg( newOrderSingle.getOrderQty2().getValue(), valueDate2.asJdkDate().getTime() , null, order.isBuy(), order.getAccount() );
                                    farLeg.set_id( StageOrderIdProvider.getInstance().nextId() );
                                    farLeg.setOrderId( order.getId() );
                                    legs.add( farLeg );

                                    order.setLegs( legs );
                                }
                                else
                                {
                                    msg.addError("INVALID_VALUE_DATE2_OR_TENOR");
                                }
                            }

                        }
                    }
                    else
                    {
                        msg.addError("INVALID_VALUE_DATE_OR_TENOR");
                    }
                }
            }
            else
            {
                msg.addError("INVALID_VALUE_DATE_OR_TENOR");
            }
        }

        // no of legs with order
        List<Leg> orderLegs = order.getLegs();
        if (orderLegs == null)
        {
        	// single leg order
        	order.setNoOfLegs(1);
        }
        else
        {
        	order.setNoOfLegs(orderLegs.size());
        }

		if(newOrderSingle.isSetField(StagingAreaConstants.FIX_FIELD_EXECUTION_STRATEGY))
		{
			int executionStrategy = newOrderSingle.getInt(StagingAreaConstants.FIX_FIELD_EXECUTION_STRATEGY);
			if(StagingAreaConstants.EXECUTION_STRATEGY_AUTO == executionStrategy)
			{
				order.setAutoExecution(true);
			}
		}
		if(newOrderSingle.isSetField(StagingAreaConstants.FIX_TAG_ERISA))
		{
			boolean erisaOrder = newOrderSingle.getMessage().getBoolean(StagingAreaConstants.FIX_TAG_ERISA);
			order.setErisa(erisaOrder);
		}
		if(newOrderSingle.isSetField(StagingAreaConstants.FIX_TAG_40ACT))
		{
			boolean _40AccountOrder = newOrderSingle.getMessage().getBoolean(StagingAreaConstants.FIX_TAG_40ACT);
			order.setFortyAccount(_40AccountOrder);
		}
		populateRestricedCCs(newOrderSingle, order, msg);
		Organization tradingOrg = tradingUser.getOrganization();
		CurrencyPair currPair = CurrencyFactory.getCurrencyPairFromString(currencyPair);
		long valueDateInMilis = order.getValueDate();
		IdcDate valueDate = null;
		if (valueDateInMilis > 0)
		{
			valueDate = DateTimeFactory.newDate(new Date(valueDateInMilis));
		}
		if (tradingOrg.isMiFID() && tradingUser.isMiFIDExecutingUser()
				&& MiFIDUtils.isMiFIDEligibleTenor(tradingOrg, currPair, valueDate))
        {
			populateMIFIDParametersOnOrder(newOrderSingle, order);
			log.info("CommonTranslator.getStagedOrder:MIFID parameters populated for order:" + order.getClientOrderId());
        }
		return msg;
	}


	private void populateMIFIDParametersOnOrder(final NewOrderSingle newOrderSingle, final Order order)
	{
		try
		{
			Message message = newOrderSingle.getMessage();
			List<quickfix.Group> partyGroups = message.getGroups(NoPartyIDs.FIELD);
			String orderOriginationFirmLEI = null;
			String investmentDecisionMaker = null;
			if(partyGroups != null && partyGroups.size() > 0)
			{
		      	for (Group partyGroup : partyGroups)
	        	{
	           		String partyId = partyGroup.getString(PartyID.FIELD);
	        		int partyRole = partyGroup.getInt(PartyRole.FIELD);
	        		if (PartyRole.ORDER_ORIGINATION_FIRM == partyRole)
	        		{
	        			orderOriginationFirmLEI = partyId;
	        		}
	        		else if(FixConstants.MIFID_INVESTMENT_DECISION_MAKER_PARTYROLE == partyRole)
	        		{
	        			investmentDecisionMaker = partyId;
	        		}
	        	}
			}
			if (orderOriginationFirmLEI != null)
			{
				order.addMifidParameter(MIFID_PARAMS_LEGAL_ENTITY, orderOriginationFirmLEI);
			}
			if (investmentDecisionMaker != null)
			{
				order.addMifidParameter(MIFID_PARAMS_INVESTMENT_DECISION_MAKER, investmentDecisionMaker);
			}

			MiFIDMBean mifidMbean = MiFIDMBeanC.getInstance();
			String tradingVenue = mifidMbean.getIntegralMTF();
			if (tradingVenue != null)
			{
				order.addMifidParameter(MIFID_PARAMS_TRADING_VENUE, tradingVenue);
			}
			char tradingCapacity = TradingCapacity.DEAL.getVal();
			order.addMifidParameter(MIFID_PARAMS_TRADING_CAPACITY, "" + tradingCapacity);
			Organization tradingOrg = order.getUser().getOrganization();
			String preTradeDeferral = mifidMbean.getPreTradeWaiverValue(tradingOrg);
			order.addMifidParameter(MIFID_PARAMS_PRETRADE_DEFERRAL, preTradeDeferral);
			String postTradedeferral = mifidMbean.getPostDeferralIndicatorValue(tradingOrg);
			order.addMifidParameter(MIFID_PARAMS_POSTTRADE_DEFERRAL, postTradedeferral);
			String postTradeIndicator = NettingMBeanC.getInstance().getMifidPostTradeIndicator();
			order.addMifidParameter(MIFID_PARAMS_POSTTRADE_INDICATOR, postTradeIndicator);
			int tradeReportingStatus = NettingMBeanC.getInstance().getMifidTradeReportingIndicator();
			order.addMifidParameter(MIFID_PARAMS_TRADE_REPORTING_STATUS, "" + tradeReportingStatus);
			String apaIdentifier =  mifidMbean.getIntegralMTF();
			order.addMifidParameter(MIFID_PARAMS_APA_IDENTIFIER, apaIdentifier);
		}
		catch(Exception e)
		{
			log.info("CommonTranslator.populateMIFIDParametersOnOrder():problem with populating MIFID parameters", e);
		}
	}

	private void populateMIFIDParameterOnExecutionReport(final ExecutionReport fixER, final Order order)
	{
		try
		{
			Map<String, String> mifidParams = order.getMifidParameters();
			if (mifidParams == null || mifidParams.size() ==0)
			{
				return;
			}
			String tradingVenue = mifidParams.get(MIFID_PARAMS_TRADING_VENUE);
			if (tradingVenue != null)
			{
				fixER.addPartyIDGroup(tradingVenue, PartyIDSource.MIC, PartyRole.EXECUTION_VENUE);
			}
			String tradingCapacity = mifidParams.get(MIFID_PARAMS_TRADING_CAPACITY);
			if (tradingCapacity != null)
			{
				fixER.setLastCapacity(tradingCapacity.charAt(0));
			}
			String preTradeWaver = mifidParams.get(MIFID_PARAMS_PRETRADE_DEFERRAL);
			if (preTradeWaver != null)
			{
				int trdRegpublicationReason = TrdRegPublicationReason.getCodeFromPublicationReason(preTradeWaver);
				if (trdRegpublicationReason != -1)
				{
					fixER.setPreTradeWaiverTradePublicationType(trdRegpublicationReason);
				}
			}
			String postTradeWaver = mifidParams.get(MIFID_PARAMS_POSTTRADE_DEFERRAL);
			if (postTradeWaver != null)
			{
				int trdRegpublicationReason = TrdRegPublicationReason.getCodeFromPublicationReason(postTradeWaver);
				if (trdRegpublicationReason != -1)
				{
					fixER.setPostTradeDeferralTradePublicationType(trdRegpublicationReason);
				}
			}
			String postTradeIndicator = mifidParams.get(MIFID_PARAMS_POSTTRADE_INDICATOR);
			if (postTradeIndicator != null)
			{
				int intValue = -1;
				try
				{
					intValue = Integer.parseInt(postTradeIndicator);
				} catch (NumberFormatException ne) {
					log.warn("CommonTranslator.populateMIFIDParameterOnExecutionReport:Problem with parsing Post Trade Indicator value", ne);
					intValue = -1;
				}
				if (intValue != -1)
				{
					fixER.setTradePriceCondition(intValue);
				}
			}
			String tradeReportingStatus = mifidParams.get(MIFID_PARAMS_TRADE_REPORTING_STATUS);
			if (tradeReportingStatus != null)
			{
				int intValue = -1;
				try
				{
					intValue = Integer.parseInt(tradeReportingStatus);
				} catch (NumberFormatException ne) {
					log.warn("CommonTranslator.populateMIFIDParameterOnExecutionReport:Problem with parsing Trade Reporting Indicator value for order:" + order.getId(), ne);
					intValue = -1;
				}
				if (intValue != -1)
				{
					fixER.getMessage().setInt(FIX_TAG_TRADE_REPORTING_STATUS, intValue);
				}
			}
			String uti = mifidParams.get(MIFID_PARAMS_TRANSACTION_ID);
			if (uti != null)
			{
				fixER.addRegTradeIDGroup(uti, FIX_TAG_REG_TRADE_ID_TYPE_VALUE);
			}
			String rtn = mifidParams.get(MIFID_PARAMS_RTN);
			if (rtn != null)
			{
				fixER.setString(FIX_FIELD_RTN, rtn);
			}
			String isin = mifidParams.get(MIFID_PARAMS_ISIN);
			if (isin != null)
			{
				fixER.setString(SecurityID.FIELD, isin);
				fixER.setString(SecurityIDSource.FIELD, SecurityIDSource.ISIN_NUMBER);
			}

		}
		catch(Exception e)
		{
			log.error("CommonTranslator.populateMIFIDParameterOnExecutionReport:Problem with populating MIFID parameters on Execution Report for Order:" + order.getId());
		}
	}



	private void populateRestricedCCs(final NewOrderSingle newOrderSingle, final Order order, final WorkflowMessage msg)
	{
		try
		{
			Message message = newOrderSingle.getMessage();
			List<quickfix.Group> brokerRestrictionGroups = message.getGroups(StagingAreaConstants.FIX_TAG_NO_BROKER_RESTRICTIONS);
			if(brokerRestrictionGroups != null && brokerRestrictionGroups.size() > 0)
			{
				Set<String> brokerRestrictionSet = new HashSet<String>(brokerRestrictionGroups.size());
				for(quickfix.Group brokerRestrictionGroup : brokerRestrictionGroups)
				{
					if(brokerRestrictionGroup.isSetField(StagingAreaConstants.FIX_TAG_BROKER_RESTRICTION))
					{
						String broker = brokerRestrictionGroup.getString(StagingAreaConstants.FIX_TAG_BROKER_RESTRICTION);
						if(broker != null && !"".equals(broker.trim()))
						{
							brokerRestrictionSet.add(broker);
						}
					}
				}
				order.setRestrictedCCSet(brokerRestrictionSet);
			}
		}
		catch(FieldNotFound fnf)
		{
			log.info("CommonTranslator.populateBrokerRestrictions():Broker Restriction field not present.");
		}
	}

	private List<AllocationDetail> getAllocationDetails( NewOrderSingle newOrderSingle, WorkflowMessage msg, User user, String dCcy ) throws FieldNotFound
	{
		List<AllocationDetail> allocationDetails = FixUtilC.getAllocationDetailsForStagingOrder( newOrderSingle.getMessage(), user );
		if(allocationDetails == null || allocationDetails.size() == 0)
		{
			msg.addError("ACCOUNT_NOT_PRESENT_IN_ORDER");
		}
		else if(allocationDetails.size() > 1
				&& !StagingServiceConfig.getInstance().isMultipleAllocationsSupported(user.getOrganization().getShortName()))
		{
			msg.addError( "MULTIPLE_ALLOCATIONS_NOT_ALLOWED_FOR_ORG" );
		}
		else
		{
			return validateAllocationDetails(allocationDetails, msg, newOrderSingle.getOrderQty().getValue(), dCcy);
		}
		return null;
	}

	private List<AllocationDetail> validateAllocationDetails( List<AllocationDetail> allocationDetails, WorkflowMessage msg, double totalDealtAmt, String dCcy)
	{
		double totalAccountAmts = 0.0d;
		for(AllocationDetail allocationDetail : allocationDetails)
		{
			if( allocationDetail.getAccountName() == null)
			{
				msg.addError( "ACCOUNT_ID_IS_NULL" );
				return null;
			}

			if( allocationDetail.getAccountName().isEmpty())
			{
				msg.addError( "ACCOUNT_ID_IS_EMPTY" );
				return null;
			}

			if(allocationDetail.getAmt() == null)
			{
				msg.addError( "ALLOCATION_AMOUNT_IS_NULL_FOR_ACCOUNT_" + allocationDetail.getAccountName());
				return null;
			}

			try
			{
				double allocationAmt = allocationDetail.getAmt();
				if(allocationAmt <= 0)
				{
					msg.addError( "AMOUNT_IS_ZERO_OR_NEGATIVE_FOR_ACCOUNT_" + allocationDetail.getAccountName() );
					return null;
				}
				totalAccountAmts += allocationAmt;
			}
			catch(NumberFormatException e)
			{
				msg.addError( "AMOUNT_FIELD_CONTAINS_NON_NUMBERS_FOR_ACCOUNT_" + allocationDetail.getAccountName() );
				return null;
			}
		}

		if(Math.abs(totalDealtAmt - totalAccountAmts)  > CurrencyFactory.getCurrency( dCcy ).getTickValue())
		{
			msg.addError( "SUM_OF_AMOUNTS_FOR_ACCOUNTS_DOES_NOT_MATCH_TOTAL_DEALT_AMOUNT" );
			return null;
		}

		return allocationDetails;
	}

	public com.integral.fix.client.message.Message getExecutionReportOrderCancelPending( OrderCancelRequest orderCancelRequest, Order order, SessionID sessionID ) throws FieldNotFound
	{
		ExecutionReport executionReport = (ExecutionReport) getStagedOrderReport(order, sessionID);
		executionReport.set(orderCancelRequest.getClOrdID());
		executionReport.set(new OrigClOrdID(order.getClientOrderId()));
		executionReport.set(FixConstants.EXEC_TYPE_PENDING_CANCEL);
		executionReport.set(FixConstants.ORDER_STATUS_NEW);
		return executionReport;
	}

	public com.integral.fix.client.message.Message getExecutionReportOrderReplacePending( OrderCancelReplaceRequest orderCancelRequest, Order order, SessionID sessionID ) throws FieldNotFound
	{
		ExecutionReport executionReport = (ExecutionReport) getStagedOrderReport(order, sessionID);
		executionReport.set(orderCancelRequest.getClOrdID());
		executionReport.set(new OrigClOrdID(order.getClientOrderId()));
		executionReport.set(FixConstants.EXEC_TYPE_PENDING_REPLACE);
		executionReport.set(FixConstants.ORDER_STATUS_NEW);
		return executionReport;
	}

	public com.integral.fix.client.message.Message getExecutionReportOrderReplaced( String clOrdId, Order order, SessionID sessionID )
	{
		ExecutionReport executionReport = (ExecutionReport) getStagedOrderReport(order, sessionID);
		executionReport.set(new ClOrdID(clOrdId));
		executionReport.set(new OrigClOrdID(order.getClientOrderId()));
		executionReport.set(FixConstants.EXEC_TYPE_REPLACE);
		return executionReport;
	}


	@Override
	public com.integral.fix.client.message.Message getStagedOrderCancelReject(
			OrderCancelRequest orderCancelRequest, Order order, String errorCode, SessionID sessionID) throws FieldNotFound
	{
		OrderCancelReject orderCancelReject = FIXMessageFactory.getInstance().newOrderCancelReject(sessionID);
		orderCancelReject.set(orderCancelRequest.getClOrdID());
		orderCancelReject.set(orderCancelRequest.getOrigClOrdID());
		orderCancelReject.set(new CxlRejResponseTo(CxlRejResponseTo.ORDER_CANCEL_REQUEST));
		String orderID = order != null ? order.get_id() : "NONE";
		orderCancelReject.set(new OrderID(orderID));
		OrdStatus ordStatus = fixUtil.getStagingOrderStatus(order);
		orderCancelReject.set(ordStatus);
		if (order == null)
		{
			errorCode = FixConstants.StagingOrderCancelRejectReason.STAGING_ORDER_CANCEL_REJECT_REASON_ORDER_NOT_FOUND.getText();
		}
		FixConstants.StagingOrderCancelRejectReason reason = FixConstants.StagingOrderCancelRejectReason.getReason(errorCode);
		CxlRejReason rejReason = null;
		if (reason != null)
		{
			rejReason = new CxlRejReason(reason.getFixErrorCode());
		}
		else
		{
			rejReason = new CxlRejReason(CxlRejReason.OTHER);
		}
		orderCancelReject.set(rejReason);
		orderCancelReject.set(new Text(errorCode));
		return orderCancelReject;

	}

	private void setTif(final Order order, final ExecutionReport executionReport)
	{
		TIF tif = order.getTif();
		if (tif != null)
		{
			switch (tif)
			{
				case GTC:
					executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.GOOD_TILL_CANCEL));
					break;
				case GTD:
					executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.GOOD_TILL_DATE));
					break;
				case EOD:
					executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.DAY));
					break;
				default:
					executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.GOOD_TILL_CANCEL));
			}
		}
	}


	private void setOrdType(final Order order, final ExecutionReport executionReport)
	{
		try
		{
			char ordTypeVal = order.getOrdType();
			if (ordTypeVal != 0)
			{
				executionReport.set(new OrdType(ordTypeVal));
			}
		}
		catch (Exception e)
		{
			log.warn("CommonTranslator:setOrdType():Problem with setting OrdType for ClientOrderId:" + order.getClientOrderId());
		}

	}



	public com.integral.fix.client.message.Message getStagedOrderReport( Order order, SessionID sessionID )
	{
	    //todo : changes related to Staging order with two legs / swap order

		ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport(sessionID);
		updateStagedOrderReportWithTraderUser( order, executionReport );
		updateStagedOrderReportWithCCDetails( order, executionReport );

		String account = order.getOrigAccount();
		if (account != null && StagingServiceConfig.getInstance().sendAccount( order.getNamespaceName() ))
		{
			executionReport.set(new Account(account));
		}
		String orderId = order.get_id();
		executionReport.set(new ClOrdID(order.getClientOrderId()));
		executionReport.set(new OrderID(orderId));
		executionReport.set(FixConstants.PRODUCT);
		executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
		executionReport.set( new Currency(order.getDealtCcy()) );
		executionReport.set( new OrderQty(order.getDealtAmt()) );

		executionReport.set( new Symbol( order.getCcyPair() ));
		executionReport.set( order.isBuy() ? FixConstants.BUY : FixConstants.SELL );
		long valueDateLong = order.getValueDate();
		if (valueDateLong > 0)
		{
			Date valueDate = new Date(valueDateLong);
			SimpleDateFormat simpleDateFormat = dateFormater.get();
			if (simpleDateFormat != null)
			{
				String settleDateStr = simpleDateFormat.format(valueDate);
				executionReport.set(new SettlDate(settleDateStr));
			}
		}
		List<Leg> orderLegs = order.getLegs();
		boolean swap = orderLegs != null && orderLegs.size() == 2;
		if (swap)
		{
			Leg farLeg = orderLegs.get(1);
			double quantity2 = farLeg.getDealtAmt();
			executionReport.set( new OrderQty2(quantity2) );
			long valueDate2Long = farLeg.getValueDate();
			if (valueDate2Long > 0)
			{
				Date valueDate2 = new Date(valueDate2Long);
				SimpleDateFormat simpleDateFormat = dateFormater.get();
				if (simpleDateFormat != null)
				{
					String settleDateStr = simpleDateFormat.format(valueDate2);
					executionReport.set(new FutSettDate2(settleDateStr));
				}
			}
		}
		updateStagedOrderReportWithTradeDate( executionReport, order );

		if( order.getPrice() != null )
		{
			executionReport.set(new Price(order.getPrice()));
		}
		setTif(order, executionReport);
		setOrdType(order, executionReport);
		switch ( OrderStatusType.fromCode(order.getState()) )
		{
		case INTIAL :
			executionReport.set(new ExecID(orderId + "N"));
			executionReport.set(FixConstants.EXEC_TYPE_NEW);
			executionReport.set(FixConstants.ORDER_STATUS_NEW);
			executionReport.set(new CumQty(0.0));
			executionReport.set(new LastQty(0.0));
			executionReport.set(new LeavesQty(order.getDealtAmt()));
			executionReport.set(new AvgPx(0.0));
			executionReport.set(new LastPx(0.0));
			break;
		case CANCELLED :
			executionReport.set(new ExecID(orderId + "C"));
			executionReport.set(FixConstants.EXEC_TYPE_CANCELED);
			executionReport.set(FixConstants.ORDER_STATUS_CANCELED);
			executionReport.set(new CumQty(0.0));
			executionReport.set(new LastQty(0.0));
			executionReport.set(new LeavesQty(order.getDealtAmt()));
			executionReport.set(new AvgPx(0.0));
			executionReport.set(new LastPx(0.0));
			break;

		case REJECTED :
			executionReport.set(new ExecID(orderId + "R"));
			executionReport.set(FixConstants.EXEC_TYPE_REJECTED);
			executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
			executionReport.set(new CumQty(0.0));
			executionReport.set(new LastQty(0.0));
			executionReport.set(new LeavesQty(order.getDealtAmt()));
			executionReport.set(new AvgPx(0.0));
			executionReport.set(new LastPx(0.0));
			executionReport.set(new OrdRejReason(OrdRejReason.OTHER));
			String reason = order.getReason();
			if (reason != null)
			{
				executionReport.set(new Text(reason));
			}
			break;

		case EXECUTED  :
			executionReport.set(FixConstants.EXEC_TYPE_TRADE);
			executionReport.set(FixConstants.ORDER_STATUS_FILLED);
			executionReport.set(new CumQty(order.getFilledAmount()));
			executionReport.set(new LastQty(order.getFilledAmount()));
			executionReport.set(new LeavesQty( order.getDealtAmt() - order.getFilledAmount() ));
			executionReport.set(new ExecID(order.getTransactionId()));
			executionReport.set(new LastSpotRate( order.getSpotRateD()));
			if (swap)
			{
				Leg nearLeg = orderLegs.get(0);
				Leg farLeg = orderLegs.get(1);
				String nearLegPrice = nearLeg.getLegPrice();
				String nearLegForwardPoint = nearLeg.getLegForwardPoint();
				String farLegPrice = farLeg.getLegPrice();
				String farLegForwardPoint = farLeg.getLegForwardPoint();
				if (nearLegPrice != null)
				{
					executionReport.setString(LastPx.FIELD, nearLegPrice);
				}
				if (nearLegForwardPoint != null)
				{
					executionReport.setString(LastForwardPoints.FIELD, nearLegForwardPoint);
				}
				else
				{
					executionReport.set(new LastForwardPoints(0));
				}
				if (farLegPrice != null)
				{
					executionReport.setString(LastPx2.FIELD, farLegPrice);
				}
				if (farLegForwardPoint != null)
				{
					executionReport.setString(LastForwardPoints2.FIELD, farLegForwardPoint);
				}
				else
				{
					executionReport.setString(LastForwardPoints2.FIELD, "0.0");
				}
				Double farLegDealtAmount = farLeg.getDealtAmt();
				if (farLegDealtAmount != null)
				{
					executionReport.set(new OrderQty2(farLegDealtAmount));
				}
				Double nearLegSettleAmount = nearLeg.getSettlAmt();
				if (nearLegSettleAmount != null)
				{
					executionReport.set(new SettlCurrAmt(nearLegSettleAmount));
				}
				Double farLegSettlAmount = farLeg.getSettlAmt();
				if (farLegSettlAmount != null)
				{
					executionReport.setField(new DoubleField(SettlCurrAmt2.FIELD, farLegSettlAmount));
				}
			}
			else
			{
				executionReport.set(new AvgPx(order.getFilledPrice()));
				executionReport.set(new LastPx(order.getFilledPrice()));
				executionReport.set(new LastForwardPoints( order.getFwdPtsD()));
			}

			populateBenchMarkRateOnExecutionReport(executionReport, order);
			populateMIFIDParameterOnExecutionReport(executionReport, order);
			break;
		case INUSE :
			executionReport.set(new ExecID(orderId + "N"));
			executionReport.set(FixConstants.EXEC_TYPE_NEW);
			executionReport.set(FixConstants.ORDER_STATUS_NEW);
			// set working indicator to Y if order is locked and
			//execution report is result of OrderStatusRequest
			executionReport.set(new WorkingIndicator(true));
			executionReport.set(new CumQty(0.0));
			executionReport.set(new LastQty(0.0));
			executionReport.set(new LeavesQty(order.getDealtAmt()));
			executionReport.set(new AvgPx(0.0));
			executionReport.set(new LastPx(0.0));
			break;
		case EXPIRED:
			executionReport.set(new ExecID(orderId + "E"));
			executionReport.set(FixConstants.EXEC_TYPE_EXPIRED);
			executionReport.set(FixConstants.ORDER_STATUS_EXPIRED);
			executionReport.set(new CumQty(0.0));
			executionReport.set(new LastQty(0.0));
			executionReport.set(new LeavesQty(order.getDealtAmt()));
			executionReport.set(new AvgPx(0.0));
			executionReport.set(new LastPx(0.0));
			executionReport.set(new OrdRejReason(OrdRejReason.OTHER));
			reason = order.getReason();
			if (reason != null)
			{
				executionReport.set(new Text(reason));
			}
			break;
		default :

			break;
		}

		executionReport.setUtcTimeStamp(TransactTime.FIELD, new Date(), true);
		return executionReport;
	}
	private void populateBenchMarkRateOnExecutionReport(final ExecutionReport executionReport,  final Order order)
	{
		String namespace = order.getNamespaceName();
		int benchMarkRateTime = StagingServiceConfig.getInstance().sendBMRateWithExecutionReport(namespace);
		if (-1 == benchMarkRateTime)
		{
			return;
		}
		if (EXECUTION_REPORT_BENCHMARKRATE_ORDER_SUBMISSION_TIME == benchMarkRateTime)
		{
			executionReport.setField(new DoubleField(Spread.FIELD, order.getGtmOrdSubmissionBMRate()));
		}
		else if (EXECUTION_REPORT_BENCHMARKRATE_ORDER_WEIGHTAGE_AVERAGE == benchMarkRateTime)
		{
			executionReport.setField(new DoubleField(Spread.FIELD, order.getWtAvgBMRate()));
		}
		else if(EXECUTION_REPORT_BENCHMARKRATE_ORDER_ALLOCATION_TIME == benchMarkRateTime)
		{
			executionReport.setField(new DoubleField(Spread.FIELD, order.getAllocationBMRate()));
		}
	}

	private void updateStagedOrderReportWithTradeDate(final ExecutionReport executionReport, final Order order)
	{
		long tradeDateLong = order.getTradeDate();
		if (tradeDateLong > 0) {
			Date date = new Date(tradeDateLong);
			SimpleDateFormat simpleDateFormat = dateFormater.get();
			if (simpleDateFormat != null)
			{
				String formattedTradeDate = simpleDateFormat.format(date);
				executionReport.set(new TradeDate(formattedTradeDate));
			}
		}
		else
		{
			IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
			if (tradeDate != null)
			{
				String tradeDateStr = tradeDate.getFormattedDate(IdcDate.YYYY_MM_DD);
				if (tradeDateStr != null)
				{
					executionReport.set(new TradeDate(tradeDateStr));
				}
			}
		}
	}

	private void updateStagedOrderReportWithTraderUser( Order order, ExecutionReport executionReport )
	{
		int traderUserTagNo = order.getTraderUserTagNumber();
		String traderUser = order.getUserName();
		// check if email address need to be populated
		String nameSpace = order.getNamespaceName();
		boolean useEmailAddress = StagingServiceConfig.getInstance().useEmailAddressForUser(nameSpace);
		if (useEmailAddress)
		{
			User user = order.getUser();
			if (user == null)
			{
				String userName = order.getUserName();
				String userfullname = userName + '@' + order.getNamespaceName();
				user = UserFactory.getUser(userfullname);
			}
			if (user != null)
			{
				UserContact userContact = user.getContact();
				if (userContact != null)
				{
					String userEmailAddress = userContact.getEmailAddress();
					if (userEmailAddress != null && !"".equals(userEmailAddress.trim()))
					{
						traderUser = userEmailAddress;
					}
				}
			}
		}
		if ( SenderSubID.FIELD == traderUserTagNo)
		{
			Header header = executionReport.getHeader();
			header.setString( TargetSubID.FIELD, traderUser);
		}
		else if ( PartyID.FIELD == traderUserTagNo)
		{
			// populate partyID repeating group
			executionReport.addPartyIDGroup( traderUser,
					PartyIDSource.GENERALLY_ACCEPTED_MARKET_PARTICIPANT_IDENTIFIER, PartyRole.ORDER_ORIGINATION_TRADER );
		}
	}

	private void updateStagedOrderReportWithCCDetails( Order order, ExecutionReport executionReport )
	{
		int ccTagNo = order.getCcTagNumber();
		String cc = order.getExecutedCC();
		if(cc == null)
		{
			cc = order.getInputCC();
		}
		if (cc != null)
		{
			if ( DeliverToCompID.FIELD == ccTagNo)
			{
				Header header = executionReport.getHeader();
				header.setString( OnBehalfOfCompID.FIELD, cc);
			}
			else if( PartyID.FIELD == ccTagNo)
			{
				// populate partyID repeating group
				executionReport.addPartyIDGroup(cc,
						PartyIDSource.GENERALLY_ACCEPTED_MARKET_PARTICIPANT_IDENTIFIER, PartyRole.EXECUTING_FIRM);
			}
			else
			{
				// by default populate it as party repeating group
				String namespaceName = order.getNamespaceName();
				boolean sendCC = StagingServiceConfig.getInstance().sendCCWithExecutionReport(namespaceName);
				if (sendCC)
				{
					executionReport.addPartyIDGroup(cc,
							PartyIDSource.GENERALLY_ACCEPTED_MARKET_PARTICIPANT_IDENTIFIER, PartyRole.EXECUTING_FIRM);
				}
			}
		}

	}

    public Message getLimitOrderExecutionReport( OrderStatusRequest orderStatusRequest,
                                                 FXSingleLegOrder order,
                                                 SessionID sessionID ) throws FieldNotFound
    {


        FXDealLeg dp = order.getFXDealLeg();
        ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport( sessionID );
        executionReport.set(fixUtil.getOrdStatus(order));

        String requestClassfication = order.getOrderClassification().getShortName();

        double avgPx = order.getAverageRate();
        double orderAmount = order.getAmount();
        double filledAmount = order.getFilledAmount() > 0.0 ? order.getFilledAmount() : 0.0d;
        //TODO : validate, no logic in CDQ for partial trying derived.
        boolean isPartial =  orderAmount - filledAmount > 0.0;
        if(order.isOrderBasedNettingEnabled() && isPartial) {
            Double nettedAmount = ServiceFactory.getNettingService().getNettedAmount(order.getOrderId());
            if(nettedAmount != null && nettedAmount <= filledAmount) {
                filledAmount = filledAmount - nettedAmount;
                if(filledAmount == 0) {
                    avgPx = 0.0;
                    executionReport.set(FixConstants.ORDER_STATUS_NEW);
                }
            }
        }
        double leavesAmount = orderAmount - filledAmount;

        boolean isBid = dp.getAcceptedBidOfferMode() == DealingPrice.BID;
        Side side = null;
        com.integral.finance.currency.Currency dealtCcyObj = dp.getDealtCurrency();
        String dealtCcy = dealtCcyObj.getRealCurrency().getShortName();
        String settlCcy = dp.getSellCurrency().getRealCurrency().getShortName();
        String ccyPair = null;
        String baseCcy = dp.getBaseCurrency().getRealCurrency().getShortName();
        if ( isBid )
        {
            if ( dealtCcy.equals( baseCcy ))
            {
                side = FixConstants.BUY;
            }
            else
            {
                side = FixConstants.SELL;
            }
        }
        else
        {
            if ( dealtCcy.equals( dealtCcy ) )
            {
                side = FixConstants.SELL;
            }
            else
            {
                side = FixConstants.BUY;
            }
        }


        if (dealtCcy.equals( baseCcy ) )
        {
            ccyPair = dealtCcy + '/' + settlCcy;
        }
        else
        {
            ccyPair = settlCcy + '/' + dealtCcy;
        }

        executionReport.set(new ClOrdID(order.getClientReferenceId()));
        executionReport.set(FixConstants.PRODUCT);
        executionReport.set(side);
        executionReport.set(new AvgPx(avgPx));
        executionReport.set(new Symbol(ccyPair));
        executionReport.setUtcTimeStamp(TransactTime.FIELD,order.getCreatedDate(), true);
        executionReport.set(new OrderQty(orderAmount));
        executionReport.set(new Currency(dealtCcy));

        if ( FixConstants.LIMIT.equals(requestClassfication) )
        {
            executionReport.set(new Price(dp.getSpotRate()));
            executionReport.set(new OrdType(OrdType.LIMIT));
        }
        else if ( FixConstants.MARKET.equals(requestClassfication) )
        {
            executionReport.set(new OrdType(OrdType.MARKET));
            setPrice( order, dp, executionReport );
        }
        else if ( FixConstants.STOP.equals(requestClassfication) )
        {
            executionReport.set(new OrdType(OrdType.STOP));
            executionReport.set(new StopPx(order.getStopPrice()));
            setPrice( order, dp, executionReport );
        }
        else if ( FixConstants.STOPLIMIT.equals(requestClassfication) )
        {
            executionReport.set(new Price(dp.getSpotRate()));
            executionReport.set(new OrdType(OrdType.STOP_LIMIT));
            executionReport.set(new StopPx(order.getStopPrice()));
        }

        quickfix.field.TimeInForce tif = getTimeInForce( order );
        if ( tif != null )
        {
            executionReport.set(tif);
        }
        executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
        executionReport.set(new CumQty(filledAmount));
        executionReport.set(new LeavesQty(dealtCcyObj.round(leavesAmount)));
      //  executionReport.set(new LastQty(filledAmount));
        executionReport.set(new LastPx(0));
        executionReport.set(new ExecID("0"));
        executionReport.set(new OrderID(order.getOrderId()));
        executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
       /* if (dp.getValueDate() != null)
        {
            executionReport.set(new FutSettDate(dp.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
        }*/
        if ( orderStatusRequest.isSetClOrdID() )
        {
            executionReport.setString(790, orderStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
        }
        else
        {
            executionReport.setString(790, orderStatusRequest.getOrderID().getValue());//orderstatusreqiid.
        }
        setHeader(orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
        setHeader(orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
        setHeader(orderStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
        setHeader(orderStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

        if ( order.getCancelledBy() != null && order.getCancelledBy().equals( "" ) && order.getOrderCancelReason()!=null && !order.getOrderCancelReason().equals( "" ) )
        {
            executionReport.setString(Text.FIELD, order.getOrderCancelReason());
        }

        return executionReport.getMessage();
    }

    public Message getRFSExecutionReport( String orderStatusRequestId , int tagId,
                                          Deal deal,
                                          SessionID sessionID  ) throws FieldNotFound
    {

        // convert the deal to swap/single



        FXDealLeg nearDealLeg = null;

        boolean isSwap = deal.isSwap();

        boolean isBid = false;
        boolean isDealtCcy1;
        double filledPrice=0.0;
        double spotRate=0.0;
        double spotForwardPoints =0.0;
        double filledAmt =0.0 , filledSettledCcyAmt = 0.0;
        double orderAmount =0.0 ;
        double filledPrice2 = 0.0;
        double spotRate2 = 0.0;
        double spotForwardPoints2 = 0.0;
        double filledAmt2 = 0.0 , filledSettledAmt2 = 0.0;
        double orderAmount2 = 0.0;
        Side side = null;


        if (isSwap)
        {
            nearDealLeg =  ( ( FXSwapDealC ) deal ).getNearDealLeg();
        }
        else
        {
            nearDealLeg =  ( ( FXSingleLegDealC ) deal ).getFXDealLeg();
        }
        int bidOfferMode = nearDealLeg.getAcceptedBidOfferMode();
        isBid = bidOfferMode == FXLegDealingPrice.BID;
        isDealtCcy1 = nearDealLeg.isDealtCurrency1();
        filledPrice = nearDealLeg.getRate();
        spotRate = nearDealLeg.getSpotRate();
        spotForwardPoints = nearDealLeg.getForwardPoints();
        filledAmt = isDealtCcy1 ? nearDealLeg.getCurrency1Amount() : nearDealLeg.getCurrency2Amount();
        filledSettledCcyAmt = isDealtCcy1 ? nearDealLeg.getCurrency2Amount() : nearDealLeg.getCurrency1Amount();
        orderAmount = nearDealLeg.getDealtAmount();
        if (deal.isSwap())
        {
            FXDealLeg farLeg = ( ( FXSwapDealC ) deal ).getFarDealLeg();
            int bidOfferMode2 = farLeg.getAcceptedBidOfferMode();
            boolean isBid2 = bidOfferMode2 == FXLegDealingPrice.BID;

            isDealtCcy1 = farLeg.isDealtCurrency1();
            filledPrice2 = farLeg.getRate();
            spotRate2 = farLeg.getSpotRate();
            spotForwardPoints2 = farLeg.getForwardPoints();
            filledAmt2 = isDealtCcy1 ? farLeg.getCurrency1Amount() : farLeg.getCurrency2Amount();
            filledSettledAmt2 = isDealtCcy1 ? farLeg.getCurrency2Amount() : farLeg.getCurrency1Amount();
            orderAmount2 = farLeg.getDealtAmount();

            if ( farLeg.getDealtCurrency().isSameAs(farLeg.getBaseCurrency()) )
            {
                side = isBid2 ? FixConstants.SELL : FixConstants.BUY;
            }
            else
            {
                side = isBid2 ? FixConstants.BUY : FixConstants.SELL;
            }
        }

        ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport( sessionID );
        executionReport.set(FixConstants.PRODUCT);
        executionReport.set(FixConstants.ORD_TYPE_PREVIOUSLY_QUOTED);
        executionReport.set(new quickfix.field.TimeInForce(quickfix.field.TimeInForce.FILL_OR_KILL));
        executionReport.set(new ClOrdID(deal.getExternalRequestId()));
        executionReport.set(new ExecID(("0")));
        executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
        executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
        executionReport.set(new Symbol(nearDealLeg.getCurrencyPair()));
        executionReport.set(new FutSettDate(nearDealLeg.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
        executionReport.setUtcTimeStamp(TransactTime.FIELD, deal.getExecutionTimestamp(), true);
        executionReport.set( new Currency( nearDealLeg.getDealtCurrency().getShortName() ) );
        executionReport.set( new SettlCurrency( nearDealLeg.getSettledCurrency().getShortName() ) );
        executionReport.set(new OrderQty(orderAmount));
        executionReport.set(new Price(filledPrice));
        executionReport.set(new OrderID(deal.getTransactionId()));

        if( deal.getTradeDate() != null )
        	executionReport.set(new TradeDate(deal.getTradeDate().getFormattedDate(IdcDate.YYYY_MM_DD)));

        if ( isSwap )
        {
            executionReport.set(side);
            executionReport.setField(new Price2(filledPrice2));
            executionReport.set(new OrderQty2(orderAmount2));
            executionReport.set(new FutSettDate2(( ( FXSwapDealC ) deal ).getFarDealLeg().getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
        }
        else
        {
            if ( nearDealLeg.getDealtCurrency().isSameAs(nearDealLeg.getBaseCurrency()) )
            {
                executionReport.set(isBid ? FixConstants.SELL : FixConstants.BUY);
            }
            else
            {
                executionReport.set(isBid ? FixConstants.BUY : FixConstants.SELL);
            }
        }

        if ( nearDealLeg.getFixingDate() != null )
        {
            executionReport.set(new MaturityDate(nearDealLeg.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
        }

        DealStateFacade dealFacade = (DealStateFacade) deal.getFacade(DealStateFacade.FACADE_NAME);

        //This is hack....if a trade is rejected after delay (post trade pending message) state is
        //set to 'delayedverification' with not null rejection reason. Ideally delayed flag should be separate from deal state.
        boolean isRejected = dealFacade.isDelayedVerification() && deal.getRejectReason() != null;

        if ( dealFacade.isRejected() || dealFacade.isPendingVerification() || isRejected )
        {
            executionReport.set(FixConstants.ORDER_STATUS_NEW);
            if ( dealFacade.isRejected() || isRejected )
            {
            	if( deal.getRejectReason() != null )
                {
                	fixUtil.setRejectReason(executionReport, deal.getRejectReason());
                }
                executionReport.set(FixConstants.ORDER_STATUS_REJECTED);
            }
            executionReport.set(FixConstants.LEAVES_QTY_ZERO);
            executionReport.set(FixConstants.CUM_QTY_ZERO);
            executionReport.set(FixConstants.AVG_PX_REJECT);
            executionReport.set(new SettlCurrAmt(0.0));
            executionReport.set(new LastSpotRate(0));
            executionReport.set(new LastForwardPoints(0));

            executionReport.set(new LastPx(0));
            if ( isSwap )
            {
                executionReport.setField(new DoubleField(7542, 0));//LastspotRate2
                executionReport.setField(new DoubleField(641, 0));//LastForwardPoints2
                executionReport.setField(new DoubleField(7541, 0));//lastPx2
                executionReport.setField(new DoubleField(7543, 0));//leavesQty2
                executionReport.setField(new DoubleField(7544, 0));//CumQty2
                executionReport.setField(new DoubleField(7545, 0));//CumQty2
            }
        }
        else
        {
            //Set other quantities and prices
            executionReport.set(FixConstants.LEAVES_QTY_ZERO);
            executionReport.set(new CumQty(filledAmt));
            executionReport.set(new LastQty(filledAmt));
            executionReport.set(new SettlCurrAmt(filledSettledCcyAmt));
            executionReport.set(new LastSpotRate(spotRate));
            executionReport.set(new LastForwardPoints(spotForwardPoints));
            executionReport.set(new LastPx(filledPrice));
            if ( isSwap )
            {
                executionReport.setField(new DoubleField(7542, spotRate2));//LastspotRate2
                executionReport.setField(new DoubleField(641, spotForwardPoints2));//LastForwardPoints2
                executionReport.setField(new DoubleField(7541, filledPrice2));//lastPx2
                executionReport.setField(new DoubleField(7543, 0));//leavesQty2
                executionReport.setField(new DoubleField(7544, filledAmt2));//CumQty2
                executionReport.setField(new DoubleField(7545, filledSettledAmt2));//SettlCurAmt2
            }
            executionReport.set(new AvgPx(filledPrice));
            boolean isPartial =  orderAmount - filledAmt > 0.0;
            if ( dealFacade.isPartialFilled() )
            {
                executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
            }
            else if ( dealFacade.isFilled() || dealFacade.isVerified() || dealFacade.isDelayedVerification() )
            {
                executionReport.set(FixConstants.ORDER_STATUS_FILLED);
            }

        }

        executionReport.setString(tagId, orderStatusRequestId);//orderstatusreqiid.
        executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);

        //setHeader(orderStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD);
        //setHeader(orderStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
        //setHeader(orderStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
       // setHeader(orderStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);
        if ( deal.getCounterpartyB() != null )
        {
            setHeader( executionReport, SenderSubID.FIELD, deal.getCounterpartyB().getShortName() );
            setHeader( executionReport, OnBehalfOfCompID.FIELD, deal.getCounterpartyB().getOrganization().getShortName() );
        }

        if( deal.getCounterpartyA() != null )
        {
        	setHeader( executionReport, TargetSubID.FIELD, deal.getCounterpartyA().getShortName() );
        	setHeader(executionReport, DeliverToCompID.FIELD, deal.getCounterpartyA().getOrganization().getShortName());
        }
		if(deal.getDepositsParam() != null){
			DepositsParam dp = deal.getDepositsParam();
			if(dp.getAnnualInterestRate() != null) executionReport.getMessage().setDouble(FixConstants.FIELD_DEP_ANN_INT_RATE, dp.getAnnualInterestRate());
			if(dp.getInterest() != null) executionReport.getMessage().setDouble(FixConstants.FIELD_DEP_ACT_INT, dp.getInterest());
			if(dp.getNumberOfDays() != null) executionReport.getMessage().setInt(FixConstants.FIELD_DEP_NUM_DAYS, dp.getNumberOfDays());
		}
        return executionReport.getMessage();
    }


    public Message getLimitOrderExecutionReport( OrderMassStatusRequest orderMassStatusRequest,
                                                 FXSingleLegOrder order,
                                                 SessionID sessionID ) throws FieldNotFound
    {
        FXDealLeg dp = order.getFXDealLeg();
        ExecutionReport executionReport = FIXMessageFactory.getInstance().newExecutionReport( sessionID );
        OrdStatus orderStatus = fixUtil.getOrdStatus(order);
        executionReport.set( orderStatus );

        String requestClassfication = order.getOrderClassification().getShortName();

        double avgPx = order.getAverageRate();
        double orderAmount = order.getAmount();
        double filledAmount = order.getFilledAmount() > 0.0 ? order.getFilledAmount() : 0.0d;
        //TODO : validate, no logic in CDQ for partial trying derived.
        boolean isPartial =  orderAmount - filledAmount > 0.0;
        if(order.isOrderBasedNettingEnabled() && isPartial) {
            Double nettedAmount = ServiceFactory.getNettingService().getNettedAmount(order.getOrderId());
            if(nettedAmount != null && nettedAmount <= filledAmount) {
                filledAmount = filledAmount - nettedAmount;
                if(filledAmount == 0) {
                    avgPx = 0.0;
                    executionReport.set(FixConstants.ORDER_STATUS_NEW);
                }
            }
        }
        double leavesAmount = orderAmount - filledAmount;

        //FXSingleLegOrder remain in CREATED state even if it is partial state. For active partially filled order
        //using filled amount to determine the state.
        if( orderStatus.valueEquals( FixConstants.ORDER_STATUS_NEW.getValue()  ) && filledAmount > 0.0  )
        {
        	executionReport.set(FixConstants.ORDER_STATUS_PARTIALLY_FILLED);
        }

        boolean isBid = dp.getAcceptedBidOfferMode() == DealingPrice.BID;
        Side side = null;
        com.integral.finance.currency.Currency dealtCcyObj = dp.getDealtCurrency();
        String dealtCcy = dealtCcyObj.getRealCurrency().getShortName();
        String baseCcy = dp.getBaseCurrency().getRealCurrency().getShortName();
        String varCcy = dp.getVariableCurrency().getRealCurrency().getShortName();
        if ( isBid )
        {
            if ( dealtCcy.equals( baseCcy ))
            {
                side = FixConstants.BUY;
            }
            else
            {
                side = FixConstants.SELL;
            }
        }
        else
        {
            if ( dealtCcy.equals( dealtCcy ) )
            {
                side = FixConstants.SELL;
            }
            else
            {
                side = FixConstants.BUY;
            }
        }

        String ccyPair = baseCcy + '/' + varCcy;

        executionReport.set(FixConstants.PRODUCT);
        executionReport.set(side);
        executionReport.set(new AvgPx(avgPx));
        executionReport.set(new Symbol(ccyPair));
        executionReport.setUtcTimeStamp(TransactTime.FIELD,order.getCreatedDate(), true);
        executionReport.set(new OrderQty(orderAmount));
        executionReport.set(new Currency(dealtCcy));
        executionReport.set(new ExecID("0"));
        executionReport.setString(584, orderMassStatusRequest.getMassStatusReqID().getValue());

        if ( FixConstants.LIMIT.equals(requestClassfication) )
        {
            executionReport.set(new Price(dp.getSpotRate()));
            executionReport.set(new OrdType(OrdType.LIMIT));
        }
        else if ( FixConstants.MARKET.equals(requestClassfication) )
        {
            executionReport.set(new OrdType(OrdType.MARKET));
            setPrice( order, dp, executionReport );
        }
        else if ( FixConstants.STOP.equals(requestClassfication) )
        {
            executionReport.set(new OrdType(OrdType.STOP));
            executionReport.set(new StopPx(order.getStopPrice()));
            setPrice( order, dp, executionReport );
        }
        else if ( FixConstants.STOPLIMIT.equals(requestClassfication) )
        {
            executionReport.set(new Price(dp.getSpotRate()));
            executionReport.set(new OrdType(OrdType.STOP_LIMIT));
            executionReport.set(new StopPx(order.getStopPrice()));
        }

        quickfix.field.TimeInForce tif = getTimeInForce( order );
        if ( tif != null )
        {
            executionReport.set(tif);
        }
        executionReport.set(FixConstants.EXEC_TYPE_ORDER_STATUS);
        executionReport.set(new CumQty(filledAmount));
        executionReport.set(new LeavesQty(dealtCcyObj.round(leavesAmount)));
        //  executionReport.set(new LastQty(filledAmount));
        executionReport.set(new LastPx(0));
        executionReport.set(new ExecID("0"));
        executionReport.set(new OrderID(order.getOrderId()));
        executionReport.set(FixConstants.FOREIGN_EXCHANGE_CONTRACT);
        executionReport.set(new ClOrdID( order.getClientReferenceId() ));
       /* if (dp.getValueDate() != null)
        {
            executionReport.set(new FutSettDate(dp.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
        }*/
       /* if ( orderMassStatusRequest.isSetClOrdID() )
        {
            executionReport.setString(790, orderMassStatusRequest.getClOrdID().getValue());//orderstatusreqiid.
        }
        else
        {
            executionReport.setString(790, orderMassStatusRequest.getOrderID().getValue());//orderstatusreqiid.
        }*/
        setHeader( orderMassStatusRequest, executionReport, DeliverToCompID.FIELD, OnBehalfOfCompID.FIELD );
        setHeader(orderMassStatusRequest, executionReport, OnBehalfOfCompID.FIELD, DeliverToCompID.FIELD);
        setHeader(orderMassStatusRequest, executionReport, OnBehalfOfSubID.FIELD, DeliverToSubID.FIELD);
        setHeader(orderMassStatusRequest, executionReport, SenderSubID.FIELD, TargetSubID.FIELD);

        if ( order.getCancelledBy() != null && order.getCancelledBy().equals( "" ) && order.getOrderCancelReason()!=null && !order.getOrderCancelReason().equals( "" ) )
        {
            executionReport.setString(Text.FIELD, order.getOrderCancelReason());
        }

        return executionReport.getMessage();
    }

    /**
     * @param portfolio
     * @param newOrderMultileg
     */
    public String updateNettingPortfolio( NettingPortfolio portfolio, com.integral.fix.client.message.NewOrderMultileg newOrderMultileg ) throws FieldNotFound
    {
   		try
   		{
   			HashMap<String, NettingTradeRequest> requests = new HashMap<String, NettingTradeRequest>();
   			int requestSeqId = 0;
   			FXRateConvention conv = QuoteConventionUtilC.getInstance().getFXRateConvention(portfolio.getOrganization());
   			for ( int counter = 1 ; counter <= newOrderMultileg.getNoLegs().getValue() ; counter++ )
   			{
   				NewOrderMultilegNoLegs noLegs = newOrderMultileg.createNoLegs();
   				newOrderMultileg.getGroup(counter, noLegs);

   				String legRefId = noLegs.getString(FixConstants.FIX_FIELD_LEG_REF_ID);
   				NettingTradeRequest request = requests.get(legRefId);
   				if ( request == null )
   				{
   					request = new NettingTradeRequestC();
   					request.setExternalRequestId(legRefId);
   					request.setSequenceNumber(legRefId);
   					request.setSequenceId(++requestSeqId);
   					requests.put(legRefId, request);
   				}

   				TradeRequestLeg leg = getTradeRequestLegOrder(noLegs);
   				leg.setFXRateConvention(conv);

   				if( request.getCcyPair() == null )
   				{
   					CurrencyPair ccyp = CurrencyFactory.getCurrencyPair(leg.getBaseCurrency(), leg.getTermCurrency());
   					request.setCcyPair(ccyp);
   				}
   				request.addTradeLeg(leg);

                //Fix for Backward Compatibility - MTF-635
                //Nested ID changes at Leg Level for MIFID
                String leShortName = null;
                if ( noLegs.isSetNoNestedPartyIDs() && noLegs.getNoNestedPartyIDs().getValue() >= 1 ) {
                    quickfix.field.NoNestedPartyIDs noNestedPartyIDs1 = noLegs.getNoNestedPartyIDs();
                    for (int index = 1; index <= noNestedPartyIDs1.getValue(); index++) {
                        Group group = noLegs.getGroup(index, noLegs.createNoNestedPartyIDs());
                        List<Group> legGroups = group.getGroups(quickfix.field.NoNestedPartyIDs.FIELD);
                        if (legGroups.size() > 1) {
                            for (Group individualLeg : legGroups) {
                                String party = individualLeg.getString(NestedPartyID.FIELD);
                                if (individualLeg.isSetField(NestedPartyRole.FIELD)) {
                                    if (individualLeg.isSetField(NestedPartyIDSource.FIELD)) {
                                        char aChar = individualLeg.getChar(NestedPartyIDSource.FIELD);
                                        if (aChar == 'D') leShortName = party;
                                        else continue;
                                    }
                                } else {
                                    leShortName = party;
                                }
                            }
                        } else {
   				NewOrderMultilegNoLegsNoNestedPartyIDs44 noNestedPartyIDs = noLegs.createNoNestedPartyIDs();
   				noLegs.getGroup(1, noNestedPartyIDs);
                            leShortName = noNestedPartyIDs.getNestedPartyID().getValue();
                        }
                    }
                }

   				LegalEntity le = (LegalEntity) ReferenceDataCacheC.getInstance().getEntityByShortName(leShortName,
                                                            LegalEntity.class, portfolio.getNamespace(), Entity.ACTIVE_STATUS);

				MiFIDHandlerFactory.getInstance().getMiFIDHandler().populateISINOnPortfolioTradeRequest(noLegs,leg);
   				request.setFund(le);
   				request.setNettingPortfolio(portfolio);
   			}
   			portfolio.setAcceptedRequests(new ArrayList<NettingTradeRequest>(requests.values()));
   		}
   		catch ( Exception e )
   		{
   			// TODO Auto-generated catch block
   			e.printStackTrace();
   		}
   		return null;
   	}


   	private TradeRequestLeg getTradeRequestLegOrder( NewOrderMultilegNoLegs noLegs ) throws FieldNotFound
	{
		TradeRequestLeg leg = new TradeRequestLegC();
		CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(noLegs.getLegSymbol().getValue());
		leg.setBaseCurrency(currencyPair.getBaseCurrency());
		leg.setTermCurrency(currencyPair.getVariableCurrency());
		leg.setDealtCurrency(CurrencyFactory.getCurrency(noLegs.getLegCurrency().getValue()));
		leg.setCurrencyPair(noLegs.getLegSymbol().getValue());

		DateFormat sdf = new SimpleDateFormat(FixConstants.DATE_TIME_FORMAT);
		setLegValueDate(noLegs, leg, null, sdf);
		setLegFixingDate(noLegs, leg, null, sdf);

		String legRefId = noLegs.getString(FixConstants.FIX_FIELD_LEG_REF_ID);
		leg.setExternalLegId(legRefId);

		if ( leg.getBaseCurrency().isSameAs(leg.getDealtCurrency()) )
		{
			// Base is Dealt Currency
			if( noLegs.getLegSide().getValue() == Side.UNDISCLOSED )
			{
				leg.setSide(TradeRequestLeg.UNDISCLOSED);
			}
			else if ( noLegs.getLegSide().getValue() == Side.BUY )
			{
				// Buying base (or dealt) currency
				leg.setBuyingBase(true);
				leg.setBuyingDealt(true);
				leg.setSide(TradeRequestLeg.BUY);
			}
			else
			{
				// Selling base (or dealt) currency
				leg.setBuyingBase(false);
				leg.setBuyingDealt(false);
				leg.setSide(TradeRequestLeg.SELL);
			}
			leg.setBaseAmount(noLegs.getLegQty().getValue());
			leg.setDealtAmount(noLegs.getLegQty().getValue());
			leg.setTermAmount(0);
		}
		else
		{
			// Term is Dealt Currency

			if( noLegs.getLegSide().getValue() == Side.UNDISCLOSED )
			{
				leg.setSide(TradeRequestLeg.UNDISCLOSED);
			}
			else if ( noLegs.getLegSide().getValue() == Side.BUY )
			{
				// Buying term (or dealt) currency
				leg.setBuyingBase(false);
				leg.setBuyingDealt(true);
				leg.setSide(TradeRequestLeg.BUY);
			}
			else
			{
				// Selling term (or dealt) currency
				leg.setBuyingBase(true);
				leg.setBuyingDealt(false);
				leg.setSide(TradeRequestLeg.SELL);
			}
			leg.setTermAmount(noLegs.getLegQty().getValue());
			leg.setDealtAmount(noLegs.getLegQty().getValue());
			leg.setBaseAmount(0);
		}
		UTIWorkFlowHelper.updateTradeRequestLegsForUDB(leg , noLegs );
		UPIWorkFlowHelper.updateTradeRequestLegsForUDB(leg , noLegs );
        RTNWorkFlowHelper.updateTradeRequestLegsForUDB(leg , noLegs );
		return leg;
	}

    private void setPrice( FXSingleLegOrder order, FXDealLeg dp, ExecutionReport executionReport )
    {
        if (order.getMarketRange() != null)
        {
            Double pegDiff = Double.valueOf( order.getMarketRange() );
            if ( pegDiff != null && pegDiff != -1 )
            {
                executionReport.set(new Price(dp.getSpotRate()));
                executionReport.set(new PegDifference(Double.valueOf(pegDiff)));
            }
        }
        else
        {
            executionReport.set(new Price(dp.getSpotRate()));
        }
    }


    private quickfix.field.TimeInForce getTimeInForce(FXSingleLegOrder cdqOrder) {
        String expClf = cdqOrder.getExpirationClassification();
        if (expClf == null)
            return null;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_GTC))
            return FixConstants.GTC;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_GTD))
            return FixConstants.GTD;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_FOK))
            return FixConstants.FOK;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_DAY))
            return FixConstants.DAY;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_IOC))
            return FixConstants.IOC;

        return null;
    }


    public MarketDataSnapshotFullRefresh getQuoteV3( Quote quote, FixSubscription subscriptionData, SessionID sessionId, boolean isBreakIfNoEntry )
	{
    	MarketDataSnapshotFullRefresh mdResponse = FIXMessageFactory.getInstance().newMarketDataSnapshotFullRefresh(sessionId);

		mdResponse.set(FixConstants.PRODUCT);
		mdResponse.set(subscriptionData.getSymbol());
		boolean isTradable = FixConstants.QUOTE_TRADABLE_TYPE.equals(quote.getQuoteClassification().getShortName());
		boolean isMultiTierQuote = (quote.getPriceType() == Quote.PRICE_TYPE_MULTI_TIER);
		int marketDepth2 = FixUtilC.getInstance().getAllowedMarketDepth(subscriptionData.getOrganization(), quote.getOrganization());
		int marketDepth = subscriptionData.getMarketDepth();
		if ( marketDepth2 > 0 )
		{
			if ( marketDepth == 0 ) //Customer asked for full book.
			{
				marketDepth = marketDepth2;
			}
			else
			{
				if ( marketDepth2 < marketDepth )
				{
					marketDepth = marketDepth2;
				}
			}
		}

		Quote rateEvent = (Quote) quote;
		//MDEntryOriginator mdEntryOriginator = subscriptionData.getMaskedProviderName();
		int bidSize = 0;
		int offerSize = 0;
		String seqNum = Long.toHexString(quote.getSequenceId());
		boolean isZeroSizeCheckEnabled = ISFactory.getInstance().getISMBean().isZeroQuoteSizeActive();

		for( int i=0;i<rateEvent.getBids().size();i++ ){
			FXLegDealingPrice dp = rateEvent.getBids().get(i);
			bidSize = getMDEntry(mdResponse,subscriptionData,rateEvent,dp,seqNum,isTradable,isMultiTierQuote,marketDepth,isZeroSizeCheckEnabled,bidSize,DealingPrice.BID);
		}

		for( int i=0;i<rateEvent.getOffers().size();i++ ){
			FXLegDealingPrice dp = rateEvent.getOffers().get(i);
			offerSize = getMDEntry(mdResponse,subscriptionData,rateEvent,dp,seqNum,isTradable,isMultiTierQuote,marketDepth,isZeroSizeCheckEnabled,offerSize,DealingPrice.OFFER);
		}


		if( bidSize == 0 && offerSize == 0 ){

			mdResponse = getInactiveQuote(subscriptionData.getMaskedProviderName().getValue(),
										  subscriptionData.getCurrencyPair().getName(),
										  subscriptionData.getMdRequestID(),
										  subscriptionData.getSenderSubID(),
										  rateEvent.getGUID(),
										  rateEvent.getOrganization(),
										  sessionId);
			return mdResponse;
		}
		IdcDate valueDate = rateEvent.getValueDate();

	   	if (com.integral.finance.currency.Currency.SettlementType.FORWARD == subscriptionData.getSettlementType())
    	{
    		IdcDate fixingDate = subscriptionData.getFixingDate(valueDate);    		
    		if (fixingDate != null)
    		{
    			String maturityDate = fixingDate.getFormattedDate(IdcDate.YYYY_MM_DD);
    			mdResponse.setString(MaturityDate.FIELD, maturityDate);    			
    		}    		
    		if (valueDate != null)
    		{
    			mdResponse.setString(FutSettDate.FIELD, valueDate.getFormattedDate(IdcDate.YYYY_MM_DD));
    		}
    		else
    		{
    			// populate tenor
    			Tenor tenor = subscriptionData.getTenor();
        		if (tenor != null)
        		{
        			mdResponse.setString(FutSettDate.FIELD, tenor.getName());
        		}
    		}
    		
    	}
	   	else
	   	{
	   		if ( null !=  valueDate)
			{
				mdResponse.set(new MaturityDate(rateEvent.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD)));
			}	   		
	   	}		

		mdResponse.set(new MDReqID(subscriptionData.getMdRequestID()));       
		mdResponse.getHeader().setField(subscriptionData.getTargetSubID());
		return mdResponse;
	}

	protected int getMDEntry(MarketDataSnapshotFullRefresh mdResponse, FixSubscription subscriptionData, Quote quote, FXLegDealingPrice tick, String seqNum,
			boolean isTradable,boolean isMultiTierQuote, int marketDepth, boolean isZeroSizeCheckEnabled, int size, int bomode){

		if ( !tick.isActive() && isTradable)
		{
			return size;
		}

		if ( size >= marketDepth && marketDepth != 0 )
		{
			return size;
		}

		size++;

		ExternallySortedGroup mDEntry = new ExternallySortedGroup(268, 269, mdentryFieldOrder);
		mDEntry.setField(FixConstants.mDEntryTypes[bomode]);;
		mDEntry.setDouble(MDEntryPx.FIELD, tick.getRate());

		if(subscriptionData.getSettlementType() == com.integral.finance.currency.Currency.SettlementType.FORWARD)
        {
			mDEntry.setDouble( FixConstants.FIX_FIELD_MDEntrySpotRate, tick.getRate() );
			mDEntry.setDouble( FixConstants.FIX_FIELD_MDEntryForwardPoints, tick.getForwardPoints () );
        }
		
		mDEntry.setField(subscriptionData.getBaseCurrency());
		double mDEntrySize = quote.getCurrencyPair().getBaseCurrency().round(tick.getDealtAmount());
		mDEntry.setDouble(MDEntrySize.FIELD, mDEntrySize);
		isTradable = isTradable && (isZeroSizeCheckEnabled ? (tick.getRate() > 0) : (tick.getRate() > 0 && mDEntrySize > 0));
		if ( isTradable )
		{
			mDEntry.setField(FixConstants.QUOTE_CONDITION_ACTIVE);
		}
		else
		{
			mDEntry.setField(FixConstants.QUOTE_CONDITION_INACTIVE);
		}
		mDEntry.setField(subscriptionData.getMaskedProviderName());
		if ( quote.getMinimumSize() != null && quote.getMinimumSize() > 0 )
		{
			mDEntry.setDouble(MinQty.FIELD, quote.getMinimumSize());
		}
		switch (subscriptionData.getMdEntryIdMode()){
			case FixConstants.MD_ENTRY_ID_MODE_SEQUENCE_NUMBER:
				mDEntry.setField(new QuoteEntryID(new StringBuilder(20).append(seqNum).append('_').append(tick.getName()).toString()));
				break;
			case FixConstants.MD_ENTRY_ID_MODE_TICK_GUID:
				mDEntry.setField(new QuoteEntryID(tick.getGUID()));
				break;
			case FixConstants.MD_ENTRY_ID_MODE_PROVIDER_QUOTE_ID:
				mDEntry.setField(new QuoteEntryID(new StringBuilder(20).append(quote.getExternalQuoteId()).append('_').append(tick.getName()).toString()));
				break;
			default:
				mDEntry.setField(new QuoteEntryID(new StringBuilder(20).append(seqNum).append('_').append(tick.getName()).toString()));
				break;
		}
		if ( isMultiTierQuote )
		{
			mDEntry.setField(FixConstants.MDENTRY_POSITIONS[size]);
		}
		else
		{
			mDEntry.setField(FixConstants.MD_POSITION_NO_ZERO);
		}
		mdResponse.addGroup(mDEntry);
		return size;
	}

	public WorkflowMessage getSTPResendRequest( Trade trade, TradeCaptureReportRequest message, User user )
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		try
		{
			msg.setStatus(MessageStatus.SUCCESS);
			msg.setEvent(FixConstants.MSG_EVT_RESEND);
			msg.setTopic(FixConstants.MSG_TOPIC_TRADE);
			msg.setSender(user);
			msg.setObject(trade);
			msg.setProperty(FixConstants.WF_PARAM_ORGNAIZATION, msg.getSender().getOrganization());
			if ( message.isSetTradeRequestID() )
			{
				msg.setParameterValue(FixConstants.WF_PARAM_TRADE_REQUEST_ID, message.getTradeRequestID().getValue());
			}
		}
		catch ( FieldNotFound e )
		{
			log.error("FixTranslator43Impl.getSTPResendRequest:Exception while forming resend request: m=" + message, e);
			msg.addError(FixConstants.INTERNAL_SERVER_ERROR);
		}
		return msg;
	}

	private void setCustomParameters ( QuoteRequest quoteRequest, Request request )
	{
		try
		{
			if ( quoteRequest.isSetNoOrderAttributes () )
			{
				List<Group> groups = quoteRequest.getMessage ().getGroups ( FixConstants.FIX_FIELD_NO_ORDER_ATTRIBUTES );

				log.info ( "CT.setCustomParameters - RFS quote request attributes groups=" + groups + ",quoteRequest="
						+ quoteRequest );

				for ( Group group : groups )
				{
					String attributeName = group.getString ( FixConstants.FIX_FIELD_ORDER_ATTRIBUTE_TYPE );
					String attributeValue = group.getString ( FixConstants.FIX_FIELD_ORDER_ATTRIBUTE_VALUE );
					if ( attributeName != null && attributeValue != null )
					{
						request.addCustomParameter ( attributeName, attributeValue );
					}
				}
			}
			else if ( log.isDebugEnabled () )
			{
				log.debug ( "CT.setCustomParameters - RFS quote request custom parameters are not set. quoteRequest="
						+ quoteRequest );
			}
		}
		catch ( Exception e )
		{
			log.error ( "CT.setCustomParameters - Exception while handling custom parameters. quoteRequest="
					+ quoteRequest, e );
		}
	}
}
