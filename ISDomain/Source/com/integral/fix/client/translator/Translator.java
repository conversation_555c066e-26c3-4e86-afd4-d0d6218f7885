package com.integral.fix.client.translator;

import com.integral.aggregation.price.FXPriceBook;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXSingleLegOrder;
import com.integral.finance.marketData.MarketTierPriceChangeSet;
import com.integral.finance.trade.Trade;
import com.integral.fix.client.message.*;
import com.integral.fix.client.subscription.FixSubscription;
import com.integral.fix.client.subscription.ProviderSubscription;
import com.integral.message.MessageEvent;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.netting.model.NettingPortfolio;
import com.integral.staging.Order;
import com.integral.user.Organization;
import com.integral.user.User;

import com.integral.xems.handler.blocktrade.BlockTradeMessageHandler;
import quickfix.FieldNotFound;
import quickfix.Message;
import quickfix.SessionID;
import quickfix.field.OrdStatus;
import quickfix.field.QuoteCancelType;
import quickfix.fix44.NewOrderMultileg;

import java.util.List;
import java.util.Map;

/**
 * This Translator converts messages received for Application to FIX Messages and vice versa
 *
 * <AUTHOR> Development Corp.
 */

public interface Translator
{

	/**
	 * to translate integral Quote to MarketDataSnapshotFullRefresh
	 *
	 * @param quote
	 * @param mdRequestID
	 * @param senderSubID
	 * @param marketDepth @return MarketDataIncrementalRefresh
	 * @param organization 
	 */
	public MarketDataSnapshotFullRefresh getQuote( Quote quote, FixSubscription subscriptionData );

	/**
	 * to translate integral Trade Message to fix ExecutionReport
	 *
	 * @param wf
	 * @param newOrderSingle
	 * @return ExecutionReport
	 */
	public ExecutionReport getExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate integral Trade Message to fix ExecutionReport
	 *
	 * @param wf
	 * @param newOrderSingle
	 * @return ExecutionReport
	 */
	public ExecutionReport getRFSExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate integral Trade Message to fix ExecutionReport
	 *
	 * @param wf
	 * @param newOrderSingle
	 * @return ExecutionReport
	 */
	public ExecutionReport getLimitOrderExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate integral RFS Quote to Quote
	 *
	 * @param quote
	 * @param quoteRequestID
	 * @param quoteRequestID
	 * @param onBehalfOfCompID
	 * @param OnBehalfOfSenderSubID
	 * @return Quote
	 */
	public com.integral.fix.client.message.Quote getRFSQuote( Quote quote, String quoteRequestID, String senderSubId, String onBehalfOfCompID, String OnBehalfOfSenderSubID, SessionID sessionID );

	/**
	 * Implemented for RFS Full Book
	 * @param request
	 * @param priceBook
	 * @param quoteRequestID
	 * @param senderSubId
	 * @param onBehalfOfCompID
	 * @param OnBehalfOfSenderSubID
	 * @param sessionID
	 * @return
	 */
	public com.integral.fix.client.message.Quote getRFSQuote( Request request, FXPriceBook priceBook, String quoteRequestID, String senderSubId, String onBehalfOfCompID, String OnBehalfOfSenderSubID, SessionID sessionID );

	
	/**
	 * to translate integral FXPriceBook ( Aggregated View ) to MarketDataSnapshotFullRefresh
	 *
	 * @param book
	 * @param subscription
	 * @param guid
	 * @return
	 */
	public MarketDataSnapshotFullRefresh getQuote( FXPriceBook book, FixSubscription subscription );

	/**
	 * translates integral stale message to Inactive MarketDataSnapshotFullRefresh message
	 *
	 * @param lpName
	 * @param ccyPair
	 * @param mdRequestID
	 * @param senderSubID
	 * @return MarketDataSnapshotFullRefresh
	 */

	public MarketDataSnapshotFullRefresh getInactiveQuote( String lpName, String ccyPair, String mdRequestID, String senderSubID, Organization o, SessionID sessionID );

	/**
	 * to translate integral Trade Message to fix ExecutionReport
	 *
	 * @param wf
	 * @param NewOrderSingle
	 * @return ExecutionReport
	 */
	public ExecutionReport getOrderCancelExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate integral Trade Message to fix ExecutionReport
	 *
	 * @param wf
	 * @param NewOrderSingle
	 * @return ExecutionReport
	 */
	public ExecutionReport getOrderCancelReplaceExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate integral Trade Message to fix  ExecutionReport
	 *
	 * @param wf
	 * @param NewOrderSingle
	 * @return
	 */
	public ExecutionReport getOrderExpiryExecutionReport( WorkflowMessage wf, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate integral Quote Withdraw Message to fix Message
	 *
	 * @param quoteRequestID
	 * @param quoteID
	 * @param quoteCancelType
	 * @param senderSubID
	 * @param onBehalfOfCompID
	 * @param onBehalfOfSenderSubID
	 * @param shortName             @return
	 */
	public QuoteCancel getQuoteWithdraw( String quoteRequestID, String quoteID, QuoteCancelType quoteCancelType, String senderSubID, String onBehalfOfCompID, String onBehalfOfSenderSubID, String shortName, SessionID sessionID );

	/**
	 * 
	 * @param message
	 * @param newOrderSingle
	 * @return
	 * @throws FieldNotFound 
	 */
	public Message getOrderTriggeredExecutionReport( WorkflowMessage message, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * 
	 * @param changeSet
	 * @param subscription
	 * @param isFXPriceBookUpdate 
	 * @return
	 */
	public MarketDataIncrementalRefresh getRateUpdateMessage( MarketTierPriceChangeSet changeSet, FixSubscription subscription, boolean isFXPriceBookUpdate );

	/**
	 * @param message
	 * @param newOrderSingle
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound 
	 */
	public Message getOrderRestatedExecutionReport( WorkflowMessage message, NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate login message from fix client to integral message
	 *
	 * @param logon
	 * @param sessionID
	 * @return WorkflowMessage
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getLoginMessage( Logon logon, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate logout message from fix client to integral message
	 *
	 * @param logout
	 * @return WorkflowMessage
	 */
	public WorkflowMessage getLogoutMessage( Logout logout, SessionID sessionID ) throws FieldNotFound;

	/**
	 * to translate MarketDataRequest message from fix client to integral message
	 *
	 * @param marketDataRequest
	 * @param sessionID
	 * @param originalMDRequestID 
	 * @param useAggregationService
	 * @return WorkflowMessage
	 */
	public WorkflowMessage getSubscriptionRequest( MarketDataRequest marketDataRequest, SessionID sessionID, boolean isAggregatedView, boolean isCheckForMaskedName, String originalMDRequestID, boolean useAggregationService );

	/**
	 * to translate NewOrderSingle message from fix client to integral acceptance message
	 *
	 * @param newOrderSingle
	 * @param sessionID
	 * @return WorkflowMessage
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getTradeRequest( NewOrderSingle newOrderSingle, SessionID sessionID ) throws FieldNotFound;

	/**
	 * translates MarketDataRequest to MarketDataRequestReject with the provided reject reason
	 *
	 * @param marketDataRequest
	 * @param errorReason
	 * @return MarketDataRequestReject
	 * @throws FieldNotFound
	 */
	public MarketDataRequestReject getSubscriptionReject( MarketDataRequest marketDataRequest, String errorReason ) throws FieldNotFound;

	/**
	 * translates NewOrderSingle to Rejection ExecutionReport with the provided reject reason
	 *
	 * @param newOrderSingle
	 * @param errorCode
	 * @return ExecutionReport
	 * @throws FieldNotFound
	 */
	public ExecutionReport getExecutionReportReject( NewOrderSingle newOrderSingle, String errorCode, SessionID sessionID ) throws FieldNotFound;

    /**
     * translates NewOrderMultileg to Rejection ExecutionReport with the provided reject reason
     *
     * @param newOrderMultileg
     * @param errorCode
     * @return ExecutionReport
     * @throws FieldNotFound
     */
    public ExecutionReport getExecutionReportReject( NewOrderMultileg newOrderMultileg, String portfolioId, String errorCode, SessionID sessionID ) throws FieldNotFound;

    /**
	 * translates OrderStatusRequest and cached NewOrderSingle to ExecutionReport with the provided reject reason
	 *
	 * @param orderStatusRequest
	 * @param newOrderSingle
	 * @param request
	 * @return ExecutionReport
	 */
	public ExecutionReport getExecutionReportDelayed( OrderStatusRequest orderStatusRequest, NewOrderSingle newOrderSingle, Request request ) throws FieldNotFound;

	/**
	 * translates OrderStatusRequest and cached NewOrderSingle to ExecutionReport with the provided reject reason
	 *
	 * @param orderStatusRequest
	 * @param newOrderSingle
	 * @param request
	 * @return ExecutionReport
	 */
	public ExecutionReport getRFSExecutionReportDelayed( OrderStatusRequest orderStatusRequest, NewOrderSingle newOrderSingle, Request request ) throws FieldNotFound;

	/**
	 * translates OrderStatusRequest to Rejection ExecutionReport with the provided reject reason
	 *
	 * @param orderStatusRequest
	 * @return ExecutionReport
	 */
	public ExecutionReport getExecutionReportReject( OrderStatusRequest orderStatusRequest, String errorCode ) throws FieldNotFound;

	/**
	 * Translates OrderStatusRequest to ExecutionReport
	 *
	 * @param trade
	 * @param orderStatusRequest
	 * @return ExecutionReport
	 */
	public ExecutionReport getExecutionReport( Trade trade, OrderStatusRequest orderStatusRequest ) throws FieldNotFound;

	/**
	 * Translates OrderStatusRequest to ExecutionReport
	 *
	 * @param trade
	 * @param orderStatusRequest
	 * @return ExecutionReport
	 */
	public ExecutionReport getRFSExecutionReport( Trade trade, OrderStatusRequest orderStatusRequest ) throws FieldNotFound;

	/**
	 * Translates OrderCancelRequest to WITHDRAW REQUEST WorkflowMessage.
	 *
	 * @param orderCancelRequest
	 * @param sessionID
	 * @param orderRequest
	 * @return
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getWithdrawRequest( OrderCancelRequest orderCancelRequest, SessionID sessionID, Request orderRequest ) throws FieldNotFound;

	/**
	 *
	 * @param orderCancelRequest
	 * @param sessionID
	 * @param orderRequest
	 * @return
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getWithdrawRequest( OrderCancelRequest orderCancelRequest, SessionID sessionID, SingleLegOrder orderRequest ) throws FieldNotFound;

	/**
	 * Translates OrderCancelReplaceRequest to WITHDRAW REQUEST WorkflowMessage.
	 *
	 * @param OrderCancelReplaceRequest
	 * @param sessionID
	 * @param requestFromORS
	 * @return
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getWithdrawRequest( OrderCancelReplaceRequest orderCancelReplaceRequest, SessionID sessionID, Request requestFromORS ) throws FieldNotFound;

	/**
	 * Translates OrderMassCancelRequest to WITHDRAW REQUEST WorkflowMessage.
	 *
	 * @param orderMassCancelRequest
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getWithdrawRequest( OrderMassCancelRequest orderMassCancelRequest, SessionID sessionID ) throws FieldNotFound;

	/**
	 * Translates OrderCancelRequest to OrderCancelReject.
	 *
	 * @param orderCancelRequest
	 * @param errorCode
	 * @param sessionID 
	 * @return
	 * @throws FieldNotFound
	 */
	public OrderCancelReject getOrderCancelReject( OrderCancelRequest orderCancelRequest, String errorCode, SessionID sessionID ) throws FieldNotFound;

	/**
	 * Translates OrderCancelReplaceRequest to OrderCancelReject.
	 *
	 * @param orderCancelReplaceRequest
	 * @param errorCode
	 * @return
	 * @throws FieldNotFound
	 */
	public OrderCancelReject getOrderCancelReject( OrderCancelReplaceRequest orderCancelReplaceRequest, String errorCode, SessionID id ) throws FieldNotFound;

	/**
	 * @param orderCancelRequest
	 * @return
	 */
	public Message getExecutionReportOrderCancelPending( OrderCancelRequest orderCancelRequest, NewOrderSingle newOrderSingle, Request orderRequest ) throws FieldNotFound;

	/**
	 *
	 * @param orderCancelRequest
	 * @param newOrderSingle
	 * @param orderRequest
	 * @return
	 * @throws FieldNotFound
	 */
	public Message getExecutionReportOrderCancelPending( OrderCancelRequest orderCancelRequest, NewOrderSingle newOrderSingle, SingleLegOrder orderRequest, SessionID sessionID ) throws FieldNotFound;

	/**
	 * @param orderCancelReplaceRequest
	 * @return
	 */
	public Message getExecutionReportOrderCancelReplacePending( OrderCancelReplaceRequest orderCancelReplaceRequest, NewOrderSingle newOrderSingle, Request orderRequest ) throws FieldNotFound;

	/**
	 * @param orderMassCancelRequest
	 * @param errorCode
	 * @return
	 * @throws FieldNotFound
	 */
	public Message getOrderMassCancelReject( OrderMassCancelRequest orderMassCancelRequest, String errorCode ) throws FieldNotFound;

	/**
	 * @param orderMassCancelRequest
	 * @param response
	 * @return
	 * @throws FieldNotFound
	 */
	public Message getOrderMassCancelReport( OrderMassCancelRequest orderMassCancelRequest, WorkflowMessage response ) throws FieldNotFound;

	/**
	 * @param OrderCancelReplaceRequest
	 * @param NewOrderSingle
	 * @return
	 * @throws FieldNotFound
	 */
	public NewOrderSingle getNewOrderSingle( OrderCancelReplaceRequest orderCancelReplaceRequest ) throws FieldNotFound;

	/**
	 * Returns 'PENDING NEW
	 * @param newOrderSingle
	 * @param status
	 * @return
	 * @throws FieldNotFound
	 */
	public Message getOrderSubmissionExecutionReport( NewOrderSingle newOrderSingle, Request request, String status ) throws FieldNotFound;

	/**
	 * Returns "PENDING_NEW" ExecutionReport
	 * @param newOrderSingle
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound
	 */
	public Message getOrderSubmissionPendingExecutionReport( NewOrderSingle newOrderSingle,SessionID sessionID) throws FieldNotFound;

	/**
	 * @param newOrderSingle
	 * @param orderRequest
	 * @param status
	 * @param sessionID
	 * @return
	 */
	public Message getFXOrderSubmissionExecutionReport(NewOrderSingle newOrderSingle, SingleLegOrder orderRequest, String status, SessionID sessionID) throws FieldNotFound;

	/**
	 * @param orderStatusRequest
	 * @param request
	 * @param order
	 * @return
	 * @throws FieldNotFound
	 */
	public Message getExecutionReport( OrderStatusRequest orderStatusRequest, Request request ) throws FieldNotFound;

	public Message getExecutionReport( OrderStatusRequest orderStatusRequest, SingleLegOrder request ) throws FieldNotFound;

	/**
	 * @param quoteRequest
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getRFSRequest( QuoteRequest quoteRequest, SessionID sessionID ) throws FieldNotFound;
	
	/**
	 * @param quoteRequest
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getNettingPortfolioRequest( QuoteRequest quoteRequest, SessionID sessionID ) throws FieldNotFound;


	public WorkflowMessage getNettingPortfolioRequest(NewOrderMultileg newOrderMultiLeg, SessionID sessionID ) throws FieldNotFound;

	/**
	 * @param quoteReq
	 * @param errorCode @return
	 */
	public Message getQuoteRequestReject( QuoteRequest quoteReq, String errorCode );

	/**
	 * @param quoteReqId
	 * @param errorCode        @return
	 * @param senderSubID
	 * @param onBehalfOfCompID
	 * @param onBehalfOfSubID
	 * @param deliverToSubID
	 * @param ccyPair 
	 */
	public Message getQuoteRequestReject( String quoteReqId, String errorCode, String senderSubID, String onBehalfOfCompID, String onBehalfOfSubID, String deliverToSubID, String ccyPair );

	/**
	 * @param Request
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getRFSQuoteWithdrawRequest( Request request, SessionID sessionID );

	/**
	 * @param quotecancel
	 * @param errorCode
	 * @return
	 * @throws FieldNotFound
	 */
	public Message getQuoteCancelReject( QuoteCancel quotecancel, String errorCode ) throws FieldNotFound;

	/**
	 * @param message
	 * @param errorCode
	 * @return
	 */
	public Message getTradeCaptureReportRequestReject( TradeCaptureReportRequest message, String errorCode ) throws FieldNotFound;

	/**
	 * @param trade
	 * @param message
	 * @return
	 */
	public WorkflowMessage getSTPResendRequest( Trade trade, TradeCaptureReportRequest message, User user );

	/**
	 * @param request
	 * @param orderMassStatusRequest
	 * @return
	 */
	public Message getOrderMassStatusExecutionReport( Request request, OrderMassStatusRequest orderMassStatusRequest ) throws FieldNotFound;

	/**
	 * @param request
	 * @param orderMassStatusRequest
	 * @return
	 */
	public Message getRFSOrderMassStatusExecutionReport( Request request, OrderMassStatusRequest orderMassStatusRequest ) throws FieldNotFound;

	/**
	 * @param newOrderSingle
	 * @param request
	 * @param orderStatusRequest
	 * @return
	 */
	public Message getLimitOrderStatusExecutionReport( WorkflowMessage wfMsg, NewOrderSingle newOrderSingle, Request request, OrderStatusRequest orderStatusRequest ) throws FieldNotFound;

	public Message getLimitOrderStatusExecutionReport( WorkflowMessage wfMsg, NewOrderSingle newOrderSingle, SingleLegOrder request, OrderStatusRequest orderStatusRequest ) throws FieldNotFound;

	/**
	 * @param request
	 * @param orderMassStatusRequest
	 * @return
	 */
	public Message getLimitOrderMassStatusExecutionReport( Request request, OrderMassStatusRequest orderMassStatusRequest, boolean queryOrder ) throws FieldNotFound;

	/**
	 * @param orderMassStatusRequest
	 * @param errorCode
	 * @return
	 */
	public Message getOrderMassStatusReject( OrderMassStatusRequest orderMassStatusRequest, String errorCode ) throws FieldNotFound;

	/**
	 * 
	 * @param request
	 * @return
	 */
	public WorkflowMessage getOrderStatusRequest( Request request, User user );

	/**
	 * 
	 * @param wfMsg
	 * @param newOrderSingle
	 * @param request
	 * @param orderMassStatusRequest
	 * @return
	 * @throws FieldNotFound
	 */

	public Message getLimitOrderMassStatusExecutionReport( WorkflowMessage wfMsg, NewOrderSingle newOrderSingle, Request request, OrderMassStatusRequest orderMassStatusRequest ) throws FieldNotFound;

	/**
	 * 
	 * @param request
	 * @param cancelRequestID
	 * @param isOrderCancelReplace
	 * @param sessionID
	 * @return
	 */
	public OrderCancelReject getOrderCancelReject( Request request, String cancelRequestID, boolean isOrderCancelReplace, SessionID sessionID );

	/**
	 *
	 * @param request
	 * @param cancelRequestID
	 * @param isOrderCancelReplace
	 * @param sessionID
	 * @param ordStatus
	 * @return
	 */
	public OrderCancelReject getOrderCancelReject( Request request, String cancelRequestID, boolean isOrderCancelReplace, SessionID sessionID, OrdStatus ordStatus );

	/**
	 *
	 * @param orderRequest
	 * @param cancelRequestID
	 * @param orderCancelReplace
	 * @param sessionID
	 * @param orderStatusFilled
	 * @return
	 */
	public OrderCancelReject getOrderCancelReject( SingleLegOrder orderRequest, String cancelRequestID, boolean orderCancelReplace, SessionID sessionID, OrdStatus orderStatusFilled );

	/**
	 *
	 * @param marketDataRequest
	 * @param originalMDReqID
	 * @param ccyPair
	 * @param sessionID
	 * @param useAggregationService
	 * @return
	 */
	public ProviderSubscription getSingleProviderSubscription( MarketDataRequest marketDataRequest, String originalMDReqID, CurrencyPair ccyPair, SessionID sessionID, boolean useAggregationService );

	/**
	 *
	 * @param marketDataRequest
	 * @param originalMDReqID
	 * @param ccyPair
	 * @param sessionID
	 * @param useAggregationService
	 * @return
	 */
	public ProviderSubscription getMultiProviderSubscription( MarketDataRequest marketDataRequest, String originalMDReqID, CurrencyPair ccyPair, SessionID sessionID, boolean useAggregationService );

	/**
	 *
	 * @param marketDataRequest
	 * @param originalMDReqID
	 * @param sessionID
	 * @param ccyPair
	 * @param isMultiProviderView
	 * @param useAggregationService
	 * @return
	 * @throws Exception
	 */
	public FixSubscription createFixSubscription( MarketDataRequest marketDataRequest, String originalMDReqID, SessionID sessionID, CurrencyPair ccyPair, boolean isMultiProviderView, boolean useAggregationService ) throws Exception;

	/**
	 * 
	 * @param requests
	 * @param orderStatusRequest
	 * @return
	 * @throws FieldNotFound
	 */
	public Message getListStatusMessage( List<Request> requests, OrderStatusRequest orderStatusRequest ) throws FieldNotFound;

	/**
	 * @param orderCancelReplaceRequest
	 * @param request
	 */
	public WorkflowMessage getOrderAmendMessage( OrderCancelReplaceRequest orderCancelReplaceRequest, Request request ) throws FieldNotFound;

    public WorkflowMessage getOrderAmendMessage( OrderCancelReplaceRequest orderCancelReplaceRequest, SingleLegOrder request ) throws FieldNotFound;

	/**
	 * @param marketDataRequest
	 * @param sessionID
	 * @param isMultiProviderView
	 * @param useAggregationService
	 * @param mdRequestID
	 * @return
	 */
	public WorkflowMessage getAggregationSubscriptionRequest( MarketDataRequest marketDataRequest, SessionID sessionID );

	/**
	 * Generate download message without doing STP download.
	 * @param trade
	 * @param message
	 * @param sessionID
	 * @return
	 */
	public WorkflowMessage getGeneratedMessage( Trade trade, TradeCaptureReportRequest message, SessionID sessionID );

    /**
     *
     * @param request
     * @param orderMassStatusRequest
     * @param queryOrder
     * @return
     */
    Message getLimitOrderMassStatusExecutionReport(SingleLegOrder request, OrderMassStatusRequest orderMassStatusRequest, boolean queryOrder) throws FieldNotFound;

    /**
     *
     * @param response
     * @param newOrderSingle
     * @param request
     * @param orderMassStatusRequest
     * @return
     */
    Message getLimitOrderMassStatusExecutionReport(WorkflowMessage response, NewOrderSingle newOrderSingle, SingleLegOrder request, OrderMassStatusRequest orderMassStatusRequest) throws FieldNotFound;

    /**
     *
     * @param request
     * @param orderMassStatusRequest
     * @return
     */
    Message getOrderMassStatusExecutionReport(SingleLegOrder request, OrderMassStatusRequest orderMassStatusRequest) throws FieldNotFound;

    Message getExecutionReportOrderCancelReplacePending( OrderCancelReplaceRequest orderCancelReplaceRequest, NewOrderSingle newOrderSingle, SingleLegOrder singleLegOrder ) throws FieldNotFound;

    /**
     * converts the netting portfolio into a mass quote that for the client
     * @param quote
     * @param quoteRequestID
     * @param senderSubID
     * @param onBehalfOfCompID
     * @param onBehalfOfSenderSubID
     * @param sessionId
     * @param provider 
     * @param quoteId 
     * @param quoteType 
     * @return
     */
    Message getPortfolioQuote(NettingPortfolio quote, String quoteRequestID, String senderSubID, String onBehalfOfCompID, String onBehalfOfSenderSubID, SessionID sessionId, String quoteId, String provider, String quoteType);

    /**
     * 
     * @param newOrderMultileg
     * @param portfolioID
     * @param pending
     * @param allocatedTrades
     * @return
     */
    Message getOrderSubmissionExecutionReport(NewOrderMultileg newOrderMultileg, String portfolioID, String pending, List<Trade> allocatedTrades);
    
    /**
     * 
     * @param newOrderMultileg
     * @param portfolio
     * @param execType
     * @param allocatedTrades
     * @return
     */
	Message getOrderFilledExecutionReport( NewOrderMultileg newOrderMultileg, NettingPortfolio portfolio, String execType, List<Trade> allocatedTrades );

	/**
	 * @param osr
	 * @param portfolio
	 * @return
	 */
	public Message getExecutionReport( OrderStatusRequest osr, NettingPortfolio portfolio );
	
	/**
	 * 
	 * @param orderCancelRequest
	 * @param order
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound
	 */
	public com.integral.fix.client.message.Message getExecutionReportOrderCancelPending( OrderCancelRequest orderCancelRequest, Order order, SessionID sessionID ) throws FieldNotFound;

	/**
	 * 
	 * @param orderCancelRequest
	 * @param order
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound
	 */
	public com.integral.fix.client.message.Message getExecutionReportOrderReplacePending( OrderCancelReplaceRequest occr, Order order, SessionID sessionID ) throws FieldNotFound;

	
	/**
	 * @param order
	 * @param sessionID
	 * @return
	 */
	public com.integral.fix.client.message.Message getStagedOrderReport( Order order, SessionID sessionID );
	
	/**
	 * 
	 * @param order
	 * @param sessionID
	 * @return
	 */
	com.integral.fix.client.message.Message getStagedOrderCancelReject( OrderCancelRequest orderCancelRequest, Order order, String errorCode, SessionID sessionID ) throws FieldNotFound;

	/**
	 * @param clOrdId
	 * @param order
	 * @param sessionID
	 * @return
	 */
	public com.integral.fix.client.message.Message getExecutionReportOrderReplaced( String clOrdId, Order order, SessionID sessionID );
	
	/**
	 * 
	 * @param newOrderSingle
	 * @param sessionID
	 * @return
	 * @throws FieldNotFound
	 */
	public WorkflowMessage getStagedOrder( NewOrderSingle newOrderSingle, SessionID sessionID , MessageEvent messageEvent ) throws FieldNotFound;

	public Message getAllocationPortfolioQuote( Quote quote, String quoteRequestID, String senderSubID, String onBehalfOfCompID, String onBehalfOfSenderSubID, SessionID sessionId, String quoteId, String providerName, String quoteType );

    public Message getLimitOrderExecutionReport( OrderStatusRequest orderStatusRequest,
                                                 FXSingleLegOrder order,
                                                 SessionID sessionID ) throws FieldNotFound;

    public Message getRFSExecutionReport( String orderStatusRequest, int tagId, 
                                          Deal deal,
                                          SessionID sessionID  ) throws FieldNotFound;

    public Message getLimitOrderExecutionReport( OrderMassStatusRequest orderMassStatusRequest,
                                                 FXSingleLegOrder order,
                                                 SessionID sessionID ) throws FieldNotFound;

    public String updateNettingPortfolio( NettingPortfolio portfolio, com.integral.fix.client.message.NewOrderMultileg newOrderMultileg ) throws FieldNotFound;

    public MarketDataSnapshotFullRefresh getQuoteV3( Quote quote, FixSubscription subscriptionData, SessionID sessionId, boolean isBreakIfNoEntry );

	/**
	 *
	 * @param newOrderMultileg
	 * @param portfolioID
	 * @param pending
	 * @return
	 */
	public Message getBlockTradeOrderSubmissionExecutionReport(NewOrderMultileg newOrderMultileg,SingleLegOrder order, String portfolioID, String pending);

    Message getBlockTradeOrderFillExecutionReport(SingleLegTrade trade, NettingPortfolio portfolio, Map<String, BlockTradeMessageHandler.AllocationTradeInfo> tradeMap) throws FieldNotFound;

	Message getBlockTradeOrderExecutionReport(SingleLegOrder order, NettingPortfolio portfolio) throws FieldNotFound;

	Message getOrderStatusExecutionReport(SingleLegOrder o, SessionID sessionID) throws FieldNotFound;
}
