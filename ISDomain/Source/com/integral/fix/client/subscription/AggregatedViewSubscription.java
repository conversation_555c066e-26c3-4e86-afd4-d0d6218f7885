package com.integral.fix.client.subscription;

import com.integral.aggregation.price.FXPriceBook;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.fix.client.FixConfiguration;
import com.integral.fix.client.subscription.FixSubscription;
import com.integral.fix.client.cache.ClientCache;
import com.integral.fix.client.handler.rates.RateHandler;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 *
 */
public class AggregatedViewSubscription implements ProviderSubscription
{
	private Log log = LogFactory.getLog(this.getClass());

	protected FixConfiguration configuration;
	protected FixSubscription subscription;
	protected CurrencyPair ccyPair;

	private boolean isSubscribed = false;
	private String subscriptionKey;

	public AggregatedViewSubscription( FixConfiguration configuration, FixSubscription subscription )
	{
		this.subscription = subscription;
		this.configuration = configuration;
		this.ccyPair = subscription.getCurrencyPair();
		this.subscriptionKey = subscription.getSessionIDAsString() + "-" + (ccyPair != null ? ccyPair.getName() : "");
	}

	public boolean isSubscribed()
	{
		return isSubscribed;
	}

	public void sendRate( Quote quote )
	{

	}

	public void setSubscribed( boolean subscribed )
	{
		isSubscribed = subscribed;
	}

	public void sendPriceBook( FXPriceBook book )
	{
		try
		{
			if ( checkSession() )
			{
				RateHandler.handlePriceBookUpdate(book, subscription);
			}
			else
			{
				log.warn(new StringBuilder(128).append("Dropping the rate receieved becasue the session has logged out for User: ").append(subscription.getUser()).append(" currency pair: ").append(subscription.getCurrencyPair()).append(" session: ").append(subscription.getSessionIDAsString()).toString());
			}
		}
		catch ( Exception e )
		{

		}
		catch ( Throwable e )
		{
			e.printStackTrace();
		}
	}

	private boolean checkSession()
	{
		if ( ClientCache.getInstance().getSession(subscription.getUser(), 0) == null )
		{
			return false;
		}
		return true;
	}

	@Override
	public String toString(){
		return subscriptionKey;
	}
}
