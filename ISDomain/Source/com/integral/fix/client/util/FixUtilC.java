package com.integral.fix.client.util;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.client.ClientService;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.facade.EventTimeFacade;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.*;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.*;
import com.integral.finance.dealing.facade.DealStateFacade;
import com.integral.finance.dealing.facade.OrderStateFacade;
import com.integral.finance.dealing.facade.QuoteEventTimeFacade;
import com.integral.finance.dealing.facade.RequestStateFacade;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.*;
import com.integral.finance.marketData.RateIdPrefix;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.TradeServiceFactory;
import com.integral.fix.FIXUserSessionC;
import com.integral.fix.client.*;
import com.integral.fix.client.cache.ClientCache;
import com.integral.fix.client.handler.ApplicationHandler;
import com.integral.fix.client.handler.RateRejectionExecutionHandler;
import com.integral.fix.client.mbean.FIXClientConfig;
import com.integral.fix.client.mbean.FIXClientConfigMBean;
import com.integral.fix.client.message.*;
import com.integral.fix.client.message.grp.NoRelatedSym;
import com.integral.fix.client.monitor.SessionMonitor;
import com.integral.fix.client.query.SpacesFixClientQueryService;
import com.integral.fix.client.translator.Translator;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.log.MessageLogger;
import com.integral.is.management.monitor.AlertRuntimeMonitor;
import com.integral.is.oms.OrderConstants;
import com.integral.is.spaces.fx.client.fix.FXFIXClientService;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.jsp.JSPApplication;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.management.alert.*;
import com.integral.message.MessageFactory;
import com.integral.message.*;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.State;
import com.integral.model.dealing.descriptor.algo.AlgoDescriptor;
import com.integral.model.dealing.descriptor.algo.AlgoParametersExternalAlgo;
import com.integral.netting.NettingMBeanC;
import com.integral.netting.NettingUtils;
import com.integral.persistence.*;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryCriteriaBuilderC;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.security.LoginMBeanC;
import com.integral.security.SecurityServiceC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcUserSession;
import com.integral.staging.AllocationDetail;
import com.integral.staging.OrderStagingServiceFactory;
import com.integral.staging.OrderStatusType;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerType;
import com.integral.time.IdcDate;
import com.integral.user.*;
import com.integral.util.MathUtilC;
import com.integral.util.Tuple;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import quickfix.Message;
import quickfix.*;
import quickfix.Message.Header;
import quickfix.field.TimeInForce;
import quickfix.field.*;

import java.net.InetAddress;
import java.rmi.RemoteException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

import static com.integral.fix.client.FixConstants.SERVER_SESSION_PREFIX;
import static quickfix.FixVersions.*;
import static quickfix.field.PartyIDSource.GENERALLY_ACCEPTED_MARKET_PARTICIPANT_IDENTIFIER;


// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * Utility for FIXClientService
 *
 * <AUTHOR> Development Corp.
 */

/**
 * <AUTHOR>
 */
@SuppressWarnings( "unchecked" )
public class FixUtilC
{

    private static FixUtilC fixUtil = new FixUtilC();

    /**
     * stores ExternalSystem shortName ---> ExternalSystem to avoid DB lookup
     */
    private final static Map<String, ExternalSystem> externalSystems = new ConcurrentHashMap<String, ExternalSystem>();

    /**
     * stores RequestClassification shortName ---> RequestClassification to avoid DB lookup
     */
    private final static Map<String, RequestClassification> requestClassifications = new ConcurrentHashMap<String, RequestClassification>();

    /**
     * stores FXRateConvention shortName ---> FXRateConvention to avoid DB lookup
     */
    private final static Map<String, FXRateConvention> rateConventions = new ConcurrentHashMap<String, FXRateConvention>();

    /**
     * stores Organization shortName ---> Organization to avoid DB lookup
     */
    private final static Map<String, Organization> organizations = new ConcurrentHashMap<String, Organization>();

    /**
     * stores Counterparty shortName ---> Counterparty to avoid DB lookup
     */
    private final static Map<String, Counterparty> counterparties = new ConcurrentHashMap<String, Counterparty>();

    /**
     * stores ExternalSystem shortName ---> TradingParty to avoid DB lookup
     */
    private final static Map<String, TradingParty> externalSystem_TradingParty = new ConcurrentHashMap<String, TradingParty>();

    /**
     * stores ExternalSystem shortName ---> Organization to avoid DB lookup
     */
    private final static Map<String, Organization> externalSystem_Organizations = new ConcurrentHashMap<String, Organization>();

    /**
     * DefaultUser required for DB lookup
     */
    private String user;

    /**
     * DefaultUser reference
     */
    private User defaultUser;

    /**
     * Service to query objects from DB
     */
    private static QueryService queryService;

    /**
     * Service to retrieve Trade message from DB
     */
    private TradeService tradeService;

    private RequestService orderRequestService;

    private RequestService isRequestService;

    private RequestService primaryRequestService;

    private static final String GUID_DELIMITER = "-";

    private static final String GUID_PREFIX = "G-";

    private static String IPNAME;

    private static int GUID_COUNTER = 0;

    private AtomicLong SEQUENCE_NUM = new AtomicLong( 0 );

    private static final Object GUID_SINGELTON = new Object();

    private FixConfiguration configuration;

    private static Log log = LogFactory.getLog( FixUtilC.class );

    private Executor executor = new ThreadPoolExecutor( 5, Integer.MAX_VALUE, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>() );

    /*
     * Masked - Real name mapping
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, String>> realLPNames = new ConcurrentHashMap<String, ConcurrentHashMap<String, String>>();

    /*
     * Real - Masked name mapping
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, String>> maskedLPNames = new ConcurrentHashMap<String, ConcurrentHashMap<String, String>>();

    /*
     * Cache of allowed market depths for a FI-LP combination (LP name is real name not masked)
     * Organization indexes are used to store market depths
     */
    private ConcurrentHashMap<Integer, ConcurrentHashMap<Integer, Integer>> marketDepths = new ConcurrentHashMap<Integer, ConcurrentHashMap<Integer, Integer>>();

    static FIXClientConfigMBean configMBean = FIXClientConfig.getInstance();

    /**
     * type safe handler for fix messages
     */
    private Map<SessionID, quickfix.fix42.MessageCracker> handlers = new ConcurrentHashMap<SessionID, quickfix.fix42.MessageCracker>();

    private ThreadedSocketAcceptor acceptor = null;

    public ThreadedSocketAcceptor getAcceptor() {
        return acceptor;
    }

    public void setAcceptor(ThreadedSocketAcceptor acceptor) {
        this.acceptor = acceptor;
    }

    /**
     * Stores valid values for aggreation types (tag = 7548), will be used for validation of this
     * custom tag
     */
    private static Set<String> aggregation_types = new HashSet<String>( 10 );

    private static ThreadPoolExecutor inMessageProcessor;

    private static ISMBean isMbean = ISFactory.getInstance().getISMBean();
    private static ISUtilImpl isUtil = ISUtilImpl.getInstance();
    public final String ALERT_DK_Received = "DK.RECEVIED.STAGING.ORDER";

    static
    {
        inMessageProcessor = new ThreadPoolExecutor( configMBean.getThreadPoolInitialSize( "Shared" ), configMBean.getThreadPoolMaxSize( "Shared" ), configMBean.getThreadPoolKeepAliveTime( "Shared" ), TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>( configMBean.getThreadPoolWorkQueueSize( "Shared" ) ), new UserMessageWorkerThreadFactory( "inMessagesProcessor" ) );
        inMessageProcessor.prestartAllCoreThreads();
        inMessageProcessor.setRejectedExecutionHandler( new ThreadPoolExecutor.CallerRunsPolicy() );
        aggregation_types.add( FixConstants.VWAP_AGGREGATION_TYPE );
        aggregation_types.add( FixConstants.FULL_BOOK_AGGREGATION_TYPE );
        aggregation_types.add( FixConstants.MULTI_TIER_MARKET_AGGREGATION_TYPE );
        aggregation_types.add( FixConstants.MULTI_TIER_PRICES_AGGREGATION_TYPE );
        aggregation_types.add( FixConstants.SYNTHETIC_FULL_BOOK_MULTI_TIER_AGGREGATION_TYPE );
        aggregation_types.add( FixConstants.SYNTHETIC_FULL_BOOK_VWAP_AGGREGATION_TYPE );
        aggregation_types.add( FixConstants.BEST_PRICE_AGGREGATION_TYPE );
        aggregation_types.add( FixConstants.MULTI_TIER_FOK_AGGREGATION_TYPE );
        aggregation_types.add(FixConstants.SYNTHETIC_FULL_BOOK_MULTI_TIER_FOK_AGGREGATION_TYPE);
        aggregation_types.add(FixConstants.FULL_AMOUNT_MULTI_TIER_AGGREGATION_TYPE);
        aggregation_types.add(FixConstants.FULL_AMOUNT_MULTI_QUOTE_AGGREGATION_TYPE);
    }

    public static FixUtilC getInstance()
    {
        return fixUtil;
    }

    public static void ______setInstance(FixUtilC newutil){
        fixUtil = newutil;
    }

    public FIXClientConfigMBean getFIXClientConfig()
    {
        return configMBean;
    }
    private static final OrderConfigurationMBean _orderConfig = OrderConfiguration.getInstance ();

    /**
     * initilizes Utils
     *
     * @param configuration
     */
    public void init( FixConfiguration configuration )
    {
        this.configuration = configuration;
        user = configMBean.getDefaultUser();
        loadMaskedProviderNameCache();
        loadMarketDepths();
    }

    private void loadMarketDepths()
    {
        Properties keys = configMBean.getPropertiesWithPrefix( FIXClientConfigMBean.Key_market_depth );
        if ( keys.isEmpty() )
        {
            return;
        }

        marketDepths.clear();
        for ( Object oKey : keys.keySet() )
        {
            String key = ( String ) oKey;
            String[] tokens = key.split( "[.]" );
            if ( tokens.length < 3 )
            {
                log.warn( "FIXUtilC.loadMarketDepths - Invalid key skipped. Key -" + key );
                continue;
            }
            else
            {
                int marketDepth = configMBean.getIntProperty( key, -1 );
                if ( marketDepth == -1 )
                {
                    continue;
                }
                Organization fi = getOrganization( tokens[2] );
                if ( fi == null )
                {
                    log.warn( "FIXUtilC.loadMarketDepths - FI not found in database . Key skipped. Key -" + key );
                    continue;
                }

                if ( tokens.length == 4 )
                {
                    Organization lp = getOrganization( tokens[3] );
                    if ( lp == null )
                    {
                        log.warn( "FIXUtilC.loadMarketDepths - LP not found in database . Key skipped. Key -" + key );
                        continue;
                    }
                    ConcurrentHashMap<Integer, Integer> fiMarketDepths = marketDepths.get( fi.getIndex() );
                    if ( fiMarketDepths == null )
                    {
                        fiMarketDepths = new ConcurrentHashMap<Integer, Integer>();
                        marketDepths.put( fi.getIndex(), fiMarketDepths );
                    }
                    fiMarketDepths.put( lp.getIndex(), marketDepth );
                }
                else
                {
                    ConcurrentHashMap<Integer, Integer> fiMarketDepths = marketDepths.get( fi.getIndex() );
                    if ( fiMarketDepths == null )
                    {
                        fiMarketDepths = new ConcurrentHashMap<Integer, Integer>();
                        marketDepths.put( fi.getIndex(), fiMarketDepths );
                    }
                    fiMarketDepths.put( fi.getIndex(), marketDepth );
                }
            }
        }

        StringBuilder buff = new StringBuilder( 200 );
        buff.append( "FIXUtilC.loadMarketDepths - Loaded MarketDepths -> " ).append( marketDepths );
        log.warn( buff.toString() );

    }

    public int getAllowedMarketDepth( Organization fi, Organization lp )
    {
        ConcurrentHashMap<Integer, Integer> m = marketDepths.get( fi.getIndex() );
        if ( m == null )
        {
            return -1;
        }
        Integer val = lp == null ? null : m.get( lp.getIndex() );
        if ( val == null )
        {
            val = m.get( fi.getIndex() );
        }
        return ( val != null ) ? val : -1;
    }

    private void loadMaskedProviderNameCache()
    {
        Properties mppings = configMBean.getPropertiesWithPrefix( FIXClientConfigMBean.Key_masked_name );
        if ( mppings.isEmpty() )
        {
            return;
        }
        for ( Object oKey : mppings.keySet() )
        {
            String key = ( String ) oKey;
            String[] tokens = key.split( "[.]" );
            if ( tokens.length != 5 )
            {
                log.warn( "FIXUtilC.loadMaskedProviderNameCache - Invalid key skipped. Key -" + key );
                continue;
            }
            else
            {
                String maskedName = mppings.getProperty( key );
                String realName = tokens[3];
                String fiName = tokens[4];
                ConcurrentHashMap<String, String> reals = realLPNames.get( fiName );
                if ( reals == null )
                {
                    reals = new ConcurrentHashMap<String, String>();
                    realLPNames.put( fiName, reals );
                }
                reals.put( maskedName, realName );
                ConcurrentHashMap<String, String> masks = maskedLPNames.get( fiName );
                if ( masks == null )
                {
                    masks = new ConcurrentHashMap<String, String>();
                    maskedLPNames.put( fiName, masks );
                }
                masks.put( realName, maskedName );
            }
        }
        StringBuilder buff = new StringBuilder( 200 );
        buff.append( "FIXUtilC.loadMaskedProviderNameCache - Loaded Real-Masked LP names mapping." );
        buff.append( "Real-Masked -> " ).append( maskedLPNames );
        buff.append( "Masked-Real -> " ).append( realLPNames );
        log.warn( buff.toString() );
    }

    /**
     * returns reference to FixConfiguration
     *
     * @return FixConfiguration
     */
    public FixConfiguration getFixConfiguration()
    {
        return configuration;
    }

    /**
     * Gets Currency Pair given the request object
     *
     * @param request
     * @return CurrencyPair
     */
    public CurrencyPair getCurrencyPair( Request request )
    {
        FXTrade fxt = ( FXTrade ) request.getTrade();
        FXLeg fxLeg;
        if ( ISUtilImpl.isSwap( fxt ) )
        {
            fxLeg = ( ( FXSwap ) fxt ).getNearLeg();
        }
        else
        {
            fxLeg = ( ( FXSingleLeg ) fxt ).getFXLeg();
        }
        return fxLeg.getFXPayment().getFXRate().getFXRateBasis().getCurrencyPair();
    }

    public int getBuySell( FXPaymentParameters payment )
    {
        boolean buyingCurrency1 = payment.isBuyingCurrency1();
        boolean isCcy1Dealt = payment.isDealtCurrency1();
        boolean isBuy = ( buyingCurrency1 && isCcy1Dealt ) || ( !buyingCurrency1 && !isCcy1Dealt );
        return isBuy ? FixConstants.Buy : FixConstants.Sell;
    }

    /**
     * set Reject Reason on ExecutionReport Message
     *
     * @param executionReport
     * @param rejectReason
     */
    public void setRejectReason( ExecutionReport executionReport, String rejectReason )
    {
        if ( rejectReason == null )
        {
            rejectReason = "";
        }
        executionReport.set( new Text( rejectReason ) );
        if ( !contains( FixConstants.NO_FIX_CODE_REJECT_REASONS, rejectReason ) )
        {
            if ( contains( FixConstants.TOO_LATE_TO_ENTER_REJECT_REASONS, rejectReason ) )
            {
                executionReport.set( FixConstants.ORD_REJ_REASON_TOO_LATE_TO_ENTER );
            }
            else if ( contains( FixConstants.ORDER_EXCEEDS_LIMIT_REJECT_REASONS, rejectReason ) )
            {
                executionReport.set( FixConstants.ORD_REJ_REASON_ORDER_EXCEEDS_LIMIT );
            }
            else if ( FixConstants.ACCEPTANCE_REJECTION_REASON_DUPLICATE_ORDER.equals( rejectReason ) )
            {
                executionReport.set( FixConstants.ORD_REJ_REASON_DUPLICATE_ORDER );
            }
            else
            {
                executionReport.set( FixConstants.ORD_REJ_REASON_BROKER_EXCHANGE_OPTION );
            }
        }
    }
    public void setRejectReasonForManualOrder( ExecutionReport executionReport, String rejectReason )
    {
        if ( rejectReason == null )
        {
            rejectReason = "";
        }
        executionReport.set( new Text( rejectReason ) );

        if ( contains( FixConstants.TOO_LATE_TO_ENTER_REJECT_REASONS, rejectReason ) )
        {
            executionReport.set( FixConstants.ORD_REJ_REASON_TOO_LATE_TO_ENTER );
        }
        else if ( contains( FixConstants.ORDER_EXCEEDS_LIMIT_REJECT_REASONS, rejectReason ) )
        {
            executionReport.set( FixConstants.ORD_REJ_REASON_ORDER_EXCEEDS_LIMIT );
        }
        else if ( FixConstants.ACCEPTANCE_REJECTION_REASON_DUPLICATE_ORDER.equals( rejectReason ) )
        {
            executionReport.set( FixConstants.ORD_REJ_REASON_DUPLICATE_ORDER );
        }
        else
        {
            executionReport.set(new OrdRejReason(OrdRejReason.OTHER));
        }

    }

    public TradingParty getTradingPartyFromCache( String extSys, String sysIdValue, String orgName )
    {
        String key = sysIdValue + '.' + orgName;
        TradingParty tp = externalSystem_TradingParty.get( key );
        return tp;
    }

    /**
     * get trading party for the provided external system value
     *
     * @param extSys
     * @param sysIdValue
     * @return TradingParty
     */
    public TradingParty getTradingParty( String extSys, String sysIdValue, String orgName )
    {
        String key = sysIdValue + '.' + orgName;
        TradingParty tp = externalSystem_TradingParty.get( key );
        if ( tp == null )
        {
            Vector tps = getAllObjects( getExternalSystem( extSys ), TradingParty.class, sysIdValue );
            if ( tps != null )
            {
                for ( Object object : tps )
                {
                    TradingParty tp1 = ( TradingParty ) object;
                    String key1 = sysIdValue + '.' + tp1.getLegalEntityOrganization().getShortName();
                    externalSystem_TradingParty.put( key1, tp1 );
                }
            }
        }
        tp = externalSystem_TradingParty.get( key );
        return tp;
    }

    public LegalEntity getCounterparty( String shortName, Organization org )
    {
        LegalEntity le = org.getLegalEntity ( shortName );
        if ( le == null )
        {
            log.info ( "FixUtilC.getCounterparty - No legal entity with shortname=" + shortName + " in org=" + org );
        }
        return le;
    }

    public TradingParty getTradingParty( String shortName, Namespace namespace )
    {
        return ( TradingParty ) ReferenceDataCacheC.getInstance().getCachedEntityByShortNameInNamespace( shortName, TradingPartyC.class, namespace, null );
    }

    /**
     * get Organization for the provided external system value
     *
     * @param extSys    external system name
     * @param shortName shortname
     * @return TradingParty
     */
    public Organization getCustomerOrganization( String extSys, String shortName )
    {
        Organization o = ReferenceDataCacheC.getInstance().getOrganization( shortName );
        if ( o == null ) //only if org with shortname is not found, we need to look up the external system name.
        {
            try
            {
                // in this check the ext sys cache first before attempting the query.
                //todo we will have to eliminate the use of external system ids once we find that org name only is used.
                o = externalSystem_Organizations.get( shortName );
                if ( o == null )
                {
                    o = ( Organization ) getObject( getExternalSystem( extSys ), Organization.class, shortName );
                    if ( o != null )
                    {
                        externalSystem_Organizations.put( shortName, o );
                    }
                }
            }
            catch ( Exception e )
            {
                log.warn( "FixUtilC.getCustomerOrganization - Org not found with name " + shortName, e );
            }
        }
        return o;
    }

    /**
     * returns a collection relatedLPs excluding the orgs specified in property file
     *
     * @param custOrg
     * @return
     */
    public Collection<Organization> getRelatedLPs( Organization custOrg, MarketDataRequest marketDataRequest, boolean includeVenueProviders )
    {

        if ( !isMbean.isLiquidityProvisioningEnabled( custOrg.getShortName() ) )
        {
            Collection<Organization> relatedOrg = custOrg.getRelatedOrganizations( FixConstants.FI_ORG_RELATIONSHIP );
            Collection<Organization> relatedLPOrg = new ArrayList<Organization>();
            if ( relatedOrg != null )
            {
                String excludingLPOrgList = configMBean.getOrganizationsToBeExcluded();
                if ( "".equalsIgnoreCase( excludingLPOrgList ) )
                {
                    return relatedLPOrg;
                }
                else
                {
                    for ( Iterator<Organization> relatedOrgIterator = relatedOrg.iterator(); relatedOrgIterator.hasNext(); )
                    {
                        Organization relatedOrganization = relatedOrgIterator.next();
                        String relatedOrgName = relatedOrganization.getShortName();
                        if ( !excludingLPOrgList.contains( relatedOrgName ) )
                        {
                            relatedLPOrg.add( relatedOrganization );
                        }
                    }
                }
            }
            return relatedLPOrg;
        }
        else
        {
            //If liquidity provisioning is enabled, obtain all aggregation providers from rule
            return getAggregationProviders( custOrg, marketDataRequest, includeVenueProviders );
        }

    }

    /**
     * returns a collection relatedLPs excluding the orgs specified in property file
     *
     * @param custOrg
     * @return
     */
    public Collection<Organization> getRelatedME( Organization custOrg, MarketDataRequest marketDataRequest )
    {


        Collection<Organization> aggregationProviders = new ArrayList<Organization>();
        try
        {
           /* com.integral.fix.client.message.grp.NoRelatedSym symbolGroup = marketDataRequest.createNoRelatedSym();
            marketDataRequest.getGroup( 1, symbolGroup );
            String ccyPairStr = symbolGroup.getSymbol().getValue();
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair( ccyPairStr.substring( 0, 3 ), ccyPairStr.substring( 4 ) );

            */
            if(AggregationServiceFactory.getInstance().getAggregationMBean().isClobSubscriptionEnabled(custOrg.getShortName())){
                aggregationProviders.addAll( isUtil.getCLOBProvidersFromLiquidityRules( custOrg ) );
            }

            return aggregationProviders;
        }
        catch ( Exception e )
        {
            log.error( "FixUtilC.getRelatedME: Error while getting getRelatedME provider-", e );
        }
        return aggregationProviders;


    }

    /**
     * Obtain the aggregation providers based on liquidity rules defined for customer
     *
     * @param customerOrg       customer org
     * @param marketDataRequest mdr
     * @return list of aggregation providers
     */
    private Collection<Organization> getAggregationProviders( Organization customerOrg, MarketDataRequest marketDataRequest, boolean includeVenueProviders )
    {
        Collection<Organization> aggregationProviders = new ArrayList<Organization>();
        try
        {
            CurrencyPair ccyPair = marketDataRequest.getCurrencyPairDetails().getDerivedCurrencyPair();
            aggregationProviders = isUtil.getAggregationProvidersFromLiquidityRule(customerOrg, ccyPair);
            if(includeVenueProviders){
                if(AggregationServiceFactory.getInstance().getAggregationMBean().isRiskNetSubscriptionEnabled(customerOrg.getShortName())){
                    aggregationProviders.addAll( isUtil.getRiskNetProvidersFromLiquidityRules( customerOrg ) );
                }
                if(AggregationServiceFactory.getInstance().getAggregationMBean().isClobSubscriptionEnabled(customerOrg.getShortName())){
                    aggregationProviders.addAll( isUtil.getCLOBProvidersFromLiquidityRules( customerOrg ) );
                }
            }
            return aggregationProviders;
        }
        catch ( Exception e )
        {
            log.error( "FixUtilC.getAggregationProviders: Error while getting aggregation providers-", e );
        }
        return aggregationProviders;
    }

    /**
     * get Counterparty reference for shortName
     *
     * @param counterparty
     * @return Counterparty
     */
    //	public Counterparty getCounterparty(String counterparty)
    //	{
    //		Counterparty c = counterparties.get(counterparty);
    //		if ( c == null )
    //		{
    //			counterparties.put(counterparty, c = (Counterparty) getObject(Counterparty.class, counterparty));
    //		}
    //		return c;
    //	}

    /**
     * get Organization reference for the shortName
     *
     * @param org
     * @return organization
     */
    public Organization getOrganization( String org )
    {

        String tempOrg = org;
        if(tempOrg ==null || (tempOrg =org.trim()).isEmpty()||"ALL".equals(tempOrg)){
            return null;
        }

        Organization o = ReferenceDataCacheC.getInstance().getOrganization(tempOrg);
        if ( o == null )
        {
            o = organizations.get( tempOrg );
            if ( o == null )
            {
                o = ( Organization ) getObject( Organization.class, tempOrg );
                if(  o != null )
                {
                    organizations.put( tempOrg, o );
                }
            }
        }
        return o;
    }

    /**
     * get request classification for the shortName
     *
     * @param classification
     * @return requestClassification
     */
    public RequestClassification getRequestClassification( String classification )
    {
        RequestClassification rc = requestClassifications.get( classification );
        if ( rc == null )
        {
            rc = ( RequestClassification ) getObject( RequestClassification.class, classification );
            if( rc != null ){
                requestClassifications.put( classification, rc );
            }
        }
        return rc;
    }

    /**
     * get External system for shortName
     *
     * @param extSys
     * @return ExternalSystem
     */
    public ExternalSystem getExternalSystem( String extSys )
    {
        ExternalSystem es = externalSystems.get( extSys );
        if ( es == null )
        {
            es = ( ExternalSystem ) getObject( ExternalSystem.class, extSys );
            if( es != null ){
                externalSystems.put( extSys, es );
            }
        }
        return es;
    }

    /**
     * get request channel for FIX it would be the version of FIX we use
     *
     * @param message
     * @return ExternalSystem
     */
    public ExternalSystem getRequestChannel( Message message )
    {
        ExternalSystem extSys = null;
        try
        {
            extSys = getExternalSystem( message.getHeader().getString( BeginString.FIELD ).replace( ".", "" ) );//removes dots as shortname cannot have dots
        }
        catch ( FieldNotFound e )
        {
            e.printStackTrace();
        }
        return extSys;
    }

    /**
     * get RateConvention for shortName
     *
     * @param rateConvention
     * @return rateConvention
     */
    public FXRateConvention getFXRateConvention( String rateConvention )
    {
        FXRateConvention rc = rateConventions.get( rateConvention );
        if ( rc == null )
        {
            rc = ( FXRateConvention ) getObject( FXRateConvention.class, rateConvention );
            if( rc != null ) {
                rateConventions.put( rateConvention, rc );
            }
        }
        return rc;
    }

    /**
     * sets the Integral1 user context and retrieves object using QueryService.
     *
     * @param ifsClass
     * @param shortName
     * @return Object
     */
    public Object getObject( Class ifsClass, String shortName )
    {
        Object obj = null;
        IdcSessionContext oldctx = IdcSessionManager.getInstance().getSessionContext();
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( getDefaultUser() );
        IdcSessionManager.getInstance().setSessionContext( ctx );
        try
        {
            obj = getQueryService().find( ifsClass, shortName );
        }
        catch ( IdcNoSuchObjectException e )
        {
            log.warn( "FU.getObject : Object not found for " + ifsClass + " shortName " + shortName );
        }
        IdcSessionManager.getInstance().setSessionContext( oldctx );
        return obj;
    }

    /**
     * get Entity reference with owns given name ExternalSystem
     *
     * @param extSys
     * @param ifsClass
     * @param shortName
     * @return Object
     */
    public Object getObject( ExternalSystem extSys, Class ifsClass, String shortName )
    {
        Object obj = null;
        IdcSessionContext oldctx = IdcSessionManager.getInstance().getSessionContext();
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( getDefaultUser() );
        IdcSessionManager.getInstance().setSessionContext( ctx );
        try
        {
            log.info( new StringBuilder( 200 ).append( "FU.getObject : Querying database for organization with ext sys value=" ).append( shortName ).append( ",extSys=" ).append( extSys ).toString() );
            obj = getQueryService().find( ifsClass, extSys, shortName );
        }
        catch ( IdcNoSuchObjectException e )
        {
            log.warn( "FU.getObject : no object found of type=" + ifsClass + ",shortName=" + shortName );
        }
        IdcSessionManager.getInstance().setSessionContext( oldctx );
        return obj;
    }

    public Vector getAllObjects( ExternalSystem extSys, Class ifsClass, String shortName )
    {
        Vector obj = null;
        IdcSessionContext oldctx = IdcSessionManager.getInstance().getSessionContext();
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( getDefaultUser() );
        IdcSessionManager.getInstance().setSessionContext( ctx );
        try
        {
            obj = _find( null, ifsClass, extSys, shortName );

        }
        catch ( IdcNoSuchObjectException e )
        {
            log.warn( "FU.geAllObjects : no objects found of type=" + ifsClass + ",shortName=" + shortName );
        }
        IdcSessionManager.getInstance().setSessionContext( oldctx );
        return obj;
    }

    private Vector _find( Namespace aNamespace, Class aClass, ExternalSystem anExternalSystem, String sysId ) throws IdcNoSuchObjectException
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression extSysIdExpr = eb.anyOf( "externalSystemIds" );
        Expression sysidExpr = extSysIdExpr.get( "name" ).equal( sysId );

        ReadAllQuery query = new ReadAllQuery( aClass );
        query.setSelectionCriteria( sysidExpr );
        Vector results = ( Vector ) getQueryService().find( aNamespace, query );

        if ( ( results != null ) && ( results.size() >= 1 ) )
        {
            return results;
        }
        else
        {
            return null;
        }
    }

    /**
     * returns RequestService
     *
     * @param wfMsg
     * @return
     * @throws RemoteException
     */
    public RequestService getRequestService( WorkflowMessage wfMsg ) throws RemoteException
    {
        Object obj = wfMsg.getObject();
        RequestService rs = getPrimaryRequestService();
        if ( obj != null && ( ( obj instanceof Organization )
                || ( obj instanceof ExternalSystem ) || ( obj instanceof Request ) ) ||
                (obj instanceof Trade))
        {
            String tradeType = ( String ) wfMsg.getParameterValue( FixConstants.TRADE_TYPE );
            if ( FixConstants.TRADE_TYPE_ESP.equalsIgnoreCase( tradeType )
                    || FixConstants.TRADE_TYPE_RFS.equalsIgnoreCase( tradeType ) )
            {
                return getISRequestService();
            }
            else if ( FixConstants.TRADE_TYPE_LIMIT_ORDERS.equalsIgnoreCase( tradeType ) )
            {
                return getOrderRequestService();
            }
            else
            {
                return rs;
            }
        }
        else if ( obj instanceof Quote )
        {
            //In this case its RFS Trade request.
            return getISRequestService();
        }
        else
        {
            return rs;
        }
    }

    public ClientService getClientService()
    {
        return clientService;
    }

    private RequestService getISRequestService()
    {
        return isRequestService;

    }

    private RequestService getOrderRequestService()
    {
        return orderRequestService;
    }

    private RequestService getPrimaryRequestService()
    {
        return primaryRequestService;
    }

    /**
     * get QueryService
     *
     * @return QueryService
     */
    public QueryService getQueryService()
    {
        if ( queryService == null )
        {
            return queryService = QueryFactory.getQueryService();
        }
        return queryService;
    }

    /**
     * get Quote Timestamp
     *
     * @param quote
     * @param name
     * @return long
     */
    public long getQuoteTime( Quote quote, String name )
    {
        QuoteEventTimeFacade etFacade = ( QuoteEventTimeFacade ) quote.getFacade( EventTimeFacade.NAME );
        long quoteReceivedTime = 0;
        if ( etFacade != null )
        {
            quoteReceivedTime = etFacade.getTime( name );
        }
        return quoteReceivedTime == 0 ? System.currentTimeMillis() : quoteReceivedTime;
    }

    /**
     * returns reference to default user
     *
     * @return User
     */
    public User getDefaultUser()
    {
        if ( defaultUser == null )
        {
            defaultUser = UserFactory.getUser( user );
        }
        return defaultUser;
    }

    public TradeService getTradeService()
    {
        if ( tradeService == null )
        {
            tradeService = TradeServiceFactory.getTradeService();
        }
        return tradeService;
    }

    private boolean contains( String[] rejectReasons, String rejectReason )
    {
        for ( String tmp : rejectReasons )
        {
            if ( tmp.equals( rejectReason ) )
            {
                return true;
            }
        }
        return false;
    }

    /**
     * generate new provider quote id
     *
     * @param provider
     * @param timestamp
     * @return
     */
    public String newProviderQuoteId( String provider, long timestamp )
    {
        if ( IPNAME == null )
        {
            StringBuilder hostname;
            try
            {
                hostname = new StringBuilder( 12 );
                String hostnameWithDot = InetAddress.getLocalHost().getHostAddress();
                for ( int i = 0; i < hostnameWithDot.length(); i++ )
                {
                    if ( hostnameWithDot.charAt( i ) != '.' )
                    {
                        hostname.append( hostnameWithDot.charAt( i ) );
                    }
                }
                IPNAME = GUID_PREFIX + Long.toHexString( Long.parseLong( hostname.toString() ) );
            }
            catch ( Exception e )
            {
                log.warn( "FixUtilC.newProviderQuoteId : GUID generation host lookup failed ", e );
                IPNAME = GUID_PREFIX + Long.toHexString( 127001L );
            }
        }
        StringBuilder buffer = new StringBuilder( IPNAME );
        buffer.append( GUID_DELIMITER ).append( Long.toHexString( timestamp ) ).append( GUID_DELIMITER ).append( provider ).append( GUID_DELIMITER );

        int num;
        synchronized ( GUID_SINGELTON )
        {
            num = ++GUID_COUNTER;
        }

        buffer.append( Integer.toHexString( num ) );
        return buffer.toString();
    }

    public String newSequenceNumber()
    {
        long sequenceNum = SEQUENCE_NUM.incrementAndGet();

        if ( sequenceNum >= 10000000 )
        {
            SEQUENCE_NUM.set( 0 );
        }

        return Long.toHexString( sequenceNum );
    }

    public User getUser(Message message, SessionID sessionID) throws FieldNotFound {
        Message.Header header = message.getHeader();
        if (FixUtilC.getInstance().isServer2ServerSession(sessionID)) {
            return getUserFromMessageForServerToServerSession(message, sessionID);
        } else if (header.isSetField(OnBehalfOfCompID.FIELD)) {
            String onBehalfOfCompID = header.getString(OnBehalfOfCompID.FIELD);
            int orgIndex = onBehalfOfCompID.indexOf(FixConstants.DELIMITER_USER_ORGANIZATION);
            String orgExtName = onBehalfOfCompID.substring(orgIndex + 1);
            Organization customerOrg = fixUtil.getCustomerOrganization(FixConstants.ON_BEHALF_OF_COMP_ID_EXTERNAL_SYSTEM, orgExtName);
            if (orgIndex > 0) {
                String custUser = onBehalfOfCompID.substring(0, orgIndex);
                String custUserName = new StringBuilder(custUser).append(FixConstants.DELIMITER_USER_ORGANIZATION).append(customerOrg.getShortName()).toString();
                return UserFactory.getUser(custUserName);
            } else {
                if (customerOrg != null) {
                    return customerOrg.getDefaultDealingUser();
                }
                return null;
            }
        } else {
            return ClientCache.getInstance().getUser(sessionID);
        }
    }

    private User getUserFromMessageForServerToServerSession(Message message, SessionID serverToServerSessionId) throws FieldNotFound{
        String userShortName = null;
        String userOrgShortName = null;
        if( BEGINSTRING_FIX44.equals(serverToServerSessionId.getBeginString())) {
            String msgType = message.getHeader().getString(MsgType.FIELD);
            if (MsgType.ORDER_SINGLE.equals(msgType)) {
                quickfix.fix44.NewOrderSingle.NoPartyIDs noPartyIDs = new quickfix.fix44.NewOrderSingle.NoPartyIDs();
                int groupCount = message.getGroupCount(noPartyIDs.getFieldTag());
                for(int i=1;i<=groupCount;++i){
                    message.getGroup(i,noPartyIDs);
                    PartyRole role = noPartyIDs.getPartyRole();
                    if( role != null ){
                        switch (role.getValue()){
                            case PartyRole.ENTERING_FIRM:
                                userOrgShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                            case PartyRole.ENTERING_TRADER:
                                userShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                        }
                    }
                }
            } else if (MsgType.ORDER_CANCEL_REQUEST.equals(msgType)) {
                quickfix.fix44.OrderCancelRequest.NoPartyIDs noPartyIDs = new quickfix.fix44.OrderCancelRequest.NoPartyIDs();
                int groupCount = message.getGroupCount(noPartyIDs.getFieldTag());
                for(int i=1;i<=groupCount;++i){
                    message.getGroup(i,noPartyIDs);
                    PartyRole role = noPartyIDs.getPartyRole();
                    if( role != null ){
                        switch (role.getValue()){
                            case PartyRole.ENTERING_FIRM:
                                userOrgShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                            case PartyRole.ENTERING_TRADER:
                                userShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                        }
                    }
                }
            }else if (MsgType.ORDER_CANCEL_REPLACE_REQUEST.equals(msgType)) {
                quickfix.fix44.OrderCancelReplaceRequest.NoPartyIDs noPartyIDs = new quickfix.fix44.OrderCancelReplaceRequest.NoPartyIDs();
                int groupCount = message.getGroupCount(noPartyIDs.getFieldTag());
                for(int i=1;i<=groupCount;++i){
                    message.getGroup(i,noPartyIDs);
                    PartyRole role = noPartyIDs.getPartyRole();
                    if( role != null ){
                        switch (role.getValue()){
                            case PartyRole.ENTERING_FIRM:
                                userOrgShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                            case PartyRole.ENTERING_TRADER:
                                userShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                        }
                    }
                }
            }else if (MsgType.ORDER_STATUS_REQUEST.equals(msgType)) {
                quickfix.fix44.OrderStatusRequest.NoPartyIDs noPartyIDs = new quickfix.fix44.OrderStatusRequest.NoPartyIDs();
                int groupCount = message.getGroupCount(noPartyIDs.getFieldTag());
                for(int i=1;i<=groupCount;++i){
                    message.getGroup(i,noPartyIDs);
                    PartyRole role = noPartyIDs.getPartyRole();
                    if( role != null ){
                        switch (role.getValue()){
                            case PartyRole.ENTERING_FIRM:
                                userOrgShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                            case PartyRole.ENTERING_TRADER:
                                userShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                        }
                    }
                }
            }else if (MsgType.TRADE_CAPTURE_REPORT_REQUEST.equals(msgType)) {
                quickfix.fix44.TradeCaptureReportRequest.NoPartyIDs noPartyIDs = new quickfix.fix44.TradeCaptureReportRequest.NoPartyIDs();
                int groupCount = message.getGroupCount(noPartyIDs.getFieldTag());
                for(int i=1;i<=groupCount;++i){
                    message.getGroup(i,noPartyIDs);
                    PartyRole role = noPartyIDs.getPartyRole();
                    if( role != null ){
                        switch (role.getValue()){
                            case PartyRole.ENTERING_FIRM:
                                userOrgShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                            case PartyRole.ENTERING_TRADER:
                                userShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                        }
                    }
                }
            }else if (MsgType.ORDER_MASS_CANCEL_REQUEST.equals(msgType)) {
                quickfix.fix44.OrderMassCancelRequest.NoPartyIDs noPartyIDs = new quickfix.fix44.OrderMassCancelRequest.NoPartyIDs();
                int groupCount = message.getGroupCount(noPartyIDs.getFieldTag());
                for(int i=1;i<=groupCount;++i){
                    message.getGroup(i,noPartyIDs);
                    PartyRole role = noPartyIDs.getPartyRole();
                    if( role != null ){
                        switch (role.getValue()){
                            case PartyRole.ENTERING_FIRM:
                                userOrgShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                            case PartyRole.ENTERING_TRADER:
                                userShortName = noPartyIDs.isSetPartyID() ? noPartyIDs.getString(PartyID.FIELD) : null;
                                break;
                        }
                    }
                }
            }
            else if(MsgType.ORDER_LIST.equals(msgType)){
                NoPartyIDs noPartyIDs = new NoPartyIDs();
                List<Group> groups = message.getGroups(noPartyIDs.getTag());
                for(Group group : groups){
                    if(group.isSetField(PartyRole.FIELD)){
                        int partyRole = group.getInt(PartyRole.FIELD);
                        switch (partyRole){
                            case PartyRole.ENTERING_FIRM:
                                if( group.isSetField(PartyID.FIELD)){
                                    userOrgShortName = group.getString(PartyID.FIELD);
                                }
                                break;
                            case PartyRole.ENTERING_TRADER:
                                if( group.isSetField(PartyID.FIELD)){
                                    userShortName = group.getString(PartyID.FIELD);
                                }
                                break;
                        }
                    }
                }
            }
            else {
                log.warn("getUserFromMessage : unsupported message type for retrieving user from messages in server level session: "+ msgType);
            }
        }else {
            log.warn("getUserFromMessage : unsupported fix version: "+ serverToServerSessionId.getBeginString());
        }
        if( userShortName != null && userOrgShortName != null){
            String custUserName = new StringBuilder( userShortName ).append( FixConstants.DELIMITER_USER_ORGANIZATION ).append( userOrgShortName ).toString();
            return UserFactory.getUser( custUserName );
        }else {
            log.warn("getUserFromMessage : Didn't find user in Message [NestedParties]: " + message);
        }
        return null;
    }


    public SessionID getUserSessionIdForServerToServerSessionId(final Message message, final SessionID serverToServerSessionId) throws FieldNotFound {
        String msgType = message.getHeader().getString(MsgType.FIELD);
        SessionID userSessionId = serverToServerSessionId;
        if(MsgType.USER_REQUEST.equals(msgType) || MsgType.BUSINESS_MESSAGE_REJECT.equals(msgType)){
            return userSessionId;
        }
        User customer = getUserFromMessageForServerToServerSession(message,serverToServerSessionId);
        if( customer == null){
            if(log.isDebugEnabled()){
                log.debug("getServerLevelSessionId : User not found from message. Looking for user from cached order");
            }
            if (!MsgType.ORDER_SINGLE.equals(msgType) && !MsgType.ORDER_LIST.equals(msgType)) {
                String clOrdId = message.isSetField(ClOrdID.FIELD) ? message.getField(new ClOrdID()).getValue() : null;
                if (clOrdId != null) {
                    SingleLegOrder order = ClientCache.getInstance().getOrderRequest(serverToServerSessionId, clOrdId);
                    if (order != null) {
                        customer = order.getUser();
                    }
                } else {
                    log.warn("getServerLevelSessionId : the message is missing ClOrdID so not able to find the existing order: " + message);
                }
            }
        }
        if (customer != null) {
            ServerLevelSessionID userServerLevelSession = ClientCache.getInstance().getServerLevelSession(serverToServerSessionId, customer);
            if (userServerLevelSession == null) {
                userServerLevelSession = createServerLevelSessionId(serverToServerSessionId, customer);
            }
            else{
                if( !userServerLevelSession.getParent().equals(serverToServerSessionId)){
                    removeServerLevelSessionId(userServerLevelSession.getParent(),customer);
                    userServerLevelSession = createServerLevelSessionId(serverToServerSessionId,customer);
                }
            }
            userSessionId = userServerLevelSession;
        } else {
            log.warn("getServerLevelSessionId : Not able to create ServerLevelSessionId due to missing user info: " + message);
        }
        return userSessionId;
    }

    public ServerLevelSessionID createServerLevelSessionId( SessionID parentSessionId,  User user){
        ServerLevelSessionID serverLevelSessionID = ServerLevelSessionIDFactory.getInstance().create(parentSessionId, user);
        ClientCache.getInstance().addServerSessionUser( serverLevelSessionID, user);
        ClientCache.getInstance().addUserForServerSession(user, serverLevelSessionID);
        return serverLevelSessionID;
    }

    public ServerLevelSessionID removeServerLevelSessionId( SessionID parentSessionId,  User user){
        ServerLevelSessionID serverLevelSessionID = ServerLevelSessionIDFactory.getInstance().create(parentSessionId, user);
        ClientCache.getInstance().removeServerSessionUser( serverLevelSessionID);
        ClientCache.getInstance().removeUserForServerSession(user, serverLevelSessionID);
        return serverLevelSessionID;
    }

    public Organization getCustomerOrg( Message message, SessionID sessionID ) throws FieldNotFound
    {

        Message.Header header = message.getHeader();
        String onBehalfOfCompID = null;
        boolean isSetOnBehalfOfCompID = header.isSetField( OnBehalfOfCompID.FIELD ) ? ( onBehalfOfCompID = header.getString( OnBehalfOfCompID.FIELD ).trim() ).length() > 0 : false;

        if ( isSetOnBehalfOfCompID )
        {
            int orgIndex = onBehalfOfCompID.indexOf( FixConstants.DELIMITER_USER_ORGANIZATION );
            String orgExtName = onBehalfOfCompID.substring( orgIndex + 1 );
            Organization customerOrg = fixUtil.getCustomerOrganization( FixConstants.ON_BEHALF_OF_COMP_ID_EXTERNAL_SYSTEM, orgExtName );
            return customerOrg;
        }
        else
        {
            final User user = ClientCache.getInstance().getUser( sessionID );
            if ( user != null )
            {
                return user.getOrganization();
            }
            else
            {
                String senderCompID = header.getString(SenderCompID.FIELD);
                int startIndex = senderCompID.indexOf('.');
                if( startIndex < 0 ){
                    if(log.isDebugEnabled()){
                        log.debug("failed to find org. m="+message+", sessionid="+sessionID);
                    }
                    return null;
                }
                String userOrgName = null;
                int endIndex = senderCompID.indexOf('.', startIndex + 1);
                if (endIndex < 0) {
                    userOrgName = senderCompID.substring(startIndex + 1);
                } else {
                    userOrgName = senderCompID.substring(startIndex + 1, endIndex);
                }
                return ReferenceDataCacheC.getInstance().getOrganization(userOrgName);
            }
        }
    }

    public OrdStatus getOrdStatus( Request request )
    {
        OrdStatus ordStatus = null;
        RequestStateFacade stateFacade = ( RequestStateFacade ) request.getFacade( RequestStateFacade.REQUEST_STATE_FACADE );
        int timeInForce = request.getTimeInForce();

        if ( stateFacade != null )
        {
            if ( stateFacade.isInitial() )
            {
                if ( request.getRequestAttributes().isExexcutionSuspended() )
                {
                    ordStatus = FixConstants.ORDER_STATUS_STOPPED;
                }
                else
                {
                    ordStatus = FixConstants.ORDER_STATUS_NEW;
                }
            }
            else if ( stateFacade.isDeclined() )
            {
                ordStatus = FixConstants.ORDER_STATUS_REJECTED;
            }
            else if ( stateFacade.isExpired() )
            {
                if ( timeInForce == 3 || timeInForce == 4 )
                {
                    ordStatus = FixConstants.ORDER_STATUS_CANCELED;
                }
                else
                {
                    ordStatus = FixConstants.ORDER_STATUS_EXPIRED;
                }
            }
            else if ( stateFacade.isCancelled() || stateFacade.isWithdrawn() )
            {
                ordStatus = FixConstants.ORDER_STATUS_CANCELED;
            }
            else if ( stateFacade.isAcceptVerified() )
            {
                ordStatus = FixConstants.ORDER_STATUS_FILLED;
            }
            else if ( stateFacade.isPartial() )
            {
                ordStatus = FixConstants.ORDER_STATUS_PARTIALLY_FILLED;
            }
            else
            {
                ordStatus = FixConstants.ORDER_STATUS_NEW;
            }
        }
        else
        {
            ordStatus = FixConstants.ORDER_STATUS_NEW;
        }
        return ordStatus;
    }

    public OrdStatus getOrdStatus( Order order )
    {
        OrdStatus ordStatus = null;
        OrderStateFacade stateFacade = ( OrderStateFacade ) order.getFacade( OrderStateFacade.FACADE_NAME );
        stateFacade.setOrder( order );

        if ( stateFacade != null )
        {
            if(stateFacade.isExpired())
            {
                ordStatus = FixConstants.ORDER_STATUS_EXPIRED;
            }
            else if ( stateFacade.isFailed())
            {
                ordStatus = FixConstants.ORDER_STATUS_REJECTED;
            }
            else if ( stateFacade.isCancelled() )
            {
                ordStatus = FixConstants.ORDER_STATUS_CANCELED;
            }
            else if ( stateFacade.isFilled() )
            {
                ordStatus = FixConstants.ORDER_STATUS_FILLED;
            }
            else if ( stateFacade.isCreated() )
            {
                ordStatus = FixConstants.ORDER_STATUS_NEW;
            }
            else
            {
                ordStatus = FixConstants.ORDER_STATUS_NEW;
            }
        }
        else
        {
            ordStatus = FixConstants.ORDER_STATUS_NEW;
        }
        return ordStatus;
    }

    public OrdStatus getStagingOrderStatus(final com.integral.staging.Order order)
    {
        if (order == null)
        {
            // If order is not there in database that means it was rejected before being persisted in database
            // so setting state to "Rejected"
            return new OrdStatus(OrdStatus.REJECTED);
        }
        OrdStatus ordStatus = null;
        switch (OrderStatusType.fromCode(order.getState())) {
            case INTIAL:
                ordStatus = FixConstants.ORDER_STATUS_NEW;
                break;
            case CANCELLED:
                ordStatus = FixConstants.ORDER_STATUS_CANCELED;
                break;

            case REJECTED:
                ordStatus = FixConstants.ORDER_STATUS_REJECTED;
                break;

            case EXECUTED:
                ordStatus = FixConstants.ORDER_STATUS_FILLED;
                break;

            case INUSE:
                ordStatus = FixConstants.ORDER_STATUS_NEW;
                break;

            case EXPIRED:
                ordStatus = FixConstants.ORDER_STATUS_EXPIRED;
                break;
            default:
                ordStatus = FixConstants.ORDER_STATUS_NEW;
                break;
        }
        return ordStatus;
    }

    public OrdStatus getOrdStatus( SingleLegOrder orderRequest )
    {
        OrdStatus ordStatus = null;
        State state = orderRequest.getState();
        if ( state.getName() != null )
        {
            switch ( state.getName() )
            {
                case RSINIT:
                    if ( orderRequest.isExecutionSuspended() || orderRequest.isOrderMigrationInProgress())
                    {
                        ordStatus = FixConstants.ORDER_STATUS_STOPPED;
                    }
                    else
                    {
                        ordStatus = FixConstants.ORDER_STATUS_NEW;
                    }
                    break;
                case RSDECLINED:
                    ordStatus = FixConstants.ORDER_STATUS_REJECTED;
                    break;
                case RSEXPIRED:
                    com.integral.model.dealing.TimeInForce tif = orderRequest.getTimeInForce();
                    if(tif == null)
                    {
                        ordStatus = FixConstants.ORDER_STATUS_EXPIRED;
                    }
                    else
                    {
                        switch ( tif )
                        {
                            case IOC:
                            case FOK:
                                ordStatus = FixConstants.ORDER_STATUS_CANCELED;
                                break;
                            default:
                                ordStatus = FixConstants.ORDER_STATUS_EXPIRED;
                        }
                    }
                    break;
                case RSCANCELLED:
                    ordStatus = FixConstants.ORDER_STATUS_CANCELED;
                    break;
                case RSEXECUTED:
                    ordStatus = FixConstants.ORDER_STATUS_FILLED;
                    break;
                case RSPARTIAL:
                    ordStatus = FixConstants.ORDER_STATUS_PARTIALLY_FILLED;
                    break;
                case RSPRERATEPARTIAL:
                    ordStatus = FixConstants.ORDER_STATUS_PARTIALLY_FILLED;
                    break;
                case RSPRERATECANCELLED:
                    ordStatus = FixConstants.ORDER_STATUS_CANCELED;
                    break;
                case RSPRERATEEXPIRED:
                    ordStatus = FixConstants.ORDER_STATUS_EXPIRED;
                    break;
                case RSPRERATECOMPLETE:
                    ordStatus = FixConstants.ORDER_STATUS_FILLED;
                    break;
            }
        }
        if ( ordStatus == null )
        {
            ordStatus = FixConstants.ORDER_STATUS_NEW;
        }
        return ordStatus;
    }

    public ExecType getExecType( SingleLegOrder orderRequest )
    {
        ExecType execType = null;
        State state = orderRequest.getState();
        if ( state.getName() != null )
        {
            switch ( state.getName() )
            {
                case RSINIT:
                    execType = FixConstants.EXEC_TYPE_NEW;
                    break;
                case RSDECLINED:
                    execType = FixConstants.EXEC_TYPE_REJECTED;
                    break;
                case RSEXPIRED:
                    com.integral.model.dealing.TimeInForce tif = orderRequest.getTimeInForce();

                    if ( tif != null )
                    {
                        switch ( tif )
                        {
                            case IOC:
                            case FOK:
                                execType = FixConstants.EXEC_TYPE_CANCELED;
                                break;
                            default:
                                execType = FixConstants.EXEC_TYPE_EXPIRED;
                        }
                    }
                    break;
                case RSCANCELLED:
                    execType = FixConstants.EXEC_TYPE_CANCELED;
                    break;
                case RSEXECUTED:
                    execType = FixConstants.EXEC_TYPE_FILL;
                    break;
                case RSPARTIAL:
                    execType = FixConstants.EXEC_TYPE_PARTIAL;
                    break;
                default:
                    execType = FixConstants.EXEC_TYPE_NEW;
                    break;
            }
        }
        else
        {
            execType = FixConstants.EXEC_TYPE_NEW;
        }
        return execType;
    }

    public ExecType getExecType( Request request )
    {
        ExecType execType = null;
        RequestStateFacade stateFacade = ( RequestStateFacade ) request.getFacade( RequestStateFacade.REQUEST_STATE_FACADE );
        int timeInForce = request.getTimeInForce();
        if ( stateFacade != null )
        {
            if ( stateFacade.isInitial() )
            {
                execType = FixConstants.EXEC_TYPE_NEW;
            }
            else if ( stateFacade.isDeclined() )
            {
                execType = FixConstants.EXEC_TYPE_REJECTED;
            }
            else if ( stateFacade.isExpired() )
            {
                if ( timeInForce == 3 || timeInForce == 4 )
                {
                    execType = FixConstants.EXEC_TYPE_CANCELED;
                }
                else
                {
                    execType = FixConstants.EXEC_TYPE_EXPIRED;
                }
            }
            else if ( stateFacade.isCancelled() || stateFacade.isWithdrawn() )
            {
                execType = FixConstants.EXEC_TYPE_CANCELED;
            }
            else if ( stateFacade.isAcceptVerified() )
            {
                execType = FixConstants.EXEC_TYPE_FILL;
            }
            else if ( stateFacade.isPartial() )
            {
                execType = FixConstants.EXEC_TYPE_PARTIAL;
            }
            else
            {
                execType = FixConstants.EXEC_TYPE_NEW;
            }
        }
        else
        {
            execType = FixConstants.EXEC_TYPE_NEW;
        }
        return execType;
    }

    public CxlRejReason getCxlRejReason( String errorCode )
    {
        CxlRejReason reason = new CxlRejReason();
        if ( FixConstants.ORDER_CANCEL_UNKNOWN_ORDER.equals( errorCode ) )
        {
            reason.setValue( CxlRejReason.UNKNOWN_ORDER );
        }
        else if ( FixConstants.ORDER_CANCEL_TO_LATE_TO_CANCEL.equals( errorCode ) )
        {
            reason.setValue( CxlRejReason.TOO_LATE_TO_CANCEL );
        }
        else if ( FixConstants.ORDER_CANCEL_DUPLICATE_CLIENT_ORD_ID.equals( errorCode ) )
        {
            reason.setValue( CxlRejReason.DUPLICATE_CLORDID_RECEIVED );
        }
        else if ( FixConstants.ORDER_CANCEL_PENDING_CANCEL.equals( errorCode ) )
        {
            reason.setValue( CxlRejReason.ORDER_ALREADY_IN_PENDING_CANCEL_OR_PENDING_REPLACE_STATUS );
        }
        else
        {
            reason.setValue( CxlRejReason.OTHER );
        }
        return reason;
    }


    public QuoteRequestRejectReason getQuoteRequestRejectReason( String errorCode )
    {
        QuoteRequestRejectReason quoteRejectReason = new QuoteRequestRejectReason();

        if ( FixConstants.SUBSCRIPTION_CURRENCY_PAIR_NOT_SUPPORTED.equals( errorCode ) )
        {
            quoteRejectReason.setValue( QuoteRequestRejectReason.UNKNOWN_SYMBOL );
        }
        else if ( FixConstants.RFS_REJECT_EXCHANGE_CLOSED.equals( errorCode ) )
        {
            quoteRejectReason.setValue( QuoteRequestRejectReason.EXCHANGE_CLOSED );
        }
        else if ( FixConstants.RFS_REJECT_NOT_AUTHORIZED_TO_REQUEST_QUOTE.equals( errorCode ) )
        {
            quoteRejectReason.setValue( QuoteRequestRejectReason.NOT_AUTHORIZED_TO_REQUEST_QUOTE );
        }
        else
        {
            quoteRejectReason.setValue( QuoteRequestRejectReason.OTHER );
        }

        return quoteRejectReason;
    }

    /**
     * Borrowed from LoginAction class.
     *
     * @param jspApp
     * @param user
     * @return
     *
     */

    ConcurrentHashMap<String, JSPApplication> jspApps = new ConcurrentHashMap<String, JSPApplication>(1);

    public JSPApplication getJSPApplication( String jspApp, User user )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "FixUtilC: Finding JSPApplication: " + jspApp );
        }
        JSPApplication jspAppObj = jspApps.get(jspApp);

        if( jspAppObj != null ){
            return jspAppObj;
        }

        QueryService qs = null;
        try
        {
            qs = QueryFactory.getQueryService();
            jspAppObj = ( JSPApplication ) qs.find( com.integral.jsp.JSPApplication.class, jspApp );
            if(jspAppObj != null){
                if ( log.isDebugEnabled() )
                {
                    log.debug( "FixUtilC: Found jspApplication in namespace. JspApp is " + jspAppObj );
                }
                jspApps.put(jspApp, jspAppObj);
            }else{
                log.info( "FixUtilC: Not Found jspApplication in namespace. JspApp is " + jspAppObj );
            }
        }
        catch ( IdcNoSuchObjectException noSuchObj1 )
        {
            try
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "FixUtilC: Couldn't find object in user's namespace, Querying in namespacegroup...\n" + noSuchObj1 );
                }
                Namespace myNamespaceGroup = user.getNamespace().getNamespaceGroup();
                jspAppObj = ( JSPApplication ) qs.find( myNamespaceGroup, com.integral.jsp.JSPApplication.class, jspApp );
                if ( log.isDebugEnabled() )
                {
                    log.debug( "FixUtilC: Found jspApplication in namespaceGroup. JspApp is " + jspAppObj );
                }
            }
            catch ( IdcNoSuchObjectException noSuchObj2 )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "FixUtilC: Couldn't find object in user's namespaceGroup too, Querying in main namespace...\n" + noSuchObj2 );
                }
                try
                {
                    long mainNamespace = 1;
                    Namespace main = ( Namespace ) qs.find( com.integral.persistence.Namespace.class, mainNamespace );
                    jspAppObj = ( JSPApplication ) qs.find( main, com.integral.jsp.JSPApplication.class, jspApp );
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "FixUtilC: Found jspApplication in main namespace. JspApp is " + jspAppObj );
                    }
                }
                catch ( IdcNoSuchObjectException noSuchObj3 )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "FixUtilC: Couldn't find object in namespace main too...." + noSuchObj3 );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "FixUtilC: Exception occured when getting a jspApplication object ", e );
            }
        }
        return jspAppObj;
    }

    private ClientService clientService;

    public long getClOrdIdExpiryTime()
    {
        String schdulerTimeString = configMBean.getClOrdIdExpiryTime();
        long schdulerTime = 60 * 60 * 1000;// defualt is 1 hour.
        if ( schdulerTimeString != null )
        {
            try
            {
                SimpleDateFormat formatter = new SimpleDateFormat( "HH:mm:ss.SSS" );
                Date date = formatter.parse( schdulerTimeString );
                return date.getTime();
            }
            catch ( Exception e )
            {
                log.error( "FixUtilC.getSchedulerTime:Exception while getting time for ClOrdId scheduling.Hence returning default time 1 hour.", e );
                return 60 * 60 * 1000;
            }
        }
        return schdulerTime;
    }

    public long getSchedulerPeriod()
    {
        return configMBean.getSchedulerPeriod();
    }

    public Date getFromDate()
    {
        int orderStatusPeriod = configMBean.getMaxOrderMassStatusQueryPeriod();
        Date fromDate = new Date( System.currentTimeMillis() );
        Calendar cal = Calendar.getInstance();
        cal.setTime( fromDate );
        cal.add( Calendar.DATE, orderStatusPeriod * -1 );
        return cal.getTime();
    }

    public User getUser( Organization org )
    {
        User usr = getWarmupUser( org );
        if ( usr == null )
        {
            Collection<User> users = org.getUsers();
            Iterator<User> itr = users.iterator();
            usr = itr.next();
            if ( usr.hasRole( "MARKETMAKER" ) )
            {
                return usr;
            }
        }
        return usr;
    }

    private User getWarmupUser( Organization org )
    {
        User usr = ( User ) getNamedEntityByShortNameAndNamespace( org.getNamespace(), User.class, WarmUpTradeUtilC.WARMUP_WORKFLOW_USER_NAME );

        if (usr == null && RuntimeFactory.getServerRuntimeMBean().isTakerOrgDefaultUserCreationEnabled() )
        {
            String longName = "Warmup User For " + org.getShortName();
            usr = UserUtilC.createUser( WarmUpTradeUtilC.WARMUP_WORKFLOW_USER_NAME, longName, org, UserUtilC.MARKET_MAKER_ROLE_NAME );
        }

        return usr;
    }

    protected NamedEntity getNamedEntityByShortNameAndNamespace( Namespace ns, Class className, String shortName )
    {
        NamedEntity result = null;
        try
        {
            if ( shortName != null )
            {
                result = queryService.find( ns, className, shortName );
            }
        }
        catch ( IdcNoSuchObjectException e )
        {
            log.warn( "Entity of class " + className + "and shortName : " + shortName + " is not present in given NameSpace=" + ns );
        }
        catch ( Exception e )
        {
            log.error( "Error getting entity.", e );
        }
        return result;
    }

    public void loadAllTradingParties( String shortName )
    {
        IdcSessionContext oldctx = IdcSessionManager.getInstance().getSessionContext();
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( getDefaultUser() );
        IdcSessionManager.getInstance().setSessionContext( ctx );
        try
        {
            ExternalSystem extSys = getExternalSystem( shortName );
            QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
            com.integral.query.QueryCriteria criteria = buildr.get( "" );
            Expression exp = new ExpressionBuilder();
            Expression externalSysExp = exp.get( "externalSystem" ).equal( extSys );
            criteria.setExpression( externalSysExp );
            Collection<ExternalSystemId> extSysIds = getQueryService().findAll( CounterpartyExternalSystemIdC.class, criteria );
            log.warn( "FixUtilC.loadAllTradingParties:Queried ExtSysIDs of size:" + extSysIds.size() + " for shortName:" + shortName );
            for ( ExternalSystemId extSysId : extSysIds )
            {
                String extSysValue = extSysId.getSystemId();
                TradingParty tp = ( TradingParty ) extSysId.getOwner();
                String key = extSysValue + '.' + tp.getLegalEntity().getOrganization().getShortName();
                externalSystem_TradingParty.put( key, tp );
            }
        }
        catch ( Exception e )
        {
            log.error( "FixUtilC.loadAllTradingParties: exception while loading TradingParties:", e );
        }
        IdcSessionManager.getInstance().setSessionContext( oldctx );
    }

    public Executor getExecutor()
    {
        return executor;
    }

    public boolean isSubscriptionAlreadyExist( MarketDataRequest marketDataRequest, String providerName, SessionID sessionID, boolean isMultiProviderView )
    {
        String ccyPair = marketDataRequest.getCurrencyPairDetails().getDerivedCurrencyPair().getName();
        return ClientCache.getInstance().isDuplicateRequest( sessionID, ccyPair, providerName, isMultiProviderView );
    }

    public boolean isValidRequestForUnSubscription( String ccyPair, MarketDataRequest marketDataRequest )
    {
        String ccyPairinMessage = marketDataRequest.getCurrencyPairDetails().getDerivedCurrencyPair().getName();
        return ccyPairinMessage.equals( ccyPair );
    }

    public ThreadPoolExecutor newThreadPoolExecutor( SessionID sessionID, String org )
    {
        ThreadPoolExecutor executor = new ThreadPoolExecutor( configMBean.getThreadPoolInitialSize( org ), configMBean.getThreadPoolMaxSize( org ), configMBean.getThreadPoolKeepAliveTime( org ), TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>( configMBean.getThreadPoolWorkQueueSize( org ) ), new RateWorkerThreadFactory( sessionID.getTargetCompID() ) );

        executor.setRejectedExecutionHandler( new RateRejectionExecutionHandler() );
        return executor;
    }

    public ThreadPoolExecutor newUserMessageThreadPoolExecutor( SessionID sessionID, String org )
    {
        if ( configMBean.isUseSessionThreadPool() )
        {
            ThreadPoolExecutor executor = new ThreadPoolExecutor( configMBean.getThreadPoolInitialSize( org ), configMBean.getThreadPoolMaxSize( org ), configMBean.getThreadPoolKeepAliveTime( org ), TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>( configMBean.getThreadPoolWorkQueueSize( org ) ), new UserMessageWorkerThreadFactory( sessionID.getTargetCompID() ) );
            executor.prestartAllCoreThreads();
            executor.setRejectedExecutionHandler( new ThreadPoolExecutor.CallerRunsPolicy() );
            return executor;
        }
        else
        {
            return getSharedInMessageProcessorTPE();
        }
    }

    public ThreadPoolExecutor newUserMessageThreadPoolExecutorForServerSession( SessionID sessionID)
    {
        if ( configMBean.isUseSessionThreadPool() )
        {
            ThreadPoolExecutor executor = new ThreadPoolExecutor( 2, 8, 300000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>( 10), new UserMessageWorkerThreadFactory( sessionID.getTargetCompID() ) );
            executor.prestartAllCoreThreads();
            executor.setRejectedExecutionHandler( new ThreadPoolExecutor.CallerRunsPolicy() );
            return executor;
        }
        else
        {
            return getSharedInMessageProcessorTPE();
        }
    }

    public ThreadPoolExecutor getSharedInMessageProcessorTPE()
    {
        return inMessageProcessor;
    }

    public static Map<String, Organization> getExternalSystem_Organizations()
    {
        return Collections.unmodifiableMap( externalSystem_Organizations );
    }

    public static Map<String, ExternalSystem> getExternalSystems()
    {
        return Collections.unmodifiableMap( externalSystems );
    }

    public static Map<String, RequestClassification> getRequestClassifications()
    {
        return Collections.unmodifiableMap( requestClassifications );
    }

    public static Map<String, FXRateConvention> getRateConventions()
    {
        return Collections.unmodifiableMap( rateConventions );
    }

    public static Map<String, Organization> getOrganizations()
    {
        return Collections.unmodifiableMap( organizations );
    }

    public static Map<String, Counterparty> getCounterparties()
    {
        return Collections.unmodifiableMap( counterparties );
    }

    public static Map<String, TradingParty> getExternalSystem_TradingParty()
    {
        return Collections.unmodifiableMap( externalSystem_TradingParty );
    }

    public void initServices( String clientServiceName, String orderRequestServiceName, String tradeRequestServiceName, String primaryRequestServiceName )
    {
        try
        {
            createServices( clientServiceName, orderRequestServiceName, tradeRequestServiceName );

            if ( "OrderRequestService".equalsIgnoreCase( primaryRequestServiceName ) )
            {
                primaryRequestService = orderRequestService;
            }
            else
            {
                primaryRequestService = isRequestService;
            }

            StringBuilder buffer = new StringBuilder( 200 );
            buffer.append( "FIX Client: Services Loaded. [Name,Instance]" );
            buffer.append( '[' ).append( clientServiceName ).append( ',' ).append( clientService );
            buffer.append( "][" ).append( orderRequestServiceName ).append( ',' ).append( orderRequestService );
            buffer.append( "][" ).append( tradeRequestServiceName ).append( ',' ).append( isRequestService );
            buffer.append( "][" ).append( primaryRequestServiceName ).append( ',' ).append( primaryRequestService ).append( ']' );
            log.warn( buffer.toString() );
        }
        catch ( Exception e )
        {
            log.error( "FixUtilC.initServices - Failed to initilize.", e );
        }
    }

    private void createServices( String clientServiceName, String orderRequestServiceName, String tradeRequestServiceName ) throws ClassNotFoundException, InstantiationException, IllegalAccessException
    {
        if ( orderRequestServiceName != null && !orderRequestServiceName.equals( "" ) )
        {
            orderRequestService = com.integral.is.finance.dealing.ServiceFactory.getOrderRequestService();
        }

        if ( tradeRequestServiceName != null && !tradeRequestServiceName.equals( "" ) )
        {
            isRequestService = com.integral.is.finance.dealing.ServiceFactory.getISRequestService();
        }

        if ( clientServiceName != null && !clientServiceName.equals( "" ) )
        {
            clientService = new FXFIXClientService();
        }
    }

    public String getMaskedName( String fiName, String realLPName )
    {
        if ( realLPName == null )
        {
            return null;
        }
        Map<String, String> maskedNames = maskedLPNames.get( fiName );
        if ( maskedNames == null )
        {
            return realLPName;
        }
        String maskedName = maskedNames.get( realLPName );
        if ( maskedName == null || "".equals( maskedName ) )
        {
            return realLPName;
        }
        return maskedName;
    }

    public String getRealName( String fiName, String maskedLPName )
    {
        if ( maskedLPName == null )
        {
            return null;
        }
        Map<String, String> realNames = realLPNames.get( fiName );
        if ( realNames == null )
        {
            return maskedLPName;
        }
        String realName = realNames.get( maskedLPName );
        if ( realName == null || "".equals( realName ) )
        {
            return maskedLPName;
        }
        return realName;
    }

    public void reloadCaches( String action )
    {
        if ( action.equalsIgnoreCase( "ReloadRealMaskedLPMapping" ) )
        {
            log.warn( "FIXUtilC.reloadCaches - Reloading Real-Masked LP mapping" );
            loadMaskedProviderNameCache();
        }
        else if ( action.equalsIgnoreCase( "ReloadMarketDepths" ) )
        {
            log.warn( "FIXUtilC.reloadCaches - Reloading Allowed MarketDepths" );
            loadMarketDepths();
        }
    }

    public ConcurrentHashMap<String, ConcurrentHashMap<String, String>> getRealLPNames()
    {
        return realLPNames;
    }

    public ConcurrentHashMap<String, ConcurrentHashMap<String, String>> getMaskedLPNames()
    {
        return maskedLPNames;
    }

    public boolean isRFS( String quoteId )
    {
        RateIdPrefix prefix = RateIdPrefix.decode(quoteId);
        if( prefix != null ){
            switch (prefix){
                case MDS_FixedPeriod:
                case MDS_FixedPeriodAuto:
                    return false;
            }
        }
        //todo QuoteId of RFS quotes should have a defined pattern that can be used to differentiate ESP & RFS
        int idx = quoteId.indexOf( "BID" );
        if ( idx == -1 )
        {
            idx = quoteId.indexOf( "OFFER" );
        }
        return ( idx == -1 );
    }

    public Entity lookupEntity( String quoteId, String provider, boolean isRFS )
    {
        try
        {
            if ( isRFS )
            {
                return CacheFactory.getQuote( quoteId );
            }
            //not used in case of ESP.
        }
        catch ( Exception e )
        {
            log.error( "FixUtilC.lookupEntity - Failed", e );
        }
        return null;
    }

    public void setMessageCraker( SessionID sessionID, quickfix.fix42.MessageCracker handler )
    {
        handlers.put( sessionID, handler );
    }

    public quickfix.fix42.MessageCracker getMessageCracker( SessionID sessionID )
    {
        return handlers.get( sessionID instanceof ServerLevelSessionID ? ((ServerLevelSessionID) sessionID).getParent():sessionID );
    }

    public LegalEntity getLegalEntity( Organization customerOrg, Message message ) throws FieldNotFound
    {
        Message.Header header = message.getHeader();
        TradingParty tradingParty = null;
        String value = null;
        boolean isSetSenderSubID = header.isSetField( SenderSubID.FIELD ) ? ( value = header.getString( SenderSubID.FIELD ).trim() ).length() > 0 : false;
        if ( isSetSenderSubID )
        {
            tradingParty = fixUtil.getTradingPartyFromCache( FixConstants.SENDER_SUB_ID_EXTERNAL_SYSTEM, value, customerOrg.getShortName() );
        }
        boolean isSetOnBehalfOfSubID = header.isSetField( OnBehalfOfSubID.FIELD ) ? ( value = header.getString( OnBehalfOfSubID.FIELD ).trim() ).length() > 0 : false;
        if ( isSetOnBehalfOfSubID )
        {
            tradingParty = fixUtil.getTradingPartyFromCache( FixConstants.ON_BEHALF_OF_SUB_ID_EXTERNAL_SYSTEM, value, customerOrg.getShortName() );
        }
        if ( !isSetSenderSubID && !isSetOnBehalfOfSubID )
        {
            return ( LegalEntity ) customerOrg.getDefaultDealingEntity();
        }
        if ( tradingParty == null )
        {
            LegalEntity le = fixUtil.getCounterparty( value, customerOrg );
            if ( le != null )
            {
                return le;
            }
            //if here means one of SudID is set
            String extName = isSetOnBehalfOfSubID ? FixConstants.ON_BEHALF_OF_SUB_ID_EXTERNAL_SYSTEM : FixConstants.SENDER_SUB_ID_EXTERNAL_SYSTEM;
            //not in cache , check in DB and load in cache for future.
            tradingParty = fixUtil.getTradingParty( extName, value, customerOrg.getShortName() );
            if ( tradingParty != null )
            {
                return tradingParty.getLegalEntity();
            }
            else
            {
                return null;
            }
        }
        else
        {
            return tradingParty.getLegalEntity();
        }
    }

    public static WorkflowMessage getValidateOrderCancelRequestMessage( Message message, SessionID sessionID ) throws FieldNotFound
    {
        WorkflowMessage validateMessage = new WorkflowMessageC();
        validateMessage.setTopic( FixConstants.MSG_TOPIC_REQUEST );
        validateMessage.setEventName( FixConstants.MSG_EVT_VALIDATE );
        validateMessage.setSender( ClientCache.getInstance().getUser( sessionID ) );
        Organization customerOrg = fixUtil.getCustomerOrg( message, sessionID );
        LegalEntity customerLe = fixUtil.getLegalEntity( customerOrg, message );
        validateMessage.setParameterValue( OrderConstants.WF_Param_RequestType, OrderConstants.WF_RequestType_OrderCancel);
        if( message.isSetField(OrigClOrdID.FIELD)){
            validateMessage.setParameterValue( OrderConstants.WF_Param_OrigClOrdId, message.getString(OrigClOrdID.FIELD));
        }
        if( message.isSetField(OrderID.FIELD)){
            validateMessage.setParameterValue(OrderConstants.WF_Param_OrderId, message.getString(OrderID.FIELD));
        }
        validateMessage.setParameterValue( FixConstants.WF_PARAM_CUSTOMER_LE, customerLe );
        boolean serverToServerSession = FixUtilC.getInstance().isServer2ServerSession(sessionID);
        validateMessage.setParameterValue(FixConstants.WF_PARAM_ServerToServerSession,serverToServerSession);

        return validateMessage;
    }

    public static WorkflowMessage getValidateOrderCancelReplaceRequestMessage( OrderCancelReplaceRequest orderCancelReplaceRequest, SessionID sessionID ) throws FieldNotFound
    {
        boolean serverToServerSession = FixUtilC.getInstance().isServer2ServerSession(sessionID);
        WorkflowMessage validateMessage = new WorkflowMessageC();
        if( serverToServerSession ){
            /*
                Only amendment of few parameters is supported
             */
            Message message = orderCancelReplaceRequest.getMessage();
            validateMessage.setTopic(FixConstants.MSG_TOPIC_REQUEST);
            validateMessage.setEventName(FixConstants.MSG_EVT_VALIDATE);
            validateMessage.setSender(ClientCache.getInstance().getUser(sessionID));
            Organization customerOrg = fixUtil.getCustomerOrg( message, sessionID );
            LegalEntity customerLe = fixUtil.getLegalEntity( customerOrg, message );
            validateMessage.setParameterValue( FixConstants.WF_PARAM_CUSTOMER_LE, customerLe );
            validateMessage.setParameterValue(OrderConstants.WF_Param_RequestType, OrderConstants.WF_RequestType_ServerToServer_OrderAmend);
            if( message.isSetField(ClOrdID.FIELD)){
                validateMessage.setParameterValue( OrderConstants.WF_Param_ClOrdId, message.getString(ClOrdID.FIELD));
            }
            if( message.isSetField(OrigClOrdID.FIELD)){
                validateMessage.setParameterValue( OrderConstants.WF_Param_OrigClOrdId, message.getString(OrigClOrdID.FIELD));
            }
            if( message.isSetField(OrderID.FIELD)){
                validateMessage.setParameterValue(OrderConstants.WF_Param_OrderId, message.getString(OrderID.FIELD));
            }
            if( orderCancelReplaceRequest.getMessage().isSetField(OrderQty.FIELD)) {
                validateMessage.setParameterValue(FixConstants.WF_PARAM_NEW_ORDER_AMOUNT, orderCancelReplaceRequest.getOrderQty().getValue());
            }
        }
        else {
            Message message = orderCancelReplaceRequest.getMessage();
            validateMessage.setTopic(FixConstants.MSG_TOPIC_REQUEST);
            validateMessage.setEventName(FixConstants.MSG_EVT_VALIDATE);
            validateMessage.setSender(ClientCache.getInstance().getUser(sessionID));
            Organization customerOrg = fixUtil.getCustomerOrg(message, sessionID);
            LegalEntity customerLe = fixUtil.getLegalEntity(customerOrg, message);

            //New Additions
            CurrencyPairDetails cpd = orderCancelReplaceRequest.getCurrencyPairDetails();
            CurrencyPair currencyPair = cpd.getDerivedCurrencyPair();
            String ccyp = currencyPair.getName();
            String dealtccy = cpd.getDerivedDealtCurrency().getShortName();
            validateMessage.setParameterValue(OrderConstants.WF_Param_RequestType, OrderConstants.WF_RequestType_OrderCancelReplace);
            validateMessage.setParameterValue(OrderConstants.WF_Param_OrigClOrdId, message.getField(new OrigClOrdID()).getValue());
            validateMessage.setParameterValue(OrderConstants.WF_Param_ClOrdId, message.getField(new ClOrdID()).getValue());
            validateMessage.setParameterValue(FixConstants.WF_PARAM_CUSTOMER_LE, customerLe);
            validateMessage.setParameterValue(FixConstants.WF_PARAM_CURRENCY_PAIR, currencyPair);
            validateMessage.setParameterValue(FixConstants.WF_PARAM_NEW_ORDER_AMOUNT, orderCancelReplaceRequest.getOrderQty().getValue());
            validateMessage.setParameterValue(FixConstants.WF_PARAM_DEALING_IN_TERMCCY, !ccyp.startsWith(dealtccy));
        }
        return validateMessage;
    }


    public boolean isQuoteSessionLoggedIn( User user){
        return isQuoteSessionLoggedIn(user,false);
    }

    public boolean isQuoteSessionLoggedIn( User user, boolean isServerSession )
    {
        SessionID quoteSessionID = ClientCache.getInstance().getSession( user, FixConstants.QUOTE_SESSION,isServerSession );
        if ( quoteSessionID != null )
        {
            Session session = Session.lookupSession( quoteSessionID );
            if ( session != null && session.isLoggedOn() )
            {
                return true;
            }
        }
        return false;
    }

    public boolean isOrdersSessionLoggedIn( User user ){
        return  isOrdersSessionLoggedIn(user, false);
    }

    public boolean isOrdersSessionLoggedIn( User user, boolean isServerSession)
    {
        SessionID orderSessionID = ClientCache.getInstance().getSession( user, FixConstants.ORDER_SESSION, isServerSession );
        SessionID tradeSessionID = ClientCache.getInstance().getSession( user, FixConstants.TRADE_SESSION, isServerSession );
        if ( orderSessionID != null )
        {
            Session session = Session.lookupSession( orderSessionID );
            if ( session != null && session.isLoggedOn() )
            {
                return true;
            }
        }
        if ( tradeSessionID != null )
        {
            Session session = Session.lookupSession( tradeSessionID );
            if ( session != null && session.isLoggedOn() )
            {
                return true;
            }
        }
        return false;
    }

    public WorkflowMessage sendMessage( WorkflowMessage wfm )
    {
        try
        {
            return fixUtil.getClientService().execute( wfm );
        }
        catch ( Exception e )
        {
            log.error( "FixReceiver.fromApp unable to send message to client service ", e );
        }
        return null;
    }

    public WorkflowMessage getRFSQuoteWithdrawRequest( Request request, SessionID sessionID )
    {
        WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
        wfm.setStatus( MessageStatus.SUCCESS );
        wfm.setEvent( MessageEvent.WITHDRAW );
        wfm.setTopic( FixConstants.MSG_TOPIC_REQUEST );
        wfm.setSender( ClientCache.getInstance().getUser( sessionID ) );
        wfm.setParameterValue( FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_RFS );
        wfm.setObject( request );
        return wfm;
    }

    public WorkflowMessage getUnSubscriptionRequest( Request request, SessionID sessionID )
    {
        return createUnsubscriptionMessage( request, sessionID );
    }

    private WorkflowMessage createUnsubscriptionMessage( Request request, SessionID sessionID )
    {
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        User user = ClientCache.getInstance().getUser( sessionID );
        msg.setMessageId( 0 );
        msg.setSender( user );
        msg.setEvent( MessageEvent.CREATE );
        msg.setEvent( MessageEvent.WITHDRAW );
        msg.setTopic( FixConstants.MSG_TOPIC_REQUEST );
        msg.setObject( request );
        return msg;
    }

    public Request getSubscriptionRequest( MarketDataRequest marketDataRequest, SessionID sessionID )
    {
        try
        {
            return getSubscriptionRequest( marketDataRequest, marketDataRequest.getHeader().getString( DeliverToCompID.FIELD ), sessionID );
        }
        catch ( FieldNotFound e )
        {
            log.error( "FixUtilC.getSubscriptionRequest: exception while checking for subscriptionRequest:" + e.getMessage() );
            return null;
        }
    }

    public Request getSubscriptionRequest( MarketDataRequest marketDataRequest, String providerOrgName, SessionID sessionID )
    {
        try
        {
            com.integral.fix.client.message.grp.NoRelatedSym symbolGroup = marketDataRequest.createNoRelatedSym();
            marketDataRequest.getGroup( 1, symbolGroup );
            if ( !symbolGroup.isSetSymbol() )
            {
                return null;
            }
            String ccyPair = symbolGroup.getSymbol().getValue();

            return ClientCache.getInstance().getRequest( sessionID, ccyPair, providerOrgName );
        }
        catch ( FieldNotFound e )
        {
            log.error( "FixUtilC.getSubscriptionRequest: exception while checking for subscriptionRequest:" + e.getMessage() );
            return null;
        }
    }

    public TimeInForce getTimeInForce( final Request orderRequest )
    {
        int tif = orderRequest.getTimeInForce();
        switch ( tif )
        {
            case com.integral.finance.dealing.TimeInForce.IOC:
                return FixConstants.IOC;
            case com.integral.finance.dealing.TimeInForce.FOK:
                return FixConstants.FOK;
            case com.integral.finance.dealing.TimeInForce.GTC:
                return FixConstants.GTC;
            case com.integral.finance.dealing.TimeInForce.GTD:
                return FixConstants.GTD;
            case com.integral.finance.dealing.TimeInForce.GFS:
                return FixConstants.GFS;
            case com.integral.finance.dealing.TimeInForce.DAY:
                return FixConstants.DAY;
            default:
                break;
        }
        return null;
    }

    public TimeInForce getTimeInForce(SingleLegOrder orderRequest)
    {
        if ( com.integral.model.dealing.TimeInForce.GTC == orderRequest.getTimeInForce() )
        {
            return FixConstants.GTC;
        }
        else if ( com.integral.model.dealing.TimeInForce.GTD == orderRequest.getTimeInForce() )
        {
            return FixConstants.GTD;
        }
        else if ( com.integral.model.dealing.TimeInForce.GFS == orderRequest.getTimeInForce() )
        {
            return FixConstants.GFS;
        }
        else if ( com.integral.model.dealing.TimeInForce.FOK == orderRequest.getTimeInForce() )
        {
            return FixConstants.FOK;
        }
        else if ( com.integral.model.dealing.TimeInForce.DAY == orderRequest.getTimeInForce() )
        {
            return FixConstants.DAY;
        }
        else if ( com.integral.model.dealing.TimeInForce.IOC == orderRequest.getTimeInForce() )
        {
            return FixConstants.IOC;
        }
        return null;
    }

    public OrdType getOrdType( final Request orderRequest )
    {
        String requestClassification = orderRequest.getRequestClassification().getShortName();

        if ( FixConstants.LIMIT.equalsIgnoreCase( requestClassification ) )
        {
            return FixConstants.ORD_TYPE_LIMIT;
        }
        else if ( FixConstants.MARKET.equalsIgnoreCase( requestClassification ) )
        {
            return FixConstants.ORD_TYPE_MARKET;
        }
        else if ( FixConstants.STOP.equalsIgnoreCase( requestClassification ) )
        {
            return FixConstants.ORD_TYPE_STOP;
        }
        else if ( FixConstants.STOPLIMIT.equalsIgnoreCase( requestClassification ) )
        {
            return FixConstants.ORD_TYPE_STOP_LIMIT;
        }

        return null;
    }

    public OrdType getOrdType( final SingleLegOrder orderRequest )
    {
        OrderRequest.Type type = orderRequest.getType();
        if ( type != null )
        {
            switch ( type )
            {
                case LIMIT:
                    return FixConstants.ORD_TYPE_LIMIT;
                case MARKET:
                    return FixConstants.ORD_TYPE_MARKET;
                case STOP:
                    return FixConstants.ORD_TYPE_STOP;
                case STOPLIMIT:
                    return FixConstants.ORD_TYPE_STOP_LIMIT;
            }
        }
        return null;
    }

    /**
     * Updates the user on login for last login time.
     *
     * @param user user
     */
    public void updateUserOnLogin( User user )
    {
        if ( user == null )
        {
            return;
        }
        try
        {
            if ( LoginMBeanC.getInstance().isAuditEnabled( LoginChannel.FIX_API ) )
            {
                synchronized ( user )
                {
                    SecurityServiceC.getInstance().auditLoginSuccess( user, true );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "FixUtilC.updateUserLogin.ERROR : Error while updating the user=" + user );
        }
    }

    public int getVersion( SessionID sessionID )
    {
        if ( BEGINSTRING_FIX43.equals( sessionID.getBeginString() ) )
        {
            return 43;
        }
        else if ( BEGINSTRING_FIX42.equals( sessionID.getBeginString() ) )
        {
            return 42;
        }
        else if ( BEGINSTRING_FIX44.equals( sessionID.getBeginString() ) )
        {
            return 44;
        }

        return -1;
    }

    /**
     * Tuple.first = true if 128 equals ALL or LP1,LP2 or LP with aggregation type specified.
     *
     * @param mdr
     * @param deliverToCompID
     * @param user
     * @return
     */
    public Tuple<Boolean, Collection<Organization>> getProviderOrgsFromMarketDataRequest( MarketDataRequest mdr, String deliverToCompID, User user ) throws FieldNotFound
    {
        Tuple<Boolean, Collection<Organization>> tuple = new Tuple<Boolean, Collection<Organization>>();
        if ( "".equalsIgnoreCase( deliverToCompID ) )
        {
            tuple.first = false; // get all lps
            tuple.second = FixUtilC.getInstance().getRelatedLPs( user.getOrganization(), mdr, true );

        }
        else if ( "ALL".equals( deliverToCompID ) )
        {
            //get all lps
            tuple.first = true;
            tuple.second = FixUtilC.getInstance().getRelatedLPs( user.getOrganization(), mdr, false );
        }
        else if ("ME".equals( deliverToCompID ))
        {
            tuple.first = false;
            tuple.second = FixUtilC.getInstance().getRelatedME( user.getOrganization(), mdr );
        }
        else
        {
            boolean isAggregationTypeDefined = mdr.isSetField( FixConstants.FIX_FIELD_AGGREGATION_TYPE );
            tuple.first = isAggregationTypeDefined || deliverToCompID.contains( "," );
            List<String> deliverToCompIDList = Arrays.asList( deliverToCompID.split( "," ) );
            tuple.second = new ArrayList<Organization>( deliverToCompIDList.size() );
            Collection<Organization> relatedLPs = FixUtilC.getInstance().getRelatedLPs( user.getOrganization(), mdr, false );
            log.info ( "FixUtilC.getProviderOrgsFromMarketDataRequest: relatedLPs=" + relatedLPs + ",isAggregationTypeDefined=" + isAggregationTypeDefined + ",deliverToCompID=" + deliverToCompID );
            for ( String providerOrg : deliverToCompIDList )
            {
                Organization org = getOrganization( providerOrg );
                if ( org == null )
                {
                    continue;
                }
                //Temp Fix for bug - 54885
                if ( ( mdr.getSubscriptionRequestType().getValue() == SubscriptionRequestType.DISABLE_PREVIOUS_SNAPSHOT_PLUS_UPDATE_REQUEST )
                        && isMbean.isLiquidityProvisioningEnabled( user.getOrganization().getShortName() ) )
                {
                    tuple.second.add( org );
                }
                else
                {
                    if ( isContains( relatedLPs, org ) || org.getIndex() == user.getOrganization().getIndex() )
                    {
                        tuple.second.add( org );
                    }
                }
            }
        }
        log.info ( "FixUtilC.getProviderOrgsFromMarketDataRequest: tuple.first=" + tuple.first + ",tuple.second="
                + tuple.second + ",deliverToCompID=" + deliverToCompID + ",singleLpClient=" + mdr.isSingleLPFromClient() );
        return tuple;
    }

    protected boolean isContains( Collection<Organization> relatedOrgs, Organization requestorOrg )
    {
        for ( Organization org : relatedOrgs )
        {
            if ( requestorOrg.getObjectId() == org.getObjectId() && requestorOrg.getShortName().equalsIgnoreCase( org.getShortName() ) )
            {
                return true;
            }
        }
        return false;
    }

    public List<Double> getTiers( String strTiers )
    {
        List<Double> tiers = null;
        try
        {
            if ( strTiers != null && !strTiers.equals( "" ) )
            {
                String _tiers[] = strTiers.split( "," );

                for ( String tier : _tiers )
                {
                    if ( tiers == null )
                    {
                        tiers = new ArrayList<Double>();
                    }
                    double tierSize = convertToDouble( tier );
                    tiers.add( tierSize );
                }
            }
        }
        catch ( Exception exc )
        {
            log.error( "FixUtilC.getTiers.ERROR : Error while parsing the tiers." );
        }

        return tiers;
    }

    public double convertToDouble( String tier )
    {
        double tierSize;

        try
        {
            tierSize = Double.parseDouble( tier );
        }
        catch ( NumberFormatException exc )
        {
            String amountPostFix = tier.substring( tier.length() - 1 );
            String amount = tier.substring( 0, tier.length() - 1 );

            NumberRepresentation numRef = getNumberRepresentation( amountPostFix );
            tierSize = Double.parseDouble( amount ) * numRef.getValue();
        }

        return tierSize;
    }

    private NumberRepresentation getNumberRepresentation( String numberRep )
    {
        if ( numberRep.equalsIgnoreCase( NumberRepresentation.Thousand.getName() ) )
        {
            return NumberRepresentation.Thousand;
        }
        else if ( numberRep.equalsIgnoreCase( NumberRepresentation.Million.getName() ) )
        {
            return NumberRepresentation.Million;
        }
        else if ( numberRep.equalsIgnoreCase( NumberRepresentation.Billion.getName() ) )
        {
            return NumberRepresentation.Billion;
        }
        else if ( numberRep.equalsIgnoreCase( NumberRepresentation.Trillion.getName() ) )
        {
            return NumberRepresentation.Trillion;
        }
        else
        {
            return null;
        }
    }

    static int[] headerFields = {SenderSubID.FIELD, OnBehalfOfCompID.FIELD, OnBehalfOfSubID.FIELD, DeliverToCompID.FIELD};

    public static boolean isAmendOrder( OrderCancelReplaceRequest ocr, Request orderRequest, SessionID sessionID ) throws FieldNotFound
    {
        NewOrderSingle newOrderSingle = ClientCache.getInstance().getRequest( sessionID, ocr.getOrigClOrdID().getValue() );
        if ( newOrderSingle != null )
        {
            return isAmendOrder( ocr, newOrderSingle );
        }
        else
        {
            FXLegDealingPrice dp = ( FXLegDealingPrice ) orderRequest.getRequestPrice( ISCommonConstants.SINGLE_LEG );
            int boMode = dp.getBidOfferMode();
            FXPrice prc = ( FXPrice ) dp.getPriceElement().getPrice();
            FXRate rate = ( DealingPrice.BID == boMode ? ( FXRate ) prc.getBid() : ( FXRate ) prc.getOffer() );
            //neither amount nor rate are modified, return false.
            boolean amountChanged = ocr.getOrderQty().getValue() != dp.getDealtAmount();
            boolean priceChanged = ocr.isSetPrice() && ocr.getPrice().getValue() != rate.getRate();
            return amountChanged || priceChanged;
        }
    }

    public static boolean isAmendOrder( OrderCancelReplaceRequest ocr, SingleLegOrder orderRequest, SessionID sessionID ) throws FieldNotFound
    {
        NewOrderSingle newOrderSingle = ClientCache.getInstance().getRequest( sessionID, ocr.getOrigClOrdID().getValue() );
        if ( newOrderSingle != null )
        {
            return isAmendOrder( ocr, newOrderSingle );
        }
        else
        {
            if(!orderRequest.isPQOrder()){
                if(ocr.isSetTimeInForce() &&  ocr.getTimeInForce().getValue()!= orderRequest.getTimeInForce().getValue()){
                    return false;
                }
            }
            if(ocr.isSetStopPx() && orderRequest.getOrderTrigger()!=null && ocr.getStopPx().getValue()!=orderRequest.getOrderTrigger().getTriggerRate()){
                return false;
            }
            return ocr.getOrderQty().getValue() != orderRequest.getOrderAmount() || ocr.isSetPrice() && ocr.getPrice().getValue() != orderRequest.getOrderSpotRate();
        }
    }

    private static boolean isAmendOrder( OrderCancelReplaceRequest ocr, NewOrderSingle newOrderSingle ) throws FieldNotFound {
        boolean isPriceChanged = isPriceChanged( ocr, newOrderSingle );
        boolean isAmountChanged = ( ocr.getOrderQty().getValue() != newOrderSingle.getOrderQty().getValue() );
        if ( !isAmountChanged && !isPriceChanged )
        {
            //Amount and price hasn't changed.
            return false;
        }

        //Compare header fields
        Header nosHeader = newOrderSingle.getHeader();
        Header ocrrHeader = ocr.getHeader();

        for ( int i = 0; i < headerFields.length; i++ )
        {
            int fieldNum = headerFields[i];
            boolean isSetInNOS = nosHeader.isSetField( fieldNum );
            boolean isSetInOCRR = ocrrHeader.isSetField( fieldNum );

            if ( ( isSetInNOS && !isSetInOCRR ) || ( !isSetInNOS && isSetInOCRR ) )//not set in both of them.
            {
                log.warn( "Not set in both messages. f: " + fieldNum + " ocrr: " + isSetInOCRR + " nos: " + isSetInNOS );
                return false;
            }
            else if ( isSetInNOS && isSetInOCRR ) //if set in both compare them
            {
                String val1 = nosHeader.getString( fieldNum );
                String val2 = ocrrHeader.getString( fieldNum );
                if ( !val1.equalsIgnoreCase( val2 ) )
                {
                    log.warn( "changed f: " + fieldNum + " ocrr: " + val2 + " nos: " + val1 );
                    return false;
                }
            }
        }

        Iterator<Field<?>> fields = ocr.getMessage().iterator();

        while ( fields.hasNext() )
        {
            Field<?> field = fields.next();

            int fieldNum = field.getTag();

            if ( fieldNum == OrderQty.FIELD || fieldNum == OrigClOrdID.FIELD || fieldNum == ClOrdID.FIELD || fieldNum == TransactTime.FIELD || fieldNum == Product.FIELD || fieldNum == NoPartyIDs.FIELD || fieldNum == PartyID.FIELD || fieldNum == PartyIDSource.FIELD || fieldNum == PartyRole.FIELD || fieldNum == Price.FIELD )
            {
                continue;
            }
            if( newOrderSingle.getOrdType().getValue() == OrdType.PREVIOUSLY_INDICATED ){
                if( fieldNum == OrderID.FIELD ) {
                    continue;
                }
            }

            if ( newOrderSingle.isSetField( fieldNum ) )
            {
                //Encounter field which is not same
                if ( !field.getObject().equals( newOrderSingle.getMessage().getString( fieldNum ) ) )
                {
                    log.warn( "changed f: " + fieldNum + " ocrr: " + field.getObject() + " nos: " + newOrderSingle.getMessage().getString( fieldNum ) );
                    return false;
                }
                //else continue;
            }
            else
            {
                log.warn( "extra f: " + fieldNum + " ocrr: " + field.getObject() + " not present in nos " );
                return false;
            }
        }

        return ( ocr.isSetPrice() == newOrderSingle.isSetPrice() ) && ( ocr.isSetField( OrderQty.FIELD ) == newOrderSingle.isSetField( OrderQty.FIELD ) );
    }

    private static boolean isPriceChanged( OrderCancelReplaceRequest ocr, NewOrderSingle nos ) throws FieldNotFound
    {
        if ( ( !ocr.isSetPrice() && !nos.isSetPrice() ) )
        {
            return false;
        }
        if ( ocr.isSetPrice() && nos.isSetPrice() )
        {
            if ( ocr.getPrice().getValue() == nos.getPrice().getValue() )
            {
                return false;
            }
        }
        return true;
    }

    public boolean isValidAggregationType( String type )
    {
        return aggregation_types.contains( type );
    }

    public static boolean isBatchQuoteRequest(QuoteRequest quoteReq)
    {
        try {
            if(quoteReq.isSetNoRelatedSym()){
                quickfix.field.NoRelatedSym noRelatedSym = quoteReq.getNoRelatedSym();
                // Verify for multiple symbols, if present.
                for(int counter = 1; counter <= noRelatedSym.getValue(); counter++){
                    NoRelatedSym noRS = quoteReq.createNoRelatedSym();
                    quoteReq.getGroup(counter, noRS);
                    // To avoid throwing an error
                    if(noRS.isSetField(NoLegs.FIELD) && null != noRS.getNoLegs() && noRS.getNoLegs().getValue() >= 1){
                        return true;
                    }
                }
            }
        } catch (FieldNotFound e) {
            log.warn( "FIXUtilC.isBatchQuoteRequest - NoRelatedSym group not found for in batch QuoteRequest - " + e.getMessage());
        }
        return false;
    }

    public void setSessionContext( User user )
    {
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( user );
        IdcSessionManager.getInstance().setSessionContext( ctx );
        setSessionContextAttribute( ctx );
    }

    public void setSessionContextAttribute( IdcSessionContext ctx )
    {
        ctx.setAttribute( FixConstants.COMPACTION_TYPE, FixConstants.COMPACTION_TYPE_FXI );
        ctx.setLoginChannel( LoginChannel.FIX_API );
    }

    public void setSessionContextOnLogon( User user, String clientVersion )
    {
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( user );
        IdcSessionManager.getInstance().setSessionContext( ctx );
        setSessionContextAttribute( ctx );
        ctx.setAttribute( FixConstants.ATTR_ISCLIENTSESSION, Boolean.TRUE );
        ctx.setAttribute(ISCommonConstants.CTX_ATTR_CLIENT_VERSION, clientVersion);
    }


    public Translator getTranslator( SessionID sessionID )
    {
        if ( BEGINSTRING_FIX43.equals( sessionID.getBeginString() ) )
        {
            return getFixConfiguration().getTranslator43();
        }
        else if ( BEGINSTRING_FIX42.equals( sessionID.getBeginString() ) )
        {
            return getFixConfiguration().getTranslator42();
        }
        else if ( BEGINSTRING_FIX44.equals( sessionID.getBeginString() ) )
        {
            return getFixConfiguration().getTranslator44();
        }

        return null;
    }

    public boolean isHandlInstManualOrder(NewOrderSingle newOrderSingle)
    {
        try
        {
            return newOrderSingle.isSetHandlInst() && newOrderSingle.getHandlInst().getValue() == HandlInst.MANUAL_ORDER;
        }
        catch ( FieldNotFound fieldNotFound )
        {
            return false;
        }
    }


    public void processOrderStatusRequestForRFS( OrderStatusRequest orderStatusRequest, SessionID sessionID, String clientOrderId, User user ) throws FieldNotFound
    {
        Deal deal = SpacesFixClientQueryService.getDealByExternalID( clientOrderId,user );
        if (deal != null)
        {
            String requestId = orderStatusRequest.isSetOrderID() ? orderStatusRequest.getOrderID().getValue() : orderStatusRequest.getClOrdID().getValue();
            configuration.getApplicationHandler().sendOrderStatus(requestId, 790 ,deal,sessionID);
        }
        else
        {
            log.info("FUC.processOrderStatusRequest:OrderStatusRequest for clientOrderId:" + clientOrderId + " has been rejected for Reason UnknownOrder. Cant find in database. ");
            configuration.getApplicationHandler().sendTradeReject(orderStatusRequest, sessionID, FixConstants.TRADE_RECOVERY_UNKNOWN_ORDER);
            return;
        }
    }

    public boolean processOrderStatusRequestForRFS_V2( OrderStatusRequest orderStatusRequest, SessionID sessionID, String clientOrderId, User user ) throws FieldNotFound
    {
        Deal deal = SpacesFixClientQueryService.getDealByExternalID( clientOrderId,user );
        if (deal != null)
        {
            String requestId = orderStatusRequest.isSetOrderID() ? orderStatusRequest.getOrderID().getValue() : orderStatusRequest.getClOrdID().getValue();
            configuration.getApplicationHandler().sendOrderStatus(requestId, 790 ,deal,sessionID);
            return true;
        }

        return false;
    }


    public void processOrderMassStatusRequestForRFS( OrderMassStatusRequest orderStatusRequest, SessionID sessionID, List<Deal> deals, User user ) throws FieldNotFound
    {
        for(Deal deal : deals)
        {
            if( ISCommonConstants.REQ_CLSF_RFQ.equals( deal.getDealRequest().getRequestClassification().getShortName() ) )
            {
                DealStateFacade dealFacade = ( DealStateFacade ) deal.getFacade( DealStateFacade.FACADE_NAME );
                if (!dealFacade.isCancelled() && !dealFacade.isRejected()) {
                    String requestId = orderStatusRequest.getMassStatusReqID().getValue();
                    configuration.getApplicationHandler().sendOrderStatus(requestId,584,deal,sessionID);
                }
            }
            else
            {
                if(log.isDebugEnabled())
                {
                    log.debug("Deal is non-rfq deal.Deal="+deal);
                }
            }
        }
        //else
        //{
        //log.info("FUC.processOrderStatusRequest:OrderStatusRequest for clientOrderId:" + clientOrderId + " has been rejected for Reason UnknownOrder. Cant find in database. ");
        //configuration.getApplicationHandler().sendTradeReject(orderStatusRequest, sessionID, FixConstants.TRADE_RECOVERY_UNKNOWN_ORDER);
        //return;
        //}
    }

    public IdcUserSession getUserSession( quickfix.Session session )
    {
        return new FIXUserSessionC( session );
    }

    public boolean isREXDefaultOrder( SessionID sessionID ){
        User user = ClientCache.getInstance().getUser( sessionID );
        return user != null && user.hasPermission( ISConstantsC.REX_DEFAULT_VALUES_PERM );
    }

    public boolean isServer2ServerSession( SessionID sessionID){
        /*
            TargetCompID is in the format : server.order.MAIN.<TARGET_VS>.<SOURCE_VS>
            TARGET_VS : Virtual Server that initiated this connection (UIG etc)
            SOURCE_VS : Virtual Server of the current server
            e.g. server.order.MAIN.UIG1.ppfxiadp75-OAWl4
         */
        return  sessionID != null && sessionID.getTargetCompID() != null && sessionID.getTargetCompID().startsWith(SERVER_SESSION_PREFIX);
    }

    public boolean isDirectedOrder(com.integral.fix.client.message.Message fixMessage, SessionID sessionID) {
        if (isREXDefaultOrder(sessionID)) {
            return true;
        }
        try {
            User user = ClientCache.getInstance().getUser(sessionID);
            if(fixMessage.getString( OrdType.FIELD ).equals( String.valueOf( OrdType.PREVIOUSLY_INDICATED ) )){
                if(fixMessage.isSetField( FixConstants.TARGET_STRATEGY_FIELD )) {
                    if ( fixMessage.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_MID ||
                            fixMessage.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_LIT_SWITCH ||
                            fixMessage.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_FIXING ) {
                        return true;
                    }
                }
                else if( fixMessage.isSetField(FixConstants.EXTERNAL_ALGO_NAME)){
                    if( fixMessage.isSetField(FixConstants.EXTERNAL_ALGO_PARAMETERS)){
                        if( fixMessage.isSetField(ExDestination.FIELD)){
                            return true;
                        }
                    }
                }
            }
            else if (fixMessage.isSetField(ExecInst.FIELD)) {
                String[] execInsts = fixMessage.getString(ExecInst.FIELD).split(" ");
                for (String execInst : execInsts) {
                    if (" ".equalsIgnoreCase(execInst)) {
                        continue;
                    }
                    if (ISCommonConstants.EXECINST_TRADING_VENUE.equalsIgnoreCase(execInst) ||
                            ISCommonConstants.EXECINST_MID_POINT_MATCH.equalsIgnoreCase(execInst)) {
                        return true;
                    }
                }
            }
            else if (user != null && isMbean.isClobRoutingEnabled(user.getOrganization().getShortName())) {
                return true;
            }
        } catch (Exception e) {
            log.error("isDirectedOrder : Exception while validating :  Message->  " + fixMessage, e);
        }
        return false;
    }

    public boolean isClobDirectedOrder(SessionID sessionID) {
        try {
            User user = ClientCache.getInstance().getUser(sessionID);
            if (user != null && isMbean.isClobRoutingEnabled(user.getOrganization().getShortName())) {
                return true;
            }
        } catch (Exception e) {
            log.error("isClobDirectedOrder : Exception while validating message : ", e);
        }
        return false;
    }


    public boolean isRiskNetDirectedOrder(com.integral.fix.client.message.Message fixMessage, SessionID sessionID) {
        if (isREXDefaultOrder(sessionID)) {
            return true;
        }
        try {
            if(fixMessage.getString( OrdType.FIELD ).equals( String.valueOf(OrdType.PREVIOUSLY_INDICATED) )){
                if(fixMessage.isSetField( FixConstants.TARGET_STRATEGY_FIELD )) {
                    if ( fixMessage.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_MID ||
                            fixMessage.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_FIXING ) {
                        return true;
                    }
                }
            }
            else if (fixMessage.isSetField(ExecInst.FIELD)) {
                String[] execInsts = fixMessage.getString(ExecInst.FIELD).split(" ");
                for (String execInst : execInsts) {
                    if (" ".equalsIgnoreCase(execInst)) {
                        continue;
                    }
                    if (ISCommonConstants.EXECINST_TRADING_VENUE.equalsIgnoreCase(execInst) ||
                            ISCommonConstants.EXECINST_MID_POINT_MATCH.equalsIgnoreCase(execInst)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("isRiskNetDirectedOrder : Exception while validating :  Message->  " + fixMessage, e);
        }
        return false;
    }

    public boolean isRiskNetDirectedOrder_NewAlgo(NewOrderSingle newOrderSingle) {
        try {
            if(newOrderSingle.getString( OrdType.FIELD ).equals( String.valueOf(OrdType.PREVIOUSLY_INDICATED) )){
                if(newOrderSingle.isSetField( FixConstants.TARGET_STRATEGY_FIELD )) {
                    if ( newOrderSingle.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_MID ||
                            newOrderSingle.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_FIXING ) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("isRiskNetDirectedOrder_NewAlgo : Exception while validating :  Message->  " + newOrderSingle, e);
        }
        return false;
    }

    public boolean isRiskNetDirectedOrder_NewAlgo_MidBook(NewOrderSingle newOrderSingle) {
        try {
            if(newOrderSingle.getString( OrdType.FIELD ).equals( String.valueOf(OrdType.PREVIOUSLY_INDICATED) )){
                if(newOrderSingle.isSetField( FixConstants.TARGET_STRATEGY_FIELD )) {
                    if ( newOrderSingle.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_MID ) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("isRiskNetDirectedOrder_NewAlgo_MidBook : Exception while validating :  Message->  " + newOrderSingle, e);
        }
        return false;
    }

    public boolean isRiskNetDirectedOrder_NewAlgo_Fixing(NewOrderSingle newOrderSingle) {
        try {
            if(newOrderSingle.getString( OrdType.FIELD ).equals( String.valueOf(OrdType.PREVIOUSLY_INDICATED) )){
                if(newOrderSingle.isSetField( FixConstants.TARGET_STRATEGY_FIELD )) {
                    if ( newOrderSingle.getInt( FixConstants.TARGET_STRATEGY_FIELD ) == ISCommonConstants.TARGET_STRATEGY_FIELD_VAL_FIXING ) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("isRiskNetDirectedOrder_NewAlgo_Fixing : Exception while validating :  Message->  " + newOrderSingle, e);
        }
        return false;
    }

    public boolean isRiskNetDirectedOrder_LPCross_NewAlgo(NewOrderSingle newOrderSingle) {
        try {
            if(newOrderSingle.isSetField( FixConstants.ACTION_ON_EXPIRY_FIELD ) && newOrderSingle.isSetTimeInForce() && newOrderSingle.getTimeInForce().getValue() == TimeInForce.GOOD_TILL_DATE ){
                if(newOrderSingle.getInt( FixConstants.ACTION_ON_EXPIRY_FIELD ) == ISCommonConstants.ACTION_ON_EXPIRY_FIELD_LP_CROSS) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("isRiskNetDirectedOrder_LPCross_NewAlgo : Exception while validating :  Message->  " + newOrderSingle, e);
        }
        return false;
    }

    public String getFixingName( int fixingSource ){
        return ISUtilImpl.getInstance().getFixingName( fixingSource );
    }

    public boolean isLPCrossOrder( NewOrderSingle newOrderSingle ){
        boolean isLPCross = false;
        try
        {
            if ( newOrderSingle.isSetExecInst() )
            {
                String[] execInsts = newOrderSingle.getExecInst().getValue().split( " " );
                for ( String execInst : execInsts )
                {
                    if ( " ".equalsIgnoreCase( execInst ) )
                    {
                        continue;
                    }
                    if(  ExecInst.OK_TO_CROSS == execInst.charAt(0) ){
                        isLPCross = true;
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error("isLPCrossOrder : Exception while validating :  Message->  " + newOrderSingle, e);
        }
        return isLPCross;
    }

    /*
     * 0. isRegisterLogin
     * 1. isUpdateLoginInDB
     */
    public Tuple<Boolean, Boolean> isRegisterLogin(String senderCompID, User loggedInUser)
    {
        ServerRuntimeMBean runtime = RuntimeFactory.getServerRuntimeMBean();
        VirtualServer vs = runtime.getVirtualServer();

        if ( (senderCompID.startsWith(FixConstants.QUOTE_SESSION_PREFIX) ||
                senderCompID.startsWith(FixConstants.SERVER_QUOTE_SESSION_PREFIX)) &&
                ClientCache.getInstance().getSession(loggedInUser, FixConstants.RFS_SESSION) == null)
        {
            //quote session on non-ocx servers
            //quote session on mdf servers
            return new Tuple<Boolean, Boolean>(Boolean.TRUE, Boolean.TRUE);
        }
        else if (senderCompID.startsWith(FixConstants.RFS_SESSION_PREFIX) &&
                ClientCache.getInstance().getSession(loggedInUser, FixConstants.QUOTE_SESSION) == null)
        {
            return new Tuple<Boolean, Boolean>(Boolean.TRUE, Boolean.TRUE);
        }
        else if( runtime.isOCXDeploymentEnabled() )
        {
            if( VirtualServerType.MarketDataFeedServer.equals(vs.getVirtualServerType().getShortName()) )
            {
                //if ( senderCompID.startsWith(FixConstants.ORDER_SESSION_PREFIX) || senderCompID.startsWith(FixConstants.TRADE_SESSION_PREFIX) )
                {
                    //ideally order and trade session should not come to MDF server. if comes then treat them as if they are coming on OA server.
                    return new Tuple<Boolean, Boolean>(Boolean.FALSE, Boolean.FALSE); // order/trade session on mdf servers
                }
            }
            else
            {
                if( configMBean.isRegisterOrderSessionOnOCXEMS() )
                {
                    return new Tuple<Boolean, Boolean>(Boolean.TRUE, Boolean.FALSE); // order/trade session on ems servers
                }
            }
        }
        return new Tuple<Boolean, Boolean>(Boolean.FALSE, Boolean.FALSE); // order/trade session on non-ocx servers
    }

    public static String getProcessedAccount( User user, String account )
    {
        Map<String, String> managerCAccountMapping = NettingUtils.getInstance().getManagerCAccountMapping(user.getOrganization());

        String managerCAccount = managerCAccountMapping.get(account);
        if( managerCAccount != null)
        {
            account = managerCAccount;
        }

        if(!NettingMBeanC.getInstance().isStagingAreaAccountCheckCaseSensitive(user.getOrganization()))
        {
            account = account.toUpperCase();
        }
        return account;
    }

    public static List<AllocationDetail> getAllocationDetailsForStagingOrder( final Message message, User user)
    {
        List<AllocationDetail> allocList = new ArrayList<AllocationDetail>();
        try
        {
            // check if allocation group is present . if not then check for Account tag<1>
            if (message.isSetField(NoAllocs.FIELD))
            {
                List<Group> allocGroups = message.getGroups(NoAllocs.FIELD);
                if (allocGroups != null)
                {
                    for (Group allocGroup : allocGroups)
                    {
                        String allocAcoount = allocGroup.getString(AllocAccount.FIELD);
                        String allocQty = allocGroup.getString(AllocQty.FIELD);
                        allocList.add(new AllocationDetail(
                                allocAcoount,
                                Double.valueOf( allocQty )));
                    }
                }
            }
            else
            {
                String account = getTag1Account( message, null );
                if(account == null)
                {
                    return null;
                }

                String orderQty = message.getString(OrderQty.FIELD);
                allocList.add(new AllocationDetail(
                        account,
                        Double.valueOf(orderQty)));
            }
        }
        catch(Exception e)
        {
            return null;
        }
        return allocList;
    }

    public static String getTag1Account( Message message, AllocationDetail allocationDetail)
    {
        try
        {
            if(!message.isSetField(Account.FIELD))
            {
                return allocationDetail == null ? null : allocationDetail.getAccountName();
            }

            return message.getString(Account.FIELD);
        }
        catch ( Exception e)
        {
            return null;
        }
    }

    public static Tuple<Integer, String> getTradingUser(final Message message) {
        try
        {
            if (message != null )
            {
                List<Group> partyGroups = message.getGroups(NoPartyIDs.FIELD);
                if (partyGroups != null)
                {
                    for (Group partyGroup : partyGroups)
                    {
                        String partyId = partyGroup.getString(PartyID.FIELD);
                        int partyRole = partyGroup.getInt(PartyRole.FIELD);
                        if (PartyRole.ORDER_ORIGINATION_TRADER == partyRole)
                        {
                            return new Tuple<Integer, String>(PartyID.FIELD, partyId);
                        }
                    }
                }
                // if repeating group is not present then use SenderSubId.
                Header header = message.getHeader();
                if (header != null)
                {
                    if (!header.isSetField(SenderSubID.FIELD))
                    {
                        return null;
                    }
                    String tradingUser = header.getString(SenderSubID.FIELD);
                    return new Tuple<Integer, String>(SenderSubID.FIELD, tradingUser);
                }
            }
        }
        catch (Exception e)
        {
            log.error("getTradingUser : Problem with retrieving Trading User info.", e);
        }

        return null;
    }

    public static Tuple<Integer, String> getCCTuple(final Message message) {
        try
        {
            if (message != null )
            {
                List<Group> partyGroups = message.getGroups(NoPartyIDs.FIELD);
                if (partyGroups != null)
                {
                    for (Group partyGroup : partyGroups)
                    {
                        String partyId = partyGroup.getString(PartyID.FIELD);
                        int partyRole = partyGroup.getInt(PartyRole.FIELD);
                        if (PartyRole.EXECUTING_FIRM == partyRole)
                        {
                            return new Tuple<Integer, String>(PartyID.FIELD, partyId);
                        }
                    }
                }
                // if repeating group is not present then use SenderSubId.
                Header header = message.getHeader();
                if (header != null)
                {
                    if (!header.isSetField(DeliverToCompID.FIELD))
                    {
                        return null;
                    }
                    String cc = header.getString(DeliverToCompID.FIELD);
                    return new Tuple<Integer, String>(DeliverToCompID.FIELD, cc);
                }
            }
        }
        catch (Exception e)
        {
            log.warn("getCCTuple : Problem with retrieving CC info", e);
        }

        return null;
    }

    public void processOrderCancelReject(final OrderCancelRequest orderCancelRequest,
                                         final String errorCode, final SessionID sessionID, final ApplicationHandler applicationHandler) throws FieldNotFound
    {
        log.info(new StringBuilder("processOrderCancelReject(OrderCancelRequest) RequestId ").append(orderCancelRequest.getClOrdID().getValue()).append(" OrderId ").append(orderCancelRequest.getOrigClOrdID().getValue()).append(" could not be processed as following error was generated ").append(errorCode).toString());
        applicationHandler.sendOrderCancelReject(orderCancelRequest, sessionID, errorCode);
        SessionMonitor.getInstance().sendAlert(sessionID, "OrderCancelRequest-" + errorCode, orderCancelRequest.getOrigClOrdID().getValue());
    }

    public void processStagedOrderCancelReject(final OrderCancelRequest orderCancelRequest,
                                               final com.integral.staging.Order order, final String errorCode, final SessionID sessionID, final ApplicationHandler applicationHandler) throws FieldNotFound
    {
        log.info(new StringBuilder("processStagedOrderCancelReject(OrderCancelRequest) RequestId ").append(orderCancelRequest.getClOrdID().getValue()).append(" OrderId ").append(orderCancelRequest.getOrigClOrdID().getValue()).append(" could not be processed for Staging Order as following error was generated ").append(errorCode).toString());
        applicationHandler.sendStagedOrderCancelReject(orderCancelRequest, order, sessionID, errorCode);
        SessionMonitor.getInstance().sendAlert(sessionID, "OrderCancelRequest-" + errorCode, orderCancelRequest.getOrigClOrdID().getValue());
    }

    public static void setTradeDate(final com.integral.staging.Order order)
    {
        if (order == null) return;
        // set trade date on order
        IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
        if (tradeDate != null)
        {
            Date date = tradeDate.asJdkDate();
            if (date != null)
            {
                order.setTradeDate(date.getTime());
            }
        }
    }

    public boolean processDontKnowTradeForStagedOrder(final DKReason dkReason, final Text text, final String orderId, final String execId, final User user)
    {
        try
        {
            if (orderId == null || "".equals(orderId.trim())) return false;
            com.integral.staging.Order order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderId, user.getNamespace());
            if (order == null) return false;
            StringBuilder sb = new StringBuilder();
            sb.append("processDontKnowTradeForStagedOrder : Processing DontKnowTrade for Staged Order:");
            sb.append("OrderID:");
            sb.append(orderId);
            sb.append(":ExecID:");
            sb.append(execId);
            sb.append(":User:");
            sb.append(user);
            log.info(sb.toString());
            String clientOrderId = order.getClientOrderId();
            Alert alert = AlertRuntimeMonitor.getInstance().newAlert(clientOrderId, AlertState.RAISED, AlertLevels.CRITICAL, AlertType.TRADE);
            alert.setUserOrganization((user != null && user.getOrganization() != null) ?
                    user.getOrganization().getShortName() : "");
            Organization brokOrg = user != null ? user.getOrganization().getBrokerOrganization() : null;
			if(brokOrg != null) alert.setBrokerOrganization(brokOrg.getShortName());
            String dkReasonText = (text != null ? text.getValue() + "" : "");
            String dkReasonCode = (dkReason != null ? dkReason.getValue() + "" : "");
            StringBuilder desc = new StringBuilder("DontKnowTrade received ClientOrderID=").append(clientOrderId).
                    append(",OrderID=").append(orderId).append(",reasonCode=").append(dkReasonCode).append(",Text=").append(dkReasonText);
            List<Tuple<AlertDataKey,String>> data = new ArrayList<Tuple<AlertDataKey,String>>(8);
            data.add(new Tuple<AlertDataKey, String>(AlertDataKey.OrderId, orderId));
            data.add(new Tuple<AlertDataKey, String>(AlertDataKey.ClOrdId, clientOrderId));
            data.add(new Tuple<AlertDataKey, String>(AlertDataKey.TradeId, execId));
            alert.setText("DontKnowTrade received:" + dkReasonCode + ":" + dkReasonText);
            alert.setUser((user != null) ? user.getShortName() : "");
            alert.setData(data);
            //1. raise alert
            MessageLogger.getInstance().log(ALERT_DK_Received, "FixUtilC", desc.toString(), null);
            //2. Send alert to GM
            AlertRuntimeMonitor.getInstance().sendAlertMessage(alert);
        }
        catch (Exception e)
        {
            log.warn("processDontKnowTradeForStagedOrder : problem with processing DontKnowTrade for : OrderID:" + orderId + ":ExecID:" + execId, e);
            return false;
        }
        return true;
    }

    public static List<AllocationDetail> getProcessedAllocationDetails( List<AllocationDetail> allocationDetails, User user )
    {
        for(AllocationDetail allocationDetail : allocationDetails){
            String origAccount = allocationDetail.getAccountName();
            allocationDetail.setAccountName(
                    getProcessedAccount( user, origAccount )
            );
        }
        return allocationDetails;
    }

    public String getCurrencyPair( MarketDataRequest marketDataRequest ) throws FieldNotFound
    {
        NoRelatedSym symbolGroup = marketDataRequest.createNoRelatedSym();
        marketDataRequest.getGroup(1, symbolGroup);
        return symbolGroup.getSymbol().getValue();
    }


    public void addUserDetails(Message message, ServerLevelSessionID serverLevelSessionID) {
        try {
            if (BEGINSTRING_FIX44.equals(serverLevelSessionID.getParent().getBeginString())) {
                String msgType = message.getHeader().getString(MsgType.FIELD);
                if (MsgType.EXECUTION_REPORT.equals(msgType)) {
                    quickfix.fix44.ExecutionReport.NoPartyIDs noPartyIDsOrg = new quickfix.fix44.ExecutionReport.NoPartyIDs();
                    noPartyIDsOrg.set(new PartyID(serverLevelSessionID.getUser().getOrganization().getShortName()));
                    noPartyIDsOrg.set(new PartyRole(PartyRole.ENTERING_FIRM));
                    noPartyIDsOrg.set(new PartyIDSource(GENERALLY_ACCEPTED_MARKET_PARTICIPANT_IDENTIFIER));
                    message.addGroup(noPartyIDsOrg);

                    quickfix.fix44.ExecutionReport.NoPartyIDs noPartyIDsUser = new quickfix.fix44.ExecutionReport.NoPartyIDs();
                    noPartyIDsUser.set(new PartyID(serverLevelSessionID.getUser().getShortName()));
                    noPartyIDsUser.set(new PartyRole(PartyRole.ENTERING_TRADER));
                    noPartyIDsUser.set(new PartyIDSource(GENERALLY_ACCEPTED_MARKET_PARTICIPANT_IDENTIFIER));
                    message.addGroup(noPartyIDsUser);
                }
            }
        }
        catch (Throwable th){
            log.error("addUserDetails : Failed for message="+message+", sessionId="+serverLevelSessionID, th);
        }
    }

    public void populateExecutionReportForServer2ServerSession(ExecutionReport er, SingleLegOrder orderRequest) {
        try {
            /**
                Populate order rate(s) and amount(s) from {@link SingleLegOrder} instead of cached {@link NewOrderSingle}
             */
            OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
            double orderAmount = requestLeg.getAmount();
            double filledAmount = requestLeg.getFilledAmount();
            double leavesAmount = orderRequest.getDealtCurrency().round( MathUtilC.subtract(orderAmount,filledAmount ));
            double cfdTakerMultiplier = 1.0D;
            if( orderRequest.isCfdTakerAmountAdjustmentDone() ){
                cfdTakerMultiplier = MathUtilC.correctFloatingPointsCalculationPrecision(1.0D/orderRequest.getClientDescriptor().getCfdMultiplicationFactor());
                orderAmount = MathUtilC.multiply(cfdTakerMultiplier,orderAmount);
                leavesAmount = MathUtilC.multiply(cfdTakerMultiplier,leavesAmount);
                filledAmount = MathUtilC.multiply(cfdTakerMultiplier,filledAmount);
            }
            er.set( new Price( orderRequest.getOrderSpotRate() ) );
            if ( orderRequest.getMarketRange() > 0 ) {
                er.set( new PegDifference( orderRequest.getMarketRange() ) );
            }
            er.set( new OrderQty(orderAmount));
            er.set( new CumQty(filledAmount));
            er.set( new LeavesQty( leavesAmount ) );
            if ( orderRequest.getOrderTrigger().getTriggerRate() > ISConstantsC.MIN_RATE ) {
                er.set( new StopPx( orderRequest.getOrderTrigger().getTriggerRate() ) );
            }
            String exDestCode = orderRequest.getExecutionInstructions().getRoutingInstruction().getExternalDestinationCode();
            if( exDestCode != null ){
                er.setString(ExDestination.FIELD,exDestCode);
            }
            AlgoDescriptor algoDescriptor = orderRequest.getAlgoDescriptor();
            if (algoDescriptor != null) {
                AlgoDescriptor.Type type = algoDescriptor.getType();
                String params = orderRequest.getAlgoDescriptor().getParameters();
                switch (type){
                    case AtBest:
                        er.setTargetStrategy(FixConstants.TARGET_STRATEGY_AtBest);
                        if( params != null ) er.setTargetStrategyParameters(params);
                        break;
                    case PAYMENTS:
                        er.setTargetStrategy(FixConstants.TARGET_STRATEGY_PAYMENT);
                        if( params != null ) er.setTargetStrategyParameters(params);
                        break;
                    case ExternalAlgo:
                        er.setExternalAlgoName(algoDescriptor.getName());
                        AlgoParametersExternalAlgo algoParametersExternalAlgo = AlgoParametersExternalAlgo.deserialize(params);
                        if( algoParametersExternalAlgo != null ) er.setExternalAlgoParameters(algoParametersExternalAlgo.getParameters());
                        break;
                }
            }
            else {
                com.integral.model.dealing.TargetStrategy targetStrategy = orderRequest.getTargetStrategy();
                if( targetStrategy != null ){
                    er.setTargetStrategy(targetStrategy.getCode());
                }
                if( orderRequest.getVenueTargetStrategyParams() != null ){
                    er.setTargetStrategyParameters(orderRequest.getVenueTargetStrategyParams());
                }
            }
            if( orderRequest.isOTOInactiveOrder() ){
                er.set(new WorkingIndicator(false));
            }
            String execInst = null;
            if( er.isSetField(ExecInst.FIELD)){
                execInst = er.getString(ExecInst.FIELD);
            }
            if( (orderRequest.getExecutionFlags() & ExecutionFlags.TRAILING) == ExecutionFlags.TRAILING){
                FXRateBasis rb = orderRequest.getFxRateBasis();
                double trailingDistance = orderRequest.getOrderTrigger().getTrailingDistance();
                double trailingDistanceInPips = MathUtilC.multiply(trailingDistance,rb.getPipsFactor());
                er.setTrailingDistance(trailingDistanceInPips);
                boolean addTrailingStopPegExecInst = true;
                if( execInst == null ) {
                    execInst = "";
                }
                else {
                    StringTokenizer st = new StringTokenizer(execInst, " ");
                    String v = String.valueOf(ExecInst.TRAILING_STOP_PEG);
                    while (st.hasMoreTokens()) {
                        String t = st.nextToken();
                        if(t.equals(v)){
                            /*
                                The execinst is already set. no need to set it again.
                             */
                            addTrailingStopPegExecInst = false;
                            break;
                        }
                    }
                }
                if( addTrailingStopPegExecInst ) {
                    execInst = execInst + " " + ExecInst.TRAILING_STOP_PEG;
                }
            }
            if( execInst != null ) {
                er.set(new ExecInst(execInst.trim()));
            }
        }
        catch (Throwable th){
            log.warn("populateExecutionReportForServer2ServerSession : Failed for oId="+orderRequest.get_id(),th);
        }
    }

    public void populateExecutionReportForServer2ServerSession(ExecutionReport er, NewOrderSingle newOrderSingle) {
        String clOrdId = "N/A";
        try {
            clOrdId = newOrderSingle.isSetClOrdID() ? newOrderSingle.getClOrdID().getValue() : "N/A";
            if( newOrderSingle.isSetField(ExDestination.FIELD)){
                er.setString(ExDestination.FIELD,newOrderSingle.getExDestination().getValue());
            }
            if( newOrderSingle.isSetExternalAlgoName()){
                er.setExternalAlgoName(newOrderSingle.getExternalAlgoName());
            }
            if( newOrderSingle.isSetExternalAlgoParameters()){
                er.setExternalAlgoParameters(newOrderSingle.getExternalAlgoParameters());
            }
            if( newOrderSingle.isSetTargetStrategy()){
                er.setTargetStrategy(newOrderSingle.getTargetStrategy());
            }
            if( newOrderSingle.isSetTargetStrategyParameters()){
                er.setTargetStrategyParameters(newOrderSingle.getTargetStrategyParameters());
            }
            if( newOrderSingle.isSetTrailingDistance()){
                er.setTrailingDistance(newOrderSingle.getTrailingDistance());
            }
        }
        catch (Throwable th){
            log.warn("populateExecutionReportForServer2ServerSession(NOS) : Failed for cId="+clOrdId,th);
        }
    }

    public static boolean isOMSOrder(SessionID sessionID, NewOrderSingle newOrderSingle){
        boolean isOMSOrder = false;
        try {
            User user = ClientCache.getInstance().getUser(sessionID);
            if ( user == null ){
                return false;
            }
            Organization customerOrg = user.getOrganization();
            String customerOrgName = customerOrg.getShortName ();
            Organization brokerOrg = customerOrg.getBrokerOrganization();
            if ( newOrderSingle == null || newOrderSingle.getOrdType() == null ){
                return false;
            }
            OrderRequest.Type orderType = getType(newOrderSingle);
            if ( brokerOrg != null && orderType != null )
            {
                String brokerName = brokerOrg.getShortName ();
                List<OrderRequest.Type> orderTypeRouteToOMS = _orderConfig.getOrderTypeRouteToOMS ( brokerName, customerOrgName );
                isOMSOrder = orderTypeRouteToOMS != null && orderTypeRouteToOMS.contains ( orderType )
                        && _orderConfig.isOmsCustomerOrderAdditionalStates ( brokerName, customerOrgName );
                ExDestination exDestination = newOrderSingle.getExDestination();
                if( exDestination != null && !isOMSOrder ) {
                    isOMSOrder = exDestination.getValue().equals(ISCommonConstants.EXECUTION_DESTINATION_OMS) && _orderConfig.isOmsCustomerOrderAdditionalStates ( brokerName, customerOrgName );
                }
            }
        } catch (Exception e) {
            log.error( "isOMSOrder : Error while checking is the order of type OMS: ", e );
        }
        return isOMSOrder;
    }

    private static OrderRequest.Type getType(NewOrderSingle newOrderSingle) throws FieldNotFound {
        OrderRequest.Type type;
        switch (newOrderSingle.getOrdType().getValue()) {
            case 'E':
                type = OrderRequest.Type.MARKET;
                break;
            case '2':
                type = OrderRequest.Type.LIMIT;
                break;
            case '3':
                type = OrderRequest.Type.STOP;
                break;
            default:
                type = null;
                break;
        }
        return type;
    }

    public boolean isLPCrossOrder( NewOrderMultileg newOrderMultileg ){
        boolean isLPCross = false;
        try
        {
            if ( newOrderMultileg.isSetField(ExecInst.FIELD))
            {
                String[] execInsts = newOrderMultileg.getString(ExecInst.FIELD).split( " " );
                for ( String execInst : execInsts )
                {
                    if ( " ".equalsIgnoreCase( execInst ) )
                    {
                        continue;
                    }
                    if(  ExecInst.OK_TO_CROSS == execInst.charAt(0) ){
                        isLPCross = true;
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error("isLPCrossOrder : Exception while validating :  Message->  " + newOrderMultileg, e);
        }
        return isLPCross;
    }

    public static SessionID getRFSSession(User user, boolean isQuote){
        ClientCache clientCache = ClientCache.getInstance();
        SessionID session = clientCache.getSession(user, FixConstants.RFS_SESSION);
        if(session != null) return session;
        if(isQuote) return clientCache.getSession(user, FixConstants.QUOTE_SESSION);
        session = clientCache.getSession(user, FixConstants.TRADE_SESSION);
        if(session != null) return session;
        return clientCache.getSession(user, FixConstants.ORDER_SESSION);
    }
}
