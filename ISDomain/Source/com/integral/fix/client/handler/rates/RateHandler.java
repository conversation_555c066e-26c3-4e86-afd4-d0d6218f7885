package com.integral.fix.client.handler.rates;

import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.price.FXPriceBookC;
import com.integral.finance.dealing.Quote;
import com.integral.fix.client.cache.ClientCache;
import com.integral.fix.client.mbean.FIXClientConfig;
import com.integral.fix.client.message.MarketDataSnapshotFullRefresh;
import com.integral.fix.client.subscription.FixSubscription;
import com.integral.fix.client.translator.Translator;
import com.integral.fix.client.util.FixUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.metrics.Counter;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.user.User;
import quickfix.Message;
import quickfix.Session;
import quickfix.SessionID;
import quickfix.SessionNotFound;

import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class to handle ESP rate distribution in FCS.
 * This class handles distribution of both aggregated and non-aggregated rate distribution.  
 * 
 * <AUTHOR>
 *
 */
public class RateHandler
{
	private RateHandler(){}
	
	//private variables
	static Log log = LogFactory.getLog(RateHandler.class);
	static ClientCache clientCache = ClientCache.getInstance();
    static Log errorLog = LogFactory.getLog( "com.integral.is.rates.error.xml" );

	public static Counter _lpRatesStats = new Counter("fixlprate");
	public static Counter _aggregatedRatesStats = new Counter("fixaggrate");
	
	static
	{
		MetricsManager.instance().register(new Metrics() {
			
			@Override
			public StringBuilder report()
			{
				StringBuilder report = new StringBuilder(_lpRatesStats.toString());
				_lpRatesStats.reset();
				return report;
			}
		});
		
		MetricsManager.instance().register(new Metrics() {
			
			@Override
			public StringBuilder report()
			{
				StringBuilder report =  new StringBuilder(_aggregatedRatesStats.toString());
				_aggregatedRatesStats.reset();
				return report;
			}
		});
	}
	

	/**
	 * Distributes Provider rate updates.
	 * @param quote
	 * @param subscription
	 */
	public static void handleProviderRateUpdate( Quote quote, FixSubscription subscription)
	{
		long st = System.nanoTime();
        User user = subscription.getUser();
        String mdRequestID = subscription.getMdRequestID();
        String senderSubID = subscription.getSenderSubID();

        try
        {
            SessionID sessionId = clientCache.getUserSession( subscription.getUserSessionCacheKey() );

            if ( sessionId == null )
            {
                StringBuilder sb = new StringBuilder( 200 );
                sb.append( "Rate dropped - No session Id in cache for User-" );
                sb.append( user ).append( " SenderSubID-" ).append( senderSubID );
                sb.append( " GUID-" ).append( quote.getGUID() );
                errorLog.info( sb.toString() );
                return;
            }

            if ( subscription.isIncrementalUpdate() )
            {
                subscription.computeAndSendMessage( quote, subscription.getTranslator(), sessionId );
                return;
            }

            //if flow control is not enabled then use thread pool to keep IS Threads unblocked.
            if ( !subscription.isFlowControlEnabled() )
            {
                //ThreadPoolExecutor executor = clientCache.getThreadPoolExecutor(sessionId);
                //executor.execute(new WorkerC(quote, subscription));
                MarketDataSnapshotFullRefresh fixQuote = subscription.getTranslator().getQuoteV3( quote, subscription, sessionId, true );
                if ( fixQuote != null )
                {
                	if( !FIXClientConfig.getInstance().isLogDirectLPRates() )
        			{
                		fixQuote.getMessage().setLog(false);
        			}
                	
                    sendRateMessage( fixQuote.getMessage(), sessionId );
                }
            }
            else
            {
            	MarketDataSnapshotFullRefresh snapshot = subscription.getTranslator().getQuoteV3( quote, subscription, sessionId, true );
                quickfix.Message fixQuote = snapshot.getMessage();
                if ( fixQuote != null )
                {

        			if( !FIXClientConfig.getInstance().isLogDirectLPRates() )
        			{
        				fixQuote.setLog(false);
        			}
        			
                    String key = quote.getOrganization().getShortName() + fixQuote.getField( new quickfix.field.Symbol() ).getValue();
                    ConcurrentHashMap<String, quickfix.Message> buffer = clientCache.getRateBuffer( sessionId );
                    if ( buffer != null )
                    {
                        quickfix.Message replaced = buffer.put( key, fixQuote );
                        clientCache.getExchanger( sessionId ).offer( key );
                        if ( replaced != null )
                        {
                            com.integral.fix.client.util.Counter.increment( sessionId );
                        }
                    }
                    else
                    {
                        errorLog.debug( "RH.handleProviderRateUpdate - Messsage dropped - RateBuffer is null. GUID - " + quote.getGUID() );
                    }
                }
                else
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "RH.handleProviderRateUpdate : FixQuote is Null- RequestId " + mdRequestID );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.warn( "RH.handleProviderRateUpdate failed to handle rate update ", e );
        }
        finally
        {
            //quote.decReference(Quote.ReferenceHolder.QuoteWorkflow);
            long et = System.nanoTime();
    		_lpRatesStats.record( et - st );
        }
    }
		
	public static void handlePriceBookUpdate( FXPriceBook prices, FixSubscription subscription )
	{
		long st = System.nanoTime();
		try
		{			
            if ( subscription.isIncrementalUpdate() )
			{

                FXPriceBook adjustedBook = adjustToMarketDepth(prices, subscription);
                subscription.computeAndSendMessage(adjustedBook, subscription.getTranslator(), subscription.getSessionID());
				return;
			}
			sendFXPriceBook(prices, subscription);
		}
		catch ( Exception e )
		{
			log.warn("RH.handlePriceBookUpdate failed to handle rate update for subscription=" + subscription, e);
		}
		finally
		{
			long et = System.nanoTime();
			_aggregatedRatesStats.record( et - st );
		}
	}

	
	static void sendFXPriceBook( FXPriceBook prices, FixSubscription subscription)
	{
		try
		{
			if ( (subscription.isMultiPriceAggregation() || subscription.isVWAPAggregation()) && !subscription.isAggregatedBook() )
			{
				FXPriceBook tempBook = new FXPriceBookC(1, 1);
				tempBook.setBookId(prices.getBookId());
                tempBook.setSequenceNo( prices.getSequenceNo() );
				Iterator<FXPrice> bids = prices.getBids().iterator();
				Iterator<FXPrice> offers = prices.getOffers().iterator();

				while ( bids.hasNext() && offers.hasNext() )
				{
					tempBook.addBid(bids.next());
					tempBook.addOffer(offers.next());
					sendPriceBookMessage(tempBook,subscription);
					tempBook.getBids().clear();
					tempBook.getOffers().clear();
				}

				while ( bids.hasNext() )
				{
					tempBook.addBid(bids.next());
					sendPriceBookMessage(tempBook,subscription);
					tempBook.getBids().clear();
				}

				while ( offers.hasNext() )
				{
					tempBook.addOffer(offers.next());
					sendPriceBookMessage(tempBook,subscription);
					tempBook.getOffers().clear();
				}
			}
			else
			{
				sendPriceBookMessage(prices,subscription);
			}
		}
		catch ( Exception ex )
		{
			if( log.isDebugEnabled() )
			{
				log.debug("RH.sendFXPriceBook :failed to translate and send quote to Fix Session: fixsubscription " + subscription.getSessionID() + " "+subscription.getCurrencyPair() + " " +subscription.getMdRequestID() + " " + subscription.getUser().getFullName() , ex);
			}
		}
	}

	private static void sendPriceBookMessage( FXPriceBook priceBook, FixSubscription subscription )
	{
		SessionID sessionId = subscription.getSessionID();
		Translator apt =  subscription.getTranslator() ;
		MarketDataSnapshotFullRefresh mds = apt.getQuote(priceBook, subscription);
		Message fixQuote = mds.getMessage();

		if ( fixQuote != null )
		{
			if( !FIXClientConfig.getInstance().isLogAggregatedRates() )
			{
				//disable logging.
				fixQuote.setLog(false);
			}
			
			//if flow control is not enabled then use thread pool to keep IS Threads unblocked.
			if ( !subscription.isFlowControlEnabled() )
			{
				sendRateMessage(fixQuote, sessionId);
			}
			else
			{
				//Assumption here is duplicates are already filtered out.
				String key = subscription.getMdRequestID();
				ConcurrentHashMap<String, quickfix.Message> buffer = clientCache.getRateBuffer(sessionId);
				if ( buffer != null )
				{
					quickfix.Message replaced = buffer.put(key, fixQuote);
					clientCache.getExchanger(sessionId).offer(key);
					if ( replaced != null )
					{
						com.integral.fix.client.util.Counter.increment(sessionId);
					}
				}
				else
				{
					errorLog.debug("RH.sendPriceMessage(PriceBook) - Messsage dropped - RateBuffer is null. GUID - " + priceBook.toString());
				}
			}
		}
		else
		{
			if ( log.isDebugEnabled() )
			{
				log.debug("RH.sendPriceMessage : FixQuote is Null- RequestId " + subscription.getMdRequestID());
			}
		}
	}
	
    public static void sendRateMessage( Message message, SessionID sessionID )
    {
        try
        {
            long st = System.currentTimeMillis();
            Session.sendToTarget( message, sessionID );
            long timeTaken = System.currentTimeMillis() - st;
            if ( timeTaken > FIXClientConfig.getInstance().getProcessingDelayThreshold() )
            {
                errorLog.info( "qfj-minaqueue-add delayed. M#" + message + ",d#" + timeTaken );
            }
        }
        catch ( SessionNotFound e )
        {
        	if( log.isDebugEnabled() )
        	{
        		log.debug( "RH.sendRateMessage session not found. Message not sent. m=" + message + ", sessionId="+sessionID , e );
        	}
        }
    }

    private static FXPriceBook adjustToMarketDepth(FXPriceBook prices, FixSubscription subscription)
    {
        if ( prices == null )
        {
            return prices;
        }

        int marketDepth = getMarketDepth(subscription);

        if ( marketDepth <= 0 )
        {
            return prices;
        }

        int bidSize = prices.getBids().size();
        int offerSize = prices.getOffers().size();

        if (bidSize > marketDepth || offerSize > marketDepth) {
            int newBidSize = bidSize > marketDepth ? marketDepth : bidSize;
            int newOfferSize = offerSize > marketDepth ? marketDepth : offerSize;

            FXPriceBook adjustedBook = new FXPriceBookC(newBidSize, newOfferSize);
            adjustedBook.setBookId(prices.getBookId());
            adjustedBook.setSequenceNo( prices.getSequenceNo() );

            int count = 0;
            while (count < newBidSize)
                adjustedBook.addBid(prices.getBid(count++));

            count = 0;
            while (count < newOfferSize)
                adjustedBook.addOffer(prices.getOffer(count++));

            return adjustedBook;
        }

        return prices;
    }

    private static int getMarketDepth(FixSubscription subscription)
    {
        int marketDepth2 = FixUtilC.getInstance().getAllowedMarketDepth( subscription.getOrganization(), null );
        int marketDepth = subscription.getMarketDepth();
        if ( marketDepth2 > 0 )
        {
            if ( marketDepth == 0 ) //Customer asked for full book.
            {
                marketDepth = marketDepth2;
            }
            else
            {
                if ( marketDepth2 < marketDepth )
                {
                    marketDepth = marketDepth2;
                }
            }
        }
        return marketDepth;
    }
}
