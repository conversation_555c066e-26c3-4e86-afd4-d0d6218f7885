package com.integral.aggregation.validator;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.aggregation.AggregationMethod;
import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.subscription.AggregationRequest;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;
import com.integral.user.User;

import java.util.ArrayList;
import java.util.List;

/**
 * Validates the customer ESP aggregation requests.
 *
 */
public class ESPAggregationRequestValidator {
    protected static Log log = LogFactory.getLog(ESPAggregationRequestValidator.class.getName());
    public static final String ERROR_MSG_REQUEST_ID_NOT_DEFINED = "Request id is not defined.";
    public static final String ERROR_MSG_FI_ORG_NOT_DEFINED = "Organization is not defined.";
    public static final String ERROR_MSG_USER_NOT_DEFINED = "User is not defined.";
    public static final String ERROR_MSG_CCY_PAIR_NOT_DEFINED = "CurrencyPair is not defined.";
    public static final String ERROR_MSG_CCY_PAIR_NOT_SUPPORTED = "CurrencyPair not supported for Taker";
    public static final String ERROR_MSG_DLT_CCY_INVALID = "Dealt currency is invalid.";
    public static final String ERROR_MSG_PROVIDERS_NOT_DEFINED = "Providers are not defined.";
    public static final String ERROR_MSG_RESPONSE_HANDLER_NOT_DEFINED = "Aggregation response handler is not defined.";
    public static final String ERROR_MSG_AGGREGATION_METHOD_NOT_DEFINED = "Aggregation method is not defined.";
    public static final String ERROR_MSG_TIERS_NOT_DEFINED = "View Tiers are not defined.";
    public static final String ERROR_MSG_TIERS_NOT_VALID_LIMITS = "Tiers limit must be greater than zero.";
    public static final String ERROR_MSG_SUBSCRIPTION_SIZE_NOT_VALID = "Requested size must be greater than or equal to zero.";
    public static final String ERROR_MSG_MIN_QTY_NOT_VALID = "Min Qty must be greater than or equal to zero.";
    public static final String ERROR_MSG_HOURGLASS_MULTI_QUOTE = "Aggregation method not supported.";
    public static final String ERROR_MSG_INSUFFICIENT_PERMISSION = "Insufficient Permission.";

    public static Response validateSubscriptionRequest(final AggregationRequest request) {
        Response response = request.createResponse();

        try {
            if (validateRequestId(response, request).isFailed()) {
                return response;
            }

            if (validateOrganization(response, request).isFailed()) {
                return response;
            }

            if (validateUser(response, request).isFailed()) {
                return response;
            }

            if (validateCurrencyPair(response, request).isFailed()) {
                return response;
            }

            if (validateDealtCurrency(response, request).isFailed()) {
                return response;
            }

            if (validateMinQty(response, request).isFailed()) {
                return response;
            }

            if (validateSubscriptionSize(response, request).isFailed()) {
                return response;
            }

            if (validateAggregationMethod(response, request).isFailed()) {
                return response;
            }

            if (validateResponseHandler(response, request).isFailed()) {
                return response;
            }

            if (validateProviders(response, request).isFailed()) {
                return response;
            }

            if (validateTakerCurrencyPairConfig(response, request).isFailed()) {
                return response;
            }

            AggregationMethod method = request.getAggregationMethod();
            if (method == AggregationMethod.WEIGHTED_AVERAGE || method == AggregationMethod.MULTI_PRICE_TIERS
                    || method == AggregationMethod.FB_WEIGHTED_AVERAGE || method == AggregationMethod.FB_MULTI_PRICE_TIERS) {

                if (validateViewTiers(response, request).isFailed()) {
                    return response;
                }
            }

        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                .append(ESPAggregationRequestValidator.class.getName()).append(".validateSubscriptionRequest()")
                .append(' ').append(request)
                .append(' ').append("Exception while validating the request.")
                .append(' ').append(exc.getMessage());
            log.error(sb.toString());
            exc.printStackTrace();
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    public static Response validateUnsubscriptionRequest(final AggregationRequest request) {
        Response response = request.createResponse();

        try {
            if (validateRequestId(response, request).isFailed()) {
                return response;
            }

            if (validateOrganization(response, request).isFailed()) {
                return response;
            }

            if (validateUser(response, request).isFailed()) {
                return response;
            }

        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                .append(ESPAggregationRequestValidator.class.getName()).append(".validateUnsubscriptionRequest()")
                .append(' ').append(request)
                .append(' ').append("Exception while validating the request.")
                .append(' ').append(exc.getMessage());

            log.error(sb.toString());
            exc.printStackTrace();
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    private static Response validateRequestId(Response response, AggregationRequest request) {
        String reqId = request.getRequestId();
        if (reqId == null || reqId.trim().equals("")) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_REQUEST_ID_NOT_DEFINED);
        }
        return response;
    }

    private static Response validateOrganization(Response response, AggregationRequest request) {
        Organization fiOrg = request.getOrganization();
        if (fiOrg == null) {
            if(request.isCleintTagSubscription()){
                response.setStatus(Response.STATUS_FAIL);
                response.setMsg(ERROR_MSG_INSUFFICIENT_PERMISSION);
            }else{
                response.setStatus(Response.STATUS_FAIL);
                response.setMsg(ERROR_MSG_FI_ORG_NOT_DEFINED);
            }
        }
        return response;
    }

    private static Response validateUser(Response response, AggregationRequest request) {
        User user = request.getUser();
        if (user == null) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_USER_NOT_DEFINED);
        }
        return response;
    }

    private static Response validateCurrencyPair(Response response, AggregationRequest request) {
        CurrencyPair ccyPair = request.getCcyPair();
        if (ccyPair == null) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_CCY_PAIR_NOT_DEFINED);
        }
        return response;
    }

    private static Response validateTakerCurrencyPairConfig(Response response, AggregationRequest request) {
        CurrencyPair ccyPair = request.getCcyPair();
        if (ccyPair == null) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_CCY_PAIR_NOT_DEFINED);
            return response;
        }
        Organization fiOrg = request.getOrganization();
        if (OrganizationUtil.manageTakerConfig(fiOrg)) {
            // Validate for each Provider, remove the provider for whom the ccyPair is not supported
            List<Organization> providers = request.getPriceProviders();
            if (providers == null || providers.isEmpty()) {
                response.setStatus(Response.STATUS_FAIL);
                response.setMsg(ERROR_MSG_PROVIDERS_NOT_DEFINED);
                return response;
            }

            TakerOrganizationFunction takerOrganizationFunction = fiOrg.getTakerOrganizationFunction();
            if (takerOrganizationFunction == null) {
                log.info("Case1: ManageCcyPairConfig is enabled for " + fiOrg.getShortName() + " but there's no takerOrgFunction configured. Hence not validating subscription");
                return response;
            }
            CurrencyPairGroup takerDefaultCpg = takerOrganizationFunction.getOneClickCurrencyPairGroup();
            if (takerDefaultCpg == null) {
                log.info("Case2: ManageCcyPairConfig is enabled for " + fiOrg.getShortName() + " but there's no ccyPairs configured. Hence not validating subscription");
                return response;
            }
            List<Organization> effectiveProviders = new ArrayList<Organization>(providers.size());
            for (Organization provider: providers) {
                OrganizationRelationship orgRel = fiOrg.getOrgRelationship(provider, OrganizationRelationship.FI_LP_RELATIONSHIP);
                CurrencyPairGroup effectiveCpg = takerDefaultCpg;
                if (orgRel != null && orgRel.getOneClickCurrencyPairGroup() != null) {
                    effectiveCpg = orgRel.getOneClickCurrencyPairGroup();
                }
                else {
                    log.info("Case1: CcyPair" + ccyPair.getName() + " not configured in " + fiOrg.getShortName() + " for " + provider.getShortName());
                }
                if (effectiveCpg != null && effectiveCpg.getCurrencyPairs() != null
                        && effectiveCpg.getCurrencyPairs().contains(ccyPair)) {
                    effectiveProviders.add(provider);
                }
                else {
                    log.info("Case2: CcyPair" + ccyPair.getName() + " not configured in " + fiOrg.getShortName() + " for " + provider.getShortName());
                }
            }

            if (effectiveProviders.isEmpty()) {
                response.setStatus(Response.STATUS_FAIL);
                response.setMsg(ERROR_MSG_CCY_PAIR_NOT_SUPPORTED);
                return response;
            }
            request.setPriceProviders(effectiveProviders);
        }
        return response;
    }

    private static Response validateDealtCurrency(Response response, AggregationRequest request) {
        CurrencyPair ccyPair = request.getCcyPair();
        Currency dealtCcy = request.getDealtCcy();

        if (dealtCcy != null && !ccyPair.getBaseCurrency().equals(dealtCcy) && !ccyPair.getVariableCurrency().equals(dealtCcy)) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_DLT_CCY_INVALID);
        }
        return response;
    }

    private static Response validateMinQty(Response response, AggregationRequest request) {
        Double minQty = request.getMinQty();

        if (minQty != null && minQty < 0) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_MIN_QTY_NOT_VALID);
        }
        return response;
    }

    private static Response validateSubscriptionSize(Response response, AggregationRequest request) {
        Double subscriptionSize = request.getSubscriptionSize();

        if (subscriptionSize != null && subscriptionSize < 0) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_SUBSCRIPTION_SIZE_NOT_VALID);
        }
        return response;
    }

    private static Response validateAggregationMethod(Response response, AggregationRequest request) {
        AggregationMethod aggMethod = request.getAggregationMethod();
        if (aggMethod == null) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_AGGREGATION_METHOD_NOT_DEFINED);
        }
        return response;
    }

    private static Response validateProviders(Response response, AggregationRequest request) {
        List<Organization> providers = request.getPriceProviders();
        if (providers == null || providers.size() == 0) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_PROVIDERS_NOT_DEFINED);
        }
        return response;
    }

    private static Response validateResponseHandler(Response response, AggregationRequest request) {
        AggregationResponseHandler<Response, FXPriceBook> handler = request.getPriceHandler();
        if (handler == null) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_RESPONSE_HANDLER_NOT_DEFINED);
        }
        return response;
    }

    private static Response validateViewTiers(Response response, AggregationRequest request) {
        List<Double> tiers = request.getViewTiers();
        if (tiers == null || tiers.size() == 0) {
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(ERROR_MSG_TIERS_NOT_DEFINED);
        } else {
            for (Double tier : tiers) {
                if (tier <= 0.0) {
                    response.setStatus(Response.STATUS_FAIL);
                    response.setMsg(ERROR_MSG_TIERS_NOT_VALID_LIMITS);
                    break;
                }
            }
        }
        return response;
    }
}
