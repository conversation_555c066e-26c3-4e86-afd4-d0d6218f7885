package com.integral.oms.spaces.fx.esp.service;

import com.integral.adaptor.order.cache.PendingRequestCache;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.categorization.StreamCategory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.dealing.ContingencyParameter;
import com.integral.finance.dealing.ContingencyType;
import com.integral.finance.dealing.ExecutionFlags;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.order.configuration.OrderServiceMBeanC;
import com.integral.fix.client.FixConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.log.MessageLogger;
import com.integral.is.oms.*;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.is.oms.calculator.OrderWorkflowCalculatorC;
import com.integral.is.pq.PQServiceFactory;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.esp.validation.OrderEventValidator;
import com.integral.is.spaces.fx.esp.workflowhandler.FXESPOrderWorkflowHandler;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.is.spaces.fx.service.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.message.*;
import com.integral.model.dealing.*;
import com.integral.model.dealing.ExecutionInstructions.SecondaryPricePriority;
import com.integral.model.dealing.OrderRequest.Type;
import com.integral.oms.spaces.fx.esp.FXOMSUtil;
import com.integral.oms.spaces.fx.esp.FXOrder;
import com.integral.oms.spaces.fx.esp.cache.FXOrderCache;
import com.integral.oms.spaces.fx.esp.calculator.FXAmendOrderWorkflowCalculator;
import com.integral.oms.spaces.fx.esp.calculator.FXESPCalculatorFactory;
import com.integral.oms.spaces.fx.esp.calculator.FXOrderWorkflowCalculator;
import com.integral.oms.spaces.fx.esp.validation.FXESPOrderRequestValidator;
import com.integral.persistence.util.LogUtil;
import com.integral.rds.util.RefDataUtil;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.MathUtilC;
import com.integral.util.Tuple;
import com.integral.v4ems.util.EMSServerUtil;
import com.integral.xems.XEmsFactory;
import com.integral.xems.XEmsUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.integral.util.StringUtilC.isNull;

/**
 * Service to process orders
 */
public class FXESPOrderRequestService extends OrderRequestServiceC {
	private static final Log log = LogFactory.getLog( FXESPOrderRequestService.class );
	private static final FXOrderWorkflowCalculator orderWorkflowCalculator = FXESPCalculatorFactory.getInstance().getOrderWorkflowCalculator();
	private static final FXESPOrderWorkflowHandler orderWorkflowHandler = FXESPOrderWorkflowHandler.getInstance();
	protected static OrderConfigurationMBean orderConfig = OrderConfiguration.getInstance();
	public WorkflowMessage submitRequest( SingleLegOrder orderRequest, WorkflowMessage msg ) {
		return _submitRequest(orderRequest, msg , false);
	}		
	
	public WorkflowMessage _submitRequest( SingleLegOrder orderRequest, WorkflowMessage msg , boolean isPartOfBatch ) {
		long t00 = System.nanoTime();
		//update limit request with opAdjustment
		adjustOrderPrice( orderRequest );

		long st = System.nanoTime();

		if(isMbean.isProviderPriorityEnabled(orderRequest.getOrgShortName())) {
			boolean isStreamGroupingEnabled = isMbean.isStreamGroupingBasedProviderPriorityEnabled(orderRequest.getOrgShortName());
			boolean isMetal = ISUtilImpl.getInstance().isMetal(orderRequest.getCurrencyPair());
			if(isMetal) isStreamGroupingEnabled = isStreamGroupingEnabled && isMbean.isStreamCategoryEnabledForMetals();

			if(isStreamGroupingEnabled) {
				StringBuilder sb = new StringBuilder(100).append("ORS.submitRequest: ").append(orderRequest.get_id());
				sb.append(' ').append(orderRequest.getCurrencyPair().getName()).append(' ');
				sb.append(orderRequest.getOrderAmount()).append(' ').append("StreamCategories:");
				List<StreamCategory> strmCategories = isMbean.getStreamCategoriesForOrg(orderRequest.getOrgShortName(),
						orderRequest.getCurrencyPair().getName(), orderRequest.getOrderAmount()); // this should include V and X by default.
				for(StreamCategory category : strmCategories) {
					orderRequest.enableFlagForStreamCategory(category);
					sb.append(category.getDisplayValue()).append(' ');

				}
				orderRequest.enableStreamCategoryCheckFlag(); // global flag enable.
				log.info(sb.toString());
			}
		}

		/*
			Exclusive FA
		 */
		if( orderRequest.isExclusiveFullAmountMatchingCheckEnabled()){
			if(isMbean.isExclusiveFullAmountMatchingCheckEnabled(orderRequest.getType())){
				if((orderRequest.getExecutionFlags() & isMbean.getExclusiveFullAmountMatchingCheckMaskForExecutionFlags()) == 0L){
					if (XEmsUtil.useOnlyFullAmountStreamsInOrderMatching(orderRequest.getOrganization().getShortName(),
							orderRequest.getCurrencyPair().getName(),
							orderRequest.getOrderAmount(),
							orderRequest.getDealtCurrency().getName())) {
						orderRequest.setExecutionFlags(orderRequest.getExecutionFlags() | ExecutionFlags.ONLY_FA_STREAMS);
					}
				}
			}
		}

		//Needs to be added here so that ISRequestService can notify GM with proper market snapshot.
		long t0 = System.nanoTime();

		String errorCode = XEmsFactory.getApi().setMarketData(orderRequest);
        if(errorCode != null)
        {
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
            addError(wfMsg, errorCode);
            msg.setReplyMessage(wfMsg);
            return wfMsg;
        }

		long t1 = System.nanoTime();

		final TakerOrganizationFunction takerOrganizationFunction = orderRequest.getOrganization().getTakerOrganizationFunction();
		if (takerOrganizationFunction != null && takerOrganizationFunction.getOrderExecutionParameters() != null) {
			boolean isNettingEnabled = takerOrganizationFunction.getOrderExecutionParameters().isNetTradeOnOrderCompletion();
			if (isNettingEnabled) {
				boolean isShowFills = takerOrganizationFunction.getOrderExecutionParameters().isShowFillsBeforeNetting();
				TimeInForce tif = orderRequest.getTimeInForce();
				if(tif != null) {
					switch (tif) {
					case GTD:
					case IOC:
						orderRequest.setShowFills(isShowFills);
                        if (orderRequest.isPQOrder()) {
                            orderRequest.setNettingEnabled(false);
                        } else {
                        	boolean nettingEnabled = !orderRequest.isStrategyOrder() || isMbean.isNettingEnabledForStrategyOrders(orderRequest.getOrgShortName());
                            orderRequest.setNettingEnabled(nettingEnabled);
                        }
                        break;
					default:
						orderRequest.setNettingEnabled(false);
					}
				}
			}
		}
        // Price-regeneration changes
        orderRequest.setUsePriceRegenerationInOrderMatch(isMbean.isUsePriceRegenerationServiceInOrderMatch()
				&& !orderRequest.isPQDirectedOrder() && !orderRequest.isAlgoOrderAtBest()
		);

		long t2 = System.nanoTime();

        // set the customer stream id if broker for customer is set.
        setCustomerStreamId( orderRequest );

		long t3 = System.nanoTime();

		//Order Submission.
		WorkflowMessage reply = ( WorkflowMessage ) ServiceFactory.getFxisRequestService().process( msg ).getReplyMessage();
		if ( reply.getStatus() == MessageStatus.FAILURE ) {
			log.info( "ORS.submitRequest.INFO : Failed to create order/request.Errors -> " + reply.getErrors() );
			return msg;
		}

		long t4 = System.nanoTime();

		orderRequest = ( SingleLegOrder ) reply.getObject();
		WorkflowMessage responseMessage = createRequest( orderRequest , isPartOfBatch );

		long t5 = System.nanoTime();
		//add workflow parameters returned by ISRequestService
		for ( Object o : reply.getParameters() ) {
			ParameterReference p = ( ParameterReference ) o;
			responseMessage.setParameterValue( p.getKey(), p.getValue() );
		}
		msg.setReplyMessage( responseMessage );
		// if create workflow is successfull, then start the rate subscription from the auto subscription user.
		if ( responseMessage != null && MessageStatus.SUCCESS.equals( responseMessage.getStatus() ) && !EMSServerUtil.isMatchingEngineEnabled()) {
			RateSubscriptionManagerC.getInstance().subscribeRatesForRequest( orderRequest );
		}
		OrderRequestAttributes attr = orderRequest.getOrderRequestAttributes();
		attr.addProcessingTime( "ORS.aOP", ( st - t00 ) );
		attr.addProcessingTime( "ORS.sSCF", ( t0 - st) );
		attr.addProcessingTime( "ORS.sMD", ( t1 - t0 ) );
		attr.addProcessingTime( "ORS.ntEnbld", ( t2 - t1 ) );
		attr.addProcessingTime( "ORS.sCSId", ( t3 - t2 ) );
		attr.addProcessingTime( "ORS.ISRS.prs", ( t4 - t3 ) );
		attr.addProcessingTime( "ORS.cR", ( t5 - t4 ) );

		return msg;
	}

	protected WorkflowMessage processReloadRequest( WorkflowMessage wm ) {
		Object obj = wm.getObject();
		if ( obj == null || !SingleLegOrder.class.equals( obj.getClass() ) ) {
			return super.processReloadRequest( wm );
		}

		SingleLegOrder orderRequest = ( SingleLegOrder ) obj;
		try {
			//enable the log bit for validating the SQLs that get generated if server is not in warmup period.
			if ( !serverMBean.isServerWarmingUp() ) {
				LogUtil.setSwitch( true );
			}
			synchronized ( orderRequest ) {
				String errorCode = validateOrder( orderRequest, false );
				if ( errorCode != null ) {
					log.info( new StringBuilder( 200 ).append( "ORS.processReloadRequest.INFO :#1 Validation errors during reloading order=" )
							.append( orderRequest.getTransactionId() ).append( ",orderId=" ).append( orderRequest.get_id() ).append( ",orderRequest=" )
							.append( orderRequest ).append( ",errorCode=" ).append( errorCode ).toString() );
					WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
					addError( wfMsg, errorCode );
					wm.setReplyMessage( wfMsg );
					return wm;
				}

				errorCode = FXESPOrderRequestValidator.validateAndSetPreferredProviders( orderRequest, orderRequest.getPreferredProviders() );
				if ( errorCode != null ) {
					log.info( new StringBuilder( 200 ).append( "ORS.processReloadRequest.INFO :#2 Validation errors during reloading order=" )
							.append( orderRequest.getTransactionId() ).append( ",orderId=" ).append( orderRequest.get_id() ).append( ",request=" )
							.append( orderRequest ).append( ",errorCode=" ).append( errorCode ).toString() );
					WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
					addError( wfMsg, errorCode );
					wm.setReplyMessage( wfMsg );
					return wm;
				}

				FXESPOrderRequestValidator.setDefaultVenueForRouting(orderRequest);

				OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().setMarketData( orderRequest );
				WorkflowMessage reply = ( WorkflowMessage ) com.integral.is.finance.dealing.ServiceFactory.getISRequestService().process( wm ).getReplyMessage();
				if ( MessageStatus.FAILURE.equals( reply.getStatus() ) ) {
					log.info( new StringBuilder( 200 ).append( "ORS.processReloadRequest.INFO : Failed to reload order/request=" )
							.append( orderRequest.get_id() ).append( ",orderId=" ).append( orderRequest.get_id() ).append( ",errors=" )
							.append( reply.getErrors() ).toString() );
					wm.setReplyMessage( reply );
					return wm;
				}
				orderRequest = ( SingleLegOrder ) reply.getObject();
				if(!orderRequest.isOMSOrder()){
					cancelMatchOnReload( orderRequest );
				}
				wm.setReplyMessage( reloadRequest( orderRequest ) );
				return wm;
			}
		}
		finally {
			LogUtil.removeSwitch();
		}
	}

	public WorkflowMessage reloadRequest( SingleLegOrder orderRequest ) {
		WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, MessageEvent.CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
		try {
			if ( ( short ) ( orderRequest.getExecutionFlags() & ExecutionFlags.HIDDEN_ORDER ) != ExecutionFlags.HIDDEN_ORDER ) {
				wfMsg.setParameterValue( ISConstantsC.BROADCASTORDER, Boolean.TRUE );
			}
            // submit the reloaded order to the order book.
            WorkflowMessage respMsg = OrderCalculatorFactory.getInstance().getOrderRouter().route(orderRequest, false, orderRequest.isOMSOrder());
			if ( respMsg.getStatus().equals( MessageStatus.FAILURE ) ) {
				log.info( new StringBuilder( 200 ).append( "ORS.reloadRequest.INFO : Failure to reload order=" )
						.append( orderRequest.getTransactionId() ).append( ",respMessage=" ).append( respMsg ).append( ",request=" )
						.append( orderRequest ).toString() );
				wfMsg.setStatus( MessageStatus.FAILURE );
				wfMsg.setErrors( respMsg.getErrors() );
				return wfMsg;
			}
			else {
				if(!serverMBean.isOCXDeploymentEnabled()){
				setParametersOnMessage( wfMsg, orderRequest );
			}
		}
		}
		catch ( Exception ex ) {
			addError( wfMsg, fatalErrorMessage );
			log.error( "OrderRequestService:reloadRequest: Exception: ", ex );
		}
		return wfMsg;
	}

    private void cancelMatchOnReload(OrderRequest orderRequest){
        if(orderRequest.getActiveMatchRequests().size() > 0){
            WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
            wfm.setEvent(ISConstantsC.MSG_EVT_CANCEL);
            wfm.setTopic(ISConstantsC.MSG_TOPIC_MATCH);
            wfm.setObject(orderRequest);
            com.integral.is.finance.dealing.ServiceFactory.getISRequestService().process(wfm);
        }
    }

	@Override
	protected void cancelRequestsOnLogout( WorkflowMessage wfMsg, ArrayList col, boolean unsolicitedCancel, boolean isCancelOrders, boolean isCancelSubscriptions ) {
		super.cancelRequestsOnLogout( wfMsg, col, unsolicitedCancel, isCancelOrders, isCancelSubscriptions );
		if(isCancelOrders){
			User user = wfMsg.getSender();
			List<SingleLegOrder> orderRequests = FXESPWorkflowCache.getOrderRequestsForUser( user );
			if( orderRequests != null && !orderRequests.isEmpty() ){
				for( SingleLegOrder orderRequest : orderRequests ){
					if ( FXOMSUtil.shouldWithdrawRequest( orderRequest, "ORS.logout" ) ) {
						WorkflowMessage withdrawMessage = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST );
						try {
							orderRequest.setUnsolicitedCancel( unsolicitedCancel );
							withdrawMessage.setSender( wfMsg.getSender() );
							log.info( "cancelRequestsOnLogout : Withdrawing request event=logout,txnid=" + orderRequest.getTransactionId() + ",orderid=" + orderRequest.get_id() + ",clsf=" + orderRequest.getType() );
                            OrderCalculatorFactory.getInstance().getOrderRouter().cancel(withdrawMessage);
						}
						catch ( Exception e ) {
							log.error( "cancelRequestsOnLogout : Exception ", e );
							ISUtilImpl.getInstance().addError( wfMsg,ISCommonConstants.INTERNAL_SERVER_ERROR );
						}
					}
				}
			}
		}
	}

	@Override
	protected WorkflowMessage processValidateRequest( WorkflowMessage msg ) {
		return FXESPOrderRequestValidator.validateRequest( msg );
	}

    protected WorkflowMessage processSuspendRequest(WorkflowMessage msg) {
        SingleLegOrder request = ( SingleLegOrder ) msg.getObject();
        if ( request == null ) {
            String orderId = ( String ) msg.getParameterValue( ISCommonConstants.IS_ORDER_ID );
            if ( orderId != null ) {
                request = FXESPWorkflowCache.getOrderRequest( orderId );
            }
        }

        return OrderCalculatorFactory.getInstance().getSuspendResumeOrderWorkflowCalculator().suspendOrder(request, msg);
    }

    protected WorkflowMessage processResumeRequest(WorkflowMessage msg) {
        SingleLegOrder request = ( SingleLegOrder ) msg.getObject();
        if ( request == null ) {
            String orderId = ( String ) msg.getParameterValue( ISCommonConstants.IS_ORDER_ID );
            if ( orderId != null ) {
                request = FXESPWorkflowCache.getOrderRequest( orderId );
            }
        }

        return OrderCalculatorFactory.getInstance().getSuspendResumeOrderWorkflowCalculator().resumeOrder(request, msg);
    }


	protected WorkflowMessage processAmendRequest( WorkflowMessage msg ) {
		SingleLegOrder request = ( SingleLegOrder ) msg.getObject();
		if ( request == null ) {
			String orderId = ( String ) msg.getParameterValue( ISCommonConstants.IS_ORDER_ID );
			if ( orderId != null ) {
				request = FXESPWorkflowCache.getOrderRequest( orderId );
			}
		}

		String errorMsg = FXAmendOrderWorkflowCalculator.amendOrder( request, msg );

		WorkflowMessage reply = OrderWorkflowCalculatorC.getWorkflowMessage( request, msg.getTopic(), MessageEvent.AMEND );
		if ( errorMsg != null )
		{
			reply.addError( errorMsg );
			reply.setStatus( MessageStatus.FAILURE );
		}
		msg.setReplyMessage( reply );

		return msg;
	}

	protected WorkflowMessage processReplaceRequest( WorkflowMessage msg, long receivedTimeNanos, long receivedTimeMillis ) {
		SingleLegOrder request = ( SingleLegOrder ) msg.getObject();
		//msg belongs to new Order. Request to be cancelled is in parameters.
		Object isOptOCROrdExecObj = msg.getParameterValue( FixConstants.WF_PARAM_USE_OPT_OCR_EXEC);
		boolean isOptOCROrdExec = isOptOCROrdExecObj == null ? false : (Boolean)isOptOCROrdExecObj;

		initializeNewRequest( msg );
		request.getOrderRequestEventTimes().setReceivedTimeNano( receivedTimeNanos );
		request.getOrderRequestEventTimes().setReceivedTime( receivedTimeMillis );
		request.setUser( msg.getSender() );
		setParametersonRequest( request, msg, null );

		//#0 validate new request
		validateAndSetParameters( request, msg ,false);
		if ( msg.getReplyMessage() != null && msg.getReplyMessage().getStatus() == MessageStatus.FAILURE )
		{
			return msg;
		}

		//#1 Cancel old request
		SingleLegOrder oldRequest = ( SingleLegOrder ) msg.getParameterValue( FixConstants.CANCELLED_REQUEST );
		oldRequest.setReplaced( true );
		WorkflowMessage withdrawRequestMsg = getWithdrawRequestMessage( oldRequest, request, msg );
		WorkflowMessage cancelResponseMessage = processWithdrawRequest( withdrawRequestMsg, receivedTimeNanos, receivedTimeMillis );

        WorkflowMessage replyMsg = ( WorkflowMessage ) cancelResponseMessage.getReplyMessage();
        if(replyMsg.getParameterValue( OrderConstants.Key_AllowFixingOrderCancelReplace ) != null){
            WorkflowMessage errorMessage = MessageFactory.newWorkflowMessage();
            errorMessage.addError( FixConstants.ORDER_CANCEL_TO_LATE_TO_CANCEL );
            msg.setReplyMessage( errorMessage );
            return msg;
        }
        //#2 if fails return
		if ( cancelResponseMessage.getReplyMessage() == null || !cancelResponseMessage.getReplyMessage().getStatus().equals( MessageStatus.SUCCESS ) )
		{
			msg.setReplyMessage( cancelResponseMessage.getReplyMessage() );
			return msg;
		}
		else
		{
			request.setReplacedId( oldRequest.get_id() );
			if ( replyMsg.getParameterValue( ISCommonConstants.WF_PARAM_LINKED_ORDERID ) != null )
			{
				msg.setParameterValue( ISCommonConstants.WF_PARAM_LINKED_ORDERID, replyMsg.getParameterValue( ISCommonConstants.WF_PARAM_LINKED_ORDERID ) );
			}

			//Check if replace request needs to be hold
			//$3 if onHold then put request on hold.
			boolean isHold = replyMsg.getParameterValue( FixConstants.Key_HoldOrderCancelReplaceRequest ) != null;
			if ( isHold )
			{
				String clOrderId = request.getClientReferenceId();
				if ( clOrderId != null && !clOrderId.trim().equals( "" ) )
				{
					String key2 = clOrderId + "_" + request.getUser().getObjectId();
					PendingRequestCache.storeReplaceRequest( key2, msg );
				}
				msg.setReplyMessage( replyMsg );
				return msg;
			}
			else
			{
				if( !isOptOCROrdExec )
				{
					//#3 if successful - send PendingNew report for new request
					MessageHandler handler = ( MessageHandler ) msg.getParameterValue( messageHandler );
					WorkflowMessage pendingNewMsg = getWorkflowMessage( request, ISCommonConstants.MSG_TOPIC_ORDER, MessageEvent.PENDINGNEW );
					OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().notifyUser( request.getUser(), pendingNewMsg, handler );
				}
				else
				{
					log.info("process(REPLACE<REQUEST) OptOCRExec ");
				}
				msg.setEvent( MessageEvent.CREATE );
				msg = submitRequest( request, msg );
			}
		}
		return msg;
	}

	@Override
	protected void addOrderWFProcessingTime( Object request, String wfCode, long timeTaken ) {
		( ( SingleLegOrder ) request ).getOrderRequestAttributes().addProcessingTime( wfCode, timeTaken );
	}

	@Override
	public void initializeNewRequest( WorkflowMessage msg ) {
		if ( log.isDebugEnabled() ) {
			log.debug( "initailizeNewRequest WorkflowMessage Object " + msg.getObject() );
		}
		Object obj = msg.getObject();
		if ( obj != null && SingleLegOrder.class.equals( obj.getClass() ) ) {
			SingleLegOrder orderRequest = ( SingleLegOrder ) obj;
			initialize( msg, orderRequest );
		}
		else {
			super.initializeNewRequest( msg );
		}
	}

	/**
	 * Processes the withdraw request workflow and return the results in workflow message.
	 *
	 * @param msg                workflow message
	 * @param receivedTimeNanos  received time in nano seconds
	 * @param receivedTimeMillis received time in milliseconds
	 * @return result workflow message
	 */
	@Override
	protected WorkflowMessage processWithdrawRequest( WorkflowMessage msg, long receivedTimeNanos, long receivedTimeMillis ) {
		Object obj = msg.getObject();
		if ( obj != null && SingleLegOrder.class.equals( obj.getClass() ) ) {
			SingleLegOrder orderRequest = ( SingleLegOrder ) obj;
			Tuple<Boolean, Boolean> ocoGroupLockTuple = null;
			FXOrder fxOrder = null;
			try {
				//enable the log bit for validating the SQLs that get generated if server is not in warmup period.
				if ( !serverMBean.isServerWarmingUp() ) {
					LogUtil.setSwitch( true );
				}

				if( !orderRequest.getOrderRequestAttributes().isCancellationOnStartup()) {
					/*
						cancellation on startup handles each order separately.
					 */
					fxOrder = FXOrderCache.get(orderRequest.get_id());
					if (fxOrder != null) {
						if(orderRequest.isExternalVenueDirectedOrder() && fxOrder.isVenueAmendInProgress() ){
							int state = fxOrder.checkInitialAndLockOrder();
							WorkflowMessage wfMsg = getWorkflowMessage( orderRequest, ISConstantsC.MSG_TOPIC_REQUEST, MessageEvent.WITHDRAW );
							log.warn("amendOrder : Fail to lock order for amendment. Already amend is in progress. State " + Order.ORDER_STATES[state] + ", OrderId " + orderRequest.get_id());
							ISUtilImpl.getInstance().addError( wfMsg, "Already amend is in progress." );
							wfMsg.setStatus( MessageStatus.FAILURE );
							msg.setReplyMessage( wfMsg );
							return msg;
						}
						boolean ocoOrders = fxOrder.getEntityDescriptor().isOCOOrder();
						if (ocoOrders && orderConfig.isLinkedOCOCancellationEnabled(orderRequest.getUserOrgShortName())) {
							ocoGroupLockTuple = fxOrder.getOCOGroupLock().acquire(fxOrder, 1000L);
                            if (ocoGroupLockTuple.first) {
                                /*
                                    OCO Group Locked. Now Proceed with cancellation of orders
                                 */
                                for (String orderId : fxOrder.getOCOGroupLock().getOrders()) {
                                    SingleLegOrder ocoOrder = FXESPWorkflowCache.getOrderRequest(orderId);
                                    if (ocoOrder != null) {
                                        if (ocoOrder.get_id().equals(orderRequest.get_id())) {
                                            continue;
                                        }
                                        synchronized (ocoOrder) {
                                            ocoOrder.setCancelReceived(true);
                                            WorkflowMessage ocoCancelMsg = ISUtilImpl.getInstance().createWorkflowMessage(ocoOrder, ISCommonConstants.MSG_EVT_WITHDRAW, ISCommonConstants.MSG_TOPIC_REQUEST);
                                            ocoCancelMsg.setSender(msg.getSender());
                                            ocoOrder.getOrderRequestEventTimes().setCancellationReceivedTime(receivedTimeMillis);
                                            ocoOrder.getOrderRequestEventTimes().setCancellationReceivedTimeNano(receivedTimeNanos);
                                            WorkflowMessage responseMessage = OrderCalculatorFactory.getInstance().getOrderRouter().cancel(ocoCancelMsg);
                                            if (responseMessage != null && responseMessage.getReplyMessage() != null) {
                                                WorkflowMessage replyMessage = (WorkflowMessage) responseMessage.getReplyMessage();
                                                if (replyMessage == null || replyMessage.getStatus() != MessageStatus.SUCCESS) {
                                                    log.warn("ORS.processWithdrawRequest : Failed to cancel OCO Order. orderId=" + ocoOrder.get_id());
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else{
                                log.warn("ORS.processWithdrawRequest : Failed to acquire OCO Group Lock. orderId=" + orderRequest.get_id()+", Linked OCO Orders will not be cancelled.");
                            }
                        }
					}
				}
				synchronized ( orderRequest ) {
					orderRequest.setCancelReceived( true );
					// check if the request is getting cancelled during startup when persistent order is enabled.
					if ( orderRequest.getOrderRequestAttributes().isCancellationOnStartup() ) {
                        WorkflowMessage workflowMessage = OrderCalculatorFactory.getInstance().getOrderRouter().cancelOnStartup( msg );
                        if ( log.isDebugEnabled() ) {
							log.debug( new StringBuilder( 200 ).append( "ORS.processWithdrawRequest.DEBUG : Processed cancellation of order request on startup. orderId=" )
									.append( orderRequest.get_id() ).append( ",responseMessage=" ).append( workflowMessage ).toString() );
						}
						return workflowMessage;
					}
					else {
						orderRequest.getOrderRequestEventTimes().setCancellationReceivedTime( receivedTimeMillis );
						orderRequest.getOrderRequestEventTimes().setCancellationReceivedTimeNano( receivedTimeNanos );
						WorkflowMessage responseMessage = OrderCalculatorFactory.getInstance().getOrderRouter().cancel(msg);
						if ( responseMessage != null && responseMessage.getReplyMessage() != null ) {
							WorkflowMessage replyMessage = ( WorkflowMessage ) responseMessage.getReplyMessage();
							if ( replyMessage.getParameterValue( OrderConstants.Key_HoldOrderCancelReplaceRequest ) != null ) {
								replyMessage.setEventName( ISCommonConstants.MSG_TOPIC_PENDING );
							}
						}
						return responseMessage;
					}
				}
			}
			finally {
				if( ocoGroupLockTuple != null ){
					if( ocoGroupLockTuple.first ){
						fxOrder.getOCOGroupLock().release(fxOrder);
					}
				}
				LogUtil.removeSwitch();
			}
		}
		else {
			return super.processWithdrawRequest( msg, receivedTimeNanos, receivedTimeMillis );
		}
	}


	protected boolean isRFQ( Object object ) {
		if ( SingleLegOrder.class.equals( object.getClass() ) ) {
			SingleLegOrder orderRequest = ( SingleLegOrder ) object;
			switch ( orderRequest.getType() ) {
			case RFSSwap:
			case RFSFwd:
			case RFSSpot:
				return true;
			case LIMIT:
			case STOP:
			case STOPLIMIT:
			case TLSTOP:
			case MARKET:
			case ALGO:
				return false;
			}
		}
		return super.isRFQ( object );
	}

	protected boolean isSubscriptionRequest( Object object ) {
		if ( SingleLegOrder.class.equals( object.getClass() ) ) {
			return false;
		}
		return super.isSubscriptionRequest( object );
	}

	protected boolean isTradeRequest( Object object ) {
		if ( SingleLegOrder.class.equals( object.getClass() ) ) {
			return false;
		}
		return super.isTradeRequest( object );
	}

	protected boolean isOrder( Object object ) {
		if ( SingleLegOrder.class.equals( object.getClass() ) ) {
			SingleLegOrder orderRequest = ( SingleLegOrder ) object;
			if( log.isDebugEnabled() ){
				log.debug( "isOrder : SingleLegOrder is of type " + orderRequest.getType() );
			}
			return DealingModelUtil.isESPOrder( orderRequest );
		}
		return super.isOrder( object );
	}

	protected String validateOrder( SingleLegOrder orderRequest, boolean isWarmUpOrder) {

		User user = orderRequest.getUser();

			//always take user' org
			Organization org = user.getOrganization();
		String errorCode = null ;
		if (!serverMBean.isOCXDeploymentEnabled()) {
			errorCode = FXESPOrderRequestValidator.validateOrdersOrg(org);
			if ( errorCode != null ) {
				return errorCode;
			}
			errorCode = validateOrderNotes(orderRequest);
			if (null != errorCode) {
				return errorCode;
			}
			
			if( com.integral.model.dealing.TimeInForce.DAY.equals(orderRequest.getTimeInForce() ) ){
				if ( !user.hasPermission( ISConstantsC.DAY_TRADING_PERM ) ) {
					return ISConstantsC.USER_VALIDATION_INSUFFICIENT_PERMISSION;
				}
			}
		}

			errorCode = FXESPOrderRequestValidator.validateStopOrder( orderRequest );
			if ( errorCode != null ) {
				return errorCode;
			}

		//Check for duplicate order id
		if ( !orderRequest.getOrderRequestAttributes().isReloadedOrderRequest() ) {
			String clOrderId = orderRequest.getClientReferenceId();
			if ( clOrderId != null && !clOrderId.trim().equals( "" ) ) {
				SingleLegOrder order = FXESPWorkflowCache.getOrderRequestByClientReferenceId( clOrderId, user.getShortName(), user.getOrganization().getShortName() );
				if ( order != null ) {
					return FixConstants.ACCEPTANCE_REJECTION_REASON_DUPLICATE_ORDER;
				}
			}
		}

		if (orderRequest.getType() != null) {
			switch (orderRequest.getType()) {
				case MARKET:
					if (!orderRequest.isMarketRangeOrder()) {
						return null;
					}
					if ((orderRequest.getExecutionFlags() & ExecutionFlags.ALGO_RISKNET_DIRECTED_ORDER) == ExecutionFlags.ALGO_RISKNET_DIRECTED_ORDER) {
						return null;
					}
					break;
				case STOP:
					return null;
				case ALGO:
					if ((orderRequest.getExecutionFlags() & ExecutionFlags.ALGO_AT_BEST_ORDER) == ExecutionFlags.ALGO_AT_BEST_ORDER) {
						return null;
					}
			}
		}

		if ( orderRequest.getOrderSpotRate() < ISConstantsC.MIN_RATE ) {
			return ISCommonConstants.REQUEST_VALIDATION_INVALID_ORDER_RATE;
		}

		return null;
	}
	
	private String validateOrderNotes( SingleLegOrder orderRequest)
	{
		 String notes = orderRequest.getNotes();
         if(!StringUtils.isBlank(notes))
         {
    		 char[] invalidChars=  ISFactory.getInstance().getISMBean().getExcludedSpecialCharcter();
    		 if( notes.length()>ISCommonConstants.ORDER_NOTES_MAX_LENGTH)
    		 {
    			 log.info( "ORS.validate :: Order Notes Max length exceeded , Max Length Supported: "+ISCommonConstants.ORDER_NOTES_MAX_LENGTH);
        		 return ISCommonConstants.REQUEST_VALIDATION_INVALID_ORDER_NOTES;
    		 }
    		 else
        	 if(!StringUtils.containsNone(notes, invalidChars))
        	 {
        		 String invalidChar = new String(invalidChars);
    			 log.info( "ORS.validate :: Order Notes contains invalid Characters : " + invalidChar );
        		 return ISCommonConstants.REQUEST_VALIDATION_INVALID_ORDER_NOTES;
        	 }
         }
		 
		 return null;
	}
	 
	protected RequestType getRequestType( WorkflowMessage msg ) {
		Object obj = msg.getObject();
		if ( SingleLegOrder.class.equals( obj.getClass() ) ) {
			return RequestType.ORDER_REQUEST;
		}
		return null;
	}

	@Override
	protected WorkflowMessage processCreateOrderRequest( WorkflowMessage msg, long receivedTimeNanos, long receivedTimeMillis ) {
		if ( log.isDebugEnabled() ) {
			log.debug( "processCreateRequest WorkflowMessage Object ->" + msg.getObject() );
		}
		return processCreateOrderRequest( ( ( SingleLegOrder ) msg.getObject() ), msg, receivedTimeNanos, receivedTimeMillis );
	}

	protected String validateAndSetCounterparty( WorkflowMessage msg, SingleLegOrder orderRequest ) {
		return orderWorkflowHandler.validateAndSetCounterpartyOnRequest( orderRequest, msg );
	}

	private WorkflowMessage processCreateOrderRequest( SingleLegOrder orderRequest, WorkflowMessage msg, Long receivedTimeNanos, Long receivedTimeMillis ) {
		long t0 = System.nanoTime();
		try {
			//enable the log bit for validating the SQLs that get generated if server is not in warmup period.
			if ( !serverMBean.isServerWarmingUp() ) {
				LogUtil.setSwitch( true );
			}
			synchronized ( orderRequest ) {
				OrderRequestEventTimes eventTimes = orderRequest.getOrderRequestEventTimes();
				eventTimes.setReceivedTimeNano( receivedTimeNanos );
				eventTimes.setSubmissionTimeNano( receivedTimeNanos );
				eventTimes.setReceivedTime( receivedTimeMillis );
				if ( orderRequest.getUser() == null ) {
					orderRequest.setUser( msg.getSender() );
				}
				long t1 = System.nanoTime();
				setParametersonRequest( orderRequest, msg, null );
				long t2 = System.nanoTime();

                /*if ( orderConfigMBean.isDirectOASetup() ) {
					int execFlags = orderRequest.getExecutionFlags();
					String channel = orderRequest.getChannel();
					if ( OrderWorkflowCalculatorC.isClientCrossRequest( execFlags, channel ) ) {//If here means DirectOA setup - Old .NET client has connected to do ESP/RFS.
						return ServiceFactory.getFxisRequestService().process( msg );
					}
				}*/
				msg = validateAndSetParameters( orderRequest, msg ,false);
				long t3 = System.nanoTime();
				if ( msg.getReplyMessage() != null && msg.getReplyMessage().getStatus() == MessageStatus.FAILURE ) {
                    WorkflowMessage responseMsg = (WorkflowMessage) msg.getReplyMessage();
                    responseMsg.setParameterValue(ISConstantsC.IS_ORDER_ID, orderRequest.get_id());
                    StringBuilder sb = ISUtilImpl.getInstance().getErrMessage(responseMsg.getErrors());
                    String rejectCode = ISUtilImpl.getInstance().getFirstErrorCode(responseMsg.getErrors());
                    orderWorkflowHandler.orderFailed(orderRequest, rejectCode, sb.toString());
					return msg;
				}
				msg = submitRequest( orderRequest, msg );
				long t4 = System.nanoTime();
				OrderRequestAttributes attr = orderRequest.getOrderRequestAttributes();
				attr.addProcessingTime( "ORS.pCOR1", ( t1 - t0 ) );
				attr.addProcessingTime( "ORS.sPR", ( t2 - t1 ) );
				attr.addProcessingTime( "ORS.vASP", ( t3 - t2 ) );
				attr.addProcessingTime( "ORS.sR", ( t4 - t3 ) );
				return msg;
			}
		}
		finally {
			LogUtil.removeSwitch();
		}
	}

	private WorkflowMessage createRequest( SingleLegOrder orderRequest , boolean isPartOfBatch ) {
		WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, MessageEvent.CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
		if ( orderRequest == null ) {
			ISUtilImpl.getInstance().addError( wfMsg, ISConstantsC.ERROR_INTERNALSERVER, new Object[]{ISConstantsC.WF_ORDERING, "", "Request is NULL"} );
			log.error( "ORS.createRequest :: Request is null." );
			return wfMsg;
		}
		try {
			int execFlags = orderRequest.getExecutionFlags();
			if ( ( execFlags & ExecutionFlags.HIDDEN_ORDER ) != ExecutionFlags.HIDDEN_ORDER ) {
				wfMsg.setParameterValue( ISConstantsC.BROADCASTORDER, Boolean.TRUE );
			}


            WorkflowMessage respMsg = XEmsFactory.getApi().submitOrder(orderRequest,isPartOfBatch);

			if ( respMsg.getStatus().equals( MessageStatus.FAILURE ) ) {
				wfMsg.setStatus( MessageStatus.FAILURE );
				wfMsg.setErrors( respMsg.getErrors() );
                StringBuilder sb = ISUtilImpl.getInstance().getErrMessage( respMsg.getErrors() );
                String rejectCode = ISUtilImpl.getInstance().getFirstErrorCode(respMsg.getErrors());
                FXESPOrderWorkflowHandler.getInstance().orderFailed(orderRequest, rejectCode, sb.toString());
				clearCache( orderRequest );
				return wfMsg;
			}
			else {
				if( !serverMBean.isOCXDeploymentEnabled() ){
				setParametersOnMessage( wfMsg, orderRequest );
			}
		}
		}
		catch ( Exception ex ) {
			addError( wfMsg, fatalErrorMessage );
			log.error( "OrderRequestService:createRequest: Exception: ", ex );
		}
		return wfMsg;
	}

	protected void clearCache( SingleLegOrder orderRequest ) {
		orderWorkflowHandler.removeCache( orderRequest );
		if ( orderRequest.get_id() != null ) {
			FXOrderCache.remove( orderRequest.get_id() );
		}
	}

	/*
	 * Set parameters in order submit response message. Required for Chief dealer message.
	 */
	private void setParametersOnMessage( WorkflowMessage wfMsg, SingleLegOrder orderRequest ) {
		String orderId = orderRequest.get_id();
		wfMsg.setParameterValue( ISConstantsC.IS_ORDER_ID, orderId );
		String externalRequestId = orderRequest.getClientReferenceId();
		if ( externalRequestId != null && !externalRequestId.trim().equals( "" ) ) {
			wfMsg.setParameterValue( ISConstantsC.EXTERNAL_REQUEST_ID, externalRequestId );
		}

		OrderTrigger.Type triggerType = orderRequest.getStopLossTriggerType();
		if ( triggerType != null ) {
			wfMsg.setParameterValue( ISConstantsC.SL_TRIGGER_TYPE, triggerType.name() );
		}
		String timeInForce = "";
		TimeInForce tif = orderRequest.getTimeInForce();
		if(tif != null) {
			switch (tif) {
			case IOC:
				timeInForce = ISConstantsC.ORDER_CLASSIFICATION_IOC;
				break;
			case GTC:
				timeInForce = ISConstantsC.ORDER_CLASSIFICATION_GTC;
				break;
			case GTD:
				timeInForce = ISConstantsC.ORDER_CLASSIFICATION_GTD;
				break;
			case DAY:
				timeInForce = ISConstantsC.ORDER_CLASSIFICATION_DAY;
				break;
			case GFS:
				timeInForce = ISConstantsC.ORDER_CLASSIFICATION_GFS;
				break;
            case GTF:
                timeInForce = ISConstantsC.ORDER_CLASSIFICATION_GTF;
                break;
			}
		}
		wfMsg.setParameterValue( ISConstantsC.TIME_IN_FORCE, timeInForce );
		String orderType = orderRequest.getType().name();
		if ( orderType != null ) {
			wfMsg.setParameterValue( ISConstantsC.ORDER_TYPE, orderType );
		}
	}

	private void adjustOrderPrice( SingleLegOrder orderRequest ) {
		if ( orderConfigMBean.isOPAdjustmentEnabled() ) {
			String channel = orderRequest.getChannel();
			if ( channel != null && orderConfigMBean.getOPAdjustmentAllowedChannels().contains( channel ) ) {
				String reqClsf = orderRequest.getType().name();
				boolean isMarketRangeAllowed = orderConfigMBean.getOPAdjustmentAllowedOrderTypes().contains( OrderConstants.MARKET_RANGE_ORDER );
				boolean isMarketRange = orderRequest.isMarketRangeOrder();
				if ( orderConfigMBean.getOPAdjustmentAllowedOrderTypes().contains( reqClsf ) ||
						( isMarketRangeAllowed && ISCommonConstants.MARKET.equalsIgnoreCase( reqClsf )
								&& isMarketRange ) ) {
					String orgName = orderRequest.getUser().getOrganization().getShortName();
					String ccyPair = orderRequest.getCurrencyPair().getName();
					Double adjPips = orderConfigMBean.getOPAdjustmentPips( orgName, ccyPair );
					if ( adjPips == null ) {
						log.info( "ORS.adjustOrderPrice.INFO - Order Price is not adjusted. Adjustment Pips are not defined for OrgName : " + orgName + " Ccypair : " + ccyPair );
					}
					else {
						FXRateBasis fxRateBasis = orderRequest.getFxRateBasis();
						double shift = adjPips / fxRateBasis.getPipsFactor();
						OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
						double origSpotRate = orderRequest.getOrderSpotRate();
						double adjustedSpotRate = origSpotRate;
						switch ( requestLeg.getBuySellMode() ) {
						case BUY://BID
							adjustedSpotRate = origSpotRate + shift;
							break;
						case SELL://OFFER
							adjustedSpotRate = origSpotRate - shift;

						}
						adjustedSpotRate = DealingModelUtil.roundSpotRate( fxRateBasis, orderRequest.getTermCurrency().getShortName(), adjustedSpotRate );
						requestLeg.setSpotRate( adjustedSpotRate );
						log.info( "ORS.adjustOrderPrice.INFO - Order Price is adjusted. Details - External Order Id : " + orderRequest.getClientReferenceId() + " OrgName : " + orgName + " Ccypair : " + ccyPair + " Adjustment Pips : " + adjPips + " Orignal Price : " + origSpotRate + " Adjusted Price : " + adjustedSpotRate );
					}
				}
			}
		}
	}


	private WorkflowMessage validateAndSetParameters( SingleLegOrder orderRequest, WorkflowMessage msg ,boolean ignoreCGRValidations ) {
		final boolean isWarmUpOrder = ISUtilImpl.getInstance().isWarmUpObject( orderRequest );
		orderRequest.setServerManaged( true );
		final long t0 = System.nanoTime();
		String errorCode = validateOrder( orderRequest, isWarmUpOrder);
		if ( errorCode != null ) {
			log.info( "ORS.processCreateOrderRequest.INFO : #1 Validation errors --  " + errorCode );
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}

		final long t1 = System.nanoTime();
		errorCode = validateAndSetCounterparty( msg, orderRequest );
		if ( errorCode != null ) {
			log.info( "ORS.processCreateOrderRequest.INFO : #2 Validation errors on setting counterparty --  " + errorCode );
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}

		final long t2 = System.nanoTime();
		//Call IS layer's workflowhandler to do rest of validations
		//Call order workflow handle to populate order id etc.
		//Order id is required for contingent orders validation and population of contingency parameters.
		orderWorkflowHandler.populateOrderRequest( orderRequest );
		orderRequest.setNamespace( orderRequest.getOrganization().getNamespace() );

		final long t3 = System.nanoTime();
		errorCode = FXESPOrderRequestValidator.validateAndSetPreferredProviders( orderRequest, msg );
		if ( errorCode != null ) {
			log.info( "ORS.processCreateOrderRequest.INFO :#3 Preferred provider validation errors --  " + errorCode );
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}

		final long t4 = System.nanoTime();
		if ( !ignoreCGRValidations )
		{
			errorCode = FXESPOrderRequestValidator.validateAndSetContingentRequestGroup( orderRequest, msg );
			if ( errorCode != null ) {
				log.info( "ORS.processCreateOrderRequest.INFO : #4 Contingent order validation errors --  " + errorCode );
				WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
				addError( wfMsg, errorCode );
				msg.setReplyMessage( wfMsg );
				return msg;
			}
		}
		
		final long t5 = System.nanoTime();
		errorCode = setMinFillAmt( orderRequest, msg );
		if ( errorCode != null ) {
			log.info( "ORS.processCreateOrderRequest.INFO :#5 Minimum Fill size validation errors -- " + errorCode );
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}
		

		final long t6 = System.nanoTime();
		errorCode = FXESPOrderRequestValidator.validateAndPopulateStrategyParameters(msg, orderRequest);
		if(orderRequest.getTimeInForce() == null) {
			orderRequest.setTimeInForce(TimeInForce.UNDEFINED);
		}
		if ( errorCode != null )
		{
			log.info("ORS.processCreateOrderRequest.INFO : #6 Strategy order validation errors --  " + errorCode);
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
			addError(wfMsg, errorCode);
			msg.setReplyMessage(wfMsg);
			return msg;
		}

		OrderRequestAttributes attributes = orderRequest.getOrderRequestAttributes();

		TimeInForce tif = orderRequest.getTimeInForce();
		if(tif != null) {
			switch (tif) {
			case IOC:
			case FOK:
				if ( orderRequest.isLiftOrder() ) {
					Tuple<Boolean, Integer> result = FXOMSUtil.isExpiryEnabled( orderRequest );
					if ( result.first && result.second > 0 ) {
						orderRequest.setExpireTime( orderRequest.getSubmissionTime() + result.second );
					}
				}
				break;
			}
		}
		
		/**
		 * Added to support resting orders
		 * 
		 */
		final long t7 = System.nanoTime();
		errorCode = FXESPOrderRequestValidator.populateAndValidateOutrightLimitOrderFields(orderRequest, msg);
		if ( errorCode != null )
		{
			log.info("ORS.processCreateOrderRequest.INFO : #7 Outright order validation errors --  " + errorCode);
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
			addError(wfMsg, errorCode);
			msg.setReplyMessage(wfMsg);			
			return msg;
		}

		final long t8 = System.nanoTime();
		WorkflowMessage responseMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, MessageEvent.CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
		//Set true to ignore check.
		attributes.setIgnoreTradesEnabledCheck(true);
		OrderEventValidator.validate( orderRequest, responseMsg, OrderEventValidator.ValidationEvent.ORDER_SUBMIT );
		boolean isFailed = responseMsg != null && responseMsg.getErrors() != null && responseMsg.getErrors().size() > 0;
		if ( responseMsg.getStatus() == MessageStatus.FAILURE || isFailed ) {
			msg.setReplyMessage(responseMsg);
			return msg;
		}
		else {
			attributes.setValidated(true);
		}

		final long t9 = System.nanoTime();
		errorCode = FXESPOrderRequestValidator.validateAndSetDirectedOrderParameters(orderRequest, msg);
        if(errorCode != null) {
            log.info("ORS.processCreateOrderRequest.INFO: #8 Directed order validation errors -- " + errorCode);
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
            addError( wfMsg, errorCode );
            msg.setReplyMessage( wfMsg );
            return msg;
        }
        
        final long t10 = System.nanoTime();
		OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
		errorCode = FXESPOrderRequestValidator.validateOrdersize( orderRequest, requestLeg.getAmount(), isWarmUpOrder );
		if ( errorCode != null ) {
			log.info( "ORS.processCreateOrderRequest.INFO :#9 Order size validation errors --  " + errorCode );
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}
        
		final long t11 = System.nanoTime();
		errorCode = FXESPOrderRequestValidator.validateOutrightRestingOrder(orderRequest, msg);
		if(errorCode != null)
		{
			log.info("ORS.processCreateOrderRequest.INFO : #10 Outright order validation errors --  " + errorCode);
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
			addError(wfMsg, errorCode);
			msg.setReplyMessage(wfMsg);			
			return msg;
		}
		
        final long t12 = System.nanoTime();
        errorCode = FXESPOrderRequestValidator.validateAndSetFixingOrderParameters(orderRequest, msg);
        if(errorCode != null) {
            log.info("ORS.processCreateOrderRequest.INFO : #11 Fixing order validation errors --  " + errorCode);
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
            addError(wfMsg, errorCode);
            msg.setReplyMessage(wfMsg);
            return msg;
        }
        
        final long t13 = System.nanoTime();
        
        errorCode = validateAndSetAccount(orderRequest, msg);
        if(errorCode != null)
        {
            log.info("ORS.processCreateOrderRequest.INFO : #12 Account validation errors --  " + errorCode);
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
            addError(wfMsg, errorCode);
            msg.setReplyMessage(wfMsg);
            return msg;
        }
        
        final long t14 = System.nanoTime();
        
        errorCode = FXESPOrderRequestValidator.validateAndSetVenueTargetStrategyParameters(orderRequest, msg);
        if(errorCode != null) {
            log.info("ORS.processCreateOrderRequest.INFO : #13 Venue Target Strategy validation errors --  " + errorCode);
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
            addError(wfMsg, errorCode);
            msg.setReplyMessage(wfMsg);
            return msg;
        }

		final long t15 = System.nanoTime();

		errorCode = FXESPOrderRequestValidator.validateOrderEntryTime( orderRequest, isWarmUpOrder );
		if ( errorCode != null )
		{
			log.info( "ORS.processCreateOrderRequest.INFO : #14 Validation errors on checking order entry time --  " + errorCode );
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}

		final long t16 = System.nanoTime();
		// setting venue if applicable.
		FXESPOrderRequestValidator.setDefaultVenueForRouting(orderRequest);
		final long t17 = System.nanoTime();

        FXESPOrderRequestValidator.setMinimumRestingTimeInRiskNet(orderRequest);
        final long t18 = System.nanoTime();

		errorCode = FXESPOrderRequestValidator.validateCurrencyPairSettlementType( orderRequest );
		if ( errorCode != null )
		{
			log.info( "ORS.processCreateOrderRequest.INFO : #15 Validation errors on checking currency pair settlement type --  " + errorCode );
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}
		final long t19 = System.nanoTime();

		errorCode = FXESPOrderRequestValidator.validatePQDirectedOrder(orderRequest);
		if ( errorCode != null )
		{
			log.info( "ORS.processCreateOrderRequest.INFO : #16 Validation errors on checking PQ Directed Order --  " + errorCode +" orderId="+orderRequest.get_id());
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}
		final long t20 = System.nanoTime();

		errorCode = FXESPOrderRequestValidator.validateExDestinationDirectedOrder(orderRequest);
		if ( errorCode != null )
		{
			log.info( "ORS.processCreateOrderRequest.INFO : #17 Validation errors on checking ExDestination Directed Order --  " + errorCode +" orderId="+orderRequest.get_id());
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}
		final long t21 = System.nanoTime();

		WorkflowMessage wm = FXESPOrderRequestValidator.validateCustomParameters ( orderRequest, isWarmUpOrder );
		if ( wm != null )
		{
			log.info( "ORS.processCreateOrderRequest.INFO : #18 Validation errors on checking custom parameters in the order=" + wm.getErrorMessage () +" orderId="+ orderRequest.get_id() );
			msg.setReplyMessage( wm );
			return msg;
		}
		 final long t22 = System.nanoTime ();

		errorCode = FXESPOrderRequestValidator.validateAtBestDirectedOrder(orderRequest);
		if ( errorCode != null )
		{
			log.info( "ORS.processCreateOrderRequest.INFO : #19 Validation errors on checking ExDestination Directed Order --  " + errorCode +" orderId="+orderRequest.get_id());
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( orderRequest, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
			addError( wfMsg, errorCode );
			msg.setReplyMessage( wfMsg );
			return msg;
		}
		final long t23 = System.nanoTime();

		attributes.addProcessingTime( "ORS.vAsP1", ( t1 - t0 ) );
		attributes.addProcessingTime( "ORS.vAsP2", ( t2 - t1 ) );
		attributes.addProcessingTime( "ORS.vAsP3", ( t3 - t2 ) );
		attributes.addProcessingTime( "ORS.vAsP4", ( t4 - t3 ) );
		attributes.addProcessingTime( "ORS.vAsP5", ( t5 - t4 ) );
		attributes.addProcessingTime( "ORS.vAsP6", ( t7 - t6 ) );
		attributes.addProcessingTime( "ORS.vAsP7", ( t8 - t7 ) );
        attributes.addProcessingTime( "ORS.vAsP8", ( t9 - t8 ) );
        attributes.addProcessingTime( "ORS.vAsP9", ( t10 - t9 ) );
        attributes.addProcessingTime( "ORS.vAsP10", ( t11 - t10 ) );
        attributes.addProcessingTime( "ORS.vAsP11", ( t12 - t11 ) );
        attributes.addProcessingTime( "ORS.vAsP12", ( t13 - t12 ) );
        attributes.addProcessingTime( "ORS.vAsP13", ( t14 - t13 ) );
		attributes.addProcessingTime( "ORS.vAsP14", ( t15 - t14 ) );
		attributes.addProcessingTime( "ORS.vAsP15", ( t16 - t15 ) );
		attributes.addProcessingTime( "ORS.vAsP16", ( t17 - t16 ) );
        attributes.addProcessingTime( "ORS.vAsP17", ( t18 - t17 ) );
		attributes.addProcessingTime( "ORS.vAsP18", ( t19 - t18 ) );
		attributes.addProcessingTime( "ORS.vAsP19", ( t20 - t19 ) );
		attributes.addProcessingTime( "ORS.vAsP20", ( t21 - t20 ) );
		attributes.addProcessingTime( "ORS.vAsP21", ( t22 - t21 ) );
		//Successful
		return msg;
	}

	private String validateAndSetAccount(SingleLegOrder orderRequest, WorkflowMessage msg)
	{
        if(ISFactory.getInstance().getISMBean().setAccountOnOrder())
        {
            return populateAccountIdentifiers(orderRequest, orderRequest.getNamespaceName());
        }
        return null;
	}

    private String populateAccountIdentifiers(SingleLegOrder orderRequest,String accountNamespace) {
        AccountRef aRef = orderRequest.getAccountRef();
        if(aRef != null)
        {
            if(accountNamespace == null)
            {
                log.info(".setAccount: Broker Org name not found for the order request, rejecting order: " + orderRequest.get_id() + "");
                return ISCommonConstants.REQUEST_VALIDATION_ACCOUNT_NAMESPACE_NOT_FOUND;
            }

            aRef.setNamespace(accountNamespace);
            String customerOrg = aRef.getCustOrg();
            String accountId = aRef.getShortName();

            if(accountId == null || accountId.trim().isEmpty()){
                log.info(".setAccount: accountId field is null or empty, hence cannot set Account on this order request: " + accountId);
                return ISCommonConstants.REQUEST_VALIDATION_INVALID_ACCOUNT_ID;
            }

            String accId = RefDataUtil.generateAccountKey(accountNamespace, customerOrg, accountId);
            aRef.setUid(accId);
            String accountGroupShortName = aRef.getGroupShortName();
            if(null != accountGroupShortName){
            	String accGrpId = RefDataUtil.generateAccountGroupKey(accountNamespace,accountGroupShortName);
            	aRef.setGroupId(accGrpId);
			}
        }

        return null;
    }

	private void setParametersonRequest( SingleLegOrder orderRequest, WorkflowMessage msg, WorkflowMessage parent ) {
		setSubmissionTime( orderRequest );
		//setTimeInForce( orderRequest, msg );
		setMarketRange( orderRequest, msg );
		setMaxShowAmt( orderRequest, msg );
		setExecutionFlags( orderRequest, msg );
		setOrderExpTime( orderRequest, msg );
		setOMSOrderExecutionFlags(orderRequest, parent);
		setISMakerFlag( orderRequest );
		setOrderReplaceParameters( orderRequest, msg );
		setPersistantOrderRequest( orderRequest );
		orderWorkflowHandler.setOrderRequestChannel( orderRequest, msg );
		setExternalSpread( orderRequest );
        orderWorkflowHandler.setParametersInRequest( orderRequest, msg );
	}

	private void setOMSOrderExecutionFlags( SingleLegOrder singleLegOrder, WorkflowMessage parent ) {
	if (!ConfigurationFactory.getServerMBean().isOMSServer()) {
		Organization custOrg = singleLegOrder.getOrganization();
		Organization toOMSOrg = custOrg.getBrokerOrganization();
		if(!(singleLegOrder.getClientDescriptor() != null && singleLegOrder.getClientDescriptor().getChannel() != null && singleLegOrder.getClientDescriptor().getChannel().contains("LOOP")) 
				&& (singleLegOrder.getOrderContingencies().size() == 0 || (toOMSOrg != null && 
				OrderConfiguration.getInstance().isLinkedOrderRouteToOMS(toOMSOrg.getShortName(), custOrg.getShortName())))){        	
    		String toOMSOrgName = null;
    		if ( toOMSOrg != null ) {
    			toOMSOrgName = toOMSOrg.getShortName();
    			List<Type> orderTypeRouteToOMS = OrderConfiguration.getInstance().getOrderTypeRouteToOMS(toOMSOrgName, custOrg.getShortName());
    			if ((singleLegOrder.getOrderContingencies().size() == 0 || !isParentMarketOrStopLmtOrder( singleLegOrder, parent )) 
    					&& orderTypeRouteToOMS.contains(singleLegOrder.getType())) {
    				long omsManualOrderAcceptanceExpiryTime = OrderConfiguration.getInstance().getOMSManualOrderAcceptanceExpiryTime(toOMSOrgName);
    				long expirtTime = singleLegOrder.getExpireTime() - singleLegOrder.getCreatedTime();
    				if((singleLegOrder.getTimeInForce().getShortName().equals(TimeInForce.GTD.getShortName()) && expirtTime >= omsManualOrderAcceptanceExpiryTime)
    						|| singleLegOrder.getTimeInForce().getShortName().equals(TimeInForce.GTC.getShortName())){
                        setAsOMSOrder(singleLegOrder, toOMSOrgName);
                    }
    			} else {
                    if (singleLegOrder.getExecutionInstructions() != null && singleLegOrder.getExecutionInstructions().getRoutingInstruction() != null &&
                            singleLegOrder.getExecutionInstructions().getRoutingInstruction().getTradingVenue() != null &&
                            singleLegOrder.getExecutionInstructions().getRoutingInstruction().getTradingVenue().equals(ISCommonConstants.EXECUTION_DESTINATION_OMS)) {
                        log.info("FXESPOrderRequestService.setOMSOrderExecutionFlags(): this order should go to OMS id," + singleLegOrder.getClientDescriptor().getClientReferenceId());
                        setAsOMSOrder(singleLegOrder, toOMSOrgName);
                    }
                }
    		}
    	}
        }
	}

    private void setAsOMSOrder(SingleLegOrder singleLegOrder, String toOMSOrgName) {
        singleLegOrder.getExecutionInstructions().getRoutingInstruction().setTradingVenue(toOMSOrgName);
        singleLegOrder.setExecutionFlags(singleLegOrder.getExecutionFlags() | ExecutionFlags.OMS_ORDER);
        singleLegOrder.getRequestLeg().setMinFillAmount(singleLegOrder.getRequestLeg().getAmount());
    }

    private boolean isParentMarketOrStopLmtOrder( SingleLegOrder singleLegOrder, WorkflowMessage parent ) {
		int objectIndex = 0;
		if(singleLegOrder.getOrderContingencies().size() > 1){
				objectIndex = 1;
		}
		ContingencyParameter contingencyParameter = (ContingencyParameter)((ArrayList)singleLegOrder.getOrderContingencies()).
							get(objectIndex);
		if(contingencyParameter.getType() == 4){
			return false;//No need to check for TPSL order
	}
		if(contingencyParameter.getPrimaryOrderId() == null){
			return false;//No need to check for primary order
		}
		SingleLegOrder primaryOrder = (SingleLegOrder) parent.getObject();
		if(primaryOrder.getType().equals(Type.MARKET) || primaryOrder.getType().equals(Type.STOPLIMIT)){
			return true;
		}
		return false;		
	}
	
	
	private void setExternalSpread( SingleLegOrder orderRequest ) {
		OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
		List<SpreadInfo> externalSpreads = requestLeg.getExternalSpreads();
		SpreadInfo externalSpread = null;
		if ( externalSpreads != null && !externalSpreads.isEmpty() ) {
			externalSpread = externalSpreads.get( 0 );
		}
		if ( externalSpread != null ) {
			FXRateBasis fxRateBasis = orderRequest.getFxRateBasis();
			if ( requestLeg.getSpotRate() > ISConstantsC.MIN_RATE ) {
				double revisedOrderRate = 0.0d;
				switch ( requestLeg.getBuySellMode() ) {
				case BUY:
					revisedOrderRate = MathUtil.round( requestLeg.getSpotRate() + externalSpread.getSpread(), fxRateBasis.getSpotPrecision(), BigDecimal.ROUND_CEILING );
					break;
				case SELL:
					revisedOrderRate = MathUtil.round( requestLeg.getSpotRate() - externalSpread.getSpread(), fxRateBasis.getSpotPrecision(), BigDecimal.ROUND_FLOOR );
					break;
				}
				requestLeg.setSpotRate( revisedOrderRate );
			}
			//Revise stop price
			if( orderRequest.getOrderTrigger().getTriggerRate() > ISConstantsC.MIN_RATE ){
				double revisedStopRate = 0.0d;
				switch ( requestLeg.getBuySellMode() ) {
				case BUY:
					revisedStopRate = MathUtil.round( orderRequest.getOrderTrigger().getTriggerRate() + externalSpread.getSpread(), fxRateBasis.getSpotPrecision(), BigDecimal.ROUND_CEILING );
					break;
				case SELL:
					revisedStopRate = MathUtil.round( orderRequest.getOrderTrigger().getTriggerRate() - externalSpread.getSpread(), fxRateBasis.getSpotPrecision(), BigDecimal.ROUND_FLOOR );
					break;
				}
				orderRequest.setStopLossTriggerRate( revisedStopRate );
			}
		}
	}

	private void setPersistantOrderRequest( SingleLegOrder orderRequest ) {
		if ( OrderServiceMBeanC.getInstance().isPersistentOrderEnabled() ) {
			if ( OrderServiceMBeanC.getInstance().getPeristentOrderClassificationNames().contains( orderRequest.getType().name() ) ) {
				orderRequest.setPersist( true );
			}
		}
	}

	private void setSubmissionTime( SingleLegOrder orderRequest ) {
		OrderRequestEventTimes eventTimes = orderRequest.getOrderRequestEventTimes();
		if ( eventTimes.getSubmissionTime() == 0 ) {
			eventTimes.setSubmissionTime( System.currentTimeMillis() );
		}
	}

	private void setOrderReplaceParameters( SingleLegOrder orderRequest, WorkflowMessage msg ) {
		String replacedOrderID = ( String ) msg.getParameterValue( ISConstantsC.REPLACED_ORDERID );
		if ( replacedOrderID != null ) {
			orderRequest.setReplacedId( replacedOrderID );
		}
	}

	private void setISMakerFlag( SingleLegOrder orderRequest ) {
		//Initialize to taker
		orderRequest.setMaker( false );
		//Only GTC/GTD/Day/GFS can be display orders.
		int execFlags = orderRequest.getExecutionFlags();
		//All display/broadcast orders are maker, other are taker
		if ( ( execFlags & ExecutionFlags.HIDDEN_ORDER ) != ExecutionFlags.HIDDEN_ORDER ) {
			orderRequest.setMaker( true );
		}
	}

	private void setOrderExpTime( SingleLegOrder orderRequest, WorkflowMessage msg ) {
		TimeInForce tif = orderRequest.getTimeInForce();
		if ( tif != null ) {
			switch (tif) {
			case GTD:
			case GFS:
				Object expiryTime = msg.getParameterValue( ISConstantsC.EXP_TIME );
				long expiryTimeinMillis = 0;
				if ( expiryTime != null ) {
					if ( expiryTime instanceof Long ) {
						expiryTimeinMillis = ( Long ) expiryTime;
					}
					else if ( expiryTime instanceof String ) {
						expiryTimeinMillis = Long.valueOf( expiryTime.toString() );
					}
					// this will be over written where strategy parameters are set for the
					// cases of delayed start (strategy order)
					orderRequest.setExpireTime( orderRequest.getOrderRequestEventTimes().getSubmissionTime() + expiryTimeinMillis );
				}
				break;
			}
		}
	}

	private void setExecutionFlags( SingleLegOrder orderRequest, WorkflowMessage msg ) {
		orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.ALLOW_ORDER_MATCH );

		if ( msg.getParameterValue( ISConstantsC.LP_CROSSING_ENBLD ) != null ) {
			orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.ALLOW_QUOTE_CROSS );
		}
		if ( orderRequest.getOrderVisibilityType().equals( OrderVisibility.Type.HIDDEN ) ) {
			orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.HIDDEN_ORDER );
		}
		TimeInForce tif = orderRequest.getTimeInForce();		
		if ((tif != null) && tif == TimeInForce.FOK ) {
			orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.AON );
		}
		String em = ( String ) msg.getParameterValue( ISCommonConstants.COVER_EXECUTION_METHOD );
		if ( em != null ) {
			orderRequest.getCoverExecutionDescriptor().setCoverExecutionMethod( em );
		}

		//This comes only from .NET client other client services set it to dealing price directly.
		String strExecInsts = ( String ) msg.getParameterValue( ISCommonConstants.WF_PARAM_ExecutionInstruction );
		boolean isSSPSet = false;
		if ( strExecInsts != null ) {
			String[] instructions = strExecInsts.split( "," );
			for ( String current : instructions ) {
				if ( ISCommonConstants.CROSS_WITH_LP.equals( current ) ) {
					orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.ALLOW_QUOTE_CROSS );
				}
				else if ( ISCommonConstants.PROVIDER_PRIORITY.equals( current ) ) {
					orderRequest.setSecondaryPricePriority( ExecutionInstructions.SecondaryPricePriority.SPP );
					isSSPSet = true;
				}
				else if ( ISCommonConstants.TIME_PRIORITY.equals( current ) ) {
					orderRequest.setSecondaryPricePriority( ExecutionInstructions.SecondaryPricePriority.SPT );
					isSSPSet = true;
				}
				else if ( ISCommonConstants.SIZE_PRIORITY.equals( current ) ) {
					orderRequest.setSecondaryPricePriority( ExecutionInstructions.SecondaryPricePriority.SPS );
					isSSPSet = true;
				}
				else if ( ISCommonConstants.AT_RATE_TRIGGER.equals( current ) ) {
					orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.AT_RATE );
				}
				else if ( ISCommonConstants.EXECINST_STRATEGY_ORDER.equals( current ) ) {
					orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.STRATEGY );
				}
				else {
					log.info( ISCommonConstants.WF_PARAM_ExecutionInstruction + " Skipped. Val - " + current );
				}
			}
		}else{
			if( orderRequest.getSecondaryPricePriority() != null){
				isSSPSet = true;
			}
		}
		
		if(!isSSPSet)
		{
            LiquidityProvision liqProv = ISUtilImpl.getLiquidityProvision(orderRequest.getOrganization(), orderRequest.getCurrencyPair());
            if( liqProv != null )
            {
            	Integer intssp = liqProv.getHierarchicalSecondarySortPriority();
            	if( intssp != null )
            	{
            		for( SecondaryPricePriority val : ExecutionInstructions.SecondaryPricePriority.values())
            		{
            			if( val.ordinal() == intssp )
            			{
            				orderRequest.setSecondaryPricePriority(val);
            				break;
            			}
            		}
            	}
            }
		}

		//All peg orders are strategy orders
		String pegType = ( String ) msg.getParameterValue( ISCommonConstants.PEGTYPE );
		if ( pegType != null ) {
			orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.STRATEGY );
		}

		if ( isTWAPOrder( msg ) ) {
			orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.TWAP );
			//defensive set in case some client service miss to set it.
			orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.STRATEGY );
		}
		else if ( isStrategyOrder( msg ) ) {
			orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.STRATEGY );
		}
		
		TargetStrategy targetStrategy = (TargetStrategy) msg.getParameterValue(ISCommonConstants.STRATEGY_TYPE);
		if ( targetStrategy != null  )
		{
			if(targetStrategy == TargetStrategy.LIT_SWITCH) {
				orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.LIT_SWITCH );
			}
			if(targetStrategy == TargetStrategy.DARK_SWITCH) {
				orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.DARK_SWITCH );
			}
			if(targetStrategy == TargetStrategy.FIXING_TWAP) {
				orderRequest.setExecutionFlags( orderRequest.getExecutionFlags() | ExecutionFlags.FIXING_TWAP_STRATEGY );
			}
		}

	}

	private String setMinFillAmt( SingleLegOrder orderRequest, WorkflowMessage msg ) {
        if (!ISUtilImpl.getInstance().isWarmUpObject(orderRequest)) {
            if(!orderRequest.isRiskNetDirectedOrder()) {
                Object minimumFillAmountObject = msg.getParameterValue(ISConstantsC.MIN_FILL_AMOUNT);
                if (minimumFillAmountObject == null) {
                    minimumFillAmountObject = orderRequest.getRequestLeg().getMinFillAmount();
                }
                Double liqProvMinFillSize = null;
                String orgName = orderRequest.getOrganization().getShortName();
                if (isMbean.isLiquidityProvisioningEnabled(orgName)) {
                    LiquidityProvision liqProv = ISUtilImpl.getLiquidityProvision(orderRequest.getOrganization(), orderRequest.getCurrencyPair());
                    liqProvMinFillSize = liqProv == null ? null : liqProv.getHierarchicalMinFillSize();
                }
                if (liqProvMinFillSize != null) {
                    if (minimumFillAmountObject != null) {
                        if (minimumFillAmountObject instanceof String) {
                            minimumFillAmountObject = Double.parseDouble(minimumFillAmountObject.toString());
                        }
                        minimumFillAmountObject = Math.max((Double) minimumFillAmountObject, liqProvMinFillSize);
                    } else {
                        minimumFillAmountObject = liqProvMinFillSize;
                    }
                }
                if (minimumFillAmountObject != null) {
                    double minimumFillAmt;
                    if (minimumFillAmountObject instanceof String) {
                        minimumFillAmountObject = Double.parseDouble(minimumFillAmountObject.toString());
                    }
                    minimumFillAmt = (Double) minimumFillAmountObject;

                    double dealtAmount = orderRequest.getRequestLeg().getAmount();
                    if (minimumFillAmt > dealtAmount || minimumFillAmt < 0) {
                        return ISConstantsC.REQUEST_VALIDATION_INVALID_MIN_QTY;
                    }
                    orderRequest.getRequestLeg().setMinFillAmount(minimumFillAmt);
                }
            }
        }
        return null;
    }

	private void setMaxShowAmt( SingleLegOrder orderRequest, WorkflowMessage msg ) {
		OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
		Object ds = msg.getParameterValue( ISConstantsC.CLIENT_DISPLAY_AMOUNT_KEY );
		boolean setDisplayAmount = ( ds != null );
		if ( setDisplayAmount ) {
			//TODO This should be moved to channel specific handler.
			log.info( new StringBuilder( 100 ).append( "ORS.setMaxShowAmt : Received display amount from client:" ).append( ds ).toString() );
			Double displayAmt = null;
			try {
				if ( ds instanceof String )//request from FXI client.
				{
					displayAmt = Double.parseDouble( ( String ) ds );
					//if false then it is Broadcast order setup for .NET.
					// Due to some bug in client 'DS' param may come as '0' for braodcast order
					String orgName = orderRequest.getUser().getOrganization().getShortName();
					if ( !orderConfigMBean.isDirectOASetup() && isMbean.isClientCrossingEnabled( orgName ) ) {
						if ( displayAmt == 0 ) {
							displayAmt = requestLeg.getAmount();
						}
					}
				}
				else {
					displayAmt = ( Double ) ds;
				}
			}
			catch ( Exception e ) {
				log.warn( "ORS.setMaxShowAmt : Error in TypeCasting display amount --> " + e );
			}
			if ( displayAmt != null ) {
				if ( displayAmt < MathUtilC.getMinAmount(orderRequest.getDealtCurrency())) {
					orderRequest.setOrderVisibilityType( OrderVisibility.Type.HIDDEN );
				}
				else if ( requestLeg.getAmount() - displayAmt > MathUtilC.getMinAmtLessDelta(orderRequest.getDealtCurrency()) ) {
					//Iceberg
					orderRequest.setOrderVisibilityType( OrderVisibility.Type.ICEBERG );
				}
				else {
					orderRequest.setOrderVisibilityType( OrderVisibility.Type.DISPLAY );
				}
				orderRequest.setMaxShowAmount( displayAmt );
				orderRequest.setOriginalMaxShowAmount( displayAmt );
			}
			else {
				//hidden
				orderRequest.setOrderVisibilityType( OrderVisibility.Type.HIDDEN );
			}
		}
		else {
			if ( orderRequest.getOrderVisibilityType() == null ) {
				orderRequest.setOrderVisibilityType( OrderVisibility.Type.DISPLAY );
				orderRequest.setMaxShowAmount( requestLeg.getAmount() );
				orderRequest.setOriginalMaxShowAmount( requestLeg.getAmount() );
			}
		}
	}

	private void setTimeInForce( SingleLegOrder orderRequest, WorkflowMessage wfMsg ) {
		if ( orderRequest.getTimeInForce() == null ) {
			String timeInForce = ( String ) wfMsg.getParameterValue( ISConstantsC.TIME_IN_FORCE );
			if ( timeInForce != null ) {
				orderRequest.setTimeInForce( timeInForce );
			}
		}
	}

	private void setMarketRange( SingleLegOrder orderRequest, WorkflowMessage msg ) {
		if ( SingleLegOrder.Type.MARKET.equals( orderRequest.getType() ) || SingleLegOrder.Type.STOP.equals( orderRequest.getType() ) ) {
			String marketRange = ( String ) msg.getParameterValue( ISConstantsC.MARKET_RANGE );
			String marketRange_abs = ( String ) msg.getParameterValue( ISConstantsC.MARKET_RANGE_ABS );
			if ( marketRange == null && marketRange_abs == null ) {
				orderRequest.setMarketRange( -1.0 );
				orderRequest.setMarketRangeOrder( false );
			}
			else {
				orderRequest.setMarketRangeOrder( true );
			}
		}
	}

	private void initialize( WorkflowMessage msg, SingleLegOrder orderRequest ) {
		if ( log.isDebugEnabled() ) {
			log.debug( "initialize : SingleLegOrder " + orderRequest );
		}
		OrderRequestEventTimes orderRequestEventTimes = orderRequest.getOrderRequestEventTimes();
		orderRequestEventTimes.setSentByConsumer( System.currentTimeMillis() );
		User user = msg.getSender();
		IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext();
		if ( ctx == null ) {
			orderWorkflowCalculator.setSessionContext( user );
			ctx = IdcSessionManager.getInstance().getSessionContext();
		}
		user = ( User ) ctx.getUser();
		State state = orderRequest.getState();
		state.setName( State.Name.valueOf( ISConstantsC.REQUEST_STATE_INIT ) );
		state.setTimestamp( System.currentTimeMillis() );
		state.setActor( user.getFullyQualifiedName() );
		if ( ISUtilImpl.getInstance().isWarmUpObject( msg ) ) {
			DealingModelUtil.setAsWarmUpObject( orderRequest );
		}
		String sLTriggerType = ( String ) msg.getParameterValue( ISConstantsC.SL_TRIGGER_TYPE );
		if ( sLTriggerType != null ) {
			orderRequest.setStopLossTriggerType( sLTriggerType );
		}
	}

	public WorkflowMessage getWithdrawRequestMessage( SingleLegOrder cancelledRequest, SingleLegOrder newRequest, WorkflowMessage msg )
	{
		WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
		wfm.setStatus( MessageStatus.SUCCESS );
		wfm.setTopic( FixConstants.MSG_TOPIC_REQUEST );
		wfm.setEvent( FixConstants.MSG_EVENT_WITHDRAW );
		wfm.setParameterValue( FixConstants.TRADE_TYPE, FixConstants.TRADE_TYPE_LIMIT_ORDERS );
		wfm.setObject( cancelledRequest );
		wfm.setSender( msg.getSender() );
		wfm.setParameterValue( OrderConstants.Key_ReplaceRequest, true );
		wfm.setParameterValue( FixConstants.ORDER_CANCEL_REPLACE_REQUEST_ID, newRequest.getClientReferenceId() );
		wfm.setParameterValue(FixConstants.WF_PARAM_USE_OPT_OCR_EXEC, msg.getParameterValue(FixConstants.WF_PARAM_USE_OPT_OCR_EXEC));
		return wfm;
	}

	public void updateMinFillFromLiquidyProvision(SingleLegOrder orderRequest, LiquidityProvision liqProv)
	{
        if(orderRequest.isRiskNetDirectedOrder()) return;

		Double originalMinFillAmount =orderRequest.getRequestLeg().getMinFillAmount();
		Double liqProvMinFillSize = null;
		if (isMbean.isLiquidityProvisioningEnabled(orderRequest.getOrganization().getShortName()))
		{
			if (liqProv != null)
			{
				liqProvMinFillSize = liqProv.getHierarchicalMinFillSize();
			}
		}

		Double minFillAmount = null;
		if (liqProvMinFillSize != null)
		{
			if (originalMinFillAmount != null)
			{
				minFillAmount = Math.max((Double)originalMinFillAmount, liqProvMinFillSize);
			}
			else
			{
				minFillAmount = liqProvMinFillSize;
			}
		}
		else
		{
			minFillAmount = originalMinFillAmount;
		}

		if (minFillAmount != null)
		{
			orderRequest.getRequestLeg().setMinFillAmount(minFillAmount);
		}
	}

	protected Collection<ContingencyParameter> getContingencyParameters(WorkflowMessage wfm) {
		if (wfm.isSpacesEnabled()) {
			OrderRequest orderRequest = (OrderRequest)wfm.getObject();
			return orderRequest.getOrderContingencies();
		} else {
			return super.getContingencyParameters(wfm);
		}
	};
	protected String getClientOrderId(WorkflowMessage wfm) {
		if (wfm.isSpacesEnabled()) {
			OrderRequest orderRequest = (OrderRequest)wfm.getObject();
			return orderRequest.getClientDescriptor().getClientReferenceId();
		} else {
			return super.getClientOrderId(wfm);
		}
	};

	private boolean isOCO(ContingencyParameter contingencyParam) {
		return contingencyParam.getType() == ContingencyType.OCO;
	}

	private boolean isOTO(ContingencyParameter contingencyParam) {
		return contingencyParam.getType() == ContingencyType.OTO;
	}
	
	protected void populateRequestGraph(List<WorkflowMessage> wfMsgs) 
	{
		if (wfMsgs.get(0).isSpacesEnabled()) 
		{
			DirectedGraph<OrderRequest> dg = null;
			HashMap<String, WorkflowMessage> map = indexOrderMessages(wfMsgs);
			// Sorted list expected
			for(WorkflowMessage wfm : wfMsgs) {
				OrderRequest req = (OrderRequest) wfm.getObject();
				if( req.get_id() == null )
				{
					req.set_id(newOrderId());
				}
				if( req.getCorrelationId() == null )
				{
					req.setCorrelationId(req.get_id());
				}
				
				Collection<ContingencyParameter> contParams = req.getOrderContingencies();
				for(ContingencyParameter cp : contParams) 
				{
					if (isOTO(cp)) 
					{
						//only primary order will have linked OrderIds. 
						//these whole things needs to be revisited if child node have leaf nodes as well.
						if(isLeaf(cp)) 
						{
							req.setOTOInactiveOrder(true);
							continue;
						}
						if (dg == null) 
						{
							dg = new DirectedGraph<OrderRequest>();
						}
						dg.addVertex(req);
						dg.setGroupId(newGroupId());
						cp.setGroupId(dg.getGroupId());
						req.getOrderRequestAttributes().setOtoRequestGraph(dg);
						
						
						// for each linked order id
						for(String clientOrderId : cp.getLinkedOrderIds()) 
						{
							WorkflowMessage msg = map.get(clientOrderId);
							OrderRequest linkedRequest = (OrderRequest) msg.getObject();
							if(linkedRequest.get_id() == null)
							{
								linkedRequest.set_id(newOrderId());
							}
							linkedRequest.setCorrelationId(req.getCorrelationId());
							linkedRequest.getOrderRequestAttributes().setOtoRequestGraph(dg);
							dg.addEdge(req, linkedRequest);
							
							//add order id to contingency parameter for GM. in case of OTO only primary
							//order's CP will have LinkedOMSIds. In GM it is the list of orders which this order can trigger.
							cp.addLinkedOMSId(linkedRequest.get_id());
							//Assign a group id to child orders. 
							for( ContingencyParameter childCP : linkedRequest.getOrderContingencies()) 
							{
								if (isOTO(childCP)) 
								{
									childCP.setGroupId(dg.getGroupId());
									childCP.setPrimaryOrderId( req.get_id() );
								}
							}
						}
					}
					else if(isOcoOrOuo(cp)) {
						final boolean isOCO = isOCO(cp); 
						//As of now an order can join only one of the the group. OCO or OUO.
						if(isNull(cp.getGroupId())) {
							final String groupId = newGroupId();
							cp.setGroupId(groupId);
							OCOGroupLock lock = isOCO ? new OCOGroupLock(groupId) : new OUOGroupLock(groupId) ;
							if( isOCO )
							{
								req.getOrderRequestAttributes().setOcoGroupLock(lock);
							}
							else
							{
								req.getOrderRequestAttributes().setOuoGroupLock(lock);
							}
							lock.addOrderId(req.get_id());
							assignGroupIds(map, cp, lock, req.getCorrelationId());
						}
						else
						{
							OCOGroupLock lock = (OCOGroupLock) (isOCO ? req.getOrderRequestAttributes().getOcoGroupLock() : req.getOrderRequestAttributes().getOuoGroupLock()) ;
							assignGroupIds(map, cp, lock, req.getCorrelationId());
						}
					}
				}
			}

		} else {
			super.populateRequestGraph(wfMsgs);
		}
	}

	/*
	 * This method is applicable to only OCO/OUO groups.
	 */
	private void assignGroupIds(HashMap<String, WorkflowMessage> map, ContingencyParameter cp, OCOGroupLock lock, String correlationId) {
		//Assign same groupId to other linked orders. 
		for(String clientOrderId : cp.getLinkedOrderIds()) {
			WorkflowMessage msg = map.get(clientOrderId);
			OrderRequest linkedRequest = (OrderRequest) msg.getObject();
			for(ContingencyParameter childCP : linkedRequest.getOrderContingencies()) {
				if (!isOTO(childCP)) {
					if(isNull(childCP.getGroupId())) {
						if( linkedRequest.get_id() == null )
						{
							linkedRequest.set_id(newOrderId());
						}
						linkedRequest.setCorrelationId(correlationId);
						childCP.setGroupId(cp.getGroupId());
						boolean isOCO = isOCO(childCP);
						if( isOCO )
						{
							linkedRequest.getOrderRequestAttributes().setOcoGroupLock(lock);
						}
						else
						{
							linkedRequest.getOrderRequestAttributes().setOuoGroupLock(lock);
						}
						lock.addOrderId(linkedRequest.get_id());
					}
					else if (!childCP.getGroupId().equalsIgnoreCase(cp.getGroupId())) {
						log.warn("Invalid input . Order can't be part of two non-OTO contingent groups. Id="+linkedRequest.getClientDescriptor().getClientReferenceId());
					}
				}
			}
		}
	}

	private String newGroupId() {
		return ISFactory.getInstance().getISOrderIdFacadeC().getID("IS");
	}
	
	private String newOrderId() {
		return ISFactory.getInstance().getISOrderIdFacadeC().getID("IS");
	}

	private boolean isLeaf(ContingencyParameter cp) {
		return cp.getLinkedOrderIds() == null || cp.getLinkedOrderIds().isEmpty() ;
	}

	private boolean isOcoOrOuo(ContingencyParameter cp) {
		return cp.getType() == ContingencyType.OCO || cp.getType() == ContingencyType.OUO_ABSOLUTE 
				|| cp.getType() == ContingencyType.OUO_PROPORTIONAL;
	}
	
	private boolean isOuo(ContingencyParameter cp) {
		return cp.getType() == ContingencyType.OUO_ABSOLUTE 
				|| cp.getType() == ContingencyType.OUO_PROPORTIONAL;
	}

	protected void markRequestInactive(WorkflowMessage wfm) {
		if (wfm.isSpacesEnabled()) {
			OrderRequest orderRequest = (OrderRequest)wfm.getObject();
			orderRequest.setReloadAsInActive(true);
		} else {
			super.markRequestInactive(wfm);
		}
	}
	
	protected void setOMSOrderIds(WorkflowMessage wfm, ContingencyParameter cp)
	{
		if (wfm.isSpacesEnabled()) {
			OrderRequest orderRequest = (OrderRequest)wfm.getObject();
			OCOGroupLock lock = (OCOGroupLock) (cp.getType() == ContingencyType.OCO ? orderRequest.getOrderRequestAttributes().getOcoGroupLock() : orderRequest.getOrderRequestAttributes().getOuoGroupLock()); 
			cp.setLinkedOMSIds(OMSUtilC.getOrderIdsCSS(lock.getOrders()));
			orderRequest.setReloadAsInActive(true);
		} else {
			super.setOMSOrderIds(wfm, cp);
		}
	}
	
	
	protected boolean processCreateOrderRequestList( List<WorkflowMessage> msgs, long receivedTimeNanos, long receivedTimeMillis, int contingencyType , boolean isSpacesEnabled )
	{
		if( !isSpacesEnabled )
		{
			return super.processCreateOrderRequestList( msgs, receivedTimeNanos, receivedTimeMillis, contingencyType, isSpacesEnabled );
		}
		
		boolean isSuccess = true;
		try
		{
			long t0 = System.nanoTime();
			//enable the log bit for validating the SQLs that get generated if server is not in warmup period.
			if ( !serverMBean.isServerWarmingUp() )
			{
				LogUtil.setSwitch(true);
			}

			BitSet failures = new BitSet(msgs.size());

			for ( int i=0; i <  msgs.size() ; i++ )
			{
				WorkflowMessage msg = msgs.get(i);
				Object object = msg.getObject();
				long t1 = System.nanoTime();
				initializeNewRequest(msg);
				long t2 = System.nanoTime();

				addOrderWFProcessingTime(object, "ORS.prs_1", (t1 - t0));
				addOrderWFProcessingTime(object, "ORS.prs_2", (t2 - t1));

				SingleLegOrder request = (SingleLegOrder) msg.getObject();
				long t3 = System.nanoTime();

				// set the virtual server name on the request.
				request.setVirtualServer(ConfigurationFactory.getServerMBean().getVirtualServerName());
				OrderRequestEventTimes eventTimes = request.getOrderRequestEventTimes();
				eventTimes.setReceivedTimeNano( receivedTimeNanos );
				eventTimes.setSubmissionTimeNano( receivedTimeNanos );
				eventTimes.setReceivedTime( receivedTimeMillis );
				request.setUser(msg.getSender());

				long t4 = System.nanoTime();
				setParametersonRequest(request, msg, msgs.get(0));
				long t5 = System.nanoTime();
				msg = validateAndSetParameters(request, msg, true);
				long t6 = System.nanoTime();
				String errorCode = validateOCOOUOPermission(msg, contingencyType);
				
				if ( errorCode != null )
				{
					log.info("ORS.validateOCOOUOPermission.INFO : Contingent order validation errors --  " + errorCode);
					WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(request, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
					addError(wfMsg, errorCode);
					msg.setReplyMessage(wfMsg);
					//return false;
				}

				if (msg.getReplyMessage() != null && msg.getReplyMessage().getStatus() == MessageStatus.FAILURE) {
					failures.set(i);
                    WorkflowMessage responseMsg = (WorkflowMessage) msg.getReplyMessage();
                    responseMsg.setParameterValue(ISConstantsC.IS_ORDER_ID, request.get_id());
                    StringBuilder sb = ISUtilImpl.getInstance().getErrMessage(responseMsg.getErrors());
					String rejectCode = ISUtilImpl.getInstance().getFirstErrorCode(responseMsg.getErrors());
                    orderWorkflowHandler.orderFailed(request, rejectCode, sb.toString());
				}

				addOrderWFProcessingTime(object,"ORS.pCOR1", (t4 - t3));
				addOrderWFProcessingTime(object,"ORS.sPR", (t5 - t4));
				addOrderWFProcessingTime(object,"ORS.vASP", (t6 - t5));
			}

			if( !failures.isEmpty() )
			{
				log.warn("processCreateOrderRequestList - one or more orders failed validation check. rejecting list.");
				for (int i = 0; i < msgs.size() ; i++)
				{
					WorkflowMessage msg = msgs.get(i);
					if ( msg.getReplyMessage() == null || msg.getReplyMessage().getStatus() == MessageStatus.SUCCESS )
					{
						//reply will be null if message is successful.
						if( msg.getReplyMessage() == null )
						{
							WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( msg.getObject(), ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST );
							msg.setReplyMessage( wfMsg );
						}
						log.info("Setting status to fail for successful requests.");
						msg.getReplyMessage().addError("One of component request in list failed.");
						msg.getReplyMessage().setStatus(MessageStatus.FAILURE);
					}
				}
				isSuccess = false;
				return isSuccess;
			}

			for ( WorkflowMessage msg : msgs )
			{
				long t7 = System.nanoTime();
				SingleLegOrder request = (SingleLegOrder) msg.getObject();
				//main
				//for spaces wiring is already done in preprocess method this is not required.
				//FXESPOrderRequestValidator.validateAndSetContingentRequestGroup(request, msg);
				long t8 = System.nanoTime();
				msg = _submitRequest(request, msg,true);
				long t9 = System.nanoTime();

				addOrderWFProcessingTime(request,"ORS.vAsCRG", (t8 - t7));
				addOrderWFProcessingTime(request,"ORS.sR", (t9 - t8));
			}

			return isSuccess;
		}
		finally
		{
			LogUtil.removeSwitch();
		}
	}

    protected  WorkflowMessage processConfirmTradeMessage( WorkflowMessage message ){
        return FXESPOrderRequestValidator.getInstance().validateConfirmTradeMessage( message );
    }

    protected WorkflowMessage processValidateOrderTradeMessage(WorkflowMessage message){
        return FXESPOrderRequestValidator.getInstance().validateOrderTradeMessage( message );
    }

    protected WorkflowMessage processDontKnowTrade( WorkflowMessage msg )
    {
        SingleLegTrade trade = (SingleLegTrade) msg.getObject();
		StringBuilder sb = new StringBuilder(300);
		sb.append("Trade verification not acknowledged. ");
		sb.append("tId=").append(trade.get_id());
		sb.append(", user=").append(trade.getUserShortName()).append('@').append(trade.getUserOrgShortName());
		MessageLogger.getInstance().log(ISAlertMBean.TRADE_DONT_KNOW_TRADE,this.getClass().getName(),sb.toString(),null);

        log.info("FXESPORS.processDontKnowTrade.INFO : Request Details : " + trade.get_id());
		PersistenceServiceFactory.getISSpacesPersistenceService().publish( trade, ApplicationEventCodes.EVENT_ESP_TRADE_DONTKNOW, "VERIFIED_NOT_ACKNOWLEDGED" );
        return msg;
    }

    protected WorkflowMessage processOrderTimeout( WorkflowMessage msg )
    {
        SingleLegOrder order = (SingleLegOrder) msg.getObject();

        log.info("ORS.processOrderTimeout.INFO : Request Details : " + order.getTransactionId());

        order.setTimedOut( true );
        //Sending Risk Email and GM notifications are handled by notification server
        PersistenceServiceFactory.getISSpacesPersistenceService().persist( order, ApplicationEventCodes.EVENT_ESP_ORDER_TIMEDOUT, "OrderTimedOut" );

        //handler this via notification server
        //Generate Risk Position mail
        //ISEmailUtil.getInstance().sendOrderTimeoutNotification(request);

        //Send OrderMonitor object message to GM
        //OrderRuntimeMonitor.getInstance().notifyEvent(request, ISConstantsC.MSG_EVENT_ORDER_TIMEOUT, "Order Timeout received");

        WorkflowMessage wfm = ISUtilImpl.getInstance().createWorkflowMessage(order, ISConstantsC.MSG_EVT_ORDER_TIMEOUT, ISConstantsC.MSG_TOPIC_REQUEST);
        wfm.setReplyMessage(wfm);
        return wfm;

    }

    protected void setCustomerStreamId( final OrderRequest orderRequest )
    {
        final Organization brokerOrg = orderRequest.getOrganization().getBrokerOrganization();
        if ( brokerOrg != null )
        {
        	if( orderRequest.isPQDirectedOrder() ){
				orderRequest.setStream(PQServiceFactory.getPQWorkflowService().getStream(brokerOrg,orderRequest.getLegalEntity(),orderRequest.getClientDescriptor().getReferenceQuoteId()));
				if( orderRequest.getStream() != null ){
					return;
				}
			}
            TradingParty tp = CounterpartyUtilC.getTradingParty( orderRequest.getLegalEntity(), brokerOrg );
            if ( tp != null )
            {
                final Stream brokerStream = tp.getBrokerStream();
                if ( brokerStream != null )
                {
                    orderRequest.setStream( brokerStream );
                }
                else
                {
                    final BrokerOrganizationFunction bof = brokerOrg.getBrokerOrganizationFunction();
                    if ( bof != null )
                    {
                        orderRequest.setStream( bof.getDefaultStream() );
                    }
                }
            }
        }
    }
}
