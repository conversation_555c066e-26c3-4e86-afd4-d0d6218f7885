package com.integral.broker.rfs.subscribe;

import java.util.Map;

import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.ExecutionMethod;
import com.integral.broker.model.ExecutionRuleCondition;
import com.integral.broker.model.PricingStrategy;
import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.broker.model.TenorSpread;
import com.integral.broker.quote.MDSPriceFacade;
import com.integral.broker.rfs.RFSValidationException;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.trade.Tenor;
import com.integral.is.message.rfs.RFSFXLeg;
import com.integral.is.message.rfs.RFSSubscribe;
import com.integral.scheduler.Expiration;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;

public interface RFSRequestFacade {

	String TRADER = "Trader";

	RFSSubscribe getRFSSubscribe();

	Organization getBroker();

	BrokerOrganizationFunction getBrokerFunction()
			throws IdcNoSuchObjectException;

	Stream getStream() throws IdcNoSuchObjectException;

	CurrencyPair getCurrencyPair();

	Product getProduct() throws IdcNoSuchObjectException;

	// seems this is Swap specific, doesn't seem called in SSP flow
	boolean isTenorSet();

	boolean isTenorSet(int i);

	Tenor getFixingTenor();

	IdcDate getFixingDate();

	Tenor getFixingTenorFar();

	IdcDate getFixingDateFar();

	Tenor getTenor();

	Tenor getTenor(int i);

	boolean isValueDateSet();

	IdcDate getValueDate();

	IdcDate getVariableValueDate();

	Tenor getVariableTenor();

	boolean isVariableTenorSet();

	boolean isVariableValueDateSet();

	boolean isWindowForwardRequest() ;

	Currency getDealtCurrency();

	Currency getSettledCurrency();

	String getDealtCurrencyProperty();

	// convert an arbitrary amount of dealt to base, avoids duplicating a get*AmountInBase() for any get*DealAmount()
	double getDealtInBase(double amount);

	// this is only used for setting up ESP product backing the ESP+MDS publishing
	// note tiered ESP aggs ( wAvg will do isFilled() test before getting first price => never gets a price if requested amt is 0 )
	// note todo this gets used to construct synthetic ESP product tiers but getESPPriceDealtAmount() is what is required by ESPRFSPublisher => exposure to mismatch - clean up the concepts here.
	double getESPMDSProductDealtAmount();

	// todo SSPlater no practical impl as even 0 will get first price, but preSSP code uses max(near, far) for subscribe (see getESPMDSProductDealtAmount() ) but only near for deriving the price
	double getESPPriceDealtAmount();

	// Bid is +ve, Offer is -ve
	double getNetSignedAmountSSP();

	double getNetAmountSSP();

	double getNetAmount();

	double getNetAmountInBase();

	double getESPInflateDealtAmount();

	double getDealtAmount();

	double getDealtAmountInBase();

	double getFarDealtAmount();

	double getFarDealtAmountInBase();

	// this respects SSP
	int getBidOfferMode();

	// return the mode based on the netted trade - I think this *should* be the same as the getBidOfferMode() that IS sets, check 'boundary' case netSpot == 0
	int getBidOfferModeNetSpot();

	int getSSPBidOfferMode();

	int getFarBidOfferMode();

	boolean isFarTenorSet();

	Tenor getFarTenor();

	boolean isFarValueDateSet();

	IdcDate getFarValueDate();

	boolean isNearTenorSpot();

	boolean isNearTenorLessThanSpot();

	Integer getSpotLag();

	boolean isNearTenorEqualsTradeDate();

	boolean isTomAndSpotTenorSameValueDate();

	boolean isNearTenor(Tenor t);

	// introduced for  59768
	boolean isFarTenor(Tenor t);

	boolean isFarTenorSpot();

	Expiration getExpiration();

	String getSubscriptionId();

	boolean isTraderRequest();

	LegalEntity getCustomerLegalEntity();

	TradingParty getCustomerTradingParty();

	Organization getRequestingOrganization();

	User getUser();

	boolean isSwap();

	boolean isNDF();

	boolean isNDFSwap();

	TenorSpread getTenorSpread();

	TenorSpread getOTRTenorSpread(Tenor specifiedTenor,
			IdcDate specifiedValueDate);

	String getTradeClassification();

	IdcDate getCalculatedNearValueDate();

	IdcDate getCalculatedVariableNearValueDate();

	IdcDate getCalculatedFarValueDate();

	/**
	 * Any Swap trade that is Pre Spot
	 * ie TOD/TOM, TOD/SPOT, TOM/SPOT
	 *
	 * @return
	 */
	boolean isPreSpotSwap();

    /**
     * type of FSR where near leg is not SPOT
     * @return
     */
	boolean isNonSpotNearLegFSR();

    /**
     * type of FSR where Far leg is fixed and customer sends Fixed Far leg instead of Fixed near leg
     * @return
     */
    boolean isFixedFarLegFSR();

	IdcDate getCalculatedValueDate(RFSFXLeg leg);

	IdcDate getValueDateFromTenor(Tenor tenor);

	PricingStrategy getPricingStrategy()
			throws IdcNoSuchObjectException, RFSValidationException;

	ExecutionRuleCondition getExecutionRuleCondition();

	ExecutionMethod getExecutionMethod()
			throws IdcNoSuchObjectException, RFSValidationException;

	String getDealingChannel();

	long getCreateTime();

	boolean isFSRRequest();

	boolean isSSPRequest();

    boolean isUDBRequest();

	double getBidSpotRate();

	double getOfferSpotRate();

    double getBidFarRate();

    double getOfferFarRate();

	String getMT300Field72();


    boolean isMisMatchSwap();

    boolean isMatchSwap();

	double getMisMatchNetAmount();

	MDSPriceFacade getMdsPriceFacade()
			throws IdcNoSuchObjectException, RFSValidationException;
	double getDealtAmountGrossInBase();
	boolean hasPercentSwapPointSpread();

	boolean isDealerInterventionEnabled();

	void setExecutionRuleCondition(ExecutionRuleCondition executionRuleCondition);

	Map<String, String> getValidationFailures();

	boolean isManualRquest();

	boolean isOutright();

	boolean isSpotAndSwapEnabled();

	boolean isCoverOnSpotAndSwapEnabled();

	double getFwdPointsDifference();

	void setFwdPointsDifference(double fwdPointsDifference);

	boolean isVariableDateFwdPointsSelected();

	void setVariableDateFwdPointsSelected( boolean flag );

	boolean isMakerCreditOverride();

	void setMakerCreditOverride(boolean makerCreditOverride);

	String getMakerCreditOverrideUser();
	void setMakerCreditOverrideUser(String makerCreditOverrideUser);

	boolean isSentToManual();

	void setSentToManual( boolean flag );
	boolean isFwdFwdNearLegWithInSpotFarLegBeyondSpot();

	boolean isProrataForwardRequest() ;
}