// Copyright (c) 2007 Integral Development Corp. All rights reserved.
package com.integral.broker.rfs.subscribe;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.integral.broker.BrokerAdaptorFactory;
import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.calculator.ExecutionRuleConditionCalculator;
import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.broker.model.*;
import com.integral.broker.quote.MDSPriceFacade;
import com.integral.broker.quote.MDSPriceFacadeC;
import com.integral.broker.rfs.RFSValidationException;
import com.integral.broker.util.TenorBasedSpreadCalculationService;
import com.integral.exception.IdcMissingArgumentException;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.trade.Tenor;
import com.integral.is.ISCommonConstants;
import com.integral.is.broker.BrokerWorkflowFunctorC;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.MDSFactory;
import com.integral.is.common.rfs.RFSConstants;
import com.integral.is.message.rfs.RFSFXLeg;
import com.integral.is.message.rfs.RFSFXRate;
import com.integral.is.message.rfs.RFSSubscribe;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.config.MarketMakerConfigMBean;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.scheduler.Expiration;
import com.integral.scheduler.ExpirationC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.MathUtilC;

/**
 * Converts object identifiers in {@link com.integral.is.message.rfs.RFSSubscribe} into domain objects.
 *
 * <AUTHOR> Development Corp.
 */
public class RFSRequestFacadeC implements RFSRequestFacade {
    /**
     * Logger for this class and its descendants.
     */
    protected Log log = LogFactory.getLog( this.getClass() );

    private final RFSSubscribe rfsSubscribe;
    private transient Organization broker;
    private transient BrokerOrganizationFunction brokerFunction;
    private transient Stream stream;
    private transient CurrencyPair currencyPair;
    private transient Product product;
    private transient Tenor tenor;
    private transient IdcDate valueDate;
    private transient Tenor variableTenor;
    private transient IdcDate variableValueDate;
    private transient Currency dealtCurrency;
    private transient String dealtCurrencyProperty;
    private transient Currency settledCurrency;
    private transient Double dealtAmount;
    private transient Double dealtAmountInBase;
    private transient Double farDealtAmount;
    private transient Integer bidOfferMode;
    private transient Integer farBidOfferMode;
    private transient Tenor farTenor;
    private transient IdcDate farValueDate;
    private transient IdcDate fixingDate;
    private transient Tenor fixingTenor;
    private transient IdcDate fixingDateFar;
    private transient Tenor fixingTenorFar;
    private transient Expiration expiration;
    private transient LegalEntity customerLE;
    private transient TradingParty customerTP;
    private transient Boolean isSwap;
    private transient Boolean isPreSpotSwap;
    private transient Boolean isNonSpotNearLegFSR;
    private transient Boolean isFixedFarLegFSR;
    
    private transient User customer;
    private TenorSpread tenorSpread;
    private Boolean isNearTenorSpot;
    private Boolean isNearTenorLessThanSpot;
    private Boolean isNearTenorEqualsTradeDate;
    private Boolean isFarTenorSpot;
    private IdcDate calculatedNearValueDate;
    private IdcDate calculatedVariableNearValueDate;
    private IdcDate calculatedFarValueDate;
    private ExecutionRuleCondition executionRuleCondition;
    private Organization requestingOrganization;
    private Double farDealtAmountInBase;
    private final long createTime;
    private Boolean isFSRRequest;
    private Boolean isSSPRequest;
    private Boolean isWindowForwardRequest;
    private boolean makerCreditOverride;
    private Double bidSpotRate;
    private Double offerSpotRate;
    private Double bidFarRate;
    private Double offerFarRate;
    private MDSPriceFacade mdsPriceFacade;
    private Map<String, String> validationFailures;
    private transient Boolean isOutright;
    private double fwdPointsDifference;
    private String makerCreditOverrideUser;
    private boolean sentToManual;
    private boolean variableDateFwdPointsSelected;

    private Boolean isProrataForwardRequest;

    public RFSRequestFacadeC( RFSSubscribe rfsSubscribe ) throws IdcMissingArgumentException {
        if ( rfsSubscribe == null ) {
            throw new IdcMissingArgumentException( "RFSSubscribe is required." );
        }
        createTime = System.currentTimeMillis();
        this.rfsSubscribe = rfsSubscribe;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getRFSSubscribe()
	 */
    @Override
	public RFSSubscribe getRFSSubscribe() {
        return rfsSubscribe;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getBroker()
	 */
    @Override
	public Organization getBroker() {
        if ( broker == null ) {
            String name = rfsSubscribe.getProviderShortName();
            broker = ReferenceDataCacheC.getInstance().getOrganization(name);

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getBroker " );
                sb.append( "{ name = " ).append( name );
                sb.append( ", broker = " ).append( broker );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( '}' );
                log.debug( sb.toString() );
            }
        }
        return broker;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getBrokerFunction()
	 */
    @Override
	public BrokerOrganizationFunction getBrokerFunction() {
        if ( brokerFunction == null ) {
            Organization broker = getBroker();
            String name = BrokerOrganizationFunction.ORG_FUNC_NAME;
            brokerFunction = broker.getBrokerOrganizationFunction();

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getBrokerFunction " );
                sb.append( "{ broker = " ).append( broker );
                sb.append( ", name = " ).append( name );
                sb.append( ", brokerFunction = " ).append( brokerFunction );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( '}' );
                log.debug( sb.toString() );
            }
        }
        return brokerFunction;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getStream()
	 */
    @Override
	public Stream getStream() {
        if ( stream == null ) {
            String streamId = rfsSubscribe.getStreamId();
            BrokerOrganizationFunction function = getBrokerFunction();
            stream = function.getStream(streamId);

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getStream " );
                sb.append( "{ streamId = " ).append( streamId );
                sb.append( ", function = " ).append( function );
                sb.append( ", stream = " ).append( stream );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return stream;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getCurrencyPair()
	 */
    @Override
	public CurrencyPair getCurrencyPair() {
        if ( currencyPair == null ) {
            String baseCcy = rfsSubscribe.getBaseCurrency();
            String variableCcy = rfsSubscribe.getVariableCurrency();
            currencyPair = CurrencyFactory.newCurrencyPair(baseCcy, variableCcy);

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getCurrencyPair " );
                sb.append( "{ baseCcy = " ).append( baseCcy );
                sb.append( ", variableCcy = " ).append( variableCcy );
                sb.append( ", currencyPair = " ).append( currencyPair );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( '}' );
                log.debug( sb.toString() );
            }
        }
        return currencyPair;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getProduct()
	 */
    @Override
	public Product getProduct() {
        if ( product == null ) {
            Stream stream = getStream();
            CurrencyPair ccyPair = getCurrencyPair();

            product = stream.getProduct( ccyPair, false );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getProduct " );
                sb.append( "{ stream = " ).append( stream );
                sb.append( ", ccyPair = " ).append( ccyPair );
                sb.append( ", product = " ).append( product );
                if ( product != null ) {
                    sb.append( ", isChannelOverrideEnabled = " )
                            .append( product.getConfiguration().isChannelOverrideEnabled() );
                    sb.append( ", isESPEnabled = " ).append( product.getConfiguration().isESPEnabled() );
                    sb.append( ", isRFSEnabled = " ).append( product.getConfiguration().isRFSEnabled() );
                }
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return product;
    }

    // seems this is Swap specific, doesn't seem called in SSP flow
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isTenorSet()
	 */
    @Override
	public boolean isTenorSet() { // note SSP invalid for ssp, leave it unsafe and see if it's ever called, testing manual & auto indicates not
        return rfsSubscribe.getNearLeg().getTenor() != null;
    }
    
    
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isVariableTenorSet()
	 */
    @Override
	public boolean isVariableTenorSet() { // for Window Forward
    	return rfsSubscribe.getNearLeg().getVariableTenor() != null;    	
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isTenorSet(int)
	 */
    @Override
	public boolean isTenorSet(int i) {
        return rfsSubscribe.getTradeLegs().get(i).getTenor() != null;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getFixingTenor()
	 */
    @Override
	public Tenor getFixingTenor() {
        if(isSSPRequest()) {
            if (fixingTenor == null){
                String tenorName = rfsSubscribe.getTradeLegs().get(0).getFixingTenor();
                if (tenorName != null){
                    fixingTenor = new Tenor(tenorName);
                }
            }
        } else {
            if (fixingTenor == null){
                String tenorName = rfsSubscribe.getNearLeg().getFixingTenor();
                if (tenorName != null){
                    fixingTenor = new Tenor(tenorName);
                }
            }
        }
        return fixingTenor;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getFixingDate()
	 */
    @Override
	public IdcDate getFixingDate(){
        if(isSSPRequest()){
            if (fixingDate == null){
                Date date = rfsSubscribe.getTradeLegs().get(0).getFixingDate();
                fixingDate = date == null ? null : DateTimeFactory.newDate(date);
            }
        } else {
            if (fixingDate == null){
                Date date = rfsSubscribe.getNearLeg().getFixingDate();
                fixingDate = date == null ? null : DateTimeFactory.newDate(date);
            }
        }
        return fixingDate;
    }
    
	public Tenor getFixingTenorFar(){
        if (fixingTenorFar == null && rfsSubscribe.getFarLeg()!=null){
            String tenorName = rfsSubscribe.getFarLeg().getFixingTenor();
            if (tenorName != null){
                fixingTenorFar = new Tenor(tenorName);
            }
        }
        return fixingTenorFar;		
	}

	public IdcDate getFixingDateFar(){
        if (fixingDateFar == null && rfsSubscribe.getFarLeg()!=null){        	
            Date date = rfsSubscribe.getFarLeg().getFixingDate();
            fixingDateFar = date == null ? null : DateTimeFactory.newDate(date);
        }
        return fixingDateFar;
	}
	
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getTenor()
	 */
    @Override
	public Tenor getTenor() {
        if ( tenor == null && isTenorSet()) {
            String tenorName = rfsSubscribe.getNearLeg().getTenor();
            tenor = new Tenor( tenorName );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getTenor " );
                sb.append( "{ tenor = " ).append( tenor );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return tenor;
    }
    
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getVariableTenor()
	 */
    @Override
	public Tenor getVariableTenor() {
        if ( variableTenor == null && isVariableTenorSet()) {
        	String tenorName = rfsSubscribe.getNearLeg().getVariableTenor();           
            variableTenor = new Tenor( tenorName );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getVariableTenor " );
                sb.append( "{ variableTenor = " ).append( variableTenor );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return variableTenor;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getTenor(int)
	 */
    @Override
	public Tenor getTenor(int i) {
        if ( tenor == null && isTenorSet(i)) {
            String tenorName = rfsSubscribe.getTradeLegs().get(i).getTenor();
            tenor = new Tenor( tenorName );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getTenor " );
                sb.append( "{ tenor = " ).append( tenor );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return tenor;
    }


    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isValueDateSet()
	 */
    @Override
	public boolean isValueDateSet() {
        return rfsSubscribe.getNearLeg().getValueDate() != null;
    }
    
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isVariableValueDateSet()
	 */
    @Override
	public boolean isVariableValueDateSet() {
    	return rfsSubscribe.getNearLeg().getVariableValueDate() != null;       
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getValueDate()
	 */
    @Override
	public IdcDate getValueDate() {
        if ( valueDate == null && isValueDateSet()) {
            Date jdkDate = rfsSubscribe.getNearLeg().getValueDate();
            valueDate = DateTimeFactory.newDate(jdkDate);

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getValueDate " );
                sb.append( "{ valueDate = " ).append( valueDate );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return valueDate;
    }
    
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getVariableValueDate()
	 */
    @Override
	public IdcDate getVariableValueDate() {
        if ( variableValueDate == null && isVariableValueDateSet()) {
        	Date jdkDate = rfsSubscribe.getNearLeg().getVariableValueDate();
            variableValueDate = DateTimeFactory.newDate(jdkDate);

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getVariableValueDate " );
                sb.append( "{ variableValueDate = " ).append( variableValueDate );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return variableValueDate;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getDealtCurrency()
	 */
    @Override
	public Currency getDealtCurrency() {
        if(isSSPRequest()) {
            if ( dealtCurrency == null ) {
                // todo perhaps add a getAnyLeg() for the cases where it doesn't matter.
                String dealtCurrencyName = rfsSubscribe.getTradeLegs().get(0).getDealtCurrency(); // any leg will do ...
                dealtCurrency = CurrencyFactory.getCurrency( dealtCurrencyName );


                if ( log.isDebugEnabled() ) {
                    StringBuilder sb = new StringBuilder( 100 );
                    sb.append( this ).append( ".getDealtCurrency " );
                    sb.append( "{ dealtCurrency = " ).append( dealtCurrency );
                    sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                    sb.append( " }" );
                    log.debug( sb.toString() );
                }
            }
        } else {
            if ( dealtCurrency == null ) {
                String dealtCurrencyName = rfsSubscribe.getNearLeg().getDealtCurrency();
                dealtCurrency = CurrencyFactory.getCurrency( dealtCurrencyName );

                if ( log.isDebugEnabled() ) {
                    StringBuilder sb = new StringBuilder( 100 );
                    sb.append( this ).append( ".getDealtCurrency " );
                    sb.append( "{ dealtCurrency = " ).append( dealtCurrency );
                    sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                    sb.append( " }" );
                    log.debug( sb.toString() );
                }
            }
        }

        return dealtCurrency;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getSettledCurrency()
	 */
    @Override
	public Currency getSettledCurrency() {
        if ( settledCurrency == null ) {
            settledCurrency = BrokerAdaptorUtil.getInstance()
                    .getSettledCcy( getCurrencyPair(), getDealtCurrency() );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getSettledCurrency " );
                sb.append( "{ settledCurrency = " ).append( settledCurrency );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return settledCurrency;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getDealtCurrencyProperty()
	 */
    @Override
	public String getDealtCurrencyProperty() {
        if ( dealtCurrencyProperty == null ) {
            dealtCurrencyProperty = BrokerAdaptorUtil.getInstance()
                    .getDealtCurrencyProperty(getCurrencyPair(), getDealtCurrency());

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getDealtCurrencyProperty " );
                sb.append( "{ dealtCurrencyProperty = " ).append( dealtCurrencyProperty );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return dealtCurrencyProperty;
    }

    // convert an arbitrary amount of dealt to base, avoids duplicating a get*AmountInBase() for any get*DealAmount()
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getDealtInBase(double)
	 */
    @Override
	public double getDealtInBase( double amount ) {
        CurrencyPair ccyPair = getCurrencyPair();
        Currency dltCcy = getDealtCurrency();
        double dealtInBase = MDSFactory.getInstance().convertAmount(rfsSubscribe.getProviderShortName(), dltCcy,
                ccyPair.getBaseCurrency(), amount, "RFSRequestFacade.getDealtAmountInBase", true);
        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getDealtInBase " );
            sb.append( "{ baseDealtAmount = " ).append(dealtAmountInBase);
            sb.append( ", dltCcy = " ).append( dltCcy );
            sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
            sb.append( " }" );
            log.debug( sb.toString() );
        }
        return dealtInBase;
    }

    // this is only used for setting up ESP product backing the ESP+MDS publishing
    // note tiered ESP aggs ( wAvg will do isFilled() test before getting first price => never gets a price if requested amt is 0 )
    // note todo this gets used to construct synthetic ESP product tiers but getESPPriceDealtAmount() is what is required by ESPRFSPublisher => exposure to mismatch - clean up the concepts here.
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getESPMDSProductDealtAmount()
	 */
    @Override
	public double getESPMDSProductDealtAmount() { // todo SSPresolve Simon: all leg == 0 case, sets tier size in ESP, if tier == 0 => no rates. Do we want 'all liq' or 'first liq', nb: !=0 legs have same issue (hidden) as they are net
        boolean useNetAmount = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(rfsSubscribe.getProviderShortName()).isRfqMultiLegUseNetAmount();
        if(!useNetAmount){
            double amt = BrokerAdaptorUtil.getInstance().maxDealtAmount( rfsSubscribe );
            return isSSPRequest() ? Math.max( amt, getNetAmountSSP() ) : amt;
        }else {
            // returning the net amount for SSP and SWAP, if net amount is zero, use highest amount
            double netAmount;
            if (isSSPRequest()) netAmount = getNetAmountSSP();
            else netAmount = BrokerAdaptorUtil.getInstance().getNetAmount(rfsSubscribe);
            if(netAmount < MathUtilC.AMT_RND_FCT){
                netAmount = BrokerAdaptorUtil.getInstance().maxDealtAmount( rfsSubscribe );
            }
           return netAmount;
        }
    }

    // todo SSPlater no practical impl as even 0 will get first price, but preSSP code uses max(near, far) for subscribe (see getESPMDSProductDealtAmount() ) but only near for deriving the price
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getESPPriceDealtAmount()
	 */
    @Override
	public double getESPPriceDealtAmount() {
        if( isSSPRequest() ) {
            return getNetAmountSSP();
        } else {
            boolean useNetAmount = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(rfsSubscribe.getProviderShortName()).isRfqMultiLegUseNetAmount();
            if(!useNetAmount){
                return getDealtAmount();
            }else {
                // returning the net amount for SSP and SWAP, if net amount is zero, use first leg amount
                double netAmount = BrokerAdaptorUtil.getInstance().getNetAmount(rfsSubscribe);
                if(netAmount < MathUtilC.AMT_RND_FCT){
                    netAmount = getDealtAmount();
                }
                
                return netAmount;
            }
        }
    }

    // Bid is +ve, Offer is -ve
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getNetSignedAmountSSP()
	 */
    @Override
	public double getNetSignedAmountSSP() {

        List<RFSFXLeg> tradeLegs = getRFSSubscribe().getTradeLegs();
        double netAmount =0.0;
        for(RFSFXLeg tradeLeg : tradeLegs)
        {
            if(tradeLeg.getBidRate()!=null)
            {
                netAmount = MathUtilC.add(netAmount, tradeLeg.getBidRate().getDealtAmount());
            }
            else
            {
                netAmount = MathUtilC.subtract(netAmount, tradeLeg.getOfferRate().getDealtAmount());
            }
        }
        return netAmount;
    }

    @Override
    public double getNetAmount(){
        if(isSSPRequest()) return getNetAmountSSP();
        else if(isSwap()) return getMisMatchNetAmount();
        else return getDealtAmount();
    }

    @Override
    public double getNetAmountInBase(){
        double netAmount = getNetAmount();
        double netAmountInBase = MDSFactory.getInstance().convertAmount(rfsSubscribe.getProviderShortName(), getDealtCurrency(),
                getCurrencyPair().getBaseCurrency(), netAmount, "RFSRequestFacade.getNetAmountInBase", true);
        return netAmountInBase;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getNetAmountSSP()
	 */
    @Override
	public double getNetAmountSSP() {
        return isUDBRequest() ? getRFSSubscribe().getNetSpotAmont() : Math.abs( getNetSignedAmountSSP() );
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getESPInflateDealtAmount()
	 */
	@Override
	public double getESPInflateDealtAmount() {
        if( isSSPRequest() ) {
            // todo SSPresolve Simon spot? max? ... cfgetESPPriceDealtAmount
            return getNetAmountSSP();
        } else if(isSwap()){
        	return getMisMatchNetAmount();
        }else {
            return getDealtAmount();
        }
    }
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getDealtAmount()
	 */
    @Override
	public double getDealtAmount() {
        if ( dealtAmount == null ) {
            dealtAmount = BrokerAdaptorUtil.getInstance().getRequestedAmount( rfsSubscribe );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getDealtAmount " );
                sb.append( "{ dealtAmount = " ).append( dealtAmount );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return dealtAmount;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getDealtAmountInBase()
	 */
    @Override
	public double getDealtAmountInBase() {
        if ( dealtAmountInBase == null ) {
            double requestAmt = getDealtAmount();
            CurrencyPair ccyPair = getCurrencyPair();
            Currency dltCcy = getDealtCurrency();
            dealtAmountInBase = MDSFactory.getInstance().convertAmount(rfsSubscribe.getProviderShortName(), dltCcy,
                    ccyPair.getBaseCurrency(), requestAmt, "RFSRequestFacade.getDealtAmountInBase", true);
            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getDealtAmountInBase " );
                sb.append( "{ baseDealtAmount = " ).append(dealtAmountInBase);
                sb.append( ", dltCcy = " ).append( dltCcy );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return dealtAmountInBase;
    }

    public double getHighestLegAmountInBase(){
        List<RFSFXLeg> tradeLegs = rfsSubscribe.getTradeLegs();
        double highestAmt = 0;
        for(RFSFXLeg leg : tradeLegs){
            double legAmt = (leg.getBidOfferMode() == DealingPrice.BID ? leg.getBidRate() : leg.getOfferRate()).getDealtAmount();
            if(highestAmt < legAmt) highestAmt = legAmt;
        }
        double amtInBase = MDSFactory.getInstance().convertAmount(rfsSubscribe.getProviderShortName(), getDealtCurrency(),
                getCurrencyPair().getBaseCurrency(), highestAmt, "RFSRequestFacade.getHighestLegAmountInBase", true);
        return amtInBase;
    }

    @Override
    public double getDealtAmountGrossInBase(){
        double netAmt = getNetAmountInBase();
        double highestAmt = getHighestLegAmountInBase();
        return Math.max(netAmt, highestAmt);
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getFarDealtAmount()
	 */
    @Override
	public double getFarDealtAmount() {
        if ( farDealtAmount == null ) {
            farDealtAmount = BrokerAdaptorUtil.getInstance().getRequestedFarAmount(rfsSubscribe);

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getFarDealtAmount " );
                sb.append( "{ farDealtAmount = " ).append( farDealtAmount );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return farDealtAmount;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getFarDealtAmountInBase()
	 */
    @Override
	public double getFarDealtAmountInBase() {
        if ( farDealtAmountInBase == null ) {
            double farDealtAmount = getFarDealtAmount();
            CurrencyPair ccyPair = getCurrencyPair();
            Currency dltCcy = getDealtCurrency();
            farDealtAmountInBase = MDSFactory.getInstance().convertAmount(rfsSubscribe.getProviderShortName(), dltCcy,
                    ccyPair.getBaseCurrency(), farDealtAmount, "RFSRequestFacade.getFarDealtAmountInBase", true);

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getFarDealtAmountInBase " );
                sb.append( "{ farDealtAmount = " ).append( farDealtAmountInBase );
                sb.append( ", dltCcy = " ).append( dltCcy );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return farDealtAmountInBase;
    }

    // this respects SSP
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getBidOfferMode()
	 */
    @Override
	public int getBidOfferMode() {
        if ( bidOfferMode == null ) {
            if(!isSSPRequest())
                bidOfferMode = rfsSubscribe.getNearLeg().getBidOfferMode();
            else {
                bidOfferMode = isUDBRequest() ? rfsSubscribe.getNetSpotBidOfferMode() : getSSPBidOfferMode();
            }

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getBidOfferMode " );
                sb.append( "{ bidOfferMode = " ).append( bidOfferMode );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return bidOfferMode;
    }
    // return the mode based on the netted trade - I think this *should* be the same as the getBidOfferMode() that IS sets, check 'boundary' case netSpot == 0
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getBidOfferModeNetSpot()
	 */
    @Override
	public int getBidOfferModeNetSpot() {
        // used all over the place for UDB always returns +ve, as it should but it should check rather than accidentally...
        return getSSPBidOfferMode();
    }
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getSSPBidOfferMode()
	 */
    @Override
	public int getSSPBidOfferMode() {
        return ( getNetSignedAmountSSP() >= 0 )? DealingPrice.BID : DealingPrice.OFFER;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getFarBidOfferMode()
	 */
    @Override
	public int getFarBidOfferMode() {
        if ( farBidOfferMode == null ) {
            farBidOfferMode = rfsSubscribe.getFarLeg().getBidOfferMode();

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getFarBidOfferMode " );
                sb.append( "{ farBidOfferMode = " ).append( farBidOfferMode );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return farBidOfferMode;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isFarTenorSet()
	 */
    @Override
	public boolean isFarTenorSet() {
        return isSwap() && rfsSubscribe.getFarLeg().getTenor() != null;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getFarTenor()
	 */
    @Override
	public Tenor getFarTenor() {
        if ( farTenor == null && isFarTenorSet()) {
            String tenorName = rfsSubscribe.getFarLeg().getTenor();
            farTenor = tenorName == null ? null : new Tenor( tenorName );
            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getFarTenor " );
                sb.append( "{ farTenor = " ).append( farTenor );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return farTenor;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isFarValueDateSet()
	 */
    @Override
	public boolean isFarValueDateSet() {
        return isSwap() && rfsSubscribe.getFarLeg().getValueDate() != null;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getFarValueDate()
	 */
    @Override
	public IdcDate getFarValueDate() {
        if ( farValueDate == null && isFarValueDateSet()) {
            Date jdkDate = rfsSubscribe.getFarLeg().getValueDate();
            farValueDate = jdkDate == null ? null : DateTimeFactory.newDate( jdkDate );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getFarValueDate " );
                sb.append( "{ farValueDate = " ).append( farValueDate );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return farValueDate;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isNearTenorSpot()
	 */
    @Override
	public boolean isNearTenorSpot(){
        if (this.isNearTenorSpot == null) {
            boolean isNearTenorSpot = false;
            IdcDate spotDate = getValueDateFromTenor(Tenor.SPOT_TENOR);
            IdcDate nearValueDate = getCalculatedNearValueDate();
            if (nearValueDate.equals(spotDate)){
                isNearTenorSpot = true;
            }
            this.isNearTenorSpot = isNearTenorSpot;
        }
        return this.isNearTenorSpot;
    }

    /* (non-Javadoc)
     * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isNearTenorLessThanSpot()
     */
    @Override
    public boolean isNearTenorLessThanSpot(){
        if (this.isNearTenorLessThanSpot == null) {
            boolean isNearTenorLessThanSpot = false;
            IdcDate spotDate = getValueDateFromTenor(Tenor.SPOT_TENOR);
            IdcDate nearValueDate = getCalculatedNearValueDate();
            if (nearValueDate.isEarlierThan(spotDate)){
                isNearTenorLessThanSpot = true;
            }
            this.isNearTenorLessThanSpot = isNearTenorLessThanSpot;
        }
        return this.isNearTenorLessThanSpot;
    }

    /* (non-Javadoc)
     * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isNearTenorEqualsTradeDate()
     */
    @Override
    public boolean isNearTenorEqualsTradeDate(){
        if (this.isNearTenorEqualsTradeDate == null) {
            boolean isNearTenorEqualsTradeDate = false;
            IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate nearValueDate = getCalculatedNearValueDate();
            if (nearValueDate.equals(tradeDate)){
                isNearTenorEqualsTradeDate = true;
            }
            this.isNearTenorEqualsTradeDate = isNearTenorEqualsTradeDate;
        }
        return this.isNearTenorEqualsTradeDate;
    }

    /* (non-Javadoc)
     * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isNearTenorEqualsTradeDate()
     */
    @Override
    public boolean isTomAndSpotTenorSameValueDate(){
        IdcDate spotDate = getValueDateFromTenor(Tenor.SPOT_TENOR);
        IdcDate tomDate = getValueDateFromTenor(Tenor.TOMORROW_TENOR);
        return spotDate.isSameAs(tomDate);
    }
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isNearTenor(com.integral.finance.trade.Tenor)
	 */
    @Override
	public boolean isNearTenor(Tenor t){
        IdcDate tDate = getValueDateFromTenor(t);
        IdcDate nearValueDate = getCalculatedNearValueDate();
        return nearValueDate.equals(tDate);
    }

    // introduced for  59768
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isFarTenor(com.integral.finance.trade.Tenor)
	 */
    @Override
	public boolean isFarTenor(Tenor t){
        IdcDate tDate = getValueDateFromTenor(t);
        IdcDate farValueDate = getCalculatedFarValueDate();
        return farValueDate.equals(tDate);
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isFarTenorSpot()
	 */
    @Override
	public boolean isFarTenorSpot(){
        if (this.isFarTenorSpot == null) {
            boolean isFarTenorSpot = false;
            if (isSwap()){
                IdcDate spotDate = getValueDateFromTenor(Tenor.SPOT_TENOR);
                IdcDate farValueDate = getCalculatedFarValueDate();
                if (farValueDate.equals(spotDate)){
                    isFarTenorSpot = true;
                }
            }
            this.isFarTenorSpot = isFarTenorSpot;
        }
        return this.isFarTenorSpot;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getExpiration()
	 */
    @Override
	public Expiration getExpiration() {
        if ( expiration == null ) {
            int requestExpiry = rfsSubscribe.getRequestExpiryTimeInSeconds();

            expiration = new ExpirationC();
            if(!rfsSubscribe.isRfq()) {
                long configuredExpiry;
                if (getProduct().getConfiguration().isChannelOverrideEnabled()) {
                    configuredExpiry = getProduct().getConfiguration().getRequestExpiry();
                } else {
                    configuredExpiry = getStream().getRequestExpiry();
                }

                //if a request expiry less than or equal to 0 is recd, pass it to IS to determine the max configured
                //expiry. If request expiry is more than max expiry use max expiry
                if (configuredExpiry > 0 && configuredExpiry < requestExpiry) {
                    requestExpiry = Long.valueOf(configuredExpiry).intValue();
                }
            }

            expiration.setSeconds( requestExpiry );
        }
        return expiration;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getSubscriptionId()
	 */
    @Override
	public String getSubscriptionId() {
        return rfsSubscribe.getRequestId();
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isTraderRequest()
	 */
    @Override
	public boolean isTraderRequest() {
        return rfsSubscribe.getProperty( TRADER ) != null;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getCustomerLegalEntity()
	 */
    @Override
	public LegalEntity getCustomerLegalEntity() {
        if ( customerLE == null ) {
            String customerLEName = rfsSubscribe.getLeShortName();
            String customerOrgName = rfsSubscribe.getOrgShortName();

            Organization customerOrg = ReferenceDataCacheC.getInstance().getOrganization( customerOrgName );

            customerLE = BrokerAdaptorUtil.getInstance().getLegalEntity( customerLEName, customerOrg.getNamespace() );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getCustomerLegalEntity " );
                sb.append( "{ customerLE = " ).append( customerLE );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return customerLE;
    }

    public TradingParty getCustomerTradingParty()
    {
        if ( customerTP == null )
        {
            LegalEntity le = getCustomerLegalEntity ();
            if ( le != null && getBroker () != null )
            {
                customerTP = le.getTradingParty ( getBroker () );
            }
        }
        return customerTP;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getRequestingOrganization()
	 */
    @Override
	public Organization getRequestingOrganization(){
        if (requestingOrganization == null){
            String orgShortName = rfsSubscribe.getUserOrganization();
            requestingOrganization = ReferenceDataCacheC.getInstance().getOrganization( orgShortName );
        }
        return requestingOrganization;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getUser()
	 */
    @Override
	public User getUser() {
        if ( customer == null ) {
            String customerName = rfsSubscribe.getUserShortName();
            String customerOrgName = rfsSubscribe.getOrgShortName();

            Organization customerOrg = ReferenceDataCacheC.getInstance().getOrganization( customerOrgName );
            customer = BrokerAdaptorUtil.getInstance().getUser( customerName, customerOrg.getNamespace() );

            if (customer == null) {
                if ( log.isInfoEnabled() ) {
                    StringBuilder sb = new StringBuilder( 100 )
                            .append( this ).append( ".getUser() User is null for " )
                            .append(' ').append( customerName )
                            .append(' ').append( customerOrgName )
                            .append ( ' ' ).append ( customerOrg.getNamespace().getShortName () )
                            .append(' ').append( rfsSubscribe );
                    sb.append( ". Using the default dealing user." );
                    log.info( sb.toString() );
                }
                //if user is a sales dealer user use default dealing user.
                customer = customerOrg.getDefaultDealingUser();
            }

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getUser " );
                sb.append( "{ customer = " ).append( customer );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return customer;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isSwap()
	 */
    @Override
	public boolean isSwap() {
        if ( isSwap == null ) {
            isSwap = BrokerAdaptorUtil.getInstance().isSwap( rfsSubscribe.getTradeClassification() );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".isSwap " );
                sb.append( "{ isSwap = " ).append( isSwap );
                sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return isSwap;
    }

    @Override
    public boolean hasPercentSwapPointSpread(){
        Configuration config = getProduct().getConfiguration();
        return config.getCalculationBasisTenorSwap().equals(CalculationBasis.PERCENT_SPD) || config.getPostCalculationBasisTenorSwap().equals(CalculationBasis.PERCENT_SPD);
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isNDF()
	 */
    @Override
	public boolean isNDF(){
        return ISCommonConstants.TRD_CLSF_FXNDF.equals(rfsSubscribe.getTradeClassification());
    }

    /* (non-Javadoc)
     * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isNDF()
     */
    @Override
    public boolean isNDFSwap(){
        return ISCommonConstants.TRD_CLSF_FXNDF_SWAP.equals(rfsSubscribe.getTradeClassification());
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getTenorSpread()
	 */
    @Override
	public TenorSpread getTenorSpread() {
        if ( tenorSpread == null && !isSSPRequest()  ) {
            try {
                Product product = getProduct();
                Configuration configuration = product.getConfiguration();
                if (configuration.getTenorSpreadType() == SpreadType.DISABLED){
                    tenorSpread = TenorBasedSpreadCalculationService.getInstance().getSpotTenor();
                    return tenorSpread;
                }
                IdcDate current = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
                Tenor tenor = isTenorSet() ? getTenor() : null;
                IdcDate valueDate = isValueDateSet() ? getValueDate() : null;
                double amount = getDealtAmountGrossInBase();
                if ( isSwap() ) 
                {
                    Tenor farTenor = isFarTenorSet() ? getFarTenor() : null;
                    IdcDate farValueDate = isFarValueDateSet() ? getFarValueDate() : null;
                    Collection<TenorSpread> tenorSpreads =
                            ( configuration.isSwapTenorSpreadOverride() ? configuration.getOutrightTenorSpreadsFacade(amount,rfsSubscribe.getTradeClassification()) :
                                    configuration.getSwapTenorSpreadsFacade(amount,rfsSubscribe.getTradeClassification()) );
                    
                    tenorSpread = TenorBasedSpreadCalculationService.getInstance()
                            .getSwapSpread( configuration.isTenorSpreadInterpolationEnabled(), tenorSpreads, tenor, farTenor, valueDate, farValueDate,
                                    product.getRateBasis(), current, rfsSubscribe.getRequestId() );
                    if(!configuration.isSwapTenorSpreadOverride() && tenorSpread instanceof TenorSpreadC){
                        double multiplier = configuration.getTierMultiplier(amount);
                        tenorSpread = new SwapTenorSpreadFacade(tenorSpread, multiplier, configuration,rfsSubscribe.getTradeClassification());
                    }
                }
                else
                {
                    tenorSpread = TenorBasedSpreadCalculationService.getInstance()
                            .getOutrightSpread( configuration.isTenorSpreadInterpolationEnabled(), configuration.getOutrightTenorSpreadsFacade(amount,rfsSubscribe.getTradeClassification()),
                                    tenor, valueDate, product.getRateBasis(), current, rfsSubscribe.getRequestId() );
                }
                log.info("RFSRequestFacade.getTenorSpread: " + tenorSpread.toString());
            } catch ( Exception e ) {
                log.warn( this.getClass().getName() + ".setTenorSpread() " + getSubscriptionId(), e );
            }
        }
        return tenorSpread;
    }
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getOTRTenorSpread(com.integral.finance.trade.Tenor, com.integral.time.IdcDate)
	 */
    @Override
	public TenorSpread getOTRTenorSpread(Tenor specifiedTenor, IdcDate specifiedValueDate) {
        TenorSpread otrSpread = null;
        try {
            Product product = getProduct();
            Configuration configuration = product.getConfiguration();
            if (configuration.getTenorSpreadType() == SpreadType.DISABLED){
                return TenorBasedSpreadCalculationService.getInstance().getSpotTenor();
            }
            IdcDate current = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            double amount = getDealtAmountGrossInBase();
            return TenorBasedSpreadCalculationService.getInstance()
                    .getOutrightSpread( configuration.isTenorSpreadInterpolationEnabled(), configuration.getOutrightTenorSpreadsFacade(amount,rfsSubscribe.getTradeClassification()),
                            specifiedTenor, specifiedValueDate, product.getRateBasis(), current, rfsSubscribe.getRequestId() );
        } catch ( Exception e ) {
            log.warn( this.getClass().getName() + ".setTenorSpread() " + getSubscriptionId(), e );
        }
        return otrSpread;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getTradeClassification()
	 */
    @Override
	public String getTradeClassification() {
        return rfsSubscribe.getTradeClassification();
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getCalculatedNearValueDate()
	 */
    @Override
	public IdcDate getCalculatedNearValueDate(){
        if(calculatedNearValueDate == null){
            IdcDate calculatedNearValueDate = getValueDate();
            if (calculatedNearValueDate == null && isTenorSet()){
                calculatedNearValueDate = getValueDateFromTenor(getTenor());
            }
            this.calculatedNearValueDate = calculatedNearValueDate;
        }
        return this.calculatedNearValueDate;
    }
    
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#calculatedVariableNearValueDate()
	 */
    @Override
	public IdcDate getCalculatedVariableNearValueDate(){
        if(calculatedVariableNearValueDate == null){
            IdcDate calculatedVariableNearValueDate = getVariableValueDate();
            if (calculatedVariableNearValueDate == null && isVariableTenorSet()){
            	calculatedVariableNearValueDate = getValueDateFromTenor(getVariableTenor());
            }
            this.calculatedVariableNearValueDate = calculatedVariableNearValueDate;
        }
        return this.calculatedVariableNearValueDate;
    }



    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getCalculatedFarValueDate()
	 */
    @Override
	public IdcDate getCalculatedFarValueDate(){
        if (calculatedFarValueDate == null) {
            IdcDate calculatedFarValueDate = getFarValueDate();
            if(calculatedFarValueDate == null && isFarTenorSet()){
                calculatedFarValueDate = getValueDateFromTenor(getFarTenor());
            }
            this.calculatedFarValueDate = calculatedFarValueDate;
        }
        return this.calculatedFarValueDate;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isPreSpotSwap()
	 */
    @Override
	public boolean isPreSpotSwap()
    {
    	if( isSwap() &&  null==isPreSpotSwap)
    	{
    		IdcDate dt;
            dt = getProduct().getRateBasis().getValueDate(EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate(), new Tenor("SPOT"));
            isPreSpotSwap = new Boolean(getCalculatedFarValueDate().compare(dt) <=0 );
		}
    	else
    	{
    		isPreSpotSwap =  ( null== isPreSpotSwap ) ? new Boolean(false):isPreSpotSwap;
    	}
    	return isPreSpotSwap.booleanValue();
    }



    public boolean isFwdFwdNearLegWithInSpotFarLegBeyondSpot()
    {
        boolean nearLegWithInSpot=false;
		boolean farLegBeyondSpot=false;
		if( isSwap())
    	{
    		IdcDate dt;
            dt = getProduct().getRateBasis().getValueDate(EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate(), new Tenor("SPOT"));
            nearLegWithInSpot = getCalculatedNearValueDate().compare(dt) <=0 ;
			farLegBeyondSpot=   getCalculatedFarValueDate().compare(dt) >=0 ;

			return nearLegWithInSpot&&farLegBeyondSpot;
		}
    	
    	return false;
    }



	

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isNonSpotNearLegFSR()
	 * This is special case of FSR where near date is != SPOT and far date > SPOT
	 */
    @Override
    public boolean isNonSpotNearLegFSR()
    {
        if( isFSRRequest() && null== isNonSpotNearLegFSR )
        {
            IdcDate spotDt;
                spotDt = getProduct().getRateBasis().getValueDate(EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate(), new Tenor("SPOT"));
                isNonSpotNearLegFSR = getCalculatedNearValueDate().compare( spotDt ) != 0;
        }
        else
        {
            isNonSpotNearLegFSR =  ( null== isNonSpotNearLegFSR ) ? Boolean.FALSE :isNonSpotNearLegFSR;
        }
        return isNonSpotNearLegFSR;
    }

    /**
     * near leg < far leg but far leg rate is fixed and passed by customer
     * @return
     */
    @Override
    public boolean isFixedFarLegFSR()
    {
        if( isFSRRequest() && null== isFixedFarLegFSR )
        {
            isFixedFarLegFSR = getBidFarRate()  != 0.0 ||  getOfferFarRate() != 0.0;
        }
        else
        {
            isFixedFarLegFSR =  ( null== isFixedFarLegFSR ) ? Boolean.FALSE :isFixedFarLegFSR;
        }
        return isFixedFarLegFSR;
    }


    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getCalculatedValueDate(com.integral.is.message.rfs.RFSFXLeg)
	 */
    @Override
	public IdcDate getCalculatedValueDate(RFSFXLeg leg){

        IdcDate calculatedValueDate =  DateTimeFactory.newDate(leg.getValueDate());
        if(calculatedValueDate == null && leg.getTenor()!=null){
            calculatedValueDate = getValueDateFromTenor(new Tenor(leg.getTenor()));
        }
        return  calculatedValueDate;
    }



    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getValueDateFromTenor(com.integral.finance.trade.Tenor)
	 */
    @Override
	public IdcDate getValueDateFromTenor(Tenor tenor){
        IdcDate current = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
        FXRateBasis rateBasis = getProduct().getRateBasis();
        return rateBasis.getValueDate(current, tenor);
    }

    /* (non-Javadoc)
     * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getSpotLag()
     */
    @Override
    public Integer getSpotLag(){
        Integer spotSettlementDays = null;
        FXRateBasis rateBasis = getProduct().getRateBasis();
        if(rateBasis.getFXBusinessCalendar() != null) {
            spotSettlementDays = rateBasis.getFXBusinessCalendar().getLag();
        }
        if(spotSettlementDays == null){
            spotSettlementDays = Math.max(currencyPair.getBaseCurrency().getBusinessCalendar().getLag(),
                    currencyPair.getVariableCurrency().getBusinessCalendar().getLag());
        }
        return spotSettlementDays;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getPricingStrategy()
	 */
    @Override
	public PricingStrategy getPricingStrategy() throws IdcNoSuchObjectException, RFSValidationException {
        ExecutionRuleCondition executionCondition = getExecutionRuleCondition();
        if (executionRuleCondition == null ){
            throw new RFSValidationException("Execution rule condition not defined.");
        }
        if(Configuration.PRICING_SOURCE_MDS.equals(getProduct().getConfiguration().getPricingSource())){
            return PricingStrategy.RFS_FROM_MDS;
        }
        if( isFSRRequest() && !executionCondition.getPricingStrategy().equals(PricingStrategy.RFS) ) // if FSR => pricing strategy is RFS or RFS_MDS
            return PricingStrategy.RFS_MDS;
        else if( isSSPRequest() || isOutright() || isSwap()) { // SSP => strip any 'RFS' aspect of the pricing strategy
        	if(isSpotAndSwapEnabled()){
        		if(executionCondition.getExecutionPricingParameters().isSpotAndMDSEnabled()){
        			return PricingStrategy.getSpotAndSwapPricingStrategy(executionCondition.getPricingStrategy(), true);
        		}else{
        			return PricingStrategy.getSpotAndSwapPricingStrategy(executionCondition.getPricingStrategy(), false);
        		}
        	}else{
        		if(isSSPRequest() || isWindowForwardRequest()){
        			return PricingStrategy.getNoDoRFSVersionOf(executionCondition.getPricingStrategy());
        		}
        		if(isOutright()){
        			return executionCondition.getPricingStrategy();
        		}
        	}
		}
        
		return executionCondition.getPricingStrategy();
	}

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getExecutionRuleCondition()
	 */
    @Override
	public ExecutionRuleCondition getExecutionRuleCondition() {
        if (executionRuleCondition == null){
            executionRuleCondition = ExecutionRuleConditionCalculator.getInstance().calculateRfsExecutionRuleCondition( product.getConfiguration(), this );
        }
        return executionRuleCondition;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getExecutionMethod()
	 */
    @Override
	public ExecutionMethod getExecutionMethod() throws IdcNoSuchObjectException, RFSValidationException {
        boolean mmProduct = product.getConfiguration().isMarketMaker();
        boolean mmUsingNoCover = MarketMakerConfigMBean.YMMode.NO_YM.equals(MarketMakerConfig.getInstance().getYMMode(broker.getShortName()));
        ExecutionRuleCondition executionCondition = getExecutionRuleCondition();
        if (executionRuleCondition == null ){
            throw new RFSValidationException("Execution rule condition not defined.");
        }
        CoverExecutionMethod coverExecutionMethod = executionCondition.getExecutionMethodParameters().getCoverExecutionMethod();
        if(mmProduct && mmUsingNoCover && CoverExecutionMethod.Warehouse.equals(coverExecutionMethod)){
            boolean npc =   executionCondition.getExecutionMethodParameters().isNoPriceCheckEnabled();
            if(log.isDebugEnabled()) { log.debug("Using NoCover for " + broker.getShortName() + ", " + getCurrencyPair() + ", " + stream+", npc: "+npc); }
            if(npc){
                return ExecutionMethod.NoCoverNoPriceValidation;
            }else {
                return ExecutionMethod.NoCover;
            }
        }
        return executionCondition.getExecutionMethod();
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getDealingChannel()
	 */
    @Override
	public String getDealingChannel(){
        return (String) rfsSubscribe.getProperty(BrokerWorkflowFunctorC.DEALING_CHANNEL);
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getCreateTime()
	 */
    @Override
	public long getCreateTime() {
        return createTime;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isFSRRequest()
	 */
    @Override
	public boolean isFSRRequest() {
        if (isFSRRequest == null) {
            isFSRRequest = rfsSubscribe.isFSR();
        }
        return isFSRRequest;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isWindowForwardRequest()
	 */
    @Override
	public boolean isWindowForwardRequest() {
        if (isWindowForwardRequest == null) {
        	isWindowForwardRequest = ISCommonConstants.WINDOW_FORWARD.equalsIgnoreCase(rfsSubscribe.getSubTradeType());
        }
        return isWindowForwardRequest;
    }
    
    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isSSPRequest()
	 */
    @Override
	public boolean isSSPRequest() {
        if (isSSPRequest == null) {
            isSSPRequest = (rfsSubscribe.getTradeClassification().equals(ISConstantsC.TRD_CLSF_FXSSP ));
        }
        return isSSPRequest;
    }


    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isUDBRequest()
	 */
    @Override
    public boolean isUDBRequest() {
        return isSSPRequest() && rfsSubscribe.getNetSpotBidOfferMode() == DealingPrice.TWO_WAY;
    }


    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getBidSpotRate()
	 */
    @Override
	public double getBidSpotRate() {
        if (bidSpotRate == null) {
            bidSpotRate = rfsSubscribe.getNearLeg().getBidRate().getSpotRate();
        }
        return bidSpotRate;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getOfferSpotRate()
	 */
    @Override
	public double getOfferSpotRate() {
        if (offerSpotRate == null) {
            offerSpotRate = rfsSubscribe.getNearLeg().getOfferRate().getSpotRate();
        }
        return offerSpotRate;
    }

    /* (non-Javadoc)
    * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getBidFarRate()
    */
    @Override
    public double getBidFarRate() {
        if (bidFarRate == null) {
            if(rfsSubscribe.getFarLeg() != null)
            {
                RFSFXRate rate = rfsSubscribe.getFarLeg().getBidRate();
                bidFarRate = rate == null ? 0.0 : rate.getRate();
            }
            else
            {
                bidFarRate = 0.0;
            }
        }
        return bidFarRate;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getOfferFarRate()
	 */
    @Override
    public double getOfferFarRate() {
        if (offerFarRate == null) {
            if(rfsSubscribe.getFarLeg() != null)
            {
                RFSFXRate rate = rfsSubscribe.getFarLeg().getOfferRate();
                offerFarRate = rate == null ? 0.0 : rate.getRate();
            }
            else
            {
                offerFarRate = 0.0;
            }
        }
        return offerFarRate;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#isMisMatchSwap()
	 */
    @Override
	public boolean isMisMatchSwap() {
        return isSwap() && getDealtAmount() != getFarDealtAmount();
    }
    
    @Override
	public boolean isMatchSwap() {
		return isSwap() && getDealtAmount() == getFarDealtAmount();
	}

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getMisMatchNetAmount()
	 */
    @Override
	public double getMisMatchNetAmount() {
        //(bug: AP-2264)return isMisMatchSwap() ? Math.abs(getFarDealtAmount() - getDealtAmount()) : 0.0;
        double netAmount = 0;
        if(isMisMatchSwap()) {
            netAmount = Math.abs(getFarDealtAmount() - getDealtAmount());
            Currency dc = getDealtCurrency();
            if(dc != null) netAmount = dc.round(netAmount);
        }
        return netAmount;
    }

    /* (non-Javadoc)
	 * @see com.integral.broker.rfs.subscribe.RFSRequestFacade#getMdsPriceFacade()
	 */
    @Override
	public MDSPriceFacade getMdsPriceFacade() throws IdcNoSuchObjectException, RFSValidationException {
        if (mdsPriceFacade == null) {
            mdsPriceFacade = new MDSPriceFacadeC(this);
        }
        return mdsPriceFacade;
    }
    
    @Override
	public boolean isDealerInterventionEnabled(){
		ExecutionRuleCondition executionCondition = getExecutionRuleCondition();
		return executionCondition.getExecutionMethodParameters().isDealerInterventionEnabled()
				&& ConfigurationFactory.getInstance().getPriceConfigurationMBean().isDealerInterventionEnabled(rfsSubscribe.getProviderShortName());
	}

	@Override
	public void setExecutionRuleCondition(ExecutionRuleCondition executionRuleCondition) {
		this.executionRuleCondition = executionRuleCondition;
	}

	@Override
	public Map<String, String> getValidationFailures() {
		return (Map<String, String>) rfsSubscribe.getProperty(RFSConstants.VALIDATION_FAILURES);
	}

	@Override
	public boolean isManualRquest() {
		return rfsSubscribe.isManualRequest();
	}

	@Override
	public boolean isOutright() {
		if (isOutright == null) {
			isOutright = ISConstantsC.TRD_CLSF_OR.equals(rfsSubscribe.getTradeClassification());

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".isOutright " );
            sb.append( "{ isOutright = " ).append( isOutright );
            sb.append( ", rfsSubscribe = " ).append( rfsSubscribe );
            sb.append( " }" );
            log.debug( sb.toString() );
			}
		}
		return isOutright;
	}

	@Override
	public boolean isSpotAndSwapEnabled() {
		return getExecutionRuleCondition().getExecutionPricingParameters().isSpotAndSwapEnabled()
				&& ((isSSPRequest() && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceSSPUsingSwapEnabled(rfsSubscribe.getProviderShortName()))
				|| (isOutright() && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceOTUsingSwapEnabled(rfsSubscribe.getProviderShortName()))
                || ((isNDF() || isNDFSwap()) && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceNDFAndNDFSwapUsingSpotAndSwapEnabled(rfsSubscribe.getProviderShortName()))
                || (isSwap() && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceSwapUsingSpotAndSwapEnabled(rfsSubscribe.getProviderShortName())));
	}

	@Override
	public boolean isCoverOnSpotAndSwapEnabled() {
		return isSpotAndSwapEnabled() 
				&& getExecutionRuleCondition().getExecutionMethodParameters().getSpotAndSwapCoverExecutionMethod() == CoverExecutionMethod.Cover
				&& getExecutionRuleCondition().getExecutionMethodParameters().getCoverExecutionMethod() != CoverExecutionMethod.Manual;
	}
	

	@Override
	public double getFwdPointsDifference(){
		return fwdPointsDifference;
	}
	
	@Override
	public void setFwdPointsDifference(double fwdPointsDifference){
		this.fwdPointsDifference = fwdPointsDifference;
	}

    @Override
    public boolean isVariableDateFwdPointsSelected()
    {
        return variableDateFwdPointsSelected;
    }

    @Override
    public void setVariableDateFwdPointsSelected( boolean flag )
    {
        this.variableDateFwdPointsSelected = flag;
    }

    @Override
	public boolean isMakerCreditOverride(){
		return makerCreditOverride;
	}
	
	@Override
	public void setMakerCreditOverride(boolean makerCreditOverride){
		this.makerCreditOverride = makerCreditOverride;
	}
	
	@Override
	public String getMakerCreditOverrideUser(){
		return makerCreditOverrideUser;
	}
	
	@Override
	public void setMakerCreditOverrideUser(String makerCreditOverrideUser){
		this.makerCreditOverrideUser = makerCreditOverrideUser;
	}

    @Override
    public boolean isSentToManual()
    {
        return this.sentToManual;
    }

    @Override
    public void setSentToManual( boolean flag )
    {
        this.sentToManual = flag;
    }


    @Override
    public String getMT300Field72(){
        return rfsSubscribe.getMT300Field72();
    }

    @Override
    public boolean isProrataForwardRequest()
    {
        if (isProrataForwardRequest == null)
        {
            isProrataForwardRequest = ISCommonConstants.PRORATA_FORWARD.equalsIgnoreCase( rfsSubscribe.getSubTradeType() );
        }
        return isProrataForwardRequest;
    }
}