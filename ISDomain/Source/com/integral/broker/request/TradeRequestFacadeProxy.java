package com.integral.broker.request;

import com.integral.broker.model.*;
import com.integral.broker.price.PriceWrapper;
import com.integral.broker.rfs.RFSHandler;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.trade.Trade;
import com.integral.is.message.ISMessage;
import com.integral.is.message.Timing;
import com.integral.is.message.TradeRequest;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.message.WorkflowMessage;
import com.integral.model.ems.EMSExecutionType;
import com.integral.persistence.ExternalSystem;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.collections.ConcurrentHashSet;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * <AUTHOR> Development Corp.
 */

public class TradeRequestFacadeProxy implements TradeRequestFacade {
    TradeRequestFacade tradeRequestFacade;
    private double fillAmount;
    private double fillPrice;
    private double minQty;
    private double topOfTheBookRate;

    public TradeRequestFacadeProxy( TradeRequestFacade tradeRequestFacade, double fillAmount , double fillPrice, double minQty) {
        this.tradeRequestFacade = tradeRequestFacade;
        this.fillAmount = fillAmount;
        this.fillPrice = fillPrice;
        this.minQty = minQty;
    }

    public TradeRequest getTradeRequest() {
        return tradeRequestFacade.getTradeRequest() ;
    }

    public RFSTradeRequest getRFSTradeRequest() {
        return tradeRequestFacade.getRFSTradeRequest() ;
    }

    public String getVrifiedBrokerTradeId() {
        return tradeRequestFacade.getVrifiedBrokerTradeId();
    }

    public ISMessage getTradeRequestMessage() {
        return tradeRequestFacade.getTradeRequestMessage() ;
    }

    public boolean isRFSRequest() {
        return tradeRequestFacade.isRFSRequest() ;
    }

    public Product getProduct() {
        return tradeRequestFacade.getProduct() ;
    }

    public Stream getStream() {
        return tradeRequestFacade.getStream() ;
    }

    public Configuration getConfiguration() {
        return tradeRequestFacade.getConfiguration() ;
    }

    public FXRateBasis getRateBasis() {
        return tradeRequestFacade.getRateBasis() ;
    }

    public String getBaseCcy() {
        return tradeRequestFacade.getBaseCcy() ;
    }

    public String getVariableCcy() {
        return tradeRequestFacade.getVariableCcy() ;
    }

    public String getDealtCcy() {
        return tradeRequestFacade.getDealtCcy() ;
    }

    public int getBuySell() {
        return tradeRequestFacade.getBuySell() ;
    }

    public int getSpotCoverRequestBuySell() {
        return tradeRequestFacade.getSpotCoverRequestBuySell() ;
    }


    @Override
    public int getMisMatchBidOfferMode() {
        return tradeRequestFacade.getMisMatchBidOfferMode();
    }

    @Override
    public int getMisMatchBuySell() {
        return tradeRequestFacade.getMisMatchBuySell();
    }

    public String getRequestStreamId() {
        return tradeRequestFacade.getRequestStreamId() ;
    }

    public String getProviderShortName() {
        return tradeRequestFacade.getProviderShortName() ;
    }

    public String getLeShortName() {
        return tradeRequestFacade.getLeShortName() ;
    }

    public String getOrgShortName() {
        return tradeRequestFacade.getOrgShortName() ;
    }

    public String getUserShortName() {
        return tradeRequestFacade.getUserShortName() ;
    }

    public CurrencyPair getCurrencyPair() {
        return tradeRequestFacade.getCurrencyPair() ;
    }

    public Currency getDealtCurrency() {
        return tradeRequestFacade.getDealtCurrency() ;
    }

    public Currency getSettledCurrency() {
        return tradeRequestFacade.getSettledCurrency() ;
    }

    public boolean isDealingBaseCurrency() {
        return tradeRequestFacade.isDealingBaseCurrency() ;
    }

    public boolean isDealingVariableCurrency() {
        return tradeRequestFacade.isDealingVariableCurrency() ;
    }

    public String getDealtCurrencyProperty() {
        return tradeRequestFacade.getDealtCurrencyProperty() ;
    }

    public String getBidRate() {
        return tradeRequestFacade.getBidRate() ;
    }

    public String getOfferRate() {
        return tradeRequestFacade.getOfferRate() ;
    }

    public int getBidOfferMode() {
        return tradeRequestFacade.getBidOfferMode() ;
    }

    public int getFarBidOfferMode() {
        return tradeRequestFacade.getFarBidOfferMode() ;
    }

    public ExternalSystem getChannel() {
        return tradeRequestFacade.getChannel() ;
    }

    public boolean isActive() {
        return tradeRequestFacade.isActive() ;
    }

    public boolean isTraderRequest() {
        return tradeRequestFacade.isTraderRequest() ;
    }

    public Integer getSpotPrecision() {
        return tradeRequestFacade.getSpotPrecision() ;
    }

    public Integer getForwardRatePrecision() {
        return tradeRequestFacade.getForwardRatePrecision() ;
    }

    public Double getClientAcceptedRate() {
        return tradeRequestFacade.getClientAcceptedRate() ;
    }

    public Double getBaseRate() {
        return tradeRequestFacade.getBaseRate() ;
    }

    public Double getDoubleProperty(String key) {
        return tradeRequestFacade.getDoubleProperty(key) ;
    }

    public double[] getRequestRateComponents() {
        return tradeRequestFacade.getRequestRateComponents();
    }


    public double getRequestRate() {
        return fillPrice;
    }

    public void setRequestRate(double rate) {
        tradeRequestFacade.setRequestRate(rate) ;
    }

    public double getRequestSpotRate() {
        return tradeRequestFacade.getRequestSpotRate() ;
    }

    public void setRequestSpotRate(double spotRate) {
        tradeRequestFacade.setRequestSpotRate(spotRate) ;
    }

    public double getRequestForwardPoints() {
        return tradeRequestFacade.getRequestForwardPoints() ;
    }

    public double getRequestFarRate() {
        return tradeRequestFacade.getRequestFarRate() ;
    }

    public double getRequestFarSpotRate() {
        return tradeRequestFacade.getRequestFarSpotRate() ;
    }

    public double getRequestFarForwardPoints() {
        return tradeRequestFacade.getRequestFarForwardPoints() ;
    }

    public IdcDate getRequestValueDate() {
        return tradeRequestFacade.getRequestValueDate() ;
    }

    public IdcDate getRequestFarValueDate() {
        return tradeRequestFacade.getRequestFarValueDate() ;
    }

    public String getTradeTxnID() {
        return tradeRequestFacade.getTradeTxnID() ;
    }

    public String getRequestTxnID() {
        return tradeRequestFacade.getRequestTxnID() ;
    }

    public String getOrderID() {
        return tradeRequestFacade.getOrderID() ;
    }

    public String getOrderTxnID() {
        return tradeRequestFacade.getOrderTxnID() ;
    }

    public String getDealingChannel() {
        return tradeRequestFacade.getDealingChannel() ;
    }

    public String getProviderQuoteId() {
        return tradeRequestFacade.getProviderQuoteId() ;
    }

    public Timing getTiming() {
        return tradeRequestFacade.getTiming() ;
    }

    public User getUser() {
        return tradeRequestFacade.getUser() ;
    }

    public String getOriginatingUserID() {
        return tradeRequestFacade.getOriginatingUserID() ;
    }

    public TradingParty getTradingParty() {
        return tradeRequestFacade.getTradingParty() ;
    }

    public Organization getOrganization() {
        return tradeRequestFacade.getOrganization() ;
    }

    public Organization getProvider() {
        return tradeRequestFacade.getProvider() ;
    }

    public String getOriginatingCptyID() {
        return tradeRequestFacade.getOriginatingCptyID() ;
    }
    
    public Integer getPrecision(String propName) {
        return tradeRequestFacade.getPrecision(propName) ;
    }

    public double getFillAmount() {
        return fillAmount;
    }

    public RFSHandler getRFSHandler() {
        return tradeRequestFacade.getRFSHandler();
    }

    public BrokerTradeHandler getBrokerTradeHandler() {
        return tradeRequestFacade.getBrokerTradeHandler();
    }

    public boolean isClientPriceImprovementAllowed() {
        return tradeRequestFacade.isClientPriceImprovementAllowed();
    }

    public String getExternalRequestID() {
        return tradeRequestFacade.getExternalRequestID();
    }

    public String getOrderType() {
        return tradeRequestFacade.getOrderType();
    }

    public double getMarketRange() {
        return tradeRequestFacade.getMarketRange();
    }

    public boolean isCoverMarketRangeEnabled() {
       return tradeRequestFacade.isCoverMarketRangeEnabled();
    }

    public double getCoverMarketRange() {
       return tradeRequestFacade.getCoverMarketRange();
    }
    
    public boolean isZeroCoverMarketRange() {
        return tradeRequestFacade.isZeroCoverMarketRange();
     }

    public double getExecutionMarketRange(){
       return tradeRequestFacade.getExecutionMarketRange();
    }

    public boolean isMarket() {
        return tradeRequestFacade.isMarket();
    }

    public boolean isLimitOrder() {
        return tradeRequestFacade.isLimitOrder();
    }

    public boolean isMarketRangeOrder() {
        return tradeRequestFacade.isMarketRangeOrder();
    }

    public boolean isStopOrder() {
        return tradeRequestFacade.isStopOrder();
    }

    public boolean isTrailingStopOrder() {
        return tradeRequestFacade.isTrailingStopOrder();
    }

    public   boolean isCoverOrderMarketRange() {
        return tradeRequestFacade.isCoverOrderMarketRange();
    }

    public boolean isCoverPureOrderMarket() {
        return tradeRequestFacade.isCoverPureOrderMarket();
    }

    public boolean isCoverOrderMarket() {
        return tradeRequestFacade.isCoverOrderMarket();
    }

    public boolean isCoverOrderLimit() {
        return tradeRequestFacade.isCoverOrderLimit();
    }
    
    public boolean isPureMarketOrderExecution() {
        return tradeRequestFacade.isPureMarketOrderExecution();
    }

    public boolean isMarketExecution(){
        return tradeRequestFacade.isMarketExecution();
    }

    public boolean isMarketOrderExecution(){
        return tradeRequestFacade.isMarketOrderExecution();
    }

    public boolean isMarketRangeOrderExecution(){
        return tradeRequestFacade.isMarketRangeOrderExecution();
    }

    public boolean isMarketRangeSyntheticOrderExecution(){
        return tradeRequestFacade.isMarketRangeSyntheticOrderExecution();
    }

    public boolean isLimitOrderExecution(){
        return tradeRequestFacade.isLimitOrderExecution();
    }

    public boolean isValidOrderType() {
        return tradeRequestFacade.isValidOrderType();
    }

    public boolean isCustomerFullFillEnabled() {
       return tradeRequestFacade.isCustomerFullFillEnabled();
    }

    public double getBrokerPipsFactor() {
        return tradeRequestFacade.getBrokerPipsFactor();
    }

    public Object getProperty( String propName ) {
        return tradeRequestFacade.getProperty( propName );
    }

    public double getESPMinimumSpread() {
        return tradeRequestFacade.getESPMinimumSpread();
    }

    public double getBaseOrderMatchingRate() {
        return tradeRequestFacade.getBaseOrderMatchingRate();
    }

    public double getSpread() {
        return tradeRequestFacade.getSpread();
    }

    public boolean isMarketOrder() {
        return tradeRequestFacade.isMarketOrder();
    }

    public boolean isPureMarketOrder() {
        return tradeRequestFacade.isPureMarketOrder();
    }

    public double getRequestedAmount() {
        return tradeRequestFacade.getRequestedAmount();
    }
	
	public double getCustomerOrderAmount() {
        return tradeRequestFacade.getCustomerOrderAmount();
    }

    public double getRequestedFarAmount() {
        return tradeRequestFacade.getRequestedFarAmount();
    }

    public double getCoverTradeAmount() {
        return tradeRequestFacade.getCoverTradeAmount();
    }
	
	public ExecutionMethod getExecutionMethod() {
        return tradeRequestFacade.getExecutionMethod();
    }

    public ExecutionMethod getCoverExecutionMethod() {
        return tradeRequestFacade.getCoverExecutionMethod();
    }

    public String getCustomerOrderType() {
        return tradeRequestFacade.getCustomerOrderType();
    }

    public String getCustomerAddExecInst() {
        return tradeRequestFacade.getCustomerAddExecInst();
    }

    public int getCustomerOrderTIF() {
        return tradeRequestFacade.getCustomerOrderTIF();
    }

    public boolean isCoverTradingDisabled()
    {
        return tradeRequestFacade.isCoverTradingDisabled();
    }

    @Override
    public boolean isWarehouseEnabled() {
        return tradeRequestFacade.isWarehouseEnabled();
    }

    @Override
    public boolean isSyntheticCross() {
        return tradeRequestFacade.isSyntheticCross();
    }

    @Override
    public EMSExecutionType getExecutionType() {
        return tradeRequestFacade.getExecutionType();
    }


    public ExecutionStrategy getExecutionStrategy() {
        return tradeRequestFacade.getExecutionStrategy();
    }

    public String getVerifiedCoverTradeIds() {
        return tradeRequestFacade.getVerifiedCoverTradeIds();
    }

    public double getSwapSpread() {
        return tradeRequestFacade.getSwapSpread();
    }

    public double getSwapPostSpread() {
        return tradeRequestFacade.getSwapPostSpread();
    }

    public double getSpotSpread() {
        return tradeRequestFacade.getSpotSpread();
    }
    
    public double getSpotSpreadRaw() {
        return tradeRequestFacade.getSpotSpreadRaw();
    }

    public double getSpotPostSpread(double rate) {
        return tradeRequestFacade.getSpotPostSpread(rate);
    }

    public double getESPPostSpread(double rate) {
        return tradeRequestFacade.getESPPostSpread(rate);
    }

    public double getESPSpread() {
        return tradeRequestFacade.getESPSpread();
    }
    
    public double getESPSpreadRaw() {
        return tradeRequestFacade.getESPSpreadRaw();
    }

    public double getMinQty() {
        return minQty;
    }

	public String getSettlementInstructions() {
        return tradeRequestFacade.getSettlementInstructions();
    }

    public boolean isOneWayRequest() {
        return tradeRequestFacade.isOneWayRequest();
    }

    public ConcurrentHashSet getOrderMatchedQuotes() {
        return tradeRequestFacade.getOrderMatchedQuotes();
    }

    public void addToOrderMatchedQuotes(String orderMatchedRateId){
        tradeRequestFacade.addToOrderMatchedQuotes(orderMatchedRateId);
    }

     public boolean isOrderMatchedQuote(String orderMatchedRateId ){
         return tradeRequestFacade.isOrderMatchedQuote(orderMatchedRateId);
     }

    public double getTenorBasedOutrightSpread() {
        return tradeRequestFacade.getTenorBasedOutrightSpread();
    }
    
    public double getTenorBasedOutrightSpreadRaw() {
        return tradeRequestFacade.getTenorBasedOutrightSpreadRaw();
    }

    public double getTenorBasedSwapSpread() {
        return tradeRequestFacade.getTenorBasedSwapSpread();
    }

    public double getTenorBasedOutrightPostSpread(double rate, double fwdPts) {
        return tradeRequestFacade.getTenorBasedOutrightPostSpread(rate, fwdPts);
    }

    public double getTenorBasedSwapPostSpread() {
        return tradeRequestFacade.getTenorBasedSwapPostSpread();
    }

    public void setReferenceSwapPointsLP(double d){
        tradeRequestFacade.setReferenceSwapPointsLP(d);
    }
    public double getReferenceSwapPointsLP(){
        return tradeRequestFacade.getReferenceSwapPointsLP();
    }

    public void setReferenceSwapPointsCustomer(double d){
        tradeRequestFacade.setReferenceSwapPointsCustomer(d);
    }
    public double getReferenceSwapPointsCustomer(){
        return tradeRequestFacade.getReferenceSwapPointsCustomer();
    }

    public Set<String> getVerifiedTxnIdSet() {
        return tradeRequestFacade.getVerifiedTxnIdSet();
    }

    public void setCoverOrderId(String orderId) {
        tradeRequestFacade.setCoverOrderId(orderId);
    }

    public String getCoverOrderId() {
        return tradeRequestFacade.getCoverOrderId();
    }

    public void setCoverOrderTxnId(String orderTxnId) {
        tradeRequestFacade.setCoverOrderTxnId(orderTxnId);
    }

    public String getCoverOrderTxnId() {
        return tradeRequestFacade.getCoverOrderTxnId();
    }

    public void setAverageCoverRate( double coverRate ) {
        tradeRequestFacade.setAverageCoverRate( coverRate );
    }

    public double getAverageCoverRate() {
        return tradeRequestFacade.getAverageCoverRate();
    }

    public boolean isVwapExecution()
    {
        return tradeRequestFacade.isVwapExecution();
    }

    public void setCurrentOrderRate(double currentOrderRate)
    {
        tradeRequestFacade.setCurrentOrderRate(currentOrderRate);
    }

    public double getCurrentOrderRate()
    {
        return tradeRequestFacade.getCurrentOrderRate();
    }

    public String getVerifiedCoverTradeMakerRefIds() {
        return tradeRequestFacade.getVerifiedCoverTradeMakerRefIds();
    }

    public void addVerifiedCoverTrades(String transactionID, Trade trade ) {
        tradeRequestFacade.addVerifiedCoverTrades(transactionID, trade );
    }

    public String getCoveredTradeTakerReferenceId() {
        return tradeRequestFacade.getCoveredTradeTakerReferenceId();
    }

    public boolean isCustBrokerTradeCreated() {
        return tradeRequestFacade.isCustBrokerTradeCreated();
    }

    public void setCustBrokerTradeCreated(boolean flag) {
        tradeRequestFacade.setCustBrokerTradeCreated( flag );
    }

    public Collection<Trade> getVerifiedCoverTrades() {
        return tradeRequestFacade.getVerifiedCoverTrades();
    }

    public void setSingleProvider( Organization org ) {
        tradeRequestFacade.setSingleProvider( org );
    }

    public Organization getSingleProvider() {
        return tradeRequestFacade.getSingleProvider();
    }

    public LegalEntity getCoverTradeLegalEntity() {
        return tradeRequestFacade.getCoverTradeLegalEntity();
    }

    public TradingParty getCoverTradeTradingParty() {
        return tradeRequestFacade.getCoverTradeTradingParty();
    }

    public LegalEntity getCoverTradeLegalEntity( Organization provider ) {
        return tradeRequestFacade.getCoverTradeLegalEntity( provider );
    }

    public Set<PriceWrapper> createDummyNoCoverPrices() {
        return tradeRequestFacade.createDummyNoCoverPrices();
    }

    public boolean isMakerTradeNotificationDisabled() {
        return tradeRequestFacade.isMakerTradeNotificationDisabled();
    }

    public boolean isMoreThanMinTradeSize(double amount, Organization org, Organization toOrg) {
        return tradeRequestFacade.isMoreThanMinTradeSize(amount, org, toOrg);
    }

    public void addToMatchedProvider(Organization provider) {
        tradeRequestFacade.addToMatchedProvider(provider);
    }

    public Collection<Organization> getMatchedProviders() {
        return tradeRequestFacade.getMatchedProviders();
    }

    public String getExcludedProviders() {
        return tradeRequestFacade.getExcludedProviders();
    }

    public List<Organization> getOrderProviders() {
        if (minQty > 0 && ! tradeRequestFacade.isCoverTradingDisabled()) {
            return tradeRequestFacade.getNonPartialFillOrderProviders();
        } else {
            return tradeRequestFacade.getOrderProviders();
        }
    }

    public void providerResponse(Organization provider, boolean isRejected) {
        tradeRequestFacade.providerResponse(provider, isRejected);
    }

    public boolean validateCircuit(Organization provider) {
        return tradeRequestFacade.validateCircuit(provider);
    }

    public boolean isFarLegSpot() {
        return tradeRequestFacade.isFarLegSpot();
    }

    public double getESPMaximumSpread() {
        return tradeRequestFacade.getESPMaximumSpread();
    }

    public double getRFSMinimumSpread() {
         return tradeRequestFacade.getRFSMinimumSpread();
    }

    public double getRFSMaximumSpread() {
         return tradeRequestFacade.getRFSMaximumSpread();
    }

    public TradeRequestFacade getESPTradeRequestFacade() {
        return tradeRequestFacade.getESPTradeRequestFacade();
    }

    public void setEspTradeRequestFacade(TradeRequestFacade espTradeRequestFacade) {
        tradeRequestFacade.setEspTradeRequestFacade(espTradeRequestFacade);
    }

    public List<Organization> getNonPartialFillOrderProviders() {
        return tradeRequestFacade.getNonPartialFillOrderProviders();
    }

    public Double getMinTradeSize(Organization org, Organization toOrg) {
        return tradeRequestFacade.getMinTradeSize(org, toOrg);
    }

    public ExecutionRuleCondition getExecutionRuleCondition() {
        return tradeRequestFacade.getExecutionRuleCondition();
    }

    public Collection<PriceWrapper> createRFSDummyNoCoverPrices() {
        return tradeRequestFacade.createRFSDummyNoCoverPrices();
    }

    public boolean isSpreadedProviderMatchingEnabled() {
        return tradeRequestFacade.isSpreadedProviderMatchingEnabled();
    }

    public boolean isSpreadedProvider(String provider) {
        return tradeRequestFacade.isSpreadedProvider(provider);
    }

    public double getCustomerFilledAmount() {
        return tradeRequestFacade.getCustomerFilledAmount();
    }

    public void addCustomerFilledAmount(double filledAmt) {
        tradeRequestFacade.addCustomerFilledAmount(filledAmt);
    }

    public Long getCustomerOrderExpirationTime() {
        return tradeRequestFacade.getCustomerOrderExpirationTime();
    }

    public long getStreamTIF() {
        return tradeRequestFacade.getStreamTIF();
    }

    public long getEffectiveCoverTIFPeriod() {
        return tradeRequestFacade.getEffectiveCoverTIFPeriod();
    }

    public Timestamp getEffectiveCoverTIFTime() {
        return tradeRequestFacade.getEffectiveCoverTIFTime();
    }

    public boolean isECNBAOnlyOrderProviders() {
        return tradeRequestFacade.isECNBAOnlyOrderProviders();
    }

    public ConcurrentMap<Organization, AtomicInteger> getProvidersRejectCount() {
        return tradeRequestFacade.getProvidersRejectCount();
    }

    public void setOrderMatchedQuotes(ConcurrentHashSet appliedQuotes) {
        tradeRequestFacade.setOrderMatchedQuotes(appliedQuotes);
    }

    public void setProvidersRejectCount(ConcurrentMap<Organization, AtomicInteger> providersRejectCount) {
        tradeRequestFacade.setProvidersRejectCount(providersRejectCount);
    }

    public int getCoverType() {
        return tradeRequestFacade.getCoverType();
    }

    public WorkflowMessage getControlMsg() {
        return tradeRequestFacade.getControlMsg();
    }

    public void setControlMsg(WorkflowMessage controlMsg) {
        tradeRequestFacade.setControlMsg(controlMsg);
    }

    public boolean isRFSESPSupported() {
        return tradeRequestFacade.isRFSESPSupported();
    }

    public boolean isWarmUpRequest() {
        return tradeRequestFacade.isWarmUpRequest();
    }

    public double getTopOfTheBookRate() {
        return topOfTheBookRate;
}

    public void setTopOfTheBookRate(double topOfTheBookRate) {
        this.topOfTheBookRate = topOfTheBookRate;
    }

    public boolean isAutoCoverEnabled() {
        return tradeRequestFacade.isAutoCoverEnabled();
    }

    public void initializeProviders() {
        tradeRequestFacade.initializeProviders();
    }

    public void setUnwind(boolean isUnwind) {
        tradeRequestFacade.setUnwind(isUnwind);
    }

    public boolean isUnwind() {
        return tradeRequestFacade.isUnwind();
    }

    public void setMatchingStartTime(long matchingStartTime) {
        tradeRequestFacade.setMatchingStartTime(matchingStartTime);
    }

    public long getMatchingStartTime() {
        return tradeRequestFacade.getMatchingStartTime();
    }

    public ExecutionMethod getCurrentExecutionMethod() {
        return tradeRequestFacade.getCurrentExecutionMethod();
    }

    public void setCurrentExecutionMethod(ExecutionMethod currentExecutionMethod) {
        tradeRequestFacade.setCurrentExecutionMethod(currentExecutionMethod);
    }

    public double getRFSPriceProvisionSpotSpread() {
        return tradeRequestFacade.getRFSPriceProvisionSpotSpread();
    }

    public double getRFSPriceProvisionNearFwdSpread() {
        return tradeRequestFacade.getRFSPriceProvisionNearFwdSpread();
    }

    public double getRFSPriceProvisionFarFwdSpread() {
        return tradeRequestFacade.getRFSPriceProvisionFarFwdSpread();
    }

   @Override
   public LiquidityProvision getBrokerCustomerLiquidityProvision()
   {
      return tradeRequestFacade.getBrokerCustomerLiquidityProvision();
   }

   @Override
   public void setBrokerCustomerLiquidityProvision(LiquidityProvision liqudityProvision)
   {
      tradeRequestFacade.setBrokerCustomerLiquidityProvision(liqudityProvision);
   }

   @Override
   public LiquidityProvision getBrokerLiquidityProvision()
   {
      return tradeRequestFacade.getBrokerLiquidityProvision();
   }

   @Override
   public void setBrokerLiquidityProvision(LiquidityProvision liqudityProvision)
   {
      tradeRequestFacade.setBrokerLiquidityProvision(liqudityProvision);
   }

    @Override
    public boolean isFSR() {
        return tradeRequestFacade.isFSR();
    }

    @Override
    public int getCoverBidOfferMode() {
        return tradeRequestFacade.getCoverBidOfferMode();
    }

    @Override
    public boolean isMisMatchSwap() {
        return tradeRequestFacade.isMisMatchSwap();
    }

    
    @Override
	public boolean isPreSpotSwap() {
		return tradeRequestFacade.isPreSpotSwap();
	}

	@Override
    public double getFarDealtAmount() {
        return tradeRequestFacade.getFarDealtAmount();
    }

    @Override
    public String getMT300Field72() {
        return tradeRequestFacade.getMT300Field72();
    }


    @Override
    public double getDealtAmount() {
        return tradeRequestFacade.getDealtAmount();
    }

    public boolean isSSP() {
        return tradeRequestFacade.isSSP();
    }

    public SpreadCalculatorC getSpreadCalculator() {
        return tradeRequestFacade.getSpreadCalculator();
    }

    public int getNetSpotBidOfferMode() {
        return tradeRequestFacade.getNetSpotBidOfferMode();
    }

    public boolean isNetSpotBid() {
        return tradeRequestFacade.isNetSpotBid();
    }

    @Override
    public boolean isInstanceResponseEnabled() {
        return tradeRequestFacade.isInstanceResponseEnabled();
    }

    public Double getRFSPriceProvisionFwdSpread(String legName){
        return tradeRequestFacade.getRFSPriceProvisionFwdSpread(legName);
    }

	@Override
	public boolean isNetted() {
		return tradeRequestFacade.isNetted();
	}

    @Override
    public String getOriginatingOrderId() {
        return tradeRequestFacade.getOriginatingOrderId();
    }

    @Override
    public double getNetAmountInBase(){
        return tradeRequestFacade.getNetAmountInBase();
    }

    public double getSwapPointSpreadCust(){
        return tradeRequestFacade.getSwapPointSpreadCust();
    }

    @Override
    public void setSkew(double value){
        tradeRequestFacade.setSkew(value);
    }
    @Override
    public double getSkew(){
        return tradeRequestFacade.getSkew();
    }

    @Override
    public boolean isCoverOnMTF() {
        return tradeRequestFacade.isCoverOnMTF();
    }

    @Override
    public boolean isFilterNonMiFIDProviders() {
        return tradeRequestFacade.isFilterNonMiFIDProviders();
    }

	@Override
	public boolean isDealerInterventionEnabled() {
		return tradeRequestFacade.isDealerInterventionEnabled();
	}

	@Override
	public double getRFSDealerSpotSpread() {
		return tradeRequestFacade.getRFSDealerSpotSpread();
	}

	@Override
	public double getRFSDealerFwdSpread() {
		return tradeRequestFacade.getRFSDealerFwdSpread();
	}

	@Override
	public double getRFSDealerFarFwdSpread() {
		return tradeRequestFacade.getRFSDealerFarFwdSpread();
	}

	@Override
	public double getReferenceRate() {
		return tradeRequestFacade.getReferenceRate();
	}

	@Override
	public List<TradeRequestFacade> getSpotAndSwapTradeRequestFacade() {
		return tradeRequestFacade.getSpotAndSwapTradeRequestFacade();
	}

	@Override
	public boolean isNearLegSpot() {
		return tradeRequestFacade.isNearLegSpot();
	}

	@Override
	public TradeRequestFacade getParentTradeRequestFacade() {
		return tradeRequestFacade.getParentTradeRequestFacade();
	}

	@Override
	public boolean isSpotAndSwapTradeRequest() {
		return tradeRequestFacade.isSpotAndSwapTradeRequest();
	}
	
	@Override
	public boolean isOutright() {
		return tradeRequestFacade.isOutright();
	}

	@Override
    public  boolean isSwap(){
        return  tradeRequestFacade.isSwap();
    }

    @Override
    public boolean isNDFOrNDFSwap(){
        return tradeRequestFacade.isNDFOrNDFSwap();
    }

    @Override
    public boolean isFwdFwd(){
        return tradeRequestFacade.isFwdFwd();
    }

	@Override
	public boolean isCoverOnSpotAndSwapEnabled() {
		return tradeRequestFacade.isCoverOnSpotAndSwapEnabled();
	}

	@Override
	public boolean isSpotAndSwapEnabled() {
		return tradeRequestFacade.isSpotAndSwapEnabled();
	}
	
	@Override
	public double getFwdPointsDifference(){
		return tradeRequestFacade.getFwdPointsDifference();
	}
	
	@Override
	public void setFwdPointsDifference(double fwdPointsDifference){
		//do nothing
	}

	@Override
    public List<Organization> getOrderProvidersFromConfig(){
        return  tradeRequestFacade.getOrderProvidersFromConfig();
    }

    @Override
    public boolean isAtBestOrderRFS()
    {
        return tradeRequestFacade.isRFSRequest () && tradeRequestFacade.isAtBestOrderRFS ();
    }

    @Override
    public boolean isSkipValueDateMisMatchProvider(){
        return tradeRequestFacade.isSkipValueDateMisMatchProvider();
    }

    @Override
    public void setSkipValueDateMisMatchProvider(boolean filterValueDateMisMatchProvider){
        tradeRequestFacade.setSkipValueDateMisMatchProvider(filterValueDateMisMatchProvider);
    }

    @Override
    public boolean isRfqTrade()
    {
        return tradeRequestFacade.isRFSRequest () && tradeRequestFacade.isRfqTrade ();
    }

    @Override
    public void setIgnoreRejectRFS(boolean ignoreRejectRFS) {
        //do nothing
    }
    @Override
    public boolean isIgnoreRejectRFS() {
        return tradeRequestFacade.isRFSRequest () && tradeRequestFacade.isIgnoreRejectRFS ();
    }

    @Override
    public int asBuySell(int bidOfferMode ){
        return tradeRequestFacade.asBuySell(bidOfferMode);
    }
}
