// Copyright (c) 2006-2008 Integral Development Corp. All rights reserved.
package com.integral.broker.request;

import com.integral.adaptor.AdaptorConstantC;
import com.integral.broker.BrokerAdaptorFactory;
import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.calculator.ExecutionRuleConditionCalculator;
import com.integral.broker.config.BrokerAdaptorMBean;
import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.broker.model.*;
import com.integral.broker.price.*;
import com.integral.broker.publish.ESPRFSPublisher;
import com.integral.broker.rfs.RFSHandler;
import com.integral.broker.rfs.RFSHandlerC;
import com.integral.broker.rfs.subscribe.RFSRequestFacade;
import com.integral.broker.util.BrokerMiFIDUtil;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.TimeInForce;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.finance.dealing.mifid.MiFIDUtils;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.finance.trade.Trade;
import com.integral.is.ISCommonConstants;
import com.integral.is.broker.BrokerWorkflowFunctorC;
import com.integral.is.broker.RFSBrokerWorkflowFunctorC;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.MDSFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.YMUtil;
import com.integral.is.message.ISMessage;
import com.integral.is.message.Timing;
import com.integral.is.message.TradeRequest;
import com.integral.is.message.rfs.RFSFXLeg;
import com.integral.is.message.rfs.RFSFXRate;
import com.integral.is.message.rfs.RFSSubscribe;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.config.MarketMakerConfigMBean;
import com.integral.marketmaker.rule.Rule;
import com.integral.marketmaker.rule.pricecontrol.MMChannelConfig;
import com.integral.message.WorkflowMessage;
import com.integral.model.ems.EMSExecutionType;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.riskmanagement.rw.provision.RWProvisionConfigManager;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.util.MathUtilC;
import com.integral.util.StringUtilC;
import com.integral.util.collections.ConcurrentHashSet;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * Converts primitive information in {@link TradeRequest} into usable objects.
 *
 * <AUTHOR> Development Corp.
 */
public class TradeRequestFacadeC implements TradeRequestFacade {

    /* TradeRequest Property */
    public static final String ORDER_ID = BrokerWorkflowFunctorC.ORDER_ID;
    public static final String ORDER_TXN_ID = BrokerWorkflowFunctorC.ORDER_TXN_ID;
    public static final String REQUEST_TXN_ID = BrokerWorkflowFunctorC.REQUEST_TXN_ID;
    public static final String DEALING_CHANNEL = BrokerWorkflowFunctorC.DEALING_CHANNEL;
    public static final String MARKET_SNAPSHOT = BrokerWorkflowFunctorC.MARKET_SNAPSHOT;

    public static final String CHANNEL = "channel";
    public static final String TRADER = "Trader";
    public static final String REQUEST = "Request";
    public static final String HYPHEN = "-";

    public static final String NEAR_RATE = "NEAR_RATE";
    public static final String NEAR_SPOT_RATE = "NEAR_SPOT_RATE";
    public static final String NEAR_FWD_POINTS = "NEAR_FWD_POINTS";

    public static final String FAR_RATE = "FAR_RATE";
    public static final String FAR_SPOT_RATE = "FAR_SPOT_RATE";
    public static final String FAR_FWD_POINTS = "FAR_FWD_POINTS";
    public static final int REGULAR_COVER = 0;
    public static final int AUTO_COVER = 1;
    private double fwdPointsDifference;

    private StringBuilder logSB;
    private ConcurrentHashSet appliedQuotes = new ConcurrentHashSet();
    private Collection<Organization> matchedOrganizations =  new HashSet<Organization>();
    private ConcurrentMap<Organization, AtomicInteger> providersRejectCount = new ConcurrentHashMap<Organization, AtomicInteger>();
    /**
     * Logger for this class and its descendants.
     */
    protected Log log = LogFactory.getLog( this.getClass() );
    protected TradeRequest tradeRequest;
    private RFSTradeRequest rfsTradeRequest;
    private transient Product product;
    private transient ExternalSystem channel;
    private String strVerifiedTxnIDs;
    private String strVerifiedMakerRefIDs;
    private String coverOrderId;
    private String coverOrderTxnId;
    private double averageCoverRate;
    private double currentOrderRate;
    protected ExecutionRuleCondition executionRuleCondition;
    private boolean isCustBrokerTradeCreated = false;

    private Organization singleProvider;
    private LegalEntity coverTradeLE;
    protected List<Organization> orderProviders;
    protected List<Organization> nonPartialFillOrderProviders;
    protected String excludedProviders;
    protected String spreadedProvider;
    private TradeRequestFacade espTradeRequestFacade;
    protected ExecutionMethod currentExecutionMethod;
    private double customerFilledAmount;
    // Key -> Cover trade transaction Id, value -> Cover trade maker ref Id
    ConcurrentHashMap<String, Trade> listVerifiedCoverTrades = new ConcurrentHashMap<String, Trade>();
    private boolean isWarmUpRequest;
    private boolean isUnwind;
    private long matchingStartTime;
    private Organization org;
    /**
     * This is the broker organization
     */
    private Organization provider;
    TradingParty party;
    public Set<String> getVerifiedTxnIdSet() {
        return Collections.unmodifiableSet( listVerifiedCoverTrades.keySet());
    }

    public static final String RFS_HANDLER = "RFS_HANDLER";
    public static final String TRADE_HANDLER = "TRADE_HANDLER";
    protected ExecutionMethod executionMethod;
    private ExecutionMethod coverExecutionMethod;
    private SpreadCalculatorC spreadCalculator;
    private Integer spotPrecision;
    private Integer forwardRatePrecision;
    protected Long effectiveCoverTIFPeriod;
    protected Timestamp effectiveCoverTIFTime;

    //: todo have enumerator for Timeinforce
    public static final String FOK = "FOK";
    public static final String IOC = "IOC";
    public static final String IOC1 = "IOC1";
    public static final String NOC = "NOC";
    public static final String NCNPV = "NCNPV";
    private Long interval;
    protected boolean isECNBAOnlyOrderProviders;

    private WorkflowMessage controlMsg;

    private transient LiquidityProvision liquidityProvision;
    private transient LiquidityProvision providerLiquidityProvision;

    protected Boolean warehouseEnabled;
    protected Boolean coverTradingDisabled;
    protected Boolean syntheticCross;
    private double swapPointSpreadLP;
    private double swapPointSpreadCust;
    private final BrokerAdaptorMBean brokerAdaptorMBean;
    protected Boolean coverOnMTF;
    protected Boolean filterNonMiFIDLps;
    public Boolean dealerInterventionEnabled;
    protected  boolean useSwapProviders;
    protected boolean atBestOrderRFS;
    protected boolean rfqTrade;
    protected boolean ignoreRejectRFS;
    //This will be used when tradeType is SSP and Pricing Strategy is SpotAndSwap
    //Cover On Swap is checked.
    private transient List<TradeRequestFacade> spotAndSwapTradeRequestFacade = new ArrayList<TradeRequestFacade>();
    protected boolean skipValueDateMisMatchProvider;
    
    public TradeRequestFacadeC( RFSTradeRequest req ) {
        this.rfsTradeRequest = req;
        this.brokerAdaptorMBean = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(req.getProviderShortName());
        logSB = new StringBuilder();
        init( req );
    }
    public TradeRequestFacadeC( RFSTradeRequest req, boolean useSwapProviders ) {
        this.rfsTradeRequest = req;
        this.brokerAdaptorMBean = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(req.getProviderShortName());
        logSB = new StringBuilder();
        this.useSwapProviders = useSwapProviders;
        init( req );
    }

    public TradeRequestFacadeC( TradeRequest req ) {
        this.tradeRequest = req;
        this.brokerAdaptorMBean = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(req.getProviderShortName());
        init( req );
    }

    public TradeRequestFacadeC(TradeRequest tradeRequest, Product product) {
        this(tradeRequest);
        this.product = product;
    }

    private void init( ISMessage req ) {
        spreadCalculator = new SpreadCalculatorC( this );
        isWarmUpRequest = ISCommonUtilC.isWarmUpObject(tradeRequest);
        String atBestOrderRFSParam = ( String ) req.getProperty ( ISCommonConstants.AT_BEST_ORDER_RFS_PARAM );
        if ( !StringUtilC.isNullOrEmpty ( atBestOrderRFSParam ) && Boolean.TRUE.toString ().equalsIgnoreCase ( atBestOrderRFSParam  ) )
        {
            atBestOrderRFS = true;
        }

        String rfqTradeParam = ( String ) req.getProperty ( ISCommonConstants.RFQ_TRADE_PARAM );
        if ( !StringUtilC.isNullOrEmpty ( rfqTradeParam ) && Boolean.TRUE.toString ().equalsIgnoreCase ( rfqTradeParam  ) )
        {
            rfqTrade = true;
        }
    }

    public TradeRequestFacadeC initTranient(){
        //priming the transient values
        getExecutionRuleCondition();
        getExecutionMethod();
        getCoverExecutionMethod();
        getSpotPrecision();
        getForwardRatePrecision();
        getEffectiveCoverTIFPeriod();
        getStreamTIF();
        getBrokerCustomerLiquidityProvision();
        getBrokerLiquidityProvision();
        isWarehouseEnabled();
        isCoverTradingDisabled();
        isSyntheticCross();
        if(isRFSRequest()) spreadCalculator.initTransient();
        return this;
    }

    @Override
    public boolean isDealerInterventionEnabled() {
		ExecutionRuleCondition executionRuleCondition = getExecutionRuleCondition();
		if(dealerInterventionEnabled == null)
			dealerInterventionEnabled = executionRuleCondition.getExecutionMethodParameters().isDealerInterventionEnabled();
		return dealerInterventionEnabled && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isDealerInterventionEnabled(getProviderShortName());
	}

	public TradeRequest getTradeRequest() {
        return tradeRequest;
    }

    public RFSTradeRequest getRFSTradeRequest() {
        return rfsTradeRequest;
    }

    private void setECNBAOnlyProvider() {
        List<Organization> orderProviders = getOrderProviders();
        if (orderProviders == null || orderProviders.size() != 1) return;
        isECNBAOnlyOrderProviders = orderProviders.get(0).isBroker() || orderProviders.get(0).isECN();
    }

    public String getVerifiedCoverTradeIds(){
        if (listVerifiedCoverTrades.isEmpty() && espTradeRequestFacade != null){
            strVerifiedTxnIDs = espTradeRequestFacade.getVerifiedCoverTradeIds();
        } else if (null  == strVerifiedTxnIDs){
            strVerifiedTxnIDs = convertToString1( listVerifiedCoverTrades.keySet() ).toString();
        }
        return strVerifiedTxnIDs;
    }

    public String getVerifiedCoverTradeMakerRefIds(){
        if (listVerifiedCoverTrades.isEmpty() && espTradeRequestFacade != null){
            strVerifiedTxnIDs = espTradeRequestFacade.getVerifiedCoverTradeMakerRefIds();
        } else if (null == strVerifiedMakerRefIDs ) {
            strVerifiedMakerRefIDs = convertToString( listVerifiedCoverTrades.values() ).toString();
        }
        return strVerifiedMakerRefIDs;
    }

    public String getSettlementInstructions() {
        if( isRFSRequest() ){
            return getRFSTradeRequest().getSettlementInstruction();
        }
        return null;
    }


    public String getVrifiedBrokerTradeId() {
        String coverTradeIds = getVerifiedCoverTradeIds();
        if ( coverTradeIds == null || coverTradeIds.trim().length() == 0 ) {
            coverTradeIds = getTradeTxnID();
        }
        return coverTradeIds;
    }

    public ISMessage getTradeRequestMessage() {
        return isRFSRequest() ? rfsTradeRequest : tradeRequest;
    }

    public boolean isRFSRequest() {
        return rfsTradeRequest != null;
    }

    private BrokerOrganizationFunction getOrganizationFunction() {
        Organization org = getProvider();
        String name = BrokerOrganizationFunction.ORG_FUNC_NAME;
        BrokerOrganizationFunction function = (BrokerOrganizationFunction) org.getOrganizationFunction(name);

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getOrganizationFunction " );
            sb.append( "{ org = " ).append( org );
            sb.append( ", name = " ).append( name );
            sb.append( ", function = " ).append( function );
            sb.append( '}' );
            log.debug( sb.toString() );
        }

        return function;
    }

    public Product getProduct() {
        if ( product == null ) {
            Stream stream = findStream();
            CurrencyPair ccyPair = findCurrencyPair();
            product = stream.getProduct( ccyPair, !isRFSRequest() );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getProduct " );
                sb.append( "{ stream = " ).append( stream );
                sb.append( ", ccyPair = " ).append( ccyPair );
                sb.append( ", product = " ).append( product );
                sb.append(", isChannelOverrideEnabled = ")
                        .append(product.getConfiguration().isChannelOverrideEnabled());
                sb.append( ", isESPEnabled = " ).append( product.getConfiguration().isESPEnabled() );
                sb.append( ", isRFSEnabled = " ).append( product.getConfiguration().isRFSEnabled() );
                sb.append( " }" );
                log.debug( sb.toString() );
            }
        }
        return product;
    }

    private Stream findStream() {
        String streamId = getRequestStreamId();
        BrokerOrganizationFunction function = getOrganizationFunction();
        Stream stream = function.getStream( streamId );

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".findStream " );
            sb.append( "{ streamId = " ).append( streamId );
            sb.append( ", function = " ).append( function );
            sb.append( ", stream = " ).append( stream );
            sb.append( " }" );
            log.debug( sb.toString() );
        }

        return stream;
    }

    public Stream getStream() {
        Configuration config = getConfiguration();
        if ( null == config ) {
            return null;
        }
        Stream stream = config.getStream();

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getStream " );
            sb.append( "{ config = " ).append( config );
            sb.append( ", stream = " ).append( stream );
            sb.append( " }" );
            log.debug( sb.toString() );
        }

        return stream;
    }

    public Configuration getConfiguration() {
        Product product = getProduct();
        if ( null == product ) {
            return null;
        }
        Configuration config = product.getConfiguration();

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getConfiguration " );
            sb.append( "{ product = " ).append( product );
            sb.append( ", config = " ).append( config );
            sb.append( " }" );
            log.debug( sb.toString() );
        }

        return config;
    }

    public FXRateBasis getRateBasis() {
        Configuration config = getConfiguration();
        CurrencyPair ccyPair = getCurrencyPair();
        FXRateBasis basis = config.getRateBasis( ccyPair );

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getRateBasis " );
            sb.append( "{ config = " ).append( config );
            sb.append( ", ccyPair = " ).append( ccyPair );
            sb.append( ", basis = " ).append( basis );
            sb.append( '}' );
            log.debug( sb.toString() );
        }

        return basis;
    }

    public String getBaseCcy() {
        return isRFSRequest() ? rfsTradeRequest.getBaseCurrency() : tradeRequest.getBaseCcy();
    }

    public String getVariableCcy() {
        return isRFSRequest() ? rfsTradeRequest.getVariableCurrency() : tradeRequest.getVariableCcy();
    }

    public String getDealtCcy() {
        return isRFSRequest() ? rfsTradeRequest.getTradeLegs().get(0).getDealtCurrency() : tradeRequest.getDealtCcy();
    }

    public RFSFXLeg getNearestLeg() {
        RFSFXLeg tl = rfsTradeRequest.getNearLeg();
        if( tl == null ) tl = rfsTradeRequest.getTradeLegs().get(0);
        return tl;
    }

    public int asBuySell(int bidOfferMode ) {
        return bidOfferMode == DealingPrice.BID ? TradeRequest.SELL : TradeRequest.BUY;
    }
    // used in RFSHandlerC.getESPTradeRequestFacade()
    // what BS should my spot cover request have
    public int getSpotCoverRequestBuySell() {
        if (!isRFSRequest()) return tradeRequest.getBuySell();
        if (isSSP()) return asBuySell(getNetSpotBidOfferMode());
        if (getRFSHandler().getRFSSubscriptionFacade().isMisMatchSwap()) {
            return asBuySell(getMisMatchBidOfferMode());
        } else {
            return asBuySell(getNearestLeg().getBidOfferMode());
        }
    }

    // todo probably this kind of thing is done 'all over the place'
    protected String bomString ( int bom) {
        if( bom == DealingPrice.BID ) return "BID";
        if( bom == DealingPrice.OFFER ) return "OFFER";
        if( bom == DealingPrice.TWO_WAY ) return "TWO_WAY";
        return ""+bom;
    }
    public int getBuySell() {
        if( ! isRFSRequest() ) return tradeRequest.getBuySell();
        int bom = getNearestLeg().getBidOfferMode();
        if( isSSP() ) {
            // bom = getRFSHandler().getRFSSubscriptionFacade().getBidOfferMode();   // udb returns what is set on subscribe, 2-way
            // int nsbom = rfsTradeRequest.getNetSpotBidOfferMode(); // this is -1, probably IS could have set this correctly but it's redundant given computing from the legs
            // always compute the done net spot bid offer mode from the legs
            double bidNetAmount = 0;
            double offerNetAmount = 0;
            for( RFSFXLeg l : rfsTradeRequest.getTradeLegs()) {
                if( l.getBidOfferMode() == DealingPrice.BID )
                    bidNetAmount += l.getBidRate().getDealtAmount();
                else if( l.getBidOfferMode() == DealingPrice.OFFER )
                    offerNetAmount += l.getOfferRate().getDealtAmount();
            }
            // these reflect accepted leg sides, tested UDB both ways & SSP - so I know we can derive accepted allocs and net spot and side color from here
            boolean calcedNetIsBid = MathUtilC.greaterThanOrEqualTo( bidNetAmount, offerNetAmount, MathUtilC.epsCCYAMOUNT );
            bom = calcedNetIsBid ? DealingPrice.BID : DealingPrice.OFFER;
        }
        return asBuySell( bom );
    }

    public int getNetSpotBidOfferMode() {
        return isNetSpotBid() ? DealingPrice.BID : DealingPrice.OFFER;
    }
    // swap portion from PriceCalculationService
    public boolean isNetSpotBid() {
        if ( !isRFSRequest() ) {
            return getBidOfferMode() == DealingPrice.BID;
        } else   if( isSSP() ) {
            if( getRFSHandler().getRFSSubscriptionFacade().isUDBRequest() ) {
                double bidDealt = 0.0, askDealt = 0.0;
                for( RFSFXLeg l : rfsTradeRequest.getTradeLegs() ) {
                    if( l.getBidOfferMode() == DealingPrice.BID ) bidDealt += l.getBidRate().getDealtAmount();
                    if( l.getBidOfferMode() == DealingPrice.OFFER ) askDealt += l.getOfferRate().getDealtAmount();
                }
                return bidDealt >= askDealt;
            } else {
                return getRFSHandler().getRFSSubscriptionFacade().getBidOfferModeNetSpot() == DealingPrice.BID;
            }
        } if( getRFSTradeRequest().getFarLeg() == null ) {
            return getBidOfferMode() == DealingPrice.BID;
        } else {
            int boMode = getBidOfferMode();  // for swaps this is nearLeg.bidOfferMode
            boolean isBidRate = getBidOfferMode() == DealingPrice.BID;
            double nearAmount;
            double farAmount;
            RFSFXLeg nearLeg = getRFSTradeRequest().getNearLeg();
            RFSFXLeg farLeg = getRFSTradeRequest().getFarLeg();
            if ( isBidRate ){
                nearAmount = nearLeg.getBidRate().getDealtAmount();
                farAmount = farLeg.getOfferRate().getDealtAmount();
            } else {
                nearAmount = nearLeg.getOfferRate().getDealtAmount();
                farAmount = farLeg.getBidRate().getDealtAmount();
            }
            return ( isBidRate && nearAmount >= farAmount ) || ( !isBidRate && farAmount > nearAmount );
        }
    }

    @Override
    public boolean isInstanceResponseEnabled() {
        return brokerAdaptorMBean.isInstanceResponseEnabled()
            && getStream().isInstantResponse()
            && getExecutionRuleCondition().isFullFillEnabled();
    }

    public int getMisMatchBidOfferMode() {
        RFSRequestFacade rfsSubFacade = getRFSHandler().getRFSSubscriptionFacade();
        boolean isFarLegGreaterAmt = rfsSubFacade.getFarDealtAmount() > rfsSubFacade.getDealtAmount();
        return isFarLegGreaterAmt ? rfsTradeRequest.getFarLeg().getBidOfferMode() : rfsTradeRequest.getNearLeg().getBidOfferMode();
    }

    public int getMisMatchBuySell() {
        return getMisMatchBidOfferMode() == DealingPrice.BID ? TradeRequest.SELL : TradeRequest.BUY;
    }


    public String getRequestStreamId() {
        return isRFSRequest() ? rfsTradeRequest.getStreamId() : tradeRequest.getStreamId();
    }

    public String getProviderShortName() {
        return isRFSRequest() ? rfsTradeRequest.getProviderShortName() : tradeRequest.getProviderShortName();
    }

    public String getLeShortName() {
        return isRFSRequest() ? rfsTradeRequest.getLeShortName() : tradeRequest.getLeShortName();
    }

    public String getOrgShortName() {
        return isRFSRequest() ? rfsTradeRequest.getOrgShortName() : tradeRequest.getOrgShortName();
    }

    public String getUserShortName() {
        return isRFSRequest() ? rfsTradeRequest.getUserShortName() : tradeRequest.getUserShortName();
    }

    private CurrencyPair findCurrencyPair() {
        String baseCcy = getBaseCcy();
        String variableCcy = getVariableCcy();
        CurrencyPair ccyPair = CurrencyFactory.newCurrencyPair( baseCcy, variableCcy );

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".findCurrencyPair " );
            sb.append( "{ baseCcy = " ).append( baseCcy );
            sb.append( ", variableCcy = " ).append( variableCcy );
            sb.append( ", ccyPair = " ).append( ccyPair );
            sb.append( '}' );
            log.debug( sb.toString() );
        }

        return ccyPair;
    }

    public CurrencyPair getCurrencyPair() {
        Product product = getProduct();
        CurrencyPair ccyPair = product.getCurrencyPair();

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getCurrencyPair " );
            sb.append( "{ product = " ).append( product );
            sb.append( ", ccyPair = " ).append( ccyPair );
            sb.append( " }" );
            log.debug( sb.toString() );
        }

        return ccyPair;
    }

    public Currency getDealtCurrency() {
        String ccy = getDealtCcy();
        return CurrencyFactory.getCurrency( ccy );
    }

    public Currency getSettledCurrency() {
        String baseCcy = getBaseCcy();
        String varCcy = getVariableCcy();
        String dealtCcy = getDealtCcy();
        String settledCcy = dealtCcy.equals( baseCcy ) ? varCcy : baseCcy;
        return CurrencyFactory.getCurrency( settledCcy );
    }

    public boolean isDealingBaseCurrency() {
        String baseCcy = getBaseCcy();
        String dealtCcy = getDealtCcy();
        return dealtCcy.equals( baseCcy );
    }

    public boolean isDealingVariableCurrency() {
        String varCcy = getVariableCcy();
        String dealtCcy = getDealtCcy();
        return dealtCcy.equals( varCcy );
    }

    public String getDealtCurrencyProperty() {
        if ( isDealingBaseCurrency() ) {
            return FXLegDealingPrice.CCY1;
        } else if ( isDealingVariableCurrency() ) {
            return FXLegDealingPrice.CCY2;
        } else {
            return null;
        }
    }

    public String getBidRate() {
        if ( DealingPrice.BID == getBidOfferMode() ) {
            return String.valueOf( getRequestRate() );
        }
        return null;
    }

    public String getOfferRate() {
        if ( DealingPrice.OFFER == getBidOfferMode() ) {
            return String.valueOf( getRequestRate() );
        }
        return null;
    }

    public int getBidOfferMode() {
        if ( isRFSRequest()  ) {
            if( isSSP() )
                return getRFSHandler().getRFSSubscriptionFacade().getBidOfferMode();
            else
                return getNearestLeg().getBidOfferMode();
        } else {
            switch ( getBuySell() ) {
                case TradeRequest.BUY:
                    return DealingPrice.OFFER;
                case TradeRequest.SELL:
                    return DealingPrice.BID;
            }
            return DealingPrice.UNDEFINED;
        }
    }

    public int getCoverBidOfferMode() {
        if ( isRFSRequest() ) {
            return isMisMatchSwap() ? getMisMatchBidOfferMode() : (isSSP() ? getNetSpotBidOfferMode() : rfsTradeRequest.getNearLeg().getBidOfferMode());
        } else {
            switch ( getBuySell() ) {
                case TradeRequest.BUY:
                    return DealingPrice.OFFER;
                case TradeRequest.SELL:
                    return DealingPrice.BID;
            }
            return DealingPrice.UNDEFINED;
        }
    }

    public boolean isMisMatchSwap() {
        return getRFSHandler().getRFSSubscriptionFacade().isMisMatchSwap();
    }
    
    public boolean isPreSpotSwap() 
    {
    	if(getRFSHandler().getRFSSubscriptionFacade().isSwap())
    	{
    		return getRFSHandler().getRFSSubscriptionFacade().isPreSpotSwap();
    	}
    	return false;
    }
    public double getFarDealtAmount() {
        return getRFSHandler().getRFSSubscriptionFacade().getFarDealtAmount();
    }

    public double getDealtAmount() {
        return getRFSHandler().getRFSSubscriptionFacade().getDealtAmount();
    }

    @Override
    public double getNetAmountInBase(){
        double netAmount;
        String baseCurrency, dealtCurrency;
        if(isRFSRequest()){
            dealtCurrency = rfsTradeRequest.getTradeLegs().get(0).getDealtCurrency();
            baseCurrency = rfsTradeRequest.getBaseCurrency();
            netAmount = BrokerAdaptorUtil.getInstance().getNetAmount(getRFSHandler().getRFSSubscriptionFacade().getRFSSubscribe());

        }else {
            netAmount = tradeRequest.getAmount();
            dealtCurrency = tradeRequest.getDealtCcy();
            baseCurrency = tradeRequest.getBaseCcy();
        }
        if(!baseCurrency.equals(dealtCurrency)){
            netAmount = MDSFactory.getInstance().convertAmount(rfsTradeRequest.getProviderShortName(), CurrencyFactory.getCurrency(dealtCurrency), CurrencyFactory.getCurrency(baseCurrency), netAmount, "TradeRequestFacade");
        }
        return netAmount;
    }

    public int getFarBidOfferMode() {
        if ( isRFSRequest() ) {
            return rfsTradeRequest.getFarLeg().getBidOfferMode();
        }
        return DealingPrice.UNDEFINED;
    }

    public ExternalSystem getChannel() {
        if ( channel == null ) {
            String channelName = ( String ) tradeRequest.getProperty( CHANNEL );
            if ( channelName == null ) {
                channelName = brokerAdaptorMBean.getRequestChannel();
            }
            channel = ISUtilImpl.getInstance().getExternalSys( channelName );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getChannel " );
                sb.append( "{ channelName = " ).append( channelName );
                sb.append( ", channel = " ).append( channel );
                sb.append( '}' );
                log.debug( sb.toString() );
            }
        }
        return channel;
    }

    /**
     * Determines if order execution is enabled.
     *
     * @return <code>true</code> if execution is enabled, <code>false</code> if execution is disabled
     */
    public boolean isActive() {
        return getProduct().isOrderExecutionEnabled() &&  ISUtilImpl.isOrderExecutionEnabledForTakerOrg(getProvider()) && getCoverExecutionMethod() != null && isClientChannelEnabled();
    }
    private boolean isClientChannelEnabled(){
        try {
            if(!ConfigurationFactory.getInstance().getConfigurationMBean().isClientChannelExecutionEnabled()) {
                if(log.isDebugEnabled()) {
                    log.debug("TRFC.isClientChannelEnabled: isClientChannelExecutionEnabled is false" );
                }
                return true;
            }
            Stream stream = getStream();
            CurrencyPair currencyPair = getProduct().getCurrencyPair();
            Organization organization = getOrganization();
            Configuration configuration = getConfiguration();
            if (stream == null || configuration == null || currencyPair == null || organization == null)
                return true;
            // not a Market Maker configuration
            if(!configuration.isMarketMaker()) {
                if(log.isDebugEnabled()) {
                    log.debug("TRFC.isClientChannelEnabled: Not an Market Maker Configuration "+ currencyPair.getName() );
                }
                return true;
            }

            //No Client channel assigned
            if (organization.getClientChannel() == null) {
                if(log.isDebugEnabled()) {
                    log.debug("TRFC.isClientChannelEnabled: Client channel is not configured for  "+ organization.getShortName() );
                }
                return true;
            }
            String clientChannel = organization.getClientChannel().getShortName();
            Organization brokerOrg = stream.getBrokerOrganizationFunction().getOrganization();
            log.info("TRFC.isClientChannelEnabled:: org: "+ organization.getShortName() + "| currencyPair:"+ currencyPair.getName() +"| BrokerOrg:"+ brokerOrg.getShortName() +"| clientChannel:"+ clientChannel );

            MMChannelConfig channelConfig = MarketMakerFactory.getInstance().getPriceControlService().getClientChannelStatus(brokerOrg.getShortName(), currencyPair.getName());
            if (channelConfig == null) {
                if(log.isDebugEnabled()) {
                    log.debug("TRFC.isClientChannelEnabled: Market Maker Client Channel Configuration not found for Broker "+ brokerOrg.getShortName() + "|"+ currencyPair.getName());
                }
                return true;
            }
            log.info("TRFC.isClientChannelEnabled:: MMChannelConfig: "+ channelConfig );

            if (channelConfig.getClientChannel().containsKey(clientChannel)) {
                Rule rule = channelConfig.getClientChannel().get(clientChannel);
                if (rule == Rule.OFF) {
                    StringBuilder sb = new StringBuilder(500);
                    sb.append("TRFC.isClientChannelEnabled:: ").append(organization.getShortName()).append("|").append(clientChannel).append("|").append(currencyPair.getName()).append("|").append(rule);
                    log.info(sb.toString());
                    return false;
                }
            }
        }catch (Exception ex){
            log.error("Error occurred at TRFC.isClientChannelEnabled:: ", ex);
        }
        return true;
    }
    public boolean isTraderRequest() {
        boolean rfsRequest =
                isRFSRequest() ? rfsTradeRequest.getProperty(TRADER) != null : tradeRequest.getProperty(TRADER) != null;
        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( "TraderRequestFacadeC.isTraderRequest - " );
            sb.append( "is RFSRequest()=" ).append( isRFSRequest() );
            if ( rfsTradeRequest != null ) {
                sb.append( ", rfsTradeRequest property=" ).append( rfsTradeRequest.getProperty( TRADER ) );
            }
            if ( tradeRequest != null ) {
                sb.append( ", tradeRequest property=" ).append( tradeRequest.getProperty( TRADER ) );
            }
            log.debug( sb.toString() );
        }
        return rfsRequest;
    }

    public Integer getSpotPrecision() {
        if (spotPrecision == null){
            spotPrecision = getPrecision(RFSBrokerWorkflowFunctorC.SPOT_PRECISION);
        }
        return spotPrecision;
    }

    public Integer getForwardRatePrecision() {
        if (forwardRatePrecision == null){
            forwardRatePrecision = getPrecision( RFSBrokerWorkflowFunctorC.FORWARD_RATE_PRECISION );
        }
        return forwardRatePrecision;
    }

    public Double getClientAcceptedRate() {
        Object rateObj;
        if ( isRFSRequest() ) {
            rateObj = rfsTradeRequest.getProperty( BrokerWorkflowFunctorC.RATE );
        } else {
            rateObj = tradeRequest.getProperty( BrokerWorkflowFunctorC.RATE );
        }

        Double rate = null;
        if ( rateObj instanceof String ) {
            rate = Double.valueOf( ( String ) rateObj );
        } else if ( rateObj instanceof Double ) {
            rate = ( Double ) rateObj;
        }

        return rate;
    }

    public Double getBaseRate() {
        Object rateObj;
        if ( isRFSRequest() ) {
            rateObj = rfsTradeRequest.getProperty( BrokerWorkflowFunctorC.BASE_RATE );
        } else {
            rateObj = tradeRequest.getProperty( BrokerWorkflowFunctorC.BASE_RATE );
        }

        Double rate = null;
        if ( rateObj instanceof String ) {
            rate = Double.valueOf( ( String ) rateObj );
        } else if ( rateObj instanceof Double ) {
            rate = ( Double ) rateObj;
        }

        return rate;
    }

    public double getAverageCoverRate() {
        return averageCoverRate;
    }

    public void setAverageCoverRate(double averageCoverRate ) {
        this.averageCoverRate = averageCoverRate;
    }

    public double getCurrentOrderRate() {
        return currentOrderRate;
    }

    public void setCurrentOrderRate(double currentOrderRate) {
        this.currentOrderRate = currentOrderRate;
    }

    /**
     * returns double property set on trade request
     *
     * @param key key
     * @return rate
     */
    public Double getDoubleProperty( String key ) {
        Object rateObj;
        if ( isRFSRequest() ) {
            rateObj = rfsTradeRequest.getProperty( key );
        } else {
            rateObj = tradeRequest.getProperty( key );
        }

        Double rate = null;
        if ( rateObj instanceof String ) {
            rate = Double.valueOf( ( String ) rateObj );
        } else if ( rateObj instanceof Double ) {
            rate = ( Double ) rateObj;
        }

        return rate;
    }

    public RFSFXRate getRFSFXRate ( RFSFXLeg l ) {
        return getBidOfferRFSFXRate( l, l.getBidOfferMode() );
    }
    public RFSFXRate getBidOfferRFSFXRate( RFSFXLeg l, int boMode ) {
        return boMode == DealingPrice.BID ?
                l.getBidRate() :
                l.getOfferRate();
    }
    // for logging and other processes we may need all the Rate components
    // introduced with SSP - dis not change existing swap behvaiour for back compat
    // [spot, leg1.rate, eg2.rate, ...]
    public double[] getRequestRateComponents() {
        if( !isSSP() ) return new double[] { getRequestRate() };
        List<RFSFXLeg> rLegs = rfsTradeRequest.getTradeLegs();
        double[] pcs = new double[ rLegs.size() + 1];
        for( int li=0; li < rLegs.size(); li++ ) {
            RFSFXLeg l = rLegs.get(li);
            RFSFXRate r = getRFSFXRate( l );
            pcs [0 ] = r.getSpotRate();
            pcs[ li+1 ] = r.getRate();
        }
        return pcs;
    }

    // during test SSP trading only came up for logging sweeps on acceptance
    public double getRequestRate() {    	
    	if (isRFSRequest())
    	{
    		boolean ssp = isSSP();
    		if (ssp)
    		{
    			 // Logic is modified only for SSP request as effective bid/offer mode for SSP request 
    			// may not be same as nearest leg bid offer mode so 
    			// for SSP request take the rate from the nearest leg
    			RFSFXLeg nearestLeg = getNearestLeg();
    			int bidOfferMode = nearestLeg.getBidOfferMode();   
    			double rate = 0.0;
    			// Even if bid/offer mode is bid and bid rate is invalid then check offer rate and vice versa
    			// This rate is used to calculate base amount and approximate base amount to pick up execution rule
    			//is fine for SPP request 
    			if (DealingPrice.BID == bidOfferMode)
    			{
    				// Bid , use bid rate
    				RFSFXRate rateObject = nearestLeg.getBidRate();
    				if (rateObject != null)
    				{
    					rate = rateObject.getRate();
    					if (rate == 0.0)
    					{
    						rateObject = nearestLeg.getOfferRate();
    						if (rateObject != null)
    						{
    							rate = rateObject.getRate();
    						}
    					}
    				}
    				else
    				{
   						rateObject = nearestLeg.getOfferRate();
						if (rateObject != null)
						{
							rate = rateObject.getRate();
						}
    				}
    			} 
    			else
    			{
    				RFSFXRate rateObject = nearestLeg.getOfferRate();
       				if (rateObject != null)
    				{
    					rate = rateObject.getRate();
    					if (rate == 0.0)
    					{
    						rateObject = nearestLeg.getBidRate();
    						if (rateObject != null)
    						{
    							rate = rateObject.getRate();
    						}
    					}
    				}
       				else
       				{
						rateObject = nearestLeg.getBidRate();
						if (rateObject != null)
						{
							rate = rateObject.getRate();
						}
       				}
    			}
				return rate;
    		}
    		return (getBidOfferMode() == DealingPrice.BID ?
                        getNearestLeg().getBidRate().getRate() :
                        getNearestLeg().getOfferRate().getRate());
    		
    	}    
    	return tradeRequest.getRate();
    	/*
        return isRFSRequest() ?
                (getBidOfferMode() == DealingPrice.BID ?
                        getNearestLeg().getBidRate().getRate() :
                        getNearestLeg().getOfferRate().getRate())
                : tradeRequest.getRate();
        */
    }

    public void setRequestRate( double rate ) {
        if ( isRFSRequest() ) {
            switch ( getBidOfferMode() ) {
                case DealingPrice.BID:
                    getRFSTradeRequest().getNearLeg().getBidRate().setRate( rate );
                    break;
                case DealingPrice.OFFER:
                    getRFSTradeRequest().getNearLeg().getOfferRate().setRate( rate );
                    break;
            }
        } else {
            tradeRequest.setRate( rate );
        }
    }

	public double getRequestSpotRate() {
		if (isRFSRequest()) {
			if (!isSSP()) {
				return getBidOfferMode() == DealingPrice.BID ? rfsTradeRequest.getNearLeg().getBidRate().getSpotRate()
						: rfsTradeRequest.getNearLeg().getOfferRate().getSpotRate();
			} else {
				List<RFSFXLeg> tradeLegs = getRFSTradeRequest().getTradeLegs();
				RFSFXLeg rfsfxLeg = tradeLegs.get(0);
				return rfsfxLeg.getBidOfferMode() == DealingPrice.BID ? rfsfxLeg.getBidRate().getSpotRate()
						: rfsfxLeg.getOfferRate().getSpotRate();
			}
		}
		return tradeRequest.getRate();
	}

    public void setRequestSpotRate( double spotRate ) {
        if ( isRFSRequest() ) {
            switch ( getBidOfferMode() ) {
                case DealingPrice.BID:
                    getRFSTradeRequest().getNearLeg().getBidRate().setSpotRate( spotRate );
                    break;
                case DealingPrice.OFFER:
                    getRFSTradeRequest().getNearLeg().getOfferRate().setSpotRate( spotRate );
                    break;
            }
        } else {
            tradeRequest.setRate( spotRate );
        }
    }

    public double getRequestForwardPoints() {
    	if (isRFSRequest()){
    		if(!isSSP()){
    			return getBidOfferMode() == DealingPrice.BID ?
                        rfsTradeRequest.getNearLeg().getBidRate().getForwardPoints() :
                        rfsTradeRequest.getNearLeg().getOfferRate().getForwardPoints();
    		}
    	}
    	
    	return 0;
    }

    public double getRequestFarRate() {
        if ( isRFSRequest() ) {
            if ( rfsTradeRequest.getFarLeg() == null ) {
                return 0;
            }
            return (getFarBidOfferMode() == DealingPrice.BID ?
                    rfsTradeRequest.getFarLeg().getBidRate().getRate() :
                    rfsTradeRequest.getFarLeg().getOfferRate().getRate());
        } else {
            return 0;
        }
    }

    public double getRequestFarSpotRate() {
        if ( isRFSRequest() ) {
            if ( rfsTradeRequest.getFarLeg() == null ) {
                return 0;
            }
            return (getFarBidOfferMode() == DealingPrice.BID ?
                    rfsTradeRequest.getFarLeg().getBidRate().getSpotRate() :
                    rfsTradeRequest.getFarLeg().getOfferRate().getSpotRate());
        } else {
            return 0;
        }
    }

    public double getRequestFarForwardPoints() {
        if ( isRFSRequest() ) {
            if ( rfsTradeRequest.getFarLeg() == null ) {
                return 0;
            }
            return (getFarBidOfferMode() == DealingPrice.BID ?
                    rfsTradeRequest.getFarLeg().getBidRate().getForwardPoints() :
                    rfsTradeRequest.getFarLeg().getOfferRate().getForwardPoints());
        } else {
            return 0;
        }
    }

    public IdcDate getRequestValueDate() {
        IdcDate valueDate;
        if ( isRFSRequest() ) {
            Date jdkDate = rfsTradeRequest.getNearLeg().getValueDate();
            valueDate = DateTimeFactory.newDate( jdkDate );
        } else {
            Date jdkDate = tradeRequest.getValueDate();
            valueDate = DateTimeFactory.newDate( jdkDate );
        }
        return valueDate;
    }

    public IdcDate getRequestFarValueDate() {
        IdcDate valueDate;
        if ( isRFSRequest() ) {
            if ( rfsTradeRequest.getFarLeg() == null ) {
                return null;
            }
            Date jdkDate = rfsTradeRequest.getFarLeg().getValueDate();
            valueDate = DateTimeFactory.newDate( jdkDate );
        } else {
            valueDate = null;
        }
        return valueDate;
    }

    public String getTradeTxnID() {
        return isRFSRequest() ? rfsTradeRequest.getRequestId() : tradeRequest.getTradeId();
    }

    public String getRequestTxnID() {
        return (String) (isRFSRequest() ?
                rfsTradeRequest.getProperty(REQUEST_TXN_ID) :
                tradeRequest.getProperty(REQUEST_TXN_ID));
    }

    public String getOrderID() {
        return ( String ) getProperty( ORDER_ID );
    }

    public String getOrderTxnID() {
        return ( String ) getProperty( ORDER_TXN_ID );
    }

    public String getDealingChannel() {
        String dealingChannel = ( String ) getProperty( DEALING_CHANNEL );

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getDealingChannel-" );
            sb.append( "dealingChannel=" ).append( dealingChannel );
            log.debug( sb.toString() );
        }
        return dealingChannel;
    }

    public String getProviderQuoteId() {
        return isRFSRequest() ? rfsTradeRequest.getProviderQuoteId() : tradeRequest.getProviderQuoteId();
    }

    public Timing getTiming() {
        return isRFSRequest() ? rfsTradeRequest.getTiming() : tradeRequest.getTiming();
    }

    public User getUser() {
        //String userName = getUserShortName();
        //String orgName = getOrgShortName();
        //User user = UserFactory.getUser( userName + "@" + orgName );
        User user = BrokerAdaptorUtil.getInstance().getCoveredUser( this );
        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getUser " );
            sb.append( "{ tradeRequest = " ).append( getTradeRequestMessage() );
            sb.append( ", userName = " ).append( getUserShortName() );
            sb.append( ", orgName = " ).append( getOrgShortName() );
            sb.append( ", user = " ).append( user );
            sb.append( '}' );
            log.debug( sb.toString() );
        }

        return user;
    }

    public String getOriginatingUserID() {
        return isRFSRequest() ? rfsTradeRequest.getOriginatingUser() : tradeRequest.getOriginatingUser();
    }

    public TradingParty getTradingParty() {
        if (party == null) {
            Organization broker = getProvider();
            String leShortName = getLeShortName();
            String orgName = getOrgShortName();
            Organization custOrg = ReferenceDataCacheC.getInstance().getOrganization( orgName );
            LegalEntity le = (LegalEntity) ReferenceDataCacheC.getInstance().getEntityByShortName( leShortName, LegalEntity.class, custOrg.getNamespace(), null );
            party = le.getTradingParty( broker );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getTradingParty " );
                sb.append( "{ tradeRequest = " ).append( getTradeRequestMessage() );
                sb.append( ", broker = " ).append( broker );
                sb.append( ", le = " ).append( le );
                sb.append( ", party = " ).append( party );
                sb.append( ", leShortName = " ).append( leShortName );
                sb.append( ", customerOrg = " ).append( orgName );
                sb.append( '}' );
                log.debug( sb.toString() );
            }
        }
        return party;
    }

    public Organization getOrganization() {
        if (org == null) {
            String orgName = getOrgShortName();
            org = ISUtilImpl.getInstance().getOrg( orgName );

            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getOrganization " );
                sb.append( "{ tradeRequest = " ).append( getTradeRequestMessage() );
                sb.append( ", orgName = " ).append( orgName );
                sb.append( ", org = " ).append( org );
                sb.append( '}' );
                log.debug( sb.toString() );
            }
        }

        return org;
    }

    public Organization getProvider() {
        if( provider == null ){
            String providerName = getProviderShortName();
            provider = ISUtilImpl.getInstance().getOrg( providerName );
            if ( log.isDebugEnabled() ) {
                StringBuilder sb = new StringBuilder( 100 );
                sb.append( this ).append( ".getProvider " );
                sb.append( "{ tradeRequest = " ).append( getTradeRequestMessage() );
                sb.append( ", providerShortName = " ).append( providerName );
                sb.append( ", provider = " ).append( org );
                sb.append( '}' );
                log.debug( sb.toString() );
            }

        }
        return provider;
    }

    public String getOriginatingCptyID() {
        return isRFSRequest() ? rfsTradeRequest.getOriginatingCpty() : tradeRequest.getOriginatingCpty();
    }

    public Integer getPrecision( String propName ) {
        Object precisionObj;
        if ( isRFSRequest() ) {
            precisionObj = rfsTradeRequest.getProperty( propName );
        } else {
            precisionObj = tradeRequest.getProperty( propName );
        }

        Integer precision = null;
        if ( precisionObj instanceof String ) {
            precision = Integer.valueOf( ( String ) precisionObj );
        } else if ( precisionObj instanceof Integer ) {
            precision = ( Integer ) precisionObj;
        }
        return precision;
    }

    public double getFillAmount() {
        return isRFSRequest() ? 0.0 : tradeRequest.getAmount(); //todo RFS amount
    }

    public RFSHandler getRFSHandler() {
    	if(isRFSRequest()){
    		return (RFSHandlerC) rfsTradeRequest.getProperty(TradeRequestFacadeC.RFS_HANDLER);
    	}else{
    		return (RFSHandlerC) tradeRequest.getProperty(TradeRequestFacadeC.RFS_HANDLER);
    	}
    }

    public BrokerTradeHandler getBrokerTradeHandler() {
        if( isRFSRequest() ){
            return ( BrokerTradeHandler ) rfsTradeRequest.getProperty( TRADE_HANDLER );
        } else {
            return ( BrokerTradeHandler ) tradeRequest.getProperty( TRADE_HANDLER );
        }
    }

    public boolean isClientPriceImprovementAllowed() {
        return !getStream().isPriceFixed();
    }

    public String getExternalRequestID() {
        return ( String ) getTradeRequestMessage().getProperty( BrokerWorkflowFunctorC.EXTERNAL_REQUEST_ID );
    }

    public String getOrderType() {
        return ( String ) getProperty( BrokerWorkflowFunctorC.ORDER_TYPE );
    }

    public double getMarketRange() {
        Double range = ( Double ) getProperty( BrokerWorkflowFunctorC.MARKET_RANGE );
        return range != null ? range : -1.0;
    }

    public boolean isCoverMarketRangeEnabled() {
        if (  getExecutionRuleCondition() != null) {
            return getExecutionRuleCondition().getExecutionMethodParameters().isRangeEnabled();
        }
        return false;
    }

    public double getCoverMarketRange() {

        double coverMarketRange = 0.0;
        if (  getExecutionRuleCondition() != null) {

            if( isCoverMarketRangeEnabled()) {

                ExecutionRangeType rangeType = getExecutionRuleCondition().getExecutionMethodParameters().getRangeType();

                if(ExecutionRangeType.PercentSpread.equals(rangeType)){

                    double spreadPercentage = getExecutionRuleCondition().getExecutionMethodParameters().getRange();
                    coverMarketRange = spreadPercentage/100 * spreadCalculator.getFixedSpread();

                } else if(ExecutionRangeType.Pips.equals(rangeType)){

                    double coverMarketPips = getExecutionRuleCondition().getExecutionMethodParameters().getRange();

                    if (coverMarketPips > 0) {

                        double pipsFactor = getBrokerPipsFactor();
                        coverMarketRange = MathUtilC.subtract(MathUtilC.add(coverMarketPips / pipsFactor, 1),
                                1);// this is to avoide 5.700000000000001E-4

                    }
                }
            }

        }

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this ).append( ".getCoverMarketRange " );
            sb.append( "{ transaction id = " ).append( getTradeTxnID() );
            sb.append( ", coverMarketRange = " ).append( coverMarketRange );
            sb.append( '}' );
            log.debug( sb.toString() );
        }

        return coverMarketRange;
    }
    
	public boolean isZeroCoverMarketRange() {

		double coverMarketRange = 0.0;
		if (getExecutionRuleCondition() != null) {
			if (isCoverMarketRangeEnabled()) {
				coverMarketRange = getExecutionRuleCondition().getExecutionMethodParameters().getRange();
			}
		}

		if (log.isDebugEnabled()) {
			StringBuilder sb = new StringBuilder(100);
			sb.append(this).append(".getCoverMarketRangeFromConfig ");
			sb.append("{ transaction id = ").append(getTradeTxnID());
			sb.append(", coverMarketRange = ").append(coverMarketRange);
			sb.append('}');
			log.debug(sb.toString());
		}
		return coverMarketRange == 0.0;
	}



    public double getExecutionMarketRange(){
        double marketRange = getMarketRange();
        marketRange = marketRange == -1 ? 0 : marketRange;
        return MathUtilC.add( marketRange,getCoverMarketRange());
    }

    public boolean isMarket() {
        String orderType = getOrderType();
        return ISCommonConstants.MARKET.equals( orderType ) || ISCommonConstants.STOP_TYPE.equals( orderType )
                || ISCommonConstants.TLSTOP_TYPE.equals( orderType );
    }

    public boolean isLimitOrder() {
        String orderType = getOrderType();
        return ISCommonConstants.MAKEPRICE_CREATE_TYPE.equals( orderType );
    }

    public boolean isStopOrder() {
        String orderType = getOrderType();
        return ISCommonConstants.STOP_TYPE.equals( orderType );
    }

    public boolean isTrailingStopOrder() {
        String orderType = getOrderType();
        return ISCommonConstants.TLSTOP_TYPE.equals( orderType );
    }

    public boolean isMarketOrder() {
        return ISCommonConstants.MARKET.equals( getOrderType() ) && getMarketRange() < 0;
    }

    public boolean isPureMarketOrder() {
        return isTrailingStopOrder() || isStopOrder() || isMarketOrder();
    }

    public boolean isMarketRangeOrder() {
        return ISCommonConstants.MARKET.equals( getOrderType() ) && getMarketRange() >= 0.0;
    }

    public boolean isValidOrderType() {
        return isMarket() || isLimitOrder();
    }

    public boolean isCoverPureOrderMarket() {
        return (ExecutionMethod.MarketIOC.equals(getCoverExecutionMethod())
                || ExecutionMethod.MarketIOCSingle.equals(getCoverExecutionMethod())
                || ExecutionMethod.MarketFOK.equals(getCoverExecutionMethod()))
                && !isCoverMarketRangeEnabled() ;
    }

    public boolean isCoverOrderMarket() {
        return (ExecutionMethod.MarketIOC.equals(getCoverExecutionMethod())
                || ExecutionMethod.MarketIOCSingle.equals(getCoverExecutionMethod())
                || ExecutionMethod.MarketFOK.equals( getCoverExecutionMethod() ) ) ;
    }

    public boolean isCoverOrderLimit() {
        return ExecutionMethod.LimitIOC.equals(getCoverExecutionMethod())
                || ExecutionMethod.LimitIOCSingle.equals(getCoverExecutionMethod())
                || ExecutionMethod.LimitFOK.equals(getCoverExecutionMethod())
                || ExecutionMethod.WeightedAverage.equals(getCoverExecutionMethod());
    }

    public boolean isCoverOrderMarketRange() {
        return (ExecutionMethod.MarketIOC.equals(getCoverExecutionMethod())
                || ExecutionMethod.MarketIOCSingle.equals(getCoverExecutionMethod())
                || ExecutionMethod.MarketFOK.equals(getCoverExecutionMethod()))
                || (brokerAdaptorMBean.isRanageVwapEnabled() &&
                        ExecutionMethod.WeightedAverage.equals(getCoverExecutionMethod()) && getCoverMarketRange() > 0.0)
                && isCoverMarketRangeEnabled();
    }

    public boolean isPureMarketOrderExecution() {
        return isTrailingStopOrder() || isStopOrder() || isMarketOrderExecution();
    }

    public boolean isMarketExecution() {
        return isTrailingStopOrder() || isStopOrder() || isMarketOrderExecution() ||isMarketRangeOrderExecution() || isMarketRangeSyntheticOrderExecution();
    }

    public boolean isMarketOrderExecution(){
        String executionMethod = getExecutionMethod().name();
        return (ExecutionMethod.MarketIOCSingle.name().equals(executionMethod)
                || ExecutionMethod.MarketIOC.name().equals(executionMethod)
                || ExecutionMethod.MarketFOK.name().equals( executionMethod  ));
    }

    public boolean isMarketRangeOrderExecution(){
        String executionMethod = getExecutionMethod().name();
        return (ExecutionMethod.MarketRangeIOCSingle.name().equals(executionMethod)
                || ExecutionMethod.MarketRangeIOC.name().equals(executionMethod)
                || ExecutionMethod.MarketRangeFOK.name().equals( executionMethod  ));
    }

    public boolean isMarketRangeSyntheticOrderExecution(){
        String executionMethod = getExecutionMethod().name();
        return getProduct().isSynthetic() && (ExecutionMethod.LimitFOK.name().equals(executionMethod));
    }

    public boolean isLimitOrderExecution(){
        String executionMethod = getExecutionMethod().name();
        return (ExecutionMethod.LimitIOCSingle.name().equals(executionMethod)
                || ExecutionMethod.LimitIOC.name().equals(executionMethod)
                || ExecutionMethod.LimitFOK.name().equals(executionMethod)
                || ExecutionMethod.WeightedAverage.name().equals(executionMethod));
    }

    public boolean isCustomerFullFillEnabled(){
        ExecutionRuleCondition condition = getExecutionRuleCondition();
        Organization org= getStream().getBrokerOrganizationFunction().getOrganization();
        boolean mmenabled = BrokerAdaptorUtil.isFullFillEnabled(org,getCurrencyPair().getName(),getStream().getShortName(), "RFS");
        return (condition != null && condition.isFullFillEnabled())&&mmenabled;
    }

    public double getBrokerPipsFactor() {
        return getProduct().getRateBasis().getPipsFactor();
    }

    public Object getProperty( String propName ) {
        return isRFSRequest() ? rfsTradeRequest.getProperty( propName ) : tradeRequest.getProperty( propName );
    }

    public double getESPMinimumSpread() {
        return spreadCalculator.getESPMinimumSpread();
    }

    public double getESPMaximumSpread() {
        return spreadCalculator.getESPMaximumSpread();
    }

    public double getRFSMinimumSpread() {
        return spreadCalculator.getRFSMinimumSpread();
    }

    public double getRFSMaximumSpread() {
        return spreadCalculator.getRFSMaximumSpread();
    }

    public double getCoverTradeAmount() {
        if( !isRFSRequest() || !getRFSHandler().getRFSSubscriptionFacade().isSSPRequest() ) return getRequestedAmount();
        return getRFSHandler().getRFSSubscriptionFacade().getNetAmountSSP();
    }

    public double getRequestedAmount() {
        double dealtAmount;
        if ( isRFSRequest() ) {
        		dealtAmount = BrokerAdaptorUtil.getInstance().getRequestedAmount( getRFSTradeRequest() );
        } else {
            dealtAmount = getTradeRequest().getAmount();
        }
        return dealtAmount;
    }

    public double getCustomerOrderAmount() {
        Double cusOrderAmt = (Double)getProperty(BrokerWorkflowFunctorC.CUSTOMER_ORDER_AMT);
        if (!brokerAdaptorMBean.useCustomerOrderAmountForSpread()
                || getOrganization().getBrokerOrganization() == null
                || cusOrderAmt == null) {
            cusOrderAmt = getRequestedAmount();
        }
        return cusOrderAmt;
    }

    public double getRequestedFarAmount() {
        double dealtAmount;
        if ( isRFSRequest() ) {
        		dealtAmount = BrokerAdaptorUtil.getInstance().getRequestedFarAmount( getRFSTradeRequest() );
        } else {
            dealtAmount = getTradeRequest().getAmount();
        }
        return dealtAmount;
    }

    public double getBaseOrderMatchingRate() {
        return ( Double ) getProperty( BASE_ORDER_MATCHING_RATE );
    }

    @Override
    public double getSkew(){
        Double value = (Double)getProperty(SKEW);
        return value != null ? value : 0D;
    }
    @Override
    public void setSkew(double value){
        if(isRFSRequest())
            rfsTradeRequest.setProperty(SKEW, value);
        else
            tradeRequest.setProperty(SKEW, value);
    }

    public double getSpread() {
        return isRFSRequest() ? 0.0 : getESPSpread();
    }

    public double getSwapSpread(){
        return spreadCalculator.getSwapSpread();
    }

    public double getSwapPostSpread(){
        return spreadCalculator.getSwapPostSpread();
    }

    public double getTenorBasedOutrightSpread(){
        return spreadCalculator.getTenorBasedOutrightSpreadJIT();
    }
    
    public double getTenorBasedOutrightSpreadRaw(){
        return spreadCalculator.getTenorBasedOutrightSpreadJITRaw();
    }
    
    public double getTenorBasedSwapSpread(){
        return spreadCalculator.getTenorBasedSwapSpread();
    }

    public double getTenorBasedOutrightPostSpread(double rate, double fwdPts){
        return spreadCalculator.getTenorBasedOutrightPostSpreadJIT(rate, fwdPts);
    }

    public double getTenorBasedSwapPostSpread(){
        return spreadCalculator.getTenorBasedSwapPostSpreadJIT();
    }

    double referenceSwapPointsLP;

    public void setReferenceSwapPointsLP(double d){
        referenceSwapPointsLP = d;
    }

    public double getReferenceSwapPointsLP(){
        return referenceSwapPointsLP;
    }

    double referenceSwapPointsCustomer;

    public void setReferenceSwapPointsCustomer(double d){
        referenceSwapPointsCustomer = d;
    }

    public double getReferenceSwapPointsCustomer(){
        return referenceSwapPointsCustomer;
    }

    public double getSpotSpread(){
        return spreadCalculator.getSpotSpread();
    }
    
    public double getSpotSpreadRaw(){
        return spreadCalculator.getSpotSpreadRaw();
    }

    public double getSpotPostSpread(double rate){
        return spreadCalculator.getSpotPostSpread(rate);
    }

    public double getESPSpread() {
        return spreadCalculator.getESPSpread();
    }
    
    public double getESPSpreadRaw() {
        return spreadCalculator.getESPSpreadRaw();
    }

    public double getESPPostSpread(double rate) {
        return spreadCalculator.getESPPostSpread(rate);
    }


    public double getMinQty() {
        Double minQuantity = (Double)getProperty(BrokerWorkflowFunctorC.MINIMUM_FILL_QTY);
        if (minQuantity == null || (getExecutionRuleCondition().isFullFillEnabled() && (TimeInForce.FOK == getCustomerOrderTIF()) )) {
            return 0;
        } else {
            return minQuantity;
        }
    }

    public ExecutionMethod getExecutionMethod() {
        if (executionMethod == null) {
            ExecutionMethod executionMethod;
            String customerAddExecInt = getCustomerAddExecInst();
            int customerOrderTIF = isCustomerFullFillEnabled() ? TimeInForce.UNDEFINED : getCustomerOrderTIF();
            ExecutionStrategy coverExecStrategy = getCoverExecutionMethod() != null ? getCoverExecutionMethod().getExecutionStrategy() : ExecutionStrategy.BP;
            String coverTimeInForce = getCoverTimeInForce();
            if ( !isRFSRequest() ) {
                if (NCNPV.equals(coverExecStrategy.name()) || NCNPV.equals(customerAddExecInt)) {
                    executionMethod=ExecutionMethod.NoCoverNoPriceValidation;
                } else if (NOC.equals(customerAddExecInt) || NOC.equals(coverExecStrategy.name()) ) {
                    executionMethod= ExecutionMethod.NoCover;
                } else if (isPureMarketOrder() || isCoverPureOrderMarket()) {
                    if (FOK.equals(customerAddExecInt) || FOK.equals(coverTimeInForce) ||
                            (TimeInForce.FOK == customerOrderTIF)) {
                        executionMethod=ExecutionMethod.MarketFOK;
                    } else if (IOC1.equals(customerAddExecInt) || IOC1.equals(coverTimeInForce)) {
                        executionMethod=ExecutionMethod.MarketIOCSingle;
                    } else {
                        executionMethod=ExecutionMethod.MarketIOC;
                    }
                } else if (isMarketRangeOrder() || isCoverOrderMarketRange()) {
                    if (FOK.equals(customerAddExecInt) || FOK.equals(coverTimeInForce) ||
                            (TimeInForce.FOK == customerOrderTIF)) {
                        executionMethod=ExecutionMethod.MarketRangeFOK;
                    } else if (IOC1.equals(customerAddExecInt) || IOC1.equals(coverTimeInForce)) {
                        executionMethod=ExecutionMethod.MarketRangeIOCSingle;
                    } else {
                        executionMethod=ExecutionMethod.MarketRangeIOC;
                    }
                } else if (isLimitOrder() || isCoverOrderLimit()) {
                    if (FOK.equals(customerAddExecInt) || FOK.equals(coverTimeInForce) ||
                            (TimeInForce.FOK == customerOrderTIF)) {
                        executionMethod=ExecutionMethod.LimitFOK;
                    } else if (IOC1.equals(customerAddExecInt) || IOC1.equals(coverTimeInForce)) {
                        executionMethod=ExecutionMethod.LimitIOCSingle;
                    } else {
                        executionMethod=ExecutionMethod.LimitIOC;
                    }
                } else {
                    executionMethod = ExecutionMethod.LimitIOC;
                }
            } else {
                executionMethod = getCoverExecutionMethod();
                if( isFSR() ) {
                    boolean isMDS = false;
                    try {
                        isMDS = getRFSHandler().getRFSSubscriptionFacade().getPricingStrategy() == PricingStrategy.RFS_MDS;
                    }catch(Exception e){}
                    if( isMDS && executionMethod != ExecutionMethod.Manual )
                        executionMethod = ExecutionMethod.NoCoverNoPriceValidation;
                }
            }

            // bug 57541 4.11 BA: 'Cover' execution method should act like 'NoCover NPC' if cover amount is 0
            boolean zeroImpliesForceNCNPV = true;
            if( zeroImpliesForceNCNPV && executionMethod != ExecutionMethod.Manual && getRFSHandler() != null ) {
                RFSRequestFacade rfsRF = getRFSHandler().getRFSSubscriptionFacade();
                if( isSSP() ) {
                    if( rfsRF.getNetAmountSSP() < MathUtilC.epsCCYAMOUNT ) {
                        executionMethod = ExecutionMethod.NoCoverNoPriceValidation;
                    }
                } else if( rfsRF.isMisMatchSwap() ) {
                    if( rfsRF.getMisMatchNetAmount() < MathUtilC.epsCCYAMOUNT ) {
                        // "shouldn't" happen, but isMisMatchSwap code based on (near != far) not ( abs(near - far) < eps)
                        executionMethod = ExecutionMethod.NoCoverNoPriceValidation;
                    }
                } else if( BrokerAdaptorUtil.getInstance().getRequestedAmount( rfsRF.getRFSSubscribe() ) < MathUtilC.epsCCYAMOUNT ) {
                    executionMethod = ExecutionMethod.NoCoverNoPriceValidation;
				} else if (rfsRF.isSwap()
						&& MathUtilC.equal(rfsRF.getDealtAmount(), rfsRF.getFarDealtAmount(), MathUtilC.AMT_RND_FCT)
						&& ((this.getProduct().isSynthetic() && !isRFSChecked()) || ((isSpotMDSChecked() || isSpotAndSwapEnabled()) && BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(getProviderShortName()).isEspRfsEvenSwapNoCoverEnabled(this.getProviderShortName())))) {
                    log.info("getExecutionMethod: Using NoCoverNoPriceValidation executionMethod as the request is Even SWAP for Synthetic ESP RFS. TradeId=" + this.getRequestTxnID());
                    executionMethod = ExecutionMethod.NoCoverNoPriceValidation;
                }
            }

            this.executionMethod = executionMethod;
        }

        return executionMethod;
    }
    
    /**
     * This method will return true if only spot&mds is checked in the PriceMaking configuration page
     * @return true/false
     */
	private boolean isSpotMDSChecked() {
		ExecutionRuleCondition ruleCondition = getExecutionRuleCondition();
		if (ruleCondition == null)
			return false;
		boolean spotMdsChecked = ruleCondition.getExecutionPricingParameters().isSpotAndMDSEnabled();
		boolean rfsChecked = ruleCondition.getExecutionPricingParameters().isRFSEnabled();
		
		if (!rfsChecked && spotMdsChecked)
			return true;

		return false;
	}

    /**
     * This method will return true if RFS is checked in the PriceMaking configuration page
     * @return true/false
     */
    private boolean isRFSChecked() {
        ExecutionRuleCondition ruleCondition = getExecutionRuleCondition();
        if (ruleCondition == null)
            return false;
        return ruleCondition.getExecutionPricingParameters().isRFSEnabled();
    }


    public String getCustomerOrderType() {
        String customerOrderType;
        if (isPureMarketOrder()) {
            customerOrderType = ExecutionMethod.MarketIOC.getOrderType();
        } else if (isMarketRangeOrder()) {
            customerOrderType = ExecutionMethod.MarketRangeIOC.getOrderType();
        } else if (isLimitOrder()) {
            customerOrderType = ExecutionMethod.LimitIOC.getOrderType();
        } else {
            customerOrderType = ExecutionMethod.LimitIOC.getOrderType();
        }

        return customerOrderType;
    }

    public ExecutionMethod getCoverExecutionMethod() {
        boolean mmProduct = product.getConfiguration().isMarketMaker();
        boolean mmUsingNoCover = MarketMakerConfigMBean.YMMode.NO_YM.equals(MarketMakerConfig.getInstance().getYMMode(getProviderShortName()));
        if ( coverExecutionMethod == null && getExecutionRuleCondition() != null) {
        	CoverExecutionMethod coverExecutionMethod = getExecutionRuleCondition().getExecutionMethodParameters().getCoverExecutionMethod();
            if(mmProduct && mmUsingNoCover && CoverExecutionMethod.Warehouse.equals(coverExecutionMethod)){
                boolean npc =   getExecutionRuleCondition().getExecutionMethodParameters().isNoPriceCheckEnabled();
                if(log.isDebugEnabled()) { log.debug("Using NoCover for " + getProviderShortName() + ", " + getCurrencyPair() + ", " + getStream()+", npc: "+npc); }
                if(npc){
                    this.coverExecutionMethod =  ExecutionMethod.NoCoverNoPriceValidation;
                }else {
                    this.coverExecutionMethod = ExecutionMethod.NoCover;
                }
            }else {
                this.coverExecutionMethod = getExecutionRuleCondition().getExecutionMethod();
            }
        }
        return coverExecutionMethod;
    }

    public ExecutionRuleCondition getExecutionRuleCondition(){
        if (executionRuleCondition == null) {
            if( isRFSRequest()){
                executionRuleCondition = getRFSHandler().getRFSSubscriptionFacade().getExecutionRuleCondition();
            } else {
                executionRuleCondition = ExecutionRuleConditionCalculator.getInstance().calculateEspExecutionRule( getConfiguration() ,this);
            }
        }
        return executionRuleCondition;
    }

    public Collection<PriceWrapper> createRFSDummyNoCoverPrices() {
        RFSRequestFacade rfsSubscriptionFacade = getRFSHandler().getRFSSubscriptionFacade();
        String extReqId = getTradeTxnID() + "_dummy";
        String guid = getTradeTxnID() + "_DUMMY";
        double dealtAmount = rfsSubscriptionFacade.getDealtAmount();
        double farDealtAmount = rfsSubscriptionFacade.getFarDealtAmount();
        boolean isSwap = rfsSubscriptionFacade.isSwap();
        boolean isSSP = rfsSubscriptionFacade.isSSPRequest();
        if(isSSP){
            Map<String, Double> dealtAmtMap = new HashMap<String, Double>();
            Map<String, Double> bidFwdPtsMap = new HashMap<String, Double>();
            Map<String, Double> offerFwdPtsMap = new HashMap<String, Double>();
            Map<String, Boolean> isBidMap = new HashMap<String, Boolean>();
            double bidSpotRate = 0;
            double offerSpotRate = 0;
            for(RFSFXLeg leg : rfsTradeRequest.getTradeLegs()){
                String legName = leg.getName();
                boolean bid = leg.getBidOfferMode() == DealingPrice.BID;
                RFSFXRate rate = bid ? leg.getBidRate() : leg.getOfferRate();
                dealtAmtMap.put(legName, rate.getDealtAmount());
                if(bid) bidFwdPtsMap.put(legName, rate.getForwardPoints());
                else offerFwdPtsMap.put(legName, rate.getForwardPoints());
                isBidMap.put(legName, bid);
                if(bid) bidSpotRate = rate.getSpotRate();
                else offerSpotRate = rate.getSpotRate();
            }
            Quote quote = BrokerAdaptorUtil.getInstance().createSSPDummyQuote(new OrganizationC(), getCurrencyPair(), ESPRFSPublisher.synthetic, extReqId, getRateBasis(),
                    guid, dealtAmtMap, isBidMap, bidSpotRate, offerSpotRate, bidFwdPtsMap, offerFwdPtsMap, getRFSHandler().getRFSSubscriptionFacade());
            PriceWrapper wrapper = SSPPriceWrapperC.newFrom(quote);
            ArrayList<PriceWrapper> priceWrappers = new ArrayList<PriceWrapper>();
            priceWrappers.add(wrapper);
            return priceWrappers;
        }
        boolean isBid = getBidOfferMode() == DealingPrice.BID;
        RFSFXLeg rfsFxLeg = rfsTradeRequest.getNearLeg();
        RFSFXRate rfsRate = isBid ? rfsFxLeg.getBidRate() : rfsFxLeg.getOfferRate();
        double spot = rfsRate.getSpotRate();
        double farFwdPts = 0;
        double nearFwdPts = rfsRate.getForwardPoints();
        RFSFXLeg farLeg = rfsTradeRequest.getFarLeg();
        if (farLeg != null){
            rfsRate = !isBid ? farLeg.getBidRate() : farLeg.getOfferRate();
            farFwdPts = rfsRate.getForwardPoints();
        }
		Double nearBidSpotRate = rfsFxLeg.getBidRate() != null ? rfsFxLeg.getBidRate().getSpotRate() : null;
		Double nearOfferSpotRate = rfsFxLeg.getOfferRate() != null ? rfsFxLeg.getOfferRate().getSpotRate() : null;
		Double midSpotRate = BrokerAdaptorUtil.getInstance().getMid(nearBidSpotRate, nearOfferSpotRate);

        Quote quote = BrokerAdaptorUtil.getInstance()
                .createRFSDummyQuote(new OrganizationC(), getCurrencyPair(), getDealtCurrency(), ESPRFSPublisher.synthetic, extReqId,
                        getRateBasis(), guid, dealtAmount, farDealtAmount, isBid, spot,
                        nearFwdPts, farFwdPts, !isBid, spot, nearFwdPts, farFwdPts,midSpotRate, getRFSHandler().getRFSSubscriptionFacade(), 0, 0, null, null);
        PriceWrapper wrapper;
        // todo SSP accept PriceWrapper createRFSDummyNoCoverPrices
        if (isSwap){
            FXLegDealingPrice nearDP = (FXLegDealingPrice) quote.getQuotePrice(FXLegDealingPrice.NEAR_LEG);
            FXLegDealingPrice farDP = (FXLegDealingPrice) quote.getQuotePrice(FXLegDealingPrice.FAR_LEG);
            wrapper = new SwapPriceWrapperC(nearDP, farDP, dealtAmount, farDealtAmount);
        } else {
            FXLegDealingPrice dp = (FXLegDealingPrice) quote.getQuotePrice(FXLegDealingPrice.SINGLE_LEG);
            wrapper = new RFSPriceWrapperC(dp, dealtAmount);
        }
        ArrayList<PriceWrapper> priceWrappers = new ArrayList<PriceWrapper>();
        priceWrappers.add(wrapper);
        return priceWrappers;
    }

    public String getCustomerAddExecInst() {
        String clientExecutionMethodStr =
                (String) getTradeRequestMessage().getProperty(ISCommonConstants.EXECUTION_METHOD);
        clientExecutionMethodStr = (clientExecutionMethodStr == null) ? "" : clientExecutionMethodStr;
        return clientExecutionMethodStr;
    }
    


    public String getCoverTimeInForce() {
        return getCoverExecutionMethod() != null ? getCoverExecutionMethod().getTimeInForce() : IOC;
    }

    public ExecutionStrategy getExecutionStrategy() {
        return (ExecutionMethod.LimitIOC.equals( getExecutionMethod() )
                ||  ExecutionMethod.MarketRangeIOC.equals( getExecutionMethod() )
                ||  ExecutionMethod.NoCover.equals( getExecutionMethod() )) ?
                getCoverExecutionMethod().getExecutionStrategy() : ExecutionStrategy.BP;
    }

    public boolean isVwapExecution() {
        return ExecutionStrategy.VWAP.equals( getExecutionStrategy() ) ;
    }


    public int getCustomerOrderTIF() {
        Integer tifStr = ( Integer ) getTradeRequestMessage().getProperty( ISCommonConstants.CAP_Order_TimeInForce);
        int tif;
        try {
            tif =  (tifStr == null) ? TimeInForce.UNDEFINED : tifStr;
        } catch (NumberFormatException e) {
            tif = TimeInForce.UNDEFINED;
            if(log.isDebugEnabled()) {
                e.printStackTrace();
            }
        }
        return tif;
    }

    public boolean isCoverTradingDisabled() {
        if( coverTradingDisabled == null ){
            boolean isCoverTradingDisabled =  getExecutionMethod() != null && getExecutionMethod().isCoverTradingDisabled();
            coverTradingDisabled = isCoverTradingDisabled || isWarehouseEnabled();
        }
        return coverTradingDisabled;
    }

    public boolean isSyntheticCross(){
        if( syntheticCross == null ){
            syntheticCross = getProduct() != null && getProduct().isSynthetic();
        }
        return syntheticCross;
    }

    public boolean isWarehouseEnabled() {
        if( warehouseEnabled == null ){
            boolean isWarehouseExecutionEnabled = false;
            boolean isWarehouseOptionEnabled = false;
            boolean isWarehouseSupported = false;
            CoverExecutionMethod coverExecMthd = getCoverExecutionMethod(getExecutionRuleCondition());
            if (isRFSRequest()) {
                if ( RWProvisionConfigManager.getInstance().getRwProvisionConfig().isRFSNetSpotEnabledForRiskWarehouse(getProviderShortName())) {
                    isWarehouseExecutionEnabled = getExecutionRuleCondition() != null &&
                            coverExecMthd == CoverExecutionMethod.Warehouse;
                    isWarehouseOptionEnabled = YMUtil.isWarehouseOptionEnabled(getProviderShortName());
                    isWarehouseSupported = YMUtil.isRiskWarehouseEnabled(getCurrencyPair().getName(), getProviderShortName(),getYMBookNameForCoverExecutionMethod(coverExecMthd));
                }
            } else {
                isWarehouseExecutionEnabled = getExecutionRuleCondition() != null &&
                        coverExecMthd == CoverExecutionMethod.Warehouse;
                isWarehouseOptionEnabled = YMUtil.isWarehouseOptionEnabled(getProviderShortName());
                isWarehouseSupported = YMUtil.isRiskWarehouseEnabled(getCurrencyPair().getName(), getProviderShortName(),getYMBookNameForCoverExecutionMethod(coverExecMthd));
            }
            warehouseEnabled = isWarehouseExecutionEnabled && isWarehouseOptionEnabled && isWarehouseSupported;
            log.info("warehouseEnabled="+warehouseEnabled+", isRFS="+isRFSRequest()+", ccyPair="+getCurrencyPair()+", synthetic="+isSyntheticCross());
        }
        return warehouseEnabled;
    }

    public EMSExecutionType getExecutionType() {
        if (isCoverTradingDisabled()) {
            return isWarehouseEnabled() ? EMSExecutionType.WAREHOUSE : EMSExecutionType.NO_COVER;
        } else {
            return EMSExecutionType.COVER;
        }
    }

    public void addVerifiedCoverTrades( String transactionID, Trade trade ) {
        listVerifiedCoverTrades.put( transactionID, trade );
        // be sure to flush cached info
        strVerifiedMakerRefIDs = null;
        strVerifiedTxnIDs = null;
    }

    public boolean isOneWayRequest(){
        if (isRFSRequest()){
            RFSSubscribe subscribe = ( RFSSubscribe ) getRFSTradeRequest().getProperty( REQUEST );
            if (subscribe.getTradeLegs().get(0).getBidOfferMode() != DealingPrice.TWO_WAY){
                return true;
            }
        }
        return false;
    }

    public ConcurrentHashSet getOrderMatchedQuotes() {
        return appliedQuotes;
    }

    public void addToOrderMatchedQuotes(String orderMatchedRateId){
        appliedQuotes.add(orderMatchedRateId );
    }

    public boolean isOrderMatchedQuote(String orderMatchedRateId ){
        return appliedQuotes.contains(orderMatchedRateId);
    }

    private StringBuilder convertToString( Collection<Trade> trades ) {
        StringBuilder _strIds = new StringBuilder( 100 );
        boolean isFirstEntry = true;
        for ( Trade trade : trades ) {
            if (!isFirstEntry) _strIds.append( HYPHEN );
            _strIds.append( trade.getMakerReferenceId() );
            isFirstEntry = false;
        }
        return _strIds;
    }

    private StringBuilder convertToString1( Collection<String> ids ) {
        StringBuilder _strIds = new StringBuilder( 100 );
        boolean isFirstEntry = true;
        for ( String txnId : ids ) {
            if (!isFirstEntry) _strIds.append( HYPHEN );
            _strIds.append( txnId );
            isFirstEntry = false;
        }
        return _strIds;
    }

    public void setCoverOrderId(String orderId) {
        coverOrderId = orderId;
    }

    public String getCoverOrderId() {
        return coverOrderId;
    }

    public void setCoverOrderTxnId( String orderTxnId ) {
        coverOrderTxnId = orderTxnId;
    }

    public String getCoverOrderTxnId() {
        return coverOrderTxnId;
    }

    public String getCoveredTradeTakerReferenceId() {
        return isRFSRequest() ? rfsTradeRequest.getTakerReferenceId() : tradeRequest.getTakerReferenceId();
    }

    public boolean isCustBrokerTradeCreated() {
        return isCustBrokerTradeCreated;
    }

    public void setCustBrokerTradeCreated(boolean flag) {
        isCustBrokerTradeCreated = flag;
    }

    public Collection<Trade> getVerifiedCoverTrades(){
        if (listVerifiedCoverTrades.isEmpty() && espTradeRequestFacade != null){
            return espTradeRequestFacade.getVerifiedCoverTrades();
        } else {
            return Collections.unmodifiableCollection( listVerifiedCoverTrades.values() );
        }
    }

    public void setSingleProvider(Organization org){
        if ( isSingleProviderTrade()){
            singleProvider = org;
        }
    }

    public Organization getSingleProvider() {
        return singleProvider;
    }

    private boolean isSingleProviderTrade() {
        return getExecutionMethod() == ExecutionMethod.LimitFOK
                || getExecutionMethod() == ExecutionMethod.LimitIOCSingle
                || getExecutionMethod() == ExecutionMethod.MarketFOK
                || getExecutionMethod() == ExecutionMethod.MarketIOCSingle
                || getExecutionMethod() == ExecutionMethod.MarketRangeFOK
                || getExecutionMethod() == ExecutionMethod.MarketRangeIOCSingle;
    }

    public LegalEntity getCoverTradeLegalEntity(){
        if (coverTradeLE == null && singleProvider != null){
            coverTradeLE = BrokerAdaptorUtil.getInstance().getBrokerOrgLEFor(getProvider(), singleProvider);
        }
        return coverTradeLE;
    }

    public TradingParty getCoverTradeTradingParty(){
        LegalEntity le = getCoverTradeLegalEntity();
        if (le != null){
            return le.getTradingParty(singleProvider);
        }
        return null;
    }

    public LegalEntity getCoverTradeLegalEntity( Organization provider ) {
        LegalEntity le = null;
        if ( isSingleProviderTrade() || product.getConfiguration().isMultiFillEnabled() ) {
            le = BrokerAdaptorUtil.getInstance().getBrokerOrgLEFor(getProvider(), provider );
        }
        return le;
    }

    public Set<PriceWrapper> createDummyNoCoverPrices() {
        Set<PriceWrapper> noCoverPrices = new HashSet<PriceWrapper>();
        PriceWrapper fillPrice = new PriceWrapperC(createCoverDealingPrice(), getFillAmount());

        noCoverPrices.add(fillPrice);
        return noCoverPrices;
    }

    private FXLegDealingPrice createCoverDealingPrice() {
        double amount = getRequestedAmount();
        double coverRate = getRequestRate();

        FXPrice price = FXPriceFactory.newFXPrice();

        FXDealingPriceElement dpe = FXDealingFactory.newFXDealingPriceElementDependent();
        dpe.setPrice( price );

        // Create FXLegDealingPrice
        FXLegDealingPrice dp = FXDealingFactory.newFXLegDealingPrice();
        dp.setBidOfferMode( getBidOfferMode() );
        dp.setValueDate( getRequestValueDate() );
        dp.setDealtCurrencyProperty( getDealtCurrencyProperty() );
        dp.setDealtCurrency( getDealtCurrency() );
        dp.setSettledCurrency( getSettledCurrency() );
        dp.setDealtAmount( amount );
        dp.setPriceElement( dpe );
        dp.setAcceptedDealingPrice( dp );

        FXRate rate = FXFactory.newFXRate();
        rate.setBaseCurrency(dp.getDealtCurrency());
        rate.setVariableCurrency(dp.getSettledCurrency());
        rate.setRate(coverRate);
        rate.setSpotRate(coverRate);

        if (getBidOfferMode() == DealingPrice.BID) {
            price.setBidFXRate( rate );
        } else {
            price.setOfferFXRate( rate );
        }
        Quote quote = DealingFactory.newQuote();
        quote.setPriceType(Quote.PRICE_TYPE_MULTI_TIER);
        quote.setQuotePrice(FXLegDealingPrice.SINGLE_LEG, dp);
        dp.setQuote(quote);
        OrganizationC dummy = new OrganizationC();
        dummy.setShortName("NCNPV_SYNTHETIC");
        quote.setOrganization(dummy);
        return dp;
    }

    public boolean isMakerTradeNotificationDisabled(){
        Object property = tradeRequest.getProperty(ISConstantsC.MAKER_NOTIFICATION_DISABLED);
        return property != null && property.equals("1");
    }

    public boolean isMoreThanMinTradeSize(double amount, Organization org, Organization toOrg) {
        if (getCoverType() == AUTO_COVER) return true;
        Double minTradeSize = getMinTradeSize(org, toOrg);
        boolean isMinTradeSizePassed = !(minTradeSize != null && minTradeSize > amount);
        if (!isMinTradeSizePassed) {
            log.info(new StringBuilder().append(this.getClass().getName()).append(" Match for ")
                    .append(getTradeTxnID()).append(" matched amount ").append(amount)
                    .append(" is less than min Trade size ").append(minTradeSize).toString());
        }
        return isMinTradeSizePassed;
    }

    public Double getMinTradeSize(Organization org, Organization toOrg){
        CurrencyPair ccyPair = getCurrencyPair();
        Double minTradeSize = ISFactory.getInstance().getISMBean().getMinimumTradeSize(org, ccyPair);
        if(ISFactory.getInstance().getISMBean().isLiquidityProvisioningEnabled(org.getShortName()))
        {
            LiquidityProvision provision = null;
            if (org.getShortName().equals(getOrganization().getShortName()))
            {
                provision = getBrokerCustomerLiquidityProvision();
            }
            else if (org.getShortName().equals(getProvider().getShortName()))
            {
                provision = getBrokerLiquidityProvision();
            }
            else
            {
                provision = ISUtilImpl.getLiquidityProvision(org, ccyPair);
            }
            minTradeSize = provision == null ? null : provision.getHierarchicalMinMatchSize();
        }

        if (!isDealingBaseCurrency() && (minTradeSize != null && minTradeSize != 0)) {
            minTradeSize = MDSFactory.getInstance()
                    .convertAmount(toOrg.getShortName(), ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(),
                            minTradeSize, getTradeTxnID(), true);
        }
        return minTradeSize;
    }

    public void addToMatchedProvider(Organization provider) {
        matchedOrganizations.add(provider);
    }

    public Collection<Organization> getMatchedProviders() {
        return matchedOrganizations;
    }

    public String getExcludedProviders() {
        return excludedProviders;
    }

    public List<Organization> getOrderProviders() {
        if (getExecutionMethod().getTimeInForce().equals(FOK)) {
            return nonPartialFillOrderProviders;
        } else {
            return orderProviders;
        }
    }

    public List<Organization> getNonPartialFillOrderProviders() {
        return nonPartialFillOrderProviders;
    }

    public boolean isFarLegSpot(){
        return getRFSHandler() != null && getRFSHandler().getRFSSubscriptionFacade().isFarTenorSpot();
    }

    public List<Organization> getOrderProvidersFromConfig(){
        Configuration configuration = getConfiguration();

        List<Organization> tempProviders = null;
	    if(useSwapProviders){
            tempProviders =  configuration.getSwapProviders();
        }
	    else {
            if (configuration.isLiquidityGroupOnly()) {
                tempProviders = new ArrayList<Organization>(configuration.getLiquidityGroupOrderProviders(product.getCurrencyPair().getName()));
            } else {
                tempProviders = configuration.getOrderProviders();
            }
        }

        if ( log.isInfoEnabled()) {

            StringBuilder sb = new StringBuilder( 100 );
            sb.append( this.getClass().getName() ).append( ".getOrderProvidersFromConfig " );
            sb.append( "{ useSwapProviders = " ).append( useSwapProviders );
            sb.append( ", providers = " ).append(getOrgList( tempProviders) );
            if(rfsTradeRequest != null)
                sb.append(", RFS ");
            else
                sb.append(", ESP");
            sb.append( '}' );
            log.info( sb.toString() );
        }
	    return tempProviders;
    }

    protected String getOrgList(Collection<Organization> providers) {
        StringBuilder sb = new StringBuilder(15 * providers.size());
        for (Organization org : providers) {
            sb.append(org.getShortName()).append('|');
        }
        return sb.toString();
    }
    public void initializeProviders() {
        long acceptanceReceivedByAdaptor = getTiming().getTime(AdaptorConstantC.EVENT_TIME_DISP_ADAPTER_REC_ACC).getTime();
        Configuration configuration = getConfiguration();

        orderProviders = getOrderProvidersFromConfig();
        Collection<Organization> excludedProviders = configuration.getExcludedProviders();
        Collection<Organization> spreadProviders = configuration.getSpreadProviders();
        Collection<Organization> excludedProvidersFromClient = toArray(
                (String) getTradeRequest().getProperty(ISCommonConstants.EXCLUSION_LIST_TO_BA));
        StringBuilder sb = new StringBuilder().append("TradeRequestFacadeC.initializeProviders " );
        if (!excludedProviders.isEmpty() ||!excludedProvidersFromClient.isEmpty()) {
            List<Organization> allOrderProviders = BrokerAdaptorUtil.getInstance().getAllOrderProviders(configuration, product.getCurrencyPair());
            int sendExcludedProviders = 0;

            int acceptanceReceived = (int) (acceptanceReceivedByAdaptor % 10);
            Set<Integer> randomLogic = configuration.getRandomLogic();
            boolean isRandom = randomLogic.contains(acceptanceReceived);
            Collection<Organization> allExcludedProviders;
            if(!isRandom){  // if random kicks in, all excluded providers are considered.
                allExcludedProviders = new HashSet<Organization>();
                allExcludedProviders.addAll(excludedProviders);

                allExcludedProviders.addAll(excludedProvidersFromClient);
            } else {// only excluded providers from clients are considered and rest list is send to next level.
                allExcludedProviders = excludedProvidersFromClient;
                sendExcludedProviders = 1;
            }



            if (allExcludedProviders.containsAll(allOrderProviders)){// if no order providers left, do not use any exclusion list.
                sendExcludedProviders = 2;
                allExcludedProviders = Collections.emptySet();
            }

            if (allExcludedProviders.isEmpty()){
                orderProviders = allOrderProviders;
            } else {
                orderProviders = new ArrayList<Organization>();
                orderProviders.addAll(allOrderProviders);
                orderProviders.removeAll(allExcludedProviders); // remove all exclusion providers from config providers.
            }

            switch (sendExcludedProviders){
                case 1 :
                    this.excludedProviders = toString(excludedProviders);
                    break;
                case 2 :
                    if (!(excludedProviders.isEmpty() && excludedProvidersFromClient.isEmpty()) ){
                        Collection<Organization> organizations = new HashSet<Organization>();
                        organizations.addAll(excludedProvidersFromClient);
                        organizations.addAll(excludedProviders);
                        this.excludedProviders = toString(organizations);
                    }
                    break;
            }


            sb.append(getTradeTxnID()).append(" ").append(randomLogic).append(" ").append(acceptanceReceived)
                    .append(" EP ").append(toString(excludedProviders)).append(" HP ")
                    .append(toString(configuration.getHiddenProviders())).append(" CP ")
                    .append(toString(getOrderProvidersFromConfig())).append(" EPC ")
                    .append((String) getTradeRequest().getProperty(ISCommonConstants.EXCLUSION_LIST_TO_BA))
                    .append(" EPS ").append(this.excludedProviders);
        }

        // Populate the non partial fill order providers.
        Set<Organization> partialFillProviders = brokerAdaptorMBean.getPartialFillProviders();
        nonPartialFillOrderProviders = new ArrayList<Organization>();

        for (Organization orderProvider : orderProviders) {
            Organization realOrderProviderLP = orderProvider.getRealLP() != null ? orderProvider.getRealLP() : orderProvider;
            if (!partialFillProviders.contains(realOrderProviderLP)) {
                nonPartialFillOrderProviders.add(orderProvider);
            }
        }

        if (log.isDebugEnabled()) {
            StringBuilder stringBuilder = new StringBuilder(this.getClass().getName()).append(".initializeProviders() Partial/Order providers.")
                    .append(' ').append(orderProviders)
                    .append(' ').append(partialFillProviders)
                    .append(' ').append(nonPartialFillOrderProviders);

            log.debug(stringBuilder.toString());
        }
        //nonPartialFillOrderProviders.removeAll(partialFillProviders);

        if( !spreadProviders.isEmpty() ) {
            spreadedProvider = spreadProviders.iterator().next().getShortName() ;
        }

        setECNBAOnlyProvider();


        sb.append(getTradeTxnID()).append(' ')
                .append(" SP ").append(toString(configuration.getSpreadProviders())).append(' ')
                .append("PP ").append(toString(partialFillProviders)).append(' ')
                .append("OP ").append(toString(orderProviders));

        log.info(sb.toString());

    }

    public static Set<String> toStringArray(String orgList){
        Set<String> organizations = Collections.emptySet();
        if (orgList != null && orgList.trim().length() != 0) {
            char[] chars = orgList.trim().toCharArray();
            organizations = new HashSet<String>();
            StringBuilder orgName = new StringBuilder();
            for (char aChar : chars) {
                switch (aChar) {
                    case ISCommonConstants.EXCLUDED_PROVIDERS_SEPARATOR:
                        String shortName = orgName.toString();
                        //Organization organization = ReferenceDataCacheC.getInstance().getOrganization(shortName);
                        organizations.add(shortName);
                        orgName = new StringBuilder();
                        break;
                    default:
                        orgName.append(aChar);
                }
            }
            String shortName = orgName.toString();
            //Organization organization = ReferenceDataCacheC.getInstance().getOrganization(shortName);
            organizations.add(shortName);
        }
        return organizations;
    }

    private Collection<Organization> toArray(String orgList) {
        Set<String> orgs = toStringArray(orgList);
        Set<Organization> organizations = new HashSet<Organization>();
        for(String org : orgs){
            Organization organization = ReferenceDataCacheC.getInstance().getOrganization(org);
            organizations.add(organization);
        }
        return organizations;
    }

    private String toString(Collection<Organization> organizations) {
        if (organizations.isEmpty()) {
            return null;
        }
        StringBuilder sb = new StringBuilder(organizations.size() * 10);
        Iterator<Organization> iterator = organizations.iterator();
        while (iterator.hasNext()) {
            Organization organization = iterator.next();
            sb.append(organization.getShortName());
            if (iterator.hasNext()){
                sb.append(ISCommonConstants.EXCLUDED_PROVIDERS_SEPARATOR);
            }
        }
        return sb.toString();
    }

    public void providerResponse(Organization provider, boolean isRejected){
        AtomicInteger count = providersRejectCount.get(provider);
        if (count == null && isRejected){
            count = new AtomicInteger(0);
            providersRejectCount.putIfAbsent(provider, count);
            count = providersRejectCount.get(provider);
        }
        if (isRejected){
            count.incrementAndGet();
        } else{
            if (count != null) count.set(0);
        }
    }

    public boolean validateCircuit(Organization provider){
        AtomicInteger counter = providersRejectCount.get(provider);
        if (counter != null && counter.get() != 0){
            int thresholdLimit = brokerAdaptorMBean.getProviderRejectionThreshold(
                    provider);
            if (thresholdLimit <= counter.get()) return true;
        }
        return false;
    }

    public TradeRequestFacade getESPTradeRequestFacade(){
        return espTradeRequestFacade;
    }

    public void setEspTradeRequestFacade(TradeRequestFacade espTradeRequestFacade) {
        this.espTradeRequestFacade = espTradeRequestFacade;
    }

    public boolean isSpreadedProviderMatchingEnabled() {
        return (spreadedProvider != null);
    }

    public boolean isSpreadedProvider(String provider) {
        return spreadedProvider != null && spreadedProvider.equalsIgnoreCase(provider);
    }

    public double getCustomerFilledAmount() {
        return customerFilledAmount;
    }

    public void addCustomerFilledAmount(double filledAmt) {
        this.customerFilledAmount = this.customerFilledAmount + filledAmt;
    }

    public Long getCustomerOrderExpirationTime() {
        Object time = tradeRequest.getProperty(ISCommonConstants.CAP_Order_ExpireTime);
        Long customerExpirationTime = null;

        if (time != null) {
            customerExpirationTime = ((Timestamp)time).getTime();
        }
        return customerExpirationTime;
    }

    public long getStreamTIF(){
        if ( interval == null){
            if( isMarketExecution()) {
                interval = getStream().getMarketTimeInForceInterval();
            } else {
                interval = getStream().getTimeInForceInterval();
            }
        }
        return interval;
    }

    private boolean isESPZeroPercentageFullFill() {
        return !isRFSRequest() && getExecutionRuleCondition().isFullFillEnabled() && getExecutionRuleCondition().getFullFill() <= 0;
    }

    /**
     * Logic is mentioned in
     * http://wiki/wiki/index.php/Product_Management/Use_Customer_Order%27s_Expiration_Time
     * @return
     */
    public long getEffectiveCoverTIFPeriod() {
        if (effectiveCoverTIFPeriod == null) {
            String brokerOrgShortName = getProviderShortName();
            String stream = getStream().getName();
            boolean isCustomerExpirationTimeEnabled = brokerAdaptorMBean.isCustomerExpirationTimeEnabled(brokerOrgShortName, stream);

            long streamTIFInterval = getStreamTIF();
            long effectiveTIFInterval;
            Long customerExpirationTime = null;
            long submitOrderMinExpiryTime = -1;
            long customerIOCFOKExpirationTime = -1;
            int customerTIF = TimeInForce.UNDEFINED;

            // In case of instantaneous response, use internal property driven TIF for the 0% full fill esp execution.
            // This change is done to send the customer response quickly if execution rule is configured for 0% full fill.
            if (isInstanceResponseEnabled() && isESPZeroPercentageFullFill()) {
                streamTIFInterval = brokerAdaptorMBean.getZeroPercentFullFillTIF();
            }

            if (brokerAdaptorMBean.isSingleSweepEnabled() && isCustomerRequestedSingleSweep()){
                effectiveTIFInterval = 0;
            } else if (isCustomerExpirationTimeEnabled && !isRFSRequest()) {
                customerTIF = getCustomerOrderTIF();

                if (customerTIF == TimeInForce.GTC) {
                    effectiveTIFInterval = streamTIFInterval;
                } else if (customerTIF == TimeInForce.FOK || customerTIF == TimeInForce.IOC || customerTIF == TimeInForce.GTD) {
                    customerExpirationTime = getCustomerOrderExpirationTime();

                    if (customerExpirationTime != null) {
                        submitOrderMinExpiryTime = brokerAdaptorMBean.getSubmitOrdersMinExpirationTime(brokerOrgShortName, stream);

                        long absoluteTime = customerExpirationTime - System.currentTimeMillis();
                        effectiveTIFInterval = Math.min(absoluteTime, streamTIFInterval);

                        effectiveTIFInterval =  effectiveTIFInterval < submitOrderMinExpiryTime ? submitOrderMinExpiryTime : effectiveTIFInterval;

                    } else {
                        if (customerTIF == TimeInForce.GTD) {
                            effectiveTIFInterval = streamTIFInterval;
                        } else {
                            customerIOCFOKExpirationTime = brokerAdaptorMBean.getCustomerIOCFOKExpirationTime(brokerOrgShortName, stream);
                            effectiveTIFInterval = customerIOCFOKExpirationTime <= 0 ? streamTIFInterval : customerIOCFOKExpirationTime;
                        }
                    }
                } else {
                    effectiveTIFInterval = streamTIFInterval;
                }
            } else {
                effectiveTIFInterval = streamTIFInterval;
            }

            if(log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(this.getClass().getName()).append(".getEffectiveCoverTIF()")
                        .append(' ').append(getTradeTxnID())
                        .append(' ').append(brokerOrgShortName)
                        .append(' ').append(stream)
                        .append(' ').append(effectiveTIFInterval);
                log.debug(sb.toString());
            }

            Organization customerOrg = getOrganization();
            Organization brokerOrg = ReferenceDataCacheC.getInstance().getOrganization( brokerOrgShortName );
            boolean isBrokerForCustomer = brokerOrg.isSameAs(customerOrg.getBrokerOrganization());
            if (!isBrokerForCustomer) {
                long nonCustomerTIF = brokerAdaptorMBean.getNonCustomerTIF(brokerOrgShortName);
                if (nonCustomerTIF == 0) {
                    effectiveTIFInterval = 0; // equivalent of Single Sweep
                } else if (nonCustomerTIF > 0) {
                    effectiveTIFInterval = Math.min(nonCustomerTIF, effectiveTIFInterval);
                }
            }


            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(this.getClass().getName()).append(".getEffectiveCoverTIF()")
                        .append(' ').append(getTradeTxnID())
                        .append(' ').append(brokerOrgShortName)
                        .append(' ').append(stream)
                        .append(' ').append(isBrokerForCustomer)
                        .append(' ').append(brokerAdaptorMBean.isSingleSweepEnabled())
                        .append(' ').append(isCustomerRequestedSingleSweep())
                        .append(' ').append(isCustomerExpirationTimeEnabled)
                        .append(' ').append(streamTIFInterval)
                        .append(' ').append(customerTIF)
                        .append(' ').append(customerExpirationTime == null ? "" : customerExpirationTime.longValue())
                        .append(' ').append(submitOrderMinExpiryTime)
                        .append(' ').append(customerIOCFOKExpirationTime)
                        .append(' ').append(effectiveTIFInterval);
                log.info(sb.toString());
            }

            effectiveCoverTIFPeriod = effectiveTIFInterval;
            effectiveCoverTIFTime = new Timestamp(System.currentTimeMillis() + effectiveCoverTIFPeriod);
        }
        return effectiveCoverTIFPeriod;
    }

    private boolean isCustomerRequestedSingleSweep() {
        return "T".equals(getProperty(BrokerWorkflowFunctorC.DO_SINGLE_SWEEP));
    }

    public Timestamp getEffectiveCoverTIFTime() {
        if (effectiveCoverTIFTime == null) {
            // Calculate the effective cover TIF period and time.
            getEffectiveCoverTIFPeriod();
        }

        return effectiveCoverTIFTime;
    }

    public boolean isECNBAOnlyOrderProviders() {
        return isECNBAOnlyOrderProviders;
    }

    public ConcurrentMap<Organization, AtomicInteger> getProvidersRejectCount() {
        return providersRejectCount;
    }

    public void setOrderMatchedQuotes(ConcurrentHashSet appliedQuotes) {
        this.appliedQuotes = appliedQuotes;
    }

    public void setProvidersRejectCount(ConcurrentMap<Organization, AtomicInteger> providersRejectCount) {
        this.providersRejectCount = providersRejectCount;
    }

    public int getCoverType() {
        return REGULAR_COVER;
    }

    public WorkflowMessage getControlMsg() {
        return controlMsg;
    }

    public void setControlMsg(WorkflowMessage controlMsg) {
        this.controlMsg = controlMsg;
    }

    public boolean isRFSESPSupported() {
        return espTradeRequestFacade != null;
    }

    public boolean isWarmUpRequest() {
        return isWarmUpRequest;
    }

    public double getTopOfTheBookRate() {
        return 0;
    }

    public void setTopOfTheBookRate(double topOfTheBookRate) {
        //do nothing;
    }

    public boolean isAutoCoverEnabled(){
        if( isWarehouseEnabled() ){
            return false;
        }
        return getExecutionRuleCondition().isAutoCoverEnabled() && getExecutionRuleCondition().isFullFillEnabled();
    }

    public void setUnwind(boolean isUnwind) {
        this.isUnwind = isUnwind;
    }

    public boolean isUnwind() {
        return isUnwind;
    }

    public void setMatchingStartTime(long matchingStartTime) {
        this.matchingStartTime = matchingStartTime;
    }

    public long getMatchingStartTime() {
        return matchingStartTime;
    }

    public ExecutionMethod getCurrentExecutionMethod() {
        return currentExecutionMethod;
    }

    public void setCurrentExecutionMethod(ExecutionMethod currentExecutionMethod) {
        this.currentExecutionMethod = currentExecutionMethod;
    }

    public double getRFSPriceProvisionSpotSpread(){
        return spreadCalculator.getRFSPriceProvisionSpotSpread();
    }

    public Double getRFSPriceProvisionFwdSpread(String legName){
        return spreadCalculator.getRFSPriceProvisionFwdSpread(legName);
    }

    public double getRFSPriceProvisionNearFwdSpread(){
        return spreadCalculator.getRFSPriceProvisionNearFwdSpread();
    }

    public double getRFSPriceProvisionFarFwdSpread(){
        return spreadCalculator.getRFSPriceProvisionFarFwdSpread();
    }

    @Override
    public LiquidityProvision getBrokerCustomerLiquidityProvision()
    {
        if (liquidityProvision == null)
        {
            liquidityProvision = ISUtilImpl.getLiquidityProvision(null, getOrganization(), getCurrencyPair());
        }
        return liquidityProvision;
    }

    @Override
    public void setBrokerCustomerLiquidityProvision(LiquidityProvision liqudityProvision)
    {
        this.liquidityProvision = liqudityProvision;
    }

    @Override
    public LiquidityProvision getBrokerLiquidityProvision()
    {
        if (providerLiquidityProvision == null)
        {
            providerLiquidityProvision = ISUtilImpl.getLiquidityProvision(null, getProvider(), getCurrencyPair());
        }
        return providerLiquidityProvision;
    }

    @Override
    public void setBrokerLiquidityProvision(LiquidityProvision liqudityProvision)
    {
        this.providerLiquidityProvision = liquidityProvision;
    }

    public boolean isFSR() {
        return isRFSRequest() && rfsTradeRequest.isFSR();
    }

    public boolean isSSP() {
        return isRFSRequest() && ISConstantsC.TRD_CLSF_FXSSP.equals( rfsTradeRequest.getTradeClassification() ); // todo SSPlater optimise JIT & cache
    }

    public SpreadCalculatorC getSpreadCalculator() {
        return spreadCalculator;
    }

	@Override
	public boolean isNetted() {
		if ( isRFSRequest() )
		{
			Object isNetRequest = rfsTradeRequest.getProperty(ISCommonConstants.RFS_SUBSCRIPTION_IS_NET);
			boolean isNetted = false;
			if ( isNetRequest != null )
			{
				isNetted = "true".equals( String.valueOf( isNetRequest ) );
			}
			return isNetted;
		}
		return false;
	}

    @Override
    public String getOriginatingOrderId() {
        if(isRFSRequest()) {
            return rfsTradeRequest.getOriginatingOrderid();
        }else {
            return tradeRequest.getOriginatingOrderid();
        }
    }

    public double getSwapPointSpreadLP() {
        return swapPointSpreadLP;
    }

    public void setSwapPointSpreadLP(double swapPointSpreadLP) {
        this.swapPointSpreadLP = swapPointSpreadLP;
    }

    public double getSwapPointSpreadCust() {
        return swapPointSpreadCust;
    }

    public void setSwapPointSpreadCust(double swapPointSpreadCust) {
        this.swapPointSpreadCust = swapPointSpreadCust;
    }

    @Override
    public boolean isCoverOnMTF(){
        if(coverOnMTF == null) {
            boolean override = product.getConfiguration().getOverrideStreamMifidParams() != null && product.getConfiguration().getOverrideStreamMifidParams();
            boolean streamValue = BrokerMiFIDUtil.isCoverOnMTF(product, override);
            boolean enabled = MiFIDUtils.isMiFIDEnabled(getProvider());
            boolean mifidTenor = isMiFIDEligibleTenor();
            coverOnMTF = streamValue && enabled && mifidTenor;
        }
        return coverOnMTF;
    }

    @Override
    public boolean isFilterNonMiFIDProviders(){
        if(filterNonMiFIDLps == null) {
            boolean mtf = isCoverOnMTF();
            CoverExecutionMethod executionMethod = getCoverExecutionMethod(getExecutionRuleCondition());
            boolean execMthd = executionMethod.equals(CoverExecutionMethod.Cover) || executionMethod.equals(CoverExecutionMethod.Warehouse);
            boolean rfsEnabled = getExecutionRuleCondition().getExecutionPricingParameters().isRFSEnabled();
            filterNonMiFIDLps = mtf && execMthd && rfsEnabled;
        }
        return filterNonMiFIDLps;
    }

    public boolean isMiFIDEligibleTenor(){
        if(isSSP() || getRFSHandler().getRFSSubscriptionFacade().isSwap()){
            return true;
        }
        if(getRequestValueDate() != null) {
            return MiFIDUtils.isMiFIDEligibleTenor(product.getBroker(), product.getCurrencyPair(), getRequestValueDate());
        }
        return false;
    }

    private CoverExecutionMethod getCoverExecutionMethod(ExecutionRuleCondition erc){
        boolean mmProduct = product.getConfiguration().isMarketMaker();
        boolean mmUsingNoCover = MarketMakerConfigMBean.YMMode.NO_YM.equals(MarketMakerConfig.getInstance().getYMMode(getProviderShortName()));
        CoverExecutionMethod coverExecutionMethod = erc.getExecutionMethodParameters().getCoverExecutionMethod();
        if(mmProduct && mmUsingNoCover && CoverExecutionMethod.Warehouse.equals(coverExecutionMethod)){
            if(log.isDebugEnabled()) { log.debug("Using NoCover for " + getProviderShortName() + ", " + getCurrencyPair() + ", " + getStream()); }
            return CoverExecutionMethod.NoCover;
        }else {
            return erc != null ? erc.getExecutionMethodParameters().getCoverExecutionMethod() : null;
        }
    }

    protected String getYMBookNameForCoverExecutionMethod(CoverExecutionMethod coverExecutionMethod){
        switch (coverExecutionMethod){
            case Warehouse:
                return ISCommonConstants.YM_BOOK_NAME_B;
        }
        return null;
    }

	@Override
	public double getRFSDealerSpotSpread() {
		double pipsFactor = getRateBasis().getPipsFactor();
		DealerSpread dealerSpread = getRFSHandler().getDealerSpread();
		double dealerSpotSpread = getCoverBidOfferMode() == DealingPrice.BID ? dealerSpread.getBidSpotSpread() : dealerSpread.getOfferSpotSpread()*-1;
		if(isMisMatchSwap() && getDealtAmount() > getFarDealtAmount()){
			dealerSpotSpread = getCoverBidOfferMode() == DealingPrice.BID ? dealerSpread.getOfferSpotSpread()*-1 : dealerSpread.getBidSpotSpread();
		}
		return dealerSpotSpread/pipsFactor;
	}

	@Override
	public double getRFSDealerFwdSpread() {
		double pipsFactor = getRateBasis().getPipsFactor();
		DealerSpread dealerSpread = getRFSHandler().getDealerSpread();
		double dealerFwdSpread = getBidOfferMode() == DealingPrice.BID ? dealerSpread.getBidFwdSpread() : dealerSpread.getOfferFwdSpread()*-1;
		return dealerFwdSpread/pipsFactor;
	}

	@Override
	public double getRFSDealerFarFwdSpread() {
		double pipsFactor = getRateBasis().getPipsFactor();
		DealerSpread dealerSpread = getRFSHandler().getDealerSpread();
		double dealerFarFwdSpread = getBidOfferMode() == DealingPrice.BID ? dealerSpread.getOfferFarFwdSpread()*-1 : dealerSpread.getBidFarFwdSpread();
		return dealerFarFwdSpread/pipsFactor;
	}

	@Override
	public double getReferenceRate() {
		return getRFSHandler().getReferenceRate();
	}

	@Override
	public List<TradeRequestFacade> getSpotAndSwapTradeRequestFacade() {
		return spotAndSwapTradeRequestFacade;
	}

	@Override
	public boolean isNearLegSpot() {
		return getRFSHandler() != null && getRFSHandler().getRFSSubscriptionFacade().isNearTenorSpot();
	}

	@Override
	public TradeRequestFacade getParentTradeRequestFacade() {
		return null;
	}

	@Override
	public boolean isSpotAndSwapTradeRequest() {
		return false;
	}

	@Override
	public boolean isOutright() {
		return isRFSRequest() ? ISConstantsC.TRD_CLSF_OR.equals(rfsTradeRequest.getTradeClassification()) :  false;
	}

	@Override
    public boolean isSwap(){
        return isRFSRequest() ? (ISCommonConstants.TRD_CLSF_SWAP.equals(rfsTradeRequest.getTradeClassification()) || ISCommonConstants.TRD_CLSF_FWD.equals((rfsTradeRequest.getTradeClassification()))): false;
    }

    @Override
    public boolean isNDFOrNDFSwap(){
        return isRFSRequest() ? (ISCommonConstants.TRD_CLSF_FXNDF.equals(rfsTradeRequest.getTradeClassification()) || ISCommonConstants.TRD_CLSF_FXNDF_SWAP.equals((rfsTradeRequest.getTradeClassification()))): false;
    }

    @Override
    public  boolean isFwdFwd(){
        return isRFSRequest() ? (ISCommonConstants.TRD_CLSF_FWD.equals((rfsTradeRequest.getTradeClassification()))): false;
    }

	@Override
	public boolean isSpotAndSwapEnabled() {
		return getExecutionRuleCondition().getExecutionPricingParameters().isSpotAndSwapEnabled()
				&& ((isSSP() && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceSSPUsingSwapEnabled(getProviderShortName()))
				|| (isOutright() && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceOTUsingSwapEnabled(getProviderShortName()))
                || (isNDFOrNDFSwap() && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceNDFAndNDFSwapUsingSpotAndSwapEnabled(getProviderShortName()))
                || (isSwap() && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isPriceSwapUsingSpotAndSwapEnabled(getProviderShortName())));
	}

	@Override
	public boolean isCoverOnSpotAndSwapEnabled() {
		ExecutionMethodParameters executionMethodParameters = getExecutionRuleCondition().getExecutionMethodParameters();
		boolean isEnabled =  isSpotAndSwapEnabled()
				&& executionMethodParameters.getSpotAndSwapCoverExecutionMethod() == CoverExecutionMethod.Cover
				&& executionMethodParameters.getCoverExecutionMethod() != CoverExecutionMethod.Manual;
        boolean isEspRfsEvenSwapNoCoverEnabled = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(getProviderShortName()).isEspRfsEvenSwapNoCoverEnabled(this.getProviderShortName());
		if(isEnabled == true && isSwap() == true && isMisMatchSwap() == false && isEspRfsEvenSwapNoCoverEnabled == false ){
	        isEnabled = false;
        }
        return isEnabled;
    }
	
	@Override
	public double getFwdPointsDifference(){
		return fwdPointsDifference;
	}
	
	@Override
	public void setFwdPointsDifference(double fwdPointsDifference){
		this.fwdPointsDifference = fwdPointsDifference;
	}


    @Override
    public String getMT300Field72() {
        RFSRequestFacade requestFacade = getRFSHandler().getRFSSubscriptionFacade();
        return  requestFacade.getMT300Field72();
    }

    public boolean isAtBestOrderRFS()
    {
        return atBestOrderRFS;
    }

    @Override
    public boolean isRfqTrade() {
        return rfqTrade;
    }

    @Override
    public boolean isSkipValueDateMisMatchProvider() {
        return skipValueDateMisMatchProvider;
    }

    @Override
    public void setSkipValueDateMisMatchProvider(boolean skipValueDateMisMatchProvider) {
        this.skipValueDateMisMatchProvider = skipValueDateMisMatchProvider;
    }

    @Override
    public boolean isIgnoreRejectRFS() {
        return ignoreRejectRFS;
    }

    @Override
    public void setIgnoreRejectRFS(boolean ignoreRejectRFS) {
        this.ignoreRejectRFS = ignoreRejectRFS;
    }
}