package com.integral.is.fix.client.handler;

import java.util.Map;

import javax.jms.JMSException;

import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.exception.IdcException;
import com.integral.finance.dealing.Quote;
import com.integral.fix.client.FixConfiguration;
import com.integral.fix.client.subscription.ProviderSubscription;
import com.integral.fix.client.subscription.SubscriptionManager;
import com.integral.fix.client.util.FixUtilC;
import com.integral.is.common.MessageNormalizer;
import com.integral.is.common.cache.bucket.MessageNotifier;
import com.integral.is.common.cache.bucket.MessageNotifierType;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.persistence.CacheFactory;

/**
 *
 */
public class FixRateHandler implements MessageHand<PERSON>, MessageNotifier, SubscriptionManager, AggregationResponseHandler<Response, FXPriceBook>
{
	private Log log = LogFactory.getLog(this.getClass());

	public static final String YES = "Y";
	public static final String MULTI_TIER_QUOTE = "MultiTierQuote";

	protected ProviderSubscription singleProviderSubscription;
	protected ProviderSubscription multiProviderSubscription;
	FixConfiguration config = FixUtilC.getInstance().getFixConfiguration();

	public FixRateHandler()
	{
	}

	public Message handle( Message message ) throws IdcException
	{
		return null;
	}

	public void sendRate( Quote quote, Map properties )
	{
		if ( quote == null )
		{
			log.warn("RateHandler.sendRate : Quote is null ");
			return;
		}
		addRate(quote);
	}

    @Override
    public MessageNotifierType getType() {
        return MessageNotifierType.Fix;
    }

    public void addRate( Quote quote )
	{
		if ( singleProviderSubscription != null )
		{
			singleProviderSubscription.sendRate(quote);
		}

		if ( multiProviderSubscription != null )
		{
			multiProviderSubscription.sendRate(quote);
		}
	}

	public ProviderSubscription getSingleProviderSubscription()
	{
		return singleProviderSubscription;
	}

	public void setSingleProviderSubscription( ProviderSubscription singleProviderSubscription )
	{
		this.singleProviderSubscription = singleProviderSubscription;
	}

	public ProviderSubscription getMultiProviderSubscription()
	{
		return multiProviderSubscription;
	}

	public void setMultiProviderSubscription( ProviderSubscription multiProviderSubscription )
	{
		this.multiProviderSubscription = multiProviderSubscription;
	}

	public boolean isMultiProviderSubscribed()
	{
		return multiProviderSubscription != null && multiProviderSubscription.isSubscribed();
	}

	public boolean isSingleProviderSubscribed()
	{
		return singleProviderSubscription != null && singleProviderSubscription.isSubscribed();
	}
	/* (non-Javadoc)
	 * @see com.integral.aggregation.AggregationResponseHandler#onCompleted(java.lang.Object)
	 */
	public void onCompleted( Response response )
	{
		// TODO Auto-generated method stub
		
	}
	/* (non-Javadoc)
	 * @see com.integral.aggregation.AggregationResponseHandler#onPriceUpdate(java.lang.Object)
	 */
	public void onPriceUpdate( FXPriceBook book )
	{
		if ( singleProviderSubscription != null )
		{
			singleProviderSubscription.sendPriceBook(book);
		}

		if ( multiProviderSubscription != null )
		{
			multiProviderSubscription.sendPriceBook(book);
		}
	}

	@Override
	public String getSubscriptionKey(){
		return singleProviderSubscription != null ? singleProviderSubscription.toString() :
				multiProviderSubscription != null ? multiProviderSubscription.toString() : "FIX_RATE_HANDLER";
	}
}
