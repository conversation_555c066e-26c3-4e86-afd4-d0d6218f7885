package com.integral.is.spaces.fx.listener;

import com.integral.adaptor.response.ResponseHandler;
import com.integral.imtp.message.BaseIMTPMessage;
import com.integral.imtp.message.IMTPApplicationMessage;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.DirectedOrderCommonMBean;
import com.integral.is.common.tv.MatchingVenueResponseMessageHandler;
import com.integral.is.message.*;
import com.integral.is.message.directed.orders.request.DOCMessage;
import com.integral.is.message.directed.orders.responses.*;
import com.integral.is.message.directed.orders.responses.OrderResponse;
import com.integral.is.message.directed.orders.serializer.DirectedOrderSerializer;
import com.integral.is.message.rfs.RFSMarketRateC;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.handler.AdaptorResponseHandler;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.persistence.util.LogUtil;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.ObjectMessage;
import javax.jms.TextMessage;

/**
 * class used in handing of message from the receiver thread to the
 * threads in executor.
 */
public class TradeListener implements Runnable {
    private static Log log = LogFactory.getLog(TradeListener.class);
    /**
     * Threshold for the maximum amount of time taken to process a trade
     * response. If processing of a trade response takes more than this
     * threshold, we log a warning.
     */
    protected static final long THRESHOLD_TIME_MS = 5;

    protected final String TRADE_RESPONSES_CLASS_NAME = TradeResponsesC.class
            .getName().toLowerCase();

    private IMTPApplicationMessage imtpMessage;
    private Message jmsMessage;
    private final boolean isJmsMessage;



    protected long sentTimeStamp;



    protected long receivedTime;
    protected String serialTradeMessageListenerKey;

    public TradeListener(boolean isJmsMessage) {
        this.isJmsMessage = isJmsMessage;
    }

    public TradeListener(IMTPApplicationMessage imtpMessage,
                         long receivedTime) {
        this.imtpMessage = imtpMessage;
        this.jmsMessage = null;
        this.isJmsMessage = false;
        this.receivedTime = receivedTime;
        this.sentTimeStamp = imtpMessage.getIMTPSentTime();
    }

    public TradeListener(Message jmsMessage, long receivedTime)
            throws JMSException {
        this.jmsMessage = jmsMessage;
        this.imtpMessage = null;
        this.isJmsMessage = true;
        this.receivedTime = receivedTime;
        this.sentTimeStamp = jmsMessage.getJMSTimestamp();
    }

    public void setJmsMessage(Message jmsMessage) {
        this.jmsMessage = jmsMessage;
        try {
            this.sentTimeStamp = jmsMessage != null ? jmsMessage
                    .getJMSTimestamp() : 0L;
        } catch (JMSException ex) {
            log.warn("Exception while reading timestamp from JMS Message. Setting the sent time to '0'."
                    + " Exception: " + ex.getMessage());
        }
    }

    public void setImtpMessage(IMTPApplicationMessage imtpMessage1) {
        this.imtpMessage = imtpMessage1;
        this.sentTimeStamp = imtpMessage1 != null ? imtpMessage1
                .getIMTPSentTime() : 0L;
    }

    @Override
    public void run() {
        try {
            LogUtil.setSwitch(true);
            String type = null;
            if (!isJmsMessage) {
                type = imtpMessage.getAppDataType();
            }

            if (sentTimeStamp > 0
                    && receivedTime - sentTimeStamp > THRESHOLD_TIME_MS) {
                // TODO-METRICS: Add metrics for this.
                log.info("TradeListener.run: Time taken to receive the trade message ms="
                        + (receivedTime - sentTimeStamp)
                        + ": isJMSMessage: " + isJmsMessage);
            }

            if (jmsMessage instanceof TextMessage
                    || IMTPApplicationMessage.APP_DATA_TYPE_STRING
                            .equals(type)) {
                String text = null;
                String objType = null;
                String formatType = null;
                boolean isResponses = false;
                try {
                    if (this.isJmsMessage) {
                        TextMessage textMessage = (TextMessage) jmsMessage;
                        text = textMessage.getText();
                        objType = textMessage.getStringProperty("objType");
                    } else {
                        text = (String) imtpMessage.getApplicationData();
                        objType = imtpMessage.getProperty("objType");
                        formatType = imtpMessage
                                .getProperty(ResponseHandler.STRING_FORMAT);
                    }

                    isResponses = objType != null
                            && TRADE_RESPONSES_CLASS_NAME.equals(objType
                                    .toLowerCase());
                } catch (Exception e) {
                    // TODO-RISK: handle the risk notification
                    log.error(
                            "TradeListener.run: Exception while handling trade message: "
                                    + (isJmsMessage ? jmsMessage
                                            : imtpMessage), e);
                }

                if (text != null) {
                    handleStringMessage(text, objType, formatType,
                            isResponses);
                }

            } else if (jmsMessage instanceof ObjectMessage
                    || IMTPApplicationMessage.APP_DATA_TYPE_OBJECT
                            .equals(type)) {
                Object message = null;
                try {
                    if (this.isJmsMessage) {
                        ObjectMessage objMsg = (ObjectMessage) jmsMessage;
                        message = objMsg.getObject();
                    } else {
                        message = imtpMessage.getApplicationData();
                    }
                } catch (Exception ex) {
                    // TODO-RISK: Handle risk notification.
                    log.error(
                            "TradeListener.run: Exception while handling the trade message: "
                                    + (isJmsMessage ? jmsMessage
                                            : imtpMessage), ex);
                }
                if (message != null) {
                    handleObjectMessage(message,
                            message instanceof TradeResponses);
                }
            } else {
                // TODO-RISK: Handle risk notification.
                log.error("TradeListener.run: Unable to handle the received message: "
                        + imtpMessage);
            }

        } finally {
            if (!this.isJmsMessage) {
                ((BaseIMTPMessage) imtpMessage).decrementReference(); // Release
                                                                        // the
                                                                        // object
                                                                        // to
                                                                        // the
                                                                        // pool.
            }
            LogUtil.removeSwitch();
        }
    }

    void setReceivedTime(long receivedTime) {
        this.receivedTime = receivedTime;
    }

    public void setSerialTradeMessageListenerKey(
            String serialTradeMessageListenerKey) {
        this.serialTradeMessageListenerKey = serialTradeMessageListenerKey;
    }

    public void handleObjectMessage(Object obj, boolean isResponses) {

        if (!RFSMarketRateC.class.getName().equalsIgnoreCase(
                obj.getClass().getName())) {
            log.info("handleObjectMessage : Received the message: " + obj
                    + " of type: " + obj.getClass().getName());
        }

        AdaptorResponseHandler handler = SpacesTradeListener.getHandler(obj.getClass()
                .getName());

        if (handler != null) {
            if (!isResponses) {
                ISMessage response = (ISMessage) obj;

                if (response instanceof BrokerOrderResponse) {
                    BrokerOrderResponse bor = (BrokerOrderResponse) response;
                    bor.setResponseSentByAdaptorTime(sentTimeStamp);
                    bor.setResponseReceivedByAppTime(receivedTime);
                } else {
                    response.getTiming()
                            .setTime(
                                    ISConstantsC.EVENT_TIME_DISP_ADAPTER_SENT_RESPONSE,
                                    sentTimeStamp);
                    response.getTiming()
                            .setTime(
                                    AdaptorResponseHandler.EVENT_TIME_RESPONSE_RECEIVED_BY_APP,
                                    receivedTime);
                }

                // Give the response to handler.
                handler.handleResponse(response);

                // Remove the serial trade message processor after receiving
                // the status response.
                if (obj instanceof TradeStatusResponse) {
                    SerialTradeMessageProcessorManager.removeSerialTradeMessageProcessor(serialTradeMessageListenerKey);
                } else if (obj instanceof BrokerOrderResponse) {
                    BrokerOrderResponse res = (BrokerOrderResponse) obj;
                    switch (res.getResponseType()) {
                    case STATUS_RESPONSE:
                    case REJECT:
                        SerialTradeMessageProcessorManager.removeSerialTradeMessageProcessor(serialTradeMessageListenerKey);
                        break;
                    }
                } else if (obj instanceof TradeResponse) {
                    SingleLegTrade request = FXESPWorkflowCache
                            .getTrade(((TradeResponse) response)
                                    .getTradeId());
                    if (request != null && request.isAutoCancelled()) {
                        SerialTradeMessageProcessorManager.removeSerialTradeMessageProcessor(serialTradeMessageListenerKey);
                    }
                }

            } else {

                TradeResponses tradeResponses = (TradeResponses) obj;

                // Set the timestamp for each TradeResponse.
                for (TradeResponse resp : tradeResponses
                        .getTradeResponses()) {
                    resp.getTiming()
                            .setTime(
                                    ISConstantsC.EVENT_TIME_DISP_ADAPTER_SENT_RESPONSE,
                                    sentTimeStamp);
                    resp.getTiming()
                            .setTime(
                                    AdaptorResponseHandler.EVENT_TIME_RESPONSE_RECEIVED_BY_APP,
                                    receivedTime);
                }

                // Give the response to handler.
                handler.handleResponse(tradeResponses);
                SerialTradeMessageProcessorManager.removeSerialTradeMessageProcessor(serialTradeMessageListenerKey);
            }
        } else if (obj.getClass().isArray()) {
            try {
                byte[] msg = (byte[]) obj;
                DOCMessage docMessage = DirectedOrderSerializer
                        .deserialize(msg, (byte) DirectedOrderCommonMBean.getInstance().getDoSerializableVesrion());
                long s0 = System.currentTimeMillis();
                log.info("handleObjectMessage : docMessage=" + docMessage);
                if(log.isDebugEnabled()){
                    try {
                        log.debug("handleObjectMessage : docMessage=" + docMessage);
                    }
                    catch (Throwable th){
                        log.debug("handleObjectMessage : Exception in logging docMessage=",th);
                    }
                }
                if (docMessage instanceof OrderFillResponse) {
                    MatchingVenueResponseMessageHandler.getInstance()
                            .handleFillResponse(
                                    (OrderFillResponse) docMessage);
                } else if (docMessage instanceof UnsolicitedCancel) {

                } else if (docMessage instanceof OrderExpiry) {
                    MatchingVenueResponseMessageHandler.getInstance()
                            .handleExpiryResponse((OrderExpiry) docMessage);
                } else if (docMessage instanceof OrderStatusResponse) {
                    MatchingVenueResponseMessageHandler.getInstance()
                            .handleStatusResponse(
                                    (OrderStatusResponse) docMessage);
                } else if (docMessage instanceof OrderRejectResponse) {
                    MatchingVenueResponseMessageHandler.getInstance()
                            .handleRejectResponse(
                                    (OrderRejectResponse) docMessage);
                }else if (docMessage instanceof OrderResponse) {
                    OrderResponse response = (OrderResponse) docMessage;
                    response.getEventTimes()[ISCommonConstants.RESPONSE_RECVD_BY_HANDLER_POS] = receivedTime;
                    response.getEventTimes()[ISCommonConstants.RESPONSE_RECVD_BY_SERIALIZER_POS] = s0;
                    switch (response.getRespType()) {
                        case ResponseType.AMENDED:
                            MatchingVenueResponseMessageHandler.getInstance().handleAmendSuccess(response);
                            break;
                        case ResponseType.AMEND_REJECT:
                            MatchingVenueResponseMessageHandler.getInstance().handleAmendReject(response);
                            break;
                        case ResponseType.PENDING_AMEND:
                            MatchingVenueResponseMessageHandler.getInstance().handlePendingAmend(response);
                            break;

                    }
                }
            } catch (Exception e) {
                log.error("Error while de-serializing imtp message: ", e);
            } finally {
                ((BaseIMTPMessage) imtpMessage).decrementReference();
            }
        } else {

            // TODO-RISK: Handle risk notification.
            log.error("TradeListener.handleTradeObject: Received unknown TradeResponse of type: "
                    + obj.getClass().getName() + " from adaptor.");
        }
    }

    private void handleStringMessage(String text, String objType,
            String formatType, boolean isResponses) {
        if (objType != null) {
            if (!RFSMarketRateC.class.getName().equalsIgnoreCase(objType)) {
                log.info("handleStringMessage : Received the message: "
                        + text + " of type: " + objType);
            }
            // get the correct handler for the incoming objectType
            AdaptorResponseHandler handler = SpacesTradeListener.getHandler(objType);

            if (handler != null) {
                // populate the TradeResponse object from the serialized
                // string.
                Object obj = null;
                try {
                    obj = handler.deserializeObject(text, formatType);
                } catch (Exception e) {
                    // TODO-RISK: Handle risk notification.
                    log.error("handleStringMessage : Could not deserialize the received message: "
                            + text + " of type: " + objType, e);
                }
                if (obj == null) {
                    log.warn("handleStringMessage : Null obj returned after deserializing String for Object Type: "
                            + objType);
                } else {
                    handleObjectMessage(obj, isResponses);
                }
            } else {
                // TODO-RISK: Handle risk notification.
                log.error("handleStringMessage : Received unknown response of type: "
                        + objType + " from adaptor.");
            }
        } else {
            // TODO-RISK: Handle risk notification.
            log.error("handleStringMessage : Received a message with objType as null.");
        }
    }

    public long getSentTimeStamp() {
        return sentTimeStamp;
    }

    public void setSentTimeStamp(long sentTimeStamp) {
        this.sentTimeStamp = sentTimeStamp;
    }
    public long getReceivedTime() {
        return receivedTime;
    }
}
