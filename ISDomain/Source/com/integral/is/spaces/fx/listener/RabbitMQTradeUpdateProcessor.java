package com.integral.is.spaces.fx.listener;

import com.integral.is.message.BrokerOrderResponse;
import com.integral.is.message.ISMessage;
import com.integral.is.spaces.fx.handler.AdaptorResponseHandler;
import com.integral.log.Log;
import com.integral.log.LogFactory;

class RabbitMQTradeUpdateProcessor implements Runnable {
    private static Log log = LogFactory.getLog(RabbitMQTradeUpdateProcessor.class);
    private ISMessage tradeResponse;
    private long receivedTime;

    public RabbitMQTradeUpdateProcessor(ISMessage obj, long receivedTime) {
        tradeResponse = obj;
        this.receivedTime = receivedTime;
    }

    @Override
    public void run() {
        AdaptorResponseHandler handler = SpacesTradeListener.getHandler(tradeResponse
                .getClass().getName());

        if (handler != null) {

            if (tradeResponse instanceof BrokerOrderResponse) {
                BrokerOrderResponse bor = (BrokerOrderResponse) tradeResponse;
                // bor.setResponseSentByAdaptorTime(sentTimeStamp);
                bor.setResponseReceivedByAppTime(receivedTime);
            } else {
                /*
                 * response.getTiming() .setTime( ISConstantsC.
                 * EVENT_TIME_DISP_ADAPTER_SENT_RESPONSE, sentTimeStamp);
                 */
                tradeResponse
                        .getTiming()
                        .setTime(
                                AdaptorResponseHandler.EVENT_TIME_RESPONSE_RECEIVED_BY_APP,
                                receivedTime);
            }

            // Give the response to handler.
            handler.handleResponse(tradeResponse);
        } else {
            // TODO-RISK: Handle risk notification.
            log.error("TradeListener.handleTradeObject: Received unknown TradeResponse of type: "
                    + tradeResponse.getClass().getName() + " from adaptor.");
        }
    }
}
