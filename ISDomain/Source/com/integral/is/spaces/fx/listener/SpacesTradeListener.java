package com.integral.is.spaces.fx.listener;

import com.integral.adaptor.AdaptorConstantC;
import com.integral.imtp.IMTPMessageReceiver;
import com.integral.imtp.message.IMTPMessage;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.*;
import com.integral.is.message.rfs.*;
import com.integral.is.spaces.fx.handler.*;
import com.integral.is.spaces.fx.handler.rfs.*;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.DefaultMessageListener;
import com.integral.messaging.MessageReceiver;
import com.integral.messaging.MessageReceiverFactory;
import com.integral.messaging.MessagingException;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.xems.XEmsFactory;
import com.integral.xems.api.XEmsAPI;

import javax.jms.Message;
import javax.jms.MessageListener;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Listener for listening to trade response messages sent by the adaptor either
 * over IMTP or over JMS namespaces to the integration server.
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class SpacesTradeListener implements MessageListener,
		IMTPMessageReceiver {
	private static final Log log = LogFactory.getLog(SpacesTradeListener.class);

	private static MessageReceiver messageReceiver;

	private static DefaultMessageListener rabbitMQListener = new RabbitMQTradeUpdateListener();

	private static final String REQUEST_REFERENCE_ID_PROP = AdaptorConstantC.REQUEST_REFERENCE_ID;

	/**
	 * ThreadPool for TradeListener Processing
	 */
	private static final ThreadPoolExecutor executor = ThreadPoolFactory
			.getInstance().getTradeListenerThreadPool();

	private static final Map<String, AdaptorResponseHandler> handlerMap = new HashMap<String, AdaptorResponseHandler>();

	static {
		// Add handlers for different types of messages.
		handlerMap.put(TradePendingC.class.getName().toLowerCase(),
				new TradePendingHandler());
		handlerMap.put(TradeVerifyC.class.getName().toLowerCase(),
				new TradeVerifyHandler());
		handlerMap.put(TradeRejectC.class.getName().toLowerCase(),
				new TradeRejectHandler());
		handlerMap.put(TradeStatusResponseC.class.getName().toLowerCase(),
				new TradeStatusResponseHandler());
		handlerMap.put(TradeResponsesC.class.getName().toLowerCase(),
				new TradeResponsesHandler());
		handlerMap.put(BrokerOrderResponse.class.getName().toLowerCase(),
				new BrokerOrderResponseHandler());
		handlerMap.put(RFSMarketRateC.class.getName().toLowerCase(),
				new RFSMarketRateHandler());
		handlerMap.put(RFSTradeRejectC.class.getName().toLowerCase(),
				new RFSTradeRejectHandler());
		handlerMap.put(RFSTradeVerifyC.class.getName().toLowerCase(),
				new RFSTradeVerifyHandler());
		handlerMap.put(RFSTradePendingC.class.getName().toLowerCase(),
				new RFSTradePendingHandler());
		handlerMap.put(RFSResponseMessageC.class.getName().toLowerCase(),
				new RFSTradeResponseHandler());

		try {
			messageReceiver = MessageReceiverFactory.newMessageReceiver(
					ServerRuntimeMBean.TRADE_UPDATE_COMPONENT_EXCHANGE,
					ServerRuntimeMBean.TRADE_UPDATE_COMPONENT_EXCHANGE
							+ "_"
							+ ConfigurationFactory.getServerMBean()
									.getVirtualServerName() + "_Q", true,
					false, false, true, rabbitMQListener);
			messageReceiver.addBinding(ConfigurationFactory.getServerMBean()
					.getVirtualServerName());
		} catch (MessagingException e) {
			log.error("Message Receiver initialization failed", e);
			MessageLogger
					.getInstance()
					.log("MESG_INIT_FAILED",
							SpacesTradeListener.class.getName(),
							"Error when initializing Message receiver;Check log for more details",
							null);
		}
	}

	/**
	 * Returns the instance of {@code AdaptorResponseHandler} which is supposed
	 * to handle the message of given type.
	 */
	public static AdaptorResponseHandler getHandler(String className) {
		return handlerMap.get(className.toLowerCase());
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void messageReceived(IMTPMessage message) {
		process(message);
	}

	public void process(IMTPMessage message) {
        XEmsFactory.getApi().processTradeResponseMessage(XEmsAPI.MessageChannel.IMTP,message);
	}

	@Override
	public void onMessage(Message message) {
	    process(message);
	}

	public void process(Message message) {
        XEmsFactory.getApi().processTradeResponseMessage(XEmsAPI.MessageChannel.JMS,message);
	}

	public void process(BrokerOrderResponse message) {
		XEmsFactory.getApi().processTradeResponseMessage(XEmsAPI.MessageChannel.LOCAL,message);
	}
}