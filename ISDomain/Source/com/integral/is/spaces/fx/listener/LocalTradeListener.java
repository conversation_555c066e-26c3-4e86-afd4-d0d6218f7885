package com.integral.is.spaces.fx.listener;

import com.integral.imtp.message.BaseIMTPMessage;
import com.integral.is.message.BrokerOrderResponse;
import com.integral.is.spaces.fx.handler.AdaptorResponseHandler;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.util.LogUtil;

public class LocalTradeListener extends TradeListener {
    private static Log log = LogFactory.getLog(LocalTradeListener.class);

    public BrokerOrderResponse getBrokerOrderResponse() {
        return brokerOrderResponse;
    }

    public void setBrokerOrderResponse(BrokerOrderResponse brokerOrderResponse) {
        this.brokerOrderResponse = brokerOrderResponse;
    }

    private BrokerOrderResponse brokerOrderResponse;

    public LocalTradeListener() {
        super(false);
    }

    public LocalTradeListener(BrokerOrderResponse response, long receivedTime) {
        super(false);
        this.brokerOrderResponse = response;
        this.setReceivedTime(receivedTime);
        this.setSentTimeStamp(response.getResponseSentByAdaptorTime());

    }



    @Override
    public void run() {
        try {
            LogUtil.setSwitch(true);
            String type = null;


            if (getSentTimeStamp() > 0
                    && getReceivedTime() - getSentTimeStamp() > THRESHOLD_TIME_MS) {
                // TODO-METRICS: Add metrics for this.
                log.info("TradeListener.run: Time taken to receive the trade message ms="
                        + (receivedTime - sentTimeStamp));
            }

            if (brokerOrderResponse != null) {
                handleBrokerOrderResponse();
            }

        } finally {

           // brokerOrderResponse.decrementReference(); // Release

            LogUtil.removeSwitch();
        }
    }

    public void handleBrokerOrderResponse() {
        AdaptorResponseHandler handler = SpacesTradeListener.getHandler(brokerOrderResponse.getClass()
                .getName());
        if (handler != null) {

            brokerOrderResponse.setResponseReceivedByAppTime(System.currentTimeMillis());
            handler.handleResponse(brokerOrderResponse);
            switch (brokerOrderResponse.getResponseType()) {
                case STATUS_RESPONSE:
                case REJECT:
                    SerialTradeMessageProcessorManager.removeSerialTradeMessageProcessor(brokerOrderResponse.getTradeId());
                    break;
            }
        } else {
            // TODO-RISK: Handle risk notification.
            log.error("LocalTradeListener.handleTradeObject: Received unknown TradeResponse of type: "
                    + brokerOrderResponse.getClass().getName() + " from adaptor.");
        }


    }

}
