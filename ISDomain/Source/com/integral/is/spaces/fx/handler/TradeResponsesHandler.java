package com.integral.is.spaces.fx.handler;

import com.integral.is.AlertMBean;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.Provider;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.log.MessageLogger;
import com.integral.alert.*;
import com.integral.is.message.ISMessage;
import com.integral.is.message.TradeReject;
import com.integral.is.message.TradeResponse;
import com.integral.is.message.TradeResponses;
import com.integral.is.message.TradeResponsesC;
import com.integral.is.message.TradeStatusResponse;
import com.integral.is.message.TradeVerify;
import com.integral.is.message.TradeVerifyC;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.listener.SpacesTradeListener;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.Adaptor;
import com.integral.lp.AdaptorManagerC;
import com.integral.lp.ProviderManagerC;
import com.integral.model.dealing.SingleLegTrade;

import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * Handler for handling TradeResponses messages from adaptor to IS.
 *
 * <AUTHOR>
 */
public class TradeResponsesHandler implements AdaptorResponseHandler
{
    private static Log log = LogFactory.getLog(TradeResponsesHandler.class);
    private static ISMBean isMBean = ISFactory.getInstance().getISMBean();

    /**
     * Creates the TradeResponses Object and deserializes the text information
     * into that TradeResponse.
     */
    @Override
    public TradeResponses deserializeObject(String text, String stringFormat ) throws Exception
    {
        if (log.isDebugEnabled()) {
            log.debug("SpacesBaseTradeListener.handle :: Received TradeResponses from Adaptor, " + text);
        }

        // Create empty TradePending response object
        TradeResponses tradeResponses = new TradeResponsesC();

        // populate the object
        tradeResponses.populateObject(text);

        return tradeResponses;
    }

    /**
     * Handling of collection of TradeResponse
     */
    @Override
    public void handleResponse( TradeResponses tradeResponses)
    {
        boolean isValid = validateResponses(tradeResponses);

        if (isValid) {
            Collection<TradeResponse> responseCollection = tradeResponses.getTradeResponses();
            processTradeResponses(responseCollection);
        }
    }

    // TODO can this method be removed as each response gets delegated to it's own TradeResponse handler which does the
    // complete validation before handling the response. We need to decide upon whether to involve adaptor at this level,
    // If so, using which tradeResponse object of the collection do we need to retrieve the adaptor.
    private boolean validateResponses(TradeResponses tradeResponses)
    {
        Collection<TradeResponse> list = tradeResponses.getTradeResponses();

        Adaptor adaptor = null;
        TradeResponse tr = null;

        if (log.isDebugEnabled()) {
            log.debug("TradeResponsesHandler.validateResponses : Received trade responses " + list);
        }

        // Get adaptor using any of the tradeResponse objects in the collection.
        if(!list.isEmpty()) {
            adaptor = getAdaptor((TradeResponse)list.toArray()[0]);
        }

        if (adaptor == null) {
            log.error("Not validating TradeResponses since the adaptor is null.");
            return false;
        }

        if (list.size() > 1) {
            if (!adaptor.getConfiguration().isMultipleFillSupported()) {
                StringBuilder sb = new StringBuilder("TradeListenerC.handleESPTradeResponse WARNING ADAPTOR = ");
                sb.append(adaptor.getName()).append(" DOES NOT SUPPORT MULTIPLEFILL. IGNORING THE TRADE RESPONSES = ");
                sb.append(list);

                log.error(sb.toString());

                AlertLoggerFactory.getMessageLogger().log(AlertMBean.VERIFYTRADE,
                                             "TradeListenerC.handleESPTradeResponse",
                                             "INVALID CONFIGURATION MAKING ADAPTOR INACTIVE FOR CLIENTS",
                                             sb.toString());

                for (Provider provider : adaptor.getProviders()) {
                    ProviderManagerC.getInstance().makeProviderInactiveForClient(provider.getName());
                }

                return false;
            }
            if (!isValidMultifillAcceptedAmounts(list)) {
                StringBuilder sb = new StringBuilder(150);
                sb.append("Invalid Accepted Amount in TradeResponses for tradeId: ")
                        .append(tr.getTradeId())
                        .append(" from adaptor: ")
                        .append(adaptor.getName());
                log.warn(sb.toString());
            }

        }
        else if (list.size() == 1) {
            if (!isValidSingleFillAcceptedAmount(tr)) {
                StringBuilder sb = new StringBuilder(150);
                sb.append("TL.handleESPTradeResponse : Accepted Amount set in TradeResponse is INVALID for ")
                        .append(tr.getTradeId())
                        .append(" from ").append(adaptor.getName());

                log.warn(sb.toString());

                AlertLoggerFactory.getMessageLogger().log(AlertMBean.VERIFICATIONINCORRECT,
                        "TradeListenerC.handleESPTradeResponse",
                        "INVALID ACCEPTED AMOUNT IN TRADE RESPONSE",
                        sb.toString());
            }
        }

        return true;
    }

    /**
     * Processes the tradeResponse collection.
     */
    private boolean processTradeResponses(Collection<TradeResponse> list)
    {
        List sortedResponses = arrangeTradeResponses(list);

        for (Object sortedResponse : sortedResponses) {
            TradeResponse tradeResponse = (TradeResponse) sortedResponse;

            //get the handler
            AdaptorResponseHandler innerHandler =
                SpacesTradeListener.getHandler(tradeResponse.getClass().getName());

            //delegate the processing to the specific handler
            if(innerHandler != null) {
                innerHandler.handleResponse(tradeResponse);
            }
            else {
            	String mesg = "TradeResponsesHandler.processTradeResponses : Received unknown TradeResponse from Adaptor " +
                        tradeResponse;
                log.warn( mesg);
                MessageLogger.getInstance().log(ISAlertMBean.ADAPTOR_UNKNOW_TRADE_RESPONSE, "TradePendingHandler.processTradeResponses",  mesg, null);
            }
        }

        return true;
    }

    @Override
    public void handleResponse( ISMessage isMessage)
    {
        throw new UnsupportedOperationException("TradeResponsesHandler does not support this API");
    }

    /**
     * Utility Methods
     */
    private List arrangeTradeResponses(Collection tradeResponses)
    {
        LinkedList sortedResponses = new LinkedList(tradeResponses);
        Iterator iterator = tradeResponses.iterator();
        boolean isTradeRejected = false;
        boolean containsTradeStatusResponse = false;

        while (iterator.hasNext()) {
            TradeResponse tradeResponse = (TradeResponse) iterator.next();

            if (tradeResponse instanceof TradeReject) {
                isTradeRejected = true;
            }

            // In case of multiple verifications, the verify message with the original trade id
            // should be the first to be processed.
            if (tradeResponse instanceof TradeVerify) {
                String transactionId = (String) tradeResponse.getProperty(ISConstantsC.TRADE_TRANSACTIONID);
                if (transactionId != null && tradeResponse.getTradeId().equals(transactionId)) {

                    // Insert this verify message as the first in the list.
                    sortedResponses.remove(tradeResponse);
                    sortedResponses.addFirst(tradeResponse);
                }
            }

            // Following rearrangement is required by ALL
            if (tradeResponse instanceof TradeStatusResponse) {
                sortedResponses.remove(tradeResponse);
                sortedResponses.addLast(tradeResponse);
                containsTradeStatusResponse = true;
            }
        }

        if (isTradeRejected && containsTradeStatusResponse) {
            log.info("Removing TradeStatusResponse since this collection has a rejected trade.");
            sortedResponses.removeLast();
        }

        return sortedResponses;
    }

    private boolean isValidSingleFillAcceptedAmount(TradeResponse tradeResponse)
    {
        if (isMBean.isSingleFillTrdVerifyAmountValidationEnabled()) {
            if (tradeResponse instanceof TradeVerifyC) {
                return ((TradeVerifyC) tradeResponse).getAcceptedAmount() != 0;
            }
        }
        return true;
    }

    private boolean isValidMultifillAcceptedAmounts(Collection tradeResponses)
    {
        for (Object response : tradeResponses) {
            if (response instanceof TradeVerifyC) {
                if (((TradeVerify) response).getAcceptedAmount() != 0) {
                    continue;
                }
                return false;
            }
        }

        return true;
    }

    private String getAdaptorName(TradeResponse tr)
    {
        SingleLegTrade trade = FXESPWorkflowCache.getTrade(tr.getTradeId());

        if( trade != null ) {
            return DealingModelUtil.getAdaptorName(trade.getMatchEvent());
        }

        return null;
    }

    private Adaptor getAdaptor(TradeResponse tr)
    {
        String adaptorName = (String) tr.getProperty(ISConstantsC.ProviderName);

        if (adaptorName == null) {
            adaptorName = getAdaptorName(tr);
        }

        if (adaptorName == null) {
            StringBuilder sb = new StringBuilder(100);
            sb.append("TL.getAdaptor : AdaptorName not found for Trade ").append(tr.getTradeId());
            log.error(sb.toString());
            AlertLoggerFactory.getMessageLogger().log(AlertMBean.VERIFYTRADE, "TradeListenerC.getAdaptor", "AdaptorName Not Found", sb.toString());
        }
        else {
            return AdaptorManagerC.getInstance().getAdaptor(adaptorName);
        }

        return null;
    }
}
