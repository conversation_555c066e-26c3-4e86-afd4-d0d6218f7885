package com.integral.is.common.admin;

import com.integral.is.common.ISConstantsC;
import com.integral.is.ecn.ECNWorkflowFunctorC;
import com.integral.is.message.TradeResponse;
import com.integral.is.message.TradeVerify;
import com.integral.is.message.TradeVerifyC;
import com.integral.is.spaces.fx.listener.SpacesTradeListener;
import com.integral.message.Message;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.TradeLeg;

import java.util.Date;

public class SpacesLEManualUpdateHandler extends SpacesManualDealUpdateNotificationHandler
{
    @Override
    public Message handle(Message msg)
    {
        WorkflowMessage wfMsg = (WorkflowMessage) msg;
        String leShortName = (String) wfMsg.getParameterValue(ISConstantsC.LE_SHORTNAME_WFKEY);
        String userName = (String) wfMsg.getParameterValue("fiUserName");
        String dealID = (String) wfMsg.getParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY);
        String namespaceSN = ( String ) wfMsg.getParameterValue( "namespace" );
        if (dealID == null || dealID.length() == 0) {
            log.error("handle: TransactionId cannot null or blank " + dealID);
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }
        log.warn("handle : LE Update Received from Admin for :" + dealID + " Le: " + leShortName + " User : " + userName);

        SingleLegTrade trade = getTrade( dealID, namespaceSN );
        if (trade == null) {
            log.error("handle: No TRADE found for tradeId : " + dealID + " Hence Returning ");
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }
        if ( getProvider( trade ) == null ) {
            log.error("handle : Provider Instance Not Found Hence Returning ");
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }

        OrderRequest request = trade.getOrderRequest();
        if (request == null) {
            log.error("handle: No Request found for dealId : " + dealID + " Hence Returning ");
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }

        //Trigger the trade-workFlow...
        long timeReceived = System.currentTimeMillis();
        TradeVerify trdVerify = new TradeVerifyC();
        try {

            trdVerify.setStatus(TradeResponse.MANUAL_LE_UPDATE_BY_ADMIN);
            trdVerify.setTradeId(dealID);

            TradeLeg tl = trade.getTradeLeg();
            trdVerify.setAcceptedAmount( tl.getAcceptedDealtCurrencyAmount() ); // todo: confirm trdVerify.setAcceptedAmount(fxLegDealingPrice.getDealtAmount());
            trdVerify.setAcceptedPrice( tl.getRate() );                     // todo: confirm trdVerify.setAcceptedPrice(fxPayment.getFXRate().getRate());
            trdVerify.setValueDate( new Date( tl.getValueDate() ) );            // todo: confirm trdVerify.setValueDate(fxPayment.getValueDate().asJdkDate());

            trdVerify.setProperty(ECNWorkflowFunctorC.LEGAL_ENTITY, leShortName);
            SpacesTradeListener.getHandler(trdVerify.getClass().getName()).handleResponse(trdVerify);
        }
        catch (Exception e) {
            log.error("Exception in SpacesLEManualUpdateHandler.handle : Exception ", e);
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }
        wfMsg.setStatus(MessageStatus.SUCCESS);
        return wfMsg;
    }

}
