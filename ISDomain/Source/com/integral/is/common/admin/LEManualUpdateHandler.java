package com.integral.is.common.admin;

import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.trade.Trade;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.is.common.ISConstantsC;
import com.integral.is.ecn.ECNWorkflowFunctorC;
import com.integral.is.message.TradeResponse;
import com.integral.is.message.TradeVerify;
import com.integral.is.message.TradeVerifyC;
import com.integral.is.spaces.fx.listener.SpacesTradeListener;
import com.integral.message.Message;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;


public class LEManualUpdateHandler extends ManualDealUpdateNotificationHandler {
    public Message handle(Message msg) {
        WorkflowMessage wfMsg = (WorkflowMessage) msg;
        String leShortName = (String) wfMsg.getParameterValue(ISConstantsC.LE_SHORTNAME_WFKEY);
        String userName = (String) wfMsg.getParameterValue("fiUserName");
        String dealID = (String) wfMsg.getParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY);
        if (dealID == null || dealID.length() == 0) {
            log.error(" Error: LEManualUpdateHandler.handle: TransactionId cannot null or blank " + dealID);
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }
        log.warn("LEManualUpdateHandler.handle LE Update Received from Admin for :" + dealID + " Le: " + leShortName + " User : " + userName);

        Trade trade = getTrade(dealID, userName);
        if (trade == null) {
            log.error(" Error: LEManualUpdateHandler.handle: No TRADE found for tradeId : " + dealID + " Hence Returning ");
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }
        if (getProvider(trade.getRequest()) == null) {
            log.error("ERROR : LEManualUpdateHandler.handle  Provider Instance Not Found Hence Returning ");
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }


        Request request = trade.getRequest();
        if (request == null) {
            log.error(" Error: LEManualUpdateHandler.handle: No Request found for dealId : " + dealID + " Hence Returning ");
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }

        //Trigger the trade-workFlow...
        long timeReceived = System.currentTimeMillis();
        TradeVerify trdVerify = new TradeVerifyC();
        try {

            FXLegDealingPrice fxLegDealingPrice = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.SINGLE_LEG);
            FXPaymentParameters fxPayment = ((FXSingleLeg) request.getTrade()).getFXLeg().getFXPayment();

            trdVerify.setStatus(TradeResponse.MANUAL_LE_UPDATE_BY_ADMIN);
            trdVerify.setTradeId(dealID);
            trdVerify.setAcceptedAmount(fxLegDealingPrice.getDealtAmount());
            trdVerify.setAcceptedPrice(fxPayment.getFXRate().getRate());
            trdVerify.setValueDate(fxPayment.getValueDate().asJdkDate());
            trdVerify.setProperty(ECNWorkflowFunctorC.LEGAL_ENTITY, leShortName);
            SpacesTradeListener.getHandler(trdVerify.getClass().getName()).handleResponse(trdVerify);
        }
        catch (Exception e) {
            log.error("Exception in LEManualUpdateHandler.handle  Exception ", e);
            wfMsg.setStatus(MessageStatus.FAILURE);
            return wfMsg;
        }
        wfMsg.setStatus(MessageStatus.SUCCESS);
        return wfMsg;
    }

}
