package com.integral.is.common.comm;

import com.integral.SEF.SEFUtilC;
import com.integral.adaptor.request.RFQRateCache;
import com.integral.admin.services.AdminServiceProvider;
import com.integral.admin.services.org.AdminCreditWebService;
import com.integral.admin.services.org.TradingRelationshipService;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.admin.utils.organization.UserUtil;
import com.integral.admin.utils.transaction.DefaultTransactionMessage;
import com.integral.admin.utils.transaction.TransactionException;
import com.integral.admin.utils.transaction.TransactionalMessage;
import com.integral.alert.AlertErrorCode;
import com.integral.alert.AlertLoggerFactory;
import com.integral.dbservice.is.ISPersistenceService;
import com.integral.facade.EventTimeFacade;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitClassification;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.admin.CreditLimitAdminService;
import com.integral.finance.creditLimit.admin.CreditLimitAdminServiceFactory;
import com.integral.finance.creditLimit.external.ExternalCreditCheckUtil;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.*;
import com.integral.finance.dealing.facade.QuoteEventTimeFacade;
import com.integral.finance.dealing.facade.RequestStateFacade;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXLegDealingPriceC;
import com.integral.finance.dealing.mifid.MiFIDRequestParams;
import com.integral.finance.dealing.mifid.MiFIDTradeParams;
import com.integral.finance.dealing.mifid.MiFIDUtils;
import com.integral.finance.fx.*;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.*;
import com.integral.fx.ISDomainConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.Provider;
import com.integral.is.common.RFSMessageNormalizer;
import com.integral.is.common.cache.HandlerCacheC;
import com.integral.is.common.cache.ISTradeCache;
import com.integral.is.common.cache.TransactionMonitoringAgent;
import com.integral.is.common.email.ISEmailUtil;
import com.integral.is.common.exception.MessageCommunicationExceptionC;
import com.integral.is.common.facade.ISTradeFactory;
import com.integral.is.common.facade.RFSWorkflowFacade;
import com.integral.is.common.log.ISLogger;
import com.integral.is.common.maintenance.MaintenanceAgent;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.is.common.rfs.RFSMessageLogger;
import com.integral.is.common.util.*;
import com.integral.is.credit.ISRFSCreditWorkflowManagerC;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.finance.dealing.workflowhandler.RFSWorkflowHandlerC;
import com.integral.is.finance.trade.calculator.RFSFXTradeRatesCalculatorC;
import com.integral.is.management.facade.ISQuoteEventTimeFacadeC;
import com.integral.is.management.monitor.WorkflowRuntimeMonitor;
import com.integral.is.message.*;
import com.integral.is.message.TradeAckC.LegInfo;
import com.integral.is.message.rfs.*;
import com.integral.is.priceprovision.PriceProvisionInput;
import com.integral.is.priceprovision.PriceProvisionInputBuilder;
import com.integral.is.primebroker.PrimeBrokerWorkflowServiceManager;
import com.integral.is.primebroker.RFSPrimeBrokerWorkflowServiceC;
import com.integral.is.rfs.util.RfsUtils;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.Adaptor;
import com.integral.lp.ProviderManagerC;
import com.integral.lp.multihost.MultihostMBean;
import com.integral.lp.validator.RFSAccountIDValidator;
import com.integral.management.trade.TradeStatus;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.mifid.MiFIDMBean;
import com.integral.mifid.MiFIDMBeanC;
import com.integral.mifid.MiFIDRequestValidatorC;
import com.integral.model.TradeInfo.TradeEvent;
import com.integral.model.ems.EMSExecutionType;
import com.integral.persistence.SecondaryPersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.trade.notification.TradeNotificationUtil;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.MathUtilC;
import com.integral.util.StringUtilC;
import com.integral.util.Tuple;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadObjectQuery;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * RFSMessageListenerC handles all RFS messages received by TradeMessageListener
 */
public class RFSMessageListenerC
{
    protected Log log = LogFactory.getLog( this.getClass() );
    protected ISMBean isMBean = ISFactory.getInstance().getISMBean();
    protected MultihostMBean mhMBean = ISFactory.getInstance().getMultihostMBean();
    protected QuoteService rfsQuoteService = ServiceFactory.getRFSQuoteService();
    protected static RFSWorkflowHandlerC rfsWorkflowHandler = new RFSWorkflowHandlerC();
    private static final char LOG_DELIMITER = ',';
    protected ThreadPoolExecutor poolExecutor = ThreadPoolFactory.getInstance().getRFSResponseHandlerThreadPool();
    MiFIDRequestValidatorC miFIDRequestValidator = new MiFIDRequestValidatorC();

    public RFSMessageListenerC()
    {
    }

    public void handleRFSMarketRate( RFSMarketRate rate, long isReceived, long sentTimeStamp )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "RFSMessageListenerC.handlerRFSResponseMessage Received RFS response message. " + rate.toString() );
        }
        Request request = getRequest(rate.getRequestId(), false);
        if( request == null )
        {
            log.warn("RFSMessageListenerC.handleRFSMarketRate : Request not found for "+rate.getRequestId()+". Dropping Rate");
            return;
        }
        String mappedProviderName = request.getRequestAttributes().getRFSProvider( rate.getProviderShortName() );
        if( mappedProviderName == null )
        {
            log.warn( "RFSMessageListenerC.handleRFSMarketRate : Mapped Provider not found for Provider " + rate.getProviderShortName() + " . Dropping rate for " + rate.getRequestId() );
            return;
        }
        Provider provider = getProvider( mappedProviderName );
        if ( provider != null )
        {
            if( provider.getAdaptor().isMultihostAdaptor() )
            {
                String mappedStream = mhMBean.getStreamMappingForHostedOrg( provider.getAdaptor().getName(),rate.getProviderShortName(),rate.getStreamId() );
                rate.setStreamId( mappedStream );
            }
            rate.setProviderShortName( mappedProviderName );
            Quote quote = RFSMessageNormalizer.getQuote( rate, provider );
            if ( quote == null )
            {
                return;
            }
            quote.setSentDate( new Date( isReceived ) );
            provider.getRFSQuoteCache().add( quote.getTransactionID(), quote, rate );

            captureEvent( quote, ISConstantsC.EVENT_TIME_DISP_ADAPTER_SENT_RATE, sentTimeStamp );
            captureEvent( quote, ISConstantsC.EVENT_TIME_DISP_IS_REC_RATE, isReceived );
            captureEvent( quote, ISConstantsC.EVENT_TIME_QUOTE_CREATED, System.currentTimeMillis() );

            acceptMiFIDParametersOnQuote( rate, request, quote );
            poolExecutor.execute( new RFSQuotePublisher( quote ) );
            if (request.isRfq() && quote.getQuoteClassification() != null
                    && !quote.getQuoteClassification().getShortName().equals(ISConstantsC.QUOTE_UNSUPPORTED_TYPE)) {
                quote.setRfq(true);
                request.setPublishedQuote(quote);
                request.setRawRate(rate.getRawRate());
                ISPersistenceService.instance().rfsRequestPublished(request);
                log.info("RFSMessageListenerC.handleRFSMarketRate Persistence request and quote RFQ publishing to customer. " + request.getTransactionID());
            }
        }
        else
        {
            log.warn("RFSMessageListenerC.handleRFSMarketRate : Provider " + mappedProviderName + " not found. Dropping rate for " + rate.getRequestId());
        }
    }

    private void acceptMiFIDParametersOnQuote( RFSMarketRate rate, Request request, Quote quote )
    {
        if( MiFIDMBeanC.getInstance().isMiFIDEnabled() && MiFIDUtils.isMiFIDEligibleTenor( request ))
        {
            MiFIDTradeParams rateParams = rate.getMiFIDTradeParams();
            MiFIDRequestParams requestParams = request.getMiFIDRequestParams();

            /*
                We would have received some MiFIDParams on RFSMarketRate. Merge them with remaining params from request and set it on quote.
                If we haven't received any MiFIDParam on MarketRate, that means, we do not have any MiFIDParams to be set on a quote level.
             */
            if(rateParams!=null)
            {
                if(requestParams!=null)
                {
                    rateParams.setTakerInvestmentDecisionMaker( requestParams.getTakerInvestmentDecisionMaker() );
                    rateParams.setTakerInvestmentDecisionMakerType( requestParams.getTakerInvestmentDecisionMakerType() );
                    rateParams.setTakerInvestmentDecisionMakerCodeSource( requestParams.getTakerInvestmentDecisionMakerCodeSource() );
                    rateParams.setTakerExecutingUser( requestParams.getTakerExecutingUser() );
                    rateParams.setTakerExecutingUserType( requestParams.getTakerExecutingUserType() );
                    rateParams.setTakerExecutingUserCodeSource( requestParams.getTakerExecutingUserCodeSource() );
                    rateParams.setTakerTradingCapacity( requestParams.getTakerTradingCapacity() );
                    rateParams.setReportTrackingNumber( requestParams.getReportTrackingNumber() );
                    rateParams.setFarReportTrackingNumber( requestParams.getFarReportTrackingNumber() );
                    rateParams.setTakerExecutingFirm( requestParams.getTakerExecutingFirm() );
                    rateParams.setTakerCountryCode( requestParams.getTakerCountryCode() );
                    if(requestParams.getMakerExecutingFirm()!=null){
                        rateParams.setMakerExecutingFirm( requestParams.getMakerExecutingFirm() );
                    }
                    rateParams.setCustomerAccount( requestParams.getCustomerAccount() );
                    rateParams.setCustomerCountryCode( requestParams.getCustomerCountryCode() );
                    //rateParams.setNPFT( requestParams.getNPFT() );
                    //rateParams.setSecuritiesFinancingTransaction( requestParams.getSecuritiesFinancingTransaction() );
                    //rateParams.setPreTradeWaiverIndicator( requestParams.getPreTradeWaiverIndicator() );
                    //rateParams.setPostTradeDeferralIndicator( requestParams.getPostTradeDeferralIndicator() );
                    //rateParams.setMiFIDOrderSubmissionTime( requestParams.getMiFIDOrderSubmissionTime() );
                    rateParams.setTradeClsf( request.getTradeClassification().getName() );
                }
                //validate
                RequestAttributes requestAttributes = request.getRequestAttributes();
                String lpName = quote.getOrganization().getShortName();
                if(request.isMTF() && !requestAttributes.hasQuoteValidated(lpName)) {
                    boolean externalMTF = !MiFIDUtils.isIntegralMTF(request.getMiFIDExecutionVenue());
                    Collection<String> validationsList = MiFIDMBeanC.getInstance().getMiFIDFValidationsList(!externalMTF, MiFIDMBean.QUOTE, quote.getOrganization().getShortName());
                    miFIDRequestValidator.validateMakerParams(rateParams, quote.getOrganization(), rate.getRequestId(), externalMTF, validationsList);
                    requestAttributes.setQuoteMiFIDValidated(lpName, true);
                }
                quote.setMiFIDTradeParams( rateParams );
            }
        }
    }

    public void republishRFSQuote( Quote quote, Organization lp, RFSMarketRate rate )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "RFSMessageListenerC.republishRFSQuote Entering with quote with TxnId = " + quote.getTransactionID() );
        }
        Provider provider = getProvider( lp.getShortName() );
        provider.getRFSQuoteCache().add( quote.getTransactionID(), quote, rate );
        captureEvent( quote, ISConstantsC.EVENT_TIME_QUOTE_CREATED, System.currentTimeMillis() );
        poolExecutor.execute( new RFSQuotePublisher( quote ) );

        if ( log.isDebugEnabled() )
        {
            log.debug( "RFSMessageListenerC.republishRFSQuote Exiting successfully with quote.getTransactionID() = " + quote.getTransactionID() );
        }
    }

    public void publishRFSQuote(Quote quote)
    {
        poolExecutor.execute(new RFSQuotePublisher(quote));
    }

	public void withdrawRFSQuote( Request request , Quote quote) {

        RFSResponseMessage response = MessageFactory.newRFSResponse();
        response.setRequestReferenceId( request.getReferenceId() );
        response.setStatus( RFSResponseMessage.REQUEST_EXPIRED );
        response.setLegalEntity(request.getCounterparty().getShortName());
        response.setOrganization(request.getOrganization().getShortName());
        response.setProvider( quote.getOrganization().getShortName());
        response.setUser(request.getUser().getShortName());
        response.setFailureReason("Quote not Available");
        poolExecutor.execute(new WithdrawQuote( request, response ));
    }
	
    public void handleRFSResponseMessage( RFSResponseMessage rfsResponseMessage )
    {
        log.warn( "RFSMessageListenerC.handlerRFSResponseMessage Received RFS response message. " + rfsResponseMessage.toString() );
        String transactionId = rfsResponseMessage.getRequestReferenceId();
        Request request = HandlerCacheC.getHandlerCache().getRequest(transactionId);
        if ( request == null )
        {
            log.warn("RFSMessageListenerC.handleRFSResponseMessage :: MSG DISCARDED. Request not found for TXID " + transactionId);
            return;
        }
        String mappedProvider = request.getRequestAttributes().getRFSProvider(rfsResponseMessage.getProvider());
        if ( mappedProvider != null )
        {
            rfsResponseMessage.setProvider( mappedProvider );
        }

        if ( rfsResponseMessage.getStatus() == RFSResponseMessage.QUOTE_WITHDRAWN )
        {
            poolExecutor.execute(new WithdrawQuote(request, rfsResponseMessage));
        }
        else if ( rfsResponseMessage.getStatus() == RFSResponseMessage.REQUEST_DECLINED )
        {
            poolExecutor.execute( ( new DeclineQuote( request, rfsResponseMessage ) ) );
        } else if (rfsResponseMessage.getStatus() == RFSResponseMessage.MANUAL_PRICING ||
                        rfsResponseMessage.getStatus() == RFSResponseMessage.SELECTED_FOR_PRICING) {
            if(log.isDebugEnabled()) log.debug("RFSMessageListenerC.handleRFSResponseMessage :: Status Message for TXID " + transactionId + " " + rfsResponseMessage.getStatus());
            Organization org = ISUtilImpl.getInstance().getOrg(rfsResponseMessage.getProvider());
            String status = rfsResponseMessage.getStatus() == RFSResponseMessage.MANUAL_PRICING ? ISDomainConstants.MANUAL_PRICING : ISDomainConstants.SELECTED_FOR_PRICING;
            poolExecutor.execute(new StatusMessage(request, org, status));
        } else if ( rfsResponseMessage.getStatus() == RFSResponseMessage.REQUEST_EXPIRED ) {
            Quote publishedQuote = request.getPublishedQuote();
            if (publishedQuote != null && publishedQuote.isRfq()) {
                if (System.currentTimeMillis() >= publishedQuote.getCreatedTimestamp().getTime() + publishedQuote.getExpiration().getMilliSeconds()) {
                    poolExecutor.execute((new ExpireQuote(request, rfsResponseMessage)));
                } else {
                    log.warn("RFSMessageListenerC.handleRFSResponseMessage :: MSG DISCARDED. RFQ Customer Request cannot be expired on cover expiry. TXID " + transactionId);
                }
            } else {
                poolExecutor.execute((new ExpireQuote(request, rfsResponseMessage)));
            }
        }
    }

    /**
     * @param tradeResponse
     */
    public void handleRFSTradeResponse( RFSTradeResponse tradeResponse, long isReceived )
    {
        if ( tradeResponse.getTradeId() == null || tradeResponse.getTradeId().length() == 0 )
        {
            log.warn( "RFSMessageListenerC.handleRFSTradeResponse: Got trade response with null or blank transactionId " + tradeResponse.getTradeId() );
            sendAckToProvider(tradeResponse, TradeAckMessages.NO_TRADE_ID);
            return;
        }
        Request request = getRequest( tradeResponse );
        if( request == null )
        {
            log.error( " RFSMessageListenerC.handleRFSTradeResponse : Request not found for transaction id " + tradeResponse.getTradeId() + " RFS Trade Response Dropped " );
            sendAckToProvider(tradeResponse, TradeAckMessages.INVALID_TRADE_ID);
            return;
        }
        if ( request.getAcceptedQuote () != null )
        {
            tradeResponse.setProvider ( request.getAcceptedQuote ().getOrganization ().getShortName () );
        }
        Provider provider = getProvider( tradeResponse.getProvider() );
        if ( provider == null )
        {
            log.error( " RFSMessageListenerC.handleRFSTradeResponse : Provider not found for " + tradeResponse.getProvider() + " RFS Trade Response Dropped for " + tradeResponse.getTradeId() );
            sendAckToProvider(tradeResponse, TradeAckMessages.INVALID_PROVIDER);
            return;
        }
        synchronized ( request )
        {
            RFSWorkflowFacade rFSWorkflowFacade = ( RFSWorkflowFacade ) request.getFacade( RFSWorkflowFacade.NAME );
            if ( rFSWorkflowFacade.getRequestState().equalsIgnoreCase( ISCommonConstants.REQUEST_STATE_EXECUTED ) )
            {
                try
                {
                    log.error( "RFSMessageListenerC.handleRFSTradeResponse :: DISCARDING MSG. Trade already Verified for TXID " + tradeResponse.getTradeId() + ". Msg is " + tradeResponse.getToString() );
                    if(tradeResponse instanceof RFSTradeVerify)
                    {
                        sendAckToProvider(request, (RFSTradeVerify) tradeResponse, true, null);
                        if(tradeResponse.getProperty(TradeAckMessages.TRD_EXECUTION_TIME) != null)
                        {
                            log.warn(".handleRFSTradeResponse: trade state already verified. Unable to update execution time " + tradeResponse.getTradeId());
                            AlertLoggerFactory.getMessageLogger().log(AlertErrorCode.LP_ACK_UNABLE_TO_UPDATE_EXEC_TIME, "RFSMessageListenerC", "LP ACK EXECUTION TIME NOT UPDATED " + ' ' + tradeResponse.getTradeId(), null);
                        }
                    }
                }
                catch ( Exception e )
                {
                    e.printStackTrace();
                }
                return;
            }
            else if ( rFSWorkflowFacade.getRequestState().equalsIgnoreCase( ISCommonConstants.REQUEST_STATE_DECLINED ) )
            {
                try
                {
                    log.error( "RFSMessageListenerC.handleRFSTradeResponse :: DISCARDING MSG. Trade already Rejected for TXID " + tradeResponse.getTradeId() + ". Msg is " + tradeResponse.getToString() );
                    if ( tradeResponse instanceof RFSTradeVerify )
                    {
                    	captureEvent( request.getAcceptedQuote(), tradeResponse.getTiming().getAllTimings() );
                    	request.setAdaptorOrganization( request.getTrade().getCounterpartyB().getOrganization() );
                        String generatedTradeId = ISUtilImpl.getInstance().generateDummyTradeTransactionID( request.getTrade() );
                        log.warn( "Generated Dummy Trade TransactionID " + generatedTradeId + " for Trade Verified Response received after Auto Cancel for TradeId " + tradeResponse.getTradeId() );
                        Map<String, Object> additionalParams = new HashMap<String, Object>( 2 );
                        additionalParams.put( ISConstantsC.DUMMY_TRADE_ID, generatedTradeId );
                        String providerTradeId = ( ( RFSTradeVerify ) tradeResponse ).getProviderTradeId();
                        if( providerTradeId != null )
                        {
                            additionalParams.put( ISConstantsC.PROVIDER_TRADE_ID,providerTradeId );
                        }
                        WorkflowRuntimeMonitor.getInstance().notifyWorkflowEvent( request, request.getTrade(), TradeStatus.REJECTED_BY_INTEGRAL_VERIFIED, additionalParams );
                        sendAckToProvider(request, (RFSTradeVerify) tradeResponse, false, TradeAckMessages.ALREADY_REJECTED);
                    }
                    else if ( tradeResponse instanceof RFSTradeReject )
                    {
                    	captureEvent( request.getAcceptedQuote(), tradeResponse.getTiming().getAllTimings() );
                    	request.setAdaptorOrganization( request.getTrade().getCounterpartyB().getOrganization() );
                        WorkflowRuntimeMonitor.getInstance().notifyWorkflowEvent( request, request.getTrade(), TradeStatus.REJECTED_BY_INTEGRAL_REJECTED, null );
                    }
                }
                catch ( Exception e )
                {
                	 log.error("RFSMessageListenerC.handleRFSTradeResponse ::", e);
                }
                return;
            }
            captureEvent( request.getAcceptedQuote(), tradeResponse.getTiming().getAllTimings() );
            USIUtilC.updateExternalUSIs( request, tradeResponse );
            if ( tradeResponse instanceof RFSTradeVerify )
            {
                handleRFSTradeVerify( request, tradeResponse, isReceived );
            }
            else if ( tradeResponse instanceof RFSTradeReject )
            {
                handleRFSTradeReject( request, tradeResponse, isReceived );
            }
            else if ( tradeResponse instanceof RFSTradePending )
            {
                handleRFSTradePending( request, tradeResponse );
            }

        }
    }

    private void acceptMIFIDParameters( RFSTradeResponse rfsTradeResponse, Trade trade )
    {
        if( MiFIDMBeanC.getInstance().isMiFIDEnabled() && MiFIDUtils.isMiFIDEligibleTenor( trade.getRequest() ))
        {
            MiFIDTradeParams miFIDResponseParams = rfsTradeResponse.getMiFIDTradeParams();
            log.info("RFSMessageListenerC.acceptMIFIDParameters :: RFSTradeResponse for Trade="+trade.getTransactionID()+" | MiFIDParams="  + miFIDResponseParams);
            MiFIDTradeParams miFIDTradeParams = null;
            if(trade !=null)
            {
                miFIDTradeParams = trade.getMiFIDTradeParams();
            }
            if(miFIDResponseParams!=null)
            {
                if(miFIDTradeParams==null)
                {
                    miFIDTradeParams = new MiFIDTradeParams();
                    trade.setMiFIDTradeParams( miFIDTradeParams );
                }

                Counterparty cptyB = trade.getCounterpartyB();
                if(cptyB!=null)
                {
                    String cptyBLEI = CounterpartyUtilC.getLEI( cptyB );
                    if(cptyBLEI!=null){
                        miFIDTradeParams.setMakerExecutingFirm( cptyBLEI);
                        miFIDTradeParams.setMakerCountryCode( cptyB.getAddress().getCountry());
                    }
                }
                User makerUser = trade.getMakerUser();
                if(makerUser!=null)
                {
                    miFIDTradeParams.setMakerExecutingUser(  MiFIDUtils.getMiFIDShortCode(makerUser) );
                    miFIDTradeParams.setMakerExecutingUserType( makerUser.getUserTypeEnum() );
                    miFIDTradeParams.setMakerExecutingUserCodeSource(MiFIDUtils.getShortCodeSource(makerUser.getOrganization()) );
                    miFIDTradeParams.setMakerInvestmentDecisionMakerCodeSource(MiFIDUtils.getShortCodeSource(makerUser.getOrganization()) );
                }
                acceptMiFIDParameters( miFIDResponseParams, miFIDTradeParams );
            }
        }
    }

    private void acceptMiFIDParameters( MiFIDTradeParams miFIDResponseParams, MiFIDTradeParams miFIDTradeParams )
    {
        if( !isNullOrEmpty(miFIDResponseParams.getMakerExecutingFirm())){
            miFIDTradeParams.setMakerExecutingFirm(miFIDResponseParams.getMakerExecutingFirm());
        }
        if( !isNullOrEmpty(miFIDResponseParams.getMakerExecutingUser())){
            miFIDTradeParams.setMakerExecutingUser( miFIDResponseParams.getMakerExecutingUser() );
        }
        if( !isNullOrEmpty(miFIDResponseParams.getMakerExecutingUserCodeSource())){
            miFIDTradeParams.setMakerExecutingUserCodeSource( miFIDResponseParams.getMakerExecutingUserCodeSource() );
        }
        if(miFIDResponseParams.getMakerExecutingUserType()!=null){
            miFIDTradeParams.setMakerExecutingUserType(miFIDResponseParams.getMakerExecutingUserType());
        }
        if(!isNullOrEmpty(miFIDResponseParams.getMakerInvestmentDecisionMaker())){
            miFIDTradeParams.setMakerInvestmentDecisionMaker(miFIDResponseParams.getMakerInvestmentDecisionMaker());
        }
        if(!isNullOrEmpty(miFIDResponseParams.getMakerInvestmentDecisionMakerCodeSource())){
            miFIDTradeParams.setMakerInvestmentDecisionMakerCodeSource( miFIDResponseParams.getMakerInvestmentDecisionMakerCodeSource() );
        }
        if(miFIDResponseParams.getMakerInvestmentDecisionMakerType()!=null){
            miFIDTradeParams.setMakerInvestmentDecisionMakerType(miFIDResponseParams.getMakerInvestmentDecisionMakerType());
        }
        miFIDTradeParams.setMakerTradingCapacity(miFIDResponseParams.getMakerTradingCapacity());

        //If Country code came from LP, use it. Else default to MakerExecutingFirm.address.country (which was already set by now)
        if(miFIDResponseParams.getMakerCountryCode()!=null){
            miFIDTradeParams.setMakerCountryCode( miFIDResponseParams.getMakerCountryCode() );
        }

        if(!isNullOrEmpty( miFIDResponseParams.getMakerVenueCode() )){
            miFIDTradeParams.setMakerVenueCode(miFIDResponseParams.getMakerVenueCode());
        }

        miFIDTradeParams.setSIExecution( miFIDResponseParams.getSIExecution() );
    }

    private boolean isNullOrEmpty(String str)
    {
        return StringUtilC.isNullOrEmpty(str) || "null".equalsIgnoreCase(str);
    }

    /**
     * @param request
     * @param tradeResponse
     */
    private void handleRFSTradeVerify( Request request, RFSTradeResponse tradeResponse, long isReceived )
    {
        Quote acceptedQuote = null;
        try
        {
            log.warn( "RFSMessageListenerC.handleRFSTradeVerify :: started for TXID = " + request.getTransactionID() );
            acceptedQuote = request.getAcceptedQuote();
            ISUtilImpl.getInstance().setSessionContext( request.getUser() );
            RFSWorkflowFacade rFSWorkflowFacade = ( RFSWorkflowFacade ) request.getFacade( RFSWorkflowFacade.NAME );
            rFSWorkflowFacade.setLpQuoteState( acceptedQuote.getOrganization(), ISCommonConstants.QUOTE_STATE_VERIFIED, ISCommonConstants.TradeVerifiedReceivedByServer, null );

            RFSTradeVerify rfsTradeVerify = ( RFSTradeVerify ) tradeResponse;
            RFSFXRate nearLegRate = rfsTradeVerify.getAcceptedNearLegRate ();
            if ( nearLegRate != null )
            {
                ISUtilImpl.RFSSpreadLog.info ( "VERIFY - verified rates from provider. near leg=" + nearLegRate.getSpotRate ()
                        + "/" + nearLegRate.getForwardPoints () + "/" + nearLegRate.getRate () + ",tid=" + request.getTrade ().getTransactionID () );
            }
            RFSFXRate farLegRate = rfsTradeVerify.getAcceptedFarLegRate ();
            if ( farLegRate != null )
            {
                ISUtilImpl.RFSSpreadLog.info ( "VERIFY - verified rates from provider. far leg=" + farLegRate.getSpotRate ()
                        + "/" + farLegRate.getForwardPoints () + "/" + farLegRate.getRate () + ",tid=" + request.getTrade ().getTransactionID () );
            }
            if ( rfsTradeVerify.getStatus() == TradeResponse.MANUAL_VERIFICATION )
            {
                request.getRequestAttributes().setManualIntervention( true );
            }

            if ( rfsTradeVerify.getAcceptedNearLegRate() != null )
            {
            	if( rfsTradeVerify.getAcceptedNearLegRate().getRate() < 0.0
            			|| rfsTradeVerify.getAcceptedNearLegRate().getSpotRate() < 0.0
            			|| rfsTradeVerify.getAcceptedNearLegRate().getDealtAmount() < 0.0)
            	{
                	log.error("handleVerify : Dropping trade since negative nearLeg accepted amount or rate received for trade " + rfsTradeVerify.getTradeId());
                	sendAckToProvider(request, rfsTradeVerify, false, TradeAckMessages.NEGATIVE_AMOUNT_OR_RATE);
            		return;
            	}
            }
            if ( rfsTradeVerify.getAcceptedFarLegRate() != null )
            {
            	if( rfsTradeVerify.getAcceptedFarLegRate().getRate() < 0.0
            			|| rfsTradeVerify.getAcceptedFarLegRate().getSpotRate() < 0.0
            			 || rfsTradeVerify.getAcceptedFarLegRate().getDealtAmount() < 0.0 )
            	{
                	log.error("handleVerify : Dropping trade since negative farLeg accepted amount or rate received for trade " + rfsTradeVerify.getTradeId());
                	sendAckToProvider(request, rfsTradeVerify, false, TradeAckMessages.NEGATIVE_AMOUNT_OR_RATE);
            		return;
            	}
            }

            if ( request.getRequestAttributes().isNDF() || request.getRequestAttributes().isNDFSwap())
            {
				verifyFixingDate(request, rfsTradeVerify);
				changeMakerLegalEntityOnTrade(request, rfsTradeVerify);
			}
			setMakerUser( rfsTradeVerify, request );
			setMakerDetails(rfsTradeVerify, request);
            request.setAcceptedQuote( acceptedQuote );
            acceptedQuote.setRequest( request );
            rFSWorkflowFacade = ( RFSWorkflowFacade ) request.getFacade( RFSWorkflowFacade.NAME );
            Object makerUser = request.getRequestAttributes().getMakerUser();
            if ( makerUser != null )
            {
                request.getTrade().setMakerUser( ( User ) makerUser );
            }
            Trade trade = request.getTrade();
            String synthFlag = (String)rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS);
            boolean syntheticCross = false;
            if(synthFlag != null) {
                syntheticCross = Boolean.parseBoolean(synthFlag);
                trade.setSyntheticCross(syntheticCross);
            }
            String fCp = (String)rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_FOREIGN_CP);
            if(fCp != null){
                trade.setForeignCurrencyPair(fCp);
            }
            String lCp = (String)rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_LOCAL_CP);
            if(lCp != null){
                trade.setLocalCurrencyPair(lCp);
            }
            Long vCcyObjectId = (Long)rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_VEHICLE_CCY);
            if(vCcyObjectId != null){
                trade.setVehicleCCY(vCcyObjectId);
            }
            Double vCcyAmt = (Double)rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_VEHICLE_AMT);
            if(vCcyAmt != null){
                trade.setVehicleCCYAmount(vCcyAmt);
            }
            Long primaryDealtCcyId = (Long)rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_PRIMARY_DEALT_CCY);
            if(primaryDealtCcyId != null){
                trade.setPrimaryDealtCcyId(primaryDealtCcyId);
            }
            Long secondaryDealtCcyId = (Long)rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_SECONDARY_DEALT_CCY);
            if(secondaryDealtCcyId != null){
                trade.setSecondaryDealtCcyId(secondaryDealtCcyId);
            }

            Long primaryValueDate = (Long) rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_PRIMARY_SPOT_VALUEDATE);
            if(primaryValueDate != null){
                trade.setPrimarySpotValueDate(primaryValueDate);
            }

            Long secondaryValueDate = (Long) rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_SECONDARY_SPOT_VALUEDATE);
            if(secondaryValueDate != null){
                trade.setSecondarySpotValueDate(secondaryValueDate);
            }

            String primaryExecType = (String) rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_PRIMARY_EXECTYPE);
            if(primaryExecType != null){
                trade.setPrimaryExecutionType(EMSExecutionType.valueOf(primaryExecType));
            }

            String secondaryExecType = (String) rfsTradeVerify.getProperty(ISConstantsC.SYNTHETICCROSS_SECONDARY_EXECTYPE);
            if(secondaryExecType != null){
                trade.setSecondaryExecutionType(EMSExecutionType.valueOf(secondaryExecType));
            }

            /*
                Default value of A-Booking is true to make sure only when the property is being sent
             */
            boolean isABooking = true, netBuyingBase = false;
            Double netSpotAmount = null, netSpotAmountTerm = null;
            IdcDate spotValueDate = null;
            String isABookingStr = (String) rfsTradeVerify.getProperty(ISConstantsC.A_BOOKING);
            if (isABookingStr != null) {
                isABooking = Boolean.parseBoolean(isABookingStr);
            }
            String brokerExecTypeStr = (String) rfsTradeVerify.getProperty(ISConstantsC.EMS_EXECUTION_TYPE);
            EMSExecutionType brokerExecType = null;
            if( brokerExecTypeStr != null ){
                try {
                    brokerExecType = EMSExecutionType.valueOf(brokerExecTypeStr);
                }
                catch (Exception ex){
                    log.warn("Unsupported EMSExecutionType :"+brokerExecTypeStr);
                }
            }
            //if(!request.isNetRequest()) {
                netSpotAmount = (Double) rfsTradeVerify.getProperty(ISConstantsC.NET_SPOT_AMOUNT);
                netSpotAmountTerm = (Double) rfsTradeVerify.getProperty(ISConstantsC.NET_SPOT_AMOUNT_TERM);
                String spotValueDateStr = (String) rfsTradeVerify.getProperty(ISConstantsC.SPOT_VALUE_DATE);
                if (spotValueDateStr != null) {
                    spotValueDate = DateTimeFactory.newDate(spotValueDateStr, "yyyy-MM-dd");
                }else {
                    IdcDate tradeDate = trade.getTradeDate();
                    spotValueDate = ((FXLegDealingPrice) request.getRequestPrices().iterator().next()).getFXRate().getFXRateBasis().getValueDate(tradeDate, Tenor.SPOT_TENOR);
                }
                String netBuyingBaseStr = (String) rfsTradeVerify.getProperty(ISConstantsC.NET_BUYING_BASE);
                if (netBuyingBaseStr != null) {
                    netBuyingBase = Boolean.parseBoolean(netBuyingBaseStr);
                }
                if(netSpotAmount == null){
                    double netSpotAmountDealt = calculateNetSpotAmount(trade);
                    netBuyingBase = netSpotAmountDealt > 0;
                    netSpotAmountDealt = Math.abs(netSpotAmountDealt);
                    FXLeg fxLeg = (FXLeg) trade.getTradeLegs().iterator().next();
                    double spotRate = ((FXLeg) trade.getTradeLegs().iterator().next()).getFXPayment().getFXRate().getSpotRate();
                    if(isDealingInBaseCcy(request)){
                        netSpotAmount = netSpotAmountDealt;
                        netSpotAmountTerm = getSettleCurrency(request).round(netSpotAmount * spotRate);
                    }else {
                        netSpotAmountTerm = netSpotAmountDealt;
                        netSpotAmount = getSettleCurrency(request).round(netSpotAmountTerm / spotRate);
                    }
                }
            //}
            if (trade.getTradeLegs().size() == 1) {
                setProviderUTIToTrade(rfsTradeVerify, trade, ISCommonConstants.NEAR_LEG);
            } else {
                setProviderUTIToTrade(rfsTradeVerify, trade, ISCommonConstants.NEAR_LEG);
                setProviderUTIToTrade(rfsTradeVerify, trade, ISCommonConstants.FAR_LEG);
            }
            captureEvent( acceptedQuote, ISConstantsC.EVENT_TIME_DISP_IS_REC_VERIFY, isReceived );
            ISQuoteEventTimeFacadeC.copyEventTimesToTrade( request.getTrade(), acceptedQuote );
            ISQuoteEventTimeFacadeC.copyRFSQuoteSnapshot( request, acceptedQuote );

            acceptMIFIDParameters( rfsTradeVerify, request.getTrade() );
            
            String pricingType = (String) rfsTradeVerify.getProperty(ISConstantsC.PRICING_TYPE);
            pricingType = pricingType == null ? ISCommonUtilC.PricingType.RFS.name() : pricingType;
            trade.setPricingType(pricingType);
            
            if(ISCommonUtilC.PricingType.DI_ESPnSWAP.name().equals(pricingType) || ISCommonUtilC.PricingType.ESPnSWAP.name().equals(pricingType)
            		|| request.getRequestAttributes().isSpotAndSwapEnabled()){
            	trade.setFeatureFlags(trade.getFeatureFlags() | FeatureFlags.SPOTANDSWAP);
            }

            WorkflowMessage tradeServiceResponseMsg = null;
            boolean isPrimeBrokerWorkflowEnabled = ISUtilImpl.getInstance().isPrimeBrokerCoverTradeEnabled( request );
            Long execTime = (Long) rfsTradeVerify.getProperty(TradeAckMessages.TRD_EXECUTION_TIME);
            MiFIDTradeParams miFIDParams = trade.getMiFIDTradeParams();
            if(execTime != null && miFIDParams != null)
            {
                miFIDParams.setMiFIDTradeExecutionTime(execTime);
                trade.setMiFIDVenueExecutionTime( new Timestamp( execTime ) );
            }
            if(request.isRfq()) {
                User tradeUser = RFQRateCache.getInstance().getTxnIdTradeRequestUser().get(request.getTransactionID());
                trade.setEntryUser(tradeUser);
                log.warn("RFSMessageListenerC.handleRFSTradeVerify :: TXID = " + request.getTransactionID()+", trade user: "+request.getTrade().getEntryUser()+", user: "+tradeUser);
            }
            Trade coverTrade = null;
            if ( isPrimeBrokerWorkflowEnabled )
            {
                RFSPrimeBrokerWorkflowServiceC.doPreVerify( request, rfsTradeVerify );
                RFSFXTradeRatesCalculatorC.calculateTradeResponseRates( request,rfsTradeVerify );
                getProvider( request.getAcceptedQuote().getOrganization().getShortName() ).getRFSWorkflowFunctor().doPreVerify( request, rfsTradeVerify );
                ISUtilImpl.getInstance().setTradeId( request, request.getAcceptedQuote().getOrganization(), ( rfsTradeVerify.getProviderTradeId() == null || rfsTradeVerify.getProviderTradeId().trim().equals( "" ) ) ? rfsTradeVerify.getTradeId() : rfsTradeVerify.getProviderTradeId() );
                updateQuoteExternalIds( rfsTradeVerify, acceptedQuote );
                // updates the request price filled amount/prices.
                rfsWorkflowHandler.updateRequestOnVerification( request );
                tradeServiceResponseMsg = ISTradeFactory.getInstance().getISTradeFacade().verifyRFSTrade( request,rfsTradeVerify );
                RFSPrimeBrokerWorkflowServiceC.doPostVerify( request, tradeServiceResponseMsg,rfsTradeVerify );
                rFSWorkflowFacade.setRequestState(ISCommonConstants.REQUEST_STATE_EXECUTED);
                addToTradeCache(request.getTrade());
                ISPersistenceService.instance().rfsTradeVerification(request.getTrade());
                coverTrade = ( Trade ) tradeServiceResponseMsg.getParameterValue( TradeService.COVER_TRADE_KEY );
                addToTradeCache( coverTrade );
                coverTrade.setABooking(isABooking);
                coverTrade.setBrokerExecutionType(brokerExecType);
                if(netSpotAmount != null) {
                    coverTrade.setNetSpotAmount(netSpotAmount);
                    trade.setNetSpotAmount(netSpotAmount);
                }
                if(netSpotAmountTerm != null) {
                    coverTrade.setNetSpotAmountTerm(netSpotAmountTerm);
                    trade.setNetSpotAmountTerm(netSpotAmountTerm);
                }
                coverTrade.setSpotValueDate(spotValueDate);
                trade.setSpotValueDate(spotValueDate);
                coverTrade.setNetBuyingBase(netBuyingBase);
                coverTrade.setSyntheticCross(syntheticCross);
                coverTrade.setForeignCurrencyPair(fCp);
                coverTrade.setLocalCurrencyPair(lCp);
                if(vCcyObjectId != null) coverTrade.setVehicleCCY(vCcyObjectId);
                if(vCcyAmt != null) coverTrade.setVehicleCCYAmount(vCcyAmt);
                if(primaryValueDate != null) coverTrade.setPrimarySpotValueDate(primaryValueDate);
                if(secondaryValueDate != null) coverTrade.setSecondarySpotValueDate(secondaryValueDate);
                if(primaryExecType != null) coverTrade.setPrimaryExecutionType(EMSExecutionType.valueOf(primaryExecType));
                if(secondaryExecType != null) coverTrade.setSecondaryExecutionType(EMSExecutionType.valueOf(secondaryExecType));
                YMUtil.setYMBookName(coverTrade);
                for ( Object tl: coverTrade.getTradeLegs () )
                {
                    FXLeg fxLeg = ( FXLeg ) tl;
                    FXPaymentParameters fxPayment = fxLeg.getFXPayment ();
                    ISUtilImpl.RFSSpreadLog.info ( "VERIFY - final PB cover trade rates=" + fxPayment.getFXRate ().getToString () + ",leg=" + fxLeg.getName () + ",tid=" + request.getTrade ().getTransactionID () );
                    FXCoverRate coverRate = fxPayment.getFXCoverRate ();
                    if ( coverRate != null )
                    {
                        ISUtilImpl.RFSSpreadLog.info ( "VERIFY - final PB cover trade Cover rates=" + coverRate.getFXRate ().getToString () + ",leg=" + fxLeg.getName () + ",tid=" + request.getTrade ().getTransactionID () );
                    }
                }
            }
            else
            {
                trade.setABooking(isABooking);
                trade.setBrokerExecutionType(brokerExecType);
                if(netSpotAmount != null) trade.setNetSpotAmount(netSpotAmount);
                if(netSpotAmountTerm != null) trade.setNetSpotAmountTerm(netSpotAmountTerm);
                trade.setSpotValueDate(spotValueDate);
                trade.setNetBuyingBase(netBuyingBase);
                YMUtil.setYMBookName(trade);
                RFSFXTradeRatesCalculatorC.calculateTradeResponseRates( request,rfsTradeVerify );
                getProvider( request.getAcceptedQuote().getOrganization().getShortName() ).getRFSWorkflowFunctor().doPreVerify( request, rfsTradeVerify );
                ISUtilImpl.getInstance().setTradeId( request, request.getAcceptedQuote().getOrganization(), ( rfsTradeVerify.getProviderTradeId() == null || rfsTradeVerify.getProviderTradeId().trim().equals( "" ) ) ? rfsTradeVerify.getTradeId() : rfsTradeVerify.getProviderTradeId() );
                updateQuoteExternalIds( rfsTradeVerify, acceptedQuote );
                rfsWorkflowHandler.updateRequestOnVerification( request );
                ISTradeFactory.getInstance().getISTradeFacade().verifyRFSTrade( request,rfsTradeVerify );
                rFSWorkflowFacade.setRequestState( ISCommonConstants.REQUEST_STATE_EXECUTED );
                addToTradeCache( request.getTrade() );
                ISPersistenceService.instance().rfsTradeVerification( request.getTrade() );
            }
            boolean postSpreadApplied = isPostSpreadApplied(rfsTradeVerify);
            for ( Object tl: request.getTrade ().getTradeLegs () )
            {
                FXLeg fxLeg = ( FXLeg ) tl;
                FXPaymentParameters fxPayment = fxLeg.getFXPayment ();
                FXRate tradeFinalFXRate = fxPayment.getFXRate ();
                boolean mismatch = false;
                String acceptedQuoteDetailsStr = null;
                String legName = ISCommonConstants.FX_LEG.equals ( fxLeg.getName () ) ? ISCommonConstants.SINGLE_LEG : fxLeg.getName ();
                FXLegDealingPriceC requestPrice = ( FXLegDealingPriceC ) request.getRequestPrice ( legName );
                if ( requestPrice != null )
                {
                    FXRate acceptedFXRate = requestPrice.getCustomerAcceptedFXRate ();
                    if ( acceptedFXRate != null )
                    {
                        acceptedQuoteDetailsStr = acceptedFXRate.getToString ();
                        if ( acceptedFXRate.getSpotRate () != tradeFinalFXRate.getSpotRate ()
                                || acceptedFXRate.getForwardPoints () != tradeFinalFXRate.getForwardPoints ()
                                || acceptedFXRate.getRate () != tradeFinalFXRate.getRate () )
                        {
                            mismatch = true;
                            if(postSpreadApplied){
                                boolean matched = false;
                                if(ISCommonConstants.FX_LEG.equals ( fxLeg.getName () )){
                                    matched = isMatched(rfsTradeVerify, Math.abs(MathUtilC.subtract(acceptedFXRate.getSpotRate () , tradeFinalFXRate.getSpotRate ())),
                                            Math.abs(MathUtilC.subtract(acceptedFXRate.getForwardPoints () , tradeFinalFXRate.getForwardPoints ())), ISCommonConstants.FX_LEG);
                                }else if(ISCommonConstants.NEAR_LEG.equals ( fxLeg.getName () )){
                                    matched = isMatched(rfsTradeVerify, Math.abs(MathUtilC.subtract(acceptedFXRate.getSpotRate () , tradeFinalFXRate.getSpotRate ())),
                                            Math.abs(MathUtilC.subtract(acceptedFXRate.getForwardPoints () , tradeFinalFXRate.getForwardPoints ())), ISCommonConstants.NEAR_LEG);
                                }else if(ISCommonConstants.FAR_LEG.equals ( fxLeg.getName () )){
                                    matched = isMatched(rfsTradeVerify, Math.abs(MathUtilC.subtract(acceptedFXRate.getSpotRate () , tradeFinalFXRate.getSpotRate ())),
                                            Math.abs(MathUtilC.subtract(acceptedFXRate.getForwardPoints () , tradeFinalFXRate.getForwardPoints ())), ISCommonConstants.FAR_LEG);
                                }
                                if(matched){
                                    mismatch = false;
                                }
                            }
                        }
                    }
                }
                ISUtilImpl.RFSSpreadLog.info ( "VERIFY - " + ( mismatch ? "MISMATCH " : "" ) + "final trade rates="
                        + tradeFinalFXRate.getToString () + ",acceptedQuote=" + acceptedQuoteDetailsStr + ",leg="
                        + fxLeg.getName () + ",ccy1Amt=" + fxPayment.getCurrency1Amount () + fxPayment.getCurrency1 ()
                        + ",ccy2Amt="+ fxPayment.getCurrency2Amount () + fxPayment.getCurrency2 () + ",tid="
                        + request.getTrade ().getTransactionID () );
                FXCoverRate coverRate = fxPayment.getFXCoverRate ();
                if ( coverRate != null )
                {
                    ISUtilImpl.RFSSpreadLog.info ( "VERIFY - final trade cover rates=" + coverRate.getFXRate ().getToString ()
                            + ",leg=" + fxLeg.getName () + ",tid=" + request.getTrade ().getTransactionID () );
                }
            }
            //raise alert if maker parameters are missing in trade verify
            if(trade.isMTFTrade()) {
                MiFIDTradeParams mifidParams = trade.getMiFIDTradeParams();
                Organization providerOrg = getProvider(rfsTradeVerify.getProvider()).getProviderOrg();
                boolean externalMTF = !MiFIDUtils.isIntegralMTF(request.getMiFIDExecutionVenue());
                Collection<String> validationsList = MiFIDMBeanC.getInstance().getMiFIDFValidationsList(!externalMTF, MiFIDMBean.TRADE, providerOrg.getShortName());
                miFIDRequestValidator.validateMakerParams(mifidParams, providerOrg, rfsTradeVerify.getTradeId(), externalMTF, validationsList);
                miFIDRequestValidator.validateTrade(trade, externalMTF);
            }
            //send ack to provider is requested for
            sendAckToProvider(request, rfsTradeVerify, true, null);

            // publish to notification service
            if( request.isNetRequest() )
            {
                log.info("Trade notification not set for 'Net' trade. Id = " + request.getTransactionID() );
            } else if (ISDomainConstants.DEPOSITS.equals(trade.getSubTradeType()))
            {
                log.info("Trade notification not set for 'Deposits' trade. Id = " + request.getTransactionID() );
            } else
            {
                TradeNotificationUtil.publishTradeNotification(request.getTrade(), TradeEvent.VERIFY);
            }
            ExternalCreditCheckUtil.getInstance().confirmExternalCreditLimitIfRequired(request);
            
            TransactionMonitoringAgent.getInstance().addToConfirmationPending( request.getTransactionID(), request.getTrade() );
            rfsQuoteService.executeQuote( acceptedQuote );
            if ( isPrimeBrokerWorkflowEnabled )
            {
                PrimeBrokerWorkflowServiceManager.getInstance().getRFSPrimeBrokerWorkflowService( request ).addEvent( RFSPrimeBrokerWorkflowServiceC.VERIFY_TRADE, new Object[]{coverTrade}, System.currentTimeMillis() );
				if ( coverTrade != null && isMBean.isPrimeBrokerRFSCoverTradeEmailEnabled(coverTrade.getOrganization().getShortName()) )
                {
					ISEmailUtil.getInstance().sendMailMessage( coverTrade.getRequest() );
                }
            }
            log.warn("RFSMessageListenerC.handleRFSTradeVerify :: ended for TXID = " + request.getTransactionID()+", trade user: "+request.getTrade().getEntryUser());
            ExternalCreditCheckUtil.getInstance().tradeVerified(request);
        }
        catch ( Exception e )
        {
            log.error( "RFSMessageListenerC.handleRFSTradeVerify :: Exception ", e );
        }
        logEvent( acceptedQuote );
    }

    private boolean isPostSpreadApplied(RFSTradeVerify rfsTradeVerify) {
        boolean postSpreadApplied = false;
        List<SpreadElement> spreads = rfsTradeVerify.getSpreads();
        if(spreads != null) {
            for (SpreadElement spreadElement : spreads) {
                if (FXCoverRate.PP_POST_SPOT_SPREAD.equals(spreadElement.getSpreadName()) || FXCoverRate.PP_POST_FWD_SPREAD.equals(spreadElement.getSpreadName()) ||
                        FXCoverRate.PP_POST_NEAR_SPREAD.equals(spreadElement.getSpreadName()) || FXCoverRate.PP_POST_FAR_SPREAD.equals(spreadElement.getSpreadName())) {
                    if (spreadElement.getSpreadValue() != 0.0) {
                        postSpreadApplied = true;
                        break;
                    }
                }
            }
        }
        return postSpreadApplied;
    }

    private boolean isMatched(RFSTradeVerify rfsTradeVerify, double spotSpread, double fwdSpread, String leg) {
        boolean matched = false;
        double ppSpotSpread = 0.0;
        double ppFwdSpread = 0.0;
        double ppNearSpread = 0.0;
        double ppFarSpread = 0.0;
        List<SpreadElement> spreads = rfsTradeVerify.getSpreads();
        if (spreads != null) {
            for (SpreadElement spreadElement : spreads) {
                if (FXCoverRate.PP_POST_SPOT_SPREAD.equals(spreadElement.getSpreadName())) {
                    ppSpotSpread = Math.abs(spreadElement.getSpreadValue());
                } else if (leg .equals(ISCommonConstants.FX_LEG) && FXCoverRate.PP_POST_FWD_SPREAD.equals(spreadElement.getSpreadName())) {
                    ppFwdSpread = Math.abs(spreadElement.getSpreadValue());
                } else if (leg .equals(ISCommonConstants.NEAR_LEG) && FXCoverRate.PP_POST_NEAR_SPREAD.equals(spreadElement.getSpreadName())) {
                    ppNearSpread = Math.abs(spreadElement.getSpreadValue());
                } else if (leg .equals(ISCommonConstants.FAR_LEG) && FXCoverRate.PP_POST_FAR_SPREAD.equals(spreadElement.getSpreadName())) {
                    ppFarSpread = Math.abs(spreadElement.getSpreadValue());
                }
            }
            if (leg .equals(ISCommonConstants.FX_LEG)) {//spot or outright
                if (spotSpread == ppSpotSpread && fwdSpread == ppFwdSpread) {
                    matched = true;
                }
            } else if (leg .equals(ISCommonConstants.NEAR_LEG)) {// swap near leg
                if (spotSpread == ppSpotSpread && fwdSpread == ppNearSpread) {
                    matched = true;
                }
            } else if (leg .equals(ISCommonConstants.FAR_LEG)) {//swap far leg
                if (spotSpread == ppSpotSpread && fwdSpread == ppFarSpread) {
                    matched = true;
                }
            }
        }
        return matched;
    }

    private void setProviderUTIToTrade(RFSTradeVerify rfsTradeVerify, Trade trade, String legName) {
        RFSFXLeg tradeLeg = rfsTradeVerify.getTradeLeg(legName);
        if(tradeLeg != null){
            String uti = tradeLeg.getUTI();
            if(uti != null) {
                log.info(" RFSMessageListenerC.handleRFSTradeVerify().setProviderUTIToTrade: setting provider UTI: "+uti);
                TradeLeg tradeLegOnTrade = trade.getTradeLeg(legName);
                if(tradeLegOnTrade != null){
                    tradeLegOnTrade.setUTI(uti);
                }else{
                    tradeLegOnTrade = trade.getTradeLeg(FXSingleLeg.SINGLE_LEG);
                    tradeLegOnTrade.setUTI(uti);
                }
            }
        }
    }

    private boolean isDealingInBaseCcy(Request request){
        CurrencyPair currencyPair = request.getCurrencyPair();
        FXLegDealingPrice dealingPrice = (FXLegDealingPrice)request.getRequestPrices().iterator().next();
        Currency dealtCurrency = dealingPrice.getDealtCurrency();
        return dealtCurrency.isSameAs(currencyPair.getBaseCurrency());
    }

    private Currency getSettleCurrency(Request request){
        FXLegDealingPrice dealingPrice = (FXLegDealingPrice)request.getRequestPrices().iterator().next();
        return dealingPrice.getSettledCurrency();
    }

    private double calculateNetSpotAmount(Trade trade){
        double netAmount = 0.0D;
        Collection tradeLegs = trade.getTradeLegs();
        for(Object obj : tradeLegs){
            FXLeg leg = (FXLeg)obj;
            double dealtAmount = leg.getFXPayment().getDealtAmount();
            boolean buying = leg.getFXPayment().isBuyingBaseCurrency();
            if(!buying) dealtAmount = MathUtilC.multiply(dealtAmount, -1D);
            netAmount = MathUtilC.add(netAmount, dealtAmount);
        }
        return netAmount;
    }

    private void sendAckToProvider(final RFSTradeResponse tradeResponse, String reason)
    {
        try
        {
            if (!(tradeResponse instanceof RFSTradeVerify)) {
                if (log.isDebugEnabled()) {
                    log.debug(".sendAckToProvider:: skipping as ack not requested " + tradeResponse.getProvider() + ' ' + tradeResponse.getOrganization() + ' ' + tradeResponse.getTradeId());
                }
                return;
            }
            RFSTradeVerify tradeVerify = (RFSTradeVerify) tradeResponse;
            boolean ackRequested = tradeVerify.getProperty(TradeAckMessages.ACK_REQUESTED) != null;
            ackRequested = ackRequested && ((String) tradeVerify.getProperty(TradeAckMessages.ACK_REQUESTED)).equalsIgnoreCase("true");
            if (!ackRequested) {
                if (log.isDebugEnabled()) {
                    log.debug(".sendAckToProvider:: skipping as ack not requested " + tradeVerify.getProvider() + ' ' + tradeVerify.getOrganization() + ' ' + tradeResponse.getTradeId());
                }
                return;
            }
            String providerName = tradeVerify.getProvider();
            String organization = tradeVerify.getOrganization();
            String userName = tradeVerify.getUser();
            User user = UserUtil.getUser(organization, userName);
            if (user == null) {
                AlertLoggerFactory.getMessageLogger().log(AlertErrorCode.LP_ACK_INVALID_USER, "RFSMessageListenerC", "LP ACK INVALID USER " + ' ' + providerName + ' ' + userName + '@' + organization + ' ' + tradeVerify.getTradeId(), null);
                return;
            }
            Provider provider = getProvider(providerName);
            if (provider == null) {
                AlertLoggerFactory.getMessageLogger().log(AlertErrorCode.LP_ACK_INVALID_PROVIDER, "RFSMessageListenerC", "LP ACK INVALID PROVIDER " + ' ' + providerName + ' ' + userName + '@' + organization + ' ' + tradeVerify.getTradeId(), null);
                return;
            }
            String tradeId = tradeVerify.getTradeId();
            String providerTradeId = tradeVerify.getProviderTradeId();
            TradeAck ack = new TradeAckC();
            ack.setTradeId(tradeId);
            ack.setProviderExecId(providerTradeId);
            ack.setProviderOrderId(tradeVerify.getProviderOrderId());
            ack.setExecAckStatus(TradeAckMessages.DO_NOT_KNOW);
            ack.setDkReason(reason);
            Request request = getRequest(tradeResponse);
            if(request == null){
                AlertLoggerFactory.getMessageLogger().log(AlertErrorCode.LP_ACK_REQUEST_NOT_FOUND, "RFSMessageListenerC", "LP ACK REQUEST NOT FOUND " + ' ' + providerName + ' ' + userName + '@' + organization + ' ' + tradeVerify.getTradeId(), null);
                return;
            }
            String stream = request.getTrade().getStream();
            ack.setStreamName(stream);
            Adaptor adaptor = provider.getAdaptor();
            Map subExtSysIds = adaptor.getConfiguration().getRFSSubscriptionExternalSystemsMap();
            Map tradeExtSysIds = adaptor.getConfiguration().getRFSTradeExternalSystemMap();
            RFSAccountIDValidator accountIDValidator = new RFSAccountIDValidator(adaptor, subExtSysIds, tradeExtSysIds);
            accountIDValidator.setTradeAccountId(request, ack);
            sendAckToProvider(ack, provider, user);
        }catch (Exception e)
        {
            log.warn(".sendAckToProvider: Exception during sending ack to provider " + tradeResponse.getTradeId(), e);
            AlertLoggerFactory.getMessageLogger().log(AlertErrorCode.LP_ACK_EXCEPTION, "RFSMessageListenerC", "LP ACK EXCEPTION " + ' ' + tradeResponse.getTradeId(), null);
        }
    }

    private void sendAckToProvider(final Request request, RFSTradeVerify tradeVerify, boolean accepted, String rejectReason)
    {
        try
        {
            boolean ackRequested = tradeVerify.getProperty(TradeAckMessages.ACK_REQUESTED) != null;
            ackRequested = ackRequested && ((String) tradeVerify.getProperty(TradeAckMessages.ACK_REQUESTED)).equalsIgnoreCase("true");
            if (!ackRequested) {
                if (log.isDebugEnabled()) {
                    log.debug(".sendAckToProvider:: skipping as ack not requested " + tradeVerify.getProvider() + ' ' + tradeVerify.getOrganization() + ' ' + tradeVerify.getTradeId());
                }
                return;
            }
            Trade trade = request.getTrade();
            final Provider provider = getProvider(tradeVerify.getProvider());
            final TradeAck tradeAck = new TradeAckC();
            MiFIDTradeParams miFIDParams = trade.getMiFIDTradeParams();
            Date executionTime;
            if (miFIDParams != null && miFIDParams.getMiFIDTradeExecutionTime() != null)
            {
                executionTime = new Date(miFIDParams.getMiFIDTradeExecutionTime());
            } else
            {
                executionTime = trade.getExecutionDateTime();
            }
            tradeAck.setExecutionDateTime(executionTime);
            if (accepted) {
                tradeAck.setExecAckStatus(TradeAckMessages.ACCEPTED);
            } else
            {
                tradeAck.setExecAckStatus(TradeAckMessages.REJECTED);
                tradeAck.setDkReason(rejectReason);
            }
            tradeAck.setProviderExecId(tradeVerify.getProviderTradeId());
            tradeAck.setTradeId(tradeVerify.getTradeId());
            tradeAck.setProviderOrderId(tradeVerify.getProviderOrderId());
            String stream = request.getTrade().getStream();
            tradeAck.setStreamName(stream);
            try {
                String isinLinkId = request.getISINLinkId();
                tradeAck.setIsinLinkId(isinLinkId);
                boolean mtf = request.isMTF();
                tradeAck.setMtf(mtf);
                Organization miFIDExecutionVenue = request.getMiFIDExecutionVenue();
                String execVenueMic = miFIDExecutionVenue != null ? miFIDExecutionVenue.getMICCode() : null;
                tradeAck.setExecutionVenueMIC(execVenueMic);
                FXRateBasis rateBasis = ISUtilImpl.getInstance().getFXRateBasis(request.getOrganization(), provider.getAdaptor().getOrganization(), request.getCurrencyPair());
                IdcDate spotDate = rateBasis.getSpotDate(request.getTrade().getTradeDate());
                tradeAck.setSpotDate(spotDate.asJdkDate().getTime());
                tradeAck.setMifidParams(miFIDParams);
                Collection<TradeLeg> tradeLegs = trade.getTradeLegs();
                Map<String, LegInfo> legInfoMap = tradeAck.getLegInfo();
                for (TradeLeg leg : tradeLegs) {
                    String isin = leg.getISIN();
                    String uti = leg.getUTI();
                    FXLeg fxLeg = (FXLeg) leg;
                    Double dealtAmount = null;
                    Double rate = null;
                    String side = null;
                    if(fxLeg!=null && fxLeg.getFXPayment()!=null){
                    	dealtAmount = fxLeg.getFXPayment().getDealtAmount();
                    	rate = fxLeg.getFXPayment().getFXRate() !=null ? fxLeg.getFXPayment().getFXRate().getRate() : null;
                    	if(fxLeg.getFXPayment().isDealtCurrency1()){
                    		side = fxLeg.getFXPayment().isBuyingCurrency1() ? TradeAckMessages.BUY : TradeAckMessages.SELL;
                    	}else{
                    		side = fxLeg.getFXPayment().isBuyingCurrency1() ? TradeAckMessages.SELL : TradeAckMessages.BUY;
                    	}
                    }
                    LegInfo legInfo = new LegInfo(isin,uti,dealtAmount,rate,side);
                    legInfoMap.put(leg.getName(),legInfo);
                }
                
                tradeAck.setCurrencyPair(tradeVerify.getBaseCurrency()+"/"+tradeVerify.getVariableCurrency());
                
            }catch (Exception e)
            {
                log.warn("Exception during sending additonal parameters. tid=" + tradeVerify.getTradeId(), e);
            }
            Adaptor adaptor = provider.getAdaptor();
            Map subExtSysIds = adaptor.getConfiguration().getRFSSubscriptionExternalSystemsMap();
            Map tradeExtSysIds = adaptor.getConfiguration().getRFSTradeExternalSystemMap();
            RFSAccountIDValidator accountIDValidator = new RFSAccountIDValidator(adaptor, subExtSysIds, tradeExtSysIds);
            accountIDValidator.setTradeAccountId(request, tradeAck);
            sendAckToProvider(tradeAck, provider, request.getUser());
        }catch (Exception e)
        {
            log.warn(".sendAckToProvider: Exception during sending ack to provider " + tradeVerify.getTradeId(), e);
            AlertLoggerFactory.getMessageLogger().log(AlertErrorCode.LP_ACK_EXCEPTION, "RFSMessageListenerC", "LP ACK EXCEPTION " + ' ' + tradeVerify.getTradeId(), null);
        }
    }

    private void sendAckToProvider(final TradeAck ack, final Provider provider, final User user)
    {
        String adaptorOrgName = provider.getAdaptor().getOrganization().getShortName();
        ISMBean isMbean = ISFactory.getInstance().getISMBean();
        String serverId = isMbean.getServerId() + '@' + adaptorOrgName;
        ack.setServerId(serverId);
        ack.setProviderName(adaptorOrgName);
        MaintenanceAgent.getSingleThreadedExecutorService().execute(new Runnable() {
            @Override
            public void run() {
                try {
                    TradeRequestDispatcher.sendMessageToProvider(ack, provider, user);
                    log.info(".sendAckToProvider:: " + provider.getName() + ' ' + user + ' ' + ack);
                } catch (MessageCommunicationExceptionC e) {
                    log.warn(".sendAckToProvider: failed to send trade ack to provider. ack=" + ack, e);
                    AlertLoggerFactory.getMessageLogger().log(AlertErrorCode.LP_ACK_EXCEPTION, "RFSMessageListenerC", ack.getTradeId(), ack.toString());
                }
            }
        });
        if(!ack.getExecAckStatus().equals(TradeAckMessages.ACCEPTED))
        {
            AlertLoggerFactory.getMessageLogger().log(AlertErrorCode.LP_ACK_REJECT, "RFSMessageListenerC", "LP ACK REJECT " + ' ' + provider.getName() + ' ' + user + ' ' + ack, null);
        }
    }

    private void changeMakerLegalEntityOnTrade(Request request, RFSTradeVerify rfsTradeVerify) throws Exception
    {    	
    	Map<String, String> sefParameters = rfsTradeVerify.getSEFParameters();
    	if(sefParameters != null && sefParameters.size() > 0)
    	{
    		String makerLEI = sefParameters.get(RFSMessage.MAKER_LEI);
    		
			StringBuilder sb = new StringBuilder(250);
			sb.append("RFSMessageListenerC.changeMakerLegalEntityOnTrade : TransactionId=").append(request.getTransactionID());
			sb.append(" | MakerLEI received in trade verification = ").append(makerLEI);    			
            log.info(sb.toString());
            
    		if(makerLEI != null && !"".equals(makerLEI.trim()))
    		{               
    			//1. Check whether the CounterPartyB LEI on trade is same as makerLEI received in the verify message.
    			Counterparty oldCounterPartyB = request.getTrade().getCounterpartyB();
    			String oldMakerLEI = oldCounterPartyB.getLEI();
    			
    			// LE on trade is not same as the one received from ProviderAdap. So replace the LE on trade with the one received from PA
    			if(!makerLEI.equals(oldMakerLEI))
    			{    			
	    			//2. Get LE corresponding to this LEI                
	                Organization lpOrg = request.getAcceptedQuote().getOrganization();
	                LegalEntity newMakerLe = OrganizationUtil.getLegalEntityByLEI(lpOrg, makerLEI);
	                
	                if (newMakerLe == null) 
	                {	      
	                	//TODO
	                	//TODO: Notify GridMonitor
	                	//TODO
	                	//WorkflowRuntimeMonitor.getInstance().notifyWorkflowEvent( request, request.getTrade(), ManagementConstants.CH_SDR_UPDATE_EVENT, null);
	                	
	                	StringBuilder sbError = new StringBuilder(350);
	                	sbError.append("RFSMessageListenerC.changeMakerLegalEntityOnTrade : LE corresponding to MakerLEI ").
	                	append(makerLEI).append(" Could not be found. ");
	                	sbError.append(" | LP Organisation=").append(lpOrg.getShortName());
	                	sbError.append(" | FI Organization=").append(request.getOrganization().getShortName());
	                	sbError.append(" | transactionId=").append(request.getTransactionID());
	                	log.error(sbError.toString());
	                	
	                	//TODO
	                	//TODO:: Send Email action to SEFEngr with above message
	                	//TODO
	                } 
	                else 
	                {
	                	long t1 = System.currentTimeMillis();
	                	
	                    Trade trade = request.getTrade();
	                    Organization fiOrg = request.getOrganization();
	                    LegalEntity fiLe = (LegalEntity)trade.getCounterpartyA();
	                    	                    
	                	StringBuilder sbInfo = new StringBuilder(330);
	                	sbInfo.append("RFSMessageListenerC.changeMakerLegalEntityOnTrade : Changing CPTYB on Trade ").append(request.getTransactionID());
	                	sbInfo.append(" | New MakerLE=").append(newMakerLe.getShortName());
	                	sbInfo.append(" | Old MakerLE=").append(oldCounterPartyB.getShortName());
	                	sbInfo.append(" | LP=").append(lpOrg.getShortName());
	                	sbInfo.append(" | FI=").append(fiOrg.getShortName());
	                    log.info(sbInfo.toString());
	                    
	                    
	                    //3. Set the relationship between newMakerLe and FI LE if not already existing.
	                    long t2 = System.currentTimeMillis();
	                    TradingRelationshipService tradingRelationshipService = AdminServiceProvider.getInstance().getTradingRelationshipService();
	                    boolean isNewMakeLeAssociatedToFI = tradingRelationshipService.isLegalEntityToCounterPartyLegalEntityAssociated(fiLe, newMakerLe);
	                    if(!isNewMakeLeAssociatedToFI)
	                    {
		                	StringBuilder sb2 = new StringBuilder(330);
		                	sb2.append("RFSMessageListenerC.changeMakerLegalEntityOnTrade : Setting up relationship and initializing " +
		                			"credit between FI-le and NewMaker-le for trade ");
		                	sb2.append(request.getTransactionID());
		                	sb2.append(" | LPOrg=").append(lpOrg.getShortName());
		                	sb2.append(" | NewMakerLe=").append(newMakerLe.getShortName());
		                	sb2.append(" | FIOrg=").append(fiOrg.getShortName());
		                	sb2.append(" | FILe=").append(fiLe.getShortName());		                			                	
		                    log.info(sb2.toString());
		                    
	                        //TODO: CreditInitialization is a costly activity (was taking around 5 secs). See if it can be handled in a different thread
	                        createTradingRelationShipAndInitializeCredit(newMakerLe, (LegalEntity)oldCounterPartyB, fiLe);
	                    }
	                    long t3 = System.currentTimeMillis();
	                    trade.setCounterpartyB(newMakerLe);
	                    TradeCloneServiceC.refreshCptysOnCptyTrades(trade);
	                    ISPersistenceService.instance().changeLPLEOnTrade(trade);
	            		
	            		StringBuilder timingString = new StringBuilder(50);
	            		timingString.append("RFSMessageListenerC.changeMakerLegalEntityOnTrade: Time (mills) taken to change CPTYB on Trade: ").append('\n');
	            		timingString.append(" | Total Time = ").append(t3-t1).append('\n');
	            		timingString.append(" | Time to set relationship between NewMakerLe and FILe and initialize credit = ").append(t3-t2).append('\n');
	            		log.info(timingString.toString());
	                }
    			}
    			else
    			{    				
                    log.info("RFSMessageListenerC.changeMakerLegalEntityOnTrade : MakerLEI received from tradeverify " + makerLEI +
                    		" is same as the one already on the trade " + request.getTransactionID() +". Hence No-Op");    				
    			}
    		}
    	}
	}


    /**
     * Creates two way trading parties between FI and LP LegalEntities and sets two way relationship between the two
     * 
     * @param newMakerLe
     * @param oldMakerLe
     * @param fiLe
     */
    
	private void createTradingRelationShipAndInitializeCredit(final LegalEntity newMakerLe, final LegalEntity oldMakerLe, final LegalEntity fiLe) 
	{
		TransactionalMessage message = new DefaultTransactionMessage() {
			
			@Override
			public void processTransaction() throws TransactionException {
				long t1 = System.currentTimeMillis();
				Organization fiOrg = fiLe.getOrganization();
				Organization lpOrg = newMakerLe.getOrganization();
				TradingRelationshipService tradingRelationshipService = AdminServiceProvider.getInstance().getTradingRelationshipService();
				tradingRelationshipService.createTradingParty(newMakerLe, fiOrg);
				tradingRelationshipService.createTradingParty(fiLe, lpOrg);
				tradingRelationshipService.associateCounterPartyOrganizationToLegalEntity(fiLe, lpOrg, newMakerLe, false, false);
				tradingRelationshipService.associateCounterPartyOrganizationToLegalEntity(newMakerLe, fiOrg, fiLe, false, false);
				long t2 = System.currentTimeMillis();
				log.info("RFSMessageListenerC.createTradingRelationShipAndInitializeCredit:  time taken in mills to setup relationship between MakerLe and FILe = " + (t2-t1));
			}
			
			@Override
			public Collection<Class> getWriteableClass() {
				Collection<Class> writeableClass = new ArrayList<Class>();
		        writeableClass.add( com.integral.finance.counterparty.LegalEntityC.class );
		        writeableClass.add( com.integral.user.UserC.class );
		        writeableClass.add( com.integral.finance.counterparty.TradingPartyC.class );
		        writeableClass.add( com.integral.persistence.NamespaceC.class );
		        writeableClass.add( com.integral.user.OrganizationC.class );
		        writeableClass.add( com.integral.finance.counterparty.CounterpartyCustomFieldC.class );
		        return writeableClass;
			}
			
			@Override
			public void postTransaction() throws TransactionException 
			{
				initializeAndSetCreditLimit(newMakerLe, oldMakerLe, fiLe);
			}
			
			@Override
			public String getActivityDetails() {
				return "Creating FI-LP relationship post trade";
			}
		};
		
		message.process();		
	}

	
	private void initializeAndSetCreditLimit(LegalEntity newMakerLe, LegalEntity oldMakerLe, LegalEntity fiLe) 
	{		
		Organization fiOrg = fiLe.getOrganization();
		Organization lpOrg = newMakerLe.getOrganization();
		try{
			long t1 = System.currentTimeMillis();
			// Establish credit relationship
			AdminCreditWebService creditService = AdminServiceProvider.getInstance().getAdminCreditWebService();
			creditService.initializeCreditForCounterPartyLegalEntity( fiOrg, newMakerLe );
					
			// Copy the Credit limits form the OldMakerLe to the newMakerLe
			long t2 = System.currentTimeMillis();
			Currency creditCurrency = CreditUtilC.getCounterpartyCreditLimitCurrency( fiOrg, lpOrg, newMakerLe.getTradingParty(fiOrg));			 
					
			CreditLimitClassification[] classifications = { CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 
					CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION};
			CreditLimitAdminService creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService();
			for(CreditLimitClassification clsf:classifications)
			{
				long tt1 = System.currentTimeMillis();
				cloneCreditLimit(fiOrg, oldMakerLe, newMakerLe, creditAdminSvc, clsf, creditCurrency);
				long tt2 = System.currentTimeMillis();
				log.info("RFSMessageListenerC.createTradingRelationShipAndInitializeCredit: Time taken for cloning credit limit: " + (tt2-tt1));
			}
			long t3 = System.currentTimeMillis();
			StringBuilder sb = new StringBuilder(50);
			sb.append("RFSMessageListenerC.createTradingRelationShipAndInitializeCredit: Time taken in mills to initialize credit between MakerLe and FILe = ").append(t2-t1);
			sb.append(" | Total time for cloning credit limits = ").append(t3-t2);
			log.info(sb.toString());
		}
		catch(Exception ex)
		{
			log.error("Exception when initializingAndSettingCreditLimit: ", ex);
		}
	}
	
	
	private void cloneCreditLimit(Organization creditProviderOrg, LegalEntity fromLe,  LegalEntity toLe, 
			CreditLimitAdminService creditAdminSvc, CreditLimitClassification clsf, Currency ccy)
	{		
		 double creditLimit = creditAdminSvc.getCreditLimit(creditProviderOrg, fromLe.getTradingParty(creditProviderOrg), clsf);
		 creditAdminSvc.setCreditLimit(creditProviderOrg, toLe.getTradingParty(creditProviderOrg), clsf, creditLimit, ccy);		
	}

	/**
     * @param request
     * @param tradeResponse
     */
    private void handleRFSTradeReject( Request request, RFSTradeResponse tradeResponse, long isReceived )
    {
        Quote acceptedQuote = null;
        try
        {
            log.warn( "RFSMessageListenerC.handleRFSTradeReject :: started for TXID = " + request.getTransactionID() );
            acceptedQuote = request.getAcceptedQuote();
            ISUtilImpl.getInstance().setSessionContext( request.getUser() );
            RFSWorkflowFacade rFSWorkflowFacade = ( RFSWorkflowFacade ) request.getFacade( RFSWorkflowFacade.NAME );
            rFSWorkflowFacade.setLpQuoteState( acceptedQuote.getOrganization(), ISCommonConstants.QUOTE_STATE_REJECTED, ISCommonConstants.TradeRejectedReceivedByServer ,null);

            request.setAcceptedQuote( acceptedQuote );
            acceptedQuote.setRequest( request );
            rFSWorkflowFacade = ( RFSWorkflowFacade ) request.getFacade( RFSWorkflowFacade.NAME );

            RFSTradeReject rfsTradeReject = ( RFSTradeReject ) tradeResponse;
            if ( rfsTradeReject.getStatus() == TradeResponse.MANUAL_REJECTION )
            {
                request.getRequestAttributes().setManualIntervention( true );
            }
            RFSFXTradeRatesCalculatorC.calculateTradeResponseRates( request,rfsTradeReject );
            boolean isPrimeBrokerWorkflowEnabled = ISUtilImpl.getInstance().isPrimeBrokerCoverTradeEnabled( request );
            if ( isPrimeBrokerWorkflowEnabled )
            {
                RFSPrimeBrokerWorkflowServiceC.doPreDecline( request, (RFSTradeReject) tradeResponse );
            }
            getProvider( request.getAcceptedQuote().getOrganization().getShortName() ).getRFSWorkflowFunctor().doPreDecline( request, rfsTradeReject );

            ISUtilImpl.getInstance().setTradeId( request, request.getAcceptedQuote().getOrganization(), rfsTradeReject.getTradeId() );
            String rejReason = getRejectionReason( rfsTradeReject );
            if ( rfsTradeReject.getStatus() == TradeResponse.REJECTED_BY_ADAPTOR )
            {
            	request.setResponseType( ResponseType.REJECT_AUTO_CANCEL );
                request.getRequestAttributes().setRejectedByAdaptor(ISConstantsC.REJ_BY_ADAPTOR_AUTOCANCELLATION);
            }
            else
            {
            	request.getRequestAttributes().setRejectedByAdaptor(ISConstantsC.REJ_BY_ADAPTOR_NO );
            }
            request.getTrade().getWorkflowStateMap().setWorkflowCodeArgument( rejReason );

            captureEvent( acceptedQuote, ISConstantsC.EVENT_TIME_DISP_IS_REC_REJ, isReceived );
            ISQuoteEventTimeFacadeC.copyEventTimesToTrade( request.getTrade(), acceptedQuote );
            ISQuoteEventTimeFacadeC.copyRFSQuoteSnapshot( request, acceptedQuote );
            WorkflowMessage tradeServiceResponseMsg =ISTradeFactory.getInstance().getISTradeFacade().rejectTrade( request, rejReason );
            if ( isPrimeBrokerWorkflowEnabled )
            {
                RFSPrimeBrokerWorkflowServiceC.doPostDecline( request, tradeServiceResponseMsg );
            }
            rFSWorkflowFacade.setRequestState( ISCommonConstants.REQUEST_STATE_DECLINED );
            acceptMIFIDParameters(rfsTradeReject,request.getTrade());
            ISPersistenceService.instance().rfsTradeRejection( request.getTrade() );
            rfsQuoteService.declineQuote( acceptedQuote );
            if ( isPrimeBrokerWorkflowEnabled )
            {
                Trade coverTrade = ( Trade ) tradeServiceResponseMsg.getParameterValue( TradeService.COVER_TRADE_KEY );
                PrimeBrokerWorkflowServiceManager.getInstance().getRFSPrimeBrokerWorkflowService( request ).addEvent( RFSPrimeBrokerWorkflowServiceC.REJECT_TRADE, new Object[]{coverTrade}, System.currentTimeMillis() );
            }
            log.warn( "RFSMessageListenerC.handleRFSTradeReject :: ended for TXID = " + request.getTransactionID() );
        }
        catch ( Exception e )
        {
            log.error( "RFSMessageListenerC.handleRFSTradeReject :: Exception ", e );
        }
        logEvent( acceptedQuote );
    }

    /**
     * @param request
     * @param tradeResponse
     */
    private void handleRFSTradePending( Request request, RFSTradeResponse tradeResponse )
    {
        try
        {
        	log.warn( "RFSMessageListenerC.handleRFSTradePending :: started for TXID = " + request.getTransactionID() );
            request.getRequestAttributes().setDelayedResponse( "true" );

            acceptMIFIDParameters(tradeResponse,request.getTrade());
            ISPersistenceService.instance().rfsTradePendingVerification( request );

            RequestService rs = ServiceFactory.getISRequestService();
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage( request, ISConstantsC.MSG_EVT_PENDING, ISConstantsC.MSG_TOPIC_REQUEST );
            rs.process( wfMsg );

            EventTimeFacade quoteETF = ( QuoteEventTimeFacade ) request.getAcceptedQuote().getFacade( EventTimeFacade.NAME );
            quoteETF.setTime( ISCommonConstants.EVENT_TIME_DEAL_PENDING, tradeResponse.getTiming().getLongTime( ISCommonConstants.EVENT_TIME_DISP_TIMEOUT_AT_ADAPTOR ) );
        }
        catch ( Exception e )
        {
            log.error( "RFSMessageListenerC.handleRFSTradePending :: Exception ", e );
        }
    }

    /**
     * Get instance of provider
     *
     * @return provider
     */
    protected Provider getProvider( String providerShortName )
    {
        return ProviderManagerC.getInstance().getProvider( providerShortName );
    }

    protected Request getRequest( RFSTradeResponse tradeResponse )
    {
        String tradeId = tradeResponse.getTradeId();
        return getRequest( tradeId,true );
    }

    private Request getRequest( String tradeId, boolean doDBLookup )
    {
        int idx = tradeId.lastIndexOf( 'C' );
        if( idx != -1 )
        {
            tradeId = tradeId.substring( 0,idx );
        }
        Request request = HandlerCacheC.getHandlerCache().getRequest( tradeId );

        if ( doDBLookup )
        {
            if ( request == null )
            {
                try
                {
                    ExpressionBuilder eb = new ExpressionBuilder();
                    Expression expr = eb.get( "transactionID" ).equal( tradeId );
                    ReadObjectQuery raq = new ReadObjectQuery();
                    raq.setReferenceClass( RequestC.class );
                    raq.setSelectionCriteria( expr );
                    request = ( Request ) SecondaryPersistenceFactory.newSecondarySession().executeQuery( raq );
                }
                catch ( Exception e )
                {
                    log.error( "RFSMessageListenerC.getRequest :: Exception occurred ", e );
                }
            }
        }
        return request;
    }

    /**
     * Log the event time entries for transaction Id on console/file
     *
     * @param quote
     */
    protected void logEvent( Quote quote )
    {
        QuoteEventTimeFacade etFacade = ( QuoteEventTimeFacade ) quote.getFacade( EventTimeFacade.NAME );
        if ( null != etFacade )
        {
            etFacade.setTransactionId( quote.getTransactionID() );
            ( ( ISQuoteEventTimeFacadeC ) etFacade ).logRFS( ISCommonConstants.EVENT_TIMES, ISConstantsC.EVENT_TIME_SERVER_OFFSET_LIST );
        }
    }


    /**
     * Captures message for given time
     *
     * @param quote
     * @param message
     * @param time
     */
    protected void captureEvent( Quote quote, String message, long time )
    {
        EventTimeFacade etFacade = ( EventTimeFacade ) quote.getFacade( EventTimeFacade.NAME );
        if ( null != etFacade )
        {
            etFacade.setTime( message, time );
        }
    }

    protected void captureEvent( Quote quote, Map timings )
    {
        if(quote == null){
            return;
        }
        EventTimeFacade etFacade = ( EventTimeFacade ) quote.getFacade( EventTimeFacade.NAME );
        if ( etFacade != null )
        {
            if ( timings != null )
            {
                Iterator keyIt = timings.keySet().iterator();
                while ( keyIt.hasNext() )
                {
                    String key = ( String ) keyIt.next();
                    etFacade.setTime( key, ( ( Long ) timings.get( key ) ).longValue() );
                }
            }
        }
    }

    protected void log( Quote quote )
    {
        if ( ISLogger.isRFSRateLogDebugEnabled() )
        {
            StringBuilder sb = new StringBuilder( 400 );
            String txnId = quote.getRequest().getTransactionID();
            sb.append( txnId );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getQuoteClassification().getShortName() );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getOrganization().getShortName() );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getGUID() );
            sb.append( LOG_DELIMITER );
            String ccyPair = "";

            StringBuffer priceB = new StringBuffer( 100 );
            Collection<FXLegDealingPrice> quotePrices = quote.getQuotePrices();
            for ( FXLegDealingPrice dp : quotePrices )
            {
                priceB.append( dp.getName() );
                priceB.append( LOG_DELIMITER );
                priceB.append( dp.getGUID() );
                priceB.append( LOG_DELIMITER );
                priceB.append( dp.getBidOfferMode() );
                priceB.append( LOG_DELIMITER );
                priceB.append( dp.getDealtAmount() );
                priceB.append( LOG_DELIMITER );
                priceB.append( dp.getDealtCurrency() );
                priceB.append( LOG_DELIMITER );
                FXPrice price = ( FXPrice ) dp.getPriceElement().getPrice();
                if ( price.getBidFXRate() != null )
                {
                    priceB.append( price.getBidFXRate().getSpotRate() );
                    priceB.append( LOG_DELIMITER );
                    priceB.append( price.getBidFXRate().getRate() );
                    priceB.append( LOG_DELIMITER );
                    priceB.append( price.getBidFXRate().getForwardPoints() );
                    priceB.append( LOG_DELIMITER );
                    ccyPair = price.getBidFXRate().getCurrencyPair().getName();
                }
                else
                {
                    priceB.append( LOG_DELIMITER );
                    priceB.append( LOG_DELIMITER );
                    priceB.append( LOG_DELIMITER );
                }
                if ( price.getOfferFXRate() != null )
                {
                    priceB.append( price.getOfferFXRate().getSpotRate() );
                    priceB.append( LOG_DELIMITER );
                    priceB.append( price.getOfferFXRate().getRate() );
                    priceB.append( LOG_DELIMITER );
                    priceB.append( price.getOfferFXRate().getForwardPoints() );
                    priceB.append( LOG_DELIMITER );
                    ccyPair = price.getOfferFXRate().getCurrencyPair().getName();
                }
                else
                {
                    priceB.append( LOG_DELIMITER );
                    priceB.append( LOG_DELIMITER );
                    priceB.append( LOG_DELIMITER );
                }
                if ( dp.getTenor() != null )
                {
                    priceB.append( dp.getTenor().getName() );
                }
                priceB.append( LOG_DELIMITER );
                if ( dp.getValueDate() != null )
                {
                    priceB.append( dp.getValueDate().toString() );
                }
                priceB.append( LOG_DELIMITER );
                if ( dp.getFixingTenor() != null )
                {
                    priceB.append( dp.getFixingTenor().toString() );
                }
                priceB.append( LOG_DELIMITER );
                if ( dp.getFixingDate() != null )
                {
                    priceB.append( dp.getFixingDate().toString() );
                }
                priceB.append( LOG_DELIMITER );
            }

            sb.append( ccyPair );
            sb.append( LOG_DELIMITER );
            sb.append( priceB.toString() );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getNotes() );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getUser() != null ? quote.getUser().getShortName() : null );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getRequest().getCounterparty().getLEI() == null ? quote.getRequest().getOrganization().getLEI() : quote.getRequest().getCounterparty().getLEI() );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getLegalEntity().getLEI() == null ? quote.getLegalEntity().getOrganization().getLEI() : quote.getLegalEntity().getLEI() );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getTakerPBLegalEntity() == null ? null : quote.getTakerPBLegalEntity().getShortName() );
            sb.append( LOG_DELIMITER );
            sb.append( quote.getMakerPBLegalEntity() == null ? null : quote.getMakerPBLegalEntity().getShortName() );
            ISLogger.logRFSRateByIS( sb.toString() );
        }
    }

    /**
     * publishes RFS Quotes to '<hostname><username>.RFS.Quotes' topic
     * and
     * to Chief Dealer
     */
    private class RFSQuotePublisher implements Runnable
    {
        Quote quote = null;

        public RFSQuotePublisher( Quote quote )
        {
            this.quote = quote;
        }

        public void run()
        {
            ISUtilImpl.getInstance().setSessionContext( quote.getRequest().getUser() );
            RFSWorkflowFacade rFSWorkflowFacade = ( RFSWorkflowFacade ) quote.getRequest().getFacade( RFSWorkflowFacade.NAME );
            try
            {
                if ( quote.getQuoteClassification().getShortName().equalsIgnoreCase( ISConstantsC.QUOTE_RFQ_CREATE_TYPE ) )
                {
                    boolean proceed = rFSWorkflowFacade.setRequestState( ISCommonConstants.REQUEST_STATE_QUOTED );
                    if ( !proceed )
                    {
                        log.warn( "RFSMessageListenerC.RFSQuotePublisher :: RATE DISCARDED. Request State Conflict for TXID = " + quote.getTransactionID() );
                        return;
                    }

                    if ( rFSWorkflowFacade.getLpQuoteState( quote.getOrganization() ).equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_PENDING ) )
                    {
                        rFSWorkflowFacade.setLpQuoteState( quote.getOrganization(), ISCommonConstants.QUOTE_STATE_ACTIVE, ISCommonConstants.QuoteReceivedAtServer, null );
                    }
                }

                WorkflowMessage wfMsg = MessageFactory.newWorkflowMessage();
                wfMsg.setEvent( ISConstantsC.MSG_EVT_CREATE );
                wfMsg.setTopic( ISConstantsC.MSG_TOPIC_QUOTE );
                wfMsg.setObject( quote );
                wfMsg.setParameterValue( ISConstantsC.QUOTE_TYPE, ISConstantsC.QUOTE_TYPE_RFS );

                MessageHandler handler = HandlerCacheC.getHandlerCache().getHandler( quote.getTransactionID() );
                wfMsg.setParameterValue( ISConstantsC.JMS_PROP_USERNAME, quote.getRequest().getUser().getFullName() );
                if( quote.getNotes() != null )
                	wfMsg.setParameterValue( ISCommonConstants.SETTLEMENT_INSTRUCTIONS, quote.getNotes() );
                long isSent = System.currentTimeMillis();
                handler.handle( wfMsg );
                if(quote.getRequest().getRequestAttributes().isPersistRFSQuotes() && !quote.getRequest().getRequestAttributes().isTransient()){
                    RFSMessageLogger.logMessage(quote,ISCommonConstants.QUOTE_STATE_ACTIVE);
                }
                log( quote );
                captureEvent( quote, ISConstantsC.EVENT_TIME_DISP_IS_SENT_RATE, isSent );
            }
            catch ( Exception e )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "RFSQuotePublisher.run :: Exception occurred while publishing Quote - LP=" + quote.getOrganization().getShortName() + " TXID=" + quote.getTransactionID() + ". IGNORE EXCEPTION.", e );
                }
                else
                {
                    String exClz = e.getClass().getSimpleName().replace( "Exception", "Ex");
                    log.warn( "RFSQuotePublisher.run :: error occurred while publishing Quote - LP=" + quote.getOrganization().getShortName() + " TXID=" + quote.getTransactionID() + ". ignore error." + e.getMessage() + ",exClz=" + exClz );
                }
            }
            finally
            {
                this.quote = null;
            }
        }
    }

    /**
     * Adaptor sends WithdrawQuote, when the provider is able to send the rates but withdraws after sometime
     */
    private class WithdrawQuote implements Runnable
    {
        Request request = null;
        Organization withdrawedOrg = null;
        String reason = null;

        public WithdrawQuote( Request request, RFSResponseMessage rfsResponseMessage )
        {
            this.request = request;
            this.withdrawedOrg = ISUtilImpl.getInstance().getOrg( rfsResponseMessage.getProvider() );
            this.reason = ISUtilImpl.getInstance().getFailureReason( rfsResponseMessage.getFailureReasons() );
        }

        public void run()
        {
            WorkflowMessage wfMessage = null;
            try
            {
                ISUtilImpl.getInstance().setSessionContext( request.getUser() );
                RFSWorkflowFacade rFSWorkflowFacade = ( RFSWorkflowFacade ) request.getFacade( RFSWorkflowFacade.NAME );
                ISRFSCreditWorkflowManagerC.getInstance().onUnsubscription( request, withdrawedOrg );
                ISMBean isMbean = ISFactory.getInstance().getISMBean();
                if (isMbean.refreshPriceProvisionData())
                {
                    TradingParty pbTp = request.getRequestAttributes().getPBTP( withdrawedOrg.getShortName() );
                    if ( pbTp != null )
                    {
                        Provider provider = ProviderManagerC.getInstance().getProvider( withdrawedOrg.getShortName() );
                        PriceProvisionInput ppi = PriceProvisionInputBuilder.buildInput( provider.getAdaptor().getOrganization(), withdrawedOrg, request.getUser().getOrganization(), pbTp.getLegalEntityOrganization(), request.getCurrencyPair(), request.getTransactionID() );
                        ServiceFactory.getPriceProvisionRulesService().unsubscribe( ppi );
                    }
                }

                if ( rFSWorkflowFacade.getLpQuoteState( withdrawedOrg ).equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_INIT ) ||
                        rFSWorkflowFacade.getLpQuoteState( withdrawedOrg ).equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_PENDING ) ||
                        rFSWorkflowFacade.getLpQuoteState( withdrawedOrg ).equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_ACTIVE ) )
                {
                    wfMessage = ISUtilImpl.getInstance().createWorkflowMessage( request, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_QUOTE );
                    wfMessage.setParameterValue( ISConstantsC.LP_ORG_SHORTNAME, withdrawedOrg.getShortName() );
                    wfMessage.setParameterValue( ISConstantsC.LP_ORG_NAMESPACE, withdrawedOrg.getNamespace().getPath() );
                    wfMessage.setParameterValue( ISConstantsC.TRANSACTION_ID_WFKEY, request.getTransactionID() );
                    wfMessage.setParameterValue( ISConstantsC.REQUEST_TYPE, ISConstantsC.REQUEST_TYPE_SUBSCRIPTION );
                    wfMessage.setParameterValue( ISConstantsC.WF_MSG_PARAM_ACCOUNT, request.getCounterparty().getShortName() );

                    ISUtilImpl.getInstance().addError( wfMessage, ISConstantsC.REQUEST_REJECTED_CODE, new Object[]{ISConstantsC.WF_SUBSCRIPTION, reason, request.getTransactionID()} );
                    wfMessage.setStatus( MessageStatus.SUCCESS );

                    rFSWorkflowFacade.setLpQuoteState( withdrawedOrg, ISCommonConstants.QUOTE_STATE_CANCELLED, ISCommonConstants.QuoteCancelledByProvider , null);

                    MessageHandler handler = HandlerCacheC.getHandlerCache().getHandler( request.getTransactionID() );
                    handler.handle( wfMessage );

                    if ( rFSWorkflowFacade.isAllLPQuotesInTerminalState() )
                    {
                        RequestExpireHandler.getInstance().expireRequest( request );
                    }
                }
                else
                {
                    log.warn( "RFSMessageListenerC.WithdrawQuote :: " + withdrawedOrg.getShortName() + " MSG DISCARDED. LpQuote already in Terminal State for TXID = " + request.getTransactionID() );
                }
            }
            catch ( Exception e )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "RFSMessageListenerC.WithdrawQuote :: Exception in handling Quote Withdraw for TXID = "
                            + request.getTransactionID() + " lpOrg = " + withdrawedOrg.getShortName() + " . IGNORE EXCEPTION.", e );
                }
                else
                {
                    log.warn( "RFSMessageListenerC.WithdrawQuote :: Exception in handling Quote Withdraw for TXID = "
                            + request.getTransactionID() + " lpOrg = " + withdrawedOrg.getShortName() + " . IGNORE EXCEPTION." + e.getMessage() );
                }
            }
            finally
            {
                this.request = null;
                this.withdrawedOrg = null;
                this.reason = null;
            }
        }
    }

    private class StatusMessage implements Runnable
    {
        Request request;
        Organization providerOrg;
        String status;
        public StatusMessage( final Request request, Organization providerOrg, String status )
        {
            this.request = request;
            this.providerOrg = providerOrg;
            this.status = status;
        }
        @Override
        public void run()
        {
            try
            {
                log.info("StatusMessage: " + status + ' ' + providerOrg + ' ' + request.getTransactionID());
                ISUtilImpl.getInstance().setSessionContext( request.getUser() );
                WorkflowMessage wfMessage = ISUtilImpl.getInstance().createWorkflowMessage( request, ISDomainConstants.RFS_STATUS, ISConstantsC.MSG_TOPIC_REQUEST );
                wfMessage.setParameterValue( ISConstantsC.LP_ORG_SHORTNAME, providerOrg.getShortName() );
                wfMessage.setParameterValue( ISConstantsC.LP_ORG_NAMESPACE, providerOrg.getNamespace().getPath() );
                wfMessage.setParameterValue( ISConstantsC.TRANSACTION_ID_WFKEY, request.getTransactionID() );
                wfMessage.setParameterValue( ISConstantsC.REQUEST_TYPE, ISConstantsC.REQUEST_TYPE_SUBSCRIPTION );
                wfMessage.setParameterValue( ISConstantsC.WF_MSG_PARAM_ACCOUNT, request.getCounterparty().getShortName() );
                wfMessage.setParameterValue( ISDomainConstants.RFS_STATUS, status );
                wfMessage.setStatus( MessageStatus.SUCCESS );
                MessageHandler handler = HandlerCacheC.getHandlerCache().getHandler( request.getTransactionID() );
                handler.handle( wfMessage );
                }catch (Exception e)
            {
                log.error("RFSMessageListenerC.StatusMessage :: Exception ", e);
            }
        }
    }

    /**
     * Adaptor sends DeclineQuote, when the provider is not able to send even a single rate and declines the request
     */
    private class DeclineQuote implements Runnable
    {
        Request request;
        Organization declinedOrg;
        String reason;

        public DeclineQuote( final Request request, RFSResponseMessage rfsResponseMessage )
        {
            this.request = request;
            this.declinedOrg = ISUtilImpl.getInstance().getOrg( rfsResponseMessage.getProvider() );
            this.reason = ISUtilImpl.getInstance().getFailureReason( rfsResponseMessage.getFailureReasons() );
        }

        public void run()
        {
            WorkflowMessage wfMessage;
            try
            {
                if ( isMBean.isIgnoreQuoteCancelOnRequestAcceptance () )
                {
                    synchronized ( request )
                    {
                        String stateName = request.getWorkflowStateMap () != null && request.getWorkflowStateMap ().getState () != null ? request.getWorkflowStateMap ().getState ().getShortName () : null;
                        RequestStateFacade requestStateFacade = ( RequestStateFacade ) request.getFacade( RequestStateFacade.REQUEST_STATE_FACADE );
                        if ( requestStateFacade.isAcceptInitiated () || requestStateFacade.isAccepted () || requestStateFacade.isAcceptVerified () )
                        {
                            log.info ( "RML.DeclineQuote.run : skipping decline workflow as the request is accepted/verified. request=" + request.getTransactionID () + ",state=" + stateName + ",declinedOrg=" + declinedOrg );
                            return;
                        }
                        else
                        {
                            log.info ( "RML.DeclineQuote.run : request=" + request.getTransactionID () + ",state=" + stateName + ",declinedOrg=" + declinedOrg );
                        }
                    }
                }

                ISUtilImpl.getInstance().setSessionContext( request.getUser() );
                RFSWorkflowFacade rFSWorkflowFacade = ( RFSWorkflowFacade ) request.getFacade( RFSWorkflowFacade.NAME );
                ISRFSCreditWorkflowManagerC.getInstance().onUnsubscription( request, declinedOrg );
                ISMBean isMbean = ISFactory.getInstance().getISMBean();
                if (isMbean.refreshPriceProvisionData())
                {
                    TradingParty pbTp = request.getRequestAttributes().getPBTP( declinedOrg.getShortName() );
                    if ( pbTp != null )
                    {
                        Provider provider = ProviderManagerC.getInstance().getProvider( declinedOrg.getShortName() );
                        PriceProvisionInput ppi = PriceProvisionInputBuilder.buildInput( provider.getAdaptor().getOrganization(), declinedOrg, request.getUser().getOrganization(), pbTp.getLegalEntityOrganization(), request.getCurrencyPair(), request.getTransactionID() );
                        ServiceFactory.getPriceProvisionRulesService().unsubscribe( ppi );
                    }
                }

                String quoteState = rFSWorkflowFacade.getLpQuoteState( declinedOrg );
                if ( quoteState.equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_INIT ) ||
                        quoteState.equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_PENDING ) ||
                        quoteState.equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_ACTIVE ) )
                {
                    wfMessage = ISUtilImpl.getInstance().createWorkflowMessage( request, ISConstantsC.MSG_EVT_REJECT, ISConstantsC.MSG_TOPIC_REQUEST );

                    wfMessage.setParameterValue( ISConstantsC.LP_ORG_SHORTNAME, declinedOrg.getShortName() );
                    wfMessage.setParameterValue( ISConstantsC.LP_ORG_NAMESPACE, declinedOrg.getNamespace().getPath() );
                    wfMessage.setParameterValue( ISConstantsC.TRANSACTION_ID_WFKEY, request.getTransactionID() );
                    wfMessage.setParameterValue( ISConstantsC.REQUEST_TYPE, ISConstantsC.REQUEST_TYPE_SUBSCRIPTION );
                    wfMessage.setParameterValue( ISConstantsC.WF_MSG_PARAM_ACCOUNT, request.getCounterparty().getShortName() );

                    ISUtilImpl.getInstance().addError( wfMessage, ISConstantsC.REQUEST_REJECTED_CODE, new Object[]{ISConstantsC.WF_SUBSCRIPTION, reason, request.getTransactionID()} );
                    wfMessage.setStatus( MessageStatus.SUCCESS );


                    MessageHandler handler = HandlerCacheC.getHandlerCache().getHandler( request.getTransactionID() );
                    handler.handle( wfMessage );

                    if ( rFSWorkflowFacade.isAllLPQuotesInTerminalState() )
                    {
                        RequestExpireHandler.getInstance().expireRequest( request );
                    }
                    String rejectReason = null;
                    if ( wfMessage.getErrors() != null )
                    {
                        StringBuffer sb = ISUtilImpl.getInstance().getErrorMessage( wfMessage.getErrors() );
                        rejectReason = sb.toString();
                    }

                    log.info( "RFSMessageListenerC.DeclineQuote : declined by " + declinedOrg.getShortName() + " TxId = " + request.getTransactionID() + ",reason=" + this.reason + ",rejectReason=" + rejectReason );

                    rFSWorkflowFacade.setLpQuoteState( declinedOrg, ISCommonConstants.QUOTE_STATE_DECLINED, ISCommonConstants.AdaptorOrLPRejectedRequest, rejectReason );
                    ISEmailUtil.getInstance().sendRFSPriceRequestRejectEmailToFIOrg( request,declinedOrg,rejectReason,true );
                }
                else
                {
                    log.warn( "RFSMessageListenerC.DeclineQuote :: " + declinedOrg.getShortName() + " MSG DISCARDED. LpQuote already in Terminal State for TXID = " + request.getTransactionID() + ",quoteState=" + quoteState );
                }
            }
            catch ( Exception e )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "RFSMessageListenerC.DeclineQuote :: Exception in handling Quote Decline for TXID = "
                            + request.getTransactionID() + " lpOrg = " + declinedOrg.getShortName() + " . IGNORE EXCEPTION.", e );
                }
                else
                {
                    log.warn( "RFSMessageListenerC.DeclineQuote :: Exception in handling Quote Decline for TXID = "
                            + request.getTransactionID() + " lpOrg = " + declinedOrg.getShortName() + " . IGNORE EXCEPTION." + e.getMessage() );
                }
            }
            finally
            {
                this.request = null;
                this.declinedOrg = null;
                this.reason = null;
            }
        }
    }

    /**
     * Adaptor sends ExpireQuote, when the request expires for a particular LP in adaptor
     */
    private class ExpireQuote implements Runnable
    {
        Request request = null;
        Organization expiredOrg = null;
        String reason = null;

        public ExpireQuote( Request request, RFSResponseMessage rfsResponseMessage )
        {
            this.request = request;
            this.expiredOrg = ISUtilImpl.getInstance().getOrg( rfsResponseMessage.getProvider() );
            this.reason = ISUtilImpl.getInstance().getFailureReason( rfsResponseMessage.getFailureReasons() );
        }

        public void run()
        {
            WorkflowMessage wfMessage = null;
            try
            {
                ISUtilImpl.getInstance().setSessionContext( request.getUser() );
                RFSWorkflowFacade rFSWorkflowFacade = ( RFSWorkflowFacade ) request.getFacade( RFSWorkflowFacade.NAME );
                ISRFSCreditWorkflowManagerC.getInstance().onUnsubscription( request, expiredOrg );
                ISMBean isMbean = ISFactory.getInstance().getISMBean();
                if (isMbean.refreshPriceProvisionData())
                {
                    TradingParty pbTp = request.getRequestAttributes().getPBTP( expiredOrg.getShortName() );
                    if ( pbTp != null )
                    {
                        Provider provider = ProviderManagerC.getInstance().getProvider( expiredOrg.getShortName() );
                        PriceProvisionInput ppi = PriceProvisionInputBuilder.buildInput( provider.getAdaptor().getOrganization(), expiredOrg, request.getUser().getOrganization(), pbTp.getLegalEntityOrganization(), request.getCurrencyPair(), request.getTransactionID() );
                        ServiceFactory.getPriceProvisionRulesService().unsubscribe( ppi );
                    }
                }
                if ( rFSWorkflowFacade.getLpQuoteState( expiredOrg ).equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_INIT ) ||
                        rFSWorkflowFacade.getLpQuoteState( expiredOrg ).equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_PENDING ) ||
                        rFSWorkflowFacade.getLpQuoteState( expiredOrg ).equalsIgnoreCase( ISCommonConstants.QUOTE_STATE_ACTIVE ) )
                {
                    wfMessage = ISUtilImpl.getInstance().createWorkflowMessage( request, ISConstantsC.MSG_EVT_EXPIRE, ISConstantsC.MSG_TOPIC_QUOTE );
                    wfMessage.setParameterValue( ISConstantsC.LP_ORG_SHORTNAME, expiredOrg.getShortName() );
                    wfMessage.setParameterValue( ISConstantsC.LP_ORG_NAMESPACE, expiredOrg.getNamespace().getPath() );
                    wfMessage.setParameterValue( ISConstantsC.TRANSACTION_ID_WFKEY, request.getTransactionID() );
                    wfMessage.setParameterValue( ISConstantsC.REQUEST_TYPE, ISConstantsC.REQUEST_TYPE_SUBSCRIPTION );
                    wfMessage.setParameterValue( ISConstantsC.WF_MSG_PARAM_ACCOUNT, request.getCounterparty().getShortName() );

                    ISUtilImpl.getInstance().addError( wfMessage, ISConstantsC.REQUEST_REJECTED_CODE, new Object[]{ISConstantsC.WF_SUBSCRIPTION, reason, request.getTransactionID()} );
                    wfMessage.setStatus( MessageStatus.SUCCESS );

                    rFSWorkflowFacade.setLpQuoteState( expiredOrg, ISCommonConstants.QUOTE_STATE_EXPIRE, ISCommonConstants.QuoteExpiredAtAdaptor, null );

                    MessageHandler handler = HandlerCacheC.getHandlerCache().getHandler( request.getTransactionID() );
                    handler.handle( wfMessage );

                    if ( rFSWorkflowFacade.isAllLPQuotesInTerminalState() )
                    {
                        RequestExpireHandler.getInstance().expireRequest( request );
                    }
                }
                else
                {
                    log.warn( "RFSMessageListenerC.ExpireQuote :: " + expiredOrg.getShortName() + " MSG DISCARDED. LpQuote already in Terminal State for TXID = " + request.getTransactionID() );
                }
            }
            catch ( Exception e )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "RFSMessageListenerC.ExpireQuote :: Exception in handling Quote Expire for TXID = "
                            + request.getTransactionID() + " lpOrg = " + expiredOrg.getShortName() + " . IGNORE EXCEPTION.", e );
                }
                else
                {
                    log.warn( "RFSMessageListenerC.ExpireQuote :: Exception in handling Quote Expire for TXID = "
                            + request.getTransactionID() + " lpOrg = " + expiredOrg.getShortName() + " . IGNORE EXCEPTION." + e.getMessage() );
                }
            }
            finally
            {
                this.request = null;
                this.expiredOrg = null;
                this.reason = null;
            }
        }
    }

    private void updateQuoteExternalIds( RFSTradeVerify tr, Quote quote )
    {
        if ( ( String ) tr.getProperty( ISConstantsC.MAKER_REQUEST_TRANSACTIONID ) != null )
        {
            quote.setExternalRequestId( ( String ) tr.getProperty( ISConstantsC.MAKER_REQUEST_TRANSACTIONID ) );
        }
        quote.setExternalTradeId( tr.getProviderTradeId() );
    }

    private String validateReason( String reason )
    {
    	Tuple<String[], String[]> rejReasonDelimiters = isMBean.getRejectionReasonDelimiters();
    	if( rejReasonDelimiters != null )
    	{
    		String[] delimiters = rejReasonDelimiters.first;
    		String[] replaceWith = rejReasonDelimiters.second;
    		if( delimiters != null )
    		{
    			for( int index = 0; index < delimiters.length; index++ )
    			{
    				reason = reason.replace (delimiters[index], replaceWith[index]);
    			}
    		}
    	}
        if ( reason.length() > 255 )
        {
            log.warn( "RFSMessageListenerC Reject reason has more than 255 characters. Truncating to 255 characters. Reason = " + reason );
            reason = reason.substring( 0, 255 );
        }
        return reason;
    }

    private void setMakerUser( RFSTradeVerify tr, Request request )
    {
        try
        {
            User user = null;
            Organization org;
            Object obj = tr.getProperty( "MakerUserObjID" );
            if ( obj != null )
            {
                long objId = ( Long ) obj;
                try
                {
                    user = ISUtilImpl.getInstance().getUser( objId );
                }
                catch ( Exception e )
                {
                    log.error( "RFSMessageListenerC: setMakerUser : Error in finding maker user using MakerUserObjID ", e );
                }
            }
            else
            {
                org = request.getAcceptedQuote().getOrganization();
                if ( tr.getProperty( "MakerUser" ) != null )
                {
                    String makerUser = ( String ) tr.getProperty( "MakerUser" );
                    try
                    {
                        user = org.getUser( makerUser );
                    }
                    catch ( Exception e )
                    {
                        log.error( "RFSMessageListenerC: setMakerUser : Error in finding maker user ", e );
                    }
                }
            }
            if ( user == null )
            {
                org = request.getAcceptedQuote().getOrganization();
                Provider lp = ( Provider ) ProviderManagerC.getInstance().getProvider( org.getShortName() );
                if ( lp != null )
                {
                    user = lp.getProviderConfig().getDefaultUser();
                }
            }
            if ( user != null )
            {
                request.getRequestAttributes().setMakerUser( user );
            }
        }
        catch ( Exception e )
        {
            log.warn( "RFSMessageListenerC.setMakerUser:Error while setting maker user:", e );
            if ( log.isDebugEnabled() )
            {
                e.printStackTrace();
            }
        }
    }

   private void setMakerDetails( RFSTradeVerify tr, Request request )
   {
   	// In case of resting orders RFSTradeVerify will have beow details from matched order for this trade verify
   	try
   	{
           Long objId = ( Long ) tr.getProperty( ISCommonConstants.MAKER_SD_CPTYID );
           if ( objId != null && objId != 0 )
           {
               LegalEntity makerSDCpty = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByObjectId( objId, LegalEntityC.class );
               if ( makerSDCpty != null )
               {
                   request.getTrade().setMakerSalesDealerCounterparty( makerSDCpty );
               }
           }
           Long makerSdobjId = ( Long ) tr.getProperty( ISCommonConstants.MAKER_SD_USERID );
           if ( makerSdobjId != null && makerSdobjId != 0 )
           {
               User makerSDUser = ISUtilImpl.getInstance().getUser( makerSdobjId );
               if ( makerSDUser != null )
               {
                   request.getTrade().setMakerSalesDealerUser( makerSDUser );
               }
           }
           String makerOrderID = (String)tr.getProperty( ISCommonConstants.MAKER_ORDER_ID );
           if( makerOrderID != null )
           {
               request.getTrade().setMakerOrderId( makerOrderID );
           }
           String makerOrderUserChannel = (String)tr.getProperty( ISCommonConstants.MAKER_ORDER_CHANNEL );
           if( makerOrderUserChannel != null )
           {
               request.getTrade().setMakerOrderUserChannel( makerOrderUserChannel );
           }
           String makerOrderDealtCcy = (String)tr.getProperty( ISCommonConstants.MAKER_ORDER_DEALT_CCY );
           if( makerOrderDealtCcy != null )
           {
               request.getTrade().setMakerOrderDealtCurrency( makerOrderDealtCcy );
           }
           String makerOrderMarketSnapshot = (String)tr.getProperty( ISCommonConstants.MAKER_ORDER_MARKET_SNAPSHOT );
           if( makerOrderMarketSnapshot != null )
           {
               request.getTrade().setMakerMarketSnapshot( makerOrderMarketSnapshot );
           }
           Double unfilledAmount = ( Double ) tr.getProperty( ISCommonConstants.ORDER_UNFILLED_AMT );
           if( unfilledAmount != null )
           {
               setUnfilledAmount( request, unfilledAmount );
           }
   	}
   	catch(Exception e)
   	{
           log.error( "RFSMessageListenerC: setMakerDetails : Error in setting maker order details ", e );
   	}
   }
   
    private void setUnfilledAmount( Request request, Double unfilledAmount )
    {
        FXLeg leg = SEFUtilC.getFxLeg( request.getTrade() );
        if( leg != null )
        {
            leg.setMakerUnfilledAmount( unfilledAmount );
        }
    }

    private String getRejectionReason( RFSTradeReject rfsTradeReject )
    {
        String rejReason = rfsTradeReject.getRejectReason();
        if ( rejReason == null || rejReason.trim().equals( "" ) )
        {
            log.warn( "RFSMessageListenerC.handleRFSTradeReject : Rejection reason for tradeId " + rfsTradeReject.getTradeId() + " is null. Substituting it with default reason." );
            rejReason = ISConstantsC.TRADE_REJECT_DEFAULT_REJECT_REASON;
        }
        else
        {
            rejReason = validateReason( rejReason );
        }
        return rejReason;
    }

    private void addToTradeCache( Trade trade )
    {
        ISTradeCache.getInstance().add( trade );
    }

    private void verifyFixingDate( Request request, RFSTradeVerify rfsTrdVerify )
    {
        try
        {        	
        	if(request.getRequestAttributes().isNDF()){
	        	Date providerFixingDate = RfsUtils.getInstance().getProviderFixingDate(rfsTrdVerify, ISConstantsC.SINGLE_LEG);
	        	if( providerFixingDate == null ){
	        		return;
	        	}
	            FXLegDealingPrice reqDp = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.SINGLE_LEG);
	            validateFixingDateMatch(request, providerFixingDate, reqDp);
        	}
        	if(request.getRequestAttributes().isNDFSwap())
        	{
	        	Date providerFixingDateNear = RfsUtils.getInstance().getProviderFixingDate(rfsTrdVerify, ISConstantsC.NEAR_LEG);
	        	if(providerFixingDateNear == null){
	        		return;
	        	}
	            FXLegDealingPrice reqDpNear = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.NEAR_LEG);
	            validateFixingDateMatch(request, providerFixingDateNear, reqDpNear);
	            
	        	Date providerFixingDateFar = RfsUtils.getInstance().getProviderFixingDate(rfsTrdVerify, ISConstantsC.FAR_LEG);
	        	if(providerFixingDateFar == null ){
	        		return;
	        	}
	            FXLegDealingPrice reqDpFar = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.FAR_LEG);
	            validateFixingDateMatch(request, providerFixingDateFar, reqDpFar);
        	}
        }
        catch ( Exception ex )
        {
            log.error( "RFSMessageListenerC.verifyFixingDate : Exception ", ex );
        }
    }

	private void validateFixingDateMatch(Request request, Date providerFixingDate, FXLegDealingPrice reqDp) 
	{
		CurrencyPair ccyPair = ISUtilImpl.getInstance().getCurrencyPair( request );
		Date systemFixingDate = reqDp.getFixingDate().asJdkDate();
		Organization toOrg = request.getOrganization();
		if ( !( providerFixingDate.compareTo( systemFixingDate ) == 0 ) )
		{
		    if ( !RuntimeFactory.getServerRuntimeMBean().isServerWarmingUp() )
		    {
		    	StringBuilder sb = new StringBuilder( 200 );
		    	sb.append("RFSMessageListenerC.verifyFixingDate : Trade FixingDate Mismatch for [").append(request.getTrade().getTransactionID())
		    	.append(",System FixingDate is ").append(systemFixingDate).append(" , Provider's FixingDate is ").append(providerFixingDate)
		    	.append("] for currency pair " ).append(ccyPair.getName())
		    	.append( " for Provider " )
		    	.append( toOrg.getShortName() );
		    	log.warn(sb.toString());
                AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.RFS_FIXING_DATE_MISMATCH, this.getClass().getName(), sb.toString(), null);
		    }
		}
	}
}
