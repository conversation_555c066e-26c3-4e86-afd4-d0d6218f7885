package com.integral.is.common.comm;

import java.util.concurrent.Executor;

import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.Provider;
import com.integral.is.common.exception.MessageCommunicationExceptionC;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.is.common.pool.ThreadPoolMBeanC;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.*;
import com.integral.is.message.rfs.RFSTradePending;
import com.integral.is.message.rfs.RFSTradeReject;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.is.spaces.fx.esp.builder.AdaptorMessageBuilder;
import com.integral.is.spaces.fx.esp.builder.AdaptorMessageBuilderFactory;
import com.integral.is.spaces.fx.handler.AdaptorResponseHandler;
import com.integral.is.spaces.fx.listener.SpacesTradeListener;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.user.User;

public class TradeRequestDispatcher {
	private static boolean singleThreaded = false;

	Executor executor = ThreadPoolFactory.getInstance()
			.getTradeProcessorThreadPool();
	private static final TradeRequestDispatcher dispatcher = new TradeRequestDispatcher();
	private static final String REJECT_REASON_PREFIX = "Trade request rejected: ";
	private final static Log log = LogFactory
			.getLog(TradeRequestDispatcher.class);

	private TradeRequestDispatcher() {
	}

	public static TradeRequestDispatcher getInstance() {
		return dispatcher;
	}

	public void setSingleThread(boolean flag) {
		singleThreaded = flag;
	}

	public ResponseMessage dispatch(Provider provider, ISMessage message,
			User user) {
		ResponseMessage responseMessage = MessageFactory.newResponseMessage();
		responseMessage.setStatus(ResponseMessage.SUCCESS);

		if (ThreadPoolMBeanC.getInstance().isTradeProcessorThreadPoolDisabled()) {
			// no executor; setup a listener on the IMTP message
			try {
				sendMessageToProvider(message, provider, user);
			} catch (Exception e) {
				StringBuilder logErrorMessage = new StringBuilder(
						"TradeRequestDispatcher.Worker.run:: Error in accepting quote For TxnId [");
				if (message instanceof TradeRequest) {
					logErrorMessage.append(((TradeRequest) message)
							.getTradeId());
				} else if (message instanceof RFSTradeRequest) {
					logErrorMessage.append(((RFSTradeRequest) message)
							.getRequestId());
				}
				logErrorMessage.append(".Trade message -")
						.append(message.toString()).append(". Exception - ");
				log.warn(logErrorMessage.toString() + e, e);
				MessageLogger.getInstance().log(
						"AcceptQuote",
						this.getClass().getName(),
						"Exception while sending acceptance message-Error"
								+ e.getMessage(), message.toString());
				// Don't send warmup message rejections asynchronously.
				if (!message.isWarmupMessage()) {
					sendTradeReject(provider, message,
							"Message communication failure", true, true);
				}
			}
		} else {
			if (singleThreaded)
				new Worker(message, provider, user).run();
			else
				executor.execute(new Worker(message, provider, user));
		}
		return responseMessage;
	}

	public static void sendTradeReject(Provider provider,
									   ISMessage requestMessage, String reason, boolean isSynchronous, boolean internalReject) {
		try {
			if (requestMessage instanceof TradeRequest) {

				AdaptorMessageBuilder builder = AdaptorMessageBuilderFactory
						.getAdaptorMessageBuilder(provider.getAdaptor()
								.getAdaptorType());
				final TradeResponse response = builder.getTradeRejected(
						provider, requestMessage, reason);
				// Set the response received time in the response object.
				if (response instanceof BrokerOrderResponse) {
					BrokerOrderResponse bor = (BrokerOrderResponse) response;
					bor.setInternalRejection(true);
					bor.setResponseReceivedByAppTime(System.currentTimeMillis());
				} else {
					if( response instanceof TradeReject ){
						((TradeReject)response).setInternalRejection(internalReject);
					}
					response.getTiming()
							.setTime(
									AdaptorResponseHandler.EVENT_TIME_RESPONSE_RECEIVED_BY_APP,
									System.currentTimeMillis());
				}

				if (isSynchronous) {
					SpacesTradeListener.getHandler(
							response.getClass().getName())
							.handleResponse(response);
				} else {
					String tradeId = ((TradeRequest) requestMessage)
							.getTradeId();

					Runnable t = new Thread() {

						@Override
						public void run() {
							SpacesTradeListener.getHandler(
									response.getClass().getName())
									.handleResponse(response);
						}
					};
					ThreadPoolFactory.getInstance().getSecondaryThreadPool().execute(t);
				}

			} else if (requestMessage instanceof RFSTradeRequest) {
				final RFSTradeReject rfsTradeReject = MessageFactory
						.newRFSTradeReject();
				RFSTradeRequest rfsTradeRequest = (RFSTradeRequest) requestMessage;
				rfsTradeReject.setServerId(requestMessage.getServerId());
				rfsTradeReject.setFailureReason(REJECT_REASON_PREFIX + reason);
				rfsTradeReject.setRejectReason(REJECT_REASON_PREFIX + reason);
				rfsTradeReject.setStatus(TradeResponse.REJECTED);
				rfsTradeReject.setServerId(requestMessage.getServerId());
				rfsTradeReject.setBaseCurrency(rfsTradeRequest
						.getBaseCurrency());
				rfsTradeReject.setVariableCurrency(rfsTradeRequest
						.getVariableCurrency());
				rfsTradeReject.setProvider(provider.getName());
				rfsTradeReject.setTradeId(rfsTradeRequest.getRequestId());
				if (rfsTradeRequest.getRequestId().endsWith("C")) {
					rfsTradeReject.setTradeId(rfsTradeRequest.getRequestId()
							.substring(
									0,
									rfsTradeRequest.getRequestId().lastIndexOf(
											'C')));
				} else {
					rfsTradeReject.setTradeId(rfsTradeRequest.getRequestId());
				}
				rfsTradeReject.setRequestReferenceId(rfsTradeRequest
						.getRequestId());
				if (requestMessage
						.getProperty(ISConstantsC.isPrimeBrokerCoverTradeEnabled) != null) {
					rfsTradeReject.setProperty(ISConstantsC.ProviderName,
							((RFSTradeRequest) requestMessage)
									.getProviderShortName());
				}

				Runnable t = new Thread() {

					@Override
					public void run() {
						new RFSMessageListenerC().handleRFSTradeResponse(
								rfsTradeReject, System.currentTimeMillis());
					}
				};

                ThreadPoolFactory.getInstance().getSecondaryThreadPool().execute(t);
			}
		} catch (Exception e) {
			StringBuilder logErrorMessage = new StringBuilder(
					"TradeRequestDispatcher.sendTradeReject:: Error in accepting quote For TxnId [");

			log.error(logErrorMessage .toString() + e, e);
			MessageLogger.getInstance().log(ISAlertMBean.DO_ALERT_EVENT_EXCEPTION_PROCESSING_RESPONSE, "DOVH", logErrorMessage+e.getMessage(), null);

		}
	}

	public static void sendTradePending(Provider provider,
			ISMessage requestMessage, String reason) {
		try {
			if (requestMessage instanceof TradeRequest) {
				AdaptorMessageBuilder builder = AdaptorMessageBuilderFactory
						.getAdaptorMessageBuilder(provider.getAdaptor()
								.getAdaptorType());
				TradeResponse response = builder.getTradePending(provider,
						requestMessage, reason);

				// Set the response received time in the response object.
				if (response instanceof BrokerOrderResponse) {
					BrokerOrderResponse bor = (BrokerOrderResponse) response;
					bor.setResponseReceivedByAppTime(System.currentTimeMillis());
				} else {
					response.getTiming()
							.setTime(
									AdaptorResponseHandler.EVENT_TIME_RESPONSE_RECEIVED_BY_APP,
									System.currentTimeMillis());
				}
				SpacesTradeListener.getHandler(response.getClass().getName())
						.handleResponse(response);
			} else if (requestMessage instanceof RFSTradeRequest) {
				RFSTradePending rfsTrdPending = MessageFactory
						.newRFSTradePending();
				RFSTradeRequest rfsTradeRequest = (RFSTradeRequest) requestMessage;
				rfsTrdPending.setServerId(requestMessage.getServerId());
				rfsTrdPending.setFailureReason(REJECT_REASON_PREFIX + reason);
				rfsTrdPending.setStatus(TradeResponse.REJECTED);
				rfsTrdPending.setServerId(requestMessage.getServerId());
				rfsTrdPending
						.setBaseCurrency(rfsTradeRequest.getBaseCurrency());
				rfsTrdPending.setVariableCurrency(rfsTradeRequest
						.getVariableCurrency());
				rfsTrdPending.setProvider(provider.getName());
				rfsTrdPending.setTradeId(rfsTradeRequest.getRequestId());
				if (rfsTradeRequest.getRequestId().endsWith("C")) {
					rfsTrdPending.setTradeId(rfsTradeRequest.getRequestId()
							.substring(
									0,
									rfsTradeRequest.getRequestId().lastIndexOf(
											'C')));
				} else {
					rfsTrdPending.setTradeId(rfsTradeRequest.getRequestId());
				}
				rfsTrdPending.setRequestReferenceId(rfsTradeRequest
						.getRequestId());
				if (requestMessage
						.getProperty(ISConstantsC.isPrimeBrokerCoverTradeEnabled) != null) {
					rfsTrdPending.setProperty(ISConstantsC.ProviderName,
							((RFSTradeRequest) requestMessage)
									.getProviderShortName());
				}
				new RFSMessageListenerC().handleRFSTradeResponse(rfsTrdPending,
						System.currentTimeMillis());
			}
		} catch (Exception e) {
			log.error(
					"TradeRequestDispatcher.sendTradePending: Exception while handling trade message and exception is : "
							+ e, e);
		}
	}

	public static ResponseMessage sendMessageToProvider(ISMessage message,
			Provider provider, User user) throws MessageCommunicationExceptionC {
		IdcSessionContext ctx = IdcSessionManager.getInstance()
				.getSessionContext(user);
		IdcSessionManager.getInstance().setSessionContext(ctx);

		return (ResponseMessage) provider.sendMessage(message);
	}

	public static void processResponse(ResponseMessage response,
			ISMessage message, String providerName) {
		Provider provider = ProviderManagerC.getInstance().getProvider(
				providerName);
		String tradeId = null;
		if (message instanceof TradeRequest) {
			tradeId = ((TradeRequest) message).getTradeId();
		} else if (message instanceof RFSTradeRequest) {
			tradeId = ((RFSTradeRequest) message).getRequestId();
		}
		try {
			if (response.getStatus() == ResponseMessage.TIMEOUT) {
				StringBuilder logWarnMessage = new StringBuilder(
						"TradeRequestDispatcher.Worker.run:Timed out while sending request to provider when accepting quote For TxnId [");
				logWarnMessage.append(tradeId);
				logWarnMessage.append(".Trade message -").append(
						message.toString());
				log.warn(logWarnMessage.toString());
				if (!response.getFailureReasons().isEmpty()) {
					sendTradePending(provider, message, response
							.getFailureReasons().get(0).toString());
				} else {
					sendTradePending(provider, message,
							ResponseMessage.REQUEST_TIMEOUT);
				}
			} else if (response.getStatus() == ResponseMessage.FAILURE) {
				StringBuilder logErrorMessage = new StringBuilder(
						"TradeRequestDispatcher.Worker.run:FAILURE response from provider when accepting quote For TxnId [");

				logErrorMessage.append(tradeId);
				logErrorMessage.append(".Trade message -").append(
						message.toString());
				log.warn(logErrorMessage.toString());
				if (!response.getFailureReasons().isEmpty()) {
					sendTradeReject(provider, message, response
							.getFailureReasons().get(0).toString(), false, true);
				} else {
					sendTradeReject(provider, message,
							"Message communication failure", false, true);
				}
			}
		} catch (Exception e) {
			StringBuilder logErrorMessage = new StringBuilder(
					"TradeRequestDispatcher: Error in accepting quote For TxnId [");
			logErrorMessage.append(tradeId);
			logErrorMessage.append(".Trade message -")
					.append(message.toString()).append(". Exception - ");
			log.warn(logErrorMessage.toString() + e, e);
			MessageLogger.getInstance().log(
					"AcceptQuote",
					TradeRequestDispatcher.class.getName(),
					"Exception while sending acceptance message-Error"
							+ e.getMessage(), message.toString());
			// Don't send warmup message rejections asynchronously.
			if (!message.isWarmupMessage())
				sendTradeReject(provider, message,
						"Message communication failure", true, true);
		}
	}

	private class Worker implements Runnable {

		ISMessage message = null;
		Provider provider = null;
		User user = null;

		public Worker(ISMessage message, Provider provider, User user) {
			this.message = message;
			this.provider = provider;
			this.user = user;
		}

		public void run() {
			ResponseMessage response = null;
			try {
				response = sendMessageToProvider(message, provider, user);
			} catch (Exception e) {
				StringBuilder logErrorMessage = new StringBuilder(
						"TradeRequestDispatcher.Worker.run:: Error in accepting quote For TxnId [");
				if (message instanceof TradeRequest) {
					logErrorMessage.append(((TradeRequest) message)
							.getTradeId());
				} else if (message instanceof RFSTradeRequest) {
					logErrorMessage.append(((RFSTradeRequest) message)
							.getRequestId());
				}
				logErrorMessage.append(".Trade message -")
						.append(message.toString()).append(". Exception - ");
				log.warn(logErrorMessage.toString() + e, e);
				MessageLogger.getInstance().log(
						"AcceptQuote",
						this.getClass().getName(),
						"Exception while sending acceptance message-Error"
								+ e.getMessage(), message.toString());
				// Don't send warmup message rejections asynchronously.
				if (!message.isWarmupMessage())
					sendTradeReject(provider, message,
							"Message communication failure", true, true);
			}
			if (response != null) {
				processResponse(response, message, provider.getName());
			}
		}
	}
}
