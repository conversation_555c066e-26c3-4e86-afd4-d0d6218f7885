package com.integral.is.common.comm;

import java.lang.reflect.Method;

import com.integral.imtp.future.IMTPMessageFutureC;
import com.integral.imtp.message.ApplicationMessageC;
import com.integral.imtp.message.IMTPMessage;
import com.integral.imtp.message.IMTPMessageListener;
import com.integral.is.common.Provider;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.BrokerOrderRequest;
import com.integral.is.message.ISMessage;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.TradeRequest;
import com.integral.is.message.TradeRequestC;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.is.message.rfs.RFSTradeRequestC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.serialization.ISMessageSerializerUtil;

public class IMTPTradeMessageListener implements IMTPMessageListener {

	private static final Log log = LogFactory
			.getLog(IMTPTradeMessageListener.class);

	private static IMTPTradeMessageListener instance = new IMTPTradeMessageListener();

	public static IMTPTradeMessageListener getInstance() {
		return instance;
	}

	private IMTPTradeMessageListener() {

	}

	@Override
	public void notifyIMTPMessageEvent(IMTPMessage message,
			IMTPMessageEvent event) {

		ApplicationMessageC appMesg = (ApplicationMessageC) message;
		String provider = appMesg.getProperties().get(
				BaseMessageSender.PROVIDER_NAME);
		try {
			ResponseMessage responseMessage;
			IMTPMessageFutureC futureMessage = (IMTPMessageFutureC) event.payload;
			String responseStr = (String) ((ApplicationMessageC) futureMessage
					.getResponse()).getApplicationData();
			log.info("Received IMTP Message event response for sent message:"
					+ appMesg.getApplicationData() + ":status=" + event.type
					+ ":responseStr:" + responseStr);
			if (responseStr.isEmpty()) {
				log.error("sendMessage : No response message received from adaptor for the sent message:"
						+ appMesg.getApplicationData());
				return;
			} else {
				responseMessage = getReturnMessage(responseStr);
			}
			if (responseMessage.getStatus() == ResponseMessage.TIMEOUT
					|| responseMessage.getStatus() == ResponseMessage.FAILURE) {
				ISMessage messageSent = deserializeSentMessage(appMesg);
				if (messageSent == null) {
					throw new Exception(
							"Error in deserializing the sent message");
				}
				String tradeId = null;
				if (messageSent instanceof TradeRequest) {
					tradeId = ((TradeRequest) messageSent).getTradeId();
				} else if (messageSent instanceof RFSTradeRequest) {
					tradeId = ((RFSTradeRequest) messageSent).getRequestId();
				}
				log.info("Received IMTP Message as a Timeout/Failure response for:"
						+ tradeId);

				// process the timeout or failure message
				TradeRequestDispatcher.processResponse(responseMessage,
						messageSent, provider);
			}

		} catch (Exception e) {
			ISMessage messageSent = deserializeSentMessage(appMesg);
			StringBuilder logErrorMessage = new StringBuilder(
					"IMTPTradeMessageListener: Error in processing response:");
			logErrorMessage.append(":Trade message:")
					.append(message.toString()).append(":Exception:");
			log.warn(logErrorMessage.toString() + e, e);
			MessageLogger.getInstance().log(
					"AcceptQuote",
					TradeRequestDispatcher.class.getName(),
					"Exception while sending acceptance message-Error"
							+ e.getMessage(), message.toString());
			if (messageSent != null) {
				String tradeId = null;
				if (messageSent instanceof TradeRequest) {
					tradeId = ((TradeRequest) messageSent).getTradeId();
				} else if (messageSent instanceof RFSTradeRequest) {
					tradeId = ((RFSTradeRequest) messageSent).getRequestId();
				}
				log.warn(logErrorMessage.toString() + ":Trade Id:" + tradeId);
				// Don't send warmup message rejections asynchronously.
				if (!messageSent.isWarmupMessage()) {
					Provider prov = ProviderManagerC.getInstance().getProvider(
							provider);
					TradeRequestDispatcher.sendTradeReject(prov, messageSent,
							"Message communication failure", true, true);
				}
			}
		}
	}

	private ISMessage deserializeSentMessage(ApplicationMessageC appMesg) {
		ISMessage messageSent;
		// message that was sent
		String mesgSentStr = (String) appMesg.getApplicationData();

		String mesgSentType = appMesg.getProperties().get(
				BaseMessageSender.OBJECT_TYPE);
		if (mesgSentType.equals(TradeRequestC.class.getName())) {
			messageSent = MessageFactory.newTradeRequest();
		} else if (mesgSentType.equals(RFSTradeRequestC.class.getName())) {
			messageSent = MessageFactory.newRFSTradeRequest();
		} else if (mesgSentType.equals(BrokerOrderRequest.class.getName())) {
			messageSent = MessageFactory.newBrokerOrderRequest();
		} else {
			return null;
		}

		try {
			ISMessageSerializerUtil.deserialize(messageSent, mesgSentStr);
		} catch (Exception e) {
			log.error("Error in deserializing the sent message:", e);
			return null;
		}

		return messageSent;
	}

	private ResponseMessage getReturnMessage(String responseStr)
			throws Exception {
		String objType = "";
		int idx = 0;
		// responseString is like OBJECT_TYPE|OBJECT_STRING
		idx = responseStr.indexOf(BaseMessageSender.PIPE);
		if (idx == -1) {
			throw new Exception("responseString is invalid");
		}
		objType = responseStr.substring(0, idx);
		if (log.isDebugEnabled()) {
			log.debug("getReturnMessage : objType is " + objType);
		}
		String objStr = responseStr.substring(idx + 1);
		Object obj = Class.forName(objType).newInstance();
		Class[] arr = new Class[] { String.class };
		try {
			Method method = obj.getClass().getMethod("populateObject", arr);
			method.invoke(obj, (java.lang.Object[]) new String[] { objStr });
		} catch (NoSuchMethodException nsme) {
			log.error("getReturnMessage : Method populateObject not found for class "
					+ obj.getClass().getName() + nsme);
			throw new Exception();
		} catch (Exception ex) {
			log.error("getReturnMessage failed with Exception", ex);
		}
		ResponseMessage resp = (ResponseMessage) obj;
		return resp;
	}
}
