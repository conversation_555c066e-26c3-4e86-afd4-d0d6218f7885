package com.integral.is.common.cache.bucket;

import com.integral.finance.dealing.Quote;
import com.integral.jmsx.proxy.FIFOMessage;

import javax.jms.JMSException;

import java.util.Map;

// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * MessageNotifier gets callback whenever new rate has come.
 * <AUTHOR> Development Corp.
 */
public interface MessageNotifier {


    /**
     * Message notifier is notified with a new rate using this method. The quote object passed in this method is pooled
     * and quaranteed to be avaialble from the pool for the duration of the method. If the implementation requires to
     * cache the quote object for future use, then it should increment the quote reference and handle the quote lifecycle
     * on its own. Please refer to Quote.incReference(ReferenceHolder) for more information.
     *
     * Best Practices:
     *
     * In case of a thread handover for your task, use following model:
     *  try {
     *      quote.incReference(reference);
     *      try {
     *          new Task(quote);
     *          task.start();
     *      }finally {
     *          quote.decReference(reference);
     *      }
     * }catch ( IllegalPooledObjectStateException ex) {
     *
     * }
     *
     * class Task implements Runnable {
     * private Quote quote;
     *     Task(Quote quote) throws IllegalPooledObjectStateException {
     *          quote.incReference(reference);
     *          this.quote = quote;
     *     }
     *
     *     public void run() {
     *         try{
     *             //Do business operation on quote.
     *         } finally {
     *             quote.decReference(reference);
     *         }
     *     }
     * }
     *
     * @param quote
     * @param properties
     */
    void sendRate(Quote quote, Map properties);

    /**
     * Used for comparison between two instances. If toString() is same for
     * two instances then only one will be invoked.
     */
    String toString();

    MessageNotifierType getType();
}
