package com.integral.is.common.mbean;

import com.integral.categorization.StreamCategory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.trade.Tenor;
import com.integral.lp.QuoteAmountRoundingMode;
import com.integral.model.dealing.OrderRequest;
import com.integral.system.configuration.IdcMBean;
import com.integral.system.server.VirtualServer;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.Tuple;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.

/**
 * Class java doc
 *
 * <AUTHOR> Development Corp.
 */

public interface ISMBean extends IdcMBean
{
    /**
     * Broker name
     */
    String IDC_IS_SDK_BROKER = "IDC.IS.SDK.BROKER";

    /**
     * Secure Broker
     */
    String IDC_IS_SDK_SECURE_BROKER = "IDC.IS.SDK.SECURE.BROKER";

    /**
     * Timetolive for QT messages
     */
    String IDC_IS_SDK_TIMETOLIVE_QT = "IDC.IS.SDK.TIMETOLIVE.QT";
    /**
     * Timetolive for RFQ messages
     */
    String IDC_IS_SDK_TIMETOLIVE_RFQ = "IDC.IS.SDK.TIMETOLIVE.RFQ";

    /**
     * Log4J XML property
     */
    String IDC_IS_LOGF4J_CATEGORY = "IDC.IS.LOGF4J.CATEGORY";
    String IDC_IS_SDK_ZIP_ENABLE = "IDC.IS.SDK.ZIP.ENABLE";
    String IDC_IS_MESSAGE_ENCODING_ENABLE = "IDC.IS.MESSAGE.ENCODING.ENABLE";
    String IDC_IS_SDK_ZIP_ALGO = "IDC.IS.SDK.ZIP.ALGO";
    String IDC_IS_SDK_AUDIT_TYPE = "IDC.IS.SDK.AUDIT.TYPE";
    String IDC_IS_HEARTBEAT_INTERVAL = "IDC.IS.PROVIDER.HEARTBEAT.INTERVAL";
    String IDC_IS_IS_SYSTEM_USER = "IDC.IS.PROVIDER.ISSYSTEMUSER";
    String IDC_IS_EXTERNALSYSTEM = "IDC.IS.ExternalSystem";
    String IDC_IS_TEMPLATE_QUOTE_ESP = "IDC.IS.TEMPLATE.QUOTE.ESP";

    String IDC_IS_TEMPLATE_QUOTE_SPOTRFS = "IDC.IS.TEMPLATE.QUOTE.SPOTRFS";
    String IDC_IS_TEMPLATE_QUOTE_FWDRFS = "IDC.IS.TEMPLATE.QUOTE.FWDRFS";
    String IDC_IS_TEMPLATE_QUOTE_SWAPRFS = "IDC.IS.TEMPLATE.QUOTE.SWAPRFS";
    String IDC_IS_TEMPLATE_QUOTE_FFRFS = "IDC.IS.TEMPLATE.QUOTE.FFRFS";


    String IDC_IS_TEMPLATE_QUOTE_SPOTRFS_WITH_MID = "IDC.IS.TEMPLATE.QUOTE.SPOTRFS.WITH.MID";
    String IDC_IS_TEMPLATE_QUOTE_FWDRFS_WITH_MID = "IDC.IS.TEMPLATE.QUOTE.FWDRFS.WITH.MID";
    String IDC_IS_TEMPLATE_QUOTE_SWAPRFS_WITH_MID = "IDC.IS.TEMPLATE.QUOTE.SWAPRFS.WITH.MID";
    String IDC_IS_TEMPLATE_QUOTE_FFRFS_WITH_MID = "IDC.IS.TEMPLATE.QUOTE.FFRFS.WITH.MID";


    String IDC_IS_TEMPLATE_QUOTE_FXIESP = "IDC.IS.TEMPLATE.QUOTE.FXIESP";
    String IDC_IS_TEMPLATE_TRADE_FXIESP = "IDC.IS.TEMPLATE.TRADE.FXIESP";
    String IDC_IS_TEMPLATE_RESPONSE_ESP = "IDC.IS.TEMPLATE.RESPONSE.ESP";
    String IDC_IS_TEMPLATE_CSDK_RESPONSE_ESP = "IDC.IS.TEMPLATE.CSDK.RESPONSE.ESP";
    // done Trades Query response compaction

    String IDC_IS_TEMPLATE_QUERY_TRADE_RESPONSE_ESP_1 = "IDC.IS.TEMPLATE.QUERY.TRADE.RESPONSE.ESP_1";
    String IDC_IS_TEMPLATE_NOTIFICATION_MSG = "IDC.IS.TEMPLATE.NOTIFICATION.MSG";
    String IDC_IS_INCREMENTAL_DATA = "IDC.IS.INCREMENTAL.DATA";
    String IDC_IS_TRANSACTIONID_EXTERNALSYSTEM = "IDC.IS.TransactionID.ExternalSystem";
    String IDC_IS_INSTALLATION_SPREAD_LIMIT_VALIDATION = "IDC.IS.INSTALLATION.SPREAD.LIMIT.VALIDATION";
    String IDC_IS_CLIENT_MSG_LOGCATEGORY = "IDC.IS.CLIENT.MSG.LOGCATEGORY";
    String IDC_IS_MONITORING_ENABLED = "IDC.IS.MONITORING.ENABLED";
    String IDC_IS_MONITORING_PROVIDER_STATUS_INTERVAL = "IDC.IS.MONITORING.PROVIDER.STATUS.INTERVAL";
    String IDC_IS_MONITORING_USER_STATUS_INTERVAL = "IDC.IS.MONITORING.USER.STATUS.INTERVAL";
    String IDC_IS_EXPIRATION_SYSTEM_NO_OF_CONTAINER = "IDC.IS.EXPIRATION.SYSTEM.NO_OF_CONTAINER";
    String IDC_IS_EXPIRATION_SYSTEM_ROTATION_TIME = "IDC.IS.EXPIRATION.SYSTEM.ROTATION_TIME";
    String IDC_IS_MONITORING_CCYPAIRS_FOR_RATE_STATUS = "IDC.IS.MONITORING.CCYPAIRS.FOR.RATE.STATUS";
    String IDC_IS_MONITORING_STALE_RATES_THRESHOLD = "IDC.IS.MONITORING.STALE.RATES.THRESHOLD";
    String IDC_IS_DEFAULT_MDSNAME = "IDC.IS.DEFAULT.MDSNAME";
    String IDC_IS_CLIENT_MSG_ORPHANTRADELOGCATEGORY = "IDC.IS.CLIENT.MSG.ORPHANTRADELOGCATEGORY";
    String IDC_IS_VERIFICATIONTIMEOUT_INTERVAL = "IDC.IS.VERIFICATIONTIMEOUT.INTERVAL";
    String IDC_IS_VERIFICATIONTIMEOUT_ENABLED = "IDC.IS.VERIFICATIONTIMEOUT.ENABLED";
    String IDC_IS_SERVER_ID = "IDC.IS.SERVER.ID";
    String IDC_IS_NON_USD_CCYS = "IDC.IS.NON.USD.CCYS";
    String IDC_IS_CCYPAIR_LIST = "IDC.IS.CCYPAIR.LIST";
    String IDC_IS_AUTO_CANCEL_ENABLED_AT_ADAPTOR = "IDC.IS.AUTO.CANCEL.ENABLED.AT.ADAPTOR";
    String IDC_IS_MONITORING_IS_STATUS_INTERVAL = "IDC.IS.MONITORING.IS.STATUS.INTERVAL";
    String IDC_IS_MULTITIER_ORGS = "IDC.IS.MULTITIER.ORGS";
    String IDC_IS_OMS_LOGF4J_CATEGORY = "IDC.IS.OMS.LOGF4J.CATEGORY";
    String IDC_IS_TO_ADAPTOR_HTTP_READ_TIMEOUT = "IDC.IS.TO.ADAPTOR.HTTP.READ.TIMEOUT";
    String IDC_IS_CLIENTCROSSING_ENABLED = "IDC.IS.CLIENT.BASED.ORDER.MATCHING";
    String IDC_IS_ORDER_BROADCAST_ENABLED = "IDC.IS.ORDER.BROADCAST.ENABLED";
    String IDC_IS_SERVICE_NAME_QUALIFIER = "IDC.IS.SERVICE.NAME.QUALIFIER";
    String IDC_IS_ORDER_INTRAFLOOR_MATCHING_ENABLED = "IDC.IS.ORDER.INTRAFLOOR_MATCHING_ENABLED";
    String IDC_IS_ORDER_PROVIDERS_LIST_SHORTNAME = "IDC.IS.ORDER.Providers.list.ShortName";
    String IDC_IS_SERVER_NAME = "IDC.IS.SERVER.NAME";
    String IDC_IS_NOTIFICATIONMSG_COMPATION_ENABLED = "IDC.IS.NOTIFICATION_MESSAGE_COMPACTION_ENABLED";
    String IDC_IS_PROVIDER_GROUP = "IDC.IS.PROVIDER.GROUP";
    String IDC_IS_TRD_EXEC_DATETIME_FORMAT = "IDC.IS.TRD.EXEC.DATETIME.FORMAT";
    String IDC_IS_TRADE_CHANNEL = "IDC.IS.TRADE.CHANNEL";
    String TRADE_CHANNEL_PREFIX = "Idc.TradeChannel.";
    String IDC_IS_PRICE_PROVISION_ACCEPTANCE_ROUNDING_FACTOR = "IDC.DEALING.PRICE.PROVISION.ACCEPTANCE.TOLERANCE.FACTOR";
    String IDC_IS_JMS_DELIVERYMODE = "Idc.IS.DeliveryMode";
    String IDC_IS_STP_REQUEST_CHANNEL_NAME = "IDC.IS.STP.REQUEST.CHANNEL.NAME";
    String IDC_IS_RFS_REQUEST_EXPIRE_TIME = "IDC.IS.RFS.REQUEST.EXPIRE.TIME";
    String IDC_IS_RFS_REQUEST_EXPIRE_TIME_PREFIX = IDC_IS_RFS_REQUEST_EXPIRE_TIME + ".";
    String IDC_IS_BROKER_CUSTOMERS_RFS_REQUEST_EXPIRE_TIME = "IDC.IS.Broker.Customers.RFS.REQUEST.EXPIRE.TIME";
    String IDC_IS_BROKER_CUSTOMERS_RFS_REQUEST_EXPIRE_TIME_PREFIX = IDC_IS_BROKER_CUSTOMERS_RFS_REQUEST_EXPIRE_TIME + ".";
    String IDC_IS_UI_RFS_REQUEST_EXPIRE_TIME = "IDC.IS.UI.RFS.REQUEST.EXPIRE.TIME";
    String IDC_IS_UI_RFS_REQUEST_EXPIRE_TIME_PREFIX = IDC_IS_UI_RFS_REQUEST_EXPIRE_TIME + ".";
    String IDC_IS_BROKER_CUSTOMERS_UI_RFS_REQUEST_EXPIRE_TIME = "IDC.IS.Broker.Customers.UI.RFS.REQUEST.EXPIRE.TIME";
    String IDC_IS_BROKER_CUSTOMERS_UI_RFS_REQUEST_EXPIRE_TIME_PREFIX = IDC_IS_BROKER_CUSTOMERS_UI_RFS_REQUEST_EXPIRE_TIME + ".";
    String IDC_IS_RFS_PROXY_TOPIC = "IDC.IS.RFS.PROXY.TOPIC";
    String IDC_IS_MAX_TENOR = "IDC.IS.MAX.TENOR";

    String IDC_IS_RFS_INVERSE_SUPPORTED = "IDC.IS.RFS.INVERSE.SUPPORTED";
    String IDC_IS_RFS_STATISTICS_ENABLED = "IDC.IS.RFS.STATISTICS.ENABLED";
    String IDC_IS_RFS_CLIENTSDKv1009_SUPPORTED = "IDC.IS.RFS.CLIENTSDKv1009.SUPPORTED";
    String IDC_IS_PRICE_BASED_ACCEPTANCE_ENABLED = "IDC.IS.PRICE.BASED.ACCEPTANCE.ENABLED";
    String IDC_IS_RATELOGGINGDELAY = "IDC.IS.RATELOGGINGDELAY";
    String IDC_IS_DEAL_QUERY_RESPONSE_THRESHOLD = "IDC.IS.DEAL.QUERY.RESPONSE.THRESHOLD";
    String IDC_IS_SPREADINGBYSERVER_ORG_LIST = "IDC.IS.SPREADINGBYSERVER.ORG.LIST";
    String IDC_IS_ORDER_MINIMUM_MARKETRATE_AMOUNT = "IDC.IS.ORDER.MINIMUM.MARKETRATE.AMOUNT";
    String IDC_IS_MONITORING_STALE_RATES_THRESHOLD_MILLIS = "IDC_IS_MONITORING_STALE_RATES_THRESHOLD_MILLIS";
    String TRADING_LIMIT_VALIDATION_DISABLED = "IDC.TRADING.LIMIT.VALIDATION.DISABLED";
    String IDC_IS_USE_PROVIDER_TP_DEFAULT_LE = "IDC.IS.UseProviderTradingPartyDefaultLE";

    String IDC_MISMATCHED_SWAPS_SUPPORT = "IDC.MismatchedSwapsSupport";
    String IDC_MISMATCHED_SWAPS_SUPPORT_PREFIX = "Idc.MismatchedSwapsSupport";
    String IDC_MISMATCHED_SWAPS_BY_DEFAULT_ENABLED = "IDC.MismatchedSwaps.ByDefault.Enabled";
    String IDC_IS_DEFUALTMDS_SUBSCRIBER = "IDC.IS.DefaultMDS.Subscriber";

    String BA_RFS_ESPMDS_ESPSPREADING_ENABLED = "Idc.BrokerAdaptor.RFS.ESPMDS.ESPSpreading.Enabled";

    String IDC_IS_DEFUALTMDS_PROVIDER = "IDC.IS.DefaultMDS.Provider";

    String IDC_IS_PROVIDER_VALUE_DATE_CHECK = "IDC.IS.ProviderValueDateCheck";

    String IDC_IS_OUT_OF_SEQUENCE_QUOTES_CHECK_ENABLED = "IDC.IS.OUT.OF.SEQUENCE.QUOTES.CHECK.ENABLED";
    String IDC_IS_TRADE_VALUE_DATE_VERIFICATION_ENABLED = "IDC.IS.TRADE.VALUE.DATE.VERIFICATION.ENABLED";
    String IDC_IS_TRADE_FIXING_DATE_VERIFICATION_ENABLED = "IDC.IS.TRADE.FIXING.DATE.VERIFICATION.ENABLED";
    String IDC_IS_ADMINPORTAL_URL_PREFIX = "IDC.IS.AdminPortal.URL.";

    String IDC_IS_QUOTE_POOL_MAX_IDLE_SIZE = "IDC.IS.QUOTE.POOL.MAX.IDLE.SIZE";

    String IDC_PRIMEBROKER_ENABLECOVERTRADEWORKFLOW_PREFIX = "Idc.PrimeBroker.EnableCoverTradeWorkflow.";

    String IDC_IS_FXI_PRIME_UPDATE_CREDIT_ENABLED = "IDC.IS.FXI.PRIME.UPDATE.CREDIT.ENABLED";
    String IDC_IS_PRIMEBROKER_LPPB_CCYPAIR_CHECK = "IDC.IS.PRIMEBROKER.LPPB.CCYPAIR.CHECK";
    String IDC_BROKER_OLD_PRE_ACCEPT = "IDC.IS.BROKER.USE.OLD.PRE.ACCEPT.METHOD";

    // JMSProxy related properties.
    String IDC_IS_JMSPROXY_STREAM_ENABLE = "IDC.IS.JMSPROXY.STREAM.ENABLE";
    String IDC_IS_JMSPROXY_CURRENCYPAIR_BUFFER_LENGTH = "IDC.IS.JMSPROXY.CURRENCYPAIR.BUFFER.LENGTH";
    String IDC_IS_JMSPROY_RATES_RETRANSMIT_ENABLED = "IDC.IS.JMSPROXY.RATES.RETRANSMIT.ENABLED";
    //temp keys for broker workflow functor.
    String IDC_BA_VWAP_ENABLED = "IDC.BA.VWAP.ENABLED";

    String IDC_IS_SERVERSIDEPROVISIONING_ENABLED = "Idc.IS.ServerSideProvisioning.Enabled";

    String IDC_IS_MarketRange_UseSpreadAdjustedVWAP = "IDC.IS.MarketRange.UseSpreadAdjustedVWAP";

    String IDC_IS_MarketRange_SpreadPercentage = "IDC.IS.MarketRange.SpreadPercentage";

    String IDC_IS_MarketRange_GiveBackPercentage = "IDC.IS.MarketRange.GiveBackPercentage";

    String IDC_IS_MCAST_MAX_PACKET_SIZE = "IDC.IS.MCAST.MAX.PACKET.SIZE";

    String IDC_IS_MCAST_BOOKS_PER_PORT = "IDC.IS.MCAST.BOOKS.PER.PORT";

    String IDC_IS_QUOTE_COMPACTION_LEVEL = "IDC.IS.QUOTE.COMPACTION.LEVEL";

    String IDC_IS_MCAST_BOOK_FUTILE_SWEEPS_BEFORE_IDLE = "IDC.IS.MCAST.BOOK.FUTILE.SWEEPS.BEFORE.IDLE";

    String IDC_IS_MCAST_BOOK_SWEEP_IDLE_INTERVAL = "IDC.IS.MCAST.BOOK.SWEEP.IDLE.INTERVAL.MICROS";

    String IDC_IS_BUCKET_MANAGER_THREADPOOL_MAX_SIZE = "IDC.IS.BUCKET.MANAGER.THREADPOOL.MAX.SIZE";

    String IDC_IS_BUCKET_MANAGER_THREADPOOL_MIN_SIZE = "IDC.IS.BUCKET.MANAGER.THREADPOOL.MIN.SIZE";

    String IDC_IS_THREADPOOL_BUCKET_MANAGER_ENABLED = "IDC.IS.THREADPOOL.BUCKET.MANAGER.ENABLED";

    String IDC_IS_BUCKET_MANAGER_THREADPOOL_KEEPALIVE = "IDC.IS.BUCKET.MANAGER.THREADPOOL.KEEPALIVE";

    String IDC_IS_MCAST_BOOKS_PROVIDER_ASSIGNMENT = "IDC.IS.MCAST.BOOKS.PROVIDER.ASSIGNMENT";

    String IDC_IS_SINGLE_FILL_TRADE_VERIFY_AMOUNT_VALIDATION_ENABLED = "IDC.IS.SINGLE.FILL.TRADE.VERIFY.AMOUNT.VALIDATION.ENABLED";

    String IDC_IS_ADMIN_READONLY_CLASSES = "IDC.IS.ADMIN.READONLY.CLASSES";

    String IDC_IS_TRADECACHE_MAX_SIZE = "IDC.IS.TRADECACHE.MAX.SIZE";

    String IDC_IS_TRADE_WORKFLOW_REFERENCE_DATA_ENTITY_CLASSES = "IDC.IS.TRADE.WORKFLOW.REFERENCE.DATA.ENTITY.CLASSES";

    String IDC_REFERENCE_DATA_WARMUP_ON_SUBSCRIPTION_ENABLED = "IDC.REFERENCE.DATA.WARMUP.ON.SUBSCRIPTION.ENABLED";

    String IDC_ASYNC_REFERENCE_DATA_WARMUP_ON_SUBSCRIPTION = "IDC.ASYNC.REFERENCE.DATA.WARMUP.ON.SUBSCRIPTION";

    String IDC_IS_BROKER_ADAPTOR_USE_LIMITPRICE = "IDC.IS.BROKER.ADAPTOR.USE.LIMITPRICE";

    String IDC_IS_PENDING_SUBSCRIPTION_PROCESSING_SYNC  = "IDC.IS.Pending.Subscription.Processing.Sync";

    String IDC_IS_MINIMUM_TRADE_SIZE_KEY = "Idc.MinTradeSize";
    String IDC_IS_MINIMUM_TRADE_SIZE_PREFIX = IDC_IS_MINIMUM_TRADE_SIZE_KEY + '.';

    String IDC_IS_MAXIMUM_ORDER_SIZE_KEY = "Idc.MaxOrderSize";
    String IDC_IS_MAXIMUM_ORDER_SIZE_PREFIX = IDC_IS_MAXIMUM_ORDER_SIZE_KEY + '.';

    String IDC_IS_USE_PB_PRICE_STREAM_PREFIX = "Idc.IS.UsePBPriceStream.";

    String IDC_IS_Providers_Broker_ShortName = "IDC.IS.Providers.Broker.ShortName";
    String IDC_IS_EXCLUSIONLIST_RANDOMIZER_PATTERN = "IDC.ORDER.Providers.Excluded.Randomizer.";

    String IDC_TRADE_DELIVERY_MODE = "IDC.IS.TRADE.DISPATCHER.DELIVERY.MODE";

    String IDC_TRADE_TIME_TO_LIVE = "IDC.IS.TRADE.TIME.TO.LIVE";

    String IDC_IS_OBOWORKFLOW_ENABLED = "Idc.IS.OBOWorkflow.Enabled";
    String IDC_IS_OBOWORKFLOW_SPREADMODEL = "Idc.IS.OBOWorkflow.SpreadModel";
    String IDC_IS_OBOWORKFLOW_SPREADMODEL_ALL_ESP = "Idc.IS.OBOWorkflow.SpreadModel._ALL_.ESP";
    String IDC_IS_OBOWORKFLOW_SPREADMODEL_ALL_RFS = "Idc.IS.OBOWorkflow.SpreadModel._ALL_.RFS";

    String IDC_IS_SUBMIT_ORDERS = "IDC.IS.SubmitOrders";

    String IDC_IS_SUBMIT_ORDERS_MIN_EXPIRY_TIME = "IDC.IS.SubmitOrders.MinExpiryTime";

    String IDC_CUSTOMER_REFERENCE_DATA_WARMUP_LEVEL = "IDC.CUSTOMER.REFERENCE.DATA.WARMUP.LEVEL";

    String IDC_MAKERUSER_MASKEDLP = "Idc.makeruser.maskedLP";

    String IDC_IS_ENABLE_RFS_SPREADS = "IDC.RFS.SPREAD.ENABLED";

    String IDC_BA_MRFQ_HIDE_CHANNEL = "Idc.BrokerAdaptor.ManualRFQ.HideChannel";

    String IDC_PERSIST_RFSRATES = "IDC.PERSIST.RFSRATES";
    String IDC_PERSIST_RFSRATES_PREFIX = IDC_PERSIST_RFSRATES + ".";

    String IDC_PERSIST_BROKER_CUSTOMERS_RFSRATES = "IDC.PERSIST.BROKER.CUSTOMERS.RFSRATES";
    String IDC_PERSIST_BROKER_CUSTOMER_RFSRATES_PREFIX = IDC_PERSIST_BROKER_CUSTOMERS_RFSRATES + ".";

    String IDC_IS_VIRTUAL_SERVER_FOR_CONFIG_ISSUE_EMAIL = "IDC.IS.VIRTUALSERVER.FOR.CONFIG.ISSUE.EMAIL";

    String IDC_IS_MULTIHOST_CONFIG_ISSUE_EMAIL_ADDRESS = "IDC.IS.CONFIG.ISSUE.EMAIL.ADDRESS.MULTIHOST";

    String IDC_IS_LP_CONFIG_FACTORY_ENABLED = "IDC.IS.LP.CONFIG.FACTORY.ENABLED";

    String IDC_IS_MONGO_INIT = "IDC.IS.MONGO.INIT";

    String IDC_STP_SPREAD_DIRECTION = "IDC.STP.Spreads.UseSpreadDirection";

    String IDC_NEW_ACCOUNT_ID_LOOKUP_ENABLED = "IDC.IS.UseCoveredOrg.SubAccountId.New.Lookup.Enabled";

    String IDC_USE_COVEREDORG_SUBACCOUNTID_ESPRFS = "IDC.IS.UseCoveredOrg.SubAccountId.ESPRFS";

    String IDC_DA_STREAM_MAPPING_USE_ADMIN = "Idc.DistributedAdaptor.VirtualServerToStreamMapping.UseAdmin";

    String IDC_IS_INTEGRAL_SPACES_ENABLED = "IDC.IS.IntegralSpaces.Enabled";

    String NUM_ADAPTOR_STATUS_HANDLER_THREADS = "IDC.IS.LP.AdaptorStatus.NumThreads";

    String MAX_ADAPTOR_STATUS_HANDLER_THREADS = "IDC.IS.LP.AdaptorStatus.MaxThreads";

    String ADAPTOR_STATUS_HANDLER_QUEUE_SIZE = "IDC.IS.LP.AdaptorStatus.QueueSize";

    String ADAPTOR_STATUS_HANDLER_POOL_DYNAMIC = "IDC.IS.LP.AdaptorStatus.DynamicPool";

    /**
     * The below properties determines whether PB/Broker should exclude customer order info from the client blotters.
     */
    String CUSTOMER_ORDER_EXCLUDED = "IDC.IS.CUSTOMER.ORDER.EXCLUDED";
    String CUSTOMER_ORDER_EXCLUDED_PREFIX = "IDC.IS.CUSTOMER.ORDER.EXCLUDED.";

    /**
     * The below property is to enable the Trade Response's state check
     */
    String IDC_IS_TRADE_RESPONSE_STATE_CHECK = "IDC.IS.TRADE.RESPONSE.STATE.CHECK";

    /**
     * The below property is to activate quote with size zero
     */
    String IDC_IS_ZERO_QUOTE_SIZE_ACTIVE = "IDC.IS.ZERO.QUOTE.SIZE.ACTIVE";

    /**
     * The below property is to enable application of increased trading limit on unbalanced tiers
     */
    String IDC_IS_APPLY_TRADINGLIMITS_UNBALANCEDTIERS = "Idc.IS.Apply.TradingLimits.UnbalancedTiers";

    /**
     * The below property determines whether Liquidity Provisioing is enabled or not.
     */
    String LIQUIDITY_PROVISIONING_ENABLED = "IDC.IS.Liquidity.Provisioning.Enabled";

    /**
     * The below properties determines whether Liquidity Provisioning is enabled for an org or not.
     */
    String LIQUIDITY_PROVISIONING_ORG_ENABLED_PREFIX = "IDC.IS.Liquidity.Provisioning.Org.";

    /**
     * The below property determines whether the trade JMS listener is setup after the server is fully started.
     */
    String POST_STARTUP_TRADE_JMS_LISTENER_SETUP = "IDC.IS.Post.Startup.Trade.JMS.Listener.Setup";

    /**
     * The below property is to set timeout in seconds for synchronous trade-response for corresponding trade-request call
     */
    String IDC_IS_TO_ADAPTOR_TRADE_REQUEST_TIMEOUT = "IDC.IS.TO.ADAPTOR.TRADE.REQUEST.TIMEOUT";

    /**
     * The below property determines whether the new account id lookup is enabled for RFS subscription.
     */
    String RFS_SUBSCRIPTION_ACCOUNTID_NEW_LOOKUP_ENABLED = "IDC.IS.RFS.SUBSCRIPTION.ACCOUNTID.NEW.LOOKUP.ENABLED";

    /**
     * URLs for MET (Material Electronic Terms) to be added in login response.
     */
    String IDC_MET_Public_URL = "Idc.MET.PublicURL";
    String IDC_MET_Public_URL_SEF = "Idc.MET.PublicURL.SEF";
    String DO_NOT_DISPLAY_URL = "DO_NOT_DISPLAY";

    /**
     * The below property determines whether the mid rate calculation is enabled.
     */
    String IDC_MID_RATE_CALC_PROVIDER_ENABLED = "Idc.MidRate.Calc.Provider.Enabled";
    String IDC_MID_RATE_CALC_CUSTOMER_ENABLED = "Idc.MidRate.Calc.Customer.Enabled";
    String IDC_MID_RATE_CALC_BROKER_ENABLED = "Idc.MidRate.Calc.Broker.Enabled";
    String IDC_MID_RATE_CALC_BROKER_USELPRAWRATE_ENABLED = "Idc.MidRate.Calc.Broker.UseLpRawRate.Enabled";
    String IDC_MID_RATE_CALC_BROKER_ESP_UNSKEWED_ENABLED = "Idc.MidRate.Calc.Broker.ESP.Unskewed";
    String IDC_MID_RATE_TWO_WAY_CONVERSION_BROKER_ENABLED = "Idc.MidRate.Calc.Broker.TwoWay.Conversion.Enabled";


    /**
     * The below property determines whether the mid rate calculation is enabled.
     */
    String IDC_MID_RATE_SEND_PROVIDER_ENABLED = "Idc.MidRate.Send.Provider.Enabled";
    String IDC_MID_RATE_SEND_CUSTOMER_ENABLED = "Idc.MidRate.Send.Customer.Enabled";


    String IDC_MID_RATE_SEND_EMAIL_CUSTOMER_ENABLED = "Idc.MidRate.Email.Customer.Enabled";
    String IDC_MID_RATE_SEND_EMAIL_PROVIDER_ENABLED = "Idc.MidRate.Email.Provider.Enabled";

    // reference data warm-up related properties
    String IDC_PERIODIC_REFERENCE_DATA_WARMUP_ENABLED = "Idc.Periodic.Reference.Data.Warmup.Enabled";
    String IDC_IMMEDIATE_CUSTOMER_REFERENCE_DATA_WARMUP_ENABLED = "Idc.Immediate.Customer.Reference.Data.Warmup.Enabled";
    String IDC_REFERENCE_DATA_WARMUP_PERIOD = "Idc.Reference.Data.Warmup.Period";
    long IDC_REFERENCE_DATA_WARMUP_PERIOD_DEFAULT = 2 * 60 * 1000;
    String IDC_REFERENCE_DATA_FULL_WARMUP_PERIOD = "Idc.Reference.Data.Full.Warmup.Period";

    String IDC_IS_OUTRIGHT_DISPLAY_ORDER_ENABLED = "Idc.IS.Outright.Display.Order.Enabled";

    String IDC_IS_PERSISTENCE_CACHE_ENTRY_TIMETOLIVE_PERIOD = "Idc.IS.Persistence.Cache.Entry.TimeToLive.Period";
    String IDC_IS_PERSISTENCE_CACHE_ENTRY_DELAYED_REMOVAL_ENABLED = "Idc.IS.Persistence.Cache.Entry.Delayed.Removal.Enabled";

    String IDC_DEAL_ORDER_PERSISTENCE_SPACES_ENABLED = "Idc.DealOrder.Spaces.Persistence.Enabled";

    String IDC_DEAL_ORDER_PERSISTENCE_ORACLE_DISABLED = "Idc.DealOrder.Oracle.Persistence.Disabled";

    /**
     * The below property enables new way of applying spreads for uneven swap. Taking the bidOfferMode of the larger leg.
     */
    String IDC_IS_APPLY_NEW_UNEVEN_SWAP_SPREADING = "Idc.IS.Apply.New.Uneven.Swap.Spreading";

    /**
     * This property determines whether multi-app messages need to be sent from dealing database transactions.
     */
    String IDC_IS_DEALING_TRANSACTION_MULTIAPP_UPDATE_ENABLED = "Idc.IS.Dealing.Transaction.Multiapp.Update.Enabled";

    /**
     * This property determines which events are enabled for multi-app messages in dealing database transactions. This list will be used
     * to send out multi-app message even when multi-app message sending is disabled.
     */
    String IDC_IS_DEALING_TRANSACTION_MULTIAPP_UPDATE_ENABLED_EVENTS = "Idc.IS.Dealing.Transaction.Multiapp.Update.Enabled.Events";

    String IDC_DEAL_ORDER_EXPIRATION_DAYS = "Idc.DealOrder.Expiration.Days";
    String IDC_SPACES_DEAL_CACHE_NUM_OF_CONTAINER = "Idc.IS.SpacesDeal.Cache.Container.Count";
    /**
     * This property checks if rfs/esp is enabled at stream level.
     */
    String IDC_IS_LPSTREAM_SUPPORTED_TRADETYPES_CHECK = "Idc.IS.LPStream.Supported.TradeTypes.Check";
    String IDC_IS_LPSTREAM_SUPPORTED_TRADETYPES_CHECK_PREFIX = "Idc.IS.LPStream.Supported.TradeTypes.Check.";

    String IDC_IS_DEAL_ORDER_SPACES_QUERY_ENABLED = "Idc.DealOrder.Spaces.Query.Enabled";

    String IDC_IS_MULTI_LEG_RFS_CREDIT_CHECK_ON_SUBSCRIPTION_ENABLED = "Idc.IS.MultiLeg.RFS.Credit.Check.OnSubscription.Enabled";

    /**
     * The below property determines whether RFS currency pairs is validated against currency group defined for provider.
     */
    String IDC_RFS_PROVIDER_CCYPAIRGROUP_VALIDATION_ENABLED = "IDC.IS.RFS.Provider.CcyPairGroup.Validation.Enabled";
    String IDC_RFS_PROVIDER_CCYPAIRGROUP_VALIDATION_ENABLED_PREFIX = IDC_RFS_PROVIDER_CCYPAIRGROUP_VALIDATION_ENABLED + ".";

    // Property to query Rounding Factor for currency pair
    String IDC_IS_ROUNDING_FACTOR_PREFIX = "Idc.IS.ProvisioningRoundingFactor";


    /**
     * The below property determines whether to mark rate active on ccy pair addition and inactive on ccy pair removal or not.
     */
    String IDC_IS_MARKRATE_ACTIVE_INACTIVE_ON_CCYPAIR_ADDREMOVE = "Idc.IS.ChangeRateStatusOnCcyPairChange";

    /**
     * The below property determines whether to update the value date cache present at QDM level.
     */
    String IDC_IS_UPDATE_VALUE_DATE_CACHE = "Idc.IS.UpdateValueDateCache";

    String IDC_IS_SPACES_DO_ENABLED = "Idc.IS.Spaces.DO.Enabled";

    String IDC_FXIDIRECT_CURRENCY_PAIRS_ORDER = "Idc.FXIDirect.CurrencyPairs.Order";

    String IDC_SPACES_TRAIANA_STP_ENABLED = "Idc.Spaces.Traiana.STP.Enabled";

    String IDC_QUOTES_POOL_ENABLED = "Idc.Quotes.Pool.Enabled";

    String IDC_PB_LP_WORKFLOW_CCY_PAIRS_ENABLED = "Idc.PB.LP.Workflow.Ccy.Pairs.Enabled";
    String IDC_PB_LP_WORKFLOW_CCY_PAIRS_ENABLED_PREFIX = "Idc.PB.LP.Workflow.Ccy.Pairs.Enabled.";

    String IDC_PB_LP_WORKFLOW_TRADING_CHANNEL_ENABLED = "Idc.PB.LP.Workflow.Trading.Channel.Enabled";
    String IDC_PB_LP_WORKFLOW_TRADING_CHANNEL_ENABLED_PREFIX = "Idc.PB.LP.Workflow.Trading.Channel.Enabled.";

    String IDC_IS_ASYNC_INTERNALREJECTION_ENABLED = "Idc.IS.Asyc.InternalRejection.Enabled";

    /**
     * Property to look up special character exclusion in Order notes ( or say settlement instruction),
     * this is to throw validation error , default <,> characters
     */
    String IDC_IS_SPECIALCHARACTER_EXCLUSION = "Idc.IS.Character.Exclusion";

    /**
     * Internal property not to be configured via config portal
     */
    String IDC_RESTING_ORDER_EMAIL_EXCLUSION_TIF = "Idc.Resting.Order.Email.Exclusion.TIF";
    String IDC_RESTING_ORDER_EMAIL_EXCLUSION_TIF_PREFIX = IDC_RESTING_ORDER_EMAIL_EXCLUSION_TIF + ".";
    String IDC_RESTING_ORDER_EMAIL_GTD_EXPIRATION_TIME_THRESHOLD = "Idc.Resting.Order.Email.GTD.ExpirationTime.Threshold";
    String IDC_RESTING_ORDER_EMAIL_GTD_EXPIRATION_TIME_THRESHOLD_PREFIX = IDC_RESTING_ORDER_EMAIL_GTD_EXPIRATION_TIME_THRESHOLD + ".";

    String IDC_IS_MULTI_PB_SERIALIZATION_ENABLED = "Idc.IS.MultiPb.SerializationEnabled";

    String IDC_SPACES_ID_SERVICE_MAXCON = "Idc.Spaces.IDService.MaxConnections";

    String IDC_IS_USE_LPSTREAM_LE = "Idc.IS.UseLpStreamLe";

    String IDC_IS_MS_RETRY_COUNT = "Idc.IS.MSRetryCount";

    public static String TRANSACTION_ID_APP_NAME = "IDC.TransactionID.ApplicationName";

	String IDC_IS_DIRECTED_ORDER_CHECK_IN_BA_MESSAGE_BUILDER_ENABLED = "Idc.IS.Directed.Order.Check.In.BAMessage.Builder.Enabled";

	String IDC_IS_QDI_INITIALIZATION_CHECK_ENABLED = "Idc.IS.QDI.Initialization.Check.Enabled";

    String IDC_IS_TRADE_REQUEST_LATENCY_CIRCUIT_BREAKER_ENABLED = "Idc.IS.TradeRequest.Latency.CB.Enabled";
    String IDC_IS_TRADE_REQUEST_LATENCY_CIRCUIT_BREAKER_THRESHOLD = "Idc.IS.TradeRequest.Latency.CB.Threshold";

    String IDC_NOTIFICATION_ORG_ROUTING_PREFIX = "Idc.Notification.Org.Routing.Prefix";

    String IDC_NOTIFICATION_DEFAULT_ROUTING_PREFIX = "Idc.Notification.Default.Routing.Prefix";

    String IDC_IS_MESSAGE_SERIALIZER_BUFFER_SIZE = "Idc.IS.Message.Serializer.BufferSize";

    String IDC_IS_SEND_VD_NOTIFICATION = "IDC.IS.Send.ValueDate.CD.Notifications";

    String IDC_IS_AUTO_DEPLOY_FILTER_TAKER_ORG = "Idc.IS.Auto.Deploy.Filter.Taker.Org";

    String IDC_IS_AUTO_DEPLOY_MASKLP_OA_HB_ENABLED = "Idc.IS.Auto.Deploy.MaskLP.OA.Enabled";

    String IDC_IS_AUTO_DEPLOY_TAKER_FOR_INTRA_FLOOR = "Idc.IS.Auto.Deploy.Taker.For.Intra.Floor";

    String IDC_IS_SUBSCRIPTION_ACCOUNTID_FALLBACKONREAL = "Idc.IS.Subscription.AccountId.FallBack.Real";

    String IDC_NOTIFICATION_MS_CONFIG = "Idc.Notification.MS.Config";

    String IDC_TRADEEMAIL_PBCOVER_TRADE_ENABLED = "IDC.TRADEEMAIL.RFS.PB.COVER.ENABLED";

    String IDC_ABROUTER_GRID_BASED_ROUTING_ENABLED = "IDC.CustomerRoutingRules.Grid.Based.Routing.Enabled";

    String IDC_IS_USE_PRICE_REGENERATION_SERVICE_IN_ORDER_MATCH = "IDC.IS.Use.PriceRegenerationService.InOrderMatch";

    String IDC_FMA_ALLOW_SUBSCRIPTIONS_FOR_NONCUSTOMERS = "Idc.FMA.AllowSubscriptionsForNonCustomers";
    String IDC_FMA_ALLOW_SUBSCRIPTIONS_FOR_NONCUSTOMERS_PREFIX = "Idc.FMA.AllowSubscriptionsForNonCustomers.";

    String IDC_IS_INTEGRAL_SERVICES_ENABLED = "IDC.IS.IntegralServices.Enabled";

    String GRID_MID_RATE_PRICE_SOURCES_STRING = "Idc.Supported.Price.Source";

    String GRID_MID_RATE_DEFAULT_SRC = "GMR";

    String IDC_RDS_CLIENT_SERVICE_ENABLED = "IDC.RDSClient.Service.Enabled";

    String IDC_RFS_REQUEST_AMOUNT_VALIDATION= "IDC.RFS.REQUEST.AMOUNT.VALIDATION";

    String IDC_RFS_REJECT_ON_INVALID_TRADE_CHANNEL= "IDC.RFS.Reject.Invalid.TradeChannel";

    String IDC_GLOBAL_JNDI_HEARTBEAT_TOPIC_NAMES = "Idc.IS.HeartBeat.Jndi.Topic.Names";

    String IDC_INTERNAL_ADMIN_URL = "IDC.IS.Internal.Admin.URL";

    String IDC_IS_VALUE_DATE_CHECK_ENABLED = "IDC.IS.ValueDate.Check.Enabled";

    String IDC_IS_ABROUTER_ENABLED = "IDC.IS.ABRouter.Enabled";

    String IDC_IS_ACCOUNT_SET_ON_ORDER = "IDC.IS.Account.Set.On.OrderRequest";

    String IDC_IS_ACCOUNT_CACHE_WARM_UP_ENABLED = "IDC.IS.Account.Cache.Warmup.Enabled";

    String IDC_IS_ORIGIN_TICKET_ID_ENABLED = "IDC.EnableOriginTicketId.";

    String IDC_IS_CLOB_ROUTING_ENABLED = "IDC.IS.ClobRoutingEnabled";
    String IDC_IS_CLOB_ROUTING_FOR_PEG_AT_MID_ORDERS_ENABLED = "IDC.IS.ClobRouting.PegAtMidOrders.Enabled";

    String IDC_CPTYS_ENABLED_FOR_AGGREGATED_FILL = "Idc.Cptys.Enabled.For.Aggregated.Fill";
    String IDC_CPTYS_ENABLED_FOR_AGGREGATED_FILL_PREFIX = "Idc.Cptys.Enabled.For.Aggregated.Fill.";

    String IDC_PROVIDER_PRIORITY_ENABLED = "Idc.Provider.Priority.Enabled";
    String IDC_PROVIDER_PRIORITY_ENABLED_PREFIX = "Idc.Provider.Priority.Enabled.";

    String IDC_PROVIDER_STREAM_GROUPING_BASED_PRIORITY_ENABLED = "Idc.Provider.Stream.Grouping.Based.Priority.Enabled";
    String IDC_PROVIDER_STREAM_GROUPING_BASED_PRIORITY_ENABLED_PREFIX = "Idc.Provider.Stream.Grouping.Based.Priority.Enabled.";

    String IDC_PROVIDER_STREAM_CATEGORY_DEFAULT = "Idc.Provider.StreamCategory.Default";
    String IDC_PROVIDER_STREAM_CATEGORY_METALS_ENABLED = "Idc.Provider.StreamCategory.Metals.Enable";
    String IDC_PROVIDER_STREAM_GROUPS = "Idc.Provider.StreamCategory.OrderSize";
    String IDC_PROVIDER_STREAM_GROUPS_PREFIX = "Idc.Provider.StreamCategory.OrderSize.";

    String IDC_IS_RATE_DISTRIBUTION_STRATEGY = "Idc.IS.Rate.Distribution.Strategy";

    String IDC_IS_RATE_QUOTE_CACHE_OPTIMIZATION_ENABLED = "Idc.IS.Rate.Quote.Cache.Optimization.Enabled";

    String IDC_IS_RATE_QUOTE_CACHE_INDEX_BASED_REFERENCE_COUNTER_ENABLED = "Idc.IS.Rate.Quote.Cache.Index.Based.Reference.Counter.Enabled";

    String IDC_IS_RATE_PROCESSORS_POOL_SIZE = "Idc.IS.Rate.Processors.Pool.Size";

    String IDC_IS_RATE_METRICS_SNAPSHOT_ENABLED= "Idc.IS.Rate.Metrics.Snapshot.Enabled";

    String IDC_IS_RATE_LOAD_BALANCING_ENABLED = "Idc.IS.Rate.Load.Balancing.Enabled";

    String IDC_IS_RATE_LOAD_BALANCING_MIN_DROPS = "Idc.IS.Rate.Load.Balancing.Min.Drops";

    String IDC_IS_RATE_LOAD_BALANCING_MIN_DROP_PERCENTAGE = "Idc.IS.Rate.Load.Balancing.Min.Drops.Percentage";

    String IDC_IS_RATE_LOAD_BALANCING_PROCESSOR_MAX_DROP_PERCENTAGE = "Idc.IS.Rate.Load.Balancing.Target.Processor.Max.Drop.Percentage";

    String IDC_IS_CIRCUIT_BREAKER_ENABLED = "Idc.IS.Circuit.Breaker.Enabled";

    String IDC_IS_RISKNET_MARKETDATA_SPREAD_IN_BASISPOINTS = "Idc.IS.RiskNet.MarketData.Spread.In.BasisPoints";

    String IDC_IS_RISKNET_MARKETDATA_SPREAD_IN_BASISPOINTS_PREFIX = IDC_IS_RISKNET_MARKETDATA_SPREAD_IN_BASISPOINTS + ".";

    String IDC_IS_VENUE_MARKETDATA_SPREAD_IN_BASISPOINTS = "Idc.IS.Venue.MarketData.Spread.In.BasisPoints";

    String IDC_IS_VENUE_MARKETDATA_SPREAD_IN_BASISPOINTS_PREFIX = IDC_IS_VENUE_MARKETDATA_SPREAD_IN_BASISPOINTS + ".";

    String IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_INBPS = "Idc.IS.Venue.BrokersCustomer.Trade.Spread.InBPS";

    String IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_INBPS_PREFIX = IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_INBPS + ".";

    String IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_STRATEGY = "Idc.IS.Venue.BrokersCustomer.Trade.Spread.Strategy";

    String IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_ENABLED_STRATEGY = IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_STRATEGY + ".";

    String IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_STRATEGY_NONE = "NONE";

    String IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_STRATEGY_PRE = "PRE";

    String IDC_IS_VENUE_BROKERSCUSTOMER_TRADE_SPREAD_STRATEGY_POST = "POST";

    String IDC_IS_ORDER_USE_PERCENTAGE_MARKET_RANGE = "IDC.IS.ORDER.Use.MarketRange.Percentage";

    String IDC_IS_SEND_TRADE_MESSAGE_SONIC_ROUTE = "IDC.IS.Send.Trade.Message.Sonic.Route";

    String IDC_IS_RISKNET_FIXING_NAMES = "Idc.IS.RiskNet.Fixing.Names";

    String IDC_IS_CREDIT_ADMIN_NOTIFICATION_FILTER_ENABLED = "Idc.IS.Credit.Admin.Notification.Filter.Enabled";

    String IDC_IS_NEW_PROVISIONING_SERVICE_ENABLED = "Idc.IS.New.Provisioning.Service.Enabled";

    String IDC_IS_SUSPEND_VENUE_REQUESTS = "IDC.IS.Suspend.Venue.Requests";

    String IDC_IS_CANCEL_VENUE_REQUESTS_RETRY_COUNT = "IDC.IS.Cancel.Venue.Requests.Retry.Count";

    String IDC_IS_VENUE_ACCESS_FOR_BROKER_CUSTOMERS_ENABLED = "Idc.IS.Venue.Access.For.Broker.Customers.Enabled";

    String IDC_IS_VENUE_ACCESS_FOR_PRIME_BROKER_CUSTOMERS_ENABLED = "Idc.IS.Venue.Access.For.PrimeBroker.Customers.Enabled";

    String IDC_IS_HOSTED_ADAPTOR_SUBSCRIPTION_ENABLED= "Idc.IS.Hosted.Adaptor.Subscription.Enabled";

    String IDC_IS_RFS_SUBSCRIBE_REAL_AND_MASK = "Idc.IS.RFS.Subscribe.RealAndMask";

    String IDC_IS_APPLYFEE_SELFMATCH_ENABLED = "Idc.IS.ApplyFee.SelfMatch.Enabled";

    String IDC_IS_BENCHMARK_PRICE_SOURCE_STALE_INTERVAL = "Idc.IS.BenchMark.Price.Source.Stale.Interval";

    String IDC_IS_BENCHMARK_SERVICE_ENABLED = "Idc.IS.BenchMark.Service.Enabled";

    String IDC_IS_BENCHMARK_BUFFER_SIZE = "Idc.IS.BenchMark.Buffer.Size";

    String IDC_IS_PROVIDER_STATUS_ASYNC_NOTIFICATION_ENABLED = "IDC.IS.ASyncNotification.Of.Provider.Status.Enabled";

    String IDC_IS_FIX_SESSION_RDS_PERSIST_ENABLED = "IDC.IS.FIX.Sessions.RDS.Persist.Enabled";

    String BROKER_ADAPTOR_AUTO_SUBSCRIPTIONS = "Idc.BrokerAdaptor.AutoSubscriptions";
    String BROKER_ADAPTOR_AUTO_SUBSCRIPTIONS_PREFIX = BROKER_ADAPTOR_AUTO_SUBSCRIPTIONS + ".";
    String BROKER_ADAPTOR_NEW_SUBSCRIPTION = "Idc.BrokerAdaptor.New.Subscriptions";
    String BROKER_ADAPTOR_NEW_SUBSCRIPTION_PREFIX = BROKER_ADAPTOR_NEW_SUBSCRIPTION + ".";
    String BROKER_ADAPTOR_LP_SUBSCRIPTION_FORCE_SUBSCRIBE = "Idc.BrokerAdaptor.LP.Subscriptions.Force.Subscribe";
    String BROKER_ADAPTOR_LP_SUBSCRIPTION_FORCE_SUBSCRIBE_PREFIX = BROKER_ADAPTOR_LP_SUBSCRIPTION_FORCE_SUBSCRIBE + ".";
    String BROKER_ADAPTOR_OPTIMIZED_RFS = "Idc.IS.Optimized.RFS";
    String IDC_IS_SEQ_JUMPBY_FACTOR = "IDC.IS.SEQ.JUMPBY.FACTOR";
    String IDC_IS_PRICE_PROVISION_DATA_REFRESH = "IDC.IS.PRICE.PROVISION.DATA.REFRESH";

    String Idc_IS_Pending_Subscriptions_CheckAndExecute_Enabled = "Idc.IS.Pending.Subscriptions.CheckAndExecute.Enabled";
    String Idc_IS_Pending_Subscriptions_CheckAndExecute_Interval = "Idc.IS.Pending.Subscriptions.CheckAndExecute.Interval";

    String IDC_PRICE_PROTECTION_RANGE_IN_BASIS_POINTS = "Idc.Vwap.Price.Protection.Range.In.Basis.Points";
    double DEFAULT_VWAP_PRICE_PROTECTION_RANGE_IN_BASIS_POINTS = -1.00d;
    double BASIS_FACTOR = 0.0001d;
    int BPS_PRECISION = 5;

    String IDC_IS_FULL_AMOUNT_MIN_ORDER_SIZE = "Idc.IS.FullAmount.MinOrderSize";

    String IDC_IS_FULL_AMOUNT_MIN_ORDER_SIZE_PREFIX = IDC_IS_FULL_AMOUNT_MIN_ORDER_SIZE + ".";

    String IDC_IS_MM_LIQUIDITYPROVISIONRULES = "IDC.IS.MarketMaker.VirtualServer.LiquidityRules";
    String IDC_IS_MM_LIQUIDITYPROVISIONRULES_PREFIX = "IDC.IS.MarketMaker.VirtualServer.LiquidityRules.";
    String IDC_IS_RFS_ACCEPTANCE_PAYMENT_PARAMETERS_USE_ACCEPTED_DEALING_PRICE_CONVENTION = "IDC.IS.Acceptance.Payment.Parameters.Use.Accepted.Dealing.Price.Convention";

    String IDC_MAX_CONCURRENT_RFS_REQUESTS = "Idc.Max.Concurrent.RFS.Requests";
	String IDC_IS_PRICEPROVISION_SWAP_SPREAD_ENABLED= "IDC.ADMIN.PRICEPROVISION.SWAP.SPREAD.ENABLED";
	String IDC_IS_PRICEPROVISION_SWAP_SPREAD_ENABLED_PREFIX=IDC_IS_PRICEPROVISION_SWAP_SPREAD_ENABLED+".";
	
	String IDC_IS_RFS_BPS_MIDRATE_ENABLED= "IDC.IS.RFS.BPS.MIDRATE.ENABLED";
    String IDC_IS_RFS_BPS_MIDRATE_ENABLED_PREFIX = IDC_IS_RFS_BPS_MIDRATE_ENABLED+ ".";
	
    String IDC_PORTFOLIO_CACHE_CONFIG = "Idc.Portfolio.Cache.Config";

    String IDC_SDORG_SAMEAS_PBORG_ALLOWED = "Idc.SDOrg.SameAs.PBOrg.Allowed";
    String CDQ_MAKER_ORDER_ON_ORDER_SUBMISSION = "Idc.CDQ.Order.MakerOrder.OnSubmit";
    String CRYPTO_EXCHANGE_NAME = "Idc.IS.Crypto.Exchange.Name";
    String IDC_IS_LOG_OB_GAPI_MSG = "Idc.IS.Log.Outbound.GridAPI.Message";
    String SHOW_PROVISIONED_LIMITS_PREFIX = "Idc.IS.Show.Provisioned.Limits";
    String PROP_QUOTE_AMOUNT_ROUNDING_MODE = "IDC.IS.QuoteAmountRoundingMode";
    String PROP_QUOTE_AMOUNT_ROUNDING_MODE_PREFIX = PROP_QUOTE_AMOUNT_ROUNDING_MODE + ".";
    String RFS_VALIDATIONFAILURE_SENDTOMANUAL = "IDC.RFS.ValidationFailure.SendToManual";
    String RFS_CREDIT_VALIDATION_FAILURE_ZERO_FORWARD_POINT_HANDLING = "IDC.RFS.Credit.ValidationFailure.ZeroForwardPointHandling";
    String RFS_CREDIT_VALIDATION_FAILURE_ZERO_FORWARD_POINT_HANDLING_PREFIX = RFS_CREDIT_VALIDATION_FAILURE_ZERO_FORWARD_POINT_HANDLING + ".";

    String IDC_IS_FULL_AMOUNT_ExclusiveFullAmountCheckEnabledForOrderType_Prefix = "Idc.IS.FullAmount.ExclusiveFullAmountCheck.OrderType.";
    String IDC_IS_FULL_AMOUNT_ExclusiveFullAmountCheckOrderExecutionFlagsMask = "Idc.IS.FullAmount.ExclusiveFullAmountCheck.OrderExecutionFlagsMask";

    String REENABLE_OLD_POSITION = "Idc.IS.RE.Enable.OldPOS";

    String IDC_IS_ExternalClientTagEnabled = "IDC.IS.ExternalClientTagEnabled";
    String IDC_IS_UseAccountIdAsExternalClientTag = "IDC.IS.UseAccountIdAsExternalClientTag";
    String IDC_IS_NettingEnabledForStrategyOrders = "IDC.IS.NettingEnabledForStrategyOrders";
    String IDC_IS_PersistNettedTradesOnNetTrade  = "IDC.IS.PersistNettedTradesOnNetTrade";


    String IDC_IS_OPTIMIZED_JSONFACORY_ENABLED = "Idc.IS.Optimized.JSON.Factory.Enabled";

    /* Populate Spot Rate in the Swap subscription request when sending to Provider adaptor*/
    String IDC_IS_RFS_INCLUDE_SPOT_RATE_SUBSCRIBE_PREFIX = "Idc.IS.RFS.Include.SpotRate.Subscribe.";

    String IDC_IS_UseCptyVisibilityTypeForOcx2DealOrder = "IDC.IS.OCX2.UseCptyVisibilityTypeForDealOrder";

    String IDC_IS_IGNORE_QUOTE_CANCEL_ON_REQUEST_ACCEPTANCE = "IDC.IS.Ignore.Quote.Cancel.On.Request.Acceptance";

    String IDC_IS_OA_WARMUP_WORKER_ENABLED = "IDC.IS.OA.Warmup.Worker.Enabled";

    String IDC_IS_FIX_CLIENT_WARMUP_ENABLED = "IDC.IS.FIX.Client.Warmup.Enabled";

    String IDC_IS_CIRCUIT_BREAKER_RULES_PRELOAD_ENABLED = "IDC.IS.Circuit.Breaker.Rules.Preload.Enabled";

    String IDC_IS_BROKER_CUSTOMERS_LIQUIDITY_RULES_MAX_ORDER_SIZE_CHECK_DISABLED_TRADETYPES = "IDC.IS.Broker.Customers.LiquidityRules.MaxOrderSize.Check.Disabled.TradeTypes";
    String IDC_IS_BROKER_CUSTOMERS_LIQUIDITY_RULES_MAX_ORDER_SIZE_CHECK_DISABLED_TRADETYPES_PREFIX = IDC_IS_BROKER_CUSTOMERS_LIQUIDITY_RULES_MAX_ORDER_SIZE_CHECK_DISABLED_TRADETYPES + ".";

    String IDC_IS_PPSPOT_SPREAD_FOR_OUTRIGHT_DISABLED = "IDC.IS.PPSpot.Spread.For.Outright.Disabled.";
    String IDC_IS_PPSPOT_SPREAD_FOR_SWAP_DISABLED = "IDC.IS.PPSpot.Spread.For.Swap.Disabled.";
    String IDC_IS_ACTIVE_RFS_ROADCAST_ENABLED = "IDC.IS.Active.RFS.Broadcast.Enabled";
    String IDC_IS_ACTIVE_RFS_ROADCAST_ENABLED_PREFIX = IDC_IS_ACTIVE_RFS_ROADCAST_ENABLED + ".";
    String IDC_IS_MANUAL_RFQ_BROADCAST_ENABLED = "IDC.IS.Manual.RFQ.Broadcast.Enabled";
    String IDC_IS_MANUAL_RFQ_BROADCAST_ENABLED_PREFIX = IDC_IS_MANUAL_RFQ_BROADCAST_ENABLED + ".";
    String IDC_IS_MANUAL_RFQ_BROADCAST_VIA_MULTICAST_ENABLED = "IDC.IS.Manual.RFQ.Broadcast.Via.Multicast.Enabled";
    String IDC_IS_MANUAL_RFQ_BROADCAST_VIA_MULTICAST_ENABLED_PREFIX = IDC_IS_MANUAL_RFQ_BROADCAST_VIA_MULTICAST_ENABLED + ".";
    String IDC_IS_ACTIVE_RFS_ROADCAST_RATE_PULISH_INTERVAL = "IDC.IS.Active.RFS.Broadcast.Rate.Publish.IntervalInMills";
    String IDC_IS_ACTIVE_RFS_ROADCAST_RATE_PULISH_INTERVAL_PREFIX = IDC_IS_ACTIVE_RFS_ROADCAST_RATE_PULISH_INTERVAL + ".";
    String IDC_IS_ACTIVE_RFS_WAITTIME_NO_PRICE_ALERT = "IDC.IS.Active.RFS.Broadcast.WaitTime.NoPrice.AlertInSec";
    String IDC_IS_ACTIVE_RFS_WAITTIME_NO_PRICE_ALERT_PREFIX = IDC_IS_ACTIVE_RFS_WAITTIME_NO_PRICE_ALERT + ".";
    String IDC_IS_BROKERS_WHO_USE_OWN_MDS_FOR_PNL_CALC = "IDC.IS.BROKERS.WHO.USE.OWN.MDS.FOR.PNL.CALC";
    String IDC_IS_SEND_POSITION_UPDATE_WITHOUT_UNREALIZED_PNL = "IDC.IS.SEND.POSITION.UPDATE.WITHOUT.UNREALIZED.PNL";
    String IDC_IS_MULTI_BOOK_STAMPING_ON_ESP_TRADE_ENABLED = "IDC.IS.MultiBook.Stamping.On.ESP.Trade.Enabled";
    String IDC_IS_MULTI_BOOK_STAMPING_ON_ESP_TRADE_ENABLED_PREFIX = IDC_IS_MULTI_BOOK_STAMPING_ON_ESP_TRADE_ENABLED + ".";
    String IDC_IS_MULTI_BOOK_STAMPING_ON_RFS_TRADE_ENABLED = "IDC.IS.MultiBook.Stamping.On.RFS.Trade.Enabled";
    String IDC_IS_MULTI_BOOK_STAMPING_ON_RFS_TRADE_ENABLED_PREFIX = IDC_IS_MULTI_BOOK_STAMPING_ON_RFS_TRADE_ENABLED + ".";
    String IDC_IS_ACCEPTANCE_HOOK_VERSION_PREFIX = "IDC.IS.ACCEPTANCE.HOOK.VERSION.";
    String IDC_IS_SALESDEALER_HOME_CURRENCY = "IDC.SalesDealer.PnL.HomeCurrency";
    String IDC_IS_SALESDEALER_HOME_CURRENCY_PREFIX = IDC_IS_SALESDEALER_HOME_CURRENCY + ".";
    String IDC_IS_TRADE_RESPONSE_IMTP_MESSAGE_PERSISTENCE_ENABLED = "IDC.IS.TradeResponse.IMTP.Message.Persistence.Enabled";
    String IDC_IS_TRADING_RESTRICTION_MAX_VALUE = "Idc.IS.TradingRestriction.MaxValue";
    double IDC_IS_TRADING_RESTRICTION_MAX_VALUE_DEFAULT = 2000000000.0;
    String IDC_IS_EXTRAPOLATION_MDS_BROKER_ENABLED = "IDC.IS.Extrapolation.MDS.Broker.Enabled";
    String IDC_IS_EXTRAPOLATION_MDS_BROKER_ENABLED_PREFIX = IDC_IS_EXTRAPOLATION_MDS_BROKER_ENABLED +".";

    String IDC_LP_ENABLED_FOR_CALCULATEDREPORT = "Idc.LP.Enabled.For.CalculatedReport";
    String IDC_LP_ENABLED_FOR_CALCULATEDREPORT_PREFIX = IDC_LP_ENABLED_FOR_CALCULATEDREPORT +".";

    String IDC_IS_ADMIN_PRICEPROVISION_GLOBAL_ADDITIONAL_SPREAD_MARKUP_PERCENTAGE_PREFIX = "IDC.ADMIN.PRICEPROVISION.Global.Additional.Spread.Markup.Percentage.";
    String IDC_IS_ADMIN_PRICEPROVISION_GLOBAL_ADDITIONAL_SPREAD_CURRENCIES = "IDC.ADMIN.PRICEPROVISION.Global.Additional.Spread.Markup.Currencies";
    String IDC_IS_EXTERNAL_CREDIT_CHECK_TRADE_TYPES = "IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.TradeTypes";
    String IDC_IS_EXTERNAL_CREDIT_CHECK_TENORS = "IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.Tenors";
    String IDC_IS_EXTERNAL_CREDIT_CHECK_MESSAGE_FORMAT = "IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.FORMAT";
    String IDC_IS_EXTERNAL_CREDIT_CHECK_MESSAGE_FORMAT_DEFAULT = "XML";
    String IDC_IS_EXTERNAL_CREDIT_CHECK_SEQUENCE = "IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.Sequence";
    String IDC_IS_EXTERNAL_CREDIT_CHECK_SEQUENCE_DEFAULT = "INTERNAL-AND-EXTERNAL";
    String IDC_IS_EXTERNAL_CREDIT_CHECK_IGNORE_INTERNAL_CREDIT = "IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.IGNORE.INTERNAL.CREDIT.ENABLED";
    String IDC_IS_EXTERNAL_CREDIT_CHECK_IGNORE_INTERNAL_CREDIT_PREFIX = IDC_IS_EXTERNAL_CREDIT_CHECK_IGNORE_INTERNAL_CREDIT + ".";
    String EXTERNAL_CREDIT_TRADE_TYPES_OR_TENORS_VALUE_ALL = "ALL";
    String IDC_IS_RATE_BAND_FILTER_ENABLED = "IDC.IS.RATE.BAND.FILTER.ENABLED";
    String IDC_IS_RATE_BAND_FILTER_ENABLED_PREFIX = IDC_IS_RATE_BAND_FILTER_ENABLED + ".";

    boolean isMultiPbSerializationEnabled();

    long getTimetoliveRFQ();


    String getBroker();

    String getLogCategory();

    int getHeartbeatInterval();

    String getISSystemUser();

    String getQuoteESPTemplate();

    String getQuoteSPOTRFSTemplate();

    String getQuoteFWDRFSTemplate();

    String getQuoteSWAPRFSTemplate();

    String getQuoteFFRFSTemplate();


    String getQuoteSPOTRFSNewTemplate();

    String getQuoteFWDRFSNewTemplate();

    String getQuoteSWAPRFSNewTemplate();

    String getQuoteFFRFSNewTemplate();


    String getQuoteFXIESPTemplate();

    String getTradeFXIESPTemplate();

    String getResponseESPTemplate();

    String getNotificationMSGTemplate();

    String getTradeExecutionDateTimeFormat();

    boolean isIncrementalData();

    String getCSDKResponseESPTemplate();

    String getServerName();

    String getClientLogCategory();

    long getProviderStatusMonitorInterval();

    int getUserStatusMonitorInterval();

    Collection getMonitoredCcyPairs();

    Map getOrgToMDSMap();

    int getThresholdMillisForStaleness();

    int getVerificationTimeoutInterval();

    boolean isVerificationTimeoutEnabled();

    String getServerId();

    boolean getAutoCancelEnabledAtAdaptor();

    int getISStatusMonitorInterval();

    String getMultiTierOrgList();

    boolean getTradingLimitValidationDisabled();

    int getISToAdaptorHttpReadTimeout();

    String getServiceNameQualifier();

    boolean isClientCrossingEnabled( Organization org );

    boolean isClientCrossingEnabled( String orgShortName );

    boolean isNotificationMessageCompactionEnabled();

    boolean isMessageEncodingEnabled();

    String getProviderGroup( String providerName );

    void setProviderGroups();

    Map getProviderGroupMap();

    String getQueryTradeResponseESP_1Template();

    /**
     * Value returned by this method will be set on <channel> element of Simple New STP message.
     * If not set will return FXI.
     *
     * @param requestChannel request channel
     * @return trade channel
     */
    String getTradeChannel( String requestChannel );

    /**
     * Returns the acceptance tolerance amount in the power of 10s by which
     * validation of the trading limit is done when trade is  accepted.
     * The default value of 4 will result in acceptance of trade amount + 10k. To disable this tolerance, use
     * negative values such as -4 which means 0.0001 will be the tolerance allowed during acceptance.
     *
     * @return tolerance factor
     */
    int getPriceProvisionAcceptanceToleranceFactor();

    /**
     * Value returned by this method will be name of ons of the external request id of Simple New STP message.
     * If not set will return request channel.
     *
     * @param requestChannel request channel
     * @return display name
     */
    String getRequestChannelDisplayName( String requestChannel );

    boolean isStreamDeployment();

    int getRFSRequestExpiryTime( Organization org );
    int getUIRFSRequestExpiryTime( Organization org );

    String getRFSTopic();

    String getMaxTenor();

    String getMaxTenor( String orgName );

    Tenor getMaxTenorObject();

    Tenor getMaxTenorObject( String orgName );

    boolean isInverseSupported();

    boolean isRFSStatisticsEnabled();

    boolean isRFSClientSDKv1009Supported();

    boolean isPriceBasedAcceptanceEnabled();

    String getDefaultQuoteConvention();

    long getRateLoggingDelay();

    int getDealQueryThreshold();

    boolean isUseProviderTradingPartyDefaultLE( Organization lp );

    /**
     * @param lp LP Name
     * @param fi FI Name
     * @return true if mismatched swap is supported for the lp fi combination
     */
    boolean isMismatchedSwapsSupported( Organization lp, Organization fi );

    /**
     * If enabled, mismatched swap property at lp or lp-fi combinations are considered enabled unless otherwise specified
     * as long as default level it is enabled. It removes the need to set the property at each LP-FI level.
     * @return mismatched swaps enabled
     */
    boolean isMismatchedSwapsByDefaultEnabled();

    boolean isBA_RFS_ESPMDS_ESPSpreading_Enabled(String brokerSN, String ccyPair);

    String getDefaultMDSSubscriberUser();

    String getDefaultMDSProvider();

    int getProviderValueDateCheckType();

    boolean isOutOfSequenceQuotesCheckEnabled();

    boolean isTradeValueDateVerificationEnabled();

    boolean isTradeFixingDateVerificationEnabled();

    String getAdminPortalUrlForOrg( String orgShortName );

    String getAdminPortalUrlForBrand( String brandName );

    int getQuotePoolMaxIdleSize();

    /**
     * This property determines whether credit update amount workflow is enabled for FXI prime.
     *
     * @return enabled
     */
    boolean isFXIPrimeUpdateCreditEnabled();

    boolean useOldBrokerPreAccept();

    boolean isLPPBCcyPairValidationEnabled();

    /**
     * this will enable BA workflow functor to use VWAP methodology.
     *
     * @return
     */
    boolean isBAVWAPEnabled();

    /**
     * The max size of multicast packets.
     *
     * @return
     */
    int getMcastMaxPacketSize();

    int getMcastBooksPerPort();

    int getQuoteCompactionLevel();

    int getMcastBookFutileSweepsBeforeIdle();

    int getMcastBookIdleIntervalInMicros();

    int getBucketManagerThreadPoolMaxSize();

    int getBucketManagerThreadPoolMinSize();

    boolean isThreadPoolBasedBucketManagerEnabled();

    long getBucketManagerThreadPoolKeepAlive();

    String getMcastBooksProviderAssignment();

    /**
     * Returns whether Spread Adjustment is enabled for  org
     *
     * @param orgname
     * @return
     */
    boolean isBAVwapUserSpreadAdjustedEnabled( String orgname );

    /**
     * Returns Vwap Spread Percentage for given org if specified else returns default value
     *
     * @param orgName
     * @return
     */
    double getBAVwapSpreadPercentage( String orgName );

    /**
     * Returns Vwap Giveback Percentage for given org if specified else returns default value
     *
     * @param orgName
     * @return
     */
    double getBAVwapGivebackPercentage( String orgName );

    /**
     * @return true if the validation for amount set in the TradeVerify message is enabled for Single Fill Providers
     */
    boolean isSingleFillTrdVerifyAmountValidationEnabled();

    /**
     * Return delimiter arrays which needs to be replaced in rejection reason.
     *
     * @return
     */
    public Tuple<String[], String[]> getRejectionReasonDelimiters();

    /**
     * Return trade channle description
     *
     * @return
     */
    String getTradeChannelDescription( String key );

    /**
     * Return all the classes required to be as ReadOnly for Toplink.
     *
     * @return
     */
    List getAdminReadonlyClasses();

    int getISTradeCacheMaxSize();

    /**
     * Returns a collection of reference data classes which are used in the trade workflow.
     *
     * @return classes
     */
    Collection<Class> getTradeWorkflowReferenceDataEntityClasses();

    /**
     * Returns whether the specified class is used in trade workflow as a reference data entity class. For example LegalEntityC
     *
     * @param aClass class
     * @return reference data entity class
     */
    boolean isTradeWorkflowReferenceDataEntityClass( Class aClass );

    /**
     * Returns whether reference data warmup is done on currency pair rate subscription. This reference data warmup will be limited to the
     * parties of the subscription such as requesting FI org and liquidity provider organization.
     *
     * @return enabled
     */
    boolean isReferenceDataWarmupOnSubscriptionEnabled();

    /**
     * Returns the level at which the customer organization's reference data will be warmed up. If the value is zero, no warm-up will be done.
     * If the value is one, then basic reference data will be initialized. If the value is 2, then deeper reference data warm-up will be done.
     *
     * @return reference data warmup level
     */
    public int getCustomersReferenceDataWarmupLevel();

    /**
     * Returns whether the reference data warmup on subscription if enabled, should be done asynchronously.
     *
     * @return asynchronous warm-up
     */
    boolean isAsyncReferenceDataWarmupOnSubscription();

    /**
     * for back to back BA, use customer limit price to send to the BA2.
     * refer http://bugzilla.integral.com/show_bug.cgi?id=45189
     *
     * @return
     */
    boolean isUseLimitPriceBA();

    /**
     * Returns the minimum amount need for the trade for a customer and currency pair. If the trade amount is below this value, then trade will be declined.
     *
     * @param org     requesting organization
     * @param ccyPair currency pair
     * @return minimum trade amount
     */
    Double getMinimumTradeSize( Organization org, CurrencyPair ccyPair );

    /**
     * Returns the minimum amount need for the trade for a customer and currency pair. If the trade amount is below this value, then trade will be declined.
     *
     * @param orgShortName shortname of the requesting/customer org
     * @param ccyPair      currency pair
     * @return
     */
    Double getMinimumTradeSize( String orgShortName, String ccyPair );

    /**
     * Returns the maximum amount which can be allowed for the order for a customer and currency pair. If the order amount is above this value, then order will be declined.
     *
     * @param org     requesting organization
     * @param ccyPair currency pair
     * @return minimum trade amount
     */
    Double getMaximumOrderSize( Organization org, CurrencyPair ccyPair );

    /**
     * Returns the maximum amount allowed for an order for an organization and currency pair
     *
     * @param orgShortName shortName of the organization
     * @param ccyPair      currency pair
     * @return maximum allowed order size.
     */
    Double getMaximumOrderSize( String orgShortName, String ccyPair );

    String getMinimumTradeSizeConfigurationForOrg( Organization org );

    String getMaximumOrderSizeConfigurationForOrg( Organization org );

    boolean isPBPriceStreamEnabled( String anonymousLPName );

    String getBrokerShortName();

    /**
     * returns ExclusionListRandomizer Pattern
     */
    int[] getExclusionListRandomizerPattern( String orgName );

    VirtualServer getVirtualServer();

    boolean isOBOWorkflowEnabled( User user );

    String getOBOWorkflowSpreadModelESP( Organization salesDealerOrg );

    String getOBOWorkflowSpreadModelRFS( Organization salesDealerOrg );

    boolean isECNSubmitOrdersSupported( String orgName );

    long getECNSubmitOrdersMinExpiryTime( String orgName, String lpName );

    boolean isMRPoolEnabled();

    /**
     * This property enables the new rate calculator workflow for Sales dealer workflow.
     * todo: need to remove this post 4.1
     *
     * @return
     */
    @Deprecated
    boolean isNewRateCalculatorEnabled();

    boolean isMaskedLPUserAsMakerUser( String maskedLPOrgName );

    boolean isRFSSpreadsEnabled( Organization lp );

    boolean isMRFQChannelHidden();

    boolean isPersistRFSRatesEnabled( Organization org );

    String getVirtualServerForConfigIssueEmail();

    String getEmailAddressForMultihostConfigIssueNotification();

    boolean isLPConfigurationFactoryEnabled();

    boolean isMongoInitEnabled();

    boolean isSTPSpreadDirectionEnabled( Organization lpOrg );

    boolean isDistributedAdaptorStreamMappingFromAdmin( String distributedAdaptorOrgName );

    boolean isNewAccountIDLookupEnabled();
    
    boolean isNewAccountIDLookupEnabled(Organization adapterOrg);

    /**
     * Returns the number of threads configured to handle Provider Status(Heartbeat) messages.
     *
     * @return numThreads.
     */
    int getNumProviderStatusHandlerThreads();

    boolean isProviderStatusHandlerPoolDynamic();

    /**
     * Returns the max number of threads configured to handle Provider Status(Heartbeat) messages.
     *
     * @return numThreads. -1 if no value is set in the configuration.
     */
    int getMaxProviderStatusHandlerThreads();

     /**
     * Returns the number of threads configured to handle Provider Status(Heartbeat) messages.
     *
     * @return numThreads.
     */
    int getProviderStatusHandlerTaskQueueSize();

    /**
     * Returns whether customer orders need to be excluded from the chief dealer query and notifications.
     *
     * @return customer order excluded
     */
    boolean isCustomerOrderExcluded();

    /**
     * Returns whether customer orders need to be excluded from chief dealer query and  notification.
     *
     * @param org organization
     * @return excluding customer orders
     */
    boolean isCustomerOrderExcluded( Organization org );

    /**
     * Returns whether trade response's state check needs to be done.
     *
     * @return trade response state check needed
     */
    boolean isTradeResponseStateCheckEnabled();

    /**
     * Returns whether quote with size zero is active
     *
     * @return zero quote size active
     */
    boolean isZeroQuoteSizeActive();

    /**
     * Return whether increase in trading limit is applied on unbalanced tiers, i.e on the side with lesser tiers.
     * @return
     */
    boolean isApplyTradingLimitIncrease();

    /**
     * Checks if the Liquidity Provisioning is enabled for the given org or not.
     *
     * @param org FI Organization that needs to be checked. If null, global value is checked.
     * @return true if Liquidity Provisioning is enabled, false otherwise.
     */
    boolean isLiquidityProvisioningEnabled( String org );

    /**
     * Returns whether JMS trade listeners are set up after the server startup.
     *
     * @return enabled
     */
    boolean isPostStartupTradeJMSListenerSetup();

    /**
     * Returns the time in seconds for the Trade request's time-out
     *
     * @return trade request time out
     */
    public int getISToAdaptorTradeRequestTimeout();

    String getMETPublicURL();

    String getMETPublicURLSEF();

    String getMETPublicURL(String orgName);

    String getMETPublicURLSEF(String orgName);

    /**
     * Checks whether "Calculate mid rate" feature is enabled for this provider
     *
     * @param orgShortName : shortName of the organization to check this feature for
     * @return Returns true if
     * <ul>
     * 	<li> this feature is enabled for the organization
     *  <li> If this feature is not defined for this org and if this org is a mask LP, then checks whether the feature is enabled for the real LP
     *  <li> If the feature is not defined for the real LP also, then checks whether it is enabled globally.
     * </ul>
     */
    boolean isMidRateCalcProviderEnabled( String orgShortName );
    boolean isMidRateCalcCustomerEnabled( String orgShortName );
    boolean isMidRateCalcBrokerEnabled( String orgShortName );
    boolean isTwoWayConversionBrokerEnabled( String orgShortName );

    boolean isMidRateCalcProviderEnabled();
    boolean isMidRateCalcCustomerEnabled();
    boolean isMidRateCalcBrokerEnabled();
    boolean isMidRateCalcBrokerUseLpRawRateEnabled(String brokerOrg);
    boolean isMidRateCalcEspUnskewedEnabled( String brokerOrg );
    boolean isTwoWayConversionBrokerEnabled();

    boolean isMidRateSendCustomerEnabled( String orgShortName );
    boolean isMidRateSendProviderEnabled( String orgShortName );
    boolean isMidRateSendCustomerEnabled();
    boolean isMidRateSendProviderEnabled();

    boolean isMidRateEmailCustomerEnabled(String orgShortName);
    boolean isMidRateEmailProviderEnabled( String orgShortName );

    /**
     * Returns whether periodic reference data warm-up is enabled or not.
     * @return periodic reference data warm-up is enabled.
     */
    boolean isPeriodicReferenceDataWarmupEnabled();

    /**
     * Return whether reference data warm-up is enabled for immediate customers of the FIs configured in the server.
     *
     * @return immediate customer reference data warm-up enabled
     */
    boolean isImmediateCustomerReferenceDataWarmupEnabled();

    /**
     * Returns the interval period in milliseconds of periodic reference data warm-up.
     *
     * @return period
     */
    long getReferenceDataWarmupPeriod();

    /**
     * Returns the interval period in milliseconds of periodic reference data full warm-up. Full warm-up will go through
     * all the provisioned takers, taker customers and lps.
     *
     * @return period
     */
    long getReferenceDataFullWarmupPeriod();


    /**
     * Returns the check for enabling out right display orders.
     * @return
     */
    boolean isOutrightDisplayOrderEnabled(String orgName);


    /**
     * Returns the interval period in milliseconds for removing the cache entries in ISPersistenceServiceCache.
     *
     * @return period
     */
    long getISPersistenceCacheEntryTimeToLivePeriod();

    /**
     * Returns whether the delayed removal of IS persistence cache is enabled.
     *
     * @return delayed cache removal is enabled
     */
    boolean isISPersistenceCacheEntryDelayedRemovalEnabled();

    /**
     * Returns whether to apply new way of spreading on uneven swap.
     * @return
     */
    boolean isApplyNewUnevenSwapSpreading();

    /**
     * Returns whether multi-app messages are enabled from dealing database transactions.
     * @return multi-app enabled
     */
    boolean isDealingTransactionMultiAppUpdateEnabled();

    /**
     * Returns whether multi-app message sending is enabled for the event specified.
     *
     * @param event event
     * @return multi-app message sending enabled
     */
    boolean isDealingTransactionMultiAppUpdateEnabled( String event );

    /**
     * A timeToLive stored on objects to support mongo ttl collections
     * @return
     */
    public int getDealOrderExpirationDays();


    boolean isTradeTypeSupportedForStream();

    boolean isTradeTypeSupportedForStream( Organization org );

    public int getSpacesDealCacheNumContainer();

    boolean isMultiLegRFSCreditCheckOnSubscriptionEnabled();

    boolean isRFSProviderCcyPairGroupValidationEnabled( Organization org );

    long getRoundingFactor(String ccyPair,String orgName);


    boolean isMarkRateActiveInactiveOnCcyPairAddRemove();

    boolean isUpdateValueDateCache();

    /**
     * This is a temporary method and will be removed when DO functionality is completed on spaces.
     * @return true if Display Order Functionality is enabled on spaces.
     */
    boolean isSpacesDOEnabled();

    /**
     * Returns the list of ccy pairs in specified order.
     *
     * @return
     */
    String getFXIDirectCcyPairsOrder();

    boolean isSpacesTraianaSTPEnabled();

    boolean isQuotesPoolEnabled();

    boolean isPBtoLPWorkflowCcyPairsEnabled(Organization pbOrg, Organization lpOrg);

    boolean isPBLPTradingChannelEnabled(Organization pbOrg,Organization lpOrg);

    boolean isAsyncInternalRejectionEnabled();

    public char[] getExcludedSpecialCharcter();

    String[] getOrderEmailTIFExclusion( Organization org );

    /**
     * Returns the threshold of expiry time in milliseconds equal of greater of which will make the order eligible for
     * sending order emails when it is part of included list of time in forces.
     * @return expiration time thresholds in milliseconds
     */
    long getOrderEmailGTDExpirationTimeThreshold( Organization org );

    public int getSpacesIDServiceMaxConnectionSize();

    boolean isUseLPStreamLE();

    public int getMsRetryCount();

    /**
     * Return the name of the application for which the transaction ID is generated.
     */
    String getTransactionIDAppName();

	boolean isDirectedOrderCheckInBAMessageBuilderEnabled();

    /**
     *
     * @return true if the circuit breaker for Latency between a match and its corresponding TradeRequest Dispatch is enabled.
     *
     */
    boolean isTradeRequestLatencyCircuitBreakerEnabled();

    /**
     *
     * @return the threshold in milliseconds that is used for Trade Request Latency Circuit Breaker.
     */
    long getTradeRequestLatencyCircuitBreakerThreshold();

	boolean isQDIInitializationCheckEnabled();

    String getRoutingKeyPrefix(String orgName);

    String getDefaultRoutingKeyPrefix();

    public int getISMessageSerializerBufferSize();

	boolean isSendValueDateNotifications();

	/**
	 * if true then list of organizations derived from org - virtual server mapping will be filterd for ECN,Prime Broker and Masked LP and Enternal Provider.
	 * This is applicable only when Auto Deployment is true.
	 * @return
	 */
	boolean isFilterAutoDeployTakers();

	/**
	 * if true then OAHeartbeatSender will send ProviderStatus message for Masked LPs which are mapped to OA's virtual server .
	 * @return
	 */
	boolean isSendOAHeartbeatForMaskLP();

	/**
	 * if true then FIs will be deployed to there own OA as LP to facilitate intra floor trading.
	 * @return
	 */
	boolean isDeployTakersForIntraFloor();

    /**
     * Returns the Cache sizing for MarketSnapshot caching on Notification Servers
     * @return
     */
    String getMSCacheConfig();

	/**
	 * If true send trade mail for RFS prime broker cover trade
	 */
	boolean isPrimeBrokerRFSCoverTradeEmailEnabled( String orgShortName );

    boolean isUsePriceRegenerationServiceInOrderMatch();

	boolean isFMASubscriptionsForNonCustomersEnabled(Organization brokerOrg, Organization customerOrg);

	boolean isGridBasedABRoutingEnabled( String orgShortName );

    /**
     * Returns whether services should be initialized on this server
     * @return
     */
    boolean isServicesEnabled();

    String getGridMidRatePriceSource(String priceSrcName);

    boolean isSubscriptionAccountIdFallBack();

    boolean isRDSClientEnabled();

    boolean validateRFSRequestAmount();

    String getGlobalJndiHeartBeatTopicNames();

    String getInternalAdminURL();

	boolean isRejectRFSOnInvalidChannel();

    boolean isValueDateCheckEnabled();

    boolean isABRouterEnabled();

    boolean setAccountOnOrder();

    boolean isAccountWarmUpEnabled();

    boolean isClobRoutingEnabled(String org);

    boolean isClobRoutingForPegAtMidOrdersEnabled(String org);

    boolean isProviderPriorityEnabled(String orgShortName);

    String getCptysEnabledForAggregatedFill(String fiOrg);

    RateDistributionStrategyTypes getRateDistributionStrategy();

    boolean isQuoteCacheOptimizationEnabled();

    boolean isIndexBasedReferenceCounterEnabled();

    double getMaxDropPercentageForTakeOver();

    double getDropPercentageThreshold();

    long getNumberOfDropsThreshold();

    boolean isRateSnapShotsEnabled();

    boolean isRateStreamAllocationMonitorEnabled();

    int getRateProcessorPoolSize();

    Collection<Organization> getProvisionedOrgs();

    boolean isCircuitBreakerEnabled();

    double getRiskNetMarketDataSpreads(String venueOrg, String customerOrg, String ccyPair);

    double getVenueMarketDataSpreads(String customerOrg, String venueOrg, String ccyPair);

    boolean isStreamGroupingBasedProviderPriorityEnabled(String fiOrg);

    StreamCategory getDefaultStreamCategory();

    boolean isStreamCategoryEnabledForMetals();

    List<StreamCategory> getStreamCategoriesForOrg(String fiOrg, String ccyPair, Double amount);

    boolean usePercentageMarketRange( String orgShortName );

    boolean isTradeMessageSonicRoute();

    String getFixingNames();

    public boolean isMarketDataFeedServer();

    public boolean isNewProvisioningService();

    public boolean isSuspendEnabledForVenueRequests();

    public boolean isSuspendEnabledForVenueRequests(String fiOrg);

    public boolean isHostedAdaptorSubscriptionEnabled();

    public int cancelVenueRequestSynchronousRetryCount();

    boolean isVenueAccessForBrokerCustomersEnabled(String brokerOrg);

    boolean isSubscribeRFSRealAndMask();
    /**
     * send RFS subscription request to real and all mask LPs
     * @param org
     * @return
     */
    boolean isSubscribeRFSRealAndMask(String org);

	boolean isApplyFeeSelfMatchEnabled(String org);

	long getBenchMarkPriceSourceStaleInterval();

	boolean isBenchMarkServiceEnabled();

	int getBenchMarkRateBufferSize();
    boolean isASyncNotificationOfProviderStatusEnabled();

    boolean isFIXSessionRDSPersistEnabled();

    public boolean isBrokerAdaptorAutoSubscriptionEnabled(String brokerName);

    public boolean isNewSubscriptionEnabled(String brokerName);

    public boolean isNewSubscriptionForceSubscribeEnabled(String brokerName);

	public boolean isRFSOptimized();

    public boolean isPendingSubscriptionProcessingSync();

    double getVenueBrokerForCustomerTradeSpread(String brokerOrg);

    String getVenueBrokerForCustomerTradeSpreadStrategy( String brokerOrg );

	long getSequenceIdJumpByFactor();

    boolean refreshPriceProvisionData();

    boolean isPendingSubscriptionsCheckEnabled();

    int getPendingSubscriptionsCheckInterval();

    double getVwapPriceProtectionRangeInBasisPoints();

	boolean isVenueAccessForPrimeBrokerCustomersEnabled(String pbOrgShortName);

    public double getMinOrderSizeForFullAmount(String adaptorName);

    boolean skipLiquidityRulesInMMVirtualServer(String customerOrg);

    boolean useAcceptedDPConventionForRFSAceptancePaymentParams();

    int getMaxConcurrentRFSRequests();

    boolean isMaxConcurrentRFSEnabled();

    String getPortfolioCacheConfig();

    boolean isSDOrgSameAsPBOrgAllowed();

    boolean createCDQMakerOrderOnSubmission(String broker);

    String getCryptoExchangeName();

    boolean isProvisionedDisplayLimits(String fi,String realLP);

	boolean isLogFullGridAPIMessage(String handlerId);

    QuoteAmountRoundingMode getQuoteAmountRoundingMode(String key);

    boolean useCoveredOrgSubAccountIdForEspRfs(String brokerName);

    boolean isSendValidationFailuresToManualEnabled(String broker);
    boolean isCreditValidationFaliureZeroForwardPointHandlingEnabled(String broker);

    boolean isExclusiveFullAmountMatchingCheckEnabled(OrderRequest.Type type);

    long getExclusiveFullAmountMatchingCheckMaskForExecutionFlags();

    boolean isOldPOSEnabled();

    boolean isExternalClientTagEnabled(String custShortName, String lpShortName);

    boolean isOptimizedJsonFactoryEnabled();

    /* Populate SpotRate in RFS Subscription request*/
    boolean isIncludeSpotRateInRFSSubscriptionRequest(String orgShortName);

    boolean useCptyVisibilityTypeForOcx2DealOrder();

	boolean isSwapSpreadFeatureEnabledCheckMap( Organization org );
    boolean isIgnoreQuoteCancelOnRequestAcceptance();

    boolean isOAWarmupWorkerEnabled();

    boolean isFIXClientWarmupEnabled();
	
	boolean isBPSMidRateFeatureEnabled(String brokerName);

	boolean isCircuitBreakerRulesPreloadEnabled();

	boolean isBrokerCustomersLiquidityRulesMaxOrderSizeCheckDisabled( Organization org, String tradeClsf );

    boolean useAccountIdAsExternalClientTag(String custOrg);

    boolean isNettingEnabledForStrategyOrders(String orgShortName);

    boolean isPersistNettedTradesOnNetTrade(String orgShortName);

    boolean isPPSpotSpreadForOutrightDisabledForOrg(Organization custOrg, Organization brokerOrg );

    boolean isPPSpotSpreadForSwapDisabledForOrg(Organization custOrg, Organization brokerOrg );

    boolean isActiveRFSBroadcastEnabledForOrg(String orgShortName);

    boolean isManualRFQBroadcastEnabledForOrg(String orgShortName);
    boolean isSendMRFQViaMulticastEnabledForOrg(String orgShortName);

    int getActiveRFSBroadcastRatePublishIntervalInMills(String orgShortName);

    int getActiveRFSBroadcastWaitTimeNoPriceAlertInSec(String orgShortName);
    /**
     * All the brokers that use own MDS for PNL calculation
     * @return
     */
    Set<String> getBrokersWhoUseOwnMDSForPnlCalc();
    /**
     * send position update even unrealized pnl could not be computed.
     * @return
     */
    boolean sendPositionUpdateWithoutUnrealizedPnl();

    int getAcceptanceHookVersion(String fromOrg, String toOrg);

    String getSalesdealerHomeCurrency(String orgName);

    boolean isSendStopOrderAsMarketEnabled(String toOrg, String fromOrg);

    boolean isTradeResponseImtpMessagePersistenceEnabled();

    double getTradingRestrictionMaxValue();

    boolean isOrgEnabledForExtrapolationMDS(String orgName);
    public boolean isLPEnabledForCalculatedReport(String fiOrg);

    boolean isMultiBookStampingOnESPTradeEnabledForOrg(String orgName);

    boolean isMultiBookStampingOnRFSTradeEnabledForOrg(String orgName);

    double getPriceProvisionAdditionalMarkupSpreadPercentageForOrg(String orgName);
    List<String> getPriceProvisionAdditionalMarkupSpreadCurrencies(String orgName);
    List<String> getExternalCreditCheckTradeTypes(String orgName);
    List<String> getExternalCreditCheckTenors(String orgName);
    String getExternalCreditCheckMessageFormat(String orgName);
    String getExternalCreditCheckSequence(String orgName);
    boolean getExternalCreditCheckIgnoreInternalCreditEnabled(String orgName);
    boolean isRateBandFilterEnabled(String orgName);
}
