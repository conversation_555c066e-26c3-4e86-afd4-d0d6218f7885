package com.integral.is.priceprovision.rules;

import java.math.BigDecimal;

import com.integral.math.MathUtil;
import com.integral.time.IdcDate;
import com.integral.util.MathUtilC;


/**
 * 
 * <AUTHOR> Development Corp.
 */
public class PipsSpreadRule extends DefaultSpreadRule 
{

	public PipsSpreadRule() {
		super(SpreadConstants.PIPS_CALC,SpreadConstants.PIPS);
	}
	
	@Override
	public double calculateGiveBackBidSpotRate(SpreadRuleParameter srp,double bidRate, double givebackBidRate) 
	{
		double fRate = givebackBidRate;
		boolean useInOrderMatching =  srp.isGivebackUtilizedOrderMatching();
		if( srp.isPreTradeSpreadEnabled())
		{
			double fSpread =useInOrderMatching? getBidSpreadWithImprovement(srp,bidRate):0;
			fRate = useInOrderMatching ? givebackBidRate-fSpread : bidRate;
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateGiveBackOfferSpotRate(givebackBidRate:" + givebackBidRate + ",Rate:" + bidRate+")", givebackBidRate, fRate, fSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			fRate = useInOrderMatching? MathUtilC.subtract(fRate, srp.getBidGiveBackSkew(fRate)): MathUtilC.subtract(fRate, srp.getBidSkew(fRate)); 
		}
		return fRate;		
	}
	
	@Override
	public double calculateGiveBackBidSpotRate(SpreadRuleParameter srp,double bidRate, double givebackBidRate, double amount) 
	{
		double fRate = givebackBidRate;
		boolean useInOrderMatching =  srp.isGivebackUtilizedOrderMatching();
		if( srp.isPreTradeSpreadEnabled())
		{
			double fSpread =useInOrderMatching? getBidSpreadWithImprovement(srp, bidRate, amount):0;
			fRate = useInOrderMatching ? givebackBidRate-fSpread : bidRate;
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateGiveBackOfferSpotRate(givebackBidRate:" + givebackBidRate + ",Rate:" + bidRate+")", amount, givebackBidRate, fRate, fSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			fRate = useInOrderMatching? MathUtilC.subtract(fRate, srp.getBidGiveBackSkew(fRate)): MathUtilC.subtract(fRate, srp.getBidSkew(fRate)); 
		}
		return fRate;		
	}

	@Override
	public double calculateGiveBackOfferSpotRate(SpreadRuleParameter srp,double offerRate, double givebackOfferRate) 
	{
		boolean useInOrderMatching =  srp.isGivebackUtilizedOrderMatching();
		double fRate = givebackOfferRate;
		if( srp.isPreTradeSpreadEnabled())
		{
			double fSpread = srp.isGivebackUtilizedOrderMatching()?getOfferSpreadWithImprovement(srp, offerRate):0;
			fRate=  useInOrderMatching ?givebackOfferRate + fSpread:offerRate;
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateGiveBackOfferSpotRate(GivebackOfferRate:" + givebackOfferRate + ",Rate:" + offerRate+")", givebackOfferRate, fRate, fSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			fRate = useInOrderMatching ? MathUtilC.add(fRate, srp.getOfferGiveBackSkew(offerRate)): MathUtilC.add(fRate, srp.getOfferSkew(offerRate)); 
		}
		return fRate;
	}
	
	@Override
	public double calculateGiveBackOfferSpotRate(SpreadRuleParameter srp,double offerRate, double givebackOfferRate, double amount) 
	{
		boolean useInOrderMatching =  srp.isGivebackUtilizedOrderMatching();
		double fRate = givebackOfferRate;
		if( srp.isPreTradeSpreadEnabled())
		{
			double fSpread = srp.isGivebackUtilizedOrderMatching()?getOfferSpreadWithImprovement(srp, offerRate, amount):0;
			fRate=  useInOrderMatching ?givebackOfferRate + fSpread:offerRate;
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateGiveBackOfferSpotRate(GivebackOfferRate:" + givebackOfferRate + ",Rate:" + offerRate+")", amount, givebackOfferRate, fRate, fSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			fRate = useInOrderMatching ? MathUtilC.add(fRate, srp.getOfferGiveBackSkew(offerRate)): MathUtilC.add(fRate, srp.getOfferSkew(offerRate)); 
		}
		return fRate;
	}

	@Override
	public double calculateBidSpotRate(SpreadRuleParameter srp,double spotRate, boolean applyGiveback)
	{
		double finalSpotRate = spotRate;
		if(  srp.isPreTradeSpreadEnabled())
		{
			double finalSpread = - srp.getRawBidSpread();
			if(applyGiveback)finalSpread = (srp.getRawBidSpread() - getBidSpreadWithImprovement(srp, spotRate));
			finalSpotRate = spotRate + finalSpread;
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateBidSpotRate(GiveBack:" + applyGiveback +")", spotRate, finalSpotRate, finalSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			finalSpotRate = applyGiveback? MathUtilC.subtract(finalSpotRate, srp.getBidGiveBackSkew(finalSpotRate)): MathUtilC.subtract(finalSpotRate, srp.getBidSkew(finalSpotRate)); 
		}
		return finalSpotRate;
	}
	

	@Override
	public double calculateBidSpotRate(SpreadRuleParameter srp,double spotRate, boolean applyGiveback, double amount)
	{
		double finalSpotRate = spotRate;
		if(  srp.isPreTradeSpreadEnabled())
		{
			double finalSpread = - srp.getRawBidSpread(amount);
			if(applyGiveback)finalSpread = (srp.getRawBidSpread(amount) - getBidSpreadWithImprovement(srp, spotRate, amount));
			finalSpotRate = spotRate + finalSpread;
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateBidSpotRate(GiveBack:" + applyGiveback +")", amount, spotRate, finalSpotRate, finalSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			finalSpotRate = applyGiveback? MathUtilC.subtract(finalSpotRate, srp.getBidGiveBackSkew(finalSpotRate)): MathUtilC.subtract(finalSpotRate, srp.getBidSkew(finalSpotRate)); 
		}
		return finalSpotRate;
	}

	@Override
	public double calculateOfferSpotRate(SpreadRuleParameter srp, double spotRate,boolean applyGiveback) 
	{
		double finalSpotRate = spotRate;
		if( srp.isPreTradeSpreadEnabled())
		{
			double finalSpread =  srp.getRawOfferSpread();
			if(applyGiveback)finalSpread = - (srp.getRawOfferSpread() - getOfferSpreadWithImprovement(srp, spotRate));
			finalSpotRate = spotRate + finalSpread;
			
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateOfferSpotRate(GiveBack:" + applyGiveback +")", spotRate, finalSpotRate, finalSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			finalSpotRate = applyGiveback ? MathUtilC.add(finalSpotRate, srp.getOfferGiveBackSkew(finalSpotRate)): MathUtilC.add(finalSpotRate, srp.getOfferSkew(finalSpotRate)); 
		}
		return finalSpotRate;
	}
	
	@Override
	public double calculateOfferSpotRate(SpreadRuleParameter srp, double spotRate, boolean applyGiveback, double amount) 
	{
		double finalSpotRate = spotRate;
		if( srp.isPreTradeSpreadEnabled())
		{
			double finalSpread =  srp.getRawOfferSpread(amount);
			if(applyGiveback)finalSpread = - (srp.getRawOfferSpread(amount) - getOfferSpreadWithImprovement(srp, spotRate, amount));
			finalSpotRate = spotRate + finalSpread;
			
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateOfferSpotRate(GiveBack:" + applyGiveback +")", amount, spotRate, finalSpotRate, finalSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			finalSpotRate = applyGiveback ? MathUtilC.add(finalSpotRate, srp.getOfferGiveBackSkew(finalSpotRate)): MathUtilC.add(finalSpotRate, srp.getOfferSkew(finalSpotRate)); 
		}
		return finalSpotRate;
	}
	
	@Override
	public double calculateBidSpotRate(SpreadRuleParameter srp,	double spotRate, double refRate, boolean applyGiveback,Double preCalcSpread) 
	{
		double finalSpotRate = spotRate;
		if(  srp.isPreTradeSpreadEnabled())
		{
			double finalSpread = - srp.getRawBidSpread();
			if(preCalcSpread != null){
				finalSpread = - preCalcSpread;
			}
			if(applyGiveback)finalSpread = (srp.getRawBidSpread() - getBidSpreadWithImprovement(srp, spotRate));
			finalSpotRate = spotRate + finalSpread;
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateBidSpotRate(GiveBack:" + applyGiveback +")", spotRate, finalSpotRate, finalSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			finalSpotRate = applyGiveback? MathUtilC.subtract(finalSpotRate, srp.getBidGiveBackSkew(finalSpotRate)): MathUtilC.subtract(finalSpotRate, srp.getBidSkew(finalSpotRate)); 
		}
		return finalSpotRate;
	}
	
	@Override
	public double calculateBidSpotRate(SpreadRuleParameter srp,	double spotRate, double refRate, boolean applyGiveback, Double preCalcSpread, double amount) 
	{
		double finalSpotRate = spotRate;
		if(  srp.isPreTradeSpreadEnabled())
		{
			double finalSpread = - srp.getRawBidSpread(amount);
			if(preCalcSpread != null){
				finalSpread = - preCalcSpread;
			}
			if(applyGiveback)finalSpread = (srp.getRawBidSpread(amount) - getBidSpreadWithImprovement(srp, spotRate, amount));
			finalSpotRate = spotRate + finalSpread;
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateBidSpotRate(GiveBack:" + applyGiveback +")", amount, spotRate, finalSpotRate, finalSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			finalSpotRate = applyGiveback? MathUtilC.subtract(finalSpotRate, srp.getBidGiveBackSkew(finalSpotRate)): MathUtilC.subtract(finalSpotRate, srp.getBidSkew(finalSpotRate)); 
		}
		return finalSpotRate;
	}

	@Override
	public double calculateOfferSpotRate(SpreadRuleParameter srp, double spotRate, double refRate, boolean applyGiveback,Double preCalcSpread) 
	{
		double finalSpotRate = spotRate;
		if( srp.isPreTradeSpreadEnabled())
		{
			double finalSpread =  srp.getRawOfferSpread();
			if(preCalcSpread != null){
				finalSpread = preCalcSpread;
			}
			if(applyGiveback)finalSpread = - (srp.getRawOfferSpread() - getOfferSpreadWithImprovement(srp, spotRate));
			finalSpotRate = spotRate + finalSpread;
			
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateOfferSpotRate(GiveBack:" + applyGiveback +")", spotRate, finalSpotRate, finalSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			finalSpotRate = applyGiveback ? MathUtilC.add(finalSpotRate, srp.getOfferGiveBackSkew(finalSpotRate)): MathUtilC.add(finalSpotRate, srp.getOfferSkew(finalSpotRate)); 
		}
		return finalSpotRate;
	}
	
	@Override
	public double calculateOfferSpotRate(SpreadRuleParameter srp, double spotRate, double refRate, boolean applyGiveback, Double preCalcSpread, double amount) 
	{
		double finalSpotRate = spotRate;
		if( srp.isPreTradeSpreadEnabled())
		{
			double finalSpread =  srp.getRawOfferSpread(amount);
			if(preCalcSpread != null){
				finalSpread = preCalcSpread;
			}
			if(applyGiveback)finalSpread = - (srp.getRawOfferSpread(amount) - getOfferSpreadWithImprovement(srp, spotRate, amount));
			finalSpotRate = spotRate + finalSpread;
			
			if(log.isDebugEnabled())
			{
				logSpreadFinal(srp, "calculateOfferSpotRate(GiveBack:" + applyGiveback +")", amount, spotRate, finalSpotRate, finalSpread);
			}
		}
		if( srp.isSkewEnabled())
		{
			finalSpotRate = applyGiveback ? MathUtilC.add(finalSpotRate, srp.getOfferGiveBackSkew(finalSpotRate)): MathUtilC.add(finalSpotRate, srp.getOfferSkew(finalSpotRate)); 
		}
		return finalSpotRate;
	}

	@Override
	public double calculateBidSpotRate(SpreadRuleParameter srp,	double bidSpotRate) 
	{
		if( srp.isEnabled())
		{
			double finalRate=  calculateBidSpotRate(srp,bidSpotRate,false);
			if(log.isDebugEnabled())
			{
				logFinalRate(srp, "calculateBidSpotRate", bidSpotRate, finalRate);
			}
			return finalRate;
		}
		return bidSpotRate;
	}
	
	@Override
	public double calculateBidSpotRate(SpreadRuleParameter srp,	double bidSpotRate, double amount) 
	{
		if( srp.isEnabled())
		{
			double finalRate=  calculateBidSpotRate(srp,bidSpotRate,false, amount);
			if(log.isDebugEnabled())
			{
				logFinalRate(srp, "calculateBidSpotRate", amount, bidSpotRate, finalRate);
			}
			return finalRate;
		}
		return bidSpotRate;
	}

	@Override
	public double calculateOfferSpotRate(SpreadRuleParameter srp, double offerSpotRate) 
	{
		if( srp.isEnabled())
		{
			double finalRate=  calculateOfferSpotRate(srp,offerSpotRate,false);
			if(log.isDebugEnabled())
			{
				logFinalRate(srp, "calculateOfferSpotRate", offerSpotRate, finalRate);
			}
			return finalRate;
		}
		return offerSpotRate;
	}
	
	@Override
	public double calculateOfferSpotRate(SpreadRuleParameter srp, double offerSpotRate, double amount) 
	{
		if( srp.isEnabled())
		{
			double finalRate=  calculateOfferSpotRate(srp, offerSpotRate, false, amount);
			if(log.isDebugEnabled())
			{
				logFinalRate(srp, "calculateOfferSpotRate", amount, offerSpotRate, finalRate);
			}
			return finalRate;
		}
		return offerSpotRate;
	}

	@Override
	public double removeSpread(SpreadRuleParameter srp,	double rate, boolean isbid) 
	{
		if( srp.isPreTradeSpreadEnabled())
		{
			double finalRate =  (isbid ? rate +  srp.getRawBidSpread():rate - srp.getRawOfferSpread());
			if(log.isDebugEnabled())
			{
				logFinalRate(srp, "removeSpread(Bid:"+isbid+")", rate, finalRate);
			}
			return finalRate;
		}
		return rate;
	}
	

	@Override
	public double removeSpread(SpreadRuleParameter srp,	double rate, boolean isbid, double amount) 
	{
		if( srp.isPreTradeSpreadEnabled())
		{
			double finalRate =  (isbid ? rate +  srp.getRawBidSpread(amount):rate - srp.getRawOfferSpread(amount));
			if(log.isDebugEnabled())
			{
				logFinalRate(srp, "removeSpread(Bid:"+isbid+")", amount, rate, finalRate);
			}
			return finalRate;
		}
		return rate;
	}

	@Override
	public double getBidForwardPointsSpread(SpreadRuleParameter srp, double fwdPoints,IdcDate valueDate,boolean isSwap,boolean isProrata) 
	{
		if( srp.isEnabled())
		{
			double spread = 0;
			boolean isSet=false;
			if(isSwap && srp.isSwpSpreadGrpEnabled() && srp.isSwapSpreadFeatureEnabled())
			{

			   spread = interpolateForwardPoints(srp,valueDate,true,isSwap,false)/srp.getFXRateBasis().getPipsFactor();
			   isSet=true;
			}
			if (!isSet && isProrata && srp.isProrataSpreadProfileEnabled()) {
				spread = interpolateForwardPoints(srp,valueDate,true,false,true)/srp.getFXRateBasis().getPipsFactor();
				isSet=true;
			}
			if(!isSet && srp.isFwdSpreadGrpEnabled())
			{
				spread = interpolateForwardPoints(srp,valueDate,true,isSwap,false)/srp.getFXRateBasis().getPipsFactor();
				isSet=true;
			}
			
			if(!isSet)
			{
				spread = srp.getRawBidFwdPointsSpread();
			}
			if(log.isDebugEnabled())
			{
				logSpreadFwd(srp, "getBidForwardPointsSpread", fwdPoints, spread, spread, valueDate); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}

	@Override
	public double getOfferForwardPointsSpread(SpreadRuleParameter srp, double fwdPoints,IdcDate valueDate,boolean isSwap,boolean isProrata)
	{
		if( srp.isEnabled())
		{
			double spread = 0;
			boolean isSet=false;
			if(isSwap && srp.isSwpSpreadGrpEnabled() && srp.isSwapSpreadFeatureEnabled())
			{
				spread = interpolateForwardPoints(srp,valueDate,false,isSwap,false)/srp.getFXRateBasis().getPipsFactor();
				isSet=true;

			}
			if (!isSet && isProrata && srp.isProrataSpreadProfileEnabled()) {
				spread = interpolateForwardPoints(srp,valueDate,false,false,true)/srp.getFXRateBasis().getPipsFactor();
				isSet=true;
			}
			if(!isSet && srp.isFwdSpreadGrpEnabled())
			{
				spread = interpolateForwardPoints(srp,valueDate,false,isSwap,false)/srp.getFXRateBasis().getPipsFactor();
				isSet=true;
			}
			
			if(!isSet)
			{
				spread = srp.getRawOfferFwdPointsSpread();
			}
			if(log.isDebugEnabled())
			{
				logSpreadFwd(srp, "getOfferForwardPointsSpread", fwdPoints, spread, spread, valueDate); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}

	@Override
	public double getBidForwardPointsSpread(SpreadRuleParameter srp,double spotRate, double fwdPoint,IdcDate valueDate,boolean isSwap,boolean isProrata)
	{
		return getBidForwardPointsSpread(srp,fwdPoint,valueDate,isSwap,isProrata);
	}

	@Override
	public double getOfferForwardPointsSpread(SpreadRuleParameter srp,double spotRate, double fwdPoint,IdcDate valueDate,boolean isSwap,boolean isProrata)
	{
		return getOfferForwardPointsSpread(srp,fwdPoint,valueDate,isSwap,isProrata);
	}

	@Override
	public double getBidSpread(SpreadRuleParameter srp,double spotRate) 
	{
		if( srp.isEnabled())
		{
			double spread =  srp.getRawBidSpread();
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpread", spotRate, spread,spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}

	@Override
	public double getOfferSpread(SpreadRuleParameter srp,double spotRate) 
	{
		if( srp.isEnabled())
		{
			double spread =  srp.getRawOfferSpread();
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpread", spotRate, spread,spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}
	
	
	@Override
	public double getBidSpread(SpreadRuleParameter srp, double spotRate, double amount) 
	{
		if( srp.isEnabled())
		{
			double spread =  srp.getRawBidSpread(amount);
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpread", amount, spotRate, spread,spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}
	
	@Override
	public double getBidSpread(SpreadRuleParameter srp, double spotRate, double amount, double spread) 
	{
		if( srp.isEnabled())
		{
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpread", amount, spotRate, spread,spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}

	@Override
	public double getOfferSpread(SpreadRuleParameter srp, double spotRate, double amount) 
	{
		if( srp.isEnabled())
		{
			double spread =  srp.getRawOfferSpread(amount);
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpread", amount, spotRate, spread,spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}
	

	@Override
	public double getOfferSpread(SpreadRuleParameter srp, double spotRate, double amount, double spread) 
	{
		if( srp.isEnabled())
		{
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpread", amount, spotRate, spread,spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}

	@Override
	public double getBidSpreadImprovement(SpreadRuleParameter srp, double spotRate) 
	{
		if( srp.isEnabled())
		{
			double spread =   srp.isGivebackAllowed() ? srp.getRawBidSpread()* srp.getGiveBackPercentage()/100:0;
			spread = MathUtil.round(spread, srp.getFXRateBasis().getSpotPrecision(),BigDecimal.ROUND_FLOOR);
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpreadImprovement", spotRate, srp.getRawBidSpread(),spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}
	
	@Override
	public double getBidSpreadImprovement(SpreadRuleParameter srp, double spotRate, double amount) 
	{
		if( srp.isEnabled())
		{
			double rawBidSpread = srp.getRawBidSpread(amount);
			double spread =   srp.isGivebackAllowed() ? rawBidSpread * srp.getGiveBackPercentage()/100:0;
			spread = MathUtil.round(spread, srp.getFXRateBasis().getSpotPrecision(),BigDecimal.ROUND_FLOOR);
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpreadImprovement", amount, spotRate, rawBidSpread, spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}
	
	@Override
	public double getBidSpreadImprovement(SpreadRuleParameter srp, double spotRate, double amount, double rawBidSpread) 
	{
		if( srp.isEnabled())
		{
			double spread =   srp.isGivebackAllowed() ? rawBidSpread * srp.getGiveBackPercentage()/100:0;
			spread = MathUtil.round(spread, srp.getFXRateBasis().getSpotPrecision(),BigDecimal.ROUND_FLOOR);
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpreadImprovement", amount, spotRate, rawBidSpread, spread); // for pips there is not calculation just log teh configured data
			}
			return spread;
		}
		return 0;
	}

	@Override
	public double getBidSpreadWithImprovement(SpreadRuleParameter srp, double spotRate) 
	{
		if( srp.isEnabled())
		{
			double bidSpread =  srp.getRawBidSpread();
			
			double fSpread = srp.isGivebackAllowed() ? bidSpread -getBidSpreadImprovement(srp, spotRate): bidSpread;
			
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpreadWithImprovement", spotRate, bidSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}
	
	@Override
	public double getBidSpreadWithImprovement(SpreadRuleParameter srp, double spotRate, double amount) 
	{
		if( srp.isEnabled())
		{
			double bidSpread =  srp.getRawBidSpread(amount);
			
			double fSpread = srp.isGivebackAllowed() ? bidSpread - getBidSpreadImprovement(srp, spotRate, amount): bidSpread;
			
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpreadWithImprovement", amount, spotRate, bidSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}
	
	@Override
	public double getBidSpreadWithImprovement(SpreadRuleParameter srp, double spotRate, double amount, double bidSpread) 
	{
		if( srp.isEnabled())
		{
			
			double fSpread = srp.isGivebackAllowed() ? bidSpread - getBidSpreadImprovement(srp, spotRate, amount, bidSpread): bidSpread;
			
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getBidSpreadWithImprovement", amount, spotRate, bidSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}
	
	@Override
	public double getOfferSpreadImprovement(SpreadRuleParameter srp, double spotRate) 
	{
		if( srp.isEnabled())
		{
			double offerSpread =  srp.getRawOfferSpread();
			double fSpread =  srp.isGivebackAllowed()? srp.getRawOfferSpread() * srp.getGiveBackPercentage()/100:0;
			fSpread = MathUtil.round(fSpread, srp.getFXRateBasis().getSpotPrecision(),BigDecimal.ROUND_FLOOR);
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpreadImprovement", spotRate, offerSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}
	
	@Override
	public double getOfferSpreadImprovement(SpreadRuleParameter srp, double spotRate, double amount) 
	{
		if( srp.isEnabled())
		{
			double offerSpread =  srp.getRawOfferSpread(amount);
			double fSpread =  srp.isGivebackAllowed()? offerSpread * srp.getGiveBackPercentage()/100:0;
			fSpread = MathUtil.round(fSpread, srp.getFXRateBasis().getSpotPrecision(),BigDecimal.ROUND_FLOOR);
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpreadImprovement", amount, spotRate, offerSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}
	
	@Override
	public double getOfferSpreadImprovement(SpreadRuleParameter srp, double spotRate, double amount, double offerSpread) 
	{
		if( srp.isEnabled())
		{
			double fSpread =  srp.isGivebackAllowed()? offerSpread * srp.getGiveBackPercentage()/100:0;
			fSpread = MathUtil.round(fSpread, srp.getFXRateBasis().getSpotPrecision(),BigDecimal.ROUND_FLOOR);
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpreadImprovement", amount, spotRate, offerSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}


	@Override
	public double getOfferSpreadWithImprovement(SpreadRuleParameter srp, double spotRate) 
	{
		if( srp.isEnabled())
		{
			double offerSpread = srp.getRawOfferSpread();
			double fSpread =   srp.isGivebackAllowed() ? (offerSpread -getOfferSpreadImprovement(srp,spotRate)) :offerSpread;	
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpreadWithImprovement", spotRate, offerSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}
	
	
	
	@Override
	public double getOfferSpreadWithImprovement(SpreadRuleParameter srp, double spotRate, double amount) 
	{
		if( srp.isEnabled())
		{
			double offerSpread = srp.getRawOfferSpread(amount);
			double fSpread =   srp.isGivebackAllowed() ? (offerSpread - getOfferSpreadImprovement(srp, spotRate, amount)) :offerSpread;	
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpreadWithImprovement", amount, spotRate, offerSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}
	
	@Override
	public double getOfferSpreadWithImprovement(SpreadRuleParameter srp, double spotRate, double amount, double offerSpread) 
	{
		if( srp.isEnabled())
		{
			double fSpread =   srp.isGivebackAllowed() ? (offerSpread - getOfferSpreadImprovement(srp, spotRate, amount, offerSpread)) :offerSpread;	
			if(log.isDebugEnabled())
			{
				logSpread(srp, "getOfferSpreadWithImprovement", amount, spotRate, offerSpread,fSpread); 
			}
			return fSpread;
		}
		return 0;
	}

}

