package com.integral.is.priceprovision.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.TreeMap;

import com.integral.admin.services.exceptions.AdminServiceException;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.priceProvision.SpotSpreadProfile;
import com.integral.finance.dealing.priceProvision.TierSpreadParameter;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.is.common.cache.MDSFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.priceprovision.rules.SpreadConstants;
import com.integral.is.priceprovision.rules.SpreadRuleParameter;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.PriceProvision;
import com.integral.util.MathUtilC;
import com.integral.util.Tuple;

public class PriceProvisionUtilC 
{
	private static final Log LOG = LogFactory.getLog(PriceProvisionUtilC.class);

	private static final ISMBean isMBean = ISFactory.getInstance().getISMBean();
	public static Map<String, List<PriceProvision>> retrievePriceProvisions( Organization fiOrg, Organization lpOrg )
    {
        List<PriceProvision> provisionCollection = ServiceFactory.getPriceProvisionDataSource().queryPriceProvision( fiOrg, lpOrg );
        Map<String, List<PriceProvision>> fiPriceProvisions = new HashMap<String, List<PriceProvision>>();
        for ( PriceProvision priceProvision : provisionCollection )
        {
            if ( priceProvision.getOrganization().isSameAs( fiOrg ) )
            {
                Currency baseCcy = priceProvision.getCurrencyPair().getBaseCurrency();
                Currency varCcy = priceProvision.getCurrencyPair().getVariableCurrency();
                String trdCcyPair = baseCcy.toString() + "/" + varCcy.toString();
                List<PriceProvision> ccyPriceProvision = fiPriceProvisions.get( trdCcyPair );
                if ( ccyPriceProvision == null )
                {
                    ccyPriceProvision = new ArrayList<PriceProvision>();
                }
                ccyPriceProvision.add( priceProvision );
                fiPriceProvisions.put( trdCcyPair, ccyPriceProvision );
            }
        }
        return fiPriceProvisions;
    }
	
	public static List<Organization> getOrganizations( String fiOrgNames )
    {
        String[] orgStr = fiOrgNames.split( "," );
        List<Organization> orgs = new ArrayList<Organization>();
        for( String org : orgStr )
        {
        	Organization o = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByShortName( org, OrganizationC.class, null, 'A' ) ;
        	if( o != null )
        		orgs.add(o);        	
        }
        return orgs;
    }
	
	private static double getRawSpread(double spread, final double pipFactor, final int precision, final int spreadType) {
		if (SpreadConstants.PIPS == spreadType) {
			int mode = BigDecimal.ROUND_FLOOR;
			if (spread >= 0) {
				mode = BigDecimal.ROUND_CEILING;
			}
			spread = MathUtilC.divide(spread, pipFactor, precision, mode);
			return spread;
		}
		return spread;
	}
	
	/**
	 * return Tree Map of Bid and offer spread for a spread profile
	 * @param spotSpreadProfile
	 * @return
	 */
	public static Tuple<NavigableMap<Double, Double>, NavigableMap<Double, Double>> getTierSpreadMap(final SpreadRuleParameter srp, final SpotSpreadProfile spotSpreadProfile) {
		if (spotSpreadProfile == null) {
			return null;
		}
		Organization fi = srp.getFI();
		FXRateBasis fxRateBasis = srp.getFXRateBasis();
		CurrencyPair çcypair = fxRateBasis.getCurrencyPair();
		NavigableMap<Double, Double> tierBidSpreadMap = new TreeMap<Double, Double>();
		NavigableMap<Double, Double> tierOfferSpreadMap = new TreeMap<Double, Double>();
		String currency = spotSpreadProfile.getCurrency();
		int spreadType = spotSpreadProfile.getSpreadType();
		Currency baseCurrencyObj = çcypair.getBaseCurrency();
		int precision = fxRateBasis.getSpotPrecision() + 2; 
		double pipFactor = fxRateBasis.getPipsFactor();
		if (!"BASE".equals(currency) && (!currency.equals(baseCurrencyObj.getShortName()))) {
			// convert to base amount from MDS
			Currency currencyObj = CurrencyFactory.getCurrency(currency);
			FXMarketDataElement mde = MDSFactory.getInstance().getMDE( fi.getShortName(), currencyObj, baseCurrencyObj);
			if (mde == null) {
				StringBuilder sb = new StringBuilder();
				sb.append("PriceProvisionUtilC.getTierSpreadMap::Converting tier limit amount from currency:").append(currency).
				append(":To Currency:").append(baseCurrencyObj.getShortName()).
				append("But No Market Data element available from LiveFXMDS/MAINStaticFXMDS so not applying spot spread from Spot Profile with ID:").
				append(spotSpreadProfile.getShortName());
				LOG.error(sb.toString());
				return new Tuple<NavigableMap<Double,Double>, NavigableMap<Double,Double>>(tierBidSpreadMap, tierOfferSpreadMap);
			}

			Collection<TierSpreadParameter> tierParams = spotSpreadProfile.getTierSpreadParameters();
			if (tierParams != null) {
				for (TierSpreadParameter tsp : tierParams) {
					try {
						double bidLimit = tsp.getBidLimit();
						double offerLimit = tsp.getOfferLimit();
						bidLimit = mde.getFXRate().getAmount(bidLimit, currencyObj);
						bidLimit = DealingModelUtil.applyContractMultiplier(bidLimit, çcypair, true);
						offerLimit = mde.getFXRate().getAmount(offerLimit, currencyObj);
						offerLimit = DealingModelUtil.applyContractMultiplier(offerLimit, çcypair, true);
						double bidSpread = tsp.getBidSpread();
						double offerSpread = tsp.getOfferSpread();
						bidSpread = getRawSpread(bidSpread, pipFactor, precision, spreadType);
						offerSpread = getRawSpread(offerSpread, pipFactor, precision, spreadType);
						tierBidSpreadMap.put(bidLimit, bidSpread);
						tierOfferSpreadMap.put(offerLimit, offerSpread);
					} catch (Throwable t) {
						LOG.error("Problem with converting Tier Limit to Base currency:Currency:" 
									+ currency + ":Base Currency:" + baseCurrencyObj.getShortName(), t);
					}

				}
			}

		} else {
			Collection<TierSpreadParameter> tierParams = spotSpreadProfile.getTierSpreadParameters();
			if (tierParams != null) {
				for (TierSpreadParameter tsp : tierParams) {
					double bidLimit = tsp.getBidLimit();
					double offerLimit = tsp.getOfferLimit();
					double bidSpread = tsp.getBidSpread();
					double offerSpread = tsp.getOfferSpread();
					bidSpread = getRawSpread(bidSpread, pipFactor, precision, spreadType);
					offerSpread = getRawSpread(offerSpread, pipFactor, precision, spreadType);
					tierBidSpreadMap.put(bidLimit, bidSpread);
					tierOfferSpreadMap.put(offerLimit, offerSpread);
				}
			}
		}

		return new Tuple<NavigableMap<Double,Double>, NavigableMap<Double,Double>>(tierBidSpreadMap, tierOfferSpreadMap);
		

				
	}
	
	/**
	 * return bid/offer skew spread for an accumulation Limit from the skew spread profile
	 * @param srp
	 * @param skewSpreadProfile
	 * @param accumulationLimit
	 * @return
	 */
	public static Tuple<Double, Double> getSkewSpread(final SpreadRuleParameter srp, final SpotSpreadProfile skewSpreadProfile, final double accumulationLimit) {
		if (srp == null || skewSpreadProfile == null || accumulationLimit <= 0) {
			return new Tuple<Double, Double>(0.0, 0.0);
		}
		Organization fi = srp.getFI();
		FXRateBasis fxRateBasis = srp.getFXRateBasis();
		CurrencyPair çcypair = fxRateBasis.getCurrencyPair();
		double bidSkewSpread = 0.0;
		double offerSkewSpread = 0.0;
		String currency = skewSpreadProfile.getCurrency();
		int spreadType = skewSpreadProfile.getSpreadType();
		Currency baseCurrencyObj = çcypair.getBaseCurrency();
		int precision = fxRateBasis.getSpotPrecision() + 2; 
		double pipFactor = fxRateBasis.getPipsFactor();
		NavigableMap<Double, Double> tierBidSpreadMap = new TreeMap<Double, Double>();
		NavigableMap<Double, Double> tierOfferSpreadMap = new TreeMap<Double, Double>();
		if (!"BASE".equals(currency) && (!currency.equals(baseCurrencyObj.getShortName()))) {
			// convert to base amount from MDS
			Currency currencyObj = CurrencyFactory.getCurrency(currency);
			FXMarketDataElement mde = MDSFactory.getInstance().getMDE( fi.getShortName(), currencyObj, baseCurrencyObj);
			if (mde == null) {
				StringBuilder sb = new StringBuilder();
				sb.append("PriceProvisionUtilC.getTierSpreadMap::Converting tier limit amount from currency:").append(currency).
				append(":To Currency:").append(baseCurrencyObj.getShortName()).
				append("But No Market Data element available from LiveFXMDS/MAINStaticFXMDS so not applying spot spread from Spot Profile with ID:").
				append(skewSpreadProfile.getShortName());
				LOG.error(sb.toString());
				return new Tuple<Double, Double>(0.0, 0.0);
			}
			
			Collection<TierSpreadParameter> tierParams = skewSpreadProfile.getTierSpreadParameters();
			
			if (tierParams != null) {
				for (TierSpreadParameter tsp : tierParams) {
					try {
						double bidLimit = tsp.getBidLimit();
						double offerLimit = tsp.getOfferLimit();
						bidLimit = mde.getFXRate().getAmount(bidLimit, currencyObj);
						bidLimit = DealingModelUtil.applyContractMultiplier(bidLimit, çcypair, true);
						offerLimit = mde.getFXRate().getAmount(offerLimit, currencyObj);
						offerLimit = DealingModelUtil.applyContractMultiplier(offerLimit, çcypair, true);
						double bidSpread = tsp.getBidSpread();
						double offerSpread = tsp.getOfferSpread();
						tierBidSpreadMap.put(bidLimit, bidSpread);
						tierOfferSpreadMap.put(offerLimit, offerSpread);
					} catch (Throwable t) {
						LOG.error("Problem with converting Tier Limit to Base currency:Currency:" 
									+ currency + ":Base Currency:" + baseCurrencyObj.getShortName(), t);
					}
				}
			}

		} else {
			Collection<TierSpreadParameter> tierParams = skewSpreadProfile.getTierSpreadParameters();
			if (tierParams != null) {
				for (TierSpreadParameter tsp : tierParams) {
					double bidLimit = tsp.getBidLimit();
					double offerLimit = tsp.getOfferLimit();
					double bidSpread = tsp.getBidSpread();
					double offerSpread = tsp.getOfferSpread();
					tierBidSpreadMap.put(bidLimit, bidSpread);
					tierOfferSpreadMap.put(offerLimit, offerSpread);
				}
			}
		}
		Map.Entry<Double, Double> bidSkewEntry = tierBidSpreadMap.ceilingEntry(accumulationLimit);
		Map.Entry<Double, Double> offerSkewEntry = tierOfferSpreadMap.ceilingEntry(accumulationLimit);
		if (bidSkewEntry != null) {
			bidSkewSpread = bidSkewEntry.getValue();
			//bidSkewSpread = getRawSpread(bidSkewSpread, pipFactor, precision, spreadType);
		} else {
			// check floor
			bidSkewEntry = tierBidSpreadMap.floorEntry(accumulationLimit);
			if (bidSkewEntry != null) {
				bidSkewSpread = bidSkewEntry.getValue();
			}
		}
		if (offerSkewEntry != null) {
			offerSkewSpread = offerSkewEntry.getValue();
			//offerSkewSpread = getRawSpread(offerSkewSpread, pipFactor, precision, spreadType);
		} else {
			// check floor
			offerSkewEntry = tierOfferSpreadMap.floorEntry(accumulationLimit);
			if (offerSkewEntry != null) {
				offerSkewSpread = offerSkewEntry.getValue();
			}
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("PriceProvisionUtilC.getSkewSpread:tierBidSpreadMap" + tierBidSpreadMap + ":tierOfferSpreadMap:" + tierOfferSpreadMap + 
					":bidSkewSpread:" + bidSkewSpread + ":offerSkewSpread:" + offerSkewSpread + ":spreadType:" + spreadType) ;
		}

		return new Tuple<Double,Double>(bidSkewSpread, offerSkewSpread);
	}

	public static double getPercentageSpread( Organization org, CurrencyPair ccyPair )
	{
		double percentageSpread = isMBean.getPriceProvisionAdditionalMarkupSpreadPercentageForOrg(org.getShortName());
		if(percentageSpread == 0.0) return 0;
		List<String> currencies = isMBean.getPriceProvisionAdditionalMarkupSpreadCurrencies(org.getShortName());
		if(currencies == null || currencies.isEmpty()) return percentageSpread;
		if(currencies.contains(ccyPair.getBaseCurrency().getShortName())) return percentageSpread;
		if(currencies.contains(ccyPair.getVariableCurrency().getShortName())) return percentageSpread;
		return 0;
	}
}
