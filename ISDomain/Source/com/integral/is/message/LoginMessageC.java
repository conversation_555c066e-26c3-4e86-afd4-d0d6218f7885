// Copyright (c) 2005 Integral Development Corp. All rights reserved.
package com.integral.is.message;


/**
 * Transfers login info from Integration Server to Adaptor
 */
public class LoginMessageC extends ISMessageC implements LoginMessage {

    private String rateTopic = null;
    private String tradeQueue = null;
    private String dbQueue = null;
    private String brokerId = null;
    private String adminUser = null;
    private String rfsRateTopic = null;
    private boolean isTextMessages = false;
    private boolean multicast = false;
    private boolean imtp = false;
    private boolean rfsOverMulticast = false;

    /**
     * Get the topic for market rate where integration server is listening to.
     * @return rateTopic
     */
    public String getRateDestination() {
        return rateTopic;
    }

    /**
     * Set rate topic of integration server.
     * @param topic
     */
    public void setRateDestination(String topic) {
        rateTopic = topic;
    }

    /**
     * Get trade queue
     * @return tradeQueue
     */
    public String getTradeDestination() {
        return tradeQueue;
    }

    /**
     * Set trade queue
     * @param queue
     */
    public void setTradeDestination(String queue) {
        tradeQueue = queue;
    }

    /**
     * Get audit queue
     * @return auditQueue
     */
    public String getPersistenceMessageQueue() {
        return dbQueue;
    }

    /**
     * Set database queue
     * @param queue
     */
    public void setPersistenceMessageQueue(String queue) {
        dbQueue = queue;
    }

    /**
     * Get broker id
     * @return
     */
    public String getBrokerId() {
        return brokerId;
    }

    /**
     * set broker id
     * @param broker
     */
    public void setBrokerId(String broker) {
        brokerId = broker;
    }

    /**
     * Get admin user
     * @return
     */
    public String getAdminUser() {
        return adminUser;
    }

    /**
     * set admin user
     * @param user
     */
    public void setAdminUser(String user) {
        adminUser = user;
    }

    public boolean isMulticastListener() {
        return multicast;
    }

    public void setMulticast(boolean multicast) {
        this.multicast = multicast;
    }

    public boolean isIMTPListener() {
        return imtp;
    }

    public void setIMTP(boolean imtp) {
        this.imtp = imtp;
    }

    @Override
    public boolean isRFSOverMulticast() {
        return rfsOverMulticast;
    }

    @Override
    public void setRFSOverMulticast(boolean rfsOverMulticast) {
        this.rfsOverMulticast = rfsOverMulticast;
    }

    public String toString() {
        return "LoginMessageC{" +
                "ServerId='" + getServerId() + "'" +
                ", RateTopic='" + rateTopic + "'" +
                ", TradeQueue='" + tradeQueue + "'" +
                ", PersistenceMessageQueue='" + dbQueue + "'" +
                ", BrokerId='" + brokerId + "'" +
                ", multicast='" + (multicast?"T":"F") + "'" +
                ", imtp='" + (imtp?"T":"F") + "'" +
                ", rfsOverMulticast='" + rfsOverMulticast + "'" +
                ", RFSRateTopic='" + rfsRateTopic + "'" +
                ", IsTextMessages='" + isTextMessages + "'" +
                "}";
    }

    /**
     *
     * @return the string representation of the object
     * @throws Exception
     */
    public String getToString() throws Exception {
        StringBuffer sb = new StringBuffer(super.getToString());
        sb.append(rateTopic).append(PIPE);
        sb.append(tradeQueue).append(PIPE);
        sb.append(dbQueue).append(PIPE);
        sb.append(brokerId).append(PIPE);
        sb.append(adminUser).append(PIPE);
        sb.append(multicast).append(PIPE);
        sb.append(rfsRateTopic).append(PIPE);
        sb.append(isTextMessages).append(PIPE);
        sb.append(rfsOverMulticast).append(PIPE);
        sb.append(OBJ_SEPARATOR);
        return sb.toString();
    }

    /**
     * Parse the str and populate the object
     * @param str
     * @return the remaining string which needs to be parsed.
     * @throws Exception
     */
    public String populateObject(String str) throws Exception {
        String str1 = super.populateObject(str);
        int idx1 = 0;
        String returnStr = "";
        String tempStr;
        idx1 = str1.indexOf(OBJ_SEPARATOR);
        //get String part of this object
        str1 = str1.substring(0,idx1);

        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0,idx1);
        if (!tempStr.equals("null"))
            this.setRateDestination(tempStr);
        str1 = str1.substring(idx1 + 1);

        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0,idx1);
        if (!tempStr.equals("null"))
            this.setTradeDestination(tempStr);
        str1 = str1.substring(idx1 + 1);

        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0,idx1);
        if (!tempStr.equals("null"))
            this.setPersistenceMessageQueue(tempStr);
        str1 = str1.substring(idx1 + 1);

        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0,idx1);
        if (!tempStr.equals("null"))
            this.setBrokerId(tempStr);
        str1 = str1.substring(idx1 + 1);

        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0,idx1);
        if (!tempStr.equals("null"))
            this.setAdminUser(tempStr);
        str1 = str1.substring(idx1 + 1);

        //These are optional
        idx1 = str1.indexOf(PIPE);
        if(idx1 != -1)
        {
	        tempStr = str1.substring(0,idx1);
	        if (!tempStr.equals("null"))
	            this.setMulticast(tempStr.equals("true"));
	        str1 = str1.substring(idx1 + 1);
	
	        idx1 = str1.indexOf(PIPE);
        	tempStr = str1.substring(0,idx1);
        	if(!tempStr.equals("null"))
        		this.setRFSRateTopic(tempStr);
            str1 = str1.substring(idx1 + 1);
        	//Both should be set
            idx1 = str1.indexOf(PIPE);
        	tempStr = str1.substring(0,idx1);
        	if(!tempStr.equals("null"))
        		this.setTextMessages(Boolean.valueOf(tempStr));

            str1 = str1.substring(idx1 + 1);
            idx1 = str1.indexOf(PIPE);
            tempStr = str1.substring(0,idx1);
            if(!tempStr.equals("null"))
                this.setRFSOverMulticast(Boolean.valueOf(tempStr));
        }
        
        return returnStr;
    }
    
    public String getRFSRateTopic()
    {
    	return this.rfsRateTopic;
    }

	public void setRFSRateTopic(String rfsRateTopic) 
	{
		this.rfsRateTopic = rfsRateTopic;
	}
	
    public boolean isTextMessages() 
    {
		return isTextMessages;
	}

	public void setTextMessages(boolean isTextMessages) 
	{
		this.isTextMessages = isTextMessages;
	}

}
