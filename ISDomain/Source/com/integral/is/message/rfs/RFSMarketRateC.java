package com.integral.is.message.rfs;

import com.integral.is.message.Timing;
import com.integral.is.message.TimingC;

import java.util.Date;
import java.util.Iterator;
import java.util.Map;

public class RFSMarketRateC extends RFSMessageC implements RFSMarketRate {

    protected String quoteId;
    protected String providerQuoteId;
    protected int timeToLiveInSeconds;
    protected boolean isStale;
    protected boolean isSpreadsApplied;
    protected int quoteValidityInSeconds;
    private String quoteStaleReason;


    public int getTimeToLiveInSeconds() {
        return timeToLiveInSeconds;
    }

    public void setTimeToLiveInSeconds(int timeToLiveInSeconds) {
        this.timeToLiveInSeconds = timeToLiveInSeconds;
    }

    public String getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(String quoteId) {
        this.quoteId = quoteId;
    }

    public String getProviderQuoteId() {
        return providerQuoteId;
    }

    public void setProviderQuoteId(String quoteId) {
        providerQuoteId = quoteId;
    }


    public boolean isStale() {
        return isStale;
    }

    public void setStale(boolean stale) {
        isStale = stale;
    }

    public boolean isSpreadsApplied()
    {
    	return isSpreadsApplied;
    }

    public void setSpreadsApplied( boolean flag )
    {
    	this.isSpreadsApplied = flag;
    }

	@Override
	public int quoteValidityInSeconds() {
		return quoteValidityInSeconds;
	}

	@Override
	public void setQuoteValidityInSeconds(int quoteValidityInSeconds) {
		this.quoteValidityInSeconds = quoteValidityInSeconds;
	}

    @Override
    public String getQuoteStaleReason() {
        return quoteStaleReason;
    }
    @Override
    public void setQuoteStaleReason(String quoteStaleReason) {
        this.quoteStaleReason = quoteStaleReason;
    }

    public String getToString() throws Exception {

        StringBuilder sb = new StringBuilder(600);

        sb.append(getServerId()).append(PIPE);
        Map props = getProperties();
        sb.append(OPEN_CURLY_BRACE);
        if (props != null && props.size() != 0) {
            Iterator propItr = props.keySet().iterator();

            while (propItr.hasNext()) {
                Object key = propItr.next();
                sb.append(key).append(TILDE).append(props.get(key));
                sb.append(COMMA);
            }

        }
        sb.append(CLOSE_CURLY_BRACE);
        sb.append(PIPE);
        sb.append(OPEN_CURLY_BRACE);
        Map timings = getTiming().getAllTimings();
        if (timings != null && timings.size() > 0) {
            Iterator timeItr = timings.keySet().iterator();

            while (timeItr.hasNext()) {
                Object key = timeItr.next();
                sb.append(key).append(TILDE).append(timings.get(key));
                sb.append(COMMA);
            }

        }
        sb.append(CLOSE_CURLY_BRACE).append(PIPE);

        sb.append(requestId).append(PIPE);
        sb.append(provider).append(PIPE);
        sb.append(streamId).append(PIPE);

        sb.append(OPEN_CURLY_BRACE);
        if (nearLeg == null)
            sb.append("null");
        else {
            sb.append(nearLeg.getTenor()).append(PIPE);
            sb.append(nearLeg.getValueDate() == null ? "null" : nearLeg.getValueDate().getTime()).append(PIPE);
            sb.append(OPEN_SQUARE);
            sb.append(nearLeg.getBidRate() == null ? "null" : ((RFSFXRateC) nearLeg.getBidRate()).getToString());
            sb.append(CLOSE_SQUARE);
            sb.append(PIPE);
            sb.append(OPEN_SQUARE);
            sb.append(nearLeg.getOfferRate() == null ? "null" : ((RFSFXRateC) nearLeg.getOfferRate()).getToString());
            sb.append(CLOSE_SQUARE);
            sb.append(PIPE);
            sb.append(nearLeg.getFixingTenor() == null ? "null" : nearLeg.getFixingTenor()).append(PIPE);
            sb.append(nearLeg.getFixingDate() == null ? "null" : nearLeg.getFixingDate().getTime()).append(PIPE);

        }
        sb.append(CLOSE_CURLY_BRACE);
        sb.append(PIPE);

        sb.append(OPEN_CURLY_BRACE);
        if (farLeg == null)
            sb.append("null");
        else {
            sb.append(farLeg.getTenor()).append(PIPE);
            sb.append(farLeg.getValueDate() == null ? "null" : farLeg.getValueDate().getTime()).append(PIPE);
            sb.append(OPEN_SQUARE);
            sb.append(farLeg.getBidRate() == null ? "null" : ((RFSFXRateC) farLeg.getBidRate()).getToString());
            sb.append(CLOSE_SQUARE);
            sb.append(PIPE);
            sb.append(OPEN_SQUARE);
            sb.append(farLeg.getOfferRate() == null ? "null" : ((RFSFXRateC) farLeg.getOfferRate()).getToString());
            sb.append(CLOSE_SQUARE);
            sb.append(PIPE);
            sb.append(farLeg.getFixingTenor() == null ? "null" : farLeg.getFixingTenor()).append(PIPE);
            sb.append(farLeg.getFixingDate() == null ? "null" : farLeg.getFixingDate().getTime()).append(PIPE);
        }
        sb.append(CLOSE_CURLY_BRACE);
        sb.append(PIPE);
        sb.append(quoteId).append(PIPE);
        sb.append(providerQuoteId).append(PIPE);
        sb.append(timeToLiveInSeconds).append(PIPE);
        sb.append(quoteValidityInSeconds).append(PIPE);
        sb.append(isStale).append(PIPE);
        sb.append( settlementInstruction ).append( PIPE );
        sb.append( isSpreadsApplied ).append(PIPE);
        sb.append(OPEN_CURLY_BRACE);
        if (tradeLegs.size() != 0) {
            Iterator itr = tradeLegs.iterator();
            while (itr.hasNext())
            {
                sb.append( ( (RFSFXLegC)itr.next() ).getToString() );
                sb.append(TILDE);
            }
        }
        sb.append(CLOSE_CURLY_BRACE).append(PIPE);

        return sb.toString();
    }

    public String populateObject(String str1) throws Exception {
        int idx1 = 0;
        String tempStr, str2 = "";
        int idx2 = 0;

        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        this.setServerId(tempStr);
        str1 = str1.substring(idx1 + 2);
        idx1 = str1.indexOf(CLOSE_CURLY_BRACE);
        str2 = str1.substring(0, idx1);


        // res~1234,rtef~23445,
        while ((idx2 = (str2.indexOf(COMMA))) != -1) {

            tempStr = str2.substring(0, idx2);
            //res~1234
            int idx3 = tempStr.indexOf(TILDE);
            this.setProperty(tempStr.substring(0, idx3), tempStr.substring(idx3 + 1, tempStr.length()));
            str2 = str2.substring(idx2 + 1);
        }
        //end

        str1 = str1.substring(idx1 + 3);
        idx1 = str1.indexOf(CLOSE_CURLY_BRACE);
        str2 = str1.substring(0, idx1);
        Timing timings = new TimingC();
        this.setTiming(timings);
        //res~1234,rtef~23445
        while ((idx2 = (str2.indexOf(COMMA))) != -1) {

            tempStr = str2.substring(0, idx2);
            //res~1234
            int idx3 = tempStr.indexOf(TILDE);
            timings.setTime(tempStr.substring(0, idx3), Long.parseLong(tempStr.substring(idx3 + 1, tempStr.length())));
            str2 = str2.substring(idx2 + 1);
        }


        str1 = str1.substring(idx1 + 2);
        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        this.setRequestId(tempStr);
        str1 = str1.substring(idx1 + 1);
        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        this.setProviderShortName(tempStr);
        str1 = str1.substring(idx1 + 1);
        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        if(!tempStr.equals("null"))
            this.setStreamId(tempStr);

        str1 = str1.substring(idx1 + 2);
        idx1 = str1.indexOf(CLOSE_CURLY_BRACE);
        str2 = str1.substring(0, idx1);
        if ("null".equalsIgnoreCase(str2)) {
            this.setNearLeg_Deserialization(null);
        } else {
            RFSFXLeg nearLeg = new RFSFXLegC();
            this.setNearLeg_Deserialization(nearLeg);
            idx2 = str2.indexOf(PIPE);
            tempStr = str2.substring(0, idx2);
            if ("null".equalsIgnoreCase(tempStr))
                nearLeg.setTenor(null);
            else
                nearLeg.setTenor(tempStr);
            str2 = str2.substring(idx2 + 1);
            idx2 = str2.indexOf(PIPE);
            tempStr = str2.substring(0, idx2);
            if ("null".equalsIgnoreCase(tempStr))
                nearLeg.setValueDate(null);
            else {
                Date date = new Date();
                date.setTime(Long.parseLong(tempStr));
                nearLeg.setValueDate(date);
            }

            str2 = str2.substring(idx2 + 2);
            idx2 = str2.indexOf(CLOSE_SQUARE);
            tempStr = str2.substring(0, idx2);
            if ("null".equalsIgnoreCase(tempStr))
                nearLeg.setBidRate(null);
            else {
                RFSFXRateC bidFxRate = new RFSFXRateC();
                bidFxRate.populateObject(tempStr);
                nearLeg.setBidRate(bidFxRate);
            }
            str2 = str2.substring(idx2 + 3);
            idx2 = str2.indexOf(CLOSE_SQUARE);
            tempStr = str2.substring(0, idx2);
            if ("null".equalsIgnoreCase(tempStr))
                nearLeg.setOfferRate(null);
            else {
                RFSFXRateC offerFxRate = new RFSFXRateC();
                offerFxRate.populateObject(tempStr);
                nearLeg.setOfferRate(offerFxRate);
            }

            //fixingTenor and fixingDate
            str2 = str2.substring(idx2 + 2);
            //for backward compatibility
            if(str2 != null && str2.length() != 0){
            	 idx2 = str2.indexOf(PIPE);
                 tempStr = str2.substring(0,  idx2);
                 if("null".equalsIgnoreCase(tempStr))
                 	nearLeg.setFixingTenor(null);
                 else{
                 	nearLeg.setFixingTenor(tempStr);
                 }

                 str2 = str2.substring(idx2 + 1);
                 idx2 = str2.indexOf(PIPE);
                 tempStr = str2.substring(0, idx2);
                 if ("null".equalsIgnoreCase(tempStr))
                     nearLeg.setFixingDate(null);
                 else {
                     Date date = new Date();
                     date.setTime(Long.parseLong(tempStr));
                     nearLeg.setFixingDate(date);
                 }
            }

        }

        str1 = str1.substring(idx1 + 3);
        idx1 = str1.indexOf(CLOSE_CURLY_BRACE);
        str2 = str1.substring(0, idx1);
        if ("null".equalsIgnoreCase(str2)) {
            this.setFarLeg_Deserialization(null);
        } else {
            RFSFXLeg farLeg = new RFSFXLegC();
            this.setFarLeg_Deserialization(farLeg);
            idx2 = str2.indexOf(PIPE);
            tempStr = str2.substring(0, idx2);
            if ("null".equalsIgnoreCase(tempStr))
                farLeg.setTenor(null);
            else
                farLeg.setTenor(tempStr);
            str2 = str2.substring(idx2 + 1);
            idx2 = str2.indexOf(PIPE);
            tempStr = str2.substring(0, idx2);
            if ("null".equalsIgnoreCase(tempStr))
                farLeg.setValueDate(null);
            else {
                Date date = new Date();
                date.setTime(Long.parseLong(tempStr));
                farLeg.setValueDate(date);
            }

            str2 = str2.substring(idx2 + 2);
            idx2 = str2.indexOf(CLOSE_SQUARE);
            tempStr = str2.substring(0, idx2);
            if ("null".equalsIgnoreCase(tempStr))
                farLeg.setBidRate(null);
            else {
                RFSFXRateC bidFxRate = new RFSFXRateC();
                bidFxRate.populateObject(tempStr);
                farLeg.setBidRate(bidFxRate);
            }
            str2 = str2.substring(idx2 + 3);
            idx2 = str2.indexOf(CLOSE_SQUARE);
            tempStr = str2.substring(0, idx2);
            if ("null".equalsIgnoreCase(tempStr))
                farLeg.setOfferRate(null);
            else {
                RFSFXRateC offerFxRate = new RFSFXRateC();
                offerFxRate.populateObject(tempStr);
                farLeg.setOfferRate(offerFxRate);
            }

            //fixingTenor and fixingDate
            str2 = str2.substring(idx2 + 2);
            //for backward compatibility
            if(str2 != null && str2.length() != 0){
            	 idx2 = str2.indexOf(PIPE);
                 tempStr = str2.substring(0,  idx2);
                 if("null".equalsIgnoreCase(tempStr))
                 	farLeg.setFixingTenor(null);
                 else{
                 	farLeg.setFixingTenor(tempStr);
                 }

                 str2 = str2.substring(idx2 + 1);
                 idx2 = str2.indexOf(PIPE);
                 tempStr = str2.substring(0, idx2);
                 if ("null".equalsIgnoreCase(tempStr))
                     farLeg.setFixingDate(null);
                 else {
                     Date date = new Date();
                     date.setTime(Long.parseLong(tempStr));
                     farLeg.setFixingDate(date);
                 }
            }
        }

        str1 = str1.substring(idx1 + 2);
        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        this.setQuoteId(tempStr);
        str1 = str1.substring(idx1 + 1);
        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        this.setProviderQuoteId(tempStr);
        str1 = str1.substring(idx1 + 1);
        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        this.setTimeToLiveInSeconds(Integer.parseInt(tempStr));
        str1 = str1.substring(idx1 + 1);
        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        this.setQuoteValidityInSeconds(Integer.parseInt(tempStr));
        str1 = str1.substring(idx1 + 1);
        idx1 = str1.indexOf(PIPE);
        tempStr = str1.substring(0, idx1);
        this.setStale(Boolean.valueOf(tempStr));
        str1 = str1.substring(idx1 + 1);
        idx1 = str1.indexOf(PIPE);
        if( idx1 != -1 )
        {
	        tempStr = str1.substring(0, idx1);
	        if( !isEmptyString( tempStr ))
	        	this.setSettlementInstruction( tempStr );
	        str1 = str1.substring(idx1 + 1);
	        idx1 = str1.indexOf(PIPE);
        }
        if( idx1 != -1 )
        {
	        tempStr = str1.substring(0, idx1);
	        if( !isEmptyString( tempStr ))
	        	this.setSpreadsApplied( Boolean.valueOf(tempStr ));
	        str1 = str1.substring(idx1 + 1);
	        idx1 = str1.indexOf(PIPE);
        }

        idx1 = str1.indexOf(CLOSE_CURLY_BRACE);
        if( idx1 != -1 )
        {
        	tempStr = str1.substring(1, idx1);
        }
        int idx3 = tempStr.indexOf( TILDE );
        String tempStr2 = "";
        while( idx3 != -1 )
        {
        	tempStr2= tempStr.substring( 0, idx3 );
	    	RFSFXLegC fxLeg = new RFSFXLegC();
	    	fxLeg.populateObject( tempStr2.substring( 0, tempStr2.length() ) );
	        this.tradeLegs.add( fxLeg );
	        tempStr = tempStr.substring( idx3 + 1 );
	        idx3 = tempStr.indexOf( TILDE );
        }

        return "";
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(400).append("RFSMarketRateC").append(OPEN_CURLY_BRACE);
        sb.append(super.toString()).append(COMMA);
        sb.append("quoteId").append("=").append(quoteId).append(COMMA);
        sb.append("providerQuoteId").append("=").append(providerQuoteId).append(COMMA);
        sb.append("timeToLiveInSeconds").append("=").append(timeToLiveInSeconds).append(COMMA);
        sb.append("isStale").append("=").append(isStale).append(COMMA);
        sb.append("settlementIns").append("=").append(settlementInstruction).append(COMMA);
        sb.append("isSpreadApplied").append("=").append(isSpreadsApplied).append(COMMA);
        sb.append("quoteValidityInSeconds").append("=").append(quoteValidityInSeconds).append(COMMA);
        sb.append("quoteStaleReason").append("=").append(quoteStaleReason).append(COMMA);
        sb.append("tradeLegs").append("=").append(tradeLegs.size() == 0 ? "null" : getTradeLegsToString(tradeLegs)).append(COMMA);
        sb.append(CLOSE_CURLY_BRACE);
        return sb.toString();
    }

    public static void main(String arg[]) {
        RFSMarketRate rate1 = new RFSMarketRateC();

        rate1.setQuoteId("G-2cbe1cf671-115ae39d629-LPF1-28");
        rate1.setProviderQuoteId("test124");
        rate1.setTimeToLiveInSeconds((int) System.currentTimeMillis());
        rate1.setStale(false);

        rate1.setBaseCurrency("EUR");
        rate1.setVariableCurrency("USD");
        rate1.setProviderShortName("BOAN");
        rate1.setOrgShortName("FI2");
        rate1.setUserShortName("fi2mm2");
        rate1.setLeShortName("FI2-le1");
        rate1.setRequestId("FXI10001");
        
        rate1.setQuoteValidityInSeconds(29);

        RFSFXLeg nearLeg = new RFSFXLegC();
        nearLeg.setDealtCurrency("EUR");
        nearLeg.setTenor("SPOT");
        nearLeg.setValueDate(new Date());

        nearLeg.setLegClassification(0);


        RFSFXRate rate2 = new RFSFXRateC();
        rate2.setDealtAmount(10000000);
        rate2.setSpotRate(1.2345);
        rate2.setRate(1.2345);
        nearLeg.setBidRate(rate2);

        RFSFXRate rate3 = new RFSFXRateC();
        rate3.setDealtAmount(10000000);
        rate3.setSpotRate(1.2134);
        rate3.setRate(1.2234);
        nearLeg.setOfferRate(rate3);


        rate1.setNearLeg(nearLeg);

        TimingC timing = new TimingC();
        timing.setTime("RateReceived", System.currentTimeMillis());
        rate1.setTiming(timing);

        rate1.setProperty("senderSubID", "test123");
        rate1.setFarLeg(nearLeg);
        rate1.setSettlementInstruction("This is a instruction");
        rate1.setSpreadsApplied(true);
        try {
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < 1; i++) {
                System.out.println("rate1:" + rate1.getToString());
                RFSMarketRateC ra = new RFSMarketRateC();
                ra.populateObject(rate1.getToString());
                System.out.println("rate2:" + ra.getToString());
            }

            System.out.println("time taken:" + (System.currentTimeMillis() - startTime));

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }
}
