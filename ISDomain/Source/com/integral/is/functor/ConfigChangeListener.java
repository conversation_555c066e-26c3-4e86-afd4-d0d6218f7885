package com.integral.is.functor;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.broker.model.StreamVisibility;
import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.util.OrgRelationUtil;
import com.integral.is.util.StreamUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.mastercontrol.MasterControlSevice;
import com.integral.maker.service.pricing.MarketMakerPriceControlService;
import com.integral.marketmaker.rule.MasterControl;
import com.integral.marketmaker.rule.Rule;
import com.integral.marketmaker.rule.pricecontrol.PriceControl;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.notifications.CurrencyPairUpdate;
import com.integral.notifications.EventTypes;
import com.integral.notifications.StreamUpdate;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetSocketAddress;
import java.net.MulticastSocket;
import java.net.SocketAddress;
import java.util.Collection;
import java.util.Collections;

/**
 * Created by nagarajap on 1/16/2018.
 */
public class ConfigChangeListener {

    private static final Log log = LogFactory.getLog(ConfigChangeListener.class);

    private static ConfigChangeListener instance = new ConfigChangeListener();

    private MulticastPublisher publisher = new MulticastPublisher();

    private ConfigChangeListener() {

    }

    public static ConfigChangeListener getInstance() {
        return instance;
    }

    public boolean isValidBilateralTradingVenueProvider(Organization org) {
        return OrgRelationUtil.isValidBilateralTradingVenueProvider(org);
    }

    public void notifyStreamSwitch(Organization lpOrg, LegalEntity fiLE, Stream oldStream, Stream newStream) {
        Organization fiOrg = fiLE.getOrganization();
        if (isValidBilateralTradingVenueProvider(lpOrg)) {
            if (fiOrg.isBroker()) {
                if (fiOrg.getDefaultDealingEntity().getObjectID() == fiLE.getObjectID()) {
                    Collection<Organization> lgOrgs = fiOrg.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
                    if (lgOrgs != null && !lgOrgs.isEmpty()) {
                        for (Organization lgOrg : lgOrgs) {
                            if (OrgRelationUtil.isValidMDFEnabledFI(lgOrg)) {
                                handleStreamSwitchNotification(lpOrg, lgOrg.getDefaultDealingEntity(), oldStream, newStream);
                            }
                        }
                    }
                }
            }
            else if (OrgRelationUtil.isValidMDFEnabledFI(fiOrg) && fiOrg.getDefaultDealingEntity().getObjectID() == fiLE.getObjectID()) {
                handleStreamSwitchNotification(lpOrg, fiLE, oldStream, newStream);
            }
            else {
                String msg = new StringBuilder().append("stream switch notification for")
                        .append(" LP=").append(lpOrg.getShortName())
                        .append(" FI=").append(fiOrg.getShortName()).append(" LE=").append(fiLE.getShortName())
                        .append(" OldStreamId=").append(oldStream.getName())
                        .append(" NewStreamId=").append(newStream.getName())
                        .toString();

                log.info("Ignoring:" + msg + ":Reason:FI is not a MDF enabled FI/LE is not the default LE");
            }
        }
        else {
            String msg = new StringBuilder().append("stream switch notification for")
                    .append(" LP=").append(lpOrg.getShortName())
                    .append(" FI=").append(fiOrg.getShortName()).append(" LE=").append(fiLE.getShortName())
                    .append(" OldStreamId=").append(oldStream.getName())
                    .append(" NewStreamId=").append(newStream.getName())
                    .toString();

            log.info("Ignoring:" + msg + ":Reason:LP not a super bank");
        }
    }

    public void notifyStreamStatusChange(Organization lpOrg, Stream stream) {
        String mesg = new StringBuilder().append("stream status change notification for")
                .append(" LP=").append(lpOrg.getShortName())
                .append(" Stream=").append(stream.getName())
                .append("Active:").append(stream.isActive())
                .toString();
        if (isValidBilateralTradingVenueProvider(lpOrg)) {
            try {

                Stream realLPStream;
                if (lpOrg.isMasked()) {
                    realLPStream = StreamUtil.getStream(stream.getShortName(), lpOrg.getRealLP());
                } else {
                    realLPStream = stream;
                }
                boolean streamStatus = stream.isActive() && realLPStream.isActive();

                StreamUpdate updateMessage = new StreamUpdate();
                updateMessage.setEvent(EventTypes.STREAM_STATUS_UPDATE);
                updateMessage.setFromStreamIndex(realLPStream.getSuperLPIndex());
                updateMessage.setToStreamIndex(realLPStream.getSuperLPIndex());
                updateMessage.setStreamName(stream.getName());
                updateMessage.setLpName(lpOrg.getShortName());
                if (streamStatus) {
                    updateMessage.setStreamStatus(StreamUpdate.STREAM_ACTIVE);
                } else {
                    updateMessage.setStreamStatus(StreamUpdate.STREAM_INACTIVE);
                }

                log.info("Sending streamupdate:" + updateMessage);

                publisher.publish(updateMessage);
            } catch (Exception e) {
                log.error("Error in processing:" + mesg, e);
            }
        } else {
            log.info("Ignoring:" + mesg + ":Reason:LP not a super bank/LP is a " +
                    "masked LP");
        }
    }

    public void notifyLPStatusChange(Organization lpOrg, Organization fiOrg, boolean enabled) {
        // For LG:
        //  Check status on Broker & if it's Inactive in Broker, dont sent notification
        //  Else, send notification

        // For Broker:
        //  Check status on all LGs & if it's Inactive in LG, dont sent notification
        //  Else, send notification for each LG where it's active
        if (isValidBilateralTradingVenueProvider(lpOrg)) {
            if (fiOrg.isBroker() && fiOrg.isOrderExecutionEnabled()) {
                Collection<Organization> lgOrgs = fiOrg.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
                if (lgOrgs != null && !lgOrgs.isEmpty()) {
                    Collection<Organization> disabledProviders;
                    BrokerOrganizationFunction bof = fiOrg.getBrokerOrganizationFunction();
                    if (bof == null) {
                        return;
                    }
                    if (bof.getDisabledProviders() == null) {
                        disabledProviders = Collections.emptyList();
                    }
                    else {
                        disabledProviders = bof.getDisabledProviders();
                    }
                    if (!disabledProviders.contains(lpOrg)) {
                        for (Organization lgOrg : lgOrgs) {
                            if (OrgRelationUtil.isValidMDFEnabledFI(lgOrg) && lgOrg.isOrderExecutionEnabled()) {
                                OrganizationRelationship lgLpRelation = OrganizationUtil.getOrganizationRelationshipByClassification(lgOrg, lpOrg, OrganizationRelationship.LG_LP_RELATIONSHIP);
                                if (lgLpRelation != null && lgLpRelation.isCounterpartyEnabled()) {
                                    handleLpOrderExecutionChangeNotificationForLG(lpOrg, lgOrg, fiOrg, enabled);
                                }
                            }
                        }
                    }
                }
            }
            else if (OrgRelationUtil.isValidMDFEnabledFI(fiOrg)) {
                if (fiOrg.isLiquidityGroup() && fiOrg.isOrderExecutionEnabled()) {
                    Organization brokerOrg = OrganizationUtil.getBrokerOfLiquidityGroup(fiOrg);
                    if (brokerOrg != null && brokerOrg.isOrderExecutionEnabled()) {
                        Collection<Organization> disabledProviders;
                        BrokerOrganizationFunction bof = brokerOrg.getBrokerOrganizationFunction();
                        if (bof == null || bof.getDisabledProviders() == null) {
                            disabledProviders = Collections.emptyList();
                        }
                        else {
                            disabledProviders = bof.getDisabledProviders();
                        }
                        if (!disabledProviders.contains(lpOrg)) {
                            OrganizationRelationship brokerLpRelation = OrganizationUtil.getOrganizationRelationshipByClassification(brokerOrg, lpOrg, OrganizationRelationship.FI_LP_RELATIONSHIP);
                            if (brokerLpRelation != null && brokerLpRelation.isCounterpartyEnabled()) {
                                handleLpOrderExecutionChangeNotificationForLG(lpOrg, fiOrg, brokerOrg, enabled);
                            }
                        }
                    }
                }
                else {
                    handleLPStatusChangeNotification(lpOrg, fiOrg, enabled);
                }
            }
            else {
                String msg = new StringBuilder().append("Liquidity rules LP status change notification for")
                        .append(" FI=").append(fiOrg.getShortName())
                        .append(" LP=").append(lpOrg.getShortName())
                        .append("Enabled:").append(enabled)
                        .toString();

                log.info("Ignoring:" + msg + ":Reason:FI is not a MDF enabled FI");
            }
        }
        else {
            String msg = new StringBuilder().append("Liquidity rules LP status change notification for")
                    .append(" FI=").append(fiOrg.getShortName())
                    .append(" LP=").append(lpOrg.getShortName())
                    .append("Enabled:").append(enabled)
                    .toString();

            log.info("Ignoring:" + msg + ":Reason:LP not a super bank");
        }
    }

    public void notifyStreamVisibilityUpdate(TradingParty tp, int oldStreamVis, int newStreamVis) {
        Organization lpOrg = tp.getOrganization();
        Organization fiOrg = tp.getLegalEntityOrganization();
        LegalEntity fiLE = tp.getLegalEntity();
        if (isValidBilateralTradingVenueProvider(lpOrg)) {
            if (fiOrg.isBroker()) {
                if (fiOrg.getDefaultDealingEntity().getObjectID() == fiLE.getObjectID()) {
                    Collection<Organization> lgOrgs = fiOrg.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
                    if (lgOrgs != null && !lgOrgs.isEmpty()) {
                        for (Organization lgOrg : lgOrgs) {
                            if (OrgRelationUtil.isValidMDFEnabledFI(lgOrg)) {
                                handleStreamVisibilityUpdateNotification(lpOrg, lgOrg.getDefaultDealingEntity(), tp, oldStreamVis, newStreamVis);
                            }
                        }
                    }
                }
            }
            else if (OrgRelationUtil.isValidMDFEnabledFI(fiOrg) && fiOrg.getDefaultDealingEntity().getObjectID() == fiLE.getObjectID()) {
                handleStreamVisibilityUpdateNotification(lpOrg, fiLE, tp, oldStreamVis, newStreamVis);
            }
            else {
                String msg = new StringBuilder().append("StreamVisibility Update notification for TP with ")
                        .append(" LP=").append(lpOrg.getShortName())
                        .append(", FI=").append(fiOrg.getShortName()).append(" LE=").append(fiLE.getShortName())
                        .append(", OldStreamVisibility=").append(StreamVisibility.getStreamVisibility(oldStreamVis).name())
                        .append(", NewStreamVisibility=").append(StreamVisibility.getStreamVisibility(newStreamVis).name())
                        .toString();

                log.info("Ignoring:" + msg + ":Reason:FI is not a MDF enabled FI/LE is not the default LE");
            }
        }
        else {
            String msg = new StringBuilder().append("StreamVisibility Update notification for TP with ")
                    .append(" LP=").append(lpOrg.getShortName())
                    .append(", FI=").append(fiOrg.getShortName()).append(" LE=").append(fiLE.getShortName())
                    .append(", OldStreamVisibility=").append(StreamVisibility.getStreamVisibility(oldStreamVis).name())
                    .append(", NewStreamVisibility=").append(StreamVisibility.getStreamVisibility(newStreamVis).name())
                    .toString();

            log.info("Ignoring:" + msg + ":Reason:LP not a super bank");
        }
    }

    public void notifyTakerCurrencyPairChangeForLp(Organization taker, Organization lp, CurrencyPair currencyPair, Stream stream, boolean enabled) {
        String mesg = new StringBuilder().append("Taker CurrencyPair change notification for ")
                .append(" Taker=").append(taker.getShortName())
                .append(" LP=").append(lp.getShortName())
                .append(" CP:").append(currencyPair.getName())
                .toString();
        if (isValidBilateralTradingVenueProvider(lp)) {
            try {
                Stream realLPStream;
                if (lp.isMasked()) {
                    realLPStream = StreamUtil.getStream(stream.getShortName(), lp.getRealLP());
                } else {
                    realLPStream = stream;
                }

                CurrencyPairUpdate updateMessage = new CurrencyPairUpdate();
                updateMessage.setEvent(EventTypes.TAKER_CP_LP_UPDATE);
                updateMessage.setFiIndex(taker.getIndex());
                updateMessage.setFiLe(taker.getDefaultDealingEntity().getObjectID());
                updateMessage.setCcyPairIndex(currencyPair.getIndex());
                updateMessage.setStreamIndex(realLPStream.getSuperLPIndex());
                updateMessage.setStreamName(stream.getName());
                updateMessage.setLpName(lp.getShortName());
                if (enabled) {
                    updateMessage.setEnabled(CurrencyPairUpdate.CP_ENABLED);
                } else {
                    updateMessage.setEnabled(CurrencyPairUpdate.CP_DISABLED);
                }
                log.info("Sending ccyPairUpdate:" + updateMessage);
                publisher.publish(updateMessage);
            }
            catch (Exception e) {
                log.error("Error in processing:" + mesg, e);
            }
        }
        else {
            log.info("Ignoring:" + mesg + ":Reason:LP not a super bank/is not Active");
        }
    }

    public void notifyTakerCurrencyPairChangeForAllLps(Organization taker, CurrencyPair currencyPair, boolean enabled) {
        String mesg = new StringBuilder().append("Taker CurrencyPair change notification for ")
                .append(" Taker=").append(taker.getShortName())
                .append(" LP=All LPs")
                .append(" CP:").append(currencyPair.getName())
                .toString();
        try {
            CurrencyPairUpdate updateMessage = new CurrencyPairUpdate();
            updateMessage.setEvent(EventTypes.TAKER_CP_ALL_LP_UPDATE);
            updateMessage.setFiIndex(taker.getIndex());
            updateMessage.setFiLe(taker.getDefaultDealingEntity().getObjectID());
            updateMessage.setCcyPairIndex(currencyPair.getIndex());
            updateMessage.setStreamIndex(-1);
            if (enabled) {
                updateMessage.setEnabled(CurrencyPairUpdate.CP_ENABLED);
            } else {
                updateMessage.setEnabled(CurrencyPairUpdate.CP_DISABLED);
            }
            log.info("Sending ccyPairUpdate:" + updateMessage);
            publisher.publish(updateMessage);
        }
        catch (Exception e) {
            log.error("Error in processing:" + mesg, e);
        }
    }

    public void notifyBrokerPriceMakingMasterControlStreamPricesUpdate(Organization broker, boolean enabled) {
        if (broker.isBroker() && broker.isPriceStreamingEnabled()) {
            Collection<Organization> lgOrgs = broker.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
            if (lgOrgs != null && !lgOrgs.isEmpty()) {
                // Send notification for all LPs
                for (Organization lgOrg : lgOrgs) {
                    if (OrgRelationUtil.isValidMDFEnabledFI(lgOrg) && lgOrg.isPriceStreamingEnabled()) {
                        handleMasterControlChangeForLg(lgOrg, broker, enabled, true);
                    }
                }
            }
        }
    }

    public void notifyBrokerPriceMakingMasterControlOrderExecutionUpdate(Organization broker, boolean enabled) {
        if (broker.isBroker() && broker.isOrderExecutionEnabled()) {
            Collection<Organization> lgOrgs = broker.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
            if (lgOrgs != null && !lgOrgs.isEmpty()) {
                // Send notification for all LPs
                for (Organization lgOrg : lgOrgs) {
                    if (OrgRelationUtil.isValidMDFEnabledFI(lgOrg) && lgOrg.isOrderExecutionEnabled()) {
                        handleMasterControlChangeForLg(lgOrg, broker, enabled, false);
                    }
                }
            }
        }
    }

    public void notifyBrokerPriceMakingLpStatusUpdate(Organization broker, Organization lp, boolean enabled) {
        if (broker.isBroker() && broker.isOrderExecutionEnabled()) {
            boolean brokerPMOrderExcnEnabled = isBrokerPMOrderExcnEnabled(broker);
            if (brokerPMOrderExcnEnabled) {
                Organization realLp = lp.getRealLP() != null ? lp.getRealLP() : lp;
                if (isValidBilateralTradingVenueProvider(realLp)) {
                    Collection<Organization> lgOrgs = broker.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
                    if (lgOrgs != null && !lgOrgs.isEmpty()) {
                        for (Organization lgOrg : lgOrgs) {
                            if (OrgRelationUtil.isValidMDFEnabledFI(lgOrg) && lgOrg.isOrderExecutionEnabled()) {
                                OrganizationRelationship brokerLpRelation = OrganizationUtil.getOrganizationRelationshipByClassification(broker, lp, OrganizationRelationship.FI_LP_RELATIONSHIP);
                                OrganizationRelationship lgLpRelation = OrganizationUtil.getOrganizationRelationshipByClassification(lgOrg, lp, OrganizationRelationship.LG_LP_RELATIONSHIP);
                                if (brokerLpRelation != null && lgLpRelation != null
                                        && brokerLpRelation.isCounterpartyEnabled() && lgLpRelation.isCounterpartyEnabled()) {
                                    // Send notification that LP is enabled/disabled
                                    handleLpOrderExecutionChangeNotificationForLG(lp, lgOrg, broker, enabled);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void notifyMasterControlStreamPricesUpdate(Organization fiOrg, boolean enabled) {
        // For LG:
        //  Check status on Broker & if it's Inactive in Broker, dont sent notification
        //  Else, send notification
        if (fiOrg.isLiquidityGroup() && OrgRelationUtil.isValidMDFEnabledFI(fiOrg)) {
            Organization brokerOrg = OrganizationUtil.getBrokerOfLiquidityGroup(fiOrg);
            if (brokerOrg != null && brokerOrg.isPriceStreamingEnabled()) {
                boolean brokerPMStreamPricesEnabled = isBrokerPMStreamPricesEnabled(brokerOrg);
                if (brokerPMStreamPricesEnabled) {
                    handleMasterControlChangeForLg(fiOrg, brokerOrg, enabled, true);
                }
            }
        }

        // For Broker:
        //  Check status on all LGs & if it's Inactive in LG, dont sent notification
        //  Else, send notification for each LG where it's active
        else if (fiOrg.isBroker()) {
            boolean brokerPMStreamPricesEnabled = isBrokerPMStreamPricesEnabled(fiOrg);
            if (brokerPMStreamPricesEnabled) {
                Collection<Organization> lgOrgs = fiOrg.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
                if (lgOrgs != null && !lgOrgs.isEmpty()) {
                    for (Organization lgOrg : lgOrgs) {
                        if (OrgRelationUtil.isValidMDFEnabledFI(lgOrg) && lgOrg.isPriceStreamingEnabled()) {
                            handleMasterControlChangeForLg(lgOrg, fiOrg, enabled, true);
                        }
                    }
                }
            }
        }
    }

    public void notifyMasterControlOrderExecutionUpdate(Organization fiOrg, boolean enabled) {
        // For LG:
        //  Check status on Broker & if it's Inactive in Broker, dont sent notification
        //  Else, send notification
        if (fiOrg.isLiquidityGroup() && OrgRelationUtil.isValidMDFEnabledFI(fiOrg)) {
            Organization brokerOrg = OrganizationUtil.getBrokerOfLiquidityGroup(fiOrg);
            if (brokerOrg != null && brokerOrg.isOrderExecutionEnabled()) {
                boolean brokerPMOrderExcnEnabled = isBrokerPMOrderExcnEnabled(brokerOrg);
                if (brokerPMOrderExcnEnabled) {
                    handleMasterControlChangeForLg(fiOrg, brokerOrg, enabled, false);
                }
            }
        }

        // For Broker:
        //  Check status on all LGs & if it's Inactive in LG, dont sent notification
        //  Else, send notification for each LG where it's active
        else if (fiOrg.isBroker()) {
            boolean brokerPMOrderExcnEnabled = isBrokerPMOrderExcnEnabled(fiOrg);
            if (brokerPMOrderExcnEnabled) {
                Collection<Organization> lgOrgs = fiOrg.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
                if (lgOrgs != null && !lgOrgs.isEmpty()) {
                    for (Organization lgOrg : lgOrgs) {
                        if (OrgRelationUtil.isValidMDFEnabledFI(lgOrg) && lgOrg.isOrderExecutionEnabled()) {
                            handleMasterControlChangeForLg(lgOrg, fiOrg, enabled, false);
                        }
                    }
                }
            }
        }
    }

    private static boolean isBrokerPMStreamPricesEnabled(Organization brokerOrg) {
        MarketMakerPriceControlService priceControlService = MarketMakerFactory.getInstance().getPriceControlService();
        boolean brokerPMStreamPricesEnabled = false;
        try {
            PriceControl priceControl = priceControlService.query(brokerOrg.getShortName());
            Rule masterStreamPrices = priceControl.getStreamPrice();
            Rule espChannel = priceControl.getChannel().getESP();
            brokerPMStreamPricesEnabled = masterStreamPrices.equals(Rule.ON) && (espChannel.equals(Rule.ON) || espChannel.equals(Rule.DEFAULT));
        } catch (Exception e) {
            // Do nothing
        }
        return brokerPMStreamPricesEnabled;
    }

    private static boolean isBrokerPMOrderExcnEnabled(Organization brokerOrg) {
        MasterControlSevice masterControlSevice = MarketMakerFactory.getInstance().getMasterControlService();
        boolean brokerPMOrderExcnEnabled = false;
        try {
            MasterControl masterControl = masterControlSevice.query(brokerOrg.getShortName());
            brokerPMOrderExcnEnabled = masterControl.getOrderExecution().equals(Rule.ON);
        } catch (Exception e) {
            // Do nothing
        }
        return brokerPMOrderExcnEnabled;
    }

    private void handleMasterControlChangeForLg(Organization lg, Organization broker,
                                                boolean enabled, boolean isAggregation) {
        Collection<Organization> lgLps = lg.getRelatedActiveOrganizations(OrganizationRelationship.LG_LP_RELATIONSHIP);
        if (lgLps != null && !lgLps.isEmpty()) {
            Collection<Organization> disabledProviders;
            if (broker.getBrokerOrganizationFunction() == null
                    || broker.getBrokerOrganizationFunction().getDisabledProviders() == null) {
                disabledProviders = Collections.emptyList();
            }
            else {
                disabledProviders = broker.getBrokerOrganizationFunction().getDisabledProviders();
            }
            for (Organization lgLp : lgLps) {
                if (!disabledProviders.contains(lgLp)) {
                    Organization realLp = lgLp.getRealLP() != null ? lgLp.getRealLP() : lgLp;
                    if (isValidBilateralTradingVenueProvider(realLp)) {
                        OrganizationRelationship brokerLpRelation = OrganizationUtil.getOrganizationRelationshipByClassification(broker, lgLp, OrganizationRelationship.FI_LP_RELATIONSHIP);
                        OrganizationRelationship lgLpRelation = OrganizationUtil.getOrganizationRelationshipByClassification(lg, lgLp, OrganizationRelationship.LG_LP_RELATIONSHIP);
                        if (brokerLpRelation != null && lgLpRelation != null
                                && brokerLpRelation.isCounterpartyEnabled() && lgLpRelation.isCounterpartyEnabled()) {
                            // Send notification that LP is enabled/disabled
                            if (isAggregation) {
                                handleLpStreamPricesChangeNotificationForLG(lgLp, lg, broker, enabled);
                            }
                            else {
                                handleLpOrderExecutionChangeNotificationForLG(lgLp, lg, broker, enabled);
                            }
                        }
                    }
                }
            }
        }
    }

    private void handleLPStatusChangeNotification(Organization lpOrg, Organization fiOrg, boolean enabled) {
        StreamUpdate updateMessage = new StreamUpdate();
        updateMessage.setEvent(EventTypes.LR_LP_UPDATE);
        updateMessage.setFiIndex(fiOrg.getIndex());
        updateMessage.setFiLe(fiOrg.getDefaultDealingEntity().getObjectId());
        updateMessage.setLpName(lpOrg.getName());

        Organization realProvider = lpOrg.isMasked() ? lpOrg.getRealLP() : lpOrg;

        String streamId = StreamUtil.getStreamId(fiOrg.getDefaultDealingEntity(), lpOrg);
        Stream stream = StreamUtil.getStream(streamId, realProvider);

        updateMessage.setFromStreamIndex(stream.getSuperLPIndex());
        updateMessage.setStreamName(stream.getName());
        if (enabled) {
            updateMessage.setStreamStatus(StreamUpdate.STREAM_ACTIVE);
        } else {
            updateMessage.setStreamStatus(StreamUpdate.STREAM_INACTIVE);
        }
        updateMessage.setToStreamIndex(stream.getSuperLPIndex());

        log.info("Sending streamupdate:" + updateMessage);

        publisher.publish(updateMessage);
    }

    private void handleLpStreamPricesChangeNotificationForLG(Organization lpOrg, Organization lgOrg,
                                                             Organization brokerOrg, boolean enabled) {
        //TODO: Implement this
        StreamUpdate updateMessage = new StreamUpdate();
        updateMessage.setEvent(EventTypes.LR_LP_UPDATE);
        updateMessage.setFiIndex(lgOrg.getIndex());
        updateMessage.setFiLe(lgOrg.getDefaultDealingEntity().getObjectId());
        updateMessage.setLpName(lpOrg.getName());

        Organization realProvider = lpOrg.isMasked() ? lpOrg.getRealLP() : lpOrg;

        String streamId = StreamUtil.getStreamId(brokerOrg.getDefaultDealingEntity(), lpOrg);
        Stream stream = StreamUtil.getStream(streamId, realProvider);

        updateMessage.setFromStreamIndex(stream.getSuperLPIndex());
        updateMessage.setStreamName(stream.getName());
        if (enabled) {
            updateMessage.setStreamStatus(StreamUpdate.STREAM_ACTIVE);
        } else {
            updateMessage.setStreamStatus(StreamUpdate.STREAM_INACTIVE);
        }
        updateMessage.setToStreamIndex(stream.getSuperLPIndex());

        log.info("Sending streamupdate:" + updateMessage);

        publisher.publish(updateMessage);
    }

    private void handleLpOrderExecutionChangeNotificationForLG(Organization lpOrg, Organization lgOrg,
                                                               Organization brokerOrg, boolean enabled) {
        StreamUpdate updateMessage = new StreamUpdate();
        updateMessage.setEvent(EventTypes.LR_LP_UPDATE);
        updateMessage.setFiIndex(lgOrg.getIndex());
        updateMessage.setFiLe(lgOrg.getDefaultDealingEntity().getObjectId());
        updateMessage.setLpName(lpOrg.getName());

        Organization realProvider = lpOrg.isMasked() ? lpOrg.getRealLP() : lpOrg;

        String streamId = StreamUtil.getStreamId(brokerOrg.getDefaultDealingEntity(), lpOrg);
        Stream stream = StreamUtil.getStream(streamId, realProvider);

        updateMessage.setFromStreamIndex(stream.getSuperLPIndex());
        updateMessage.setStreamName(stream.getName());
        if (enabled) {
            updateMessage.setStreamStatus(StreamUpdate.STREAM_ACTIVE);
        } else {
            updateMessage.setStreamStatus(StreamUpdate.STREAM_INACTIVE);
        }
        updateMessage.setToStreamIndex(stream.getSuperLPIndex());

        log.info("Sending streamupdate:" + updateMessage);

        publisher.publish(updateMessage);
    }

    private void handleStreamSwitchNotification(Organization lpOrg, LegalEntity fiLE, Stream oldStream, Stream newStream) {
        Stream fromStream;
        Stream toStream;
        if (lpOrg.isMasked()) {
            fromStream = StreamUtil.getStream(oldStream.getShortName(), lpOrg.getRealLP());
            toStream = StreamUtil.getStream(newStream.getShortName(), lpOrg.getRealLP());
        } else {
            fromStream = oldStream;
            toStream = newStream;
        }
        //take status from masked lp stream as well as from real lp stream
        boolean newStreamStatus = toStream.isActive() && newStream.isActive();

        StreamUpdate updateMessage = new StreamUpdate();
        updateMessage.setEvent(EventTypes.STREAM_SWITCH);
        updateMessage.setFiIndex(fiLE.getOrganization().getIndex());
        updateMessage.setFiLe(fiLE.getObjectID());
        updateMessage.setFromStreamIndex(fromStream.getSuperLPIndex());//index from real lp stream
        updateMessage.setStreamName(newStream.getName());
        updateMessage.setLpName(lpOrg.getShortName());
        if (newStreamStatus) {
            updateMessage.setStreamStatus(StreamUpdate.STREAM_ACTIVE);
        } else {
            updateMessage.setStreamStatus(StreamUpdate.STREAM_INACTIVE);
        }
        updateMessage.setToStreamIndex(toStream.getSuperLPIndex()); ////index from real lp stream

        log.info("Sending streamupdate:" + updateMessage);

        publisher.publish(updateMessage);
    }

    private void handleStreamVisibilityUpdateNotification(Organization lpOrg, LegalEntity fiLE, TradingParty tp,
                                                          int oldStreamVis, int newStreamVis) {
        Organization fiOrg = fiLE.getOrganization();
        Stream stream = tp.getBrokerStream();
        StreamUpdate updateMessage = new StreamUpdate();
        updateMessage.setEvent(EventTypes.STREAM_VISIBILITY_UPDATE);
        updateMessage.setLpName(lpOrg.getShortName());
        updateMessage.setFiIndex(fiOrg.getIndex());
        updateMessage.setFiLe(fiLE.getObjectID());
        updateMessage.setStreamName(stream == null ? "Default" : stream.getShortName());
        updateMessage.setFromStreamVisibility(oldStreamVis);
        updateMessage.setToStreamVisibility(newStreamVis);
        log.info("Sending streamupdate:" + updateMessage);
        publisher.publish(updateMessage);
    }

    public class MulticastPublisher {

        private MulticastSocket socket;

        private SocketAddress socketAddress;

        private MulticastPublisher() {
            try {
                socket = new MulticastSocket();
                socket.setTimeToLive(ConfigurationFactory.getServerMBean().getMulticastTTL());
                socketAddress = new InetSocketAddress(MDFConfigMBeanC.getInstance()
                        .getMDFBilateralCreditMulticastAddress(), MDFConfigMBeanC.getInstance()
                        .getMDFBilateralCreditMulticastPort());
            } catch (IOException e) {
                log.error("Exception in creating multicast socket", e);
            }
        }

        public void publish(StreamUpdate message) {
            try {

                UnSafeBuffer safeBuf = new UnSafeBuffer();
                byte[] buffer = new byte[message.getEstimatedSize()];
                safeBuf.init(buffer);
                // Add stream update version and its message type
                safeBuf.put(StreamUpdate.VERSION);
                safeBuf.putShort(StreamUpdate.MSG_TYPE);
                message.writeTo(safeBuf);
                socket.send(new DatagramPacket(safeBuf.array(), message.getEstimatedSize(), socketAddress));

                if (log.isDebugEnabled()) {
                    log.debug("Publishing stream update multicast message:" + message + ":on:"
                            + socketAddress.toString());
                }

            } catch (Exception e) {
                log.error("Exception in publishing multicast message:" + message, e);
            }
        }

        public void publish(CurrencyPairUpdate message) {
            try {

                UnSafeBuffer safeBuf = new UnSafeBuffer();
                byte[] buffer = new byte[message.getEstimatedSize()];
                safeBuf.init(buffer);
                // Add stream update version and its message type
                safeBuf.put(CurrencyPairUpdate.VERSION);
                safeBuf.putShort(CurrencyPairUpdate.MSG_TYPE);
                message.writeTo(safeBuf);
                socket.send(new DatagramPacket(safeBuf.array(), message.getEstimatedSize(), socketAddress));

                if (log.isDebugEnabled()) {
                    log.debug("Publishing CcyPair Update multicast message:" + message + ":on:"
                            + socketAddress.toString());
                }

            } catch (Exception e) {
                log.error("Exception in publishing multicast message:" + message, e);
            }
        }
    }
}
