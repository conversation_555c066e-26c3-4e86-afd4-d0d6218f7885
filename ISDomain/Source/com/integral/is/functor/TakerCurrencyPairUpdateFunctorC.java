package com.integral.is.functor;

import com.integral.admin.utils.marketdefinition.MarketDefinitionUtil;
import com.integral.broker.model.Stream;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.util.StreamUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.session.RemoteTransactionNotification;
import com.integral.user.Organization;

import java.util.HashMap;

public class TakerCurrencyPairUpdateFunctorC implements RemoteTransactionNotification {
    private Log log = LogFactory.getLog(TakerCurrencyPairUpdateFunctorC.class);
    public static final String QUOTE_CONVENTION = "QC";

    public void onCommit(HashMap props) {
        if (MDFConfigMBeanC.getInstance().isMDFConfigPublisherEnabled()) {
            // Taker
            String fi = (String) props.get(ISCommonConstants.TAKER_KEY);
            Organization taker = ISUtilImpl.getInstance().getOrg(fi);
            if (taker == null) {
                log.info("Taker " + fi + " not found. Hence not generating any Notifications to ME/MDF");
                return;
            }

            // QuoteConvention
            String qc = (String) props.get(QUOTE_CONVENTION);
            FXRateConvention quoteConvention = MarketDefinitionUtil.getQuoteConvention(qc);
            if (quoteConvention == null) {
                log.info("QuoteConvention " + qc + " not found. Hence not generating any Notifications to ME/MDF");
                return;
            }

            // CurrencyPair
            String cp = (String) props.get(ISCommonConstants.CURRENCY_PAIR);
            FXRateBasis rateBasis = quoteConvention.getFXRateBasis(cp);
            if (rateBasis == null) {
                log.info("CurrencyPair " + cp + " not found. Hence not generating any Notifications to ME/MDF");
                return;
            }

            String lp = (String) props.get(ISCommonConstants.MAKER_KEY);
            String action = (String) props.get(ISCommonConstants.ACTION_KEY);
            boolean enabled = (action != null && action.equals(ISCommonConstants.ENABLED_KEY));

            if (lp == null || lp.equals("ALL")) {
                ConfigChangeListener.getInstance().notifyTakerCurrencyPairChangeForAllLps(taker, rateBasis.getCurrencyPair(), enabled);
            }
            else {
                // Maker
                Organization maker = ISUtilImpl.getInstance().getOrg(lp);
                if (maker == null) {
                    log.info("Maker " + lp + " not found. Hence not generating any Notifications to ME/MDF");
                    return;
                }

                // Stream
                String streamName = (String) props.get(ISCommonConstants.STREAM);
                Stream stream;
                if (streamName != null) {
                    stream = maker.getBrokerOrganizationFunction() == null ? null : maker.getBrokerOrganizationFunction().getStream(streamName);
                }
                else {
                    stream = StreamUtil.getStream(taker.getDefaultDealingEntity(), maker);
                }
                if (stream == null) {
                    log.info("Stream " + streamName + " not found. Hence not generating any Notifications to ME/MDF");
                    return;
                }

                ConfigChangeListener.getInstance().notifyTakerCurrencyPairChangeForLp(taker, maker, rateBasis.getCurrencyPair(), stream, enabled);
            }
        }
    }
}
