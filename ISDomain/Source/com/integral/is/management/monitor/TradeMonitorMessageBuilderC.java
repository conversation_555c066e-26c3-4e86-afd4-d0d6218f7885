package com.integral.is.management.monitor;

import java.util.*;

import com.integral.SEF.SEFUtilC;
import com.integral.facade.EventTimeFacade;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.facade.QuoteEventTimeFacade;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSSPTrade;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.fx.FXTradeC;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeExecutionFlags;
import com.integral.finance.trade.TradeLeg;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.configuration.ISCommonConfigFactory;
import com.integral.is.configuration.ISCommonMBean;
import com.integral.is.log.MessageLogger;
import com.integral.is.management.GMMessageCompactionCodes;
import com.integral.is.management.ManagementConstants;
import com.integral.is.management.facade.ISQuoteEventTimeFacadeC;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.management.trade.TradeManagementObject;
import com.integral.management.trade.TradeManagementObjectC;
import com.integral.management.trade.TradeStatus;
import com.integral.mifid.MiFIDMBeanC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.provider.ProviderOrgFunction;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.util.MathUtilC;
import com.integral.util.StringUtilC;

import static com.integral.is.management.monitor.MonitorMessageBuilderUtil.getFirmOrgName;

//Copyright (c) 2001-2002 Integral Development Corp.  All rights reserved.

public class TradeMonitorMessageBuilderC implements TradeMonitorMessageBuilder
{
	private static final Log log = LogFactory.getLog( TradeMonitorMessageBuilderC.class );
	protected static final char ROW_DELIMITER = '|';
	protected static final char COLUMN_DELIMITER = '%';
	protected static final String ORDERID_MANUAL = "MANUAL";
	protected static final String ORDERID_NETTING = "NETTING";

	protected static ServerMBean serverMBean = ConfigurationFactory.getServerMBean();
	private static final IdcSessionManager current = IdcSessionManager.getInstance();
    private static final TradeConfigurationMBean tradeConfig = TradeConfigurationFactory.getTradeConfigurationMBean ();

    public TradeManagementObject createTradeMessage( Request request,
                                                     Trade trade,
                                                     String event, Map<String, Object> additionalParams )
	{
		if ( request == null || trade == null)
		{
			if(trade != null )
			{
				if(trade instanceof FXSwap)
				{
					return createSwapTradeMessage((FXSwap)trade, event, additionalParams);
				}
				else{
					return createTradeMessage(trade, event, additionalParams);
				}
			}
			log.warn("TradeMonitorMessageBuilder : Request/Trade is null for event " + event + ". " );
			return null;
		}
		try
		{
			String name = trade.getTransactionID();
			if ( ISCommonUtilC.isWarmUpObject( request ) )
			{
				name = request.getTransactionID();
				return new TradeManagementObjectC( name,serverMBean.getVirtualServerName() );
			}
            FXSingleLeg singleLeg = ( FXSingleLeg ) trade;
			FXLeg fxLeg = singleLeg.getFXLeg();
			Quote quote;
			QuoteEventTimeFacade etFacade = null;
			EventTimeFacade requestETFacade = null;
			quote = request.getAcceptedQuote();
			if ( quote != null )
			{
				etFacade = ( QuoteEventTimeFacade ) quote.getFacade( EventTimeFacade.NAME );
			}
			else
			{
				requestETFacade = ( EventTimeFacade ) request.getFacade( EventTimeFacade.NAME );
			}

			boolean isOAMakerTrade = ( request.getProperty(ISCommonConstants.OA_MAKER_TRADE_REQUEST_INDICATOR) != null );
			TradeManagementObject tmo = new TradeManagementObjectC( name,serverMBean.getVirtualServerName() );
            tmo.setCorelationId( ISCommonUtilC.getCorelationId( request ) );
			tmo.setPortfolioId(request.getPortfolioRefId());
            tmo.setHistoricalMatch(request.isHistoricalMatch());
            tmo.setTradeRequestId(request.getTransactionID());
            tmo.setIsSyntheticTrade(trade.isSyntheticCross());
            String firstTradeId = trade.getTransactionID();
            if(trade.getNettingPortfolio() != null){ //Trade created as part of portfolio allocation (batch trades).
                tmo.setRFS(true);
            }
			if(request.getOriginalFirstTrade()!=null){
				firstTradeId = request.getOriginalFirstTrade().getTransactionID();
			}
			tmo.setProviderRequestId(firstTradeId);
			
            if (event.equals(ManagementConstants.TRADE_EXPIRED)) {
            	tmo.setAmtRejectedByAdaptor(request.getRequestAttributes().getAdaptorRejectedAmountInStatusMessage());
                tmo.setAmtDKedByAdaptor(request.getRequestAttributes().getAdaptorDKedAmountInStatusMessage());
                tmo.setCancelledAmount(request.getRequestAttributes().getCancelledAmountInStatusMessage());
            }
            
            
            if( trade.getVirtualServer() != null && trade.getVirtualServer().length() != 0)
				tmo.setVirtualServerName( trade.getVirtualServer() );
			
			FXLegDealingPrice fxLegDealingPrice = ( FXLegDealingPrice ) request.getRequestPrice( ISCommonConstants.SINGLE_LEG );
			int bidOfferMode = fxLegDealingPrice.getAcceptedPriceBidOfferMode();

			tmo.setBuy( bidOfferMode != 0 );

			String ccyPair = fxLeg.getFXPayment().getFXRate().getCurrencyPair().toString();


			if ( ( fxLeg.getFXPayment().isDealtCurrency1() ) )
			{
				tmo.setDealtAmount( fxLeg.getFXPayment().getCurrency1Amount() );

			}
			else
			{
				tmo.setDealtAmount( fxLeg.getFXPayment().getCurrency2Amount() );
				ccyPair = ccyPair + "#";
			}

			tmo.setRequestDealtAmount(fxLegDealingPrice.getDealtAmount());
			tmo.setCcyPair( ccyPair );
			String fiCpty = trade.getCounterpartyA() == null ? "N/A" : trade.getCounterpartyA().getShortName();
			tmo.setFiCounterparty( fiCpty );
			String cptyB = trade.getCounterpartyB() == null ? "N/A" : trade.getCounterpartyB().getShortName();
			tmo.setCounterparty( cptyB );
			tmo.setDealType( getTradeType( request ) );

			if(trade.getTradeClassification() != null)
			{
				tmo.setTradeclassification( trade.getTradeClassification().getShortName() );
			}
			
			if (event.equals(ManagementConstants.TRADE_REJECTED)
		                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL)
		                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_REJECTED)
		                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_VERIFIED)
		                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_TIMEDOUT)
		                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_DONTKNOW)
		                    || event.equals( ManagementConstants.EXPIRED_BY_INTEGRAL_TIMEDOUT)
		                    || event.equals( ManagementConstants.EXPIRED_BY_INTEGRAL_DONTKNOW)
		            		) 
			{		  
				String rejectReason = getTradeRejectReason( request );
				tmo.setDescription( rejectReason );
                tmo.setInternalRejectReason(request.getTrade().getWorkflowStateMap().getInternalWorkflowCodeArgument());
			}
			else
			{
				tmo.setDescription( "" );
			}
			tmo.setExternalDealID( getExternalDealID( trade ) );
			tmo.setExternalDealId( "" );
			tmo.setOrderID( request.getOrderId() );
            if(trade.getRequest() != null && trade.getRequest().isNettingEnabled()) {
                tmo.setNetTradeId(trade.getRequest().getNetTradeId());
            }
            tmo.setNettedTradeIds(trade.getNettedTradeIds());
			Organization requestingOrg = request.getOrganization() == null ? request.getUser().getOrganization() : request.getOrganization();
            tmo.setOrgName( trade.getCounterpartyA().getOrganization().getShortName() );
			String providerName = getProviderName( request, trade );
			tmo.setProviderName( providerName );
			tmo.setRateProvider( getRateProvider( request ) );
            if(request.isRfq() && trade.getEntryUser() != null){
                tmo.setUserName(trade.getEntryUser().getShortName());
            }else {
                tmo.setUserName(request.getUser().getShortName());
            }
			setOriginatingUserDetails( request, tmo );

			tmo.setRate( String.valueOf( fxLeg.getFXPayment().getFXRate().getRate() ) );
            tmo.setFees(fxLeg.getFXPayment().getFees());
            tmo.setInitialSettledAmount(fxLeg.getFXPayment().getInitialSettledAmount());
            FXRate midRate = getFXLeg( trade ).getFXPayment().getFXMidRate();
            if(midRate!=null)
            {
            	  String midRateStr = Double.toString( midRate.getRate() );
            	  tmo.setMidNearRate(midRateStr);

            }

			if ( etFacade != null )
			{
				setSentReceivedTimes( tmo, etFacade, event, isOAMakerTrade );
			}
			if ( requestETFacade != null )
			{
				setSentReceivedTimes( tmo, requestETFacade, event, isOAMakerTrade );
			}
			if ( fxLeg.getFXPayment().isDealtCurrency1() )
			{
				tmo.setSettlementAmount( fxLeg.getFXPayment().getCurrency2Amount() );
			}
			else
			{
				tmo.setSettlementAmount( fxLeg.getFXPayment().getCurrency1Amount() );
			}
            if ( additionalParams != null ) {
            	
            	if ( (ManagementConstants.REJECTED_BY_INTEGRAL_TIMEDOUT.equals( event ) || ManagementConstants.REJECTED_BY_INTEGRAL_VERIFIED.equals( event )
            			|| ManagementConstants.EXPIRED_BY_INTEGRAL_DONTKNOW.equals( event ) || ManagementConstants.EXPIRED_BY_INTEGRAL_TIMEDOUT.equals( event )
            			) && additionalParams != null ) {


                    Double riskAmount = ( Double ) additionalParams.get( ISConstantsC.RISK_AMOUNT );
                    if ( riskAmount != null ) {
                        tmo.setDealtAmount( riskAmount );
                    }                
                    Double amountRejectedByAdaptor =  ( Double ) additionalParams.get( ISConstantsC.ADAPTER_REJECTED_AMOUNT );
                    if (amountRejectedByAdaptor != null && amountRejectedByAdaptor != 0.0 ) {
                        tmo.setAmtRejectedByAdaptor(amountRejectedByAdaptor);
                    }
                    Double amountDKedByAdaptor =  ( Double ) additionalParams.get( ISConstantsC.ADAPTER_DKED_AMOUNT );
                    if ( amountDKedByAdaptor!= null && amountDKedByAdaptor != 0.0 ) {
                    	tmo.setAmtDKedByAdaptor(amountDKedByAdaptor);
                    }
                    
                    Double rate = ( Double ) additionalParams.get( ISConstantsC.ACCEPTED_RATE );
                    if ( rate != null ) {
                        tmo.setRate( String.valueOf( rate ) );
                        if ( fxLeg.getFXPayment().isDealtCurrency1() ) {
                            tmo.setSettlementAmount( MathUtilC.multiply( tmo.getDealtAmount(),rate ) );
                        }
                        else {
                            tmo.setSettlementAmount( MathUtilC.correctFloatingPointsCalculationPrecision( tmo.getDealtAmount()/rate ) );
                        }
                    }
                }
            	
            	if ( (ManagementConstants.EXPIRED.equals( event ) || ManagementConstants.MATCH_EXPIRED.equals(event)) && additionalParams != null ) {
                    Double riskAmount = ( Double ) additionalParams.get( ISConstantsC.LP_EXPIRED_AMOUNT );
                    if ( riskAmount != null ) {
                        tmo.setDealtAmount( riskAmount );
                    }                
                 
                    Double amountRejectedByAdaptor =  ( Double ) additionalParams.get( ISConstantsC.ADAPTER_REJECTED_AMOUNT );
                    if (amountRejectedByAdaptor != null && amountRejectedByAdaptor != 0.0 ) {
                        tmo.setAmtRejectedByAdaptor(amountRejectedByAdaptor);
                    }
                    Double amountDKedByAdaptor =  ( Double ) additionalParams.get( ISConstantsC.ADAPTER_DKED_AMOUNT );
                    if ( amountDKedByAdaptor!= null && amountDKedByAdaptor != 0.0 ) {
                    	tmo.setAmtDKedByAdaptor(amountDKedByAdaptor);
                    }
                    
                    Double rate = (Double) additionalParams.get(ISConstantsC.ACCEPTED_RATE);
                    if (rate != null) {
                        tmo.setRate(String.valueOf(rate));
                        if (fxLeg.getFXPayment().isDealtCurrency1()) {
                            tmo.setSettlementAmount(MathUtilC.multiply(tmo.getDealtAmount(), rate));
                        } else {
                            tmo.setSettlementAmount(MathUtilC.correctFloatingPointsCalculationPrecision(tmo.getDealtAmount() / rate));
                        }
                    }
                }

            }
			tmo.setTradingParty( cptyB );
            if(ManagementConstants.TRADE_NET.equals(event)) {
                tmo.setStatus(ManagementConstants.TRADE_VERIFIED);
            } else if (ManagementConstants.TRADE_NETTED.equals(event)) {
                tmo.setStatus(ManagementConstants.TRADE_CANCELLED);
            } else {
                tmo.setStatus( event );
            }

			if ( tmo.getStatus().equals( ManagementConstants.TRADE_VERIFIED )
					|| tmo.getStatus().equals( ManagementConstants.TRADE_REJECTED )
					|| tmo.getStatus().equals( ManagementConstants.TRADE_CONFIRMED ) 
					|| tmo.getStatus().equals( ManagementConstants.TRADE_PENDING))
			{
				tmo.setValueDate( fxLeg.getFXPayment().getValueDate() != null ? fxLeg.getFXPayment().getValueDate().asJdkDate() : null );
                if ( trade != null && trade.getExecutionDateTime() != null )
				{
					tmo.setExecutionTime( trade.getExecutionDateTime().getTime() );
				}
				if ( trade != null && trade.getMakerOrderId() != null )
				{
					tmo.setMakerOrderId( trade.getMakerOrderId() );
				}
			}
			else if( tmo.getStatus().equals( ManagementConstants.TRADE_CANCELLED ) )
			{
				tmo.setValueDate( fxLeg.getFXPayment().getValueDate() != null ? fxLeg.getFXPayment().getValueDate().asJdkDate() : null);
				if ( trade != null && trade.getMakerOrderId() != null )
				{
					tmo.setMakerOrderId( trade.getMakerOrderId() );
				}
				
				//if cancelled by workflow then exclude from exception blotter.
				boolean isCancelByWorkflow =  (trade.getProperty(ISCommonConstants.Key_isCancelByWorkflow) != null ? (Boolean) trade.getProperty(ISCommonConstants.Key_isCancelByWorkflow) : false);
				tmo.setExcludeInBlotter(isCancelByWorkflow);
			}

			IdcDate tradeDate = trade.getTradeDate();
			tmo.setTradeDate( tradeDate != null ? tradeDate.asJdkDate() : null );
			boolean isSubmit = false;
			if ( tmo.getStatus().equals( ManagementConstants.TRADE_SUBMITTED ) )
			{
				isSubmit = true;
			}

            tmo.setAcceptedTier( getAcceptedTier( trade, request ) );
            tmo.setCoveredDealId( trade.getCoveredTradeTxId() );
            tmo.setExternalRequestId( request.getExternalRequestId() );
            tmo.setCorelationId( ISCommonUtilC.getCorelationId( request ) );
            tmo.setStreamId( trade.getStream() );
            addCustomFieldstoTradeObject( request, trade, tmo, isSubmit, isOAMakerTrade );
            if (TradeStatus.REJECTED_BY_INTEGRAL_VERIFIED.equals(event)
                		 || TradeStatus.REJECTED_BY_INTEGRAL_TIMEDOUT.equals( event ) 
                		 || TradeStatus.REJECTED_BY_INTEGRAL_DONTKNOW.equals(event)
                		 || TradeStatus.EXPIRED.equals( event ) 
                		 || TradeStatus.EXPIRED_BY_INTEGRAL_DONTKNOW.equals(event)
                		 || TradeStatus.EXPIRED_BY_INTEGRAL_TIMEDOUT.equals( event ) 
                		) {
	            if ( additionalParams != null )
                {
                    String dummyTradeId = ( String ) additionalParams.get( ISConstantsC.DUMMY_TRADE_ID );
                    if ( dummyTradeId != null )
                    {
                        tmo.setTradeTxnId( new StringBuilder( 30 ).append( tmo.getTradeTxnId() ).append( '_' ).append( dummyTradeId ).toString() );
                    }
                    String providerTradeId = ( String ) additionalParams.get( ISConstantsC.PROVIDER_TRADE_ID );
                    if( providerTradeId != null )
                    {
                        tmo.setExternalDealID( providerTradeId );
                    }
                }
            }

            if (event.equals(ManagementConstants.TRADE_RISK_POSITION)) {
                tmo.setRiskPositionsCount(request.getRequestAttributes().getPendingRequestCount());
                tmo.setRiskPositionTotalAmount(request.getRequestAttributes().getRiskPositionTotalAmount());
                tmo.setRiskPositionsAvgRate(request.getRequestAttributes().getRiskPositionAvgRate());
            }
            if( ManagementConstants.TRADE_VERIFIED.equals( event ) || ManagementConstants.TRADE_NET.equals(event) ){
                Request limitRequest = request.getParentRequest();
                if( limitRequest != null ){
                    FXLegDealingPrice limitRequestPrice = (FXLegDealingPrice) limitRequest.getRequestPrice(ISCommonConstants.SINGLE_LEG);
                    tmo.setOrderAverageRate( limitRequestPrice.getAverageRate() != null ? limitRequestPrice.getAverageRate() : 0.0 );
                }
            }

            setCptyOrgs( trade, tmo );
            setAdaptorName(request, tmo);
            setSalesDealerFields(trade, tmo);
            tmo.setCoveredTradeLE(request.getRequestAttributes().getCoveredTradeLE());
            tmo.setCoveredTradeUser(request.getRequestAttributes().getCoveredTradeUser());
            tmo.setClientTag(trade.getClientTag());
            setSEFDetailsOnTMO(tmo, trade, event, additionalParams);
            setMiFIDDetailsOnTMO( tmo, trade );
            String nextQuoteEvent = trade.getNextQuoteEvent();
            if(nextQuoteEvent!=null){
                tmo.setNextRateReceivedAt(nextQuoteEvent);
                tmo.setNextRateCompare(Character.toString(trade.getNextQuoteCompareToMatch()));
            }else{
                tmo.setNextRateReceivedAt(ManagementConstants.NOT_APPLICABLE_STR);
                tmo.setNextRateCompare(ManagementConstants.NOT_APPLICABLE_STR);
            }
            if(trade.getRequest() != null) {
                tmo.setExcludeInOrderDetail(trade.getRequest().isNettingEnabled());
            } else {
                tmo.setExcludeInOrderDetail(false);
            }
            tmo.setBenchMarkRate1(trade.getMidBenchmarkRate1());
            tmo.setBenchMarkRate2(trade.getMidBenchmarkRate2());
            tmo.setBenchMarkFwdPoints( trade.getMidBenchmarkFwdPoints() );
            tmo.setBenchMarkAllInRate( ((FXTradeC )trade).getMidBenchmarkAllinRateStr());
            tmo.setMidSpotRate(trade.getMidSpotRate());
            setYMSpecificAttributes(trade, tmo);
            populateUSDAmountsIfMissing(tmo, false);
            if(trade.getCreditValuationAdjustment() != null){
                tmo.setCreditValuationAdjustment(trade.getCreditValuationAdjustment());
            }

            if ( trade.getCustomParameters () != null )
            {
                Map<String, String> customParametersMap = trade.getCustomParametersMap();
                if ( customParametersMap != null && !customParametersMap.isEmpty () )
                {
                    Map<String, String> customParametersForGM = new HashMap<String, String> ( customParametersMap.size () );
                    for ( String param: customParametersMap.keySet () )
                    {
                        String value = tradeConfig.getTradeCustomParameterWorkflowValue ( requestingOrg, param, TradeConfigurationMBean.CustomParameterWorkflowAspect.GM );
                        if ( Boolean.TRUE.toString ().equalsIgnoreCase ( value ) || IdcUtilC.YES.equalsIgnoreCase ( value ) )
                        {
                            customParametersForGM.put ( param, customParametersMap.get ( param ) );
                        }
                    }
                    if ( !customParametersForGM.isEmpty () )
                    {
                        tmo.setCustomParameters ( IdcUtilC.serializeMapToJSON ( customParametersForGM ) );
                    }
                }
            }
            log.info("TradeMonitorMessageBuilderC.createTradeMessageRequest : ExternalRequestId = " + request.getExternalRequestId() + " for Trade = " + name);
            return tmo;
        }
        catch ( Exception e )
        {
            MessageLogger.getInstance().log( "TradeMessageCreate", this.getClass().getName(),
                    "Exception while creating Trade Message" + e.getMessage(), new StringBuilder( "For TxnId [" ).
                            append( request.getTransactionID() ).append( "]" ).append( "For Trade TxnId [" ).
                            append( request.getTrade().getTransactionID() ).toString() );
            log.error( "TradeMonitorMessageBuilderC.createTradeMessage.ERROR : Entity : " + trade, e );
        }
        return null;
    }

    protected void setYMSpecificAttributes(Trade trade, TradeManagementObject tmo) {
        tmo.setYmCptyABookName(trade.getCptyABookName());
        tmo.setYmCptyBBookName(trade.getCptyBBookName());
        tmo.setRfsNetSpotBaseAmount(trade.getNetSpotAmount());
        tmo.setRfsNetSpotTermAmount(trade.getNetSpotAmountTerm());
        tmo.setRfsNetSpotBuyBase(trade.isNetBuyingBase());
    }

    private TradeManagementObject createSwapTradeMessage(FXSwap trade, String event, Map<String, Object> additionalParams )
    {
        try
        {
                String name = trade.getTransactionID();

                TradeManagementObject tmo = new TradeManagementObjectC( name, serverMBean.getVirtualServerName() );
                if( trade.getRequest() != null )
    			{
    				tmo.setCorelationId( ISCommonUtilC.getCorelationId( trade.getRequest() ) );
    				tmo.setPortfolioId(trade.getRequest().getPortfolioRefId());
    			    tmo.setTradeRequestId(trade.getTransactionID());
    	            tmo.setProviderRequestId(trade.getTransactionID());
                    tmo.setExternalRequestId( trade.getRequest().getExternalRequestId() );
                    log.info("TradeMonitorMessageBuilderC.createSwapTradeMessage : ExternalRequestId = " + trade.getRequest().getExternalRequestId() + " for Trade = " + name);
    			}
                else if(trade.getPortfolioRefId() != null)
                {
                    tmo.setCorelationId(trade.getPortfolioRefId());
                }
                else
                {
                    tmo.setCorelationId(trade.getTransactionID());
                }

                FXPaymentParameters fxPayment = getFXLeg(trade).getFXPayment();
                FXLegDealingPrice price = createPrice(fxPayment);
                tmo.setRFS( true );

    			if( trade.getVirtualServer() != null && trade.getVirtualServer().length() != 0)
    				tmo.setVirtualServerName( trade.getVirtualServer() );
    			

               // request null handled for SWAP
                    getMultiLegParameters( trade, tmo );

                tmo.setTradeclassification( trade.getTradeClassification().getShortName() );
    			String fiCpty = trade.getCounterpartyA() == null ? "N/A" : trade.getCounterpartyA().getShortName();
    			tmo.setFiCounterparty( fiCpty );
    			String cptyB = trade.getCounterpartyB() == null ? "N/A" : trade.getCounterpartyB().getShortName();
    			tmo.setCounterparty( cptyB );
    			// request null handled for SWAP
    			double deltAmt = getDealtNotional( trade );
                tmo.setDealtAmount( deltAmt);
                tmo.setRequestDealtAmount(deltAmt);
                tmo.setDealType( "Taker" ); // hard coded every manual trade as Taker
                tmo.setIsSyntheticTrade(trade.isSyntheticCross());
                if (event.equals(ManagementConstants.TRADE_REJECTED)
	                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL)
	                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_REJECTED)
	                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_VERIFIED)
	                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_TIMEDOUT)
	                    || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_DONTKNOW)
	                    || event.equals( ManagementConstants.EXPIRED_BY_INTEGRAL_TIMEDOUT)
	                    || event.equals( ManagementConstants.EXPIRED_BY_INTEGRAL_DONTKNOW)
	            		) {
    				String rejectReason = getTradeRejectReason( trade );
    				tmo.setDescription( rejectReason );
                    if( trade.getWorkflowStateMap() != null ){
                        tmo.setInternalRejectReason( trade.getWorkflowStateMap().getInternalWorkflowCodeArgument() );
                    }
                }
                else
                {
                    tmo.setDescription( "" );
                }
                tmo.setExternalDealID( getExternalDealID( trade ) );
                tmo.setExternalDealId( "" );
                tmo.setOrderID(  ORDERID_MANUAL );
//TODO   			Organization requestingOrg = trade.getOrganization() == null ? trade.getUser().getOrganization() : trade.getOrganization();
//                Organization requestingOrg = trade.getOrganization() ;
                tmo.setOrgName( trade.getCounterpartyA().getOrganization().getShortName() );
    			tmo.setProviderName( trade.getCounterpartyB().getOrganization().getShortName() );
    			String providerName = getProviderName(  trade );
                tmo.setProviderName( providerName );
               tmo.setRateProvider( providerName ); //manula trade both providers same
                setOriginatingUserDetails( trade, tmo );
                String rate = Double.toString( getFXLeg( trade ).getFXPayment().getFXRate().getRate() );
                tmo.setRate( rate );

                FXRate midRate = getFXLeg( trade ).getFXPayment().getFXMidRate();
                if(midRate!=null)
                {
                	  String midRateStr = Double.toString( midRate.getRate() );
                	  tmo.setMidNearRate(midRateStr);

                }

                tmo.setCancelledByUser( (String)trade.getProperty("CancelledByUser") );
                tmo.setReceivedDate( new Date( System.currentTimeMillis() ) );
                tmo.setSentDate( new Date( System.currentTimeMillis() ) );

                FXLeg fxLeg = getFXLeg( trade );

                if ( fxLeg.getFXPayment().isDealtCurrency1() )
                {
                    tmo.setSettlementAmount( fxLeg.getFXPayment().getCurrency2Amount() );
                }
                else
                {
                    tmo.setSettlementAmount( fxLeg.getFXPayment().getCurrency1Amount() );
                }

    			tmo.setSettlementAmountAsUSD( trade.getSettledAmountInUSD() == null ? 0 : trade.getSettledAmountInUSD());
                tmo.setTradingParty( cptyB );
                tmo.setStatus( event );
                tmo.setUserName( trade.getEntryUser().getShortName());
                tmo.setValueDate( fxLeg.getFXPayment().getValueDate() != null ? fxLeg.getFXPayment().getValueDate().asJdkDate() : null);
                if ( tmo.getStatus().equals( ManagementConstants.TRADE_VERIFIED ) || tmo.getStatus().equals( ManagementConstants.TRADE_REJECTED ) || tmo.getStatus().equals( ManagementConstants.TRADE_CONFIRMED ) )
                {
                    if ( trade.getExecutionDateTime() != null )
                    {
                        tmo.setExecutionTime( trade.getExecutionDateTime().getTime() );
                    }
                }
                boolean isBuyingCcy1;
                Currency ccy;
                Currency dealtCcy;
                dealtCcy = price.getDealtCurrency();
                if ( fxPayment != null && dealtCcy != null )
                {
                    isBuyingCcy1 = fxPayment.isBuyingCurrency1();
                    if ( isBuyingCcy1 )
                    {
                        ccy = fxPayment.getCurrency1();
                    }
                    else
                    {
                        ccy = fxPayment.getCurrency2();
                    }
                    tmo.setCptyABuyingDealtCurrency( dealtCcy.isSameAs( ccy ) );
                }


                if ( tmo.getStatus().equals( ManagementConstants.TRADE_VERIFIED )
    					|| tmo.getStatus().equals( ManagementConstants.TRADE_REJECTED )
    					|| tmo.getStatus().equals( ManagementConstants.TRADE_CONFIRMED )
                        || tmo.getStatus().equals( ManagementConstants.MANUAL )
                        || tmo.getStatus().equals( ManagementConstants.MANUAL_COVER ))
    			{
    				tmo.setValueDate( fxLeg.getFXPayment().getValueDate() !=null ? fxLeg.getFXPayment().getValueDate().asJdkDate() : null);
    				if ( trade != null && trade.getExecutionDateTime() != null )
    				{
    					tmo.setExecutionTime( trade.getExecutionDateTime().getTime() );
    				}
    				if ( trade != null && trade.getMakerOrderId() != null )
    				{
    					tmo.setMakerOrderId( trade.getMakerOrderId() );
    				}
    			}
    			else if( tmo.getStatus().equals( ManagementConstants.TRADE_CANCELLED ) )
    			{
                    // bug id 60340
                    if(null != fxLeg && null != fxLeg.getFXPayment() && null != fxLeg.getFXPayment().getValueDate())
                    {
                        tmo.setValueDate( fxLeg.getFXPayment().getValueDate().asJdkDate() );
                    }
    				if ( trade != null && trade.getMakerOrderId() != null )
    				{
    					tmo.setMakerOrderId( trade.getMakerOrderId() );
    				}
    				//if cancelled by workflow then exclude from exception blotter.
    				boolean isCancelByWorkflow =  (trade.getProperty(ISCommonConstants.Key_isCancelByWorkflow) != null ? (Boolean) trade.getProperty(ISCommonConstants.Key_isCancelByWorkflow) : false);
    				tmo.setExcludeInBlotter(isCancelByWorkflow);
    			}

                IdcDate tradeDate;
                tradeDate = trade.getTradeDate();
                tmo.setTradeDate( tradeDate != null ? tradeDate.asJdkDate() : null);
                boolean isSubmit = false;
                if ( tmo.getStatus().equals( ManagementConstants.TRADE_SUBMITTED ) )
                {
                    isSubmit = true;
                }

                tmo.setAcceptedTier( getAcceptedTier( trade ) );
                tmo.setCoveredDealId( trade.getCoveredTradeTxId() );
                tmo.setOrderTxnId( trade.getTransactionID() );
                tmo.setStreamId( trade.getStream() );
                tmo.setTradeChannel(trade.getChannel());
//TODO                tmo.setExternalRequestId( trade.getExternalRequestId() );
              //TODO               addCustomFieldstoTradeObject( request, trade, tmo, isSubmit );
                setCptyOrgs( trade, tmo );
              //TODO                setAdaptorName( request, tmo );
                setSalesDealerFields( trade, tmo );
//                if ( TradeStatus.REJECTED_BY_INTEGRAL_VERIFIED.equals( event ) ) {
//                    if ( additionalParams != null ) {
//                        String dummyTradeId = ( String ) additionalParams.get( ISConstantsC.DUMMY_TRADE_ID );
//                        if ( dummyTradeId != null ) {
//                            tmo.setTradeTxnId( new StringBuilder( 30 ).append( tmo.getTradeTxnId() ).append( '_' ).append( dummyTradeId ).toString() );
//                        }
//                        String providerTradeId = ( String ) additionalParams.get( ISConstantsC.PROVIDER_TRADE_ID );
//                        if ( providerTradeId != null ) {
//                            tmo.setExternalDealID( providerTradeId );
//                        }
//                    }
//                }
              //TODO               tmo.setCoveredTradeLE( trade.getRequestAttributes().getCoveredTradeLE());
              //TODO                tmo.setCoveredTradeUser( trade.getRequestAttributes().getCoveredTradeUser());
                tmo.setFixingDate( price.getFixingDate() != null ? price.getFixingDate().asJdkDate() : null);
                tmo.setFixingTenor( price.getFixingTenor() == null ? "" : price.getFixingTenor().getName());
                tmo.setClientTag(trade.getClientTag());
                tmo.setPortfolioId(trade.getPortfolioRefId());
                setSEFDetailsOnTMO(tmo, trade, event, additionalParams);
                setMiFIDDetailsOnTMO( tmo, trade );
                tmo.setBenchMarkRate1(trade.getMidBenchmarkRate1());
                tmo.setBenchMarkRate2(trade.getMidBenchmarkRate2());
                tmo.setBenchMarkFwdPoints( trade.getMidBenchmarkFwdPoints() );
                tmo.setBenchMarkAllInRate( trade.getMidBenchmarkAllinRateStr());
                setYMSpecificAttributes(trade, tmo);
				populateUSDAmountsIfMissing(tmo, true);
                if(trade.getCreditValuationAdjustment() != null){
                    tmo.setCreditValuationAdjustment(trade.getCreditValuationAdjustment());
                }
            if ( trade.getCustomParameters () != null )
            {
                Map<String, String> customParametersMap = trade.getCustomParametersMap();
                if ( customParametersMap != null && !customParametersMap.isEmpty () )
                {
                    Organization cptyAOrg = trade.getCounterpartyA ().getOrganization ();
                    Map<String, String> customParametersForGM = new HashMap<String, String> ( customParametersMap.size () );
                    for ( String param: customParametersMap.keySet () )
                    {
                        String value = tradeConfig.getTradeCustomParameterWorkflowValue ( cptyAOrg, param, TradeConfigurationMBean.CustomParameterWorkflowAspect.GM );
                        if ( Boolean.TRUE.toString ().equalsIgnoreCase ( value ) || IdcUtilC.YES.equalsIgnoreCase ( value ) )
                        {
                            customParametersForGM.put ( param, customParametersMap.get ( param ) );
                        }
                    }
                    if ( !customParametersForGM.isEmpty () )
                    {
                        tmo.setCustomParameters ( IdcUtilC.serializeMapToJSON ( customParametersForGM ) );
                    }
                }
            }
            if(trade.getClientRequestId() != null){
                tmo.setExternalRequestId(trade.getClientRequestId());
                tmo.setExternalDealID(trade.getClientRequestId());
                tmo.setExternalDealId(trade.getClientRequestId());
            }else if(trade.getPortfolioRefId()!=null){
                tmo.setExternalRequestId(trade.getPortfolioRefId());
            }
                return tmo;
            }
        catch ( Exception e )
        {
            MessageLogger.getInstance().log( "TradeMessageCreate", this.getClass().getName(),
                    "Exception while creating Trade Message" + e.getMessage(), new StringBuffer( "For TxnId [" ).
                            append( trade.getTransactionID() ).append( "]" ).append( "For Trade TxnId [" ).
                            append( trade.getTransactionID() ).toString() );
            log.error( "RFSTradeMonitorMessageBuilderC.createTradeMessage.ERROR : Entity : " + trade, e );
        }
        return null;
    }

    private TradeManagementObject createTradeMessage(Trade trade,String event, Map<String, Object> additionalParams)
	{
		try
		{
			String name = trade.getTransactionID();
			if ( ISCommonUtilC.isWarmUpObject( trade ) )
			{
				name = trade.getTransactionID();
				return new TradeManagementObjectC( name, serverMBean.getVirtualServerName() );
			}

			FXSingleLeg singleLeg = ( FXSingleLeg ) trade;
			FXLeg fxLeg = singleLeg.getFXLeg();
			TradeManagementObject tmo = new TradeManagementObjectC( name, serverMBean.getVirtualServerName() );
			if( trade.getRequest() != null )
			{
				tmo.setCorelationId( ISCommonUtilC.getCorelationId( trade.getRequest() ) );
				tmo.setPortfolioId(trade.getRequest().getPortfolioRefId());
                tmo.setHistoricalMatch(trade.getRequest().isHistoricalMatch());
                tmo.setTradeRequestId(trade.getRequest().getTransactionID());
                String firstTradeId = trade.getTransactionID();
                
    			if(trade.getRequest().getOriginalFirstTrade()!=null){
    				firstTradeId = trade.getRequest().getOriginalFirstTrade().getTransactionID();
    			}
    			tmo.setProviderRequestId(firstTradeId);
                tmo.setExternalRequestId( trade.getRequest().getExternalRequestId() );
                log.info("TradeMonitorMessageBuilderC.createTradeMessage : ExternalRequestId = " + trade.getRequest().getExternalRequestId() + " for Trade = " + name);
    			
			}
            else if(trade.getPortfolioRefId() != null)
            {
                tmo.setCorelationId(trade.getPortfolioRefId());
            }
            else
            {
                tmo.setCorelationId(trade.getTransactionID());
            }
			
			if(trade.getParentTradeTxId() != null){
				tmo.setParentTradeTxId(trade.getParentTradeTxId());
			}
            if(trade.getCreditValuationAdjustment() != null){
                tmo.setCreditValuationAdjustment(trade.getCreditValuationAdjustment());
            }
            if ( trade.getCustomParameters () != null )
            {
                Map<String, String> customParametersMap = trade.getCustomParametersMap();
                if ( customParametersMap != null && !customParametersMap.isEmpty () )
                {
                    Organization cptyAOrg = trade.getCounterpartyA().getOrganization();
                    Map<String, String> customParametersForGM = new HashMap<String, String> ( customParametersMap.size () );
                    for ( String param: customParametersMap.keySet () )
                    {
                        String value = tradeConfig.getTradeCustomParameterWorkflowValue ( cptyAOrg, param, TradeConfigurationMBean.CustomParameterWorkflowAspect.GM );
                        if ( Boolean.TRUE.toString ().equalsIgnoreCase ( value ) || IdcUtilC.YES.equalsIgnoreCase ( value ) )
                        {
                            customParametersForGM.put ( param, customParametersMap.get ( param ) );
                        }
                    }
                    if ( !customParametersForGM.isEmpty () )
                    {
                        tmo.setCustomParameters ( IdcUtilC.serializeMapToJSON ( customParametersForGM ) );
                    }
                }
            }
			if(fxLeg.getFXPayment().isBuyingCurrency1())
				tmo.setBuy( true );
			else
				tmo.setBuy( false );

			String ccyPair = fxLeg.getFXPayment().getFXRate().getCurrencyPair().toString();

			if ( ( fxLeg.getFXPayment().isDealtCurrency1() ) )
			{
                tmo.setDealtAmount( fxLeg.getFXPayment().getCurrency1Amount() );
			}
			else
			{
				tmo.setDealtAmount( fxLeg.getFXPayment().getCurrency2Amount() );
				ccyPair = ccyPair + "#";
			}

			tmo.setRequestDealtAmount( tmo.getDealtAmount() ); // currency1/currency2 amount from FXPayment
			tmo.setCcyPair( ccyPair );
			tmo.setFiCounterparty( trade.getCounterpartyA().getShortName() );
			String cptyB = trade.getCounterpartyB() == null ? "N/A" : trade.getCounterpartyB().getShortName();
			tmo.setCounterparty( cptyB );
			tmo.setDealType( "Taker" ); // hard coded every manual trade as Taker
            if("RFS".equals(trade.getPricingType())) tmo.setRFS(true);

            tmo.setTradeclassification( trade.getTradeClassification().getShortName() );
			tmo.setExternalDealID( getExternalDealID( trade ) );
			tmo.setExternalDealId( "" );
            TradeStateFacade tsf = ( TradeStateFacade ) trade.getFacade( TradeStateFacade.TRADE_STATE_FACADE );
			tmo.setOrderID( tsf.isNet () ? ORDERID_NETTING : ORDERID_MANUAL );
			tmo.setOrgName( trade.getCounterpartyA().getOrganization().getShortName() );
			tmo.setProviderName( trade.getCounterpartyB().getOrganization().getShortName() );
			tmo.setRateProvider( tmo.getProviderName() ); // Same as ProviderName
			tmo.setUserName( trade.getEntryUser().getShortName() );
			tmo.setRate( String.valueOf( fxLeg.getFXPayment().getFXRate().getRate() ) );
            tmo.setIsSyntheticTrade(trade.isSyntheticCross());
            FXRate midRate = getFXLeg( trade ).getFXPayment().getFXMidRate();
            if(midRate!=null)
            {
            	  String midRateStr = Double.toString( midRate.getRate() );
            	  tmo.setMidNearRate(midRateStr);

            }

            tmo.setCancelledByUser( (String)trade.getProperty("CancelledByUser") );
            tmo.setReceivedDate( new Date( System.currentTimeMillis() ) );
            tmo.setSentDate( new Date( System.currentTimeMillis() ) );

			if ( fxLeg.getFXPayment().isDealtCurrency1() )
			{
				tmo.setSettlementAmount( fxLeg.getFXPayment().getCurrency2Amount() );
			}
			else
			{
				tmo.setSettlementAmount( fxLeg.getFXPayment().getCurrency1Amount() );
			}

			tmo.setSettlementAmountAsUSD( trade.getSettledAmountInUSD() == null ? 0 : trade.getSettledAmountInUSD());

			tmo.setTradingParty( cptyB );
            if(ManagementConstants.TRADE_NET.equals(event)) {
                tmo.setStatus(ManagementConstants.TRADE_VERIFIED);
            } else if (ManagementConstants.TRADE_NETTED.equals(event)) {
                tmo.setStatus(ManagementConstants.TRADE_CANCELLED);
            } else {
                tmo.setStatus( event );
            }

			if ( tmo.getStatus().equals( ManagementConstants.TRADE_VERIFIED )
					|| tmo.getStatus().equals( ManagementConstants.TRADE_REJECTED )
					|| tmo.getStatus().equals( ManagementConstants.TRADE_CONFIRMED )
                    || tmo.getStatus().equals( ManagementConstants.MANUAL )
                    || tmo.getStatus().equals( ManagementConstants.MANUAL_COVER ))
			{
				tmo.setValueDate( fxLeg.getFXPayment().getValueDate() != null ? fxLeg.getFXPayment().getValueDate().asJdkDate() : null);
                final Tenor tenor = fxLeg.getFXPayment().getTenor();
                if ( tenor != null )
                {
                    tmo.setNearTenor( tenor.toString() );
                }
				if ( trade != null && trade.getExecutionDateTime() != null )
				{
					tmo.setExecutionTime( trade.getExecutionDateTime().getTime() );
				}
				if ( trade != null && trade.getMakerOrderId() != null )
				{
					tmo.setMakerOrderId( trade.getMakerOrderId() );
				}
			}
			else if( tmo.getStatus().equals( ManagementConstants.TRADE_CANCELLED ) )
			{
                // bug id 60340
                if(null != fxLeg && null != fxLeg.getFXPayment() && null != fxLeg.getFXPayment().getValueDate())
                {
                    tmo.setValueDate( fxLeg.getFXPayment().getValueDate().asJdkDate() );
                }
				if ( trade != null && trade.getMakerOrderId() != null )
				{
					tmo.setMakerOrderId( trade.getMakerOrderId() );
				}
				//if cancelled by workflow then exclude from exception blotter.
				boolean isCancelByWorkflow =  (trade.getProperty(ISCommonConstants.Key_isCancelByWorkflow) != null ? (Boolean) trade.getProperty(ISCommonConstants.Key_isCancelByWorkflow) : false);
				tmo.setExcludeInBlotter(isCancelByWorkflow);
			}
			IdcDate tradeDate = trade.getTradeDate();
			tmo.setTradeDate( tradeDate != null ? tradeDate.asJdkDate() : null);
			tmo.setCoveredDealId( trade.getCoveredTradeTxId() );
            setCptyOrgs( trade, tmo );

            FXSingleLeg fxSingleLeg = ( FXSingleLeg ) trade;
            FXPaymentParameters fxPayment = fxSingleLeg.getFXLeg().getFXPayment();
            FXRate fxRate = fxPayment.getFXRate();
            if ( fxRate != null )
            {
                tmo.setNearSpotRate( "" + fxRate.getSpotRate() );
                tmo.setNearFwdPoint( fxRate.getForwardPoints() );
            }

            midRate = fxPayment.getFXMidRate();
            if ( midRate != null )
            {
            	tmo.setMidNearSpotRate( "" + midRate.getSpotRate() );
                tmo.setMidNearFwdPoint( midRate.getForwardPoints() );
            }


            Currency dealtCcy;
            if ( fxPayment.isDealtCurrency1() )
            {
                dealtCcy = fxRate.getBaseCurrency();
            }
            else
            {
                dealtCcy = fxRate.getVariableCurrency();
            }
            if ( fxPayment != null && dealtCcy != null )
            {
                boolean isBuyingCcy1 = fxPayment.isBuyingCurrency1();
                Currency ccy;
                if ( isBuyingCcy1 )
                {
                    ccy = fxPayment.getCurrency1();
                }
                else
                {
                    ccy = fxPayment.getCurrency2();
                }
                tmo.setCptyABuyingDealtCurrency( dealtCcy.isSameAs( ccy ) );
            }
            setSalesDealerFields( trade, tmo );
            tmo.setFixingDate( fxPayment.getFixingDate() != null ? fxPayment.getFixingDate().asJdkDate() : null);
            tmo.setFixingTenor( fxPayment.getFixingTenor() == null ? "" : fxPayment.getFixingTenor().getName());
            tmo.setClientTag(trade.getClientTag());
            tmo.setPortfolioId(trade.getPortfolioRefId());
            tmo.setTradeChannel(trade.getChannel());
            tmo.setClientTag(trade.getClientTag());
			setOriginatingUserDetails( trade, tmo );
            setSEFDetailsOnTMO(tmo, trade, event, additionalParams);
            setMiFIDDetailsOnTMO( tmo, trade );
            //tmo.setPortfolioId(trade.getPortfolioRefId());
            if(trade.getRequest() != null) {
                tmo.setExcludeInOrderDetail(trade.getRequest().isNettingEnabled());
            } else {
                tmo.setExcludeInOrderDetail(false);
            }
            tmo.setBenchMarkRate1(trade.getMidBenchmarkRate1());
            tmo.setBenchMarkRate2(trade.getMidBenchmarkRate2());
            tmo.setBenchMarkFwdPoints( trade.getMidBenchmarkFwdPoints() );
            tmo.setBenchMarkAllInRate( ((FXTradeC )trade).getMidBenchmarkAllinRateStr());
            tmo.setMidSpotRate(trade.getMidSpotRate());
            setYMSpecificAttributes(trade, tmo);

            if ( ManagementConstants.TRADE_NET.equalsIgnoreCase ( event  ) )
            {
                if ( tsf.isNet () )
                {
                    tmo.setNettedTradeIds ( trade.getNettedTradeIds () );
                    tmo.setTradeTxnId ( trade.getTransactionID () );
                    tmo.setExternalDealID ( "" );
                }
            }
            if ( ManagementConstants.TRADE_NETTED.equalsIgnoreCase ( event  ) )
            {
                if ( tsf.isNetted () )
                {
                    tmo.setNetTradeId ( trade.getNetTrade () != null ? trade.getNetTrade ().getTransactionID () : null );
                }
            }
            if(trade.getClientRequestId() != null){
                tmo.setExternalRequestId(trade.getClientRequestId());
                tmo.setExternalDealID(trade.getClientRequestId());
                tmo.setExternalDealId(trade.getClientRequestId());
            }else if(trade.getPortfolioRefId()!=null){
                tmo.setExternalRequestId(trade.getPortfolioRefId());
            }
            populateUSDAmountsIfMissing(tmo, false);
            return tmo;
		}
		catch ( Exception e )
		{
			log.error( "TradeMonitorMessageBuilderC.createTradeMessage.ERROR : Entity : " + trade +",Event "+event, e );
		}
		return null;
	}


    protected void setMiFIDDetailsOnTMO(TradeManagementObject tmo, Trade trade)
    {
        if( MiFIDMBeanC.getInstance().isMiFIDEnabled())
        {
            tmo.setMTFTrade( trade.isMTFTrade() );
            tmo.setExecutingVenue( trade.getExecutionVenue()!=null?trade.getExecutionVenue().getShortName():null );
            tmo.setMiFIDTradeParamsStr( trade.getMiFIDTradeParamsStr() );

            TradeLeg leg = trade.getTradeLeg( ISCommonConstants.NEAR_LEG );
            if(leg!=null)
            {
                tmo.setISIN( leg.getISIN() );
                TradeLeg farLeg = trade.getTradeLeg( ISCommonConstants.FAR_LEG );
                if(farLeg!=null){
                    tmo.setFarISIN( farLeg.getISIN() );
                }
                tmo.setISINLinkId( trade.getISINLinkId() );
            }
            else
            {
                leg = trade.getTradeLeg(FXSingleLeg.SINGLE_LEG);
                if(leg!=null){
                    tmo.setISIN( leg.getISIN() );
                }
            }
        }
    }

    protected void setSEFDetailsOnTMO(TradeManagementObject tmo, Trade trade, String event, Map<String, Object> additionalParams)
    {
        tmo.setCptyALEI( trade.getCounterpartyALEI() );
        tmo.setCptyBLEI( trade.getCounterpartyBLEI() );
        tmo.setUPI( trade.getUPI() );
        tmo.setUSI( SEFUtilC.getUSI( trade ) );
        tmo.setUTI( SEFUtilC.getUTI( trade ) );
        tmo.setSEF( trade.isSEF() );
        tmo.setFarUSI( SEFUtilC.getFarUSI( trade ) );
        tmo.setFarUTI( SEFUtilC.getFarUTI( trade ) );
        tmo.setCHAckId(trade.getClearingHouseAckId());
        tmo.setCHStatus(trade.getClearingHouseTradeStatus());
        tmo.setBuySideClearedUSI(trade.getBuySideClearedUSI());
        tmo.setSellSideClearedUSI(trade.getSellSideClearedUSI());
        tmo.setSDRAckId(trade.getSDRAckId());
        tmo.setSDRStatus(trade.getSDRTradeStatus());
        tmo.setCreditLimitHubId(trade.getCreditLimitHubId());
        tmo.setCreditLimitHubStatus(trade.getCreditLimitHubStatus());
        if(ManagementConstants.CH_SDR_UPDATE_EVENT.equals(event) && (null != additionalParams))
        {
        	String errorReason = (String)additionalParams.get(ManagementConstants.CH_SDR_ERRORREASON_KEY);
        	if(!StringUtilC.isNullOrEmpty(errorReason) && !"null".equals(errorReason))
        	{
        		tmo.setExceptionDescription( errorReason );
        		tmo.setExceptionStatus(ManagementConstants.CH_SDR_UPDATE_EVENT);
        	}
        }
    }

    protected void setSalesDealerFields( Trade trade, TradeManagementObject tmo )
    {
    	if( trade.getSalesDealerUser() != null && trade.getSalesDealerCounterparty() != null )
        {
        	if( log.isDebugEnabled() )
        	{
        		log.debug("Entry User " + trade.getEntryUser() + " - Counterparty A : " +  trade.getCounterpartyA());
        		log.debug(" State " +  trade.getState() + "  - Counterparty B : " +  trade.getCounterpartyB());
        	}
    		tmo.setSalesDealerOrg( trade.getSalesDealerUser().getOrganization().getShortName() );
            tmo.setSalesDealerLegalEntity( trade.getSalesDealerCounterparty().getShortName() );
            if( log.isDebugEnabled())
        	{
            	log.debug("Trade Lookup Info: Sales Dealer " + trade.getSalesDealerUser().getOrganization().getShortName() + "  - Counterparty : " +  trade.getSalesDealerCounterparty().getShortName());
        	}
        }
    }

	protected String getRateProvider( Request request )
	{
		try
		{
			Quote quote = request.getAcceptedQuote();
			if ( quote != null )
			{
                Organization organization = quote.getOrganization();
                if(organization != null){
                    return organization.getShortName();
                }

			}
		}
		catch ( Exception ex )
		{
			log.error( "TradeMonitorMessageBuilderC.getRateProvider : Error in getting rate provider ", ex );
		}
		return null;
	}

	/**
	 * Set Originating User and Originating Cpty fields on TradeMonitorMessage object
	 *
	 * @param request request
	 * @param tmo     trade management object
	 */
	protected void setOriginatingUserDetails( Request request, TradeManagementObject tmo )
	{
		if ( request.getOriginatingCptyId() != null )
		{
			Counterparty cpty = ( Counterparty ) ReferenceDataCacheC.getInstance().getEntityByObjectId( request.getOriginatingCptyId(), Counterparty.class );
			if ( cpty != null )
			{
				tmo.setOriginatingCpty( cpty.getShortName() );
			}
		}
		if ( request.getOriginatingUserId() != null )
		{
			User user = ( User ) ReferenceDataCacheC.getInstance().getEntityByObjectId( request.getOriginatingUserId(), User.class );
			if ( user != null )
			{
				tmo.setOriginatingUser( user.getFullName() );
			}
		}

		tmo.setOriginatingDealId( request.getOriginatingDealId() );
	}

	/**
	 * This method will return accepted tier number, which we will store in tmo's customField.
	 *
	 * @param aTrade  trade
	 * @param request request
	 * @return String string
	 */
	private String getAcceptedTier( Trade aTrade, Request request )
	{
		FXLegDealingPrice fxLegDealingPrice = null;
		if ( aTrade instanceof FXSingleLeg )
		{
			fxLegDealingPrice = ( FXLegDealingPrice ) request.getRequestPrice( "singleLeg" );
		}
		if ( fxLegDealingPrice != null )
		{
			fxLegDealingPrice = ( FXLegDealingPrice ) fxLegDealingPrice.getAcceptedDealingPrice();
		}
		if ( fxLegDealingPrice != null )
		{
			String acceptedTierName = fxLegDealingPrice.getName();
			if ( acceptedTierName != null )
			{
				return String.valueOf( fxLegDealingPrice.getTier() + 1 );
			}
		}
		return null;
	}

	private void setSentReceivedTimes( TradeManagementObject tmo, EventTimeFacade etFacade, String event, boolean isOAMakerTrade )
	{
		long sentMillis = getSentMillis( etFacade , isOAMakerTrade);
		if ( sentMillis == 0 )
		{
			if ( log.isDebugEnabled() )
			{
				log.debug( "Sent time 1970 for TMO: " + tmo.getGUID() + "  with Status: " + tmo.getStatus() );
			}
		}
		long recievedMillis = 0;
		tmo.setSentDate( new Date( sentMillis ) );
		if ( event.equals( TradeStatus.REJECTED_BY_INTEGRAL ) )
		{
			recievedMillis = etFacade.getTime( ISCommonConstants.EVENT_TIME_REJECTED_BY_INTEGRAL );
		}
		//received time is set only for verified and rejected trades
		else if ( event.equals( ManagementConstants.TRADE_REJECTED ) || event.equals( TradeStatus.REJECTED_BY_INTEGRAL_REJECTED ) )
		{
			recievedMillis = getRejectionReceivedMillis( etFacade );
		}
		else if ( event.equals( ManagementConstants.TRADE_VERIFIED ) || event.equals( TradeStatus.REJECTED_BY_INTEGRAL_VERIFIED ) )
		{
			if( isOAMakerTrade )
			{
				recievedMillis = etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_MAKER_TRADE_CREATED);
			}
			else
			{
				recievedMillis = getVerificationReceivedMillis( etFacade );
			}
		}

		if ( recievedMillis == 0 && sentMillis != 0 )
		{
			recievedMillis = sentMillis;
		}

		tmo.setReceivedDate( new Date( recievedMillis ) );

		//response time is set only for verified and rejected trades
		if (event.equals(ManagementConstants.TRADE_VERIFIED) || event.equals(ManagementConstants.TRADE_REJECTED)
                || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL)
                || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_REJECTED)
                || event.equals(ManagementConstants.REJECTED_BY_INTEGRAL_VERIFIED)
                || event.equals( ManagementConstants.REJECTED_BY_INTEGRAL_TIMEDOUT)
                || event.equals( ManagementConstants.REJECTED_BY_INTEGRAL_DONTKNOW)
                || event.equals( ManagementConstants.EXPIRED_BY_INTEGRAL_TIMEDOUT)
                || event.equals( ManagementConstants.EXPIRED_BY_INTEGRAL_DONTKNOW)) {
			tmo.setResponseTime( recievedMillis - sentMillis );
		}
	}

    protected String getTradeType( Request request )
    {
        if ( request.isPriceTakingIntended() )
        {
            return "Taker";
        }
        else
        {
            return "Maker";
        }
    }

	private void addCustomFieldstoTradeObject( Request request, Trade trade, TradeManagementObject tmo, boolean isSubmit, boolean isOAMakerTrade )
	{
		ISCommonMBean isCommonMBean = ISCommonConfigFactory.getISCommonMBean();
		Quote quote = request.getAcceptedQuote();
		QuoteEventTimeFacade etFacade = null;
		if ( quote != null )
		{
			etFacade = ( QuoteEventTimeFacade ) quote.getFacade( EventTimeFacade.NAME );
		}

		HashMap<String, Long> auditTrailValues = null;
		if ( etFacade != null )
		{
			auditTrailValues = calculateAuditTimings( etFacade, tmo, isSubmit );
		}
		long customerlatency = getUserLatency( request.getUser() );

		long providerlatency = 0;//to passed later when calculated
		String marketSnapshot = "";
		if ( request.getMarketSnapshot() != null )
		{
			marketSnapshot = request.getMarketSnapshot();
		}
		if( isOAMakerTrade )
		{
			EventTimeFacade reqEventTimeFacade = ( EventTimeFacade ) request.getFacade( EventTimeFacade.NAME );
			setSystemSnapshotOAMakerDeal( tmo,reqEventTimeFacade );
		}
		else if ( auditTrailValues != null )
		{
			setSystemSnapshot( tmo, auditTrailValues, providerlatency, customerlatency, isSubmit );
		}
		String auditTrail = getAuditTrailNew( request, tmo, isOAMakerTrade );
        boolean setMarketSnapshot = !isCommonMBean.enableMarketSnapshotOptimization() ||
							tmo.getStatus().equals(ManagementConstants.TRADE_VERIFIED)  ||
							tmo.getStatus().equals( ManagementConstants.TRADE_REJECTED );
        if ( setMarketSnapshot ) {
            tmo.setMarketSnapshot( marketSnapshot );
        }
        tmo.setAuditTrail( auditTrail );

        try
		{
	        boolean isLiftOrder = ((trade.getExecutionFlags() & TradeExecutionFlags.LIFT) == TradeExecutionFlags.LIFT);
			String cpu = String.valueOf( trade.getClientCPUUsage() );
			String lastPoll = String.valueOf( trade.getClientPollLatency() );
			String attempts = String.valueOf( trade.getNoOfAttemptsByClient() );
			String memory = String.valueOf( trade.getClientMemoryUsage() );
			String userChannel = request.getChannel().getShortName();
			String clientName = ( String ) request.getUser().getCustomFieldValue( ISCommonConstants.CLIENT_NAME );
			String applicationName = isCommonMBean.getApplicationName();
			String clientVersion = getClientVersion( request.getUser() );
			String bestQuoteStrategy = request.getRequestAttributes().getBestQuoteStrategy();
			if ( bestQuoteStrategy == null || bestQuoteStrategy.trim().length() <= 0 )
			{
				bestQuoteStrategy = "" + ProviderOrgFunction.SAME_QUOTE;
			}
			String fresherQuoteUsed = request.getRequestAttributes().getFresherQuoteUsed();
			fresherQuoteUsed = fresherQuoteUsed == null ? ISCommonConstants.FRESHER_QUOTE_USED_NO : fresherQuoteUsed;
			String tradeCancelledByUser = request.getRequestAttributes().getCancelledByUser();

			String tradingChannel;
			String orderTransactionID = null;
			Request limitRequest = request.getParentRequest();
            tradingChannel = request.getTradeChannel();
            if ( limitRequest != null )
            {
                if ( tradingChannel == null)
                {
                    tradingChannel = limitRequest.getTradeChannel();
                }
                orderTransactionID = limitRequest.getTransactionID();
            }

			if ( orderTransactionID == null )
			{
				orderTransactionID = request.getRequestAttributes().getOrderTransactionId();
			}
			if ( log.isDebugEnabled() )
			{
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::cpu " + cpu );
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::lastPoll " + lastPoll );
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::attempts " + attempts );
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::memory " + memory );
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::userChannel " + userChannel );
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::applicationName " + applicationName );
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::clientName " + clientName );
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::clientVersion " + clientVersion );
				log.debug( "ISMgmtMessageFacadeC.addCustomFieldstoTradeObject ::OrderTransactionID " + orderTransactionID );
			}
			tmo.setTradeChannel( tradingChannel );
			tmo.setCpuUsage( cpu );
			tmo.setLastPollLatency( lastPoll );
			tmo.setAttempts( attempts );
			tmo.setMemoryUsage( memory );
			tmo.setApplicationName( applicationName );
			tmo.setClientName( clientName );
			tmo.setBestQuoteStrategy( bestQuoteStrategy );
			tmo.setFresherQuoteUsed( fresherQuoteUsed );
			tmo.setClientVersion( clientVersion );
			tmo.setOrderTxnId( orderTransactionID );
			tmo.setTradeTxnId( trade.getTransactionID() );
            tmo.setLiftOrder( isLiftOrder );

			if ( tradeCancelledByUser != null )
			{
				tmo.setCancelledByUser( tradeCancelledByUser );
			}
		}
		catch ( Exception e )
		{
			log.debug( "Exception found in ISMgmtMessageFacadeC.addCustomFieldstoTradeObject" );
		}
	}

	private HashMap<String, Long> calculateAuditTimings( QuoteEventTimeFacade etFacade,
			TradeManagementObject tmo,
			boolean isSubmit )
			{
		HashMap<String, Long> auditTrailValues = new HashMap<String, Long>();
		long serverOffSet = getServerTimeOffset( etFacade.getTimes(), ISCommonConstants.EVENT_TIME_SERVER_OFFSET_LIST );
		auditTrailValues.put( ManagementConstants.RATE_PUBLISHED, etFacade.getTime( ISCommonConstants.EVENT_TIME_RATE_EFFECTIVE ) );
		long rateReceived;
		rateReceived = etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_ADAPTER_REC_RATE );
		auditTrailValues.put( ManagementConstants.RATE_RECEIVED, rateReceived );
		auditTrailValues.put( ManagementConstants.RATE_SENT, etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_JMS_SENT ) );
		auditTrailValues.put( ManagementConstants.RATE_DISPLAYED, getApproximateClientTime( etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_CLIENT_DISP_RATE ), serverOffSet ) );
		long rateTaken = etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_CLIENT_ACC_USER );
		if ( rateTaken != 0 )
		{
			auditTrailValues.put( ManagementConstants.RATE_TAKEN, getApproximateClientTime( rateTaken, serverOffSet ) );
		}
		else
		{
			rateTaken = etFacade.getTime( ISCommonConstants.EVENT_TIME_ORDER_MATCHED_BY_SERVER );
			auditTrailValues.put( ManagementConstants.RATE_TAKEN, rateTaken );
		}

		auditTrailValues.put( ManagementConstants.DEAL_REQUEST_RECEIVED, etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_REC_ACC ) );
		auditTrailValues.put( ManagementConstants.NEXT_RATE_RECEIVED, 0L );

		long dealSentTime = getSentMillis( etFacade, false );
		if ( dealSentTime == 0 )
		{
			if ( log.isDebugEnabled() )
			{
				log.debug( "#2 The deal req sent time is 1970 for TMO: " + tmo.getGUID() + " and status: " + tmo.getStatus() );
			}
		}
		auditTrailValues.put( ManagementConstants.DEAL_REQUEST_SENT, dealSentTime );
		long dealResponseReceived = 0;
		if ( tmo.getStatus().equalsIgnoreCase( ManagementConstants.TRADE_VERIFIED ) )
		{
			dealResponseReceived = etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_REC_VERIFY_FROM_PROVIDER );
			if ( dealResponseReceived == 0 )
			{
				dealResponseReceived = etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_REC_VERIFY );
			}
		}
		else if ( tmo.getStatus().equalsIgnoreCase( ManagementConstants.TRADE_REJECTED ) )
		{
			dealResponseReceived = etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_REC_REJ_FROM_PROVIDER );
			if ( dealResponseReceived == 0 )
			{
				dealResponseReceived = etFacade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_REC_REJ );
			}
		}
		auditTrailValues.put( ManagementConstants.DEAL_RESPONSE_RECEIVED, dealResponseReceived );
		long dealResponseSent = 0;

		if ( !isSubmit )
		{
			dealResponseSent = System.currentTimeMillis();
		}
		auditTrailValues.put( ManagementConstants.DEAL_RESPONSE_SENT, dealResponseSent );
		long customerThinkingTime = 0;
		if ( auditTrailValues.get( ManagementConstants.RATE_DISPLAYED ) != 0 )
		{
			customerThinkingTime = auditTrailValues.get( ManagementConstants.RATE_TAKEN ) - auditTrailValues.get( ManagementConstants.RATE_DISPLAYED );
		}
		auditTrailValues.put( ManagementConstants.CUSTOMER_THINKING_TIME, customerThinkingTime );
		return auditTrailValues;
			}

	protected long getUserLatency( User user )
	{
		double latency = 0;
		try
		{
			IdcSessionContext idcContext = current.getSessionContext( user );

			if ( idcContext.getAttribute( "Latency" ) != null )
			{
				String latencyStr = ( String ) idcContext.getAttribute( "Latency" );
				latency = Double.parseDouble( latencyStr );
			}
		}
		catch ( Exception e )
		{
			log.warn( "ISMgmtMessageFacade.getUserLatency: Error getting user Latency" + e );
		}
		latency = latency / 2;
		return ( long ) latency;
	}

	protected String getClientVersion( User user )
	{
		try
		{
			IdcSessionContext idcContext = current.getSessionContext( user );
			return ( String ) idcContext.getAttribute( ISCommonConstants.CTX_ATTR_CLIENT_VERSION );
		}
		catch ( RuntimeException e )
		{
			log.warn( "TradeMonitorMessageBuilderC.getClientVersion : Error getting clientVersion " + e );
		}
		return null;
	}

	protected long getApproximateClientTime( long clientMilliSec, long serveroffset )
	{

		return ( clientMilliSec - serveroffset );
	}

	protected long getServerTimeOffset( Map eventTimeMap, String[] offsetCalcList )
	{
		long offset = 0;
		Long clientQueried = ( Long ) eventTimeMap.get( offsetCalcList[0] );
		Long clientRecieved = ( Long ) eventTimeMap.get( offsetCalcList[2] );
		Long JMSSend = ( Long ) eventTimeMap.get( offsetCalcList[1] );
		Long JMSProxywaitTime = ( Long ) eventTimeMap.get( offsetCalcList[3] );
		if ( null != clientQueried && null != clientRecieved && null != JMSSend && null != JMSProxywaitTime )
		{
			//			offset = clientRecieved.longValue() - ((clientRecieved.longValue() - clientQueried.longValue())/2) - JMSSend.longValue();
			offset = ( clientRecieved + clientQueried + JMSProxywaitTime ) / 2 - JMSSend;
		}
		return offset;
	}

	private long getSentMillis( EventTimeFacade facade, boolean isOAMakerTrade )
	{
		long sentTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_ACC_SENT_TO_PROVIDER );
		//below is a an extra checks to accomodate the deal being shown in the blotters
		//this logically should not fail but can on exception conditions
		if ( sentTime == 0 )
		{
			if ( facade instanceof QuoteEventTimeFacade )
			{
				sentTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_SENT_ACC );
			}
			else if( isOAMakerTrade )
			{
				sentTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_TAKER_ORDER_REC_OA );
			}
			else
			{
				sentTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_REC_VERIFY );
				if ( sentTime == 0 )
				{
					sentTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_SENT_VERIFY );
				}
			}
		}
		return sentTime;
	}

	private String getRequestAuditTrail( EventTimeFacade reqEventTimeFacade, Quote quote )
	{
		String userAccGUID = " ";
		if ( quote != null )
		{
			userAccGUID = quote.getGUID();

		}
		StringBuilder reqETBuffer = new StringBuilder( 300 );
		String requestEventTimes[] = {ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_REC,
				ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_PUBLISHED,
				ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_MATCHED};
		long baseTimestamp = reqEventTimeFacade.getTime( requestEventTimes[0] );
		for ( int i = 0; i < requestEventTimes.length; i++ )
		{
			String requestEvent = requestEventTimes[i];
			if ( ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_REC.equals( requestEvent ) )
			{
				reqETBuffer.append( requestEvent );
				reqETBuffer.append( COLUMN_DELIMITER );
//				reqETBuffer.append( rateID );    //rateID
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( userAccGUID );   //quoteID
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( baseTimestamp );    //timestamp
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( 0 ); //elapsed
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( 0 ); //system elapsed
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( 0 ); //duration
				reqETBuffer.append( ROW_DELIMITER );
			}
			else
			{
				long timestamp = reqEventTimeFacade.getTime( requestEvent );
				long prevtimestamp = reqEventTimeFacade.getTime( requestEventTimes[i - 1] );
				reqETBuffer.append( requestEvent );
				reqETBuffer.append( COLUMN_DELIMITER );
//				reqETBuffer.append( rateID );    //rateID
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( userAccGUID );   //quoteID
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( timestamp );    //timestamp
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( timestamp - baseTimestamp ); //elapsed
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( timestamp - baseTimestamp ); //system elapsed
				reqETBuffer.append( COLUMN_DELIMITER );
				reqETBuffer.append( timestamp - prevtimestamp ); //duration
				reqETBuffer.append( ROW_DELIMITER );
			}
		}
		return reqETBuffer.toString();
	}

    private String getAuditTrailNew( Request request, TradeManagementObject tmo, boolean isOAMakerTrade ) {
        Quote quote = request.getAcceptedQuote();

        //Append Request Event times if available
        EventTimeFacade reqEventTimeFacade = ( EventTimeFacade ) request.getFacade( EventTimeFacade.NAME );
        String requestAuditTrail = "";
        if ( reqEventTimeFacade != null ) {
            if ( isOAMakerTrade ) {
                requestAuditTrail = getMakerTradeRequestAuditTrailNew( reqEventTimeFacade );
            }
            else {
                requestAuditTrail = getRequestAuditTrailNew( reqEventTimeFacade );
            }
        }

        if ( quote != null ) {
            StringBuilder sb;
            ISQuoteEventTimeFacadeC facade = ( ISQuoteEventTimeFacadeC ) quote.getFacade( EventTimeFacade.NAME );
            String userAccGUID = facade.getClientAcceptedQuoteGUID();
            String rateTakenGUID = facade.getAcceptedQuoteGUID();
            if ( userAccGUID == null ) {
                userAccGUID = quote.getGUID();
            }

            if ( rateTakenGUID == null ) {
                rateTakenGUID = quote.getGUID();
            }
            tmo.setUserAcceptedGuid( userAccGUID );
            tmo.setRateTakenGuid( rateTakenGUID );

            String nextGUID = facade.getNextQuoteGUID();
            tmo.setNextRateGuid( nextGUID );
            long serverOffSet = getServerTimeOffset( facade.getTimes(), ISCommonConstants.EVENT_TIME_SERVER_OFFSET_LIST );
            LinkedList<String> timestamplist = new LinkedList<String>();
            LinkedList<String> otherTimestampList = new LinkedList<String>();

            for ( int i = 0; i < ISCommonConstants.EVENT_TIMES.length; i++ ) {
                String eventName = ISCommonConstants.EVENT_TIMES[i][0];
                long timestamp = facade.getTime( eventName );
                String userAcceptedRate = request.getRequestAttributes().getUserAcceptedRate();
                if ( userAcceptedRate != null && !userAcceptedRate.equals( "" ) ) {
                    sb = new StringBuilder( 100 );
                    sb.append( GMMessageCompactionCodes.getAuditEventCode( eventName ) );
                    if ( eventName.equalsIgnoreCase( ISCommonConstants.EVENT_TIME_DISP_ADAPTER_REC_RATE ) ) {
                        sb.append( ' ' ).append( '(' );
                        sb.append( userAcceptedRate );
                        sb.append( ')' );
                    }
                }
                else {
                    sb = new StringBuilder( 25 );
                    sb.append( GMMessageCompactionCodes.getAuditEventCode( eventName ) );
                }
                sb.append( COLUMN_DELIMITER );
                //Calculate approximate server time for the client rate events
                if ( i == 8 || i == 10 || i == 11 || i == 12 || i == 13 ) {
                    timestamp = timestamp - serverOffSet;

                }
                sb.append( timestamp );
                if ( i == 8 ) {
                    otherTimestampList.add( sb.toString() );
                }
                else {
                    timestamplist.add( sb.toString() );
                }
            }
            //New rate received
            long nextTimestamp = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_NEXT_RATE_REC );
            if ( nextTimestamp != 0 ) {
                sb = new StringBuilder( 100 );
                sb.append( GMMessageCompactionCodes.getAuditEventCode( ISCommonConstants.EVENT_TIME_DISP_IS_NEXT_RATE_REC ) );
                String nextRate = request.getRequestAttributes().getNextRate();
                if ( nextRate != null ) {
                    sb.append( ' ' ).append( '(' );
                    sb.append( nextRate );
                    sb.append( ')' );
                }

                sb.append( COLUMN_DELIMITER );
                sb.append( nextTimestamp );
                otherTimestampList.add( sb.toString() );
            }

            //Accepted rate received
            long accTimestamp = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_ACC_RATE_REC );
            if ( accTimestamp != 0 ) {
                sb = new StringBuilder( 100 );
                sb.append( GMMessageCompactionCodes.getAuditEventCode( ISCommonConstants.EVENT_TIME_DISP_ACC_RATE_REC ) );
                if ( request.getRequestAttributes().getRateSentForAcceptance() != null ) {
                    sb.append( ' ' ).append( '(' );
                    sb.append( request.getRequestAttributes().getRateSentForAcceptance() );
                    sb.append( ')' );
                }
                sb.append( COLUMN_DELIMITER );
                sb.append( accTimestamp );
                otherTimestampList.add( sb.toString() );
            }

            timestamplist = sortTimestampsForMSNew( timestamplist, otherTimestampList );
            sb = new StringBuilder( 1000 );
            for ( String timestamp : timestamplist ) {
                sb.append( timestamp ).append( ROW_DELIMITER );
            }
            sb.append( requestAuditTrail );
            return sb.toString();
        }
        return requestAuditTrail;
    }

    private String getMakerTradeRequestAuditTrailNew( EventTimeFacade reqEventTimeFacade ) {
        StringBuilder sb = new StringBuilder( 150 );
        String requestEventTimes[] = {ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_REC,
                ISCommonConstants.EVENT_TIME_DISP_TAKER_ORDER_REC_OA,
                ISCommonConstants.EVENT_TIME_DISP_TAKER_ORDER_MATCHED_OA,
                ISCommonConstants.EVENT_TIME_DISP_MAKER_TRADE_CREATED,
                ISCommonConstants.EVENT_TIME_DISP_TXN_COMPLETED};
        long baseTimestamp = reqEventTimeFacade.getTime( requestEventTimes[0] );
        for ( String requestEvent : requestEventTimes ) {
            if ( ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_REC.equals( requestEvent ) ) {
                sb.append( GMMessageCompactionCodes.getAuditEventCode( requestEvent ) );
                sb.append( COLUMN_DELIMITER );
                sb.append( baseTimestamp );    //timestamp
                sb.append( ROW_DELIMITER );
            }
            else {
                long timestamp = reqEventTimeFacade.getTime( requestEvent );
                sb.append( GMMessageCompactionCodes.getAuditEventCode( requestEvent ) );
                sb.append( COLUMN_DELIMITER );
                sb.append( timestamp );    //timestamp
                sb.append( ROW_DELIMITER );
            }
        }
        return sb.toString();
    }

    protected String getRequestAuditTrailNew( EventTimeFacade reqEventTimeFacade ) {
        StringBuilder sb = new StringBuilder( 100 );
        String requestEventTimes[] = {ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_REC,
                ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_PUBLISHED,
                ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_MATCHED};
        long baseTimestamp = reqEventTimeFacade.getTime( requestEventTimes[0] );
        for ( String requestEvent : requestEventTimes ) {
            if ( ISCommonConstants.EVENT_TIME_DISP_OA_ORDER_REC.equals( requestEvent ) ) {
                sb.append( GMMessageCompactionCodes.getAuditEventCode( requestEvent ) );
                sb.append( COLUMN_DELIMITER );
                sb.append( baseTimestamp );    //timestamp
                sb.append( ROW_DELIMITER );
            }
            else {
                long timestamp = reqEventTimeFacade.getTime( requestEvent );
                sb.append( GMMessageCompactionCodes.getAuditEventCode( requestEvent ) );
                sb.append( COLUMN_DELIMITER );
                sb.append( timestamp );    //timestamp
                sb.append( ROW_DELIMITER );
            }
        }
        return sb.toString();
    }


    protected LinkedList<String> sortTimestampsForMSNew( LinkedList<String> bigList, LinkedList<String> smallList )
	{
		//start from first timestamp
        for ( String smallElement : smallList ) {
            StringTokenizer smallST = new StringTokenizer( smallElement, "%" );
            smallST.nextToken();
            long smallTime = new Long( smallST.nextToken() );
            //start from second timestamp to leave out rate effective
            for ( int j = 1; j < bigList.size(); j++ ) {
                String bigElement = bigList.get( j );
                StringTokenizer bigST = new StringTokenizer( bigElement, "%" );
                bigST.nextToken();
                long bigTime = new Long( bigST.nextToken() );
                if ( smallTime <= bigTime ) {
                    bigList.add( j, smallElement );
                    break;
                }
            }
        }
		return bigList;
	}

	protected void setSystemSnapshot( TradeManagementObject tmo, HashMap<String, Long> auditTrailValues,
                                      long providerlatency,
                                      long customerlatency, boolean isSubmit )
	{

		long dealResponseReceivedTime = auditTrailValues.get( ManagementConstants.DEAL_RESPONSE_RECEIVED );
		long dealRequestSentTime = auditTrailValues.get( ManagementConstants.DEAL_REQUEST_SENT );
		long rateRecieved = auditTrailValues.get( ManagementConstants.RATE_RECEIVED );
		long ratePublished = auditTrailValues.get( ManagementConstants.RATE_PUBLISHED );
		long rateSent = auditTrailValues.get( ManagementConstants.RATE_SENT );
		long dealRequestRecieved = auditTrailValues.get( ManagementConstants.DEAL_REQUEST_RECEIVED );
		long dealResponseSent = auditTrailValues.get( ManagementConstants.DEAL_RESPONSE_SENT );
		long rateTaken = auditTrailValues.get( ManagementConstants.RATE_TAKEN );
		long customerThinkingTime = auditTrailValues.get( ManagementConstants.CUSTOMER_THINKING_TIME );
		if ( providerlatency == 0 )
		{
			if ( dealResponseReceivedTime != 0 && dealRequestSentTime != 0 )
			{
				providerlatency = ( dealResponseReceivedTime - dealRequestSentTime ) / 2;
			}
		}

		long roundTrip = dealRequestSentTime - rateRecieved;
		long providerRoundTrip = dealRequestSentTime - ratePublished + 2 * providerlatency;
		long providerSysRoundTrip = providerRoundTrip - customerThinkingTime;
		long systemRoundTrip = roundTrip - customerThinkingTime;
		if ( systemRoundTrip > providerSysRoundTrip )
		{
            if ( log.isDebugEnabled() ) {
                log.debug( new StringBuilder( 1000 ).append( " TradeMonitorMessageBuilderC.getSystemSnapshot() : dealRequestSentTime " )
                        .append( dealRequestSentTime ).append( ",dealResponseReceivedTime " ).append( dealResponseReceivedTime )
                        .append( ",rateRecieved " ).append( rateRecieved ).append( ",rateSent " ).append( rateSent )
                        .append( ",customerThinkingTime " ).append( customerThinkingTime )
                        .append( ",providerlatency " ).append( providerlatency ).append( ",roundTrip " )
                        .append( "roundTrip " ).append( ",systemRoundTrip " ).append( systemRoundTrip ).append( ",providerRoundTrip " )
                        .append( "providerRoundTrip " ).append( ",providersystemRoundTrip " ).append( providerSysRoundTrip ).toString() );
            }
		}

		if ( !isSubmit )
		{
			tmo.setProviderRoundTrip( getSnapShotTimeStamp( providerRoundTrip ) );
            tmo.setProviderSystemRoundTrip( getSnapShotTimeStamp( providerSysRoundTrip ) );
            tmo.setProviderNetworkLatency( String.valueOf( providerlatency ) );
            if ( dealResponseSent != 0 ) {
                tmo.setCustomerExecutionResponse( getSnapShotTimeStamp( ( dealResponseSent - rateTaken + customerlatency ) ) );
            }
            else{
                tmo.setCustomerExecutionResponse( "0" );
            }
            if ( dealResponseReceivedTime != 0 ) {
                tmo.setProviderExecutionResponse( getSnapShotTimeStamp( dealResponseReceivedTime - dealRequestSentTime ) );
            }
            else{
                tmo.setProviderExecutionResponse( "0" );
            }
		}
		else
		{
            tmo.setProviderRoundTrip( "0" );
            tmo.setProviderSystemRoundTrip( "0" );
            tmo.setProviderNetworkLatency( "0" );
            tmo.setCustomerExecutionResponse( "0" );
            tmo.setProviderExecutionResponse( "0" );
		}

		tmo.setRoundTrip( getSnapShotTimeStamp( roundTrip ) );
		tmo.setSystemRoundTrip( getSnapShotTimeStamp( systemRoundTrip ) );
		if ( rateSent == 0 )
		{
			tmo.setRateProcessing( "0" );
		}
		else
		{
			tmo.setRateProcessing( getSnapShotTimeStamp( rateSent - rateRecieved ) );
		}
		tmo.setExecutionProcessing( getSnapShotTimeStamp( dealRequestSentTime - dealRequestRecieved ) );
		tmo.setCustomerNetworkLatency( String.valueOf( customerlatency ) );
	}


	protected void setSystemSnapshotOAMakerDeal( TradeManagementObject tmo, EventTimeFacade facade )
	{
		long takerOrderRec = facade.getTime(ISCommonConstants.EVENT_TIME_DISP_TAKER_ORDER_REC_OA);
		long tradeCreated = facade.getTime(ISCommonConstants.EVENT_TIME_DISP_MAKER_TRADE_CREATED);
		long transactionCompleted = facade.getTime(ISCommonConstants.EVENT_TIME_DISP_TXN_COMPLETED);

        tmo.setProviderRoundTrip( "N/A" );
        tmo.setProviderSystemRoundTrip( "N/A" );
        tmo.setRoundTrip( "N/A" );
        tmo.setSystemRoundTrip( getSnapShotTimeStamp( ( transactionCompleted - takerOrderRec ) ) );
        tmo.setRateProcessing( "N/A" );
        tmo.setExecutionProcessing( getSnapShotTimeStamp( tradeCreated - takerOrderRec ) );
        tmo.setCustomerExecutionResponse( "N/A" );
        tmo.setProviderExecutionResponse( "N/A" );
        tmo.setProviderNetworkLatency( "N/A" );
        tmo.setCustomerNetworkLatency( "N/A" );
	}

	protected String getExternalDealID( Trade trade )
	{
		try
		{
			return trade.getExternalSystemId( ISCommonConstants.EXT_REQUEST_ID ) == null ? "" :  trade.getExternalSystemId( ISCommonConstants.EXT_REQUEST_ID ).getSystemId();
		}
		catch ( Exception e )
		{
			return "";
		}

	}

	protected long getVerificationReceivedMillis( EventTimeFacade facade )
	{
		long receivedTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_REC_VERIFY_FROM_PROVIDER );
        //todo:this logic of this extra validation to be ported to monitoring server side
		if ( receivedTime == 0 )
		{
			receivedTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_REC_VERIFY );
		}
		return receivedTime;
	}

	protected long getRejectionReceivedMillis( EventTimeFacade facade )
	{
		long receivedTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_REC_REJ_FROM_PROVIDER );
        //todo:this logic of this extra validation to be ported to monitoring server side
		if ( receivedTime == 0 )
		{
			receivedTime = facade.getTime( ISCommonConstants.EVENT_TIME_DISP_IS_REC_REJ );
		}
		return receivedTime;
	}

	protected String getSnapShotTimeStamp( long timestamp )
	{
		if ( timestamp >= 0 && timestamp < ISCommonConstants.SNAPSHOT_TIMESTAMP_LIMIT )
		{
            return String.valueOf( timestamp );
		}
		else
		{
			return "N/A";
		}
	}

	/**
	 * Get the trade Rejected message from quote object
	 *
	 * @param request request
	 * @return reason
	 */
	protected String getTradeRejectReason( Request request )
	{
		return getTradeRejectReason(request.getTrade());
	}

	/**
	 * Set cptyA, cptyB, cptyC, cptyD org shortnames on TMO.
	 * @param trade trade
	 * @param tmo tmo
	 */
	protected void setCptyOrgs( Trade trade, TradeManagementObject tmo )
	{
		if( trade.getCounterpartyA() != null ) {
            tmo.setCptyAOrg(trade.getCounterpartyA().getOrganization().getShortName());
            tmo.setFirmAOrg(getFirmOrgName(trade.getCounterpartyA().getOrganization()));
        }
		if( trade.getCounterpartyB() != null ) {
            tmo.setCptyBOrg(trade.getCounterpartyB().getOrganization().getShortName());
            tmo.setFirmBOrg(getFirmOrgName(trade.getCounterpartyB().getOrganization()));
        }
		final Counterparty cptyC = trade.getCounterpartyC();
		if( cptyC != null )
		{
			if( cptyC instanceof TradingParty ) {
                tmo.setCptyCOrg(((TradingParty) cptyC).getLegalEntity().getOrganization().getShortName());
                tmo.setFirmCOrg(getFirmOrgName(((TradingParty)cptyC).getLegalEntityOrganization()));
            }
            if( cptyC instanceof LegalEntity ){
                tmo.setCptyCOrg(cptyC.getOrganization().getShortName());
                tmo.setFirmCOrg(getFirmOrgName(cptyC.getOrganization()));
            }
			tmo.setCptyC( cptyC.getShortName() );
		}
		final Counterparty cptyD = trade.getCounterpartyD();
		if( cptyD != null )
		{
			if( cptyD instanceof TradingParty ) {
                tmo.setCptyDOrg(((TradingParty) cptyD).getLegalEntity().getOrganization().getShortName());
                tmo.setFirmDOrg(getFirmOrgName(((TradingParty)cptyD).getLegalEntityOrganization()));
            }
			if( cptyD instanceof LegalEntity ){
			    tmo.setCptyDOrg(cptyD.getOrganization().getShortName());
                tmo.setFirmDOrg(getFirmOrgName(cptyD.getOrganization()));
            }
			tmo.setCptyD( cptyD.getShortName() );
		}
	}

	protected String getProviderName( Request request, Trade trade )
	{
		if( trade.getCounterpartyB() != null )
		{
			return trade.getCounterpartyB().getOrganization().getShortName();
		}
		else
		{
			Iterator toOrgIterator = request.getToOrganizations().iterator();
			Organization toOrg = null;
			if( toOrgIterator.hasNext() )
				toOrg = (Organization) toOrgIterator.next();
			return toOrg == null ? "N/A" : toOrg.getShortName();
		}
	}

	protected void setAdaptorName( Request request, TradeManagementObject tmo )
	{
		String name = request.getAdaptorOrganization() == null ? "N/A" : request.getAdaptorOrganization().getShortName();
		tmo.setAdaptorName( name );
	}


    private String getAcceptedTier( Trade aTrade )
    {
        FXLegDealingPrice fxLegDealingPrice = null;
        if ( aTrade instanceof FXSingleLeg )
        {
        	 FXPaymentParameters fxPayment = ((FXSingleLeg) aTrade).getFXLeg().getFXPayment();
             fxLegDealingPrice = createPrice(fxPayment);
        }
        else if ( aTrade instanceof FXSwap )
        {
        	FXPaymentParameters fxPayment = ((FXSwap) aTrade).getFarLeg().getFXPayment();
            fxLegDealingPrice = createPrice(fxPayment);
        }

        fxLegDealingPrice = ( FXLegDealingPrice ) fxLegDealingPrice.getAcceptedDealingPrice();
        if ( fxLegDealingPrice != null )
        {
            String acceptedTierName = fxLegDealingPrice.getName();
            if ( acceptedTierName != null )
            {
                if ( acceptedTierName.indexOf( "1" ) != -1 )
                {
                    return "2";
                }
                else if ( acceptedTierName.indexOf( "2" ) != -1 )
                {
                    return "3";
                }
                else
                {
                    return "1";
                }
            }
        }
        return null;
    }



    protected FXLegDealingPrice createPrice(FXPaymentParameters fxPayment) {
    	CurrencyPair ccyPair = fxPayment.getFXRate().getCurrencyPair();
    	Tenor tenor = fxPayment.getTenor();
        FXLegDealingPrice fxLegDealingPrice = FXDealingFactory.newFXLegDealingPrice();
        fxLegDealingPrice.setBidOfferMode(DealingPrice.TWO_WAY);
        fxLegDealingPrice.setDealtCurrencyProperty(fxPayment.isDealtCurrency1()?FXLegDealingPrice.CCY1:FXLegDealingPrice.CCY2);
        fxLegDealingPrice.setDealtAmount(fxPayment.isDealtCurrency1()?fxPayment.getBaseCurrencyAmount():fxPayment.getVariableCurrencyAmount());
        fxLegDealingPrice.setDealtCurrency(fxPayment.isDealtCurrency1()?ccyPair.getBaseCurrency():ccyPair.getVariableCurrency());
        fxLegDealingPrice.setSettledCurrency(ccyPair.getVariableCurrency());
        fxLegDealingPrice.setTenor(tenor);
        fxLegDealingPrice.setFixingDate(fxPayment.getFixingDate());
        fxLegDealingPrice.setFixingTenor(fxPayment.getFixingTenor());
        return fxLegDealingPrice;
    }

    protected void getMultiLegParameters( FXSwap fxSwapTrade, TradeManagementObject tmo )
    {
        FXRate fxFarlegRate = fxSwapTrade.getFarLeg().getFXPayment().getFXRate();
        FXRate fxNearlegRate = fxSwapTrade.getNearLeg().getFXPayment().getFXRate();

        FXRate fxFarlegMidRate = fxSwapTrade.getFarLeg().getFXPayment().getFXMidRate();
        FXRate fxNearlegMidRate = fxSwapTrade.getNearLeg().getFXPayment().getFXMidRate();

        FXPaymentParameters fxPayment = fxSwapTrade.getNearLeg().getFXPayment();
        FXPaymentParameters fxPayment2 = fxSwapTrade.getFarLeg().getFXPayment();
        FXLegDealingPrice fxnearLegPrice = createPrice(fxPayment);
        FXLegDealingPrice fxfarLegPrice = createPrice(fxPayment2);
        if ( fxnearLegPrice != null )
        {
            int bidOfferMode = fxnearLegPrice.getBidOfferMode();
            tmo.setBuy( bidOfferMode != 0 );
        }

        if ( fxFarlegRate != null )
        {
            tmo.setFarRate( "" + fxFarlegRate.getRate() );
            tmo.setFarSpotRate( "" + fxFarlegRate.getSpotRate() );
            tmo.setFarFwdPoint( fxFarlegRate.getForwardPoints() );
        }

        if ( fxFarlegMidRate != null )
        {
            tmo.setMidFarRate( "" + fxFarlegMidRate.getRate() );
            tmo.setMidFarSpotRate( "" + fxFarlegMidRate.getSpotRate() );
            tmo.setMidFarFwdPoint( fxFarlegMidRate.getForwardPoints() );
        }

        tmo.setFarValueDate( fxSwapTrade.getFarLeg().getFXPayment().getValueDate().asJdkDate() );
        //: todo convert USD Amount and set
        //tmo.setFarSettlementAmountAsUSD(swapdeal.getFarDealLeg().getReportingCurrencyAmount());
        if ( fxNearlegRate != null )
        {
            tmo.setNearSpotRate( "" + fxNearlegRate.getSpotRate() );
            tmo.setNearFwdPoint( fxNearlegRate.getForwardPoints() );
        }

        if ( fxNearlegMidRate != null )
        {
            tmo.setMidNearSpotRate( "" + fxNearlegMidRate.getSpotRate() );
            tmo.setMidNearFwdPoint( fxNearlegMidRate.getForwardPoints() );
        }


        String ccyPair = getCurrencyPair( fxSwapTrade.getNearLeg() );
        if ( fxnearLegPrice != null )
        {
            if ( fxnearLegPrice.getTenor() != null )
            {
                tmo.setNearTenor( fxnearLegPrice.getTenor().getName() );
            }
            if ( fxnearLegPrice.getFixingTenor() != null )
            {
                tmo.setFixingTenor( fxnearLegPrice.getFixingTenor().getName() );
            }
            if ( fxnearLegPrice.getFixingDate() != null )
            {
                tmo.setFixingDate( fxnearLegPrice.getFixingDate().asJdkDate());
            }
            if ( !isCurrency1DealtCurrency( fxnearLegPrice ) )
            {
                ccyPair = ccyPair + "#";
            }
        }

        if ( fxfarLegPrice != null )
        {
            if ( fxfarLegPrice.getTenor() != null )
            {
                tmo.setFarTenor( fxfarLegPrice.getTenor().getName() );
            }
            if ( fxfarLegPrice.getFixingTenor() != null )
            {
                tmo.setFarFixingTenor( fxfarLegPrice.getFixingTenor().getName() );
            }
            if ( fxfarLegPrice.getFixingDate() != null )
            {
                tmo.setFarFixingDate( fxfarLegPrice.getFixingDate().asJdkDate());
            }
            tmo.setFarDealtAmount( fxfarLegPrice.getDealtAmount() );
            if ( isCurrency1DealtCurrency( fxfarLegPrice ) )
            {
                tmo.setFarSettlementAmount( fxSwapTrade.getFarLeg().getFXPayment().getCurrency2Amount() );
            }
            else
            {
                tmo.setFarSettlementAmount( fxSwapTrade.getFarLeg().getFXPayment().getCurrency1Amount() );
            }
        }


        Currency dealtCcy;
        if ( fxPayment.isDealtCurrency1() )
        {
            dealtCcy = fxNearlegRate.getBaseCurrency();
        }
        else
        {
            dealtCcy = fxNearlegRate.getVariableCurrency();
        }
        Currency ccy;
        boolean isBuyingCcy1 = fxPayment.isBuyingCurrency1();
        if ( isBuyingCcy1 )
        {
            ccy = fxPayment.getCurrency1();
        }
        else
        {
            ccy = fxPayment.getCurrency2();
        }

        tmo.setCptyABuyingDealtCurrency( dealtCcy.isSameAs( ccy ) );
        tmo.setCcyPair( ccyPair );
    }


    private Double getDealtNotional( FXSwap trade)
    {
        FXLeg tradeLeg = trade.getNearLeg();
        FXPaymentParameters fxPayment = getFXLeg(trade).getFXPayment();
        FXLegDealingPrice dp = createPrice(fxPayment);
        boolean ccy1IsDealt = isCurrency1DealtCurrency( dp );
        if ( ccy1IsDealt )
        {
            return tradeLeg.getFXPayment().getCurrency1Amount();
        }
        return tradeLeg.getFXPayment().getCurrency2Amount();
    }




    private void setOriginatingUserDetails( Trade trade, TradeManagementObject tmo )
    {
        if ( trade.getOriginatingCptyId() != null )
        {
            Counterparty cpty = ( Counterparty ) ReferenceDataCacheC.getInstance().getEntityByObjectId( trade.getOriginatingCptyId(), Counterparty.class );
            if ( cpty != null )
            {
                tmo.setOriginatingCpty( cpty.getShortName() );
            }
        }
        if ( trade.getOriginatingUserId() != null )
        {
            User user = ( User ) ReferenceDataCacheC.getInstance().getEntityByObjectId( trade.getOriginatingUserId(), User.class );
            if ( user != null )
            {
                tmo.setOriginatingUser( user.getFullName() );
            }
        }
        tmo.setOriginatingDealId( trade.getOriginatingOrderId() );
    }


	private String getTradeRejectReason( Trade trade )
	{
        String reason = trade.getWorkflowStateMap().getWorkflowCodeArgument();
        if ( reason == null )
        	reason = "";
		return reason;
	}

		private  String getProviderName(  Trade trade )
	{
		if( trade.getCounterpartyB() != null )
		{
			return trade.getCounterpartyB().getOrganization().getShortName();
		}
		return null;
	}

    protected FXLeg getFXLeg(Trade trade) {
        if (ISUtilImpl.isSwap(trade)) {
            return ((FXSwap) trade).getNearLeg();
        } else if (trade.getTradeClassification().getShortName().equalsIgnoreCase(ISCommonConstants.TRD_CLSF_FXSSP)) {
            return ((FXSSPTrade) trade).getFXLeg(ISCommonConstants.FXSSP_LEG_ONE);
        } else {
            return ((FXSingleLeg) trade).getFXLeg();
        }
    }

    protected boolean isCurrency1DealtCurrency(FXLegDealingPrice dp) {
        FXLegDealingPrice quoteDealingPrice = (FXLegDealingPrice) dp.getAcceptedDealingPrice();
        if (quoteDealingPrice != null) {
            return quoteDealingPrice.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
        } else {
            return dp.getDealtCurrencyProperty().equals(FXLegDealingPrice.CCY1);
        }
    }

    protected String getCurrencyPair(FXLeg tradeLeg) {
        String ccyPair = "";
        try {
            ccyPair = tradeLeg.getFXPayment().getCurrency1().getShortName();
            ccyPair = ccyPair + "/" + tradeLeg.getFXPayment().getCurrency2().getShortName();
        } catch (Exception e) {
            log.error("Currency 1 or Currency 2 not found");
        }
        return ccyPair;
    }
    
    protected void populateUSDAmountsIfMissing(TradeManagementObject tmo, boolean isSwap){
    	try{
    		if(tmo.getSettlementAmountAsUSD() == 0.0) {
				CurrencyPair cp = getCurrencyPair(tmo);
				boolean isTerm = isTerm(tmo);
				Currency settleCcy = isTerm ? cp.getBaseCurrency() : cp.getVariableCurrency();
				Currency dealtCcy = isTerm ? cp.getVariableCurrency() : cp.getBaseCurrency();
				boolean isSSP = isSSP(tmo);
				double amt = isSSP ? tmo.getDealtAmount() : tmo.getSettlementAmount();
				Currency ccy = isSSP ? dealtCcy : settleCcy;
				double amtUSD;
				if (ISConstantsC.USD.equals(ccy.getRealCurrency ().getName())) {
					amtUSD = amt;
				} else {
					amtUSD = DealingModelUtil.getAmountInUSD(amt, ccy);
				}
				tmo.setSettlementAmountAsUSD(amtUSD);
			}
			if(isSwap && tmo.getFarSettlementAmountAsUSD() == 0.0 && tmo.getFarSettlementAmount() != 0.0){
				double farAmt = tmo.getFarSettlementAmount();
				CurrencyPair cp = getCurrencyPair(tmo);
				Currency ccy = isTerm(tmo) ? cp.getBaseCurrency() : cp.getVariableCurrency();
				double farAmtUSD;
                if (ISConstantsC.USD.equals(ccy.getRealCurrency ().getName())) {
                    farAmtUSD = farAmt;
                } else {
                    farAmtUSD = DealingModelUtil.getAmountInUSD(farAmt, ccy);
                }
				tmo.setFarSettlementAmountAsUSD(farAmtUSD);
			}
		}catch (Exception e){
    		log.error("Exception during calculating USD Amount " + tmo.getTradeTxnId(), e);
		}
		log.info("stlUSD=" + tmo.getSettlementAmountAsUSD() + ", farStlUSD=" + tmo.getFarSettlementAmountAsUSD());
    }
    
    private CurrencyPair getCurrencyPair(TradeManagementObject tmo){
		String ccyPair = tmo.getCcyPair();
		if (isTerm(tmo)) ccyPair = ccyPair.substring(0, ccyPair.length() - 1);
		return CurrencyFactory.getCurrencyPairFromString(ccyPair);
    }
    
    private boolean isTerm(TradeManagementObject tmo){
    	return tmo.getCcyPair().endsWith("#");
    }
    
    private boolean isSSP(TradeManagementObject tmo){
    	return ISCommonConstants.TRD_CLSF_FXSSP.equals(tmo.getTradeclassification());
    }
}
