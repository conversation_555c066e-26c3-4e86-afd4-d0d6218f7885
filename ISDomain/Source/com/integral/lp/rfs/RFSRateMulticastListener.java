package com.integral.lp.rfs;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.message.BrokerOrderResponse;
import com.integral.is.message.ISMessage;
import com.integral.is.message.TradeResponse;
import com.integral.is.message.TradeStatusResponse;
import com.integral.is.message.rfs.RFSMarketRate;
import com.integral.is.message.rfs.RFSMarketRateC;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.handler.AdaptorResponseHandler;
import com.integral.is.spaces.fx.listener.SerialTradeMessageProcessorManager;
import com.integral.is.spaces.fx.listener.SpacesTradeListener;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.server.VirtualServer;
import com.integral.transport.multicast.MulticastAddress;
import com.integral.transport.multicast.MulticastAddressPoolService;
import com.integral.transport.multicast.MulticastGroup;
import com.integral.user.Organization;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> sharma
 */
public class RFSRateMulticastListener {

    private int port;

    private MulticastSocket socket;

    static Log log = LogFactory.getLog(RFSRateMulticastListener.class);

    private static RFSRateMulticastListener instance = new RFSRateMulticastListener();

    String MCAST_ADDRESS_KEY_RFSRates = "RFSRate";

    public RFSRateMulticastListener() {

    }

    public static void __setInstance(RFSRateMulticastListener _instance){
        instance = _instance;
    }

    public void init() throws Exception {
        try {
            this.port = ISFactory.getInstance().getISRfsMBean().getRFSRateMulticastListenerPort();
            this.socket = new MulticastSocket(port);
            socket.setReuseAddress(true);
            new Thread(new Worker(10000),
                    "rfs-rate-listener").start();
            log.info("RFS Rate listener started at port="+port);
        } catch (Exception e) {
            log.error("Error in initializing rate multicast listener", e);
            throw e;
        }
    }

    public void startRates() {
        MulticastAddressPoolService maps = MulticastAddressPoolService.getInstance();
        String vs = ConfigurationFactory.getServerMBean().getVirtualServerName();
        try {
                com.integral.organization.MulticastAddress ma = maps.createMulticastAddress(vs, MCAST_ADDRESS_KEY_RFSRates);
                if(ma!=null){
                    try {
                        socket.joinGroup(InetAddress.getByName(ma.getMulitcastAddress()));
                        if(log.isInfoEnabled()) {
                            log.info("rfs-rate: joined on multicast group=" + ma.getMulitcastAddress() + ", VS=" + vs);
                        }
                    }catch(SocketException e){
                        log.warn("rfs-rate: failed to join mcast group="+ ma.getMulitcastAddress()
                                + ", VS=" + vs
                                + ", reason=" + e.getMessage());
                    }
                }
            }catch (Exception e){
                log.error("Failed to retrieve mulicast address from pool service for vs="
                        + vs + ", type=" + MCAST_ADDRESS_KEY_RFSRates,e);
            }

    }

    public static RFSRateMulticastListener getInstance() {
        return instance;
    }

    class Worker implements Runnable {

        byte[] buffer;
        UnSafeBuffer safeBuf;

        public Worker(int bufzise) {
            buffer = new byte[bufzise];
            safeBuf = new UnSafeBuffer();
        }

        @Override
        public void run() {
            while (true) {
                DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
                try {
                    socket.receive(dp);

                    // process the buffer
                    safeBuf.init(buffer);
                    RFSMarketRate rfsMarketRate = new RFSMarketRateC();
                    rfsMarketRate.readFrom(safeBuf);

                    //send to downstream.
                    handleMessage(rfsMarketRate);

                } catch (Exception e) {
                    log.warn("Unable to receive  multicast datagram packet", e);
                } finally {
                    safeBuf.resetMemoryBuffer();
                }
            }
        }

        public boolean handleMessage(RFSMarketRate response){
            long sentTimeStamp = System.currentTimeMillis();
            long receivedTime = System.currentTimeMillis();
            AdaptorResponseHandler handler = SpacesTradeListener.getHandler(RFSMarketRateC.class.getName());

            if (handler != null) {
                    response.getTiming().setTime(ISConstantsC.EVENT_TIME_DISP_ADAPTER_SENT_RESPONSE, sentTimeStamp);
                    response.getTiming().setTime(AdaptorResponseHandler.EVENT_TIME_RESPONSE_RECEIVED_BY_APP, receivedTime);

                    // Give the response to handler.
                    handler.handleResponse(response);
                    return true;
            }
            return false;
        }
    }
}
