package com.integral.lp.handler;

import com.integral.broker.BrokerAdaptorFactory;
import com.integral.broker.config.BrokerAdaptorMBean;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.imtp.config.IMTPConfigMBeanFactory;
import com.integral.imtp.config.IMTPConfigMBeanImpl;
import com.integral.imtp.config.IMTPVersion;
import com.integral.imtp.connection.IMTPConnection;
import com.integral.imtp.connection.IMTPConnection.ConnectionState;
import com.integral.imtp.connection.IMTPConnectionListener;
import com.integral.imtp.connection.IMTPConnectionManager;
import com.integral.imtp.connection.Initiator;
import com.integral.imtp.session.IMTPSession;
import com.integral.imtp.session.IMTPSessionManager;
import com.integral.is.ISCommonConstants;
import com.integral.is.broker.BrokerWorkflowFunctorC;
import com.integral.is.common.Provider;
import com.integral.is.common.comm.imtp.IMTPMessageSenderC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.LoginMessage;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.ProviderStatus;
import com.integral.is.message.ResponseMessage;
import com.integral.is.order.OAWorkflowFunctorC;
import com.integral.is.warmuptrade.WarmUpTradeFactory;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.jmsx.JMSManager;
import com.integral.jmsx.JNDIEntry;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.Adaptor;
import com.integral.lp.AdaptorC;
import com.integral.lp.SubscriptionUtil;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.query.QueryFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.server.VirtualServer;
import com.integral.transport.multicast.MulticastGroup;

import java.io.IOException;
import java.util.ArrayList;
import java.util.ConcurrentModificationException;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: verma
 * Date: 10/5/11
 * Time: 11:00 AM
 * To change this template use File | Settings | File Templates.
 */
public class AdaptorStatusHandlerC implements AdaptorStatusHandler, IMTPConnectionListener {
    protected static ISMBean mbean = ISFactory.getInstance().getISMBean();
    protected static Log log = LogFactory.getLog( AdaptorStatusHandlerC.class );
    protected Adaptor adaptor;
    private boolean isLoggedIn;
    protected IMTPConnection imtpConnection;
    protected boolean isSpacesEnabled = ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled();

    protected static Initiator initiator;

    static {
        initiator = IMTPConnectionManager.getInstance().getInitiator();
        if ( initiator == null ) {
            log.error( "Unable to obtain an IMTP Initiator from IMTPConnectionManager" );
        }
    }

    private String currentAcceptorId;

    public AdaptorStatusHandlerC( Adaptor adaptor ) {
        this.adaptor = adaptor;
    }

    protected boolean isProcessingRequired( ProviderStatus ps )
    {
        // Handle ProviderStatus message only if the local server and provider both are
        // running with spaces enabled.
        if (isSpacesEnabled) {
            if( ps.getProviderType() == null || (ps.getProviderType() != null && ps.getProviderType().equals("P")) ){
                return true;
            }
            if( ProviderStatus.PROVIDER_TYPE_BROKER.equals( ps.getProviderType() )){
                if( ps.getIsSpacesEnabled() ){
                    return true;
                }
                else{
                    if ( log.isDebugEnabled() ) {
                        log.debug( "isProcessingRequired : Non Spaces Broker's ProviderStatus. No processing required for ProviderStaus=" + getProviderStatusDesc( ps ) );
                    }
                    return false;
                }
            }
        }
        else {
            if( ps.getProviderType() == null || (ps.getProviderType() != null && ps.getProviderType().equals("P")) ){
                return true;
            }
            if( ProviderStatus.PROVIDER_TYPE_BROKER.equals( ps.getProviderType() )){
                if( ps.getIsSpacesEnabled() ){
                    if ( log.isDebugEnabled() ) {
                        log.debug( "isProcessingRequired : Spaces Broker's ProviderStatus. No processing required for ProviderStaus=" + getProviderStatusDesc( ps ) );
                    }
                    return false;
                }
                else{
                    return true;
                }
            }
        }
        return true;
    }

    private String getProviderStatusDesc( ProviderStatus ps ) {
        try{
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( '{' );
            sb.append( " name=" ).append( ps.getProviderShortName() );
            sb.append( ", type=" ).append( ps.getProviderType() );
            sb.append( ", spaces=" ).append( ps.getIsSpacesEnabled() );
            sb.append( ", vs=" ).append( ps.getVirtualServer() );
            sb.append( '}' );
            return sb.toString();
        }
        catch ( Exception ex ){
            return "";
        }
    }

    public synchronized ErrorMessage handleAdaptorStatus( ProviderStatus ps ) {
        if ( !isProcessingRequired( ps ) ) {
            return null;
        }
        int status = ps.getStatus();
        long adaptorStartTime = ps.getAdaptorStartTime();
        Map streamStatus = ( Map ) ( ps.getProperty( ISCommonConstants.STREAM_STATUS ) );
        String hostName = ps.getHostName();
        String virtualServer = ps.getVirtualServer();
        int imtpPort = ps.getImtpPort();
        String imtpVersionId = ps.getIMTPVersionId();

        if ( log.isDebugEnabled() ) {
            log.debug( "handleAdaptorStatus : Adaptor --> " + adaptor.getName() + ", VirtualServer --> " + virtualServer +
                    ", HostName --> " + hostName + ", IMTPPort --> " + imtpPort + ", Status --> " + status +
                    ", Provider.startTime --> " + adaptorStartTime + ", Current StartTime --> " + adaptor.getStartTime() +
                    ", LoggedInStatus --> " + isLoggedIn + ", IsIMTPEnabled --> " + ISFactory.getInstance().getISMBean().
                    getVirtualServer().isIMTPEnabled() + ", IMTPVersionId -->" + imtpVersionId +", spacesEnabled-->"+ps.getIsSpacesEnabled());
        }
        if ( adaptorStartTime == 0 ) {
            log.warn( "WorkflowFuctorC.handleAdaptorStatus : Adaptor " + adaptor.getName() + " has sent starttime as 0 and status as " + status +
                    ".So returning without updating provider status" );
            return null;
        }
        adaptor.setLastHeartbeatTime();
        adaptor.setStatus( status );
        if ( status == ProviderStatus.ACTIVE || status == ProviderStatus.STALE ) {
            if ( !isLoggedIn || ( adaptor.getStartTime() < adaptorStartTime ) ) {
                isLoggedIn = false;
                //validate before login.
                List<String> errors = validateAdaptor(ps);
                if(!errors.isEmpty()){
                    ErrorMessage message = new ErrorMessageC();
                    message.setMessages(errors);
                    return message;
                }
                //Step 1
                if ( ensureIMTPLogin( virtualServer, hostName, imtpPort, imtpVersionId ) ) {
                    //step 2
                    isLoggedIn = doLogin();
                    //Between Step 1 and 2 IMTP may have gone bad but login succeeded, make sure Closed notification is not being suppressed here by Login returning true.
                    if ( isLoggedIn ) {
                        ensureIMTPLogin( virtualServer, hostName, imtpPort, imtpVersionId );
                    }
                }
                if ( isLoggedIn ) {
                    //call WarmUpTradeManager
                	// TODO : Implement spaces specific warmup
                	if( !ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled())
                	{
	                    boolean isRuntimeWarmupRequired = WarmUpTradeUtilC.getInstance().getTestTradeMBean().isRuntimeWarmUpEnabled();
	                     boolean isTestTradeRequired = ( WarmUpTradeFactory.getWarmUpTradeManager() != null ) ? WarmUpTradeFactory.getWarmUpTradeManager().isWarmupTestTradeRequired( adaptor.getName() ) : false;
	                    if ( isRuntimeWarmupRequired && isTestTradeRequired ) {
	                        for ( Provider provider : adaptor.getProviders() ) {
	                            com.integral.lp.ProviderManagerC.getInstance().makeProviderInactiveForClient( provider.getName() );
	                            WarmUpTradeFactory.getWarmUpTradeManager().initiateRunTimeTestRequests( provider.getName() );
	                        }
	                    }
                	}
                    //When the startTime is 0 its the first heartbeat for the adaptor instance.
                    // Re-subscriptions will be triggered by the Provider objects as they are made active
                    if ( adaptor.getStartTime() != 0 ) {
                        doReSubscription();
                    }
                    log.info( "updating start time for adaptor " + adaptor.getName() + " to " + adaptorStartTime );
                    adaptor.setStartTime( adaptorStartTime );
                    //adaptor.setHostName(hostName); //Since we are now getting the hostname from the heartbeat, set adaptor hostname here. No need to set hostname on every heartbeat.
                }
            } else if( adaptor.getStartTime() > adaptorStartTime) {
                try{
                    MessageLogger.getInstance().log(ISCommonConstants.ALERT_ADAPTOR_DUPLICATE_HEARTBEAT, this.getClass().getName(), "Duplicate Adaptor Alert:"+adaptor.getName()+". Received Heartbeat with start Time less than previously received. heartbeat:" + ps.getToString() + ", current Adaptor Start Time:"+adaptor.getStartTime(), null);
                } catch (Exception ex) {
                    log.error(ex.getMessage(),ex);
                }
            }
        }

        int statusToReport = status;

        if ( !isLoggedIn ) {
            statusToReport = ProviderStatus.INACTIVE;
        }

        adaptor.setStatus( statusToReport );
		try {
			for (Provider provider : adaptor.getProviders()) {
				provider.setStatus(statusToReport);
				if (statusToReport != ProviderStatus.INACTIVE
						&& streamStatus != null) {
					provider.setStreamsStatus(streamStatus);
				}
			}

			if (adaptor.hasHostAdaptor()) {
				Adaptor hostAdaptor = adaptor.getHostAdaptor();
				hostAdaptor.setLastHeartbeatTime();
				hostAdaptor.updateStatus(adaptor, statusToReport);
			}
		} catch (ConcurrentModificationException e) {			
			log.warn("Adaptor:" + adaptor.getName() + " was modified concurrently");
		}

        return null;
    }

    protected List<String> validateAdaptor(ProviderStatus ps) {
        List<String> result = new ArrayList<String>(5);
        if (mbean.getVirtualServer().isLPAutoDeployEnabled()) {
            if (OAWorkflowFunctorC.class.equals(adaptor.getWorkflowFunctor().getClass())) {
                //check for workflow validation at runtime if auto deployment is true.
                String errorMsg = "Validation: Incorrect Workflow Functor defined for Adaptor:" + adaptor.getName();
                result.add(errorMsg);
                log.warn(errorMsg + ". Shutting down adaptor as it is not a Do Provider.");
                ISFactory.getInstance().getServicProvidersMBean().stopAllProvidersForAdaptor(adaptor.getName());
            } else if (BrokerWorkflowFunctorC.class.equals(adaptor.getWorkflowFunctor().getClass())) {
                //check for workflow validation at runtime if auto deployment is true.
                if (!ProviderStatus.PROVIDER_TYPE_BROKER.equals(ps.getProviderType())) {
                    String errorMsg = "Validation: Incorrect Workflow Functor defined for Adaptor:" + adaptor.getName();
                    result.add(errorMsg);
                    log.warn(errorMsg + ". Shutting down adaptor as it is not a Broker Provider.");
                    ISFactory.getInstance().getServicProvidersMBean().stopAllProvidersForAdaptor(adaptor.getName());
                }
            }
        }
        return result;
    }

    public void shutdown() {
        if(imtpConnection != null){
            imtpConnection.removeConnectionListener(this);
        }
    }

    protected boolean doLogin() {
        try {
            for ( Provider provider : adaptor.getProviders() ) {
                provider.getQuoteCache().removeAll();
            }
            LoginMessage msg = createLoginMessage();
            log.info( "doLogin : Sending login request " + msg.toString() + " to Adaptor " + adaptor.getName() + " with Multicast enabled= " + msg.isMulticastListener() );
            ResponseMessage rm = ( ResponseMessage ) adaptor.sendMessage( msg );
            if ( log.isDebugEnabled() ) {
                log.debug( "doLogin : Response " + rm.toString() + " from provider " + adaptor.getName() + "" );
            }
            if ( rm.getStatus() == ResponseMessage.SUCCESS ) {
                if ( imtpConnection != null ) {
                    imtpConnection.setAppActive();
                }
                return true;
            }
            return false;
        }
        catch ( Exception ex ) {
            log.error( "doLogin : Failed to send login request to " + adaptor.getName(), ex );
            return false;
        }
    }

    protected LoginMessage createLoginMessage() throws IdcNoSuchObjectException {
        String adaptorName = adaptor.getName();
        LoginMessage msg = MessageFactory.newLoginMessage();
        msg.setServerId( mbean.getServerId() + "@" + adaptorName );
        msg.setRateDestination("TOIS.RATES."+adaptorName);
        msg.setTradeDestination("TOIS.TRADES."+adaptorName);
        msg.setBrokerId("GlobalBroker");

//        String providerGroup = mbean.getProviderGroup( adaptorName );
//        JNDIEntry entry = JMSManager.getInstance().getDestinationInfo( "TOIS.RATES." + providerGroup );
//        if(entry != null) {
//            String brokerId = entry.getJmsBroker().getShortName();
//            msg.setRateDestination( entry.getJmsName() );
//            String vServerName = ConfigurationFactory.getServerMBean().getVirtualServerName();
//            // If brokerId = GlobalBroker then use TOIS.TRADES.<VSNAME> else TOIS.TRADES.<VSNAME>.GlobalBroker10
//            entry = ISUtilImpl.getInstance().getGlobalBrokerJNDIEntry( brokerId, "TOIS.TRADES." + vServerName );
//            msg.setTradeDestination( entry.getJmsName() );
//            msg.setBrokerId( brokerId );
//            msg.setProperty( "brokerURL", entry.getJmsBroker().getBeanProperty("brokerURL"));
//            msg.setProperty( "brokerAdminUserName", entry.getJmsBroker().getBeanProperty("brokerAdminUserName"));
//            msg.setProperty( "brokerAdminUserPassword", entry.getJmsBroker().getBeanProperty("brokerAdminUserPassword"));
////            JNDIEntry auditJNDIEntry = ISUtilImpl.getInstance().getGlobalBrokerJNDIEntry( brokerId, ISCommonConstants.TRADE_AUDIT_MESSAGE_JNDI_NAME );
////            msg.setPersistenceMessageQueue( auditJNDIEntry.getJmsName() );
//        }
        String adminUser = adaptor.getConfiguration().getDefaultUser().getFullName();
        msg.setAdminUser( adminUser );
        msg.setProperty( "AUTO.CANCEL.ENABLED", String.valueOf( mbean.getAutoCancelEnabledAtAdaptor() ) );
        msg.setProperty( "PROVIDER.NAME", adaptorName );
        VirtualServer vs = ( VirtualServer ) QueryFactory.getQueryService().find( com.integral.system.server.VirtualServerC.class, ConfigurationFactory.getServerMBean().getVirtualServerName() );
        MulticastGroup mcastGroup = adaptor.getOrganization().getMulticastGroup();
        msg.setMulticast( ( mcastGroup != null && mcastGroup.isEnabled() && !mcastGroup.getAddresses().isEmpty() && vs.isMulticastEnabled() ) );
        msg.setTextMessages( true );
        return msg;
    }

    protected void doReSubscription() {
        log.info( "doReSubscription : Starting resubscription for rates as adaptor has come up" );
        SubscriptionUtil.reSubscribeToAdaptor( adaptor.getOrganization() );
    }

    protected boolean ensureIMTPLogin(String acceptorId, String acceptorHost, int imtpPort, String imtpVersionId) {
        boolean result = true;
        VirtualServer vs = ISFactory.getInstance().getISMBean().getVirtualServer();
        boolean imtpEnabled = vs.isIMTPEnabled();
        IMTPConnection imtpConnection = getImtpConnection(acceptorId);
        log.info("Adaptor:" + adaptor.getName() + ": IMTPEnabled=" + imtpEnabled + ",imtpPort=" + imtpPort + ",imtpVersionId=" + imtpVersionId +
                ",imtpConnection=" + imtpConnection + ",current AcceptorId=" + currentAcceptorId + ", new AcceptorId=" + acceptorId + ",AdaptorInfo=" + adaptor.getClass().getName());
        if (imtpEnabled &&
                ( imtpConnection == null
                        || imtpConnection.getConnectionState().equals(ConnectionState.CLOSED)
                        || !acceptorId.equals(currentAcceptorId) )
                && imtpPort > 0)
            //Check if both server and client are enabled for IMTP
        {
            if (imtpVersionId == null || "null".equals(imtpVersionId) || IMTPVersion.getMajorVersion(imtpVersionId) != IMTPVersion.IMTP_VERSION_MAJOR) {
                log.warn("Found conflicting IMTPVersion running for provider:" + acceptorHost + ". IMTP connection would not be made. " +
                        "Initiator IMTPVersion:" + IMTPVersion.IMTP_VERSION_ID + ", Acceptor IMTPVersion:" + imtpVersionId);
            } else {
                result = doIMTPConnect(acceptorId, acceptorHost, imtpPort, vs.getShortName() );
            }
        }
        return result;
    }

    protected IMTPConnection getImtpConnection( String virtualServer ) {
        return imtpConnection;
    }

    public boolean doIMTPConnect( String acceptorId, String acceptorHost, int acceptorPort, String virtualServer ) {
        boolean result = false;
        IMTPConfigMBeanImpl config = IMTPConfigMBeanImpl.copyIMTPConfig(IMTPConfigMBeanFactory.getInstance().getIMTPConfigMBean());
        config.setAcceptorHost(acceptorHost);
        config.setAcceptorId(acceptorId);
        config.setAcceptorPort(acceptorPort);
        IMTPConnection imtpConnection = getImtpConnection( acceptorId );
        if (imtpConnection == null || imtpConnection.getConnectionState().equals(ConnectionState.CLOSED) || !acceptorId.equals(currentAcceptorId) ) {
            try {
                IMTPSession session = IMTPSessionManager.getInstance().getSession(IMTPSessionManager.getSessionId(config), initiator.getSessionPipelineSetupFunctor());
                imtpConnection = session.getConnection();
                //Session may have a connection already established if this is a multi-host adaptor or a demo bank. Reuse the same physical connection in that case.
                if (imtpConnection == null|| imtpConnection.getConnectionState().equals(ConnectionState.CLOSED)) {
                    if (!session.getAndSetIsPendingConnection(true)) {
                        try {
                            imtpConnection = session.getConnection();
                            if (imtpConnection == null || imtpConnection.getConnectionState().equals(ConnectionState.CLOSED)) {
                                //Register the receiver before initiating connection to avoid the race condition of when the receiver is set and when the peer starts sending Trade responses.
                                //session.getMessageHandler().registerReceiver(new IMTPTradeMessageListenerC(), "TOIS.TRADES"); //Setting trade listener for Trade responses.
                                //Setting receivers in startup class on connection activation.
                                log.info("Setting IMTP Connection for Adaptor:" + adaptor.getName());
                                imtpConnection = initiator.connect(config);  //1. Connection Made.
                                result = true;
                            }
                        } finally {
                            session.getAndSetIsPendingConnection(false);
                        }
                    }else{
                        log.info("IMTP Connection already pending for session:"+session.getSessionId());
                        result =false;
                    }
                }else{
                    result = true;
                }
                if (result) {
                    setImtpConnection(acceptorId,imtpConnection);
                    imtpConnection.registerConnectionListener(this, true); //2. Listener registered, so that we can be notified for close event.
                    //Between 1 and 2, the connection could have already been closed. Make sure the state is ACTIVE at this point.
                    if (imtpConnection.getConnectionState() == IMTPConnection.ConnectionState.CLOSED) {
                        cleanUpIMTPConnection( imtpConnection );
                        result = false;
                    }
                }
            } catch (IOException e) {
                log.warn("doIMTPConnect : Failed to establish imtp connection to " + adaptor.getName()
                        + ", AdaptorAddress:" + config.getAcceptorHost() + ":" + config.getAcceptorPort(), e);
                    if(imtpConnection != null){
                        cleanUpIMTPConnection( imtpConnection );
                    }

                result = false;
            }
        } else {
            result = true;
        }

        if (result) {
            ((AdaptorC) adaptor).setupMessageSender(new IMTPMessageSenderC(config));
        }
        return result;
    }

    public void notifyConnectionEvent( IMTPConnection connection, IMTPConnectionListener.ConnectionEvent event ) {
        String acceptorId = connection.getIMTPConfig().getAcceptorId();
        IMTPConnection imtpConnection = getImtpConnection( acceptorId );
        if ( connection == imtpConnection) {
            if(event.type == IMTPConnectionListener.ConnectionEventType.STATE_CHANGED ) {
                IMTPConnection.ConnectionState state = ( IMTPConnection.ConnectionState ) event.payload;
                switch ( state ) {
                    case CLOSED:
                        log.info( "notifyConnectionEvent().CLOSED called for adaptor:" + adaptor.getName() + ". IMTPSession:" + connection.getSession().getSessionId() );
                        cleanUpIMTPConnection(imtpConnection);
                }
            }
        } else {
            log.error("Received notification event for imtp connection not configured with status handler. " +
                    "Connection Registered:"+imtpConnection+", Received notification for connection:"+connection);
            logIMTPConnections();
        }
    }

    protected void cleanUpIMTPConnection(IMTPConnection imtpConnection) {
        log.info("cleanUpIMTPConnection() called for adaptor:" + adaptor.getName());
        isLoggedIn = false;
        if (imtpConnection != null) {
            removeIMTPConnection(imtpConnection.getIMTPConfig().getAcceptorId());
            imtpConnection.removeConnectionListener(this);
        }
        adaptor.setStatus(ProviderStatus.INACTIVE); //Making the adaptor inactive to make sure no trade requests slip in till Login happens again.
        for (Provider provider : adaptor.getProviders()) {
            provider.setStatus(ProviderStatus.INACTIVE);
        }
        //((AdaptorC) adaptor).setupMessageSender(); //fall back to whatever is configured.
    }

    protected void removeIMTPConnection( String acceptorId ) {
        imtpConnection = null;
    }

    protected void setImtpConnection( String acceptorId, IMTPConnection imtpConnection ) {
        this.imtpConnection = imtpConnection;
        this.currentAcceptorId = acceptorId;
    }

    protected void logIMTPConnections() {
        log.info("Existing IMTP Connections:"+imtpConnection);
    }
}