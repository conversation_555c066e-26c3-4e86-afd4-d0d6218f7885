package com.integral.lp;

import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.is.common.Provider;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.primebroker.PrimeBrokerPathUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.user.Organization;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by IntelliJ IDEA.
 * User: verma
 * Date: 1/17/11
 * Time: 12:36 PM
 * To change this template use File | Settings | File Templates.
 */
public class StreamManager
{
    private static StreamManager _instance = new StreamManager();
    private static Log log = LogFactory.getLog( StreamManager.class );
    private static ISMBean isMBean = ISFactory.getInstance().getISMBean();
    private static ProviderManagerC providerManager = ProviderManagerC.getInstance();
    private ServerMBean serverMBean = ConfigurationFactory.getServerMBean();
    //todo-malp description
    private static final ConcurrentHashMap<String, StreamManagementParams> streamParamsMap = new ConcurrentHashMap<String, StreamManagementParams>();

    public static StreamManager getInstance()
    {
        return _instance;
    }

    private StreamManager()
    {
    }

    public void resetForStreamManagementOrg( Organization streamManagementOrg )
    {
        String streamManagementOrgName = streamManagementOrg.getShortName();
        for( StreamManagementParams smp : streamParamsMap.values() )
        {
            if( smp.streamManagementOrganization.getShortName().equals( streamManagementOrgName ))
            {
                smp.setDirty();
            }
        }
    }

    public void resetForProviderOrg( Organization providerOrg )
    {
        String providerOrgName = providerOrg.getShortName();
        for( StreamManagementParams smp : streamParamsMap.values() )
        {
            if( smp.providerOrg.getShortName().equals( providerOrgName ))
            {
                smp.setDirty();
            }
        }
    }

    public void resetForAdaptorOrg( Organization adaptorOrg )
    {
        String adaptorOrgName = adaptorOrg.getShortName();
        for( StreamManagementParams smp : streamParamsMap.values() )
        {
            if( smp.adaptorOrg.getShortName().equals( adaptorOrgName ))
            {
                smp.setDirty();
            }
        }
    }

    public void reset( Organization providerOrg,Organization fiOrg )
    {
        String providerOrgName = providerOrg.getShortName();
        String fiOrgName = fiOrg.getShortName();
        for ( StreamManagementParams smp : streamParamsMap.values() )
        {
            if( smp.providerOrg.getShortName().equals( providerOrgName )
                    && smp.subscriptionLegalEntity.getOrganization().getShortName().equals( fiOrgName ))
            {
                smp.setDirty();
            }
        }
    }
    
    public void resetForFI( Organization fiOrg )
    {
        String fiOrgName = fiOrg.getShortName();
        for ( StreamManagementParams smp : streamParamsMap.values() )
        {
            if( smp.subscriptionLegalEntity.getOrganization().getShortName().equals( fiOrgName ))
            {
                smp.setDirty();
            }
        }
    }
    

    public void reset(Organization streamManagementOrganization,LegalEntity streamLegalEntity )
    {
        String streamManagementOrgName = streamManagementOrganization.getShortName();
        String streamLEName = streamLegalEntity.getFullyQualifiedName();
        for( StreamManagementParams smp : streamParamsMap.values() )
        {
            if( smp.streamManagementOrganization.getShortName().equals( streamManagementOrgName )
                    &&
                    smp.streamLegalEntity.getFullyQualifiedName().equals( streamLEName ))
            {
                smp.setDirty();
            }
        }
    }

    /**
     * @param providerOrg
     * @param customerLegalEntity
     * @return
     */
    public String getStreamId( Organization providerOrg, LegalEntity customerLegalEntity )
    {
        StringBuilder sb = new StringBuilder( 50 );
        sb.append( providerOrg.getShortName() ).append( '/' ).append( customerLegalEntity.getFullyQualifiedName() );
        String key = sb.toString();
        StreamManagementParams smp = streamParamsMap.get( key );
        if ( smp != null && smp.isDirty() )
        {
            streamParamsMap.remove( key );
            smp = null;
        }
        if ( smp == null )
        {
            Counterparty counterpartyB = CounterpartyUtilC.getCounterpartyB( customerLegalEntity, providerOrg );


            if ( counterpartyB != null )
            {
                List<TradingParty> pbTPs = PrimeBrokerPathUtil.getInstance().getCoverTradePrimeBrokerTPs( customerLegalEntity, providerOrg );
                Provider provider = providerManager.getProvider( providerOrg.getShortName() );
                if ( provider != null )
                {
                    Organization adaptorOrg = provider.getAdaptor().getOrganization();
                    smp = getStreamManagementParams( providerOrg, adaptorOrg, customerLegalEntity, pbTPs );
                }
            }
        }
        if ( smp != null )
        {
            return smp.streamId;
        }
        return null;
    }

    public StreamManagementParams getStreamManagementParams( Organization providerOrg, Organization adaptorOrg, LegalEntity subscriptionLegalEntity, List<TradingParty> primeBrokerTradingParty )
    {
        StringBuilder sb = new StringBuilder( 50 );
        sb.append( providerOrg.getShortName() ).append( '/' ).append( subscriptionLegalEntity.getFullyQualifiedName() );
        String key = sb.toString();
        StreamManagementParams smp = streamParamsMap.get( key );
        if ( smp != null && smp.isDirty() )
        {
            streamParamsMap.remove( key );
            smp = null;
        }
        if ( smp == null )
        {
            StreamManagementParams newSmp = new StreamManagementParams( providerOrg, adaptorOrg, subscriptionLegalEntity, primeBrokerTradingParty );
            StreamManagementParams oldSmp = streamParamsMap.putIfAbsent( key, newSmp );
            if ( oldSmp != null )
            {
                smp = oldSmp;
            }
            else
            {
                smp = newSmp;
            }
        }
        return smp;
    }


    public class StreamManagementParams
    {
        public final Organization streamManagementOrganization;
        public final LegalEntity streamLegalEntity;
        public final LegalEntity subscriptionLegalEntity;
        public final List<TradingParty> primeBrokerTradingParty;
        private final Organization providerOrg;
        private final Organization adaptorOrg;

        public final String streamStrategy;
        public final String streamId;
        public boolean dirty = true;

        private StreamManagementParams( Organization providerOrg, Organization adaptorOrg, LegalEntity subscriptionLegalEntity, List<TradingParty> primeBrokerTradingParty )
        {

            this.providerOrg = providerOrg;
            this.adaptorOrg = adaptorOrg;
            this.subscriptionLegalEntity = subscriptionLegalEntity;
            this.primeBrokerTradingParty = primeBrokerTradingParty;
            //Real LP
            if ( adaptorOrg.getShortName().equals( providerOrg.getShortName() ) )
            {
                if ( primeBrokerTradingParty == null || primeBrokerTradingParty.isEmpty())
                {
                    streamStrategy = "FI-LP : SM [LP-FI]";
                    streamManagementOrganization = providerOrg;
                    streamLegalEntity = subscriptionLegalEntity;
                }
                else
                {
                    streamStrategy = "FI-PB-LP : SM [LP-FI]";
                    streamManagementOrganization = providerOrg;
                    streamLegalEntity = subscriptionLegalEntity;
                }
            }
            else
            {
                if ( primeBrokerTradingParty == null  || primeBrokerTradingParty.isEmpty())
                {
                    streamStrategy = "FI-AnonLP : SM [AnonLP-FI]";
                    streamManagementOrganization = providerOrg;
                    streamLegalEntity = subscriptionLegalEntity;
                }
                else
                {
                    if ( isMBean.isPBPriceStreamEnabled( providerOrg.getShortName() ) )
                    {
                        streamStrategy = "FI-PB-AnonyLP : SM [LP-PB]";
                        streamManagementOrganization = adaptorOrg;
                        streamLegalEntity = primeBrokerTradingParty.get(primeBrokerTradingParty.size() -1).getLegalEntity();
                    }
                    else
                    {
                        streamStrategy = "FI-PB-AnonyLP : SM [AnonyLP-FI]";
                        streamManagementOrganization = providerOrg;
                        streamLegalEntity = subscriptionLegalEntity;
                    }
                }
            }
            //TODO Verify This in testing. Not sure about this logic. Is it correct?
            if(subscriptionLegalEntity.getOrganization().getName().equals(adaptorOrg.getShortName()) && serverMBean.isDoStreamEnabled() ){
                //return Do Stream if broker is subscribing to itself.
                streamId = ServerMBean.DEFAULT_DO_STREAM_NAME;
            }
            else if( ISUtilImpl.getInstance().isVenueProvider( adaptorOrg ) ){
            	TradingVenueEntity tv = adaptorOrg.getTradingVenueOrgFunction().getTradingVenue();
                streamId = ISUtilImpl.getInstance().getCustomerRexStreamId(subscriptionLegalEntity,tv);
            }
            else{
                streamId = ISUtilImpl.getInstance().getStreamId( streamLegalEntity, streamManagementOrganization );
            }
            log.info( toString() );
            dirty = false;
        }

        private void setDirty()
        {
            this.dirty = true;
            log.info("StreamManager.setDirty : Set the "+toString()+ " to Dirty");
        }

        private boolean isDirty()
        {
            return this.dirty;
        }

        public String toString()
        {
            StringBuilder sb = new StringBuilder( 100 );
            sb.append( "StreamManager.StreamManagementParams : Input[" );
            sb.append( "Provider : " ).append( providerOrg.getShortName() );
            sb.append( ",Adaptor : " ).append( adaptorOrg.getShortName() );
            sb.append( ",SubscriptionLE : " ).append( subscriptionLegalEntity.getFullyQualifiedName() );
            sb.append("pBTradingParties[");
            if(primeBrokerTradingParty != null){
                for(TradingParty pbTradingParty : primeBrokerTradingParty){
                    sb.append(pbTradingParty.getLegalEntity().getFullyQualifiedName());
                    sb.append(",");
            }

            }
            sb.append("]");
            sb.append( ']' );
            sb.append( " Strategy[" ).append( streamStrategy ).append( ']' );
            sb.append( " Output[StreamManagementOrg : " ).append( streamManagementOrganization.getShortName() );
            sb.append( ",StreamLegalEntity : " ).append( streamLegalEntity.getFullyQualifiedName() );
            sb.append( ",StreamId : " ).append( streamId );
            sb.append( ']' );
            return sb.toString();
        }
    }
}
