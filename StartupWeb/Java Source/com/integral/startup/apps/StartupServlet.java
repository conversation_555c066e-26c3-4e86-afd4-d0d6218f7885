package com.integral.startup.apps;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.runtime.SystemStartupC;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import java.util.StringTokenizer;

public class StartupServlet extends HttpServlet
{

    private static final String DELIMITER = ",";
    protected Log log = LogFactory.getLog( this.getClass() );

    public void init() throws ServletException
    {
        try
        {
            String startups = getServletConfig().getInitParameter( "startup" );
            if ( startups != null )
            {
                StringTokenizer strTok = new StringTokenizer( startups, DELIMITER );
                while ( strTok.hasMoreTokens() )
                {
                    String startup = strTok.nextToken().trim();
                    try
                    {
                        log.info( "Start executing startup " + startup + "..." );

                        SystemStartupC systemStartup = new SystemStartupC();
                        String result = systemStartup.startup( startup, null );

                        log.info( "Finished executing startup " + startup + " with result: " + result );
                    }
                    catch ( Throwable e )
                    {
                        log.error( "Error executing starup " + startup, e );
                        e.printStackTrace();
                    }
                }
            }
            else
            {
                log.info( "##### NO Startup classes defined #####" );
            }
        }
        finally
        {
            //ConfigurationFactory.getServerMBean().setApplicationStarted( true );
        }
    }
}