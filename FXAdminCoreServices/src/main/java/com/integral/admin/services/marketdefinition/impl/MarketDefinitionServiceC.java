package com.integral.admin.services.marketdefinition.impl;

import com.integral.admin.services.marketdefinition.MarketDefinitionConstant;
import com.integral.admin.services.marketdefinition.MarketDefinitionService;
import com.integral.admin.utils.*;
import com.integral.admin.utils.marketdefinition.MarketDefinitionUtil;
import com.integral.admin.utils.notification.NotificationUtil;
import com.integral.admin.utils.transaction.AdminTransactionUtil;
import com.integral.audit.*;
import com.integral.audit.configuration.AuditConfigurationFactory;
import com.integral.audit.configuration.AuditConfigurationMBean;
import com.integral.exception.IdcRuntimeException;
import com.integral.finance.businessCalendar.BusinessCalendar;
import com.integral.finance.businessCalendar.BusinessCalendarFactory;
import com.integral.finance.currency.*;
import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.dateGeneration.RollConvention;
import com.integral.finance.fx.*;
import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.instrument.InstrumentSubClassification;
import com.integral.finance.trade.Tenor;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import com.integral.rule.RuleSet;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;

public class MarketDefinitionServiceC implements MarketDefinitionService, MarketDefinitionConstant
{

    private final static Log log = LogFactory.getLog ( "com.integral.admin.services.marketdefinition.impl.MarketDefinitionService" );

    private static final String SETTLEMENTTYPE_NONE = "NONE";

    /**
     *
     */
    public MarketDefinitionServiceC ( )
    {
    }

    public void activate ( String rateConvention )
    {
        FXRateConvention fxRateConvention = MarketDefinitionUtil.getQuoteConvention ( rateConvention );
        fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
        fxRateConvention.setStatus ( 'A' );
        log.info ( "MDSC.activate - Activating RateConvention " + fxRateConvention.getShortName () );
        auditActivation ( fxRateConvention, true );
    }

    public void activate ( FXRateConvention fxRateConvention )
    {
        fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
        fxRateConvention.setStatus ( 'A' );
        log.info ( "MDSC.activate - Activating RateConvention " + fxRateConvention.getShortName () );
        auditActivation ( fxRateConvention, true );
    }

    public void activate ( Collection<FXRateConvention> fxRateConventions )
    {
        for ( FXRateConvention fxRateConvention : fxRateConventions )
        {
            activate ( fxRateConvention );
        }
    }

    public void inActivate ( String rateConvention )
    {
        FXRateConvention fxRateConvention = MarketDefinitionUtil.getQuoteConvention ( rateConvention );
        fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
        log.info ( "MDSC.inActivate - Inactivating RateConvention " + fxRateConvention.getShortName () );
        fxRateConvention.setStatus ( 'P' );
        auditActivation ( fxRateConvention, false );

    }

    public void inActivate ( Collection<FXRateConvention> fxRateConventions )
    {
        for ( FXRateConvention fxRateConvention : fxRateConventions )
        {
            inActivate ( fxRateConvention );
        }
    }

    public void inActivate ( FXRateConvention fxRateConvention )
    {
        log.info ( "MDSC.inActivate - Inactivating RateConvention " + fxRateConvention.getShortName () );
        fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
        fxRateConvention.setStatus ( 'P' );
        auditActivation ( fxRateConvention, false );

    }

    public void activateCurrency ( Collection<Currency> currencys )
    {
        for ( Currency currency : currencys )
        {
            activateCurrency ( currency );
        }

    }

    public void activateCurrency ( Currency currency )
    {
        log.info ( "MDSC.activateCurrency -  Activating Currency " + currency.getShortName () );
        currency = AdminTransactionUtil.register ( currency );
        currency.setStatus ( Entity.ACTIVE_STATUS );
        AuditEvent audit = initializeAuditCurrency ( currency, MarketDefinitionConstant.AUDIT_ACTION_ACTIVATECURRENCY );
        persistToMongo ( audit );
    }

    public void inActivateCurrency ( Collection<Currency> currencys )
    {
        for ( Currency currency : currencys )
        {
            inActivateCurrency ( currency );
        }

    }

    public void inActivateCurrency ( Currency currency )
    {
        log.info ( "MDSC.activateCurrency - Inactivating Currency " + currency.getShortName () );
        currency = AdminTransactionUtil.register ( currency );
        currency.setStatus ( Entity.PASSIVE_STATUS );
        AuditEvent audit = initializeAuditCurrency ( currency, MarketDefinitionConstant.AUDIT_ACTION_INACTIVATECURRENCY );
        persistToMongo ( audit );
    }

    public void activate ( long rateConventionID )
    {
        FXRateConvention fxRateConvention = MarketDefinitionUtil.getQuoteConvention ( rateConventionID );
        fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
        log.info ( "MDSC.activate - Inactivating RateConvention " + fxRateConvention.getShortName () );
        fxRateConvention.setStatus ( 'A' );
        auditActivation ( fxRateConvention, true );

    }

    public void inActivate ( long rateConventionID )
    {
        FXRateConvention fxRateConvention = MarketDefinitionUtil.getQuoteConvention ( rateConventionID );
        fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
        log.info ( "MDSC.inActivate  - Inactivating RateConvention " + fxRateConvention.getShortName () );
        fxRateConvention.setStatus ( 'P' );
        auditActivation ( fxRateConvention, false );
    }

    public void createFXRateBasis ( FXRateConvention fxRateConvention, String currencyPairName, Currency baseCurrency,
                                    Currency variableCurrency, boolean allowInverse, double baseCurrencyFactor, double variableCurrencyFactor,
                                    int spotPrecision, int pointsPrecision, int forwardPoint, double pipsFactor, int invSpotPrecision, int invPointsPrecision,
                                    int invForwardPoint, double invPipsFactor, Currency cross, boolean useDefaultSettlementInfo, int settlementOvrSpotLag,
                                    Collection<HolidayCalendar> newHolidayCalendars, Collection<HolidayCalendar> noLagholidayCalendars, Collection<HolidayCalendar> ignLagholidayCalendars, boolean isDeliverable,
                                    int fixingCalendarlag, Collection<HolidayCalendar> newFixingHolidayCalendars, Collection<HolidayCalendar> noLagFixingHolidayCalendars,
                                    Collection<HolidayCalendar> ignLagFixingHolidayCalendars, Double contractMultiplier, boolean marketConvention, boolean deliverableNDF, Collection<HolidayCalendar> rejectTODHolidayCalendars, boolean ignoreTODHolidayCalendars, boolean ignoreTOMHolidayCalendars )
    {
        log.info ( "MDSC.createFXRateBasis - Creating FXRateBasis " + currencyPairName + " for Quote Convention " + fxRateConvention.getShortName () );
        StringBuilder newValue = new StringBuilder ( 100 );
        fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
        FXRateBasis fxRateBasis = new FXRateBasisC ();
        fxRateBasis.setNamespace ( AdminTransactionUtil.register ( fxRateConvention.getNamespace () ) );
        fxRateBasis.setFXRateConvention ( fxRateConvention );
        fxRateBasis = AdminTransactionUtil.register ( fxRateBasis );
        fxRateConvention.addFXRateBasis ( fxRateBasis );
        // get the inputs
        FXBusinessCalendar fxBusinessCalendar = fxRateBasis.getFXBusinessCalendar ();
        fxRateBasis.setVariableCurrency ( variableCurrency );
        fxRateBasis.setName ( currencyPairName );
        fxRateBasis.setBaseCurrency ( baseCurrency );
        fxRateBasis.setCrossCurrency ( cross );
        fxRateBasis.setBaseCurrencyFactor ( baseCurrencyFactor );
        fxRateBasis.setVariableCurrencyFactor ( variableCurrencyFactor );
        fxRateBasis.setSpotPrecision ( spotPrecision );
        fxRateBasis.setAllowInverse ( allowInverse );
        fxRateBasis.setInverseSpotPrecision ( invSpotPrecision );
        fxRateBasis.setSpotPointsPrecision ( pointsPrecision );
        fxRateBasis.setInverseSpotPointsPrecision ( invPointsPrecision );
        fxRateBasis.setForwardPointsPrecision ( forwardPoint );
        fxRateBasis.setInverseForwardPointsPrecision ( invForwardPoint );
        fxRateBasis.setInverseSpotPrecision ( invSpotPrecision );
        fxRateBasis.setInversePipsFactor ( invPipsFactor );
        fxRateBasis.setPipsFactor ( pipsFactor );
        fxRateBasis.setMarketConvention ( marketConvention );
        fxRateBasis.setDeliverableNDF ( deliverableNDF );
        HolidayCalendar noLagholidayCalendar1 = null, noLagholidayCalendar2 = null;

        if ( !CollectionUtil.isEmpty ( noLagholidayCalendars ) )
        {
            Iterator<HolidayCalendar> iterator = noLagholidayCalendars.iterator ();
            noLagholidayCalendar1 = iterator.next ();
            if ( iterator.hasNext () )
            {
                noLagholidayCalendar2 = iterator.next ();
            }
        }

        HolidayCalendar ignLagholidayCalendar1 = null, ignLagholidayCalendar2 = null;

        if ( !CollectionUtil.isEmpty ( ignLagholidayCalendars ) )
        {
            Iterator<HolidayCalendar> iterator = ignLagholidayCalendars.iterator ();
            ignLagholidayCalendar1 = iterator.next ();
            if ( iterator.hasNext () )
            {
                ignLagholidayCalendar2 = iterator.next ();
            }
        }

        HolidayCalendar rejectTODHolidayCalendar1 = null, rejectTODHolidayCalendar2 = null;

        if ( !CollectionUtil.isEmpty ( rejectTODHolidayCalendars ) )
        {
            Iterator<HolidayCalendar> iterator = rejectTODHolidayCalendars.iterator ();
            rejectTODHolidayCalendar1 = iterator.next ();
            if ( iterator.hasNext () )
            {
                rejectTODHolidayCalendar2 = iterator.next ();
            }
        }

        if ( useDefaultSettlementInfo )
        {
            fxRateBasis.setFXBusinessCalendar ( null );
        }
        else
        {
            if ( ObjectUtils.isNull ( fxBusinessCalendar ) )
            {
                fxBusinessCalendar = FXFactory.newFXBusinessCalendar ();
                fxRateBasis.setFXBusinessCalendar ( fxBusinessCalendar );
            }
            fxBusinessCalendar.setLag ( settlementOvrSpotLag );
            fxBusinessCalendar.setLagType ( BusinessCalendar.LAG_TYPE_BUSINESS );
            fxBusinessCalendar.setHolidayCalendars ( AdminTransactionUtil.register ( newHolidayCalendars ) );

            fxBusinessCalendar.setNoLagHolidayCalendar1 ( AdminTransactionUtil.register ( noLagholidayCalendar1 ) );
            fxBusinessCalendar.setNoLagHolidayCalendar2 ( AdminTransactionUtil.register ( noLagholidayCalendar2 ) );

            fxBusinessCalendar.setIgnoreTODHolidayCalendars ( ignoreTODHolidayCalendars );
            fxBusinessCalendar.setIgnoreTOMHolidayCalendars ( ignoreTOMHolidayCalendars );
            fxBusinessCalendar.setIgnoreLagHolidayCalendar1 ( AdminTransactionUtil.register ( ignLagholidayCalendar1 ) );
            fxBusinessCalendar.setIgnoreLagHolidayCalendar2 ( AdminTransactionUtil.register ( ignLagholidayCalendar2 ) );
            fxBusinessCalendar.setRejectTODHolidayCalendar1 ( AdminTransactionUtil.register ( rejectTODHolidayCalendar1 ) );
            fxBusinessCalendar.setRejectTODHolidayCalendar2 ( AdminTransactionUtil.register ( rejectTODHolidayCalendar2 ) );
            // make sure the settlement date rule set is null
            fxRateBasis.setSettlementDateRuleSet ( null );
        }
        fxRateBasis.setNDF ( !isDeliverable );
        //Audit
        newValue.append ( "Base Currency: " ).append ( baseCurrency.getName () );
        newValue.append ( ",Variable Currency: " ).append ( variableCurrency.getName () );
        newValue.append ( ",Name: " ).append ( currencyPairName );
        newValue.append ( ",Base Currency Factor: " ).append ( baseCurrencyFactor );
        newValue.append ( ",Variable Currency Factor: " ).append ( variableCurrencyFactor );
        if ( ObjectUtils.isNotNull ( cross ) )
        {
            newValue.append ( ", Cross Currency: " ).append ( cross.getName () );
        }

        // Audit
        newValue.append ( ",Allow Inverse: " ).append ( allowInverse );
        newValue.append ( ",Spot Precision: " ).append ( spotPrecision );
        newValue.append ( ",Inverse Spot Precision: " ).append ( invSpotPrecision );
        newValue.append ( ",Display Offset: " ).append ( pointsPrecision );
        newValue.append ( ",Inverse Display Offset: " ).append ( invPointsPrecision );
        newValue.append ( ",Forward Points Precision: " ).append ( forwardPoint );
        newValue.append ( ",Inverse Forward Points Precision: " ).append ( invForwardPoint );
        newValue.append ( ",Pips Factor: " ).append ( pipsFactor );
        newValue.append ( ",Inverse Pips Factor: " ).append ( invPipsFactor );
        if ( useDefaultSettlementInfo )
        {
            fxRateBasis.setFXBusinessCalendar ( null );
            newValue.append ( ",Settlement - Use Default" );
        }
        else
        {
            newValue.append ( ",Settlement - Spot Lag: " ).append ( settlementOvrSpotLag );
            newValue.append ( ",Holiday Calendar : { " );
            if ( !CollectionUtil.isEmpty ( newHolidayCalendars ) )
            {
                for ( HolidayCalendar calendar : newHolidayCalendars )
                {
                    newValue.append ( calendar.getShortName () ).append ( "," );
                }
                newValue.append ( " }" );
            }
            else
            {
                newValue.append ( "- }" );
            }
            if ( ObjectUtils.isNotNull ( noLagholidayCalendar1 ) )
            {
                newValue.append ( ",No Lag Holiday Calender1: " ).append ( noLagholidayCalendar1.getShortName () );
            }
            if ( ObjectUtils.isNotNull ( noLagholidayCalendar2 ) )
            {
                newValue.append ( ",No Lag Holiday Calender2: " ).append ( noLagholidayCalendar2.getShortName () );
            }

            if ( ObjectUtils.isNotNull ( ignLagholidayCalendar1 ) )
            {
                newValue.append ( ",Ignore Lag Holiday Calender1: " ).append ( ignLagholidayCalendar1.getShortName () );
            }
            if ( ObjectUtils.isNotNull ( ignLagholidayCalendar2 ) )
            {
                newValue.append ( ",Ignore Lag Holiday Calender2: " ).append ( ignLagholidayCalendar2.getShortName () );
            }
        }
        FXBusinessCalendar fxFixingBuissCalendar = fxRateBasis.getFixingBusinessCalendar ();
        if ( !CollectionUtil.isEmpty ( newFixingHolidayCalendars ) && fixingCalendarlag != 0 )
        {
			if ( ObjectUtils.isNull ( fxFixingBuissCalendar ) )
			{
				fxFixingBuissCalendar = new FXBusinessCalendarC ();
			}
            fxFixingBuissCalendar.setLag ( fixingCalendarlag );
            fxFixingBuissCalendar.setLagType ( BusinessCalendar.LAG_TYPE_BUSINESS );
            fxFixingBuissCalendar.setHolidayCalendars ( AdminTransactionUtil.register ( newFixingHolidayCalendars ) );
            fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
            newValue.append ( ",Fixing Calendar Lag: " ).append ( fxFixingBuissCalendar.getLag () );
            newValue.append ( ",Fixing Calendar : { " );
            if ( !CollectionUtil.isEmpty ( newFixingHolidayCalendars ) )
            {
                for ( HolidayCalendar calendar : newFixingHolidayCalendars )
                {
                    newValue.append ( calendar.getShortName () ).append ( "," );
                }
                newValue.append ( " }" );
            }
            else
            {
                newValue.append ( "- }" );
            }

        }
        if ( !CollectionUtil.isEmpty ( noLagFixingHolidayCalendars ) )
        {
			if ( ObjectUtils.isNull ( fxFixingBuissCalendar ) )
			{
				fxFixingBuissCalendar = new FXBusinessCalendarC ();
			}
            fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
            Iterator<HolidayCalendar> iterator = noLagFixingHolidayCalendars.iterator ();
            fxFixingBuissCalendar.setNoLagHolidayCalendar1 ( AdminTransactionUtil.register ( iterator.next () ) );
            newValue.append ( ",No Lag Fixing Calender 1: " ).append ( fxFixingBuissCalendar.getNoLagHolidayCalendar1 ().getShortName () );
            if ( iterator.hasNext () )
            {
                fxFixingBuissCalendar.setNoLagHolidayCalendar2 ( AdminTransactionUtil.register ( iterator.next () ) );
                newValue.append ( ",No Lag Fixing Calender 2: " ).append ( fxFixingBuissCalendar.getNoLagHolidayCalendar2 ().getShortName () );
            }
        }
        if ( !CollectionUtil.isEmpty ( ignLagFixingHolidayCalendars ) )
        {
			if ( ObjectUtils.isNull ( fxFixingBuissCalendar ) )
			{
				fxFixingBuissCalendar = new FXBusinessCalendarC ();
			}
            fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
            Iterator<HolidayCalendar> iterator = ignLagFixingHolidayCalendars.iterator ();
            fxFixingBuissCalendar.setIgnoreLagHolidayCalendar1 ( AdminTransactionUtil.register ( iterator.next () ) );
            newValue.append ( ",Ignore Lag Fixing Calender 1: " ).append ( fxFixingBuissCalendar.getIgnoreLagHolidayCalendar1 ().getShortName () );
            if ( iterator.hasNext () )
            {
                fxFixingBuissCalendar.setIgnoreLagHolidayCalendar2 ( AdminTransactionUtil.register ( iterator.next () ) );
                newValue.append ( ",Ignore Lag Fixing Calender 2: " ).append ( fxFixingBuissCalendar.getIgnoreLagHolidayCalendar2 ().getShortName () );
            }
        }
        if ( contractMultiplier != null &&
                (fxRateBasis.getBaseCurrency ().getInstrumentClassification ().getShortName ().equals ( InstrumentClassification.INDEX_CLSF ) ||
                        fxRateBasis.getVariableCurrency ().getInstrumentClassification ().getShortName ().equals ( InstrumentClassification.INDEX_CLSF )) )
        {
            newValue.append ( ",Contract Multiplier: " ).append ( contractMultiplier );
            fxRateBasis.setContractMultiplier ( contractMultiplier );
        }
        newValue.append ( ", Deliverable:" ).append ( isDeliverable );
        HashMap<String, String> jmsProperties2 = new HashMap<String, String> ();
        jmsProperties2.put ( "ccyPair", fxRateBasis.getCurrencyPairString () );
        jmsProperties2.put ( "fxRateConvName", fxRateBasis.getFXRateConvention ().getShortName () );
        NotificationUtil.sendNotification ( jmsProperties2, "com.integral.finance.fx.FXRateBasisRemoteTransactionFunctor" );
        auditChanges ( fxRateBasis, null, newValue.toString (), MarketDefinitionConstant.AUDIT_ACTION_CREATERATEBASIS );
    }

    public void updateFXRateBasis ( FXRateBasis fxRateBasis, boolean useDefaultSettlementInfo, int settlementOvrSpotLag,
                                    Collection<HolidayCalendar> newHolidayCalendars, Collection<HolidayCalendar> noLagholidayCalendars, Collection<HolidayCalendar> ignLagholidayCalendars, Collection<HolidayCalendar> rejectTODHolidayCalendar, boolean ignoreTODHolidayCalendars, boolean ignoreTOMHolidayCalendars )
    {
        log.info ( "MDSC.updateFXRateBasis - Updating Settlement Details of FXRateBasis " + fxRateBasis.getCurrencyPairString () );
        fxRateBasis = AdminTransactionUtil.register ( fxRateBasis );
        StringBuilder oldValue = new StringBuilder ( 100 );
        StringBuilder newValue = new StringBuilder ( 100 );
        fxRateBasis = AdminTransactionUtil.register ( fxRateBasis );
        // get the inputs
        FXBusinessCalendar fxBusinessCalendar = fxRateBasis.getFXBusinessCalendar ();
        boolean modified = false;
        if ( useDefaultSettlementInfo )
        {
            FXBusinessCalendar oldBusinessCalendar = fxBusinessCalendar;
            if ( !ObjectUtils.isNull ( oldBusinessCalendar ) )
            {
                oldValue.append ( " Settlement - Spot Lag: " ).append ( fxBusinessCalendar.getLag () );
                newValue.append ( " Settlement - Use Default " );
                Collection<HolidayCalendar> oldHolidayCalendar = fxBusinessCalendar.getHolidayCalendars ();
                if ( !CollectionUtil.isEmpty ( oldHolidayCalendar ) )
                {
                    oldValue.append ( ", Holiday Calendar: " );
                    for ( HolidayCalendar calendar : oldHolidayCalendar )
                    {
                        oldValue.append ( calendar.getShortName () ).append ( "," );
                    }
                    oldValue.append ( " }" );
                }
                HolidayCalendar oldNoLagHolidayCalendar = fxBusinessCalendar.getNoLagHolidayCalendar1 ();
                if ( ObjectUtils.isNotNull ( oldNoLagHolidayCalendar ) )
                {
                    oldValue.append ( ", No Lag Holiday Calender: " ).append ( oldNoLagHolidayCalendar.getShortName () );
                }
            }
            fxRateBasis.setFXBusinessCalendar ( null );
            modified = true;
        }
        else
        {
            if ( ObjectUtils.isNull ( fxBusinessCalendar ) )
            {
                fxBusinessCalendar = FXFactory.newFXBusinessCalendar ();
                fxRateBasis.setFXBusinessCalendar ( fxBusinessCalendar );
            }
            if ( fxBusinessCalendar.getLag () != settlementOvrSpotLag )
            {
                modified = true;
                oldValue.append ( " Spot Lag " ).append ( fxBusinessCalendar.getLag () );
                newValue.append ( " Spot Lag: " ).append ( settlementOvrSpotLag );
            }
            fxBusinessCalendar.setLag ( settlementOvrSpotLag );
            fxBusinessCalendar.setLagType ( BusinessCalendar.LAG_TYPE_BUSINESS );

            boolean oldIgnoreTODHolidayCalendars = fxBusinessCalendar.isIgnoreTODHolidayCalendars ();
            if ( oldIgnoreTODHolidayCalendars != ignoreTODHolidayCalendars )
            {
                modified = true;
                oldValue.append ( " Ignore TOD Holiday Calendars " ).append ( oldIgnoreTODHolidayCalendars );
                newValue.append ( " Ignore TOD Holiday Calendars: " ).append ( ignoreTODHolidayCalendars );
            }
            fxBusinessCalendar.setIgnoreTODHolidayCalendars ( ignoreTODHolidayCalendars );

            boolean oldIgnoreTOMHolidayCalendars = fxBusinessCalendar.isIgnoreTOMHolidayCalendars ();
            if ( oldIgnoreTOMHolidayCalendars != ignoreTOMHolidayCalendars )
            {
                modified = true;
                oldValue.append ( " Ignore TOM Holiday Calendars " ).append ( oldIgnoreTOMHolidayCalendars );
                newValue.append ( " Ignore TOM Holiday Calendars: " ).append ( ignoreTOMHolidayCalendars );
            }
            fxBusinessCalendar.setIgnoreTOMHolidayCalendars ( ignoreTOMHolidayCalendars );

            Collection<HolidayCalendar> oldHolidayCalendar = fxBusinessCalendar.getHolidayCalendars ();
            if ( !CollectionUtil.isSame ( oldHolidayCalendar, newHolidayCalendars ) )
            {
                modified = true;
                if ( modified )
                {
                    oldValue.append ( "," );
                    newValue.append ( "," );
                }
                oldValue.append ( " Holiday Calendar : { " );
                if ( !CollectionUtil.isEmpty ( oldHolidayCalendar ) )
                {
                    for ( HolidayCalendar calendar : oldHolidayCalendar )
                    {
                        oldValue.append ( calendar.getShortName () ).append ( "," );
                    }
                    oldValue.append ( " }" );
                }
                else
                {
                    oldValue.append ( "- }" );
                }

                newValue.append ( " Holiday Calendar : { " );
                if ( !CollectionUtil.isEmpty ( newHolidayCalendars ) )
                {
                    for ( HolidayCalendar calendar : newHolidayCalendars )
                    {
                        newValue.append ( calendar.getShortName () ).append ( "," );
                    }
                    newValue.append ( " }" );
                }
                else
                {
                    newValue.append ( "- }" );
                }
                fxBusinessCalendar.setHolidayCalendars ( AdminTransactionUtil.register ( newHolidayCalendars ) );
            }

            HolidayCalendar oldNoLagHolidayCalendar1 = fxBusinessCalendar.getNoLagHolidayCalendar1 ();
            HolidayCalendar oldNoLagHolidayCalendar2 = fxBusinessCalendar.getNoLagHolidayCalendar2 ();
            HolidayCalendar newNoLagHolidayCalendar1 = null, newNoLagHolidayCalendar2 = null;
            if ( !CollectionUtil.isEmpty ( noLagholidayCalendars ) )
            {
                Iterator<HolidayCalendar> iterator = noLagholidayCalendars.iterator ();
                newNoLagHolidayCalendar1 = iterator.next ();

                if ( iterator.hasNext () )
                {
                    newNoLagHolidayCalendar2 = iterator.next ();
                }
            }
            if ( ObjectUtils.isNotNull ( oldNoLagHolidayCalendar1 ) )
            {
                if ( ObjectUtils.isNull ( newNoLagHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " No Lag Holiday Calender: " ).append ( oldNoLagHolidayCalendar1.getShortName () );
                    newValue.append ( " No Lag Holiday Calender: - " );
                    modified = true;
                }
                else if ( !newNoLagHolidayCalendar1.isSameAs ( oldNoLagHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " No Lag Holiday Calender: " ).append ( oldNoLagHolidayCalendar1.getShortName () );
                    newValue.append ( " No Lag Holiday Calender: " ).append ( newNoLagHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( newNoLagHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " No Lag Holiday Calender: - " );
                    newValue.append ( " No Lag Holiday Calender: " ).append ( newNoLagHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            fxBusinessCalendar.setNoLagHolidayCalendar1 ( AdminTransactionUtil.register ( newNoLagHolidayCalendar1 ) );

            if ( ObjectUtils.isNotNull ( oldNoLagHolidayCalendar2 ) )
            {
                if ( ObjectUtils.isNull ( newNoLagHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " No Lag Holiday Calender: " ).append ( oldNoLagHolidayCalendar2.getShortName () );
                    newValue.append ( " No Lag Holiday Calender: - " );
                    modified = true;
                }
                else if ( !newNoLagHolidayCalendar2.isSameAs ( oldNoLagHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " No Lag Holiday Calender: " ).append ( oldNoLagHolidayCalendar2.getShortName () );
                    newValue.append ( " No Lag Holiday Calender: " ).append ( newNoLagHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( newNoLagHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " No Lag Holiday Calender: - " );
                    newValue.append ( " No Lag Holiday Calender: " ).append ( newNoLagHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            fxBusinessCalendar.setNoLagHolidayCalendar2 ( AdminTransactionUtil.register ( newNoLagHolidayCalendar2 ) );


            HolidayCalendar oldIgnLagHolidayCalendar1 = fxBusinessCalendar.getIgnoreLagHolidayCalendar1 ();
            HolidayCalendar oldIgnLagHolidayCalendar2 = fxBusinessCalendar.getIgnoreLagHolidayCalendar2 ();
            HolidayCalendar newIgnLagHolidayCalendar1 = null, newIgnLagHolidayCalendar2 = null;
            if ( !CollectionUtil.isEmpty ( ignLagholidayCalendars ) )
            {
                Iterator<HolidayCalendar> iterator = ignLagholidayCalendars.iterator ();
                newIgnLagHolidayCalendar1 = iterator.next ();

                if ( iterator.hasNext () )
                {
                    newIgnLagHolidayCalendar2 = iterator.next ();
                }
            }
            if ( ObjectUtils.isNotNull ( oldIgnLagHolidayCalendar1 ) )
            {
                if ( ObjectUtils.isNull ( newIgnLagHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Ignore Lag Holiday Calender: " ).append ( oldIgnLagHolidayCalendar1.getShortName () );
                    newValue.append ( " Ignore Lag Holiday Calender: - " );
                    modified = true;
                }
                else if ( !newIgnLagHolidayCalendar1.isSameAs ( oldIgnLagHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Ignore Lag Holiday Calender: " ).append ( oldIgnLagHolidayCalendar1.getShortName () );
                    newValue.append ( " Ignore Lag Holiday Calender: " ).append ( newIgnLagHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( newIgnLagHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Ignore Lag Holiday Calender: - " );
                    newValue.append ( " Ignore Lag Holiday Calender: " ).append ( newIgnLagHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            fxBusinessCalendar.setIgnoreLagHolidayCalendar1 ( AdminTransactionUtil.register ( newIgnLagHolidayCalendar1 ) );

            if ( ObjectUtils.isNotNull ( oldIgnLagHolidayCalendar2 ) )
            {
                if ( ObjectUtils.isNull ( newIgnLagHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Ignore Lag Holiday Calender: " ).append ( oldIgnLagHolidayCalendar2.getShortName () );
                    newValue.append ( " Ignore Lag Holiday Calender: - " );
                    modified = true;
                }
                else if ( !newIgnLagHolidayCalendar2.isSameAs ( oldIgnLagHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Ignore Lag Holiday Calender: " ).append ( oldIgnLagHolidayCalendar2.getShortName () );
                    newValue.append ( " Ignore Lag Holiday Calender: " ).append ( newIgnLagHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( newIgnLagHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Ignore Lag Holiday Calender: - " );
                    newValue.append ( " Ignore Lag Holiday Calender: " ).append ( newIgnLagHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            fxBusinessCalendar.setIgnoreLagHolidayCalendar2 ( AdminTransactionUtil.register ( newIgnLagHolidayCalendar2 ) );

            HolidayCalendar oldRejectTODHolidayCalendar1 = fxBusinessCalendar.getRejectTODHolidayCalendar1 ();
            HolidayCalendar oldRejectTODHolidayCalendar2 = fxBusinessCalendar.getRejectTODHolidayCalendar2 ();
            HolidayCalendar newRejectTODHolidayCalendar1 = null, newRejectTODHolidayCalendar2 = null;
            if ( !CollectionUtil.isEmpty ( rejectTODHolidayCalendar ) )
            {
                Iterator<HolidayCalendar> iterator = rejectTODHolidayCalendar.iterator ();
                newRejectTODHolidayCalendar1 = iterator.next ();

                if ( iterator.hasNext () )
                {
                    newRejectTODHolidayCalendar2 = iterator.next ();
                }
            }
            if ( ObjectUtils.isNotNull ( oldRejectTODHolidayCalendar1 ) )
            {
                if ( ObjectUtils.isNull ( newRejectTODHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Reject TOD Holiday Calender: " ).append ( oldRejectTODHolidayCalendar1.getShortName () );
                    newValue.append ( " Reject TOD Holiday Calender: - " );
                    modified = true;
                }
                else if ( !newRejectTODHolidayCalendar1.isSameAs ( oldRejectTODHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Reject TOD Holiday Calender: " ).append ( oldRejectTODHolidayCalendar1.getShortName () );
                    newValue.append ( " Reject TOD Holiday Calender: " ).append ( newRejectTODHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( newRejectTODHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Reject TOD Holiday Calender: - " );
                    newValue.append ( " Reject TOD Holiday Calender: " ).append ( newRejectTODHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            fxBusinessCalendar.setRejectTODHolidayCalendar1 ( AdminTransactionUtil.register ( newRejectTODHolidayCalendar1 ) );

            if ( ObjectUtils.isNotNull ( oldRejectTODHolidayCalendar2 ) )
            {
                if ( ObjectUtils.isNull ( newRejectTODHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Reject TOD Holiday Calender: " ).append ( oldRejectTODHolidayCalendar2.getShortName () );
                    newValue.append ( " Reject TOD Holiday Calender: - " );
                    modified = true;
                }
                else if ( !newRejectTODHolidayCalendar2.isSameAs ( oldRejectTODHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Reject TOD Holiday Calender: " ).append ( oldRejectTODHolidayCalendar2.getShortName () );
                    newValue.append ( " Reject TOD Holiday Calender: " ).append ( newRejectTODHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( newRejectTODHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( " Reject TOD Holiday Calender: - " );
                    newValue.append ( " Reject TOD Holiday Calender: " ).append ( newRejectTODHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            fxBusinessCalendar.setRejectTODHolidayCalendar2 ( AdminTransactionUtil.register ( newRejectTODHolidayCalendar2 ) );
        }
        // make sure the settlement date rule set is null
        fxRateBasis.setSettlementDateRuleSet ( null );
        if ( modified )
        {
            HashMap<String, String> jmsProperties2 = new HashMap<String, String> ();
            jmsProperties2.put ( "ccyPair", fxRateBasis.getCurrencyPairString () );
            jmsProperties2.put ( "fxRateConvName", fxRateBasis.getFXRateConvention ().getShortName () );
            NotificationUtil.sendNotification ( jmsProperties2, "com.integral.is.priceprovision.ValueDateChangeNotification" );
            NotificationUtil.sendNotification ( jmsProperties2, "com.integral.finance.fx.FXRateBasisRemoteTransactionFunctor" );
            auditChanges ( fxRateBasis, oldValue.toString (), newValue.toString (), MarketDefinitionConstant.AUDIT_ACTION_UPDATERATEBASISSETTLE );
        }
        // do entity update
    }

    public void updateNDF ( FXRateBasis fxRateBasis, boolean isDeliverable, int fixingCalendarlag,
                            Collection<HolidayCalendar> newFixingHolidayCalendars,
                            Collection<HolidayCalendar> noLagFixingHolidayCalendars, Collection<HolidayCalendar> ignLagFixingHolidayCalendars )
    {
        try
        {
            StringBuilder oldValue = new StringBuilder ( 100 );
            StringBuilder newValue = new StringBuilder ( 100 );
            fxRateBasis = AdminTransactionUtil.register ( fxRateBasis );
            FXBusinessCalendar fxFixingBuissCalendar = fxRateBasis.getFixingBusinessCalendar ();
			if ( ObjectUtils.isNull ( fxFixingBuissCalendar ) )
			{
				fxFixingBuissCalendar = new FXBusinessCalendarC ();
			}
            boolean modified = false;
            int oldFixingLag = fxFixingBuissCalendar.getLag ();
            if ( oldFixingLag != fixingCalendarlag )
            {
                oldValue.append ( "Fixing Calendar Lag: " ).append ( oldFixingLag == -99 ? " " : oldFixingLag );
                newValue.append ( "Fixing Calendar Lag: " ).append ( fixingCalendarlag == -99 ? " " : fixingCalendarlag );
                modified = true;
            }
            fxFixingBuissCalendar.setLagType ( BusinessCalendar.LAG_TYPE_BUSINESS );
            fxFixingBuissCalendar.setLag ( fixingCalendarlag );
            Collection<HolidayCalendar> oldFixingHolidayCalendars = fxFixingBuissCalendar.getHolidayCalendars ();
            if ( !CollectionUtil.isSame ( oldFixingHolidayCalendars, newFixingHolidayCalendars ) )
            {
                if ( modified )
                {
                    oldValue.append ( "," );
                    newValue.append ( "," );
                }
                oldValue.append ( "Fixing Calendar : { " );
                if ( !CollectionUtil.isEmpty ( oldFixingHolidayCalendars ) )
                {
                    for ( HolidayCalendar calendar : oldFixingHolidayCalendars )
                    {
                        oldValue.append ( calendar.getShortName () ).append ( "," );
                    }
                    oldValue.append ( " }" );
                }
                else
                {
                    oldValue.append ( "- }" );
                }

                newValue.append ( " Fixing Calendar : { " );
                if ( !CollectionUtil.isEmpty ( newFixingHolidayCalendars ) )
                {
                    for ( HolidayCalendar calendar : newFixingHolidayCalendars )
                    {
                        newValue.append ( calendar.getShortName () ).append ( "," );
                    }
                    newValue.append ( " }" );
                }
                else
                {
                    newValue.append ( "- }" );
                }

                fxFixingBuissCalendar.setHolidayCalendars ( AdminTransactionUtil.register ( newFixingHolidayCalendars ) );
                fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
                modified = true;
            }

            HolidayCalendar oldNoLagFixingHolidayCalendar1 = fxFixingBuissCalendar.getNoLagHolidayCalendar1 ();
            HolidayCalendar oldNoLagFixingHolidayCalendar2 = fxFixingBuissCalendar.getNoLagHolidayCalendar1 ();
            HolidayCalendar noLagFixingHolidayCalendar1 = null, noLagFixingHolidayCalendar2 = null;

            if ( !CollectionUtil.isEmpty ( noLagFixingHolidayCalendars ) )
            {
                Iterator<HolidayCalendar> iterator = noLagFixingHolidayCalendars.iterator ();
                noLagFixingHolidayCalendar1 = iterator.next ();

                if ( iterator.hasNext () )
                {
                    noLagFixingHolidayCalendar2 = iterator.next ();
                }
            }
            if ( ObjectUtils.isNotNull ( oldNoLagFixingHolidayCalendar1 ) )
            {
                if ( ObjectUtils.isNull ( noLagFixingHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "No Lag Fixing Calender: " ).append ( oldNoLagFixingHolidayCalendar1.getShortName () );
                    newValue.append ( "No Lag Fixing Calender: - " );
                    modified = true;
                }
                else if ( !noLagFixingHolidayCalendar1.isSameAs ( oldNoLagFixingHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "No Lag Fixing Calender: " ).append ( oldNoLagFixingHolidayCalendar1.getShortName () );
                    newValue.append ( "No Lag Fixing Calender: " ).append ( noLagFixingHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( noLagFixingHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "No Lag Fixing Calender: - " );
                    newValue.append ( "No Lag Fixing Calender: " ).append ( noLagFixingHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            if ( ObjectUtils.isNotNull ( noLagFixingHolidayCalendar1 ) )
            {
                fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
                fxFixingBuissCalendar.setNoLagHolidayCalendar1 ( AdminTransactionUtil.register ( noLagFixingHolidayCalendar1 ) );
            }

            if ( ObjectUtils.isNotNull ( oldNoLagFixingHolidayCalendar2 ) )
            {
                if ( ObjectUtils.isNull ( noLagFixingHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "No Lag Fixing Calender: " ).append ( oldNoLagFixingHolidayCalendar2.getShortName () );
                    newValue.append ( "No Lag Fixing Calender: - " );
                    modified = true;
                }
                else if ( !noLagFixingHolidayCalendar2.isSameAs ( oldNoLagFixingHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "No Lag Fixing Calender: " ).append ( oldNoLagFixingHolidayCalendar2.getShortName () );
                    newValue.append ( "No Lag Fixing Calender: " ).append ( noLagFixingHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( noLagFixingHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "No Lag Fixing Calender: - " );
                    newValue.append ( "No Lag Fixing Calender: " ).append ( noLagFixingHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            fxFixingBuissCalendar.setNoLagHolidayCalendar2 ( AdminTransactionUtil.register ( noLagFixingHolidayCalendar2 ) );


            HolidayCalendar oldIgnLagFixingHolidayCalendar1 = fxFixingBuissCalendar.getIgnoreLagHolidayCalendar1 ();
            HolidayCalendar oldIgnLagFixingHolidayCalendar2 = fxFixingBuissCalendar.getIgnoreLagHolidayCalendar2 ();
            HolidayCalendar ignLagFixingHolidayCalendar1 = null, ignLagFixingHolidayCalendar2 = null;

            if ( !CollectionUtil.isEmpty ( ignLagFixingHolidayCalendars ) )
            {
                Iterator<HolidayCalendar> iterator = ignLagFixingHolidayCalendars.iterator ();
                ignLagFixingHolidayCalendar1 = iterator.next ();

                if ( iterator.hasNext () )
                {
                    ignLagFixingHolidayCalendar2 = iterator.next ();
                }
            }
            if ( ObjectUtils.isNotNull ( oldIgnLagFixingHolidayCalendar1 ) )
            {
                if ( ObjectUtils.isNull ( ignLagFixingHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "Ignore Lag Fixing Calender: " ).append ( oldIgnLagFixingHolidayCalendar1.getShortName () );
                    newValue.append ( "Ignore Lag Fixing Calender: - " );
                    modified = true;
                }
                else if ( !ignLagFixingHolidayCalendar1.isSameAs ( oldIgnLagFixingHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "Ignore Lag Fixing Calender: " ).append ( oldIgnLagFixingHolidayCalendar1.getShortName () );
                    newValue.append ( "Ignore Lag Fixing Calender: " ).append ( ignLagFixingHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( ignLagFixingHolidayCalendar1 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "Ignore Lag Fixing Calender: - " );
                    newValue.append ( "Ignore Lag Fixing Calender: " ).append ( ignLagFixingHolidayCalendar1.getShortName () );
                    modified = true;
                }
            }
            if ( ObjectUtils.isNotNull ( ignLagFixingHolidayCalendar1 ) )
            {
                fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
                fxFixingBuissCalendar.setIgnoreLagHolidayCalendar1 ( AdminTransactionUtil.register ( ignLagFixingHolidayCalendar1 ) );
            }

            if ( ObjectUtils.isNotNull ( oldIgnLagFixingHolidayCalendar2 ) )
            {
                if ( ObjectUtils.isNull ( ignLagFixingHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "Ignore Lag Fixing Calender: " ).append ( oldIgnLagFixingHolidayCalendar2.getShortName () );
                    newValue.append ( "Ignore Lag Fixing Calender: - " );
                    modified = true;
                }
                else if ( !ignLagFixingHolidayCalendar2.isSameAs ( oldIgnLagFixingHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "Ignore Lag Fixing Calender: " ).append ( ignLagFixingHolidayCalendar2.getShortName () );
                    newValue.append ( "Ignore Lag Fixing Calender: " ).append ( ignLagFixingHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            else
            {
                if ( ObjectUtils.isNotNull ( ignLagFixingHolidayCalendar2 ) )
                {
                    if ( modified )
                    {
                        oldValue.append ( "," );
                        newValue.append ( "," );
                    }
                    oldValue.append ( "Ignore Lag Fixing Calender: - " );
                    newValue.append ( "Ignore Lag Fixing Calender: " ).append ( ignLagFixingHolidayCalendar2.getShortName () );
                    modified = true;
                }
            }
            fxFixingBuissCalendar.setIgnoreLagHolidayCalendar2 ( AdminTransactionUtil.register ( ignLagFixingHolidayCalendar2 ) );


            if ( CollectionUtil.isEmpty ( noLagFixingHolidayCalendars ) )
            {
                if ( CollectionUtil.isEmpty ( newFixingHolidayCalendars ) )
                {
                    fxRateBasis.setFixingBusinessCalendar ( null );
                }
                else
                {
                    fxFixingBuissCalendar.setNoLagHolidayCalendar1 ( null );
                    fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
                }
            }

            if ( CollectionUtil.isEmpty ( ignLagFixingHolidayCalendars ) )
            {
                if ( CollectionUtil.isEmpty ( newFixingHolidayCalendars ) )
                {
                    fxRateBasis.setFixingBusinessCalendar ( null );
                }
                else
                {
                    fxFixingBuissCalendar.setIgnoreLagHolidayCalendar1 ( null );
                    fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
                }
            }

            if ( fxRateBasis.isNDF () != !isDeliverable )
            {
                if ( modified )
                {
                    oldValue.append ( "," );
                    newValue.append ( "," );
                }
                oldValue.append ( "Deliverable:" ).append ( !fxRateBasis.isNDF () );
                newValue.append ( "Deliverable:" ).append ( isDeliverable );
                fxRateBasis.setNDF ( !isDeliverable );
                modified = true;
            }
            if ( modified )
            {
                auditChanges ( fxRateBasis, oldValue.toString (), newValue.toString (), MarketDefinitionConstant.AUDIT_ACTION_UPDATEFXRATEBASISNDF );
            }
        }
        catch ( Exception e )
        {
            log.error ( "MDSC.updateNDF - Error Occured while Updating Fixing Calendar - FXRateBasis  " + fxRateBasis.getCurrencyPairString (), e );
            throw new IdcRuntimeException ( e );
        }
    }

    public void updateParentQuoteConvention ( FXRateConvention fxRateConvention, String parentQuoteConvention )
    {
        if ( parentQuoteConvention != null && !parentQuoteConvention.trim ().isEmpty () )
        {
            fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
            FXRateConvention oldParent = fxRateConvention.getParent ();
            FXRateConvention pConvention = MarketDefinitionUtil.getQuoteConvention ( parentQuoteConvention );
            pConvention = AdminTransactionUtil.register ( pConvention );
            fxRateConvention.setParent ( pConvention );
            if ( !parentQuoteConvention.equals ( oldParent.getShortName () ) )
            {
                AuditEvent parentAudit = initializeAudit ( fxRateConvention, MarketDefinitionConstant.AUDIT_ACTION_UPDATEPARENTQUOTECONV );
                parentAudit.setStringArg2 ( parentQuoteConvention );
                parentAudit.setStringArg3 ( oldParent == null ? "None" : oldParent.getShortName () );
                persistToMongo ( parentAudit );
            }
        }
    }

    public void updateFXRateBasis ( FXRateBasis fxRateBasis, String currencyPairName, Currency baseCurrency, Currency variableCurrency, boolean allowInverse,
                                    double baseCurrencyFactor, double variableCurrencyFactor, int spotPrecision, int pointsPrecision, int forwardPoint,
                                    double pipsFactor, int invSpotPrecision, int invPointsPrecision, int invForwardPoint, double invPipsFactor,
                                    Currency cross, Double contractMultiplier, boolean marketConvention, boolean deliverableNDF )
    {
        fxRateBasis = AdminTransactionUtil.register ( fxRateBasis );
        StringBuilder oldValue = new StringBuilder ( 100 );
        StringBuilder newValue = new StringBuilder ( 100 );
        boolean isModified = false;
        if ( !StringUtils.isEqual ( fxRateBasis.getName (), currencyPairName ) )
        {
            oldValue.append ( "Name: " ).append ( fxRateBasis.getName () );
            newValue.append ( "Name: " ).append ( currencyPairName );
            fxRateBasis.setName ( currencyPairName );
            isModified = true;
        }
        if ( ObjectUtils.isNotNull ( cross ) )
        {
            if ( ObjectUtils.isNull ( fxRateBasis.getCrossCurrency () ) )
            {
                oldValue.append ( ",Cross Currency: - " );
                newValue.append ( ",Cross Currency: " ).append ( cross.getName () );
                isModified = true;
            }
            else if ( !fxRateBasis.getCrossCurrency ().isSameAs ( cross ) )
            {
                oldValue.append ( ",Cross Currency: " ).append ( fxRateBasis.getCrossCurrency ().getName () );
                newValue.append ( ",Cross Currency: " ).append ( cross.getName () );
                isModified = true;
            }
        }
        else
        {
            if ( !ObjectUtils.isNull ( fxRateBasis.getCrossCurrency () ) )
            {
                oldValue.append ( ",Cross Currency: " ).append ( fxRateBasis.getCrossCurrency ().getName () );
                newValue.append ( ",Cross Currency: - " );
                isModified = true;
            }
        }
        fxRateBasis.setCrossCurrency ( cross );
        if ( fxRateBasis.getBaseCurrencyFactor () != baseCurrencyFactor )
        {
            oldValue.append ( ",Base Currency Factor: " ).append ( fxRateBasis.getBaseCurrencyFactor () );
            newValue.append ( ",Base Currency Factor: " ).append ( baseCurrencyFactor );
            fxRateBasis.setBaseCurrencyFactor ( baseCurrencyFactor );
            isModified = true;
        }
        if ( fxRateBasis.getVariableCurrencyFactor () != variableCurrencyFactor )
        {
            oldValue.append ( ",Variable Currency Factor: " ).append ( fxRateBasis.getVariableCurrencyFactor () );
            newValue.append ( ",Variable Currency Factor: " ).append ( variableCurrencyFactor );
            fxRateBasis.setVariableCurrencyFactor ( variableCurrencyFactor );
            isModified = true;
        }
        fxRateBasis.setVariableCurrency ( variableCurrency );
        fxRateBasis.setBaseCurrency ( baseCurrency );
        if ( fxRateBasis.isAllowInverse () != allowInverse )
        {
            oldValue.append ( ",Allow Inverse: " ).append ( fxRateBasis.isAllowInverse () );
            newValue.append ( ",Allow Inverse: " ).append ( allowInverse );
            fxRateBasis.setAllowInverse ( allowInverse );
            isModified = true;
        }
		if ( fxRateBasis.isMarketConvention () != marketConvention )
		{
			oldValue.append ( ",Market Convention: " ).append ( fxRateBasis.isMarketConvention () );
			newValue.append ( ",Market Convention: " ).append ( marketConvention );
			fxRateBasis.setMarketConvention ( marketConvention );
			isModified = true;
		}
        if ( fxRateBasis.getSpotPrecision () != spotPrecision )
        {
            oldValue.append ( ",Spot Precision: " ).append ( fxRateBasis.getSpotPrecision () );
            newValue.append ( ",Spot Precision: " ).append ( spotPrecision );
            fxRateBasis.setSpotPrecision ( spotPrecision );
            isModified = true;
        }
        if ( fxRateBasis.getInverseSpotPrecision () != invSpotPrecision )
        {
            oldValue.append ( ",Inverse Spot Precision: " ).append ( fxRateBasis.getSpotPrecision () );
            newValue.append ( ",Inverse Spot Precision: " ).append ( invSpotPrecision );
            fxRateBasis.setInverseSpotPrecision ( invSpotPrecision );
            isModified = true;
        }
        //Display Offet
        if ( fxRateBasis.getSpotPointsPrecision () != pointsPrecision )
        {
            oldValue.append ( ",Display Offset: " ).append ( fxRateBasis.getSpotPointsPrecision () );
            newValue.append ( ",Display Offset: " ).append ( pointsPrecision );
            fxRateBasis.setSpotPointsPrecision ( pointsPrecision );
            isModified = true;
        }
        if ( fxRateBasis.getInverseSpotPointsPrecision () != invPointsPrecision )
        {
            oldValue.append ( ",Inverse Display Offset: " ).append ( fxRateBasis.getInverseSpotPointsPrecision () );
            newValue.append ( ",Inverse Display Offset: " ).append ( invPointsPrecision );
            fxRateBasis.setInverseSpotPointsPrecision ( invPointsPrecision );
            isModified = true;
        }
        if ( fxRateBasis.getForwardPointsPrecision () != forwardPoint )
        {
            oldValue.append ( ",Forward Points Precision: " ).append ( fxRateBasis.getForwardPointsPrecision () );
            newValue.append ( ",Forward Points Precision: " ).append ( forwardPoint );
            fxRateBasis.setForwardPointsPrecision ( forwardPoint );
            isModified = true;
        }
        if ( fxRateBasis.getInverseForwardPointsPrecision () != invForwardPoint )
        {
            oldValue.append ( ",Inverse Forward Points Precision: " ).append ( fxRateBasis.getInverseForwardPointsPrecision () );
            newValue.append ( ",Inverse Forward Points Precision: " ).append ( invForwardPoint );
            fxRateBasis.setInverseForwardPointsPrecision ( invForwardPoint );
            isModified = true;
        }
        if ( fxRateBasis.getPipsFactor () != pipsFactor )
        {
            oldValue.append ( ",Pips Factor: " ).append ( fxRateBasis.getPipsFactor () );
            newValue.append ( ",Pips Factor: " ).append ( pipsFactor );
            fxRateBasis.setPipsFactor ( pipsFactor );
            isModified = true;
        }
        if ( fxRateBasis.getInversePipsFactor () != invPipsFactor )
        {
            oldValue.append ( ",Inverse Pips Factor: " ).append ( fxRateBasis.getInversePipsFactor () );
            newValue.append ( ",Inverse Pips Factor: " ).append ( invPipsFactor );
            fxRateBasis.setInversePipsFactor ( invPipsFactor );
            isModified = true;
        }
        if ( contractMultiplier != null && !contractMultiplier.equals ( fxRateBasis.getContractMultiplier () ) &&
                (fxRateBasis.getBaseCurrency ().getInstrumentClassification ().getShortName ().equals ( InstrumentClassification.INDEX_CLSF ) ||
                        fxRateBasis.getVariableCurrency ().getInstrumentClassification ().getShortName ().equals ( InstrumentClassification.INDEX_CLSF )) )
        {
            oldValue.append ( ",Contract Multiplier: " ).append ( fxRateBasis.getContractMultiplier () == null ? "" : fxRateBasis.getContractMultiplier () );
            newValue.append ( ",Contract Multiplier: " ).append ( contractMultiplier );
            fxRateBasis.setContractMultiplier ( contractMultiplier );
            isModified = true;
        }
        if ( fxRateBasis.isDeliverableNDF () != deliverableNDF )
        {
            oldValue.append ( ",Support both as Deliverable and Non Deliverable: " ).append ( fxRateBasis.isDeliverableNDF () );
            newValue.append ( ",Support both as Deliverable and Non Deliverable: " ).append ( deliverableNDF );
            fxRateBasis.setDeliverableNDF ( deliverableNDF );
            isModified = true;
        }

        if ( isModified )
        {
            auditChanges ( fxRateBasis, oldValue.toString (), newValue.toString (), MarketDefinitionConstant.AUDIT_ACTION_UPDATERATEBASIS );
        }
    }

    @Override
    public void createCurrency ( String isocode, String name, String alias, Integer dSort, char prefix,
                                 InstrumentClassification instrClsf, InstrumentSubClassification subClsf, Double tick, Integer roundingType,
                                 Collection<HolidayCalendar> newHolidayCalendars, String rollConvention, Integer lag, Integer lagtype,
                                 Currency currencyFixedTo, Double dConversionRate, boolean isdeliverable, String settlementTenor,
                                 String settlementType, String underlyingInstrument )
    {
        log.info ( "MDSC.createCurrency - Creating Currency " + isocode + " and adding the currency to CurrencyGroup - All" );
        Currency currency = new CurrencyC ();
        currency = AdminTransactionUtil.register ( currency );
        currency.setISOName ( isocode );
        currency.setShortName ( isocode );
        currency.setNamespace ( AdminTransactionUtil.register ( GenericAdminUtil.getMainNamespace () ) );
        currency.setLongName ( name );
        alias = StringUtils.isNull ( alias ) ? "" : alias;
        currency.setAlias ( alias );
        currency.setSortOrder ( dSort );
        currency.setPrefixCharacter ( prefix );
        instrClsf = AdminTransactionUtil.register ( instrClsf );
        currency.setInstrumentClassification ( instrClsf );
        if ( subClsf != null && instrClsf.getDescendents ().contains ( subClsf ) )
        {
            subClsf = AdminTransactionUtil.register ( subClsf );
            currency.setInstrumentSubClassification ( subClsf );
        }
        currency.setTickValue ( tick );
        currency.setRoundingType ( roundingType );
        if ( settlementTenor != null && !"".equals ( settlementTenor.trim () ) )
        {
            Tenor tenor = null;
            try
            {
                tenor = new Tenor ( settlementTenor );
            }
            catch ( Exception e )
            {

            }
            if ( tenor != null )
            {
                currency.setSettlementTenor ( tenor );
            }
        }
        if ( settlementType != null && !"".equals ( settlementType.trim () ) )
        {
            settlementType = settlementType.toUpperCase ();
            if ( !SETTLEMENTTYPE_NONE.equals ( settlementType ) )
            {
                Currency.SettlementType settlementTypeObj = null;
                try
                {
                    settlementTypeObj = Currency.SettlementType.valueOf ( settlementType );
                }
                catch ( Exception e )
                {
                    settlementTypeObj = null;
                }
                if ( settlementTypeObj != null )
                {
                    currency.setSettlementType ( settlementTypeObj );
                }
            }
        }
        if ( underlyingInstrument != null && !"".equals ( underlyingInstrument.trim () ) )
        {
            Currency underlyingCurrency = CurrencyFactory.getSpotCurrency ( underlyingInstrument );
            underlyingCurrency = AdminTransactionUtil.register ( underlyingCurrency );
            if ( underlyingCurrency != null )
            {
                currency.setUnderlyingCurrency ( underlyingCurrency );
            }
        }
        BusinessCalendar bsnsCal = currency.getBusinessCalendar ();
        if ( ObjectUtils.isNull ( bsnsCal ) )
        {
            bsnsCal = BusinessCalendarFactory.newBusinessCalendar ();
        }
        bsnsCal.setHolidayCalendars ( AdminTransactionUtil.register ( newHolidayCalendars ) );
        currency.setBusinessCalendar ( bsnsCal );
        RollConvention convention = MarketDefinitionUtil.getRollConvention ( rollConvention );
        bsnsCal.setRollConvention ( AdminTransactionUtil.register ( convention ) );
        bsnsCal.setLag ( lag );
        bsnsCal.setLagType ( lagtype );
        currency.setCurrencyFixedTo ( AdminTransactionUtil.register ( currencyFixedTo ) );
        currency.setConversionRate ( dConversionRate );
        currency.setDeliverable ( isdeliverable );
        CurrencyGroup all = MarketDefinitionUtil.getCurrencyGroup ( "All" );
        if ( !CollectionUtil.contains ( all.getIncludedCurrencies (), currency ) )
        {
            all = AdminTransactionUtil.register ( all );
            all.addIncluded ( currency );
        }
        HashMap propertyMap = new HashMap ();
        propertyMap.put ( CURRENCY_UPDATE_FUNCTORKEY_CCY_GUID, currency.getGUID () );
        NotificationUtil.notifyFunctors ( propertyMap, CURRENCY_UPDATE_FUNCTORKEY );
        auditCurrencyCreate ( currency );
    }

    public void createCurrency ( String isocode, String name, String alias, Integer dSort, char prefix, InstrumentClassification instrClsf, InstrumentSubClassification subClsf,
                                 Double tick, Integer roundingType, Collection<HolidayCalendar> newHolidayCalendars, String rollConvention,
                                 Integer lag, Integer lagtype, Currency currencyFixedTo, Double dConversionRate, boolean isdeliverable )
    {
        createCurrency ( isocode, name, alias, dSort, prefix, instrClsf, subClsf, tick, roundingType, newHolidayCalendars, rollConvention,
                lag, lagtype, currencyFixedTo, dConversionRate, isdeliverable, null, null, null );
    }

    public void updateCurrency ( Currency currency, String name, String alias, Integer dSort, char prefix, InstrumentClassification instrClsf, InstrumentSubClassification subClsf,
                                 Double tick, Integer roundingType, Collection<HolidayCalendar> newHolidayCalendars, String rollConvention,
                                 Integer lag, Integer lagtype, Currency currencyFixedTo, Double dConversionRate, boolean isdeliverable )
    {
        currency = AdminTransactionUtil.register ( currency );
        StringBuilder oldValue = new StringBuilder ( 50 );
        StringBuilder newValue = new StringBuilder ( 50 );
        boolean isModified = false;
        if ( !StringUtils.isEqual ( currency.getLongName (), name ) )
        {
            oldValue.append ( "LongName: " ).append ( StringUtils.isNull ( currency.getLongName () ) ? "" : currency.getLongName () );
            newValue.append ( "LongName: " ).append ( StringUtils.isNull ( name ) ? "" : name );
            currency.setLongName ( name );
            isModified = true;
        }
        alias = StringUtils.isNull ( alias ) ? "" : alias;
        if ( !StringUtils.isEqual ( currency.getAlias (), alias ) )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Alias: " ).append ( StringUtils.isNull ( currency.getAlias () ) ? "" : currency.getAlias () );
            newValue.append ( "Alias: " ).append ( StringUtils.isNull ( alias ) ? "" : alias );
            currency.setAlias ( alias );
        }
        if ( prefix != currency.getPrefixCharacter () )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Prefix: " ).append ( currency.getPrefixCharacter () );
            newValue.append ( "Prefix: " ).append ( prefix );
            currency.setPrefixCharacter ( prefix );
        }
        if ( ObjectUtils.isNotNull ( dSort ) && dSort != currency.getSortOrder () )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Sort Order: " ).append ( currency.getSortOrder () );
            newValue.append ( "Sort Order: " ).append ( dSort.intValue () );
            currency.setSortOrder ( dSort );
        }

        if ( ObjectUtils.isNotNull ( currency.getInstrumentClassification () ) && !currency.getInstrumentClassification ().isSameAs ( instrClsf ) )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Type : " ).append ( currency.getInstrumentClassification ().getShortName () );
            newValue.append ( "Type: " ).append ( instrClsf.getShortName () );
            instrClsf = AdminTransactionUtil.register ( instrClsf );
            currency.setInstrumentClassification ( instrClsf );
        }
        if ( ObjectUtils.isNull ( subClsf ) && currency.getInstrumentSubClassification () != null )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "SubType: " ).append ( currency.getInstrumentSubClassification ().getShortName () );
            newValue.append ( "SubType: " ).append ( "null" );
            currency.setInstrumentSubClassification ( subClsf );
        }
        else if ( ObjectUtils.isNotNull ( subClsf ) && instrClsf.getDescendents ().contains ( subClsf )
                && (currency.getInstrumentSubClassification () == null || !currency.getInstrumentSubClassification ().isSameAs ( subClsf )) )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "SubType: " ).append ( currency.getInstrumentSubClassification () == null ? "null" : currency.getInstrumentSubClassification ().getShortName () );
            newValue.append ( "SubType: " ).append ( subClsf.getShortName () );
            subClsf = AdminTransactionUtil.register ( subClsf );
            currency.setInstrumentSubClassification ( subClsf );
        }
        if ( ObjectUtils.isNotNull ( tick ) && tick != currency.getTickValue () )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Tick : " ).append ( currency.getTickValue () );
            newValue.append ( "Tick: " ).append ( tick.doubleValue () );
            currency.setTickValue ( tick );
        }
        if ( ObjectUtils.isNotNull ( roundingType ) && roundingType != currency.getRoundingType () )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Notional Display: " ).append ( currency.getRoundingType () == BigDecimal.ROUND_DOWN ? "Truncate" : "Round" );
            newValue.append ( "Notional Display: " ).append ( roundingType == BigDecimal.ROUND_DOWN ? "Truncate" : "Round" );
            currency.setRoundingType ( roundingType );
        }

        BusinessCalendar bsnsCal = currency.getBusinessCalendar ();
        if ( ObjectUtils.isNull ( bsnsCal ) )
        {
            bsnsCal = BusinessCalendarFactory.newBusinessCalendar ();
        }
        Collection<HolidayCalendar> oldCal = bsnsCal.getHolidayCalendars ();
        if ( !CollectionUtil.equals ( oldCal, newHolidayCalendars ) )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            if ( !CollectionUtil.isEmpty ( oldCal ) )
            {
                StringBuilder holCalStringBuffer = new StringBuilder ();
                for ( HolidayCalendar hol : oldCal )
                {
                    holCalStringBuffer.append ( hol.getShortName () + "," );
                }
                holCalStringBuffer.setLength ( holCalStringBuffer.length () - 1 );
                oldValue.append ( "Holiday Calendar: {" ).append ( holCalStringBuffer ).append ( "}" );
            }
            if ( !CollectionUtil.isEmpty ( newHolidayCalendars ) )
            {
                StringBuilder holCalStringBuffer = new StringBuilder ();
                for ( HolidayCalendar hol : newHolidayCalendars )
                {
                    holCalStringBuffer.append ( hol.getShortName () + "," );
                }
                holCalStringBuffer.setLength ( holCalStringBuffer.length () - 1 );
                newValue.append ( "Holiday Calendar: {" ).append ( holCalStringBuffer ).append ( "}" );
            }
            bsnsCal.setHolidayCalendars ( AdminTransactionUtil.register ( newHolidayCalendars ) );
            isModified = true;
        }
        currency.setBusinessCalendar ( bsnsCal );
        RollConvention convention = MarketDefinitionUtil.getRollConvention ( rollConvention );
        if ( ObjectUtils.isNotNull ( convention ) )
        {
            if ( ObjectUtils.isNotNull ( bsnsCal.getRollConvention () ) && !bsnsCal.getRollConvention ().isSameAs ( convention ) )
            {
                if ( isModified )
                {
                    oldValue.append ( "," );
                    newValue.append ( "," );
                }
                isModified = true;
                oldValue.append ( "Roll Convention: " ).append ( bsnsCal.getRollConvention ().getShortName () );
                newValue.append ( "Roll Convention: " ).append ( rollConvention );
                bsnsCal.setRollConvention ( AdminTransactionUtil.register ( convention ) );
            }
            else if ( ObjectUtils.isNull ( bsnsCal.getRollConvention () ) )
            {
                if ( isModified )
                {
                    oldValue.append ( "," );
                    newValue.append ( "," );
                }
                isModified = true;
                oldValue.append ( "Roll Convention: " ).append ( " " );
                newValue.append ( "Roll Convention: " ).append ( rollConvention );
                bsnsCal.setRollConvention ( AdminTransactionUtil.register ( convention ) );
            }
        }
        if ( ObjectUtils.isNotNull ( lag ) && lag != bsnsCal.getLag () )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Lag: " ).append ( currency.getBusinessCalendar ().getLag () );
            newValue.append ( "Lag: " ).append ( lag );
            bsnsCal.setLag ( lag );
        }
        if ( ObjectUtils.isNotNull ( lagtype ) && lagtype != bsnsCal.getLagType () )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Lag Type: " ).append ( bsnsCal.getLagType () == BusinessCalendar.LAG_TYPE_BUSINESS ? "Business" : "Calendar" );
            newValue.append ( "Lag Type: " ).append ( lagtype == BusinessCalendar.LAG_TYPE_BUSINESS ? "Business" : "Calendar" );
            bsnsCal.setLagType ( lagtype );
        }
        boolean isCCyModified = false;
        if ( ObjectUtils.isNotNull ( currencyFixedTo ) )
        {
            if ( ObjectUtils.isNull ( currency.getCurrencyFixedTo () ) )
            {
                isCCyModified = true;
            }
            else if ( !currencyFixedTo.isSameAs ( currency.getCurrencyFixedTo () ) )
            {
                isCCyModified = true;
            }
        }
        else
        {
            if ( ObjectUtils.isNotNull ( currency.getCurrencyFixedTo () ) )
            {
                isCCyModified = true;
            }
        }
        if ( isCCyModified )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "FixedTo: " ).append ( ObjectUtils.isNotNull ( currency.getCurrencyFixedTo () ) ? currency.getCurrencyFixedTo ().getShortName () : "" );
            newValue.append ( "FixedTo: " ).append ( ObjectUtils.isNotNull ( currencyFixedTo ) ? currencyFixedTo.getShortName () : " " );
            currency.setCurrencyFixedTo ( AdminTransactionUtil.register ( currencyFixedTo ) );
        }
        if ( ObjectUtils.isNotNull ( dConversionRate ) && dConversionRate != currency.getConversionRate () )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Conversion Rate: " ).append ( currency.getConversionRate () );
            newValue.append ( "Conversion Rate: " ).append ( dConversionRate.doubleValue () );
            currency.setConversionRate ( dConversionRate );
        }

        if ( isdeliverable != currency.isDeliverable () )
        {
            if ( isModified )
            {
                oldValue.append ( "," );
                newValue.append ( "," );
            }
            isModified = true;
            oldValue.append ( "Deliverable: " ).append ( currency.isDeliverable () );
            newValue.append ( "Deliverable: " ).append ( isdeliverable );
            currency.setDeliverable ( isdeliverable );
        }
        CurrencyGroup all = MarketDefinitionUtil.getCurrencyGroup ( "All" );
        if ( !CollectionUtil.contains ( all.getIncludedCurrencies (), currency ) )
        {
            all = AdminTransactionUtil.register ( all );
            all.addIncluded ( currency );
        }
        if ( isModified )
        {
            auditCurrency ( currency, oldValue.toString (), newValue.toString (), MarketDefinitionConstant.AUDIT_ACTION_UPDATECURRENCY );
            HashMap propertyMap = new HashMap ();
            propertyMap.put ( CURRENCY_UPDATE_FUNCTORKEY_CCY_GUID, currency.getGUID () );
            propertyMap.put ( FXRateBasisRemoteTransactionFunctor.CCY, currency.getShortName () );
            NotificationUtil.notifyFunctors ( propertyMap, CURRENCY_UPDATE_FUNCTORKEY );
        }
    }

    public FXRateConvention createQuoteConvention ( String shortName, String longName )
    {
        FXRateConvention convention = new FXRateConventionC ();
        convention = AdminTransactionUtil.register ( convention );
        convention.setShortName ( shortName );
        convention.setLongName ( longName );

        RuleSet settlementRuleSet = ( RuleSet ) GenericAdminUtil.getNamedEntityByShortName ( com.integral.rule.RuleSetC.class, "FXSettlementDateRuleSet" );
        settlementRuleSet = ( RuleSet ) settlementRuleSet.getRegisteredObject ();
        convention.setSettlementDateRuleSet ( settlementRuleSet );
        User loginUser = GenericAdminUtil.getContextUser ();
        loginUser = AdminTransactionUtil.register ( loginUser );
        AuditEventC audit = new AuditEventC ();
        audit.setUser ( loginUser );
        audit.setComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        audit.setAction ( MarketDefinitionConstant.AUDIT_ACTION_CREATEQUOTECONV );
        convention.setNamespace ( AdminTransactionUtil.register ( GenericAdminUtil.getMainNamespace () ) );
        if ( ObjectUtils.isNotNull ( convention ) )
        {
            audit.setEntity1 ( convention );
            audit.setStringArg1 ( shortName );
            audit.setStringArg2 ( longName );
            audit.setNamespace ( GenericAdminUtil.getMainNamespace () );
        }
        persistToMongo ( audit );
        return convention;
    }


    /* (non-Javadoc)
     * @see com.integral.admin.services.marketdefinition.MarketDefinitionService#removeCurrencyPairFromQuoteConvention(com.integral.finance.fx.FXRateConvention, com.integral.finance.fx.FXRateBasis)
     */
    public void removeCurrencyPairFromQuoteConvention ( FXRateConvention fxRateConvention, FXRateBasis fxRateBasis1 )
    {
        fxRateConvention = AdminTransactionUtil.register ( fxRateConvention );
        String currencyPair = fxRateBasis1.getCurrencyPairString ();
        log.info ( "MDSC.removeCurrencyPairFromQuoteConvention - Deleting Currency Pair " + fxRateBasis1.getCurrencyPairString ()
                + " from Quote Convention " + fxRateConvention.getShortName () );
        Collection<FXRateBasis> fxRateBasisCol = new ArrayList<FXRateBasis> ( fxRateConvention.getFXRateBasis () );
        for ( Iterator<FXRateBasis> fxRTBItr = fxRateBasisCol.iterator (); fxRTBItr.hasNext (); )
        {
            if ( fxRTBItr.next ().getObjectId () == fxRateBasis1.getObjectId () )
            {
                fxRTBItr.remove ();
            }
        }
        fxRateConvention.setFXRateBasis ( fxRateBasisCol );
        AuditEvent audit = initializeAudit ( fxRateConvention, MarketDefinitionConstant.REMOVE_CURRENCYPAIR );
        audit.setStringArg2 ( currencyPair );
        try
        {
            EJBServiceUtil.deleteObject ( fxRateBasis1 );
        }
        catch ( Exception exception )
        {
            log.error ( "MDSC.removeCurrencyPairFromQuoteConvention - Failed to remove Currency Pair "
                    + currencyPair + " from Quote Convention " + fxRateConvention.getShortName () );
        }
        persistToMongo ( audit );
    }


    @Override
    public void copyQuoteConvention ( String referenceQuoteConvention, String newQuoteConvention )
    {

        FXRateConvention referenceQt = MarketDefinitionUtil.getQuoteConvention ( referenceQuoteConvention );
        Namespace ns = referenceQt.getNamespace ();
        ns = ( Namespace ) ns.getRegisteredObject ();
        FXRateConvention fxRateConvention = FXFactory.newFXRateConvention ();
        fxRateConvention = ( FXRateConvention ) fxRateConvention.getRegisteredObject ();
        fxRateConvention.setName ( newQuoteConvention );
        fxRateConvention.setNamespace ( ns );
        RuleSet settlementRuleSet = ( RuleSet ) GenericAdminUtil.getNamedEntityByShortName ( com.integral.rule.RuleSetC.class, "FXSettlementDateRuleSet" );
        settlementRuleSet = ( RuleSet ) settlementRuleSet.getRegisteredObject ();
        fxRateConvention.setSettlementDateRuleSet ( settlementRuleSet );
        FXRateConvention parentRateConvention = referenceQt.getParent ();
        if ( parentRateConvention != null )
        {
            parentRateConvention = ( FXRateConvention ) parentRateConvention.getRegisteredObject ();
            fxRateConvention.setParent ( parentRateConvention );
        }

        Collection<FXRateBasis> colBasis = referenceQt.getFXRateBasis ();
        for ( FXRateBasis rateBasis : colBasis )
        {
            FXRateBasis fxRateBasis = AdminTransactionUtil.register ( new FXRateBasisC () );
            fxRateBasis.setBaseCurrencyFactor ( rateBasis.getBaseCurrencyFactor () );
            fxRateBasis.setVariableCurrencyFactor ( rateBasis.getVariableCurrencyFactor () );
            fxRateBasis.setPipsFactor ( rateBasis.getPipsFactor () );
            fxRateBasis.setSpotPrecision ( rateBasis.getSpotPrecision () );
            fxRateBasis.setForwardPointsPrecision ( rateBasis.getForwardPointsPrecision () );
            fxRateBasis.setSpotPointsPrecision ( rateBasis.getSpotPointsPrecision () );
            fxRateBasis.setInverseSpotPrecision ( rateBasis.getInverseSpotPrecision () );
            fxRateBasis.setInverseForwardPointsPrecision ( rateBasis.getInverseForwardPointsPrecision () );
            fxRateBasis.setInversePipsFactor ( rateBasis.getInversePipsFactor () );
            fxRateBasis.setInverseSpotPointsPrecision ( rateBasis.getInverseSpotPointsPrecision () );
            fxRateBasis.setAllowInverse ( rateBasis.isAllowInverse () );
            fxRateBasis.setRoundingType ( rateBasis.getRoundingType () );
            fxRateBasis.setNDF ( rateBasis.isNDF () );
            fxRateBasis.setName ( rateBasis.getName () );
            fxRateBasis.setMarketConvention ( rateBasis.isMarketConvention () );
            fxRateBasis.setDeliverableNDF ( rateBasis.isDeliverableNDF () );
            if ( ObjectUtils.isNull ( rateBasis.getBaseCurrency () ) )
            {
                log.error ( "MDSC.copyQuoteConvention - Issue in Reference quote Convention " + referenceQuoteConvention
                        + ", no base currency found RateBasis " + rateBasis.getObjectID () + ", hence skipping " );
                continue;
            }

            fxRateBasis.setBaseCurrency ( rateBasis.getBaseCurrency () );
            fxRateBasis.setNamespace ( referenceQt.getNamespace () );
            if ( ObjectUtils.isNull ( rateBasis.getVariableCurrency () ) )
            {
                log.error ( "MDSC.copyQuoteConvention - Issue in Reference quote Convention "
                        + referenceQuoteConvention + ", no variable currency found in RateBasis "
                        + rateBasis.getObjectID () + ", hence skipping " );
                continue;
            }
            fxRateBasis.setVariableCurrency ( rateBasis.getVariableCurrency () );
            if ( null != rateBasis.getCrossCurrency () )
            {
                fxRateBasis.setCrossCurrency ( rateBasis.getCrossCurrency () );
            }
            fxRateBasis.setFXRateConvention ( fxRateConvention );
            fxRateConvention.addFXRateBasis ( fxRateBasis );
            if ( null != ( RuleSet ) rateBasis.getSettlementDateRuleSet () )
            {
                fxRateBasis.setSettlementDateRuleSet ( AdminTransactionUtil.register ( rateBasis.getSettlementDateRuleSet () ) );
            }
            FXBusinessCalendar businessCalendar = rateBasis.getFXBusinessCalendar ();
            if ( null == businessCalendar )
            {
                fxRateBasis.setFXBusinessCalendar ( null );
            }
            else
            {
                FXBusinessCalendar fxBusinessCalendar = FXFactory.newFXBusinessCalendar ();
                fxRateBasis.setFXBusinessCalendar ( fxBusinessCalendar );
                fxBusinessCalendar.setLag ( businessCalendar.getLag () );
                fxBusinessCalendar.setLagType ( businessCalendar.getLagType () );
                Collection<HolidayCalendar> calendars = businessCalendar.getHolidayCalendars ();
                Collection newHolidayCalendars = new ArrayList ();
                for ( HolidayCalendar holidayCalendar : calendars )
                {
                    HolidayCalendar holidayCalendarClone = ( HolidayCalendar ) holidayCalendar.getRegisteredObject ();
                    newHolidayCalendars.add ( holidayCalendarClone );
                }
                fxBusinessCalendar.setHolidayCalendars ( newHolidayCalendars );

                HolidayCalendar holidayCalendarNoLag = businessCalendar.getNoLagHolidayCalendar1 ();
                if ( null != holidayCalendarNoLag )
                {
                    holidayCalendarNoLag = ( HolidayCalendar ) holidayCalendarNoLag.getRegisteredObject ();
                    fxBusinessCalendar.setNoLagHolidayCalendar1 ( holidayCalendarNoLag );
                }

                HolidayCalendar holidayCalendarNoLag2 = businessCalendar.getNoLagHolidayCalendar2 ();
                if ( null != holidayCalendarNoLag2 )
                {
                    holidayCalendarNoLag2 = ( HolidayCalendar ) holidayCalendarNoLag2.getRegisteredObject ();
                    fxBusinessCalendar.setNoLagHolidayCalendar2 ( holidayCalendarNoLag2 );
                }

                HolidayCalendar holidayCalendarIgnoreLag1 = businessCalendar.getIgnoreLagHolidayCalendar1 ();
                if ( null != holidayCalendarIgnoreLag1 )
                {
                    holidayCalendarIgnoreLag1 = ( HolidayCalendar ) holidayCalendarIgnoreLag1.getRegisteredObject ();
                    fxBusinessCalendar.setIgnoreLagHolidayCalendar1 ( holidayCalendarIgnoreLag1 );
                }

                HolidayCalendar holidayCalendarIgnoreLag2 = businessCalendar.getIgnoreLagHolidayCalendar2 ();
                if ( null != holidayCalendarIgnoreLag2 )
                {
                    holidayCalendarIgnoreLag2 = ( HolidayCalendar ) holidayCalendarIgnoreLag2.getRegisteredObject ();
                    fxBusinessCalendar.setIgnoreLagHolidayCalendar2 ( holidayCalendarIgnoreLag2 );
                }

                fxBusinessCalendar.setIgnoreTODHolidayCalendars ( businessCalendar.isIgnoreTODHolidayCalendars () );
                fxBusinessCalendar.setIgnoreTOMHolidayCalendars ( businessCalendar.isIgnoreTOMHolidayCalendars () );
                fxBusinessCalendar.setRejectTODHolidayCalendar1 ( businessCalendar.getRejectTODHolidayCalendar1 () );
                fxBusinessCalendar.setRejectTODHolidayCalendar2 ( businessCalendar.getRejectTODHolidayCalendar2 () );

                FXBusinessCalendar fixingBuissCalendar = rateBasis.getFixingBusinessCalendar ();
                if ( fixingBuissCalendar == null )
                {

                    fxRateBasis.setFixingBusinessCalendar ( null );
                }
                else
                {
                    FXBusinessCalendar fxFixingBuissCalendar = new FXBusinessCalendarC ();
                    fxFixingBuissCalendar.setLag ( fixingBuissCalendar.getLag () );
                    fxFixingBuissCalendar.setLagType ( fixingBuissCalendar.getLagType () );
                    Collection<HolidayCalendar> fixingCalendars = fixingBuissCalendar.getHolidayCalendars ();
                    Collection newFixingCalendars = new ArrayList ();
                    for ( HolidayCalendar holidayCalendar2 : fixingCalendars )
                    {
                        HolidayCalendar fixingholidayCalendar = ( HolidayCalendar ) holidayCalendar2.getRegisteredObject ();
                        newFixingCalendars.add ( fixingholidayCalendar );
                    }
                    fxFixingBuissCalendar.setHolidayCalendars ( newFixingCalendars );
                    HolidayCalendar holidayCalendarNoLagFixing = fixingBuissCalendar.getNoLagHolidayCalendar ();
                    if ( null != holidayCalendarNoLagFixing )
                    {
                        holidayCalendarNoLagFixing = ( HolidayCalendar ) holidayCalendarNoLagFixing.getRegisteredObject ();
                        fxFixingBuissCalendar.setNoLagHolidayCalendar1 ( holidayCalendarNoLagFixing );
                    }

                    HolidayCalendar holidayCalendarNoLagFixing2 = fixingBuissCalendar.getNoLagHolidayCalendar2 ();
                    if ( null != holidayCalendarNoLagFixing2 )
                    {
                        holidayCalendarNoLagFixing2 = ( HolidayCalendar ) holidayCalendarNoLagFixing2.getRegisteredObject ();
                        fxFixingBuissCalendar.setNoLagHolidayCalendar2 ( holidayCalendarNoLagFixing2 );
                    }

                    HolidayCalendar holidayCalendarIgnoreLagFixing1 = fixingBuissCalendar.getIgnoreLagHolidayCalendar1 ();
                    if ( null != holidayCalendarIgnoreLagFixing1 )
                    {
                        holidayCalendarIgnoreLagFixing1 = ( HolidayCalendar ) holidayCalendarIgnoreLagFixing1.getRegisteredObject ();
                        fxFixingBuissCalendar.setIgnoreLagHolidayCalendar1 ( holidayCalendarIgnoreLagFixing1 );
                    }

                    HolidayCalendar holidayCalendarIgnoreLagFixing2 = fixingBuissCalendar.getIgnoreLagHolidayCalendar2 ();
                    if ( null != holidayCalendarIgnoreLagFixing2 )
                    {
                        holidayCalendarIgnoreLagFixing2 = ( HolidayCalendar ) holidayCalendarIgnoreLagFixing2.getRegisteredObject ();
                        fxFixingBuissCalendar.setIgnoreLagHolidayCalendar2 ( holidayCalendarIgnoreLagFixing2 );
                    }

                    fxRateBasis.setFixingBusinessCalendar ( fxFixingBuissCalendar );
                }
            }
        }
    }

    public void removeCurrencyPairFromQuoteConvention ( FXRateConvention fxRateConvention, Collection<FXRateBasis> fxRateBasises )
    {
        for ( FXRateBasis fxRateBasis : fxRateBasises )
        {
            removeCurrencyPairFromQuoteConvention ( fxRateConvention, fxRateBasis );
        }
    }


    public void inActivateCurrencyPairGroup ( Collection<CurrencyPairGroup> colCurrencyPairGroup )
    {
        for ( CurrencyPairGroup currencyPairGroup : colCurrencyPairGroup )
        {
            inActivateCurrencyPairGroup ( currencyPairGroup );
        }
    }


    public void inActivateCurrencyPairGroup ( CurrencyPairGroup currencyPairGroup )
    {
        log.info ( "MDSC.inActivateCurrencyPairGroup - Inactivating Currency Pair Group " + currencyPairGroup.getShortName () );
        currencyPairGroup = AdminTransactionUtil.register ( currencyPairGroup );
        currencyPairGroup.setStatus ( 'P' );
        HashMap propertyMap = new HashMap ();
        propertyMap.put ( CCY_PAIR_GRP_NAME, currencyPairGroup.getShortName () );
        NotificationUtil.notifyFunctors ( propertyMap, "FUNCTORS.CURRENCYPAIRGROUP.INACTIVATION" );
        AuditEvent audit = audit ( currencyPairGroup );
        audit.setAction ( AUDIT_ACTION_INACTIVATEGRP );
        audit.setComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        persistToMongo ( audit );
    }

    public void activateCurrencyPairGroup ( Collection<CurrencyPairGroup> col )
    {
        for ( CurrencyPairGroup currencyPairGroup : col )
        {
            activateCurrencyPairGroup ( currencyPairGroup );
        }
    }


    public void activateCurrencyPairGroup ( CurrencyPairGroup currencyPairGroup )
    {
        log.info ( "MDSC.activateCurrencyPairGroup - Activating Currency Pair Group " + currencyPairGroup.getShortName () );
        currencyPairGroup = AdminTransactionUtil.register ( currencyPairGroup );
        currencyPairGroup.setStatus ( 'A' );
        HashMap<String, String> propertyMap = new HashMap<String, String> ();
        propertyMap.put ( CCY_PAIR_GRP_NAME, currencyPairGroup.getShortName () );
        NotificationUtil.notifyFunctors ( propertyMap, "FUNCTORS.CURRENCYPAIRGROUP.ACTIVATION" );
        AuditEvent audit = audit ( currencyPairGroup );
        audit.setAction ( AUDIT_ACTION_ACTIVATEGRP );
        audit.setComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        persistToMongo ( audit );
    }


    public void updateCurencyPairGrp ( String change, Namespace nameSpace, FXRateConvention fxRateConvention,
                                       boolean added, String groupName )
    {
        if ( change != null && change.length () > 0 )
        {
            if ( added )
            {
                updateCurencyPairGrp ( MarketDefinitionConstant.AUDIT_ACTION_ADDCURRENCYPAIRGRP, change,
                        nameSpace, fxRateConvention, groupName );
            }
            else
            {
                updateCurencyPairGrp ( MarketDefinitionConstant.AUDIT_ACTION_UPDATECURRENCYPAIRGRP, change,
                        nameSpace, fxRateConvention, groupName );
            }
        }
    }


    private void updateCurencyPairGrp ( String action, String change, Namespace nameSpace, FXRateConvention fxRateConvention, String groupName )
    {

        User loginUser = GenericAdminUtil.getContextUser ();
        loginUser = AdminTransactionUtil.register ( loginUser );
        AuditEventC audit = new AuditEventC ();
        audit.setUser ( loginUser );
        audit.setOrganization ( AdminTransactionUtil.register ( loginUser.getOrganization () ) );
        audit.setComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        audit.setAction ( action );
        if ( ObjectUtils.isNotNull ( change ) )
        {
            audit.setEntity1 ( fxRateConvention );
            audit.setStringArg2 ( "Currency Pair Group:" + groupName );
            audit.setStringArg9 ( change );
            audit.setNamespace ( nameSpace );
        }
        persistToMongo ( audit );
    }

    private static AuditEventC audit ( CurrencyPairGroup entity )
    {
        User loginUser = GenericAdminUtil.getContextUser ();
        loginUser = AdminTransactionUtil.register ( loginUser );
        AuditEventC audit = new AuditEventC ();
        audit.setUser ( loginUser );
        audit.setNamespace ( loginUser.getNamespace () );
        audit.setStringArg1 ( entity.getClass ().getName () );
        audit.setEntity1 ( entity.getFXRateConvention () );
        audit.setStringArg8 ( entity.getShortName () );

        return audit;
    }

    private void auditActivation ( FXRateConvention convention, boolean isActivation )
    {
        AuditEvent audit = initializeAudit ( convention, isActivation ? MarketDefinitionConstant.ACTIVATION : MarketDefinitionConstant.INACTIVATION );
        persistToMongo ( audit );
    }


    private static AuditEvent initializeAudit ( FXRateBasis basis, String action )
    {
        AuditEvent audit = initializeAudit ( basis.getFXRateConvention (), action );
        audit.setEntity2 ( basis );
        audit.setStringArg2 ( basis.getName () );
        return audit;
    }


    private static AuditEvent initializeAudit ( FXRateConvention convention, String action )
    {
        User loginUser = GenericAdminUtil.getContextUser ();
        loginUser = AdminTransactionUtil.register ( loginUser );
        AuditEventC audit = new AuditEventC ();
        audit.setUser ( loginUser );
        audit.setOrganization ( loginUser.getOrganization () );
        audit.setComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        audit.setAction ( action );
        if ( ObjectUtils.isNotNull ( convention ) )
        {
            audit.setEntity1 ( convention );
            audit.setStringArg1 ( convention.getShortName () );
            audit.setNamespace ( convention.getNamespace () );
        }
        audit.setModifiedNamespace ( convention != null && convention.getNamespace () != null ? convention.getNamespace ().getShortName () : IdcUtilC.MAIN_NAMESPACE );
        audit.setModifiedByUser ( loginUser.getShortName () );
        audit.setModifiedByNamespace ( loginUser.getNamespace ().getShortName () );
        return audit;
    }

    public static void persistToMongo ( AuditEvent audit )
    {
        ArrayList<AuditConfigurationMBean.PERSIST_TYPE> auditPersistSource =
                AuditConfigurationFactory.getAuditConfigurationMBean ()
                        .getAuditPersistTypeForComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        if ( auditPersistSource != null )
        {
            for ( AuditConfigurationMBean.PERSIST_TYPE type : auditPersistSource )
            {
                if ( type.toString ().equalsIgnoreCase ( AuditConfigurationMBean.PERSIST_TYPE.MONGO.toString () ) )
                {
                    ArrayList<AuditService> auditServices = AuditServiceFactory
                            .getAuditServiceToPersist ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
                    for ( AuditService service : auditServices )
                    {
                        if ( service instanceof MongoAuditServiceC )
                        {
                            service.audit ( audit );
                        }
                    }
                }
            }
        }
    }

    private void auditChanges ( FXRateBasis fxRateBasis, String oldValue, String newValue, String action )
    {
        AuditEvent event = initializeAudit ( fxRateBasis, action );
        if ( ObjectUtils.isNotNull ( oldValue ) )
        {
            event.setStringArg4 ( oldValue );
        }
        event.setStringArg9 ( newValue );
        persistToMongo ( event );
    }

    private void auditCurrencyCreate ( Currency currency )
    {
        User loginUser = GenericAdminUtil.getContextUser ();
        loginUser = AdminTransactionUtil.register ( loginUser );
        AuditEventC audit = new AuditEventC ();
        audit.setUser ( loginUser );
        audit.setOrganization ( loginUser.getOrganization () );
        audit.setComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        audit.setAction ( MarketDefinitionConstant.AUDIT_ACTION_CREATECURRENCY );
        if ( ObjectUtils.isNotNull ( currency ) )
        {
            audit.setEntity3 ( currency );
            audit.setStringArg3 ( currency.getShortName () );
            audit.setNamespace ( currency.getNamespace () );
        }
        StringBuilder newValue = new StringBuilder ( 50 );
        BusinessCalendar businessCalendar = currency.getBusinessCalendar ();
        newValue.append ( "ISCode: " ).append ( currency.getShortName () );
        newValue.append ( ",LongName: " ).append ( currency.getLongName () );
        newValue.append ( ",Alias: " ).append ( currency.getAlias () );
        newValue.append ( ",Prefix: " ).append ( currency.getPrefixCharacter () );
        newValue.append ( ",Sort Order: " ).append ( currency.getSortOrder () );
        newValue.append ( ",Type: " ).append ( currency.getInstrumentClassification ().getShortName () );
        newValue.append ( ",Tick: " ).append ( currency.getTickValue () );
        newValue.append ( ",Notional Display: " ).append ( currency.getRoundingType () == BigDecimal.ROUND_DOWN ? "Truncate" : "Round" );
        newValue.append ( ",RollConvention: " ).append ( businessCalendar.getRollConvention ().getShortName () );
        newValue.append ( ",HolidayCalendar: {" );
        Collection<HolidayCalendar> colHoldiays = businessCalendar.getHolidayCalendars ();
        for ( HolidayCalendar holidayCalendar : colHoldiays )
        {
            newValue.append ( holidayCalendar.getShortName () ).append ( "," );
        }
        newValue.append ( "}" );
        newValue.append ( ",Lag: " ).append ( businessCalendar.getLag () );
        newValue.append ( ",Lag Type: " ).append ( businessCalendar.getLagType () == BusinessCalendar.LAG_TYPE_BUSINESS ? "Business" : "Calendar" );
        newValue.append ( ",FixedTo: " ).append ( ObjectUtils.isNotNull ( currency.getCurrencyFixedTo () ) ? currency.getCurrencyFixedTo ().getShortName () : " " );
        newValue.append ( ",Conversion Rate: " ).append ( currency.getConversionRate () );
        newValue.append ( ",Deliverable: " ).append ( currency.isDeliverable () );
        audit.setStringArg9 ( newValue.toString () );
        persistToMongo ( audit );
    }


    private void auditCurrency ( Currency currency, String oldValue, String newValue, String action )
    {

        AuditEvent audit = initializeAuditCurrency ( currency, action );
        if ( ObjectUtils.isNotNull ( oldValue ) )
        {
            audit.setStringArg4 ( oldValue );
        }
        audit.setStringArg9 ( newValue );
        persistToMongo ( audit );
    }

    private AuditEvent initializeAuditCurrency ( Currency currency, String action )
    {
        User loginUser = GenericAdminUtil.getContextUser ();
        loginUser = AdminTransactionUtil.register ( loginUser );
        AuditEventC audit = new AuditEventC ();
        audit.setUser ( loginUser );
        audit.setOrganization ( loginUser.getOrganization () );
        audit.setComponent ( MarketDefinitionConstant.AUDIT_COMPONENT_MARKETDEFINITION );
        audit.setAction ( action );
        if ( ObjectUtils.isNotNull ( currency ) )
        {
            audit.setEntity3 ( currency );
            audit.setStringArg3 ( currency.getShortName () );
            audit.setNamespace ( currency.getNamespace () );
        }
        return audit;
    }
}
