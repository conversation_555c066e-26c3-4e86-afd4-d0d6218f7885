package com.integral.admin.services.marketdefinition;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.instrument.InstrumentSubClassification;
import com.integral.persistence.Namespace;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public interface MarketDefinitionService
{

    void activate ( String rateConvention );

    void activate ( Collection<FXRateConvention> rateConventions );

    void activate ( FXRateConvention rateConvention );

    void activate ( long rateConventionID );

    void inActivate ( String rateConvention );

    void inActivate ( Collection<FXRateConvention> rateConventions );

    void inActivate ( FXRateConvention rateConvention );

    void inActivate ( long rateConventionID );

    void createFXRateBasis ( FXRateConvention fxRateConvention, String currencyPairName, Currency baseCurrency, Currency variableCurrency,
                             boolean allowInverse, double baseCurrencyFactor, double variableCurrencyFactor, int spotPrecision, int pointsPrecision,
                             int forwardPoint, double pipsFactor, int invSpotPrecision, int invPointsPrecision, int invForwardPoint, double invPipsFactor,
                             Currency cross, boolean useDefaultSettlementInfo, int settlementOvrSpotLag,
                             Collection<HolidayCalendar> newHolidayCalendars, Collection<HolidayCalendar> noLagholidayCalendar,
                             Collection<HolidayCalendar> ignLagholidayCalendar, boolean isDeliverable, int fixingCalendarlag,
                             Collection<HolidayCalendar> newFixingHolidayCalendars, Collection<HolidayCalendar> noLagFixingHolidayCalendars,
                             Collection<HolidayCalendar> ignLagFixingHolidayCalendars, Double contractMultiplier, boolean marketConvention, boolean deliverableNDF, Collection<HolidayCalendar> rejectTODHolidayCalendar, boolean ignoreTODHolidayCalendars, boolean ignoreTOMHolidayCalendars );

    void updateFXRateBasis ( FXRateBasis fxRateBasis, boolean useDefaultSettlementInfo,
                             int settlementOvrSpotLag, Collection<HolidayCalendar> newHolidayCalendars, Collection<HolidayCalendar> noLagholidayCalendars,
                             Collection<HolidayCalendar> ignLagholidayCalendars, Collection<HolidayCalendar> rejectTODHolidayCalendar, boolean ignoreTODHolidayCalendars, boolean ignoreTOMHolidayCalendars );


    void updateFXRateBasis ( FXRateBasis fxRateBasis, String currencyPairName, Currency baseCurrency, Currency variableCurrency,
                             boolean allowInverse, double baseCurrencyFactor, double variableCurrencyFactor, int spotPrecision, int pointsPrecision,
                             int forwardPoint, double pipsFactor, int invSpotPrecision, int invPointsPrecision, int invForwardPoint, double invPipsFactor,
                             Currency cross, Double contractMultiplier, boolean marketConvention, boolean deliverableNDF );

    void updateNDF ( FXRateBasis fxRateBasis, boolean isDeliverable, int fixingCalendarlag, Collection<HolidayCalendar> newFixingHolidayCalendars,
                     Collection<HolidayCalendar> noLagFixingHolidayCalendars, Collection<HolidayCalendar> ignLagFixingHolidayCalendars );

    void createCurrency ( String isoCode, String name, String alias, Integer dSort, char prefix, InstrumentClassification instrClsf, InstrumentSubClassification subClsf,
                          Double tick, Integer roundingType, Collection<HolidayCalendar> newHolidayCalendars, String rollConvention,
                          Integer lag, Integer lagtype, Currency currencyFixedTo, Double dConversionRate, boolean isdeliverable );


    void createCurrency ( String isoCode, String name, String alias, Integer dSort, char prefix, InstrumentClassification instrClsf, InstrumentSubClassification subClsf,
                          Double tick, Integer roundingType, Collection<HolidayCalendar> newHolidayCalendars, String rollConvention,
                          Integer lag, Integer lagtype, Currency currencyFixedTo, Double dConversionRate, boolean isdeliverable, String settlementTenor,
                          String settlementType, String underlyingInstrument );

    void updateCurrency ( Currency currency, String name, String alias, Integer dSort, char prefix, InstrumentClassification instrClsf, InstrumentSubClassification subClsf,
                          Double tick, Integer roundingType, Collection<HolidayCalendar> newHolidayCalendars, String rollConvention,
                          Integer lag, Integer lagtype, Currency currencyFixedTo, Double dConversionRate, boolean isdeliverable );

    FXRateConvention createQuoteConvention ( String shortName, String longName );

    void removeCurrencyPairFromQuoteConvention ( FXRateConvention fxRateConvention, FXRateBasis fxRateBasis );

    void removeCurrencyPairFromQuoteConvention ( FXRateConvention fxRateConvention, Collection<FXRateBasis> fxRateBasises );

    void activateCurrency ( Collection<Currency> currencys );

    void activateCurrency ( Currency currency );

    void inActivateCurrency ( Collection<Currency> currencys );

    void inActivateCurrency ( Currency currency );

    void copyQuoteConvention ( String referenceQuoteConvention, String newQuoteConvention );

    void activateCurrencyPairGroup ( Collection<CurrencyPairGroup> col );

    void inActivateCurrencyPairGroup ( Collection<CurrencyPairGroup> col );

    void activateCurrencyPairGroup ( CurrencyPairGroup currencyPairGroup );

    void inActivateCurrencyPairGroup ( CurrencyPairGroup currencyPairGroup );

    void updateParentQuoteConvention ( FXRateConvention fxRateConvention, String parentQuoteConvention );

    void updateCurencyPairGrp ( String change, Namespace nameSpace, FXRateConvention fxRateConvention, boolean added, String groupName );
}
