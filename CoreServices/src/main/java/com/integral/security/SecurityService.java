package com.integral.security;

import com.integral.security.IntegralGroup;
import com.integral.security.IntegralUser;
import com.integral.user.User;
import com.integral.user.UserSession;

import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Hashtable;

/**
 * SecurityService : Provides security services by integrating to Security repositories (Database)
 */
public interface SecurityService
{
    /**
     * Lookup Name
     */
    public static final String JNDI_NAME = "SecurityService";

    /**
     * @param name
     * @return IntegralUser if user is present for the given <UserName>@<Namespace> combination, else return <null>
     * @throws RemoteException
     */
    public IntegralUser getUser( String name ) throws RemoteException;

    /**
     * @param name
     * @param password
     * @return IntegralUser if authenticated return the authenticated (valid password, status active), else return <null>
     * @throws RemoteException
     */
    public IntegralUser authenticateUser( String name, String password, boolean isSSOUser ) throws RemoteException;

    /**
     * @param groupName
     * @return IntegralGroup with all the groupMembers (by default) if there is a group for the given groupname, else return <null>
     * @throws RemoteException
     */
    public IntegralGroup getGroup( String groupName ) throws RemoteException;

    /**
     * @param groupName
     * @param withMembers
     * @return IntegralGroup if there is a group for the given groupname with or without members based on @param withMembers, else return <null>
     * @throws RemoteException
     */
    public IntegralGroup getGroup( String groupName, boolean withMembers ) throws RemoteException;

    /**
     * @param groupName
     * @return List of group members for the given group
     * @throws RemoteException
     */
    public Hashtable getGroupMembers( String groupName ) throws RemoteException;

    /**
     * @return List of Users
     * @throws RemoteException
     */
    public Enumeration getUsers() throws RemoteException;

    /**
     * @return List of Groups
     * @throws RemoteException
     */
    public Enumeration getGroups() throws RemoteException;

    /**
     * @param objectId
     * @return List of Groups to which the given userObjectId belongs to.
     * @throws RemoteException
     */
    public Collection getGroups( long objectId ) throws RemoteException;

    public void auditLoginFailed( String user ) throws RemoteException;

    public void auditLoginSuccess( String user ) throws RemoteException;

    public void suspendUser( User user) throws RemoteException;

    public boolean isExternalAuthEnabled( String user ) throws RemoteException;

    //public void auditLogout( User user, Boolean isSessionTimeout );

    public void auditLoginFailed( User user, boolean retryOnFailure );
    
    public void auditLoginFailed( User user , boolean retryOnFailure, boolean updateUserSession);

    public void auditLoginSuccess( User user, boolean retryOnFailure );

    public void auditLoginSuccess( User  user,  boolean retryOnFailure, boolean updateUserSession);
    
    /**
     *  Updates last login date/time of the user.
     *
     * @param user
     * @param retryOnFailure
     */
    void auditLastLogin(User user, boolean retryOnFailure);
    
    public void auditExternalLoginSuccess( User user );
    
    public void auditExternalLoginFailed( User user );
    
    public void handlePasswordForgetRequestFailed(User user);
    
    public void handlePasswordForgetRequestSuccess(User user);
    
    public void handlePasswordResetRequestFailed(User user);
    
    public void handlePasswordResetRequestSuccess(User user);
    
    public void handlePasswordForgetRequestSuccess(User user, boolean auditEvent, boolean updateUserSession);
    
}
