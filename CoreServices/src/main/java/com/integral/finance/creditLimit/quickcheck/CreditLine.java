package com.integral.finance.creditLimit.quickcheck;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;
import com.google.common.util.concurrent.AtomicDouble;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitSubscriptionManagerC;
import com.integral.finance.currency.Currency;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.DealingModel;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.util.collections.ConcurrentHashSet;

public class CreditLine 
{
	private Currency limitCurrency;
	private final AtomicDouble reservedAmtInLimitCcy = new AtomicDouble(0);
	private final AtomicBoolean creditSuspended = new AtomicBoolean(false);
	private final ConcurrentMap<String, Double> transactionId2ReservedCreditInLimitCcy = new ConcurrentHashMap<String ,Double>();

	private final Map<Currency, AtomicDouble> reservedAmtMap = new ConcurrentHashMap<Currency, AtomicDouble> ( );
	private final ConcurrentMap<String, Map<Currency, AtomicDouble>> transactionId2ReservedMap = new ConcurrentHashMap<String ,Map<Currency, AtomicDouble>>();

	private static final Log log = LogFactory.getLog( CreditLine.class );
	
	private final Set<CreditLineCollection> interestedCreditLineCollections = new ConcurrentHashSet<CreditLineCollection>();

	private boolean orgLevel;

	private boolean cashSettlement;
	
	public CreditLine( Currency limitCurrency, boolean creditSuspended, boolean orgLevelExposure, boolean cash )
	{
		if ( limitCurrency == null )
		{
			throw new IllegalArgumentException( "limitCurrency cannot be null" );
		}
		this.limitCurrency = limitCurrency;
		setSuspend(creditSuspended);
		this.orgLevel = orgLevelExposure;
		this.cashSettlement = cash;
	}
	
	public double getReservedAmountInLimitCurrency(){
		return reservedAmtInLimitCcy.get();
	}
	
	public Currency getLimitCurrency()
	{
		return limitCurrency;
	}

	public void setLimitCurrency ( Currency ccy )
	{
		this.limitCurrency = ccy;
	}

	public boolean isOrgLevel()
	{
		return orgLevel;
	}

	public void setOrgLevel ( boolean orgLevelExposure )
	{
		this.orgLevel = orgLevelExposure;
	}

	public boolean isCashSettlement()
	{
		return cashSettlement;
	}

	public void setCashSettlement ( boolean cash )
	{
		this.cashSettlement = cash;
	}

	public double getReserveAmountInCurrency( Currency ccy )
	{
		AtomicDouble dbl = reservedAmtMap.get ( ccy );
		return dbl != null ? dbl.get () : 0.0;
	}

	public boolean reserveCredit( Currency ccy, double requestedCcyAmount, double requestedAmountInLimitCcy, String transactionId, boolean isBid, DealingModel dealingEntity )
	{							
		long t1 = System.currentTimeMillis();
		if ( cashSettlement )
		{
			Map<Currency, AtomicDouble> map = transactionId2ReservedMap.get ( transactionId );
			if ( map != null )
			{
				log.warn ( new StringBuilder ().append ( "reserveCredit: Duplicate credit reserved against TransactionId=" ).
						append ( transactionId ).append ( " | oldValue=" ).append ( map ).
						append ( " | requestedAmount=" ).append ( requestedCcyAmount ).append ( ",ccy=" ).append ( ccy ).toString () );
			}
			else
			{
				map = new ConcurrentHashMap<Currency, AtomicDouble>();
				synchronized ( this )
				{
					AtomicDouble dbl = map.get ( ccy );
					if ( dbl == null )
					{
						dbl = new AtomicDouble ();
						AtomicDouble existing = map.putIfAbsent ( ccy, dbl );
						if ( existing != null )
						{
							dbl = existing;  // Use existing if another thread added it
						}
					}
					dbl.addAndGet ( requestedCcyAmount );
					transactionId2ReservedMap.put ( transactionId, map );

					AtomicDouble reserved = reservedAmtMap.get ( ccy );
					if ( reserved == null )
					{
						reserved = new AtomicDouble ();
						AtomicDouble existing = reservedAmtMap.putIfAbsent ( ccy, reserved );
						if ( existing != null )
						{
							reserved = existing;  // Use existing if another thread added it
						}
					}
					reserved.addAndGet ( requestedCcyAmount );
				}
			}
		}
		else
		{
			Double oldValue = transactionId2ReservedCreditInLimitCcy.putIfAbsent ( transactionId, requestedAmountInLimitCcy );
			if ( oldValue != null )
			{
				log.info ( new StringBuilder ().append ( "reserveCredit: Duplicate credit reserved against TransactionId=" ).
						append ( transactionId ).append ( " | OldAmtInLimitCcy=" ).append ( oldValue ).
						append ( " | newAmtInLimitCcy=" ).append ( requestedAmountInLimitCcy ).toString () );
			}
			else
			{
				reservedAmtInLimitCcy.addAndGet ( requestedAmountInLimitCcy );
			}
		}
		if(log.isDebugEnabled())
		{
			long t2 = System.currentTimeMillis()-t1;
			log.debug("CreditLine.reserveCredit : TIMETAKEN = " + t2);
		}
		return true;
	}

	/**
	 * Enhanced reserveCredit method that handles SingleLegOrder and SingleLegTrade objects
	 *
	 * @param ccy the currency for the credit reservation
	 * @param requestedCcyAmount the amount in the specified currency
	 * @param requestedAmountInLimitCcy the amount in limit currency
	 * @param dealingEntity the dealing entity (SingleLegOrder or SingleLegTrade)
	 * @param isBid whether this is a bid transaction
	 * @return true if credit was successfully reserved
	 */
	public boolean reserveCredit(String transactionId, Currency ccy, double requestedCcyAmount, double requestedAmountInLimitCcy, DealingModel dealingEntity, boolean isBid)
	{
		if (dealingEntity == null)
		{
			log.warn("CreditLine.reserveCredit: dealingEntity parameter is null");
			return false;
		}

		if (dealingEntity instanceof SingleLegOrder)
		{
			SingleLegOrder singleLegOrder = (SingleLegOrder) dealingEntity;

			// Check if credit reserve is enabled for this order
			if (singleLegOrder.isCreditReserveEnabled())
			{
				if (log.isDebugEnabled())
				{
					log.debug("CreditLine.reserveCredit: Reserving credit for SingleLegOrder with ID: " + transactionId +
							 ", amount: " + requestedCcyAmount + " " + ccy.getShortName() +
							 ", limitCcyAmount: " + requestedAmountInLimitCcy);
				}

				return reserveCredit(ccy, requestedCcyAmount, requestedAmountInLimitCcy, transactionId, isBid, dealingEntity);
			}
			else
			{
				if (log.isDebugEnabled())
				{
					log.debug("CreditLine.reserveCredit: Credit reserve is not enabled for SingleLegOrder with ID: " + singleLegOrder.get_id());
				}
				return true; // No credit reservation needed
			}
		}
		else if (dealingEntity instanceof SingleLegTrade)
		{
			SingleLegTrade singleLegTrade = (SingleLegTrade) dealingEntity;

			// Get the associated order request
			SingleLegOrder orderRequest = singleLegTrade.getOrderRequest();
			if (orderRequest != null)
			{
				String orderRequestId = orderRequest.get_id();
				boolean isCreditReserveEnabled = orderRequest.isCreditReserveEnabled();

				if (log.isDebugEnabled())
				{
					log.debug("CreditLine.reserveCredit: Processing SingleLegTrade with ID: " + transactionId +
							 ", associated OrderRequest ID: " + orderRequestId +
							 ", isCreditReserveEnabled: " + isCreditReserveEnabled);
				}

				if (isCreditReserveEnabled)
				{
					// Standard case: transfer from earmarked OrderRequest amount
					if (log.isDebugEnabled())
					{
						log.debug("CreditLine.reserveCredit: Transferring earmarked credit from OrderRequest ID: " + orderRequestId +
								 " to SingleLegTrade ID: " + transactionId +
								 ", amount: " + requestedCcyAmount + " " + ccy.getShortName() +
								 ", limitCcyAmount: " + requestedAmountInLimitCcy);
					}

					return atomicCreditTransfer(ccy, requestedCcyAmount, requestedAmountInLimitCcy, orderRequestId, transactionId, isBid);
				}
				else
				{
					// Credit reserve was not enabled for OrderRequest: treat as new reservation
					if (log.isDebugEnabled())
					{
						log.debug("CreditLine.reserveCredit: Credit reserve not enabled for OrderRequest. " +
								 "Creating new reservation for SingleLegTrade ID: " + transactionId +
								 ", amount: " + requestedCcyAmount + " " + ccy.getShortName() +
								 ", limitCcyAmount: " + requestedAmountInLimitCcy);
					}

					// Use empty string as fromTransactionId to indicate no source transaction
					return atomicCreditTransfer(ccy, requestedCcyAmount, requestedAmountInLimitCcy, "", transactionId, isBid);
				}
			}
			else
			{
				log.warn("CreditLine.reserveCredit: SingleLegTrade does not have an associated OrderRequest. Trade ID: " + singleLegTrade.get_id());
				return false;
			}
		}
		else
		{
			log.warn("CreditLine.reserveCredit: Unsupported dealingEntity type: " + dealingEntity.getClass().getName());
			return false;
		}
	}

	/**
	 * Atomically transfers credit from one transaction to another (e.g., from OrderRequest to SingleLegTrade)
	 * This ensures that partial amounts are correctly transferred without losing credit tracking
	 *
	 * @param ccy the currency for the credit transfer
	 * @param transferCcyAmount the amount in the specified currency to transfer
	 * @param transferAmountInLimitCcy the amount in limit currency to transfer
	 * @param fromTransactionId the source transaction ID (e.g., OrderRequest ID)
	 * @param toTransactionId the destination transaction ID (e.g., SingleLegTrade ID)
	 * @param isBid whether this is a bid transaction
	 * @return true if credit was successfully transferred
	 */
	private boolean atomicCreditTransfer(Currency ccy, double transferCcyAmount, double transferAmountInLimitCcy,
										String fromTransactionId, String toTransactionId, boolean isBid)
	{
		if (toTransactionId == null)
		{
			log.error("CreditLine.atomicCreditTransfer: Destination transaction ID cannot be null. To: " + toTransactionId);
			return false;
		}

		// fromTransactionId can be null or empty to indicate new reservation (no source transaction)
		boolean isNewReservation = (fromTransactionId == null || fromTransactionId.trim().isEmpty());

		if (transferCcyAmount <= 0 || transferAmountInLimitCcy <= 0)
		{
			log.warn("CreditLine.atomicCreditTransfer: Transfer amounts must be positive. CcyAmount: " + transferCcyAmount + ", LimitCcyAmount: " + transferAmountInLimitCcy);
			return false;
		}

		synchronized (this) // Ensure atomic operation
		{
			try
			{
				if (cashSettlement)
				{
					if (isNewReservation)
					{
						// Handle new reservation (no source transaction)
						// Add amount to destination transaction
						Map<Currency, AtomicDouble> toMap = transactionId2ReservedMap.get(toTransactionId);
						if (toMap == null)
						{
							toMap = new ConcurrentHashMap<Currency, AtomicDouble>();
							transactionId2ReservedMap.put(toTransactionId, toMap);
						}

						AtomicDouble toAmount = toMap.get(ccy);
						if (toAmount == null)
						{
							toAmount = new AtomicDouble(0);
							toMap.put(ccy, toAmount);
						}
						toAmount.addAndGet(transferCcyAmount);

						// Update total reserved amount for this currency
						AtomicDouble reserved = reservedAmtMap.get(ccy);
						if (reserved == null)
						{
							reserved = new AtomicDouble();
							AtomicDouble existing = reservedAmtMap.putIfAbsent(ccy, reserved);
							if (existing != null)
							{
								reserved = existing;
							}
						}
						reserved.addAndGet(transferCcyAmount);

						if (log.isDebugEnabled())
						{
							log.debug("CreditLine.atomicCreditTransfer: New cash settlement reservation. " +
									 "To: " + toTransactionId + ", Amount: " + transferCcyAmount + " " + ccy.getShortName() +
									 ", Total reserved for currency: " + reserved.get());
						}
					}
					else
					{
						// Handle cash settlement transfer from existing reservation
						Map<Currency, AtomicDouble> fromMap = transactionId2ReservedMap.get(fromTransactionId);
						if (fromMap == null)
						{
							log.error("CreditLine.atomicCreditTransfer: No reserved amount found for source transaction ID: " + fromTransactionId);
							return false;
						}

						AtomicDouble fromAmount = fromMap.get(ccy);
						if (fromAmount == null || fromAmount.get() < transferCcyAmount)
						{
							log.error("CreditLine.atomicCreditTransfer: Insufficient reserved amount for currency " + ccy.getShortName() +
									 ". Available: " + (fromAmount != null ? fromAmount.get() : 0) + ", Requested: " + transferCcyAmount);
							return false;
						}

						// Reduce amount from source transaction
						fromAmount.addAndGet(-transferCcyAmount);

						// If source amount becomes zero, remove the currency entry
						if (fromAmount.get() <= 0)
						{
							fromMap.remove(ccy);
							// If map becomes empty, remove the transaction entry
							if (fromMap.isEmpty())
							{
								transactionId2ReservedMap.remove(fromTransactionId);
							}
						}

						// Add amount to destination transaction
						Map<Currency, AtomicDouble> toMap = transactionId2ReservedMap.get(toTransactionId);
						if (toMap == null)
						{
							toMap = new ConcurrentHashMap<Currency, AtomicDouble>();
							transactionId2ReservedMap.put(toTransactionId, toMap);
						}

						AtomicDouble toAmount = toMap.get(ccy);
						if (toAmount == null)
						{
							toAmount = new AtomicDouble(0);
							toMap.put(ccy, toAmount);
						}
						toAmount.addAndGet(transferCcyAmount);

						if (log.isDebugEnabled())
						{
							log.debug("CreditLine.atomicCreditTransfer: Cash settlement transfer completed. " +
									 "From: " + fromTransactionId + ", To: " + toTransactionId +
									 ", Amount: " + transferCcyAmount + " " + ccy.getShortName());
						}
					}
				}
				else
				{
					if (isNewReservation)
					{
						// Handle new reservation (no source transaction)
						// This happens when OrderRequest.isCreditReserveEnabled is false

						// Add amount to destination transaction
						Double existingToAmount = transactionId2ReservedCreditInLimitCcy.get(toTransactionId);
						double newToAmount = (existingToAmount != null ? existingToAmount : 0) + transferAmountInLimitCcy;
						transactionId2ReservedCreditInLimitCcy.put(toTransactionId, newToAmount);

						// Atomically update reservedAmtInLimitCcy since this is new credit reservation
						reservedAmtInLimitCcy.addAndGet(transferAmountInLimitCcy);

						if (log.isDebugEnabled())
						{
							log.debug("CreditLine.atomicCreditTransfer: New credit reservation (credit reserve not enabled). " +
									 "To: " + toTransactionId + ", Amount: " + transferAmountInLimitCcy + " in limit currency. " +
									 "Total reservedAmtInLimitCcy updated to: " + reservedAmtInLimitCcy.get());
						}
					}
					else
					{
						// Handle limit currency transfer from existing reservation
						Double fromAmount = transactionId2ReservedCreditInLimitCcy.get(fromTransactionId);
						boolean hasEarmarkedAmount = (fromAmount != null && fromAmount >= transferAmountInLimitCcy);

						if (hasEarmarkedAmount)
						{
							// Standard transfer: move amount from source to destination
							// Reduce amount from source transaction
							double remainingAmount = fromAmount - transferAmountInLimitCcy;
							if (remainingAmount <= 0)
							{
								transactionId2ReservedCreditInLimitCcy.remove(fromTransactionId);
							}
							else
							{
								transactionId2ReservedCreditInLimitCcy.put(fromTransactionId, remainingAmount);
							}

							// Add amount to destination transaction
							Double existingToAmount = transactionId2ReservedCreditInLimitCcy.get(toTransactionId);
							double newToAmount = (existingToAmount != null ? existingToAmount : 0) + transferAmountInLimitCcy;
							transactionId2ReservedCreditInLimitCcy.put(toTransactionId, newToAmount);

							// reservedAmtInLimitCcy remains unchanged as this is an internal transfer

							if (log.isDebugEnabled())
							{
								log.debug("CreditLine.atomicCreditTransfer: Internal transfer completed. " +
										 "From: " + fromTransactionId + " (" + remainingAmount + " remaining), To: " + toTransactionId +
										 ", Amount: " + transferAmountInLimitCcy + " in limit currency. " +
										 "Total reservedAmtInLimitCcy unchanged: " + reservedAmtInLimitCcy.get());
							}
						}
						else
						{
							// No earmarked amount or insufficient amount: treat as new reservation
							// This happens when there is no amount earmarked against the orderRequest

							// Add amount to destination transaction
							Double existingToAmount = transactionId2ReservedCreditInLimitCcy.get(toTransactionId);
							double newToAmount = (existingToAmount != null ? existingToAmount : 0) + transferAmountInLimitCcy;
							transactionId2ReservedCreditInLimitCcy.put(toTransactionId, newToAmount);

							// Atomically update reservedAmtInLimitCcy since this is new credit reservation
							reservedAmtInLimitCcy.addAndGet(transferAmountInLimitCcy);

							if (log.isDebugEnabled())
							{
								log.debug("CreditLine.atomicCreditTransfer: New credit reservation (no earmarked amount). " +
										 "To: " + toTransactionId + ", Amount: " + transferAmountInLimitCcy + " in limit currency. " +
										 "Available from source: " + (fromAmount != null ? fromAmount : 0) +
										 ", Total reservedAmtInLimitCcy updated to: " + reservedAmtInLimitCcy.get());
							}
						}
					}
				}

				markInterestedCollectionsDirty();
				return true;
			}
			catch (Exception e)
			{
				log.error("CreditLine.atomicCreditTransfer: Exception during credit transfer from " + fromTransactionId +
						 " to " + toTransactionId, e);
				return false;
			}
		}
	}

	public String release( String transactionId )
	{
		String result = null;
		if ( transactionId == null )
		{
			return "Null TransactionId";
		}
		if ( cashSettlement )
		{
			Map<Currency, AtomicDouble> map = transactionId2ReservedMap.remove ( transactionId );
			if ( map != null )
			{
				for ( Currency ccy : map.keySet () )
				{
					AtomicDouble amtBefore = map.get ( ccy );
					if ( amtBefore != null )
					{
						AtomicDouble reserved = reservedAmtMap.get ( ccy );
						double oldReserveAmt = reserved != null ? reserved.get () : 0.0;
						if ( reserved != null && amtBefore.get () > 0 )
						{
							reserved.addAndGet ( - 1 * amtBefore.get () );
						}
						result = new StringBuilder ( 120 ).append ( "{tid=" )
								.append ( transactionId ).append ( ",oldReserveAmt=" ).append ( oldReserveAmt )
								.append ( ",amtBefore=" ).append ( amtBefore ).append ( ",newReservedAmt=" ).
										append ( reserved ).append ( '}' ).toString ();
					}
				}
			}
			else
			{
				log.info ( "CL.release - Not able to find the reserved amount map for txId=" + transactionId );
				result = "Unable to release txId=" + transactionId;
			}
		}
		else
		{
			Double creditReservedInLimitCcy = transactionId2ReservedCreditInLimitCcy.remove ( transactionId );
			double amtBefore = reservedAmtInLimitCcy.get ();
			if ( creditReservedInLimitCcy != null && amtBefore > 0 )
			{
				reservedAmtInLimitCcy.addAndGet ( - 1 * creditReservedInLimitCcy );
			}
			if ( creditReservedInLimitCcy != null )
			{
				result = new StringBuilder ( 120 ).append ( "{tid=" )
						.append ( transactionId ).append ( ",resAmt=" ).append ( creditReservedInLimitCcy )
						.append ( ",amtBefore=" ).append ( amtBefore ).append ( ",amtAfter=" ).
								append ( reservedAmtInLimitCcy.get () ).append ( '}' ).toString ();
			}
		}
		markInterestedCollectionsDirty();
		return result;
	}

    public void roundReservedAmount()
    {
        boolean success = false;
        int retryCount = 0;
        final int MAX_RETRIES = 10;

        while ( !success && retryCount < MAX_RETRIES )
        {
            double amtBeforeRounding = reservedAmtInLimitCcy.get();
            double amtAfterRounding = round( amtBeforeRounding );
            success = reservedAmtInLimitCcy.compareAndSet( amtBeforeRounding, amtAfterRounding );

            if ( !success )
            {
                retryCount++;
                if ( retryCount >= MAX_RETRIES )
                {
                    log.warn( "Failed to round reserved amount after " + MAX_RETRIES + " attempts. " +
                             "Current amount: " + reservedAmtInLimitCcy.get() );
                }
                try
                {
                    Thread.sleep( 1 );
                }
                catch ( InterruptedException e )
                {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    public void markInterestedCollectionsDirty(){
		for(CreditLineCollection creditLineCollection: interestedCreditLineCollections){
			creditLineCollection.markDirty();
		}
	}
	

	public boolean isCreditSuspended(){
		return creditSuspended.get();
	}
	

	public void setSuspend(boolean suspend){
		this.creditSuspended.set(suspend);
	}

    public Map<String, Double> getTradeId2ReservedAmount()
    {
    	if ( !isCashSettlement () )
		{
			Map<String, Double> result = new HashMap<String, Double>();

			// First, copy all entries
			for ( Map.Entry<String, Double> entry : transactionId2ReservedCreditInLimitCcy.entrySet() )
			{
				result.put( entry.getKey(), entry.getValue() );
			}

			// Then, round values in separate loop to avoid ConcurrentModificationException
			if ( limitCurrency != null )
			{
				for ( Map.Entry<String, Double> entry : result.entrySet() )
				{
					Double value = entry.getValue();
					if ( value != null )
					{
						entry.setValue( limitCurrency.round( value ) );
					}
				}
			}

			return result;
		}
		return null;
    }

	public Map<String, Map<Currency, AtomicDouble>> getTradeId2ReservedAmountMap()
	{
		return isCashSettlement () ? transactionId2ReservedMap : null;
	}


	public void addInterestedCreditLineCollection(CreditLineCollection collection)
	{
		interestedCreditLineCollections.add(collection);
	}

	public Collection<CreditLineCollection> getInterestedCreditLineCollections()
	{
		return interestedCreditLineCollections;
	}
    
	public void refresh(LegalEntity cple, TradingParty tp, boolean isCpoMaker )
	{
		// We reached here bcoz CPO->TP credit limit changed. So for all the subscriptions having this CPO->TP line, no matter the currency, refresh the line
		for(CreditLineCollection creditLineCollection:interestedCreditLineCollections)
		{
			try
			{
				CreditLimitSubscriptionManagerC.getInstance().refreshCreditLine(cple, tp, creditLineCollection.getCurrencyPair(), creditLineCollection.getValueDate(), isCpoMaker, limitCurrency, orgLevel );
			}
			catch(Exception e)
			{
				log.error("Error in CreditLine.refresh. CreditLineCollection=" + creditLineCollection,e);
			}
		}
	}

    private double round( double amtBeforeRounding )
    {
		if ( limitCurrency == null )
		{
			log.error( "CL.round - limitCurrency is null" );
			return amtBeforeRounding;  // Return original value if it can't round
		}
        if ( amtBeforeRounding <= 0 )
        {
            return 0.0;
        }
        else
        {
            return limitCurrency.round( amtBeforeRounding );
        }
    }

	public void cleanupOldTransactions()
	{
		synchronized ( this )
		{
			log.info ( "CL.cleanupOldTransactions - removing all transactions removed. transactionId2ReservedMap="
							+ transactionId2ReservedMap + " | transactionId2ReservedCreditInLimitCcy="
					+ transactionId2ReservedCreditInLimitCcy + ",reservedAmtMap=" + reservedAmtMap
					+ "reservedAmtInLimitCcy=" + reservedAmtInLimitCcy.get() + transactionId2ReservedMap );

			transactionId2ReservedMap.clear();
			transactionId2ReservedCreditInLimitCcy.clear();
			log.info ( "CL.cleanupOldTransactions - All transactions removed" );
		}
	}
}
