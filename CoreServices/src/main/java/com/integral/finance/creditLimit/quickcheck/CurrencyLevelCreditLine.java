package com.integral.finance.creditLimit.quickcheck;

import com.google.common.util.concurrent.AtomicDouble;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRate;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.price.fx.FXPrice;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.DealingModel;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Map;

public class CurrencyLevelCreditLine
{
    private final LegalEntity creditProviderLe;
    private final TradingParty tp;
    private final boolean cpoMaker;
	private final CurrencyPair ccyPair;
	private final IdcDate spotDate;
	private final String key;
	
	private double bidLimit;
	private double offerLimit;	
	private CreditLine creditLine;

    private FXPrice limitCcyBaseCcyPrice;
    private boolean limitCcyBase;
    private boolean limitCcyBaseInLimitBaseCcyPair;

    private boolean orphan;

    private boolean isDummyLine;

    private boolean cashSettlement;

    String displayKey;

    /**
     * Tolerance amount for reserve amount, an excess of amount will be allowed to account for rounding issues.
     */
    private static final double MIN_AMOUNT = -0.01;
	
							
	private static final Log log = LogFactory.getLog( CurrencyLevelCreditLine.class );
	private final Log creditLimitRefreshLog = LogFactory.getLog( CreditLimit.CREDIT_LIMIT_REFRESH_LOG_CATETORY );
	
	public CurrencyLevelCreditLine(LegalEntity cpl, TradingParty tp, boolean isCpoMaker, CurrencyPair ccyPair, IdcDate spotDate, double bidLimit, double offerLimit, CreditLine creditLine, boolean isDummyLine, boolean cash )
	{
		this.ccyPair = ccyPair;
		this.spotDate = spotDate;
		this.bidLimit = bidLimit;
		this.offerLimit = offerLimit;
        this.creditProviderLe = cpl;
        this.tp = tp;
        this.cpoMaker = isCpoMaker;
		this.creditLine = creditLine;
        limitCcyBase = creditLine.getLimitCurrency().isSameAs( ccyPair.getBaseCurrency() );
        initLimitCcyBaseCcyPrice();
        key = getCcyPairLevelCreditLineKey(getCreditProviderOrg().getShortName(), getCreditTradingParty().getObjectId(), ccyPair.getName(), spotDate, isCPOMaker());
        this.isDummyLine = isDummyLine;
        this.cashSettlement = cash;
        this.displayKey = new StringBuilder(100).append( cpl.getOrganization ().getShortName () ).append( '>' )
				.append( tp.getFullyQualifiedName () ).append ( ':' ) .append ( ccyPair ).append ( ':' ).append ( cpoMaker ).toString ();
    }

	public boolean isCashSettlement()
	{
		return cashSettlement;
	}

	public void setCashSettlement ( boolean cash )
	{
		this.cashSettlement = cash;
	}

	private String getCcyPairLevelCreditLineKey(String cpo, long ccId, String ccyPair, IdcDate valueDate, boolean isCpoMaker){
    	StringBuilder sb = new StringBuilder(40);
    	sb.append(cpo).append(':').append(ccId).append(':').append(ccyPair).append(':').append(valueDate.getFormattedDate(IdcDate.DD_MM_YYYY_HYPHEN))
                .append( ':').append( isCpoMaker ? 'T' : 'F' );
    	return sb.toString();
    }
    
    public boolean hasSufficientCredit(double requestedAmount, boolean isBid)
    {
    	long t1 = System.currentTimeMillis();
    	if(isDummy()){
    		return true;
    	}
        double availableLimit = ( isBid ? bidLimit : offerLimit ) - getReservedAmtInBaseCcy();
        boolean result = availableLimit - requestedAmount > MIN_AMOUNT ;
        if(log.isDebugEnabled())
        {
        	long t2 = System.currentTimeMillis()-t1;
        	log.debug( "CurrencyLevelCreditLine.hasSufficientCredit : timeTaken = " + ( t2 - t1 ) );
        }
        return result;
    }

    private double getReservedAmtInBaseCcy()
    {
		if ( creditLine.isCashSettlement () )
		{
			return isDummy() ? 0.0 : creditLine.getReserveAmountInCurrency ( ccyPair.getBaseCurrency () );
		}
		else
		{
			if ( isDummy () || creditLine.getReservedAmountInLimitCurrency () == 0.0 )
			{
				return 0;
			}
			if ( limitCcyBase )
			{
				return creditLine.getReservedAmountInLimitCurrency ();
			}
			FXRate fxRate = getLimitToBaseCcyFxRate ();
			double reservedAmtInBaseCcy = fxRate != null ? CreditUtilC.getAmount ( creditLine.getReservedAmountInLimitCurrency (), creditLine.getLimitCurrency (), fxRate ) : Double.MAX_VALUE;
			reservedAmtInBaseCcy = CreditUtilC.applyContractMultiplierOnBaseCcyAmt(reservedAmtInBaseCcy, ccyPair);

			return reservedAmtInBaseCcy;
		}
    }

    private FXRate getBaseToLimitCcyFxRate()
    {
        return limitCcyBaseCcyPrice != null ? limitCcyBaseInLimitBaseCcyPair ? limitCcyBaseCcyPrice.getBidFXRate() : limitCcyBaseCcyPrice.getOfferFXRate() : null;
    }

    private FXRate getLimitToBaseCcyFxRate()
    {
        return limitCcyBaseCcyPrice != null ? limitCcyBaseInLimitBaseCcyPair ? limitCcyBaseCcyPrice.getOfferFXRate() : limitCcyBaseCcyPrice.getBidFXRate() : null;
    }

    public boolean reserveCredit(double requestedAmount, String transactionId, boolean isBid, DealingModel dealingEntity )
	{
    	if(isDummy()){
    		return true;
    	}
        if(creditLine.isCreditSuspended())
        {
            log.warn("CCL.reserveCredit : Fail because credit is suspended");
            return false;
        }

		StringBuilder creditReserveLog = new StringBuilder("reserveCredit: TransactionId= ");
		creditReserveLog.append(transactionId).append(", requestedAmount= ").append(requestedAmount);

		double limitCcyAmount = requestedAmount;
		creditReserveLog.append(",  limitCcyBase= ").append(limitCcyBase);

        if ( !limitCcyBase )
        {
            FXRate fxRate = getBaseToLimitCcyFxRate();
            if ( fxRate != null )
            {
                limitCcyAmount = CreditUtilC.getAmount( requestedAmount, ccyPair.getBaseCurrency(), fxRate );
				creditReserveLog.append(",  limitCcyAmount before contractMultiplier= ").append(limitCcyAmount);
                limitCcyAmount = CreditUtilC.applyContractMultiplierOnReserveCredit(limitCcyAmount, ccyPair);
				creditReserveLog.append(",  baseToLimitCcyFxRate= ").append(fxRate.getRate());
            }
            else
            {
                log.info( "CCL.reserveCredit : conversion rate is not available for ccyPair=" + CurrencyFactory.getCurrencyPair( creditLine.getLimitCurrency(), ccyPair.getBaseCurrency() ) );
                return false;
            }
        }

		creditReserveLog.append(",  limitCcyAmount= ").append(limitCcyAmount);
        log.info(creditReserveLog.toString());

		synchronized (creditLine) 
		{		
			if(hasSufficientCredit(requestedAmount, isBid))
			{
				return creditLine.reserveCredit( transactionId, ccyPair.getBaseCurrency(), requestedAmount, limitCcyAmount, dealingEntity, isBid );
			}
		}
		creditLine.markInterestedCollectionsDirty();
		return false;
	}
	
		
	public String releaseAndRefresh(String transactionId)
	{		
    	if( isDummy() )
    	{
    		return null;
    	}
		//First refresh so that the latest bid n offer limits are picked up. Only then release the credit so that there wouldn't be any breach.
		refresh();
		return release( transactionId );
	}
	
	public String release( String transactionId )
	{
    	if( isDummy() )
    	{
    		return null;
    	}
		if( log.isDebugEnabled() )
		{
			log.debug( new StringBuilder( "releaseCredit: Releasing Credit for " ).append( transactionId ).append( " | CreditLine=" ).append( this ).toString() );
		}
		String result = creditLine.release( transactionId );
		return result != null ? this.displayKey + result : null;
	}
	
	
	public void addInterestedCreditLineCollection(CreditLineCollection collection){
		creditLine.addInterestedCreditLineCollection(collection);
	}	

	public Collection<CreditLineCollection> getInterestedCreditLineCollections()
	{
		return creditLine.getInterestedCreditLineCollections();
	}

	public void setCreditLine(CreditLine creditLine){
		this.creditLine = creditLine;
	}
	
	
	/**
	 * Get the fresh bid/offer limits and reset
	 */
	private void refresh()
	{	
    	if(isDummy()){
    		return;
    	}
		creditLine.refresh( this.creditProviderLe, getCreditTradingParty(), isCPOMaker());
	}
	
	public void resetCredit(double bidLimit, double offerLimit)
	{
    	if(isDummy()){
    		return;
    	}
		this.bidLimit = bidLimit;
		this.offerLimit = offerLimit;
		if(creditLimitRefreshLog.isDebugEnabled())
		{
			StringBuilder sb = new StringBuilder(150).append("Refreshed QuickCheck limits. CurrencyLevelCreditLine = ").append(this);
			creditLimitRefreshLog.debug(sb.toString());
		}

        creditLine.roundReservedAmount();
	}

	public double getMinAvailableBid(){
    	if(isDummy()){
    		return Double.MAX_VALUE;
    	}		
		return bidLimit-getReservedAmtInBaseCcy();
	}
	
	public double getMinAvailableOffer(){
    	if(isDummy()){
    		return Double.MAX_VALUE;
    	}		
		return offerLimit-getReservedAmtInBaseCcy();
	}	
	
	public int hashCode(){
		return key.hashCode();
	}
	
	public boolean equals(CurrencyLevelCreditLine currencyLevelCreditLine)
	{
		return this.key.equals(currencyLevelCreditLine.getKey());
	}
	

	public boolean isCreditSuspended(){
    	if(isDummy()){
    		return false;
    	}		
		return creditLine.isCreditSuspended();
	}
	
	public void setSuspend(boolean suspend){
		creditLine.setSuspend(suspend);
	}
	
	public void setDummy(boolean isDummyLine){
		this.isDummyLine = isDummyLine;
	}
	
	public boolean isDummy(){
		return this.isDummyLine;
	}


    /**
     *     Setting a CurrencyLevelCreditLine to orphan takes care of adding itself to the OrphanCollection of CreditLineManager
     *     Similarly resetting the orphan status will take care of automatically removing this line from the collection.
     *
      * @param isOrphan orphan
     */
    public void setOrphan(boolean isOrphan)
    {
        this.orphan = isOrphan;
        if(isOrphan){
            CreditLineManagerC.getInstance().addOrphanLine(this);
        }else{
            CreditLineManagerC.getInstance().removeOrphanLine(this);
        }
    }

    public boolean isOrphan(){
        return this.orphan;
    }

	public String toString(){
		StringBuilder sb = new StringBuilder(100);
		sb.append("CPO=").append(getCreditProviderOrg().getShortName());
		sb.append(" | TP=").append(getCreditTradingParty().getFullyQualifiedName());
		sb.append(" | ccyPair=").append(ccyPair.getName());
		sb.append(" | valueDate=").append(spotDate.getFormattedDate(IdcDate.DD_MM_YYYY_HYPHEN));
		sb.append(" | BidLimit=").append(bidLimit);
		sb.append(" | OfferLimit=").append(offerLimit);
		sb.append(" | isDummyLine=").append(isDummyLine);
        sb.append(" | isOrphanLine=").append(orphan);
		sb.append(" | ReservedAmtInBaseCcy=").append(getReservedAmtInBaseCcy()).append( " | CpoMaker=" ).append( isCPOMaker() );
		return sb.toString();
	}

    void initLimitCcyBaseCcyPrice()
    {
		limitCcyBase = creditLine.getLimitCurrency().isSameAs( ccyPair.getBaseCurrency() );
        if ( !limitCcyBase )
        {
            FXMarketDataElement fxMde = ReferenceDataCacheC.getInstance().getLiveFXMds().findSpotConversionMarketDataElement( creditLine.getLimitCurrency(), ccyPair.getBaseCurrency(), true );
            if ( fxMde != null && fxMde.getFXPrice() != null && fxMde.getFXPrice().isValidPrice() )
            {
                limitCcyBaseCcyPrice = fxMde.getFXPrice();
                FXRate fxRate = limitCcyBaseCcyPrice.getBidFXRate() != null ? limitCcyBaseCcyPrice.getBidFXRate() : limitCcyBaseCcyPrice.getOfferFXRate() != null ? limitCcyBaseCcyPrice.getOfferFXRate() : limitCcyBaseCcyPrice.getMidFXRate();
                if ( fxRate != null )
                {
					if ( fxRate.getFXRateBasis () != null )
					{
						limitCcyBaseInLimitBaseCcyPair = creditLine.getLimitCurrency ().isSameAs ( fxRate.getFXRateBasis ().getBaseCurrency () );
					}
					else
					{
						limitCcyBaseInLimitBaseCcyPair = creditLine.getLimitCurrency ().isSameAs ( fxRate.getBaseCurrency () );
					}
                }     
                if(log.isDebugEnabled()){
                	log.debug(new StringBuilder(100).append("CCL.initLimitCcyBaseCcyPrice : CcyPair=").append(ccyPair.getName()).append(" : BidRate=").
                		append(limitCcyBaseCcyPrice.getBidFXRate().getRate()).append(" : OfferRate=").append(limitCcyBaseCcyPrice.getOfferFXRate().getRate()).toString());
                }
            }
            else
            {
            	String ccyPairStr = creditLine.getLimitCurrency() + "/" + ccyPair.getBaseCurrency();
            	CreditLineManagerC.noRatesCurrencyPairs.add( ccyPairStr );
            	if ( log.isDebugEnabled() )
            	{
					log.debug( "CCL.initLimitCcyBaseCcyPrice : conversion rate is not found for " + ccyPairStr );
				}
            }
        }

    }

    public LegalEntity getCreditProviderLe(){
        return this.creditProviderLe;
    }

    public Organization getCreditProviderOrg(){
        return this.creditProviderLe.getOrganization();
    }

    public TradingParty getCreditTradingParty(){
        return this.tp;
    }

    public boolean isCPOMaker(){
        return this.cpoMaker;
    }

    public IdcDate getValueDate(){
        return spotDate;
    }


    public String getKey(){
    	return key;
    }

    //************************* Below Area for Diagnostics page************************
	
	public double getBidLimit(){
		return bidLimit;
	}
	
	public double getOfferLimit(){
		return offerLimit;
	}

    public double getConsumedAmount()
    {
        return ccyPair.getBaseCurrency().round( getReservedAmtInBaseCcy() );
    }

    public String getCreditProvider(){
		return getCreditProviderOrg().getShortName();
	}
	
	public String getCreditCounterparty(){
		return getCreditTradingParty().getFullyQualifiedName();
	}

    public String getCreditCounterpartyOrg(){
        return getCreditTradingParty().getLegalEntityOrganization().getShortName();
    }

	public long getCreditCounterpartyId(){
		return getCreditTradingParty().getObjectId();
	}
	
	public String getValueDateStr(){
		return spotDate.getFormattedDate(IdcDate.DD_MM_YYYY_HYPHEN);
	}
		
	public CurrencyPair getCurrencyPair(){
		return ccyPair;
	}

    public Map<String, Double> getTradeId2ReservedAmount()
    {
        return creditLine.getTradeId2ReservedAmount();
    }

    public Map<String, Map<Currency, AtomicDouble>> getTradeId2ReservedAmountMap()
	{
		return creditLine.getTradeId2ReservedAmountMap ();
	}

    public Currency getLimitCurrency()
    {
        return creditLine.getLimitCurrency();
    }
}