package com.integral.finance.creditLimit.quickcheck;

import java.util.List;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.creditLimit.CreditEntity;
import com.integral.finance.currency.CurrencyPair;
import com.integral.model.dealing.DealingModel;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.time.IdcDate;
import com.integral.workflow.dealing.fx.FXDealingLimit;


public interface CreditLineCollection 
{	
	double getAvailableCredit(boolean isBid, boolean forOrderMatch);

	FXDealingLimit getAvailableDealingLimit(boolean forOrderMatch);

    ReserveCreditResult reserveCredit(double requestedAmount, boolean isBid, String transactionId, List<CreditEntity> creditEntities, DealingModel dealingEntity);
		
	void calculateMinAvailableAmounts();

	void forceCalculateMinAvailableAmounts();
	
	void reEvaluateSuspensionStatus();
			
	void resetAllCreditLines(CurrencyLevelCreditLine[] currencyLevelCreditLines);
	
	boolean isEmtpy();
	
	void releaseAndRefresh(String transactionId, List<CreditEntity> creditEntities);
	
	LegalEntity getFILE();
	
	LegalEntity getLPLE();
	
	void setLPLE(LegalEntity lpLe);
	
	CurrencyPair getCurrencyPair();
	
	IdcDate getValueDate();
	
	void setValueDate(IdcDate valueDate);
	
	boolean forSameValueDate(IdcDate aValueDate);
	
	void markDirty();

	/**
	 *
	 * @return true if required relationships do not exist for the credit path between counterparties.
	 */
	boolean hasBrokerCreditLines();

	void setBrokenCreditLines(boolean hasBrokenCreditLines);

	LP_FI getLP_FI();
}
