# This property file is used to define which configuration property prefix belongs which MBean class. The format is property key=MBean class simple name coma separated list.
IDC.BrokerAdaptor.MaxTiers=BrokerAdaptor,PriceMakingMBeanC
Idc.SEF.Currency.BusinessCenter=SEFMBeanC
IDC.IS.UseProviderTradingPartyDefaultLE=OrganizationMBeanC
Idc.DistributedAdaptor.VirtualServerToStreamMapping.UseAdmin=ISMBeanC
Idc.BrokerAdaptor.UseNewExecutionScheduler=BrokerAdaptor
Idc.BrokerAdaptor.BackToBackBA.SingleSweep=BrokerAdaptor
IDC.BrokerAdaptor.Customer.InstantResponse=BrokerAdaptor
Idc.BrokerAdaptor.Adjust.Forward.Points=BrokerAdaptor
IDC.BrokerAdaptor.MisMatch.Swap.Spot.Cover=BrokerAdaptor
IDC.BrokerAdaptor.Zero.FullFill.TIF=BrokerAdaptor
Idc.BrokerAdaptor.MaxTiers.FullBook=BrokerAdaptor
Idc.BrokerAdaptor.Spread.UseCustomerOrderAmt=BrokerAdaptor
Idc.BrokerAdaptor.MinPublicationInterval=BrokerAdaptor
Idc.BrokerAdaptor.HomeCurrency=BrokerAdaptor
Idc.BrokerAdaptor.CustomCurrencyPair.Multiplier=BrokerAdaptor
IDC.BrokerAdaptor.MDS.PreSpotPricingTenor.Mode=ConfigurationMBean
IDC.BrokerAdaptor.MDS.ON.TN.Pricing.Modification.Enabled=ConfigurationMBean
IDC.BrokerAdaptor.MarkToMarket.Reference.Tenor=ConfigurationMBean
IDC.BrokerAdaptor.MarkToMarket.Settlement.MDS=ConfigurationMBean
IDC.BrokerAdaptor.MarkToMarket.MDE.StaleTime=ConfigurationMBean
Idc.BrokerAdaptor.Venue.Clob.Enabled=BrokerCustomConfiguration
Idc.BrokerAdaptor.Venue.RiskNet.Enabled=BrokerCustomConfiguration

Idc.BrokerAdaptor.HourglassPricing.Enabled=BrokerProvisionConfig
Idc.BrokerAdaptor.CopyTrade.Enabled=BrokerProvisionConfig
Idc.BrokerAdaptor.ExecutionMethod.Display.CoverOnly=BrokerProvisionConfig
IDC.BrokerAdaptor.Std.Tenors=BrokerProvisionConfig

IDC.IS.ORDER.Use.MarketRange.Percentage=ISMBeanC
Idc.IS.Allow.Clob.Rates.Match=OrganizationMBeanC
Idc.IS.Allow.RiskNet.Rates.Match=OrganizationMBeanC
Idc.IS.Allow.Single.LP.Test=OrganizationMBeanC
Idc.IS.UseMaskLP.DefaultLE=OrganizationMBeanC
Idc.IS.ProvisioningRoundingFactor=ISMBeanC
IDC.IS.UsePBTradingPartyDefaultLE=OrganizationMBeanC
Idc.PrimeBroker.StreamLPLE=OrganizationMBeanC
IDC.IS.FXI.SUPPORTED.VERSION=ClientConf
Idc.IS.OBOWorkflow.Enabled=ISMBeanC
Idc.SEF.NET.NDF.Button.Show=SEFMBeanC
IDC.IS.STGRD.POLL.SWITCH=ClientConf
IDC.IS.CLIENT.BASED.ORDER.MATCHING=ISMBeanC
IDC.IS.RFS.SERVER.URL=ClientConf
IDC.IS.RFS.SERVER.FALLBACK.TO.ESP.SERVER=ClientConf
JMSPROXY.POLLING.INTERVAL=ClientConf
Idc.JMSPROXY.Poll.Or.Stream=JMSProxyMBeanC
Idc.JMSPROXY.StreamRequestInterval=JMSProxyMBeanC
IDC.IS.WAITTIME.DISP.PENDING.ALERT=ClientConf
Idc.OrganizationAdmin=OrganizationMBeanC
Idc.Aggregation.Method=ClientConf
IDC.IS.FXI.SINGLELP=ClientConf
IDC.IS.FXI.FXIPRIME=ClientConf
IDC.IS.FXI.FXINEWPRO=ClientConf
IDC.IS.FXI.FORCECPTYCHANGE=ClientConf
IDC.IS.FXI.FORCECPTYCHANGELIST=ClientConf
IDC.IS.ClientMaxPeriod=ClientConf
IDC.IS.PriceTierProvider=ClientConf
Idc.Admin.LegalLink.Enabled=ClientConf
Idc.Client.SessionTimeout=ClientConf
Idc.Client.ConfirmationSessionTimeout=ClientConf
IDC.IS.OrderSync.Interval=ClientConf
Idc.Client.DisableTradingDuration=ClientConf
Idc.Client.FullBook.Max.Tiers=ClientConf
Idc.Client.MarketDepth.Max.Tiers=ClientConf
IDC.IS.Amend.Trade.DealtAmount.Percentage=ClientConf
IDC.IS.Amend.Trade.Rate.Percentage=ClientConf
IDC.IS.Amend.Post.Trade.STP.Message.Type=ClientConf
IDC.IS.Amend.Trade.Price.Modification.MaxTime.InMin=ClientConf
IDC.IS.Amend.Trade.NumberOf.Price.Modification.Allowed=ClientConf
IDC.IS.Allocate.Trade.ByTaker.Allowed=ClientConf
IDC.IS.Trades.Bulk.Cancelled.By.Maker.Allowed=ClientConf
IDC.IS.Client.RequestId.Validation.Not.Required=ClientConf
Idc.STP.MT300.Field72.Template=ClientConf
Idc.Aggregation.TierCurrency=ClientConf
Idc.JMSPROXY.MessagesPerCycle.And.CycleInMilliseconds=JMSProxyMBeanC
Idc.JMSPROXY.HEARTBEAT.RESEND.DELAY=JMSProxyMBeanC
Idc.JMSPROXY.MinorCycleTime=JMSProxyMBeanC
IDC.IS.JMSPROXY.THROTTLE.CONFIG=JMSProxyMBeanC
Idc.JMSPROXY.ACK.RESEND.DELAY=JMSProxyMBeanC
Idc.JMSPROXY.ACK.CACHE.LIFE=JMSProxyMBeanC
Idc.JMSPROXY.ACK.MAX.NUM.RETRY=JMSProxyMBeanC
IDC.IS.FXIAPI.WEBCLIENT.MAIN.SUPPORTED.VERSION=APIMBeanC
Idc.SSO.HTML.Admin.Url=SSOMBeanC
Idc.SSO.HTML.MIS.Url=SSOMBeanC
IDC.IS.CLIENT.SESSION.TIMEOUT=ClientConf
Idc.JMSPROXY.Poll.NumberOfMessages=JMSProxyMBeanC
Idc.Core.App.isGZipEnabled=ApplicationMBeanC
IDC.Aggregation.Log=AggregationServiceMBeanC
IDC.IS.CLIENT.DOWNLOAD.SIZE=ClientConf
IDC.IS.Client.MaxDealDownloadDays=ClientConf
IDC.IS.SHOW.REJECTIONS=ClientConf
Idc.Aggregation.TierSizes=ClientConf
idc.SuppressPriceEmail.Request.RFS=TradeEmailMBeanC
IDC.RFS.SPREAD.ENABLED=ISMBeanC
idc.SuppressTradeEmail.Request=TradeEmailMBeanC
Idc.Price.Making.Keep.Price.Improvement.Enabled=AdminWebServicesMBeanC
IDC.ADMIN.PRICEPROVISION.SALES.COUNTERPARTY.HIDE=AdminWebServicesMBeanC
IDC.RW.Trade.Bridge.Org.Mapping=TradeBridgeConfig
IDC.IS.WARMUP.DISPLAY.ORDER.PREFERRED.MAKER.LIST=WarmUpTradeMBeanC
Idc.MidRate.Calc.Provider.Enabled=ISMBeanC
Idc.MidRate.Calc.Customer.Enabled=ISMBeanC
Idc.MidRate.Calc.Broker.Enabled=ISMBeanC
Idc.MidRate.Calc.Broker.TwoWay.Conversion.Enabled=ISMBeanC
Idc.MidRate.Send.Provider.Enabled=ISMBeanC
Idc.MidRate.Send.Customer.Enabled=ISMBeanC
Idc.Multihost=MultihostMBean
IDC.PERSISTENT.ORDER.CANCELLATION.DISCONNECT=OrderServiceMBeanC
Idc.OrderAdaptor.AmendOrderEnabled=OrderServiceMBeanC
IDC.IS.ORDER.PRICEADJUSTMENT.PIPS=OrderConfiguration
Idc.Orders.IOC_FOK.ExpiryTime=OrderConfiguration
Idc.Orders.IOC_FOK.ExpiryTime.BrokerCustomers=OrderConfiguration
Idc.DisplayedOrders.Sanity.Filter.Enabled=OrderConfiguration
Idc.Oms.RouteFixingOrderToOMSWithAutoAccept=OrderConfiguration
IDC.Broker.CoverTradeLE=BrokerAdaptor
IDC.BrokerAdaptor.OffMarketExclude.ReferenceProviders=BrokerAdaptor
IDC.BrokerAdaptor.Quote.Tier=BrokerAdaptor
Idc.BrokerAdaptor.UseCustomerExpirationTime=BrokerAdaptor
Idc.BrokerAdaptor.Customer.IOC_FOK.ExpirationTime=BrokerAdaptor
IDC.IS.SubmitOrders.MinExpiryTime=BrokerAdaptor,ISMBeanC
Idc.BrokerAdaptor.NonCustomerTIF=BrokerAdaptor
IDC.BrokerAdaptor.MinQuoteSize=BrokerAdaptor
Idc.SEF.FIX.MidRate.in.SSP.Quotes=SEFMBeanC
Idc.SEF.FIX.IncludeUTI=SEFMBeanC
Idc.SEF.WL.UseSEFLE=SEFMBeanC
Idc.TradeClsf.Product.Mapping=TradeConfiguration
IDC.Use.Create.Event.Portfolio.Trade.STP=TradeConfiguration
IDC.Aggregation.Execution.Rule.Enabled=AggregationServiceMBeanC
IDC.Aggregation.Quote.Publication.Interval=AggregationServiceMBeanC
IDC.Aggregation.Quote.BestBidOffer.Publication.Interval=AggregationServiceMBeanC
IDC.Aggregation.Quote.Tier=AggregationServiceMBeanC
IDC.Aggregation.Term.RoundingFactor=AggregationServiceMBeanC
IDC.Aggregation.Term.DisplayAmountInBase=AggregationServiceMBeanC
IDC.Aggregation.OffMarketExclude.ReferenceProviders=AggregationServiceMBeanC
IDC.Aggregation.ShowProviderName=AggregationServiceMBeanC,FIXClientConfig
IDC.Aggregation.MultiProviderFilter.Enabled=AggregationServiceMBeanC
IDC.Aggregation.MultiProviderFilter.StalenessCheck.Enabled=AggregationServiceMBeanC
IDC.Aggregation.MultiProviderFilter.OffMarketFilter.Enabled=AggregationServiceMBeanC
IDC.Aggregation.MultiProviderFilter.InvertedRateFilter.Enabled=AggregationServiceMBeanC
IDC.Aggregation.MultiProviderFilter.InvertedRateTolerance=AggregationServiceMBeanC
IDC.Aggregation.Cached.Quote.On.Subscription=AggregationServiceMBeanC
IDC.Aggregation.RiskNet.Subscription.Enabled=AggregationServiceMBeanC
IDC.Aggregation.ClientTag.Mapping=AggregationServiceMBeanC
IDC.Aggregation.FullAmountAggregationEnabled=AggregationServiceMBeanC
IDC.POSSERVICE.ALERT.ORG=PositionServiceConfigurationC
Idc.MarketDepth=FIXClientConfig
Idc.EMS.Book.Size=EMSMBeanC
Idc.EMS.Book.TreeBased=EMSMBeanC
Idc.EMS.Book.InstrumentClassificationsForTreeBased=EMSMBeanC
Idc.EMS.Book.MaxTiersPerProvider=EMSMBeanC
Idc.EMS.MarketCheck.VwapCalculationEnabled=EMSMBeanC
Idc.EMS.MQFB.AcceptancePriceSelectorVersion=EMSMBeanC
Idc.BrokerAdaptor.PIGiveBack.MT.Enabled=BrokerCustomConfiguration
Idc.BrokerAdaptor.PIGiveBack.MP.Enabled=BrokerCustomConfiguration
Idc.BrokerAdaptor.PIGiveBack.MT.Percentage=BrokerCustomConfiguration
IDC.IS.SubmitOrders=ISMBeanC
Idc.makeruser.maskedLP=ISMBeanC
IDC.RW.GridApi.Config=TradeBridgeConfig
Idc.RateFilter.MaximumSpread=FilterConfiguration
Idc.RateFilter.MaximumPipsDeviation=FilterConfiguration
Idc.RateFilter.MaximumPipsPercent=FilterConfiguration
IDC.Aggregation.Clob.Subscription.Enabled=AggregationServiceMBeanC
IDC.Aggregation.DisplayName.For.FMAProviders=AggregationServiceMBeanC
Idc.BrokerAdaptor.RFS.ESPMDS.ESPSpreading.Enabled=ISMBeanC
IDC.BrokerAdaptor.ReturnMarketOrderRate=BrokerCustomConfiguration
IDC.IS.Providers=ALL
Idc.BrokerAdaptor.SyntheticCross.DealtCurrency=BrokerAdaptor
IDC.IS.ClobRoutingEnabled=ISMBeanC
Idc.PrimeBroker.EnableCoverTradeWorkflow=ISMBeanC
IDC.IS.Directed.Order.TradingVenue.Name=REXConfig
Idc.Order.Route.UnmatchedAmount.To.Clob.Enabled=AdaptorConfiguration,OrderConfiguration
Idc.Unity.Enabled=APIStreamConfig
Idc.BrokerAdaptor.OrderEntryRestriction=ConfigurationMBeanC
Idc.BrokerAdaptor.OrderEntryRestrictionExclude=ConfigurationMBeanC
Idc.BrokerAdaptor.PeriodicMarketDataSet.Enabled=ConfigurationMBeanC
Idc.DisplayedOrders.Sanity.Filter.Percentage=OrderConfiguration
IDC.Aggregation.MultiProviderFilter.StalenessCheck.Interval=AggregationServiceMBeanC
Idc.BrokerAdaptor.PIGiveBack.MP.Percentage=BrokerCustomConfiguration
Idc.FXIAPI.Permission=PermissionMBeanC
Idc.Excel.Session.Timeout=PricingConfigurationC
Idc.PriceProvision.Fees.Enabled=PricingConfigurationC
IDC.BrokerAdaptor.MaxActiveStreams==PriceConfigurationMBeanC
Idc.BrokerAdaptor.Enable.MakerPortal.On.AdminServer=PriceConfigurationMBeanC
Idc.BrokerAdaptor.SyntheticCross.ExposeRfsExecRules.Enabled=PriceConfigurationMBeanC
Idc.BrokerAdaptor.SyntheticForward.Enabled=PriceConfigurationMBeanC
IDC.Broker.Providers.Hidden=BrokerCustomConfiguration
IDC.Broker.Providers.Excluded=BrokerCustomConfiguration
IDC.ORDER.Providers.Excluded.Randomizer=BrokerCustomConfiguration
IDC.IS.Broker.Providers.Spreaded=BrokerCustomConfiguration
IDC.Broker.Providers.Venue.Price=BrokerCustomConfiguration
IDC.Broker.Providers.Venue.Order=BrokerCustomConfiguration
Idc.BrokerAdaptor.KeepLPPriceProvisionGiveBackSpreadImprovement=BrokerCustomConfiguration
Idc.BrokerAdaptor.CptyOrgForBookAPositionOffset=BrokerCustomConfiguration
Idc.BrokerAdaptor.CptyOrgForWarehousingCoverTradingRisk=BrokerCustomConfiguration
Idc.BrokerAdaptor.BookNameForWarehousingCoverTradingRisk=BrokerCustomConfiguration
Idc.BrokerAdaptor.AdditionalMatchingRangeToProvider=BrokerCustomConfiguration
Idc.BrokerAdaptor.KPIReferenceRateTypeBasedCalculationEnabled=BrokerCustomConfiguration
Idc.BrokerAdaptor.RegularSizedCustomerFillsEnabled=BrokerCustomConfiguration
Idc.BrokerAdaptor.OverFillProtectionEnabled=BrokerCustomConfiguration
Idc.EMS.Vwap.Price.Protection.Range.In.Basis.Points=EMSMBeanC
Idc.Netting.QuoteConv=NettingMBeanC
Idc.Netting.Display.QuoteConv=NettingMBeanC
Idc.Netting.Portfolio.Name.Validation.Enabled=NettingMBeanC
Idc.Netting.MarketData.Request.Tiers=NettingMBeanC
Idc.Netting.SpotOrders.GTD.ExpiryTime=NettingMBeanC
Idc.Netting.Apply.Allocation.Spread=NettingMBeanC
Idc.Netting.Orders.RfsWaitTime=NettingMBeanC
Idc.Netting.Orders.RfsQuoteWaitTime=NettingMBeanC
Idc.Netting.Orders.TrueupWaitTime=NettingMBeanC
Idc.Netting.Fee.Reduction=NettingMBeanC
Idc.Netting.GiveBack.Spread.When.Charging.Fee=NettingMBeanC
Idc.Netting.Manual.Fsr.Acceptance=NettingMBeanC
Idc.Netting.Sanity.Threshold=NettingMBeanC
Idc.Netting.Import.DifferentHeader.allowed=NettingMBeanC
Idc.Netting.Export.DifferentHeader.allowed=NettingMBeanC
Idc.Netting.Import.Actual.Default.Header.Map=NettingMBeanC
Idc.Netting.Import.Required.fields=NettingMBeanC
Idc.Netting.Export.Actual.Default.Header.Map=NettingMBeanC
Idc.Staging.Import.DifferentHeader.allowed=NettingMBeanC
Idc.Staging.Export.DifferentHeader.allowed=NettingMBeanC
Idc.Staging.Import.Actual.Default.Header.Map=NettingMBeanC
Idc.Staging.Import.Required.fields=NettingMBeanC
Idc.Staging.Export.Actual.Default.Header.Map=NettingMBeanC
Idc.Staging.ManagerC.Account.Map=NettingMBeanC
Idc.Staging.Account.CheckCaseSenitive=NettingMBeanC
Idc.Netting.StagingArea.MaxSize=NettingMBeanC
Idc.Netting.Trueup.ESP.SPOT=NettingMBeanC
Idc.Netting.ImportExport.Translator=NettingMBeanC
Idc.Netting.Send.Outrights.For.MissingMDS=NettingMBeanC
Idc.Netting.Validate.Quotes.For.MissingMDS=NettingMBeanC
Idc.Netting.Export.File.Prefix=NettingMBeanC
Idc.Netting.Export.AdditionalFields.Enabled=NettingMBeanC
Idc.Staging.Import.DraftOrders.Enabled=NettingMBeanC
Idc.Netting.Export.C510Translator.BrokerCode=NettingMBeanC
Idc.Netting.Export.Excluded.Trade=NettingMBeanC
Idc.Netting.Only.Allow.CoverTradingEnabledCC=NettingMBeanC
Idc.Netting.CC.DEFUALT=NettingMBeanC
Idc.Netting.Enable.Fixing.Workflow.For.NonFixing.Orders=NettingMBeanC
Idc.Netting.Only.SpotTradingEnabled.Accounts=NettingMBeanC
Idc.Netting.GTM.OrderType.Default=NettingMBeanC
Idc.Netting.GTM.MarketRange.Default=NettingMBeanC
Idc.Netting.Use.UserPreferences=NettingMBeanC
Idc.Netting.Validations.CheckActiveUser=NettingMBeanC
Idc.Staging.Validations.CheckCC=NettingMBeanC
Idc.Netting.Auto.Execution.OrgStrategy.ExecutionUser=NettingMBeanC
Idc.Netting.Portfolio.NoCover.Providers.Enabled=NettingMBeanC
Idc.Netting.Portfolio.Spot.Resubmit.NoCover.Usd.Amount=NettingMBeanC
Idc.Netting.Portfolio.Trueup.Resubmit.NoCover.Usd.Amount=NettingMBeanC
Idc.Netting.Portfolio.Trueup.NoCover.Usd.Amount=NettingMBeanC
Idc.Netting.Portfolio.Trueup.Skip.Usd.Amount=NettingMBeanC
Idc.Netting.Portfolio.NoCover.ProviderList=NettingMBeanC
Idc.Netting.Portfolio.Workflow.Skip.Steps=NettingMBeanC
Idc.Netting.Auto.Execution.Validations.GTMOrders.MaxAmountInUSD=NettingMBeanC
Idc.Netting.Auto.Execution.Validations.GTM.MaxAllowedSpread=NettingMBeanC
Idc.Netting.Org.level.Netting.Fraction.Enabled=NettingMBeanC
Idc.Netting.Exclude.Trades.Of.All.Value.Dates.For.CcyPair=NettingMBeanC
Idc.Order.Aggregated.Fill.Price.Improvement.Percentage.To.Customer=AdaptorConfiguration,OrderConfiguration
Idc.MET.PublicURL=ISMBeanC
Idc.FCS.ThreadPool.Initial.Size=FIXClientConfig
Idc.FCS.ThreadPool.Max.Size=FIXClientConfig
Idc.FCS.ThreadPool.KeepAliveTime=FIXClientConfig
Idc.FCS.ThreadPool.WorkQueue.Size=FIXClientConfig
Idc.FCS.SendSTPOnHeartbeat=FIXClientConfig
Idc.FCS.Channel=FIXClientConfig
Idc.FCS.Validation.QuoteSession=FIXClientConfig
Idc.IS.Integral.UTI.Namespace.MaxLength=FIXClientConfig
Idc.IS.Integral.USI.Namespace.MaxLength=FIXClientConfig
Idc.IS.Integral.UTI.MaxLength=FIXClientConfig
Idc.IS.Integral.USI.MaxLength=FIXClientConfig
Idc.DealOrder.Oracle.Persistence.Disabled=TradeConfiguration
Idc.DealOrder.Spaces.Persistence.Enabled=TradeConfiguration
IDC.system.startupClass.ApplicationStartup=Server
IDC.system.shutdownClasses=Server
IDC.IS.RTN.Generation.IntegralMTF.LEI.Enabled=Server
IDC.IS.MTF.RTN.Generation.Enabled=Server
IDC.IS.ALERT=AlertMBeanC
Idc.Rex.Supported.Price.Source=RexCommonMBeanC
Idc.MV.Host.Venue.Name=RexCommonMBeanC
Idc.Admin.LegalLink=ClientConf
Idc.SSO.BrokerAdmin.Url=SSOMBeanC
Idc.SSO.BrokerAdmin.Show=SSOMBeanC
IDC.STP.FinXMLV20.UseSettlementCode=TradeConfiguration
IDC.TRADEEMAIL.BRAND_NAME=TradeEmailMBeanC
IDC.TRADEEMAIL.COPYRIGHT=TradeEmailMBeanC
IDC.TRADEEMAIL.BACKGROUND.IMAGE.ENABLED=TradeEmailMBeanC
IDC.TRADEEMAIL.BACKGROUND.IMAGE=TradeEmailMBeanC
IDC.TRADEEMAIL.LOGO.ENABLED=TradeEmailMBeanC
IDC.TRADEEMAIL.LOGO=TradeEmailMBeanC
IDC.IS.FXIAPI.WEBCLIENT=APIMBeanC
Idc.Rex.API.version=DirectedOrderCommonMBean
Idc.DirectedOrder.Serializable.version=DirectedOrderCommonMBean
Idc.Staging.Service.Enabled=StagingServiceConfig
Idc.Staging.Service.FirstOrder.EmailAddress=StagingServiceConfig
Idc.Staging.Service.Orders.MAX_SIZE=StagingServiceConfig
Idc.Staging.Service.Order.SenderSubID.As.Account=StagingServiceConfig
IDC.BrokerAdaptor.DisableVWAP.IfMultiFill.Enabled=PriceMakingMBeanC
IDC.TradingPartyListPageDisplayExternalId=AdminWebServicesMBeanC
IDC.BrokerAdaptor.IsSyntheticFullBook.Enabled=PriceMakingMBeanC
IDC.BrokerAdaptor.SyntheticFullBookMTFOK.Enabled=PriceMakingMBeanC
IDC.BrokerAdaptor.FullAmountMultiTier.Enabled=PriceMakingMBeanC
IDC.BrokerAdaptor.FullAmountMultiQuote.Enabled=PriceMakingMBeanC
IDC.BrokerAdaptor.IsBenchmarkAggregation.Enabled=PriceMakingMBeanC
IDC.ADMIN.CREDIT.DISPLAY.SETTLEMENTCODE=AdminWebServicesMBeanC,CreditConfiguration
IDC.LegalEntityListPageDisplayExternalId=AdminWebServicesMBeanC
Idc.BrokerAdaptor.PriceMaking.Hide.Fma.Providers=AdminWebServicesMBeanC
IDC.BrokerAdaptor.VwapExecution.Enabled=PriceMakingMBeanC
Idc.BrokerAdaptor.DelayedOrderMatch.Enabled=PriceMakingMBeanC
Idc.BrokerAdaptor.PriceMaking.Configuration.Enabled=PriceMakingMBeanC
IDC.IS.ClientTag.Enabled.Broker=ISMBeanC,AdaptorConfig,DistributedAdaptorConfig,TradingVenueAdaptorConfig
Idc.Venue.Customer.Categories=RexCommonMBeanC
IDC.MARKET.DATASET.REALTIME.STALE.ELEMENTS.REMOVE.ENABLED=MarketDataConfiguration
Idc.Venue.Supported.Streams=RexCommonMBeanC
Idc.Venue.RegularSize=RexCommonMBeanC
IDC.IS.WARMUP.DISPLAY.ORDER.PREFERRED.TAKER.LIST=WarmUpTradeMBeanC
IDC.JMSPROXY.SECONDARY.POLLING.INTERVAL.FXInside=ClientConf
IDC.JMSPROXY.SECONDARY.POLLING.INTERVAL=ClientConf
Idc.MarketMaker.QuoteConvention=MarketMakerConfig
IDC.MM.MarketMakerServicesStartupEnabled=MarketMakerConfig
IDC.MM.TOBBasedCorePriceService.Class=MarketMakerConfig
IDC.MM.TOBBasedCorePriceService.DynamicUser=MarketMakerConfig
IDC.YM.ABRouter.Routing.Type=ABRouterConfig
IDC.YM.ABRouter.BBook.Amount=ABRouterConfig
IDC.RM.RiskWarehouse=RWProvisionConfigC
IDC.RM.RiskNet=RWProvisionConfigC
IDC.RM.MaxUnexposedLimit=RWProvisionConfigC
IDC.RM.EOD=RWProvisionConfigC
IDC.RM.Warehouse=RWProvisionConfigC
IDC.RM.ProvisionBuilderEnabled=RWProvisionConfigC
IDC.RM.YMBasedBBooking.CoverExecMethod=RWProvisionConfigC
IDC.UPNL.Calculation.Mode=RWProvisionConfigC
IDC.RM.Server=RMServerConfig
IDC.RM.RW.CB=RWCircuitBreakerConfig

IDC.REFERENCE.DATA.PURGE.ORGS=DealingDataPurgeConfiguration
IDC.REFERENCE.DATA.ORGANIZATION.FULLINACTIVATION.ENABLED=DealingDataPurgeConfiguration

IDC.RM.EMS=EMSConfig

Idc.TakerOrgControl.QueryFrom.Mongo.Enabled=ServerRuntime
Idc.TakerOrgControl.UpdateTo.Oracle.Enable=ServerRuntime
Idc.TakerOrgControl.UpdateTo.Mongo.Enabled=ServerRuntime
IDC.Broker.BenchMark.PriceLimit=BrokerCustomConfiguration
IDC.Broker.MaskLP.Avoid.DoubleHit=BrokerCustomConfiguration
Idc.BrokerAdaptor.OCX.Avoid.DoubleHit=BrokerCustomConfiguration
Idc.BrokerAdaptor.FullAmount.Stream.Tolerance=BrokerCustomConfiguration
Idc.OrderAdaptor.FullAmount.Stream.Tolerance=OrderConfiguration
Idc.Forward.Currency.Supported.Tenors=ServerRuntime
Idc.Streaming.NonSpot.Enabled=ServerRuntime
Idc.Streaming.Swap.Trading.Enabled=ServerRuntime
Idc.IS.FullAmount.MinOrderSize=ISMBeanC
Idc.IS.OBOWorkflow.SpreadModel=ISMBeanC
Idc.BrokerAdaptor.SynX.CvrExRate.Use.LpFilledRate=BrokerAdaptor
Idc.BrokerAdaptor.EspRfs.EvenSwap.NoCover.Enabled=BrokerAdaptor
Idc.BrokerAdaptor.MRFQ.ExpiredRequests.Cache.Size=PriceConfigurationMBeanC
Idc.BrokerAdaptor.MRFQ.TimeSplit=PriceConfigurationMBeanC
Idc.SendAlert.Enabled=AlertConfig
#--------------------6.4 Release-------------------
Idc.MarketMaker.Publish.Interval=MarketMakerConfig
Idc.MarketMaker.Force.Publish.Interval=MarketMakerConfig
Idc.MarketMaker.Aggregation.Interval=MarketMakerConfig
Idc.Rex.Ems.ReferenceData.Supported.Members=RexCommonMBeanC
Idc.Rex.Ems.Provider.LiquidityRegen=RexCommonMBeanC
Idc.Rex.Ems.CM.Priority=RexCommonMBeanC

IDC.YM.WEB.API.ResponseLoggingFilterEnabled=YMWebAPIConfig
IDC.YM.WEB.API.WarehouseQueryTimestampFormat=YMWebAPIConfig
Idc.SSO.OIDC.Service.Cookies.Domain.Name=CASMBeanC
Idc.SSO.SAML.successurl=CASMBeanC
Idc.SSO.SAML.failureurl=CASMBeanC
Idc.SSO.SAML.metadata=CASMBeanC
Idc.SSO.SAML.Service.Brand_Mapping=CASMBeanC

Idc.SSO.OIDC.Service.Provider.Authentication.URL=CASMBeanC
Idc.SSO.OIDC.Service.Provider.Authorization.URL=CASMBeanC
Idc.SSO.OIDC.Service.Provider.Redirect.URL=CASMBeanC
Idc.SSO.OIDC.Service.localhost=CASMBeanC
Idc.SSO.OIDC.Service.CustomerLogin=CASMBeanC
Idc.SSO.OIDC.Service.Provider.ClientId=CASMBeanC
Idc.SSO.OIDC.Service.Provider.Client.Secret=CASMBeanC
Idc.SSO.OIDC.Service.Provider.SSOID.Field=CASMBeanC
Idc.MIFID.MTF.Validations=MiFIDMBeanC
IDC.MIFID.CaptureBenchmarkMid=MiFIDMBeanC
Idc.BrokerAdaptor.MTF.TradeReport.VenueExecTime=MiFIDMBeanC
Idc.MIFID.MTF.Strict.Validations=MiFIDMBeanC
IDC.MIFID.POSTTRADEDEFERRAL=MiFIDMBeanC
IDC.MIFID.PRETRADEWAIVER=MiFIDMBeanC
Idc.MIFID.ConfirmOnClientAckTimeout=MiFIDMBeanC
#----------------------------6.7.4 Release -----------------------#
Idc.Spread.InQuote=TradeConfiguration
Idc.RFS.Account.Support=TradeConfiguration
Idc.Account.SupportSTP=TradeConfiguration
Idc.IS.Log.Outbound.GridAPI.Message=ISMBeanC
#-----------------------------6.8 Release --------------------------#
IDC.IS.QuoteAmountRoundingMode=ISMBeanC
Idc.CDQ.Order.MakerOrder.OnSubmit=ISMBeanC

Idc.Mvp.AccountBalance.Publish.Enabled=MVCreditConfigMBeanC
Idc.Mvp.CreditPublisher.Cluster.Enabled=MVCreditConfigMBeanC
Idc.Aggregation.FAStreams.Only=FIXClientConfig
Idc.BrokerAdaptor.New.Subscriptions=ISMBeanC
Idc.BrokerAdaptor.ESPRFS.MatchedSwap.UseTobSpotRate=BrokerAdaptor
Idc.BrokerAdaptor.NewCopyTrade.Enabled=BrokerProvisionConfig
Idc.BrokerAdaptor.CopyTrade.UseNewChannel=BrokerProvisionConfig

Idc.Audit.Alert=AuditAlertConfiguration
Idc.BrokerAdaptor.EMS.Propagate.MarketExecution=TradeConfiguration
Idc.ManualTrade.MakerOrg.Validation.Enabled=TradeConfiguration
IDC.IS.UseCoveredOrg.SubAccountId.ESPRFS=ISMBeanC

IDC.DateGeneration.HolidayCalendar.HolidayCacheBeginYear=Finance
IDC.GlobalBusinessDateCutOffTime=Finance
IDC.GlobalBusinessDateCutOffTimeZone=Finance
IDC.ExternalSystem.Swift.Name=Finance
IDC.DateGeneration.HolidayCalendar.HolidayCacheEndYear=Finance
IDC.MonteCarlo.Server=Finance
IDC.Risk.DataRoot=Finance
Idc.ForwardPoint.Use.QC.Precision=Finance
IDC.EnableScenarioGeneration=Finance
IDC.EnableRiskMetricsReaderThread=Finance
IDC.RiskMetricsReaderPollingDelayInSeconds=Finance
IDC.MarketData.Browser.UpdateInterval=Finance
IDC.MarketData.Browser.MaxPendingUpdates=Finance
IDC.ROLLTIME.NZD.CHECK.ENABLED=Finance
IDC.ROLLTIME.NZD.IN.GMT=Finance
IDC.ROLLTIME.NZD.IN.NST=Finance
IDC.NZD.ROLLTIME.ROLLTRADEDATE.FRI_DISABLED=Finance
Idc.Forward.Rate.Precision.Using.PipsFactor.Enabled=Finance
Idc.BrokerAdaptor.AutoCover.Options=BrokerCustomConfiguration
Idc.BrokerAdaptor.CopyTrade.CopyUser=BrokerProvisionConfig
Idc.BrokerAdaptor.MDS.UseMidFwdPoints=BrokerAdaptor
IDC.RFS.ValidationFailure.SendToManual=ISMBeanC
Idc.Admin.Stream.KeepPriceImprovementReferenceRateSelectionEnabled=PriceMakingMBeanC

IDC.IS.FXIAPI.FullAmountAggregationCheckApplicable=APIMBeanC

IDC.CREDIT.UTILIZATION.LOOKUP.SPACES.ENABLED=CreditLimitConfiguration,CreditLimitServiceConfiguration
IDC.CREDIT.LINE.BASED.NETTING.PORTFOLIO.CHECK.ENABLED=CreditLimitConfiguration

Idc.IS.FullAmount.ExclusiveFullAmountCheck.OrderType=ISMBeanC
Idc.IS.FullAmount.ExclusiveFullAmountCheck.OrderExecutionFlagsMask=ISMBeanC
Idc.BrokerAdaptor.SyntheticCross.PassRangeToPrimary.Enabled=BrokerAdaptor
Idc.BrokerAdaptor.RFS.FilterProviders.Enabled=BrokerAdaptor

Idc.STP.FIX.CoverTradeTags.On.PBDownload.Disabled=TradeConfiguration
Idc.STP.Use.CurrencyPair.Alias=TradeConfiguration
Idc.STP.Use.Currency.Alias=TradeConfiguration


IDC.IS.QUOTE.CONVENTION.CcyPair.ForceDeliverableSetting=Finance
Idc.BrokerCustomer.Org.RestrictIPMask=LoginMBeanC
Idc.STP.FIX.Use.Detail.InstrumentTypes=TradeConfiguration
Idc.STP.FIX.InstrumentType.Mapping=TradeConfiguration
Idc.MarketMaker.Skew.Allowed.Range=MarketMakerConfig
IDC.IS.RFS.DropQuote.ValueDate.Mismatch=ISRFSMBeanC
IDC.IS.RFS.Use.QC.Between.LP.AND.FI=ISRFSMBeanC
IDC.IS.RFS.Subscription.Prevent.Infinite.BackToBack.RFS=ISRFSMBeanC
Idc.IS.RFS.Provider.TradeType.Support.Filtering.Enabled=ISRFSMBeanC
Idc.IS.RFS.Include.SpotRate.Subscribe=ISMBeanC
Idc.Streaming.Swap.Trading.FIX.Channel.Enabled=ServerRuntime
Idc.BrokerAdaptor.Price.Swap.Using.Swap=PriceConfigurationMBeanC
Idc.BrokerAdaptor.Price.NDF.Swap.Using.Swap=PriceConfigurationMBeanC
IDC.ADMIN.PRICEPROVISION.SWAP.SPREAD.ENABLED=AdminWebServicesMBeanC,ISMBeanC
IDC.BROKER.CUSTOMERS.PERSISTENT.ORDER.CANCELLATION.DISCONNECT.ENABLED=OrderServiceMBeanC
IDC.IS.RFS.BPS.MIDRATE.ENABLED=ISMBeanC
IDC.IS.Broker.Customers.LiquidityRules.MaxOrderSize.Check.Disabled.TradeTypes=ISMBeanC
Idc.BrokerAdaptor.ESP.VolatileMarket.UseSpreadFactor=PriceConfigurationMBeanC
IDC.MarketMaker.MasterControl.UI.Enabled=MarketMakerConfig
IDC.Broker.Show.Accounts.In.SalesDealerGroups=TradeConfiguration
IDC.FWDFWD.NearLeg.FwdPoint.BidOffer=TradeConfiguration
Idc.BrokerAdaptor.PriceCheck.UseRange.Warehouse.Enabled=PriceConfigurationMBeanC
Idc.SSO.OIDC.Multi.Identity.AuthorizedUsers=CASMBeanC
IDC.IS.QUOTE.CONVENTION.CcyPairGroup.ForceDeliverableSetting=Finance
IDC.IS.RFS.Allow.Pricing.On.Holiday=ISRFSMBeanC
IDC.IS.RFS.Allow.Pricing.CcyPairGroup.On.Holiday=ISRFSMBeanC
IDC.CREDIT.PFE.INTERPOLATION.ENABLED=CreditConfiguration
IDC.CREDIT.ADMIN.AGGREGATE.NET.SETTLEMENT.RECEIVABLE.METHODOLOGY.ENABLED=CreditAdminConfiguration
IDC.IS.RFS.Allow.Pricing.On.Weekend=ISRFSMBeanC
Idc.BrokerAdaptor.RFS.Request.Reject.Enabled=PriceConfigurationMBeanC
IDC.RFS.Skip.Credit.Check.Trade.Types.List=TradeConfiguration
IDC.BrokerAdaptor.RFS.Provider.Points.Validation.Enabled=PriceConfigurationMBeanC
IDC.BrokerAdaptor.PriceMaking.UI.ESPOnly=PriceConfigurationMBeanC
IDC.STP.Suppress.Negative.Spreads.Enabled.Trade.Types.List=TradeConfiguration
Idc.MarketMaker.ESP.MinMaxAndBPS.Enabled=PriceConfigurationMBeanC
IDC.STP.Suppress.Negative.Spreads.Enabled.Tags.List=TradeConfiguration
IDC.IS.PQ.DirectedOrders=PQConfig
IDC.IS.PQ.DirectedOrders.Enabled=PQConfig
IDC.IS.PQ.DirectedOrders.StreamRoutingType=PQConfig
IDC.IS.PQ.DirectedOrders.RateToleranceBps=PQConfig
IDC.IS.PPSpot.Spread.For.Outright.Disabled=ISMBeanC
Idc.BrokerAdaptor.FixedPricingEnabled=ConfigurationMBeanC
IDC.Fixed.Period.Market.DataSet.Purge.Keep.Period=DealingDataPurgeConfiguration
IDC.IS.PPSpot.Spread.For.Swap.Disabled=ISMBeanC
IDC.BrokerAdaptor.RFS.Provider.Points.Validation.Synthetic.Calculation.Enabled=PriceConfigurationMBeanC
Idc.SSO.OIDC.Service.TradingApp=SSOMBeanC
Idc.BrokerAdaptor.FixedPricingDelayAtStartupInMillis=ConfigurationMBeanC
Idc.analytics.ga.cd.stream=AnalyticsMbeanImpl
Idc.analytics.ga.cd.config=AnalyticsMbeanImpl
Idc.BrokerAdaptor.FixedPricing.Frequency.MinMinute=ConfigurationMBeanC
Idc.BrokerAdaptor.FixedPricing.Validity.MinMinute=ConfigurationMBeanC
IDC.BrokerAdaptor.Allow.MarketMaker.Auto.Subscription=ConfigurationMBeanC
IDC.BrokerAdaptor.CustomerGroup.Price.Publish.Interval=ConfigurationMBeanC
IDC.BrokerAdaptor.MarketMaker.Control.IncrementByPips=ConfigurationMBeanC
Idc.BrokerAdaptor.MarketDataSet.APIResponse.TimeZone=ConfigurationMBeanC
IDC.STP.FinXMLV25.Use.Detail.InstrumentTypes=TradeConfiguration
IDC.STP.FinXMLV25.InstrumentType.Mapping=TradeConfiguration
IDC.STP.FinXMLV25.Use.Detail.TradeTypes=TradeConfiguration
IDC.STP.FinXMLV25.InstrumentTypes.Enabled=TradeConfiguration
Idc.BrokerAdaptor.ExecRule.NoDiffBetweenMinMaxTenor.Skip.MinTenorCheck=PriceConfigurationMBeanC
IDC.BrokerAdaptor.Stream.FixedPricing.TimeZone=ConfigurationMBeanC
IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.CANCEL.EXPIRE.NOTIFICATION.ENABLED=CreditAdminConfiguration
IDC.BrokerAdaptor.ProviderStatusCheck.Connection.Timeout=PriceConfigurationMBeanC
IDC.BrokerAdaptor.ProviderStatusCheck.Read.Timeout=PriceConfigurationMBeanC
Idc.UIG.Spot.MD.REST.Supported=MDFConfigMBeanC
Idc.MDF.MarketData.AutoSubscription.AggType=MDFConfigMBeanC
Idc.MDF.MarketData.AutoSubscription.Aggregation.User=MDFConfigMBeanC
IDC.ADMIN.ORG.LEGALENTITY.MT300FIELD72.STARTSWITH=AdminWebServicesMBeanC
IDC.ADMIN.ORG.LEGALENTITY.MT300FIELD72.BrokerCust.STARTSWITH=AdminWebServicesMBeanC
Idc.BrokerAdaptor.PartialCover.Enabled=BrokerProvisionConfig
Idc.BrokerAdaptor.VenueIntegration.SupportedCurrencyPairs=BrokerAdaptor
Idc.BrokerAdaptor.VenueIntegration.Enabled=BrokerAdaptor
Idc.OMS.Customer.Order.Additional.States=OrderConfiguration
Idc.OMS.Customer.Order.PendingNewWithOrderId=OrderConfiguration
IDC.IS.ClientPermission=ClientConf
IDC.CREDIT.RETAIL.MODE.ENABLED=CreditConfiguration
IDC.CREDIT.ADMIN.UTILIZATION.STOPOUT.REVALUATION.ENABLED=CreditAdminConfiguration
IDC.CREDIT.ADMIN.UTILIZATION.STOPOUT.REVALUATION.PERIOD=CreditAdminConfiguration
IDC.IS.ApplyPBPriceProvisioning=ISMBeanC
IDC.EMS.SERVER.DETAILS.LIST=IdcMBeanC
Idc.FCS.ExecutionReport.Ack.timeout=IdcMBeanC
Idc.UIG.Esp.RateCache.Timeout.Seconds=UIGConfigMBeanC
Idc.FCS.ConfirmTrade=FIXClientConfig
Idc.UIG.Max.PriceBook.Depth=UIGConfigMBeanC
Idc.UIG.Default.PriceBook.Depth=UIGConfigMBeanC
IDC.BrokerAdaptor.Import.PriceMaking.Spread=PriceConfigurationMBeanC
IDC.BrokerAdaptor.FAStreamQuoteFilter.AggregateFAQuotesInNonFAStreams=PriceConfigurationMBeanC
Idc.SSO.JWT.SECRET.KEY=CASMBeanC
Idc.SSO.AuthToken=CASMBeanC
Idc.APIGateway.IPs=SSOMBeanC
Idc.SSO.SAML.apiversion.integral=CASMBeanC
Idc.OrderAdaptor.FullAmount.AllowMatchIfNonFANotAvailable=OrderConfiguration

Idc.Mvps=ClobListenerMBeanC
Idc.Mvp=MVCreditConfigMBeanC
Idc.Mv.Clob=PAConfigMBeanC
Idc.MV.Clob=CentralClobConfigMBean
Idc.MV.CLOB=CentralClobConfigMBean
Idc.Mv.AeronDriver=CAP50ConfigC
Idc.GateWay.Container=MultiTenantContainerConfigMBeanC
Idc.GateWay.Maker=MultiTenantGateWayConfiguration
Idc.Mv.Config=CentralClobConfigMBean
Idc.Trading.Venue.Market.Status=DirectedOrderCommonMBean
IDC.RM.BRCM=BrokerCustomerRiskManagerConfigService
IDC.BRCM.EMS.FIX.Config=BRCMEmsConfig
Idc.MV.REX=MatchingVenuePropertyMBeanC
Idc.SSO=SSOMBeanC
IDC.IS.Lot.Size=ClientConf
IDC.API.ConfirmTrade=APIMBeanC
Idc.LP.Admin.Theme.Link.Show=AdminWebServicesMBeanC
Idc.SSO.App.SubDomain=SSOMBeanC
Idc.FAS=ISMBeanC

Idc.FCS.S2S=FIXClientConfig
IDC.IS.STP.Note.Workflow.Triggered.Cancel.Enabled=TradeConfiguration
IDC.IS.Trade.BatchAmendAllocateRoll.CopyParentChannel=TradeConfiguration
IDC.IS.Active.RFS.Broadcast.Enabled=ISMBeanC
IDC.IS.Manual.RFQ.Broadcast.Enabled=ISMBeanC
IDC.IS.Manual.RFQ.Broadcast.Via.Multicast.Enabled=ISMBeanC
IDC.IS.Extrapolation.MDS.Broker.Enabled=ISMBeanC
IDC.IS.Active.RFS.Broadcast.Rate.Publish.IntervalInMills=ISMBeanC
IDC.IS.Active.RFS.Broadcast.WaitTime.NoPrice.AlertInSec=ISMBeanC
Idc.IS.TradingRestriction.MaxValue=ISMBeanC
IDC.IS.ClobRouting.PegAtMidOrders.Enabled=ISMBeanC
Idc.Oms.RouteOrderToOMSWithAuto=OrderConfiguration
Idc.OMS.Order.Credit.Enabled=OrderConfiguration
Idc.BrokerAdaptor.AtBest.Order.RFS.AutoAccept.Period=BrokerAdaptor
Idc.BrokerAdaptor.Skip.ValueDate.MisMatch.Providers.On.Cover.RFS.Match=BrokerAdaptor
Idc.BrokerAdaptor.RFS.Historical.Quote.Cache.Enabled=BrokerAdaptor
Idc.BrokerAdaptor.Allow.ValueDate.MisMatch.On.Customer.Trade=BrokerAdaptor
Idc.BrokerAdaptor.AtBest.Order.RFS.Expiry.Time=BrokerAdaptor
Idc.BrokerAdaptor.RFQ.Cover.Expiry.Time=BrokerAdaptor
Idc.BrokerAdaptor.Customer.Order.AtBest.ByDefault=BrokerAdaptor
Idc.BrokerAdaptor.Manual.Publication.Interval=BrokerAdaptor
Idc.UIG.Price.CustomSubscription.MaxCount=UIGConfigMBeanC
IDC.USER.RESET.PERMISSIONS.CACHE.ON.USER.UPDATE=User
Idc.Broker.CurrencyPairConfiguration.Enable.Profile.Configuration=AdminWebServicesMBeanC
IDC.BrokerAdaptor.MaxLiquidityGroups.Allowed=PriceConfigurationMBeanC
IDC.ADMIN.ORGANIZATION.DEFAULT.ROLE=AdminWebServicesMBeanC
IDC.ADMIN.ORGANIZATION.BROKER.DEFAULT.ROLES=AdminWebServicesMBeanC
IDC.ADMIN.ORGANIZATION.PRIMEBROKER.DEFAULT.ROLES=AdminWebServicesMBeanC
IDC.Server.Transaction.Namespace.Restrictions.Validation.Enabled=ServerRuntime
Idc.UIG.Client.Heartbeat.Interval=ClientConfMBeanC
Idc.MDF.Generic.FullBook.Rate.Source=MDFConfigMBeanC
Idc.UIG.WS.message.buffer.size=WSMBeanC
Idc.UIG.Client.Inactivity.Timeout=ClientConfigMBeanC
Idc.OMS.External.Venue.Orgs=OMSConfig
Idc.OMS.CCYPairs=OMSConfig
Idc.OMS.Auto.Execute.Sales.Dealer.Org=OMSConfig
Idc.FxiApi.Rate.Subscription.Vwap.To.MTFA=APIMBeanC
Idc.FxiApi.InputValidation.Unicode.Regex=APIMBeanC
IDC.ADMIN.AllocatedOAServer=AdminWebServicesMBeanC
Idc.IS.Show.Provisioned.Limits=ISMBeanC
Idc.BrokerAdaptor.BrokerV4.Enabled=BrokerCustomConfiguration
Idc.BrokerAdaptor.BrokerV4.NONLiquidityGroupMatch.Enabled=BrokerCustomConfiguration
Idc.Mvp.StreamUpdate=MVCreditConfigMBeanC
Idc.Admin.Show.Stream.ExternalReferenceId=AdminWebServicesMBeanC
IDC.CREDIT.ADMIN.AGGREGATE.MULTIFACTOR.SETTLEMENT.METHODOLOGY.ENABLED=CreditAdminConfiguration
IDC.BrokerAdaptor.Cached.Quote.On.Subscription=BrokerAdaptor
Idc.BrokerAdaptor.SyntheticCross.ChargeForwardPointsForValueDateMismatch=BrokerAdaptor
Idc.OrderAdaptor.VirtualServerSwitch.OrderMigrationMode=OrderServiceMBeanC
Idc.OrderAdaptor.Order.Migration.On.Vs.Switch.Enabled=OrderServiceMBeanC
Idc.BrokerAdaptor.LatestForwardPointFromMDS=BrokerAdaptor
IDC.SSO.Client.App.Name=CASMBeanC
IDC.SSO.Client.App.SuccessURL=CASMBeanC
Idc.SSO.OIDC.Service.Generate.CodeChallenge.Enabled=CASMBeanC
Idc.SSO.OIDC.Service.Provider.Authentication.AdditionalParams=CASMBeanC
Idc.SSO.OIDC.Service.Provider.Authorization.AdditionalParams=CASMBeanC
Idc.SSO.OIDC.Service.Provider.CodeVerifier=CASMBeanC
idc.SuppressTradeEmail.Reject=TradeEmailMBeanC
IDC.SSO.Broker.Customers.Client.App.Name=CASMBeanC
Idc.RM.Position.Server.Regulate.Snapshot.Updates=PositionServerConfig
Idc.RM.Position.Server.Snapshot.Update.Pause.Interval=PositionServerConfig
Idc.RM.Position.Update.Batch.Size=PositionServerConfig
IDC.MDS.UPDATE.EVENT.PROCESS.FOR.NAMESPACES.OA=MDSConfigMBeanC
IDC.Position.Update.Interval=PositionServiceConfigC
IDC.IS.SEND.POSITION.UPDATE.WITHOUT.UNREALIZED.PNL = ISMBeanC
Idc.UIG.Client.Heartbeat.Retries=ClientConfMBeanC
Idc.UIG.Enabled.ORG=UIGConfigMBeanC

Idc.Client.PortalAppName=ClientConf
IDC.ExVenue.ExDestination=ExternalDestinationDirectedOrdersConfig
Idc.Trade.Netting.NetTrade.DealtCurrency=TradeConfiguration
Idc.Trade.Verification.STP.Skip.Enabled=TradeConfiguration
IDC.IS.RFS.NDF.TradeType.Mismatch.Validation.Enabled=ISRFSMBeanC
IDC.IS.RFS.REQUEST.EXPIRE.TIME=ISMBeanC
IDC.IS.Process.NonDeployed.Adaptor=AdaptorConfiguration

IDC.IS.ACCEPTANCE.HOOK.VERSION=ISMBeanC
Idc.OMS.CcyPairGroups.List=OMSConfig
IDC.ADMIN.DISPLAY.CREDIT.CUSTOMER=AdminWebServicesMBeanC
Idc.Trade.RTN.UPI.STP=TradeConfiguration
Idc.Trade.RTN.Old.UTI.Prefix.STP=TradeConfiguration
Idc.Trade.WindowForward.Minimum.ValueTenor=TradeConfiguration
Idc.Trade.ProrataForward.Support.Enabled=TradeConfiguration
Idc.Trade.ProrataForward.CurrencyPairs.List=TradeConfiguration
Idc.Trade.ProrataForward.Min.Tenor=TradeConfiguration
Idc.Trade.ProrataForward.Max.Tenor=TradeConfiguration
Idc.Trade.ProrataForward.Residual.Days=TradeConfiguration
Idc.Trade.ProrataForward.Tenors.List=TradeConfiguration

Idc.MarketMaker.MarketSignals.Enabled=MarketMakerConfig
Idc.MarketMaker.Dynamic.Pricing.Mode.Enabled=MarketMakerConfig
Idc.MarketMaker.Volatility.Service.Enabled=MarketMakerConfig

Idc.MarketSignals.Admin.Access=PriceMakingMBeanC
Idc.MarketSignals.SupportedServices=PriceMakingMBeanC

Idc.OrderAdaptor.V4EMSEnabled=OrderConfiguration
IDS.IS.ORDER.Default.MarketRange.Percentage=OrderConfiguration
Idc.BrokerAdaptor.Aggregation.MDFSOURCE.Enabled=BrokerCustomConfiguration
Idc.BrokerAdaptor.Rate.PublishToOnDemandMDF.Enabled=BrokerCustomConfiguration
Idc.BrokerAdaptor.OnDemandMDFEnabled.Venue=BrokerCustomConfiguration
Idc.BrokerAdaptor.MDF.Quote.MarketDepth=BrokerCustomConfiguration
Idc.BRAND=BrandConfig
Idc.SSO.Provisional.Validity.Period=CASMBeanC
Idc.MarketMaker.MarketSignals.Enabled=MarketMakerConfig
Idc.MarketMaker.Dynamic.Pricing.Mode.Enabled=MarketMakerConfig
Idc.STP.FIX.Pricing.Type.Enabled=TradeConfiguration
IDC.Streaming.NonSpot.InMemory.CurrencyPair.Enabled=ServerRuntime

IDC.Admin.Configure.Stream.Alias=AdminWebServicesMBeanC
IDC.Admin.Show.Stream.Alias=AdminWebServicesMBeanC
IDC.Admin.Show.Stream.Attributes=AdminWebServicesMBeanC
IDC.CREDIT.ADMIN.AGGREGATE.NET.CREDITNET.SETTLEMENT.METHODOLOGY.ENABLED=CreditAdminConfiguration
IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.NEW.FLOW.ENABLED=CreditAdminConfiguration
IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.TradeTypes=ISMBean
IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.Tenors=ISMBean
IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.FORMAT=ISMBean
IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.Sequence=ISMBean
IDC.CREDIT.ADMIN.EXTERNAL.CREDIT.PROVIDER.IGNORE.INTERNAL.CREDIT.ENABLED=ISMBean
IDC.IS.RATE.BAND.FILTER.ENABLED=ISMBean
IDC.SalesDealer.PnL.HomeCurrency=ISMBeanC
IDC.RFS.Credit.ValidationFailure.ZeroForwardPointHandling=ISMBeanC
Idc.BrokerAdaptor.LP.Subscriptions.Force.Subscribe=ISMBeanC
Idc.UIG.CDQ.Query.Response.Throttle.Delay=UIGConfigMBeanC
MongoDB.Collection.Batch.Size=Persistence
IDC.ADMIN.PRICEPROVISION.ENABLE.TIER.BASED.SPREAD.FOR.POST.TRADE.SPREAD=AdminWebServicesMBeanC

IDC.IS.BROKERS.WHO.USE.OWN.MDS.FOR.PNL.CALC=ISMBeanC
IDC.ADMIN.PRICEPROVISION.SPOT.SPREAD.PROFILE.MAX.COUNT.=AdminWebServicesMBeanC
IDC.ADMIN.PRICEPROVISION.SPOT.SPREAD.PROFILE.MAX.TIERS=AdminWebServicesMBeanC
IDC.ADMIN.PRICEPROVISION.SPOT.SPREAD.PROFILE.TIERS.DEFAULT=AdminWebServicesMBeanC
IDC.MULTI.APP.REMOTE.FUNCTOR.EXECUTION.SKIP.LIST=EntityServiceConfiguration
IDC.USER.FORGOT.PASSWORD.ALLOWED.DISABLED.ACCOUNTS=User
IDC.Streaming.NonSpot.Broken.Date.Enabled=ServerRuntime
Idc.BrokerAdaptor.Virtual.CurrencyPair.MarketRate.SerializationVersion=PriceConfigurationMBeanC
Idc.OMS.Order.Fixing.Venue=ClientConf
Idc.Fixing.Order.Channel=ClientConf
Idc.Fixing.Order.Destination=ClientConf
Idc.Fixing.AmendOrCancelOrderThresholdTime=ClientConf
Idc.Fixing.NewOrderThresholdTime=ClientConf
IDC.DEALING.DATA.PURGE.CREATED.DATE.QUERY.ENABLED=DealingDataPurgeConfiguration
IDC.IS.Broker.Customers.RFS.REQUEST.EXPIRE.TIME=ISMBeanC
IDC.Admin.Delete.Accounts.Enabled=AdminWebServicesMBeanC
Idc.PriceMaking.Commons.PriceDistribution.Venue=PriceMakingCommonsConfig
Idc.Trade.ProrataForward.Special.Clients.Max.Tenor=TradeConfiguration
Idc.Trade.ProrataForward.Special.Clients.Tenors.List=TradeConfiguration
Idc.BrokerAdaptor.RFS.Term.Swap.Order.Matching.UseCashFlow=BrokerAdaptor
Idc.OMS.Order.Fixing.Venue=ClientConf
Idc.Fixing.Order.Channel=ClientConf
Idc.Fixing.Order.Destination=ClientConf
IDC.ADMIN.PRICEPROVISION.PRORATA.SPREAD.ENABLED=AdminWebServicesMBeanC
IDC_ADMIN_ORGANIZATION_TIMEZONE=AdminWebServicesMBeanC
Idc.BrokerAdaptor.DeliverableNDF.Enabled=PriceConfigurationMBeanC
Idc.BrokerAdaptor.Broker.Customers.DeliverableNDF.Enabled=PriceConfigurationMBeanC
Idc.Trade.RFQ.Support.Enabled=TradeConfiguration
Idc.Trade.Precision.Use.BigDecimal.Enabled=TradeConfiguration
Idc.Trade.Single.Quote.RFS.Support.Enabled=TradeConfiguration
IDC.IS.UI.RFS.REQUEST.EXPIRE.TIME=ISMBeanC
IDC.IS.Broker.Customers.UI.RFS.REQUEST.EXPIRE.TIME=ISMBeanC
Idc.BrokerAdaptor.PriceFromMDS.Enabled=PricingConfigurationC
Idc.UIG.Text.SpecialCharacters=UIGConfigMBeanC
Idc.Login.UID.Cookie.Enabled=LoginMBean
IDC.ADMIN.PRICEPROVISION.SPOT.SPREAD.PROFILE.ENABLED=AdminWebServicesMBeanC
Idc.UIG.Duplicate.Login.ErrorCode=UIGConfigMBeanC
Idc.UIG.Client.Inactivity.Warning.Display.Duration=ClientConfigMBeanC
IDC.USER.FORGOT.PASSWORD.ALLOWED.DISABLED.ACCOUNT=User
IDC.ADMIN.PRICEPROVISION.SPOT.SPREAD.PROFILE.MAX.COUNT=AdminWebServicesMBeanC
Idc.UIG.Order.Cache.Timeout.Seconds=UIGConfigMBeanC
IDC.IS.NTFA.SupportsMarketRangeOrders=ProviderConfigMBean
IDC.USER.PASSWORD.RESUSE.MIN.CONSECUTIVE.CHANGE=User
Idc.UIG.Client.Hearbeat.Retries=ClientConfMBean
Idc.UIG.UI.Client.Aggregation.Interval=UIGConfigMBeanC
Idc.BrokerAdaptor.Single.Provider.Failover.Enabled=ConfigurationMBeanC
IDC.BrokerAdaptor.CFD.Failover.Enabled=ConfigurationMBeanC
Idc.UIG.WebSocket.Buffer.Size=UIGConfigMBeanC
IDC.USER.PASSWORD.POLICY.SOURCE.FROM.BRAND.ENABLED=SecurityServiceC
IDC.USER.NEW.PASSWORD.EXPIRY.TIME=SecurityServiceC
IDC.USER.FORGOT.PASSWORD.REQUEST.MAX.ATTEMPT.COUNT.PER.DAY=SecurityServiceC
IDC.USER.RESET.PASSWORD.REQUEST.MAX.ATTEMPT.COUNT.PER.DAY=SecurityServiceC
Idc.OrderAdaptor.ShowRejectReason=OrderConfiguration
Idc.STP.FIX.SendSalesDealerDetails=TradeConfiguration
IDC.ADMIN.PRICEPROVISION.FIXING.SPREAD.ENABLED=AdminWebServicesMBeanC
Idc.BrokerAdaptor.Health.Check.Enabled=BrokerAdaptor
Idc.BrokerAdaptor.Health.Check.Interval=BrokerAdaptor
Idc.IntegralTrader=ClientConf
IDC.Streaming.NonSpot.Broken.Date.Execution.Enabled=ServerRuntime
Idc.OrderAdaptor.ZeroLiquidity=OrderConfiguration
IDC.Admin.Show.Org.Manage.Taker.Configuration=AdminWebServicesMBeanC
Idc.OrderAdaptor.ClientHandler=OrderConfiguration
IDC.IS.RFS.Trade.Spot.Rate.Recalculation.Enabled=ISRFSMBeanC
Idc.BrokerAdaptor.Mismatch.Swap.Spot.Spread.Use.NetSpotAmount.Side=BrokerAdaptor
Idc.Credit.Warmup.All.LegalEntities.On.Subscription.Enabled=CreditConfiguration
IDC.PERSIST.RFSRATES=ISMBeanC
IDC.PERSIST.BROKER.CUSTOMERS.RFSRATES=ISMBeanC
IDC.IS.RFS.Skip.Unaccepted.Requests.Persistence.Enabled=ISRFSMBeanC
IDC.IS.RFS.Broker.Customers.Skip.Unaccepted.Requests.Persistence.Enabled=ISRFSMBeanC
Idc.Admin.Schedule.Task.Url=AdminWebServicesMBeanC
IDC.IS.RFS.Skip.Send.Quote.On.Disabled.PriceProvision=ISRFSMBeanC
IDC.EOD.Market.DataSet.Purge.Keep.Period=DealingDataPurgeConfiguration
Idc.MDF.Use.Broker.Level.Multicast.Address.For.Rate=MDFConfigMBeanC
Idc.UIG.Use.CDQ.Orders=UIGConfigMBeanC
Idc.BrokerAdaptor.RFS.ManualPricing.StatusMessage.Enabled=PriceConfigurationMBeanC
IDC.USER.FORGOT.PASSWORD.MAX.ATTEMPT.EXCEEDED.EMAIL.ENABLED=User
IDC.USER.RESET.PASSWORD.MAX.ATTEMPT.EXCEEDED.EMAIL.ENABLED=User
IDC.SERVICE.DISCOVERY.ADDRESS=Server
IDC.STP.TakerOrg.UseSettlementCode=TradeConfiguration
IDC.IS.JPM.Min.Size=AdaptorConfig
Idc.Admin.SchedulerLink.Enabled=AdminWebServicesMBeanC
Idc.BrokerAdaptor.TOD.TOM.CutOff.Excluded.Customers=PriceConfigurationMBeanC
IDC.IS.RFS.Credit.Workflow.Mode.Enabled=ISRFSMBeanC
IDC.IS.RFS.Broker.Customers.Credit.Workflow.Mode.Enabled=ISRFSMBeanC
Idc.BrokerAdaptor.RFQ.CoverTrade.Use.HomeCurrency=BrokerAdaptor
Idc.BrokerAdaptor.RFQ.CoverTrade.Use.RFQ=BrokerAdaptor
Idc.BrokerAdaptor.Synthetic.Forward.Secondary.RFS.AutoAccept.Period=BrokerAdaptor
IDC.DEALING.DATA.PURGE.NEW.SCHEME.ENABLED=DealingDataPurgeConfiguration
IDC.DEALING.DATA.PURGE.DRY.RUN=DealingDataPurgeConfiguration
Idc.UIG.Query.Preference=UIGConfigMBeanC
Idc.Metaspaces.Cluster.clusterrefdata=MetaspacesConfigMBeanImpl
IDC.MDG.FIX.SENDER.COMP.ID=MDGConfigMBeanC
Idc.MDF.Venue.HeartBeat.Check.Enabled=MDFConfigMBeanC
IDC.USER.RESET.PASSWORD.FROM.EMAIL.ADDRESS=SecurityServiceC
Idc.BA.RFS.ESPMDS.ESPSpreading.Enabled=ISMBeanC
Idc.MDF.Currencypair.Subscription.Filter.Enabled=MDFConfigMBeanC
IDC.DEALING.DATA.PURGE.NEW.SCHEME.ENABLED=DealingDataPurgeConfiguration
IDC.DEALING.DATA.PURGE.DRY.RUN=DealingDataPurgeConfiguration
IDC.IS.FIRST.IMM.DATE.AFTER.SPOT.DATE.ENABLED=Finance
Idc.BrokerAdaptor.PriceMaking.Configuration.Max.FileSize=FileUploadMBeanC
Idc.MarketMaker.Single.LP.Failover.Enabled=MarketMakerConfig
Idc.MarketMaker.Single.LP.Failover.Priority=MarketMakerConfig
Idc.BrokerAdaptor.PartialCover.RegularSize=BrokerProvisionConfig
Idc.BrokerAdaptor.CFD.Failover.Notification.Interval=BrokerCustomConfiguration
Idc.Admin.Dynamic.Cutoff.Profiles.Enabled=AdminWebServicesMBeanC
IDC.USER.INACTIVATE.USERS.WITHOUT.USER.SESSION=User
User.Inactivation.days.Brokers=User
Idc.Pre.Trade.Allocation.Spreading.Model.Enabled=TradeConfiguration
IDC.ADMIN.PRICEPROVISION.Global.Additional.Spread.Markup=ISMBeanC
IDC.Core.App.RequestURI.Permissions=ApplicationMBeanC

Idc.OrderBook.View=OrderBookViewAttributeMBeanC
Idc.BrokerAdaptor.RFS.Stop.Publisher.After.Trade.Response=BrokerAdaptor
Idc.Admin.PriceProvision.UI.New.SpreadProfile.DropDown.Enabled=AdminWebServicesMBeanC
IDC.ADMIN.PRICEPROVISION.SWAP.NOTIONAL.SPREAD.ENABLED=AdminWebServicesMBeanC
Idc.BrokerAdaptor.ESP.Pricing.From.MDS.Enabled=BrokerAdaptor
Idc.BrokerAdaptor.ESP.Pricing.From.MDS.TierSize=BrokerAdaptor
IDC.CREDIT.ORDER.RESERVE.WORKFLOW.ENABLED=CreditConfiguration

