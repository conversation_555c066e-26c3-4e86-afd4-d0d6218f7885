
def repoPath = "$repoSrc"
description = "This is Integral RDS module"

version = "1.1.0"

sourceSets {
    main {
       java {
          srcDirs 'src'
          
       }
   }
}

dependencies {
        compile project(':IntegralCoreServices')
}
 
jar {
    jar.archiveName = "rdserver.jar"
    destinationDir = file("$repoPath")

    includeEmptyDirs = false

     manifest {
        attributes 'Implementation-Title': project.name,
                   'Implementation-Version': project.version,
                   'Implementation-Description': project.description,
                   'Built-By': System.getProperty('user.name'),
                   'Built-JDK': System.getProperty('java.version'),
                   'Built-Hostname': hostname(),
                   'Build-Time': buildTime(),
                   'Build-Version': svnversion()
    }

}

clean {
    delete "../rdserver.jar"
    delete "build"
}

task compileTests{
}

compileJava.doFirst {
     println project.name + " start: " + buildTime()
}

build.doLast {
    println project.name + " end: " + buildTime()
}
