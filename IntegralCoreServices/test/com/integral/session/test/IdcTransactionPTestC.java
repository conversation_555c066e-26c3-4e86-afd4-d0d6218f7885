package com.integral.session.test;

import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.exception.IdcNoUpdateRightsException;
import com.integral.messaging.MessageSender;
import com.integral.messaging.MessageSenderFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;

import java.util.HashMap;
import java.util.Map;

/**
 * Tests the transaction framework
 */
public class IdcTransactionPTestC
        extends PTestCaseC
{
    static String name = "IdcTransaction Test";

    public IdcTransactionPTestC( String name )
    {
        super( name );
    }

    public void testAsynchronousRemoteFunctor()
    {
        // create audit event
        AuditEvent event = new AuditEventC( "TEST", "SessionTest" );
        event.setStatus( 'T' );

        // start a transaction
        IdcSessionContext ctx;
        IdcTransaction xact = null;
        try
        {
            ctx = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            xact = IdcSessionManager.getInstance().newTransaction( ctx, "Test" );
            xact.setAsyncFunctorExecution( true );
            HashMap props = new HashMap();
            props.put( "TestParamKey", "TestParamValue" );
            xact.addRemoteFunctor( TestRemoteFunctor.class.getName(), props  );
        }
        catch ( Exception e )
        {
            fail( "error starting transaction: " + e );
        }

        // register entity and commit new entity
        try
        {
            log.info( "start commiting new audit object" + " with hashCode " + event.hashCode() );
            xact.getUOW().removeAllReadOnlyClasses();
            AuditEvent registeredEvent = ( AuditEvent ) xact.getUOW().registerObject( event );
            log.info( "  completed commiting new audit object" + " with hashCode " + event.hashCode() + ",registeredEvent=" + registeredEvent.hashCode() );
        }
        catch ( Exception e )
        {
            fail( "Failed in AuditEvent : ", e );
        }

        // commit transaction
        try
        {
            xact.commit();

            Thread.sleep( 2000 );
            String val = TestRemoteFunctor.threadMap.get( "TestParamKey" );
            if ( isMessageSenderInit () )
            {
                assertNotNull ( val );
                assertFalse ( Thread.currentThread ().getName ().equals ( val ) );
            }
        }
        catch ( Exception e )
        {
            fail( "error committing transaction: " + e );
        }
    }


    public void testAsynchronousRemoteFunctorConfig()
    {
        // create audit event
        AuditEvent event = new AuditEventC( "TEST", "SessionTest" );
        event.setStatus( 'T' );

        // start a transaction
        IdcSessionContext ctx;
        IdcTransaction xact = null;
        try
        {
            ctx = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            xact = IdcSessionManager.getInstance().newTransaction( ctx, "Test" );
            xact.setAsyncFunctorExecution( true );
            HashMap props = new HashMap();
            props.put( "TestParamKey", "TestParamValue" );
            xact.addRemoteFunctor( TestRemoteFunctor.class.getName(), props  );
            WatchPropertyC.update( ServerRuntimeMBean.ASYNC_REMOTE_FUNCTOR_EXECUTION_ENABLED_ON_TRANSACTION_COMMIT, "false", ConfigurationProperty.DYNAMIC_SCOPE);
        }
        catch ( Exception e )
        {
            fail( "error starting transaction: " + e );
        }

        // register entity and commit new entity
        try
        {
            log.info( "start commiting new audit object" + " with hashCode " + event.hashCode() );
            xact.getUOW().removeAllReadOnlyClasses();
            AuditEvent registeredEvent = ( AuditEvent ) xact.getUOW().registerObject( event );
            log.info( "  completed commiting new audit object" + " with hashCode " + event.hashCode() + ",registeredEvent=" + registeredEvent.hashCode() );
        }
        catch ( Exception e )
        {
            fail( "Failed in AuditEvent : ", e );
        }

        // commit transaction
        try
        {
            xact.commit();

            Thread.sleep( 2000 );
            String val = TestRemoteFunctor.threadMap.get( "TestParamKey" );
            if ( isMessageSenderInit () )
            {
                assertNotNull ( val );
                assertTrue ( Thread.currentThread ().getName ().equals ( val ) );
            }
        }
        catch ( Exception e )
        {
            fail( "error committing transaction: " + e );
        }
    }

    public void testNamespaceRestrictions()
    {
        Organization lpOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );
        Organization fiOrg1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization fiOrg2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        try
        {
            User lpUser = lpOrg.getDefaultDealingUser ();
            IdcUtilC.setSessionContextUser ( lpUser );
            IdcTransaction tx = IdcSessionManager.getInstance ().newTransaction (lpUser );
            tx.getUOW ().removeAllReadOnlyClasses ();
            Organization registeredFIOrg1 = ( Organization ) fiOrg1.getRegisteredObject ();
            registeredFIOrg1.setClientTag ( System.currentTimeMillis () + "" );
            try
            {
                tx.commit ();
                if ( RuntimeFactory.getServerRuntimeMBean ().isTransactionNamespaceRestrictionsValidationEnabled () )
                {
                    fail ( "Not allowed to update other namespace entity." );
                }
            }
            catch ( IdcNoUpdateRightsException ex )
            {
                log.info ( "testNamespaceRestrictions - not updating the other namespace entity." );
                // continue
            }
        }
        catch ( Exception e )
        {
            log.error ( "testNamespaceRestrictions - Exception", e );
            fail( "testNamespaceRestrictions", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( lpOrg );
            IdcUtilC.refreshObject ( fiOrg1 );
            IdcUtilC.refreshObject ( fiOrg2 );
        }
    }

    public void testNamespaceRestrictionsBrokerForCustomer()
    {
        Organization lpOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );
        Organization fiOrg1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization fiOrg2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        try
        {
            //set the broker org
            setBrokerOrg ( fiOrg1, lpOrg );
            User lpUser = lpOrg.getDefaultDealingUser ();
            IdcUtilC.setSessionContextUser ( lpUser );
            IdcTransaction tx = IdcSessionManager.getInstance ().newTransaction (lpUser );
            tx.getUOW ().removeAllReadOnlyClasses ();
            Organization registeredFIOrg1 = ( Organization ) fiOrg1.getRegisteredObject ();
            String newClientTag = System.currentTimeMillis () + "";
            registeredFIOrg1.setClientTag ( newClientTag );
            tx.commit ();
            fiOrg1 = ( Organization ) IdcUtilC.refreshObject ( fiOrg1 );
            fiOrg1.setBrokerOrganization ( lpOrg );
            log.info ( "testNamespaceRestrictions - should be able to update customer's namespace." );
            assertNotNull ( fiOrg1.getClientTag () );
            assertEquals ( newClientTag,  fiOrg1.getClientTag () );
        }
        catch ( Exception e )
        {
            log.error ( "testNamespaceRestrictionsBrokerForCustomer - Exception", e );
            fail( "testNamespaceRestrictions", e );
        }
        finally
        {
            try
            {
                IdcUtilC.refreshObject ( lpOrg );
                IdcUtilC.refreshObject ( fiOrg1 );
                IdcUtilC.refreshObject ( fiOrg2 );
                setBrokerOrg ( fiOrg1, null );
            }
            catch ( Exception ex )
            {
                fail ( "testNamespaceRestrictionsBrokerForCustomer", ex );
            }
        }
    }

    public void testNamespaceRestrictionsNonMainOrg()
    {
        Organization fiOrg1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        try
        {
            User user = fiOrg1.getDefaultDealingUser ();
            IdcUtilC.setSessionContextUser ( user );
            IdcTransaction tx = IdcSessionManager.getInstance ().newTransaction ( user );
            tx.getUOW ().removeAllReadOnlyClasses ();
            Organization registeredFIOrg1 = ( Organization ) fiOrg1.getRegisteredObject ();
            String newClientTag = System.currentTimeMillis () + "";
            registeredFIOrg1.setClientTag ( newClientTag );
            tx.commit ();
            fiOrg1 = ( Organization ) IdcUtilC.refreshObject ( fiOrg1 );
            log.info ( "testNamespaceRestrictionsNonMainOrg - should be able to update customer's namespace." );
            assertNotNull ( fiOrg1.getClientTag () );
            assertEquals ( newClientTag,  fiOrg1.getClientTag () );
        }
        catch ( Exception e )
        {
            log.error ( "testNamespaceRestrictionsNonMainOrg - Exception", e );
            fail( "testNamespaceRestrictionsNonMainOrg", e );
        }
    }

    public void testNamespaceRestrictionsNonMainOrgBySuperAdmin()
    {
        Organization fiOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        try
        {
            User user = fiOrg.getDefaultDealingUser ();
            IdcUtilC.setSessionContextUser ( user );
            IdcTransaction tx = IdcSessionManager.getInstance ().newTransaction ( user );
            tx.getUOW ().removeAllReadOnlyClasses ();
            Organization registeredFIOrg = ( Organization ) fiOrg.getRegisteredObject ();
            String newClientTag = System.currentTimeMillis () + "";
            registeredFIOrg.setClientTag ( newClientTag );
            tx.commit ();
            fiOrg = ( Organization ) IdcUtilC.refreshObject ( fiOrg );
            log.info ( "testNamespaceRestrictionsNonMainOrgBySuperAdmin - should be able to update customer's namespace." );
            assertNotNull ( fiOrg.getClientTag () );
            assertEquals ( newClientTag,  fiOrg.getClientTag () );
        }
        catch ( Exception e )
        {
            log.error ( "testNamespaceRestrictionsNonMainOrgBySuperAdmin - Exception", e );
            fail( "testNamespaceRestrictionsNonMainOrgBySuperAdmin", e );
        }
    }

    private boolean isMessageSenderInit()
    {
        Map<String, MessageSender> map = MessageSenderFactory.getAllMessageSenders ();
        return map.containsKey ( "ConfigX" );
    }

    private void setBrokerOrg( Organization org, Organization brokerOrg ) throws Exception
    {
        User mainUser = UserFactory.getUser ( "Integral@MAIN" );
        IdcUtilC.setSessionContextUser ( mainUser );
        IdcTransaction tx = IdcSessionManager.getInstance ().newTransaction ( mainUser  );
        tx.getUOW ().removeAllReadOnlyClasses ();
        Organization registeredOrg = ( Organization ) org.getRegisteredObject ();
        if ( brokerOrg != null )
        {
            Organization registeredBrokerOrg = ( Organization ) brokerOrg.getRegisteredObject ();
            registeredOrg.setBrokerOrganization ( registeredBrokerOrg );
        }
        else
        {
            registeredOrg.setBrokerOrganization ( null );
        }
        tx.commit ();
    }
}
