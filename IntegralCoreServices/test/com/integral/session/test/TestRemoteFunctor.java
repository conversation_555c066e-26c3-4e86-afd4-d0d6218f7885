package com.integral.session.test;

import com.integral.session.RemoteTransactionNotification;

import java.util.HashMap;
import java.util.Map;

/**
 * Tests the remote functor execution
 */
public class TestRemoteFunctor implements RemoteTransactionNotification
{
    public static Map<String, String> threadMap = new HashMap<String, String>();

    public void onCommit( HashMap props )
    {
        for ( Object prop : props.keySet() )
        {
            String key = ( String ) prop;
            threadMap.put( key, Thread.currentThread().getName() );
        }
    }
}
