package com.integral.session.test;

import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;

/**
 * Tests the session framework.
 */
public class SessionPTestC
        extends PTestCaseC
{
    static String name = "Session Test";

    public SessionPTestC( String name )
    {
        super( name );
    }

    /**
     * Tests the session commit.
     * This test creates an audit event.
     */
    public void testCommit()
    {
        // create audit event
        AuditEvent event = new AuditEventC( "TEST", "SessionTest" );
        event.setStatus( 'T' );

        // start a transaction
        IdcSessionContext ctx = null;
        IdcTransaction xact = null;
        try
        {
            ctx = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            xact = IdcSessionManager.getInstance().newTransaction( ctx );
        }
        catch ( Exception e )
        {
            fail( "error starting transaction: " + e );
        }

        // register entity and commit new entity
        try
        {
            log.info( "start commiting new audit object" + " with hashCode " + event.hashCode() );
            xact.getUOW().removeAllReadOnlyClasses();
            AuditEvent event1 = ( AuditEvent ) xact.getUOW().registerObject( event );
            log.info( "  completed commiting new audit object" + " with hashCode " + event.hashCode() );
        }
        catch ( Exception e )
        {
            fail( "Failed in AuditEvent : ", e );
        }

        // commit transaction
        try
        {
            xact.commit();
        }
        catch ( Exception e )
        {
            fail( "error committing transaction: " + e );
        }

    }

}
