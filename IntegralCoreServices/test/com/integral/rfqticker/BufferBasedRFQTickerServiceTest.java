package com.integral.rfqticker;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import junit.framework.TestCase;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.rfqticker.config.MockRFQTickerConfig;
import com.integral.rfqticker.config.RFQTickerConfig;
import com.integral.ticker.TickerFactory;
import com.integral.ticker.TickerService;
import com.integral.ticker.cache.BufferBasedTickEventCacheC;
import com.integral.ticker.cache.TickEventCache;
import com.integral.ticker.cache.TickEventReaderState;
import com.integral.ticker.publish.TickEventPublisher;
import com.integral.ticker.publish.TickEventPublisherC;
import com.integral.tradeticker.publish.BaseTradeTickerServicePublisherC;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 4/8/13
 * Time: 12:06 PM
 */
public class BufferBasedRFQTickerServiceTest extends TestCase {

    private static Log log = LogFactory.getLog(SnapShotBasedRFQTickerServiceTest.class);
    private TickerService<RFQTickEvent, RFQTickerConfig> tickerService;
    private static final RFQTickerConfig config = new MockRFQTickerConfig();
    private static final String[] currencyPairs = {
    	"USD/INR", "EUR/USD", "USD/JPY", "GBP/USD"
    };

    private final TickEventPublisher<RFQTickEvent> tickPublisher;

    public BufferBasedRFQTickerServiceTest() {
        try {
            tickPublisher = new TickEventPublisherC<RFQTickEvent>(config, new RFQTickEventSerializer(config));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    
    protected void setUp() throws java.lang.Exception {
        TickerFactory.getInstance().setRFQTickerConfig(config);
        TickEventCache<RFQTickEvent> cache = new BufferBasedTickEventCacheC<RFQTickEvent>(config);
        TickerFactory.getInstance().setCache(cache, RFQTickEvent.class);
        TickerFactory.getInstance().setTickerPublisher(new BaseTradeTickerServicePublisherC(config));
        //tickerService = new TradeTickerServiceC<TradeTickEvent, TradeTickerConfig>(config);
        tickerService = TickerFactory.getInstance().getTickerService(RFQTickEvent.class);
        tickerService.start();
    }

    protected void tearDown() throws java.lang.Exception {
        tickerService.stop();
    }

    public void testTickerViewUpdate() throws Exception {
        int i = 0;
        for (String cp : currencyPairs) {
        	RFQTickEvent rfqTickEvent = new RFQTickEvent();
        	rfqTickEvent.setCurrencyPair_(cp);
        	rfqTickEvent.setDealtCurrency(cp.substring(0, 3));
        	rfqTickEvent.setAmount(1000 + i);
        	int value = i % 2;
        	rfqTickEvent.setStatus(value+"");
        	rfqTickEvent.setRequestId("10" + i);
        	rfqTickEvent.setSide(i % 2 == 0 ? "B" : "O");
        	rfqTickEvent.setTimestamp(System.currentTimeMillis());
        	rfqTickEvent.setTenor("test" + i);
        	tickPublisher.publishEvent(rfqTickEvent);
            i++;
        }
        
        log.info("Published Trade Ticker Events");
        Thread.sleep(200);
        int noOfCurrencyPairs = currencyPairs.length;
        RFQTickEvent event = tickerService.getEventView().getLatestEvent("ALL");  
        String lastCurrPair = currencyPairs[noOfCurrencyPairs-1];
        assertNotNull("RFQTickEvent not published for Cp:" + lastCurrPair , event);
        assertEquals("Published Currency pair is not matching with expected", lastCurrPair , event.getCurrencyPair());
        assertEquals("Dealt Currency does not match with expected", lastCurrPair.substring(0, 3) , event.getDealtCurrency());
        assertEquals("Amount does not match with expected", 1000+i-1 , event.getAmount(), 0.0000000000000001);
        int value = (i-1) % 2;
        assertEquals("Event Type does not match with expected", value+"" , event.getStatus());
        assertEquals("Request ID does not match with expected", "10" + (i-1) , event.getRequestId());
        String side = ((i-1) % 2 == 0 ? "B" : "O");
        assertEquals("Side does not match with expected", side , event.getSide());
        assertEquals("Tenor does not match with expected", "test" + (i-1) , event.getTenor());
        
        List<RFQTickEvent> events = new ArrayList<RFQTickEvent>();
        final TickEventReaderState readerState = new TickEventReaderState("Test-" + "ALL");
        event = tickerService.getEventView().getNextEvent(readerState, 0);
        
        while (event != null) {
            events.add(event);
            event = tickerService.getEventView().getNextEvent(readerState, 0);
        }
        i = 0;
        for (RFQTickEvent evnt : events) {
        	String currPair = currencyPairs[i];
            assertNotNull("RFQTickEvent not published for Cp:" + currPair , evnt);
            assertEquals("Published Currency pair is not matching with expected", currPair , evnt.getCurrencyPair());
            assertEquals("Dealt Currency does not match with expected", currPair.substring(0, 3) , evnt.getDealtCurrency());
            assertEquals("Amount does not match with expected", 1000+i , evnt.getAmount(), 0.00000000000000001);
            value = i % 2;
            assertEquals("Event Type does not match with expected", value+"" , evnt.getStatus());
            assertEquals("Request ID does not match with expected", "10" + i , evnt.getRequestId());
            side = (i % 2 == 0 ? "B" : "O");
            assertEquals("Side does not match with expected", side , evnt.getSide());
            assertEquals("Tenor does not match with expected", "test" + i , evnt.getTenor());
        	i++;
        }  
    }

}
