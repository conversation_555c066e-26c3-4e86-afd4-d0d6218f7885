package com.integral.rfqticker;

import com.integral.is.ISCommonConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.rfqticker.config.MockRFQTickerConfig;
import com.integral.rfqticker.config.RFQTickerConfig;
import com.integral.rfqticker.config.RFQTickerConfigMBeanC;
import com.integral.ticker.TickerFactory;
import com.integral.ticker.TickerService;
import com.integral.ticker.TickerServiceC;
import com.integral.ticker.cache.SnapshotBasedTickEventCacheC;
import com.integral.ticker.cache.TickEventCache;
import com.integral.ticker.cache.TickerSnapshot;
import com.integral.ticker.cache.TickerSnapshotC;
import com.integral.ticker.publish.TickEventPublisher;
import com.integral.ticker.publish.TickEventPublisherC;
import com.integral.ticker.publish.TickEventServicePublisherC;
import junit.framework.TestCase;

import java.io.IOException;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 3/14/13
 * Time: 1:45 PM
 */
public class SnapShotBasedRFQTickerServiceTest extends TestCase {
    private static Log log = LogFactory.getLog(SnapShotBasedRFQTickerServiceTest.class);
    private TickerService<RFQTickEvent, RFQTickerConfig> tickerService;
    private static final RFQTickerConfig config = new MockRFQTickerConfig();

    private final TickEventPublisher<RFQTickEvent> tickPublisher;
    private final SnapShotBasedMockRFQTickerViewHandler<RFQTickEvent> viewHandler = 
    									new SnapShotBasedMockRFQTickerViewHandler<RFQTickEvent>();
    private static final String[] currencyPairs = {
    	"USD/INR", "EUR/USD", "USD/JPY", "GBP/USD"
    };

    public SnapShotBasedRFQTickerServiceTest() {
        try {
            tickPublisher = new TickEventPublisherC<RFQTickEvent>(config, new RFQTickEventSerializer(config));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    protected void setUp() throws java.lang.Exception {
        TickerFactory.getInstance().setRFQTickerConfig(config);
        TickEventCache<RFQTickEvent> cache = new SnapshotBasedTickEventCacheC<RFQTickEvent>(config); 
        TickerFactory.getInstance().setCache(cache, RFQTickEvent.class);
        TickerFactory.getInstance().setTickerPublisher( new TickEventServicePublisherC<RFQTickEvent>( cache, config ) );
        tickerService = new TickerServiceC<RFQTickEvent, RFQTickerConfig>(config, cache, 
        									TickerFactory.getInstance().getEventSerializer(RFQTickEvent.class), TickerFactory.getInstance().getHighLowDataCache( RFQTickEvent.class ), ISCommonConstants.RFQ_TICKER_SERVICE_LISTENER_THREAD_NAME);
        viewHandler.reset();
        tickerService.start();
    }

    protected void tearDown() throws java.lang.Exception {
        tickerService.stop();
    }

    public void testTickerViewUpdate() throws Exception {
    	tickerService.getEventServicePublisher().subscribe(viewHandler);
        int i = 0;
        for (String cp : currencyPairs) {
        	RFQTickEvent rfqTickEvent = new RFQTickEvent();
        	rfqTickEvent.setCurrencyPair_(cp);
        	rfqTickEvent.setDealtCurrency(cp.substring(0, 3));
        	rfqTickEvent.setAmount(1000 + i);
            int value = i % 2;
            rfqTickEvent.setStatus(value+"");
        	rfqTickEvent.setRequestId("10" + i);
        	rfqTickEvent.setSide(i % 2 == 0 ? "B" : "O");
        	rfqTickEvent.setTimestamp(System.currentTimeMillis());
        	rfqTickEvent.setTenor("test" + i);
        	tickPublisher.publishEvent(rfqTickEvent);
            i++;
        }
        
        log.info("Published Trade Ticker Events");
        Thread.sleep(200);
        TickerSnapshot<RFQTickEvent> snapshot = viewHandler.getNextTickerSnapshot(config.getTickerPublishIntervalMillis() * 100);
        RFQTickEvent event = snapshot.getLatestEvent(config.getBufferKeyIndex(RFQTickerConfigMBeanC.SUPPORTED_CURRENCY_PAIRS));
        int noOfCurrencyPairs = currencyPairs.length;
        String lastCurrPair = currencyPairs[noOfCurrencyPairs-1];
        assertNotNull("RFQTickEvent not published for Cp:" + lastCurrPair , event);
        assertEquals("Published Currency pair is not matching with expected", lastCurrPair , event.getCurrencyPair());
        assertEquals("Dealt Currency does not match with expected", lastCurrPair.substring(0, 3) , event.getDealtCurrency());
        assertEquals("Amount does not match with expected", 1000+i-1 , event.getAmount(), 0.0000000000000001);
        int value = (i-1) % 2;
        assertEquals("Event Type does not match with expected", value+"" , event.getStatus());
        assertEquals("Request ID does not match with expected", "10" + (i-1) , event.getRequestId());
        String side = ((i-1) % 2 == 0 ? "B" : "O");
        assertEquals("Side does not match with expected", side , event.getSide());
        assertEquals("Tenor does not match with expected", "test" + (i-1) , event.getTenor());
        
        i = 0;
        List<RFQTickEvent> rfqTickEvents = ((TickerSnapshotC<RFQTickEvent>) snapshot).getEvent(0);
        for (RFQTickEvent evnt : rfqTickEvents) {
        	String currPair = currencyPairs[i];
            assertNotNull("RFQTickEvent not published for Cp:" + currPair , evnt);
            assertEquals("Published Currency pair is not matching with expected", currPair , evnt.getCurrencyPair());
            assertEquals("Dealt Currency does not match with expected", currPair.substring(0, 3) , evnt.getDealtCurrency());
            assertEquals("Amount does not match with expected", 1000+i , evnt.getAmount(), 0.00000000000000001);
            value = i % 2;
            assertEquals("Event Type does not match with expected", value+"", evnt.getStatus());
            assertEquals("Request ID does not match with expected", "10" + i , evnt.getRequestId());
            side = (i % 2 == 0 ? "B" : "O");
            assertEquals("Side does not match with expected", side , evnt.getSide());
            assertEquals("Tenor does not match with expected", "test" + i , evnt.getTenor());
        	i++;
        }
    }

}
