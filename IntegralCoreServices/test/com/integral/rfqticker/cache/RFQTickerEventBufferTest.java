package com.integral.rfqticker.cache;

import java.util.ArrayList;
import java.util.List;

import com.integral.rfqticker.config.RFQTickerConfig;
import junit.framework.TestCase;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.rfqticker.RFQTickEvent;
import com.integral.ticker.cache.TickEventBuffer;
import com.integral.ticker.cache.TickEventReaderState;
import com.integral.rfqticker.config.MockRFQTickerConfig;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 5/2/13
 * Time: 10:40 AM
 */
public class RFQTickerEventBufferTest extends TestCase {

    private static Log log = LogFactory.getLog(RFQTickerEventBufferTest.class);
    private TickEventBuffer<RFQTickEvent> tickerBuffer;
    private static final RFQTickerConfig config = new MockRFQTickerConfig();
    public static final String CURRENCY_PAIR = config.getSupportedBufferKeys().get(0);
    private static final String[] currencyPairs = {
    	"USD/INR", "EUR/USD", "USD/JPY", "GBP/USD"
    };
    private static final String RFS_STATUS = "RSACTIVE";

    protected void setUp() throws java.lang.Exception {
        tickerBuffer = new TickEventBuffer<RFQTickEvent>(config, "Test-" + CURRENCY_PAIR);
    }

    protected void tearDown() throws java.lang.Exception {
        tickerBuffer.cleanUp();
    }

    public void testSingleReaderEmptyRead() throws Exception {
        TickEventReaderState reader = new TickEventReaderState("Test");
        assertNull("Buffer should be empty", tickerBuffer.getLatestEvent());
        assertNull("Buffer should be empty", tickerBuffer.getNextEvent(reader));
        assertEquals("Reader should still be at initial value as nothing was read.", TickEventReaderState.INITIAL_VALUE, reader.readerCounter);
    }

    public void testSingleReaderFirstRead() throws Exception {
        TickEventReaderState reader = new TickEventReaderState("Test-" + CURRENCY_PAIR);
        final int maxEventPerCP = config.getMaxEventsSentPerBufferKey();
        List<RFQTickEvent> sentEvents = new ArrayList<RFQTickEvent>(4 * maxEventPerCP);
        
      	String cp = currencyPairs[0];
        RFQTickEvent rfqTickEvent = new RFQTickEvent();
    	rfqTickEvent.setCurrencyPair_(cp);
    	rfqTickEvent.setDealtCurrency(cp.substring(0, 3));
    	rfqTickEvent.setAmount(1000);
    	rfqTickEvent.setStatus(RFS_STATUS);
    	rfqTickEvent.setRequestId("10");
    	rfqTickEvent.setSide("B");
    	rfqTickEvent.setTimestamp(System.currentTimeMillis());
    	rfqTickEvent.setTenor("test");
        
        
        tickerBuffer.addEvent(rfqTickEvent);
        sentEvents.add(rfqTickEvent);
        RFQTickEvent firstEvent = rfqTickEvent;
        for (int i = 1; i < 3 * maxEventPerCP; ++i) {  //Let the buffer overflow and check with each added event.
            assertEquals("Buffer should return latest event", rfqTickEvent, tickerBuffer.getLatestEvent());
            assertEquals("Buffer should return first/earliest event", firstEvent, tickerBuffer.getNextEvent(reader));
            assertEquals("ReaderState should point to the earliest event stored in the buffer.", firstEvent.getId(), reader.readerCounter);
            //reset Reader
            reader.readerCounter = TickEventReaderState.INITIAL_VALUE;
            //add another event
            rfqTickEvent = new RFQTickEvent();
        	rfqTickEvent.setCurrencyPair_(cp);
        	rfqTickEvent.setDealtCurrency(cp.substring(0, 3));
        	rfqTickEvent.setAmount(1000);
        	rfqTickEvent.setStatus(RFS_STATUS);
        	rfqTickEvent.setRequestId("10");
        	rfqTickEvent.setSide("B");
        	rfqTickEvent.setTimestamp(System.currentTimeMillis());
        	rfqTickEvent.setTenor("test");            
        	tickerBuffer.addEvent(rfqTickEvent);
            sentEvents.add(rfqTickEvent);
            if (i - maxEventPerCP + 1 > 0) {
                firstEvent = sentEvents.get(i - maxEventPerCP + 1);
            }
            int j = 0;
            final int k = sentEvents.size() - maxEventPerCP;
            for (RFQTickEvent sentEvent : sentEvents) {
                if (k < 0 || k <= j) {
                    assertEquals("Buffer should return correct sent event", sentEvent, tickerBuffer.getEvent(j++));
                } else {
                    assertEquals("Buffer can't return correct sent event", null, tickerBuffer.getEvent(j++));
                }
            }
        }
    }

    public void testSingleReaderSlowRead() throws Exception {
        TickEventReaderState reader = new TickEventReaderState("Test-" + CURRENCY_PAIR);
        final int maxEventPerCP = config.getMaxEventsSentPerBufferKey();
        List<RFQTickEvent> sentEvents = new ArrayList<RFQTickEvent>(4 * maxEventPerCP);
        String cp = currencyPairs[0];
        RFQTickEvent event = new RFQTickEvent();
        event.setCurrencyPair_(cp);
        event.setDealtCurrency(cp.substring(0, 3));
        event.setAmount(1000);
        event.setStatus(RFS_STATUS);
        event.setRequestId("10");
        event.setSide("B");
        event.setTimestamp(System.currentTimeMillis());
        event.setTenor("test");        
        tickerBuffer.addEvent(event);
        sentEvents.add(event);
        assertEquals("Buffer should return latest event", event, tickerBuffer.getLatestEvent());
        assertEquals("Buffer should return first/earliest event", event, tickerBuffer.getNextEvent(reader));
        assertEquals("ReaderState should point to the earliest event stored in the buffer.", event.getId(), reader.readerCounter);
        for (int i = 1; i < 3 * maxEventPerCP; ++i) {  //Let the buffer overflow and check with each added event.
            //add another event
        	event = new RFQTickEvent();
            event.setCurrencyPair_(cp);
            event.setDealtCurrency(cp.substring(0, 3));
            event.setAmount(1000);
            event.setStatus(RFS_STATUS);
            event.setRequestId("10");
            event.setSide("B");
            event.setTimestamp(System.currentTimeMillis());
            event.setTenor("test");        
            tickerBuffer.addEvent(event);
            sentEvents.add(event);
        }
        for (int j = sentEvents.size() - maxEventPerCP; j < sentEvents.size(); ++j) {
            event = sentEvents.get(j);
            assertEquals("Buffer should return first/earliest event", event, tickerBuffer.getNextEvent(reader));
            assertEquals("ReaderState should point to the earliest event stored in the buffer.", event.getId(), reader.readerCounter);
        }
        for (int i = 0; i < 3 * maxEventPerCP + maxEventPerCP / 2; ++i) {  //Let the buffer overflow and check with each added event.
            //add another event
        	event = new RFQTickEvent();
            event.setCurrencyPair_(cp);
            event.setDealtCurrency(cp.substring(0, 3));
            event.setAmount(1000);
            event.setStatus(RFS_STATUS);
            event.setRequestId("10");
            event.setSide("B");
            event.setTimestamp(System.currentTimeMillis());
            event.setTenor("test");        
            tickerBuffer.addEvent(event);
            sentEvents.add(event);
        }
        for (int j = sentEvents.size() - maxEventPerCP; j < sentEvents.size(); ++j) {
            event = sentEvents.get(j);
            assertEquals("Buffer should return first/earliest event", event, tickerBuffer.getNextEvent(reader));
            assertEquals("ReaderState should point to the earliest event stored in the buffer.", event.getId(), reader.readerCounter);
        }
        for (int i = 0; i < 3 * maxEventPerCP - 1; ++i) {  //Let the buffer overflow and check with each added event.
            //add another event
        	event = new RFQTickEvent();
            event.setCurrencyPair_(cp);
            event.setDealtCurrency(cp.substring(0, 3));
            event.setAmount(1000);
            event.setStatus(RFS_STATUS);
            event.setRequestId("10");
            event.setSide("B");
            event.setTimestamp(System.currentTimeMillis());
            event.setTenor("test");        
            tickerBuffer.addEvent(event);
            sentEvents.add(event);
        }
        for (int j = sentEvents.size() - maxEventPerCP; j < sentEvents.size(); ++j) {
            event = sentEvents.get(j);
            assertEquals("Buffer should return first/earliest event", event, tickerBuffer.getNextEvent(reader));
            assertEquals("ReaderState should point to the earliest event stored in the buffer.", event.getId(), reader.readerCounter);
        }
    }

    public void testSingleReaderConcurrentRead() throws Exception {
        TickEventReaderState reader = new TickEventReaderState("Test-" + CURRENCY_PAIR);
        final int maxEventPerCP = config.getMaxEventsSentPerBufferKey();
        String cp = currencyPairs[0];
        RFQTickEvent event = new RFQTickEvent();
        event.setCurrencyPair_(cp);
        event.setDealtCurrency(cp.substring(0, 3));
        event.setAmount(1000);
        event.setStatus(RFS_STATUS);
        event.setRequestId("10");
        event.setSide("B");
        event.setTimestamp(System.currentTimeMillis());
        event.setTenor("test");        
        tickerBuffer.addEvent(event);
        for (int i = 1; i < 3 * maxEventPerCP; ++i) {  //Let the buffer overflow and check with each added event.
            assertEquals("Buffer should return latest event", event, tickerBuffer.getLatestEvent());
            assertEquals("Buffer should return first/earliest event", event, tickerBuffer.getNextEvent(reader));
            assertEquals("ReaderState should point to the earliest event stored in the buffer.", event.getId(), reader.readerCounter);
            //add another event
            event = new RFQTickEvent();
            event.setCurrencyPair_(cp);
            event.setDealtCurrency(cp.substring(0, 3));
            event.setAmount(1000);
            event.setStatus(RFS_STATUS);
            event.setRequestId("10");
            event.setSide("B");
            event.setTimestamp(System.currentTimeMillis());
            event.setTenor("test");        
            tickerBuffer.addEvent(event);
        }
    }

    public void testMultipleReaderConcurrentRead() throws Exception {
        final int maxEventPerCP = config.getMaxEventsSentPerBufferKey();
        final List<RFQTickEvent> sentEvents = new ArrayList<RFQTickEvent>(4000);
        RFQTickEvent event;
        String cp = currencyPairs[0];
        for (int i = 0; i < 3000; ++i) {  //Let the buffer overflow and check with each added event.
            //add another event
        	event = new RFQTickEvent();
            event.setCurrencyPair_(cp);
            event.setDealtCurrency(cp.substring(0, 3));
            event.setAmount(1000);
            event.setStatus(RFS_STATUS);
            event.setRequestId("10");
            event.setSide("B");
            event.setTimestamp(System.currentTimeMillis());
            event.setTenor("test");        
            tickerBuffer.addEvent(event);
            sentEvents.add(event);
        }
        final TickEventReaderState reader1 = new TickEventReaderState("Test1-" + CURRENCY_PAIR);
        new Thread(new Runnable() {
            public void run() {
                RFQTickEvent event;
                for (int j = sentEvents.size() - maxEventPerCP; j < sentEvents.size(); ++j) {
                    event = sentEvents.get(j);
                    assertEquals("Buffer should return first/earliest event", event, tickerBuffer.getNextEvent(reader1));
                    assertEquals("ReaderState should point to the earliest event stored in the buffer.", event.getId(), reader1.readerCounter);
                }
            }
        }).start();
        final TickEventReaderState reader2 = new TickEventReaderState("Test2-" + CURRENCY_PAIR);
        new Thread(new Runnable() {
            public void run() {
                RFQTickEvent event;
                for (int j = sentEvents.size() - maxEventPerCP; j < sentEvents.size(); ++j) {
                    event = sentEvents.get(j);
                    assertEquals("Buffer should return first/earliest event", event, tickerBuffer.getNextEvent(reader2));
                    assertEquals("ReaderState should point to the earliest event stored in the buffer.", event.getId(), reader2.readerCounter);
                }
            }
        }).start();
    }
}
