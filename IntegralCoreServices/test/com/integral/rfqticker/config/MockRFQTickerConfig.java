package com.integral.rfqticker.config;

import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.*;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 3/14/13
 * Time: 1:46 PM
 */
public class MockRFQTickerConfig implements RFQTickerConfig {
   private static final String DEFAULT_MULTICAST_ADDRESS = "**********";
   public static final String SUPPORTED_CURRENCY_PAIRS = "ALL";
   private static final long DEFAULT_TICKER_PUBLISH_INTERVAL_MS = 250;
   private static final int DEFAULT_MULTICAST_MESSAGE_BUFFER_SIZE = 1024;
   private static final int DEFAULT_CONCURRENCY_FACTOR = 5;  
   private static final int DEFAULT_MULTICAST_PORT_RANGE_START = 6750;
   private static final boolean DEFAULT_EVENT_PUBLISH_ENABLED = true;
   private static final boolean DEFAULT_SERVICE_ENABLED = true;
   private static final int DEFAULT_MAX_TICK_EVENTS_PER_CP = 50;	

   private static final String[] currencyPairs = new String[]{SUPPORTED_CURRENCY_PAIRS};
   private static final Map<String, String> addressMap = new HashMap<String, String>(10);

   static {
       addressMap.put("EUR/USD", "**********");
   }

    private static final int CONCURRENCY_FACTOR = 1;
    public static final int MAX_EVENTS = 5;
    private SocketAddress[] socketAddrs;
    private List<String> supportedCurrencyPairs;
    private ListenerConfig[] listenerConfigs;
    private Map<String, Integer> cpIndexMap;

    public MockRFQTickerConfig() {
        supportedCurrencyPairs = Arrays.asList(currencyPairs);
        int startPort = DEFAULT_MULTICAST_PORT_RANGE_START;
        int concurrencyFactor = getListenerConcurrencyFactor();
        int[] multicastPorts = new int[concurrencyFactor];
        for (int i = 0; i < concurrencyFactor; ++i) {
            multicastPorts[i] = startPort + i;
        }
        String defaultAddr = addressMap.get("Default");
        if (defaultAddr == null) {
            defaultAddr = DEFAULT_MULTICAST_ADDRESS;
        }
        int cpSize = supportedCurrencyPairs.size();
        String[] multicastAddress = new String[cpSize];
        socketAddrs = new SocketAddress[cpSize];
        cpIndexMap = new HashMap<String, Integer>(cpSize);
        int numCpsPerPort = cpSize / concurrencyFactor;
        if (numCpsPerPort == 0) {
            numCpsPerPort = 1;
        }
        listenerConfigs = new ListenerConfig[concurrencyFactor];
        for (int l = 0; l < concurrencyFactor; ++l) {
            ListenerConfig config = new ListenerConfig();
            int numAddr = multicastAddress.length / concurrencyFactor + 1;
            config.multicastAddress = new HashSet<String>(numAddr);
            config.multicastPort = multicastPorts[l];
            listenerConfigs[l] = config;
        }
        int i = 0;
        for (String cp : supportedCurrencyPairs) {
            multicastAddress[i] = addressMap.get(cp);
            if (multicastAddress[i] == null) {
                multicastAddress[i] = defaultAddr;
            }
            cpIndexMap.put(cp, i);
            final int portNum = (i / numCpsPerPort) % concurrencyFactor;
            listenerConfigs[portNum].multicastAddress.add(multicastAddress[i]);
            socketAddrs[i] = new InetSocketAddress(multicastAddress[i], multicastPorts[portNum]);
            ++i;
        }

    }

    public List<String> getSupportedBufferKeys() {
        return supportedCurrencyPairs;
    }

    public int getMaxEventsStoredPerBuffer() {
        return MAX_EVENTS;
    }

    public int getMaxEventsSentPerBufferKey() {
        return MAX_EVENTS;
    }

    public int getBufferKeyIndex(String cp) {
        return cpIndexMap.get(cp);
    }

    public String getBufferKey(int index) {
        return supportedCurrencyPairs.get(index);
    }

    public SocketAddress getMulticastAddress(int cpIdx) {
        return socketAddrs[cpIdx];
    }

    public int getMulticastMessageBufferSize() {
        return DEFAULT_MULTICAST_MESSAGE_BUFFER_SIZE;
    }

    public int getListenerConcurrencyFactor() {
        return CONCURRENCY_FACTOR;
    }

    public ListenerConfig getListenerConfig(int concurrencyNum) {
        return listenerConfigs[concurrencyNum];
    }

    public long getTickerPublishIntervalMillis() {
        return DEFAULT_TICKER_PUBLISH_INTERVAL_MS;
    }

    public boolean isTickEventPublishingEnabled() {
        return DEFAULT_EVENT_PUBLISH_ENABLED;
    }

    public boolean isTickServiceEnabled() {
        return DEFAULT_SERVICE_ENABLED;
    }

    public boolean isTickEventLoggingEnabled() {
        return false;
    }

    public int getConnectionTimeoutInMilliSec() { return DEFAULT_CONNECTION_TIMEOUT_IN_MS; }

    public int getSocketTimeoutInMilliSec() { return DEFAULT_SOCKET_TIMEOUT_IN_MS;}
}
