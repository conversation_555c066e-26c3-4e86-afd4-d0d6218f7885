package com.integral.rfqticker;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import com.integral.ticker.TickEvent;
import com.integral.ticker.cache.TickerSnapshot;
import com.integral.ticker.publish.TickEventViewHandler;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 3/14/13
 * Time: 3:14 PM
 */
public class SnapShotBasedMockRFQTickerViewHandler<T extends TickEvent> implements TickEventViewHandler {

    private volatile TickerSnapshot<T> lastTickerSnapshot;
    private Lock lock = new ReentrantLock();
    private Condition viewUpdatedCondition = lock.newCondition();
    boolean viewUpdated = false;

    public void reset() {
        lastTickerSnapshot = null;
    }

    @SuppressWarnings("unchecked")
    public void viewUpdated(Object snapshot) {
        lastTickerSnapshot = (TickerSnapshot<T>) snapshot;
        lock.lock();
        try {
            viewUpdated = true;
            viewUpdatedCondition.signal();
        } finally {
            lock.unlock();
        }
    }

    public TickerSnapshot<T> getLastTickerSnapshot() {
        return lastTickerSnapshot;
    }

    public TickerSnapshot<T> getNextTickerSnapshot(long timeMillis) throws InterruptedException, TimeoutException {
        lock.lock();
        try {
            viewUpdated = false;
            if (!viewUpdatedCondition.await(timeMillis, TimeUnit.MILLISECONDS) || !viewUpdated) {
                throw new TimeoutException();
            }
            return lastTickerSnapshot;
        } finally {
            lock.unlock();
        }
    }
}
