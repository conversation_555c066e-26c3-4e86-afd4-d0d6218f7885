package com.integral.xml.mapping.test;

import com.integral.test.MBeanTestCaseC;
import com.integral.xml.mapping.MappingFactory;
import com.integral.xml.mapping.XMLMapping;

/**
 * <AUTHOR> Development Corporation.
 */
public class XMLMappingMBeanTestC extends MBeanTestCaseC
{
    XMLMapping xmlMappingMBean = ( XMLMapping ) MappingFactory.getXMLMappingMBean();

    public XMLMappingMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( xmlMappingMBean, "dateTimeFormat", "IDC.XML.DateTimeFormat", MBeanTestCaseC.STRING );
        testProperty( xmlMappingMBean, "timeFormat", "IDC.XML.TimeFormat", MBeanTestCaseC.STRING );
        testProperty( xmlMappingMBean, "transformerFactoryClassName", "IDC.XML.Stylesheet.TransformerFactory", MBeanTestCaseC.STRING );
        //todo: add method for array property
        System.out.println( "arr=" + xmlMappingMBean.getMappingPath() );
//        testProperty( xmlMappingMBean, "mappingPath", MAPPING_PATH_KEY, MBeanTestCaseC.STRING );
        testProperty( xmlMappingMBean, "timestampFormat", "IDC.XML.TimestampFormat", MBeanTestCaseC.STRING );
    }
}
