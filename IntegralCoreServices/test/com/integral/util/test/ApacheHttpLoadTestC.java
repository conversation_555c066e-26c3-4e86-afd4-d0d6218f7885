package com.integral.util.test;

import com.integral.test.TestCaseC;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
import org.apache.commons.httpclient.methods.GetMethod;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by IntelliJ IDEA.
 * User: pandit
 * Date: Sep 22, 2008
 * Time: 2:14:37 PM
 * To change this template use File | Settings | File Templates.
 */
public class ApacheHttpLoadTestC extends TestCaseC
{
    static int minpoolSize = 150;
    static int maxpoolSize = 150;
    static int keepAliveTime = 20;

    public ApacheHttpLoadTestC( String name )
    {
        super( name );
    }

    public void testApacheHttpLoad()
    {

        // Set the default host/protocol for the methods to connect to.
        // This value will only be used if the methods are not given an absolute URI
        // create an array of URIs to perform GETs on
        String urisToGet = "/admin/integral/coreadmin/system/configuration/apacheServerTest.jsp";

        final ArrayBlockingQueue<Runnable> queue = new ArrayBlockingQueue<Runnable>( maxpoolSize );
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor( minpoolSize, maxpoolSize, keepAliveTime, TimeUnit.SECONDS, queue );

        for ( int i = 0; i < maxpoolSize; i++ )
        {
            MultiThreadedHttpConnectionManager multiConnectManager = new MultiThreadedHttpConnectionManager();
            HttpClient httpClient = new HttpClient( multiConnectManager );
            httpClient.getHostConfiguration().setHost( "mveng11", 80, "http" );

            GetMethod get = new GetMethod( urisToGet );
            get.setFollowRedirects( true );
            threadPool.execute( new ApacheHttpLoadThread( httpClient, get, i ) );
        }

        while ( threadPool.getActiveCount() > 0 )
        {
            try
            {
                Thread.sleep( 1000 );
            }
            catch ( Exception e )
            {
                log.error( "Exception in sleep : ", e );
            }
        }

    }

    public class ApacheHttpLoadThread implements Runnable
    {
        private HttpClient httpClient;
        private GetMethod method;
        private int id;

        public ApacheHttpLoadThread( HttpClient httpClient, GetMethod method, int id )
        {
            this.httpClient = httpClient;
            this.method = method;
            this.id = id;
        }

        public void run()
        {
            try
            {
                long start = System.nanoTime();
                // execute the method
                httpClient.executeMethod( method );
                log.warn( "Sending Request : " + id );
                byte[] bytes = method.getResponseBody();
                long stop = System.nanoTime();
                if ( stop - start > 500000000 )
                {
                    log.warn( "Time Taken : " + ( stop - start ) / 1000000.0 );
                }

            }
            catch ( Exception e )
            {
                log.error( id + " - error: ", e );
            }
            finally
            {
                // always release the connection after we're done
                method.releaseConnection();
                log.info( id + " - connection released" );
            }
        }
    }
}
