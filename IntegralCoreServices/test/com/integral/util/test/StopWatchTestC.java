package com.integral.util.test;

import com.integral.util.StopWatchC;
import junit.framework.TestCase;


public class StopWatchTestC
        extends TestCase
{
    public void testShortPeriod()
    {
        try
        {
            StopWatchC watch = new StopWatchC();
            watch.start( "test start short period" );
            watch.stopAndLog();
        }
        catch ( Exception e )
        {
        }
    }

    public void testLongPeriod()
    {
        try
        {
            StopWatchC watch = new StopWatchC();
            watch.start( "test start long period" );
            Thread.sleep( 3000 );
            watch.stopAndLog();
        }
        catch ( Exception e )
        {
        }
    }
}
