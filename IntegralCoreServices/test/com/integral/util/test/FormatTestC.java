package com.integral.util.test;


import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDateTime;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.user.UserFactory;
import com.integral.util.FormatFactory;
import junit.framework.TestCase;

import java.text.DecimalFormat;
import java.text.Format;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.TimeZone;


public class FormatTestC
        extends TestCase
{
    static String name = "Format Test";

    Log log = LogFactory.getLog( this.getClass() );

    public FormatTestC( String name )
    {
        super( name );
    }

    public void formatUser()
    {
        try
        {
            User user = UserFactory.newUser( "joe" );
            Format format = FormatFactory.getFormat( UserC.class );

            System.out.println( "User " + user.getShortName()
                    + ": <" + format.format( user ) + '>' );
        }
        catch ( Exception e )
        {
        }

    }

    public void formatDateTime()
    {
        try
        {
            IdcDateTime now = DateTimeFactory.newDateTime();
            Format format = FormatFactory.getFormat( IdcDateTime.class );

            System.out.println( "IdcDateTime " + now
                    + ": <" + format.format( now ) + '>' );
        }
        catch ( Exception e )
        {
            System.out.println( "error formatting date-time: " + e );
        }

    }

    public void formatJavaSimpleDateTime()
    {
        System.out.println( "############ Test java.text.SimpleDateFormat " );

        String localeLanguage = ConfigurationFactory.getServerMBean().getSystemLocaleLanguage();
        String localeCountry = ConfigurationFactory.getServerMBean().getSystemLocaleCountry();
        String localeInfoString;

        try
        {
            TimeZone tm = TimeZone.getTimeZone( "GMT" );
            TimeZone.setDefault( tm );

            Locale.setDefault( new Locale( localeLanguage, localeCountry ) );
            localeInfoString = " System locale set to: " + Locale.getDefault().toString();
        }
        catch ( Exception e )
        {
            localeInfoString = " Error setting system locale set to: " + localeLanguage + '/' + localeCountry + '\n';
            localeInfoString += " Exception message: " + e + '\n';
        }
        System.out.println( "############ Locale set to " + localeInfoString );

        try
        {
            SimpleDateFormat sdt1 = new SimpleDateFormat();
            System.out.println( "java.text.SimpleDateFormat " + sdt1 );

            Class sdtClass = Class.forName( "java.text.SimpleDateFormat" );
            Object sdt2 = sdtClass.newInstance();
            System.out.println( "java.text.SimpleDateFormat " + sdt2 );
        }
        catch ( Exception e )
        {
            System.out.println( "error creating java simple date-time: " + e );
        }

    }


    public void formatInteger()
    {
        try
        {
            Integer result = 10;
            Format format = FormatFactory.getFormat( Integer.class );

            System.out.println( "Integer " + result
                    + ": <" + format.format( result ) + '>' );
        }
        catch ( Exception e )
        {
            System.out.println( "error formatting integer: " + e );
        }

    }

    public void testParse()
    {
        String msg = "User Jeffwarner from organization Suntrust executed the following trade:\n" +
                '\n' +
                "\tTrade Transaction ID  : FXI109001\n" +
                "\tTrade Execution Date  : 15/12/2004\n" +
                "\tTrade Execution Time  : 14:55:03\n" +
                "\tOrganization          : Suntrust\n" +
                "\tOrganization Role     : FI\n" +
                "\tType                  : Quick Trade\n" +
                "\tTrade Type            : FXSPOT\n" +
                "\tCurrency Pair         : USD/CAD\n" +
                "\tDealt Amount          : 1000000.0\n" +
                "\tSettled Amount        : 1231900.0\n" +
                "\tSettled Amount in USD : 1000000.0\n" +
                "\tBuy Ccy               : USD\n" +
                "\tSell Ccy              : CAD\n" +
                "\tUser Id               : Jeffwarner";
        String value = getValue( msg, "User Id" );
        //String value = getValue(msg,"Organization");
        System.out.println( "value = <" + value + '>' );
        // String orgName  = getValue(msg,"Organization");
    }


    protected String getValue( String msg, String key )
    {
        String result = null;

        int start = msg.indexOf( key );
        if ( start > 0 )
        {
            System.out.println( "start = " + start );
            start = msg.indexOf( ':', start );
            System.out.println( "start = " + start );
            if ( start > 0 )
            {
                start++;
                start++;
                System.out.println( "start = " + start );
                System.out.println( "start substring = <" + msg.substring( start ) + '>' );
                int end = msg.indexOf( '\n', start );
                System.out.println( "end = " + end );
                if ( end == -1 )
                {
                    end = msg.indexOf( ' ', start );
                    System.out.println( "end = " + end );
                }
                if ( end > start )
                {
                    result = msg.substring( start, end );
                }
                else
                {
                    result = msg.substring( start );
                }
            }
        }
        return result;
    }

    /**
     * Test maximum numbers for int and long for sequence numbers
     */
    public void testNumberMaximums()
    {
        long max_long = Long.MAX_VALUE;
    }


    public void testDecimalFormatConstructorSpeed()
    {
        int numIterations = 10000;

        // use contructor
        long start = System.currentTimeMillis();
        for ( int i = 0; i < numIterations; i++ )
        {
            DecimalFormat df = new DecimalFormat( "#,##,###,####" );
            String s = df.format( 12345678 );
            System.out.println( s );
        }
        long end = System.currentTimeMillis();
        long time1 = end - start;

        // use factory
        start = System.currentTimeMillis();
        for ( int i = 0; i < numIterations; i++ )
        {
            NumberFormat nf = NumberFormat.getInstance();
            if ( nf instanceof DecimalFormat )
            {
                DecimalFormat df = ( DecimalFormat ) nf;
                df.applyPattern( "#,##,###,####" );
                String s = df.format( 12345678 );
                System.out.println( s );
            }
        }
        end = System.currentTimeMillis();
        long time2 = end - start;

        // use clone
        DecimalFormat master = new DecimalFormat( "#,##,###,##,##" );
        start = System.currentTimeMillis();
        for ( int i = 0; i < numIterations; i++ )
        {
            NumberFormat nf = NumberFormat.getInstance();
            if ( nf instanceof DecimalFormat )
            {
                DecimalFormat df = ( DecimalFormat ) master.clone();
                String s = df.format( 12345678 );
                System.out.println( s );
            }
        }
        end = System.currentTimeMillis();
        long time3 = end - start;

        System.out.println( "time taken for DecimalFormat(): " + time1 + "msec" );
        System.out.println( "time taken for NumberFormat.DecimalFormat(): " + time2 + "msec" );
        System.out.println( "time taken for DecimalFormat.clone(): " + time3 + "msec" );
    }
}
