package com.integral.broker.admin.test;

import com.integral.broker.admin.AdminFactory;
import com.integral.broker.admin.StreamScheduleService;
import com.integral.broker.model.ModelFactory;
import com.integral.broker.model.TODCutoffTiming;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairC;
import com.integral.persistence.Namespace;
import com.integral.persistence.NamespaceC;
import com.integral.provider.ProviderOrgFunction;
import com.integral.provider.ProviderOrgFunctionC;
import com.integral.query.QueryServiceC;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationFunction;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.user.UserPermissionClassification;

import java.sql.Time;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


public class StreamScheduleServicePTestC extends PTestCaseC {
    final String name = "Stream Schedule Service Test";

    final StreamScheduleService streamScheduleService = AdminFactory.getInstance().getStreamScheduleService();

    public StreamScheduleServicePTestC( final String aName ) {
        super( aName );
    }

    public void testStreamScheduleService() {
        try {
            IdcTransaction tx = IdcSessionManager.getInstance().newTransaction( "Integral1@MAIN" );
            long date = System.currentTimeMillis();
            Organization org = UserFactory.newOrganization();
            org = ( Organization ) org.getRegisteredObject();
            Namespace ns = ( Namespace ) new NamespaceC().getRegisteredObject();
            ns.setShortName( "TEST" + date );
            org.setShortName( "TEST" + date );
            User user = UserFactory.newUser( "TEST" + date );
            user = ( User ) user.getRegisteredObject();
            user.setOrganization( org );
            UserPermissionClassification permission = loadUserPermissionClassification();
            user.putCustomField( "DirectFX_Role", permission );
            org.addUser( user );
            //setup stream
            ProviderOrgFunction provOrgFuncForLP;
            provOrgFuncForLP = new com.integral.provider.ProviderOrgFunctionC( "PROVIDER" );
            provOrgFuncForLP = ( ProviderOrgFunctionC ) provOrgFuncForLP.getRegisteredObject();
            provOrgFuncForLP.setOrganization( org );
            QueryServiceC queryService = new QueryServiceC();
            Currency baseCcy = ( Currency ) queryService.find( CurrencyC.class, "EUR" );
            Currency varCcy = ( Currency ) queryService.find( CurrencyC.class, "USD" );
            baseCcy = ( Currency ) baseCcy.getRegisteredObject();
            varCcy = ( Currency ) varCcy.getRegisteredObject();
            CurrencyPair cp = new CurrencyPairC();
            cp.setBaseCurrency( baseCcy );
            cp.setVariableCurrency( varCcy );
            // Set the TODCutoffTimings.
            TODCutoffTiming todCutoffTiming = ModelFactory.getInstance().getTODCutoffTiming();
            todCutoffTiming = ( TODCutoffTiming ) todCutoffTiming.getRegisteredObject();
            todCutoffTiming.setProviderOrgFunction( provOrgFuncForLP );
            todCutoffTiming.setCurrencyPair( cp );
            todCutoffTiming.setCutoffTime( new Time( System.currentTimeMillis() ) );
            todCutoffTiming.setIsEnabled( true );
            todCutoffTiming.setSortOrder( 1 );
            Set<TODCutoffTiming> todSet = new HashSet<TODCutoffTiming>();
            todSet.add( todCutoffTiming );
            provOrgFuncForLP.setTODCutoffTimings( todSet );

            List<OrganizationFunction> orgFuncs = new ArrayList<OrganizationFunction>();
            orgFuncs.add( provOrgFuncForLP );
            org.setOrganizationFunctions( orgFuncs );
            tx.commitAndResume();

            //read stream and configuration now

            tx.release();

            // now query the data from DB.
            Organization org1 = ( Organization ) queryService.find( Organization.class, "TEST" + date );
            ProviderOrgFunction provOrg = org1.getProviderOrgFunction();
            Collection todSet1 = provOrg.getTODCutoffTimings();
            assert ( todSet1.size() > 0 );

            /**
             * Add a todcutoff timing to the provorg
             */
            TODCutoffTiming todCutoffTiming1 = ModelFactory.getInstance().getTODCutoffTiming();
            todCutoffTiming = ( TODCutoffTiming ) todCutoffTiming1.getRegisteredObject();
            todCutoffTiming1.setProviderOrgFunction( provOrgFuncForLP );
            todCutoffTiming1.setCurrencyPair( cp );
            todCutoffTiming1.setCutoffTime( new Time( System.currentTimeMillis() ) );
            todCutoffTiming1.setIsEnabled( true );
            todCutoffTiming1.setSortOrder( 2 );

            streamScheduleService.addCutoffTiming( todCutoffTiming1, provOrg );

            Collection<TODCutoffTiming> todSet2 = ( Collection<TODCutoffTiming> ) provOrg.getTODCutoffTimings();

            assert ( todSet2.size() > 1 );

            streamScheduleService.removeCutoffTiming( todCutoffTiming1, provOrg );

            Collection<TODCutoffTiming> todSet3 = ( Collection<TODCutoffTiming> ) provOrg.getTODCutoffTimings();

            assert ( todSet3.size() < 2 );

        }
        catch ( Exception e ) {
            IdcSessionManager.getInstance().setTransaction( null );
            fail( "Failed in testStreamService : ", e );
        }
    }

    protected UserPermissionClassification loadUserPermissionClassification() {
        return ( UserPermissionClassification ) ( new ReadNamedEntityC() )
                .execute( UserPermissionClassification.class, "MARKETMAKER" );
    }


}
