package com.integral.broker.admin.test;

import com.integral.broker.admin.AdminFactory;
import com.integral.broker.admin.ConfigurationService;
import com.integral.broker.admin.OrganizationService;
import com.integral.broker.admin.StreamService;
import com.integral.broker.model.Configuration;
import com.integral.broker.model.Stream;
import com.integral.finance.trade.Tenor;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.user.Organization;
import com.integral.user.UserFactory;
import junit.framework.Test;
import junit.framework.TestSuite;


public class ConfigurationServicePTestC extends PTestCaseC {

    static String name = "Configuration Service Test";

    static final StreamService streamService = AdminFactory.getInstance().getStreamService();
    static final OrganizationService organizationService = AdminFactory.getInstance().getOrganizationService();
    static final ConfigurationService configurationService = AdminFactory.getInstance().getConfigurationService();

    public ConfigurationServicePTestC( String name ) {
        super( name );
    }

    /**
     * Test {@link ConfigurationService}
     */
    public void testConfigurationService() {
        // create and insert stream and configuration
        try {
            IdcTransaction tx = IdcSessionManager.getInstance().newTransaction( "Integral1@MAIN" );
            long date = DateTimeFactory.newDateTime().asSeconds();
            Organization org = UserFactory.newOrganization();
            org = ( Organization ) org.getRegisteredObject();
            org.setShortName( "ORG_FOR_TEST_STREAM" + date );
            String currencyPair = "EUR/USD";

            //setup stream
            String streamName = "TEST_STREAM" + date;
            Stream stream = streamService.addStream( org, streamName );
            stream = ( Stream ) stream.getRegisteredObject();

            //setup configuration
            Configuration configuration = configurationService.addConfiguration( stream, currencyPair );
            configuration = ( Configuration ) configuration.getRegisteredObject();
            configurationService.setESPEnabled( configuration, true );
            configurationService.setRFSEnabled( configuration, false );
            configurationService.setChannelOverrideEnabled( configuration, true );
            configurationService.setRequestExpiry( configuration, 5000000l );
            configurationService.setMaximumTenor( configuration, new Tenor( "1Y" ) );

            tx.commitAndResume();

            //read stream now
            tx = IdcSessionManager.getInstance().newTransaction( "Integral1@MAIN" );
            stream = ( Stream ) ( new ReadNamedEntityC() ).execute( Stream.class, streamName );
            log.info( stream.toString() );
            tx.release();
        }
        catch ( Exception e ) {
            IdcSessionManager.getInstance().setTransaction(null);
            fail("Failed in testConfigurationService : ",e);
        }
    }

}