package com.integral.broker.admin.test;

import com.integral.broker.admin.AdminFactory;
import com.integral.broker.admin.OrganizationService;
import com.integral.broker.admin.StreamService;
import com.integral.broker.model.Stream;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.Namespace;
import com.integral.persistence.NamespaceC;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.user.UserPermissionClassification;
import junit.framework.Test;
import junit.framework.TestSuite;


public class StreamServicePTestC extends PTestCaseC
{

    static String name = "Stream Service Test";

    static final StreamService streamService = AdminFactory.getInstance().getStreamService();
    static final OrganizationService organizationService = AdminFactory.getInstance().getOrganizationService();

    public StreamServicePTestC( String name )
    {
        super( name );
    }

    /**
     * Test StreamService
     */
    public void testStreamService()
    {
        // create and insert stream
        try
        {

            IdcTransaction tx = IdcSessionManager.getInstance().newTransaction( "Integral1@MAIN" );
            long date = System.currentTimeMillis();
            Organization org = UserFactory.newOrganization();
            org = ( Organization ) org.getRegisteredObject();
            Namespace ns = ( Namespace ) new NamespaceC().getRegisteredObject();
            ns.setShortName( "TEST" + date );
            org.setShortName( "TEST" + date );
            User user = UserFactory.newUser( "TEST" + date );
            user = ( User ) user.getRegisteredObject();
            user.setOrganization( org );
            UserPermissionClassification permission = loadUserPermissionClassification();
            user.putCustomField( "DirectFX_Role", permission );
            org.addUser( user );

            organizationService.addOrganizationFunction( org );

            //setup stream
            String streamName = "STREAM" + date;
            Stream stream = streamService.addStream( org, streamName );
            stream = ( Stream ) stream.getRegisteredObject();
            streamService.setDescription( stream, "Test Stream" );
            streamService.setUser( stream, user );
            streamService.setStatus( stream, 'A' );
            streamService.setRFSEnabled( stream, true );
            streamService.setESPEnabled( stream, false );
            streamService.setRequestExpiry( stream, 10000l );
            streamService.setMaximumTenor( stream, new Tenor( "10M" ) );

            tx.commitAndResume();

            //read stream and configuration now
            stream = ( Stream ) ( new ReadNamedEntityC() ).execute( Stream.class, streamName );
            log.info( stream.toString());
            tx.release();
        }
        catch ( Exception e )
        {
            IdcSessionManager.getInstance().setTransaction(null);
            fail("Failed in testStreamService : ",e);
        }
    }

    protected UserPermissionClassification loadUserPermissionClassification()
    {
        return ( UserPermissionClassification ) ( new ReadNamedEntityC() ).execute( UserPermissionClassification.class, "MARKETMAKER" );
    }
}