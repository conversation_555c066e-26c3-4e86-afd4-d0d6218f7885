package com.integral.broker.test;

import com.integral.broker.model.BrokerAdaptorConfiguration;
import com.integral.broker.model.BrokerAdaptorConfigurationC;
import com.integral.broker.model.CurrencyPairMaxInverseSpread;
import com.integral.broker.model.CurrencyPairMaxInverseSpreadC;
import com.integral.broker.model.ProviderQuoteTier;
import com.integral.broker.model.ProviderQuoteTierC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.persistence.NamespaceC;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Map;
import java.util.Vector;


public class BrokerAdaptorConfigurationPTestC
        extends PTestCaseC
{
    static String name = "BrokerAdaptorConfiguration Test";

    public BrokerAdaptorConfigurationPTestC( String name )
    {
        super( name );
    }

    /**
     * Test testBrokerAdaptorConfiguration
     */
    public void testBrokerAdaptorConfiguration()
    {
        // insert configuration
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder eb = new ExpressionBuilder();

            // To be run against app developer db
            ReadAllQuery raq2 = new ReadAllQuery( NamespaceC.class, eb.get( "shortName" ).equal( "FI2" ) );
            NamespaceC nm1 = ( NamespaceC ) ( ( Vector ) uow.executeQuery( raq2 ) ).get( 0 );

            uow = getPersistenceSession().acquireUnitOfWork();
            BrokerAdaptorConfiguration config = new BrokerAdaptorConfigurationC();
            config.setNamespace( nm1 );
            config.setQuoteTier( 5 );
            config.setOffMarketExcludeMaxInverseSpread( 1 );
            config.setOffMarketExcludeMaxProviders( 10 );
            config.setOffMarketExcludeMinProviders( 1 );

            //do not register organization - only config
            uow.registerObject( config );
            uow.commit();
            long objId = config.getObjectID();

            uow = getPersistenceSession().acquireUnitOfWork();
            BrokerAdaptorConfiguration config1 = ( BrokerAdaptorConfiguration ) ( ( Vector ) uow.executeQuery( new ReadAllQuery( BrokerAdaptorConfigurationC.class, eb.getField( "id" ).equal( objId ) ) ) ).get( 0 );
            assertNotNull( "The config object saved to the database is not null.", config1 );

            log( " Config namespace from DB iss " + config1.getNamespace().getShortName() );
            int quoteTier = config1.getQuoteTier();
            log( "quoteTier=" + quoteTier );
            assertEquals( quoteTier, 5 );
        }
        catch ( Exception e )
        {
            fail( "testBrokerAdaptorConfiguration", e );
        }
    }

    public void testBrokerAdaptorConfigurationMap()
    {
        // insert configuration
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder eb = new ExpressionBuilder();

            // To be run against app developer db
            ReadAllQuery raq2 = new ReadAllQuery( NamespaceC.class, eb.get( "shortName" ).equal( "FI2" ) );
            NamespaceC nm1 = ( NamespaceC ) ( ( Vector ) uow.executeQuery( raq2 ) ).get( 0 );

            uow = getPersistenceSession().acquireUnitOfWork();
            BrokerAdaptorConfiguration config = new BrokerAdaptorConfigurationC();
            config.setNamespace( nm1 );
            config.setQuoteTier( 5 );

            ReadAllQuery raq = new ReadAllQuery( OrganizationC.class, eb.get( "shortName" ).equal( "FICpty11" ) );
            Organization org1 = ( Organization ) ( ( Vector ) uow.executeQuery( raq ) ).get( 0 );
            ProviderQuoteTier provQuoteTier = new ProviderQuoteTierC();
            provQuoteTier.setQuoteTier( 10 );
            provQuoteTier.setProvider( org1 );
            config.addProvidrQuoteTier( provQuoteTier );

            CurrencyPairMaxInverseSpread ccyPairMaxInvSprd = new CurrencyPairMaxInverseSpreadC();
            ccyPairMaxInvSprd.setCurrencyPair( "EUR/USD" );
            ccyPairMaxInvSprd.setMaxInversespread( 15 );
            config.addCurrencyPairMaxInverseSpread( ccyPairMaxInvSprd );

            //do not register organization - only config
            uow.registerObject( config );
            uow.commit();
            long objId = config.getObjectID();

            config = ( BrokerAdaptorConfiguration ) getPersistenceSession().refreshObject( config );
            uow = getPersistenceSession().acquireUnitOfWork();
            BrokerAdaptorConfiguration dbConfig = ( BrokerAdaptorConfiguration ) ( ( Vector ) uow.executeQuery( new ReadAllQuery( BrokerAdaptorConfigurationC.class, eb.getField( "id" ).equal( objId ) ) ) ).get( 0 );
            assertNotNull( "The config object saved to the database is not null.", dbConfig );
            Map map = config.getProviderQuoteTierMap();
            assertEquals( "provider quote tier map should not be empty", map.size(), 1 );

            int quoteTier = dbConfig.getQuoteTier();
            log( "quoteTier=" + quoteTier );
            assertEquals( quoteTier, 5 );

            CurrencyPair crncPair = CurrencyFactory.newCurrencyPair();
            crncPair.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            crncPair.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            int maxInvSprd = dbConfig.getMaxInverseSpread( crncPair );
            assertEquals( maxInvSprd, 15 );

            int qutTier = dbConfig.getQuoteTier( org1 );
            assertEquals( qutTier, 10 );

        }
        catch ( Exception e )
        {
            fail( "testBrokerAdaptorConfigurationMap", e );
        }
    }
}
