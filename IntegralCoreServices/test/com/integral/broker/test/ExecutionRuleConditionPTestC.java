package com.integral.broker.test;

import com.integral.broker.model.ExecutionRule;
import com.integral.broker.model.ExecutionRuleC;
import com.integral.broker.model.ExecutionRuleCondition;
import com.integral.broker.model.ExecutionRuleConditionC;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Date;
import java.util.Vector;
import java.sql.Time;

public class ExecutionRuleConditionPTestC extends PTestCaseC {
    static String name = "ExecutionRuleConditionPTestC Test";

    public ExecutionRuleConditionPTestC( String name ) {
        super( name );
    }

    private CurrencyPairGroup getCurrencyPairGroup() {
        try {
            Session uow = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( CurrencyPairGroupC.class );
            Vector entities = ( Vector ) uow.executeQuery( raq );
            return ( CurrencyPairGroup ) entities.get( 5 );
        }
        catch ( Exception exc ) {
            log.error( "CurrencyPairGroupRemoteTransactionFunctorC.getCurrencyPairGroups Error " + exc );
            return null;
        }

    }

    public void testPersistence() throws Exception {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( CurrencyPairGroupC.class );
            ExecutionRuleCondition orgFunc = new ExecutionRuleConditionC();
            assertFalse( orgFunc.isPartialCoverEnabled() );
            orgFunc = ( ExecutionRuleCondition ) uow.registerObject( orgFunc );
            orgFunc.setStatus( 'T' );
            CurrencyPairGroup cpg = getCurrencyPairGroup();
            orgFunc.setCurrencyPairGroup( cpg );
            //orgFunc.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            orgFunc.setCcy( CurrencyFactory.getCurrency( "USD" ) );
            //orgFunc.setVariableCurrency( CurrencyFactory.getCurrency( "JPY" ) );
            Date time = new Date();
            orgFunc.setPartialCoverEnabled( true );
            orgFunc.setCoverPercentage( 50 );

            ExecutionRule rule = ( ExecutionRule ) uow.registerObject( new ExecutionRuleC() );
            rule.setShortName( System.currentTimeMillis() + "");
            orgFunc.setExecutionRule( rule );
            orgFunc.setHighAmount( 897 );
            orgFunc.setLowAmount( 34 );
            //orgFunc.setRule( 7 );
            orgFunc.setSortOrder( 78 );
            Time start = Time.valueOf("11:11:11");
            Time end = Time.valueOf("10:10:10");

            orgFunc.setStartTime( start );
            orgFunc.setStopTime( end );

            uow.commit();

            orgFunc = ( ExecutionRuleCondition ) getPersistenceSession().refreshObject( orgFunc );
            assertNotNull( orgFunc );
            //assertEquals( orgFunc.getBaseCurrency().getShortName(), "EUR" );
            //assertEquals( orgFunc.getCcy().getShortName(), "USD" );
            //assertEquals( orgFunc.getVariableCurrency().getShortName(), "JPY" );
            //assertEquals( orgFunc.getCurrencyPairGroup().getShortName(), cpg.getShortName() );
            assertNotNull( orgFunc.getExecutionRule() );
            assertEquals( orgFunc.getHighAmount(), 897d );
            assertEquals( orgFunc.getLowAmount(), 34d );
            //assertEquals( orgFunc.getRule(), 7 );
            assertEquals( orgFunc.getSortOrder(), 78 );
            assertEquals( orgFunc.getStartTime().getTime(), start.getTime() );
            assertEquals( orgFunc.getStopTime().getTime(), end.getTime() );
            assertTrue ( orgFunc.isPartialCoverEnabled() );
            assertEquals( 50, orgFunc.getCoverPercentage() );
    }

}
