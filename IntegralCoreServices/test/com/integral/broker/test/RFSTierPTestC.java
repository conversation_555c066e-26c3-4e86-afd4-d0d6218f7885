package com.integral.broker.test;

import com.integral.broker.model.Configuration;
import com.integral.broker.model.ConfigurationC;
import com.integral.broker.model.ESPTierC;
import com.integral.broker.model.RFSTier;
import com.integral.broker.model.RFSTierC;
import com.integral.broker.model.SpreadBias;
import com.integral.broker.model.Tier;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

public class RFSTierPTestC extends PTestCaseC
{
    static String name = "RFSTierPTestC Test";

    public RFSTierPTestC( String name )
    {
        super( name );
    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();

            // add a regular tier.
            Tier tier = new ESPTierC();
            Tier registeredTier = ( Tier ) uow.registerObject( tier );
            tier.setStatus( 'T' );
            registeredTier.setBidLimit( 78.9 );
            registeredTier.setOfferLimit( 98.8 );
            registeredTier.setSortOrder( 7 );
            registeredTier.setSpreadMaximum( 77.8 );
            registeredTier.setSpreadMinimum( 32.2 );

            // add two RFS tiers.
            RFSTier rfsTier1 = new RFSTierC();
            RFSTier registeredRfsTier1 = ( RFSTier ) uow.registerObject( rfsTier1 );
            registeredRfsTier1.setStatus( 'T' );
            registeredRfsTier1.setBidLimit( 78.9 );
            registeredRfsTier1.setOfferLimit( 98.8 );
            registeredRfsTier1.setSortOrder( 7 );
            registeredRfsTier1.setSpreadMaximum( 77.8 );

            registeredRfsTier1.setSpreadMinimum( 32.2 );
            registeredRfsTier1.setMaxSpreadBias( SpreadBias.Offer );
            registeredRfsTier1.setSpreadBias( SpreadBias.Bid );

            RFSTier rfsTier2 = new RFSTierC();
            RFSTier registeredRfsTier2 = ( RFSTier ) uow.registerObject( rfsTier2 );
            registeredRfsTier2.setStatus( 'T' );
            registeredRfsTier2.setBidLimit( 78.8 );
            registeredRfsTier2.setOfferLimit( 98.7 );
            registeredRfsTier2.setSortOrder( 6 );
            registeredRfsTier2.setSpreadMaximum( 77.7 );
            registeredRfsTier2.setSpreadMinimum( 32.1 );
            registeredRfsTier2.setMaxSpreadBias( SpreadBias.Bid );
            registeredRfsTier2.setSpreadBias( SpreadBias.Offer );

            Configuration config = new ConfigurationC();
            Configuration registeredConfig = ( Configuration ) uow.registerObject( config );
            registeredConfig.setShortName( System.currentTimeMillis() + "" );
            registeredRfsTier1.setConfiguration( registeredConfig );
            registeredRfsTier2.setConfiguration( registeredConfig );
            registeredConfig.getRFSTiers().add( registeredRfsTier1 );
            registeredConfig.getRFSTiers().add( registeredRfsTier2 );
            registeredConfig.getTiers().add( registeredTier );

            uow.commit();

            tier = ( Tier ) getPersistenceSession().refreshObject( tier );
            rfsTier1 = ( RFSTier ) getPersistenceSession().refreshObject( rfsTier1 );
            rfsTier2 = ( RFSTier ) getPersistenceSession().refreshObject( rfsTier2 );
            config = ( Configuration ) getPersistenceSession().refreshObject( config );

            assertNotNull( tier );
            assertTrue( config.isSameAs( tier.getConfiguration() ) );
            assertEquals( config.getTiers().size(), 1 );

            assertTrue( config.isSameAs( rfsTier1.getConfiguration() ) );
            assertNotNull( rfsTier1 );
            assertEquals( rfsTier1.getBidLimit(), 78.9 );
            assertEquals( rfsTier1.getOfferLimit(), 98.8 );
            assertEquals( rfsTier1.getSortOrder(), 7 );
            assertEquals( rfsTier1.getSpreadMaximum(), 77.8 );
            assertEquals( rfsTier1.getSpreadMinimum(), 32.2 );
            assertNotNull( rfsTier1.getConfiguration() );
            assertEquals( rfsTier1.getMaxSpreadBias(), SpreadBias.Offer );
            assertEquals( rfsTier1.getSpreadBias(), SpreadBias.Bid );

            assertTrue( config.isSameAs( rfsTier2.getConfiguration() ) );
            assertNotNull( rfsTier2 );
            assertEquals( rfsTier2.getBidLimit(), 78.8 );
            assertEquals( rfsTier2.getOfferLimit(), 98.7 );
            assertEquals( rfsTier2.getSortOrder(), 6 );
            assertEquals( rfsTier2.getSpreadMaximum(), 77.7 );
            assertEquals( rfsTier2.getSpreadMinimum(), 32.1 );
            assertNotNull( rfsTier2.getConfiguration() );
            assertEquals( rfsTier2.getMaxSpreadBias(), SpreadBias.Bid );
            assertEquals( rfsTier2.getSpreadBias(), SpreadBias.Bid );

            assertEquals( config.getRFSTiers().size(), 2 );
        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }

}
