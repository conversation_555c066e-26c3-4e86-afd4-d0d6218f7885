package com.integral.broker.test;

import com.integral.broker.model.MultiProviderRateFilterCcyPairParams;
import com.integral.broker.model.MultiProviderRateFilterCcyPairParamsC;
import com.integral.broker.model.MultiProviderRateFilterParams;
import com.integral.broker.model.MultiProviderRateFilterParamsC;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

public class MultiProviderRateFilterCcyPairParamsPTestC extends PTestCaseC
{
    static String name = "MultiProviderRateFilterCcyPairParamsPTestC Test";

    public MultiProviderRateFilterCcyPairParamsPTestC( String name )
    {
        super( name );
    }

    private CurrencyPairGroup getCurrencyPairGroup()
    {
        try
        {
            Session uow = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( CurrencyPairGroupC.class );
            Vector entities = ( Vector ) uow.executeQuery( raq );
            return ( CurrencyPairGroup ) entities.get( 5 );
        }
        catch ( Exception exc )
        {
            log.error( "CurrencyPairGroupRemoteTransactionFunctorC.getCurrencyPairGroups Error " + exc );
            return null;
        }

    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( CurrencyPairGroupC.class );
            MultiProviderRateFilterCcyPairParams orgFunc = new MultiProviderRateFilterCcyPairParamsC();
            orgFunc = ( MultiProviderRateFilterCcyPairParams ) uow.registerObject( orgFunc );
            orgFunc.setStatus( 'T' );
            orgFunc.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            orgFunc.setVariableCurrency( CurrencyFactory.getCurrency( "JPY" ) );
            CurrencyPairGroup cpg = getCurrencyPairGroup();
            orgFunc.setCurrencyPairGroup( cpg );
            orgFunc.setSortOrder( 8 );
            orgFunc.setMultiProviderRateFilterParams( ( MultiProviderRateFilterParams ) uow.registerObject( new MultiProviderRateFilterParamsC() ) );
            orgFunc.setOffMarketMaxInverseSpread( 56.0 );
            orgFunc.setStalenessCheckInterval( 7000 );
            orgFunc.setInvertedRateTolerance( 49.0 );

            uow.commit();

            orgFunc = ( MultiProviderRateFilterCcyPairParams ) getPersistenceSession().refreshObject( orgFunc );
            assertNotNull( orgFunc );
            assertEquals( orgFunc.getBaseCurrency().getShortName(), "EUR" );
            assertEquals( orgFunc.getVariableCurrency().getShortName(), "JPY" );
            assertEquals( orgFunc.getCurrencyPairGroup().getShortName(), cpg.getShortName() );

            assertEquals( orgFunc.getSortOrder(), 8 );
            assertNotNull( orgFunc.getMultiProviderRateFilterParams() );
            assertEquals( orgFunc.getOffMarketMaxInverseSpread(), 56.0 );
            assertEquals( orgFunc.getStalenessCheckInterval(), 7000 );
            assertEquals( orgFunc.getInvertedRateTolerance(), 49.0 );
        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }


}
