package com.integral.broker.test;

import com.integral.broker.model.CurrencyPairMaxInverseSpread;
import com.integral.broker.model.CurrencyPairMaxInverseSpreadC;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


public class CurrencyPairMaxInverseSpreadPTestC
        extends PTestCaseC
{
    static String name = "CurrencyPairMaxInverseSpread Test";

    public CurrencyPairMaxInverseSpreadPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new CurrencyPairMaxInverseSpreadPTestC( "testCurrencyPairMaxInverseSpread" ) );
        return suite;
    }

    /**
     * Test testCurrencyPairMaxInverseSpread
     */
    public void testCurrencyPairMaxInverseSpread()
    {
        // insert CurrencyPairMaxInverseSpread
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder eb = new ExpressionBuilder();

            CurrencyPairMaxInverseSpread ccyPairMaxInvSprd = new CurrencyPairMaxInverseSpreadC();
            ccyPairMaxInvSprd.setCurrencyPair( "EUR/USD" );
            ccyPairMaxInvSprd.setMaxInversespread( 10 );

            //do not register organization - only config
            uow.registerObject( ccyPairMaxInvSprd );
            uow.commit();
            long objId = ccyPairMaxInvSprd.getObjectID();

            ccyPairMaxInvSprd = ( CurrencyPairMaxInverseSpread ) getPersistenceSession().refreshObject( ccyPairMaxInvSprd );
            uow = getPersistenceSession().acquireUnitOfWork();
            CurrencyPairMaxInverseSpread dbCcyMaxInvSprd = ( CurrencyPairMaxInverseSpread ) ( ( Vector ) uow.executeQuery( new ReadAllQuery( CurrencyPairMaxInverseSpreadC.class, eb.getField( "id" ).equal( objId ) ) ) ).get( 0 );
            assertNotNull( "The CurrencyPairMaxInverseSpread object saved to the database is not null.", dbCcyMaxInvSprd );

            log( " Config namespace from DB is " + dbCcyMaxInvSprd.getNamespace().getShortName() );
            int maxInvSprd = dbCcyMaxInvSprd.getMaxInversespread();
            log( "maxInvSprd=" + maxInvSprd );
            assertEquals( maxInvSprd, 10 );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairMaxInverseSpread", e );
        }
    }

}
