package com.integral.broker.test;

import com.integral.broker.model.SingleProviderRateFilterCcyPairParams;
import com.integral.broker.model.SingleProviderRateFilterCcyPairParamsC;
import com.integral.broker.model.SingleProviderRateFilterParams;
import com.integral.broker.model.SingleProviderRateFilterParamsC;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

public class SingleProviderRateFilterCcyPairParamsPTestC extends PTestCaseC
{
    static String name = "SingleProviderRateFilterCcyPairParamsPTestC Test";

    public SingleProviderRateFilterCcyPairParamsPTestC( String name )
    {
        super( name );
    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            SingleProviderRateFilterCcyPairParams orgFunc = new SingleProviderRateFilterCcyPairParamsC();
            orgFunc = ( SingleProviderRateFilterCcyPairParams ) uow.registerObject( orgFunc );
            orgFunc.setStatus( 'T' );
            CurrencyPairGroup cpg = ( CurrencyPairGroup ) uow.registerObject( CurrencyFactory.newCurrencyPairGroup() );
            cpg.setShortName( "TestCPG" );
            orgFunc.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            orgFunc.setVariableCurrency( CurrencyFactory.getCurrency( "JPY" ) );
            orgFunc.setCurrencyPairGroup( cpg );
            orgFunc.setMaxPercentDeviation( 65 );
            orgFunc.setMaxPipsDeviation( 34.0 );
            orgFunc.setMaxSpread( 897.0 );
            orgFunc.setSortOrder( 8 );
            orgFunc.setInvertedRateTolerance( 49.0 );
            orgFunc.setSingleProviderRateFilterParams( ( SingleProviderRateFilterParams ) uow.registerObject( new SingleProviderRateFilterParamsC() ) );


            uow.commit();

            orgFunc = ( SingleProviderRateFilterCcyPairParams ) getPersistenceSession().refreshObject( orgFunc );
            assertNotNull( orgFunc );
            assertEquals( orgFunc.getBaseCurrency().getShortName(), "EUR" );
            assertEquals( orgFunc.getVariableCurrency().getShortName(), "JPY" );
            assertEquals( orgFunc.getCurrencyPairGroup().getShortName(), "TestCPG" );
            assertEquals( orgFunc.getMaxPercentDeviation(), 65d );
            assertEquals( orgFunc.getMaxPipsDeviation(), 34.0 );
            assertEquals( orgFunc.getMaxSpread(), 897.0 );
            assertEquals( orgFunc.getSortOrder(), 8 );
            assertEquals( orgFunc.getInvertedRateTolerance(), 49.0 );
            assertNotNull( orgFunc.getSingleProviderRateFilterParams() );


        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }


}
