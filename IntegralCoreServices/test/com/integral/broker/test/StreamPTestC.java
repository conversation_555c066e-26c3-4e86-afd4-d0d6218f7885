package com.integral.broker.test;

import com.integral.broker.model.*;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.NewGUID;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Time;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


public class StreamPTestC
        extends PTestCaseC
{
    static String name = "StreamC Test";
    protected static ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();


    public StreamPTestC( String name )
    {
        super( name );
    }

    /**
     * Test Stream Attribute Values
     */
    public void testFieldsPersistence()
    {
        // insert configuration
        try
        {
            uow = getPersistenceSession().acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( UserC.class );
            LegalEntity takerLe = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "FI1-le1" );
            User user = ( User ) namedEntityReader.execute( User.class, "fi1mm1" );

            Stream stream = ModelFactory.getInstance().newStream();
            assertFalse( stream.isIncludeFMAProviders() );
            assertTrue( stream.isPricingEnabled() );
            NewGUID ng = new NewGUID();
            String guidVal = ng.execute();
            stream = ( Stream ) uow.registerObject( stream );

            assertTrue( stream.isPricingEnabled() );
            assertFalse ( stream.isFixedPeriodPricingEnabled () );
            assertNull ( stream.getFixedPeriodMDSName () );
            assertNull ( stream.getFixedPricingStartTime () );
            assertEquals ( 0, stream.getFixedPricingPeriod ());

            stream.setShortName( guidVal );
            stream.setMarketTimeInForceInterval(111);
            stream.setTimeInForceInterval(222);
            stream.setLegalEntity(takerLe);
            stream.setRequestExpiry(1000);
            stream.setQuotePublicationInterval(50);
            stream.setQuotePublicationCheckInterval(100);
            stream.setClientTag("TestTag");
            stream.setPricingEnabled( false );

            List<Configuration> configs = new ArrayList<Configuration>( 2 );
            Configuration config1 = ( Configuration ) uow.registerObject( new ConfigurationC() );
            Configuration config2 = ( Configuration ) uow.registerObject( new ConfigurationC() );
            config1.setShortName( "Config" + System.currentTimeMillis() );
            config2.setShortName( "Config" + System.currentTimeMillis() + 100 );
            configs.add( config1 );
            configs.add( config2 );
            stream.setConfigurations( configs );

            stream.setESPEnabled( true );
            stream.setMaximumTenor( Tenor.TODAY_TENOR );
            stream.setPriceFixed( true );
            stream.setRFSEnabled( true );
            stream.setSpreadPreserved( true );
            stream.setStreamEnabled( true );
            stream.setIncludeFMAProviders( true );
            stream.setSuperLPIndex( ( short ) 100 ); 
            stream.setTradingCapacity( "AOTC" );
            stream.setCoverOnMTF( true );
            stream.setMiFIDExecutingFirm(takerLe);
            stream.setMiFIDExecutingUser(user);
            stream.setMiFIDInvDecisionMaker(user);

            //TODO remove it later stream schedules belong to provider org function.
            /*List<StreamSchedule> schedules = new ArrayList<StreamSchedule>( 2 );
            StreamSchedule schedule1 = ( StreamSchedule ) uow.registerObject( new StreamScheduleC() );
            StreamSchedule schedule2 = ( StreamSchedule ) uow.registerObject( new StreamScheduleC() );
            schedules.add( schedule1 );
            schedules.add( schedule2 );
            stream.setStreamSchedules( schedules );*/

            stream.setUser(user);
            assertFalse(stream.isIncludeDOProviders());
            stream.setIncludeDOProviders(false);

            stream.setFixedPeriodPricingEnabled ( true );
            stream.setFixedPeriodMDSName ( "TestMDSName" );
            Time start = Time.valueOf ( "11:11:11" );
            stream.setFixedPricingStartTime ( start );
            stream.setFixedPricingPeriod ( 600 );
            stream.setFixedPricingOverlapPeriod ( 30 );
            stream.setFixedPricingDelayedStartPeriod ( 10 );


            uow.commit();

            stream = ( Stream ) getPersistenceSession().refreshObject( stream );
            assertEquals( stream.getMarketTimeInForceInterval(), 111 );
            assertEquals( stream.getTimeInForceInterval(), 222 );
            assertEquals( stream.getLegalEntity(), takerLe );
            assertEquals( stream.getRequestExpiry(), 1000 );
            assertEquals( stream.getQuotePublicationInterval(), 50 );
            assertEquals( stream.getQuotePublicationCheckInterval(), 100 );
            assertEquals( stream.getConfigurations().size(), 2 );
            assertEquals( stream.getMaximumTenor().getName(), Tenor.TODAY_TENOR.getName() );
            assertEquals( stream.isPriceFixed(), true );
            assertEquals( stream.isESPEnabled(), true );
            assertEquals( stream.isRFSEnabled(), true );
            assertEquals( stream.isSpreadPreserved(), true );
            assertEquals( stream.isStreamEnabled(), true );
            //assertEquals( stream.getStreamSchedules().size(), 2 );
            assertEquals( stream.getUser(), user );
            assertFalse(stream.isIncludeDOProviders());
            assertTrue(stream.isIncludeFMAProviders());
            assertEquals(stream.getClientTag(),"TestTag");
            assertEquals(stream.getSuperLPIndex(), (short)100);
            assertEquals(stream.isCoverOnMTF(), true);
            assertEquals(stream.getTradingCapacity(), "AOTC");
            assertTrue(user.isSameAs(stream.getMiFIDExecutingUser()));
            assertTrue(user.isSameAs(stream.getMiFIDInvDecisionMaker()));
            assertTrue(takerLe.isSameAs(stream.getMiFIDExecutingFirm()));
            assertFalse( stream.isPricingEnabled() );
            assertTrue ( stream.isFixedPeriodPricingEnabled () );
            assertEquals ( "TestMDSName", stream.getFixedPeriodMDSName () );
            assertEquals ( start.getTime (), stream.getFixedPricingStartTime ().getTime () );
            assertEquals ( 600, stream.getFixedPricingPeriod () );
            assertEquals ( 30, stream.getFixedPricingOverlapPeriod () );
            assertEquals ( 10, stream.getFixedPricingDelayedStartPeriod () );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSuperBankStreamIndexUniqueness()
    {
        try
        {
            ReferenceDataCacheC.getInstance ().loadReferenceDataForNamespaces ( null );
            UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
            uow.removeReadOnlyClass ( StreamC.class );
            uow.removeReadOnlyClass ( BrokerOrganizationFunctionC.class );
            uow.addReadOnlyClass ( OrganizationC.class );

            int streamsUpdated = 0;
            Stream lastUpdatedStream = null;
            Collection<Stream> updatedStreams = new ArrayList<Stream> ();

            Collection<Organization> orgs = PersistenceFactory.newSession ().readAllObjects ( OrganizationC.class );
            for ( Organization org: orgs )
            {
                if ( org.isExternalProvider () && org.getBrokerOrganizationFunction () != null && !org.getBrokerOrganizationFunction ().getStreams ().isEmpty () )
                {
                    org.setSuperBank ( true );
                    Collection<Stream> streams = org.getBrokerOrganizationFunction ().getStreams ();
                    for ( Stream stream: streams )
                    {
                        if ( stream.getSuperLPIndex () > 0 )
                        {
                            continue;
                        }
                        Stream registeredStream = ( Stream ) uow.registerObject ( stream );
                        registeredStream.getBrokerOrganizationFunction ().getOrganization ().setSuperBank ( true );
                        registeredStream.update ();
                        streamsUpdated++;
                        lastUpdatedStream = stream;
                        updatedStreams.add ( stream );
                    }
                }
            }

            if ( streamsUpdated > 0 )
            {
                log( "Streams updated=" + streamsUpdated );
                uow.commit ();

                lastUpdatedStream = ( Stream ) IdcUtilC.refreshObject ( lastUpdatedStream  );
                int otherStreamIndex = 1;
                for ( Stream stream: updatedStreams )
                {
                    Stream refreshedStream = (Stream) IdcUtilC.refreshObject ( stream );
                    if ( !refreshedStream.isSameAs ( lastUpdatedStream  ) )
                    {
                        otherStreamIndex = refreshedStream.getSuperLPIndex ();
                    }
                }

                lastUpdatedStream = ( Stream ) IdcUtilC.refreshObject ( lastUpdatedStream  );

                uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
                uow.removeReadOnlyClass ( StreamC.class );
                uow.removeReadOnlyClass ( BrokerOrganizationFunctionC.class );
                uow.addReadOnlyClass ( OrganizationC.class );

                Stream registeredLasUpdatedStream = ( Stream ) uow.registerObject ( lastUpdatedStream );
                registeredLasUpdatedStream.setSuperLPIndex ( (short) otherStreamIndex );

                boolean exception = false;
                try
                {
                    uow.commit ();
                }
                catch ( Exception e )
                {
                    exception = true;
                }

                assertTrue (  exception );

            }
            else
            {
                uow.release ();
            }
        }
        catch ( Exception e )
        {
            fail("testSuperBankStreamIndexUniqueness", e );
        }
        finally
        {
            resetSuperBankStreamIndex ();
        }
    }


    private void resetSuperBankStreamIndex()
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
            uow.removeReadOnlyClass ( StreamC.class );
            uow.removeReadOnlyClass ( BrokerOrganizationFunctionC.class );
            uow.addReadOnlyClass ( OrganizationC.class );

            Collection<Organization> orgs = PersistenceFactory.newSession ().readAllObjects ( OrganizationC.class );
            for ( Organization org: orgs )
            {
                if ( org.isExternalProvider () )
                {
                    org = ( OrganizationC ) IdcUtilC.refreshObject ( org );
                }
                if ( !org.isSuperBank () && org.getBrokerOrganizationFunction () != null )
                {
                    Collection<Stream> streams = org.getBrokerOrganizationFunction ().getStreams ();
                    for ( Stream stream: streams )
                    {
                        Stream registeredStream = ( Stream ) uow.registerObject ( stream );
                        registeredStream.update ();
                        registeredStream.setSuperLPIndex ( ( short ) 0 );
                    }
                }
            }

            uow.commit ();
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
        }
    }
}
