package com.integral.broker.test;

import com.integral.broker.model.ProviderQuoteTier;
import com.integral.broker.model.ProviderQuoteTierC;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


public class ProviderQuoteTierPTestC
        extends PTestCaseC
{
    static String name = "ProviderQuoteTier Test";

    public ProviderQuoteTierPTestC( String name )
    {
        super( name );
    }

    /**
     * Test testProviderQuoteTier
     */
    public void testProviderQuoteTier()
    {
        // insert ProviderQuoteTier
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder eb = new ExpressionBuilder();

            ReadAllQuery raq = new ReadAllQuery( OrganizationC.class, eb.get( "shortName" ).equal( "FICpty11" ) );
            Organization org1 = ( Organization ) ( ( Vector ) uow.executeQuery( raq ) ).get( 0 );
            ProviderQuoteTier provQuoteTier = new ProviderQuoteTierC();
            provQuoteTier.setQuoteTier( 10 );
            provQuoteTier.setProvider( org1 );

            //do not register organization - only config
            uow.registerObject( provQuoteTier );
            uow.commit();
            long objId = provQuoteTier.getObjectID();

            uow = getPersistenceSession().acquireUnitOfWork();
            ProviderQuoteTier dbQuoteTier = ( ProviderQuoteTier ) ( ( Vector ) uow.executeQuery( new ReadAllQuery( ProviderQuoteTierC.class, eb.getField( "id" ).equal( objId ) ) ) ).get( 0 );
            assertNotNull( "The ProviderQuoteTierC object saved to the database is not null.", dbQuoteTier );

            log( " Config namespace from DB is " + dbQuoteTier.getNamespace().getShortName() );
            int quoteTier = dbQuoteTier.getQuoteTier();
            log( "quoteTier=" + quoteTier );
            assertEquals( quoteTier, 10 );
        }
        catch ( Exception e )
        {
            fail( "testProviderQuoteTier", e );
        }
    }

}


