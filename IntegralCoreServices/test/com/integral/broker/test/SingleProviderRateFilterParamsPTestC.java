package com.integral.broker.test;

import com.integral.broker.model.RateFilterDefinition;
import com.integral.broker.model.RateFilterDefinitionC;
import com.integral.broker.model.SingleProviderRateFilterParams;
import com.integral.broker.model.SingleProviderRateFilterParamsC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

public class SingleProviderRateFilterParamsPTestC extends PTestCaseC
{
    static String name = "SingleProviderRateFilterParamsPTestC Test";

    public SingleProviderRateFilterParamsPTestC( String name )
    {
        super( name );
    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            SingleProviderRateFilterParams orgFunc = new SingleProviderRateFilterParamsC();
            assertTrue( orgFunc.isInvertedRateFilterCheckEnabled() );
            orgFunc = ( SingleProviderRateFilterParams ) uow.registerObject( orgFunc );
            orgFunc.setStatus( 'T' );
            orgFunc.setMaxPercentDeviation( 10.0 );
            orgFunc.setMaxPercentDeviationCheckEnabled( true );
            orgFunc.setMaxPipsDeviation( 95.0 );
            orgFunc.setMaxPipsDeviationCheckEnabled( true );
            orgFunc.setMaxSpread( 99.0 );
            orgFunc.setMaxSpreadCheckEnabled( true );
            orgFunc.setInvertedRateFilterCheckEnabled( true );
            orgFunc.setInvertedRateTolerance( 49.0 );
            RateFilterDefinition rfd = ( RateFilterDefinition ) uow.registerObject( new RateFilterDefinitionC() );
            rfd.setShortName( "Test" + System.nanoTime() );
            orgFunc.setRateFilterDefinition( rfd );

            uow.commit();

            orgFunc = ( SingleProviderRateFilterParams ) getPersistenceSession().refreshObject( orgFunc );
            assertNotNull( orgFunc );
            assertEquals( orgFunc.getMaxPercentDeviation(), 10.0d );
            assertEquals( orgFunc.isMaxPercentDeviationCheckEnabled(), true );
            assertEquals( orgFunc.getMaxPipsDeviation(), 95.0 );
            assertEquals( orgFunc.isMaxPipsDeviationCheckEnabled(), true );
            assertEquals( orgFunc.getMaxSpread(), 99.0 );
            assertEquals( orgFunc.getInvertedRateTolerance(), 49.0);
            assertEquals( orgFunc.isMaxSpreadCheckEnabled(), true );
            assertNotNull( orgFunc.getRateFilterDefinition() );
            assertTrue( orgFunc.isInvertedRateFilterCheckEnabled() );
        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }


}
