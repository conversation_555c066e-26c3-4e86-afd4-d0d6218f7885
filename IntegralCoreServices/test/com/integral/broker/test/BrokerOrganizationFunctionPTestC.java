package com.integral.broker.test;

// Copyright (c) 2008 Integral Development Corp. All rights reserved.

import com.integral.broker.model.*;
import com.integral.test.DealingPTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Time;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;


public class BrokerOrganizationFunctionPTestC extends DealingPTestCaseC
{
    static String name = "BrokerOrganizationFunction Test";

    public BrokerOrganizationFunctionPTestC ( String name )
    {
        super ( name );
    }

    /**
     * Test broker org function
     */
    public void testBrokerOrgFunctionPersistence () throws Exception
    {
        // insert broker org function
        UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
        uow.removeAllReadOnlyClasses ();
        uow.addReadOnlyClass ( OrganizationC.class );
        BrokerOrganizationFunctionC orgFunc = new BrokerOrganizationFunctionC ();
        orgFunc = ( BrokerOrganizationFunctionC ) uow.registerObject ( orgFunc );
        orgFunc.setStatus ( 'T' );
        long t = Calendar.getInstance ().getTimeInMillis ();
        Time startTime = new Time ( t );
        Time stopTime = new Time ( t + 12 * 60 * 60 );
        orgFunc.setStartTime ( startTime );
        orgFunc.setStopTime ( stopTime );
        orgFunc.setTimeZone ( "GMT" );

        BrokerAdaptorConfiguration config = new BrokerAdaptorConfigurationC ();
        config = ( BrokerAdaptorConfiguration ) uow.registerObject ( config );
        orgFunc.setConfiguration ( config );
        config.setStatus ( 'T' );

        Stream stream1 = ( Stream ) uow.registerObject ( new StreamC () );
        Stream stream2 = ( Stream ) uow.registerObject ( new StreamC () );
        List<Stream> streams = new ArrayList<Stream> ( 2 );
        streams.add ( stream1 );
        streams.add ( stream2 );
        stream1.setShortName ( "Stream1" + System.currentTimeMillis () );
        stream2.setShortName ( "Stream2" + System.currentTimeMillis () + 5 );
        orgFunc.setStreams ( streams );

        ExecutionRule rule1 = ( ExecutionRule ) uow.registerObject ( new ExecutionRuleC () );
        ExecutionRule rule2 = ( ExecutionRule ) uow.registerObject ( new ExecutionRuleC () );
        List<ExecutionRule> rules = new ArrayList<ExecutionRule> ( 2 );
        rules.add ( rule1 );
        rules.add ( rule2 );
        rule1.setShortName ( "Rule1" + System.currentTimeMillis () );
        rule2.setShortName ( "Rule2" + System.currentTimeMillis () + 5 );
        orgFunc.setExecutionRules ( rules );
        rule1.setBrokerOrganizationFunction ( orgFunc );
        rule2.setBrokerOrganizationFunction ( orgFunc );


        RateFilterDefinition rfd1 = ( RateFilterDefinition ) uow.registerObject ( new RateFilterDefinitionC () );
        RateFilterDefinition rfd2 = ( RateFilterDefinition ) uow.registerObject ( new RateFilterDefinitionC () );
        List<RateFilterDefinition> rfds = new ArrayList<RateFilterDefinition> ( 2 );
        rfd1.setShortName ( "RFD" + System.currentTimeMillis () );
        rfd2.setShortName ( "RFD" + System.currentTimeMillis () + 5 );
        rfds.add ( rfd1 );
        rfds.add ( rfd2 );
        orgFunc.setRateFilterDefinitions ( rfds );
        rfd1.setBrokerOrganizationFunction ( orgFunc );
        rfd2.setBrokerOrganizationFunction ( orgFunc );
/*
            RateFilterDefinition rfd3 = ( RateFilterDefinition ) uow.registerObject( new RateFilterDefinitionC() );
            RateFilterDefinition rfd4 = ( RateFilterDefinition ) uow.registerObject( new RateFilterDefinitionC() );
            List<RateFilterDefinition> rfds2 = new ArrayList<RateFilterDefinition>( 2 );
            rfds2.add( rfd3 );
            rfds2.add( rfd4 );
            rfd3.setBrokerOrganizationFunction( orgFunc);
            rfd4.setBrokerOrganizationFunction( orgFunc);
            orgFunc.setRateFilterDefinitions( rfds2 );
*/


        orgFunc.setActiveRateFilterDefinition ( rfd1 );

        SingleProviderRateFilterCcyPairParams obj1 = ( SingleProviderRateFilterCcyPairParams ) uow.registerObject ( new SingleProviderRateFilterCcyPairParamsC () );
        SingleProviderRateFilterCcyPairParams obj2 = ( SingleProviderRateFilterCcyPairParams ) uow.registerObject ( new SingleProviderRateFilterCcyPairParamsC () );
        List<SingleProviderRateFilterCcyPairParams> rateParams = new ArrayList<SingleProviderRateFilterCcyPairParams> ( 2 );
        obj1.setSortOrder ( 1 );
        obj2.setSortOrder ( 2 );
        rateParams.add ( obj1 );
        rateParams.add ( obj2 );

        SingleProviderRateFilterParams rateFilterParams = ( SingleProviderRateFilterParams ) uow.registerObject ( new SingleProviderRateFilterParamsC () );
        rateFilterParams.setSingleiProviderRateFilterCcyPairParams ( rateParams );
        rfd1.setSingleProviderRateFilterParams ( rateFilterParams );
        rateFilterParams.setRateFilterDefinition ( rfd1 );
        obj1.setSingleProviderRateFilterParams ( rateFilterParams );
        obj2.setSingleProviderRateFilterParams ( rateFilterParams );

        MultiProviderRateFilterCcyPairParams obj3 = ( MultiProviderRateFilterCcyPairParams ) uow.registerObject ( new MultiProviderRateFilterCcyPairParamsC () );
        MultiProviderRateFilterCcyPairParams obj4 = ( MultiProviderRateFilterCcyPairParams ) uow.registerObject ( new MultiProviderRateFilterCcyPairParamsC () );
        List<MultiProviderRateFilterCcyPairParams> multiRateParams = new ArrayList<MultiProviderRateFilterCcyPairParams> ( 2 );
        obj3.setSortOrder ( 3 );
        obj4.setSortOrder ( 4 );

        multiRateParams.add ( obj3 );
        multiRateParams.add ( obj4 );
        MultiProviderRateFilterParams multiRateFilterParams = ( MultiProviderRateFilterParams ) uow.registerObject ( new MultiProviderRateFilterParamsC () );
        multiRateFilterParams.setMultiProviderRateFilterCcyPairParams ( multiRateParams );
        multiRateFilterParams.setRateFilterDefinition ( rfd1 );
        rfd1.setMultiProviderRateFilterParams ( multiRateFilterParams );
        obj3.setMultiProviderRateFilterParams ( multiRateFilterParams );
        obj4.setMultiProviderRateFilterParams ( multiRateFilterParams );
        List<Organization> disableProviders = new ArrayList<Organization> ( 2 );
        disableProviders.add ( makerOrg );
        disableProviders.add ( takerOrg );
        orgFunc.setDisabledProviders ( disableProviders );
        String disabledCcyPairs = "EUR/USD,USD/JPY";
        orgFunc.setDisabledCurrencyPairs ( disabledCcyPairs );

        uow.commit ();

        // refresh the org function and configuration.
        orgFunc = ( BrokerOrganizationFunctionC ) getPersistenceSession ().refreshObject ( orgFunc );
        config = ( BrokerAdaptorConfiguration ) getPersistenceSession ().refreshObject ( config );

        assertNotNull ( orgFunc );
        assertNotNull ( orgFunc.getStartTime () );
        assertNotNull ( orgFunc.getStopTime () );
        assertEquals ( orgFunc.getStartTime ().toString (), startTime.toString () );
        assertEquals ( orgFunc.getStopTime ().toString (), stopTime.toString () );
        assertEquals ( orgFunc.getTimeZone (), "GMT" );
        assertNotNull ( config );
        assertNotNull ( orgFunc.getConfiguration () );
        assertNotNull ( orgFunc.getRateFilterDefinitions () );
        assertEquals ( orgFunc.getRateFilterDefinitions ().size (), 2 );
        assertNotNull ( orgFunc.getActiveRateFilterDefinition () );
        assertNotNull ( orgFunc.getActiveRateFilterDefinition ().getBrokerOrganizationFunction () );
        assertNotNull ( orgFunc.getActiveRateFilterDefinition ().getSingleProviderRateFilterParams () );
        assertNotNull ( orgFunc.getActiveRateFilterDefinition ().getMultiProviderRateFilterParams () );
        assertEquals ( orgFunc.getActiveRateFilterDefinition ().getSingleProviderRateFilterParams ().getSingleProviderRateFilterCcyPairParams ().size (), 2 );
        assertEquals ( orgFunc.getActiveRateFilterDefinition ().getMultiProviderRateFilterParams ().getMultiProviderRateFilterCcyPairParams ().size (), 2 );
        assertEquals ( orgFunc.getStreams ().size (), 2 );
        assertEquals ( orgFunc.getExecutionRules ().size (), 2 );
        assertNotNull ( orgFunc.getDisabledProviders () );
        assertEquals ( orgFunc.getDisabledProviders ().size (), 2 );

        uow = getPersistenceSession ().acquireUnitOfWork ();
        orgFunc = ( BrokerOrganizationFunctionC ) uow.registerObject ( orgFunc );
        RateFilterDefinition rfdToBeRemoved = null;    // done all this because if we try to delete ActiveRFD it will give constraints exceptions because active RFD is used in ccyPairParams etc also.
        for ( RateFilterDefinition rfd : orgFunc.getRateFilterDefinitions () )
        {
            if ( ! rfd.isSameAs ( orgFunc.getActiveRateFilterDefinition () ) )
            {
                rfdToBeRemoved = rfd;
                break;
            }
        }
        rfdToBeRemoved = ( RateFilterDefinition ) uow.registerObject ( rfdToBeRemoved );

        orgFunc.removeRateFilterDefinition ( rfdToBeRemoved );
        orgFunc.setActiveRateFilterDefinition ( null );
        rfdToBeRemoved.setBrokerOrganizationFunction ( null );
        uow.commit ();

        orgFunc = ( BrokerOrganizationFunctionC ) getPersistenceSession ().refreshObject ( orgFunc );
        rfdToBeRemoved = ( RateFilterDefinition ) getPersistenceSession ().refreshObject ( rfdToBeRemoved );
        assertEquals ( orgFunc.getRateFilterDefinitions ().size (), 1 );
        assertNull ( rfdToBeRemoved );
        assertEquals ( disabledCcyPairs, orgFunc.getDisabledCurrencyPairs () );
    }

    public void testStreamLookup ()
    {
        try
        {
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeReadOnlyClass ( BrokerOrganizationFunctionC.class );
            Organization testBroker = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            BrokerOrganizationFunction bof = testBroker.getBrokerOrganizationFunction ();
            assertNotNull ( bof );
            for ( Stream stream : bof.getStreams () )
            {
                assertEquals ( stream, bof.getStream ( stream.getShortName () ) );
            }
            BrokerOrganizationFunction registeredBof = ( BrokerOrganizationFunction ) uow.registerObject ( bof );
            registeredBof.update ();
            uow.commit ();

            for ( Stream stream : bof.getStreams () )
            {
                assertEquals ( stream, bof.getStream ( stream.getShortName () ) );
            }

            // refresh the broker org function.
            bof = ( BrokerOrganizationFunction ) session.refreshObject ( bof );
            for ( Stream stream : bof.getStreams () )
            {
                assertEquals ( stream, bof.getStream ( stream.getShortName () ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "Exception in stream lookup", e );
        }
    }
}



