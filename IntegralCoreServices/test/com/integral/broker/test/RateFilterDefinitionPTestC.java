package com.integral.broker.test;

import com.integral.broker.model.BrokerOrganizationFunctionC;
import com.integral.broker.model.MultiProviderRateFilterParams;
import com.integral.broker.model.MultiProviderRateFilterParamsC;
import com.integral.broker.model.RateFilterDefinition;
import com.integral.broker.model.RateFilterDefinitionC;
import com.integral.broker.model.SingleProviderRateFilterParams;
import com.integral.broker.model.SingleProviderRateFilterParamsC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

public class RateFilterDefinitionPTestC extends PTestCaseC
{
    static String name = "RateFilterDefinitionPTestC Test";

    public RateFilterDefinitionPTestC( String name )
    {
        super( name );
    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            RateFilterDefinition rfd = new RateFilterDefinitionC();
            rfd = ( RateFilterDefinition ) uow.registerObject( rfd );
            rfd.setStatus( 'T' );
            rfd.setRateFileringMode( 1 );
            rfd.setBrokerOrganizationFunction( ( BrokerOrganizationFunctionC ) uow.registerObject( new BrokerOrganizationFunctionC() ) );
            rfd.setMultiProviderRateFilterParams( ( MultiProviderRateFilterParams ) uow.registerObject( new MultiProviderRateFilterParamsC() ) );
            rfd.setSingleProviderRateFilterParams( ( SingleProviderRateFilterParams ) uow.registerObject( new SingleProviderRateFilterParamsC() ) );
            rfd.setShortName( "RFDTest" + System.nanoTime() );
            uow.commit();

            rfd = ( RateFilterDefinition ) getPersistenceSession().refreshObject( rfd );
            assertNotNull( rfd );
            //assertEquals( orgFunc.getShortName(), "RFDTest" );
            assertEquals( rfd.getRateFileringMode(), 1 );
            assertNotNull( rfd.getBrokerOrganizationFunction() );
            assertNotNull( rfd.getMultiProviderRateFilterParams() );
            assertNotNull( rfd.getSingleProviderRateFilterParams() );
        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }

}
