package com.integral.broker.test;

import com.integral.broker.model.MultiProviderRateFilterParams;
import com.integral.broker.model.MultiProviderRateFilterParamsC;
import com.integral.broker.model.RateFilterDefinition;
import com.integral.broker.model.RateFilterDefinitionC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

public class MultiProviderRateFilterParamsPTestC extends PTestCaseC
{
    static String name = "MultiProviderRateFilterParamsPTestC Test";

    public MultiProviderRateFilterParamsPTestC( String name )
    {
        super( name );
    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            MultiProviderRateFilterParams orgFunc = new MultiProviderRateFilterParamsC();
            assertTrue( orgFunc.isInvertedRateFilterCheckEnabled() );
            orgFunc = ( MultiProviderRateFilterParams ) uow.registerObject( orgFunc );
            orgFunc.setStatus( 'T' );
            orgFunc.setOffmarketCheckEnabled( true );
            orgFunc.setOffMarketMaxInverseSpread( 56.0 );
            orgFunc.setOffMarketMaxProviders( 169 );
            orgFunc.setOffMarketMinProviders( 89 );
            orgFunc.setStalenessCheckInterval( 789 );
            orgFunc.setStalenessCheckEnabled( true );
            orgFunc.setInvertedRateFilterCheckEnabled( true );
            orgFunc.setInvertedRateTolerance( 49.0 );
            RateFilterDefinition rfd = ( RateFilterDefinition ) uow.registerObject( new RateFilterDefinitionC() );
            rfd.setShortName( "Test" + System.nanoTime() );
            orgFunc.setRateFilterDefinition( rfd );
            rfd.setMultiProviderRateFilterParams( orgFunc );

            uow.commit();

            orgFunc = ( MultiProviderRateFilterParams ) getPersistenceSession().refreshObject( orgFunc );
            assertNotNull( orgFunc );

            assertEquals( orgFunc.isOffmarketCheckEnabled(), true );
            assertEquals( orgFunc.getOffMarketMaxInverseSpread(), 56.0 );
            assertEquals( orgFunc.getOffMarketMaxProviders(), 169 );
            assertEquals( orgFunc.getOffMarketMinProviders(), 89 );
            assertEquals( orgFunc.getStalenessCheckInterval(), 789 );
            assertEquals( orgFunc.isStalenessCheckEnabled(), true );
            assertEquals( orgFunc.getInvertedRateTolerance(), 49.0 );
            assertNotNull( orgFunc.getRateFilterDefinition() );
            assertTrue( orgFunc.isInvertedRateFilterCheckEnabled() );
        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }


}
