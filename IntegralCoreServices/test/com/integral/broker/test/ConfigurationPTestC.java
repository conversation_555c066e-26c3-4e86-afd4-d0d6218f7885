package com.integral.broker.test;

import com.integral.broker.model.Configuration;
import com.integral.broker.model.ConfigurationC;
import com.integral.broker.model.Spread;
import com.integral.broker.model.SpreadBias;
import com.integral.broker.model.SpreadC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Vector;


public class ConfigurationPTestC
        extends PTestCaseC
{
    static String name = "Configuration Test";

    public ConfigurationPTestC( String name )
    {
        super( name );
    }

    /**
     * Test testConfigurationProviderOrgNamespace for bug # 33169
     */
    public void testConfigurationProviderOrgNamespace()
    {
        // insert configuration 
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            //uow.removeAllReadOnlyClasses(); - the organization will remain as read only - so that it will get corrupt only in memory and not in DB
            ExpressionBuilder eb = new ExpressionBuilder();

            // To be run against app developer db
            //Try the below code before and after code change to ConfigurationC - make the priceProviders to have resetOwner=false

            // Read organization from DB and set it as price provider to a newly formed configuration
            String shortName = "MyTestConfig" + System.currentTimeMillis();
            ReadAllQuery raq1 = new ReadAllQuery( ConfigurationC.class, eb.get( "shortName" ).equal( shortName ) );

            Organization org = ReferenceDataCacheC.getInstance().getOrganization( "FI2" );
            Namespace nm1 = org.getNamespace();

            Organization org1 = ReferenceDataCacheC.getInstance().getOrganization( "FICpty11" );
            Organization org3 = ReferenceDataCacheC.getInstance().getOrganization( "FICpty12" );

            log( "Before config org1.getNamespace().getShortName() from memory ;" + org1.getNamespace().getShortName() );
            log( "Before config org3.getNamespace().getShortName() from memory ;" + org3.getNamespace().getShortName() );


            Collection<Organization> al = new ArrayList<Organization>( 1 );
            al.add( org1 );
            al.add( org3 );


            uow = getPersistenceSession().acquireUnitOfWork();
            Configuration config = new ConfigurationC();
            config.setShortName( shortName );
            config.setNamespace( nm1 );
            config.setSpreadType( Configuration.SPREAD_TYPE_MAXIMUM );
            Collection<Organization> al1 = new ArrayList<Organization>( al );
            al1.add( ( Organization ) new ReadNamedEntityC().execute( Organization.class, "FI1" ) );

            config.setMaxSpreadBias( SpreadBias.Bid );
            config.setMaximumTenor( Tenor.TOMORROW_TENOR );
            config.setSpreadMaximum( 89 );
            config.setSpreadMaximumEnabled( true );
            Spread sp = new SpreadC();
            sp = ( Spread ) uow.registerObject( sp );
            sp.setBidSpread( 10 );
            config.setESPSpread( sp );
            sp.setSpreadPreserved( true );
            assert(!config.isEspExternalPricingEnabled());
            config.setPricingSource( "EXCEL_PRICING" );
            config.setAllowedDeviationPercent(10.0);
            config.setCoverOnMTF(1);
            config.setTradingCapacity( "TCTest" );
            LegalEntity executingFirm = ( LegalEntity ) uow.registerObject( org1.getDefaultDealingEntity() );
            config.setMiFIDExecutingFirm( executingFirm );
            User user = (User )uow.registerObject(org.getDefaultDealingUser());
            config.setMiFIDExecutingUser(user);
            config.setMiFIDInvDecisionMaker(user);

            //do not register organization - only config
            uow.registerObject( config );
            log( " Config namespace from memory iss " + config.getNamespace().getShortName() );
            log( " Config spread type is " + config.getSpreadType() );
            log( " org1.getNamespace().getShortName() from memory ;" + org1.getNamespace().getShortName() );
            log( " org3.getNamespace().getShortName() from memory ;" + org3.getNamespace().getShortName() );
            uow.commit();


            uow = getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( org1 );
            uow.refreshObject( org3 );

            uow = getPersistenceSession().acquireUnitOfWork();
            uow = getPersistenceSession().acquireUnitOfWork();
            Configuration config1 = ( Configuration ) ( ( Vector ) uow.executeQuery( raq1 ) ).get( 0 );


            log( " Config namespace from DB iss " + config1.getNamespace().getShortName() );
            log( " org2.getNamespace().getShortName() from DB ;" + org1.getNamespace().getShortName() );
            log( " org3.getNamespace().getShortName() from memory ;" + org3.getNamespace().getShortName() );
            assertEquals( config.getSpreadType(), Configuration.SPREAD_TYPE_MAXIMUM );
            assertEquals( config.getESPSpread().getBidSpread(), 10d );
            assertEquals( config.getAllowedDeviationPercent(),10d);
            config = ( Configuration ) getPersistenceSession().refreshObject( config );

            assertEquals( config.getMaxSpreadBias(), SpreadBias.Bid );
            log( "config.getMaximumTenor=" + config.getMaximumTenor() + ",Tenor.TOMRROW=" + Tenor.TOMORROW_TENOR );
            assertEquals( config.getMaximumTenor().getName(), Tenor.TOMORROW_TENOR.getName() );
            assertEquals( config.getSpreadMaximum(), 89d );
            assertEquals( config.isSpreadMaximumEnabled(), true );
            assertEquals( config.getESPSpread().isSpreadPreserved(), true );
            assertEquals( config.getPricingSource(),"EXCEL_PRICING"  );
            assert(config.isEspExternalPricingEnabled());
            // add another update on config.maximumTenor.
            uow = getPersistenceSession().acquireUnitOfWork();
            config = ( Configuration ) uow.registerObject( config );
            config.setMaximumTenor( null );
            assertNull( config.getMaximumTenor() );
            assertEquals( config.getCoverOnMTF(), 1 );
            assertEquals(config.getTradingCapacity(),"TCTest");
            assertTrue(executingFirm.isSameAs(config.getMiFIDExecutingFirm()));
            assertTrue(user.isSameAs(config.getMiFIDInvDecisionMaker()));
            assertTrue(user.isSameAs(config.getMiFIDExecutingUser()));

            uow.commit();

            config = ( Configuration ) getPersistenceSession().refreshObject( config );
            log( "max tenor=" + config.getMaximumTenor() );
            assertNull( config.getMaximumTenor() );
        }
        catch ( Exception e )
        {
            fail("testConfigurationProviderOrgNamespace", e);
        }
    }

    public void testFieldsPersistence()
    {
        // insert configuration
        try
        {
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.addReadOnlyClass ( CurrencyC.class );
            String shortName = "MyTestConfig" + System.currentTimeMillis();
            Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FICpty11" );
            Organization org3 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );


            Collection<Organization> al = new ArrayList<Organization>( 1 );
            al.add( org1 );
            al.add( org3 );


            Configuration config = new ConfigurationC();
            assertFalse( config.isSyntheticCrossPointsEnabled () );
            assertNull ( config.getVehicleCurrency () );
            Configuration registeredConfig = ( Configuration ) uow.registerObject ( config );
            config.setStatus ( 'T' );
            registeredConfig.setShortName( shortName );
            registeredConfig.setNamespace( org3.getNamespace () );
            registeredConfig.setSpreadType( Configuration.SPREAD_TYPE_MAXIMUM );
            registeredConfig.setVehicleCurrency ( gbp );
            registeredConfig.setSyntheticCrossPointsEnabled( true );
            uow.commit();

            Configuration refreshedConfig = ( Configuration ) IdcUtilC.refreshObject ( config );
            assertNotNull ( refreshedConfig );
            assertTrue( config.isSyntheticCrossPointsEnabled () );
            assertNotNull ( config.getVehicleCurrency () );
            assertTrue ( gbp.isSameAs ( config.getVehicleCurrency () ));
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
