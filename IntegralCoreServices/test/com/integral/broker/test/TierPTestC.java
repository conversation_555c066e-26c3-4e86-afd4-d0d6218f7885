package com.integral.broker.test;

import com.integral.broker.model.Configuration;
import com.integral.broker.model.ConfigurationC;
import com.integral.broker.model.ESPTierC;
import com.integral.broker.model.SpreadBias;
import com.integral.broker.model.Tier;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

public class TierPTestC extends PTestCaseC
{
    static String name = "TierPTestC Test";

    public TierPTestC( String name )
    {
        super( name );
    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            Tier tier = new ESPTierC();
            Tier registeredTier = ( Tier ) uow.registerObject( tier );
            tier.setStatus( 'T' );
            registeredTier.setBidLimit( 78.9 );
            registeredTier.setOfferLimit( 98.8 );
            registeredTier.setSortOrder( 7 );
            registeredTier.setSpreadMaximum( 77.8 );
            registeredTier.setSpreadMinimum( 32.2 );

            Configuration config = ( Configuration ) uow.registerObject( new ConfigurationC() );
            Configuration registeredConfig = ( Configuration ) uow.registerObject( config );
            registeredConfig.setShortName( System.currentTimeMillis() + "" );
            registeredTier.setConfiguration( registeredConfig );

            registeredTier.setMaxSpreadBias( SpreadBias.Offer );
            registeredTier.setSpreadBias( SpreadBias.Bid );
            registeredConfig.getTiers().add( registeredTier );
            uow.commit();

            tier = ( Tier ) getPersistenceSession().refreshObject( tier );
            config = ( Configuration ) getPersistenceSession().refreshObject( config );
            assertNotNull( config );
            assertNotNull( tier );
            assertTrue( config.isSameAs( tier.getConfiguration() ) );
            assertEquals( tier.getBidLimit(), 78.9 );
            assertEquals( tier.getOfferLimit(), 98.8 );
            assertEquals( tier.getSortOrder(), 7 );
            assertEquals( tier.getSpreadMaximum(), 77.8 );
            assertEquals( tier.getSpreadMinimum(), 32.2 );
            assertNotNull( tier.getConfiguration() );
            assertEquals( tier.getMaxSpreadBias(), SpreadBias.Offer );
            assertEquals( tier.getSpreadBias(), SpreadBias.Bid );
            assertEquals( config.getTiers().size(), 1 );
        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }

}
