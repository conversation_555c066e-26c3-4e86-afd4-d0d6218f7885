package com.integral.broker.test;

import com.integral.broker.model.BrokerOrganizationFunctionC;
import com.integral.broker.model.ExecutionRule;
import com.integral.broker.model.ExecutionRuleC;
import com.integral.broker.model.ExecutionRuleCondition;
import com.integral.broker.model.ExecutionRuleConditionC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.List;

public class ExecutionRulePTestC extends PTestCaseC
{
    static String name = "ExecutionRulePTestC Test";

    public ExecutionRulePTestC( String name )
    {
        super( name );
    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            ExecutionRule orgFunc = new ExecutionRuleC();
            orgFunc.setShortName( System.currentTimeMillis() + "");
            orgFunc = ( ExecutionRule ) uow.registerObject( orgFunc );
            orgFunc.setStatus( 'T' );
            orgFunc.setBrokerOrganizationFunction( ( BrokerOrganizationFunctionC ) uow.registerObject( new BrokerOrganizationFunctionC() ) );

            ExecutionRuleCondition stream1 = ( ExecutionRuleCondition ) uow.registerObject( new ExecutionRuleConditionC() );
            ExecutionRuleCondition stream2 = ( ExecutionRuleCondition ) uow.registerObject( new ExecutionRuleConditionC() );
            List<ExecutionRuleCondition> ruleConditions = new ArrayList<ExecutionRuleCondition>( 2 );
            ruleConditions.add( stream1 );
            ruleConditions.add( stream2 );

            orgFunc.setExecutionRuleConditions( ruleConditions );
            uow.commit();

            orgFunc = ( ExecutionRule ) getPersistenceSession().refreshObject( orgFunc );
            assertNotNull( orgFunc );
            assertNotNull( orgFunc.getBrokerOrganizationFunction() );
            assertEquals( orgFunc.getExecutionRuleConditions().size(), 2 );
        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }

}
