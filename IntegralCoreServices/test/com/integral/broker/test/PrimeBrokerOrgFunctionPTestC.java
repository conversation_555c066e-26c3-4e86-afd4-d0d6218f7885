package com.integral.broker.test;

import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.primebroker.CustomerSegment;
import com.integral.primebroker.CustomerSegmentC;
import com.integral.primebroker.PrimeBrokerOrganizationFunctionC;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 9/6/12
 * Time: 3:31 PM
 * Persistence Test for Prime Broker Org Function.
 */
public class PrimeBrokerOrgFunctionPTestC extends PTestCaseC {

    static String name = "PrimeBrokerOrgFunction Test";

    public PrimeBrokerOrgFunctionPTestC(String name) {
        super(name);
    }

    public static void main(String args[]) {
        junit.textui.TestRunner.run(suite());
    }

    public static Test suite() {
        TestSuite suite = new TestSuite();
        suite.addTest(new PrimeBrokerOrgFunctionPTestC("testPrimeBrokerOrgFunctionLPPersistence"));
        return suite;
    }

    /**
     * Test prime broker org function
     */
    public void testPrimeBrokerOrgFunctionCustSegPersistence() throws Exception {
        // insert prime broker org function
        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();
        uow.addReadOnlyClass(OrganizationC.class);
        PrimeBrokerOrganizationFunctionC orgFunc = new PrimeBrokerOrganizationFunctionC();
        orgFunc = (PrimeBrokerOrganizationFunctionC) uow.registerObject(orgFunc);
        orgFunc.setStatus('T');

        Collection<CustomerSegment> customerSegments = new ArrayList<CustomerSegment>(5);
        CustomerSegment customerSegment = (CustomerSegment) uow.registerObject(new CustomerSegmentC());
        customerSegment.setShortName("MyName1");
        customerSegments.add(customerSegment);
        CustomerSegment customerSegment2 = (CustomerSegment) uow.registerObject(new CustomerSegmentC());
        customerSegment2.setShortName("MyName2");
        customerSegments.add(customerSegment2);
        orgFunc.setCustomerSegments(customerSegments);

        uow.commit();

        // refresh the org function
        orgFunc = (PrimeBrokerOrganizationFunctionC) getPersistenceSession().refreshObject(orgFunc);

        assertNotNull(orgFunc);
        Collection<CustomerSegment> customerSegmentCollection = orgFunc.getCustomerSegments();
        assertNotNull(customerSegmentCollection);
        assertEquals(customerSegmentCollection.size(), 2);
        Collection<String> customerSegmentNames = new ArrayList<String>(5);
        for (CustomerSegment segment : customerSegmentCollection) {
            customerSegmentNames.add(segment.getShortName());
        }
        assertTrue(customerSegmentNames.contains("MyName1"));
        assertTrue(customerSegmentNames.contains("MyName2"));

        uow = getPersistenceSession().acquireUnitOfWork();
        orgFunc = (PrimeBrokerOrganizationFunctionC) uow.registerObject(orgFunc);
        orgFunc.removeCustomerSegment(customerSegment);
        uow.commit();

        orgFunc = (PrimeBrokerOrganizationFunctionC) getPersistenceSession().refreshObject(orgFunc);
        assertNotNull(orgFunc);
        customerSegmentCollection = orgFunc.getCustomerSegments();
        assertNotNull(customerSegmentCollection);
        assertEquals(customerSegmentCollection.size(), 1);
        customerSegmentNames = new ArrayList<String>(5);
        for (CustomerSegment segment : customerSegmentCollection) {
            customerSegmentNames.add(segment.getShortName());
        }
        assertTrue(customerSegmentNames.contains("MyName2"));

        uow = getPersistenceSession().acquireUnitOfWork();
        orgFunc = (PrimeBrokerOrganizationFunctionC) uow.registerObject(orgFunc);
        orgFunc.addCustomerSegment(customerSegment);
        uow.commit();

        orgFunc = (PrimeBrokerOrganizationFunctionC) getPersistenceSession().refreshObject(orgFunc);
        assertNotNull(orgFunc);
        customerSegmentCollection = orgFunc.getCustomerSegments();
        assertNotNull(customerSegmentCollection);
        assertEquals(customerSegmentCollection.size(), 2);
        customerSegmentNames = new ArrayList<String>(5);
        for (CustomerSegment segment : customerSegmentCollection) {
            customerSegmentNames.add(segment.getShortName());
        }
        assertTrue(customerSegmentNames.contains("MyName1"));
        assertTrue(customerSegmentNames.contains("MyName2"));
        log.info("Finished PrimeBrokerOrgFunctionPTestC.testPrimeBrokerOrgFunctionPersistence");
    }

    public void testPrimeBrokerOrgFunctionLPPersistence() throws Exception {
        // insert prime broker org function
        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();
        uow.addReadOnlyClass(OrganizationC.class);
        PrimeBrokerOrganizationFunctionC orgFunc = new PrimeBrokerOrganizationFunctionC();
        orgFunc = (PrimeBrokerOrganizationFunctionC) uow.registerObject(orgFunc);
        orgFunc.setStatus('T');


        Collection<Organization> providers = new ArrayList<Organization>(5);
        Organization org = ReferenceDataCacheC.getInstance().getOrganization("DBNA");
        providers.add(org);
        org = ReferenceDataCacheC.getInstance().getOrganization("Broker1");
        providers.add(org);
        orgFunc.setSelectedProviders(providers);

        uow.commit();

        // refresh the org function
        orgFunc = (PrimeBrokerOrganizationFunctionC) getPersistenceSession().refreshObject(orgFunc);

        assertNotNull(orgFunc);
        Collection<Organization> selectedProviders = orgFunc.getSelectedProviders();
        assertNotNull(selectedProviders);
        assertEquals(selectedProviders.size(), 2);
        Collection<String> selectedProviderNames = new ArrayList<String>(5);
        for (Organization org1 : selectedProviders) {
            selectedProviderNames.add(org1.getShortName());
        }
        assertTrue(selectedProviderNames.contains("DBNA"));
        assertTrue(selectedProviderNames.contains("Broker1"));

        uow = getPersistenceSession().acquireUnitOfWork();
        orgFunc = (PrimeBrokerOrganizationFunctionC) uow.registerObject(orgFunc);
        orgFunc.removeProvider(ReferenceDataCacheC.getInstance().getOrganization("Broker1"));
        uow.commit();

        orgFunc = (PrimeBrokerOrganizationFunctionC) getPersistenceSession().refreshObject(orgFunc);
        assertNotNull(orgFunc);
        selectedProviders = orgFunc.getSelectedProviders();
        assertNotNull(selectedProviders);
        assertEquals(selectedProviders.size(), 1);
        selectedProviderNames = new ArrayList<String>(5);
        for (Organization org1 : selectedProviders) {
            selectedProviderNames.add(org1.getShortName());
        }
        assertTrue(selectedProviderNames.contains("DBNA"));
    }
}
