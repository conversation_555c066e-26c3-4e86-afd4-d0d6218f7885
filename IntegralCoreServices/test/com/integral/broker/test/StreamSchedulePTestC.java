package com.integral.broker.test;

import com.integral.broker.model.Stream;
import com.integral.broker.model.StreamC;
import com.integral.broker.model.StreamSchedule;
import com.integral.broker.model.StreamScheduleC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Time;

public class StreamSchedulePTestC extends PTestCaseC
{
    static String name = "StreamSchedulePTestC Test";

    public StreamSchedulePTestC( String name )
    {
        super( name );
    }

    public void testPersistence()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            StreamSchedule orgFunc = new StreamScheduleC();
            orgFunc = ( StreamSchedule ) uow.registerObject( orgFunc );
            orgFunc.setStatus( 'T' );

            orgFunc.setDay( 3 );
            Time start = Time.valueOf("11:11:11");
            Time end = Time.valueOf("10:10:10");
            orgFunc.setEndTimeEnabled( true );
            orgFunc.setStartTimeEnabled( true );
            Stream stream = ( Stream ) uow.registerObject( new StreamC() );
            stream.setShortName( System.currentTimeMillis() + "" );
            /*orgFunc.setStream( stream );*/
            orgFunc.setStartTime( start );
            orgFunc.setEndTime( end );

            uow.commit();

            orgFunc = ( StreamSchedule ) getPersistenceSession().refreshObject( orgFunc );
            assertNotNull( orgFunc );
            /*assertNotNull( orgFunc.getStream() );*/
            assertEquals( orgFunc.isEndTimeEnabled(), true );
            assertEquals( orgFunc.isStartTimeEnabled(), true );
            assertEquals( orgFunc.getDay(), 3 );
            assertEquals( orgFunc.getStartTime().getTime(), start.getTime() );
            assertEquals( orgFunc.getEndTime().getTime(), end.getTime() );

        }
        catch ( Exception e )
        {
            fail( "testPersistence", e );
        }
    }

}
