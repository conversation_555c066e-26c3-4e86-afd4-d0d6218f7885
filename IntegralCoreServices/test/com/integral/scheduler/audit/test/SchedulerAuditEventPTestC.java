package com.integral.scheduler.audit.test;

import com.integral.audit.AuditEvent;
import com.integral.audit.AuditFactory;
import com.integral.facade.FacadeFactory;
import com.integral.scheduler.audit.SchedulerAuditEventFacade;
import com.integral.scheduler.audit.SchedulerAuditEventFacadeC;
import com.integral.scheduler.audit.SchedulerAuditEventManagerC;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import com.integral.time.IdcDateTime;
import org.eclipse.persistence.sessions.UnitOfWork;

public class SchedulerAuditEventPTestC extends PTestCaseC
{

    static
    {
        FacadeFactory.setFacade( SchedulerAuditEventFacade.FACADE_NAME, AuditEvent.class, SchedulerAuditEventFacadeC.class );    
    }
    
    public SchedulerAuditEventPTestC( String name )
    {
        super( name );
    }
        
    public void testSchedulerAuditEventPersistence()
    {
        try
        {
            IdcTransaction tx = IdcSessionManager.getInstance().newTransaction();
            IdcSessionManager.getInstance().setTransaction( tx );

            UnitOfWork uow = IdcSessionManager.getInstance().getTransaction().getUOW();
            
            AuditEvent newAuditEvent = AuditFactory.newAuditEvent( SchedulerAuditEventManagerC.SCHEDULER_COMPONENT, SchedulerAuditEventManagerC.ACTION_START_EXECUTION, "" );
            AuditEvent registeredEvent = ( AuditEvent ) newAuditEvent.getRegisteredObject();

            SchedulerAuditEventFacade auditFacade = ( SchedulerAuditEventFacade ) registeredEvent.getFacade( SchedulerAuditEventFacade.FACADE_NAME );
            IdcDateTime dateTime = auditFacade.getDateTime();
            String description = "test scheduler audit event description";
            auditFacade.setEventDescription( description );
            String name = "test scheduler audit event name";
            auditFacade.setEventName( name );
            String userName = "test scheduler audit event username";
            auditFacade.setUserName( userName );
            String serverName = "test scheduler audit event servername";
            auditFacade.setVirtualServer( serverName );
            
            uow.commit();
            
            tx = IdcSessionManager.getInstance().newTransaction();
            IdcSessionManager.getInstance().setTransaction( tx );
            uow = IdcSessionManager.getInstance().getTransaction().getUOW();
            
            AuditEvent refreshedAuditEvent = ( AuditEvent ) uow.refreshObject( newAuditEvent );
            SchedulerAuditEventFacade refreshedAuditFacade = ( SchedulerAuditEventFacade ) refreshedAuditEvent.getFacade( SchedulerAuditEventFacade.FACADE_NAME );
            assertTrue( dateTime.equals( refreshedAuditFacade.getDateTime() ) );
            assertTrue( description.equals( refreshedAuditFacade.getEventDescription() ) );
            assertTrue( name.equals( refreshedAuditFacade.getEventName() ) );
            assertTrue( userName.equals( refreshedAuditFacade.getUserName() ) );
            assertTrue( serverName.equals( refreshedAuditFacade.getVirtualServer() ) );
            uow.deleteObject( refreshedAuditEvent );

            uow.commit();
            IdcSessionManager.getInstance().setTransaction( null );
        }
        catch ( Exception ex )
        {
            fail( "testSchedulerAuditEventPersistence failed with exception ", ex );
        }
    }
}
