package com.integral.scheduler

import com.integral.rds.client.ReferenceDataService
import com.integral.rds.exception.ErrorCode
import com.integral.rds.exception.ReferenceDataServiceException
import com.integral.rds.message.Query

import static org.mockito.Matchers.any
import static org.mockito.Mockito.*
/**
 * Created by rushabh on 10/4/16.
 */
class ScheduleEventRDSQueryServiceTest extends GroovyTestCase{

    ScheduleEventRDSQueryService queryService;
    public void setUp(){
        queryService = new ScheduleEventRDSQueryService();
        queryService = spy(queryService);
        ReferenceDataService mockRDS = mock(ReferenceDataService.class);
        when(queryService.getReferenceDataService()).thenReturn(mockRDS)
        ReferenceDataServiceException rdsException = new ReferenceDataServiceException(null,ErrorCode.ERR_RETRY_ITR,null);
        when(mockRDS.getIterator(any(Class.class),anyString(),any(Query.class))).thenThrow(rdsException)
    }

    public void testScheduleEventAllEvents(){
        List<ScheduleEventC> scheduleEventCList = queryService.getAllScheduleEvents();
        assertEquals(0,scheduleEventCList.size())
    }

    public void testScheduleEventById(){
        ScheduleEventC scheduleEventC = queryService.getScheduleEventById(0);
        assertNull(scheduleEventC)
    }

    public void testScheduleEventByName(){
        List<ScheduleEventC> scheduleEventCList = queryService.getScheduleEventsByEventName("Test");
        assertEquals(0,scheduleEventCList.size())
    }

    public void testScheduleEventByStatus(){
        List<ScheduleEventC> scheduleEventCList = queryService.getScheduleEventsByStatus('A'.charAt(0));
        assertEquals(0,scheduleEventCList.size())
    }

}
