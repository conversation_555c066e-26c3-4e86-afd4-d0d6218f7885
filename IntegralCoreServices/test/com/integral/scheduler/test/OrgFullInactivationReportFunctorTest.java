package com.integral.scheduler.test;

// Copyright (c) 2017 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.admin.OrgFullInactivationReportFunctor;
import com.integral.is.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.scheduler.ScheduleFunctor;
import com.integral.test.DealingPTestCaseC;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;

import java.util.Collection;
import java.util.HashSet;

public class OrgFullInactivationReportFunctorTest extends DealingPTestCaseC
{
    static String name = "Org Full Inactivation Report Test";

    public OrgFullInactivationReportFunctorTest( String name )
    {
        super( name );
    }

    public void testGenerateReport()
    {
        Collection<Organization> inactivatedOrgs = new HashSet<Organization> ();
        try
        {
            Organization[] orgs = ReferenceDataCacheC.getInstance ().getOrgs ();
            for ( Organization org : orgs )
            {
                org.setActive ( false );
                org.setFullInactivation ( true );
            }

            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            OrgFullInactivationReportFunctor functor = new OrgFullInactivationReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.execute ( wm );
            String listData = ( String ) wm.getParameterValue ( OrgFullInactivationReportFunctor.ORGS_LIST );
            System.out.println ("#################### org list data=" + listData );
            assertNotNull ( listData );
            sleepFor ( 1000 );
        }
        catch ( Exception e )
        {
            fail ( "testGenerateReport", e );
        }
        finally
        {
            for ( Organization org: inactivatedOrgs )
            {
                IdcUtilC.refreshObject ( org );
            }
        }
    }

    public void testValidateFunctor()
    {
        OrgFullInactivationReportFunctor functor = new OrgFullInactivationReportFunctor();
        assertFalse ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) ) ;
        functor.setFromEmailAddress ( "<EMAIL>" );
        functor.setToEmailAddresses ( "<EMAIL>" );
        assertTrue ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) ) ;

        functor.setFromEmailAddress ( "dsfdsfd" );
        assertFalse ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) ) ;
        functor.setFromEmailAddress ( "<EMAIL>" );
        functor.setToEmailAddresses ( "nobodyintegral.com" );
        assertFalse ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) ) ;

        functor.setFromEmailAddress ( "<EMAIL>" );
        functor.setToEmailAddresses ( "<EMAIL>,3543434" );
        assertFalse ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) ) ;

        functor.setFromEmailAddress ( "<EMAIL>" );
        functor.setToEmailAddresses ( "<EMAIL>,<EMAIL>" );
        assertTrue ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) ) ;
    }
}
