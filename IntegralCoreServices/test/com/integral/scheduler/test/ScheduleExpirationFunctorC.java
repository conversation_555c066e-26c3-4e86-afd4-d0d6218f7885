package com.integral.scheduler.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.scheduler.ScheduleFunctor;
import com.integral.scheduler.ScheduleFunctorC;

/**
 * Test class for testing expirations.
 */
public class ScheduleExpirationFunctorC extends ScheduleFunctorC
{
    protected Log log = LogFactory.getLog( this.getClass() );

    public ScheduleExpirationFunctorC()
    {
        log.info( "ScheduleExpirationFunctorC: constructor called on instance #" + this.hashCode() );
    }

    public void execute( WorkflowMessage msg )
    {
        log.info( "ScheduleExpirationFunctorC: execute called on instance #" + this.hashCode() );
    }

    @Override
    public String getDescription()
    {
        return "Test class for testing expirations";
    }
}
