package com.integral.scheduler.test;


import com.integral.log.rule.LogAction;
import com.integral.log.rule.LogActionC;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.PersistenceFactory;
import com.integral.rule.Rule;
import com.integral.rule.RuleC;
import com.integral.rule.RuleSet;
import com.integral.rule.RuleSetC;
import com.integral.scheduler.Expiration;
import com.integral.scheduler.ScheduleEvent;
import com.integral.scheduler.ScheduleEventC;
import com.integral.scheduler.ScheduleEventCleanupTaskC;
import com.integral.scheduler.ScheduleEventParameters;
import com.integral.scheduler.ScheduleFactory;
import com.integral.scheduler.Scheduler;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.server.VirtualServer;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.DaysOfWeekBitSet;
import com.integral.time.DaysOfWeekBitSetC;
import com.integral.time.IdcDateTime;
import com.integral.xml.binding.JavaXMLBinder;
import com.integral.xml.binding.JavaXMLBinderFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import java.util.Vector;
import junit.framework.*;


public class SchedulerPTestC
        extends PTestCaseC
{
    static String name = "Scheduler Test";
    Vector users = null;

    public SchedulerPTestC( String name )
    {
        super( name );
    }

    private RuleSet getRuleSet()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Test" );
            Object lookedupObject = getPersistenceSession().readObject( RuleSet.class, expr );

            log( "Found rule set - Test" );
            return ( RuleSet ) lookedupObject;
        }
        catch ( Exception e )
        {
            log( "Cannot find rule set - Test" );
        }
        return null;
    }


    private RuleSet getRuleSet( String name )
    {
        RuleSet rs = null;
        try
        {
            InputStream xmlStream = this.getClass().getClassLoader().getResourceAsStream( name );
            InputStreamReader xmlReader = new InputStreamReader( xmlStream );

            JavaXMLBinder conv = JavaXMLBinderFactory.newJavaXMLBinder();
            rs = ( RuleSet ) conv.convertFromXML( xmlReader, "Integral" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "error getting ruleset " + name );
        }
        return rs;
    }

    /**
     * Tests a transient single event
     */
    public void testTransientSingleEvent()
    {
        log( "Transient single test" );

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "Send" );
        // Set the event time to 2 secs from now
        sep.setEventTime( 2 );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        Scheduler.getInstance().schedule( se );
        log( "done scheduling the transient event..." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "exiting" );
    }

    public void testSimpleEvent()
    {
        log( "Simple event test" );
        ScheduleEvent se1 = insertEvent( 0 );
        if ( null == se1 )
        {
            return;
        }
        Session session = null;
        try
        {
            session = PersistenceFactory.newSession();
            se1 = ( ScheduleEvent ) session.refreshObject( se1 );
            assertTrue( se1.getScheduleEventParameters().getExecutionDays().isMonday() );
            assertTrue( se1.getScheduleEventParameters().getExecutionDays().isTuesday() );
            assertFalse( se1.getScheduleEventParameters().getExecutionDays().isWednesday() );
            assertTrue( se1.getScheduleEventParameters().getExecutionDays().isThursday() );
            assertFalse( se1.getScheduleEventParameters().getExecutionDays().isFriday() );
            assertTrue( se1.getScheduleEventParameters().getExecutionDays().isSaturday() );
            assertTrue( se1.getScheduleEventParameters().getExecutionDays().isSunday() );

        }
        catch ( Exception e )
        {
            fail( "Connecting to database" + e, e );
        }


        Scheduler.getInstance().schedule( se1 );

        log( "done scheduling the event..." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }


        log( "Cancelling scheduled request..." );
        Scheduler.getInstance().cancel( se1 );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "exiting" );
    }


    /**
     * Tests a transient periodic event
     */
    public void testTransientPeriodicEvent()
    {
        log( "Transient test" );

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet(  ) );
        sep.setEventName( "Send" );
        // Set the event time to 2 secs from now
        sep.setEventTime( 2 );
        // run every 7 seconds
        sep.setPeriod( 7 );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        Scheduler.getInstance().schedule( se );
        log( "done scheduling the transient event..." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "exiting" );
    }


    public void testPeriodicEvent()
    {
        log( "Periodic event test" );
        ScheduleEvent se1 = insertEvent( 9 );
        if ( null == se1 )
        {
            return;
        }
        Scheduler.getInstance().schedule( se1 );

        log( "done scheduling the event..." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }


        log( "Cancelling scheduled request..." );
        Scheduler.getInstance().cancel( se1 );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "exiting" );
    }


    public ScheduleEvent insertEvent( int period )
    {
        log( "Creating new event..." );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ScheduleEvent se = ScheduleFactory.newScheduleEvent();
            se = ( ScheduleEvent ) uow.registerObject( se );

            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
            RuleSet rs = getRuleSet();
            sep.setRuleSet( ( RuleSet ) uow.registerObject( rs ) );
            sep.setEventName( "Send" );
            sep.setEventTime( 2 );
            // Set weekly execution flag.
            DaysOfWeekBitSetC executionDays = new DaysOfWeekBitSetC();
            executionDays.setMonday( true );
            executionDays.setTuesday( true );
            executionDays.setWednesday( false );
            executionDays.setThursday( true );
            executionDays.setFriday( false );
            executionDays.setSaturday( true );
            executionDays.setSunday( true );
            sep.setExecutionDays( executionDays );
            sep.setVirtualServer( ( VirtualServer ) uow.registerObject( RuntimeFactory.getServerRuntimeMBean().getGlobalVirtualServer() ) );
            sep.setTimeZone( "America/New_York" );
            if ( period > 0 )
            {
                sep.setPeriod( period );
            }

            se.setScheduleEventParameters( sep );
            se.setScheduleEventOwner( rs );

            uow.commit();
            uow = getPersistenceSession().acquireUnitOfWork();
            se = ( ScheduleEvent ) uow.refreshObject( se );
            sep = se.getScheduleEventParameters();
            assertEquals( "Virtual server should be same ", sep.getVirtualServer().getObjectId(), RuntimeFactory.getServerRuntimeMBean().getGlobalVirtualServer().getObjectId() );
            assertEquals( "time zone should be America/New_York ", sep.getTimeZone(), "America/New_York" );
            assertNotNull( TimeZone.getTimeZone( sep.getTimeZone() ) );
            log( "ScheduleEvent objectID = " + se.getObjectID() );
            return se;
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
        return null;
    }

    public ScheduleEvent testPersistentEvent()
    {
        log( "Creating new event..." );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ScheduleEvent se = ScheduleFactory.newScheduleEvent();
            se = ( ScheduleEvent ) uow.registerNewObject( se );

            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
            RuleSet rs = getRuleSet();
            sep.setRuleSet( ( RuleSet ) uow.registerObject( rs ) );
            sep.setEventName( "Send" );
            sep.setEventTime( 2 );
            sep.setPeriod( 60 );
            sep.setStatus( "T" );

            se.setScheduleEventParameters( sep );
            se.setScheduleEventOwner( rs );

            uow.commit();

            log( "ScheduleEvent objectID = " + se.getObjectID() );

            log( "insertTest" );

            return se;
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
        return null;
    }

    /**
     * Sets the default system locale and timezone
     */
    public static String setDefaultLocale()
    {
        String localeLanguage = "en";
        String localeCountry = "US";
        String localeInfoString = null;

        try
        {
            TimeZone tm = TimeZone.getTimeZone( "GMT" );
            TimeZone.setDefault( tm );

            Locale.setDefault( new Locale( localeLanguage, localeCountry ) );
            localeInfoString = " System locale set to: " + Locale.getDefault().toString();
        }
        catch ( Exception e )
        {
            localeInfoString = " Error setting system locale set to: " + localeLanguage + '/' + localeCountry + '\n';
            localeInfoString += " Exception message: " + e + '\n';
        }

        return localeInfoString;
    }

    static final String PRINTRULESET = "SchedulerTestPrintRuleSet1";

    private RuleSet getPrintRuleSet()
    {
        RuleSet rs = null;
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( PRINTRULESET );
            rs = ( RuleSet ) getPersistenceSession().readObject( RuleSet.class, expr );
            log( "Found rule set - SchedulerTestPrintRuleSet" );

        }
        catch ( Exception e )
        {
            log( "Cannot find rule set - Test" );
        }

        if ( rs == null )
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            LogAction logAction = new LogActionC();
            logAction.setMessage( "LogAction Test Message" );
            logAction.setLevel( LogAction.INFO );

            Rule printRule = new RuleC();
            printRule.setShortName( "SchedulerTestPrintRule" );
            printRule.addAction( logAction );

            rs = new RuleSetC();
            rs.setShortName( PRINTRULESET );
            rs.addRule( printRule );

            uow.registerObject( rs );

            uow.commit();

            try
            {
                ExpressionBuilder eb = new ExpressionBuilder();
                Expression expr = eb.get( "shortName" ).equal( PRINTRULESET );
                rs = ( RuleSet ) getPersistenceSession().readObject( RuleSet.class, expr );
                log( "Found rule set - SchedulerTestPrintRuleSet" );

            }
            catch ( Exception e )
            {
                log( "Cannot find rule set - Test" );
            }
        }

        return rs;
    }

    public void testExpireWithTransientRequest()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ScheduleEvent se = ScheduleFactory.newScheduleEvent();
            se = ( ScheduleEvent ) uow.registerNewObject( se );

            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();

            RuleSet rs = getPrintRuleSet();
            rs = ( RuleSet ) uow.registerObject( rs );
            // CacheFactory.getCacheManager().put(rs);

            sep.setRuleSet( rs );
            sep.setEventName( "Send" );
            sep.setEventTime( 3 );
            // sep.setPeriod(60);

            se.setScheduleEventParameters( sep );
            se.setScheduleEventOwner( rs );

            uow.commit();
            log( "ScheduleEvent objectID = " + se.getObjectID() );

            Scheduler.getInstance().schedule( se );

            log( "starting to wait 10 seconds..." );
            try
            {
                Thread.sleep( 5000 );
            }
            catch ( Exception e )
            {
            }
            log( "end of waiting 10 seconds" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testExpireWithTransientRequest" );
        }
    }

    /**
     * Tests programatically invoking the scheduler cleanup task
     */
    public void testCleanupTask()
    {
        ScheduleEventCleanupTaskC task = new ScheduleEventCleanupTaskC();
        try
        {
            List eventNames = new ArrayList();

            int num = task.deleteExpiredEvents( eventNames );

            eventNames.add( "FOOBAR" );
            num = task.deleteExpiredEvents( eventNames );

            eventNames.add( "QExpired" );
            num = task.deleteExpiredEvents( eventNames );

            num = task.deleteExpiredEvents( eventNames, new Long( 10 ) );
        }
        catch ( Exception e )
        {
            fail( "Failed testCleanupTask. Exception : ", e );
        }
    }

    public void testExpiration()
    {
        Expiration expiration = ScheduleFactory.newExpiration();

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "Send" );

        // Set the event time to 2 secs from now
        sep.setEventTime( 2 );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        expiration.setScheduleEvent( se );
        expiration.start();

        log( "done scheduling the transient event..." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "finished unit test" );
    }

    public void testTransactionalNonStartedExpiration()
    {
        // do not start a transaction

        // create expiration
        Expiration expiration = ScheduleFactory.newExpiration();

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "Send" );

        // Set the event time to 2 secs from now
        sep.setEventTime( 2 );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        expiration.setScheduleEvent( se );

        try
        {
            expiration.startTransactional();
            //ptestc always starts the transaction. Hence it's not possible.
            //fail( "expect to throw an error if a transaction is not started" );
        }
        catch ( Exception e )
        {
        }

    }


    public void testTransactionalNonCommittedExpiration()
    {
        // start a transaction
        IdcSessionContext ctx = null;
        IdcTransaction xact = null;
        try
        {
            ctx = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            xact = IdcSessionManager.getInstance().newTransaction( ctx );
        }
        catch ( Exception e )
        {
            fail( "error starting transaction: " + e );
        }

        // create expiration
        Expiration expiration = ScheduleFactory.newExpiration();

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "Send" );

        // Set the event time to 2 secs from now
        sep.setEventTime( 2 );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        expiration.setScheduleEvent( se );
        expiration.startTransactional();

        log( "done scheduling the transient event..." );


        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }
        finally
        {
            IdcSessionManager.getInstance().setTransaction( null );
        }

        log( "finished unit test" );
    }


    public void testTransactionalCommittedExpiration()
    {
        // start a transaction
        IdcSessionContext ctx = null;
        IdcTransaction xact = null;
        try
        {
            ctx = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            xact = IdcSessionManager.getInstance().newTransaction( ctx );
        }
        catch ( Exception e )
        {
            fail( "error starting transaction: " + e );
        }

        // create expiration
        Expiration expiration = ScheduleFactory.newExpiration();

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "Send" );

        // Set the event time to 2 secs from now
        sep.setEventTime( 2 );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        expiration.setScheduleEvent( se );
        expiration.startTransactional();

        log( "done scheduling the transient event..." );

        // commit transaction, this will trigger the expiration
        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }
        log( "about to commit..." );
        xact.commit();
        log( "finished committing." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "finished unit test" );
    }

    public void testDaylightSavingGainHour()
    {
        try
        {
            ConfigurationFactory.getTimerMBean().setProperty( "Scheduler.UsePersistedTimeZone", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ScheduleEvent se = ScheduleFactory.newScheduleEvent();
            se = ( ScheduleEvent ) uow.registerNewObject( se );

            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
            // Set period to 1 day.
            sep.setPeriod( 24 * 60 * 60 );
            // Create DateTime for first Sunday of November 00:00:00 in America/New_York timezone.
            Calendar c = ScheduleEventTestHelperC.getCalendarGainHour();
            IdcDateTime currentEventTime = DateTimeFactory.newDateTime( c.getTime(), TimeZone.getTimeZone( "America/New_York" ) );

            sep.setEventName( "testDaylightSavingGainHour_" + Calendar.getInstance().getTime().getTime() );
            sep.setEventTime( currentEventTime );
            sep.setTimeZone( "America/New_York" );
            se.setScheduleEventParameters( sep );

            long t1 = sep.getEventTime().asJdkDate().getTime();
            uow.commit();

            uow = getPersistenceSession().acquireUnitOfWork();
            se = ( ScheduleEvent ) uow.refreshObject( se );
            long t2 = se.getScheduleEventParameters().getEventTime().asJdkDate().getTime();

            assertTrue( t1 == t2 );

            se.getScheduleEventParameters().setEventTime( se.getScheduleEventParameters().getNextEventTime() );
            uow.commit();

            uow = getPersistenceSession().acquireUnitOfWork();
            se = ( ScheduleEvent ) uow.refreshObject( se );
            long t3 = se.getScheduleEventParameters().getEventTime().asJdkDate().getTime();

            assertTrue( t3 - t2 == 25 * 60 * 60 * 1000 );

            uow = getPersistenceSession().acquireUnitOfWork();
            se = ( ScheduleEvent ) uow.refreshObject( se );
            uow.deleteObject( se );
            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
        finally
        {
            ConfigurationFactory.getTimerMBean().removeProperty( "Scheduler.UsePersistedTimeZone()", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

   /* public void testweeklySchedulerCheck()
    {
        // Event Time array
        // Create ScheduleEventParameters
        log("******************Starting testweeklySchedulerCheck********************");
        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "WeeklyScheduler" );
        sep.setPeriod( 1 );
        int year = 2013;
        int month = 5;//June
        *//**
         * the array's distribution
         * 1st line : 1st value: event's day, 2nd value: event's hour, 3rd value: event's minute, 4th value event's second
         * 2nd line : 1st value: current time's day, 2nd value: current time's hour, 3rd value: current time's minute, 4th value: current time's second
         * 3rd line : 1st value: day set, 2nd and 3rd value: (Mon-Sun => 0-6) which day is set for event(-1 means no day set), 4th value: 0/1 whether to execute now, 3rd value: 0/1 whether to turn off execution
         * Each test-case is of 3 lines.
         *//*
        int[] input =
        {
                26,00,30,00,
                25,22,00,00,
                5,-1,1,0,
                *//*20,15,45,
                22,15,30,
                5,-1,0,1,
                20,21,30,
                22,21,25,
                3,-1,1,0,
                21,21,30,
                22,21,35,
                3,-1,0,1,
                15,21,30,
                22,21,31,
                5,-1,1,0,
                21,21,30,
                20,21,35,
                3,-1,0,1,
                20,21,30,
                22,21,20,
                3,4,1,0*//*

        };

        int j = 1;
        for( int i=0; i<input.length; i++,j++ )
        {
            Calendar eventTimeCal = Calendar.getInstance( TimeZone.getTimeZone("GMT") );
            eventTimeCal.set( year, month, input[i++], input[i++], input[i++], input[i++] );
            java.util.Date eventDateTime = eventTimeCal.getTime();
            IdcDateTime eventTime = DateTimeFactory.newDateTime( eventDateTime );
            log( "*****************************event time in Turkey is " + eventTime.getDate() + "*****************************" );
            log( "*****************************event time Timezone is " + eventTime.getTimeZone() + "*****************************" );
            log( "*****************************event time Time is " + eventTime.getTime() + "*****************************" );

            Calendar currentTimeCal = Calendar.getInstance( TimeZone.getTimeZone("GMT") );
            currentTimeCal.set( year, month, input[i++], input[i++], input[i++] );
            java.util.Date currentDateTime = currentTimeCal.getTime();
            IdcDateTime currentTime = DateTimeFactory.newDateTime(currentDateTime);
            log( "*****************************current time in Turkey is " + currentTime.getDate() + "*****************************" );
            log( "*****************************current time Timezone is " + currentTime.getTimeZone() + "*****************************" );
            log( "*****************************current time Time is " + currentTime.getTime() + "*****************************" );

            sep.setEventTime( eventTime );
            sep.setTimeZone( "Turkey" );

            // setting DaysOfWeekBitSet in ExecutionDays
            DaysOfWeekBitSet dayOfWeekBitSet = new DaysOfWeekBitSetC();
            int k = 0;
            while( k < 2 )
            {
                switch( input[i++] )
                {
                    case 0 : dayOfWeekBitSet.setMonday( true );
                             break;
                    case 1 : dayOfWeekBitSet.setTuesday( true );
                             break;
                    case 2 : dayOfWeekBitSet.setWednesday( true );
                             break;
                    case 3 : dayOfWeekBitSet.setThursday( true );
                             break;
                    case 4 : dayOfWeekBitSet.setFriday( true );
                             break;
                    case 5 : dayOfWeekBitSet.setSaturday( true );
                             break;
                    case 6 : dayOfWeekBitSet.setSunday( true );
                             break;
                    default : break;
                }
                ++k;
            }
            sep.setExecutionDays( dayOfWeekBitSet );

            // Create ScheduleEvent
            ScheduleEventC event = new ScheduleEventC();
            event.setScheduleEventParameters( sep );
            event.setTransient( false );

            // create workflow message
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setProperty( ScheduleEventC.CURRENT_TIME, currentTime );
            msg.setProperty( ScheduleEventC.OLD_EVENT_TIME_GMT, sep.getEventTime() );

            // call weeklySchedulerCheck( wfmsg )
            event.weeklySchedulerCheck( msg );
            Boolean executeNow = input[i++] == 0 ? Boolean.FALSE : Boolean.TRUE;
            Boolean executionOff = input[i] == 0 ? Boolean.FALSE : Boolean.TRUE;
            assertNotNull( msg.getProperty( ScheduleEventC.EXECUTE_NOW ) );
            assertNotNull( msg.getProperty( ScheduleEventC.EXECUTION_OFF ) );
            assertEquals( executeNow, (Boolean)msg.getProperty( ScheduleEventC.EXECUTE_NOW ) );
            assertEquals( executionOff, (Boolean)msg.getProperty( ScheduleEventC.EXECUTION_OFF ) );
            if( ( i + 1 ) % 12 != 0)
            {
                log("%%%%%%%%%%%%%%%%%testweeklySchedulerCheck: i is " + i + " : fault in index increment. Abort!%%%%%%%%%%%%%%%%%");
                break;
            }
            log("**********************TestCase #" + j + "Passed!");
        }
    }

    private static int getDayOfWeek( IdcDateTime latestET, String timeZone )
    {
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTimeInMillis( latestET.asJdkDate().getTime() );
        rightNow.setTimeZone( TimeZone.getTimeZone( timeZone ) );
        return rightNow.get( Calendar.DAY_OF_WEEK );
    }*/

}
