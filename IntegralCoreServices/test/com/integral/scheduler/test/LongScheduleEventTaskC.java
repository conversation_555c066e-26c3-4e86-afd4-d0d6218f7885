package com.integral.scheduler.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.util.Date;

/**
 * Sample schedule event task to test periodic events that take a long
 * time to execute.
 */
public class LongScheduleEventTaskC
{
    protected Log log = LogFactory.getLog( this.getClass() );

    public LongScheduleEventTaskC()
    {

    }

    public void execute()
    {
        Date start = new Date();
        log.info( "LongScheduleEventTaskC: start execute at " + start );

        try
        {
            Thread.sleep( 5 );
        }
        catch ( Exception e )
        {
            //
        }

        Date end = new Date();
        log.info( "LongScheduleEventTaskC: end   execute at " + end );
    }

}
