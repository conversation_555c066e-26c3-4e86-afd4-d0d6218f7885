package com.integral.scheduler.test;

import java.util.Calendar;
import java.util.TimeZone;

public class ScheduleEventTestHelperC
{
    // Get first Sunday of November of the following year.
    // Following year is used so that date will is always in the future.
    public static Calendar getCalendarGainHour()
    {
        Calendar c = Calendar.getInstance( TimeZone.getTimeZone("America/New_York" ) );
        int year = c.get( Calendar.YEAR ) + 1; 
        c.set( Calendar.DAY_OF_MONTH, 1 );
        c.set( Calendar.MONTH, Calendar.NOVEMBER );
        c.set( Calendar.YEAR, year );
        c.set( Calendar.HOUR_OF_DAY, 0 );
        c.set( Calendar.MINUTE, 0 );
        c.set( Calendar.SECOND, 0 );
        int day = c.get( Calendar.DAY_OF_WEEK ) == Calendar.SUNDAY ? 1 : 9 - c.get( Calendar.DAY_OF_WEEK ) ;
        c.set( Calendar.DAY_OF_MONTH, day );
        return c;
    }
    
    // Get second Sunday of March of the following year.
    // Following year is used so that date will is always in the future.
    public static Calendar getCalendarSkipHour()
    {
        Calendar c = Calendar.getInstance( TimeZone.getTimeZone("America/New_York" ) );
        int year = c.get( Calendar.YEAR ) + 1;
        c.set( Calendar.DAY_OF_MONTH, 1 );
        c.set( Calendar.MONTH, Calendar.MARCH );
        c.set( Calendar.YEAR, year );
        c.set( Calendar.HOUR_OF_DAY, 0 );
        c.set( Calendar.MINUTE, 0 );
        c.set( Calendar.SECOND, 0 );
        int day = c.get( Calendar.DAY_OF_WEEK ) == Calendar.SUNDAY ? 1 : 16 - c.get( Calendar.DAY_OF_WEEK ) ;
        c.set( Calendar.DAY_OF_MONTH, day );
        return c;
    }

}
