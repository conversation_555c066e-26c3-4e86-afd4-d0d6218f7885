package com.integral.scheduler.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.scheduler.ScheduleFunctorC;

public class MockScheduleFunctorC extends ScheduleFunctorC
{
    private static int executionCount;
    protected static Log log = LogFactory.getLog( MockScheduleFunctorC.class );

    public MockScheduleFunctorC()
    {

    }

    public void execute( WorkflowMessage wm )
    {
        ++executionCount;
        log.info( "Invoked test schedule functor. execution count=" + executionCount );
    }

    public String getDescription()
    {
        return "Test";
    }

    public static int getExecutionCount()
    {
        return executionCount;
    }

    public static void resetExecutionCount()
    {
        executionCount = 0;
    }
}
