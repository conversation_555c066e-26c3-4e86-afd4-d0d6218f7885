package com.integral.scheduler.test;

import java.util.Calendar;
import java.util.TimeZone;

import com.integral.scheduler.ScheduleEventParameters;
import com.integral.scheduler.ScheduleFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDateTime;

import junit.framework.TestCase;

public class ScheduleEventParametersTestC extends TestCase
{
    public ScheduleEventParametersTestC( String name )
    {
        super( name );
    }
    
    public void testDaylightSavingGainHour()
    {
        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        // Set period to 1 day.
        sep.setPeriod( 24 * 60 * 60 );
        sep.setTimeZone( "America/New_York" );
        
        // Create DateTime for first Sunday of November 00:00:00 in America/New_York timezone.
        Calendar c = ScheduleEventTestHelperC.getCalendarGainHour();
        IdcDateTime currentEventTime = DateTimeFactory.newDateTime( c.getTime(), TimeZone.getTimeZone( "America/New_York" ) );

        sep.setEventTime( currentEventTime );
        IdcDateTime nextEventTime = sep.getNextEventTime();
        
        // Verify 25 hour difference between the two events.
        long t1 = currentEventTime.asJdkDate().getTime();
        long t2 = nextEventTime.asJdkDate().getTime();
        assertTrue( t2 - t1 == 25 * 60 * 60 * 1000 );
        
        // Verify hour is same for the two events.
        assertTrue( currentEventTime.getHours() == nextEventTime.getHours() );
    }
    
    public void testDaylightSavingSkipHour()
    {
        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        // Set period to 1 day.
        sep.setPeriod( 24 * 60 * 60 );
        sep.setTimeZone( "America/New_York" );
        
        // Create DateTime for second Sunday of March 00:00:00 in America/New_York timezone.
        Calendar c = ScheduleEventTestHelperC.getCalendarSkipHour();
        IdcDateTime currentEventTime = DateTimeFactory.newDateTime( c.getTime(), TimeZone.getTimeZone( "America/New_York" ) );

        sep.setEventTime( currentEventTime );
        IdcDateTime nextEventTime = sep.getNextEventTime();
        
        // Verify 25 hour difference between the two events.
        long t1 = currentEventTime.asJdkDate().getTime();
        long t2 = nextEventTime.asJdkDate().getTime();
        assertTrue( ( t2 - t1 == 24 * 60 * 60 * 1000 ) || ( t2 - t1 == 23 * 60 * 60 * 1000 ));
        
        // Verify hour is same for the two events.
        assertTrue( currentEventTime.getHours() == nextEventTime.getHours() );
    }

    public void testDaylightSavingGainHour2()
    {
        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        // Set period to 3 hour.
        sep.setPeriod( 3 * 60 * 60 );
        sep.setTimeZone( "America/New_York" );
        
        // Create DateTime for first Sunday of November 00:00:00 in America/New_York timezone.
        Calendar c = ScheduleEventTestHelperC.getCalendarGainHour();
        IdcDateTime currentEventTime = DateTimeFactory.newDateTime( c.getTime(), TimeZone.getTimeZone( "America/New_York" ) );

        sep.setEventTime( currentEventTime );
        IdcDateTime nextEventTime = sep.getNextEventTime();
        
        // Verify 4 hour GMT difference between the two events.
        long t1 = currentEventTime.asJdkDate().getTime();
        long t2 = nextEventTime.asJdkDate().getTime();
        assertTrue( t2 - t1 == 4 * 60 * 60 * 1000 );
        
        // Verify 3 hour local time difference.
        assertTrue( currentEventTime.getHours() + 3 == nextEventTime.getHours() );
    }
    
    public void testDaylightSavingSkipHour2()
    {
        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        // Set period to 3 hours.
        sep.setPeriod( 3 * 60 * 60 );
        sep.setTimeZone( "America/New_York" );
        
        // Create DateTime for second Sunday of March 00:00:00 in America/New_York timezone.
        Calendar c = ScheduleEventTestHelperC.getCalendarSkipHour();
        IdcDateTime currentEventTime = DateTimeFactory.newDateTime( c.getTime(), TimeZone.getTimeZone( "America/New_York" ) );

        sep.setEventTime( currentEventTime );
        IdcDateTime nextEventTime = sep.getNextEventTime();
        
        // Verify 2 hour GMT difference between the two events.
        long t1 = currentEventTime.asJdkDate().getTime();
        long t2 = nextEventTime.asJdkDate().getTime();
        assertTrue( ( t2 - t1 == 3 * 60 * 60 * 1000 ) || ( t2 - t1 == 2 * 60 * 60 * 1000 ) );
        
        // Verify 3 hour difference between the two events.
        assertTrue( ( currentEventTime.getHours() + 3 == nextEventTime.getHours() ) || ( currentEventTime.getHours() + 2 == nextEventTime.getHours() ) );
    }

}