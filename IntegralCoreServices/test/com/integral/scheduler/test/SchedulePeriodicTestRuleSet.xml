<?xml version = "1.0" encoding = "UTF-8"?>
<!-- <!DOCTYPE ruleSetDefinition SYSTEM "file:///C:/integral.dtd"> -->
<ruleSetDefinition matchFirst="TRUE">
    <shortName>SchedulePeriodicTestRuleSet</shortName>
    <longName>SchedulePeriodicTestRuleSet</longName>
    <description>Periodic Scheduler Test Rule Set</description>
    <rule>
        <shortName>testLongTask</shortName>

        <!--
                  invoke task
           -->
        <methodAction sortOrder="10">
            <methodName>execute</methodName>
            <receiverObject valueClass="com.integral.scheduler.test.LongScheduleEventTaskC">new</receiverObject>
        </methodAction>


    </rule>
</ruleSetDefinition>