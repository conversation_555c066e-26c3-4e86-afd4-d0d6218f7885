package com.integral.scheduler.test;

import com.integral.audit.AuditEvent;
import com.integral.facade.FacadeFactory;
import com.integral.log.rule.LogAction;
import com.integral.log.rule.LogActionC;
import com.integral.rule.Rule;
import com.integral.rule.RuleC;
import com.integral.rule.RuleSet;
import com.integral.rule.RuleSetC;
import com.integral.scheduler.Expiration;
import com.integral.scheduler.ExpirationC;
import com.integral.scheduler.ScheduleEvent;
import com.integral.scheduler.ScheduleEventParameters;
import com.integral.scheduler.ScheduleFactory;
import com.integral.scheduler.Scheduler;
import com.integral.scheduler.audit.SchedulerAuditEventFacade;
import com.integral.scheduler.audit.SchedulerAuditEventFacadeC;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Locale;
import java.util.TimeZone;
import java.util.Vector;

public class SchedulerTestC
        extends PTestCaseC
{
    static String name = "Scheduler Test";
    Vector users = null;

    public SchedulerTestC( String name )
    {
        super( name );
    }

    /**
     * Tests a transient periodic event
     */
    public void testTransientSingleEvent()
    {
        log( "Transient single test" );

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "Send" );
        // Set the event time to 20 secs from now
        sep.setEventTime( 20 );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        // wait for 10 seconds before scheduling
        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        Scheduler.getInstance().schedule( se );
        log( "done scheduling the transient event..." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "exiting" );
    }

    /**
     * Tests a transient periodic event
     */
    public void testTransientPeriodicEvent()
    {
        log( "Transient test" );

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "Send" );
        // Set the event time to 2 secs from now
        sep.setEventTime( 2 );
        sep.setPeriod( 2 );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        Scheduler.getInstance().schedule( se );
        log( "done scheduling the transient event..." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "exiting" );
    }

    public void testSimpleEvent()
    {
        ScheduleEvent se1 = null;
        try
        {
            log( "Simple event test" );
            se1 = insertEvent( 0 );
            if ( null == se1 )
            {
                return;
            }
            ScheduleFactory.getScheduler( "Scheduler.Default" ).schedule( se1 );

            log( "done scheduling the event..." );

            Thread.sleep( 5000 );
            log( "Cancelling scheduled request..." );
            ScheduleFactory.getScheduler( "Scheduler.Default" ).cancel( se1 );

            Thread.sleep( 5000 );

            log( "exiting" );
        }
        catch ( Exception e )
        {
            fail( "testSimpleEvent", e );
        }
        finally
        {
            if ( se1 != null )
            {
                Scheduler.getInstance().delete( se1 );
            }
        }
    }

    private void insertEventForPerformance( int numOfEvents, int triggerTimeInSecs, int period )
    {
        log( "Creating new event..." );
        try
        {
            for ( int i = 0; i < numOfEvents; i++ )
            {
                //if uow is outside the loop, events don't get inserted in proper order
                UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
                ScheduleEvent se = ScheduleFactory.newScheduleEvent();
                se = ( ScheduleEvent ) uow.registerObject( se );

                ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
                RuleSet rs = getRuleSet();
                sep.setRuleSet( ( RuleSet ) uow.registerObject( rs ) );
                sep.setEventName( "Send_" + System.currentTimeMillis () + "_" +  i );
                sep.setEventTime( triggerTimeInSecs );
                if ( period > 0 )
                {
                    sep.setPeriod( period );
                }

                se.setScheduleEventParameters( sep );
                se.setScheduleEventOwner( rs );
                uow.commit();
                Scheduler.getInstance().schedule( se );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }

    public void testPerformanceSimpleEvent()
    {
        log( "Performance Simple event test" );
        insertEventForPerformance( 300, 20, 0 );

        log( "done scheduling the events..." );

        //log("Cancelling scheduled request...");
        //Scheduler.getInstance().cancel(se1);

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "exiting" );

    }

    public void testPeriodicEvent()
    {
        ScheduleEvent se1 = null;
        try
        {
            log( "Periodic event test" );
            se1 = insertEvent( 3 );
            Scheduler.getInstance().schedule( se1 );

            log( "done scheduling the event..." );

            Thread.sleep( 5000 );

            log( "Cancelling scheduled request..." );
            Scheduler.getInstance().cancel( se1 );

            Thread.sleep( 5000 );
            log( "exiting" );
        }
        catch ( Exception e )
        {
            fail( "testPeriodicEvent", e );
        }
        finally
        {
            if ( se1 != null )
            {
                Scheduler.getInstance().delete( se1 );
            }
        }
    }


    private ScheduleEvent insertEvent( int period )
    {
        log( "Creating new event..." );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ScheduleEvent se = ScheduleFactory.newScheduleEvent();
            se = ( ScheduleEvent ) uow.registerObject( se );

            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
            RuleSet rs = getRuleSet();
            sep.setRuleSet( ( RuleSet ) uow.registerObject( rs ) );
            sep.setEventName( "Send_" + System.currentTimeMillis () );
            sep.setEventTime( 2 ); //in 2 secs
            if ( period > 0 )
            {
                sep.setPeriod( period );
            }

            se.setScheduleEventParameters( sep );
            se.setScheduleEventOwner( rs );
            uow.commit();

            log( "ScheduleEvent objectID = " + se.getObjectID() );
            return se;
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
        return null;
    }

    public ScheduleEvent testPersistentEvent()
    {
        log( "Creating new event..." );
        ScheduleEvent se = null;
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            se = ScheduleFactory.newScheduleEvent();
            se = ( ScheduleEvent ) uow.registerNewObject( se );

            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
            RuleSet rs = getRuleSet();
            sep.setRuleSet( ( RuleSet ) uow.registerObject( rs ) );
            sep.setEventName( "Send" );
            sep.setEventTime( 2 );
            sep.setPeriod( 60 );

            se.setScheduleEventParameters( sep );
            se.setScheduleEventOwner( rs );

            uow.commit();

            log( "ScheduleEvent objectID = " + se.getObjectID() );

            log( "insertTest" );

            return se;
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
        finally
        {
            if ( se != null )
            {
                Scheduler.getInstance().delete( se );
            }
        }
        return null;
    }

    private RuleSet getRuleSet()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Test" );
            Object lookedupObject = getPersistenceSession().readObject( RuleSet.class, expr );

            log( "Found rule set - Test" );
            return ( RuleSet ) lookedupObject;
        }
        catch ( Exception e )
        {
            log( "Cannot find rule set - Test" );
        }
        return null;
    }


    /**
     * Sets the default system locale and timezone
     *
     * @return locale
     */
    public static String setDefaultLocale()
    {
        String localeLanguage = "en";
        String localeCountry = "US";
        String localeInfoString;

        try
        {
            TimeZone tm = TimeZone.getTimeZone( "GMT" );
            TimeZone.setDefault( tm );

            Locale.setDefault( new Locale( localeLanguage, localeCountry ) );
            localeInfoString = " System locale set to: " + Locale.getDefault().toString();
        }
        catch ( Exception e )
        {
            localeInfoString = " Error setting system locale set to: " + localeLanguage + '/' + localeCountry + '\n';
            localeInfoString += " Exception message: " + e + '\n';
        }

        return localeInfoString;
    }

    static final String PRINTRULESET = "SchedulerTestPrintRuleSet1";

    private RuleSet getPrintRuleSet()
    {
        RuleSet rs = null;
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( PRINTRULESET );
            rs = ( RuleSet ) getPersistenceSession().readObject( RuleSet.class, expr );
            log( "Found rule set - SchedulerTestPrintRuleSet" );

        }
        catch ( Exception e )
        {
            log( "Cannot find rule set - Test" );
        }

        if ( rs == null )
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            LogAction logAction = new LogActionC();
            logAction.setMessage( "LogAction Test Message" );
            logAction.setLevel( LogAction.INFO );

            Rule printRule = new RuleC();
            printRule.setShortName( "SchedulerTestPrintRule" );
            printRule.addAction( logAction );

            rs = new RuleSetC();
            rs.setShortName( PRINTRULESET );
            rs.addRule( printRule );

            uow.registerObject( rs );

            uow.commit();

            try
            {
                ExpressionBuilder eb = new ExpressionBuilder();
                Expression expr = eb.get( "shortName" ).equal( PRINTRULESET );
                rs = ( RuleSet ) getPersistenceSession().readObject( RuleSet.class, expr );
                log( "Found rule set - SchedulerTestPrintRuleSet" );

            }
            catch ( Exception e )
            {
                log( "Cannot find rule set - Test" );
            }
        }

        return rs;
    }

    public void testExpireWithTransientRequest()
    {
        ScheduleEvent se = null;
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            se = ScheduleFactory.newScheduleEvent();
            se = ( ScheduleEvent ) uow.registerNewObject( se );

            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();

            RuleSet rs = getPrintRuleSet();
            rs = ( RuleSet ) uow.registerObject( rs );
            // CacheFactory.getCacheManager().put(rs);

            sep.setRuleSet( rs );
            sep.setEventName( "Send" );
            sep.setEventTime( 3 );
            // sep.setPeriod(60);

            se.setScheduleEventParameters( sep );
            se.setScheduleEventOwner( rs );

            uow.commit();
            log( "ScheduleEvent objectID = " + se.getObjectID() );

            Scheduler.getInstance().schedule( se );

            log( "starting to wait 10 seconds..." );
            try
            {
                Thread.sleep( 5000 );
            }
            catch ( Exception e )
            {
            }
            log( "end of waiting 10 seconds" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testExpireWithTransientRequest" );
        }
        finally
        {
            if ( se != null )
            {
                Scheduler.getInstance().delete( se );
            }
        }
    }

    /**
     * Tests a expiration event
     */
    public void testExpiry()
    {
        log( "Expiry test" );

        int time = 20;

        Expiration expiration = new ExpirationC();
        expiration.setSeconds( time );

        ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
        sep.setRuleSet( getRuleSet() );
        sep.setEventName( "Send" );
        // Set the event time to 20 secs from now
        sep.setEventTime( time );

        ScheduleEvent se = ScheduleFactory.newScheduleEvent();
        se.setScheduleEventParameters( sep );
        se.setTransient( true );

        expiration.setScheduleEvent( se );

        // wait for 10 seconds before scheduling
        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        expiration.start();
        log( "done scheduling the expiration..." );

        try
        {
            Thread.sleep( 5000 );
        }
        catch ( Exception e )
        {
        }

        log( "exiting" );
    }

    /**
     * Tests a transient periodic event
     */
    public void testTransientPeriodicFunctorEvent()
    {
        ScheduleEvent se = null;
        try
        {
            log( "testTransientPeriodicFunctorEvent" );
            User usr = UserFactory.getUser( "Integral@MAIN" );
            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
            sep.setEventName( "TestEvent" );
            // Set the event time to 20 secs from now
            sep.setEventTime( 1 );
            sep.setPeriod( 3 );
            MockScheduleFunctorC.resetExecutionCount();
            sep.setScheduleFunctor( MockScheduleFunctorC.class );

            se = ScheduleFactory.newScheduleEvent();
            se.setScheduleEventParameters( sep );
            se.setTransient( true );
            se.setUser( usr );

            Scheduler.getInstance().schedule( se );

            Thread.sleep( 5000 );
            //check the execution count
            assertEquals( 2, MockScheduleFunctorC.getExecutionCount() );

            Thread.sleep( 3000 );
            // check the execution count again after 3 seconds
            assertEquals( 3, MockScheduleFunctorC.getExecutionCount() );
        }
        catch ( Exception e )
        {
            fail( "testTransientPeriodicFunctorEvent", e );
        }
        finally
        {
            if ( se != null )
            {
                Scheduler.getInstance().cancel( se );
            }
        }
    }

    public void testTransientPeriodicFunctorEventReschedule()
    {
        ScheduleEvent se = null;
        try
        {
            log( "testTransientPeriodicFunctorEventReschedule" );
            User usr = UserFactory.getUser( "Integral@MAIN" );
            ScheduleEventParameters sep = ScheduleFactory.newScheduleEventParameters();
            sep.setEventName( "TestEvent" );
            // Set the event time to 5 secs from now
            sep.setEventTime( 10 );
            sep.setPeriod( 5 );
            MockScheduleFunctorC.resetExecutionCount();
            sep.setScheduleFunctor( MockScheduleFunctorC.class );

            se = ScheduleFactory.newScheduleEvent();
            se.setScheduleEventParameters( sep );
            se.setTransient( true );
            se.setUser( usr );

            Scheduler.getInstance().schedule( se );
            Scheduler.getInstance().cancel( se );

            Thread.sleep( 7000 );
            //check the execution count
            assertEquals( 0, MockScheduleFunctorC.getExecutionCount() );

            Scheduler.getInstance().schedule( se );
            Thread.sleep( 5000 );
            // check the execution count again
            log( "executionCount=" + MockScheduleFunctorC.getExecutionCount() );
            assertTrue( MockScheduleFunctorC.getExecutionCount() > 0 );
        }
        catch ( Exception e )
        {
            fail( "testTransientPeriodicFunctorEventReschedule", e );
        }
        finally
        {
            if ( se != null )
            {
                Scheduler.getInstance().cancel( se );
            }
        }
    }

    protected void setUp() throws Exception
    {
        super.setUp();
        FacadeFactory.setFacade( SchedulerAuditEventFacade.FACADE_NAME, AuditEvent.class, SchedulerAuditEventFacadeC.class );
    }
}
