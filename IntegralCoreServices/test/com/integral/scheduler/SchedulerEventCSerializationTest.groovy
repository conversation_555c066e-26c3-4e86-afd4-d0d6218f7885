package com.integral.scheduler

import com.integral.message.WorkflowMessage
import com.integral.rds.client.ClientFactory
import com.integral.rds.client.ReferenceDataServiceC
import com.integral.rds.mock.SetupStub
import com.integral.rds.setup.TestServerStartup
import com.integral.testframework.api.API
import com.integral.testframework.api.FI
import com.integral.testframework.api.LE
import com.integral.testframework.api.ORG
import com.integral.testframework.api.USER

/**
 * Created by shahr on 3/30/2016.
 */
class SchedulerEventCSerializationTest extends GroovyTestCase{

    def ORG MAIN
    def FI FI1
    def LE FI1LE1
    def USER FI1USER1
    def USER FI1USER2

    public void setUp(){
        API.setupInMemoryTestInfrastructure();
        TestServerStartup.startRDSServer(SetupStub.getConfig())
        setUpEntities()
    }

    @Override
    protected void tearDown() throws Exception {
        TestServerStartup.shutDownRDSServer();
    }

    public void setUpEntities(){
        MAIN = API.createORG "MAIN"

        FI1 = API.createFI "FI1"
        FI1LE1 = FI1.legalEntity "FI1LE1"
        FI1LE1.defaultDealingEntity true
        FI1USER1 = FI1.user "FI1USER1"
        FI1USER2 = FI1.user "FI1USER2"
    }

    public void testSerialization(){
        ScheduleEvent scheduleEvent = new ScheduleEventC()
        scheduleEvent.setNamespace(MAIN.org.getNamespace())
        scheduleEvent.setScheduleEventOwner(FI1USER1.user)
        scheduleEvent.setExecuteNowUser(FI1USER2.user)

        ScheduleEventParameters scheduleEventParameters = new ScheduleEventParametersC()
        scheduleEventParameters.setScheduleFunctor(ScheduleFunctor.class)
        scheduleEventParameters.setScheduleFunctorDetails("TestDetails")
        scheduleEvent.setScheduleEventParameters(scheduleEventParameters)

        ScheduleEvent insertedEvent = ClientFactory.getFactory().getReferenceDataService().create(scheduleEvent)

        println insertedEvent

    }

}
