package com.integral.audit.test;

import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.audit.AuditFactory;
import com.integral.audit.AuditService;
import com.integral.audit.AuditServiceFactory;
import com.integral.audit.config.AuditLogFileMBean;
import com.integral.audit.configuration.AuditConfiguration;
import com.integral.audit.configuration.AuditConfigurationFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;

/**
 * <AUTHOR> Development Corporation.
 */
public class AuditServicePTestC extends PTestCaseC
{
    AuditConfiguration auditConfig = ( AuditConfiguration ) AuditConfigurationFactory.getAuditConfigurationMBean();
    AuditLogFileMBean auditFileMbean = ( AuditLogFileMBean ) AuditFactory.getAuditLogFileMBean();

    public AuditServicePTestC( String aName )
    {
        super( aName );
    }

    public void testDbAuditService()
    {
        try
        {
            AuditEvent ae = createAuditEvent();
            AuditService audSvc = AuditServiceFactory.getDbAuditService();
            audSvc.audit( ae );
            if ( auditConfig.isDatabaseCommitEnabled() )
            {
                ae = ( AuditEvent ) PersistenceFactory.newSession().refreshObject( ae );
                log( "Audit Event after persisting=" + ae );
                assertNotNull( ae );
            }
        }
        catch ( Exception e )
        {
            fail( "testDbAuditService", e );
        }
    }

    public void testFileAuditService()
    {
        try
        {
            AuditEvent ae = createAuditEvent();
            AuditService audSvc = AuditServiceFactory.getFileAuditService();
            audSvc.audit( ae );
            String fileName = auditFileMbean.getFileName();
            FileInputStream is = new FileInputStream( fileName );
            InputStreamReader isr = new InputStreamReader( is );
            BufferedReader br = new BufferedReader( isr );

            boolean fileEntryPresent = false;
            String line = br.readLine();
            while ( line != null )
            {
                log( "Parsing CSV line <" + line + '>' );
                fileEntryPresent = true;
                break;
            }

            br.close();
            isr.close();

            assertTrue( fileEntryPresent );
        }
        catch ( Exception e )
        {
            fail( "testFileAuditService", e );
        }
    }

    private AuditEvent createAuditEvent()
    {
        AuditEvent event = new AuditEventC();
        event.setStatus( 'T' );
        event.setComponent( "TEST" );
        event.setAction( "UNIT TEST" );
        return event;
    }
}
