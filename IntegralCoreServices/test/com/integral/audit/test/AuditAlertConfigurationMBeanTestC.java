package com.integral.audit.test;

// Copyright (c) 2018 Integral Development Corporation.  All Rights Reserved.

import com.integral.audit.configuration.AuditAlertConfiguration;
import com.integral.audit.configuration.AuditAlertConfigurationFactory;
import com.integral.audit.configuration.AuditAlertConfigurationMBean;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;

import java.util.Collection;

/**
 * <AUTHOR> Development Corporation.
 */
public class AuditAlertConfigurationMBeanTestC extends MBeanTestCaseC
{
    AuditAlertConfiguration auditAlertConfig = ( AuditAlertConfiguration ) AuditAlertConfigurationFactory.getAuditAlertConfigurationMBean();

    public AuditAlertConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( auditAlertConfig, "auditAlertEmailNotificationInterval", AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_NOTIFICATION_INTERVAL, MBeanTestCaseC.LONG );
    }

    public void testAuditAlertEnabled()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            assertFalse( auditAlertConfig.isAuditAlertEnabled(null));
            assertFalse(auditAlertConfig.isAuditAlertEnabled(org1));
            assertFalse(auditAlertConfig.isAuditAlertEnabled(org2));

            // now set the global property
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( auditAlertConfig.isAuditAlertEnabled(org1) );
            assertTrue( auditAlertConfig.isAuditAlertEnabled(org2) );
            assertTrue( auditAlertConfig.isAuditAlertEnabled( null ) );

            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( auditAlertConfig.isAuditAlertEnabled(org1) );
            assertFalse( auditAlertConfig.isAuditAlertEnabled(org2) );
            assertFalse( auditAlertConfig.isAuditAlertEnabled(null) );


            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( auditAlertConfig.isAuditAlertEnabled(org1) );
            assertFalse( auditAlertConfig.isAuditAlertEnabled(org2) );

            assertFalse ( auditAlertConfig.isAuditAlertEnabled( dd ) );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_ENABLED_PREFIX + lp.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( auditAlertConfig.isAuditAlertEnabled ( dd ) );
            dd.setBrokerOrganization ( null );
            assertFalse ( auditAlertConfig.isAuditAlertEnabled ( dd ) );
            dd.setRealLP ( lp );
            assertTrue ( auditAlertConfig.isAuditAlertEnabled( dd ) );
            dd.setRealLP ( null );
            assertFalse ( auditAlertConfig.isAuditAlertEnabled ( dd ) );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_ENABLED_PREFIX + dd.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( auditAlertConfig.isAuditAlertEnabled ( dd ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertEmailSender()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            String senderEmail = auditAlertConfig.getAuditAlertEmailSender ( null );
            assertNotNull ( senderEmail );
            assertEquals ( senderEmail, auditAlertConfig.getAuditAlertEmailSender ( org1 ) );
            assertEquals ( senderEmail, auditAlertConfig.getAuditAlertEmailSender ( org2 ) );

            // now set the global property
            String newSenderEmail = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SENDER, newSenderEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newSenderEmail, auditAlertConfig.getAuditAlertEmailSender ( org1 ) );
            assertEquals ( newSenderEmail, auditAlertConfig.getAuditAlertEmailSender ( org2 ) );
            assertEquals ( newSenderEmail, auditAlertConfig.getAuditAlertEmailSender ( null ) );

            String org1Email = "<EMAIL>";
            String org2Email = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SENDER_PREFIX + "FI1", org1Email, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SENDER_PREFIX + "FI2", org2Email, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Email, auditAlertConfig.getAuditAlertEmailSender ( org1 ) );
            assertEquals ( org2Email, auditAlertConfig.getAuditAlertEmailSender ( org2 ) );
            assertEquals ( newSenderEmail, auditAlertConfig.getAuditAlertEmailSender ( null ) );

            String lpEmail = "<EMAIL>";
            String ddEmail = "<EMAIL>";
            assertEquals ( newSenderEmail, auditAlertConfig.getAuditAlertEmailSender ( dd ) );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SENDER_PREFIX + lp.getShortName (), lpEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpEmail, auditAlertConfig.getAuditAlertEmailSender ( dd ) );
            dd.setBrokerOrganization ( null );
            assertEquals ( newSenderEmail, auditAlertConfig.getAuditAlertEmailSender ( dd ) );
            dd.setRealLP ( lp );
            assertEquals ( lpEmail, auditAlertConfig.getAuditAlertEmailSender ( dd ) );
            dd.setRealLP ( null );
            assertEquals ( newSenderEmail, auditAlertConfig.getAuditAlertEmailSender ( dd ) );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SENDER_PREFIX + dd.getShortName (), ddEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddEmail, auditAlertConfig.getAuditAlertEmailSender ( dd ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertEmailSender" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertEmailTo()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            Collection<String> toEmail = auditAlertConfig.getAuditAlertEmailTo ( null );
            assertNull ( toEmail );
            assertNull ( auditAlertConfig.getAuditAlertEmailTo ( org1 ) );
            assertNull ( auditAlertConfig.getAuditAlertEmailTo ( org2 ) );

            // now set the global property
            String newToEmail = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_TO, newToEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailTo ( org1 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailTo ( org2 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailTo ( null ).toArray ()[0] );

            String org1Email = "<EMAIL>";
            String org2Email = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_TO_PREFIX + "FI1", org1Email, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_TO_PREFIX + "FI2", org2Email, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Email, auditAlertConfig.getAuditAlertEmailTo ( org1 ).toArray ()[0] );
            assertEquals ( org2Email, auditAlertConfig.getAuditAlertEmailTo ( org2 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailTo ( null ).toArray ()[0] );

            String lpEmail = "<EMAIL>";
            String ddEmail = "<EMAIL>";
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailTo ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_TO_PREFIX + lp.getShortName (), lpEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpEmail, auditAlertConfig.getAuditAlertEmailTo ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailTo ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            assertEquals ( lpEmail, auditAlertConfig.getAuditAlertEmailTo ( dd ).toArray ()[0] );
            dd.setRealLP ( null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailTo ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_TO_PREFIX + dd.getShortName (), ddEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddEmail, auditAlertConfig.getAuditAlertEmailTo ( dd ).toArray ()[0] );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertEmailTo" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertEmailCC()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            Collection<String> toEmail = auditAlertConfig.getAuditAlertEmailCC ( null );
            assertNull ( toEmail );
            assertNull ( auditAlertConfig.getAuditAlertEmailCC ( org1 ) );
            assertNull ( auditAlertConfig.getAuditAlertEmailCC ( org2 ) );

            // now set the global property
            String newToEmail = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CC, newToEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailCC ( org1 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailCC ( org2 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailCC ( null ).toArray ()[0] );

            String org1Email = "<EMAIL>";
            String org2Email = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CC_PREFIX + "FI1", org1Email, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CC_PREFIX + "FI2", org2Email, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Email, auditAlertConfig.getAuditAlertEmailCC ( org1 ).toArray ()[0] );
            assertEquals ( org2Email, auditAlertConfig.getAuditAlertEmailCC ( org2 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailCC ( null ).toArray ()[0] );

            String lpEmail = "<EMAIL>";
            String ddEmail = "<EMAIL>";
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailCC ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CC_PREFIX + lp.getShortName (), lpEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpEmail, auditAlertConfig.getAuditAlertEmailCC ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailCC ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            assertEquals ( lpEmail, auditAlertConfig.getAuditAlertEmailCC ( dd ).toArray ()[0] );
            dd.setRealLP ( null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailCC ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CC_PREFIX + dd.getShortName (), ddEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddEmail, auditAlertConfig.getAuditAlertEmailCC ( dd ).toArray ()[0] );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertEmailCC" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertEmailBCC()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            Collection<String> toEmail = auditAlertConfig.getAuditAlertEmailBCC ( null );
            assertNull ( toEmail );
            assertNull ( auditAlertConfig.getAuditAlertEmailBCC ( org1 ) );
            assertNull ( auditAlertConfig.getAuditAlertEmailBCC ( org2 ) );

            // now set the global property
            String newToEmail = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_BCC, newToEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailBCC ( org1 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailBCC ( org2 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailBCC ( null ).toArray ()[0] );

            String org1Email = "<EMAIL>";
            String org2Email = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_BCC_PREFIX + "FI1", org1Email, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_BCC_PREFIX + "FI2", org2Email, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Email, auditAlertConfig.getAuditAlertEmailBCC ( org1 ).toArray ()[0] );
            assertEquals ( org2Email, auditAlertConfig.getAuditAlertEmailBCC ( org2 ).toArray ()[0] );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailBCC ( null ).toArray ()[0] );

            String lpEmail = "<EMAIL>";
            String ddEmail = "<EMAIL>";
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailBCC ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_BCC_PREFIX + lp.getShortName (), lpEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpEmail, auditAlertConfig.getAuditAlertEmailBCC ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailBCC ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            assertEquals ( lpEmail, auditAlertConfig.getAuditAlertEmailBCC ( dd ).toArray ()[0] );
            dd.setRealLP ( null );
            assertEquals ( newToEmail, auditAlertConfig.getAuditAlertEmailBCC ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_BCC_PREFIX + dd.getShortName (), ddEmail, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddEmail, auditAlertConfig.getAuditAlertEmailBCC ( dd ).toArray ()[0] );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertEmailBCC" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertEmailSubject()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            String defaultValue = auditAlertConfig.getAuditAlertEmailSubject ( null );
            assertNotNull ( defaultValue );
            assertEquals ( defaultValue, auditAlertConfig.getAuditAlertEmailSubject ( org1 ) );
            assertEquals ( defaultValue, auditAlertConfig.getAuditAlertEmailSubject ( org2 ) );

            // now set the global property
            String newValue = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SUBJECT, newValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailSubject ( org1 ) );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailSubject ( org2 ) );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailSubject ( null ) );

            String org1Value = "<EMAIL>";
            String org2Value = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SUBJECT_PREFIX + "FI1", org1Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SUBJECT_PREFIX + "FI2", org2Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Value, auditAlertConfig.getAuditAlertEmailSubject ( org1 ) );
            assertEquals ( org2Value, auditAlertConfig.getAuditAlertEmailSubject ( org2 ) );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailSubject ( null ) );

            String lpValue = "<EMAIL>";
            String ddValue = "<EMAIL>";
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailSubject ( dd ) );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SUBJECT_PREFIX + lp.getShortName (), lpValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertEmailSubject ( dd ) );
            dd.setBrokerOrganization ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailSubject ( dd ) );
            dd.setRealLP ( lp );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertEmailSubject ( dd ) );
            dd.setRealLP ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailSubject ( dd ) );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_SUBJECT_PREFIX + dd.getShortName (), ddValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddValue, auditAlertConfig.getAuditAlertEmailSubject ( dd ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertEmailSubject" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertEmailContent()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            String defaultValue = auditAlertConfig.getAuditAlertEmailContent ( null );
            assertNotNull ( defaultValue );
            assertEquals ( defaultValue, auditAlertConfig.getAuditAlertEmailContent ( org1 ) );
            assertEquals ( defaultValue, auditAlertConfig.getAuditAlertEmailContent ( org2 ) );

            // now set the global property
            String newValue = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT, newValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContent ( org1 ) );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContent ( org2 ) );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContent ( null ) );

            String org1Value = "<EMAIL>";
            String org2Value = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_PREFIX + "FI1", org1Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_PREFIX + "FI2", org2Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Value, auditAlertConfig.getAuditAlertEmailContent ( org1 ) );
            assertEquals ( org2Value, auditAlertConfig.getAuditAlertEmailContent ( org2 ) );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContent ( null ) );

            String lpValue = "<EMAIL>";
            String ddValue = "<EMAIL>";
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContent ( dd ) );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_PREFIX + lp.getShortName (), lpValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertEmailContent ( dd ) );
            dd.setBrokerOrganization ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContent ( dd ) );
            dd.setRealLP ( lp );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertEmailContent ( dd ) );
            dd.setRealLP ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContent ( dd ) );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_PREFIX + dd.getShortName (), ddValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddValue, auditAlertConfig.getAuditAlertEmailContent ( dd ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertEmailContent" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertEmailContentTableColumns()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            Collection<String> defaultValue = auditAlertConfig.getAuditAlertEmailContentTableColumns ( null );
            assertNotNull ( defaultValue );
            assertNotNull ( auditAlertConfig.getAuditAlertEmailContentTableColumns ( org1 ) );
            assertNotNull ( auditAlertConfig.getAuditAlertEmailContentTableColumns ( org2 ) );

            // now set the global property
            String newValue = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_TABLE_COLUMNS, newValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( org1 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( org2 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( null ).toArray ()[0] );

            String org1Value = "<EMAIL>";
            String org2Value = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_TABLE_COLUMNS_PREFIX + "FI1", org1Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_TABLE_COLUMNS_PREFIX + "FI2", org2Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Value, auditAlertConfig.getAuditAlertEmailContentTableColumns ( org1 ).toArray ()[0] );
            assertEquals ( org2Value, auditAlertConfig.getAuditAlertEmailContentTableColumns ( org2 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( null ).toArray ()[0] );

            String lpValue = "<EMAIL>";
            String ddValue = "<EMAIL>";
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_TABLE_COLUMNS_PREFIX + lp.getShortName (), lpValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( dd ).toArray ()[0] );
            dd.setRealLP ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_CONTENT_TABLE_COLUMNS_PREFIX + dd.getShortName (), ddValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddValue, auditAlertConfig.getAuditAlertEmailContentTableColumns ( dd ).toArray ()[0] );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertEmailContentTableColumns" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertExcludedComponents()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            Collection<String> defaultValue = auditAlertConfig.getAuditAlertExcludedComponents ( null );
            assertNotNull ( defaultValue );
            assertNotNull ( auditAlertConfig.getAuditAlertExcludedComponents ( org1 ) );
            assertNotNull ( auditAlertConfig.getAuditAlertExcludedComponents ( org2 ) );

            // now set the global property
            String newValue = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_COMPONENTS, newValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedComponents ( org1 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedComponents ( org2 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedComponents ( null ).toArray ()[0] );

            String org1Value = "<EMAIL>";
            String org2Value = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_COMPONENTS_PREFIX + "FI1", org1Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_COMPONENTS_PREFIX + "FI2", org2Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Value, auditAlertConfig.getAuditAlertExcludedComponents ( org1 ).toArray ()[0] );
            assertEquals ( org2Value, auditAlertConfig.getAuditAlertExcludedComponents ( org2 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedComponents ( null ).toArray ()[0] );

            String lpValue = "<EMAIL>";
            String ddValue = "<EMAIL>";
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedComponents ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_COMPONENTS_PREFIX + lp.getShortName (), lpValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertExcludedComponents ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedComponents ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertExcludedComponents ( dd ).toArray ()[0] );
            dd.setRealLP ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedComponents ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_COMPONENTS_PREFIX + dd.getShortName (), ddValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddValue, auditAlertConfig.getAuditAlertExcludedComponents ( dd ).toArray ()[0] );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertExcludedComponents" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertExcludedUserActions()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            Collection<String> defaultValue = auditAlertConfig.getAuditAlertExcludedUserActions ( null );
            assertNull ( defaultValue );
            assertNull ( auditAlertConfig.getAuditAlertExcludedUserActions ( org1 ) );
            assertNull ( auditAlertConfig.getAuditAlertExcludedUserActions ( org2 ) );

            // now set the global property
            String newValue = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USER_ACTIONS, newValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUserActions ( org1 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUserActions ( org2 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUserActions ( null ).toArray ()[0] );

            String org1Value = "<EMAIL>";
            String org2Value = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USER_ACTIONS_PREFIX + "FI1", org1Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USER_ACTIONS_PREFIX + "FI2", org2Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Value, auditAlertConfig.getAuditAlertExcludedUserActions ( org1 ).toArray ()[0] );
            assertEquals ( org2Value, auditAlertConfig.getAuditAlertExcludedUserActions ( org2 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUserActions ( null ).toArray ()[0] );

            String lpValue = "<EMAIL>";
            String ddValue = "<EMAIL>";
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUserActions ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USER_ACTIONS_PREFIX + lp.getShortName (), lpValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertExcludedUserActions ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUserActions ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertExcludedUserActions ( dd ).toArray ()[0] );
            dd.setRealLP ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUserActions ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USER_ACTIONS_PREFIX + dd.getShortName (), ddValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddValue, auditAlertConfig.getAuditAlertExcludedUserActions ( dd ).toArray ()[0] );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertExcludedUserActions" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertComponentExcludedUserActions()
    {
        try
        {
            Collection<String> defaultValue = auditAlertConfig.getAuditAlertComponentExcludedUserActions ( null );
            assertNull ( defaultValue );

            // now set the global property
            String newDefault = "UserActionDefault";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_COMPONENT_EXCLUDED_USER_ACTIONS, newDefault, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newDefault, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( null ).toArray ()[0] );

            String value1 = "UserAction1";
            String value2 = "UserAction2";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_COMPONENT_EXCLUDED_USER_ACTIONS_PREFIX + "IFSADMIN", value1, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_COMPONENT_EXCLUDED_USER_ACTIONS_PREFIX + "CREDITADMIN", value2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( value1, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( "IFSADMIN" ).toArray ()[0] );
            assertEquals ( value2, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( "CREDITADMIN" ).toArray ()[0] );
            assertEquals ( newDefault, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( null ).toArray ()[0] );
            assertEquals ( newDefault, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( "xxx" ).toArray ()[0] );

            String newValue1 = value1 + ",UserActionX";
            String newValue2 = value2 + ",UserActionY";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_COMPONENT_EXCLUDED_USER_ACTIONS_PREFIX + "IFSADMIN", newValue1, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_COMPONENT_EXCLUDED_USER_ACTIONS_PREFIX + "CREDITADMIN", newValue2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( value1, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( "IFSADMIN" ).toArray ()[0] );
            assertEquals ( value2, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( "CREDITADMIN" ).toArray ()[0] );
            assertEquals ( newDefault, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( null ).toArray ()[0] );
            assertEquals ( newDefault, auditAlertConfig.getAuditAlertComponentExcludedUserActions ( "xxx" ).toArray ()[0] );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertComponentExcludedUserActions" );
        }
    }

    public void testAuditAlertExcludedUsers()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            Collection<String> defaultValue = auditAlertConfig.getAuditAlertExcludedUsers ( null );
            assertNull ( defaultValue );
            assertNull ( auditAlertConfig.getAuditAlertExcludedUsers ( org1 ) );
            assertNull ( auditAlertConfig.getAuditAlertExcludedUsers ( org2 ) );

            // now set the global property
            String newValue = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USERS, newValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUsers ( org1 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUsers ( org2 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUsers ( null ).toArray ()[0] );

            String org1Value = "<EMAIL>";
            String org2Value = "<EMAIL>";
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USERS_PREFIX + "FI1", org1Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USERS_PREFIX + "FI2", org2Value, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( org1Value, auditAlertConfig.getAuditAlertExcludedUsers ( org1 ).toArray ()[0] );
            assertEquals ( org2Value, auditAlertConfig.getAuditAlertExcludedUsers ( org2 ).toArray ()[0] );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUsers ( null ).toArray ()[0] );

            String lpValue = "<EMAIL>";
            String ddValue = "<EMAIL>";
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUsers ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USERS_PREFIX + lp.getShortName (), lpValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertExcludedUsers ( dd ).toArray ()[0] );
            dd.setBrokerOrganization ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUsers ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            assertEquals ( lpValue, auditAlertConfig.getAuditAlertExcludedUsers ( dd ).toArray ()[0] );
            dd.setRealLP ( null );
            assertEquals ( newValue, auditAlertConfig.getAuditAlertExcludedUsers ( dd ).toArray ()[0] );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EXCLUDED_USERS_PREFIX + dd.getShortName (), ddValue, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ddValue, auditAlertConfig.getAuditAlertExcludedUsers ( dd ).toArray ()[0] );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertExcludedUsers" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }

    public void testAuditAlertPeriodicNotificationEnabled()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        Organization org2 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI2" );
        Organization dd = ReferenceDataCacheC.getInstance ().getOrganization ( "DD1" );
        Organization lp = ReferenceDataCacheC.getInstance ().getOrganization ( "CITI" );

        try
        {
            assertTrue( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled (null));
            assertTrue(auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(org1));
            assertTrue(auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(org2));

            // now set the global property
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_PERIODIC_NOTIFICATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(org1) );
            assertTrue( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(org2) );
            assertTrue( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled( null ) );

            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_PERIODIC_NOTIFICATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(org1) );
            assertFalse( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(org2) );
            assertFalse( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(null) );


            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_PERIODIC_NOTIFICATION_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_PERIODIC_NOTIFICATION_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(org1) );
            assertFalse( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled(org2) );

            assertFalse ( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled( dd ) );
            dd.setBrokerOrganization ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_PERIODIC_NOTIFICATION_ENABLED_PREFIX + lp.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled ( dd ) );
            dd.setBrokerOrganization ( null );
            assertFalse ( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled ( dd ) );
            dd.setRealLP ( lp );
            assertTrue ( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled( dd ) );
            dd.setRealLP ( null );
            assertFalse ( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled ( dd ) );
            dd.setRealLP ( lp );
            auditAlertConfig.setProperty( AuditAlertConfigurationMBean.AUDIT_ALERT_EMAIL_PERIODIC_NOTIFICATION_ENABLED_PREFIX + dd.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( auditAlertConfig.isAuditAlertEmailPeriodicNotificationEnabled ( dd ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAuditAlertPeriodicNotificationEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( dd );
            IdcUtilC.refreshObject ( lp );
        }
    }
}
