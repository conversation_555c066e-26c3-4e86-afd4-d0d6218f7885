package com.integral.audit.test;

import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.util.StopWatchC;
import org.eclipse.persistence.sessions.UnitOfWork;

class TestBulkAuditInsertC implements Runnable
{
    private UnitOfWork uow = null;
    private StopWatchC watch = null;
    private String name = null;
    private Object monitor;
    int NUM = 100;
    int num = 0;
    Log log = LogFactory.getLog( getClass() );

    public TestBulkAuditInsertC( int n, Object m )
    {
        num = n;
        monitor = m;
    }

    public void prepare()
    {
        try
        {
            name = "#" + num + ": PARALLEL BULK LOAD " + NUM + " items";
            uow = PersistenceFactory.newSession().acquireUnitOfWork();
            watch = LogFactory.getStopWatch( this.getClass(), name );

            long now = System.currentTimeMillis();

            for ( int i = 0; i < NUM; i++ )
            {
                AuditEvent event = new AuditEventC();
                event.setStatus( 'T' );
                event.setComponent( "TEST-" + now + '-' + i );
                event.setAction( "BULK TEST" );

                uow.registerObject( event );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void run()
    {
        try
        {
            synchronized ( monitor )
            {
                log.debug( name + " - waiting" );
                monitor.wait();
                log.debug( name + " - wait over" );
            }

            commit();
        }
        catch ( Exception e )
        {
            log.debug( name + " - wait error", e );
        }
    }

    public void commit()
    {
        watch.start();
        uow.commit();
        watch.stopAndLog();
    }
}
