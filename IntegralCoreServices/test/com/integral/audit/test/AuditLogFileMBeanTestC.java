package com.integral.audit.test;

import com.integral.audit.AuditFactory;
import com.integral.audit.config.AuditLogFile;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class AuditLogFileMBeanTestC extends MBeanTestCaseC
{
    AuditLogFile auditMBean = ( AuditLogFile ) AuditFactory.getAuditLogFileMBean();

    public AuditLogFileMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {

        testProperty( auditMBean, "fileName", "Audit.Log.FileName", MBeanTestCaseC.STRING, false, true );
        //todo: add method to support String[]
//        testProperty( auditMBean, "fieldsToBePersisted", "Audit.Log.FieldsToBePersisted", MBeanTestCaseC.STRING, false, true );
        testProperty( auditMBean, "dataDelimiter", "Audit.Log.Delimiter", MBeanTestCaseC.STRING, false, true );

    }
}
