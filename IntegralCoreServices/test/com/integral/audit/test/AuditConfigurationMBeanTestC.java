package com.integral.audit.test;

import com.integral.audit.configuration.AuditConfiguration;
import com.integral.audit.configuration.AuditConfigurationFactory;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class AuditConfigurationMBeanTestC extends MBeanTestCaseC
{
    AuditConfiguration auditConfig = ( AuditConfiguration ) AuditConfigurationFactory.getAuditConfigurationMBean();

    public AuditConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( auditConfig, "databaseCommitEnabled", AuditConfiguration.DATABASE_COMMIT_ENABLED, MBeanTestCaseC.BOOLEAN );
    }
}
