package com.integral.audit.test;

import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditMessageEvent;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.log.LogFactory;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDateTime;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.StopWatchC;
import com.integral.xml.binding.JavaXMLBinder;
import com.integral.xml.binding.JavaXMLBinderFactory;
import com.integral.xml.mapping.MappingFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;


/**
 * Unit test for audit events
 */
public class AuditEventPTestC extends PTestCaseC
{
    static String name = "Audit Event Test";

    public AuditEventPTestC( String msg )
    {
        super( msg );
    }

    /**
     * Write a simple audit event
     */
    public void testInsert()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            AuditEvent event = new AuditEventC();
            event.setStatus( 'T' );
            event.setComponent( "TEST" );
            event.setAction( "UNIT TEST" );

            uow.registerObject( event );
            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "Exception in testInsert : ", e );
        }
    }

    /**
     * Write a bulk of audit event
     */
    public void testBulkInsert()
    {
        int NUM = 100;
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            long now = System.currentTimeMillis();

            for ( int i = 0; i < NUM; i++ )
            {
                AuditEvent event = new AuditEventC();
                event.setStatus( 'T' );
                event.setComponent( "TEST-" + now + '-' + i );
                event.setAction( "BULK TEST" );

                uow.registerObject( event );
            }

            StopWatchC watch = LogFactory.getStopWatch( this.getClass(), "BULK LOAD of " + NUM + " items" );
            watch.start();
            log( "start commit" );
            uow.commit();
            log( "end commit" );
            watch.stopAndLog();
        }
        catch ( Exception e )
        {
            fail( "Exception in testBulkInsert : ", e );
        }
    }


    /**
     * Write a bulk of audit event
     */
    public void testParallelBulkInsert()
    {
        int num = 5;

        try
        {
            String monitor = "MONITOR";

            // start them
            for ( int i = 0; i < num; i++ )
            {
                TestBulkAuditInsertC insert = new TestBulkAuditInsertC( i, monitor );
                insert.prepare();

                Thread th = new Thread( insert, "PARALLEL BULK INSERT TEST #" + i );
                th.setDaemon( true );
                th.start();

            }
            log( "Finished setting up threads" );
            Thread.sleep( 10000 );

            synchronized ( monitor )
            {
                log( "Start commit" );
                Thread.sleep( 5000 );
                monitor.notifyAll();
            }

            Thread.sleep( 60000 );
        }
        catch ( Exception e )
        {
            fail( "Exception in testParallelBulkInsert : ", e );
        }
    }

    public void testCreateAuditFromXML()
    {
        try
        {
            JavaXMLBinder conv = JavaXMLBinderFactory.newJavaXMLBinder();
            // get date
            IdcDateTime dateTime = DateTimeFactory.newDateTime();
            String dateTimeFormat = MappingFactory.getXMLMappingMBean().getDateTimeFormat();
            SimpleDateFormat defaultDateTimeParsingFormat = new SimpleDateFormat( dateTimeFormat );
            String dateTimeString = defaultDateTimeParsingFormat.format( dateTime.asJdkDate() );

            // get user and org
            User user = UserFactory.getUser( "Integral@MAIN" );
            String userXML = "<user>"
                    + "<className>com.integral.user.UserC</className>"
                    + "<shortName>Integral</shortName>"
                    + "<namespace>MAIN</namespace>"
                    + "</user>";

            String ownerXML = "<owner>"
                    + "<className>com.integral.user.UserC</className>"
                    + "<shortName>Integral</shortName>"
                    + "<namespace>MAIN</namespace>"
                    + "</owner>";

            // construct xml and entity
            String xml = "<auditEvent action=\"TESTACTION\" "
                    + "           component=\"TESTCOMPONENT\" "
                    + "           facade-name=\"TESTFACADE\" "
                    + "           type=\"TESTTYPE\" "
                    + "            >"
                    + userXML
                    + ownerXML
                    + " <date-time-arg1>" + dateTimeString + "</date-time-arg1>"
                    + " <double-arg1>99.9</double-arg1>"
                    + " <integer-arg1>100</integer-arg1>"
                    + " <string-arg1>TESTSTRING1</string-arg1>"
                    + "</auditEvent>";
            log.info( "XML = " + xml );
            StringReader xmlReader = new StringReader( xml );
            AuditEvent event = ( AuditEvent ) conv.convertFromXML( xmlReader, "EntityService" );

            // compare basic attributes
            assertEquals( "TESTACTION", event.getAction() );
            assertEquals( "TESTCOMPONENT", event.getComponent() );
            assertEquals( "TESTFACADE", event.getFacadeName() );
            assertEquals( "TESTTYPE", event.getType() );

            // compare basic elements
            log.info( "owner = " + event.getOwner() );
            // assertEquals(owner.getGUID(),   event.getOwner().getGUID());
            assertEquals( user.getGUID(), event.getUser().getGUID() );

            // compare value elements
            assertEquals( dateTime, event.getDateTimeArg1() );
            assertEquals( 99.9, event.getDoubleArg1() );
            // assertEquals(null,              event.getEntity1());
            assertEquals( new Integer( 100 ), event.getIntegerArg1() );
            assertEquals( "TESTSTRING1", event.getStringArg1() );
        }
        catch ( Exception e )
        {
            fail( "error creating audit event from XML", e );
        }
    }

    public void testAuditEventQuery()
    {
        try
        {
            Expression componentExpression = new ExpressionBuilder().get( "component" ).equal( CreditUtilizationEvent.class.getName() );
            Expression amountTypeExpression = new ExpressionBuilder().get( "stringArg1" ).equal( CreditLimit.AMOUNT_USE );
            Expression actionExpression = new ExpressionBuilder().get( "action" ).equal( CreditMessageEvent.APPLY.getName() );
            Expression finalExpr = componentExpression.and( amountTypeExpression ).and( actionExpression );
            ReadAllQuery query = new ReadAllQuery( AuditEventC.class, finalExpr );
            query.useCollectionClass( ArrayList.class );
            ArrayList result = ( ArrayList ) getPersistenceSession().executeQuery( query );
            log( "result=" + result );
        }
        catch ( Exception e )
        {
            fail( "testAuditEventQuery", e );
        }
    }

    public void testAuditEventStrings()
    {
        try
        {
            String testStr = "ABC";
            while ( testStr.length() < 5000 )
            {
                testStr += testStr;
            }
            log( "testStr=" + testStr );
            AuditEvent ae = new AuditEventC();
            ae.setStringArg1( testStr );
            ae.setStringArg2( testStr );
            ae.setStringArg3( testStr );
            ae.setStringArg4( testStr );
            ae.setStringArg5( testStr );
            ae.setStringArg6( testStr );
            ae.setStringArg7( testStr );
            ae.setStringArg8( testStr );
            ae.setStringArg8( testStr );
            ae.setStringArg9( testStr );
            ae.setStringArg10( testStr );
            ae.setStringArg11( testStr );
            ae.setStringArg12( testStr );
            ae.setStringArg13( testStr );
            ae.setStringArg14( testStr );
            ae.setStringArg15( testStr );
            ae.setStringArg16( testStr );
            ae.setStringArg17( testStr );
            ae.setStringArg18( testStr );
            ae.setStringArg19( testStr );
            ae.setStringArg20( testStr );

            assertEquals( ae.getStringArg1().length(), AuditEventC.MAX_LENGTH_STRING_ARG1 );
            assertEquals( ae.getStringArg2().length(), AuditEventC.MAX_LENGTH_STRING_ARG2 );
            assertEquals( ae.getStringArg3().length(), AuditEventC.MAX_LENGTH_STRING_ARG3 );
            assertEquals( ae.getStringArg4().length(), AuditEventC.MAX_LENGTH_STRING_ARG4 );
            assertEquals( ae.getStringArg5().length(), AuditEventC.MAX_LENGTH_STRING_ARG5 );
            assertEquals( ae.getStringArg6().length(), AuditEventC.MAX_LENGTH_STRING_ARG6 );
            assertEquals( ae.getStringArg7().length(), AuditEventC.MAX_LENGTH_STRING_ARG7 );
            assertEquals( ae.getStringArg8().length(), AuditEventC.MAX_LENGTH_STRING_ARG8 );
            assertEquals( ae.getStringArg9().length(), AuditEventC.MAX_LENGTH_STRING_ARG9 );
            assertEquals( ae.getStringArg10().length(), AuditEventC.MAX_LENGTH_STRING_ARG10 );
            assertEquals( ae.getStringArg11().length(), AuditEventC.MAX_LENGTH_STRING_ARG11 );
            assertEquals( ae.getStringArg12().length(), AuditEventC.MAX_LENGTH_STRING_ARG12 );
            assertEquals( ae.getStringArg13().length(), AuditEventC.MAX_LENGTH_STRING_ARG13 );
            assertEquals( ae.getStringArg14().length(), AuditEventC.MAX_LENGTH_STRING_ARG14 );
            assertEquals( ae.getStringArg15().length(), AuditEventC.MAX_LENGTH_STRING_ARG15 );
            assertEquals( ae.getStringArg16().length(), AuditEventC.MAX_LENGTH_STRING_ARG16 );
            assertEquals( ae.getStringArg17().length(), AuditEventC.MAX_LENGTH_STRING_ARG17 );
            assertEquals( ae.getStringArg18().length(), AuditEventC.MAX_LENGTH_STRING_ARG18 );
            assertEquals( ae.getStringArg19().length(), AuditEventC.MAX_LENGTH_STRING_ARG19 );
            assertEquals( ae.getStringArg20().length(), AuditEventC.MAX_LENGTH_STRING_ARG20 );

            // set different values which does not exceed max lengths.
            AuditEvent ae1 = new AuditEventC();
            ae1.setStringArg1( "A" );
            ae1.setStringArg2( "B" );
            ae1.setStringArg3( "C" );
            ae1.setStringArg4( "D" );
            ae1.setStringArg5( "E" );
            ae1.setStringArg6( "F" );
            ae1.setStringArg7( "G" );
            ae1.setStringArg8( "H" );
            ae1.setStringArg9( "I" );
            ae1.setStringArg10( "J" );
            ae1.setStringArg11( "K" );
            ae1.setStringArg12( "L" );
            ae1.setStringArg13( "M" );
            ae1.setStringArg14( "N" );
            ae1.setStringArg15( "O" );
            ae1.setStringArg16( "P" );
            ae1.setStringArg17( "Q" );
            ae1.setStringArg18( "R" );
            ae1.setStringArg19( "S" );
            ae1.setStringArg20( "T" );

            assertEquals( ae1.getStringArg1(), "A" );
            assertEquals( ae1.getStringArg2(), "B" );
            assertEquals( ae1.getStringArg3(), "C" );
            assertEquals( ae1.getStringArg4(), "D" );
            assertEquals( ae1.getStringArg5(), "E" );
            assertEquals( ae1.getStringArg6(), "F" );
            assertEquals( ae1.getStringArg7(), "G" );
            assertEquals( ae1.getStringArg8(), "H" );
            assertEquals( ae1.getStringArg9(), "I" );
            assertEquals( ae1.getStringArg10(), "J" );
            assertEquals( ae1.getStringArg11(), "K" );
            assertEquals( ae1.getStringArg12(), "L" );
            assertEquals( ae1.getStringArg13(), "M" );
            assertEquals( ae1.getStringArg14(), "N" );
            assertEquals( ae1.getStringArg15(), "O" );
            assertEquals( ae1.getStringArg16(), "P" );
            assertEquals( ae1.getStringArg17(), "Q" );
            assertEquals( ae1.getStringArg18(), "R" );
            assertEquals( ae1.getStringArg19(), "S" );
            assertEquals( ae1.getStringArg20(), "T" );

            // now try setting the null value.
            AuditEvent ae2 = new AuditEventC();
            ae2.setStringArg1( null );
            ae2.setStringArg1( null );
            ae2.setStringArg2( null );
            ae2.setStringArg3( null );
            ae2.setStringArg4( null );
            ae2.setStringArg5( null );
            ae2.setStringArg6( null );
            ae2.setStringArg7( null );
            ae2.setStringArg8( null );
            ae2.setStringArg9( null );
            ae2.setStringArg10( null );
            ae2.setStringArg11( null );
            ae2.setStringArg12( null );
            ae2.setStringArg13( null );
            ae2.setStringArg14( null );
            ae2.setStringArg15( null );
            ae2.setStringArg16( null );
            ae2.setStringArg17( null );
            ae2.setStringArg18( null );
            ae2.setStringArg19( null );
            ae2.setStringArg20( null );

            assertNull( ae2.getStringArg1() );
            assertNull( ae2.getStringArg2() );
            assertNull( ae2.getStringArg3() );
            assertNull( ae2.getStringArg4() );
            assertNull( ae2.getStringArg5() );
            assertNull( ae2.getStringArg6() );
            assertNull( ae2.getStringArg7() );
            assertNull( ae2.getStringArg8() );
            assertNull( ae2.getStringArg9() );
            assertNull( ae2.getStringArg10() );
            assertNull( ae2.getStringArg11() );
            assertNull( ae2.getStringArg12() );
            assertNull( ae2.getStringArg13() );
            assertNull( ae2.getStringArg14() );
            assertNull( ae2.getStringArg15() );
            assertNull( ae2.getStringArg16() );
            assertNull( ae2.getStringArg17() );
            assertNull( ae2.getStringArg18() );
            assertNull( ae2.getStringArg19() );
            assertNull( ae2.getStringArg20() );
        }
        catch ( Exception e )
        {
            fail( "testAuditEventStrings", e );
        }

    }
}
