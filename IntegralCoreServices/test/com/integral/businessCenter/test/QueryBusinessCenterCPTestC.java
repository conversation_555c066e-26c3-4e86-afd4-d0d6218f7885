package com.integral.businessCenter.test;


import com.integral.persistence.Namespace;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.User;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.Session;

import java.util.Vector;


public class QueryBusinessCenterCPTestC
        extends PTestCaseC
{
    static String name = "QueryBusinessCenterCPTestC Test";
    Vector users = null;

    public QueryBusinessCenterCPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new QueryBusinessCenterCPTestC( "queryBusinessCenter_oldVersion" ) );
        suite.addTest( new QueryBusinessCenterCPTestC( "queryBusinessCenter_newVersion" ) );
        return suite;
    }

    public void setUp() throws Exception
    {
        super.setUp();

        getPersistenceSession().readAllObjects( Namespace.class );
        getPersistenceSession().readAllObjects( Organization.class );
        users = getPersistenceSession().readAllObjects( User.class );
    }

    /**
     * Queries for all business centers that are in the MAIN namespace
     * and in a user's namespace.
     * <p/>
     * The query is copied from the OrganizationUserDetailControllerC
     * and always forces the ReferenceDataService to go to the database
     * since it is using <EM>getField()</EM>
     * <p/>
     * See the improved version in the <EM>queryBusinessCenter_newVersion</EM>
     * method.
     */
    public void queryBusinessCenter_oldVersion()
    {
        log( "queryBusinessCenter_oldVersion" );
        try
        {
            Session uow = getPersistenceSession();

            User user = null;
            for ( int i = 0; i < users.size(); i++ )
            {
                user = ( User ) users.elementAt( i );
                if ( user.getShortName().equalsIgnoreCase( "member12" ) )
                {
                    break;
                }
            }
/*
			System.out.println("using user #" + user.getObjectID() + "-" + user.getShortName());

			// create RDS
			ReferenceDataServiceC rds = new ReferenceDataServiceC(uow,user);

			// build query expression
			long namespaceID = user.getOrganization().getObjectID();
			System.out.println("using user namespace #" + user.getOrganization().getObjectID() + "-" + user.getOrganization().getShortName());
			long idArray[] = {1, namespaceID};

			// We want all business centers in main namspace and in the namespace
			// of the organization the user belongs to
			ExpressionBuilder eb = new ExpressionBuilder();
			Expression exp = eb.get(BusinessCenter.Status).equal('A')
							   .and(eb.getField("namespaceid").in(idArray));
			ArrayList results = IdcUtilC.convertEnumToArrayList(rds.findByPredicate(BusinessCenter.class,exp));

			// read all LE
			if (results != null)
			{
				for (int j=0; j<results.size(); j++)
				{
					BusinessCenter bc = (BusinessCenter)results.get(j);
					System.out.println("Found #" + bc.getObjectID());
				}
			}
*/

            log( "query BC" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "query BC" );
        }
    }

    /**
     * Queries for all business centers that are in the MAIN namespace
     * and in a user's namespace.
     * <p/>
     * The query is a tuned version of the query used in <EM>queryBusinessCenter_oldVersion</EM>.
     * This version never forces the ReferenceDataService to go to the database.
     */
    public void queryBusinessCenter_newVersion()
    {
        log( "queryBusinessCenter_newVersion" );
        try
        {
            Session uow = getPersistenceSession();

            User user = null;
            for ( int i = 0; i < users.size(); i++ )
            {
                user = ( User ) users.elementAt( i );
                if ( user.getShortName().equalsIgnoreCase( "member12" ) )
                {
                    break;
                }
            }
            System.out.println( "using user #" + user.getObjectID() + '-' + user.getShortName() );

/*
			// create RDS
			ReferenceDataServiceC rds = new ReferenceDataServiceC(uow,user);

			// build query expression
			long namespaceID = user.getOrganization().getObjectID();
			System.out.println("using user namespace #" + user.getOrganization().getObjectID() + "-" + user.getOrganization().getShortName());
			long idArray[] = {1, namespaceID};

			// We want all business centers in main namspace and in the namespace
			// of the organization the user belongs to
			ExpressionBuilder eb = new ExpressionBuilder();
			Expression exp = eb.get(BusinessCenter.Status).equal('A')
							   .and(eb.get("namespace").get("objectID").in(idArray));
			ArrayList results = (ArrayList)rds.findByPredicate(BusinessCenter.class,ArrayList.class,exp);

			// read all LE
			if (results != null)
			{
				for (int j=0; j<results.size(); j++)
				{
					BusinessCenter bc = (BusinessCenter)results.get(j);
					System.out.println("Found #" + bc.getObjectID());
				}
			}
*/

            log( "query BC" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "query BC" );
        }
    }
}
