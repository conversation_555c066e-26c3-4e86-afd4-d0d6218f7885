package com.integral.sso.test;

// Copyright (c) 2001-2009 Integral Development Corp. All rights reserved.

import com.integral.sso.SSOMBeanC;
import com.integral.sso.SSOMBean;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;

import java.util.Map;

public class SSOMBeanTestC extends MBeanTestCaseC
{
    SSOMBeanC ssoConfig = ( SSOMBeanC ) SSOMBeanC.getInstance();

    public SSOMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( ssoConfig, "adminServerURL", "Idc.SSO.AdminServer.Url", MBeanTestCaseC.STRING );
        testProperty( ssoConfig, "configServerURL", "Idc.SSO.ConfigServer.Url", MBeanTestCaseC.STRING );
        testProperty( ssoConfig, "MISPortalServerURL", "Idc.SSO.MISPortalServer.Url", MBeanTestCaseC.STRING );
        testProperty( ssoConfig, "openInNewWindow", "Idc.SSO.NewWindow", MBeanTestCaseC.BOOLEAN );
    }

    public void testSSOAppBasedPermission(){
        String appName = "OMS";
        String permission = "OMSAdminUserPermission";
        String defaultOMSPermission = "OMSAdminUser,OMSTraderUser,OMSChiefDealerUser";
        // no property set
        assertEquals( ssoConfig.getSsoAppBasedPermission().get(appName) ,defaultOMSPermission);
        //set property
        ssoConfig.setProperty( "Idc.SSO.App.Permission."  + appName, permission, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( ssoConfig.getSsoAppBasedPermission().get(appName) , permission );

        //remove property
        ssoConfig.removeProperty( "Idc.SSO.App.Permission."  + appName,  ConfigurationProperty.DYNAMIC_SCOPE );
        assertEquals( ssoConfig.getSsoAppBasedPermission().get(appName) ,defaultOMSPermission);

    }

    public void testSSOAppSubDomain(){
        String appName = "Watch";
        String defaultSubDomain = "watch.";
        String subDomain = "watch1.";
        // no property set
        assertEquals( ssoConfig.getAppSubDomain(appName), defaultSubDomain);
        //set property
        ssoConfig.setProperty( SSOMBean.IDC_SSO_APP_SUBDOMAIN  + appName, subDomain, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( ssoConfig.getAppSubDomain(appName) , subDomain );

        //remove property
        ssoConfig.removeProperty( SSOMBean.IDC_SSO_APP_SUBDOMAIN  + appName,  ConfigurationProperty.DYNAMIC_SCOPE );
        assertEquals( ssoConfig.getAppSubDomain(appName), defaultSubDomain);

    }



    public void testOIDCServiceTradingAppName() {
        try {
            String customerOrg1 = "AdarshOrg1";
            String customerOrg2 = "AdarshOrg2";

            //When no property set
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg1), "OCX");
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg2), "OCX");

            //set broker property at global level
            ssoConfig.setProperty("Idc.SSO.OIDC.Service.TradingApp", "FXI7", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg1), "OCX");
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg2), "OCX");

            //set broker property as null at customer level
            ssoConfig.setProperty("Idc.SSO.OIDC.Service.TradingApp." + customerOrg1, null, ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg1), "");

            //set broker property at customer level
            ssoConfig.setProperty("Idc.SSO.OIDC.Service.TradingApp." + customerOrg1, "FXI7", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg1), "FXI7");
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg2), "OCX");

            ssoConfig.setProperty("Idc.SSO.OIDC.Service.TradingApp." + customerOrg1, "OCX", ConfigurationProperty.DYNAMIC_SCOPE, null);
            ssoConfig.setProperty("Idc.SSO.OIDC.Service.TradingApp." + customerOrg2, "FXI7", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg1), "OCX");
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg2), "FXI7");

            ssoConfig.setProperty("Idc.SSO.OIDC.Service.TradingApp." + customerOrg1, "ocx", ConfigurationProperty.DYNAMIC_SCOPE, null);
            ssoConfig.setProperty("Idc.SSO.OIDC.Service.TradingApp." + customerOrg2, "fxi7", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg1), "ocx");
            assertEquals(ssoConfig.getOIDCServiceTradingAppName(customerOrg2), "fxi7");

            ssoConfig.removeProperty("Idc.SSO.OIDC.Service.TradingApp." + customerOrg1, ConfigurationProperty.DYNAMIC_SCOPE);
            ssoConfig.removeProperty("Idc.SSO.OIDC.Service.TradingApp." + customerOrg2, ConfigurationProperty.DYNAMIC_SCOPE);
            ssoConfig.removeProperty("Idc.SSO.OIDC.Service.TradingApp", ConfigurationProperty.DYNAMIC_SCOPE);

        } catch (Exception e) {
            e.printStackTrace();
            fail("testOIDCServiceTradingAppName");
        }
    }
}
