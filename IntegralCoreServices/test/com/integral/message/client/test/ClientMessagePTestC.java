package com.integral.message.client.test;

import com.integral.message.client.ClientMessage;
import com.integral.message.client.ClientMessageC;
import com.integral.message.client.ClientMessageFactory;
import com.integral.test.PTestCaseC;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Date;
import java.util.Enumeration;
import java.util.Vector;

public class ClientMessagePTestC
        extends PTestCaseC
{
    static String name = "User Creation Test";

    public ClientMessagePTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new ClientMessagePTestC( "testCreateClientMessage" ) );
        return suite;
    }

    public void testCreateClientMessage()
    {
        log.info( "testCreateClientMessage" );
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
/*
                    UnitOfWork uow1 = session1.acquireUnitOfWork();
                    uow1.removeAllReadOnlyClasses();
*/

            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "MAIN" );
            OrganizationC org = ( OrganizationC ) session1.readObject( OrganizationC.class, expr );


            ExpressionBuilder eb1 = new ExpressionBuilder();
            Expression expr1 = eb1.get( "shortName" ).equal( "HeartbeatUsr" );
            User usr = ( User ) session1.readObject( UserC.class, expr1 );


            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();
            org = ( OrganizationC ) uow1.registerObject( org );
            usr = ( User ) uow1.registerObject( usr );

            ClientMessage cm = ClientMessageFactory.newClientMessage();
            ClientMessage cm1 = ( ClientMessage ) uow1.registerNewObject( cm );
            cm1.setOrg( org );
            cm1.setUser( usr );
            cm1.setMessage( "test MEssage - may be upto 4000 characters" );
            cm1.setMessageDateTime( new Date() );

            uow1.commit();

            Vector result = ( Vector ) session1.executeQuery( new ReadAllQuery( ClientMessageC.class ) );
            Enumeration enum1 = result.elements();
            uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();
            int i = 0;
            while ( enum1.hasMoreElements() )
            {
                ClientMessageC cm2 = ( ClientMessageC ) enum1.nextElement();
                System.out.println( "cm2  iss " + cm2 );
                if ( i == 2 )
                {
                    cm2 = ( ClientMessageC ) uow1.registerObject( cm2 );
                    cm2.setMessage( "Modified message" );
                }
                i++;
            }
            uow1.commit();

        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }

    }

}
