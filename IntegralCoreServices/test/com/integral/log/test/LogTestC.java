package com.integral.log.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import junit.framework.TestCase;


/**
 * Tests the log subsystem
 */
public class LogTestC
        extends TestCase
{
    static String name = "Log Test";

    public LogTestC( String name )
    {
        super( name );
    }

    public void testLogCategory()
    {
        Log log = LogFactory.getLog( "com.integral.trade.Trade" );
        log.debug( "DEBUG - test log message" );
/*
        try
        {
            int ch = System.in.read();
        }
        catch (Exception e)
        {
        }
*/
        log.debug( "DEBUG - test log message 2" );
        log.info( "INFO - test log message" );
        log.warn( "WARN - test log message" );
        log.error( "ERROR - test log message" );
        log.fatal( "FATAL - test log message" );
    }

    /**
     * Test localized messages. The resource bundle used is configured
     * in log4j.xml
     * <p/>
     * This test will print unexpanded messages since we removed the
     * localization support.
     */
    public void testLocalizedLogMessages()
    {
        Log log = LogFactory.getLog( "com.integral.trade.Trade" );

        String args[] = {"ARG1", "ARG2", "ARG3"};

        log.debugI18N( "IDC_TEST", args );
        log.infoI18N( "IDC_TEST", args );
        log.warnI18N( "IDC_TEST", args );
        log.errorI18N( "IDC_TEST", args );
        log.fatalI18N( "IDC_TEST", args );
    }
}
