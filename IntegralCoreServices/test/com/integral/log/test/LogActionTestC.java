package com.integral.log.test;

import com.integral.log.rule.LogAction;
import com.integral.log.rule.LogActionC;
import junit.framework.TestCase;

public class LogActionTestC
        extends TestCase
{

    public void testLogAction()
    {

        LogAction action = new LogActionC();

        action.setCategory( "com.integral.rule" );
        action.setLevel( LogAction.ERROR );
        action.setMessage( "Static message" );
        try
        {
            action.execute( null );
        }
        catch ( Exception e )
        {
            fail( "Error in executing LogAction : " );
            e.printStackTrace();
        }

        action.setMessage( "Message with date parameter {0}" );
        action.putCustomField( "$0", new java.util.Date() );

        try
        {
            action.execute( null );
        }
        catch ( Exception e )
        {
            fail( "Error in executing LogAction : " );
            e.printStackTrace();
        }

        action.setMessage( "Message with two parameters: {0} and {1}" );
        action.putCustomField( "$0", new java.util.Date() );
        action.putCustomField( "$1", 3 );

        try
        {
            action.execute( null );
        }
        catch ( Exception e )
        {
            fail( "Error in executing LogAction : " );
            e.printStackTrace();
        }

    }
}
