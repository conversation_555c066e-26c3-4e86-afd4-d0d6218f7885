package com.integral.rtdata.deployment.test;

import com.integral.persistence.CustomField;
import com.integral.persistence.Entity;
import com.integral.rtdata.DataHandler;
import com.integral.rtdata.Deployment;
import com.integral.rtdata.DeploymentGroup;
import com.integral.rtdata.ExternalDataSourceRef;
import com.integral.rtdata.HandlerController;
import com.integral.rtdata.deployment.DeploymentFactory;
import com.integral.rtdata.deployment.DeploymentGroupC;
import com.integral.rtdata.handler.HandlerControllerC;
import com.integral.rtdata.handler.HandlerFactory;
import com.integral.rtdata.handler.RulebasedHandlerC;
import com.integral.rtdata.server.RTDataServerC;
import com.integral.rule.RuleSet;
import com.integral.rule.RuleSetC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.Vector;

/**
 * Unit tests for persisting rtdata entities
 */
public class DeploymentPersistencePTestC
        extends PTestCaseC
{

    static String name = "Deployment Lookup Test";

    public DeploymentPersistencePTestC( String name )
    {
        super( name );
    }

    private void deleteDeploymentGroup( UnitOfWork uow, DeploymentGroup dg )
    {
        DeploymentGroup clone = ( DeploymentGroup ) uow.registerObject( dg );
        Collection<Deployment> deployments = clone.getDeployments().values();
        Iterator it = deployments.iterator();
        while ( it.hasNext() )
        {
            Deployment dep = ( Deployment ) uow.registerObject( it.next() );
            dep.getHandlerControllers().clear();
            dep.getDataSourceReferences().clear();
        }
        clone.getDeployments().clear();
        clone.setDataServer( null );
        uow.deleteObject( clone );
    }

    public void testDelete()
    {
        log( "Calling DeploymentPersistencePTestC.... testDelete" );

        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            Iterator rtDataServers = getPersistenceSession().readAllObjects( RTDataServerC.class ).iterator();


            while ( rtDataServers.hasNext() )
            {
                RTDataServerC rtDataServer = ( RTDataServerC ) uow.registerObject( rtDataServers.next() );
                Iterator dgs = rtDataServer.getDeploymentGroups().iterator();

                while ( dgs.hasNext() )
                {
                    DeploymentGroup dg = ( DeploymentGroup ) dgs.next();
                    deleteDeploymentGroup( uow, dg );
                }
                uow.deleteObject( rtDataServer );
            }
            Iterator dgs = getPersistenceSession().readAllObjects( DeploymentGroupC.class, expr ).iterator();
            while ( dgs.hasNext() )
            {
                DeploymentGroup dg = ( DeploymentGroup ) dgs.next();
                deleteDeploymentGroup( uow, dg );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            log.error( "error when deleting", e );
            fail( "testDelete", e );
        }
    }

    //
    public void testDeploymentGroupQuery()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            ReadAllQuery query = new ReadAllQuery( DeploymentGroupC.class, eb );
            query.setSelectionCriteria( expr );
            query.addOrdering( eb.get( "objectID" ) );

            Iterator dgs = ( ( Vector ) getPersistenceSession().executeQuery( query ) ).iterator();

            while ( dgs.hasNext() )
            {
                DeploymentGroup dg = ( DeploymentGroup ) dgs.next();
                printDeploymentGroup( dg );
            }
        }
        catch ( Exception e )
        {
            log.error( "error when reading", e );
            fail( "testDeploymentGroupQuery", e );
        }
    }

    public DeploymentGroup testDeploymentGroupQueryByObjectId()
    {
        String objectId = "2501";
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "objectID" ).equal( objectId );

            ReadAllQuery query = new ReadAllQuery( DeploymentGroupC.class, eb );
            query.setSelectionCriteria( expr );
            query.addOrdering( eb.get( "objectID" ) );

            // TODO: check why interface doesn't work
            Iterator dgs = ( ( Vector ) getPersistenceSession().executeQuery( query ) ).iterator();

            while ( dgs.hasNext() )
            {
                DeploymentGroup dg = ( DeploymentGroup ) dgs.next();
                printDeploymentGroup( dg );
                return dg;
            }
        }
        catch ( Exception e )
        {
            log.error( "error when reading", e );
            fail( "testDeploymentGroupQueryByObjectId", e );
        }
        return null;
    }

    public void testHandlerControllersQuery()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            // TODO: why does querying by interface not work
            ReadAllQuery query = new ReadAllQuery( HandlerControllerC.class, eb );
            query.setSelectionCriteria( expr );
            query.addOrdering( eb.get( "objectID" ) );

            Iterator dgs = ( ( Vector ) getPersistenceSession().executeQuery( query ) ).iterator();

            while ( dgs.hasNext() )
            {
                HandlerController dg = ( HandlerController ) dgs.next();
                printHandlerController( dg );
            }
        }
        catch ( Exception e )
        {
            log.error( "error when reading", e );
            fail( "testHandlerControllersQuery", e );
        }
    }

    public void printDeploymentGroup( DeploymentGroup dg )
    {
        log( "Deployment Group: " + dg );
        Map deployments = dg.getDeployments();
        Iterator iter = deployments.keySet().iterator();
        while ( iter.hasNext() )
        {
            Object key = iter.next();
            Object value = deployments.get( key );
            if ( value instanceof Deployment )
            {
                Deployment depl = ( Deployment ) value;
                log( "\tDeployment: " + depl );
                Map handlerControllers = depl.getHandlerControllers();
                Iterator it = handlerControllers.values().iterator();
                while ( it.hasNext() )
                {
                    HandlerController hc = ( HandlerController ) it.next();
                    printHandlerController( hc );
                }
            }
            else
            {
                log( "\tNot instance of deployment" );
            }
        }//end of main while

    }

    private void printCustomFields( Entity e, int tabs )
    {
        Map map = e.getCustomFields();
        if ( map == null )
        {
            return;
        }

        Iterator it = map.values().iterator();
        String tabStr = "\t";
        for ( int i = 0; i < tabs; i++ )
        {
            tabStr += "\t";
        }
        while ( it.hasNext() )
        {
            CustomField cf = ( CustomField ) it.next();
            log( tabStr + "CustomField: " + cf.getKey() + " = " + cf.getValue().toString() );
        }
    }

    private void printHandlerController( HandlerController hc )
    {
        log( "\t\tHandler Controller: " + hc );
        printCustomFields( hc, 3 );

        DataHandler dh = hc.getDataHandler();
        log( "\t\t\tData Handler: " + dh );
        if ( dh instanceof RulebasedHandlerC )
        {
            RuleSet rs = ( ( RulebasedHandlerC ) dh ).getRuleSet();
            log( "\t\t\t\tRuleSet: " + rs );
        }
        else
        {
            log( "\t\t\t\tEntry not a RulebasedHandler object: " + dh.toString() );
            printCustomFields( dh, 4 );
        }

        log( "\t\t\tNext Data Handlers:" );
        Iterator nexts = hc.getNextControllers().iterator();
        while ( nexts.hasNext() )
        {
            HandlerController next = ( HandlerController ) nexts.next();
            log( "\t\t\t\tNext Handler Controller: " + next );
        }
    }


    /**
     * Tests inserting a complex deployment group graph
     */
    public void testInsert()
    {
        try
        {

            log( "Calling DeploymentPersistencePTestC.... testInsert" );

            DeploymentGroup dg = DeploymentFactory.getInstance().newDeploymentGroup();

            //UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
            //uow.registerObject(dg);

            dg.setName( "Test_Group1" + System.currentTimeMillis() );
            dg.setStatus( 'T' );

            Deployment deployment = DeploymentFactory.getInstance().newDeployment();
            deployment.setName( "TEST_EUR_USD" + System.currentTimeMillis() );
            deployment.setStatus( 'T' );

            dg.addDeployment( deployment );
            deployment.setOwner( dg );

            ExternalDataSourceRef dsRef = DeploymentFactory.getInstance().newExternalDsRef( "SimpleDataSource" );
            dsRef.setStatus( 'T' );
            deployment.addDataSourceRef( dsRef );

            RuleSet rs = new RuleSetC();
            rs.setStatus( 'T' );
            rs.setShortName( "TEST_RS" + System.currentTimeMillis() );

            // ruleset data handler
            DataHandler ruleset = HandlerFactory.getInstance().newRuleSetDataHandler( rs );
            ruleset.setName( "TEST_Ruleset" + System.currentTimeMillis() );
            ruleset.setStatus( 'T' );
            ruleset = HandlerFactory.getInstance().newCustomDataHandler( "com.integral.rtdata.handler.RulebasedHandlerC" );

            // basic data handler
            DataHandler basic = HandlerFactory.getInstance().newCustomDataHandler( "com.integral.rtdata.handler.ConsoleSinkC" );
            basic.setStatus( 'T' );

            // sink data handler
            DataHandler sink = HandlerFactory.getInstance().newCustomDataHandler( "com.integral.rtdata.handler.ConsoleSinkC" );
            sink.setStatus( 'T' );

            // scrubber1 handler controller
            HandlerController scrubber1 = HandlerFactory.getInstance().newHandlerController();
            scrubber1.setStatus( 'T' );
            scrubber1.setName( "TEST_Scrubber" + System.currentTimeMillis() );

            scrubber1.setDataHandler( basic );
            deployment.addHandlerController( scrubber1 );

            // scrubber1a  handler controller
            HandlerController scrubber1a = HandlerFactory.getInstance().newHandlerController();
            scrubber1a.setStatus( 'T' );
            scrubber1a.setName( "TEST_Scrubber" + System.currentTimeMillis() );

            scrubber1a.setDataHandler( ruleset );
            scrubber1.addNextController( scrubber1a );

            // scrubber1b  handler controller
            HandlerController scrubber1b = HandlerFactory.getInstance().newHandlerController();
            scrubber1b.setStatus( 'T' );
            scrubber1b.setName( "TEST_Scrubber" + System.currentTimeMillis() );

            scrubber1b.setDataHandler( ruleset );
            scrubber1.addNextController( scrubber1b );

            // publisher handler controller
            HandlerController publisher = HandlerFactory.getInstance().newHandlerController();
            publisher.setStatus( 'T' );
            publisher.setName( "TEST_Publisher" + System.currentTimeMillis() );

            publisher.setDataHandler( sink );
            // sink.setOwner(publisher);
            deployment.addHandlerController( publisher );

            // save to db
            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
            //uow.registerObject( dg );
            uow.commit();
        }
        catch ( Exception e )
        {
            log.error( "error when saving", e );
            fail( "testInsert", e );
        }

    }

    public void saveDeploymentGroup( DeploymentGroup dg )
    {
        // save to db
        UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
        uow.registerObject( dg );
        uow.commit();
    }
}