package com.integral.rtdata.server.test;

import com.integral.rtdata.DeploymentGroup;
import com.integral.rtdata.deployment.DeploymentGroupC;
import com.integral.rtdata.server.RTDataServer;
import com.integral.rtdata.server.RTDataServerC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Iterator;
import java.util.Vector;

/**
 * Unit tests for persisting rtdata server entities
 */
public class ServerPPTestC
        extends PTestCaseC
{

    static String name = "RTDataServer Persistence Test";

    public ServerPPTestC( String name )
    {
        super( name );
    }

    /**
     * Tests querying for all rtdata servers
     */
    public void testQuery()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            ReadAllQuery query = new ReadAllQuery( RTDataServer.class, eb );
            query.setSelectionCriteria( expr );
            query.addOrdering( eb.get( "objectID" ) );

            Iterator dgs = ( ( Vector ) getPersistenceSession().executeQuery( query ) ).iterator();

            while ( dgs.hasNext() )
            {
                RTDataServer server = ( RTDataServer ) dgs.next();
                printRTDataServer( server );
            }
        }
        catch ( Exception e )
        {
            log.error( "error when reading", e );
            fail();
        }
    }

    /**
     * Tests inserting a rtdata server
     */
    public void testInsert()
    {
        try
        {
            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
            DeploymentGroup grp1 = ( DeploymentGroup ) uow.registerObject( new DeploymentGroupC() );
            grp1.setStatus( 'T' );
            grp1.setShortName( "TEST1-" + System.currentTimeMillis() );
            DeploymentGroup grp2 = ( DeploymentGroup ) uow.registerObject( new DeploymentGroupC() );
            grp2.setStatus( 'T' );
            grp2.setShortName( "TEST2-" + System.currentTimeMillis() );

            RTDataServer server = ( RTDataServer ) uow.registerObject( new RTDataServerC() );
            server.setStatus( 'T' );
            server.setServerName( "TEST-" + System.currentTimeMillis() );
            server.setServerEnabled( true );

            server.getDeploymentGroups().add( grp1 );
            server.getDeploymentGroups().add( grp2 );

            printRTDataServer( server );

            // save to db
            uow.commit();
        }
        catch ( Exception e )
        {
            log.error( "error when inserting", e );
            fail( "Str", e );
        }
    }

    /**
     * Tests removing a group from all rtdata servers
     */
    public void testRemoveGroup()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            ReadAllQuery query = new ReadAllQuery( RTDataServer.class, eb );
            query.setSelectionCriteria( expr );
            query.addOrdering( eb.get( "objectID" ) );

            Iterator dgs = ( ( Vector ) getPersistenceSession().executeQuery( query ) ).iterator();

            while ( dgs.hasNext() )
            {
                RTDataServer server = ( RTDataServer ) dgs.next();

                UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
                server = ( RTDataServer ) uow.registerObject( server );

                if ( server.getDeploymentGroups().size() > 0 )
                {
                    DeploymentGroup grp = ( DeploymentGroup ) server.getDeploymentGroups().iterator().next();
                    server.getDeploymentGroups().remove( grp );

                    // TODO: this should not be required
                    server.removeDeploymentGroup( grp );
                }

                uow.commit();

            }
        }
        catch ( Exception e )
        {
            log.error( "error when removing group", e );
            fail();
        }
    }

    private void printRTDataServer( RTDataServer server )
    {
        log.info( "RTDataServer: " + server );
        log.info( "\t server name: " + server.getServerName() );
        log.info( "\t server enabled: " + server.getServerEnabled() );
        Iterator grps = server.getDeploymentGroups().iterator();
        while ( grps.hasNext() )
        {
            DeploymentGroup grp = ( DeploymentGroup ) grps.next();
            log.info( "\t deployment group: " + grp );
        }
    }


}