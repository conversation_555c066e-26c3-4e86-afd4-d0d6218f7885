package com.integral.cache.bucket.test;

import com.integral.cache.bucket.Bucket;
import com.integral.cache.bucket.BucketManagerC;

// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * <AUTHOR> Development Corp.
 */
public class TestBucketManagerC extends BucketManagerC
{
    /**
     * Create a new bucket that is represented by the key. The bucket is al so added to the internal cache after its
     * creation.
     *
     * @param key
     * @return
     */
    public synchronized Bucket createBucket( Object key )
    {
        TestBucketC bucket = new TestBucketC( key );
        super.putBucket( bucket );

        return bucket;
    }
}
