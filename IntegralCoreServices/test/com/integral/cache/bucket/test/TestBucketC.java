package com.integral.cache.bucket.test;

import com.integral.cache.bucket.BucketC;
import com.integral.log.Log;
import com.integral.log.LogFactory;

// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * <AUTHOR> Development Corp.
 */
public class TestBucketC extends BucketC
{
    protected Log log = LogFactory.getLog( this.getClass() );

    public TestBucketC( Object key )
    {
        super( key );
        for ( int i = 0; i < 1000; i++ )
        {
            super.addElement( Integer.toString( i ) );
        }
    }

    /**
     * Notify an element of the Bucket about an event. This method should be overriden by
     * the implementing classes, applying a bussiness meaning to the event.<br>
     * This method impelmentation should be fail-safe - i.e. it should trap all exception conditions.
     *
     * @param element
     * @param event
     */
    public void notifyElement( Object element, Object event )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "TestBucketC.notifyElement: element=" + element + " event=" + event );
        }

    }
}
