package com.integral.cache.bucket.test;

import com.integral.cache.bucket.BucketManager;

import java.util.ArrayList;

// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * <AUTHOR> Development Corp.
 */
public class TestDriver
{
    public static final String KEY_PREFIX = "bucket";

    BucketManager bucketMgr = new TestBucketManagerC();

    public void addBuckets()
    {
        for ( int i = 0; i < 20; i++ )
        {
            String key = KEY_PREFIX + i;
            TestBucketC bucket = new TestBucketC( key );
            bucketMgr.putBucket( bucket );
        }

    }

    public void testPublishRates()
    {
        System.out.println( "trace1" );
        addBuckets();
        ArrayList list = new ArrayList();

        for ( int i = 0; i < 20; i++ )
        {
            String key = KEY_PREFIX + i;
            PublisherThread pt = new PublisherThread( key, 100 );
            list.add( pt );
        }
        for ( int i = 0; i < list.size(); i++ )
        {
            PublisherThread pt = ( PublisherThread ) list.get( i );
            pt.setDaemon( true );
            pt.start();
        }
        while ( true )
        {
            try
            {
                Thread.sleep( 5000 );
            }
            catch ( InterruptedException e )
            {
                //
            }
        }
    }

    public static void main( String[] args )
    {
        System.out.println( "trace0" );
        TestDriver test = new TestDriver();
        test.testPublishRates();
    }

    private class PublisherThread extends Thread
    {
        String key = null;
        long waitTime = 100;

        public PublisherThread( String key, long waitTime )
        {
            this.key = key;
            this.waitTime = waitTime;
        }

        public void run()
        {
            while ( true )
            {
                bucketMgr.notifyEvent( key, Long.toString( System.currentTimeMillis() ) );
                try
                {
                    Thread.sleep( waitTime );
                }
                catch ( InterruptedException e )
                {
                    //
                }
            }
        }
    }
}
