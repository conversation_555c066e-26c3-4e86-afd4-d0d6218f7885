package com.integral.persistence.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairC;
import com.integral.finance.currency.CurrencyPairObjectC;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.instrument.AmountOfInstrumentC;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TradeCustomFieldC;
import com.integral.finance.trade.TradeFactory;
import com.integral.persistence.CustomField;
import com.integral.persistence.CustomFieldC;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.user.OrganizationCustomFieldC;
import com.integral.user.UserFactory;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Vector;


/**
 * Tests the custom field implementation using a TradeCustomFieldC
 */
public class CustomFieldPTestC
        extends PTestCaseC
{
    static String name = "Custom Field Test";

    public CustomFieldPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }


    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new CustomFieldPTestC( "testListTradeCustomFields" ) );
        suite.addTest( new CustomFieldPTestC( "testTenor" ) );
        suite.addTest( new CustomFieldPTestC( "testCurrencyPair" ) );
        suite.addTest( new CustomFieldPTestC( "testAmountOfInstrument" ) );
        return suite;
    }


    public void testListTradeCustomFields()
    {
        try
        {
            Vector cfs = getPersistenceSession().readAllObjects( TradeCustomFieldC.class );
            for ( int i = 0; i < cfs.size(); i++ )
            {
                CustomFieldC cf = ( CustomFieldC ) cfs.elementAt( i );
                log.info( "CF #" + i + ":\t" + cf );
                log.info( "\t\t\tvalue         = " + cf.getValue() );
                log.info( "\t\t\tobject value  = " + cf.getObjectValue() );
                log.info( "\t\t\tobject type   = " + cf.getObjectType() );
                log.info( "\t\t\ttenor         = " + cf.getTenor() );
                log.info( "\t\t\tcurrency pair = " + cf.getCurrencyPair() );
                log.info( "\t\t\taoi           = " + cf.getAmountOfInstrument() );
            }
            cfs = getPersistenceSession().acquireUnitOfWork().readAllObjects( TradeCustomFieldC.class );
            for ( int i = 0; i < cfs.size(); i++ )
            {
                CustomFieldC cf = ( CustomFieldC ) cfs.elementAt( i );
                log.info( "CF #" + i + ":\t" + cf );
                log.info( "\t\t\tvalue         = " + cf.getValue() );
                log.info( "\t\t\tobject value  = " + cf.getObjectValue() );
                log.info( "\t\t\tobject type   = " + cf.getObjectType() );
                log.info( "\t\t\ttenor         = " + cf.getTenor() );
                log.info( "\t\t\tcurrency pair = " + cf.getCurrencyPair() );
                log.info( "\t\t\taoi           = " + cf.getAmountOfInstrument() );
            }
        }
        catch ( Exception e )
        {
            fail( "testListTradeCustomFields: exception " + e );
        }
    }

    /**
     * Tests storing a tenor as a custom field value
     */
    public void testTenor()
    {
        try
        {
            // setting directly
            Tenor t1 = Tenor.SPOT_TENOR;
            CustomField cf1 = TradeFactory.newTradeCustomField();
            cf1.setKey( "TEST-" + System.currentTimeMillis() );
            cf1.setTenor( t1 );

            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.registerObject( cf1 );
            uow1.commit();

            // setting indirectly
            Tenor t2 = Tenor.SPOT_TENOR;
            CustomField cf2 = TradeFactory.newTradeCustomField();
            cf2.setKey( "TEST-" + System.currentTimeMillis() );
            cf2.setValue( t2 );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            uow2.registerObject( cf2 );
            uow2.commit();
        }
        catch ( Exception e )
        {
            fail( "testTenor: exception " + e );
        }
    }

    /**
     * Tests storing a currency pair as a custom field value
     */
    public void testCurrencyPair()
    {
        try
        {
            Currency usd = getCurrency( "USD" );
            Currency jpy = getCurrency( "JPY" );

            // setting directly
            CurrencyPair cp1 = new CurrencyPairC();
            cp1.setBaseCurrency( usd );
            cp1.setVariableCurrency( jpy );
            CustomField cf1 = TradeFactory.newTradeCustomField();
            cf1.setStatus( 'T' );
            cf1.setKey( "TEST-" + System.currentTimeMillis() );
            cf1.setCurrencyPair( cp1 );


            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.registerObject( cf1 );
            uow1.commit();

            // setting indirectly
            CurrencyPair cp2 = new CurrencyPairC();
            cp2.setBaseCurrency( usd );
            cp2.setVariableCurrency( jpy );
            CustomField cf2 = TradeFactory.newTradeCustomField();
            cf2.setStatus( 'T' );
            cf2.setKey( "TEST-" + System.currentTimeMillis() );
            cf2.setValue( cp2 );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            uow2.registerObject( cf2 );
            uow2.commit();

            // retrieve all and update value
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Session session = PersistenceFactory.newSession();
            UnitOfWork uow3 = session.acquireUnitOfWork();
            Vector cfs = ( Vector ) uow3.readAllObjects( TradeCustomFieldC.class, expr );
            Iterator cfIt = cfs.iterator();
            while ( cfIt.hasNext() )
            {
                CustomField cf = ( CustomField ) cfIt.next();
                CurrencyPair cp = cf.getCurrencyPair();
                if ( cp != null )
                {
                    CurrencyPair newCp = new CurrencyPairC();
                    newCp.setBaseCurrency( cp.getVariableCurrency() );
                    newCp.setVariableCurrency( cp.getBaseCurrency() );
                    cf.setCurrencyPair( newCp );
                }
            }
            uow3.commit();

        }
        catch ( Exception e )
        {
            fail( "testCurrencyPair: exception " + e );
        }
    }

    /**
     * Tests storing an amount of instrument as a custom field value
     */
    public void testAmountOfInstrument()
    {
        try
        {
            Currency usd = getCurrency( "USD" );

            // setting directly
            AmountOfInstrument aoi1 = new AmountOfInstrumentC();
            aoi1.setAmount( 99 );
            aoi1.setInstrument( usd );
            CustomField cf1 = TradeFactory.newTradeCustomField();
            cf1.setKey( "TEST-" + System.currentTimeMillis() );
            cf1.setAmountOfInstrument( aoi1 );

            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.registerObject( cf1 );
            uow1.commit();

            // setting indirectly
            AmountOfInstrument aoi2 = new AmountOfInstrumentC();
            aoi2.setAmount( 66 );
            aoi2.setInstrument( usd );
            CustomField cf2 = TradeFactory.newTradeCustomField();
            cf2.setKey( "TEST-" + System.currentTimeMillis() );
            cf2.setValue( aoi2 );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            uow2.registerObject( cf2 );
            uow2.commit();
        }
        catch ( Exception e )
        {
            fail( "testTenor: exception " + e );
        }
    }

    /**
     * Returns a currency with the given name
     *
     * @param aName
     * @return the currency
     */
    private Currency getCurrency( String aName )
    {
        Currency result = null;

        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( aName );

            Session session = PersistenceFactory.newSession();
            result = ( Currency ) session.readObject( Currency.class, expr );
        }
        catch ( Exception e )
        {
            ;
        }
        return result;
    }

    /**
     * Tests storing a string list as a custom field value
     */
    public void testStringList()
    {
        try
        {
            // setting directly
            List sList1 = new ArrayList( 10 );
            sList1.add( "ABC" );
            sList1.add( "123" );

            CustomField cf1 = UserFactory.newOrganizationCustomField();
            cf1.setKey( "TEST-" + System.currentTimeMillis() );
            cf1.setStringList( sList1 );

            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.registerObject( cf1 );
            uow1.commit();

            // setting indirectly
            List sList2 = new ArrayList( 10 );
            sList2.add( "ABC" );
            sList2.add( "123" );
            CustomField cf2 = UserFactory.newOrganizationCustomField();
            cf2.setKey( "TEST-" + System.currentTimeMillis() );
            cf2.setValue( sList2 );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            uow2.registerObject( cf2 );
            uow2.commit();

            try
            {
                Vector cfs = getPersistenceSession().readAllObjects( OrganizationCustomFieldC.class );
                for ( int i = 0; i < cfs.size(); i++ )
                {
                    CustomFieldC cf = ( CustomFieldC ) cfs.elementAt( i );
                    log.info( "CF #" + i + ":\t" + cf );
                    log.info( "\t\t\tvalue         = " + cf.getValue() );
                    log.info( "\t\t\tobject value  = " + cf.getObjectValue() );
                    log.info( "\t\t\tobject type   = " + cf.getObjectType() );
                    log.info( "\t\t\ttenor         = " + cf.getTenor() );
                    log.info( "\t\t\tcurrency pair = " + cf.getCurrencyPair() );
                    log.info( "\t\t\taoi           = " + cf.getAmountOfInstrument() );
                    log.info( "\t\t\tstring list   = " + cf.getStringList() );
                }
                cfs = getPersistenceSession().acquireUnitOfWork().readAllObjects( TradeCustomFieldC.class );
                for ( int i = 0; i < cfs.size(); i++ )
                {
                    CustomFieldC cf = ( CustomFieldC ) cfs.elementAt( i );
                    log.info( "CF #" + i + ":\t" + cf );
                    log.info( "\t\t\tvalue         = " + cf.getValue() );
                    log.info( "\t\t\tobject value  = " + cf.getObjectValue() );
                    log.info( "\t\t\tobject type   = " + cf.getObjectType() );
                    log.info( "\t\t\ttenor         = " + cf.getTenor() );
                    log.info( "\t\t\tcurrency pair = " + cf.getCurrencyPair() );
                    log.info( "\t\t\taoi           = " + cf.getAmountOfInstrument() );
                    log.info( "\t\t\tstring list   = " + cf.getStringList() );
                }
            }
            catch ( Exception e )
            {
                fail( "testListOrgCustomFields: exception " + e );
            }

        }
        catch ( Exception e )
        {
            fail( "testTenor: exception " + e );
        }
    }

    /**
     * Tests storing a currency pair as a custom field value
     */
    public void testCurrencyPairObject()
    {
        try
        {
            Currency usd = getCurrency( "USD" );
            Currency jpy = getCurrency( "JPY" );

            // setting directly
            CurrencyPair cp1 = new CurrencyPairObjectC( usd, jpy );
            CustomField cf1 = TradeFactory.newTradeCustomField();
            cf1.setStatus( 'T' );
            cf1.setKey( "TEST-" + System.currentTimeMillis() );
            cf1.setCurrencyPair( cp1 );


            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.registerObject( cf1 );
            uow1.commit();

            // setting indirectly
            CurrencyPair cp2 = new CurrencyPairObjectC( usd, jpy );
            CustomField cf2 = TradeFactory.newTradeCustomField();
            cf2.setStatus( 'T' );
            cf2.setKey( "TEST-" + System.currentTimeMillis() );
            cf2.setValue( cp2 );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            uow2.registerObject( cf2 );
            uow2.commit();

            // retrieve all and update value
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Session session = PersistenceFactory.newSession();
            UnitOfWork uow3 = session.acquireUnitOfWork();
            Vector cfs = uow3.readAllObjects( TradeCustomFieldC.class, expr );
            Iterator cfIt = cfs.iterator();
            while ( cfIt.hasNext() )
            {
                CustomField cf = ( CustomField ) cfIt.next();
                CurrencyPair cp = cf.getCurrencyPair();
                if ( cp != null )
                {
                    CurrencyPair newCp = new CurrencyPairObjectC( cp.getBaseCurrency(), cp.getVariableCurrency() );
                    cf.setCurrencyPair( newCp );
                }
            }
            uow3.commit();

        }
        catch ( Exception e )
        {
            fail( "testCurrencyPair: exception " + e );
        }
    }

}
