package com.integral.persistence.test;

import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.TransactionIdService;
import com.integral.persistence.TransactionIdServiceC;
import com.integral.persistence.TransactionIdServiceFactory;
import com.integral.persistence.configuration.EntityServiceConfigurationFactory;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.Record;
import org.eclipse.persistence.sessions.Session;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashSet;
import java.util.Vector;

public class TransactionIdServicePTestC extends PTestCaseC
{
    public TransactionIdServicePTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest(new TransactionIdServicePTestC("testTransactionIdAndOrderId"));
        suite.addTest(new TransactionIdServicePTestC("testTransactionId"));
        suite.addTest(new TransactionIdServicePTestC("testOrderId"));
        suite.addTest(new TransactionIdServicePTestC("testTransactionIdAsyncPreFetch"));
        suite.addTest(new TransactionIdServicePTestC("testOrderIdAsyncPreFetch"));
        return suite;
    }

    /**
     * This test case is used to ensure that order and trade transaction id sequences follow the individual sequences properly.
     */
    public void testTransactionIdAndOrderId()
    {
        try
        {
            // create a random mismatch between the order id and tx id sequences.
            int randomNum = 5 + ( int ) ( Math.random() * 10.0 );
            for ( int i = 0; i < randomNum; i++ )
            {
                getNextValueOfSequence( "IDCTRANSACTIONID_IS_SEQ" );
            }

            TransactionIdService ts = TransactionIdServiceFactory.getTransactionIdService();
            String oid1 = ts.getOrderID( "IS" );
            assertNotNull( oid1 );
            long orderId1 = Long.parseLong( oid1 );

            String tid1 = ts.getID( "IS" );
            assertNotNull( tid1 );
            long txId1 = getTransactionIdNumber( tid1 );
            log( "oid1=" + oid1 + ",tid1=" + tid1 );

            String tid2 = ts.getID( "IS" );
            String oid2 = ts.getOrderID( "IS" );
            log( "oid2=" + oid2 + ",tid2=" + tid2 );
            long orderId2 = Long.parseLong( oid2 );
            long txId2 = getTransactionIdNumber( tid2 );
            assertTrue( txId2 - txId1 == 1 );
            assertTrue( orderId2 - orderId1 == 1 );

            String tid3 = ts.getID( "IS" );
            String oid3 = ts.getOrderID( "IS" );
            log( "oid3=" + oid3 + ",tid3=" + tid3 );
            long orderId3 = Long.parseLong( oid3 );
            long txId3 = getTransactionIdNumber( tid3 );
            assertTrue( txId3 - txId2 == 1 );
            assertTrue( orderId3 - orderId2 == 1 );

            long initialTid = txId3;
            long initialOid = orderId3;
            for ( int i = 0; i < TransactionIdServiceC.SEQUENCE_PREALLOCATION_SIZE * 10; i++ )
            {
                if ( i > TransactionIdServiceC.SEQUENCE_PREALLOCATION_SIZE / 2 )
                {
                    Thread.sleep( 10 );
                }
                String tid = ts.getID( "IS" );
                String oid = ts.getOrderID( "IS" );
                log( "oid=" + oid + ",tid=" + tid );
                long orderId = Long.parseLong( oid );
                long txId = getTransactionIdNumber( tid );
                assertTrue( txId - initialTid == 1 );
                assertTrue( orderId - initialOid == 1 );
                initialOid = orderId;
                initialTid = txId;
            }

        }
        catch ( Exception e )
        {
            fail( "testTransactionIdAndOrderId", e );
        }
    }

    public void testTransactionId()
    {
        try
        {
            TransactionIdService ts = TransactionIdServiceFactory.getTransactionIdService();
            String tid = ts.getID( "IS" );
            log( tid );
            assertNotNull( tid );

            String tid1 = ts.getID( "IS" );
            log( tid1 );
            assertNotNull( tid1 );
        }
        catch ( Exception e )
        {
            fail( "testTransactionId", e );
        }
    }

    public void testOrderId()
    {
        try
        {
            TransactionIdService ts = TransactionIdServiceFactory.getTransactionIdService();
            String oid = ts.getOrderID( "IS" );
            log( oid );
            assertNotNull( oid );

            String oid1 = ts.getOrderID( "IS" );
            log( oid1 );
            assertNotNull( oid1 );
        }
        catch ( Exception e )
        {
            fail( "testOrderId", e );
        }
    }

    public void testTransactionIdAsyncPreFetch()
    {
        try
        {
            TransactionIdService ts = TransactionIdServiceFactory.getTransactionIdService();
            Collection<String> tids = new HashSet<String>( 300 );
            String tid0 = ts.getID( "IS" );
            log( tid0 );
            tids.add( tid0 );
            getNextValueOfSequence( "IDCTRANSACTIONID_IS_SEQ" );
            for ( int i = 0; i < TransactionIdServiceC.SEQUENCE_PREALLOCATION_SIZE * 3; i++ )
            {
                String tid = ts.getID( "IS" );
                log( tid );
                assertNotNull( tid );
                if ( i > TransactionIdServiceC.SEQUENCE_PREALLOCATION_SIZE / 2 )
                {
                    Thread.sleep( 10 );
                }
                assertFalse( tids.contains( tid ) );
                tids.add( tid );
            }
        }
        catch ( Exception e )
        {
            fail( "testTransactionIdAsyncPreFetch", e );
        }
    }

    public void testOrderIdAsyncPreFetch()
    {
        try
        {
            TransactionIdService ts = TransactionIdServiceFactory.getTransactionIdService();
            Collection<String> oids = new HashSet<String>( 300 );
            String oid0 = ts.getOrderID( "IS" );
            log( oid0 );
            oids.add( oid0 );
            getNextValueOfSequence( "IDCFXIORDERID_IS_SEQ" );
            for ( int i = 0; i < TransactionIdServiceC.SEQUENCE_PREALLOCATION_SIZE * 3; i++ )
            {
                String oid = ts.getOrderID( "IS" );
                log( oid );
                assertNotNull( oid );
                if ( i > TransactionIdServiceC.SEQUENCE_PREALLOCATION_SIZE / 2 )
                {
                    Thread.sleep( 10 );
                }
                assertFalse( oids.contains( oid ) );
                oids.add( oid );
            }
        }
        catch ( Exception e )
        {
            fail( "testOrderIdAsyncPreFetch", e );
        }
    }

    private void getNextValueOfSequence( String sequenceName )
    {
        try
        {
            String sql = "SELECT " + sequenceName + ".NEXTVAL from DUAL";
            Session session = PersistenceFactory.newSession();
            Vector resultSet = session.executeSQL( sql );
            Record row = ( Record ) resultSet.elementAt( 0 );
            BigDecimal val = ( BigDecimal ) row.get( "NEXTVAL" );
            log.info( new StringBuilder( 200 ).append( "getNextValueOfSequence : executing sql=" ).append( sql )
                    .append( ",newValue=" ).append( val ).toString() );
        }
        catch ( Exception e )
        {
            log.error( "getNextValueOfSequence", e );
        }
    }

    private long getTransactionIdNumber( String txIdStr )
    {
        long txId = 0;
        String transactionIdPrefix = EntityServiceConfigurationFactory.getEntityServiceConfigurationMBean().getTransactionIdPrefix();
        if ( txIdStr.startsWith( transactionIdPrefix ) )
        {
            txIdStr = txIdStr.replace( transactionIdPrefix, "" );
            txId = Long.parseLong( txIdStr );
        }
        txId = Long.parseLong( txIdStr );
        return txId;
    }
}