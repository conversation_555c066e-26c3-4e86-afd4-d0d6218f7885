package com.integral.persistence.test;

import com.integral.persistence.Entity;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

/**
 * Tests whether classes in the com.integral.user package persist properly
 */
public class TemplatePersistencePTestC
        extends PTestCaseC
{
    public TemplatePersistencePTestC( String aName )
    {
        super( aName );
    }


    /**
     * Tests persistance of User
     */
    public void testUserPersistence()
    {
        Class clz = UserC.class;

        log( "test persistence of " + clz.getName() );

        // insert
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();

            User entity = new UserC();
            entity.setShortName( "TEST" );
            entity.setStatus( 'T' );

            uow.registerObject( entity );

            uow.commit();
            log( "test insert of " + clz.getName() );
        }
        catch ( Exception e )
        {
            fail( "test insert " + clz.getName() );
            e.printStackTrace();
        }

        // query
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = ( Vector ) uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tretrieved " + entity );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            fail( "test query " + clz.getName() );
            e.printStackTrace();
        }

        // delete
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = ( Vector ) uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tdeleting " + entity );
                uow.deleteObject( entity );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "test delete " + clz.getName() );
            e.printStackTrace();
        }

    }
}
