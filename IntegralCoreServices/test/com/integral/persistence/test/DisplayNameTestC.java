package com.integral.persistence.test;


import com.integral.persistence.DisplayNameUtilC;
import com.integral.persistence.PersistenceFactory;
import com.integral.user.User;
import com.integral.user.UserC;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

public class DisplayNameTestC
        extends TestCase
{
    public DisplayNameTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new DisplayNameTestC( "testUserDisplayName" ) );
        return suite;
    }

    public void testUserDisplayName()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            User joe = ( User ) PersistenceFactory.newSession().readObject( User.class, expr );

            String dn = DisplayNameUtilC.getDisplayName( joe );
            System.out.println( "DISPLAY NAME FOR User Integral: " + dn );

            String sn = DisplayNameUtilC.getObject( UserC.class, dn );
            System.out.println( "REVERSE DISPLAY NAME FOR User Integral: " + sn );
        }
        catch ( Exception e )
        {
            fail( "Exception e" );
        }


    }

}
