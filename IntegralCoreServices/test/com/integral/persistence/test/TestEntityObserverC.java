package com.integral.persistence.test;

import com.integral.persistence.Entity;
import com.integral.persistence.EntityObserver;

import java.util.Date;
import java.util.List;

/**
 * Private helper class as a entity observe
 */

public class TestEntityObserverC implements EntityObserver
{
    private List<Date> updates = null;

    public TestEntityObserverC( List<Date> list )
    {
        this.updates = list;
    }

    public void onUpdate( Entity anEntity )
    {
        this.updates.add( new Date() );
        System.out.println( "TestEntityObserverC.onUpdate() called for entity " + anEntity + " with hashCode " + anEntity.hashCode() );
    }

    public List<Date> getUpdates()
    {
        return updates;
    }
}
