package com.integral.persistence.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;

import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * Private helper class as a cache synchronization observer
 */
class CacheSyncObserverC implements MessageListener
{
    protected Log log = LogFactory.getLog( this.getClass() );

    public CacheSyncObserverC()
    {
    }

    public void onMessage( Message aMessage )
    {
        log.info( "  CacheSyncObserverC.onMessage() called" );
        try
        {
            // get changes
            ObjectMessage om = ( ObjectMessage ) aMessage;

            List changes = ( ArrayList ) om.getObject();
            Map additions = ( Map ) changes.get( 0 );
            Map modifications = ( Map ) changes.get( 1 );
            Map deletions = ( Map ) changes.get( 2 );

            printItems( "Additions", additions );
            printItems( "Modification", modifications );
            printItems( "Deletions", deletions );
        }
        catch ( Exception e )
        {
            log.debug( "CacheSyncObserverC: error when processing message " + aMessage, e );
        }
    }

    private void printItems( String title, Map additions )
    {
        log.info( "    " + title );
        Iterator classIt = additions.keySet().iterator();
        while ( classIt.hasNext() )
        {
            String clzName = ( String ) classIt.next();
            try
            {
                Class clz = Class.forName( clzName );

                Iterator tupelIt = ( ( List ) additions.get( clzName ) ).iterator();
                while ( tupelIt.hasNext() )
                {
                    List tupel = ( List ) tupelIt.next();
                    Long oid = ( Long ) tupel.get( 0 );
                    Long version = ( Long ) tupel.get( 1 );

                    log.info( "      " + clzName + ':' + oid + ':' + version );
                }
            }
            catch ( Exception e )
            {

            }
        }
    }
}
