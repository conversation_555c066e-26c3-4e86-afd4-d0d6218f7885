package com.integral.persistence.test;


import com.integral.jmsx.JMSManager;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemId;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.user.UserContact;
import com.integral.user.UserContactC;
import com.integral.user.UserExternalSystemIdC;
import com.integral.user.UserPermission;
import com.integral.user.UserPermissionC;
import com.integral.user.UserRole;
import com.integral.user.UserRoleC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import javax.jms.MessageListener;


/**
 * Tests the cache synchronization framework for persistent entities.
 */
public class CacheSynchronizationPTestC
        extends PTestCaseC
{
    static String name = "CacheSynchronization Test";
    private MessageListener listener = new CacheSyncObserverC();

    public CacheSynchronizationPTestC( String name )
    {
        super( name );
    }

    public void setUp() throws Exception
    {
        super.setUp();

        try
        {
            JMSManager.getInstance().setMessageListenerForDestination( "TOPIC.ENTITYCACHE", listener, null );
        }
        catch ( Exception e )
        {
            fail( "unable to register listener", e );
        }
    }

    /**
     * Tests adding a new entity
     */
    public void testNewObject()
    {

        User user = new UserC();
        user.setShortName( "TestUser" + System.currentTimeMillis() );
        user.setStatus( 'T' );

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();
        uow.registerObject( user );
        uow.commit();

        try
        {
            Thread.sleep( 2000 );
        }
        catch ( Exception e )
        {
            ;
        }
    }

    /**
     * Tests updating a simple field on an existing entity
     */
    public void testUpdateDirectField()
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "status" ).equal( 'T' );
        User user = ( User ) getPersistenceSession().readObject( User.class, expr );

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        User clone = ( User ) uow.registerObject( user );
        clone.setDescription( "TestDesc-" + System.currentTimeMillis() );

        uow.commit();

        try
        {
            Thread.sleep( 2000 );
        }
        catch ( Exception e )
        {
            ;
        }
    }

    /**
     * Tests adding a one-to-one relationship on an existing entity to a new entity
     */
    public void testAddOneToOne()
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "status" ).equal( 'T' );
        User user = ( User ) getPersistenceSession().readObject( User.class, expr );

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        User clone = ( User ) uow.registerObject( user );
        UserContact contact = new UserContactC();
        contact.setStatus( 'T' );
        contact.setShortName( "TestContact" + System.currentTimeMillis() );
        clone.setContact( contact );

        uow.commit();

        try
        {
            Thread.sleep( 2000 );
        }
        catch ( Exception e )
        {
            ;
        }
    }

    /**
     * Tests adding a one-to-many relationship on an existing entity to an existing entity
     */
    public void testAddOneToMany()
    {
        String name = "TestOnetoMany" + System.currentTimeMillis();

        // query external system
        ExternalSystem sys = ( ExternalSystem ) getPersistenceSession().readObject( ExternalSystem.class );

        // add entities
        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        User user = new UserC();
        user.setStatus( 'T' );
        user.setShortName( name );
        uow.registerObject( user );

        UserExternalSystemIdC extid = new UserExternalSystemIdC();
        extid.setStatus( 'T' );
        extid.setName( name );
        extid.setExternalSystem( ( ( ExternalSystem ) uow.registerObject( sys ) ) );
        uow.registerObject( extid );

        uow.commit();

        // query entities
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).equal( name );
        user = ( User ) getPersistenceSession().readObject( User.class, expr );
        log.info( "user =" + user );

        ExpressionBuilder eb1 = new ExpressionBuilder();
        Expression expr1 = eb1.get( "name" ).equal( name );
        extid = ( UserExternalSystemIdC ) getPersistenceSession().readObject( UserExternalSystemIdC.class, expr1 );
        log.info( "extid = " + extid );

        // modify them
        uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        User userClone = ( User ) uow.registerObject( user );
        ExternalSystemId extidClone = ( ExternalSystemId ) uow.registerObject( extid );
        userClone.addExternalSystemId( extidClone );
        extidClone.setOwner( userClone );

        // should not need this
        // roleClone.update();
        // permClone.update();

        uow.commit();

        try
        {
            Thread.sleep( 2000 );
        }
        catch ( Exception e )
        {
            ;
        }
    }

    /**
     * Tests adding a many-to-many relationship on an existing entity to an existing entity
     */
    public void testAddManyToMany()
    {
        String name = "TestManytoMany" + System.currentTimeMillis();

        // add entities
        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        UserRole role = new UserRoleC();
        role.setStatus( 'T' );
        role.setShortName( name );
        uow.registerObject( role );

        UserPermission perm = new UserPermissionC();
        perm.setStatus( 'T' );

        perm.setShortName( name );
        uow.registerObject( perm );

        uow.commit();

        // query entities
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).equal( name );
        role = ( UserRole ) getPersistenceSession().readObject( UserRole.class, expr );
        perm = ( UserPermission ) getPersistenceSession().readObject( UserPermission.class, expr );
        log.info( "role =" + role );
        log.info( "perm = " + perm );

        // modify them
        uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        UserRole roleClone = ( UserRole ) uow.registerObject( role );
        UserPermission permClone = ( UserPermission ) uow.registerObject( perm );

        //roleClone.addUserPermission(permClone);
        //permClone.addUserRole(roleClone);
        roleClone.getUserPermissions().add( permClone );
        permClone.getUserRoles().add( roleClone );

        // should not need this
        // roleClone.update();
        // permClone.update();

        uow.commit();

        try
        {
            Thread.sleep( 2000 );
        }
        catch ( Exception e )
        {
            ;
        }
    }
}
