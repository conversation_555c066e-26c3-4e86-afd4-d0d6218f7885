package com.integral.persistence.test;


import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.persistence.Entity;
import com.integral.persistence.EntityObserver;
import com.integral.persistence.NotificationManager;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * Tests the notification manager framework for persistent entities.
 */
public class NotificationManagerPTestC
        extends PTestCaseC
{
    static String name = "NotificationManager Test";

    public NotificationManagerPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }


    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        //suite.addTest(new NotificationManagerPTestC("testNotificationManager"));
        suite.addTest( new NotificationManagerPTestC( "testNotificationOnNewObject" ) );
        //suite.addTest(new NotificationManagerPTestC("testNotificationOnExistingObject"));
        //suite.addTest(new NotificationManagerPTestC("testNotificationGC"));
        return suite;
    }

    /**
     * Tests the core notification manager
     */
    public void testNotificationManager()
    {
        NotificationManager manager = PersistenceFactory.getNotificationManager();

        Entity entity = new UserC();
        List updates = new ArrayList();
        EntityObserver observer = new TestEntityObserverC( updates );

        // test simple notification
        updates.clear();
        manager.addUpdateObserver( entity, observer );
        assertEquals( 0, updates.size() );
        manager.notifyUpdateObservers( entity );
        assertEquals( 1, updates.size() );

        // test that registering the same observer twice will only
        // once call the observer
        updates.clear();
        manager.addUpdateObserver( entity, observer );
        manager.notifyUpdateObservers( entity );
        assertEquals( 1, updates.size() );

        // test unregistering
        updates.clear();
        manager.removeUpdateObserver( entity, observer );
        manager.notifyUpdateObservers( entity );
        assertEquals( 0, updates.size() );

        // test weak references in manager by removing observer
        // and invoking GC
        updates.clear();
        manager.addUpdateObserver( entity, observer );
        manager.notifyUpdateObservers( entity );
        assertEquals( 1, updates.size() );
        observer = null;
        System.gc();
        System.gc();
        System.gc();
        manager.notifyUpdateObservers( entity );
        assertEquals( 1, updates.size() );
    }

    /**
     * Tests whether a simple update of a new entity triggers a notification.
     * The tests creates an audit event
     */
    public void testNotificationOnNewObject()
    {
        // create audit event
        AuditEvent event = new AuditEventC( "TEST", "NotificationTest" );

        // register an event listener
        List updates = new ArrayList();
        TestEntityObserverC observer = new TestEntityObserverC( updates );
        event.addUpdateObserver( observer );

        // register entity and commit new entity
        try
        {
            log.info( "start commiting new audit object" + " with hashCode " + event.hashCode() );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            AuditEvent event1 = ( AuditEvent ) uow.registerObject( event );
            event1.setStringArg1( "Test" );
            uow.commit();
            log.info( "  completed commiting new audit object" + " with hashCode " + event.hashCode() + ",updates.size()=" + updates.size() );
            assertEquals( 1, observer.getUpdates().size());
        }
        catch ( Exception e )
        {
            fail( "testNotificationOnNewObject", e );
        }

    }

    /**
     * Tests whether a simple update of an existing entity triggers a notification.
     * The tests updates the user Integral of class UserC.class
     */
    public void testNotificationOnExistingObject()
    {
        User user = null;

        // fetch user Integral
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            Session session = PersistenceFactory.newSession();
            user = ( User ) session.readObject( User.class, expr );
            log.info( "using user : " + user + " with hashCode " + user.hashCode() );
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user ", e );
            return;
        }

        // register an event listener
        List updates = new ArrayList();
        EntityObserver observer = new TestEntityObserverC( updates );
        user.addUpdateObserver( observer );

        // refresh entity
        try
        {
            updates.clear();
            log.info( "start refreshing user object" + " with hashCode " + user.hashCode() );
            log.info( "  completed refreshing user object" + " with hashCode " + user.hashCode() );
            assertEquals( 0, updates.size() );
        }
        catch ( Exception e )
        {
        }

        // register entity only
        try
        {
            updates.clear();
            log.info( "start registering user object" + " with hashCode " + user.hashCode() );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            log.info( "  completed registering user object" + " with hashCode " + user.hashCode() );
            assertEquals( 0, updates.size() );
        }
        catch ( Exception e )
        {
        }

        // register entity and commit without any changes
        try
        {
            updates.clear();
            log.info( "start commiting unmodified user object" + " with hashCode " + user.hashCode() );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.commit();
            log.info( "  completed commiting unmodified user object" + " with hashCode " + user.hashCode() );
            assertEquals( 0, updates.size() );
        }
        catch ( Exception e )
        {
        }

        // update entity with commit
        try
        {
            updates.clear();
            log.info( "start commiting modified user object" + " with hashCode " + user.hashCode() );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            User user1 = ( User ) uow.registerObject( user );
            user1.setDescription( "Test Description @" + ( new Date() ) );
            //  user1.update();
            uow.commit();
            log.info( "  completed commiting modified user object" + " with hashCode " + user.hashCode() );
            assertEquals( 1, updates.size() );
        }
        catch ( Exception e )
        {
        }

        // update entity with rollback
        try
        {
            updates.clear();
            log.info( "start rolling back modified user object" + " with hashCode " + user.hashCode() );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            User user1 = ( User ) uow.registerObject( user );
            user1.update();
            uow.release();
            log.info( "  completed rolling back modified user object" + " with hashCode " + user.hashCode() );
            assertEquals( 0, updates.size() );
        }
        catch ( Exception e )
        {
        }
    }


    /**
     * Tests the GC facility in the notification manager
     */
    public void testNotificationGC()
    {
        // activate notification manager
        NotificationManager manager = PersistenceFactory.getNotificationManager();

        // add an observer
        Entity entity = new UserC();
        List updates = new ArrayList();
        EntityObserver observer = new TestEntityObserverC( updates );
        manager.addUpdateObserver( entity, observer );

        // set the observer to null and call GC
        try
        {
            observer = null;
            System.gc();
            System.gc();
            System.gc();
        }
        catch ( Exception e )
        {
        }

        // sleep
        try
        {
            log.info( "Start sleeping for 5 minutes - you should see at least 4 GC messages" );
            Thread.sleep( 5 * 60 * 1000 );
            log.info( "Stop sleeping" );
        }
        catch ( Exception e )
        {
        }
    }
}
