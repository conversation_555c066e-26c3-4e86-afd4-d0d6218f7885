package com.integral.persistence.test;

import com.integral.finance.currency.CurrencyC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRateConvention;
import com.integral.messaging.EntityUpdateMessage;
import com.integral.messaging.PayloadType;
import com.integral.messaging.RMQMessage;
import com.integral.persistence.*;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.configuration.EntityServiceConfigurationMBean;
import com.integral.persistence.util.RemoteFunctorUtilC;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;

import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.*;

public class MultiappUpdateHandlerPTestC extends PTestCaseC
{
    private EntityServiceC _es = ( EntityServiceC ) EntityServiceFactory.getEntityService();

    public MultiappUpdateHandlerPTestC( String name )
    {
        super( name );
    }

    protected void setUp()
    {
        WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_AUXILIARY_REFERENCE_DATA_UPDATE_PERIOD, "20", ConfigurationProperty.DYNAMIC_SCOPE );
        EntityServiceC.initializeAuxiliaryReferenceDataUpdateTask();
    }


    public void testMultiappUpdateHandlingPolicyHandleAll()
    {
        IdcTransaction tx = null;
        try
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "0", ConfigurationProperty.DYNAMIC_SCOPE );
            Organization fi1 = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
            Organization fi2 = ReferenceDataCacheC.getInstance().getOrganization( "FI2" );
            fi1 = (Organization) IdcUtilC.refreshObject( fi1 );
            fi2 = (Organization) IdcUtilC.refreshObject( fi2 );
            Collection<Long> provisionedNamespaces = new HashSet<Long>();
            provisionedNamespaces.add( ReferenceDataCacheC.getInstance().getMAINNamespace().getObjectID() );
            provisionedNamespaces.add ( fi1.getNamespace().getObjectID() );
            ReferenceDataCacheC.getInstance().setProvisionedNamespaceIds( provisionedNamespaces );

            long fi1Ver = fi1.getVersion();
            long fi2Ver = fi2.getVersion();

            // update version of fi1 and fi2.
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi1Ver + 1) + " WHERE ID=" + fi1.getObjectID() );
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi2Ver + 1) + " WHERE ID=" + fi2.getObjectID() );

            ReferenceDataEntityCacheSynchronizationC sync = new ReferenceDataEntityCacheSynchronizationC();
            EntityUpdateMessage msg = createEntityUpdateMessage( fi1, fi2  );

            HashMap props = new HashMap();
            long now = System.currentTimeMillis();
            String key = "TestParamKey" + now;
            String value = "TestParamValue" + now;
            props.put( key, value );
            tx = IdcSessionManager.getInstance().newFakeTransaction(IdcUtilC.getSessionContextUser(), "testMultiappUpdateHandlingPolicyHandleAll" );
            RemoteFunctorUtilC.addRemoteFunctor( TestRemoteNotificationFunctor.class.getName(), props, tx.getUOW() );
            ArrayList functorNames = ( ArrayList ) tx.getUOW().getProperty(RemoteFunctorUtilC.REMOTE_FUNCTOR_NAMES_PROP);
            msg.setFunctorNames( functorNames );

            RMQMessage message = new RMQMessage("dummy", "dummy",
					"TestBindingKey", 0, msg, PayloadType.OBJECT_PAYLOAD,
					false, System.currentTimeMillis());
            sync.onMessage(message);

            Thread.sleep( 1000 );
            long fi1Ver1 = fi1.getVersion();
            long fi2Ver1 = fi2.getVersion();
            assertTrue( fi1Ver1 > fi1Ver );
            assertTrue( fi2Ver1 > fi2Ver );
            assertNotNull( TestRemoteNotificationFunctor.threadMap.get( key ));
        }
        catch ( Exception e )
        {
            fail( "testMultiappUpdateHandlingPolicyHandleAll", e );
        }
        finally
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "0", ConfigurationProperty.DYNAMIC_SCOPE );
            if ( tx != null )
            {
                tx.release();
            }
            _es.getAuxReferenceDataEntityUpdateTask().clear();
        }
    }

    public void testUnprovisionedOnNonRefDataThreadPolicy()
    {
        IdcTransaction tx = null;
        try
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "1", ConfigurationProperty.DYNAMIC_SCOPE );

            Organization fi1 = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
            Organization fi2 = ReferenceDataCacheC.getInstance().getOrganization( "FI2" );
            fi1 = (Organization) IdcUtilC.refreshObject( fi1 );
            fi2 = (Organization) IdcUtilC.refreshObject( fi2 );
            Collection<Long> provisionedNamespaces = new HashSet<Long>();
            provisionedNamespaces.add( ReferenceDataCacheC.getInstance().getMAINNamespace().getObjectID() );
            provisionedNamespaces.add ( fi1.getNamespace().getObjectID() );
            ReferenceDataCacheC.getInstance().setProvisionedNamespaceIds( provisionedNamespaces );

            long fi2Ver = fi2.getVersion();

            // update version of fi1 and fi2.
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi2Ver + 1) + " WHERE ID=" + fi2.getObjectID() );

            ReferenceDataEntityCacheSynchronizationC sync = new ReferenceDataEntityCacheSynchronizationC();
            EntityUpdateMessage msg = createEntityUpdateMessage( fi2  );

            HashMap props = new HashMap();
            long now = System.currentTimeMillis();
            String key = "TestParamKey" + now;
            String value = "TestParamValue" + now;
            props.put( key, value );
            tx = IdcSessionManager.getInstance().newFakeTransaction(IdcUtilC.getSessionContextUser(), "testUnprovisionedOnNonRefDataThreadPolicy" );
            RemoteFunctorUtilC.addRemoteFunctor( TestRemoteNotificationFunctor.class.getName(), props, tx.getUOW() );
            ArrayList functorNames = ( ArrayList ) tx.getUOW().getProperty(RemoteFunctorUtilC.REMOTE_FUNCTOR_NAMES_PROP);
            msg.setFunctorNames( functorNames );

			RMQMessage message = new RMQMessage("dummy", "dummy",
					"TestBindingKey", 0, msg, PayloadType.OBJECT_PAYLOAD,
					false, System.currentTimeMillis());
			sync.onMessage(message);

            Thread.sleep( 1000 );
            long fi2Ver1 = fi2.getVersion();
            assertTrue( fi2Ver1 > fi2Ver );
            String threadName = TestRemoteNotificationFunctor.threadMap.get(key);
            log.warn("##################### thread name=" + threadName);
            assertNotNull( threadName );
            assertTrue( threadName.contains("MultiAppEntityUpdateThread" ));
        }
        catch ( Exception e )
        {
            fail( "testUnprovisionedOnSingleThreadPolicy", e );
        }
        finally
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "0", ConfigurationProperty.DYNAMIC_SCOPE );
            if ( tx != null )
            {
                tx.release();
            }
            _es.getAuxReferenceDataEntityUpdateTask().clear();
        }
    }


    public void testUnprovisionedOnSingleThreadPolicy()
    {
        IdcTransaction tx = null;
        try
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "2", ConfigurationProperty.DYNAMIC_SCOPE );

            Organization fi1 = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
            Organization fi2 = ReferenceDataCacheC.getInstance().getOrganization( "FI2" );
            fi1 = (Organization) IdcUtilC.refreshObject( fi1 );
            fi2 = (Organization) IdcUtilC.refreshObject( fi2 );
            Collection<Long> provisionedNamespaces = new HashSet<Long>();
            provisionedNamespaces.add( ReferenceDataCacheC.getInstance().getMAINNamespace().getObjectID() );
            provisionedNamespaces.add ( fi1.getNamespace().getObjectID() );
            ReferenceDataCacheC.getInstance().setProvisionedNamespaceIds( provisionedNamespaces );

            long fi2Ver = fi2.getVersion();

            // update version of fi1 and fi2.
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi2Ver + 1) + " WHERE ID=" + fi2.getObjectID() );

            ReferenceDataEntityCacheSynchronizationC sync = new ReferenceDataEntityCacheSynchronizationC();
            EntityUpdateMessage msg = createEntityUpdateMessage( fi2  );

            HashMap props = new HashMap();
            long now = System.currentTimeMillis();
            String key = "TestParamKey" + now;
            String value = "TestParamValue" + now;
            props.put( key, value );
            tx = IdcSessionManager.getInstance().newFakeTransaction(IdcUtilC.getSessionContextUser(), "testUnprovisionedOnSingleThreadPolicy" );
            RemoteFunctorUtilC.addRemoteFunctor( TestRemoteNotificationFunctor.class.getName(), props, tx.getUOW() );
            ArrayList functorNames = ( ArrayList ) tx.getUOW().getProperty(RemoteFunctorUtilC.REMOTE_FUNCTOR_NAMES_PROP);
            msg.setFunctorNames( functorNames );

            RMQMessage message = new RMQMessage("dummy", "dummy",
					"TestBindingKey", 0, msg, PayloadType.OBJECT_PAYLOAD,
					false, System.currentTimeMillis());
            sync.onMessage(message);

            Thread.sleep( 1000 );
            long fi2Ver1 = fi2.getVersion();
            assertTrue( fi2Ver1 > fi2Ver );
            String threadName = TestRemoteNotificationFunctor.threadMap.get(key);
            log.warn("##################### thread name=" + threadName);
            assertNotNull( threadName );
            assertTrue( threadName.equals( EntityServiceC.AUXILIARY_REFERENCE_DATA_UPDATE_THREAD ));
        }
        catch ( Exception e )
        {
            fail( "testUnprovisionedOnSingleThreadPolicy", e );
        }
        finally
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "0", ConfigurationProperty.DYNAMIC_SCOPE );
            if ( tx != null )
            {
                tx.release();
            }
            _es.getAuxReferenceDataEntityUpdateTask().clear();
        }
    }

    public void testMultiappUpdateHandlingPolicyDropUnprovisioned()
    {
        IdcTransaction tx = null;
        try
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "4", ConfigurationProperty.DYNAMIC_SCOPE );
            Organization fi1 = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
            Organization fi2 = ReferenceDataCacheC.getInstance().getOrganization( "FI2" );
            fi1 = (Organization) IdcUtilC.refreshObject( fi1 );
            fi2 = (Organization) IdcUtilC.refreshObject( fi2 );
            Collection<Long> provisionedNamespaces = new HashSet<Long>();
            provisionedNamespaces.add( ReferenceDataCacheC.getInstance().getMAINNamespace().getObjectID() );
            provisionedNamespaces.add ( fi1.getNamespace().getObjectID() );
            ReferenceDataCacheC.getInstance().setProvisionedNamespaceIds( provisionedNamespaces );

            long fi1Ver = fi1.getVersion();
            long fi2Ver = fi2.getVersion();

            // update version of fi1 and fi2.
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi1Ver + 1) + " WHERE ID=" + fi1.getObjectID() );
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi2Ver + 1) + " WHERE ID=" + fi2.getObjectID() );

            ReferenceDataEntityCacheSynchronizationC sync = new ReferenceDataEntityCacheSynchronizationC();
            EntityUpdateMessage msg = createEntityUpdateMessage( fi2  );

            HashMap props = new HashMap();
            long now = System.currentTimeMillis();
            String key = "TestParamKey" + now;
            String value = "TestParamValue" + now;
            props.put( key, value );
            tx = IdcSessionManager.getInstance().newFakeTransaction(IdcUtilC.getSessionContextUser(), "testMultiappUpdateHandlingPolicyDropUnprovisioned" );
            RemoteFunctorUtilC.addRemoteFunctor( TestRemoteNotificationFunctor.class.getName(), props, tx.getUOW() );
            ArrayList functorNames = ( ArrayList ) tx.getUOW().getProperty(RemoteFunctorUtilC.REMOTE_FUNCTOR_NAMES_PROP);
            msg.setFunctorNames( functorNames );

            RMQMessage message = new RMQMessage("dummy", "dummy",
					"TestBindingKey", 0, msg, PayloadType.OBJECT_PAYLOAD,
					false, System.currentTimeMillis());
            sync.onMessage(message);

            Thread.sleep( 1000 );
            long fi2Ver1 = fi2.getVersion();
            assertTrue( fi2Ver1 == fi2Ver );
            assertNull(TestRemoteNotificationFunctor.threadMap.get(key));
        }
        catch ( Exception e )
        {
            fail( "testMultiappUpdateHandlingPolicySkipUnprovisioned", e );
        }
        finally
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "0", ConfigurationProperty.DYNAMIC_SCOPE );
            if ( tx != null )
            {
                tx.release();
            }
            _es.getAuxReferenceDataEntityUpdateTask().clear();
        }
    }

    public void testMultiappUpdateHandlingPolicySkipFunctors()
    {
        IdcTransaction tx = null;
        try
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "3", ConfigurationProperty.DYNAMIC_SCOPE );
            Organization fi1 = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
            Organization fi2 = ReferenceDataCacheC.getInstance().getOrganization( "FI2" );
            fi1 = (Organization) IdcUtilC.refreshObject( fi1 );
            fi2 = (Organization) IdcUtilC.refreshObject( fi2 );
            Collection<Long> provisionedNamespaces = new HashSet<Long>();
            provisionedNamespaces.add( ReferenceDataCacheC.getInstance().getMAINNamespace().getObjectID() );
            provisionedNamespaces.add ( fi1.getNamespace().getObjectID() );
            ReferenceDataCacheC.getInstance().setProvisionedNamespaceIds( provisionedNamespaces );

            long fi1Ver = fi1.getVersion();
            long fi2Ver = fi2.getVersion();

            // update version of fi1 and fi2.
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi1Ver + 1) + " WHERE ID=" + fi1.getObjectID() );
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi2Ver + 1) + " WHERE ID=" + fi2.getObjectID() );

            ReferenceDataEntityCacheSynchronizationC sync = new ReferenceDataEntityCacheSynchronizationC();
            EntityUpdateMessage msg = createEntityUpdateMessage( fi2  );

            HashMap props = new HashMap();
            long now = System.currentTimeMillis();
            String key = "TestParamKey" + now;
            String value = "TestParamValue" + now;
            props.put( key, value );
            tx = IdcSessionManager.getInstance().newFakeTransaction(IdcUtilC.getSessionContextUser(), "testMultiappUpdateHandlingPolicySkipFunctors" );
            RemoteFunctorUtilC.addRemoteFunctor( TestRemoteNotificationFunctor.class.getName(), props, tx.getUOW() );
            ArrayList functorNames = ( ArrayList ) tx.getUOW().getProperty(RemoteFunctorUtilC.REMOTE_FUNCTOR_NAMES_PROP);
            msg.setFunctorNames( functorNames );

            RMQMessage message = new RMQMessage("dummy", "dummy",
					"TestBindingKey", 0, msg, PayloadType.OBJECT_PAYLOAD,
					false, System.currentTimeMillis());
            sync.onMessage(message);

            Thread.sleep( 1000 );
            long fi2Ver1 = fi2.getVersion();
            assertTrue( fi2Ver1 > fi2Ver );
            assertNull(TestRemoteNotificationFunctor.threadMap.get(key));
        }
        catch ( Exception e )
        {
            fail( "testMultiappUpdateHandlingPolicySkipFunctors", e );
        }
        finally
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "0", ConfigurationProperty.DYNAMIC_SCOPE );
            if ( tx != null )
            {
                tx.release();
            }
            _es.getAuxReferenceDataEntityUpdateTask().clear();
        }
    }

    public void testProvisionedOnSingleThreadPolicy()
    {
        IdcTransaction tx = null;
        try
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "2", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_SINGLE_THREAD_ENBABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Organization fi2 = ReferenceDataCacheC.getInstance().getOrganization( "FI2" );
            fi2 = (Organization) IdcUtilC.refreshObject( fi2 );
            Collection<Long> provisionedNamespaces = new HashSet<Long>();
            provisionedNamespaces.add( ReferenceDataCacheC.getInstance().getMAINNamespace().getObjectID() );
            provisionedNamespaces.add ( fi2.getNamespace().getObjectID() );
            ReferenceDataCacheC.getInstance().setProvisionedNamespaceIds( provisionedNamespaces );

            long fi2Ver = fi2.getVersion();

            // update version of fi2.
            getPersistenceSession().executeNonSelectingSQL( "UPDATE IDCORG SET VERSION=" + (fi2Ver + 1) + " WHERE ID=" + fi2.getObjectID() );

            ReferenceDataEntityCacheSynchronizationC sync = new ReferenceDataEntityCacheSynchronizationC();
            EntityUpdateMessage msg = createEntityUpdateMessage( fi2  );

            HashMap props = new HashMap();
            long now = System.currentTimeMillis();
            String key = "TestParamKey" + now;
            String value = "TestParamValue" + now;
            props.put( key, value );
            tx = IdcSessionManager.getInstance().newFakeTransaction(IdcUtilC.getSessionContextUser(), "testProvisionedOnSingleThreadPolicy" );
            RemoteFunctorUtilC.addRemoteFunctor( TestRemoteNotificationFunctor.class.getName(), props, tx.getUOW() );
            ArrayList functorNames = ( ArrayList ) tx.getUOW().getProperty(RemoteFunctorUtilC.REMOTE_FUNCTOR_NAMES_PROP);
            msg.setFunctorNames( functorNames );

            RMQMessage message = new RMQMessage("dummy", "dummy",
					"TestBindingKey", 0, msg, PayloadType.OBJECT_PAYLOAD,
					false, System.currentTimeMillis());
            sync.onMessage(message);

            Thread.sleep( 1000 );
            long fi2Ver1 = fi2.getVersion();
            assertTrue( fi2Ver1 > fi2Ver );
            String threadName = TestRemoteNotificationFunctor.threadMap.get(key);
            log.warn("##################### thread name=" + threadName);
            assertNotNull( threadName );
            assertTrue( threadName.contains("MultiAppRefDataSingleThread-"));
        }
        catch ( Exception e )
        {
            fail( "testProvisionedOnSingleThreadPolicy", e );
        }
        finally
        {
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, "0", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( EntityServiceConfigurationMBean.MULTI_APP_REFERENCE_DATA_UPDATE_SINGLE_THREAD_ENBABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            if ( tx != null )
            {
                tx.release();
            }
            _es.getAuxReferenceDataEntityUpdateTask().clear();
        }
    }

    private EntityUpdateMessage createEntityUpdateMessage( Entity... entities )
    {
        List modifiedEntities = new ArrayList();
        EntityUpdateMessage msg = new EntityUpdateMessage();
        Set<Long> namespaceIds = new HashSet<Long>();
        for ( Entity entity: entities )
        {
            namespaceIds.add(entity.getNamespace().getObjectID());
            modifiedEntities.add( entity );
        }

        ArrayList<Map<String, List<List<Long>>>> mainMessages = new ArrayList<Map<String, List<List<Long>>>>();

        Map<String, List<List<Long>>> additions, modifications, deletions;
        additions = processEntities( Collections.EMPTY_LIST, 0 );
        modifications = processEntities( modifiedEntities, 1);
        deletions = processEntities( Collections.EMPTY_LIST, 2 );
        mainMessages.add(additions);
        mainMessages.add(modifications);
        mainMessages.add(deletions);

        msg.setMainMessages(mainMessages);
        msg.setNamespaceIds( namespaceIds );
        Map<String, String> propMap = new HashMap<String, String>();
        propMap.put(ClusterCommitEventAdapterC.SOURCE_VM_PROPERTY, "xxx");
        msg.setMessageProperties( propMap );
        return msg;
    }

    private HashMap<String, List<List<Long>>> processEntities( List objects, int operation )
    {
        HashMap<String, List<List<Long>>> result = null;

        try
        {
            if ( objects != null )
            {
                result = new HashMap<String, List<List<Long>>>( objects.size() );

                for ( Object object : objects )
                {
                    Entity entity = ( Entity ) object;

                    // collect data
                    List<Long> tupel = new ArrayList<Long>( 2 );
                    tupel.add( new Long( entity.getObjectId() ) );
                    tupel.add( new Long( entity.getVersion() + 1) );

                    // store under classname
                    List<List<Long>> classChanges = result.get( entity.getClass().getName() );
                    if ( classChanges == null )
                    {
                        classChanges = new ArrayList<List<Long>>();
                        result.put( entity.getClass().getName(), classChanges );
                    }
                    classChanges.add( tupel );
                }
            }
            else
            {
                result = new HashMap<String, List<List<Long>>>( 1 );
            }
        }
        catch ( Exception e )
        {
            log.error("processEntities : exception while creating the addition/modification/deletion list=" + objects + ",operation=" + operation, e);
        }

        return result;
    }

    public void testDeletion()
    {
        try
        {
            UnitOfWork unitOfWork = this.getPersistenceSession().acquireUnitOfWork();
            unitOfWork.removeAllReadOnlyClasses();
            unitOfWork.addReadOnlyClass( CurrencyC.class );
            FXRateConvention conv = FXFactory.newFXRateConvention();
            String name = "TestConv" + System.currentTimeMillis();
            FXRateConvention regConv = ( FXRateConvention ) unitOfWork.registerObject( conv );
            regConv.setShortName( name );
            unitOfWork.commit();

            conv = (FXRateConvention) IdcUtilC.refreshObject( conv );
            assertNotNull( conv );


            FXRateConvention cachedConv = ( FXRateConvention ) getPersistenceSession().getIdentityMapAccessor().getFromIdentityMap( conv.getObjectID(), conv.getClass() );
            assertNotNull( cachedConv );
            assertTrue( cachedConv.isSameAs( conv ));


            // now delete the entity using direct sql
            getPersistenceSession().executeNonSelectingSQL( "DELETE FROM IDCFXRTQOTCONV WHERE ID=" + conv.getObjectID() );

            conv = (FXRateConvention) IdcUtilC.refreshObject( conv );
            assertNull( conv );

            getPersistenceSession().getIdentityMapAccessor().removeFromIdentityMap( cachedConv );
            FXRateConvention cachedConv1 = ( FXRateConvention ) getPersistenceSession().getIdentityMapAccessor().getFromIdentityMap( cachedConv.getObjectID(), cachedConv.getClass() );
            assertNull( cachedConv1 );
        }
        catch ( Exception e )
        {
            fail( "testDeletion" );
        }
    }

}