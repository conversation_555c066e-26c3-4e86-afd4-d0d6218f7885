package com.integral.persistence.test;


import com.integral.broker.model.BrokerOrganizationFunctionC;
import com.integral.broker.model.Stream;
import com.integral.broker.model.StreamC;
import com.integral.classification.Classification;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.calculator.Calculator;
import com.integral.finance.counterparty.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.persistence.CachePolicyC.CacheLoadPolicy;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.Namespace;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.DataPreloaderC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.cache.ShortnameIndex;
import com.integral.rule.RuleSet;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerC;
import com.integral.test.PTestCaseC;
import com.integral.user.Contact;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.workgroup.WorkGroup;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Map;

/**
 * Unit tests for EntityC
 */
public class ReferenceDataCachePTestC extends PTestCaseC
{
    public ReferenceDataCachePTestC( String name )
    {
        super( name );
    }

    public void testCurrencyCache()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<Currency> ccys = PersistenceFactory.newSession().readAllObjects( Currency.class );
            for ( Currency ccy : ccys )
            {
                assertEquals( "check the currency cache should contain the same currency. ccy=" + ccy + " idx=" + ccy.getIndex(), ReferenceDataCacheC.getInstance().getCurrency( ccy.getIndex() ), ccy );
            }
        }
        catch ( Exception e )
        {
            fail( "testCurrencyCache", e );
        }
    }

    public void testOrganizationCache()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<Organization> orgs = PersistenceFactory.newSession().readAllObjects( Organization.class );
            for ( Organization org : orgs )
            {
                assertEquals( "check the organization cache should contain the same organization. org=" + org + " idx=" + org.getIndex(), ReferenceDataCacheC.getInstance().getOrganization( org.getIndex() ), org );
                if ( org.getShortName() == null || org.getShortName().trim().length() == 0 )
                {
                    continue;
                }
                final Organization queriedOrg = ReferenceDataCacheC.getInstance().getOrganization( org.getShortName() );
                if ( !queriedOrg.isSameAs( org ) )
                {
                    log( "duplicate shortname found. queriedOrg=" + queriedOrg + ",org=" + org );
                }
                assertEquals( "check the org shortname cache. shortname=" + org.getShortName(), queriedOrg, org );
                if ( org.getNamespace().getObjectID() != 1 )
                {
                    assertEquals( "check the org namespace cache. ns=" + org.getNamespace(), ReferenceDataCacheC.getInstance().getOrganization( org.getNamespace() ), org );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testOrganizationCache", e );
        }
    }

    public void testEntityGuidCache()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<LegalEntity> les = PersistenceFactory.newSession().readAllObjects( LegalEntity.class );
            for ( LegalEntity le : les )
            {
                assertEquals( "check the guid cache should contain the same le", ReferenceDataCacheC.getInstance().getEntityByGuid( le.getGUID(), LegalEntity.class ), le );
            }

            Collection<RuleSet> rulesets = PersistenceFactory.newSession().readAllObjects( RuleSet.class );
            for ( RuleSet rs : rulesets )
            {
                assertEquals( "check the id cache should contain the same ruleset", ReferenceDataCacheC.getInstance().getEntityByGuid( rs.getGUID(), RuleSet.class ), rs );
            }
        }
        catch ( Exception e )
        {
            fail( "testEntityGuidCache", e );
        }
    }

    public void testEntityObjectIdCache()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<LegalEntity> les = PersistenceFactory.newSession().readAllObjects( LegalEntity.class );
            for ( LegalEntity le : les )
            {
                assertEquals( "check the id cache should contain the same le", ReferenceDataCacheC.getInstance().getEntityByObjectId( le.getObjectId(), LegalEntity.class ), le );
            }

            Collection<RuleSet> rulesets = PersistenceFactory.newSession().readAllObjects( RuleSet.class );
            for ( RuleSet rs : rulesets )
            {
                assertEquals( "check the id cache should contain the same ruleset", ReferenceDataCacheC.getInstance().getEntityByObjectId( rs.getObjectId(), RuleSet.class ), rs );
            }
        }
        catch ( Exception e )
        {
            fail( "testEntityGuidCache", e );
        }
    }

    public void testEntityShortNameCache()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<LegalEntity> les = PersistenceFactory.newSession().readAllObjects( LegalEntity.class );
            for ( LegalEntity le : les )
            {
                assertEquals( "check the id cache should contain the same le", ReferenceDataCacheC.getInstance().getEntityByShortName( le.getShortName(), LegalEntityC.class, le.getNamespace(), le.getStatus() ), le );
            }

            Collection<FXMarketDataSet> marketDataSets = PersistenceFactory.newSession().readAllObjects( FXMarketDataSet.class );
            for ( FXMarketDataSet marketDataSet : marketDataSets )
            {
                if ( ReferenceDataCacheC.END_OF_DAY_MDS.equals( marketDataSet.getShortName() ) || marketDataSet.isFixedPeriod () )
                {
                    continue;
                }
                FXMarketDataSet aMarketDataSet = ( FXMarketDataSet ) ReferenceDataCacheC.getInstance().getEntityByShortNameInNamespace( marketDataSet.getShortName(), FXMarketDataSet.class, marketDataSet.getNamespace(), marketDataSet.getStatus() );
                if ( aMarketDataSet.isSameAs( marketDataSet ) )
                {
                    log( "mismatch found. aMarketDataSet=" + aMarketDataSet + ",marketDataSet=" + marketDataSet );
                }
                assertEquals( "check the id cache should contain the same mds", aMarketDataSet, marketDataSet );
            }

            IdcSessionManager.getInstance().setTransaction( null );
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral@MAIN" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );

            Organization aig = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI2", Organization.class, ( ( User ) sessContext.getUser() ).getNamespace(), Entity.ACTIVE_STATUS );
            assertNotNull( aig );

        }
        catch ( Exception e )
        {
            fail( "testEntityShortNameCache", e );
        }
    }

    public void testRemoveEntityFromCache()
    {
        try
        {
            // create new stream
            Namespace mainNs = ( Namespace ) namedEntityReader.execute( NamespaceC.class, "MAIN", null );
            UnitOfWork aUow = getPersistenceSession().acquireUnitOfWork();
            aUow.removeReadOnlyClass( StreamC.class );
            aUow.addReadOnlyClass( NamespaceC.class );
            String testStreamName = "Test" + System.nanoTime();
            Stream stream = new StreamC();
            Stream registeredStream = ( Stream ) aUow.registerObject( stream );

            registeredStream.setShortName( testStreamName );
            registeredStream.setMarketTimeInForceInterval( 111 );
            registeredStream.setTimeInForceInterval( 222 );
            registeredStream.setRequestExpiry( 1000 );
            registeredStream.setQuotePublicationInterval( 50 );
            registeredStream.setQuotePublicationCheckInterval( 100 );
            registeredStream.setStatus( Entity.ACTIVE_STATUS );
            registeredStream.setNamespace( mainNs );
            aUow.commit();

            Stream queriedStreamByOID = ( Stream ) ReferenceDataCacheC.getInstance().getEntityByObjectId( stream.getObjectID(), StreamC.class );
            Stream queriedStreamByGuid = ( Stream ) ReferenceDataCacheC.getInstance().getEntityByGuid( stream.getGUID(), StreamC.class );
            Stream queriedStream = ( Stream ) ReferenceDataCacheC.getInstance().getEntityByShortName( testStreamName, StreamC.class, mainNs, Entity.ACTIVE_STATUS );
            assertNotNull( queriedStreamByOID );
            assertNotNull( queriedStreamByGuid );
            assertNotNull( queriedStream );
            assertTrue( queriedStream.isSameAs( stream ) );

            // now delete the stream
            UnitOfWork aUow1 = getPersistenceSession().acquireUnitOfWork();
            aUow1.removeReadOnlyClass( StreamC.class );
            Stream registeredStream1 = ( Stream ) aUow.registerObject( stream );
            aUow1.deleteObject( registeredStream1 );
            aUow1.commit();

            ReferenceDataCacheC.getInstance().removeEntity( stream );
            Stream queriedStreamByOID1 = ( Stream ) ReferenceDataCacheC.getInstance().getEntityByObjectId( stream.getObjectID(), StreamC.class );
            Stream queriedStreamByGuid1 = ( Stream ) ReferenceDataCacheC.getInstance().getEntityByGuid( stream.getGUID(), StreamC.class );
            Stream queriedStream1 = ( Stream ) ReferenceDataCacheC.getInstance().getEntityByShortName( testStreamName, StreamC.class, mainNs, Entity.ACTIVE_STATUS );
            assertNull( queriedStreamByOID1 );
            assertNull( queriedStreamByGuid1 );
            assertNull( queriedStream1 );
        }
        catch ( Exception e )
        {
            fail( "testRemoveEntityFromCache", e );
        }
    }

    public void testGetEntityFromCache()
    {
        try
        {
            Collection<Stream> streams = ( Collection<Stream> ) PersistenceFactory.newSession().readAllObjects( StreamC.class );
            if ( !PersistenceFactory.getCachePolicy().isCached( StreamC.class ) )
            {
                Stream stream = streams.iterator().next();
                // since it is not a cached entity, it will not be available in the cache.
                Entity cachedEntity = ReferenceDataCacheC.getInstance().getEntityByObjectIdFromCacheOnly( stream.getObjectID(), StreamC.class );
                assertNull( cachedEntity );

                cachedEntity = ReferenceDataCacheC.getInstance().getEntityByObjectId( stream.getObjectID(), StreamC.class );
                assertNotNull( cachedEntity );
                Entity cachedEntity1 = ReferenceDataCacheC.getInstance().getEntityByObjectIdFromCacheOnly( stream.getObjectID(), StreamC.class );
                assertNotNull( cachedEntity1 );
            }
        }
        catch ( Exception e )
        {
            fail( "testGetEntityFromCache", e );
        }
    }

    public void testEntityIdLookupUsingInterface()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
            Collection<Class> classes = PersistenceFactory.getCachePolicy().getClassesToCache(CacheLoadPolicy.ALL);
            for ( Class cls : classes )
            {
                Collection<Entity> entities = PersistenceFactory.newSession().readAllObjects( cls, expr );
                for ( Entity entity : entities )
                {
                    Entity cachedEntity = ReferenceDataCacheC.getInstance().getEntityByObjectId( entity.getObjectID(), entity.getClass() );
                    assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + entity.getClass(), cachedEntity.isSameAs( entity ) );

                    Class[] interfaces = cls.getInterfaces();
                    for ( Class i : interfaces )
                    {
                        if ( Entity.class.isAssignableFrom( i ) )
                        {
                            cachedEntity = ReferenceDataCacheC.getInstance().getEntityByObjectId( entity.getObjectID(), i );
                            assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + i, cachedEntity.isSameAs( entity ) );
                            log( "entity=" + entity + ",interface=" + i );
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testEntityIdLookupUsingInterface", e );
        }
    }

    public void testEntityIdLookupFromCacheUsingInterface()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
            Collection<Class> classes = PersistenceFactory.getCachePolicy().getClassesToCache(CacheLoadPolicy.ALL);
            for ( Class cls : classes )
            {
                Collection<Entity> entities = PersistenceFactory.newSession().readAllObjects( cls, expr );
                for ( Entity entity : entities )
                {
                    if ( !( entity instanceof NamedEntity ) )
                    {
                        continue;
                    }
                    if ( entity instanceof Classification || entity instanceof Calculator || entity instanceof SettlementParty
                            || entity instanceof Contact || entity instanceof WorkGroup )
                    {
                        continue;
                    }
                    Map<Class, Map<Long, Entity>> refCacheMap = ReferenceDataCacheC.getInstance().getObjectIdEntityMap();
                    Map<Long, Entity> idMap = refCacheMap.get( entity.getClass() );
                    if ( idMap == null )
                    {
                        log.info( "#1 id map null for " + entity.getClass() + ",entity=" + entity );
                    }
                    assertNotNull( idMap );
                    Entity cachedEntity = idMap.get( entity.getObjectID() );
                    if ( cachedEntity == null )
                    {
                        log.info( "#2 cached entity null for " + entity.getClass() + ",entity=" + entity );
                    }
                    assertNotNull( cachedEntity );
                    assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + entity.getClass(), cachedEntity.isSameAs( entity ) );

                    Class[] interfaces = cls.getInterfaces();
                    for ( Class i : interfaces )
                    {
                        if ( Entity.class.isAssignableFrom( i ) )
                        {
                            if ( i.equals( Namespace.class ) )
                            {
                                continue;
                            }

                            Map<Long, Entity> idMap1 = refCacheMap.get( i );
                            if ( idMap1 == null )
                            {
                                log.info( "#3 id map null for " + i + ",entity=" + entity );
                            }
                            assertNotNull( idMap1 );
                            cachedEntity = idMap1.get( entity.getObjectID() );
                            if ( cachedEntity == null )
                            {
                                log.info( "#4 cached entity null for " + i + ",entity=" + entity );
                            }
                            assertNotNull( cachedEntity );
                            assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + i, cachedEntity.isSameAs( entity ) );
                            log( "entity=" + entity + ",interface=" + i );
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testEntityIdLookupFromCacheUsingInterface", e );
        }
    }


    public void testGetEntityByIDForVirtualServers()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<VirtualServer> virtualServers = ( Collection<VirtualServer> ) PersistenceFactory.newSession().readAllObjects( VirtualServerC.class );
            for ( Entity entity : virtualServers )
            {
                Entity cachedEntity = ReferenceDataCacheC.getInstance().getEntityByObjectId( entity.getObjectID(), entity.getClass() );
                assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + entity.getClass(), cachedEntity.isSameAs( entity ) );

                Class[] interfaces = entity.getClass().getInterfaces();
                for ( Class i : interfaces )
                {
                    if ( Entity.class.isAssignableFrom( i ) )
                    {
                        cachedEntity = ReferenceDataCacheC.getInstance().getEntityByObjectId( entity.getObjectID(), i );
                        assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + i, cachedEntity.isSameAs( entity ) );
                        log( "entity=" + entity + ",interface=" + i );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testGetEntityByIDForVirtualServers", e );
        }
    }

    public void testGetEntityByIDFromCacheForVirtualServers()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<VirtualServer> virtualServers = ( Collection<VirtualServer> ) PersistenceFactory.newSession().readAllObjects( VirtualServerC.class );
            for ( Entity entity : virtualServers )
            {
                Map<Class, Map<Long, Entity>> refCacheMap = ReferenceDataCacheC.getInstance().getObjectIdEntityMap();
                Map<Long, Entity> idMap = refCacheMap.get( entity.getClass() );
                if ( idMap == null )
                {
                    log.info( "#1 id map null for " + entity.getClass() + ",entity=" + entity );
                }
                assertNotNull( idMap );
                Entity cachedEntity = idMap.get( entity.getObjectID() );
                if ( cachedEntity == null )
                {
                    log.info( "#2 cached entity null for " + entity.getClass() + ",entity=" + entity );
                }
                assertNotNull( cachedEntity );
                assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + entity.getClass(), cachedEntity.isSameAs( entity ) );

                Class[] interfaces = entity.getClass().getInterfaces();
                for ( Class i : interfaces )
                {
                    if ( Entity.class.isAssignableFrom( i ) )
                    {
                        Map<Long, Entity> idMap1 = refCacheMap.get( i );
                        if ( idMap1 == null )
                        {
                            log.info( "#3 id map null for " + i + ",entity=" + entity );
                        }
                        assertNotNull( idMap1 );
                        cachedEntity = idMap1.get( entity.getObjectID() );
                        if ( cachedEntity == null )
                        {
                            log.info( "#4 cached entity null for " + i + ",entity=" + entity );
                        }
                        assertNotNull( cachedEntity );
                        assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + i, cachedEntity.isSameAs( entity ) );
                        log( "entity=" + entity + ",interface=" + i );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testGetEntityByIDFromCacheForVirtualServers", e );
        }
    }


    public void testGetEntityByIDForTradingParties()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<TradingParty> tps = ( Collection<TradingParty> ) PersistenceFactory.newSession().readAllObjects( TradingParty.class );
            for ( Entity entity : tps )
            {
                Entity cachedEntity = ReferenceDataCacheC.getInstance().getEntityByObjectId( entity.getObjectID(), entity.getClass() );
                assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + entity.getClass(), cachedEntity.isSameAs( entity ) );

                Class[] interfaces = entity.getClass().getInterfaces();
                for ( Class i : interfaces )
                {
                    if ( Entity.class.isAssignableFrom( i ) )
                    {
                        cachedEntity = ReferenceDataCacheC.getInstance().getEntityByObjectId( entity.getObjectID(), i );
                        assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + i, cachedEntity.isSameAs( entity ) );
                        log( "entity=" + entity + ",interface=" + i );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testGetEntityByIDForTradingParties", e );
        }
    }

    public void testGetEntityByIDFromCacheForTradingParties()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<TradingParty> tps = ( Collection<TradingParty> ) PersistenceFactory.newSession().readAllObjects( TradingParty.class );
            for ( Entity entity : tps )
            {
                Map<Class, Map<Long, Entity>> refCacheMap = ReferenceDataCacheC.getInstance().getObjectIdEntityMap();
                Map<Long, Entity> idMap = refCacheMap.get( entity.getClass() );
                if ( idMap == null )
                {
                    log.info( "#1 id map null for " + entity.getClass() + ",entity=" + entity );
                }
                assertNotNull( idMap );
                Entity cachedEntity = idMap.get( entity.getObjectID() );
                if ( cachedEntity == null )
                {
                    log.info( "#2 cached entity null for " + entity.getClass() + ",entity=" + entity );
                }
                assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + entity.getClass(), cachedEntity.isSameAs( entity ) );

                Class[] interfaces = entity.getClass().getInterfaces();
                for ( Class i : interfaces )
                {
                    if ( Entity.class.isAssignableFrom( i ) )
                    {
                        Map<Long, Entity> idMap1 = refCacheMap.get( i );
                        if ( idMap1 == null )
                        {
                            log.info( "#3 id map null for " + i + ",entity=" + entity );
                        }
                        assertNotNull( idMap1 );
                        cachedEntity = idMap1.get( entity.getObjectID() );
                        if ( cachedEntity == null )
                        {
                            log.info( "#4 cached entity null for " + i + ",entity=" + entity );
                        }
                        assertNotNull( cachedEntity );
                        assertTrue( "cachedEntity=" + cachedEntity + ",entity=" + entity + ",queryClass=" + i, cachedEntity.isSameAs( entity ) );
                        log( "entity=" + entity + ",interface=" + i );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testGetEntityByIDFromCacheForTradingParties", e );
        }
    }

    public void testMarketDataSetLookup()
    {
        FXMarketDataSet liveMds = ReferenceDataCacheC.getInstance().getLiveFXMds();
        FXMarketDataSet staticMds = ReferenceDataCacheC.getInstance().getMainStaticMds();
        assertNotNull( liveMds );
        assertNotNull( staticMds );
        assertEquals( ReferenceDataCacheC.LIVE_FX_MDS, liveMds.getShortName() );
        assertEquals( ReferenceDataCacheC.MAIN_STATIC_FX_MDS, staticMds.getShortName() );
    }

    public void testCurrencyPairGroupByDbQuery()
    {
        try
        {
            Collection<CurrencyPairGroup> cpgs = ( Collection<CurrencyPairGroup> ) PersistenceFactory.newSession().readAllObjects( CurrencyPairGroup.class );
            for ( CurrencyPairGroup cpg : cpgs )
            {
                CurrencyPairGroup queriedCpg1 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( cpg.getShortName(), CurrencyPairGroup.class, cpg.getNamespace(), null );
                CurrencyPairGroup queriedCpg2 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( cpg.getShortName(), CurrencyPairGroupC.class, cpg.getNamespace(), null );
                if ( cpg.isPrivatelyOwned() )
                {
                    assertTrue( queriedCpg1 == null || !queriedCpg1.isPrivatelyOwned() );
                    assertTrue( queriedCpg2 == null || !queriedCpg2.isPrivatelyOwned() );
                }
                else
                {
                    assertTrue( cpg.isSameAs( queriedCpg1 ) );
                    assertTrue( cpg.isSameAs( queriedCpg2 ) );
                }

                if ( cpg.getNamespace().getShortName().equals( IdcUtilC.MAIN_NAMESPACE ) )
                {
                    CurrencyPairGroup queriedCpg3 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( cpg.getShortName(), CurrencyPairGroup.class, null, null );
                    CurrencyPairGroup queriedCpg4 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( cpg.getShortName(), CurrencyPairGroupC.class, null, null );
                    if ( cpg.isPrivatelyOwned() )
                    {
                        assertNull( queriedCpg3 );
                        assertNull( queriedCpg4 );
                    }
                    else
                    {
                        assertTrue( cpg.isSameAs( queriedCpg3 ) );
                        assertTrue( cpg.isSameAs( queriedCpg4 ) );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairGroupByDbQuery", e );
        }
    }

    public void testCurrencyPairGroupByCacheLookup()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<CurrencyPairGroup> cpgs = ( Collection<CurrencyPairGroup> ) PersistenceFactory.newSession().readAllObjects( CurrencyPairGroup.class );
            for ( CurrencyPairGroup cpg : cpgs )
            {
                CurrencyPairGroup queriedCpg1 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( cpg.getShortName(), CurrencyPairGroup.class, cpg.getNamespace(), null );
                CurrencyPairGroup queriedCpg2 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( cpg.getShortName(), CurrencyPairGroupC.class, cpg.getNamespace(), null );
                if ( cpg.isPrivatelyOwned() )
                {
                    assertTrue( queriedCpg1 == null || !queriedCpg1.isPrivatelyOwned() );
                    assertTrue( queriedCpg2 == null || !queriedCpg2.isPrivatelyOwned() );
                }
                else
                {
                    assertTrue( cpg.isSameAs( queriedCpg1 ) );
                    assertTrue( cpg.isSameAs( queriedCpg2 ) );
                }
                if ( cpg.getNamespace().getShortName().equals( IdcUtilC.MAIN_NAMESPACE ) )
                {
                    CurrencyPairGroup queriedCpg3 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( cpg.getShortName(), CurrencyPairGroup.class, null, null );
                    CurrencyPairGroup queriedCpg4 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( cpg.getShortName(), CurrencyPairGroupC.class, null, null );
                    if ( cpg.isPrivatelyOwned() )
                    {
                        assertNull( queriedCpg3 );
                        assertNull( queriedCpg4 );
                    }
                    else
                    {
                        assertTrue( cpg.isSameAs( queriedCpg3 ) );
                        assertTrue( cpg.isSameAs( queriedCpg4 ) );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairGroupByCacheLookup", e );
        }
    }

    public void testCounterpartyCache()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<Counterparty> cptys = PersistenceFactory.newSession().readAllObjects( CounterpartyC.class );
            for ( Counterparty cpty : cptys )
            {
                // disable temporarily until loading issue is fixed.
                //assertEquals( "check the cpty cache should contain the same cpty=" + cpty + " cptyIdx=" + cpty.getCptyIndex(), ReferenceDataCacheC.getInstance().getCounterparty(cpty.getCptyIndex()), cpty );
            }
        }
        catch ( Exception e )
        {
            fail( "testCounterpartyCache", e );
        }
    }

    public void testLegalEntityCache()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<LegalEntity> les = PersistenceFactory.newSession().readAllObjects( LegalEntityC.class );
            for ( LegalEntity le : les )
            {
                // disable temporarily until loading issue is fixed.
                //assertEquals( "check the le cache should contain the same le=" + le + " idx=" + le.getIndex(), ReferenceDataCacheC.getInstance().getLegalEntity(le.getIndex()), le );
            }
        }
        catch ( Exception e )
        {
            fail( "testLegalEntityCache", e );
        }
    }

    public void testTradingPartyCache()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Collection<TradingParty> tps = PersistenceFactory.newSession().readAllObjects( TradingPartyC.class );
            for ( TradingParty tp : tps )
            {
                // disable temporarily until loading issue is fixed.
                //assertEquals( "check the tp cache should contain the same tp=" + tp + " idx=" + tp.getIndex(), ReferenceDataCacheC.getInstance().getTradingParty( tp.getIndex()), tp );
            }
        }
        catch ( Exception e )
        {
            fail( "testTradingPartyCache", e );
        }
    }

    public void testNullShortnameLookup()
    {
        try
        {
            Organization org = (Organization) ReferenceDataCacheC.getInstance().getEntityByShortName( null, OrganizationC.class, null, null );
            assertNull( org );

            Organization org1 = (Organization) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1", null, null, null );
            assertNull( org1 );

            try
            {
                Organization org2 = (Organization) ShortnameIndex.findByShortName(OrganizationC.class, null, null);
            }
            catch ( IdcNoSuchObjectException e )
            {

            }

            try
            {
                Organization org3 = (Organization) ShortnameIndex.findByShortName(null, "FI1", null);
            }
            catch ( IdcNoSuchObjectException e )
            {

            }
        }
        catch ( Exception e )
        {
            fail ( "testNullShortnameLookup", e );
        }
    }

    public void testSuperBankStreamLookup()
    {
        try
        {
            ReferenceDataCacheC.getInstance ().loadReferenceDataForNamespaces ( null );
            Stream stream1 = ReferenceDataCacheC.getInstance ().getSuperBankStreamByIndex ( ******** );
            assertNull ( stream1 );
        }
        catch ( Exception e )
        {
            log.error ( "testSuperBankStreamLookup" );
        }
    }

    public void testSuperBankStreamIndexSet()
    {
        try
        {
            ReferenceDataCacheC.getInstance ().loadReferenceDataForNamespaces ( null );
            UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
            uow.removeReadOnlyClass ( StreamC.class );
            uow.removeReadOnlyClass ( BrokerOrganizationFunctionC.class );
            uow.addReadOnlyClass ( OrganizationC.class );

            int streamsUpdated = 0;
            Stream lastUpdatedStream = null;
            Collection<Stream> updatedStreams = new ArrayList<Stream> ();

            Collection<Organization> orgs = PersistenceFactory.newSession ().readAllObjects ( OrganizationC.class );
            for ( Organization org: orgs )
            {
                if ( org.isExternalProvider () && org.getBrokerOrganizationFunction () != null && !org.getBrokerOrganizationFunction ().getStreams ().isEmpty () )
                {
                    org.setSuperBank ( true );
                    Collection<Stream> streams = org.getBrokerOrganizationFunction ().getStreams ();
                    for ( Stream stream: streams )
                    {
                        if ( stream.getSuperLPIndex () > 0 )
                        {
                            continue;
                        }
                        Stream registeredStream = ( Stream ) uow.registerObject ( stream );
                        registeredStream.getBrokerOrganizationFunction ().getOrganization ().setSuperBank ( true );
                        registeredStream.update ();
                        streamsUpdated++;
                        lastUpdatedStream = stream;
                        updatedStreams.add ( stream );
                    }
                }
            }

            if ( streamsUpdated > 0 )
            {
                log( "Streams updated=" + streamsUpdated );
                uow.commit ();

                for ( Stream stream: updatedStreams )
                {
                    IdcUtilC.refreshObject ( stream );
                }

                lastUpdatedStream = ( Stream ) IdcUtilC.refreshObject ( lastUpdatedStream  );

                ReferenceDataCacheC.getInstance ().resetSuperBankStreamArray ();

                int index = lastUpdatedStream.getSuperLPIndex ();
                assertTrue ( index > 0 );
                Stream streamFromCache = ReferenceDataCacheC.getInstance ().getSuperBankStreamByIndex ( index );
                assertNotNull ( streamFromCache );
                assertTrue ( streamFromCache == lastUpdatedStream );
                assertTrue ( streamFromCache.getVersion () == lastUpdatedStream.getVersion () );
            }
            else
            {
                uow.release ();
            }
        }
        catch ( Exception e )
        {
            fail("testSuperBankStreamIndexSet", e );
        }
        finally
        {
            resetSuperBankStreamIndex ();
        }
    }

    private void resetSuperBankStreamIndex()
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
            uow.removeReadOnlyClass ( StreamC.class );
            uow.removeReadOnlyClass ( BrokerOrganizationFunctionC.class );
            uow.addReadOnlyClass ( OrganizationC.class );

            Collection<Organization> orgs = PersistenceFactory.newSession ().readAllObjects ( OrganizationC.class );
            for ( Organization org: orgs )
            {
                if ( org.isExternalProvider () )
                {
                    org = ( OrganizationC ) IdcUtilC.refreshObject ( org );
                }
                if ( !org.isSuperBank () && org.getBrokerOrganizationFunction () != null )
                {
                    Collection<Stream> streams = org.getBrokerOrganizationFunction ().getStreams ();
                    for ( Stream stream: streams )
                    {
                        Stream registeredStream = ( Stream ) uow.registerObject ( stream );
                        registeredStream.update ();
                        registeredStream.setSuperLPIndex ( ( short ) 0 );
                    }
                }
            }

            uow.commit ();
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
        }
    }

    public void testCounterpartyIndexLookup()
    {
        try
        {
            ReferenceDataCacheC referenceDataCacheC = ReferenceDataCacheC.getInstance ();
            referenceDataCacheC.resetCounterpartiesArray ();

            Collection<Counterparty> counterparties = PersistenceFactory.newSession ().readAllObjects ( CounterpartyC.class );
            assertNotNull ( counterparties );
            assertFalse ( counterparties.isEmpty () );
            for ( Counterparty cpty: counterparties )
            {
                int index = cpty.getCptyIndex ();
                assertEquals ( cpty, referenceDataCacheC.getCounterparty ( index  ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testCounterpartyIndexLookup", e);
        }
    }

    public void testLegalEntityIndexLookup()
    {
        try
        {
            ReferenceDataCacheC referenceDataCacheC = ReferenceDataCacheC.getInstance ();
            referenceDataCacheC.resetCounterpartiesArray ();

            Collection<LegalEntity> legalEntities = PersistenceFactory.newSession ().readAllObjects ( LegalEntityC.class );
            assertNotNull ( legalEntities );
            assertFalse ( legalEntities.isEmpty () );
            for ( LegalEntity le: legalEntities )
            {
                int index = le.getIndex ();
                assertEquals ( le, referenceDataCacheC.getLegalEntity ( index  ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testLegalEntityIndexLookup", e);
        }
    }

    public void testTradingPartyIndexLookup()
    {
        try
        {
            ReferenceDataCacheC referenceDataCacheC = ReferenceDataCacheC.getInstance ();
            referenceDataCacheC.resetCounterpartiesArray ();

            Collection<TradingParty> tradingParties = PersistenceFactory.newSession ().readAllObjects ( TradingPartyC.class );
            assertNotNull ( tradingParties );
            assertFalse ( tradingParties.isEmpty () );
            for ( TradingParty tp: tradingParties )
            {
                int index = tp.getIndex ();
                assertEquals ( tp, referenceDataCacheC.getTradingParty ( index  ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testTradingPartyIndexLookup", e);
        }
    }

    public void testMTFVenueOrgsList()
    {
        try
        {
            Collection<Organization> orgs = PersistenceFactory.newSession ().readAllObjects ( OrganizationC.class );
            assertNotNull ( orgs  );
            assertFalse ( orgs.isEmpty () );
            Collection<Organization> mtfOrgs = new HashSet<Organization>();
            for ( Organization org: orgs )
            {
                if ( org.isMTFVenue () && org.isActive () )
                {
                    mtfOrgs.add ( org );
                }
            }

            ReferenceDataCacheC.getInstance ().loadOrganizations ();
            Collection<Organization> mtfOrgsFromRDC = ReferenceDataCacheC.getInstance ().getMTFVenueOrgs ();
            assertTrue ( "mtfOrgsFromRDC=" + mtfOrgsFromRDC + ",mtfOrgs=" + mtfOrgs, mtfOrgsFromRDC.size () == mtfOrgs.size () );
            if ( !mtfOrgs.isEmpty () )
            {
                for ( Organization org: mtfOrgs )
                {
                    assertTrue (  mtfOrgsFromRDC.contains ( org  ));
                }
            }
        }
        catch ( Exception e )
        {
            fail ( "testMTFVenueOrgsList", e );
        }
    }

    public void testGetOrgLegalEntities()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Organization[] orgs = ReferenceDataCacheC.getInstance ().getOrgs ();
            for ( Organization org: orgs )
            {
                Collection<LegalEntity> les = org.getLegalEntities ();
                boolean validLes = true;
                for ( LegalEntity le: les )
                {
                    if ( le.getOrganization () == null )
                    {
                        validLes = false;
                    }
                }
                if ( !validLes )
                {
                    continue;
                }
                Collection<LegalEntity> lesFromCache = ReferenceDataCacheC.getInstance ().getLegalEntities ( org );
                assertNotNull ( lesFromCache );
                assertTrue ( les.size () == lesFromCache.size () );
                for ( LegalEntity legalEntity: les )
                {
                    assertTrue ( lesFromCache.contains ( legalEntity ) );
                }
                for ( LegalEntity legalEntity: lesFromCache )
                {
                    assertTrue ( les.contains ( legalEntity ) );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testGetOrgLegalEntities", e );
        }
    }

    public void testGetOrgTradingParties()
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC();
            loader.setVerboseMode( true );
            loader.run( null );

            Organization[] orgs = ReferenceDataCacheC.getInstance ().getOrgs ();
            for ( Organization org: orgs )
            {
                Collection<TradingParty> tps = org.getTradingParties ();
                boolean validTps = true;
                for ( TradingParty tp: tps )
                {
                    if ( tp.getOrganization () == null || tp.isAnonymous () )
                    {
                        validTps = false;
                        break;
                    }
                }
                if ( !validTps )
                {
                    continue;
                }

                Collection<TradingParty> tpsFromCache = ReferenceDataCacheC.getInstance ().getTradingParties ( org );
                assertNotNull ( tpsFromCache );
                if ( tps.size () != tpsFromCache.size () )
                {
                    System.out.println ( "org=" + org + ",tps=" + tps + ",tpsFromCache=" + tpsFromCache );
                    for ( TradingParty tp: tpsFromCache )
                    {
                        if ( !tps.contains ( tp ))
                        {
                            System.out.println ("Not found tp=" + tp);
                        }
                    }
                }
                assertTrue ( tps.size () == tpsFromCache.size () );
                for ( TradingParty tp: tps )
                {
                    assertTrue ( tpsFromCache.contains ( tp ) );
                }
                for ( TradingParty tp: tpsFromCache )
                {
                    assertTrue ( tps.contains ( tp ) );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testGetOrgTradingParties", e );
        }
    }
}
