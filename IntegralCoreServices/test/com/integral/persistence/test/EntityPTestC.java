package com.integral.persistence.test;


import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.fx.FXLegC;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.finance.trade.TradeLeg;
import com.integral.persistence.Entity;
import com.integral.persistence.EntityC;
import com.integral.persistence.EntityFactory;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.Namespace;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadEntityC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserExternalSystemIdC;
import com.integral.user.UserFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Iterator;

/**
 * Unit tests for EntityC
 */
public class EntityPTestC extends PTestCaseC
{
    public EntityPTestC( String name )
    {
        super( name );
    }

    /**
     * Tests GUID generation
     */
    public void testGUID()
    {
        User user = UserFactory.newUser( "TEST" );
        log.debug( "\tguid = " + user.getGUID() );
        assertNotNull( user.getGUID() );
    }

    /**
     * Tests cloning of an entity
     */
    public void testCloneEntity()
    {
        try
        {
            Session session = getPersistenceSession();
            // get currency
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "USD" );
            Currency usd = ( Currency ) session.readObject( Currency.class, expr );
            // test normal cloning
            Currency clone1 = ( Currency ) EntityFactory.newEntity( session, usd, false );
            assertEquals( 0, clone1.getObjectID() );
            assertNotSame( usd.getGUID(), clone1.getGUID() );
            assertEquals( usd.getNamespace(), clone1.getNamespace() );
            // test registered cloning
            Currency clone2 = ( Currency ) EntityFactory.newEntity( session, usd, true );
            assertEquals( 0, clone2.getObjectID() );
            assertNotSame( usd.getGUID(), clone2.getGUID() );
            assertEquals( usd.getNamespace(), clone2.getNamespace() );
            // test registered cloning without namespace
            Currency clone3 = ( Currency ) EntityFactory.newEntity( session, usd, true, true );
            assertEquals( 0, clone3.getObjectID() );
            assertNotSame( usd.getGUID(), clone3.getGUID() );
            assertNull( clone3.getNamespace() );
        }
        catch ( Exception e )
        {
            fail( "Exception in testCloneEntity : ", e );
        }
    }

    /**
     * Tests that properties are preserved across database transactions
     */
    public void testPreservedPropertyMap()
    {
        try
        {
            Session session = getPersistenceSession();
            // get currency
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "USD" );
            Currency usd = ( Currency ) session.readObject( Currency.class, expr );
            String originalPreservedValue = "preserveValue";
            String originalTransientValue = "transientValue";
            usd.setStickyProperty( "preserve", originalPreservedValue );
            usd.setProperty( "transient", originalTransientValue );

            // clone via registerObject
            Currency clone1 = ( Currency ) session.acquireUnitOfWork().registerObject( usd );
            String transValue = ( String ) clone1.getProperty( "transient" );
            String presValue = ( String ) clone1.getStickyProperty( "preserve" );
            assertEquals( "Preserved property should remain after clone.", originalPreservedValue, presValue );

            //refresh the object
            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
            Currency clone2 = ( Currency ) uow.refreshObject( usd );
            String transValue2 = ( String ) clone2.getProperty( "transient" );
            String presValue2 = ( String ) clone2.getStickyProperty( "preserve" );
            assertEquals( "Preserved property should remain after refresh.", originalPreservedValue, presValue2 );
            assertNull( "Transient property should be null after refresh.", transValue2 );

            UnitOfWork uow1 = session.acquireUnitOfWork();
            Currency regCcy = ( Currency ) uow1.registerObject( usd );
            String newPreservedValue = "newPreserveValue";
            String newTransientValue = "newTransientValue";
            regCcy.setProperty( "transient", newTransientValue );
            regCcy.setStickyProperty( "preserve", newPreservedValue );
            uow1.commit();

            regCcy = ( Currency ) PersistenceFactory.newSession().refreshObject( usd );
            String transValue3 = ( String ) regCcy.getProperty( "transient" );
            String presValue3 = ( String ) regCcy.getStickyProperty( "preserve" );
            assertEquals( "Preserved property should remain after refresh.", newPreservedValue, presValue3 );
            assertNull( "Transient property should be null after refresh.", transValue3 );

        }
        catch ( Exception e )
        {
            fail( "Exception in testPreservedPropertyMap : ", e );
        }
    }

    /**
     * Tests cloning of an entity tree
     */
    public void testCloneEntityTree()
    {
        try
        {
            Session session = getPersistenceSession();

            // get namespace F1
            ExpressionBuilder eb1 = new ExpressionBuilder();
            Expression expr1 = eb1.get( "shortName" ).equal( "FI1" );
            Namespace fi1 = ( Namespace ) session.readObject( Namespace.class, expr1 );
            assertNotNull( fi1 );

            // get namespace F2
            Expression expr2 = eb1.get( "shortName" ).equal( "FI2" );
            Namespace fi2 = ( Namespace ) session.readObject( Namespace.class, expr2 );
            assertNotNull( fi2 );

            // get trade with status 'T'
            Expression expr = eb1.get( "status" ).equal( 'T' );
            Trade trade = ( Trade ) session.readObject( FXTrade.class, expr );
            if ( trade == null || trade.getTradeLeg( "LEG1" ) == null )
            {
                UnitOfWork uow = session.acquireUnitOfWork();
                Trade newTrade = new FXSingleLegC();
                Trade regTrade = ( Trade ) uow.registerObject( newTrade );
                regTrade.setStatus( 'T' );
                regTrade.setNamespace( fi1 );
                TradeLeg leg1 = new FXLegC();
                TradeLeg regTrdLeg = ( TradeLeg ) uow.registerObject( leg1 );
                regTrdLeg.setStatus( 'T' );
                regTrdLeg.setNamespace( fi1 );
                regTrade.setTradeLeg( "LEG1", regTrdLeg );
                regTrdLeg.setTrade( regTrade );

                uow.commit();

                trade = ( Trade ) PersistenceFactory.newSession().refreshObject( newTrade );
            }
            assertNotNull( trade );

            UnitOfWork uow = session.acquireUnitOfWork();
            Trade regTrade = ( Trade ) uow.registerObject( trade );
            Collection<TradeLeg> trdLegs = regTrade.getTradeLegs();
            for ( TradeLeg trdLeg : trdLegs )
            {
                if ( trdLeg.getName() == null )
                {
                    trdLeg = ( TradeLeg ) uow.registerObject( trdLeg );
                    trdLeg.setName( "LEG1" );
                }
            }
            uow.commit();

            // test normal cloning
            Trade clone1 = ( Trade ) EntityFactory.newEntity( session, trade, false );
            assertEquals( 0, clone1.getObjectID() );
            assertNotSame( trade.getGUID(), clone1.getGUID() );
            assertEquals( trade.getNamespace(), clone1.getNamespace() );
            assertNotNull( clone1.getTradeLeg( "LEG1" ) );
            assertEquals( 0, clone1.getTradeLeg( "LEG1" ).getObjectID() );
            assertNotSame( trade.getTradeLeg( "LEG1" ).getGUID(), clone1.getTradeLeg( "LEG1" ).getGUID() );
            assertEquals( trade.getTradeLeg( "LEG1" ).getNamespace(), clone1.getTradeLeg( "LEG1" ).getNamespace() );

            // test registered cloning
            Trade clone2 = ( Trade ) EntityFactory.newEntity( session, trade, true );
            assertEquals( 0, clone2.getObjectID() );
            assertNotSame( trade.getGUID(), clone2.getGUID() );
            assertEquals( trade.getNamespace(), clone2.getNamespace() );
            assertNotNull( clone2.getTradeLeg( "LEG1" ) );
            assertEquals( 0, clone2.getTradeLeg( "LEG1" ).getObjectID() );
            assertNotSame( trade.getTradeLeg( "LEG1" ).getGUID(), clone2.getTradeLeg( "LEG1" ).getGUID() );
            assertEquals( trade.getTradeLeg( "LEG1" ).getNamespace(), clone2.getTradeLeg( "LEG1" ).getNamespace() );

            // test registered cloning without namespace
            Trade clone3 = ( Trade ) EntityFactory.newEntity( session, trade, true, true );
            assertEquals( 0, clone3.getObjectID() );
            assertNotSame( trade.getGUID(), clone3.getGUID() );
            assertNull( clone3.getNamespace() );
            assertNotNull( clone3.getTradeLeg( "LEG1" ) );
            assertEquals( 0, clone3.getTradeLeg( "LEG1" ).getObjectID() );
            assertNotSame( trade.getTradeLeg( "LEG1" ).getGUID(), clone3.getTradeLeg( "LEG1" ).getGUID() );
            assertNull( clone3.getTradeLeg( "LEG1" ).getNamespace() );

            // test registered cloning with new namespace
            Trade clone4 = ( Trade ) EntityFactory.newEntity( session, trade, true, fi2 );
            assertEquals( 0, clone4.getObjectID() );
            assertNotSame( trade.getGUID(), clone4.getGUID() );
            assertEquals( fi2, clone4.getNamespace() );
            assertNotNull( clone4.getTradeLeg( "LEG1" ) );
            assertEquals( 0, clone4.getTradeLeg( "LEG1" ).getObjectID() );
            assertNotSame( trade.getTradeLeg( "LEG1" ).getGUID(), clone4.getTradeLeg( "LEG1" ).getGUID() );
            assertEquals( fi2, clone4.getTradeLeg( "LEG1" ).getNamespace() );
        }
        catch ( Exception e )
        {
            fail( "Exception in testCloneEntityTree : ", e );
        }
    }

    /**
     * Adds a user externals system id to a user.
     */
    public void testAddEntityReferenceId()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();

            // get user
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            User user = ( User ) uow.readObject( User.class, expr );
            log.info( "got user " + user );

            // get an external system
            ExternalSystem sys = ( ExternalSystem ) uow.readObject( ExternalSystem.class );
            log.info( "got external system " + sys );

            // print all the external system IDs
            Iterator it = user.getExternalSystemIds().iterator();
            while ( it.hasNext() )
            {
                ExternalSystemId id = ( ExternalSystemId ) it.next();
                log.info( "\t with external system id " + id );
            }

            // add external system Id using object
            ExternalSystemId id = new UserExternalSystemIdC();
            id.setExternalSystem( sys );
            id.setName( "Test" + System.currentTimeMillis() );
            user.addExternalSystemId( id );
            log.info( "added external system id " + id );

            // add external system Id using ext sys and id
            // user.addExternalSystemId(sys,"Test" + System.currentTimeMillis())
            // log.info("added external system id " + id);

            // print all the external system IDs
            it = user.getExternalSystemIds().iterator();
            while ( it.hasNext() )
            {
                ExternalSystemId id1 = ( ExternalSystemId ) it.next();
                log.info( "\t with external system id " + id1 );
            }

            uow.commit();

        }
        catch ( Exception e )
        {
            fail( "Error in testEntityReferenceId", e );
        }

    }

    /**
     * Removes a user external system id from a user
     */
    public void testRemoveEntityReferenceId()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();

            // get user
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            User user = ( User ) uow.readObject( User.class, expr );
            log.info( "got user " + user );

            // get an external system
            ExternalSystem sys = ( ExternalSystem ) uow.readObject( ExternalSystem.class );
            log.info( "got external system " + sys );

            ExternalSystemId id = null;

            // print all the external system IDs
            Iterator it = user.getExternalSystemIds().iterator();
            while ( it.hasNext() )
            {
                ExternalSystemId id1 = ( ExternalSystemId ) it.next();
                log.info( "\t with external system id " + id1 );

                if ( id1.getName().startsWith( "Test" ) )
                {
                    id = id1;
                }

            }

            // remove external system Id using object
            if ( id != null )
            {
                user.removeExternalSystemId( id );
                log.info( "removed external system id " + id );
            }

            // print all the external system IDs
            it = user.getExternalSystemIds().iterator();
            while ( it.hasNext() )
            {
                ExternalSystemId id1 = ( ExternalSystemId ) it.next();
                log.info( "\t with external system id " + id1 );
            }

            uow.commit();

        }
        catch ( Exception e )
        {
            fail( "Error in testEntityReferenceId", e );
        }

    }

    /**
     * Tests setting of lastModifiedBy fields
     */
    public void testLastModifiedBy()
    {
        try
        {
            Session session = getPersistenceSession();

            // create new entity without session context expecting null in lastModifiedBy
            //
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            String name = "TEST-" + System.currentTimeMillis();
            Currency crnc = new CurrencyC();
            crnc.setShortName( name );
            crnc.setStatus( 'T' );
            uow.registerObject( crnc );
            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( name );
            crnc = ( Currency ) session.readObject( Currency.class, expr );
            log( "lastModifiedBy=" + crnc.getLastModifiedBy() );
            assertNotNull( crnc.getLastModifiedBy() );

            // update existing entity without session context expecting null in lastModifiedBy
            //
            uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            name = "1TEST-" + System.currentTimeMillis();
            Currency crnc1 = ( Currency ) uow.registerObject( crnc );
            crnc1.setShortName( name );
            crnc1.update();
            uow.commit();

            assertNotNull( crnc.getLastModifiedBy() );

            // get users
            expr = eb.get( "shortName" ).equal( "Integral" );
            User integral = ( User ) session.readObject( User.class, expr );
            log.info( "got user " + integral );

            expr = eb.get( "shortName" ).equal( "HeartbeatUsr" );
            User heartbeat = ( User ) session.readObject( User.class, expr );
            log.info( "got user " + heartbeat );

            // create new entity with session context expecting Integral in lastModifiedBy
            //
            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( ctxt );
            IdcTransaction xact = IdcSessionManager.getInstance().newTransaction( ctxt );
            log.info( "xact user = " + xact.getUser() );
            uow = xact.getUOW();
            uow.removeAllReadOnlyClasses();

            name = "2TEST-" + System.currentTimeMillis();
            crnc = new CurrencyC();
            crnc.setShortName( name );
            crnc.setStatus( 'T' );
            uow.registerObject( crnc );
            xact.commit();

            expr = eb.get( "shortName" ).equal( name );
            crnc = ( Currency ) session.readObject( Currency.class, expr );
            assertNotNull( crnc.getLastModifiedBy() );
            assertEquals( integral, crnc.getLastModifiedBy() );

            // update existing entity with  session context expecting Integral in lastModifiedBy
            //
            ctxt = IdcSessionManager.getInstance().getSessionContext( "HeartbeatUsr" );
            IdcSessionManager.getInstance().setSessionContext( ctxt );
            xact = IdcSessionManager.getInstance().newTransaction( ctxt );
            log.info( "xact user = " + xact.getUser() );
            uow = xact.getUOW();
            uow.removeAllReadOnlyClasses();

            Currency regCrnc = ( Currency ) uow.registerObject( crnc );
            regCrnc.update();
            xact.commit();

            // refresh the currency as with the new toplink, the original object does not seem to have changes done by modifying the DatabaseRow.
            crnc = ( Currency ) PersistenceFactory.newSession().refreshObject( crnc );
            assertNotNull( crnc.getLastModifiedBy() );
            assertEquals( heartbeat, crnc.getLastModifiedBy() );
        }
        catch ( Exception e )
        {
            IdcSessionManager.getInstance().setTransaction( null );
            fail( "Exception in testLastModifiedBy :", e );
        }
    }

    public void testSameAs()
    {
        try
        {
            // test new entities
            Entity entity1 = new EntityC();
            Entity entity2 = new EntityC();
            log( "entity1=" + entity1 + ",entity2=" + entity2 );
            assertFalse( entity1.isSameAs( entity2 ) );
            assertFalse( entity1.isSameAs( new TradeC() ) );
            assertFalse( entity1.isSameAs( null ) );

            // test existing entities
            ReadNamedEntityC reader = new ReadNamedEntityC();
            Organization fi1 = ( Organization ) reader.execute( OrganizationC.class, "FI1" );
            Organization fi2 = ( Organization ) reader.execute( OrganizationC.class, "FI2" );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            Organization regOrg = ( Organization ) uow.registerObject( fi1 );
            log( "org=" + fi1 + ",newOrg=" + regOrg );
            assertFalse( fi1.isSameAs( fi2 ) );
            assertTrue( fi1.isSameAs( regOrg ) );

            // check for user with id 1 and org with id 1
            ReadEntityC entityReader = new ReadEntityC();
            Organization org = ( Organization ) entityReader.execute( Organization.class, 1 );
            User user = ( User ) entityReader.execute( User.class, 1 );
            assertFalse( org.isSameAs( user ) );

            assertFalse( fi1.isSameAs( new Object() ) );
        }
        catch ( Exception e )
        {
            fail( "testSameAs", e );
        }
    }
}
