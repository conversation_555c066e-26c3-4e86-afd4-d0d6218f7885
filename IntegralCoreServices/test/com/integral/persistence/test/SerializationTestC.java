package com.integral.persistence.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

public class SerializationTestC
        extends TestCase
{
    public SerializationTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new SerializationTestC( "testLog" ) );
        return suite;
    }

    public void testLog()
    {
        Log log = LogFactory.getLog( this.getClass() );
        log.debug( "new logger = " + log );
        try
        {

            FileOutputStream ostream = new FileOutputStream( "log.ser" );
            ObjectOutputStream os = new ObjectOutputStream( ostream );
            os.writeObject( log );
            os.flush();
            os.close();

            FileInputStream istream = new FileInputStream( "log.ser" );
            ObjectInputStream is = new ObjectInputStream( istream );
            Log log2 = ( Log ) is.readObject();

            log.debug( "de-serialized logger = " + log2 );
            log2.debug( "using de-serialized logger = " + log2 );
        }
        catch ( Exception e )
        {
            System.out.println( "Error when serializing Log: " + e );
            e.printStackTrace();
        }

    }


}
