package com.integral.persistence.test;


import com.integral.persistence.EntityFormat;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.util.Locale;


public class EntityFormatPTestC
        extends PTestCaseC
{
    static String name = "Entity Format Test";

    public EntityFormatPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new EntityFormatPTestC( "testDisplayName" ) );
        return suite;
    }


    public void testDisplayName()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            User joe = ( User ) PersistenceFactory.newSession().readObject( User.class, expr );

            String pattern = "%sn - %dn";

            // positive formatting default locale
            try
            {
                EntityFormat format = new EntityFormat( User.class, pattern );
                String txt = format.format( joe );
                System.out.println( "DEFAULT LOCALE PATTERN[" + pattern + "] ==format==> <" + txt + '>' );
            }
            catch ( Exception pe )
            {
            }

            // positive formatting french locale
            try
            {
                Locale locale = new Locale( "fr", "CA" );
                EntityFormat format = new EntityFormat( User.class, pattern, locale );
                String txt = format.format( joe );
                System.out.println( "FRENCH LOCALE PATTERN[" + pattern + "] ==format==> <" + txt + '>' );
            }
            catch ( Exception pe )
            {
            }
        }
        catch ( Exception e )
        {
            fail( "Exception e" );
        }
    }

}
