package com.integral.persistence.test;

import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationC;
import com.integral.persistence.Persistence;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.PersistenceMBean;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import org.eclipse.persistence.sessions.Session;

import java.util.Collection;


/**
 * <AUTHOR> Development Corporation.
 */
public class PersistenceMBeanTestC extends MBeanTestCaseC
{
    Persistence persitenceMBean = null;

    public PersistenceMBeanTestC( String aName )
    {
        super( aName );
        persitenceMBean = ( Persistence ) PersistenceFactory.getPersistenceMBean();
    }

    public void testProperties()
    {
        testProperty( persitenceMBean, "databaseType", "JDBC.login.type", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "persistenceMappingProjectClass", "TopLink.project.class.name", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "persistenceMappingLicenseKey", "TopLink.license.key", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "persistenceCacheSubscribeMode", "IDC.Persistence.Cache.Subscribe.Mode", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "persistenceCacheSynchronizationEnabledOnSameVM", "IDC.Persistence.Cache.Synchronization.EnabledOnSameVM", MBeanTestCaseC.BOOLEAN );
        testProperty( persitenceMBean, "databaseJDBCDriverClass", "JDBC.driver.class.name", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "databaseJDBCURLHeader", "JDBC.url.header", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "databaseName", "Database.name", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "preloadedMarketDataSets", "IDC.PRELOADED.MARKETDATASET.LIST", MBeanTestCaseC.COLLECTION_STRING );
        testProperty( persitenceMBean, "ignoredUniqueConstraints", "IDC.VALID.UNIQUE.CONSTRAINTS", MBeanTestCaseC.COLLECTION_STRING );
        testProperty( persitenceMBean, "databaseLogSQL", "TopLink.log.sql", MBeanTestCaseC.BOOLEAN );
        testProperty( persitenceMBean, "toplinkLogDefaultDebuglevel", "TOPLINK.DEFAULT.DEBUG_LEVEL", MBeanTestCaseC.BOOLEAN );
        testProperty( persitenceMBean, "inactiveNoPreLoadClasses", PersistenceMBean.INACTIVE_NO_PRELOAD_CLASSES, MBeanTestCaseC.COLLECTION_CLASS);
        testProperty( persitenceMBean, "dealingTransactionClasses", "IDC.Dealing.Transaction.Classes", MBeanTestCaseC.COLLECTION_CLASS);
        testProperty( persitenceMBean, "periodicDatabaseConnectionCheckAndRecoveryEnabled", PersistenceMBean.PERIODIC_DATABASE_CONNECTION_CHECK_AND_RECOVERY_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( persitenceMBean, "databaseConnectionCheckPeriod", PersistenceMBean.DATABASE_CONNECTION_CHECK_PERIOD, MBeanTestCaseC.LONG );
        testProperty( persitenceMBean, "cachedClientSessionDuringReconnectionEnabled", PersistenceMBean.CACHED_CLIENT_SESSION_DURING_RECONNECTION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( persitenceMBean, "databaseConnectionMonitorStartDelayInMillis", PersistenceMBean.DATABASE_CONNECTION_MONITOR_START_DELAY_IN_MILLIS, MBeanTestCaseC.LONG );
    }
    
    public void testConnectionProperties(){
        testProperty( persitenceMBean, "secondaryDatabaseReadConnectionPoolMinimum", "TopLink.SecondaryConnectionPool.Read.Min", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "secondaryDatabaseReadConnectionPoolMaximum", "TopLink.SecondaryConnectionPool.Read.Max", MBeanTestCaseC.STRING );
        testProperty( persitenceMBean, "secondaryDatabaseWriteConnectionPoolMaximum", "TopLink.SecondaryConnectionPool.Write.Max", MBeanTestCaseC.STRING );
        
    }

    public void testJDBCConnectTimeOutGlobalProperty()
    {
        WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), null, ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), null, ConfigurationProperty.DYNAMIC_SCOPE );
        try
        {
            Session session = PersistenceFactory.newSession();

            WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_IN_MILLIS, "50", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), "50", ConfigurationProperty.DYNAMIC_SCOPE );

            boolean timedOut = false;
            try
            {
                Collection<CreditUtilization> cus = ( Collection<CreditUtilization> ) session.readAllObjects( CreditUtilizationC.class );
                log( "cus=" + cus );
            }
            catch ( Exception e )
            {
                timedOut = true;
            }
            log ( "timeout=" + timedOut );
            assertTrue( PersistenceFactory.getPersistenceMBean().getJDBCNetworkConnectTimeout( ConfigurationFactory.getServerMBean().getVirtualServerType() ) ==  50 );
        }
        catch ( Exception e )
        {
            fail ( "testJDBCConnectTimeOutGlobalProperty" );
        }
    }

    public void testJDBCReadTimeOutGlobalProperty()
    {
        WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), null, ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), null, ConfigurationProperty.DYNAMIC_SCOPE );
        try
        {
            Session session = PersistenceFactory.newSession();

            WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_IN_MILLIS, "20", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), "20", ConfigurationProperty.DYNAMIC_SCOPE );

            boolean timedOut = false;
            try
            {
                long readTimeout = PersistenceFactory.getPersistenceMBean().getJDBCReadTimeout( ConfigurationFactory.getServerMBean().getVirtualServerType() );
                log ( "JDBC read timeout=" + readTimeout );
                Collection<CreditUtilization> cus = ( Collection<CreditUtilization> ) session.readAllObjects( CreditUtilizationC.class );
                log( "cus=" + cus );
            }
            catch ( Exception e )
            {
                timedOut = true;
            }
            log ( "timeout=" + timedOut );
            assertTrue( PersistenceFactory.getPersistenceMBean().getJDBCReadTimeout( ConfigurationFactory.getServerMBean().getVirtualServerType() ) ==  20 );
        }
        catch ( Exception e )
        {
            fail ( "testJDBCReadTimeOutGlobalProperty" );
        }
    }

    public void testJDBCConnectTimeOutVirtualServerTypeProperty()
    {
        WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), null, ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_IN_MILLIS, null, ConfigurationProperty.DYNAMIC_SCOPE );
        try
        {
            Session session = PersistenceFactory.newSession();

            WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), "100", ConfigurationProperty.DYNAMIC_SCOPE );

            boolean timedOut = false;
            try
            {
                Collection<CreditUtilization> cus = ( Collection<CreditUtilization> ) session.readAllObjects( CreditUtilizationC.class );
                log( "cus=" + cus );
            }
            catch ( Exception e )
            {
                timedOut = true;
            }
            log ( "timeout=" + timedOut );
            assertTrue( PersistenceFactory.getPersistenceMBean().getJDBCNetworkConnectTimeout( ConfigurationFactory.getServerMBean().getVirtualServerType() ) ==  100 );
        }
        catch ( Exception e )
        {
            fail ( "testJDBCConnectTimeOutVirtualServerTypeProperty" );
        }
    }

    public void testJDBCReadTimeOutVirtualServerTypeProperty()
    {
        WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), null, ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_IN_MILLIS, null, ConfigurationProperty.DYNAMIC_SCOPE );
        try
        {
            Session session = PersistenceFactory.newSession();

            WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( PersistenceMBean.JDBC_READ_TIMEOUT_IN_MILLIS_PREFIX + ConfigurationFactory.getServerMBean().getVirtualServerType(), "25", ConfigurationProperty.DYNAMIC_SCOPE );

            boolean timedOut = false;
            try
            {
                Collection<CreditUtilization> cus = ( Collection<CreditUtilization> ) session.readAllObjects( CreditUtilizationC.class );
                log( "cus=" + cus );
            }
            catch ( Exception e )
            {
                timedOut = true;
            }
            log ( "timeout=" + timedOut );
            assertTrue( PersistenceFactory.getPersistenceMBean().getJDBCReadTimeout( ConfigurationFactory.getServerMBean().getVirtualServerType() ) ==  25 );
        }
        catch ( Exception e )
        {
            fail ( "testJDBCReadTimeOutVirtualServerTypeProperty" );
        }
    }
}
