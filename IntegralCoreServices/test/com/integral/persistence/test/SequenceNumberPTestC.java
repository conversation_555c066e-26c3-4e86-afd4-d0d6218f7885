package com.integral.persistence.test;


import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.Record;

import java.math.BigDecimal;
import java.util.Hashtable;
import java.util.Vector;


/**
 * Tests allocation of sequence numbers used in transaction ID service EJB
 */
public class SequenceNumberPTestC
        extends PTestCaseC
{
    static String name = "Sequence Number Test";

    public SequenceNumberPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }


    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new SequenceNumberPTestC( "testSequenceNumber" ) );
        return suite;
    }


    private static Hashtable sequences = new Hashtable( 5 );

    /**
     * Returns the next sequence name for a given sequence name
     */
    private long nextNum( String aName )
            throws IllegalArgumentException
    {
        long result = -1;

        SequenceHolderC nextVal = ( SequenceHolderC ) sequences.get( aName );
        if ( nextVal == null )
        {
            // no entry, so fetch next val from db
            long val = nextDatabaseSequence( aName );
            nextVal = new SequenceHolderC( aName, val, 500 );
            sequences.put( aName, nextVal );
        }

        if ( nextVal.count <= 0 )
        {
            // get next sequence number
            long val = nextDatabaseSequence( aName );
            nextVal.value = val;
            nextVal.count = 500; // PersistenceFactory.getMBean().getDatabaseSequencePreallocationSize()));
        }

        result = nextVal.value;
        nextVal.value++;
        nextVal.count--;

        return result;
    }

    private long nextDatabaseSequence( String aName )
            throws IllegalArgumentException
    {
        StringBuilder sql = new StringBuilder( "SELECT IDCTRANSACTIONID_" );
        sql.append( aName );
        sql.append( "_SEQ.NEXTVAL FROM DUAL" );
        log( "executing sql " + sql );

        BigDecimal val = null;
        try
        {
            Vector resultSet = PersistenceFactory.newSession().executeSQL( sql.toString() );
            /*
            for (int i=0; i<resultSet.size(); i++)
            {
                DatabaseRow row = (DatabaseRow)resultSet.elementAt(i);
                Enumeration keys = row.keys();
                while (keys.hasMoreElements())
                {
                    Object key = keys.nextElement();
                    Object value = row.get(key);
                    log("sql result : " + key + " --> " + value);
                }
            }
            */
            Record row = ( Record ) resultSet.elementAt( 0 );
            val = ( BigDecimal ) row.get( "NEXTVAL" );
            log( "sql next val = " + val + " of class " + val.getClass() );
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving next sequence number from DB", e );
            throw new IllegalArgumentException( "Unknown database sequence IDCTRANSACTIONID_" + aName + "_SEQ requested." );
        }
        return val.longValue();
    }

    public void testSequenceNumber()
    {
        String schema = "TRADING";
        try
        {
            for ( int i = 0; i < 600; i++ )
            {
                long seq = nextNum( schema );
                log( "sequence number #" + i + ": \t" + seq );
            }
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving next sequence number for schema " + schema, e );
        }
    }
}
