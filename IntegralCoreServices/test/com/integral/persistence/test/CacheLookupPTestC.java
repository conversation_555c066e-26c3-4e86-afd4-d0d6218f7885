package com.integral.persistence.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.QuoteC;
import com.integral.finance.instrument.Instrument;
import com.integral.finance.instrument.InstrumentC;
import com.integral.persistence.CacheFactory;
import com.integral.persistence.CacheManager;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.DataPreloaderC;
import com.integral.persistence.cache.ShortnameIndex;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.List;


public class CacheLookupPTestC
        extends PTestCaseC
{
    static String name = "Cache Lookup Test";

    public CacheLookupPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }


    protected void setUp() throws Exception
    {
        DataPreloaderC loader = new DataPreloaderC();
        loader.setVerboseMode( true );
        loader.run( null );

        super.setUp();
    }


    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        // suite.addTest(new CacheLookupPTestC("lookupOrganization"));
        suite.addTest( new CacheLookupPTestC( "testLookup" ) );
        return suite;
    }


    public void testLookup()
    {
        try
        {

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "MAIN" );
            Namespace main = ( Namespace ) getPersistenceSession().readObject( Namespace.class, expr );

            expr = eb.get( "shortName" ).equal( "CITI" );
            Namespace citi = ( Namespace ) getPersistenceSession().readObject( Namespace.class, expr );

            expr = eb.get( "shortName" ).equal( "FI1" );
            Namespace fi1 = ( Namespace ) getPersistenceSession().readObject( Namespace.class, expr );

            Organization org = null;

            List ns = new ArrayList();
            ns.add( main );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI1",
                        ns );
                log( "find(com.integral.user.Organization,FI1,main) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI1,main) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( fi1 );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI1",
                        ns );
                log( "find(com.integral.user.Organization,FI1,FI1) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI1,FI1) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( citi );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI1",
                        ns );
                log( "find(com.integral.user.Organization,FI1,CITI) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI1,CITI) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( main );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "MAIN",
                        ns );
                log( "find(com.integral.user.Organization,MAIN,MAIN) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,MAIN,MAIN) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( fi1 );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "MAIN",
                        ns );
                log( "find(com.integral.user.Organization,MAIN,FI1) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,MAIN,FI1) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( citi );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "MAIN",
                        ns );
                log( "find(com.integral.user.Organization,MAIN,CITI) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,MAIN,CITI) ==> " + org );
            }


        }
        catch ( Exception e )
        {
            fail( "lookupOrganization: exception " + e );
        }
    }

    public void testRename()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "MAIN" );
            Namespace main = ( Namespace ) getPersistenceSession().readObject( Namespace.class, expr );

            expr = eb.get( "shortName" ).equal( "FI2" );
            Namespace fi2 = ( Namespace ) getPersistenceSession().readObject( Namespace.class, expr );

            Organization org = null;
            Organization fi2_org = null;

            List ns = new ArrayList();
            ns.add( main );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI2",
                        ns );
                log( "find(com.integral.user.Organization,FI2,main) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI2,main) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( fi2 );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI2",
                        ns );
                fi2_org = org;
                log( "find(com.integral.user.Organization,FI2,FI2) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI2,FI2) ==> " + org );
            }

            log( "---------- change name from FI2 to FI2_CLONE ------------" );

            // now change the name
            //
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            Organization fi2_org_clone = ( Organization ) uow.registerObject( fi2_org );
            fi2_org_clone.setShortName( "FI2_CLONE" );
            uow.commit();

            ns = new ArrayList();
            ns.add( main );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI2",
                        ns );
                log( "find(com.integral.user.Organization,FI2,main) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI2,main) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( main );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI2_CLONE",
                        ns );
                log( "find(com.integral.user.Organization,FI2_CLONE,main) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI2_CLONE,main) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( fi2 );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI2",
                        ns );
                log( "find(com.integral.user.Organization,FI2,FI2) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI2,FI2) ==> " + org );
            }

            ns = new ArrayList();
            ns.add( fi2 );
            try
            {
                org = null;
                org = ( Organization ) ShortnameIndex.findByShortName( com.integral.user.Organization.class,
                        "FI2_CLONE",
                        ns );
                log( "find(com.integral.user.Organization,FI2_CLONE,FI2) ==> " + org );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
                log( "find(com.integral.user.Organization,FI2_CLONE,FI2) ==> " + org );
            }

            log( "---------- change name from FI2_CLONE to FI2 ------------" );
            // now change the name back
            //
            uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            fi2_org_clone = ( Organization ) uow.registerObject( fi2_org );
            fi2_org_clone.setShortName( "FI2" );
            uow.commit();

            log( "---------- test no change on FI2 ------------" );
            uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            fi2_org_clone = ( Organization ) uow.registerObject( fi2_org );
            uow.commit();

            log( "---------- test insert of new entity FI2_NEW ------------" );
            uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            Organization fi2_new = new OrganizationC();
            fi2_new.setShortName( "FI2_NEW" );
            Organization fi2_new_clone = ( Organization ) uow.registerObject( fi2_new );
            uow.commit();

            log( "---------- test remove of new entity FI2_NEW ------------" );
            uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.deleteObject( fi2_new );
            uow.commit();

        }
        catch ( Exception e )
        {
            fail( "lookupOrganization: exception " + e );
        }
    }

    /**
     * Tests whether caching the superclass will automatically cache
     * all implementation classes
     */
    public void testInheritance()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "MAIN" );
            Namespace main = ( Namespace ) getPersistenceSession().readObject( Namespace.class, expr );

            List ns = new ArrayList();
            ns.add( main );

            Currency usd = null;

            // lookup by concrete class
            try
            {
                usd = null;
                usd = ( Currency ) ShortnameIndex.findByShortName( CurrencyC.class,
                        "USD",
                        ns );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
            }
            assertNotNull( usd );

            // lookup by interface
            try
            {
                usd = null;
                usd = ( Currency ) ShortnameIndex.findByShortName( Currency.class,
                        "USD",
                        ns );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
            }
            assertNotNull( usd );

            // lookup by concrete superclass
            try
            {
                usd = null;
                usd = ( Currency ) ShortnameIndex.findByShortName( InstrumentC.class,
                        "USD",
                        ns );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
            }
            assertNotNull( usd );

            // lookup by superclass interface
            try
            {
                usd = null;
                usd = ( Currency ) ShortnameIndex.findByShortName( Instrument.class,
                        "USD",
                        ns );
            }
            catch ( com.integral.exception.IdcNoSuchObjectException nsoe )
            {
            }
            assertNotNull( usd );
        }
        catch ( Exception e )
        {
            fail( "testInheritance: exception " + e );
        }
    }

    public void testCacheFactoryQuoteLookup()
    {
        try
        {
            String guid = "G-479694d4c-12429a9798d-CITIB-1a2bbd-1254829422991";
            Quote newQuote = new QuoteC();
            newQuote.setGUID( guid );
            CacheManager cm = CacheFactory.getCacheManager( "CITIB" );
            cm.put( newQuote );

            // do the look up in the cache.
            Quote quote = CacheFactory.getQuote( guid );
            assertNotNull( quote );
            cm.remove( guid );
        }
        catch ( Exception e )
        {
            fail( "testCacheFactoryQuoteLookup", e );
        }
    }
}
