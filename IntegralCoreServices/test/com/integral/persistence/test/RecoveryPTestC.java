package com.integral.persistence.test;

/*
* $Header: /svnroot/venkat/cvsroot/us/integral5src/IntegralCoreDomain/Source/com/integral/persistence/test/RecoveryPTestC.java,v 1.2.22.4 2009-03-27 20:40:22 thattilp Exp $
*
* $Log: not supported by cvs2svn $
* Revision 1.2.22.3  2008/12/11 03:07:37  singhal
* Initializing StringBuilders
*
* Revision 1.2.22.2  2008/08/22 20:46:45  kasi
* Toplink 10g migration
*
* Revision 1.2.22.1  2008/05/21 04:52:15  singhal
* Code re-formatting
*
* Revision 1.2  2004/07/01 00:17:01  Bernhard
* Converted public fields to private or protected.
*
* Revision 1.1  2004/06/17 21:45:47  Bernhard
* renamed persistence unit tests to ...PTestC.java
*
* Revision 1.6  2003/11/10 21:15:46  Bernhard
* auto-fixed by QualityAnalyzer 2.0
*
* Revision 1.5  2003/10/27 19:05:14  Bernhard
* removed empty line
*
* Revision 1.4  2003/10/27 19:04:51  Bernhard
* test cvs meta characters
*
*/

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationC;
import com.integral.is.AlertMBean;
import com.integral.is.AlertMBeanFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.PersistenceMBean;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadObjectQuery;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.server.ServerSession;

import java.math.BigDecimal;
import java.util.*;


/**
 * Tests whether the persistence framework can successfully reconnect
 * to the database after a database outage.
 * <p/>
 * This tests requires to sever the database connection by hand.
 */
public class RecoveryPTestC
        extends PTestCaseC
{
    static String name = "Database Connection Recovery Test";

    public RecoveryPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }


    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        //suite.addTest( new RecoveryPTestC( "testNamedQuery" ) );
        suite.addTest( new RecoveryPTestC( "testReestablishConnection" ) );
        suite.addTest( new RecoveryPTestC( "testCloseServerSession" ) );
        suite.addTest( new RecoveryPTestC( "testCloseServerSessionAndValueHolderInitialization" ) );
        suite.addTest( new RecoveryPTestC( "testJDBCReadTimeOut" ) );
        suite.addTest( new RecoveryPTestC( "testJDBCConnectTimeOut" ) );
        suite.addTest( new RecoveryPTestC( "testSimpleJDBCConnection" ) );
        return suite;
    }

    /**
     * Tests whether a simple query for the user Integral will work
     * after a database disconnect/reconnect.
     */
    public void testSimpleQuery()
    {
        // perform a query before disconnect
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            Session session = PersistenceFactory.newSession();
            User user = ( User ) session.readObject( User.class, expr );
            log.info( "user before disconnect: " + user );

        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user before disconnect ", e );
        }

        // prompt user to disconnect cable
        System.out.println( ">" );
        System.out.println( ">" );
        System.out.println( ">" );
        System.out.println( "> Please disconnect your computer\'s network cable and press ENTER" );
        try
        {
            int answer1 = System.in.read();
            answer1 = System.in.read();
        }
        catch ( Exception e )
        {
            log.info( "Error when reading user input ", e );
        }

        // perform a query after disconnect
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            Session session = PersistenceFactory.newSession();
            User user = ( User ) session.readObject( User.class, expr );
            log.error( "Error: user was retrieved during disconnect: " + user );
        }
        catch ( Exception e )
        {
            log.info( "Expected error when retrieving user during disconnect" );
        }

        // prompt user to reconnect cable
        System.out.println( ">" );
        System.out.println( ">" );
        System.out.println( ">" );
        System.out.println( "> Please reconnect your computer\'s network cable and press ENTER" );
        try
        {
            int answer2 = System.in.read();
        }
        catch ( Exception e )
        {
            log.info( "Error when reading user input ", e );
        }

        // trigger the reconnection
        // TODO: the first persistence call after the reconnect should actually work!
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            Session session = PersistenceFactory.newSession();
            User user = ( User ) session.readObject( User.class, expr );
        }
        catch ( Exception e )
        {
            // ignore error
        }
        try
        {
            log.debug( "sleeping...." );
            Thread.sleep( 10000 );
        }
        catch ( Exception e )
        {
        }

        // perform a query after reconnect
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            Session session = PersistenceFactory.newSession();
            User user = ( User ) session.readObject( User.class, expr );
            log.info( "User retrieved after reconnect: " + user );

        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user after reconnect ", e );
        }

    }

    /**
     * Tests whether a named query that is registered with the server session
     * for the user Integral will workafter a database disconnect/reconnect.
     */
    public void testNamedQuery()
    {
        // register the named query USER_SHORTNAME with the server session
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( eb.getParameter( "shortName" ) );
            ReadObjectQuery query = new ReadObjectQuery();
            query.setReferenceClass( User.class );
            query.setSelectionCriteria( expr );
            query.addArgument( "shortName" );
            //query.bindAllParameters();
            //query.cacheStatement();

            ServerSession serverSession = PersistenceFactory.getActiveServerSession();
            log.info( "registering query #" + query.hashCode() );
            serverSession.addQuery( "USER_SHORTNAME", query );

        }
        catch ( Exception e )
        {
            log.error( "Error when adding named query USER_SHORTNAME ", e );
        }

        // perform a query before disconnect
        try
        {
            Session session = PersistenceFactory.newSession();
            User user = ( User ) session.executeQuery( "USER_SHORTNAME", "Integral" );
            log.info( "user before disconnect: " + user );

            log.info( "queries registered with client session:" );
            printQueries( ( org.eclipse.persistence.sessions.Session ) PersistenceFactory.newSession() );
            printQueries( PersistenceFactory.getActiveServerSession() );

        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user before disconnect ", e );
        }

        // prompt user to disconnect cable
        System.out.println( ">" );
        System.out.println( ">" );
        System.out.println( ">" );
        System.out.println( "> Please disconnect your computer\'s network cable and press ENTER" );
        try
        {
            int answer1 = System.in.read();
            answer1 = System.in.read();
        }
        catch ( Exception e )
        {
            log.info( "Error when reading user input ", e );
        }

        // perform a query after disconnect
        try
        {
            Session session = PersistenceFactory.newSession();
            User user = ( User ) session.executeQuery( "USER_SHORTNAME", "Integral" );
            log.error( "Error: user was retrieved during disconnect: " + user );

        }
        catch ( Exception e )
        {
            log.info( "Expected error when retrieving user during disconnect ", e );
        }

        // prompt user to reconnect cable
        System.out.println( ">" );
        System.out.println( ">" );
        System.out.println( ">" );
        System.out.println( "> Please reconnect your computer\'s network cable and press ENTER" );
        try
        {
            int answer2 = System.in.read();
        }
        catch ( Exception e )
        {
            log.info( "Error when reading user input ", e );
        }

        // trigger the reconnection
        // TODO: the first persistence call after the reconnect should actually work!
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            Session session = PersistenceFactory.newSession();
            User user = ( User ) session.readObject( User.class, expr );
        }
        catch ( Exception e )
        {
            // ignore error
        }
        try
        {
            log.debug( "sleeping...." );
            Thread.sleep( 10000 );
        }
        catch ( Exception e )
        {
        }

        // perform a simple query after reconnect
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            Session session = PersistenceFactory.newSession();
            User user = ( User ) session.readObject( User.class, expr );
            log.info( "User retrieved after reconnect using simple query: " + user );

        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user after reconnect using a simple query " );
        }

        // perform a named query after reconnect
        // this will fail since the named query is invalid after a reconnect
        try
        {
            Session session = PersistenceFactory.newSession();

            if ( PersistenceFactory.getActiveServerSession().containsQuery( "USER_SHORTNAME" ) )
            {
                log.info( "Named query USER_SHORTNAME still registered after reconnect" );
                User user = ( User ) session.executeQuery( "USER_SHORTNAME-X", "Integral" );
                log.info( "User retrieved after reconnect using named query: " + user );
            }
            else
            {
                log.info( "No named query USER_SHORTNAME registered after reconnect" );
            }

        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user after reconnect using a named query " );
        }

        // perform a named query after reconnect
        try
        {
            Session session = PersistenceFactory.newSession();

            printQueries( ( org.eclipse.persistence.sessions.Session ) session );
            printQueries( PersistenceFactory.getActiveServerSession() );

            if ( PersistenceFactory.getActiveServerSession().containsQuery( "USER_SHORTNAME" ) )
            {
                log.info( "Named query USER_SHORTNAME still registered after reconnect" );
                User user = ( User ) session.executeQuery( "USER_SHORTNAME", "Integral" );
                log.info( "User retrieved after reconnect using named query: " + user );
            }
            else
            {
                log.info( "No named query USER_SHORTNAME registered after reconnect" );
            }


        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user after reconnect using a named query " );
        }

    }

    private void printQueries( org.eclipse.persistence.sessions.Session session )
    {
        log.info( "  queries for session " + session + ": " );
        Iterator keys = session.getQueries().keySet().iterator();
        while ( keys.hasNext() )
        {
            String key = ( String ) keys.next();
            log.info( "\t " + key + " --> " + session.getQuery( key ).hashCode() );
        }
    }

    public void testReestablishConnection()
    {
        try
        {
            Organization org = (Organization) new ReadNamedEntityC().execute( OrganizationC.class, "FI1");
            assertNotNull( org  );

            // now re-establish connection.
            PersistenceFactory.reestablishConnections( false );

            Organization org1 = (Organization) new ReadNamedEntityC().execute( OrganizationC.class, "FI2");
            assertNotNull( org1 );
        }
        catch ( Exception e )
        {
            fail( "testReestablishConnection", e );
        }
    }

    public void testCloseServerSession()
    {
        try
        {
            // initialize alert MBean
            AlertMBean alertMBean = AlertMBeanFactory.getInstance().getAlertMBean();
            Session session = PersistenceFactory.newSession();
            log.info( "Persistence ServerSession=" + session );
            Organization org = ( Organization ) session.readObject( OrganizationC.class, new ExpressionBuilder().get( NamedEntity.ShortName ).equal( "FI1") );

            log.info( "org.hashcode=" + org.hashCode() );
            assertNotNull( org  );

            Map<String, ServerSession> activeSessions = PersistenceFactory.getActiveServerSessions();
            for ( Map.Entry<String, ServerSession> name : activeSessions.entrySet() )
            {
                ServerSession serverSession = name.getValue();
                serverSession.logout();
            }
            activeSessions.clear();

            // now retrieve the session again.
            Session newSession = PersistenceFactory.newSession();
            log.info( "Persistence new session=" + newSession );
            Organization org1 = ( Organization ) newSession.readObject( OrganizationC.class, new ExpressionBuilder().get( NamedEntity.ShortName ).equal( "FI1") );
            log.info( "org1.hashcode=" + org1.hashCode() + ",isSameAs=" + ( org.isSameAs( org1 ) ) + ",equals=" + ( org.equals( org1 )) );
            assertTrue( org1.isSameAs( org ));
            assertFalse( org1.equals( org ));

            // remove the database connection.
            try
            {
                Organization orgX = ( Organization ) newSession.readObject( OrganizationC.class, new ExpressionBuilder().get( NamedEntity.ShortName ).equal( "CITI") );
            }
            catch ( Exception e )
            {
                //
            }
            try
            {
                Organization orgY = ( Organization ) session.readObject( OrganizationC.class, new ExpressionBuilder().get( NamedEntity.ShortName ).equal( "CITI") );
            }
            catch ( Exception e )
            {
                //
            }

            Organization org2 = ( Organization ) session.readObject( OrganizationC.class, new ExpressionBuilder().get( NamedEntity.ShortName ).equal( "FI2") );
            log.info( "org2.hashcode=" + org2.hashCode() + ",isSameAs=" + ( org.isSameAs( org2 ) ) + ",equals=" + ( org.equals( org2 )) );
        }
        catch ( Exception e )
        {
            fail ( "testCloseServerSession", e );
        }
    }

    public void testCloseServerSessionAndValueHolderInitialization()
    {
        try
        {
            // initialize alert MBean
            AlertMBean alertMBean = AlertMBeanFactory.getInstance().getAlertMBean();
            Session session = PersistenceFactory.newSession();
            log.info( "Persistence ServerSession=" + session );
            Organization org = ( Organization ) session.readObject( OrganizationC.class, new ExpressionBuilder().get( NamedEntity.ShortName ).equal( "FI1") );
            LegalEntity defaultLE = null;
            ReportQuery reportQuery = new ReportQuery();
            reportQuery.setReferenceClass( OrganizationC.class );
            ExpressionBuilder eb = new ExpressionBuilder();
            reportQuery.setSelectionCriteria( eb.get( NamedEntity.ShortName ).equal( "FI1") );

            reportQuery.addAttribute("LEID", eb.getField("IDCORG.DEFDEALENTID"));

            Vector legalEntities = ( Vector ) getPersistenceSession().executeQuery( reportQuery );
            for ( int i = 0; i < legalEntities.size(); i++ )
            {
                ReportQueryResult le = ( ReportQueryResult ) legalEntities.get( i );
                BigDecimal leId = (BigDecimal) le.get( "LEID" );
                defaultLE = ( LegalEntity ) session.readObject( LegalEntityC.class, new ExpressionBuilder().get(Entity.ObjectID ).equal( leId ) );
            }


            log.info( "org.hashcode=" + org.hashCode() + ",defaultLe=" + defaultLE  + ",defaultLe.hashCode=" + defaultLE.hashCode() );
            assertNotNull( org  );

            Map<String, ServerSession> activeSessions = PersistenceFactory.getActiveServerSessions();
            for ( Map.Entry<String, ServerSession> name : activeSessions.entrySet() )
            {
                ServerSession serverSession = name.getValue();
                serverSession.logout();
            }
            activeSessions.clear();

            LegalEntity valueHolderLEValue = org.getDefaultDealingEntity();
            log.info( "valueHolderLEValue.hashcode=" + valueHolderLEValue.hashCode() );

            assertTrue( defaultLE.isSameAs( valueHolderLEValue ));
            assertFalse( defaultLE.equals( valueHolderLEValue ));
        }
        catch ( Exception e )
        {
            fail ( "testCloseServerSessionAndValueHolderInitialization", e );
        }
    }

    public void testJDBCReadTimeOut()
    {
        try
        {
            PersistenceFactory.reestablishConnections( false );
            System.setProperty(PersistenceMBean.JDBC_READ_TIMEOUT_PROPERTY, "10" );
            PersistenceFactory.reestablishConnections( false );
            Session session = PersistenceFactory.newSession();
            log.info( "Persistence ServerSession=" + session );

            boolean timedOut = false;
            try
            {

                Collection<CreditUtilization> cus = ( Collection<CreditUtilization> ) session.readAllObjects( CreditUtilizationC.class );
                log( "cus=" + cus );
            }
            catch ( Exception e )
            {
                timedOut = true;
            }
            log ( "timeout=" + timedOut );
        }
        catch ( Exception e )
        {
            fail ( "testJDBCReadTimeOut", e );
        }
    }

    public void testJDBCConnectTimeOut()
    {
        try
        {
            PersistenceFactory.reestablishConnections( false );
            System.setProperty( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_PROPERTY, "10" );

            boolean timedOut = false;
            try
            {
                Session session = PersistenceFactory.newSession();
                Collection<CreditUtilization> cus = ( Collection<CreditUtilization> ) session.readAllObjects( CreditUtilizationC.class );
                log( "cus=" + cus );
            }
            catch ( Exception e )
            {
                timedOut = true;
            }
            log ( "timeout=" + timedOut );
        }
        catch ( Exception e )
        {
            fail ( "testJDBCConnectTimeOut", e );
        }
    }

    public void testSimpleJDBCConnection()
    {
        try
        {
            System.setProperty( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_PROPERTY, "10000" );
            System.setProperty( PersistenceMBean.JDBC_READ_TIMEOUT_PROPERTY, "10000" );
            long t0 = System.currentTimeMillis();
            boolean result = PersistenceFactory.checkSimpleDBConnection();
            log ( "simple connection time=" + ( System.currentTimeMillis() - t0 ) );
            assertTrue( result );
        }
        catch ( Exception e )
        {
            fail ( "testSimpleJDBCConnection", e );
        }
    }

    public void testDatabaseConnectedStatus()
    {
        try
        {
            Session session = getPersistenceSession();
            boolean result = PersistenceFactory.isDatabaseConnected();
            assertTrue( result );
        }
        catch ( Exception e )
        {
            fail ( "testDatabaseConnectedStatus", e );
        }
    }

    public void testJDBCClearConnectTimeOut()
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            PersistenceFactory.reestablishConnections( false );
            System.clearProperty( PersistenceMBean.JDBC_NETWORK_CONNECT_TIMEOUT_PROPERTY );
            PersistenceFactory.reestablishConnections( false );

            boolean timedOut = false;
            try
            {
                Session session1 = PersistenceFactory.newSession();
                Collection<CreditUtilization> cus = ( Collection<CreditUtilization> ) session1.readAllObjects( CreditUtilizationC.class );
                log( "cus=" + cus );
            }
            catch ( Exception e )
            {
                timedOut = true;
            }
            log ( "timeout=" + timedOut );
        }
        catch ( Exception e )
        {
            fail ( "testJDBCClearConnectTimeOut", e );
        }
    }

    public void testJDBCClearReadTimeOut()
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            PersistenceFactory.reestablishConnections( false );
            System.clearProperty( PersistenceMBean.JDBC_READ_TIMEOUT_PROPERTY );
            PersistenceFactory.reestablishConnections( false );
            Session session1 = PersistenceFactory.newSession();
            log.info( "Persistence ServerSession=" + session );

            boolean timedOut = false;
            try
            {
                Collection<CreditUtilization> cus = ( Collection<CreditUtilization> ) session1.readAllObjects( CreditUtilizationC.class );
                log( "cus=" + cus );
            }
            catch ( Exception e )
            {
                timedOut = true;
            }
            log ( "timeout=" + timedOut );
        }
        catch ( Exception e )
        {
            fail ( "testJDBCClearReadTimeOut", e );
        }
    }
}
