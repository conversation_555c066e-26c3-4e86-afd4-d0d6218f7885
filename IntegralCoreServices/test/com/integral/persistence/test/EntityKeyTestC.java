package com.integral.persistence.test;


import com.integral.persistence.Entity;
import com.integral.persistence.EntityC;
import com.integral.persistence.EntityFactory;
import com.integral.persistence.EntityKey;
import com.integral.persistence.NamedEntityC;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;

public class EntityKeyTestC
        extends TestCase
{
    public EntityKeyTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new EntityKeyTestC( "testEntity" ) );
        // suite.addTest(new EntityKeyTestC("testCreationPositive"));
        // suite.addTest(new EntityKeyTestC("testCreationNegative"));
        return suite;
    }

    public void testEntity()
    {
        System.out.println( "------------" );
        Entity e1 = new EntityC();
        System.out.println( "------------" );
        Entity e3 = new EntityC();
        System.out.println( "------------" );
        Entity e2 = new NamedEntityC();
        System.out.println( "------------" );
    }

    public void testCreationPositive()
    {
        int numFails = 0;

        try
        {
            /* TODO: fix unit tests
			EntityKey key1 = EntityFactory.newEntityKey();
			assertNull(key1.getObjectClass());

			EntityKey key2 = EntityFactory.newEntityKey(EntityC.class,2);
			assertEquals(key2.getObjectClass(),EntityC.class);
			assertEquals(key2.getValue(),2);

			EntityKey key3 = EntityFactory.newEntityKey();
			key3.setObjectClass(EntityC.class);
			key3.setValue(3);
			assertEquals(key3.getObjectClass(),EntityC.class);
			assertEquals(key3.getValue(),3);

			String ek4 = key3.toString();
			EntityKey key4 = EntityFactory.newEntityKey(ek4);
			assertEquals(key4.getObjectClass(),EntityC.class);
			assertEquals(key4.getValue(),3);
            */
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "positive creation" );
        }

    }

    public void testCreationNegative()
    {
        int numFails = 0;

        try
        {
            String ek5 = "abc";
            try
            {
                EntityKey key5 = EntityFactory.newEntityKey( ek5 );
                fail( "illegal creation string" );
            }
            catch ( Exception e )
            {
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "negative creation" );
        }

    }

}
