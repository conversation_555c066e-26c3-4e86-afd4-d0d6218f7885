package com.integral.persistence.test;


import com.integral.exception.IdcNoSuchObjectException;
import com.integral.persistence.EntityPatternParserC;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.util.PatternParser;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.text.ParseException;


public class EntityPatternParserPTestC
        extends PTestCaseC
{
    static String name = "Entity Pattern Parser Test";

    public EntityPatternParserPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new EntityPatternParserPTestC( "testFormat" ) );
        suite.addTest( new EntityPatternParserPTestC( "testParse" ) );
        suite.addTest( new EntityPatternParserPTestC( "testDisplayName" ) );
        return suite;
    }


    public void testFormat()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            User joe = ( User ) PersistenceFactory.newSession().readObject( User.class, expr );

            String pattern = "%id-%class-%status-%ns-%sn-%dn-%ln-%desc";
            PatternParser epp = new EntityPatternParserC( pattern );
            try
            {
                System.out.println( "PATTERN[" + pattern + "] ==> <" + epp.format( joe ) + '>' );
            }
            catch ( ParseException pe )
            {
            }

            pattern = "1%id 23%status 4";
            epp = new EntityPatternParserC( pattern );
            try
            {
                System.out.println( "PATTERN[" + pattern + "] ==> <" + epp.format( joe ) + '>' );
            }
            catch ( ParseException pe )
            {
            }
        }
        catch ( Exception e )
        {
            fail( "Exception e" );
        }
    }

    public void testParse()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            User joe = ( User ) PersistenceFactory.newSession().readObject( User.class, expr );

            String pattern = "%id-%status";
            String text = "1-A";
            PatternParser epp = new EntityPatternParserC( pattern );
            try
            {
                Object obj = epp.parseObject( UserC.class, text );
                System.out.println( "PATTERN[" + pattern + "] ==> <" + obj + '>' );
            }
            catch ( ParseException pe )
            {
            }
            catch ( IdcNoSuchObjectException nsoe )
            {
            }
        }
        catch ( Exception e )
        {
            fail( "Exception e" );
        }
    }

    public void testDisplayName()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );
            User joe = ( User ) PersistenceFactory.newSession().readObject( User.class, expr );

            String pattern = "%dn";
            String txt = null;
            PatternParser epp = new EntityPatternParserC( pattern );

            // positive formatting
            try
            {
                txt = epp.format( joe );
                System.out.println( "PATTERN[" + pattern + "] ==format==> <" + txt + '>' );
            }
            catch ( ParseException pe )
            {
            }

            // positive parsing
            // epp = new EntityPatternParserC(pattern);
            try
            {
                Object obj = epp.parseObject( UserC.class, txt );
                System.out.println( "PATTERN[" + pattern + "] ==parse==> <" + obj + '>' );
            }
            catch ( ParseException pe )
            {
            }
            catch ( IdcNoSuchObjectException nsoe )
            {
            }

            // negative parsing
            Object obj = null;
            // epp = new EntityPatternParserC(pattern);
            try
            {
                txt = "foobar";
                obj = epp.parseObject( UserC.class, txt );
                System.out.println( "ERROR PATTERN[" + pattern + "] ==parse==> <" + obj + '>' );
            }
            catch ( ParseException pe )
            {
                System.out.println( "PATTERN[" + pattern + "] ==parse neg 1==> <" + obj + '>' );
            }
            catch ( IdcNoSuchObjectException nsoe )
            {
                System.out.println( "PATTERN[" + pattern + "] ==parse neg 2==> <" + obj + '>' );
            }
        }
        catch ( Exception e )
        {
            fail( "Exception e" );
        }
    }

}
