package com.integral.persistence.test;

// Copyright (c) 2009 Integral Development Corporation.  All Rights Reserved.

import com.integral.broker.model.StreamC;
import com.integral.finance.trade.TradeC;
import com.integral.persistence.configuration.EntityServiceConfiguration;
import com.integral.persistence.configuration.EntityServiceConfigurationFactory;
import com.integral.test.MBeanTestCaseC;
import com.integral.user.OrganizationC;

/**
 * <AUTHOR> Development Corporation.
 */
public class EntityServiceConfigurationMBeanTestC extends MBeanTestCaseC
{
    EntityServiceConfiguration entityServiceConfig = ( EntityServiceConfiguration ) EntityServiceConfigurationFactory.getEntityServiceConfigurationMBean();

    public EntityServiceConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( entityServiceConfig, "JMSMessageDestination", EntityServiceConfiguration.JMS_MESSAGE_DESTINATION, MBeanTestCaseC.STRING );
        testProperty( entityServiceConfig, "multiAppSecondarySessionUpdateEnabled", EntityServiceConfiguration.MULTI_APP_SECONDARY_SESSION_UPDATE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( entityServiceConfig, "multiAppReferenceDataUpdatePolicy", EntityServiceConfiguration.MULTI_APP_REFERENCE_DATA_UPDATE_POLICY, MBeanTestCaseC.INTEGER );
        testProperty( entityServiceConfig, "multiAppAuxiliaryReferenceDataUpdatePeriod", EntityServiceConfiguration.MULTI_APP_AUXILIARY_REFERENCE_DATA_UPDATE_PERIOD, MBeanTestCaseC.LONG );
        testProperty( entityServiceConfig, "referenceDataEntityUpdateEnabled", EntityServiceConfiguration.REFERENCE_DATA_ENTITY_UPDATE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( entityServiceConfig, "referenceDataUpdateNotificationEnabled", EntityServiceConfiguration.REFERENCE_DATA_UPDATE_NOTIFICATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( entityServiceConfig, "referenceDataUpdateNotificationPeriod", EntityServiceConfiguration.REFERENCE_DATA_UPDATE_NOTIFICATION_PERIOD, MBeanTestCaseC.LONG );
        testProperty( entityServiceConfig, "referenceDataPeriodicUpdateNotificationRandomizeEnabled", EntityServiceConfiguration.REFERENCE_DATA_PERIODIC_UPDATE_NOTIFICATION_RANDOMIZE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( entityServiceConfig, "referenceDataPeriodicUpdateNotificationRandomSeedPeriod", EntityServiceConfiguration.REFERENCE_DATA_PERIODIC_UPDATE_NOTIFICATION_RANDOM_SEED_PERIOD, MBeanTestCaseC.LONG );
        testProperty( entityServiceConfig, "multiAppReferenceDataUpdateHandlerThreadPoolMinSize", EntityServiceConfiguration.MULTI_APP_REFERENCE_DATA_UPDATE_HANDLER_THREADPOOL_MIN_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( entityServiceConfig, "multiAppReferenceDataUpdateHandlerThreadPoolMaxSize", EntityServiceConfiguration.MULTI_APP_REFERENCE_DATA_UPDATE_HANDLER_THREADPOOL_MAX_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( entityServiceConfig, "multiAppReferenceDataUpdateHandlerThreadPoolQueueSize", EntityServiceConfiguration.MULTI_APP_REFERENCE_DATA_UPDATE_HANDLER_THREADPOOL_QUEUE_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( entityServiceConfig, "multiAppSecondarySessionUpdateEnabled", EntityServiceConfiguration.MULTI_APP_SECONDARY_SESSION_UPDATE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( entityServiceConfig, "referenceDataUpdateNotificationClasses", EntityServiceConfiguration.REFERENCE_DATA_UPDATE_NOTIFICATION_CLASSES, MBeanTestCaseC.COLLECTION_CLASS );
        testProperty( entityServiceConfig, "transactionIdPrefix", EntityServiceConfiguration.TRANSACTION_ID_PREFIX, MBeanTestCaseC.STRING );
        //testProperty( entityServiceConfig, "directedOrderTransactionIdPrefix", EntityServiceConfiguration.DO_TRANSACTION_ID_PREFIX, MBeanTestCaseC.STRING );
        //testProperty( entityServiceConfig, "directedOrderTransactionIdSuffix", EntityServiceConfiguration.DO_TRANSACTION_ID_SUFFIX, MBeanTestCaseC.STRING );
        //testProperty( entityServiceConfig, "sogTransactionIdSuffix", EntityServiceConfiguration.SOG_TRANSACTION_ID_SUFFIX, MBeanTestCaseC.STRING );
        testProperty( entityServiceConfig, "multiAppEntityUpdateHandlerThreadPoolMinSize", EntityServiceConfiguration.MULTI_APP_ENTITY_UPDATE_HANDLER_THREADPOOL_MIN_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( entityServiceConfig, "multiAppEntityUpdateHandlerThreadPoolMaxSize", EntityServiceConfiguration.MULTI_APP_ENTITY_UPDATE_HANDLER_THREADPOOL_MAX_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( entityServiceConfig, "multiAppEntityUpdateHandlerThreadPoolQueueSize", EntityServiceConfiguration.MULTI_APP_ENTITY_UPDATE_HANDLER_THREADPOOL_QUEUE_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( entityServiceConfig, "multiAppReferenceDataUpdateSingleThreadEnabled", EntityServiceConfiguration.MULTI_APP_REFERENCE_DATA_UPDATE_SINGLE_THREAD_ENBABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( entityServiceConfig, "multiAppReferenceDataDBFailureQueueEnabled", EntityServiceConfiguration.MULTI_APP_REFERENCE_DATA_DB_FAILURE_QUEUE, MBeanTestCaseC.BOOLEAN );
        testProperty( entityServiceConfig, "selectivelyLoadedReferenceDataClasses", EntityServiceConfiguration.REFERENCE_DATA_SELECTIVELY_LOADED_CLASSES, MBeanTestCaseC.COLLECTION_CLASS );
        testProperty( entityServiceConfig, "multiAppRemoteFunctorExecutionEnabled", EntityServiceConfiguration.MULTI_APP_REMOTE_FUNCTOR_EXECUTION_ENABLED, MBeanTestCaseC.BOOLEAN );
    }

    public void testStreamSelectiveLoadingProperty()
    {
        assertTrue ( entityServiceConfig.isSelectivelyLoadedReferenceDataClass ( StreamC.class ) );
        assertFalse ( entityServiceConfig.isSelectivelyLoadedReferenceDataClass ( TradeC.class ) );
        assertFalse ( entityServiceConfig.isSelectivelyLoadedReferenceDataClass ( OrganizationC.class ) );
    }

}
