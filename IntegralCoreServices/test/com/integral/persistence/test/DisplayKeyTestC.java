package com.integral.persistence.test;


import com.integral.persistence.EntityFactory;
import com.integral.persistence.Namespace;
import com.integral.user.OrganizationFunction;
import com.integral.user.OrganizationFunctionC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;

public class DisplayKeyTestC
        extends TestCase
{
    public DisplayKeyTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new DisplayKeyTestC( "testOrganizationFunctionDisplayKey" ) );
        suite.addTest( new DisplayKeyTestC( "testUserDisplayKey" ) );
        return suite;
    }

    public void testOrganizationFunctionDisplayKey()
    {
        OrganizationFunction orgFunc = new OrganizationFunctionC();
        assertEquals( orgFunc.getDisplayKey(), "com.integral.user.OrganizationFunctionC.MAIN.0" );

        Namespace ns = EntityFactory.newNamespace();
        ns.setShortName( "TESTNS" );
        orgFunc.setNamespace( ns );
        assertEquals( orgFunc.getDisplayKey(), "com.integral.user.OrganizationFunctionC.TESTNS.0" );
    }

    public void testUserDisplayKey()
    {
        User joe = UserFactory.newUser( "joe" );
        assertEquals( joe.getDisplayKey(), "com.integral.user.UserC.MAIN.joe" );

        Namespace ns = EntityFactory.newNamespace();
        ns.setShortName( "TESTNS" );
        joe.setNamespace( ns );
        assertEquals( joe.getDisplayKey(), "com.integral.user.UserC.TESTNS.joe" );
    }

}
