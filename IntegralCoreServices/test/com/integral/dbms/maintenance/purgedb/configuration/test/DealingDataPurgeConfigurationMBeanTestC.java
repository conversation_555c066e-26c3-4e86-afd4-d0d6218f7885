package com.integral.dbms.maintenance.purgedb.configuration.test;

// Copyright (c) 2013 Integral Development Corporation.  All Rights Reserved.

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfiguration;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationMBean;
import com.integral.finance.creditLimit.CreditUtilizationEventC;
import com.integral.finance.dealing.fx.FXSingleLegDealC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;

import java.util.Date;

/**
 * <AUTHOR> Development Corporation.
 */
public class DealingDataPurgeConfigurationMBeanTestC extends MBeanTestCaseC
{
    DealingDataPurgeConfiguration configMBean = ( DealingDataPurgeConfiguration ) DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean();

    public DealingDataPurgeConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( configMBean, "dealingDataPurgeEnabled", DealingDataPurgeConfigurationMBean.DEALING_DATA_PURGE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( configMBean, "dealOrderAsyncDeletionEnabled", DealingDataPurgeConfigurationMBean.DEAL_ORDER_ASYNC_DELETION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( configMBean, "multiThreadDealingDataPurgeEnabled", DealingDataPurgeConfigurationMBean.DEALING_DATA_PURGE_MULTI_THREAD_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( configMBean, "dataPurgeThreadPoolSize", DealingDataPurgeConfigurationMBean.DATA_PURGE_THREAD_POOL_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( configMBean, "dataPurgeThreadPoolQueueSize", DealingDataPurgeConfigurationMBean.DATA_PURGE_THREAD_POOL_QUEUE_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( configMBean, "createdDateQueryEnabled", DealingDataPurgeConfigurationMBean.DEALING_DATA_PURGE_CREATED_DATE_QUERY_ENABLED, MBeanTestCaseC.BOOLEAN );
    }

    public void testPurgeKeepPeriod()
    {
        try
        {
            configMBean.setProperty( DealingDataPurgeConfiguration.DEALING_DATA_PURGE_KEEP_PERIOD_PREFIX + FXSingleLegDealC.class.getSimpleName(), "20", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( configMBean.getPurgeKeepPeriod( FXSingleLegDealC.class ) == 20 );

            try
            {
                configMBean.getPurgeKeepPeriod( this.getClass() );
                fail( "Should have thrown a IllegalArgumentException" );
            }
            catch ( Exception e )
            {
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testPurgeKeepPeriod" );
        }
    }

    public void testTTL()
    {
        try
        {
            configMBean.setProperty( DealingDataPurgeConfiguration.DEALING_DATA_PURGE_KEEP_PERIOD_PREFIX + FXSingleLegDealC.class.getSimpleName(), "20", ConfigurationProperty.DYNAMIC_SCOPE, null );
            IdcDate date = DateTimeFactory.newDate();
            Date ttl = configMBean.getTTL( FXSingleLegDealC.class, date );
            assertNotNull( ttl );
            Date ttl1 = configMBean.getTTL( FXSingleLegDealC.class, date );
            assertNotNull( ttl1 );
            assertTrue( ttl.equals( ttl1 ) );
            Date ttl2 = configMBean.getTTL( FXSingleLegDealC.class, date.addDays( 10 ) );
            assertNotNull( ttl2 );
            assertFalse( ttl1.equals( ttl2 ) );

            // check for another class.
            Date ttl4 = configMBean.getTTL( CreditUtilizationEventC.class, date );
            assertNotNull( ttl4 );
            Date ttl5 = configMBean.getTTL( CreditUtilizationEventC.class, date );
            assertNotNull( ttl5 );
            assertTrue( ttl4.equals( ttl5 ) );

            //now the change property itself.
            configMBean.setProperty( DealingDataPurgeConfiguration.DEALING_DATA_PURGE_KEEP_PERIOD_PREFIX + FXSingleLegDealC.class.getSimpleName(), "10", ConfigurationProperty.DYNAMIC_SCOPE, null );
            Date ttl6 = configMBean.getTTL( FXSingleLegDealC.class, date );
            assertFalse( ttl.equals( ttl6 ) );
            assertTrue( ttl.after( ttl6 ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testTTL" );
        }
    }

    public void testFixedPeriodMarketDataSetPurgeKeepPeriod()
    {
        try
        {
            configMBean.setProperty( DealingDataPurgeConfiguration.FIXED_PERIOD_MARKET_DATASET_PURGE_KEEP_PERIOD, "1W", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( DateTimeFactory.newDatePeriod ( "1W" ).asDays (), configMBean.getFixedPeriodMarketDataSetPurgeKeepPeriod ().asDays () );

            configMBean.setProperty( DealingDataPurgeConfiguration.FIXED_PERIOD_MARKET_DATASET_PURGE_KEEP_PERIOD, "1M", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( DateTimeFactory.newDatePeriod ( "1M" ).asDays (), configMBean.getFixedPeriodMarketDataSetPurgeKeepPeriod ().asDays () );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFixedPeriodMarketDataSetPurgeKeepPeriod" );
        }
    }
}
