package com.integral.rds

import com.integral.spaces.config.MetaspacesConfigMBeanImpl
/**
 * Initializes the spaces persistence configuration.
 *
 * <AUTHOR>
 */
class SetupStub extends GroovyTestCase {

	private static final String MS_NAMETEST = "Test"

	public static Properties initConfig() throws Exception {
		Properties props = new Properties();
		props.putAll(propsValues);
		props.setProperty("cluster.clusterone.url", "mvmongo2:60000");
		props.setProperty("mongodb.prefix", MS_NAMETEST);
		return props;
	}

	private static Map<String, String> propsValues = new HashMap<String, String>();

	static {
		propsValues.put("cluster.clusterone.connectionSize", "1");
		propsValues.put("cluster.clusterone.writeConnectionSize", "3");
		propsValues.put("metaspace.TEST-RDS-YMS.readOnly", "false");
		propsValues.put("metaspace.TEST-RDS-YMS.cluster", "clusterone");
		propsValues.put("metaspace.TEST-RDS-YMS.reliabilityLevel", "PRIMARY_RELIABILITY");
		propsValues.put("metaspace.TEST-RDS-YMS.serializationSize", "1048576");
		propsValues.put("metaspace.TEST-RDS-YMS.enableNotifications", "false");
		propsValues.put("metaspace.TEST-RDS-YMS.bufferSize", "8");
		propsValues.put("metaspace.TEST-RDS-YMS.concurrencyFactor", "1");
		propsValues.put("metaspace.TEST-RDS-YMS.txnLoggingEnabled", "false");
		propsValues.put("metaspace.TEST-RDS-YMS.metaspacePrefix", MS_NAMETEST);

		propsValues.put("spacesWorkflowEnabled", "true");
		propsValues.put("virtualServerName", "BA1");
		propsValues.put("IDC.Server.URL", "http://localhost:9982");
		propsValues.put("txLogLocation", ".");

		propsValues.put("Idc.RDS.AMQP.UserName", "integral");
		propsValues.put("Idc.RDS.AMQP.Password", "integral");

		propsValues.put("Idc.RDS.Server.Port", "9982");
		propsValues.put("Idc.RDS.Server.url", "http://localhost:9982");
		propsValues.put("Idc.RDS.AutoID.SEQ", "TEST-RDS-YMS_SEQ");
		propsValues.put("test.config.metaspace.collection", "TEST-RDS-YMS");
		propsValues.put("test.config.metaspace.metaSpaceName", "TEST-RDS-YMS");

		propsValues.put("Idc.RDS.Service.Serializer", "integral");
		propsValues.put("Idc.RDS.AMQP.Exchange.Name", "Test-YMS");
		propsValues.put("Idc.RDS.Client.Id", "TEST_RDS_YMS_BA1");
		propsValues.put("Idc.RDS.Client.Cache.SyncInterval", "1");
	}
	
	public void testDummy(){
		// To avoid junit throwing "junit.framework.AssertionFailedError: No tests "
	}
}
