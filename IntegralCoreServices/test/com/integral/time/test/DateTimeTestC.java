package com.integral.time.test;

import com.integral.exception.IdcAssertionException;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateC;
import com.integral.time.IdcDateTime;
import junit.framework.TestCase;

import java.sql.Time;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;


public class DateTimeTestC
        extends TestCase
{
    protected Log log = LogFactory.getLog( this.getClass() );

    private static String DEFAULT_DATE_PARSING_FORMAT = "yyyy-MM-dd";
    private static DateFormat s_defaultDateParsingFormat = new SimpleDateFormat( DEFAULT_DATE_PARSING_FORMAT );
    private static int maxAllowableDateYearInfoArrSize = ( Calendar.getInstance( TimeZone.getTimeZone( "GMT" ) ).get( Calendar.YEAR ) - 1900 ) + ConfigurationFactory.getServerMBean().getMaxForwardYearsForIdcDate() + 1;

    public DateTimeTestC( String name )
    {
        super( name );
    }

    public void testDoNotAllowIdcDateWithYearMoreThanConstructorSeconds() throws Exception
    {
        try
        {
            DateTimeFactory.newDate( new Date() );
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );
            DateTimeFactory.newDate( "2009-04-23" );
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );

            boolean isInvalid = false;
            try
            {
                DateTimeFactory.newDate( "9913209-07-04" );
                isInvalid = false;
            }
            catch ( Exception e )
            {
                isInvalid = true;
            }
            assertEquals( isInvalid, true );
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );

            try
            {
                DateTimeFactory.newDate( 1, 1, 1800 );
                isInvalid = false;
            }
            catch ( IdcAssertionException e )
            {
                isInvalid = true;
            }
            assertEquals( isInvalid, true );
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );

            try
            {
                DateTimeFactory.newDate( 1, 1, 2500 );
                isInvalid = false;
            }
            catch ( IdcAssertionException e )
            {
                isInvalid = true;
            }
            assertEquals( isInvalid, true );
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );

            int maxYearAllowed = ConfigurationFactory.getServerMBean().getMaxForwardYearsForIdcDate() +
                    Calendar.getInstance( TimeZone.getTimeZone( "GMT" ) ).get( Calendar.YEAR );

            DateTimeFactory.newDate( 1, 1, maxYearAllowed );
            DateTimeFactory.newDate( 1, 1, 2000 );
            DateTimeFactory.newDate( 1, 1, 1900 );
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );

            for ( int i = -2000; i <= 2500; i++ )
            {
                assertEquals( createDate( i ), i >= 1900 && i <= maxYearAllowed );
            }

            try
            {
                DateTimeFactory.newDate( "20090423" );   //"yyyy-MM-dd"
                isInvalid = false;
            }
            catch ( Exception e )
            {
                isInvalid = true;
            }
            assertEquals( isInvalid, true );
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC.testDoNotAllowIdcDateWithYearMoreThanConstructorSeconds" );
        }
        assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );
    }

    public void testAddMonths()
    {
        try
        {
            IdcDate dt = DateTimeFactory.newDate( new Date() );
            boolean isValidMonth = true;
            try
            {
                dt.addMonths( 12000 );
                isValidMonth = true;
            }
            catch ( Exception e )
            {
                isValidMonth = false;
            }
            assertEquals( isValidMonth, false );
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );

            try
            {
                dt.addMonths( ( ConfigurationFactory.getServerMBean().getMaxForwardYearsForIdcDate() - 1 ) * 12 );
                isValidMonth = true;
            }
            catch ( Exception e )
            {
                isValidMonth = false;
            }
            assertEquals( isValidMonth, true );
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC.testAddMonths" );
        }
    }

    public void testValidMonth()
    {
        try
        {
            for ( int i = -1; i <= 15; i++ )
            {
                assertEquals( createDateMonth( i ), i >= 1 && i <= 12 );
            }
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC.testValidMonth" );
        }
    }

    public void testValidDate()
    {
        try
        {
            for ( int i = -1; i <= 35; i++ )
            {
                assertEquals( createDateDate( i ), i >= 1 && i <= 31 );
            }
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC.testValidDate" );
        }
    }


    private boolean createDateMonth( int month )
    {
        try
        {
            DateTimeFactory.newDate( month, 1, 2009 );
        }
        catch ( Exception e )
        {
            return false;
        }
        return true;
    }

    private boolean createDateDate( int date )
    {
        try
        {
            DateTimeFactory.newDate( 1, date, 2009 );
        }
        catch ( Exception e )
        {
            return false;
        }
        return true;
    }

    private boolean createDate( int year )
    {
        try
        {
            DateTimeFactory.newDate( 1, 1, year );
        }
        catch ( Exception e )
        {
            return false;
        }
        return true;
    }

    private boolean createHHMMSSDate( int year )
    {
        try
        {
            DateTimeFactory.newYYMMDDHHMMSS( year, 2, 23, 1, 0, 0 );
        }
        catch ( Exception e )
        {
            return false;
        }
        return true;
    }

    public void testDoNotAllowIdcDateWithYearMoreThanConstructorDaysMonthYear() throws Exception
    {
        try
        {
            int maxYearAllowed = ConfigurationFactory.getServerMBean().getMaxForwardYearsForIdcDate() +
                    Calendar.getInstance( TimeZone.getTimeZone( "GMT" ) ).get( Calendar.YEAR );

            for ( int i = -2000; i <= 2500; i++ )
            {
                assertEquals( createHHMMSSDate( i ), i >= 1900 && i <= maxYearAllowed );
            }
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC.testDoNotAllowIdcDateWithYearMoreThanConstructorDaysMonthYear" );
        }
    }

    public void testDateTimeCacheFormats()
    {
        try
        {
            com.integral.startup.SystemStartupC startup = new com.integral.startup.SystemStartupC();
            startup.startup( null, null );
            IdcDate dt = DateTimeFactory.newDate( null, DEFAULT_DATE_PARSING_FORMAT );
            assertNotNull( "Getting IdcDate out of  DateTimeFactory.newDate(null,DEFAULT_DATE_PARSING_FORMAT).", dt );
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC.testDateTimeCacheFormats" );
        }
    }

    public void testDateTimeCacheFormatsWithoutDefaultFormat()
    {
        try
        {
            com.integral.startup.SystemStartupC startup = new com.integral.startup.SystemStartupC();
            startup.startup( null, null );
            IdcDate dt = DateTimeFactory.newDate( "null" );
            assertNotNull( "Getting IdcDate out of DateTimeFactory.newDate(\"null\").", dt );
        }
        catch ( Exception e )
        {
        }
    }

    public void testDateTimeCacheWithFormat()
    {
        try
        {
            com.integral.startup.SystemStartupC startup = new com.integral.startup.SystemStartupC();
            startup.startup( null, null );

            Date now = new Date();
            String dateString = s_defaultDateParsingFormat.format( now );
            IdcDate dt = DateTimeFactory.newDate( dateString, DEFAULT_DATE_PARSING_FORMAT );
            log.info( "dt = " + dt );

            for ( int i = 0; i < 990; i++ )
            {
                long tm = now.getTime();
                tm += 24 * 60 * 60 * 1000;
                now = new Date( tm );
                dateString = s_defaultDateParsingFormat.format( now );
                dt = DateTimeFactory.newDate( dateString, DEFAULT_DATE_PARSING_FORMAT );
            }

            for ( int i = 0; i < 20; i++ )
            {
                long tm = now.getTime();
                tm += 24 * 60 * 60 * 1000;
                now = new Date( tm );
                dateString = s_defaultDateParsingFormat.format( now );
                dt = DateTimeFactory.newDate( dateString, DEFAULT_DATE_PARSING_FORMAT );
                log.info( "dt = " + dt );
            }
            assertEquals( IdcDateC.getDateYearInfoSize() <= maxAllowableDateYearInfoArrSize, true );
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC: error when running test" );
        }
    }

    public void testDateTimeCache()
    {
        try
        {
            com.integral.startup.SystemStartupC startup = new com.integral.startup.SystemStartupC();
            startup.startup( null, null );

            Date now = new Date();
            String dateString = s_defaultDateParsingFormat.format( now );
            IdcDate dt = DateTimeFactory.newDate( dateString );
            log.info( "dt = " + dt );

            for ( int i = 0; i < 990; i++ )
            {
                long tm = now.getTime();
                tm += 24 * 60 * 60 * 1000;
                now = new Date( tm );
                dateString = s_defaultDateParsingFormat.format( now );
                dt = DateTimeFactory.newDate( dateString );
            }

            for ( int i = 0; i < 20; i++ )
            {
                long tm = now.getTime();
                tm += 24 * 60 * 60 * 1000;
                now = new Date( tm );
                dateString = s_defaultDateParsingFormat.format( now );
                dt = DateTimeFactory.newDate( dateString );
                log.info( "dt = " + dt );
            }
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC: error when running test" );
        }
    }


    public void testDateTime()
    {
        try
        {
            IdcDateTime dt = DateTimeFactory.newDateTime();
            IdcDateTime currentDT = DateTimeFactory.newDateTime();
            Time time;
            Date date;

            log.info( "\nTimezone is " + TimeZone.getDefault().getID() );
            log.info( "Current date time is " + dt );
            log.info( "Current time is " + dt.getTime() );


            time = new Time( 3600L * 12 * 1000L );
            log.info( "\nTime at 3600*12 is " + time );

            TimeZone.setDefault( TimeZone.getTimeZone( "GMT" ) );
            time = new Time( 3600L * 12 * 1000L );
            log.info( "Time (GMT) at 3600*12 is " + time );
            TimeZone.setDefault( TimeZone.getTimeZone( "PST" ) );


            date = new SimpleDateFormat( "hh:mm:ss zzz" ).parse( "01:00:00 GMT" );
            dt = DateTimeFactory.newDateTime( date );
            log.info( "\nTime at 01:00:00 GMT " + dt );

            TimeZone.setDefault( TimeZone.getTimeZone( "GMT" ) );
            date = new SimpleDateFormat( "hh:mm:ss zzz" ).parse( "01:00:00 GMT" );
            dt = DateTimeFactory.newDateTime( date );

            log.info( "Time (GMT) at 01:00:00 GMT is " + dt );
            TimeZone.setDefault( TimeZone.getTimeZone( "PST" ) );


            date = new SimpleDateFormat( "hh:mm:ss" ).parse( "01:00:00" );
            dt = DateTimeFactory.newDateTime( date );
            log.info( "\nTime (PST) at 01:00:00" + dt );

            date = DateFormat.getTimeInstance( DateFormat.SHORT,
                    Locale.US ).parse( "4:00 PM" );
            dt = DateTimeFactory.newDateTime( date );
            dt.getTime();
            log.info( "\nTime (PST) at 4:00PM" + dt );


            TimeZone.setDefault( TimeZone.getTimeZone( "GMT" ) );
            date = new SimpleDateFormat( "hh:mm:ss" ).parse( "01:00:00" );
            dt = DateTimeFactory.newDateTime( date );

            log.info( "Time (GMT) at 01:00:00 is " + dt );
            //TimeZone.setDefault ( TimeZone.getTimeZone("PST") );

            dt = currentDT;
            log.info( "\nDateTime in GMT is " + dt.getDateTime( TimeZone.getTimeZone( "UTC" ) ) );
            log.info( "Time in GMT is " + dt.getDateTime( TimeZone.getTimeZone( "UTC" ) ).getTime() );
            log.info( "Date in GMT is " + dt.getDateTime( TimeZone.getTimeZone( "UTC" ) ).getDate() );

            log.info( "\nDateTime in EST is " + dt.getDateTime( TimeZone.getTimeZone( "EST" ) ) );
            log.info( "Time in EST is " + dt.getDateTime( TimeZone.getTimeZone( "EST" ) ).getTime() );
            log.info( "Date in EST is " + dt.getDateTime( TimeZone.getTimeZone( "EST" ) ).getDate() );

            log.info( "\nDateTime in PST is " + dt.getDateTime( TimeZone.getTimeZone( "PST" ) ) );
            log.info( "Time in PST is " + dt.getDateTime( TimeZone.getTimeZone( "PST" ) ).getTime() );
            log.info( "Date in PST is " + dt.getDateTime( TimeZone.getTimeZone( "PST" ) ).getDate() );

            TimeZone.setDefault( TimeZone.getTimeZone( "PST" ) );

            time = new Time( new SimpleDateFormat( "HH:mm:ss zzz" ).parse( "01:00:00 GMT" ).getTime() );
            dt = DateTimeFactory.newDateTime( DateTimeFactory.newDate( "2002-01-01" ),
                    time, TimeZone.getTimeZone( "GMT" ) );
            log.info( "\nDateTime 2002-01-01 01:00:00 GMT is " + dt );

            time = new Time( new SimpleDateFormat( "HH:mm:ss zzz" ).parse( "01:00:00 PST" ).getTime() );
            dt = DateTimeFactory.newDateTime( DateTimeFactory.newDate( "2002-01-01" ),
                    time, TimeZone.getTimeZone( "PST" ) );
            log.info( "\nDateTime 2002-01-01 01:00:00 PST is " + dt );

            time = new Time( new SimpleDateFormat( "HH:mm:ss zzz" ).parse( "17:00:00 PST" ).getTime() );
            dt = DateTimeFactory.newDateTime( DateTimeFactory.newDate( "2002-01-01" ),
                    time, TimeZone.getTimeZone( "PST" ) );
            log.info( "\nDateTime 2002-01-01 17:00:00 PST is " + dt );

            time = new Time( new SimpleDateFormat( "HH:mm:ss zzz" ).parse( "17:00:00 PST" ).getTime() );
            dt = DateTimeFactory.newDateTime( DateTimeFactory.newDate( "2002-08-01" ),
                    time, TimeZone.getTimeZone( "PST" ) );
            log.info( "\nDateTime 2002-08-01 17:00:00 PST is " + dt );

            time = new Time( new SimpleDateFormat( "HH:mm:ss zzz" ).parse( "20:00:00 PST" ).getTime() );
            dt = DateTimeFactory.newDateTime( DateTimeFactory.newDate( "2002-01-01" ),
                    time, TimeZone.getTimeZone( "GMT" ) );
            log.info( "\nDateTime 2002-01-01 01:00:00 GMT is " + dt );
        }
        catch ( Exception e )
        {
            fail( "DateTimeTestC: error when running test" );
        }
    }

    /**
     * Tests whether setting the timezone in the default calendar will trigger a nullpointer
     * in production
     */
    public void testTimeZoneNullPointer()
    {
        java.util.Date dt = new java.util.Date();

        // construct idcdate
        IdcDateTime idcdt1 = DateTimeFactory.newDateTime( ( java.util.Date ) dt );

        // use null timezone, this should not throw a NP
        try
        {
            IdcDateTime idcdt2 = DateTimeFactory.newDateTime( ( java.util.Date ) dt, null );
        }
        catch ( Exception e )
        {
            fail( "expected null pointer when supplying null TZ" );
        }

        // this should work now
        IdcDateTime idcd3 = DateTimeFactory.newDateTime( ( java.util.Date ) dt );
    }

    public void testDayOfWeek() throws Exception
    {
        Date jdkDate = new Date( 0 );
        IdcDate date = DateTimeFactory.newDate( jdkDate, TimeZone.getTimeZone( "GMT" ) );
        log.warn( "start date=" + date + ",currentYear=" + DateTimeFactory.newDate().getYear() + ",maxFwdYear=" + ConfigurationFactory.getServerMBean().getMaxForwardYearsForIdcDate() );
        int endYear = DateTimeFactory.newDate().getYear() + ConfigurationFactory.getServerMBean().getMaxForwardYearsForIdcDate();
        for ( int i = 0; i < 100000; i++ )
        {
            IdcDate dt = null;
            try
            {
                dt = date.addDays( i );
            }
            catch ( Exception e )
            {
                log.warn( "new date=" + dt + ",startDate=" + date );
                continue;
            }
            if ( dt.getYear() > endYear - 1 )
            {
                log.warn( "Skipping further dates as max year supported is reached. date=" + dt.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN ) );
                break;
            }
            final int idcDateDayOfWeek = dt.getCalendarDayOfWeek();
            final int calDayOfWeek = getDayOfWeek( dt, "GMT" );
            log.warn( "date=" + dt + ",calDayOfWeek=" + calDayOfWeek + ",idcDateDayOfWeek=" + idcDateDayOfWeek );
            assertTrue( "dt=" + dt + ",idcDateDayOfWeek=" + idcDateDayOfWeek + ",calDayOfWeek=" + calDayOfWeek, idcDateDayOfWeek == calDayOfWeek );
        }
    }

    private int getDayOfWeek( IdcDate date, String timeZone )
    {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone( TimeZone.getTimeZone( timeZone ) );
        calendar.setTime( date.asJdkDate() );
        return calendar.get( Calendar.DAY_OF_WEEK );
    }
}