package com.integral.time.test;

import com.integral.test.TestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcSimpleDateFormat;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
//import com.integral.user.UserDateTimeFormatC;

public class DateTimeFormatTestC extends TestCaseC
{
    private Locale[] locales;
    static String name = "Date Time Format Test";


    public static void main( String[] args )
    {
        DateTimeFormatTestC test = new DateTimeFormatTestC( name );
        test.testDisplayFormat();
        test = null;
        System.exit( 0 );
    }

    public DateTimeFormatTestC( String aName )
    {
        super( aName );
        //locales = DateFormat.getAvailableLocales();
        //DateFormatSymbols symbols;
        //for (int i = 0; i < locales.length; i++)
        //{
        //    symbols = new DateFormatSymbols(locales[i]);
        //    System.out.println(locales[i].getDisplayName() + ", Pattern=" + symbols.getLocalPatternChars());
        //symbols.getWeekdays()
        //}
        //Locale.setDefault(new Locale("fr","CA"));

    }

    public void testFormatsOfIdcDate()
    {
        IdcDate date = DateTimeFactory.newDate();
        validateDates( date );
        IdcDate date2 = date.addDays( 1 );
        validateDates( date2 );
        validateDates( date2.addDays( 2 ) );
        validateDates( date2.subtractDays( 5 ) );

        date = DateTimeFactory.newDate( 2, 2, 2008 );
        validateDates( date );
        date2 = date.addDays( 1 );
        validateDates( date2 );
        validateDates( date2.addDays( 2 ) );
        validateDates( date2.subtractDays( 5 ) );

        date = DateTimeFactory.newDate( new Date() );
        validateDates( date );
        date2 = date.addDays( 1 );
        validateDates( date2 );
        validateDates( date2.addDays( 2 ) );
        validateDates( date2.subtractDays( 5 ) );
    }

    private void validateDates( IdcDate date )
    {
        SimpleDateFormat formatter = new SimpleDateFormat( "dd-MM-yyyy" );
        Date jdkDate = date.asJdkDate();
        assertEquals( date.getFormattedDate( IdcDate.DD_MM_YYYY_HYPHEN ), formatter.format( jdkDate ) );
        formatter.applyPattern( "dd/MM/yyyy" );
        assertEquals( date.getFormattedDate( IdcDate.DD_MM_YYYY_FORWARD_SLASH ), formatter.format( jdkDate ) );
        formatter.applyPattern( "dd-MMM-yyyy" );
        assertEquals( date.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN ), formatter.format( jdkDate ) );
        formatter.applyPattern( "dd/MMM/yyyy" );
        assertEquals( date.getFormattedDate( IdcDate.DD_MMM_YYYY_FORWARD_SLASH ), formatter.format( jdkDate ) );
        formatter.applyPattern( "MM-dd-yyyy" );
        assertEquals( date.getFormattedDate( IdcDate.MM_DD_YYYY_HYPHEN ), formatter.format( jdkDate ) );
        formatter.applyPattern( "MM/dd/yyyy" );
        assertEquals( date.getFormattedDate( IdcDate.MM_DD_YYYY_FORWARD_SLASH ), formatter.format( jdkDate ) );
        formatter.applyPattern( "MMM-dd-yyyy" );
        assertEquals( date.getFormattedDate( IdcDate.MMM_DD_YYYY_HYPHEN ), formatter.format( jdkDate ) );
        formatter.applyPattern( "MMM/dd/yyyy" );
        assertEquals( date.getFormattedDate( IdcDate.MMM_DD_YYYY_FORWARD_SLASH ), formatter.format( jdkDate ) );

/*
System.out.println( "DD_MM_YYYY_HYPHEN " + date.getFormattedDate( 0 ) );
System.out.println( "DD_MM_YYYY_FORWARD_SLASH " + date.getFormattedDate( 1 ) );
System.out.println( "DD_MMM_YYYY_HYPHEN" + date.getFormattedDate( 2 ) );
System.out.println( "DD_MMM_YYYY_FORWARD_SLASH " + date.getFormattedDate( 3 ) );
System.out.println( "MM_DD_YYYY_HYPHEN " + date.getFormattedDate( 4 ) );
System.out.println( "MM_DD_YYYY_FORWARD_SLASH " + date.getFormattedDate( 5 ) );
System.out.println( "MMM_DD_YYYY_HYPHEN " + date.getFormattedDate( 6 ) );
System.out.println( "MMM_DD_YYYY_FORWARD_SLASH " + date.getFormattedDate( 7 ) );*/
    }

    private void displayFormatInfo( IdcSimpleDateFormat format )
    {
        System.out.println( "Date formated=" + format.format( new Date() ) );

        System.out.println( "Characters pattern=" + format.getDateFormatSymbols().getLocalPatternChars() );
        System.out.println( "LocalizedPattern=" + format.toLocalizedPattern() );
        System.out.println( "Pattern=" + format.toPattern() );
    }

    private void formatAndParse( IdcDate date, IdcSimpleDateFormat format, String pattern )
    {
        System.out.println( "*****************************************************************************" );
        System.out.println( "*  format and parse a date with format=" + pattern );
        System.out.println( "*****************************************************************************" );
        String result;
        format.applyPattern( pattern );

        result = format.format( date );
        System.out.println( "Format result=" + result );

        System.out.println( "Parsing now this date using the format " + format.toPattern() );
        try
        {
            IdcDate zzz = format.parseDate( result );
            System.out.println( "--> Pass" );
        }
        catch ( Exception e )
        {
            System.out.println( "--> Fail. Exception=" + e );
        }
    }

    private void justParse( IdcSimpleDateFormat format, String pattern, String text )
    {
        System.out.println( "*****************************************************************************" );
        System.out.println( "* " + text + " will be parse using format=" + pattern );
        System.out.println( "*****************************************************************************" );
        String result;
        format.applyPattern( pattern );
        try
        {
            IdcDate zzz = format.parseDate( text );
            System.out.println( "--> Pass" );
        }
        catch ( Exception e )
        {
            System.out.println( "--> Fail. Exception=" + e );
        }
    }

    public void testDisplayFormat()
    {
        //
        System.out.println( "*****************************************************************************" );
        System.out.println( "*  IdcSimpleDateFormat load IdcDateFormatSymbols to format and parse date    " );
        System.out.println( "*  Please look the output log to see the symbols loaded for each item :      " );
        System.out.println( "*  (Day, Month, Symbols Pattern, ...                                         " );
        System.out.println( "*                                                                            " );
        System.out.println( "*  ** NOTE **                                                                " );
        System.out.println( "*  1) this information is logged each time a new IdcDateFormatSymbols        " );
        System.out.println( "*     is instantiated. There is one IdcDateFormatSymbols loaded by Locale.    " );
        System.out.println( "*  2) Look at your application log to see what are the symbols used          " );
        System.out.println( "*  3) The symbols could be customized: (Resource bundle files)               " );
        System.out.println( "*     -> IFS/Properties/IdcCoreResource_<Language>_<Country>.properties      " );
        System.out.println( "*        Available bundle properties:                                        " );
        System.out.println( "*           LocaleExpandedUnitSymbols                                        " );
        System.out.println( "*           LocalePeriodicDateRuleSymbols                                    " );
        System.out.println( "*           LocalePatternCharacters * Not actualy supported                  " );
        System.out.println( "*           LocaleAmPmStrings                                                " );
        System.out.println( "*           LocaleEras                                                       " );
        System.out.println( "*           LocaleMonths                                                     " );
        System.out.println( "*           LocaleShortMonths                                                " );
        System.out.println( "*           LocaleShortWeekdays                                              " );
        System.out.println( "*           LocaleWeekdays                                                   " );
        System.out.println( "*                                                                            " );
        System.out.println( "******************************************************************************" );
        //
        System.out.println( "\n\n" );

        IdcSimpleDateFormat format = DateTimeFactory.newIdcSimpleDateFormat( "ddMMMyyyy" );

        System.out.println( "\n\n" );


        IdcDate date = DateTimeFactory.newDate( new Date() );

        formatAndParse( date, format, "ddMMMyyyy" );

        formatAndParse( date, format, "dd-MMM-yyyy" );

        formatAndParse( date, format, "dd/mm/yyyy" );

        formatAndParse( date, format, "mm/dd/yyyy" );

        formatAndParse( date, format, "ddMMMyy" );

        System.out.println( "\n\n" );
        System.out.println( "******************************************************************************" );
        System.out.println( "******************************************************************************" );
        System.out.println( "\n\n" );

        justParse( format, "ddMMMyy", "03JAN02" );
        justParse( format, "ddMMMyy", "03Jan02" );

        justParse( format, "ddMMMyy", "03/Jan/02" ); // Should failed

        justParse( format, "ddMMMyy", "03Jan2002" ); // Should pass, last 2 digits ignored !

        justParse( format, "ddMMMyy", "030102" ); // Should failed, month invalid

        /*
        try
        {
          System.out.println("Test with z - should fail, GMT-UK not a valide Timezone External ID");
          format.applyPattern("yyyy-MM-dd HH:mm z");
          Date zzz = format.parse("2001-12-13 20:51:50 GMT-UK");
          System.out.println("--> Pass");
        } catch (Exception e) {System.out.println("--> Fail. Exception=" + e);}

        try
        {
          System.out.println("Test with without z should pass, GMT-UK ignore and the actual Locale is use to process the timezone");
          format.applyPattern("yyyy-MM-dd HH:mm");
          Date zzz = format.parse("2001-12-13 20:51:50 GMT-UK");
          System.out.println("--> Pass");
        } catch (Exception e) {System.out.println("--> Fail. Exception=" + e);}
        */

        /*
        IdcDateFormatSymbols symbols = new IdcDateFormatSymbols();

        symbols.setPeriodicDateRuleSymbols(new String[]{"AA", "BB"});
        */

        //DateFormat  format = DateFormat.getDateInstance(java.text.DateFormat.SHORT);

        //System.out.println(format.format(new Date()));

        //IdcSimpleDateFormat sf = UserDateTimeFormatC.getUserDateFormat(null);

        // IdcSimpleDateFormat format = DateTimeFactory.newIdcSimpleDateFormat("yyyy-MMMM-EEEE", new Locale("fr", "CA"));
        // displayFormatInfo(format);

        //format.applyLocalizedPattern(IdcSimpleDateFormat.DATEFORMAT_YYMMDD_SLASH);
        //displayFormatInfo(format);
        //IdcSimpleDateFormat format = DateTimeFactory.newIdcSimpleDateFormat("EEE MMM d hh:mm:s yyyy");
        //displayFormatInfo(format);

        //francais.setDateFormatSymbols(new DateFormatSymbols(new Locale("fr", "CA")));
        //System.out.println("Localized pattern=" + format.toLocalizedPattern());
        //System.out.println("Pattern=" + format.toPattern());

        //IdcSimpleDateFormat myFormat = new IdcSimpleDateFormat("iiii-nn-jj", new Locale("fr","CA"));
        //myFormat.applyLocalizedPattern("\u00F6\u00F6\u00F6\u00F6-nn-jj");
        //System.out.println (myFormat.format(new Date()));
        //System.out.println("\u0032");
        //IdcDateFormatSymbols symbols;

        //symbols = DateTimeFactory.newIdcDateFormatSymbols();
        //System.out.println("Default=" + symbols.getIntegralFormatPattern(IdcDateFormatSymbols.DATEFORMAT_YYYYMMDD_SLASH));
        //symbols = DateTimeFactory.newIdcDateFormatSymbols(new Locale("fr","CA"));
        //System.out.println("fr,CA=" + symbols.getIntegralFormatPattern(IdcDateFormatSymbols.DATEFORMAT_YYYYMMDD_SLASH));
        //symbols = DateTimeFactory.newIdcDateFormatSymbols(new Locale("en","CA"));
        //System.out.println("en,CA=" + symbols.getIntegralFormatPattern(IdcDateFormatSymbols.DATEFORMAT_YYYYMMDD_SLASH));

        //String toto = new String("\u3001", "");

        //francais.applyPattern("aaaa-MMMM-EE");

        //System.out.println ("English=" + english.format(new Date()) + ", Francais=" + francais.format(new Date()));
        //francais.applyLocalizedPattern("aaaa");

        //francais.applyLocalizedPattern("aaaa");
        //francais.applyPattern("aaaa");
        //francais.f

        //System.out.println("English Pattern=" + english.getDateFormatSymbols().getLocalPatternChars() + ", Pattern Francais=" + francais.getDateFormatSymbols().getLocalPatternChars());

        //System.out.println("Pattern Francais=" + francais.getDateFormatSymbols().getLocalPatternChars());

        //GyMdkHmsSEDFwWahKz
        //GanjkHmsSEDFwWxhKz
    }
}