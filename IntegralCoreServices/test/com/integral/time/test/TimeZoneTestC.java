package com.integral.time.test;


import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.time.IdcSimpleDateFormat;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;

import java.text.FieldPosition;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.SimpleTimeZone;
import java.util.TimeZone;

public class TimeZoneTestC
        extends TestCase
{
    public TimeZoneTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new TimeZoneTestC( "testPrintAllTimeZones" ) );
        suite.addTest( new TimeZoneTestC( "testFormat" ) );
        suite.addTest( new TimeZoneTestC( "testDaylightSavingsTime" ) );
        return suite;
    }

    /**
     * Tests whether the standard timezone WET - Western European Time
     * automatically adjusts fro dayslight savings time by comparing it with GMT.
     * GMT does not automatically adjust, but WET should.
     */
    public void testDaylightSavingsTime()
    {
        try
        {

            TimeZone gmt = TimeZone.getTimeZone( "GMT" );
            TimeZone.setDefault( gmt );
            System.out.println( "GMT: useDaylightTime() = " + gmt.useDaylightTime() );

            Date currentTime_1 = new Date();
            SimpleDateFormat formatter_1 = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a z" );
            formatter_1.setTimeZone( gmt );
            String dateString_1 = formatter_1.format( currentTime_1 );
            System.out.println( "\t\tGMT now (short form):\t" + dateString_1 );

            TimeZone wet = TimeZone.getTimeZone( "WET" );
            TimeZone.setDefault( wet );
            System.out.println( "WET: useDaylightTime() = " + gmt.useDaylightTime() );

            Date currentTime_2 = new Date();
            SimpleDateFormat formatter_2 = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a z" );
            formatter_2.setTimeZone( wet );
            String dateString_2 = formatter_2.format( currentTime_2 );
            System.out.println( "\t\tWET now (short form):\t" + dateString_2 );

        }
        catch ( Exception e )
        {
            System.out.println( "got exception " + e );
            e.printStackTrace();
        }
    }

    public void testFormat()
    {
        try
        {
            //
            // IdcDate
            //
            IdcDate dt = DateTimeFactory.newDate();
            StringBuffer buf = new StringBuffer();

            IdcSimpleDateFormat formatter = new IdcSimpleDateFormat();
            formatter.format( dt, buf, new FieldPosition( 0 ) );
            System.out.println( "string format of IdcDate <" + dt + "> is <" + buf + '>' );

            //
            // IdcDateTime
            //
            IdcDateTime dt2 = DateTimeFactory.newDateTime();
            StringBuffer buf2 = new StringBuffer();

            IdcSimpleDateFormat formatter2 = new IdcSimpleDateFormat();
            formatter.format( dt2, buf2, new FieldPosition( 0 ) );
            System.out.println( "string format of IdcDateTime <" + dt2 + "> is <" + buf2 + '>' );
        }
        catch ( Exception e )
        {
            System.out.println( "got exception " + e );
            e.printStackTrace();
        }
    }

    public void testPrintAllTimeZones()
    {
        TimeZone tm = TimeZone.getTimeZone( "GMT" );
        TimeZone.setDefault( tm );

        // get 2 dates that fall outside and inside daylight savings
        SimpleDateFormat formatter = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a" );
        String febDateString = "2001.02.01 AD at 02:00:00 PM";
        formatter.setTimeZone( tm );
        Date febDate = formatter.parse( febDateString, new ParsePosition( 0 ) );

        formatter = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a" );
        String julyDateString = "2001.07.01 AD at 02:00:00 PM";
        formatter.setTimeZone( tm );
        Date julyDate = formatter.parse( julyDateString, new ParsePosition( 0 ) );

        // add new one

        SimpleTimeZone bb = new SimpleTimeZone( 2, "Bernhard" );

        TimeZone pst = TimeZone.getTimeZone( "PST" );

        String[] ids = TimeZone.getAvailableIDs();
        for ( int i = 0; i < ids.length; i++ )
        {
            System.out.println( "\t " + ids[i] );
            TimeZone tz = ( TimeZone ) TimeZone.getTimeZone( ids[i] );

            System.out.println( "\t\tDisplay Name [daylight,short]    :\t" + tz.getDisplayName( true, TimeZone.SHORT ) );
            System.out.println( "\t\tDisplay Name [not daylight,short]:\t" + tz.getDisplayName( false, TimeZone.SHORT ) );
            System.out.println( "\t\tDisplay Name [daylight,long]     :\t" + tz.getDisplayName( true, TimeZone.LONG ) );
            System.out.println( "\t\tDisplay Name [not daylight,long] :\t" + tz.getDisplayName( false, TimeZone.LONG ) );
            System.out.println( "\t\tuse DST:\t" + tz.useDaylightTime() );
            if ( tz.useDaylightTime() )
            {
                System.out.println( "\t\tDST Offset:\t" + tz.getDSTSavings() );
            }


            Date currentTime_1 = new Date();

            formatter = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a z" );
            formatter.setTimeZone( tz );
            String dateString = formatter.format( currentTime_1 );
            System.out.println( "\t\ttoday (short form):\t" + dateString );

            formatter = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a zzzz" );
            formatter.setTimeZone( tz );
            dateString = formatter.format( currentTime_1 );
            System.out.println( "\t\ttoday (long form):\t" + dateString );

            // test conversions
            //
            System.out.println( "\t\ttoday (GMT) - Date:\t" + currentTime_1 );

            formatter = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a" );
            formatter.setTimeZone( tz );
            dateString = formatter.format( currentTime_1 );
            System.out.println( "\t\ttoday (local timezone) - Format:\t" + dateString );

            formatter = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a" );
            formatter.setTimeZone( tz );
            Date currentTime_2 = formatter.parse( dateString, new ParsePosition( 0 ) );
            System.out.println( "\t\ttoday (local timezone) - Date:\t" + currentTime_2 );

            // dateString = "2001.08.13 AD at 08:54:01 PM";
            dateString = "2001.12.13 AD at 08:57:45 PM";

            formatter = new SimpleDateFormat( "yyyy.MM.dd G 'at' hh:mm:ss a" );
            formatter.setTimeZone( tz );
            currentTime_2 = formatter.parse( dateString, new ParsePosition( 0 ) );
            System.out.println( "\t\ttoday (GMT) - Date:\t" + currentTime_2 );

            // test daylight display
            formatter = new SimpleDateFormat( "yyyy-MM-dd hh:mm:ss zzzz" );
            formatter.setTimeZone( tz );
            String febDateString1 = formatter.format( febDate );
            System.out.println( "\t\tFeb 1 - Date:\t" + febDateString1 );

            formatter = new SimpleDateFormat( "yyyy-MM-dd hh:mm:ss zzzz" );
            formatter.setTimeZone( tz );
            String julyDateString1 = formatter.format( julyDate );
            System.out.println( "\t\tJuly 1 - Date:\t" + julyDateString1 );

            String idName = ids[i];
            if ( idName.length() >= 32 )
            {
                System.out.println( "\t\tALERT: timezone id too long for DB" );
            }

            System.out.println( "" );

        }

    }


}
