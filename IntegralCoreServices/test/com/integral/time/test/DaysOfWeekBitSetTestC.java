package com.integral.time.test;

import com.integral.test.TestCaseC;
import com.integral.time.DaysOfWeekBitSetC;
import java.util.Calendar;

public class DaysOfWeekBitSetTestC extends TestCaseC
{
    public DaysOfWeekBitSetTestC( String aName )
    {
        super( aName );
    }

    public void testDaysOfWeekBitSet()
    {
       try
        {
            DaysOfWeekBitSetC dofbs = new DaysOfWeekBitSetC();

            assertFalse( dofbs.isSunday() );
            assertFalse( dofbs.isMonday() );
            assertFalse( dofbs.isTuesday() );
            assertFalse( dofbs.isWednesday() );
            assertFalse( dofbs.isThursday() );
            assertFalse( dofbs.isFriday() );
            assertFalse( dofbs.isSaturday() );
            assertFalse( dofbs.isDayOfWeekSet( Calendar.SUNDAY ) );

            dofbs.setSunday( true );
            log( "Day of Week Bit set=" + dofbs );
            assertEquals( "Expected active days = [Sunday]", dofbs.getActiveDays().toString(), "[Sunday]" );
            
            // check the support flag is set correctly
            assertTrue( dofbs.isSunday() );
            assertFalse( dofbs.isMonday() );
            assertFalse( dofbs.isTuesday() );
            assertFalse( dofbs.isWednesday() );
            assertFalse( dofbs.isThursday() );
            assertFalse( dofbs.isFriday() );
            assertFalse( dofbs.isSaturday() );
            assertTrue( dofbs.isDayOfWeekSet( Calendar.SUNDAY ) );
            assertEquals( "Expected active days = [Sunday]", dofbs.getActiveDays().toString(), "[Sunday]" );
            
            // now check double setting.
            dofbs.setSunday( true );
            assertTrue( dofbs.isSunday() );
            assertFalse( dofbs.isMonday() );
            assertFalse( dofbs.isTuesday() );
            assertFalse( dofbs.isWednesday() );
            assertFalse( dofbs.isThursday() );
            assertFalse( dofbs.isFriday() );
            assertFalse( dofbs.isSaturday() );
            assertTrue( dofbs.isDayOfWeekSet( Calendar.SUNDAY ) );
            
            // now set the value as false
            dofbs.setSunday( false );
            assertFalse( dofbs.isSunday() );
            assertFalse( dofbs.isMonday() );
            assertFalse( dofbs.isTuesday() );
            assertFalse( dofbs.isWednesday() );
            assertFalse( dofbs.isThursday() );
            assertFalse( dofbs.isFriday() );
            assertFalse( dofbs.isSaturday() );
            assertFalse( dofbs.isDayOfWeekSet( Calendar.SUNDAY ) );
            
            // now set the values of false again.
            dofbs.setSunday( false );
            assertFalse( dofbs.isSunday() );
            assertFalse( dofbs.isMonday() );
            assertFalse( dofbs.isTuesday() );
            assertFalse( dofbs.isWednesday() );
            assertFalse( dofbs.isThursday() );
            assertFalse( dofbs.isFriday() );
            assertFalse( dofbs.isSaturday() );

            // now set all the values to true
            dofbs.setSunday( true );
            dofbs.setMonday( true );
            dofbs.setTuesday( true );
            dofbs.setWednesday( true );
            dofbs.setThursday( true );
            dofbs.setFriday( true );
            dofbs.setSaturday( true );
            assertTrue( dofbs.isSunday() );
            assertTrue( dofbs.isMonday() );
            assertTrue( dofbs.isTuesday() );
            assertTrue( dofbs.isWednesday() );
            assertTrue( dofbs.isThursday() );
            assertTrue( dofbs.isFriday() );
            assertTrue( dofbs.isSaturday() );
            assertTrue( dofbs.isDayOfWeekSet( Calendar.WEDNESDAY ) );
            
            // do a mix of true and false
            dofbs.setSunday( true );
            dofbs.setMonday(false);
            dofbs.setTuesday( false );
            dofbs.setWednesday( true );
            dofbs.setThursday( false );
            dofbs.setFriday( true );
            dofbs.setSaturday( false );
            assertTrue( dofbs.isSunday() );
            assertFalse(dofbs.isMonday());
            assertFalse( dofbs.isTuesday() );
            assertTrue( dofbs.isWednesday() );
            assertFalse( dofbs.isThursday() );
            assertTrue( dofbs.isFriday() );
            assertFalse( dofbs.isSaturday() );
            assertFalse( dofbs.isDayOfWeekSet( Calendar.THURSDAY ) );
            assertTrue( dofbs.isDayOfWeekSet( Calendar.FRIDAY ) );
            assertEquals( "Expected active days = [Sunday, Wednesday, Friday]", dofbs.getActiveDays().toString(), "[Sunday, Wednesday, Friday]" );
            
            // now set all the values to true again
            dofbs.setSunday( true );
            dofbs.setMonday( true );
            dofbs.setTuesday( true );
            dofbs.setWednesday( true );
            dofbs.setThursday( true );
            dofbs.setFriday( true );
            dofbs.setSaturday( true );
            assertTrue( dofbs.isSunday() );
            assertTrue( dofbs.isMonday() );
            assertTrue( dofbs.isTuesday() );
            assertTrue( dofbs.isWednesday() );
            assertTrue( dofbs.isThursday() );
            assertTrue( dofbs.isFriday() );
            assertTrue( dofbs.isSaturday() );
            assertTrue( dofbs.isDayOfWeekSet( Calendar.SATURDAY ) );

            // now set all the values to false
            dofbs.setSunday( false );
            dofbs.setMonday( false );
            dofbs.setTuesday( false );
            dofbs.setWednesday( false );
            dofbs.setThursday( false );
            dofbs.setFriday( false );
            dofbs.setSaturday( false );
            assertFalse( dofbs.isSunday() );
            assertFalse( dofbs.isMonday() );
            assertFalse( dofbs.isTuesday() );
            assertFalse( dofbs.isWednesday() );
            assertFalse( dofbs.isThursday() );
            assertFalse( dofbs.isFriday() );
            assertFalse( dofbs.isSaturday() );
            assertFalse( dofbs.isDayOfWeekSet( Calendar.SATURDAY ) );
            
            dofbs.setMonday( true );
            assertTrue( dofbs.getDaysDifference( Calendar.MONDAY ) == 0 );
            assertTrue( dofbs.getDaysDifference( Calendar.SUNDAY ) == 1 );
            assertTrue( dofbs.getDaysDifference( Calendar.TUESDAY ) == 6 );
            dofbs.setFriday( true );
            assertTrue( dofbs.getDaysDifference( Calendar.TUESDAY ) == 3 );
            log( "Day of Week Bit set=" + dofbs );
        }
        catch ( Exception e )
        {
            fail( "days of week bit set failure :exception" + e );
        }
    }

}