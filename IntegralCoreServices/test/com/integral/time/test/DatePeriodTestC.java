package com.integral.time.test;


import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;

public class DatePeriodTestC
        extends TestCase
{
    public DatePeriodTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new DatePeriodTestC( "checkSeconds" ) );
        suite.addTest( new DatePeriodTestC( "checkMinutes" ) );
        return suite;
    }

    public void checkSeconds()
    {
        try
        {
            DatePeriod dp = DateTimeFactory.newDatePeriod( "0S" );
            assertEquals( 0, dp.getSeconds() );
            assertEquals( 0, dp.asSeconds() );
            assertEquals( 0, dp.getMinutes() );
            assertEquals( 0.0, dp.asMinutes(), 0 );

            dp = DateTimeFactory.newDatePeriod( "1S" );
            assertEquals( 1, dp.getSeconds() );
            assertEquals( 1, dp.asSeconds() );
            assertEquals( 0, dp.getMinutes() );
            assertEquals( 0.0, dp.asMinutes(), 0 );

            dp = DateTimeFactory.newDatePeriod( "61S" );
            assertEquals( 61, dp.getSeconds() );
            assertEquals( 61, dp.asSeconds() );
            assertEquals( 1, dp.getMinutes() );
            assertEquals( 1.0, dp.asMinutes(), 0 );
        }
        catch ( Exception e )
        {
            System.out.println( "got exception " + e );
            e.printStackTrace();
        }
    }

    public void checkMinutes()
    {
        try
        {
            DatePeriod dp = DateTimeFactory.newDatePeriod( "0T" );
            assertEquals( 0, dp.getSeconds() );
            assertEquals( 0, dp.asSeconds() );
            assertEquals( 0, dp.getMinutes() );
            assertEquals( 0, dp.asMinutes(), 0 );

            dp = DateTimeFactory.newDatePeriod( "1T" );
            assertEquals( 0, dp.getSeconds() );
            assertEquals( 60, dp.asSeconds() );
            assertEquals( 1, dp.getMinutes() );
            assertEquals( 1.0, dp.asMinutes(), 0 );

            dp = DateTimeFactory.newDatePeriod( "61T" );
            assertEquals( 0, dp.getSeconds() );
            assertEquals( 61 * 60, dp.asSeconds() );
            assertEquals( 1, dp.getMinutes() );
            assertEquals( 61.0, dp.asMinutes(), 0 );
        }
        catch ( Exception e )
        {
            System.out.println( "got exception " + e );
            e.printStackTrace();
        }
    }


}
