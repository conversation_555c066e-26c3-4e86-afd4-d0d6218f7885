package com.integral.collections.test;


import com.integral.test.TestCaseC;
import com.integral.util.MultiPropertyComparatorC;
import com.integral.util.PropertyComparatorC;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MultiPropertySortTestC
        extends TestCaseC
{
    public MultiPropertySortTestC( String name )
    {
        super( name );
    }

    public void testSort()
    {
        try
        {
            ArrayList l = new ArrayList();

            l.add( new TestDataObj( "A", "A", "Z", "3" ) );
            l.add( new TestDataObj( "A", "A", "Y", "4" ) );
            l.add( new TestDataObj( "A", "A", "X", "1" ) );
            l.add( new TestDataObj( "C", "C", "Z", "2" ) );
            l.add( new TestDataObj( "B", "B", "Z", "6" ) );
            l.add( new TestDataObj( "B", "B", "X", "7" ) );
            l.add( new TestDataObj( "B", "A", "Y", "8" ) );

            log( "O  : " + l );

            ArrayList tempList = null;
            List propList = new ArrayList();

            propList.add( "prop1" );
            propList.add( "prop2" );
            propList.add( "prop3" );
            propList.add( "prop4" );

            PropertyComparatorC pC1 = new PropertyComparatorC( "prop1" );
            PropertyComparatorC pC2 = new PropertyComparatorC( "prop2" );
            PropertyComparatorC pC3 = new PropertyComparatorC( "prop3" );

            MultiPropertyComparatorC mPC = new MultiPropertyComparatorC( propList, false );

            tempList = ( ArrayList ) l.clone();
            Collections.sort( tempList );
            log( "R1 : " + tempList );
/*		
            tempList = (ArrayList)l.clone();
            Collections.sort(tempList, pC1);
            log("R2 : " + tempList);

            tempList = (ArrayList)l.clone();
            Collections.sort(tempList, pC2);
            log("R3 : " + tempList);

            tempList = (ArrayList)l.clone();
            Collections.sort(tempList, pC3);
            log("R4 : " + tempList);
*/
            tempList = ( ArrayList ) l.clone();
            Collections.sort( tempList, mPC );
            log( "R5 : " + tempList );

        }
        catch ( Exception e )
        {
            log.error( "Error", e );
        }
    }

}