package com.integral.collections.test;

class TestDataObj implements Comparable
{

    private String prop1;
    private String prop2;
    private String prop3;
    private String prop4;

    public TestDataObj()
    {

    }

    public TestDataObj( String prop1, String prop2,
                        String prop3, String prop4 )
    {
        this.prop1 = prop1;
        this.prop2 = prop2;
        this.prop3 = prop3;
        this.prop4 = prop4;
    }

    public String getProp1()
    {
        return this.prop1;
    }

    public String getProp2()
    {
        return this.prop2;
    }

    public String getProp3()
    {
        return this.prop3;
    }

    public String getProp4()
    {
        return this.prop4;
    }

    public void setProp1( String prop )
    {
        this.prop1 = prop;
    }

    public void setProp2( String prop )
    {
        this.prop2 = prop;
    }

    public void setProp3( String prop )
    {
        this.prop3 = prop;
    }

    public void setProp4( String prop )
    {
        this.prop4 = prop;
    }

    public String toString()
    {
        return prop1 + ' ' + prop2 + ' ' + prop3 + ' ' + prop4;
    }

    public int compareTo( Object o )
    {
        return prop1.compareTo( ( ( TestDataObj ) o ).getProp1() );
    }
}
