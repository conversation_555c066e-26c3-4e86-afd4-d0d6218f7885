package com.integral.USI.test;

import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationFactory;
import junit.framework.TestCase;

import com.integral.SEF.SEFMBean;
import com.integral.SEF.SEFMBeanC;
import com.integral.SEF.USIConfig;
import com.integral.SEF.USIInfo;
import com.integral.USI.USIUtilC;
import com.integral.finance.calculator.CalculatorFactory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXLegDealingPriceC;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.finance.trade.TradeClassification;
import com.integral.finance.trade.TradeClassificationC;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.persistence.ExternalSystem;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.user.User;
import com.integral.user.UserC;

public class USIUtilTestC extends PTestCaseC 
{
	private ReadNamedEntityC namedEntityReader;
    public static ExternalSystem CLIENTSDK;
    private User user;
    private LegalEntity FILE;
    private LegalEntity LPLE;
    private String FIName = "SEFFI1";
    private String LPName = "SEFLP1";
    
	public USIUtilTestC( String aName )
	{
		super(aName);
		namedEntityReader = new ReadNamedEntityC();
		CLIENTSDK = ( ExternalSystem ) namedEntityReader.execute( ExternalSystem.class, "ClientSDK" );
		user = ( User ) namedEntityReader.execute( UserC.class, "SEFFI1mm1" );
		
    	FILE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, FIName );
    	LPLE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, LPName );
	}
	
	public void testUSI()
	{
		try
		{
			runTestUSI();
		}
		finally
		{
			TradingParty tradingParty = CounterpartyUtilC.getTradingParty( LPLE, FILE.getOrganization() );
			tradingParty.setGenerateUSI( false );
		}
	}
	
	private void runTestUSI()
	{
		Request request = new RequestC();
		Trade trade = new TradeC();
		trade.setTradeClassification((TradeClassification) namedEntityReader.execute(TradeClassificationC.class, ISCommonConstants.TRD_CLSF_OR));
		request.setTrade(trade);
		request.setCounterparty(FILE);
		request.initUSIMaps();

		SEFMBean mbean = ConfigurationFactory.getSefMBean();

		String txIdBase = "0123456789";
		String txId = "FXI" + txIdBase;
		request.setTransactionID(txId);
		trade.setTransactionID(txId);
		String USI = USIUtilC.getGeneratedUSI(trade, FILE.getOrganization(), LPLE.getOrganization()).getUSI();
		assertTrue(USI != null);
		USIConfig usiConfig = mbean.getSDRUSIConfig();
		assertTrue(USI.equals(usiConfig.getUSIPrefix() + usiConfig.getUSIValuePrefix() + txIdBase));

		String LPFIPrefixProperty = SEFMBean.SEF_USI_PREFIX + "." + LPName + "." + FIName;
		String LPFIPrefix = "LPFIPREFIX";
        WatchPropertyC.update( LPFIPrefixProperty, LPFIPrefix, ConfigurationProperty.DYNAMIC_SCOPE );
		String FIPrefixProperty = SEFMBean.SEF_USI_PREFIX + "._ALL_." + FIName;
		String FIPrefix = "FIPREFIX";
        WatchPropertyC.update(FIPrefixProperty, FIPrefix, ConfigurationProperty.DYNAMIC_SCOPE);
		String LPPrefixProperty = SEFMBean.SEF_USI_PREFIX + "." + LPName;
		String LPPrefix = "LPPREFIX";
        WatchPropertyC.update(LPPrefixProperty, LPPrefix, ConfigurationProperty.DYNAMIC_SCOPE);

		TradingParty tradingParty = CounterpartyUtilC.getTradingParty(LPLE, FILE.getOrganization());
		tradingParty.setGenerateUSI(true);

		USI = USIUtilC.getGeneratedUSI(trade, FILE.getOrganization(), LPLE.getOrganization()).getUSI();
		assertTrue(USI != null);
		assertTrue(USI.startsWith(LPFIPrefix));
		assertTrue(USI.endsWith(txIdBase));

		mbean.removeProperty(LPFIPrefixProperty, ConfigurationProperty.DYNAMIC_SCOPE);
		USI = USIUtilC.getGeneratedUSI(trade, FILE.getOrganization(), LPLE.getOrganization()).getUSI();
		assertTrue(USI != null);
		assertTrue(USI.startsWith(LPPrefix));
		assertTrue(USI.endsWith(txIdBase));

		mbean.removeProperty(LPPrefixProperty, ConfigurationProperty.DYNAMIC_SCOPE);
		USI = USIUtilC.getGeneratedUSI(trade, FILE.getOrganization(), LPLE.getOrganization()).getUSI();
		assertTrue(USI != null);
		assertTrue(USI.startsWith(FIPrefix));
		assertTrue(USI.endsWith(txIdBase));

		mbean.removeProperty(FIPrefixProperty, ConfigurationProperty.DYNAMIC_SCOPE);
		USI = USIUtilC.getGeneratedUSI(trade, FILE.getOrganization(), LPLE.getOrganization()).getUSI();
		assertTrue(USI != null);
		assertTrue(USI.startsWith(mbean.getSDRUSIConfig().getUSIPrefix()));
		assertTrue(USI.endsWith(txIdBase));
	}

	public void testExternalUSI()
	{
	    String txid = "FXI12345";
	    String namespace = "TESTPREFIX";
	    String identifier = "11111111";
	    String USI = namespace + identifier;
	    
	    USIInfo USIHolder = USIUtilC.createUSIInfo( txid, namespace, USI, identifier );
	    validateUSI( USIHolder, namespace, identifier, USI );
	    
	    USIHolder = USIUtilC.createUSIInfo( txid, namespace, USI, null );
	    validateUSI( USIHolder, namespace, identifier, USI );
	    
        USIHolder = USIUtilC.createUSIInfo( txid, namespace, null, identifier );
        validateUSI( USIHolder, namespace, identifier, USI );

        USIHolder = USIUtilC.createUSIInfo( txid, namespace, identifier, null );
        validateUSI( USIHolder, namespace, identifier, USI );
        
        USIHolder = USIUtilC.createUSIInfo( txid, null, USI, null );
        assertTrue( null == USIHolder.getUSINamespace() );
        assertTrue( USI.equals( USIHolder.getUSI() ) );
        assertTrue( null == USIHolder.getUSIIdentifier() );
	}

    private void validateUSI(USIInfo USIHolder, String namespace, String identifier, String USI)
    {
        assertTrue( namespace.equals( USIHolder.getUSINamespace() ) );
        assertTrue( USI.equals( USIHolder.getUSI() ) );
        assertTrue( identifier.equals( USIHolder.getUSIIdentifier() ) );
    }
}
