package com.integral.exception.test;


import com.integral.exception.IdcException;
import com.integral.exception.IdcIllegalArgumentException;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.exception.IdcRuntimeException;
import com.integral.exception.IdcThrowable;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceException;
import junit.framework.TestCase;

import java.util.List;

public class ExceptionTestC
        extends TestCase
{
    protected Log log = LogFactory.getLog( this.getClass() );

    public ExceptionTestC( String name )
    {
        super( name );
    }


    public void testKey()
    {
        try
        {
            Object[] args = {"ARG1", "ARG2"};

            IdcException e1 = new IdcException( "TEST", args );
            log.info( "e1 = " + e1.getDetailMessage() );
        }
        catch ( Exception e )
        {
            fail( "Exception e:" + e );
        }
    }

    public void testNestedException()
    {
        try
        {
            IdcIllegalArgumentException iae1 = new IdcIllegalArgumentException( "level 3: no such test object" );

            IdcIllegalArgumentException iae2 = new IdcIllegalArgumentException( "level 2: no such test object" );
            iae2.setNestedException( iae1 );

            IdcNoSuchObjectException nsoe = new IdcNoSuchObjectException( "level 1: no such test object" );
            nsoe.setNestedException( iae2 );

            log.info( "nsoe = " + nsoe.hashCode() );
            log.info( "  nested = " + nsoe.getNestedException().hashCode() );

            List el1 = nsoe.getNestedExceptions();
            for ( int i = 0; i < el1.size(); i++ )
            {
                log.info( "  nested #" + i + ": " + el1.get( i ) );
            }
            List el2 = nsoe.getAllNestedExceptionsDepthFirst();
            for ( int i = 0; i < el2.size(); i++ )
            {
                log.info( "  all nested #" + i + ": " + el2.get( i ) );
            }

        }
        catch ( Exception e )
        {
            fail( "Exception e" );
        }
    }

    public void testRuntimeNestedException()
    {
        try
        {
            Class.forName( "AnUnknownClass" );
        }
        catch ( Exception e )
        {

            IdcRuntimeException re = new IdcRuntimeException( e );
            log.info( "e = " + re.getMessage() );
        }
    }


    public void testRuleException()
    {
        try
        {
            Class.forName( "AnUnknownClass" );
        }
        catch ( Exception e )
        {

            IdcThrowable re = new com.integral.rule.RuleExecutionException( e );
            log.info( "e = " + re.getDetailMessage() );
        }
    }


    public void testExceptionMessage()
    {
        try
        {
            PersistenceException exception = new PersistenceException( "test" );
            log.info( "persistence exception = " + exception.getDetailMessage() );
        }
        catch ( Exception e )
        {
        }
    }

}


