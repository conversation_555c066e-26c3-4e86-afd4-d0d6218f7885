package com.integral.SEF.test;

import com.integral.SEF.SEFMBean;
import com.integral.SEF.SEFMBeanC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;

public class SEFMBeanTestC extends MBeanTestCaseC
{
    private ReadNamedEntityC namedEntityReader;
    
    public SEFMBeanTestC( String aName )
    {
        super( aName );
        namedEntityReader = new ReadNamedEntityC();
    }

    public void testProperties()
    {
        SEFMBeanC SEFmbean = (SEFMBeanC)ConfigurationFactory.getSefMBean();
        testProperty( SEFmbean, "providersMinimum", SEFMBean.PROVIDERS_MINIMUM, MBeanTestCaseC.INTEGER );
        testProperty( SEFmbean, "SEFUSIPrefix", SEFMBean.SEF_USI_PREFIX, MBeanTestCaseC.STRING );
        testProperty( SEFmbean, "SEFQuoteConvention", SEFMBean.SEF_QUOTE_CONVENTION, MBeanTestCaseC.STRING );
        testProperty( SEFmbean, "INFXSEFId", SEFMBean.SEF_INFX_SEFID, MBeanTestCaseC.STRING );
        testProperty( SEFmbean, "SDROrgName", SEFMBean.SDR_ORG_NAME, MBeanTestCaseC.STRING );
        testProperty( SEFmbean, "USIValuePrefix", SEFMBean.USI_VALUE_PREFIX, MBeanTestCaseC.STRING );        
        testProperty( SEFmbean, "SDRUSIPrefix", SEFMBean.SDR_USI_PREFIX, MBeanTestCaseC.STRING );
        testProperty( SEFmbean, "traderUSIPrefix", SEFMBean.TRADER_USI_PREFIX, MBeanTestCaseC.STRING );    
        testProperty( SEFmbean, "sefTradeLogSourceDir", SEFMBean.SEF_TRADE_LOG_SOURCE_DIR, MBeanTestCaseC.STRING);
        testProperty( SEFmbean, "sefTradeLogDestinationDir", SEFMBean.SEF_TRADE_LOG_DESTINATION_DIR, MBeanTestCaseC.STRING);
        testProperty( SEFmbean, "sefTradeLogFileNamePrefix", SEFMBean.SEF_TRADE_LOG_NAME_PREFIX, MBeanTestCaseC.STRING);        
        testProperty( SEFmbean, "SEFBucketPrefix", SEFMBean.SEF_BUCKET_PREFIX, MBeanTestCaseC.STRING);
        testProperty( SEFmbean, "SEFAuditBucket", SEFMBean.SEF_AUDIT_BUCKET, MBeanTestCaseC.STRING);
        testProperty( SEFmbean, "SEFOrderUploadEnabled", SEFMBean.SEF_ORDER_BUCKET_CREATION_ENABLED, MBeanTestCaseC.BOOLEAN);
        testProperty( SEFmbean, "SEFOrderUploadUserName", SEFMBean.SEF_ORDER_UPLOAD_USER_NAME, MBeanTestCaseC.STRING);
                
        SEFmbean.setProperty( "Idc.SEF.Currency.BusinessCenter.USD", "NYC,16:00:00,Reuters,Reuters Screen SAEC Page opposite the symbol CYN", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( "NYC", SEFmbean.getCurrencyBusinessCenter( "USD" ) );
        assertEquals( "16:00:00", SEFmbean.getCurrencyBusinessCenterTime( "USD" ) );

        SEFmbean.setProperty( "Idc.SEF.Currency.BusinessCenter.ARS", "ARBA,,Reuters,Reuters Screen SAEC Page opposite the symbol CYN", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( "ARBA", SEFmbean.getCurrencyBusinessCenter( "ARS" ) );
        assertTrue( SEFmbean.getCurrencyBusinessCenterTime( "ARS" ) == null );
        assertNotNull(SEFmbean.getSDRUSIConfig());
        assertNotNull(SEFmbean.getTraderUSIConfig());
        validateMidRateProperty( SEFmbean );
    }

    private void validateMidRateProperty( SEFMBeanC SEFmbean )
    {
        // test mid-rate for FIX, legal entity and organization settings
        LegalEntity FILE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "SEFFI1" );
        assertFalse( SEFmbean.isMidRateSendCustomerOnFixEnabled( FILE, null ) );
        
        String LEProperty = "Idc.MidRate.Send.Customer.Fix.Enabled.SEFFI1.SEFFI1";
        String organizationProperty = "Idc.MidRate.Send.Customer.Fix.Enabled.SEFFI1"; 
        SEFmbean.setProperty( organizationProperty, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        SEFmbean.setProperty( LEProperty, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertTrue( SEFmbean.isMidRateSendCustomerOnFixEnabled( FILE, null ) );
        
        SEFmbean.setProperty( LEProperty, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertFalse( SEFmbean.isMidRateSendCustomerOnFixEnabled( FILE, null ) );
        
        SEFmbean.removeProperty( LEProperty, ConfigurationProperty.DYNAMIC_SCOPE );
        assertTrue( SEFmbean.isMidRateSendCustomerOnFixEnabled( FILE, null ) );
        
        SEFmbean.setProperty( organizationProperty, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertFalse( SEFmbean.isMidRateSendCustomerOnFixEnabled( FILE, null ) );
        
        SEFmbean.removeProperty( organizationProperty, ConfigurationProperty.DYNAMIC_SCOPE );
        LegalEntity SDLE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "SEFCM1" );
        String SDProperty = "Idc.MidRate.Send.Customer.Fix.Enabled.SEFCM1";
        SEFmbean.setProperty( SDProperty, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertTrue( SEFmbean.isMidRateSendCustomerOnFixEnabled( FILE, SDLE ) );
        
        SEFmbean.removeProperty( SDProperty, ConfigurationProperty.DYNAMIC_SCOPE );
        assertFalse( SEFmbean.isMidRateSendCustomerOnFixEnabled( FILE, null ) );
        SEFmbean.setProperty( SEFMBean.IDC_MID_RATE_SEND_CUSTOMER_FIX_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertTrue( SEFmbean.isMidRateSendCustomerOnFixEnabled( FILE, null ) );
    }
}
