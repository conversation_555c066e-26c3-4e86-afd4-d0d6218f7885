package com.integral.startup.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.system.configuration.Server;
import com.integral.test.TestCaseC;

public class WatchPropertyTestC extends TestCaseC
{
    public WatchPropertyTestC( String name )
    {
        super( name );
    }

    public void testReload()
    {
        try
        {
            WatchPropertyC test = new WatchPropertyC();
            IdcMBeanC server = new Server();

            WatchPropertyC.addListener( server );
            test.reload();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    protected Log log = LogFactory.getLog( this.getClass() );

    public static void main( String args[] )
    {
        try
        {
            WatchPropertyTestC test = new WatchPropertyTestC( "watchPropertyTest" );
            test.testReload();
            Thread.sleep( 1000000 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }
}