package com.integral.query.test;


import com.integral.audit.AuditEvent;
import com.integral.finance.creditLimit.CreditLimitRuleSet;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.persistence.Entity;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.query.QueryServiceC;
import com.integral.query.ReferenceDataNotFoundException;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.startup.PreloadDataC;
import com.integral.system.runtime.StartupTask;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;

import java.util.Collection;
import java.util.Iterator;


/**
 * Tests the query service
 */
public class QueryServicePTestC
        extends PTestCaseC
{
    static String name = "QueryService Test";

    public QueryServicePTestC( String name )
    {
        super( name );
    }

    public void setUp() throws Exception
    {
        super.setUp();
        StartupTask startup = new PreloadDataC();
        startup.startup( null, null );
    }

    private void setUser( String userName )
    {
        // start a transaction
        IdcSessionContext ctx;
        try
        {
            ctx = IdcSessionManager.getInstance().getSessionContext( userName );
            IdcSessionManager.getInstance().newTransaction( ctx );
        }
        catch ( Exception e )
        {
            fail( "QueryServicePTestC: error starting transaction for user " + userName + ": ", e );
        }
        finally
        {
            IdcSessionManager.getInstance().setTransaction( null );
        }

    }

    /**
     * Tests querying for all instances of a direct access data class: Currency
     */
    public void testDirectAccessDataQueryForAll()
    {
        try
        {
            setUser( "Integral" );
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();
            Collection crncs = qs.findAll( Currency.class );
            Iterator it = crncs.iterator();
            while ( it.hasNext() )
            {
                Currency crnc = ( Currency ) it.next();
                log( "\tcrnc " + crnc );
            }
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving currencies ", e );
        }

    }

    /**
     * Tests querying for all instances of a non-cached direct access data class: User
     */
    public void testNonCachedDirectAccessDataQueryForAll()
    {
        try
        {
            setUser( "fi2mm1@FI2" );
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();
            Collection crncs = qs.findAll( User.class );
            Iterator it = crncs.iterator();
            while ( it.hasNext() )
            {
                User user = ( User ) it.next();
                log( "\tuser " + user + " in namespace " + user.getNamespace().getShortName() );
            }
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving users ", e );
        }

    }

    /**
     * Tests querying for an instance of a direct access data class: Currency
     * based on its shortname
     */
    public void testDirectAccessDataQueryForShortName()
    {
        try
        {
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();
            Entity usd = qs.find( Currency.class, "USD" );
            assertNotNull( usd );
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving currency with shortName USD ", e );
        }

        // query by concrete class should fail
        try
        {
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();
            Entity usd = qs.find( CurrencyC.class, "USD" );
            if ( QueryFactory.getQueryServiceMBean().isDirectAccessClass( CurrencyC.class ) )
            {
                assertNull( usd );
            }
            else
            {
                assertNotNull( usd );
            }
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving currency with shortName USD ", e );
        }
    }

    /**
     * Tests querying for an instance of a direct access data class: Currency
     * based on an object id
     */
    public void testDirectAccessDataQueryForId()
    {
        try
        {
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();
            Entity usd = qs.find( Currency.class, 1 );
            assertNotNull( usd );
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving currency with id 1 ", e );
        }

    }

    /**
     * Tests querying for a non-direct access data class: AuditEvent
     */
    public void testRegularDataQuery()
    {
        try
        {
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();
            Collection crncs = qs.findAll( AuditEvent.class );
            Iterator it = crncs.iterator();
            while ( it.hasNext() )
            {
                AuditEvent event = ( AuditEvent ) it.next();
                log( "\tevent " + event );
            }
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving audit events ", e );
        }
    }

    public void testVisibility()
    {
        Organization dd = null;
        Organization fi1 = null;
        try
        {
            fi1 = ( Organization ) namedEntityReader.execute( Organization.class, "FI1" );
            dd = ( Organization ) namedEntityReader.execute( Organization.class, "DD1" );
            QueryService qs = QueryFactory.getQueryService();
            // now set the broker organization relationship for testing
            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );
            IdcSessionManager.getInstance().setSessionContext( ctxt );

            Organization ddOrg = null;
            try
            {
                ddOrg = ( Organization ) qs.find( Organization.class, dd.getObjectID() );
            }
            catch ( Exception e )
            {
            }
            assertNull( ddOrg );
            dd.setBrokerOrganization( fi1 );
            fi1.getCustomerOrganizations().add( dd );
            ddOrg = ( Organization ) qs.find( Organization.class, dd.getObjectID() );
            assertNotNull( ddOrg );
        }
        catch ( Exception e )
        {
            fail( "testVisibility", e );
        }
    }

    public void testReferenceDataQuery()
    {
        try
        {
            setUser( "Integral@MAIN" );
            QueryService qs = QueryFactory.getQueryService();
            Organization fi1 = ( Organization ) qs.find( Organization.class, "FI1" );
            assertNotNull( fi1 );

            fi1 = ( Organization ) qs.find( fi1.getNamespace(), Organization.class, "FI1" );
            assertNotNull( fi1 );

            Organization fi2 = ( Organization ) qs.find( OrganizationC.class, "FI2" );
            assertNotNull( fi2 );

            CreditLimitRuleSet clrs1 = ( CreditLimitRuleSet ) qs.find( fi1.getNamespace(), CreditLimitRuleSet.class, "CreditLimitRuleSet" );
            assertNotNull( clrs1 );
            assertEquals( clrs1.getNamespace(), fi1.getNamespace() );

            setUser( "fi1mm1@FI1" );
            fi1 = ( Organization ) qs.find( Organization.class, "FI1" );
            assertNotNull( fi1 );

            try
            {
                fi2 = ( Organization ) qs.find( Organization.class, "FI2" );
                assertNull( fi2 );
            }
            catch ( ReferenceDataNotFoundException e )
            {

            }
            clrs1 = ( CreditLimitRuleSet ) qs.find( fi1.getNamespace(), CreditLimitRuleSet.class, "CreditLimitRuleSet" );
            assertNotNull( clrs1 );
            assertEquals( clrs1.getNamespace(), fi1.getNamespace() );

        }
        catch ( Exception e )
        {
            fail( "testReferenceDataQuery", e );
        }
    }
}
