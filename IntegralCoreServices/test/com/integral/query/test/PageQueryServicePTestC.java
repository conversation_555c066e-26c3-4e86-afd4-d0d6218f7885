package com.integral.query.test;


import com.integral.finance.counterparty.CounterpartyC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.trade.Trade;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import com.integral.query.PageQueryService;
import com.integral.query.PageQueryServiceC;
import com.integral.query.QueryResult;
import com.integral.startup.PreloadDataC;
import com.integral.system.runtime.StartupTask;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.User;
import com.integral.user.UserC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;


/**
 * Tests the page query service
 */
public class PageQueryServicePTestC extends PTestCaseC
{
    static String name = "PageQueryService Test";

    public PageQueryServicePTestC( String name )
    {
        super( name );
    }

    public void setUp() throws Exception
    {
        super.setUp();
        StartupTask startup = new PreloadDataC();
        startup.startup( null, null );
    }

    public void testTradesQueryWithoutNamespace()
    {
        try
        {
            //create test trades.
            for ( int i=0; i< 50; i++ )
            {
                createTestTrade( i % 2 == 0 );
            }
            PageQueryService pageQs = new PageQueryServiceC();
            ReadAllQuery raq = new ReadAllQuery( FXTrade.class );
            Expression expr = new ExpressionBuilder().get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
            raq.setSelectionCriteria( expr );
            long count = pageQs.count( raq );
            log( "count=" + count );
            if ( count == 0 )
            {
                //create test trades.
                for ( int i=0; i< 50; i++ )
                {
                    createTestTrade( i % 2 == 0 );
                }
                count = pageQs.count( raq );
            }
            assertEquals( count > 0, true );

            // now do a page query service query for half the trades first and balance next.
            long firstSet = count > 100 ? 80 : count / 2;
            QueryResult trades1 = pageQs.find( raq, 1, firstSet, false );
            log( "first set trades1=" + trades1.getResultCollection() + ",count=" + trades1.getTotalCount() );
            assertEquals( "first set should contain " + firstSet, firstSet, trades1.getResultCollection().size() );

            long secondSetCount = ( count > 100 ? 100 : count ) - firstSet;
            QueryResult trades2 = pageQs.find( raq, firstSet + 1, ( count > 100 ? 100 : count ), false );
            log( "second set trades2=" + trades2.getResultCollection() + ",count=" + trades2.getTotalCount() );
            assertEquals( "second set should contain " + secondSetCount, secondSetCount, trades2.getResultCollection().size() );

            // now do a query for more than size of the total trades.
            long endIndex = count + 10;
            long startIndex = endIndex - 19 > 0 ? endIndex - 19 : 1;
            long resultCount = startIndex == 1 ? count : 10;
            QueryResult trades3 = pageQs.find( raq, startIndex, endIndex, false );
            log( "third set trades3=" + trades3.getResultCollection() + ",count=" + trades3.getTotalCount() );
            assertEquals( "third set should contain " + resultCount, resultCount, trades3.getResultCollection().size() );
        }
        catch ( Exception e )
        {
            fail( "testTradesQuery", e );
        }
    }

    public void testTradesQueryWithNamespace()
    {
        try
        {
            //create test trades.
            for ( int i=0; i< 50; i++ )
            {
                createTestTrade( i % 2 == 0 );
            }
            Namespace[] ns = new Namespace[1];
            ns[0] = ( Namespace ) new ReadNamedEntityC().execute( Namespace.class, "MAIN" );
            PageQueryService pageQs = new PageQueryServiceC();
            ReadAllQuery raq = new ReadAllQuery( FXTrade.class );
            Expression expr = new ExpressionBuilder().get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
            raq.setSelectionCriteria( expr );
            long count = pageQs.count( ns, raq, null, Entity.ACTIVE_STATUS, false );
            log( "count=" + count );

            if ( count == 0 )
            {
                //create test trades.
                for ( int i=0; i< 50; i++ )
                {
                    createTestTrade( i % 2 == 0 );
                }
                count = pageQs.count( raq );
            }
            assertEquals( count > 0, true );

            // now do a page query service query for half the trades first and balance next.
            long firstSet = count > 100 ? 80 : count / 2;
            QueryResult trades1 = pageQs.find( ns, raq, 1, firstSet, false, Entity.ACTIVE_STATUS, true );
            log( "first set trades1=" + trades1.getResultCollection() + ",count=" + trades1.getTotalCount() );
            assertEquals( "first set should contain " + firstSet, firstSet, trades1.getResultCollection().size() );

            long secondSetCount = ( count > 100 ? 100 : count ) - firstSet;
            QueryResult trades2 = pageQs.find( ns, raq, firstSet + 1, ( count > 100 ? 100 : count ), false, Entity.ACTIVE_STATUS, true );
            log( "second set trades2=" + trades2.getResultCollection() + ",count=" + trades2.getTotalCount() );
            assertEquals( "second set should contain " + secondSetCount, secondSetCount, trades2.getResultCollection().size() );

            // now do a query for more than size of the total trades.
            long endIndex = count + 10;
            long startIndex = endIndex - 19 > 0 ? endIndex - 19 : 1;
            long resultCount = startIndex == 1 ? count : 10;
            QueryResult trades3 = pageQs.find( ns, raq, startIndex, endIndex, false, Entity.ACTIVE_STATUS, true );
            log( "third set trades3=" + trades3.getResultCollection() + ",count=" + trades3.getTotalCount() );
            assertEquals( "third set should contain " + resultCount, resultCount, trades3.getResultCollection().size() );
        }
        catch ( Exception e )
        {
            fail( "testTradesQuery", e );
        }
    }

    private void createTestTrade( boolean active )
    {
        try
        {
            Trade trade = new FXSingleLegC();
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.addReadOnlyClass( UserC.class );
            uow1.addReadOnlyClass( CounterpartyC.class );
            Trade regTrade = ( Trade ) uow1.registerObject( trade );
            LegalEntity cptyA = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntity.class, "FI1-le1" );
            LegalEntity cptyB = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntity.class, "CITI-le1" );
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );
            regTrade.setEntryUser( user );
            regTrade.setCounterpartyA( cptyA );
            regTrade.setCounterpartyB( cptyB );
            regTrade.setTransactionID( "Test" + System.currentTimeMillis() );
            regTrade.setStatus( active ? Entity.ACTIVE_STATUS : Entity.PASSIVE_STATUS );
            uow1.commit();
        }
        catch ( Exception e )
        {
            log.error( "createTestTrade", e );
        }
    }

}
