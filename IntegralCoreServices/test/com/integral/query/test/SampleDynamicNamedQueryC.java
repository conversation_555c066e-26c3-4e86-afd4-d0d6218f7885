package com.integral.query.test;

import com.integral.finance.currency.Currency;
import com.integral.query.NamedQuery;
import com.integral.query.NamedQueryC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.DatabaseQuery;
import org.eclipse.persistence.queries.ReadAllQuery;

/**
 * A sample named query class that retrieves all users whose shortName
 * matches a supplied string. The named query is dynamic since it
 * sometimes supplies the organization.
 */
public class SampleDynamicNamedQueryC
        extends NamedQueryC
        implements NamedQuery
{
    /**
     * Default Constructor
     */
    public SampleDynamicNamedQueryC()
    {
        setDataClass( Currency.class );
        String[] params = {
                "shortName",
                "sortOrder"
        };
        setParameterKeys( params );
        setDynamic( true );
    }

    /**
     * Implements the actual query that retrieves trades created after
     * a specific date. A namespace restriction is automatically added
     * by the query service.
     *
     * @return
     */
    protected DatabaseQuery createQuery()
    {
        String shortName = ( String ) getParameter( "shortName" );
        Long sortOrder = ( Long ) getParameter( "sortOrder" );

        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).like( shortName );
        if ( sortOrder != null )
        {
            expr = expr.and( eb.get( "sortOrder" ).greaterThan( sortOrder ) );
        }
        ReadAllQuery query = new ReadAllQuery();
        query.setReferenceClass( this.getDataClass() );
        query.setSelectionCriteria( expr );

        return query;
    }
}
