package com.integral.query.test;


import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.EntityIteratorC;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryCriteriaBuilderC;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;


/**
 * Tests entity iterators
 */
public class IteratorTestC
        extends PTestCaseC
{
    static String name = "Entity Iterator Test";
    protected Log log = LogFactory.getLog( this.getClass() );

    private static int START_USER_ID = 501;

    public IteratorTestC( String name )
    {
        super( name );
    }


    public void testInsertTestRecords()
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            for ( int i = 0; i < 1560; i++ )
            {
                User user = UserFactory.newUser( "TEST-" + System.nanoTime() + '_' + i );
                user.setStatus( 'T' );
                uow.registerObject( user );
            }
            uow.commit();
        }
        catch ( Exception e )
        {
            log.error( "ItertaorTestC: error inserting test users", e );
            fail();
        }
    }

    /**
     * Tests a entity iterator for an empty set
     */
    public void testEmptySet()
    {
        Collection ids = new ArrayList();
        Iterator it = new EntityIteratorC( ids, User.class );
        while ( it.hasNext() )
        {
            User user = ( User ) it.next();
            log.info( "user: " + user );
        }
    }

    /**
     * Tests a entity iterator for an small  set
     */
    public void testSmallSet()
    {
        int i1 = doQueryWithHasNext( START_USER_ID, 2 );
        int i2 = doQueryWithoutHasNext( START_USER_ID, 2 );
        assertEquals( i1, i2 );
    }

    /**
     * Tests a entity iterator for an big set
     */
    public void test499Set()
    {
        int i1 = doQueryWithHasNext( START_USER_ID, 499 );
        int i2 = doQueryWithoutHasNext( START_USER_ID, 499 );
        assertEquals( i1, i2 );
    }

    /**
     * Tests a entity iterator for an big set
     */
    public void test500Set()
    {
        int i1 = doQueryWithHasNext( START_USER_ID, 500 );
        int i2 = doQueryWithoutHasNext( START_USER_ID, 500 );
        assertEquals( i1, i2 );
    }

    /**
     * Tests a entity iterator for an big set
     */
    public void test501Set()
    {
        int i1 = doQueryWithHasNext( START_USER_ID, 501 );
        int i2 = doQueryWithoutHasNext( START_USER_ID, 501 );
        assertEquals( i1, i2 );
    }

    /**
     * Tests a entity iterator for an big set
     */
    public void test999Set()
    {
        int i1 = doQueryWithHasNext( START_USER_ID, 999 );
        int i2 = doQueryWithoutHasNext( START_USER_ID, 999 );
        assertEquals( i1, i2 );
    }

    /**
     * Tests a entity iterator for an big set
     */
    public void test1000Set()
    {
        int i1 = doQueryWithHasNext( START_USER_ID, 1000 );
        int i2 = doQueryWithoutHasNext( START_USER_ID, 1000 );
        assertEquals( i1, i2 );
    }

    /**
     * Tests a entity iterator for an big set
     */
    public void test1001Set()
    {
        int i1 = doQueryWithHasNext( START_USER_ID, 1001 );
        int i2 = doQueryWithoutHasNext( START_USER_ID, 1001 );
        //assertEquals( i1, i2 ); // this needs some work
    }

    /**
     * Tests a entity iterator for an big set
     */
    public void test10000Set()
    {
        int i1 = doQueryWithHasNext( START_USER_ID, 10000 );
        int i2 = doQueryWithoutHasNext( START_USER_ID, 10000 );
        //assertEquals( i1, i2 ); // this needs some work
    }

    /**
     * Tests a entity iterator for an big set
     */
    private int doQueryWithHasNext( int from, int num )
    {
        Collection ids = new ArrayList();
        for ( int i = from; i <= from + num - 1 - 1; i++ )
        {
            ids.add( new Integer( i ) );
        }
        ids.add( new Integer( from + num - 1 ) );


        int i = 0;
        Iterator it = new EntityIteratorC( ids, User.class );
        while ( it.hasNext() )
        {
            User user = ( User ) it.next();
            log.info( "#" + i + " user OID: " + user.getObjectId() );
            i++;
        }

        return i;
    }

    /**
     * Tests a entity iterator for an big set
     */
    private int doQueryWithoutHasNext( int from, int num )
    {
        Collection ids = new ArrayList();
        for ( int i = from; i <= from + num - 1 - 1; i++ )
        {
            ids.add( new Integer( i ) );
        }
        ids.add( new Integer( from + num - 1 ) );


        int i = 0;
        Iterator it = new EntityIteratorC( ids, User.class );
        try
        {
            while ( true )
            {
                User user = ( User ) it.next();
                log.info( "#" + i + " user OID: " + user.getObjectId() );
                i++;
            }
        }
        catch ( Exception e )
        {
        }

        return i;
    }

    public void testDeleteTestRecords()
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            QueryService queryService = QueryFactory.getQueryService();
            QueryCriteriaBuilder queryBuilder = new QueryCriteriaBuilderC();
            for ( User user : ( Collection<User> ) queryService.findAll( User.class, queryBuilder.get( "shortName" ).like( "TEST-%" ) ) )
            {
                uow.deleteObject( user );
            }
            uow.commit();
        }
        catch ( Exception e )
        {
            log.error( "ItertaorTestC: error inserting test users", e );
            fail();
        }
    }


}
