package com.integral.query.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.user.User;
import com.integral.util.StopWatchC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadObjectQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.server.ServerSession;

class NamedQueryWorker extends Thread implements Runnable
{
    int num = 0;
    protected Log log = LogFactory.getLog( this.getClass() );
    final static int NUM_TIMES = 1;

    NamedQueryWorker( int i )
    {
        num = i;

        // register the named query USER_SHORTNAME with the server session
        log.info( "\tregistering NamedQuery Worker Thread #" + num );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( eb.getParameter( "shortName" ) );
            ReadObjectQuery query = new ReadObjectQuery();
            query.setReferenceClass( User.class );
            query.setSelectionCriteria( expr );
            query.addArgument( "shortName" );
            //query.bindAllParameters();
            //query.cacheStatement();

            ServerSession serverSession = PersistenceFactory.getActiveServerSession();
            serverSession.addQuery( "USER_SHORTNAME", query );
        }
        catch ( Exception e )
        {
            log.error( "Error when adding named query USER_SHORTNAME in thread #" + num, e );
        }
    }

    public void run()
    {
        log.info( "\tstarting NamedQuery Worker Thread #" + num );
        // perform the query 1000 times
        try
        {
            Session session = PersistenceFactory.newSession();
            StopWatchC watch = LogFactory.getStopWatch( this.getClass(),
                    "named query for retieving user Integral " + NUM_TIMES + " times in thread #" + num );
            for ( int i = 0; i < NUM_TIMES; i++ )
            {
                User user = ( User ) session.executeQuery( "USER_SHORTNAME", "Integral" );
            }
            watch.stopAndLog();
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user in thread #" + num, e );
        }
    }
}
