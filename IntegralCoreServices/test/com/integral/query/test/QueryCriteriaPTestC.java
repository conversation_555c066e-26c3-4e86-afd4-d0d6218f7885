package com.integral.query.test;


import com.integral.finance.currency.Currency;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryCriteria;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryFactory;
import com.integral.query.QueryServiceC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;

import java.util.Collection;
import java.util.Iterator;
import java.util.Vector;


/**
 * Tests the query criteria classes
 */
public class QueryCriteriaPTestC
        extends PTestCaseC
{
    static String name = "QueryCriteria Test";

    public QueryCriteriaPTestC( String name )
    {
        super( name );
    }

    /**
     * Tests whether a query can directly compare it to an object in a one-to-one relationship
     * instead of comparing it to an object ID. This should not result in a join from the
     * currency table to the namespace table.
     */
    public void testOnetoOneEqualObjectComparison()
    {
        try
        {
            // setup
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();

            // get namespace MAIN
            QueryCriteriaBuilder builder1 = QueryFactory.newQueryCriteriaBuilder();
            QueryCriteria c1 = builder1.get( "shortName" ).equal( "MAIN" );
            Collection ns = qs.findAll( Namespace.class, c1 );
            Namespace main = ( Namespace ) ns.iterator().next();
            assertNotNull( main );

            // query across relationship
            QueryCriteriaBuilder builder2 = QueryFactory.newQueryCriteriaBuilder();
            QueryCriteria c2 = builder2.get( "namespace" ).equal( main );
            Collection crncs = qs.findAll( Currency.class, c2 );

            // print result
            Iterator it = crncs.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " MAIN currency " + entity );
            }

        }
        catch ( Exception e )
        {
            log.error( "QueryCriteriaPTestC: error running test", e );
            fail( "testOnetoOneEqualObjectComparism", e );
        }
    }

    public void testOnetoOneEqualObjectComparisonUsingTOPLink()
    {
        try
        {
            Session session = PersistenceFactory.newSession();

            // get namespace MAIN
            ExpressionBuilder builder1 = new ExpressionBuilder();
            Expression c1 = builder1.get( "shortName" ).equal( "MAIN" );
            Namespace main = ( Namespace ) session.readObject( Namespace.class, c1 );
            assertNotNull( main );

            // query across relationship
            ExpressionBuilder builder2 = new ExpressionBuilder();
            Expression c2 = builder2.get( "namespace" ).equal( main );
            Vector crncs = session.readAllObjects( Currency.class, c2 );

            // print result
            Iterator it = crncs.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " MAIN currency " + entity );
            }

        }
        catch ( Exception e )
        {
            log.error( "QueryCriteriaPTestC: error running test", e );
            fail( "testOnetoOneEqualObjectComparisonUsingTOPLink", e );
        }
    }

    public void testOnetoOneInObjectComparisonUsingTOPLink()
    {
        try
        {
            Session session = PersistenceFactory.newSession();

            // get namespace MAIN
            ExpressionBuilder builder1a = new ExpressionBuilder();
            Expression c1a = builder1a.get( "shortName" ).equal( "MAIN" );
            Namespace main = ( Namespace ) session.readObject( Namespace.class, c1a );
            assertNotNull( main );

            // get namespace FI1
            ExpressionBuilder builder1b = new ExpressionBuilder();
            Expression c1b = builder1b.get( "shortName" ).equal( "FI1" );
            Namespace fi1 = ( Namespace ) session.readObject( Namespace.class, c1b );
            assertNotNull( fi1 );

            Object[] namespaces = {main.getObjectID(), fi1.getObjectID()};

            /*
            Vector namespaces = new Vector(2);
            namespaces.add(main);
            namespaces.add(fi1);
            */

            // query across relationship
            ExpressionBuilder builder2 = new ExpressionBuilder();
            Expression c2 = builder2.get( "namespace" ).get( Entity.ObjectID ).in( namespaces );
            Vector crncs = session.readAllObjects( Currency.class, c2 );

            // print result
            Iterator it = crncs.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " MAIN currency " + entity );
            }

        }
        catch ( Exception e )
        {
            log.error( "QueryCriteriaPTestC: error running test", e );
            fail( "testOnetoOneInObjectComparisonUsingTOPLink", e );
        }
    }

    /**
     * Tests a expression
     */
    public void testQueryCriteria()
    {
        try
        {
            PersistenceFactory.getCachePolicy().clear();

            // expression
            QueryCriteriaBuilder builder = QueryFactory.newQueryCriteriaBuilder();
            QueryCriteria c1 = builder.get( "namespace" ).get( "shortName" ).like( "M%" );
            QueryCriteria c2 = builder.get( "namespace" ).get( "objectID" ).greaterThanEqual( 1 );
            QueryCriteria c = c1.and( c2 );

            // setup
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );

            // do query
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();
            Collection result = qs.findAll( Currency.class, c );

            // print result
            Iterator it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " M%/>=1 currency " + entity );
            }

        }
        catch ( Exception e )
        {
            log.error( "QueryCriteriaPTestC: error running test", e );
            fail( "testQueryCriteria exception", e );
        }
    }

    /**
     * Tests a expression that uses a common stem for one query expression.
     */
    public void testQueryCriteriaStem()
    {
        try
        {
            PersistenceFactory.getCachePolicy().clear();

            // expression
            QueryCriteriaBuilder builder = QueryFactory.newQueryCriteriaBuilder();
            QueryCriteria stem = builder.get( "namespace" );
            QueryCriteria c1 = stem.get( "shortName" ).like( "M%" );
            QueryCriteria c2 = stem.get( "objectID" ).greaterThanEqual( 1 );
            QueryCriteria c = c1.and( c2 );

            // setup
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );

            // do query
            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();
            Collection result = qs.findAll( Currency.class, c );

            // print result
            Iterator it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " M%/>=1 currency " + entity );
            }

        }
        catch ( Exception e )
        {
            log.error( "QueryCriteriaPTestC: error running test", e );
            fail( "testQueryCriteriaStem exception", e );
        }
    }
}
