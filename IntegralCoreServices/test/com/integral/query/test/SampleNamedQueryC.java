package com.integral.query.test;

import com.integral.query.NamedQuery;
import com.integral.query.NamedQueryC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.DatabaseQuery;
import org.eclipse.persistence.queries.ReadAllQuery;

/**
 * A sample named query class that retrieves all users whose shortName
 * matches a supplied string
 */
public class SampleNamedQueryC
        extends NamedQueryC
        implements NamedQuery
{
    /**
     * Default Constructor
     */
    public SampleNamedQueryC()
    {
        setDataClass( com.integral.user.User.class );
        String[] params = {
                "shortName"
        };
        setParameterKeys( params );
    }

    /**
     * Implements the actual query that retrieves trades created after
     * a specific date. A namespace restriction is automatically added
     * by the query service.
     *
     * @return
     */
    protected DatabaseQuery createQuery()
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).like( eb.getParameter( "shortName" ) );

        ReadAllQuery query = new ReadAllQuery();
        query.setReferenceClass( this.getDataClass() );
        query.setSelectionCriteria( expr );
        query.addArgument( "shortName" );

        return query;
    }
}
