package com.integral.query.test;


import com.integral.finance.currency.Currency;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.NamedQuery;
import com.integral.query.QueryFactory;
import com.integral.query.QueryServiceC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.util.StopWatchC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadObjectQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.server.ServerSession;

import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Vector;


/**
 * Tests named queries
 */
public class NamedQueryPTestC
        extends PTestCaseC
{
    static String name = "Named Query Test";

    final static int NUM_TIMES = 1;
    final static int NUM_THREADS = 5;

    public NamedQueryPTestC( String name )
    {
        super( name );
    }

    /**
     * Tests named query registration
     */
    public void testRegistration()
    {
        NamedQuery nq = QueryFactory.newNamedQuery( null );
        assertNull( nq );

        nq = QueryFactory.newNamedQuery( "FOO" );
        assertNull( nq );

        nq = QueryFactory.newNamedQuery( "SampleNQ" );
        assertNull( nq );

        QueryFactory.setNamedQuery( "SampleNQ", SampleNamedQueryC.class );

        nq = QueryFactory.newNamedQuery( "SampleNQ" );
        assertNotNull( nq );
    }

    /**
     * Tests whether a simple regular query for the user Integral
     */
    public void testSimpleQuery()
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            StopWatchC watch = LogFactory.getStopWatch( this.getClass(),
                    "regular query for retrieving user Integral " + NUM_TIMES + " times" );
            for ( int i = 0; i < NUM_TIMES; i++ )
            {
                ExpressionBuilder eb = new ExpressionBuilder();
                Expression expr = eb.get( "shortName" ).equal( "Integral" );
                User user = ( User ) session.readObject( User.class, expr );
                log.info( "\tuser (simple query): " + user );
            }
            watch.stopAndLog();
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user ", e );
        }

    }

    /**
     * Tests a simple named query for the user Integral
     */
    public void testSimpleNamedQuery()
    {
        // register the named query USER_SHORTNAME with the server session
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( eb.getParameter( "shortName" ) );
            ReadObjectQuery query = new ReadObjectQuery();
            query.setReferenceClass( User.class );
            query.setSelectionCriteria( expr );
            query.addArgument( "shortName" );
            // query.bindAllParameters();
            // query.cacheStatement();

            ServerSession serverSession = PersistenceFactory.getActiveServerSession();
            serverSession.addQuery( "USER_SHORTNAME", query );
        }
        catch ( Exception e )
        {
            log.error( "Error when adding named query USER_SHORTNAME ", e );
        }

        // perform the query 1000 times
        try
        {
            Session session = PersistenceFactory.newSession();
            StopWatchC watch = LogFactory.getStopWatch( this.getClass(),
                    "named query for retieving user Integral " + NUM_TIMES + " times" );
            for ( int i = 0; i < NUM_TIMES; i++ )
            {
                User user = ( User ) session.executeQuery( "USER_SHORTNAME", "Integral" );
                log.info( "\tuser(named query): " + user );
            }
            watch.stopAndLog();
        }
        catch ( Exception e )
        {
            log.error( "Error when retrieving user ", e );
        }
    }

    /**
     * Tests a simple named query for the user Integral in concurrently running threads
     */
    public void testConcurrentNamedQuery()
    {
        Thread workers[] = new Thread[NUM_THREADS];

        // create threads
        try
        {
            for ( int i = 0; i < NUM_THREADS; i++ )
            {
                workers[i] = new Thread( new NamedQueryWorker( i ),
                        "NamedQuery Worker Thread #" + i );
                workers[i].setDaemon( true );
            }
        }
        catch ( Exception e )
        {
            log.error( "Error when creating worker threads", e );
        }

        // run threads
        try
        {
            for ( int i = 0; i < NUM_THREADS; i++ )
            {
                workers[i].start();
            }
        }
        catch ( Exception e )
        {
            log.error( "Error when running worker threads", e );
        }

        // wait for result
        try
        {
            log.debug( "sleeping...." );
            Thread.sleep( 10000 );
        }
        catch ( Exception e )
        {
        }
    }

    /**
     * Test the sample named query as an admin user
     */
    public void testSampleNamedQueryAsAdmin()
    {
        try
        {
            // setup
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( ctx );

            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();

            // get numbers to compare to
            int num_fi1 = 0;
            int num_fi2 = 0;
            Vector users = PersistenceFactory.newSession().readAllObjects( User.class );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                if ( user.getShortName().startsWith( "fi1" ) )
                {
                    num_fi1++;
                }
                if ( user.getShortName().startsWith( "fi2" ) )
                {
                    num_fi2++;
                }
            }

            // run named query for first time
            NamedQuery nq1 = new SampleNamedQueryC();
            nq1.setParameter( "shortName", "fi1%" );
            Collection result = qs.findAll( nq1 );
            assertEquals( num_fi1, result.size() );
            Iterator it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " entity " + entity );
            }

            // run again with different args
            NamedQuery nq2 = new SampleNamedQueryC();
            nq2.setParameter( "shortName", "fi2%" );
            result = qs.findAll( nq2 );
            assertEquals( num_fi2, result.size() );
            it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " entity " + entity );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "unhandled exception when executing testSampleNamedQuery " + e );
        }


    }

    /**
     * Test the sample named query as a regular user. This user is not allowed
     * to see all user records
     */
    public void testSampleNamedQueryAsUser()
    {
        try
        {
            // setup
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "Integral@MAIN" );
            IdcSessionManager.getInstance().setSessionContext( ctx );

            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();

            // get numbers to compare to
            int num_fi1 = 0;
            int num_fi2 = 0;
            Vector users = PersistenceFactory.newSession().readAllObjects( User.class );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                String nsName = user.getNamespace().getShortName();
                if ( ( user.getShortName().startsWith( "fi1mm" ) )
                        && ( nsName.equals( "MAIN" )
                        || nsName.equals( "FI1" ) )
                        )
                {
                    num_fi1++;
                }
                if ( ( user.getShortName().startsWith( "fi2mm" ) )
                        && ( nsName.equals( "MAIN" )
                        || nsName.equals( "FI2" ) )
                        )
                {
                    num_fi2++;
                }
            }

            // run named query for first time
            ctx = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );
            IdcSessionManager.getInstance().setSessionContext( ctx );

            NamedQuery nq1 = new SampleNamedQueryC();
            nq1.setParameter( "shortName", "fi1mm%" );
            Collection result = qs.findAll( nq1 );
            assertEquals( num_fi1, result.size() );
            Iterator it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " entity " + entity );
            }

            // run again
            ctx = IdcSessionManager.getInstance().getSessionContext( "fi2mm1@FI2" );
            IdcSessionManager.getInstance().setSessionContext( ctx );
            NamedQuery nq2 = new SampleNamedQueryC();
            nq2.setParameter( "shortName", "fi2mm%" );
            result = qs.findAll( nq2 );
            assertEquals( num_fi2, result.size() );
            it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " entity " + entity );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "unhandled exception when executing testSampleNamedQuery " + e );
        }
    }

    /**
     * Test the sample named query as a regular user. This user is not allowed
     * to see all user records
     */
    public void testSampleDynamicNamedQueryAsUser()
    {
        try
        {
            // setup
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );
            IdcSessionManager.getInstance().setSessionContext( ctx );

            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();

            // get numbers to compare to
            int num_U_crnc = 0;
            int num_1_crnc = 0;
            Vector crncs = PersistenceFactory.newSession().readAllObjects( Currency.class );
            for ( int i = 0; i < crncs.size(); i++ )
            {
                Currency crnc = ( Currency ) crncs.elementAt( i );
                if ( crnc.getShortName().startsWith( "U" ) )
                {
                    num_U_crnc++;
                    if ( crnc.getSortOrder() >= 1 )
                    {
                        num_1_crnc++;
                    }
                }
            }

            // run named query for first time with shortname only
            NamedQuery nq1 = new SampleDynamicNamedQueryC();
            nq1.setParameter( "shortName", "U%" );
            Collection result = qs.findAll( nq1 );
            assertEquals( num_U_crnc, result.size() );
            Iterator it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " U% currency " + entity );
            }

            // run again now with shortname and sortorder
            NamedQuery nq2 = new SampleDynamicNamedQueryC();
            nq2.setParameter( "shortName", "U%" );
            nq2.setParameter( "sortOrder", ( long ) 1 );
            result = qs.findAll( nq2 );
            assertEquals( num_1_crnc, result.size() );
            it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " U%/1 currency " + entity );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "unhandled exception when executing testSampleDynamicNamedQuery " + e );
        }
    }


    /**
     * Test the sample named query as a regular user. This user is not allowed
     * to see all user records
     */
    public void testSampleDynamicNamedQueryAsUserWithRegistration()
    {
        try
        {
            // setup
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );
            IdcSessionManager.getInstance().setSessionContext( ctx );

            QueryServiceC qs = ( QueryServiceC ) QueryFactory.getQueryService();

            // get numbers to compare to
            int num_U_crnc = 0;
            int num_1_crnc = 0;
            Vector crncs = PersistenceFactory.newSession().readAllObjects( Currency.class );
            for ( int i = 0; i < crncs.size(); i++ )
            {
                Currency crnc = ( Currency ) crncs.elementAt( i );
                if ( crnc.getShortName().startsWith( "U" ) )
                {
                    num_U_crnc++;
                    if ( crnc.getSortOrder() >= 1 )
                    {
                        num_1_crnc++;
                    }
                }
            }

            QueryFactory.setNamedQuery( "SampleDynamicQuery", SampleDynamicNamedQueryC.class );

            // run named query for first time with user only
            HashMap<String, String> args1 = new HashMap<String, String>( 10 );
            args1.put( "shortName", "U%" );
            Collection result = qs.findAll( "SampleDynamicQuery", args1 );
            assertEquals( num_U_crnc, result.size() );
            Iterator it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " U% currency " + entity );
            }

            // run again now with user and org
            HashMap<String, Object> args2 = new HashMap<String, Object>( 10 );
            args2.put( "shortName", "U%" );
            args2.put( "sortOrder", ( long ) 1 );
            result = qs.findAll( "SampleDynamicQuery", args2 );
            assertEquals( num_1_crnc, result.size() );
            it = result.iterator();
            while ( it.hasNext() )
            {
                Entity entity = ( Entity ) it.next();
                log.debug( " U%/1 currency " + entity );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "unhandled exception when executing testSampleDynamicNamedQuery " + e );
        }
    }
}
