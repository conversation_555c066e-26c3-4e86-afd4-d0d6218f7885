package com.integral.security.test;

import com.integral.security.LoginMBean;
import com.integral.security.LoginMBeanC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.LoginChannel;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;

import java.util.Collection;

/**
 * <AUTHOR> Development Corporation.
 */
public class LoginMBeanTestC extends MBeanTestCaseC
{
    LoginMBeanC loginMBean = ( LoginMBeanC ) LoginMBeanC.getInstance();

    public LoginMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( loginMBean, "restrictIPMaskUse", "Idc.Org.RestrictIPMask.Use", MBeanTestCaseC.BOOLEAN );
        testProperty( loginMBean, "restrictedIPMask", "Idc.Org.RestrictIPMask", MBeanTestCaseC.STRING );
        testProperty( loginMBean, "integralCertPassword", "integralCert.password", MBeanTestCaseC.STRING );
        testProperty( loginMBean, "integralAliasName", "integralCert.alias", MBeanTestCaseC.STRING );
        testProperty( loginMBean, "integralCertName", "integralCert.name", MBeanTestCaseC.STRING );
        testProperty( loginMBean, "SSOSenderCertName", "SSOSenderCert.name", MBeanTestCaseC.STRING );
        testProperty( loginMBean, "SSOAlgorithmProvider", "SSO.Algorithm.Defaultclass.name", MBeanTestCaseC.STRING );
        testProperty( loginMBean, "orgLevelLoginRestrictionsEnabled", LoginMBean.ORG_ENABLE_LOGIN_RESTRICTIONS, MBeanTestCaseC.BOOLEAN );
        testProperty( loginMBean, "allowedOrganizationsForLogin", LoginMBean.ALLOWED_ORGANIZATION_LOGINS, MBeanTestCaseC.STRING );
        testProperty( loginMBean, "globallyAllowedOrganizationsForLogin", LoginMBean.GLOBALLY_ALLOWED_ORGANIZATION_LOGINS, MBeanTestCaseC.STRING );
        testProperty( loginMBean, "UIDCookieEnabled", LoginMBean.LOGIN_UID_COOKIE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( loginMBean, "UIDCookieWithValueAuthTokenEnabled", LoginMBean.LOGIN_UID_COOKIE_WITH_VALUE_AUTHTOKEN_ENABLED, MBeanTestCaseC.BOOLEAN );
    }

    public void testLoginRestrictionOrgProperties()
    {
        try
        {
            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "FI1,FI2", ConfigurationProperty.DYNAMIC_SCOPE, null );
            Collection<Organization> orgs = loginMBean.getAllowedLoginOrganizationsList();
            log( "orgList=" + orgs );
            assertNotNull( orgs );
            assertEquals( orgs.size(), 2 );

            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "FI1", ConfigurationProperty.DYNAMIC_SCOPE, null );
            orgs = loginMBean.getAllowedLoginOrganizationsList();
            log( "orgList=" + orgs );
            assertNotNull( orgs );
            assertEquals( orgs.size(), 1 );

            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "FI1,454545", ConfigurationProperty.DYNAMIC_SCOPE, null );
            orgs = loginMBean.getAllowedLoginOrganizationsList();
            log( "orgList=" + orgs );
            assertNotNull( orgs );
            assertEquals( orgs.size(), 1 );

            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            orgs = loginMBean.getAllowedLoginOrganizationsList();
            log( "orgList=" + orgs );
            assertNotNull( orgs );
            assertEquals( orgs.size(), 0 );

            loginMBean.setProperty( LoginMBean.GLOBALLY_ALLOWED_ORGANIZATION_LOGINS, "FI1,FI2", ConfigurationProperty.DYNAMIC_SCOPE, null );
            orgs = loginMBean.getGloballyAllowedLoginOrganizationsList();
            log( "orgList=" + orgs );
            assertNotNull( orgs );
            assertEquals( orgs.size(), 2 );

            loginMBean.setProperty( LoginMBean.GLOBALLY_ALLOWED_ORGANIZATION_LOGINS, "FI1", ConfigurationProperty.DYNAMIC_SCOPE, null );
            orgs = loginMBean.getGloballyAllowedLoginOrganizationsList();
            log( "orgList=" + orgs );
            assertNotNull( orgs );
            assertEquals( orgs.size(), 1 );

            loginMBean.setProperty( LoginMBean.GLOBALLY_ALLOWED_ORGANIZATION_LOGINS, "FI1,454545", ConfigurationProperty.DYNAMIC_SCOPE, null );
            orgs = loginMBean.getGloballyAllowedLoginOrganizationsList();
            log( "orgList=" + orgs );
            assertNotNull( orgs );
            assertEquals( orgs.size(), 1 );

            loginMBean.setProperty( LoginMBean.GLOBALLY_ALLOWED_ORGANIZATION_LOGINS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            orgs = loginMBean.getGloballyAllowedLoginOrganizationsList();
            log( "orgList=" + orgs );
            assertNotNull( orgs );
            assertEquals( orgs.size(), 0 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testUserLoginEnabled()
    {
        try
        {
            User fi1mm1 = UserFactory.getUser( "fi1mm1@FI1" );
            User fi2mm1 = UserFactory.getUser( "fi2mm1@FI2" );
            User adminUser = UserFactory.getUser( "Integral@MAIN" );

            // set only FI1 login enabled
            loginMBean.setProperty( LoginMBean.ORG_ENABLE_LOGIN_RESTRICTIONS, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( LoginMBean.GLOBALLY_ALLOWED_ORGANIZATION_LOGINS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( loginMBean.isUserOrganizationLoginEnabled( fi1mm1 ) );
            assertFalse( loginMBean.isUserOrganizationLoginEnabled( fi2mm1 ) );
            assertFalse( loginMBean.isUserOrganizationLoginEnabled( adminUser ) );

            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "FI1", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi1mm1 ) );
            assertFalse( loginMBean.isUserOrganizationLoginEnabled( fi2mm1 ) );
            assertFalse( loginMBean.isUserOrganizationLoginEnabled( adminUser ) );

            // now set FI2 also in the list.
            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "FI1,FI2", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi1mm1 ) );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi2mm1 ) );
            assertFalse( loginMBean.isUserOrganizationLoginEnabled( adminUser ) );

            // now
            //  MAIN to the global list.
            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "FI1,FI2", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( LoginMBean.GLOBALLY_ALLOWED_ORGANIZATION_LOGINS, "MAIN", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi1mm1 ) );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi2mm1 ) );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( adminUser ) );

            // now disable the org level restriction itself with or without restrictions at org level
            loginMBean.setProperty( LoginMBean.ORG_ENABLE_LOGIN_RESTRICTIONS, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi1mm1 ) );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi2mm1 ) );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( adminUser ) );
            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( LoginMBean.GLOBALLY_ALLOWED_ORGANIZATION_LOGINS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi1mm1 ) );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( fi2mm1 ) );
            assertTrue( loginMBean.isUserOrganizationLoginEnabled( adminUser ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            loginMBean.setProperty( LoginMBean.ORG_ENABLE_LOGIN_RESTRICTIONS, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( LoginMBean.ALLOWED_ORGANIZATION_LOGINS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }

    public void testRestrictIPMaskForOrg()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        try
        {
            loginMBean.setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            String ipAddress1 = "**************";
            String ipAddress2 = "*************";
            String ipAddress3 = "0.0.0.0";
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", ipAddress1 + " , " + ipAddress2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org2.getShortName() + ".RestrictIPMask", ipAddress1 + "," + ipAddress2 + " ," + ipAddress3, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted1 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted1 );
            boolean ipRestricted2 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted2 );
            boolean ipRestricted3 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted3 );

            boolean ipRestricted4 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted4 );
            boolean ipRestricted5 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted5 );
            boolean ipRestricted6 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertTrue( ipRestricted6 );

            // add a single ip address and verify the result.
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", ipAddress1, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org2.getShortName() + ".RestrictIPMask", ipAddress2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted7 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted7 );
            boolean ipRestricted8 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertFalse( ipRestricted8 );
            boolean ipRestricted9 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted9 );

            boolean ipRestricted10 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertFalse( ipRestricted10 );
            boolean ipRestricted11 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted11 );
            boolean ipRestricted12 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertFalse( ipRestricted12 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }

    public void testLoginAuditEnabled()
    {
        try
        {
            assertTrue( loginMBean.isAuditEnabled( -1 ) );
            assertTrue( loginMBean.isAuditEnabled( LoginChannel.FIX_API ) );
            assertTrue( loginMBean.isAuditEnabled( LoginChannel.FXI_API ) );

            // now set the global property
            loginMBean.setProperty( LoginMBean.LOGIN_AUDIT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( loginMBean.isAuditEnabled( LoginChannel.FIX_API ) );
            assertFalse( loginMBean.isAuditEnabled( LoginChannel.FIX_API ) );
            assertFalse( loginMBean.isAuditEnabled( -1 ) );

            loginMBean.setProperty( LoginMBean.LOGIN_AUDIT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( loginMBean.isAuditEnabled( LoginChannel.FIX_API ) );
            assertTrue( loginMBean.isAuditEnabled( LoginChannel.FXI_API ) );
            assertTrue( loginMBean.isAuditEnabled( -1 ) );


            loginMBean.setProperty( LoginMBean.LOGIN_AUDIT_ENABLED_PREFIX + LoginChannel.FIX_API, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( LoginMBean.LOGIN_AUDIT_ENABLED_PREFIX + LoginChannel.FXI_API, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( loginMBean.isAuditEnabled( LoginChannel.FIX_API ) );
            assertFalse( loginMBean.isAuditEnabled( LoginChannel.FXI_API ) );

            loginMBean.setProperty( LoginMBean.LOGIN_AUDIT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( loginMBean.isAuditEnabled( LoginChannel.FIX_API ) );
            assertFalse( loginMBean.isAuditEnabled( LoginChannel.FXI_API ) );
            assertFalse( loginMBean.isAuditEnabled( -1 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testLoginAuditEnabled" );
        }
    }

    public void testBrokerOrgIPRestrictMaskEnabled()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        try
        {
            assertNull( loginMBean.getBrokerOrgIPRestrictMaskEnabled ( null ) );
            assertNull( loginMBean.getBrokerOrgIPRestrictMaskEnabled( org1 ) );
            assertNull( loginMBean.getBrokerOrgIPRestrictMaskEnabled( org2 ) );

            loginMBean.setProperty( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + org1.getShortName (), "*******", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull( loginMBean.getBrokerOrgIPRestrictMaskEnabled( null ) );
            assertEquals ( "*******", loginMBean.getBrokerOrgIPRestrictMaskEnabled( org1 ) );
            assertNull( loginMBean.getBrokerOrgIPRestrictMaskEnabled( org2 ) );

            loginMBean.setProperty( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + org2.getShortName (), "*******", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull( loginMBean.getBrokerOrgIPRestrictMaskEnabled( null ) );
            assertEquals ( "*******", loginMBean.getBrokerOrgIPRestrictMaskEnabled( org1 ) );
            assertEquals( "*******", loginMBean.getBrokerOrgIPRestrictMaskEnabled( org2 ) );

            loginMBean.setProperty( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + org1.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + org2.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull( loginMBean.getBrokerOrgIPRestrictMaskEnabled( null ) );
            assertEquals ( "", loginMBean.getBrokerOrgIPRestrictMaskEnabled( org1 ) );
            assertEquals( "", loginMBean.getBrokerOrgIPRestrictMaskEnabled( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testBrokerOrgIPRestrictMaskEnabled" );
        }
        finally
        {
            loginMBean.removeProperty ( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + org1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            loginMBean.removeProperty( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + org2.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testRestrictIPMaskForBrokerOrg()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        Organization broker1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker1 );
        try
        {
            loginMBean.setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            String ipAddress1 = "**************";
            String ipAddress2 = "*************";
            String ipAddress3 = "0.0.0.0";
            String ipAddress4 = "*******";
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", ipAddress1 + " , " + ipAddress2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org2.getShortName() + ".RestrictIPMask", ipAddress1 + "," + ipAddress2 + " ," + ipAddress3, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted1 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted1 );
            boolean ipRestricted2 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted2 );
            boolean ipRestricted3 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted3 );

            boolean ipRestricted4 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted4 );
            boolean ipRestricted5 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted5 );
            boolean ipRestricted6 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertTrue( ipRestricted6 );

            loginMBean.removeProperty ( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", ConfigurationProperty.DYNAMIC_SCOPE );
            loginMBean.setProperty ( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + broker1.getShortName (), ipAddress4, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted7 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress4 );
            assertTrue( ipRestricted7 );
            boolean ipRestricted8 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertFalse( ipRestricted8 );
            boolean ipRestricted9 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted9 );

            boolean ipRestricted10 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted10 );
            boolean ipRestricted11 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2);
            assertTrue( ipRestricted11 );
            boolean ipRestricted12 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress4 );
            assertFalse( ipRestricted12 );

            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted13 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertFalse( ipRestricted13 );
            boolean ipRestricted14 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertFalse( ipRestricted14 );
            boolean ipRestricted15 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted15 );
            boolean ipRestricted16 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted16 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            org1.setBrokerOrganization ( null );
        }
    }

    public void testLoginRestrictionExemptAtGlobalLevel()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        try
        {
            loginMBean.setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            String ipAddress1 = "**************";
            String ipAddress2 = "*************";
            String ipAddress3 = "*************";
            loginMBean.setProperty( "Idc.Org.RestrictIPMask", ipAddress1 + " , " + ipAddress2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted1 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted1 );
            boolean ipRestricted2 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted2 );
            boolean ipRestricted3 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted3 );
            boolean ipRestricted4 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted4 );
            boolean ipRestricted5 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted5 );
            boolean ipRestricted6 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertFalse( ipRestricted6 );

            loginMBean.setProperty( "Idc.Org.RestrictIPMask", "EXEMPT", ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted7 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted7 );
            boolean ipRestricted8 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted8 );
            boolean ipRestricted9 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertTrue( ipRestricted9 );
            boolean ipRestricted10 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted10 );
            boolean ipRestricted11 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted11 );
            boolean ipRestricted12 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertTrue( ipRestricted12 );

            loginMBean.setProperty( "Idc.Org.RestrictIPMask", "Exempt", ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted13 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted13 );
            boolean ipRestricted14 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted14 );
            boolean ipRestricted15 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertTrue( ipRestricted15 );
            boolean ipRestricted16 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted16 );
            boolean ipRestricted17 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted17 );
            boolean ipRestricted18 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertTrue( ipRestricted18 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            loginMBean.removeProperty ( "Idc.Org.RestrictIPMask", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testLoginRestrictionExemptAtOrgLevel()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        try
        {
            loginMBean.setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            String ipAddress1 = "**************";
            String ipAddress2 = "*************";
            String ipAddress3 = "*************";
            loginMBean.setProperty( "Idc.Org.RestrictIPMask", ipAddress1 + " , " + ipAddress2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted1 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted1 );
            boolean ipRestricted2 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted2 );
            boolean ipRestricted3 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted3 );
            boolean ipRestricted4 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted4 );
            boolean ipRestricted5 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted5 );
            boolean ipRestricted6 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertFalse( ipRestricted6 );

            loginMBean.setProperty( "Idc.Org.RestrictIPMask", "EXEMPT", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", ipAddress1 + " , " + ipAddress2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org2.getShortName() + ".RestrictIPMask", ipAddress3, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted7 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted7 );
            boolean ipRestricted8 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted8 );
            boolean ipRestricted9 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted9 );
            boolean ipRestricted10 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertFalse( ipRestricted10 );
            boolean ipRestricted11 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertFalse( ipRestricted11 );
            boolean ipRestricted12 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertTrue( ipRestricted12 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.removeProperty ( "Idc.Org.RestrictIPMask", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testLoginRestrictionExemptAtBrokerOrgLevel()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        Organization broker1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker1 );
        try
        {
            loginMBean.setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            String ipAddress1 = "**************";
            String ipAddress2 = "*************";
            String ipAddress3 = "*************";
            loginMBean.setProperty( "Idc.Org.RestrictIPMask", ipAddress1 + " , " + ipAddress2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted1 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted1 );
            boolean ipRestricted2 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted2 );
            boolean ipRestricted3 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertFalse( ipRestricted3 );
            boolean ipRestricted4 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted4 );
            boolean ipRestricted5 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted5 );
            boolean ipRestricted6 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertFalse( ipRestricted6 );

            loginMBean.setProperty ( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + broker1.getShortName (), "EXEMPT", ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted7 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted7 );
            boolean ipRestricted8 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted8 );
            boolean ipRestricted9 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertTrue( ipRestricted9 );
            boolean ipRestricted10 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted10 );
            boolean ipRestricted11 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted11 );
            boolean ipRestricted12 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertFalse( ipRestricted12 );

            loginMBean.setProperty ( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + broker1.getShortName (), ipAddress3, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", "Exempt", ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org2.getShortName() + ".RestrictIPMask", ipAddress3, ConfigurationProperty.DYNAMIC_SCOPE, null );
            boolean ipRestricted13 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress1 );
            assertTrue( ipRestricted13 );
            boolean ipRestricted14 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress2 );
            assertTrue( ipRestricted14 );
            boolean ipRestricted15 = loginMBean.isClientIPAllowedForOrg( org1, ipAddress3 );
            assertTrue( ipRestricted15 );
            boolean ipRestricted16 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress1 );
            assertTrue( ipRestricted16 );
            boolean ipRestricted17 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress2 );
            assertTrue( ipRestricted17 );
            boolean ipRestricted18 = loginMBean.isClientIPAllowedForOrg( org2, ipAddress3 );
            assertTrue( ipRestricted18 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.setProperty( "Idc.Org." + org1.getShortName() + ".RestrictIPMask", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            loginMBean.removeProperty ( "Idc.Org.RestrictIPMask", ConfigurationProperty.DYNAMIC_SCOPE );
            loginMBean.setProperty ( LoginMBean.BROKER_ORG_RESTRICT_IPMASK_ENABLED_PREFIX + broker1.getShortName (), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            IdcUtilC.refreshObject ( org1 );
        }
    }
}
