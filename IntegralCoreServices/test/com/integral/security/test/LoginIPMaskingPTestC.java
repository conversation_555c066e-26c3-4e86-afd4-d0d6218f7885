package com.integral.security.test;

import com.integral.security.IPAddressMaskingC;
import com.integral.security.LoginMBeanC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserFactory;

import java.util.Random;

public class LoginIPMaskingPTestC extends PTestCaseC
{
    public LoginIPMaskingPTestC( String name )
    {
        super( name );
    }

    public void tearDown()
    {
        super.tearDown ();
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.FI1.RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );

    }

    public void testAllIPMasking()
    {
        String maskStr = "*.*.*.*";
        Random generator = new Random();

        for ( int i = 0; i < 200000; i++ )
        {
            String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 );
            if ( !IPAddressMaskingC.contains( maskStr, ipStr ) )
            {
                fail( "Mask " + maskStr + " Failed for " + ipStr );
            }
        }
        log.info( "Passed Test for IPmasking " + maskStr );
    }

    public void testIPMasking1()
    {
        String maskStr = "*.*.188.*";
        Random generator = new Random();

        for ( int i = 0; i < 200000; i++ )
        {
            String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + ".188." + generator.nextInt( 255 );
            if ( !IPAddressMaskingC.contains( maskStr, ipStr ) )
            {
                fail( "Mask " + maskStr + " Failed for " + ipStr );
            }
        }
        for ( int i = 0; i < 200000; i++ )
        {
            String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 187 ) + '.' + generator.nextInt( 255 );
            if ( IPAddressMaskingC.contains( maskStr, ipStr ) )
            {
                fail( "Mask " + maskStr + " Failed for " + ipStr );
            }
        }

        log.info( "Passed Test for IPmasking " + maskStr );
    }


    public void testIPMasking2()
    {
        try
        {
            new IPAddressMaskingC( "2571.1.1.1" );
        }
        catch ( IllegalArgumentException e )
        {
            return;
        }
        fail( "IPAddressMaskingC not working properly. It Parsed invalid IP Address 2571.1.1.1" );
    }

    public void testIPMasking3()
    {
        try
        {
            new IPAddressMaskingC( "x.1.x.1" );
        }
        catch ( IllegalArgumentException e )
        {
            return;
        }
        fail( "IPAddressMaskingC not working properly. It Parsed invalid IP Address x.1.x.1" );
    }

    public void testIPMasking4()
    {
        try
        {
            new IPAddressMaskingC( "************" );
        }
        catch ( IllegalArgumentException e )
        {
            return;
        }
        fail( "IPAddressMaskingC not working properly. It Parsed invalid IP Address ************" );
    }

    public void testIPMasking5()
    {
        try
        {
            new IPAddressMaskingC( "255.255.1.*1" );
        }
        catch ( IllegalArgumentException e )
        {
            return;
        }
        fail( "IPAddressMaskingC not working properly. It Parsed invalid IP Address 255.255.1.*1" );
    }

    public void testAllAllowed()
    {
        User usr = UserFactory.getUser( "Integral1@MAIN" );
        Random generator = new Random();
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }
    }


    public void testRestrictIPMaskForAll()
    {
        String mask = "*.*.*.*";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask", mask, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        Random generator = new Random();
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + mask + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }
        log.info( "Passed Test for RestrictIPMask " + mask );
    }

    public void testRestrictIPMaskForOrg()
    {
        String mask = "*.*.*.*";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", mask, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        Random generator = new Random();
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + mask + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        log.info( "Passed Test for RestrictIPMask " + mask );
    }

    public void testRestrictIPMaskCombined()
    {
        String mask = "*.*.*.*";
        String mask1 = "***********";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", mask, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask", mask1, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );


        User usr = UserFactory.getUser( "Integral1@MAIN" );
        Random generator = new Random();
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + mask + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        log.info( "Passed Test for RestrictIPMask " + mask );
    }

    public void testRestrictIPMaskCombined1()
    {
        String mask = "***********";
        String mask1 = "***********";
        String mask2 = "***********";

        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask", mask, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", mask1, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.FI1.RestrictIPMask", mask2, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );


        User usr = UserFactory.getUser( "Integral1@MAIN" );
        Random generator = new Random();

        //test for any other random ip *.*.*.* other than  192.168.1.*
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + ( generator.nextInt( 253 ) + 2 ) + '.' + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + mask + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        //check if *********** is restricted
        try
        {
            if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" ) )
            {
                fail( "Mask " + mask + " Failed for ***********" );
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        //check if *********** is restricted
        try
        {
            if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" ) )
            {
                fail( "Mask " + mask + " Failed for ***********" );
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        //check if *********** is restricted
        try
        {
            if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" ) )
            {
                fail( "Mask " + mask + " Failed for ***********" );
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        log.info( "Passed Test for RestrictIPMask " + mask );
    }


    public void testRestrictIPMask1()
    {
        String maskStr = "*.*.188.*";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", maskStr, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );

        Random generator = new Random();
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + ".188." + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + maskStr + " Failed for " + ipStr );
                }
            }
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 187 ) + '.' + generator.nextInt( 255 );
                if ( LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + maskStr + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        log.info( "Passed Test for IPmasking " + maskStr );
    }


    public void testRestrictIPMask2()
    {
        String maskStr = "2571.1.1.1";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", maskStr, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        boolean loginAllowed;

        try
        {
            loginAllowed = LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" );
        }
        catch ( IllegalArgumentException e )
        {
            LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            return;
        }
        assertFalse( loginAllowed );
    }

    public void testRestrictIPMask3()
    {
        String maskStr = "x.1.x.1";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", maskStr, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        boolean loginAllowed;

        try
        {
            loginAllowed = LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" );
        }
        catch ( IllegalArgumentException e )
        {
            LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            return;
        }
        assertFalse( loginAllowed );
    }

    public void testRestrictIPMask4()
    {
        String maskStr = "************";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", maskStr, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        boolean loginAllowed;

        try
        {
            loginAllowed = LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" );
        }
        catch ( IllegalArgumentException e )
        {
            LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            return;
        }
        assertFalse( loginAllowed );
    }

    public void testRestrictIPMask5()
    {
        String maskStr = "255.255.1.*1";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", maskStr, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        boolean loginAllowed;

        try
        {
            loginAllowed = LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" );
        }
        catch ( IllegalArgumentException e )
        {
            LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            return;
        }
        assertFalse( loginAllowed );
    }

    public void testRestrictIPMask6()
    {
        String maskStr = "***********";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", maskStr, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        boolean loginAllowed;

        try
        {
            loginAllowed = LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********x" );
        }
        catch ( IllegalArgumentException e )
        {
            LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            return;
        }
        assertFalse( loginAllowed );
    }

    public void testAllowedIPMaskForAll()
    {
        String mask = "*.*.*.*";
        String mask1 = "*.*.*.*";
        String mask2 = "*.*.*.*";

        LoginMBeanC.getInstance().setProperty( "Idc.Org.AllowedIPMask", mask, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", mask1, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask", mask2, ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        Random generator = new Random();
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + mask + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }
        log.info( "Passed Test for testAllowedIPMaskForAll " + mask );
    }

    public void testAllowedIPMaskCombined1()
    {
        String mask = "***********";
        String mask1 = "***********";
        String mask2 = "***********";

        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask", mask, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", mask1, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.FI1.RestrictIPMask", mask2, ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        User usr = UserFactory.getUser( "Integral1@MAIN" );
        Random generator = new Random();

        //test for any other random ip *.*.*.* other than  192.168.1.*
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + ( generator.nextInt( 253 ) + 2 ) + '.' + generator.nextInt( 255 );
                if ( LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + mask + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        //check if *********** is allowed
        try
        {
            if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" ) )
            {
                fail( "Mask " + mask + " Failed for ***********" );
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        //check if *********** is allowed
        try
        {
            if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" ) )
            {
                fail( "Mask " + mask + " Failed for ***********" );
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        //check if *********** is restricted
        try
        {
            if ( LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" ) )
            {
                fail( "Mask " + mask + " Failed for ***********" );
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }

        log.info( "Passed Test for RestrictIPMask " + mask );
    }


    public void testAllowedIPMask()
    {
        String maskStr = "*.*.188.*";

        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask", maskStr, ConfigurationProperty.DYNAMIC_SCOPE, null );
        User usr = UserFactory.getUser( "Integral1@MAIN" );

        Random generator = new Random();
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + ".188." + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + maskStr + " Failed for " + ipStr );
                }
            }
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 187 ) + '.' + generator.nextInt( 255 );
                if ( LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + maskStr + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }
        //check for mask use == false
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
        try
        {
            for ( int i = 0; i < 200000; i++ )
            {
                String ipStr = generator.nextInt( 255 ) + "." + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 ) + '.' + generator.nextInt( 255 );
                if ( !LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, ipStr ) )
                {
                    fail( "Mask " + maskStr + " Failed for " + ipStr );
                }
            }
        }
        catch ( IllegalArgumentException e )
        {
            fail( "Failed : ", e );
        }


        log.info( "Passed Test for IPmasking " + maskStr );
    }

    public void testIPMask()
    {
        String maskStr = "...*";
        LoginMBeanC.getInstance().setProperty( "Idc.Org.RestrictIPMask.Use", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        LoginMBeanC.getInstance().setProperty( "Idc.Org.MAIN.RestrictIPMask", maskStr, ConfigurationProperty.DYNAMIC_SCOPE, null );
        User usr = UserFactory.getUser( "Integral1@MAIN" );
        boolean loginAllowed;

        try
        {
            loginAllowed = LoginMBeanC.getInstance().isClientIPAllowedForUser( usr, "***********" );
        }
        catch ( IllegalArgumentException e )
        {
            return;
        }
        assertFalse ( loginAllowed );
    }


}
