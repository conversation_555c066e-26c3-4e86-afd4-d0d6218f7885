package com.integral.security.test;

import com.integral.cas.config.CASMBean;
import com.integral.cas.config.CASMBeanC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import java.util.Collection;

/**
 * <AUTHOR> Development Corporation.
 */
public class CASMBeanTestC extends MBeanTestCaseC
{
    CASMBeanC casMbean = ( CASMBeanC ) CASMBeanC.getInstance();

    public CASMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( casMbean, "multiIdentityMaxUsersCount", CASMBean.MULTI_IDENTITY_SSO_ID_MAX_USERS_COUNT, MBeanTestCaseC.INTEGER );
    }

    public void testUserLoginEnabled()
    {
        try
        {
            String brokerOrg = "BRKZ";
            String org = "C234";
            String userName = "test1";

            String brokerOrg1 = "BRKZ1";
            String userName1 = "test2";

            //no property set
            assertFalse( casMbean.isOnlySAMLLoginAllowed( brokerOrg, org, userName ) );

            //set broker property
            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( casMbean.isOnlySAMLLoginAllowed( brokerOrg, org, userName ) );
            assertFalse( casMbean.isOnlySAMLLoginAllowed( brokerOrg1, org, userName ) );

            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg + '.' + org, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( casMbean.isOnlySAMLLoginAllowed( brokerOrg, org, userName ) );
            assertFalse( casMbean.isOnlySAMLLoginAllowed( brokerOrg1, org, userName ) );

            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg + '.' + org, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg + '.' + org + '.' + userName, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( casMbean.isOnlySAMLLoginAllowed( brokerOrg, org, userName ) );
            assertFalse( casMbean.isOnlySAMLLoginAllowed( brokerOrg1, org, userName ) );

            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg + '.' + org, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_SAML_LOGIN_ONLY_PREFIX  + brokerOrg + '.' + org + '.' + userName, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( casMbean.isOnlySAMLLoginAllowed( brokerOrg, org, userName ) );
            assertTrue( casMbean.isOnlySAMLLoginAllowed( brokerOrg, org, userName1 ) );
            assertFalse( casMbean.isOnlySAMLLoginAllowed( brokerOrg1, org, userName ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testUserLoginOIDCBasedEnabled()
    {
        try
        {
            String brokerOrg = "BRKZ";
            String org = "C234";
            String userName = "test1";

            String brokerOrg1 = "BRKZ1";
            String userName1 = "test2";

            //no property set
            assertFalse( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg, org, userName ) );

            //set broker property
            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg, org, userName ) );
            assertFalse( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg1, org, userName ) );

            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg + '.' + org, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg, org, userName ) );
            assertFalse( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg1, org, userName ) );

            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg + '.' + org, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg + '.' + org + '.' + userName, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg, org, userName ) );
            assertFalse( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg1, org, userName ) );

            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg + '.' + org, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_BROKER_CUST_PREFIX  + brokerOrg + '.' + org + '.' + userName, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg, org, userName ) );
            assertTrue( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg, org, userName1 ) );
            assertFalse( casMbean.isOnlyOIDCLoginAllowedForBrokerCustomer( brokerOrg1, org, userName ) );

            //no property set
            assertFalse( casMbean.isOnlyOIDCLoginAllowed( org, userName ) );
            //set org property
            casMbean.setProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_PREFIX  + org, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( casMbean.isOnlyOIDCLoginAllowed( org, userName ) );

            //remove org property
            casMbean.removeProperty( CASMBean.SSO_OIDC_LOGIN_ONLY_PREFIX + org,  ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse( casMbean.isOnlyOIDCLoginAllowed( org, userName ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testOIDCServiceSimulatorProperties()
    {
        try
        {
            //no property set
            assertFalse( casMbean.isOidcServiceSimulatorEnabled() );
            assertEquals ( 5, casMbean.getSimulatorDashboardExpiryInMin () );// default value of expiry is 5 mins

            //set Simulator property
            casMbean.setProperty( CASMBean.OIDC_SERVICE_SIMULATOR_ENABLED  , "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.OIDC_SERVICE_SIMULATOR_DASHBOARD_EXPIRY  , "10", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( casMbean.isOidcServiceSimulatorEnabled() );
            assertEquals ( 10, casMbean.getSimulatorDashboardExpiryInMin () );

            //remove Simulator property
            casMbean.removeProperty( CASMBean.OIDC_SERVICE_SIMULATOR_ENABLED,  ConfigurationProperty.DYNAMIC_SCOPE );
            casMbean.removeProperty( CASMBean.OIDC_SERVICE_SIMULATOR_DASHBOARD_EXPIRY,  ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse( casMbean.isOidcServiceSimulatorEnabled() );
            assertEquals ( 5, casMbean.getSimulatorDashboardExpiryInMin () );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testOIDCServiceAdditionalParamsProperties() {
        try {
            String brand = "integral";
            String codeVerifierDefault = "jRO2eZN0o7Eu03l1J3-IpFczA0szl8VbX-1234567890123";
            String codeVerifierIntegral = "codeVerifierIntegral";
            //no property set
            assertNull(casMbean.isOidcServiceProviderGenerateCodeChallengeEnabled(brand));
            assertNull(casMbean.getOidcServiceProviderAuthenticationAdditionalParams(brand));// default value is null
            assertNull(casMbean.getOidcServiceProviderAuthorizationAdditionalParams(brand));// default value is null
            assertEquals(casMbean.getOIDCServiceProviderCodeVerifier(brand), codeVerifierDefault);

            //set property
            casMbean.setProperty(CASMBean.OIDC_SERVICE_GENERATE_CODE_CHALLENGE_ENABLED + brand, "true", ConfigurationProperty.DYNAMIC_SCOPE, null);
            casMbean.setProperty(CASMBean.OIDC_SERVICE_PROVIDER_AUTHENTICATION_ADDITIONAL_PARAMS + brand, "abc|123,xyz|1234", ConfigurationProperty.DYNAMIC_SCOPE, null);
            casMbean.setProperty(CASMBean.OIDC_SERVICE_PROVIDER_AUTHORIZATION_ADDITIONAL_PARAMS + brand, "def|321,xyzz|4321", ConfigurationProperty.DYNAMIC_SCOPE, null);
            casMbean.setProperty(CASMBean.OIDC_SERVICE_PROVIDER_CODE_VERIFIER_PREFIX + brand, codeVerifierIntegral, ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertTrue(casMbean.isOidcServiceProviderGenerateCodeChallengeEnabled(brand));
            assertNotNull(casMbean.getOidcServiceProviderAuthenticationAdditionalParams(brand));// default value is null
            assertNotNull(casMbean.getOidcServiceProviderAuthorizationAdditionalParams(brand));// default value is null
            assertEquals(casMbean.getOIDCServiceProviderCodeVerifier(brand), codeVerifierIntegral);

            //remove property
            casMbean.removeProperty(CASMBean.OIDC_SERVICE_GENERATE_CODE_CHALLENGE_ENABLED + brand, ConfigurationProperty.DYNAMIC_SCOPE);
            casMbean.removeProperty(CASMBean.OIDC_SERVICE_PROVIDER_AUTHENTICATION_ADDITIONAL_PARAMS + brand, ConfigurationProperty.DYNAMIC_SCOPE);
            casMbean.removeProperty(CASMBean.OIDC_SERVICE_PROVIDER_AUTHORIZATION_ADDITIONAL_PARAMS + brand, ConfigurationProperty.DYNAMIC_SCOPE);
            casMbean.removeProperty(CASMBean.OIDC_SERVICE_PROVIDER_CODE_VERIFIER_PREFIX + brand, ConfigurationProperty.DYNAMIC_SCOPE);
            assertNull(casMbean.isOidcServiceProviderGenerateCodeChallengeEnabled(brand));
            assertNull(casMbean.getOidcServiceProviderAuthenticationAdditionalParams(brand));
            assertNull(casMbean.getOidcServiceProviderAuthorizationAdditionalParams(brand));
            assertEquals(casMbean.getOIDCServiceProviderCodeVerifier(brand), codeVerifierDefault);
        } catch (Exception e) {
            e.printStackTrace();
            fail();
        }
    }

    public void testOIDCServiceCookiesDomainNameProperties() {
        try {
            String domainName = ".integral.net";
            String defaultDomainName = ".fxinside.net";

            assertEquals(defaultDomainName, casMbean.getOidcServiceCookiesDomainName());

            //set property
            casMbean.setProperty(CASMBean.OIDC_SERVICE_COOKIES_DOMAIN_NAME, domainName, ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals(casMbean.getOidcServiceCookiesDomainName(), domainName);

            //remove property
            casMbean.removeProperty(CASMBean.OIDC_SERVICE_COOKIES_DOMAIN_NAME, ConfigurationProperty.DYNAMIC_SCOPE);
            assertEquals(defaultDomainName, casMbean.getOidcServiceCookiesDomainName());
        } catch (Exception e) {
            e.printStackTrace();
            fail();
        }
    }


    public void testOIDCServiceClientIdClientSecretSSOIdFieldProperties() {
        final String brand = "integral";
        final String clientIdDefault = "Integral";
        final String clientId1 = "Integral1";
        final String ssoIdDefault = "sub";
        final String ssoId1 = "upn";
        final String clientSecret = "Integral";
        //no property set
        assertEquals(casMbean.getOIDCServiceProviderClientId(brand), clientIdDefault);
        assertNull(casMbean.getOIDCServiceProviderClientSecret(brand));
        assertEquals(casMbean.getOIDCServiceProviderSSOIdField(brand), ssoIdDefault);
        //set brand property
        casMbean.setProperty(CASMBean.OIDC_SERVICE_PROVIDER_CLIENT_ID_PREFIX + brand, clientId1, ConfigurationProperty.DYNAMIC_SCOPE, null);
        casMbean.setProperty(CASMBean.OIDC_SERVICE_PROVIDER_SSO_ID_FIELD_PREFIX + brand, ssoId1, ConfigurationProperty.DYNAMIC_SCOPE, null);
        casMbean.setProperty(CASMBean.OIDC_SERVICE_PROVIDER_CLIENT_SECRET_PREFIX + brand, clientSecret, ConfigurationProperty.DYNAMIC_SCOPE, null);
        assertEquals(casMbean.getOIDCServiceProviderClientId(brand), clientId1);
        assertEquals(casMbean.getOIDCServiceProviderClientSecret(brand), clientSecret);
        assertEquals(casMbean.getOIDCServiceProviderSSOIdField(brand), ssoId1);
        //remove brand property
        casMbean.removeProperty(CASMBean.OIDC_SERVICE_PROVIDER_CLIENT_ID_PREFIX + brand, ConfigurationProperty.DYNAMIC_SCOPE);
        casMbean.removeProperty(CASMBean.OIDC_SERVICE_PROVIDER_SSO_ID_FIELD_PREFIX + brand, ConfigurationProperty.DYNAMIC_SCOPE);
        casMbean.removeProperty(CASMBean.OIDC_SERVICE_PROVIDER_CLIENT_SECRET_PREFIX + brand, ConfigurationProperty.DYNAMIC_SCOPE);
        assertEquals(casMbean.getOIDCServiceProviderClientId(brand), clientIdDefault);
        assertNull(casMbean.getOIDCServiceProviderClientSecret(brand));
        assertEquals(casMbean.getOIDCServiceProviderSSOIdField(brand), ssoIdDefault);
    }

    public void testMultiIdentityAuthorizedUsersProperty()
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            Collection<String> authorizedUsers = casMbean.getMultiIdentityAuthorizedUsers ( null );
            assertNull ( authorizedUsers );
            Collection<String> org1AuthorizedUsers = casMbean.getMultiIdentityAuthorizedUsers ( org1.getShortName () );
            assertNull( org1AuthorizedUsers );
            Collection<String> org2AuthorizedUsers = casMbean.getMultiIdentityAuthorizedUsers ( org2.getShortName () );
            assertNull( org2AuthorizedUsers );

            // now set a single spread only for spot type.
            casMbean.setProperty( CASMBean.MULTI_IDENTITY_AUTHORIZED_USERS_PREFIX + org1.getShortName (), "fi1mm1,fi1mm2", ConfigurationProperty.DYNAMIC_SCOPE, null );
            org1AuthorizedUsers = casMbean.getMultiIdentityAuthorizedUsers ( org1.getShortName () );
            assertNotNull( org1AuthorizedUsers );
            assertTrue ( org1AuthorizedUsers.contains ( "fi1mm1" ) );
            assertTrue ( org1AuthorizedUsers.contains ( "fi1mm2" ) );
            assertFalse ( org1AuthorizedUsers.contains ( "fi1mm3" ) );

            org2AuthorizedUsers = casMbean.getMultiIdentityAuthorizedUsers ( org2.getShortName () );
            assertNull( org2AuthorizedUsers );

            // sent three different spread types for spot.
            casMbean.setProperty( CASMBean.MULTI_IDENTITY_AUTHORIZED_USERS_PREFIX + org2.getShortName (), "fi2mm1", ConfigurationProperty.DYNAMIC_SCOPE, null );
            org2AuthorizedUsers = casMbean.getMultiIdentityAuthorizedUsers ( org2.getShortName () );
            assertTrue ( org2AuthorizedUsers.contains ( "fi2mm1" ) );
            assertFalse ( org2AuthorizedUsers.contains ( "fi2mm2" ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testMultiIdentityAuthorizedUsersProperty" );
        }
    }

    public void testOIDCServiceProviderProperties()
    {
        try
        {
            String brand = "integral";
            String authenticationURL = "authenticationURL";
            String authorizationURL = "authorizationURL";
            String redirectURL = "redirectURL";

            //no property set
            assertFalse( casMbean.isCustomerLogin( brand ) );
            assertFalse( casMbean.isOIDCServiceLocalhost( ) );
            assertNull( casMbean.getOIDCServiceProviderAuthenticationURL( brand ) );
            assertNull( casMbean.getOIDCServiceProviderAuthorizationURL( brand ) );
            assertNull( casMbean.getOIDCServiceProviderRedirectURL( brand ) );


            //set brand property
            casMbean.setProperty( CASMBean.OIDC_SERVICE_CUSTOMER_LOGIN  + brand, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( casMbean.isCustomerLogin( brand ) );
            //set brand property to false
            casMbean.setProperty( CASMBean.OIDC_SERVICE_CUSTOMER_LOGIN  + brand, "false", ConfigurationProperty.DYNAMIC_SCOPE, "true" );
            assertFalse( casMbean.isCustomerLogin( brand ) );

            //set brand property
            casMbean.setProperty( CASMBean.OIDC_SERVICE_LOCALHOST  , "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( casMbean.isOIDCServiceLocalhost( ) );
            //set brand property to false
            casMbean.setProperty( CASMBean.OIDC_SERVICE_LOCALHOST  , "false", ConfigurationProperty.DYNAMIC_SCOPE, "true" );
            assertFalse( casMbean.isOIDCServiceLocalhost( ) );

            //set brand property
            casMbean.setProperty( CASMBean.OIDC_SERVICE_PROVIDER_AUTHENTICATION_URL  + brand, authenticationURL, ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.OIDC_SERVICE_PROVIDER_AUTHORIZATION_URL  + brand, authorizationURL, ConfigurationProperty.DYNAMIC_SCOPE, null );
            casMbean.setProperty( CASMBean.OIDC_SERVICE_PROVIDER_REDIRECT_URL  + brand, redirectURL, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNotNull( casMbean.getOIDCServiceProviderAuthenticationURL( brand ) );
            assertNotNull( casMbean.getOIDCServiceProviderAuthorizationURL( brand ) );
            assertNotNull( casMbean.getOIDCServiceProviderRedirectURL( brand ) );

            //remove brand property
            casMbean.removeProperty( CASMBean.OIDC_SERVICE_PROVIDER_AUTHENTICATION_URL  + brand,  ConfigurationProperty.DYNAMIC_SCOPE );
            casMbean.removeProperty( CASMBean.OIDC_SERVICE_PROVIDER_AUTHORIZATION_URL  + brand,  ConfigurationProperty.DYNAMIC_SCOPE );
            casMbean.removeProperty( CASMBean.OIDC_SERVICE_PROVIDER_REDIRECT_URL  + brand,  ConfigurationProperty.DYNAMIC_SCOPE );
            assertNull( casMbean.getOIDCServiceProviderAuthenticationURL( brand ) );
            assertNull( casMbean.getOIDCServiceProviderAuthorizationURL( brand ) );
            assertNull( casMbean.getOIDCServiceProviderRedirectURL( brand ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSSOClientAppSuccessURLProperty()
    {
        try
        {
            //No property set
            assertNull( casMbean.getSSOClientAppSuccessURL( null ) );
            assertNull( casMbean.getSSOClientAppSuccessURL( CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI7 ) );
            assertNull( casMbean.getSSOClientAppSuccessURL( CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI7 ) );

            casMbean.setProperty( CASMBean.IDC_SSO_CLIENT_APP_SUCCESS_URL_PREFIX + CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI7, "http://localhost:8080/fxi7/", ConfigurationProperty.DYNAMIC_SCOPE, null);
            casMbean.setProperty( CASMBean.IDC_SSO_CLIENT_APP_SUCCESS_URL_PREFIX + CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI8, "http://localhost:8080/fxi8/", ConfigurationProperty.DYNAMIC_SCOPE, null);

            assertEquals ( "http://localhost:8080/fxi7/", casMbean.getSSOClientAppSuccessURL ( CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI7 ) );
            assertEquals ( "http://localhost:8080/fxi8/", casMbean.getSSOClientAppSuccessURL ( CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI8 ) );
            assertNull ( casMbean.getSSOClientAppSuccessURL ( "abc" )  );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            casMbean.removeProperty ( CASMBean.IDC_SSO_CLIENT_APP_SUCCESS_URL_PREFIX + CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI7, ConfigurationProperty.DYNAMIC_SCOPE );
            casMbean.removeProperty( CASMBean.IDC_SSO_CLIENT_APP_SUCCESS_URL_PREFIX + CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI8, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSSOClientAppNameProperty()
    {
        Organization fi1Org = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization fi2Org = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        User fi1User1 = fi1Org.getUser("fi1mm1");
        User fi1User2 = fi1Org.getUser("fi1mm2");
        User fi2User1 = fi2Org.getUser("fi2mm1");
        try
        {
            //No property set
            assertNull(casMbean.getSSOClientAppName (fi1User1));

            //Set property at org and user level
            casMbean.setProperty( CASMBean.IDC_SSO_CLIENT_APP_NAME_PREFIX + fi1Org.getShortName() + "." + fi1User1.getShortName(), CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI8, ConfigurationProperty.DYNAMIC_SCOPE, null);
            casMbean.setProperty(CASMBean.IDC_SSO_CLIENT_APP_NAME_PREFIX + fi1Org.getShortName(), CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI7, ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals ( CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI8, casMbean.getSSOClientAppName ( fi1User1 ) );
            assertEquals ( CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI7, casMbean.getSSOClientAppName ( fi1User2 ) );
            assertNull ( casMbean.getSSOClientAppName ( fi2User1 ) );

        } catch (Exception e) {
            e.printStackTrace();
            fail();
        }
        finally
        {
            casMbean.removeProperty (  CASMBean.IDC_SSO_CLIENT_APP_NAME_PREFIX + fi1Org.getShortName() + "." + fi1User1.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE );
            casMbean.removeProperty (  CASMBean.IDC_SSO_CLIENT_APP_NAME_PREFIX + fi1Org.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSSOClientAppNameAtBrokerLevel()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        Organization broker1 =  ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker1 );
        User org1User = org1.getUser("fi1mm1");
        try
        {
            assertNull( casMbean.getSSOClientAppName ( org1User ) );

            //Set property at org and user level
            casMbean.setProperty( CASMBean.IDC_SSO_BROKER_CUSTOMERS_CLIENT_APP_NAME_PREFIX + broker1.getShortName(), "FXI9", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals ( "FXI9", casMbean.getSSOClientAppName ( org1User ) );

            casMbean.setProperty(CASMBean.IDC_SSO_CLIENT_APP_NAME_PREFIX + org1.getShortName(), CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI8, ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertEquals ( CASMBean.IDC_SSO_CLIENT_APP_NAME_FXI8, casMbean.getSSOClientAppName ( org1User ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testSSOClientAppNameAtBrokerLevel" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker1 );
            casMbean.removeProperty (  CASMBean.IDC_SSO_BROKER_CUSTOMERS_CLIENT_APP_NAME_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            casMbean.removeProperty (  CASMBean.IDC_SSO_CLIENT_APP_NAME_PREFIX + org1.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }
}
