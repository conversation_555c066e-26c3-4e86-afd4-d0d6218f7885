package com.integral.serialize;

import java.io.IOException;
import java.io.StringWriter;

import org.codehaus.jackson.JsonFactory;
import org.codehaus.jackson.JsonGenerator;
import org.codehaus.jackson.JsonParser;

import com.integral.scheduler.Expiration;
import com.integral.scheduler.ExpirationC;
import com.integral.test.PTestCaseC;

public class ExpirationSerializerTestC extends PTestCaseC
{
	public void testSerializer()
	{
        SerializerMap serializerMap = new SerializerMap();
        ExpirationWrapper expirationWrapper = new ExpirationWrapper();
        
        ExpirationSerializer serializer = new ExpirationSerializer( new String[]{ "Expiration", "exprtn" }, ExpirationWrapper.class, serializerMap );
        JsonFactory jsonFactory = new JsonFactory();
        StringWriter writer = new StringWriter();

        JsonGenerator jsonGenerator;
		try 
		{
			expirationWrapper.getExpiration().setSeconds( 5 );
			jsonGenerator = jsonFactory.createJsonGenerator( writer );
	        jsonGenerator.writeStartObject();
	        serializer.serialize( expirationWrapper, jsonGenerator, 0);
	        jsonGenerator.writeEndObject();
	        jsonGenerator.flush();
	        System.out.println( ">>>JSON: " + writer.getBuffer().toString() );
	        
	        JsonParser parser = jsonFactory.createJsonParser( writer.getBuffer().toString() );
            parser.nextToken();
            ExpirationWrapper expirationWrapper2 = new ExpirationWrapper();
            parser.nextValue();
            serializer.deserialize( parser, expirationWrapper2, 0 );
            
            assertEquals( expirationWrapper.getExpiration().getSeconds(), expirationWrapper2.getExpiration().getSeconds() );
		}
		catch( IOException e ) 
		{
			e.printStackTrace();
			fail( "exception" + e );
		}
		catch( Exception e )
		{
			e.printStackTrace();
			fail( "exception" + e );
		}
	}
	
	class ExpirationWrapper
	{
    	private Expiration expiration;
    	
    	public Expiration getExpiration()
    	{
    		expiration = expiration == null ? new ExpirationC() : expiration;
    		return expiration;
    	}
    	
    	public void setExpiration( Expiration expiration )
    	{
    		this.expiration = expiration;
    	}
	}
}
