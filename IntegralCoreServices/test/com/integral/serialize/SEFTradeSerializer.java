package com.integral.serialize;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Collection;

import org.codehaus.jackson.JsonFactory;
import org.codehaus.jackson.JsonGenerator;
import org.codehaus.jackson.JsonParser;
import org.codehaus.jackson.JsonToken;

import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.finance.trade.TradeLeg;
import com.integral.test.DealingPTestCaseC;

public class SEFTradeSerializer extends DealingPTestCaseC 
{
	public SEFTradeSerializer( String name )
	{
		super(name);
	}

	public void testSEFFields()
	{
        Trade trade = prepareSingleLegTrade( 100.00, true, true, "EUR/USD", makerOrg, makerTpForTaker, 1.2334, spotDate );

		for( TradeLeg leg : ( Collection< TradeLeg > ) trade.getTradeLegs() )
		{
			leg.setUSI( "TESTUSI12345" );
			leg.setUSINamespace( "TESTUSI" );
			leg.setUSIIdentifier( "12345" );
		}
		
        SerializerMap serializerMap = new SerializerMap();
        TradeSerializer mrs = new TradeSerializer( new String[] { "Trade", "T" }, TradeC.class, serializerMap, TradeSerializer.FXTradeType.SingleLeg );
        JsonFactory f = new JsonFactory();
        StringWriter wri = new StringWriter();

        try
        {
	        JsonGenerator g = f.createJsonGenerator( wri );
	
	        g.writeStartObject();
	        mrs.serializeEntity( trade, g, 0 );
	        g.writeEndObject();
	
	        g.flush();
	        
	        System.out.println( ">>>JSON: " + wri.getBuffer().toString() );
	        
	        JsonParser parser = f.createJsonParser( wri.getBuffer().toString() );
	        JsonToken token = parser.nextToken();
	        Trade clonedTrade = new TradeC();
	        mrs.advanceToObjectBoundary( parser, 0 );
	        try
	        {
	            mrs.deserializeToObject( parser, clonedTrade, 0 );
	        }
	        catch ( Exception e )
	        {
	            log.error( "Exception e : ", e );
	            fail( "Exception e : ", e );
	        }
	        
	        verifySEFFields( trade, clonedTrade );        
        }
        catch( IOException e )
        {
        	log.error( "Exception e : ", e );
            fail( "Exception e : ", e );
        }
        catch( Exception e )
        {
        	log.error( "Exception e : ", e );
            fail( "Exception e : ", e );
        }
	}

	private void verifySEFFields( Trade req, Trade clonedTrade )
	{
		assertNotNull( clonedTrade );
		verifyUSIFields( req, clonedTrade );
	}

	private void verifyUSIFields( Trade req, Trade clonedTrade )
	{
		for( TradeLeg leg : ( Collection< TradeLeg > ) req.getTradeLegs() )
		{
			TradeLeg clonedLeg = clonedTrade.getTradeLeg( leg.getName() );
			assertNotNull( clonedLeg );
			verifyStrings( leg.getUSI(), clonedLeg.getUSI() );
			verifyStrings( leg.getUSINamespace(), clonedLeg.getUSINamespace() );
			verifyStrings( leg.getUSIIdentifier(), clonedLeg.getUSIIdentifier() );
		}
	}

	private void verifyStrings( String string1, String string2 )
	{
		boolean isNull1 = string1 == null;
		boolean isNull2 = string2 == null;
		assertTrue( isNull1 && isNull2 || !isNull1 && !isNull2 );
		assertTrue( isNull1 || string1.equals( string2 ) );
	}
}
