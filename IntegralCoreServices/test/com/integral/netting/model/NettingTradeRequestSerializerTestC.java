package com.integral.netting.model;


import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.trade.Tenor;
import com.integral.is.ISCommonConstants;
import com.integral.serialize.NettingPortfolioSerializer;
import com.integral.serialize.NettingTradeRequestSerializer;
import com.integral.serialize.SerializerMap;
import com.integral.serialize.TradeRequestSerializer;
import com.integral.test.PTestCaseC;
import com.integral.test.TestUtilsConstant;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserC;
import org.codehaus.jackson.JsonFactory;
import org.codehaus.jackson.JsonGenerator;
import org.codehaus.jackson.JsonParser;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class NettingTradeRequestSerializerTestC extends PTestCaseC
{

    public NettingTradeRequestSerializerTestC( String name )
    {
        super( name );
    }

    public void testTradeRequestLegSerializer() throws Exception
    {
        CurrencyPair eurUsd = TestUtilsConstant.EURUSD;
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr3 = eb.get( "shortName" ).equal( TestUtilsConstant.STANDARDQUOTECONV_NAME );
            Session session1 = getPersistenceSession();

            FXRateConvention fxRateConvention = ( FXRateConventionC ) session1.readObject( FXRateConventionC.class, expr3 );

            SerializerMap serializerMap = new SerializerMap();
            TradeRequestSerializer mrs = new TradeRequestSerializer( new String[]{"TradeRequestLeg", "tRL"}, TradeRequestLegC.class, serializerMap );
            JsonFactory f = new JsonFactory();
            StringWriter wri = new StringWriter();
            IdcDate tradeDate = IdcDateC.newDate( new Date() );
            TradeRequestLeg trdLeg = DealingTestUtilC.createTradeRequestLeg( eurUsd, fxRateConvention, Tenor.SPOT_TENOR, tradeDate,
                    1000.00, true, 1.2342, 5.00, "1234", "Extern1234" );
            JsonGenerator g = f.createJsonGenerator( wri );
            long start = System.nanoTime();
            g.writeStartObject();
            mrs.serializeEntity( trdLeg, g, 0 );
            g.writeEndObject();

            g.flush();
            long end = System.nanoTime();

            System.out.println( ">>>JSON: " + wri.getBuffer().toString() );
            System.out.println( ">>>Nanos: " + ( end - start ) );

            JsonParser parser = f.createJsonParser( wri.getBuffer().toString() );
            parser.nextToken();
            TradeRequestLegC clonedRequest = new TradeRequestLegC();
            mrs.advanceToObjectBoundary( parser, 0 );
            try
            {
                mrs.deserializeToObject( parser, clonedRequest, 0 );
                assertEquals( clonedRequest.getBaseAmount(), trdLeg.getBaseAmount() );
                assertEquals( clonedRequest.getDealtAmount(), trdLeg.getDealtAmount() );
                assertEquals( clonedRequest.getBaseCurrency().getName(), trdLeg.getBaseCurrency().getName() );
                assertEquals( clonedRequest.getTermCurrency().getName(), trdLeg.getTermCurrency().getName() );
                assertEquals( clonedRequest.getDealtCurrency().getName(), trdLeg.getDealtCurrency().getName() );
                assertEquals( clonedRequest.getFXRateConvention().getName(), trdLeg.getFXRateConvention().getName() );
                assertEquals( clonedRequest.getLegId(), trdLeg.getLegId() );
                assertEquals( clonedRequest.getExternalLegId(), trdLeg.getExternalLegId() );
                assertEquals( clonedRequest.getMarketForwardPoints(), trdLeg.getMarketForwardPoints() );
                assertEquals( clonedRequest.getMarketSpotRate(), trdLeg.getMarketSpotRate() );
            }
            catch ( Exception e )
            {
                log.error( "Exception e : ", e );
                fail( "Failed to validate TradeRequestLeg Serialization" );
            }

        }
        catch ( NullPointerException e )
        {
            log.error( "NullPointer Exception due to invalid request object Ignoring." + e );
            fail( "Failed to validate TradeRequestLeg Serialization" );
        }
    }

    public void _testNettingPortfolioSerializer() throws Exception
    {
        Session session1 = getPersistenceSession();
        try
        {

            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "fi2mm1" );
            Expression expr1 = eb.get( "namespace" ).get( "shortName" ).equal( "FI2" );
            Expression expr2 = eb.get( "shortName" ).equal( "FI2" );
            Expression expr3 = eb.get( "shortName" ).equal( "STDQOTCNV" );
            Expression expr5 = eb.get( "shortName" ).equal( "FI2-le1" );
            Expression expr6 = eb.get( "shortName" ).equal( ISCommonConstants.TRD_CLSF_SP );

            UserC userC = ( UserC ) session1.readObject( UserC.class, expr.and( expr1 ) );
            OrganizationC organizationC = ( OrganizationC ) session1.readObject( OrganizationC.class, expr2 );
            OrganizationC organization1 = ( OrganizationC ) session1.readObject( OrganizationC.class, eb.get( "namespace" ).get( "shortName" ).equal( "FI1" ) );
            OrganizationC organization2 = ( OrganizationC ) session1.readObject( OrganizationC.class, eb.get( "namespace" ).get( "shortName" ).equal( "FI2" ) );

            FXRateConvention fxRateConvention = ( FXRateConventionC ) session1.readObject( FXRateConventionC.class, expr3 );
            LegalEntity le = ( LegalEntity ) session1.readObject( LegalEntityC.class, expr5 );
            NettingPortfolioC np = new NettingPortfolioC();
            np.setUser( userC );
            np.setNamespace( userC.getNamespace() );
            np.setName( "TestPortfolio" + System.nanoTime() );
            np.setCreditCounterparty( organizationC );
            np.setReportingCcy( TestUtilsConstant.USD );
            np.setFxRateConv( fxRateConvention );
            np.setOrganization( organizationC );
            np.setPortfolioID( "unq1" + System.nanoTime() );
            np.addToOrganizations( organization2 );
            np.addToOrganizations( organization1 );
            StringWriter wri = new StringWriter();
            SerializerMap serializerMap = new SerializerMap();
            NettingPortfolioSerializer nps = new NettingPortfolioSerializer( new String[]{"NettingPortfolio", "np"}, NettingPortfolioC.class, serializerMap, true );

            JsonFactory f = new JsonFactory();
            JsonGenerator g = f.createJsonGenerator( wri );
            long start = System.nanoTime();
            g.writeStartObject();
            nps.serializeEntity( np, g, 0 );
            g.writeEndObject();

            g.flush();
            long end = System.nanoTime();
            System.out.println( ">>>JSON: " + wri.getBuffer().toString() );
            System.out.println( ">>>Nanos: " + ( end - start ) );

            JsonParser parser = f.createJsonParser( wri.getBuffer().toString() );
            parser.nextToken();
            NettingPortfolio np2 = new NettingPortfolioC();
            nps.advanceToObjectBoundary( parser, 0 );
            try
            {
                nps.deserialize( parser, np2, 0 );
                assertNotNull( np2 );
                Collection<Organization> toOrgs = np2.getToOrganizations();
                assertTrue( toOrgs.size() > 0 );
                for ( Organization organization : toOrgs )
                {
                    assertEquals( organization.getShortName().equals( "FI1" ) || organization.getShortName().equals( "FI2" ), true );
                }
            }
            catch ( Exception e )
            {
                log.error( "Exception e : ", e );
                fail( "Failed to validate TradeRequestLeg Serialization" );
            }
        }
        catch ( Exception e )
        {
            log.error( " Exception due to invalid request object Ignoring." + e );
            fail( "Failed to validate Netting Portfolio Serialization" );
        }
    }

    public void testTradeRequestLegCollectionSerializer() throws Exception
    {
        CurrencyPair eurUsd = TestUtilsConstant.EURUSD;
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr3 = eb.get( "shortName" ).equal( TestUtilsConstant.STANDARDQUOTECONV_NAME );
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();

            FXRateConvention fxRateConvention = ( FXRateConventionC ) session1.readObject( FXRateConventionC.class, expr3 );

            SerializerMap serializerMap = new SerializerMap();
            NettingTradeRequestSerializer mrs = new NettingTradeRequestSerializer( new String[]{"NettingTradeRequest", "nTR"}, NettingTradeRequestC.class, serializerMap );
            JsonFactory f = new JsonFactory();
            StringWriter wri = new StringWriter();
            IdcDate tradeDate = IdcDateC.newDate( new Date() );
            TradeRequestLeg trdLeg1 = DealingTestUtilC.createTradeRequestLeg( eurUsd, fxRateConvention, Tenor.SPOT_TENOR, tradeDate,
                    1000.00, true, 1.2342, 5.00, "1234", "Extern1234" );
            TradeRequestLeg trdLeg2 = DealingTestUtilC.createTradeRequestLeg( eurUsd, fxRateConvention, Tenor.SPOT_TENOR, tradeDate,
                    2000.00, false, 1.2342, 5.00, "1235", "Extern1235" );
            List<TradeRequestLeg> legs = new ArrayList<TradeRequestLeg>();
            legs.add( trdLeg1 );
            legs.add( trdLeg2 );
            HashMap<String, TradeRequestLeg> trdLegMap = new HashMap<String, TradeRequestLeg>();
            trdLegMap.put( trdLeg1.getLegId(), trdLeg1 );
            trdLegMap.put( trdLeg2.getLegId(), trdLeg2 );
            NettingTradeRequest nettingTradeRequest = new NettingTradeRequestC();
            nettingTradeRequest.setTradeLegs( legs );
            JsonGenerator g = f.createJsonGenerator( wri );
            long start = System.nanoTime();
            g.writeStartObject();
            mrs.serializeEntity( nettingTradeRequest, g, 0 );
            g.writeEndObject();

            g.flush();
            long end = System.nanoTime();

            System.out.println( ">>>JSON: " + wri.getBuffer().toString() );
            System.out.println( ">>>Nanos: " + ( end - start ) );

            JsonParser parser = f.createJsonParser( wri.getBuffer().toString() );
            parser.nextToken();
            NettingTradeRequest nettingTradeRequest2 = new NettingTradeRequestC();
            mrs.advanceToObjectBoundary( parser, 0 );
            try
            {
                mrs.deserialize( parser, nettingTradeRequest2, 0 );
                for ( TradeRequestLeg tradeRequestLeg : nettingTradeRequest2.getTradeLegs() )
                {
                    assertNotNull( "Persistance issue of Trade Request Leg", tradeRequestLeg.getLegId() );
                    TradeRequestLeg trdLeg = trdLegMap.get( tradeRequestLeg.getLegId() );
                    assertEquals( tradeRequestLeg.getBaseAmount(), trdLeg.getBaseAmount() );
                    assertEquals( tradeRequestLeg.getDealtAmount(), trdLeg.getDealtAmount() );
                    assertEquals( tradeRequestLeg.getBaseCurrency().getName(), TestUtilsConstant.EUR.getName() );
                    assertEquals( tradeRequestLeg.getTermCurrency().getName(), TestUtilsConstant.USD.getName() );
                    assertEquals( tradeRequestLeg.getDealtCurrency().getName(), trdLeg.getDealtCurrency().getName() );
                    assertEquals( tradeRequestLeg.getFXRateConvention().getName(), trdLeg.getFXRateConvention().getName() );
                    assertEquals( tradeRequestLeg.getLegId(), trdLeg.getLegId() );
                    assertEquals( tradeRequestLeg.getExternalLegId(), trdLeg.getExternalLegId() );
                    assertEquals( tradeRequestLeg.getMarketForwardPoints(), trdLeg.getMarketForwardPoints() );
                    assertEquals( tradeRequestLeg.getMarketSpotRate(), trdLeg.getMarketSpotRate() );
                }

            }
            catch ( Exception e )
            {
                log.error( "Exception e : ", e );
                fail( "Failed to validate TradeRequestLeg Serialization" );
            }

        }
        catch ( NullPointerException e )
        {
            log.error( "NullPointer Exception due to invalid request object Ignoring." + e );
            fail( "Failed to validate TradeRequestLeg Serialization" );
        }
    }
}
