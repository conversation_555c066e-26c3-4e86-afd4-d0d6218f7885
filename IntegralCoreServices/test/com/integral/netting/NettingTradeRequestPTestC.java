package com.integral.netting;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.currency.Currency;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TradeClassification;
import com.integral.is.ISCommonConstants;
import com.integral.netting.model.MarketSpotPrice;
import com.integral.netting.model.MarketSpotPriceC;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.netting.model.NettingTradeRequestC;
import com.integral.netting.model.TradeRequestLeg;
import com.integral.netting.model.TradeRequestLegC;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.test.TestUtilsConstant;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserC;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: root
 * Date: 10/17/12
 * Time: 12:39 PM
 * To change this template use File | Settings | File Templates.
 */
public class NettingTradeRequestPTestC extends PTestCaseC
{
    private static final String standardQuoteConvName = "STDQOTCNV";


        public NettingTradeRequestPTestC( final String aName )
    {
        super( aName );
    }

    public void testCreateNettingTradeRequest()
    {
        log.info( "testCreateNettingTradeRequest" );
        try
        {
            PersistenceFactory.getPersistenceMBean().setDatabaseLogSQL( true );
            PersistenceFactory.getPersistenceMBean().setToplinkLogDefaultDebuglevel( true );

            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();
            
            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "fi2mm1" );
            Expression expr1 = eb.get( "namespace" ).get( "shortName" ).equal( "FI2" );
            Expression expr2 = eb.get( "shortName" ).equal( "FI2" );
            Expression expr3 = eb.get( "shortName" ).equal( standardQuoteConvName );
            Expression expr5 = eb.get( "shortName" ).equal( "FI2-le1" );
            Expression expr6 = eb.get( "shortName" ).equal( ISCommonConstants.TRD_CLSF_SP );

            UserC userC = ( UserC ) uow1.readObject( UserC.class, expr.and( expr1 ) );
            OrganizationC organizationC = ( OrganizationC ) uow1.readObject( OrganizationC.class, expr2 );
            OrganizationC organization1 = ( OrganizationC ) uow1.readObject( OrganizationC.class, eb.get( "namespace" ).get( "shortName" ).equal( "FI1" ) );
            OrganizationC organization2 = ( OrganizationC ) uow1.readObject( OrganizationC.class, eb.get( "namespace" ).get( "shortName" ).equal( "FI2" ) );
            
            FXRateConvention fxRateConvention = ( FXRateConventionC ) uow1.readObject( FXRateConventionC.class, expr3 );
            LegalEntity le = ( LegalEntity ) uow1.readObject( LegalEntityC.class, expr5 );

            NettingPortfolioC np = new NettingPortfolioC();
            np = ( NettingPortfolioC ) uow1.registerObject( np );
            np.setUser( userC );
            np.setNamespace( userC.getNamespace() );
            np.setName( "TestPortfolio" + System.nanoTime() );
            np.setCreditCounterparty( organizationC );
            np.setReportingCcy((Currency)uow1.registerObject(TestUtilsConstant.USD));
            np.setFxRateConv( fxRateConvention );
            np.setOrganization( organizationC );
            np.setPortfolioID( "unq1" + System.nanoTime() );
            np.addToOrganizations(organization2);
            np.addToOrganizations(organization1);

            IdcDate dt = IdcDateC.newDate(new Date());
            IdcDate vDate = fxRateConvention.getFXRateBasis(TestUtilsConstant.EURUSD).getValueDate(dt,Tenor.SPOT_TENOR);

            TradeClassification TRD_SPOT_CLSF = (TradeClassification) uow1.readObject( TradeClassification.class, expr6);


            NettingTradeRequestC ntr = new NettingTradeRequestC( 0, "1", "_1" , "ABC", true, TestUtilsConstant.EURUSD, TestUtilsConstant.EUR, 1000.00, TestUtilsConstant.USD, vDate, TestUtilsConstant.USD, 0, "PTFLO" );
            ntr = ( NettingTradeRequestC ) uow1.registerObject( ntr );
            ntr.setNettingPortfolio( np );
            ntr.setNamespace( np.getNamespace() );
            ntr.setFund( le );
            ntr.setTradeClassification( TRD_SPOT_CLSF );

            MarketSpotPrice msp1 = new MarketSpotPriceC( );
            msp1 = ( MarketSpotPriceC ) uow1.registerObject( msp1 );
            msp1.setBidLimit(1000);
            msp1.setOfferLimit(1000.1);
            msp1.setBidRate( 1.1 );
            msp1.setOfferRate( 1.1 );
            msp1.setMidRate( 1.1 );
            msp1.setCurrencyPair( "EUR/USD" );
            msp1.setTierId( 10 );
            msp1.setPortfolio( np );
            np.getMarketPrices().add( msp1 );

            double spotrate = 1.2345;
            double forwardPoints = 23;
            String legID = "LegID1";
            String externallegID = "ExternLegID1";
            TradeRequestLeg legC = DealingTestUtilC.createTradeRequestLeg(TestUtilsConstant.EURUSD, fxRateConvention,Tenor.SPOT_TENOR, dt, 1000.00, true, spotrate, forwardPoints, legID, externallegID);
            legC.setFXRateConvention( fxRateConvention );
            legC = ( TradeRequestLegC ) uow1.registerObject(legC);
            ntr.addTradeLeg(legC);
            String legID2 = "LegID2";
            String externallegID2 = "ExternLegID1";
            TradeRequestLeg legC2 = DealingTestUtilC.createTradeRequestLeg(TestUtilsConstant.EURUSD, fxRateConvention, Tenor.SPOT_TENOR, dt , 2000.00, true, spotrate, forwardPoints, legID2, externallegID2);
            legC2.setFXRateConvention( fxRateConvention );
       		legC2 =( TradeRequestLegC ) uow1.registerObject(legC2);
            ntr.addTradeLeg(legC2);
            Collection<NettingTradeRequest> tradeRequests = new ArrayList<NettingTradeRequest>(  );
            tradeRequests.add( ntr );
            np.setInputRequestPojos(tradeRequests);
            uow1.commit();

            np = ( NettingPortfolioC ) IdcUtilC.refreshObject( np );
            assertNotNull(np);
            Expression expr4 = eb.getField("IDCNETTINGPORTFOLIO.ID" ).equal( np.get_id() );
            NettingPortfolioC np2 = ( NettingPortfolioC ) session1.readObject( NettingPortfolioC.class , expr4 );
            assertNotNull( np2 );
            assertNotNull( "ToOrganization found Null, check for persistance", np2.getToOrganizations() );
            Collection<Organization> orgs = np2.getToOrganizations();
            assertTrue("Only FI2 and FI1 organization are to be found",orgs.size()==2);
            for (Organization organization : orgs) {
				assertTrue(organization.getShortName().equals("FI1")||organization.getShortName().equals("FI2"));
			}

            for(NettingTradeRequest ntr1 : np2.getInputRequestPojos())
            {
                ntr1 = (NettingTradeRequestC)uow1.refreshObject(ntr1);
                assertNotNull( "Netting Trade Request not persisted", ntr1);
                TradeRequestLeg legfromDB = ntr1.getTradeLegs( legID );
                legfromDB = (TradeRequestLeg) uow1.refreshObject( legfromDB );
                assertNotNull( "Trade Request Leg not Persisted", legfromDB );
                assertNotNull("Quote Convention Missing from Trade Request Leg",legfromDB.getFXRateConvention());
                assertNotNull("Base Currency Missing from Trade Request Leg",legfromDB.getBaseCurrency());
                assertNotNull("Variable Currency Missing from Trade Request Leg",legfromDB.getTermCurrency());
                assertNotNull("Dealt Currency Missing from Trade Request Leg",legfromDB.getDealtCurrency());

                assertEquals( "Expected STDQuoteConvention", legfromDB.getFXRateConvention().getShortName(), standardQuoteConvName );
                assertEquals( "Base Currency Different from EUR", legfromDB.getBaseCurrency().getName(), TestUtilsConstant.EUR.getName() );
                assertEquals( "Term Currency Different from USD", legfromDB.getTermCurrency().getName(), TestUtilsConstant.USD.getName() );
                assertEquals("Dealt Currency Different from EUR",legfromDB.getDealtCurrency().getName(), TestUtilsConstant.EUR.getName());
                assertTrue( "Dealt Amount not equal to 1000", legfromDB.getDealtAmount() == 1000.00d );
                assertTrue( legfromDB.getMarketForwardPoints() == forwardPoints );
                assertTrue( legfromDB.getMarketSpotRate() == spotrate );

                Collection<MarketSpotPrice> collection= np2.getMarketPrices();
                assertTrue(collection.size() == 1);


            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCreateNettingTradeRequest" );
        }

    }
}
