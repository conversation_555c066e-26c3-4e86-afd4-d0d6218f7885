package com.integral.admin.tradechannel;

import com.integral.finance.trade.TradeChannelC;
import com.integral.test.PTestCaseC;
import org.junit.Test;

/**
 * <AUTHOR> jessy
 *         To change this template use File | Settings | File Templates.
 */
public class TradeChannelServicePTestC extends PTestCaseC {

    public TradeChannelService tradeChannelService = TradeChannelServiceImpl.getInstance();

    public TradeChannelServicePTestC(final String aName) {
        super(aName);
    }

    @Test
    public void testCreateChannel() {
//        int i = 5;
//        String channel = "testtest" + i;
//        String value = "DNET,FXI,ESP,OCO(OCO Orders),FXI .NET ESP OCO Orders";
//        String[] cols = value.split(",");
//        if (cols.length < 5) {
//            log.error(" Invalid value input");
//            return;
//        }
//        TradeChannelC tradeChannelC = new TradeChannelC(channel, cols[0], cols[1], cols[2], cols[3], cols[4]);
//        tradeChannelService.create(tradeChannelC);
//        TradeChannelC tradeChannelResult = tradeChannelService.get(channel);
//        assertObject(tradeChannelC, tradeChannelResult);
    }

    @Test
    public void testGetChannel(){
        String channel = "12testtest";
        TradeChannelC tradeChannelResult = tradeChannelService.get(channel);
        assertNull(tradeChannelResult);
    }

    @Test
    public void testUpdateExistedChannel(){
        String channel = "testtest1";
        String value = "ABC,FXI,ESP,OCO(OCO Orders),FXI .NET ESP OCO Orders";
        String[] cols = value.split(",");
        if (cols.length < 5) {
            log.error(" Invalid value input");
            return;
        }
        TradeChannelC tradeChannelC = new TradeChannelC(channel, cols[0], cols[1], cols[2], cols[3], cols[4]);
        tradeChannelService.set(tradeChannelC);
        TradeChannelC tradeChannelResult = tradeChannelService.get(channel);
        assertObject(tradeChannelC, tradeChannelResult);
    }

    public void assertObject(TradeChannelC expected, TradeChannelC actual) {
        assertEquals(expected.getChannel(), actual.getChannel());
        assertEquals(expected.getUiComponent(), actual.getUiComponent());
        assertEquals(expected.getWorkflowType(), actual.getWorkflowType());
        assertEquals(expected.getAppServerType(), actual.getAppServerType());
        assertEquals(expected.getClientApp(), actual.getClientApp());
        assertEquals(expected.getDisplay(), actual.getDisplay());
    }
}
