package com.integral.admin.scheduler

import com.integral.admin.ha.AdminMasterSlaveTransitionHandler
import com.integral.scheduler.ScheduleEvent
import com.integral.scheduler.ScheduleEventC
import com.integral.scheduler.ScheduleEventParametersC
import com.integral.system.configuration.ServerMBean
import com.integral.system.server.VirtualServer
import com.integral.system.server.VirtualServerC
import com.integral.testframework.api.API
/**
 * Created by rush<PERSON><PERSON> on 1/20/17.
 */
class ScheduleEventUpdateVerificationServiceTest extends GroovyTestCase{

    void setUp(){
        API.setupInMemoryTestInfrastructure()
    }

    void testSEUVSMaster_Slave_Disabled(){
        API.property ServerMBean.ADMIN_MASTER_SLAVE_CONFIG_ENABLED , "false"
        assertTrue(ScheduleEventUpdateVerificationService.shouldUpdateGlobalScheduleEvent(null))
    }

    void testSEUVSAllScenarios(){
        API.property ServerMBean.ADMIN_MASTER_SLAVE_CONFIG_ENABLED,"true"
        assertTrue(ScheduleEventUpdateVerificationService.shouldUpdateGlobalScheduleEvent(null))

        ScheduleEvent scheduleEvent = new ScheduleEventC()
        assertTrue(ScheduleEventUpdateVerificationService.shouldUpdateGlobalScheduleEvent(scheduleEvent))

        scheduleEvent.setScheduleEventParameters(new ScheduleEventParametersC())
        assertTrue(ScheduleEventUpdateVerificationService.shouldUpdateGlobalScheduleEvent(scheduleEvent))

        scheduleEvent.getScheduleEventParameters().setVirtualServer(new VirtualServerC())
        assertTrue(ScheduleEventUpdateVerificationService.shouldUpdateGlobalScheduleEvent(scheduleEvent))

        scheduleEvent.getScheduleEventParameters().getVirtualServer().setName(VirtualServer.GLOBAL_VIRTUAL_SERVER_NAME)
        assertTrue(ScheduleEventUpdateVerificationService.shouldUpdateGlobalScheduleEvent(scheduleEvent))

        API.property "IDC.SCHEDULEEVENTC.ALLOWGLOBALEVENTUPDATEBYPRIMARYADMINONLY","true"
        assertFalse(ScheduleEventUpdateVerificationService.shouldUpdateGlobalScheduleEvent(scheduleEvent))

        AdminMasterSlaveTransitionHandler transitionHandler = new AdminMasterSlaveTransitionHandler();
        transitionHandler.onBecomeMasterFromSlave("","")
        assertTrue(ScheduleEventUpdateVerificationService.shouldUpdateGlobalScheduleEvent(scheduleEvent))


    }
}
