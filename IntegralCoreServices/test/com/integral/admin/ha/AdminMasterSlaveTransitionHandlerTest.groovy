package com.integral.admin.ha

/**
 * Created by <PERSON><PERSON><PERSON> on 1/20/17.
 */
class AdminMasterSlaveTransitionHandlerTest extends GroovyTestCase{

    void testTransition() {
        AdminMasterSlaveTransitionHandler handler = new AdminMasterSlaveTransitionHandler();
        handler.onBecomeMasterFromSlave(null,null)
        assertTrue(PrimaryAdminIdentifier.getInstance().isPrimary())

        handler.onBecomeOffLineFromSlave(null,null)
        assertFalse(PrimaryAdminIdentifier.getInstance().isPrimary())

        handler.onBecomeSlaveFromMaster(null,null)
        assertFalse(PrimaryAdminIdentifier.getInstance().isPrimary())

        handler.onBecomeSlaveFromOffline(null,null)
        assertFalse(PrimaryAdminIdentifier.getInstance().isPrimary())
    }
}
