package com.integral.admin.provisioning;

import org.eclipse.persistence.sessions.UnitOfWork;
import org.junit.Test;

import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;

public class CustomerProvisioningPTestC extends PTestCaseC
{

   public CustomerProvisioningPTestC(final String aName)
   {
      super(aName);
   }

   @Test
   public void testCreateUser()
   {
      try
      {
         UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
         uow.removeAllReadOnlyClasses();

         CustomerProvisioning customerProvisioning = new CustomerProvisioningC();
         customerProvisioning = (CustomerProvisioning)uow.registerObject(customerProvisioning);

         Organization fi2 = ReferenceDataCacheC.getInstance().getOrganization("FI2");
         fi2 = (Organization)uow.registerObject(fi2);

         customerProvisioning.setOrganization(fi2);
         customerProvisioning.setJsonConfig("JSON { }");

         uow.commit();

         customerProvisioning = (CustomerProvisioning)getPersistenceSession().refreshObject(customerProvisioning);
         assertEquals(customerProvisioning.getOrganization().getShortName(), "FI2");
         assertEquals(customerProvisioning.getJsonConfig(), "JSON { }");

         uow = getPersistenceSession().acquireUnitOfWork();
         uow.removeAllReadOnlyClasses();
         customerProvisioning = (CustomerProvisioning)uow.registerObject(customerProvisioning);
         customerProvisioning.setJsonConfig("JSON { modified }");

         uow.commit();

         customerProvisioning = (CustomerProvisioning)getPersistenceSession().refreshObject(customerProvisioning);
         assertEquals(customerProvisioning.getOrganization().getShortName(), "FI2");
         assertEquals(customerProvisioning.getJsonConfig(), "JSON { modified }");

         uow = getPersistenceSession().acquireUnitOfWork();
         uow.removeAllReadOnlyClasses();
         customerProvisioning = (CustomerProvisioning)uow.registerObject(customerProvisioning);

         uow.deleteObject(customerProvisioning);
         uow.commit();

         customerProvisioning = (CustomerProvisioning)getPersistenceSession().refreshObject(customerProvisioning);
         assertNull("Could not delete customer provisioning object", customerProvisioning);
      }
      catch (Exception e)
      {
         fail("insert customer provisioning");
         e.printStackTrace();
      }
   }
}
