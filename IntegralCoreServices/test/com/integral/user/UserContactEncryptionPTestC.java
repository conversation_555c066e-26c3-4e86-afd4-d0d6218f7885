package com.integral.user;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.creditLimit.CreditUtilizationEventC;
import com.integral.security.CryptC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;

/**
 * Created by IntelliJ IDEA.
 * User: avinash
 * Date: 3/10/11
 * Time: 1:21 PM
 * To change this template use File | Settings | File Templates.
 */
public class UserContactEncryptionPTestC extends PTestCaseC
{
    static String leName = "FI2-le1";
    private static final String LONG_NAME = "longName";
    private static final String TITLE = "title";
    private static final String FIRST_NAME = "firstName";
    private static final String LAST_NAME = "lastName";
    private static final String POSTAL_CODE = "postalCode";
    private static final String PHONE_NUMBER = "phoneNumber";
    private static final String EMAIL_ADDRESS = "emailAddress";
    private static final String FAX_NUMBER = "faxNumber";
    private static final String COUNTRY = "country";
    private static final String ADDRESS_LINE_1 = "addressLine1";
    private static final String ADDRESS_LINE_2 = "addressLine2";
    private static final String ADDRESS_LINE_3 = "addressLine3";
    private static final String ADDRESS_LINE_4 = "addressLine4";
    private static final String QUESTION = "question";
    private static final String SHORT_NAME = "shortName";
    private static final String ORGANIZATION = "organization";
    private static final String ANSWER = "answer";

    public UserContactEncryptionPTestC( final String aName )
    {
        super( aName );
    }

    public void testCreateUser()
    {
        log.info( "testCreateUser" );
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( SHORT_NAME ).equal( "FI2" );
            OrganizationC org = ( OrganizationC ) session1.readObject( OrganizationC.class, expr );

            org = ( OrganizationC ) uow1.registerObject( org );
            String userShortName =  "user10" + System.nanoTime();
            String answer = ANSWER + System.nanoTime();
            User user1 = UserFactory.newUser( userShortName );
            user1 = ( UserC ) uow1.registerObject( user1 );
            user1.setOrganization( org );
            user1.setNamespace( org.getNamespace() );
            LegalEntity ddLe = (LegalEntity) namedEntityReader.execute( LegalEntity.class, leName );
            ddLe = (LegalEntity) uow1.registerObject(ddLe);
            user1.setDefaultDealingEntity(ddLe);
            user1.setLongName( userShortName );
            user1.setEncFields( true );
            user1.setPasswordQuestion( QUESTION );
            user1.setPasswordAnswer( answer );
            uow1.commit();

            Expression expr1 = eb.get( SHORT_NAME ).equal( userShortName );
            Expression expr2 = eb.get( ORGANIZATION ).get( SHORT_NAME ).equal( org.getShortName() );

            UserC user2 = ( UserC ) session1.readObject( UserC.class, expr1.and(expr2) );
            assertEquals( user2.getLongName() , userShortName );
            assertEquals( user2.getPasswordAnswer() , answer );

            ReportQuery query = new ReportQuery( eb );
            query.setReferenceClass( UserC.class );
            query.setSelectionCriteria( expr1.and( expr2 ) );
            query.useCollectionClass( ArrayList.class );
            query.addAttribute( LONG_NAME, eb.getField( LONG_NAME ) );
            query.addAttribute( ANSWER, eb.getField( ANSWER ) );
            Collection<ReportQueryResult> rqResult = (Collection<ReportQueryResult>) session1.executeQuery( query );
            for ( ReportQueryResult row : rqResult )
            {
                String encLongName = ( String ) row.get( LONG_NAME );
                String encAnswer = ( String ) row.get( ANSWER );
                assertEquals( userShortName , CryptC.decrypt( encLongName ) );
                assertEquals( answer , CryptC.decrypt( encAnswer ) );
            }

            //user contact query


        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCreateUser" );
        }
    }

    public void testCreateUserContact()
    {
        log.info( "testCreateUserContact" );
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( SHORT_NAME ).equal( "FI2" );
            OrganizationC org = ( OrganizationC ) session1.readObject( OrganizationC.class, expr );

            org = ( OrganizationC ) uow1.registerObject( org );
            String userShortName =  "user10" + System.nanoTime();
            String answer = ANSWER + System.nanoTime();
            String title = TITLE + System.nanoTime();
            String firstName = FIRST_NAME + System.nanoTime();
            String lastName = LAST_NAME + System.nanoTime();
            String ph = "ph" + System.nanoTime();
            String fax = "fax" + System.nanoTime();
            String email = "email" + System.nanoTime();
            String zip = "zip" + System.nanoTime();
            String country = COUNTRY + System.nanoTime();
            String addr1 = "addr1" + System.nanoTime();
            String addr2 = "addr2" + System.nanoTime();
            String addr3 = "addr3" + System.nanoTime();
            String addr4 = "addr4" + System.nanoTime();

            User user1 = UserFactory.newUser( userShortName );
            user1 = ( UserC ) uow1.registerObject( user1 );
            user1.setOrganization( org );
            user1.setNamespace( org.getNamespace() );
            LegalEntity ddLe = (LegalEntity) namedEntityReader.execute( LegalEntity.class, leName );
            ddLe = (LegalEntity) uow1.registerObject(ddLe);
            user1.setDefaultDealingEntity( ddLe );
            user1.setLongName( userShortName );
            user1.setEncFields( true );
            user1.setPasswordQuestion( QUESTION );
            user1.setPasswordAnswer( answer );

            UserContactC contact = new UserContactC();
            contact = ( UserContactC ) uow1.registerObject( contact );
            contact.setShortName( userShortName );
            contact.setLongName( userShortName );
            contact.setUser( user1 );
            contact.setTitle( title );
            contact.setFirstName( firstName );
            contact.setLastName( lastName );
            contact.setPhoneNumber( ph );
            contact.setFaxNumber( fax );
            contact.setEmailAddress( email );
            contact.setPostalCode( zip );
            contact.setCountry( country );
            contact.setAddressLine1( addr1 );
            contact.setAddressLine2( addr2 );
            contact.setAddressLine3( addr3 );
            contact.setAddressLine4( addr4 );

            uow1.commit();


            Expression expr3 = eb.get( SHORT_NAME ).equal( userShortName );
            Expression expr4 = eb.get( "user" ).get( SHORT_NAME ).equal( userShortName );
            Expression expr5 = eb.get( "user" ).get( ORGANIZATION ).get( SHORT_NAME ).equal( org.getShortName() );

            UserContactC contact1 = ( UserContactC ) session1.readObject( UserContactC.class, expr3.and(expr4).and(expr5) );
            assertEquals( contact1.getLongName() , userShortName );
            assertEquals( contact1.getTitle(), title );
            assertEquals( contact1.getFirstName(), firstName );
            assertEquals( contact1.getLastName(), lastName );
            assertEquals( contact1.getPhoneNumber(), ph );
            assertEquals( contact1.getEmailAddress(), email );
            assertEquals( contact1.getCountry(), country );
            assertEquals( contact1.getAddressLine1(), addr1 );
            assertEquals( contact1.getAddressLine2(), addr2 );
            assertEquals( contact1.getAddressLine3(), addr3 );
            assertEquals( contact1.getAddressLine4(), addr4 );

            //user contact query
            ReportQuery query1 = new ReportQuery( eb );
            query1.setReferenceClass( UserContactC.class );
            query1.setSelectionCriteria( expr3.and( expr4 ).and( expr5 ) );
            query1.useCollectionClass( ArrayList.class );
            query1.addAttribute( LONG_NAME, eb.getField( LONG_NAME ) );
            query1.addAttribute( TITLE, eb.getField( TITLE ) );
            query1.addAttribute( FIRST_NAME, eb.getField( FIRST_NAME ) );
            query1.addAttribute( LAST_NAME, eb.getField( LAST_NAME ) );
            query1.addAttribute( ADDRESS_LINE_1, eb.getField( ADDRESS_LINE_1 ) );
            query1.addAttribute( ADDRESS_LINE_2, eb.getField( ADDRESS_LINE_2 ) );
            query1.addAttribute( ADDRESS_LINE_3, eb.getField( ADDRESS_LINE_3 ) );
            query1.addAttribute( ADDRESS_LINE_4, eb.getField( ADDRESS_LINE_4 ) );
            query1.addAttribute( POSTAL_CODE, eb.getField( POSTAL_CODE ) );
            query1.addAttribute( COUNTRY, eb.getField( COUNTRY ) );
            query1.addAttribute( EMAIL_ADDRESS, eb.getField( EMAIL_ADDRESS ) );
            query1.addAttribute( PHONE_NUMBER, eb.getField( PHONE_NUMBER ) );
            query1.addAttribute( FAX_NUMBER, eb.getField( FAX_NUMBER ) );
            Collection<ReportQueryResult> rqResult1 = (Collection<ReportQueryResult>) session1.executeQuery( query1 );
            for ( ReportQueryResult row : rqResult1 )
            {
                String encLongName = ( String ) row.get( LONG_NAME );
                String encTitle = ( String ) row.get( TITLE );
                String encFirstName = ( String ) row.get( FIRST_NAME );
                String encLastName = ( String ) row.get( LAST_NAME );
                String enczip = ( String ) row.get( POSTAL_CODE );
                String encph = ( String ) row.get( PHONE_NUMBER );
                String encemail = ( String ) row.get( EMAIL_ADDRESS );
                String encfax = ( String ) row.get( FAX_NUMBER );
                String encCountry = ( String ) row.get( COUNTRY );
                String encaddr1 = ( String ) row.get( ADDRESS_LINE_1 );
                String encaddr2 = ( String ) row.get( ADDRESS_LINE_2 );
                String encaddr3 = ( String ) row.get( ADDRESS_LINE_3 );
                String encaddr4 = ( String ) row.get( ADDRESS_LINE_4 );
                assertEquals( userShortName , CryptC.decrypt( encLongName ) );
                assertEquals( title , CryptC.decrypt( encTitle ) );
                assertEquals( firstName , CryptC.decrypt( encFirstName ) );
                assertEquals( lastName , CryptC.decrypt( encLastName ) );
                assertEquals( zip , CryptC.decrypt( enczip ) );
                assertEquals( ph , CryptC.decrypt( encph ) );
                assertEquals( email , CryptC.decrypt( encemail ) );
                assertEquals( fax , CryptC.decrypt( encfax ) );
                assertEquals( country , CryptC.decrypt( encCountry ) );
                assertEquals( addr1 , CryptC.decrypt( encaddr1 ) );
                assertEquals( addr2 , CryptC.decrypt( encaddr2 ) );
                assertEquals( addr3 , CryptC.decrypt( encaddr3 ) );
                assertEquals( addr4 , CryptC.decrypt( encaddr4 ) );
            }


        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCreateUser" );
        }
    }

}
