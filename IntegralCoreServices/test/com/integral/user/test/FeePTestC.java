package com.integral.user.test;

import com.integral.finance.currency.Currency;
import com.integral.persistence.Entity;
import com.integral.test.PTestCaseC;
import com.integral.user.Fee;
import com.integral.user.FeeSchedule;
import com.integral.user.UserFactory;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Iterator;
import java.util.Vector;


/**
 * Tests fees and fee schedules
 */
public class FeePTestC
        extends PTestCaseC
{
    static String name = "Fee Test";

    public FeePTestC( String name )
    {
        super( name );
    }

    public void testInsertFee()
    {
        try
        {
            Session session = ( Session ) getPersistenceSession();

            Currency crnc = ( Currency ) session.readObject( Currency.class, new ExpressionBuilder().get( Entity.Status ).equal( Entity.ACTIVE_STATUS ) );

            UnitOfWork uow = session.acquireUnitOfWork();
            Fee fee = UserFactory.newFee();
            fee.setSpotFeeAmount( new Long( 10 ) );
            fee.setNonSpotFeeAmount( new Long( 15 ) );
            fee.setRangeStart( new Long( 0 ) );
            fee.setRangeEnd( new Long( 9999 ) );
            fee.setCurrency( crnc );

            uow.registerObject( fee );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insert fee" );
        }
    }

    public void testInsertFeeSchedule()
    {
        try
        {
            Session session = ( Session ) getPersistenceSession();

            Currency crnc = ( Currency ) session.readObject( Currency.class, new ExpressionBuilder().get( Entity.Status ).equal( Entity.ACTIVE_STATUS ) );

            UnitOfWork uow = session.acquireUnitOfWork();
            Fee fee = UserFactory.newFee();
            fee.setSpotFeeAmount( new Long( 10 ) );
            fee.setNonSpotFeeAmount( new Long( 15 ) );
            fee.setRangeStart( new Long( 0 ) );
            fee.setRangeEnd( new Long( 9999 ) );
            fee.setCurrency( crnc );

            FeeSchedule feeSchedule = UserFactory.newFeeSchedule();
            feeSchedule.addFee( fee );

            uow.registerObject( feeSchedule );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insert fee schedule" );
        }
    }

    public void testFeeLookup()
    {
        try
        {
            Session session = ( Session ) getPersistenceSession();
            Vector fees = session.readAllObjects( Fee.class );
            for ( int i = 0; i < fees.size(); i++ )
            {
                Fee fee = ( Fee ) fees.elementAt( i );
                printFee( fee, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookup fee" );
        }
    }

    public void testFeeScheduleLookup()
    {
        try
        {
            Session session = ( Session ) getPersistenceSession();
            Vector fees = session.readAllObjects( FeeSchedule.class );
            for ( int i = 0; i < fees.size(); i++ )
            {
                FeeSchedule fee = ( FeeSchedule ) fees.elementAt( i );
                printFeeSchedule( fee, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookup fee schedule" );
        }
    }

    private void printFee( Fee fee, int i )
    {
        log.info( "fee #" + i + ": " + fee );
        log.info( "\tamount: " + fee.getSpotFeeAmount() );
        log.info( "\tnon-spot amount: " + fee.getNonSpotFeeAmount() );
        log.info( "\tcrnc  : " + fee.getCurrency() );
        log.info( "\trange: " + fee.getRangeStart() + " --> " + fee.getRangeEnd() );
        log.info( "\tfee schedule: " + fee.getFeeSchedule() );
    }

    private void printFeeSchedule( FeeSchedule feeSchedule, int i )
    {
        log.info( "fee schedule #" + i + ": " + feeSchedule );
        Iterator it = feeSchedule.getFees().iterator();
        int j = 0;
        while ( it.hasNext() )
        {
            Fee fee = ( Fee ) it.next();
            printFee( fee, j );
            j++;
        }
    }
}
