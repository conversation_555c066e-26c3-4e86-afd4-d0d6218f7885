package com.integral.user.test;

import com.integral.persistence.Namespace;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.User;
import com.integral.user.UserContact;
import com.integral.user.UserFactory;
import com.integral.user.config.UserMBean;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.queries.ReadObjectQuery;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Iterator;
import java.util.Vector;

/**
 * Query tests on user objects
 */
public class UserQueryPTestC
        extends PTestCaseC
{
    public UserQueryPTestC( String aName )
    {
        super( aName );
    }

    public void testFindAllUsersNamedIntegralUsingQueryService()
    {
        log( "testFindAllUsersNamedIntegral" );
        try
        {
            QueryService qs = QueryFactory.getQueryService();
            ExpressionBuilder eb = new ExpressionBuilder();

            Expression expr11 = eb.get( "objectID" ).equal( 1 );
            ReadObjectQuery query11 = new ReadObjectQuery( Namespace.class, expr11 );
            Namespace ns = ( Namespace ) qs.find( query11 );
            ExpressionBuilder eb1 = new ExpressionBuilder();
            Expression expr = eb1.get( "shortName" ).equal( "Integral2" );
            ReadAllQuery raQuery = new ReadAllQuery();
            raQuery.setReferenceClass( com.integral.user.User.class );
            raQuery.setSelectionCriteria( expr );

            Vector users = ( Vector ) qs.find( ns, raQuery );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                log.info( "\t#" + i + ":\t" + user.getObjectID()
                        + '\t' + user.getShortName()
                        + '\t' + user.getLongName() );
            }
        }
        catch ( Exception e )
        {
            fail( "finding all Integral users", e );
        }
    }

    /**
     * Finds all active users with the short name "Integral"
     */

    public void testFindAllUsersNamedIntegral()
    {
        log( "testFindAllUsersNamedIntegral" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr1 = eb.get( "status" ).equal( 'A' );
            Expression expr2 = eb.get( "shortName" ).equal( "Integral" );
            Expression expr = expr1.and( expr2 );

            Vector users = getPersistenceSession().readAllObjects( User.class, expr );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                log.info( "\t#" + i + ":\t" + user.getObjectID()
                        + '\t' + user.getShortName()
                        + '\t' + user.getLongName() );
            }
        }
        catch ( Exception e )
        {
            fail( "finding all Integral users", e );
        }
    }

    public void testFindUserIntegralWithID()
    {
        log( "testFindUserIntegralWithID" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr1 = eb.get( "status" ).equal( 'A' );
            Expression expr2 = eb.get( "objectID" ).equal( 1 );
            Expression expr3 = eb.get( "namespace" ).get( "objectID" ).equal( 1 );
            Expression expr = expr1.and( expr2 ).and( expr3 );

            Vector users = getPersistenceSession().readAllObjects( User.class, expr );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                log.info( "\t#" + i + ":\t" + user.getObjectID()
                        + '\t' + user.getShortName()
                        + '\t' + user.getLongName() );
            }
        }
        catch ( Exception e )
        {
            fail( "finding all Integral users", e );
        }
    }

    public void testFindUserIntegralWithIDUsingQueryService()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr1 = eb.get( "status" ).equal( 'A' );
            Expression expr2 = eb.get( "objectID" ).equal( 1 );
            Expression expr3 = eb.get( "namespace" ).get( "objectID" ).equal( 1 );
            Expression expr = expr1.and( expr2 ).and( expr3 );

            ReadObjectQuery query = new ReadObjectQuery( User.class, expr );
            QueryService qs = QueryFactory.getQueryService();
            User us = ( User ) qs.find( query );

            log.info( "*** \t" + us.getObjectID()
                    + '\t' + us.getShortName()
                    + '\t' + us.getNamespace().getObjectID() );
        }
        catch ( Exception e )
        {
            fail( "finding user Interal", e );
        }
    }

    public void testFindUserIntegralWithSNUsingQueryService()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Integral" );

            ReadObjectQuery query = new ReadObjectQuery( User.class, expr );
            QueryService qs = QueryFactory.getQueryService();
            User us = ( User ) qs.find( query );

            log.info( "*** \t" + us.getObjectID()
                    + '\t' + us.getShortName()
                    + '\t' + us.getNamespace().getObjectID() );
        }
        catch ( Exception e )
        {
            fail( "finding user Interal", e );
        }
    }

    public void testFindUserIntegralWithLNUsingQueryService()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "longName" ).equal( "Integral1" );

            ReadObjectQuery query = new ReadObjectQuery( User.class, expr );
            QueryService qs = QueryFactory.getQueryService();
            User us = ( User ) qs.find( query );

            if ( us == null )
            {
                log.info( "****** UserQueryPTestC.testFindUserIntegralWithLNUsingQueryService(): result is null" );
            }
            else
            {
                log.info( "*** \t" + us.getObjectID()
                        + '\t' + us.getShortName()
                        + '\t' + us.getNamespace().getObjectID() );
            }
        }
        catch ( Exception e )
        {
            fail( "finding user Interal", e );
        }
    }

    public void testNSFindUserIntegralWithIDUsingQueryService2()
    {
        try
        {
            QueryService qs = QueryFactory.getQueryService();
            ExpressionBuilder eb = new ExpressionBuilder();

            Expression expr11 = eb.get( "objectID" ).equal( 1 );
            ReadObjectQuery query11 = new ReadObjectQuery( Namespace.class, expr11 );
            Namespace ns = ( Namespace ) qs.find( query11 );

            //this tests the objectID value is String.
            Expression expr = eb.get( "objectID" ).equal( "1" );

            ReadObjectQuery query = new ReadObjectQuery( com.integral.user.UserC.class, expr );

            User us = ( User ) qs.find( ns, query );

            if ( us == null )
            {
                log.info( "****** UserQueryPTestC.testNSFindUserIntegralWithIDUsingQueryService2(): result is null" );
            }
            else
            {
                log.info( "*** \t" + us.getObjectID()
                        + '\t' + us.getShortName()
                        + '\t' + us.getNamespace().getObjectID() );
            }
        }
        catch ( Exception e )
        {
            fail( "finding user Interal", e );
        }
    }

    public void testNSFindUserIntegralWithSNUsingQueryService()
    {
        try
        {
            QueryService qs = QueryFactory.getQueryService();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr11 = eb.get( "objectID" ).equal( 1 );
            ReadObjectQuery query11 = new ReadObjectQuery( Namespace.class, expr11 );
            Namespace ns = ( Namespace ) qs.find( query11 );

            Expression expr1 = eb.get( "status" ).equal( 'A' );
            Expression expr2 = eb.get( "shortName" ).equal( "Integral" );
            Expression expr = expr1.and( expr2 );

            ReadObjectQuery query = new ReadObjectQuery( User.class, expr );

            User us = ( User ) qs.find( ns, query );

            log.info( "*** \t" + us.getObjectID()
                    + '\t' + us.getShortName()
                    + '\t' + us.getNamespace().getObjectID() );
        }
        catch ( Exception e )
        {
            fail( "finding user Interal", e );
        }
    }

    /**
     * Finds all active users with the short name "Integral"
     * Instead of fetching the user object, we only read the
     * shortName, longName and description of the user
     */
    public void testFindAllUsersNamedIntegralAsReportQuery()
    {
        log( "testFindAllUsersNamedIntegralAsReportQuery" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr1 = eb.get( "status" ).equal( 'A' );
            Expression expr2 = eb.get( "shortName" ).equal( "Integral" );
            Expression expr = expr1.and( expr2 );

            ReportQuery query = new ReportQuery( eb );
            query.setReferenceClass( User.class );
            query.setSelectionCriteria( expr );

            query.addAttribute( "ID", eb.get( "objectID" ) );
            query.addAttribute( "SN", eb.get( "shortName" ) );
            query.addAttribute( "LN", eb.get( "longName" ) );
            query.addAttribute( "DE", eb.get( "description" ) );

            Vector users = ( Vector ) getPersistenceSession().executeQuery( query );
            for ( int i = 0; i < users.size(); i++ )
            {
                ReportQueryResult user = ( ReportQueryResult ) users.get( i );
                log.info( "\t#" + i + ":\t" + user.get( "ID" )
                        + "\t" + user.get( "SN" )
                        + "\t" + user.get( "LN" )
                        + "\t" + user.get( "DE" ) );
            }
        }
        catch ( Exception e )
        {
            fail( "finding all users Integral using report query", e );
        }
    }

    /**
     * Finds all active user contacts using query service
     */
    public void testFindAllUserCOntactsUsingQueryService()
    {
        try
        {
            QueryService qs = QueryFactory.getQueryService();
            Iterator it = qs.findAll( UserContact.class ).iterator();
            int i = 0;
            while ( it.hasNext() )
            {
                UserContact contact = ( UserContact ) it.next();
                log.info( "\t#" + i + ":\t" + contact.getObjectID()
                        + '\t' + contact.getShortName()
                        + '\t' + contact.getLongName() );
                i++;
            }
        }
        catch ( Exception e )
        {
            fail( "finding all user contacts", e );
        }
    }

    public void testUserInactivationQuery()
    {
        CursoredStream cursor;
        try
        {
            //get properties from userMBean
            UserMBean userMBean = UserFactory.getUserMBean();
            String s = userMBean.getUserInactivationDays();
            int userInactivationDays = Integer.parseInt( s );

            Session aSession = PersistenceFactory.newSession();
            UnitOfWork uow = aSession.acquireUnitOfWork();
            uow.addReadOnlyClass( com.integral.user.OrganizationC.class );
            uow.addReadOnlyClass( com.integral.persistence.NamespaceC.class );
            uow.addReadOnlyClass( com.integral.user.DisplayPreferenceC.class );
            uow.addReadOnlyClass( com.integral.user.DisplayPreference.class );
            uow.addReadOnlyClass( com.integral.persistence.NamespaceGroupC.class );
            uow.addReadOnlyClass( com.integral.user.UserCustomFieldC.class );

            ExpressionBuilder userEb = new ExpressionBuilder();
            Expression lastLoginExpr = userEb.get( "lastLogin" );
            Expression createdDateExpr = userEb.get( "createdDateTime" );
            Expression notLoggedInExpr = lastLoginExpr.isNull().and( createdDateExpr.lessThan( DateTimeFactory.newDate().subtractDays( userInactivationDays ) ) );
            Expression oldLoginExpr = lastLoginExpr.notNull().and( lastLoginExpr.lessThanEqual( DateTimeFactory.newDate().subtractDays( userInactivationDays ) ) );
            Expression exemptExpr = userEb.get( "autoInactivationExempt" ).isNull().or( userEb.get( "autoInactivationExempt" ).equal( false ) );
            Expression userExpr = notLoggedInExpr.or( oldLoginExpr );
            Expression userExpr2 = userEb.get( "status" ).equal( 'A' );
            userExpr = userExpr.and( userExpr2 ).and( exemptExpr );

            ReadAllQuery raq = new ReadAllQuery( com.integral.user.User.class, userExpr );
            raq.useCursoredStream( 100, 100 );

            cursor = ( CursoredStream ) aSession.executeQuery( raq );

            IdcDate now = DateTimeFactory.newDate();
            while ( !cursor.atEnd() )
            {

                User user = ( User ) cursor.read();
                user = ( User ) uow.refreshObject( user );
                if ( user != null )
                {
                    log( "user=" + user.getFullName() + ",lastLogin=" + user.getLastLogin() + ",createdTime=" + user.getCreatedDate() + ",exemptStatus=" + user.isAutoInactivationExempt() );
                    assertFalse( user.isAutoInactivationExempt() );

                    IdcDate refDate = user.getLastLogin() != null ? user.getLastLogin().getDate() : DateTimeFactory.newDate( user.getCreatedDate() );
                    int diff = now.asDays() - refDate.asDays();
                    assertTrue( diff >= userInactivationDays );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testUserInactivationQuery", e );
        }
    }
}
