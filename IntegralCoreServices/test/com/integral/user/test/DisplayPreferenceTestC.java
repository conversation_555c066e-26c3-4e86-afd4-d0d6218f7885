package com.integral.user.test;

import com.integral.test.TestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.user.DisplayPreference;
import com.integral.user.DisplayPreferenceC;

import java.text.DecimalFormat;

public class DisplayPreferenceTestC extends TestCaseC
{
    static String name = "DisplayPreference Test";

    public DisplayPreferenceTestC( String aName )
    {
        super( aName );
    }

    public static void main( String[] args )
    {
        try
        {
            DisplayPreferenceTestC test = new DisplayPreferenceTestC( name );
            test.testDisplayPreference();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void testDisplayPreference()
    {
        try
        {
            DisplayPreference df = new DisplayPreferenceC();
            DecimalFormat decimalFormat;

            df.setDateFormatPattern( "dd-MM-yy" );
            df.setTimeFormatPattern( "hh:mm:ss zzz" );
            df.setTimeZoneID( "EST" );
            df.setNegativeAmountIndicator( DisplayPreference.AMOUNT_NEGATIVE_PARENTHESIS );
            df.setDecimalAmountSeparator( "," );
            df.setThousandsAmountSeparator( ";" );
            df.setGroupingSize( 2 );

            log.info( "DateFormat: " + df.getDateFormat().toPattern() );
            log.info( "\tDate: " +
                    df.getDateFormat().format( DateTimeFactory.newDate().asJdkDate() ) );

            log.info( "DateTimeFormat: " + df.getDateTimeFormat().toPattern() );
            log.info( "\tDateTime: " +
                    df.getDateTimeFormat().format( DateTimeFactory.newDateTime().asJdkDate() ) );


            log.info( "TimeFormat: " + df.getTimeFormat().toPattern() );
            log.info( "\tTime: " +
                    df.getTimeFormat().format( DateTimeFactory.newDateTime().getTime() ) );

            log.info( "TimeFormat: " + df.getTimeFormat().toPattern() );
            log.info( "\tTime: " +
                    df.getTimeFormat().format( DateTimeFactory.newDateTime().asJdkDate() ) );


            log.info( "Default DecimalFormat: " + df.getDecimalFormat().toPattern() );
            log.info( "\tNumber 1135.678\t\t" +
                    df.getDecimalFormat().format( new Double( 1135.678 ) ) );
            log.info( "\tNumber -1135.678\t\t" +
                    df.getDecimalFormat().format( new Double( -1135.678 ) ) );


            log.info( "DecimalFormat #,#.0000: " + df.getDecimalFormat( "#,#.0000" ).toPattern() );
            log.info( "\tNumber 1135.678\t\t" +
                    df.getDecimalFormat( "#,#.0000" ).format( new Double( 1135.678 ) ) );
            log.info( "\tNumber -1135.678\t\t" +
                    df.getDecimalFormat( "#,#.0000" ).format( new Double( -1135.678 ) ) );

            decimalFormat = df.getDecimalFormat();
            decimalFormat.setMultiplier( 10 );

            log.info( "DecimalFormat x10 " + decimalFormat.toPattern() );
            log.info( "\tNumber 1135.678\t\t" +
                    decimalFormat.format( new Double( 1135.678 ) ) );
            log.info( "\tNumber -1135.678\t\t" +
                    decimalFormat.format( new Double( -1135.678 ) ) );
            try
            {
                log.info( "\tReadNumber -1135.678\t\t" + decimalFormat.parseObject( "(1135,678)" ) );
            }
            catch ( Exception e )
            {
                e.printStackTrace();
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
