//package com.integral.persistence.test;
package com.integral.user.test;

import com.integral.test.PTestCaseC;
import com.integral.user.*;
import junit.framework.Test;
import junit.framework.TestSuite;

public class UserPasswordPTestC
        extends PTestCaseC
{
    public UserPasswordPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new UserPasswordPTestC( "testPasswordHistory" ) );
        suite.addTest( new UserPasswordPTestC( "testVerifyPassword" ) );
        suite.addTest( new UserPasswordPTestC( "testAccountStatus" ) );
        return suite;
    }

    public void testPasswordHistory()
    {
        log.debug( "testPasswordHistory" );
        try
        {
            User user = new UserC();

            user.setPassword( "hello" );
            log.debug( "old password: " + ( ( UserC ) user ).getOldPasswords() );
            log.debug( "date/time of setting password: " + user.getPasswordLastTimeModified() );

            user.setPassword( "hello2" );
            log.debug( "old password: " + ( ( UserC ) user ).getOldPasswords() );
            log.debug( "date/time of setting password: " + user.getPasswordLastTimeModified() );

            user.setPassword( "hello3" );
            log.debug( "old password: " + ( ( UserC ) user ).getOldPasswords() );
            log.debug( "date/time of setting password: " + user.getPasswordLastTimeModified() );

            user.setPassword( "hello4" );
            log.debug( "old password: " + ( ( UserC ) user ).getOldPasswords() );
            log.debug( "date/time of setting password: " + user.getPasswordLastTimeModified() );

            user.setPassword( "hello5" );
            log.debug( "old password: " + ( ( UserC ) user ).getOldPasswords() );
            log.debug( "date/time of setting password: " + user.getPasswordLastTimeModified() );

            user.setPassword( "hello6" );
            log.debug( "old password: " + ( ( UserC ) user ).getOldPasswords() );
            log.debug( "date/time of setting password: " + user.getPasswordLastTimeModified() );

            user.setPassword( "hello7" );
            log.debug( "old password: " + ( ( UserC ) user ).getOldPasswords() );
            log.debug( "date/time of setting password: " + user.getPasswordLastTimeModified() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Exception in testPasswordHistory: " + e );
        }
    }

    public void testVerifyPassword()
    {
        log.debug( "testVerifyPassword" );
        try
        {
            User user = new UserC();

            user.setPassword( "hello" );

            try
            {
                user.verifyPassword( "hello" );
                log.debug( "check correct password ok" );
            }
            catch ( Exception e1 )
            {
            }

            try
            {
                user.verifyPassword( "hello2" );
                log.debug( "check incorrect password failed" );
                log.debug( "number of failed attempts: " + user.getNumberOfFailedAuthorizationAttempts() );
            }
            catch ( Exception e1 )
            {
                log.debug( "check incorrect password ok" );
            }


            try
            {
                user.verifyPassword( "hello3" );
                log.debug( "check incorrect password failed" );
                log.debug( "number of failed attempts: " + user.getNumberOfFailedAuthorizationAttempts() );
            }
            catch ( Exception e1 )
            {
                log.debug( "check incorrect password ok" );
            }


            try
            {
                user.verifyPassword( "hello" );
                log.debug( "check correct password ok" );
                log.debug( "number of failed attempts: " + user.getNumberOfFailedAuthorizationAttempts() );
            }
            catch ( Exception e1 )
            {
                log.debug( "check correct password failed" );
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Exception in testVerifyPassword: " + e );
        }
    }

    public void testAccountStatus()
    {
        log.debug( "testAccountStatus" );
        try
        {
            User user = new UserC();
            user.setPassword( "hello" );

            try
            {
                user.verifyPassword( "hello" );
                log.debug( "check account status DEFAULT ok " );
            }
            catch ( Exception e1 )
            {
                log.debug( "check account status DEFAULT failed" );
            }

            try
            {
                user.setAccountStatus( User.ACCOUNT_STATUS_VALID );
                user.verifyPassword( "hello" );
                log.debug( "check account status ACCOUNT_STATUS_VALID ok" );
            }
            catch ( Exception e1 )
            {
                log.debug( "check account status ACCOUNT_STATUS_VALID failed" );
            }

            try
            {
                user.setAccountStatus( User.ACCOUNT_STATUS_PASSWORD_MUST_CHANGE_AT_NEXT_LOGON );
                user.verifyPassword( "hello" );
                log.debug( "check account status ACCOUNT_STATUS_PASSWORD_MUST_CHANGE_AT_NEXT_LOGON failed" );
            }
            catch ( Exception e1 )
            {
                log.debug( "check account status ACCOUNT_STATUS_PASSWORD_MUST_CHANGE_AT_NEXT_LOGON ok" );
            }

            try
            {
                user.setAccountStatus( User.ACCOUNT_STATUS_PASSWORD_NEVER_EXPIRES );
                user.verifyPassword( "hello" );
                log.debug( "check account status ACCOUNT_STATUS_PASSWORD_NEVER_EXPIRES ok" );
            }
            catch ( Exception e1 )
            {
                log.debug( "check account status ACCOUNT_STATUS_PASSWORD_NEVER_EXPIRES failed" );
            }

            try
            {
                user.setAccountStatus( User.ACCOUNT_STATUS_ACCOUNT_DISABLED );
                user.verifyPassword( "hello" );
                log.debug( "check account status ACCOUNT_STATUS_ACCOUNT_DISABLED failed" );
            }
            catch ( Exception e1 )
            {
                log.debug( "check account status ACCOUNT_STATUS_ACCOUNT_DISABLED ok" );
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Exception in testAccountStatus: " + e );
        }
    }

    public void testRandomPasswordGeneration()
    {
        PasswordGeneratorC pwordGen = UserFactory.getPasswordGenerator();
        for ( int i=0; i < 100000; i++ )
        {
            String pword = pwordGen.generateRandomPassword();
            boolean result = PasswordValidation.containsValidPasswordCharacters( pword );
            assertTrue( "Generated Password=" + pword, result );
        }
    }
}
