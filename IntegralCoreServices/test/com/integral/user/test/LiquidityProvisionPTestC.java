package com.integral.user.test;

import java.util.ArrayList;
import java.util.Collection;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyGroup;
import com.integral.finance.currency.CurrencyGroupC;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvisionC;
import com.integral.persistence.Namespace;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserFactory;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

public class LiquidityProvisionPTestC extends PTestCaseC
{
    public static final String name = "LiquidityProvisionPTestC";

    public LiquidityProvisionPTestC(String name)
    {
        super(name);

    }

    public void testInsert()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext("Integral");
            IdcSessionManager.getInstance().setSessionContext(sessContext);
            long time = System.currentTimeMillis();
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass(LiquidityProvisionC.class);
            uow.addReadOnlyClass(CurrencyPairGroupC.class);
            uow.addReadOnlyClass(OrganizationC.class);
            Organization org = UserFactory.newOrganization();
            org = (Organization) uow.registerObject(org);
            Namespace ns = (Namespace) uow.registerObject(new NamespaceC());
            ns.setShortName("TestOrg" + time);
            org.setShortName("TestOrg" + time);
            LiquidityProvision test = (LiquidityProvision) uow.registerObject(new LiquidityProvisionC());

            Collection<Organization> orgs = new ArrayList<Organization>();
            orgs.add(org);
            test.setMinQuoteSize(0.0);
            test.setMinFillSize(0.0);
            test.setMinMatchSize(0.0);
            test.setRegularMatchSize(0.0);
            test.setMinOrderSize(0.0);
            test.setMaxOrderSize(0.0) ;
            test.setSortOrder(0);
            CurrencyGroup baseCcyGroup;
            CurrencyGroup varCcyGroup;
            CurrencyPairGroup ccyPairGroup1;
            CurrencyPairGroup ccyPairGroup;
            ccyPairGroup = new CurrencyPairGroupC();
            baseCcyGroup = new CurrencyGroupC();
            varCcyGroup = new CurrencyGroupC();
            Currency usd, eur, jpy;
            usd = new CurrencyC();
            usd.setShortName( "USD" );
            eur = new CurrencyC();
            eur.setShortName( "EUR" );
            jpy = new CurrencyC();
            jpy.setShortName( "JPY" );
            baseCcyGroup.getIncludedCurrencies().add( usd );
            baseCcyGroup.getIncludedCurrencies().add( eur );
            varCcyGroup.getIncludedCurrencies().add( jpy );
            varCcyGroup.getIncludedCurrencies().add( eur );
            ccyPairGroup1 = new CurrencyPairGroupC();
            ccyPairGroup1.setBaseCurrencyGroup( baseCcyGroup );
            ccyPairGroup1.setVariableCurrencyGroup( varCcyGroup );
            ccyPairGroup = new CurrencyPairGroupC();
            ccyPairGroup.getCurrencyPairGroups().add( ccyPairGroup1 );
            test.setCurrencyPairGroup(ccyPairGroup);
            test.setExecutionProviders(orgs);
            test.setAggregationProviders(orgs); 
       	    test.setOrganization(org);
       	    test.setOwner(org);
            uow.commit();
            log("LiquidityProvision=" + test + ",test.executionProviders=" + test.getExecutionProviders() + ",test.maxOrderSize=" + test.getMaxOrderSize());
            assertEquals("maxOrderSize should be same.", test.getMaxOrderSize(), 0.0);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            fail("Failed: "+ e);
        }
    }
}
