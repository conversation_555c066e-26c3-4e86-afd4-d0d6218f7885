package com.integral.user.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.test.PTestCaseC;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.user.UserContact;
import com.integral.user.UserFactory;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

public class UserCreationPTestC
        extends PTestCaseC
{
    static String name = "User Creation Test";
    static String ddLeName = "DD1-le1";
    public UserCreationPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new UserCreationPTestC( "checkCreateUser" ) );
        suite.addTest( new UserCreationPTestC( "checkCreateOrg" ) );
        suite.addTest( new UserCreationPTestC( "checkCreateDuplicateShortNameUser" ) );
        suite.addTest( new UserCreationPTestC( "checkStatusNoChange" ) );
        suite.addTest( new UserCreationPTestC( "createUserContact" ) );
        suite.addTest( new UserCreationPTestC( "checkUserContact" ) );
        return suite;
    }

    public void checkCreateOrg()
    {
        log.info( "checkCreateOrg" );
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "FICpty11" );
            OrganizationC org = ( OrganizationC ) session1.readObject( OrganizationC.class, expr );

            org = ( OrganizationC ) uow1.registerObject( org );

            User user1 = UserFactory.newUser( "user10" + System.nanoTime() );
            user1 = ( UserC ) uow1.registerObject( user1 );
            user1.setOrganization( org );
            user1.setNamespace( org.getNamespace() );
            uow1.commit();

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "checkCreateOrg" );
        }


    }


    public void checkCreateUser()
    {
        log.info( "checkCreateUser" );
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "FICpty11" );
            OrganizationC org = ( OrganizationC ) session1.readObject( OrganizationC.class, expr );

            org = ( OrganizationC ) uow1.registerObject( org );

            User user1 = UserFactory.newUser( "user10" + System.nanoTime() );
            user1 = ( UserC ) uow1.registerObject( user1 );
            user1.setOrganization( org );
            user1.setNamespace( org.getNamespace() );
            LegalEntity ddLe = (LegalEntity) namedEntityReader.execute( LegalEntity.class, ddLeName );
            ddLe = (LegalEntity) uow1.registerObject(ddLe);
            user1.setDefaultDealingEntity(ddLe);
            uow1.commit();

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "checkCreateUser" );
        }


    }

    public void checkCreateDuplicateShortNameUser()
    {
        log.info( "checkCreateDuplicateShortNameUser" );
        try
        {
            String testUserName = "user10" + System.nanoTime();
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "FICpty11" );
            OrganizationC org = ( OrganizationC ) session1.readObject( OrganizationC.class, expr );

            org = ( OrganizationC ) uow1.registerObject( org );

            User user1 = UserFactory.newUser( testUserName );
            user1 = ( UserC ) uow1.registerNewObject( user1 );
            user1.setOrganization( org );
            user1.setNamespace( org.getNamespace() );
            uow1.commit();

            UnitOfWork uow2 = session1.acquireUnitOfWork();
            uow2.removeAllReadOnlyClasses();

            User duplicateUser = UserFactory.newUser( testUserName );
            duplicateUser = ( UserC ) uow2.registerNewObject( duplicateUser );
            duplicateUser.setOrganization( org );
            duplicateUser.setNamespace( org.getNamespace() );
            uow2.commit();


            fail( "checkCreateDuplicateShortNameUser" );
        }
        catch ( Exception e )
        {
            //This should throw the exception since DB unique constraint
            log.info( "checkCreateDuplicateShortNameUser test success" );
        }
    }

    public void checkStatusNoChange()
    {
        log.info( "checkStatusNoChange" );
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();

            User user = ( User ) session1.readObject( User.class );

            log.info( "user : " + user );
            log.info( "user version: " + user.getVersion() );
            // log.info("user status : " + user.getStatus());
            // log.info("user status LTM : " + user.getStatusLastDateTimeModified());
            log.info( "user LTM        : " + user.getModifiedDate() );

            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            User user2 = ( User ) uow1.registerObject( user );
            // log.info("user 2 status : " + user2.getStatus());
            // log.info("user 2 status LTM : " + user2.getStatusLastDateTimeModified());
            log.info( "user 2 LTM        : " + user2.getModifiedDate() );

            uow1.commit();

            log.info( "user : " + user );
            log.info( "user version: " + user.getVersion() );
            // log.info("user status : " + user.getStatus());
            // log.info("user status LTM : " + user.getStatusLastDateTimeModified());
            log.info( "user LTM        : " + user.getModifiedDate() );

            log.info( "checkStatusNoChange" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "checkStatusNoChange" );
        }
    }

    public void checkStatus()
    {
        log.info( "checkStatus" );
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();

            User user = ( User ) session1.readObject( User.class );

            log.info( "user : " + user );
            log.info( "user version: " + user.getVersion() );
            log.info( "user status : " + user.getStatus() );
            log.info( "user status LTM : " + user.getStatusLastDateModified() );
            log.info( "user LTM        : " + user.getModifiedDate() );

            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            User user2 = ( User ) uow1.registerObject( user );
            user2.setStatus( 'A' );
            log.info( "user 2 status : " + user2.getStatus() );
            log.info( "user 2 status LTM : " + user2.getStatusLastDateModified() );
            log.info( "user 2 LTM        : " + user2.getModifiedDate() );

            uow1.commit();

            log.info( "user : " + user );
            log.info( "user version: " + user.getVersion() );
            log.info( "user status : " + user.getStatus() );
            log.info( "user status LTM : " + user.getStatusLastDateModified() );
            log.info( "user LTM        : " + user.getModifiedDate() );

            log.info( "create user" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create user" );
        }
    }

    public void createUserContact()
    {
        log.info( "createUserContact" );
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr;
            User user = ( User ) session1.readObject( User.class );

            log.info( "user : " + user );
            log.info( "user version: " + user.getVersion() );
            log.info( "user LTM    : " + user.getModifiedDate() );

            UnitOfWork uow1 = session1.acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            User user2 = ( User ) uow1.registerObject( user );
            user2.setDescription( "" + user.getDescription() + 'X' );

            log.info( "user 2 version: " + user2.getVersion() );
            log.info( "user 2 LTM    : " + user2.getModifiedDate() );
            uow1.commit();
            log.info( "user version: " + user.getVersion() );
            log.info( "user LTM    : " + user.getModifiedDate() );

            expr = eb.get( "shortName" ).equal( user.getShortName() );
            User user3 = ( User ) session1.readObject( User.class, expr );
            log.info( "user 3 version: " + user3.getVersion() );
            log.info( "user 3 LTM    : " + user3.getModifiedDate() );

            User user4 = ( User ) session1.refreshObject( user );
            log.info( "user 4 version: " + user4.getVersion() );
            log.info( "user 4 LTM    : " + user4.getModifiedDate() );

            log.info( "create user" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create user" );
        }
    }

    public void checkUserContact()
    {
        log.info( "checkUserContact" );
        try
        {
            Session session = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            String testContact = "test" + System.nanoTime();
            UserContact userContact = UserFactory.newUserContact();
            userContact = ( UserContact ) uow.registerObject( userContact );
            userContact.setLastName( testContact );


            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "fi1mm1" );

            User user = ( User ) session.readObject( User.class, expr );
            user = ( User ) uow.registerObject( user );
            user.setContact( userContact );
            userContact.setUser( user );
            uow.commit();

            UserContact contact = user.getContact();
            log.info( "\tcontact #" + contact.getObjectID() );

            user = ( User ) session.refreshObject( user );

            contact = user.getContact();
            log.info( "\tcontact #" + contact.getObjectID() );

            log.info( "create user" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create user" );
        }
    }

}
