package com.integral.user.test;


import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import junit.framework.Test;
import junit.framework.TestSuite;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import com.integral.finance.dealing.priceProvision.SpreadGroup;
import com.integral.finance.dealing.priceProvision.SpreadGroupC;
import com.integral.finance.dealing.priceProvision.SpreadTenorParameters;
import com.integral.finance.dealing.priceProvision.SpreadTenorParametersC;
import com.integral.finance.trade.Tenor;
import com.integral.provider.ProviderOrgFunction;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.OrganizationRelationship;
import com.integral.user.PriceProvision;


public class PriceProvisionPersistencePTestC
        extends PTestCaseC
{
    static String name = "Organization Test";
    static String lpUserName = "Citigroup";

    public PriceProvisionPersistencePTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new PriceProvisionPersistencePTestC( "testSpreadProfileSave" ) );
        return suite;
    }

    /**
     * Test inserting trading limits for an org
     */
    public void testSpreadProfileSave()
    {
        // insert org and trading limit
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "CITI" );
            Expression expr2 = eb.get( "shortName" ).equal( "BPS" ).and(eb.get("namespace").get( "shortName" ).equal("CITI"));

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            OrganizationC org = ( OrganizationC ) uow.readObject( OrganizationC.class, expr );
            SpreadGroup grpcurrent = ( SpreadGroup ) uow.readObject( SpreadGroupC.class, expr2 );
            uow.removeAllReadOnlyClasses();
            OrganizationRelationship organizationRelationship = getOrganizationRelationship(org,"FI1","LP_to_FI");
            if( null!= grpcurrent )
            {
            	log.info("Deleting Existing Test Object" +grpcurrent.toString());
            	if( null!=organizationRelationship.getCustomerPriceProvision().getSpreadGroup() && organizationRelationship.getCustomerPriceProvision().getSpreadGroup().isSameAs(grpcurrent))
            	{
            		organizationRelationship = (OrganizationRelationship)uow.registerObject(organizationRelationship);
            		organizationRelationship.getCustomerPriceProvision().setSpreadGroup(null);
            	}
            	uow.deleteObject(grpcurrent);
            	uow.commitAndResume();
            }
            ProviderOrgFunction function= org.getProviderOrgFunction();
            function = ( ProviderOrgFunction ) uow.registerObject( function );
            SpreadGroup group = new SpreadGroupC();
            group.setSpreadType(SpreadGroup.BPS);
            group.setName("BPS");
            group.setLongName("Basis Point Spread");
            group.setDescription("Basis Point Spread");
            
            group = ( SpreadGroup ) uow.registerObject( group );
            List<SpreadGroup> spreadGroups = new ArrayList<SpreadGroup>();
            spreadGroups.add(group);
            function.setSpreadGroups(spreadGroups);
            uow.commit();

            Organization organization= (Organization)uow.refreshObject(org);
            
            Collection<SpreadGroup> collection = organization.getProviderOrgFunction().getSpreadGroups();
            assertEquals("Failed to persist Spread Groups",collection.size()==1,true);
            for (SpreadGroup spreadGroup : collection) {
				assertEquals("Spread Type mismatch",spreadGroup.getSpreadType(), SpreadGroup.BPS);
				assertEquals(spreadGroup.getName(), "BPS");
			}
        }
        catch ( Exception e )
        {
            fail( "test spread profile" );
            e.printStackTrace();
        }
    }
    public void testCustomerPricePRovision()
    {
        // insert org and trading limit
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "CITI" );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            OrganizationC org = ( OrganizationC ) uow.readObject( OrganizationC.class, expr );
            uow.removeAllReadOnlyClasses();
            OrganizationRelationship organizationRelationship = getOrganizationRelationship(org,"FI1","LP_to_FI");

            organizationRelationship = (OrganizationRelationship)uow.registerObject(organizationRelationship);
            organizationRelationship.getCustomerPriceProvision().setCustomerSpecificSpreads(true);
            organizationRelationship.getCustomerPriceProvision().setCustomerTradeLimit(true);
            organizationRelationship.getCustomerPriceProvision().setPreTradeSpreading(false);
            organizationRelationship.getCustomerPriceProvision().setAllowSpreadImprovement(true);
            organizationRelationship.getCustomerPriceProvision().setGivebackUtilizedOrderMatching(true);
            organizationRelationship.getCustomerPriceProvision().setSpotBidSpread(1);
            organizationRelationship.getCustomerPriceProvision().setSpotOfferSpread(2);
            organizationRelationship.getCustomerPriceProvision().setSpotSpreadClsf(PriceProvision.SPREAD_CLSF_BPS);
            organizationRelationship.getCustomerPriceProvision().setSpreadImprovementPercent(10.0d);

            uow.commit();

            OrganizationRelationship organizationRelationship1= (OrganizationRelationship)uow.refreshObject(organizationRelationship);
            assertEquals(organizationRelationship1.getCustomerPriceProvision().isCustomerSpecificSpreads(), true);
            assertEquals(organizationRelationship1.getCustomerPriceProvision().isCustomerTradeLimit(), true);
            assertEquals(organizationRelationship1.getCustomerPriceProvision().isPreTradeSpreading(),false);
            assertEquals(organizationRelationship1.getCustomerPriceProvision().getSpotBidSpread(), 1.0);
            assertEquals(organizationRelationship1.getCustomerPriceProvision().getSpotOfferSpread(), 2.0);
            assertEquals(organizationRelationship1.getCustomerPriceProvision().getSpotSpreadClsf(), PriceProvision.SPREAD_CLSF_BPS);
            assertEquals(organizationRelationship1.getCustomerPriceProvision().isAllowSpreadImprovement(),true);            
            assertEquals(organizationRelationship1.getCustomerPriceProvision().isGivebackUtilizedOrderMatching(),true);            
            assertEquals(organizationRelationship1.getCustomerPriceProvision().getSpreadImprovementPercent(),10.0d);            
        }
        catch ( Exception e )
        {
            fail( "test spread profile" );
            e.printStackTrace();
        }
    }
    /**
     * Test inserting trading limits for an org
     */
    public void testSpreadProfileSaveOrgRel()
    {
        // insert org and trading limit
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "CITI" );
            Expression expr2 = eb.get( "shortName" ).equal( "BPS2" ).and(eb.get("namespace").get( "shortName" ).equal("CITI"));

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            OrganizationC org = ( OrganizationC ) uow.readObject( OrganizationC.class, expr );
            SpreadGroup grpcurrent = ( SpreadGroup ) uow.readObject( SpreadGroupC.class, expr2 );
            uow.removeAllReadOnlyClasses();
            if( null== grpcurrent )
            {
            	
	            ProviderOrgFunction function= org.getProviderOrgFunction();
	            function = ( ProviderOrgFunction ) uow.registerObject( function );
	            SpreadGroup group = new SpreadGroupC();
	            group.setSpreadType(SpreadGroup.BPS);
	            group.setName("BPS2");
	            group.setLongName("Basis Point Spread");
	            group.setDescription("Basis Point Spread");
	            
	            group = ( SpreadGroup ) uow.registerObject( group );
	            grpcurrent = group;
	            List<SpreadGroup> spreadGroups = new ArrayList<SpreadGroup>();
	            spreadGroups.add(group);
	            function.setSpreadGroups(spreadGroups);
	            uow.commitAndResume();
            }

            grpcurrent = ( SpreadGroup ) uow.registerObject( grpcurrent );

            OrganizationRelationship organizationRelationship = getOrganizationRelationship(org,"FI1","LP_to_FI");
            organizationRelationship = ( OrganizationRelationship ) uow.registerObject( organizationRelationship );
            organizationRelationship.getCustomerPriceProvision().setSpreadGroup(grpcurrent);
            uow.commit();
            
            OrganizationRelationship relationship= (OrganizationRelationship)uow.refreshObject(organizationRelationship);
            assertEquals("Spread Type mismatch",relationship.getCustomerPriceProvision().getSpreadGroup().getSpreadType(), SpreadGroup.BPS);
			assertEquals(relationship.getCustomerPriceProvision().getSpreadGroup().getName(), "BPS2");
        }
        catch ( Exception e )
        {
            fail( "test spread profile" );
            e.printStackTrace();
        }
    }
    
    /**
     * Test inserting trading limits for an org
     */
    public void testSpreadProfileWithTenorParameter()
    {
        // insert org and trading limit
        try
        {
        	long systime = System.currentTimeMillis();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "CITI" );
            Expression expr2 = eb.get( "shortName" ).equal( "BPSY" ).and(eb.get("namespace").get( "shortName" ).equal("CITI"));

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            OrganizationC org = ( OrganizationC ) uow.readObject( OrganizationC.class, expr );
            SpreadGroup grpcurrent = ( SpreadGroup ) uow.readObject( SpreadGroupC.class, expr2 );
            uow.removeAllReadOnlyClasses();
            OrganizationRelationship organizationRelationship = getOrganizationRelationship(org,"FI1","LP_to_FI");
            ProviderOrgFunction function= org.getProviderOrgFunction();
            function = ( ProviderOrgFunction ) uow.registerObject( function );
            if( null== grpcurrent )
            {
            	SpreadGroup group = new SpreadGroupC();
                group.setSpreadType(SpreadGroup.BPS);
                group.setName("BPSY");
                group.setLongName("Basis Point Spread");
                group.setDescription("Basis Point Spread");
                group.setNamespace(function.getNamespace());
                group = ( SpreadGroup ) uow.registerObject( group );
                Set<SpreadTenorParameters> parameters = new TreeSet<SpreadTenorParameters>();
                SpreadTenorParameters parameters2 = new SpreadTenorParametersC(new Tenor("SPOT"),1d,2d);
                parameters2.setLowerLimit(10000000);
                parameters2.setUpperLimit(20000000);
           
                SpreadTenorParameters parameters3 = new SpreadTenorParametersC(new Tenor("1M"),1d,2d);
                parameters2.setLowerLimit(10000000);
                parameters2.setUpperLimit(20000000);
                parameters.add((SpreadTenorParametersC)uow.registerObject(parameters2));
                parameters.add((SpreadTenorParametersC)uow.registerObject(parameters3));
                group.setSpreadTenorParameters(parameters);
                List<SpreadGroup> spreadGroups = new ArrayList<SpreadGroup>();
                spreadGroups.add(group);
                function.setSpreadGroups(spreadGroups);
                uow.commitAndResume();
            }
            uow.commit();

            Organization organization= (Organization)uow.refreshObject(org);
            
            Collection<SpreadGroup> collection = organization.getProviderOrgFunction().getSpreadGroups();
            assertEquals("Failed to persist Spread Groups",collection.size()==1,true);
            for (SpreadGroup spreadGroup : collection) {
				assertEquals("Spread Type mismatch",spreadGroup.getSpreadType(), SpreadGroup.BPS);
				assertEquals(spreadGroup.getName(), "BPSY");
				Collection<SpreadTenorParameters> spreadTenorParameters=spreadGroup.getSpreadTenorParameters();
				for (SpreadTenorParameters spreadTenorParameters2 : spreadTenorParameters) {
					assertEquals(spreadTenorParameters2.getLowerLimit(), 10000000);
					assertEquals(spreadTenorParameters2.getUpperLimit(), 20000000);

				}
			}
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        	fail( "test spread profile" );
        }
    }
    
    private OrganizationRelationship getOrganizationRelationship(Organization org,String taker,String clsf)
    {
    	Collection<OrganizationRelationship> organizationRelationships = org.getOrganizationRelationships();
    	for (OrganizationRelationship organizationRelationship : organizationRelationships) {
    		if( organizationRelationship.getClassification().getShortName().equals(clsf) && organizationRelationship.getRelatedOrganization().getShortName().equals(taker))
    		{
    			return organizationRelationship;
    		}
		}
    	return null;
    }
}
