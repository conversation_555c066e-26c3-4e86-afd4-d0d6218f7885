//package com.integral.persistence.test;
package com.integral.user.test;


import com.integral.test.PTestCaseC;
import com.integral.user.DisplayPreference;
import com.integral.user.User;
import com.integral.user.UserFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Iterator;

public class UserPreferenceCustomFieldPTestC
        extends PTestCaseC
{
    public UserPreferenceCustomFieldPTestC( String aName )
    {
        super( aName );
    }

    public void testUserPreferenceInsert()
    {
        try
        {
            User user = UserFactory.newUser( "test" + System.currentTimeMillis() );
            user.setStatus( 'T' );
            user.putCustomField( "test", 123 );

            DisplayPreference pref = UserFactory.newDisplayPreference();
            pref.setStatus( 'T' );
            pref.setCountry( "us" );

            pref.putCustomField( "testInt", 1 );
            pref.putCustomField( "testString", "abc" );

            user.setDisplayPreference( pref );
            /*
            String[] stringVals =  {"abc", "def"};
            pref.putCustomField("testStringList",stringVals);
            */

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            uow.registerObject( user );

            uow.commit();

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "error inserting test user" );
        }

    }

    public void testUserPreferenceQuery()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Iterator users = getPersistenceSession().readAllObjects( User.class, expr ).iterator();
            while ( users.hasNext() )
            {
                User user = ( User ) users.next();
                System.out.println( " user = " + user.getFullName() );
                Iterator uCFs = user.getCustomFields().keySet().iterator();
                while ( uCFs.hasNext() )
                {
                    String key = ( String ) uCFs.next();
                    System.out.println( "   user cf[" + key + "]:\t" + user.getCustomField( key ) );
                }
                DisplayPreference dp = user.getDisplayPreference();
                System.out.println( "   user display pref = " + dp );
                if ( dp != null )
                {
                    Iterator dpCFs = dp.getCustomFields().keySet().iterator();
                    while ( uCFs.hasNext() )
                    {
                        String key = ( String ) uCFs.next();
                        System.out.println( "     user display pref cf[" + key + "]:\t" + dp.getCustomField( key ) );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "error querying test user" );
        }

    }

    public void testUserPreferenceDelete()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            Iterator users = uow.readAllObjects( User.class, expr ).iterator();
            while ( users.hasNext() )
            {
                User user = ( User ) users.next();
                log( " user = " + user.getFullName() );

                uow.deleteObject( user );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "error deleting test user" );
        }

    }
/*
//Test for CustomField Class
	public void createSuccess()
	{
		try
		{
			//
			// value test
			//

			// null
			CustomField o0 = UserFactory.newUserPreferenceCustomField();
			o0.setKey("key0");
			assertNull(o0.getValue());

			// int
			CustomField o1 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key1");
			int v1 = 1;
			o1.setInteger(v1);
			assertEquals(o1.getInteger(),v1);

			// long
			CustomField o2 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key2");
			long v2 = 2;
			o2.setLong(v2);
			assertEquals(o2.getLong(),v2);

			// double
			CustomField o3 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key3");
			double v3 = 3.3;
			o3.setDouble(v3);
			assertEquals(o3.getDouble(),v3,0);

			// char
			CustomField o4 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key4");
			char v4 = 'a';
			o4.setChar(v4);
			assertEquals(o4.getChar(),v4);

			// boolean
			CustomField o5 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key5");
			boolean v5 = false;
			o5.setBoolean(v5);
			assertEquals(o5.getBoolean(),v5);

			CustomField o6 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key6");
			boolean v6 = true;
			o6.setBoolean(v6);
			assertEquals(o6.getBoolean(),v6);

			// String
			CustomField o7 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key7");
			String v7 = "abc";
			o7.setValue(v7);
			assertEquals(o7.getValue(),v7);

			// IdcDate & IdcDateTime
			CustomField o8 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key7b");
			IdcDate v8 = DateTimeFactory.timeNow();
			o8.setValue(v8);
			assertEquals(o8.getValue(),v8);


			CustomField o8b = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key8");
			IdcDateTime v8b = DateTimeFactory.timeNowTime();
			o8b.setValue(v8b);
			assertEquals(o8b.getValue(),v8b);

			// Timestamp
			CustomField o9 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key9");
			Timestamp v9 = new Timestamp(0);
			o9.setValue(v9);
			assertEquals(o9.getValue(),v9);

			// Entity
			CustomField o11 = UserFactory.newUserPreferenceCustomField();
			o1.setKey("key11");
			Entity v11 = UserFactory.newUser("BB");
			o11.setValue(v11);
			assertEquals(o11.getValue(),v11);

		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("positive creation");
		}

	}
	public void createFailure()
	{
		try
		{
			try
			{
				// illegal object
				CustomField o1 = UserFactory.newUserPreferenceCustomField();
				IllegalArgumentException v1 = new IllegalArgumentException("test");
				o1.setValue(v1);
				fail("set custom field to exception object");
			}
			catch (Exception e)
			{
			}
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("negative creation");
		}

	}
	//Test for if the customFields are atoring into the DBMS
	public void createPersistence()
	{
		try
		{
			CustomField objs[] = new CustomField[100];
			int i=0;

			// UOW
			UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

			// null
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertNull(objs[i].getValue());
			i++;

			// int
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			int v1 = 1;
			objs[i].setInteger(v1);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getInteger(),v1);
			i++;

			// long
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			long v2 = 2;
			objs[i].setLong(v2);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getLong(),v2);
			i++;

			// double
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			double v3 = 3.3;
			objs[i].setDouble(v3);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getDouble(),v3,0);
			i++;

			// char
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			char v4 = 'a';
			objs[i].setChar(v4);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getChar(),v4);
			i++;

			// boolean
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			boolean v5 = false;
			objs[i].setBoolean(v5);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getBoolean(),v5);
			i++;

			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			boolean v6 = true;
			objs[i].setBoolean(v6);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getBoolean(),v6);

			i++;
			// String
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			String v7 = "abc";
			objs[i].setValue(v7);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),v7);
			i++;

			// IdcDate
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			IdcDate v8 = DateTimeFactory.timeNow();
			objs[i].setValue(v8);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),v8);
			i++;

			// IdcDateTime
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			IdcDateTime v8b = DateTimeFactory.timeNowTime();
			objs[i].setValue(v8b);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),v8b);
			i++;

			// Timestamp
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			Timestamp v9 = new Timestamp(0);
			objs[i].setValue(v9);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),v9);
			i++;

			// Entity
			objs[i] = UserFactory.newUserPreferenceCustomField();
			objs[i].setKey("key" + i);
			Entity v11 = (Entity)uow.readObject(UserC.class);
			objs[i].setValue(v11);

			objs[i] = (CustomField)uow.registerObject(objs[i]);
			if (objs[i].getValue() instanceof Entity)
			{
				Entity v11_p = (Entity)objs[i].getValue();
				assertTrue(((v11_p instanceof User)
						 && (v11.getClass() == v11_p.getClass())
						 && (v11.getObjectID() == v11_p.getObjectID())));
			}
			else
			{
				fail("Entity");
			}
			i++;

			uow.commit();
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("positive persistence creation");
		}

	}


	public void testEntity()
	{
		try
		{
			String BACKOFFICEID_KEY = "backOfficeId";
			String RELATEDCRNC_KEY = "relatedCrnc";

			CustomField objs[] = new CustomField[100];
			int i=0;

			try
			{
				// UnsupportedOperationException
				UserC crnc = new UserC();
				crnc.putCustomField("try","abc");
				fail("expect UnsupportedOperationException");
			}
			catch (Exception e)
			{
			}
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("UserPreference test");
		}

	}

	public void testUserPreference()
	{
		try
		{
			String BACKOFFICEID_KEY = "backOfficeId";
			String RELATEDUSR_KEY = "relatedUsr";

			int i=0;
			String dateFormat = "yyyy/MM/dd";

			//
			// Phase 1: add objects and save them to DBMS
			//

			// UOW
			UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();

			User usr = (User)uow1.readObject(UserC.class);
			DisplayPreference userPref = UserFactory.newDisplayPreference();
			userPref = (DisplayPreference)uow1.registerObject(userPref);
			userPref.setDateFormat(dateFormat);
			userPref.putCustomField(BACKOFFICEID_KEY,"abc123");
			userPref.putCustomField(RELATEDUSR_KEY,usr );
			uow1.commit();

			//
			// Phase 2: Select a  DisplayPreference and display custom fields
			//

			// UOW
			UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query2 = new ReadAllQuery();
			query2.setReferenceClass(DisplayPreference.class);
			query2.setSelectionCriteria(new ExpressionBuilder().get("dateFormat").equal(dateFormat));
			query2.addJoinedAttribute(query2.getExpressionBuilder().anyOf("customFields"));
			Vector userPrefs2 = (Vector)uow2.executeQuery(query2);

			if (userPrefs2 != null)
			{
				for (i=0; i<userPrefs2.size(); i++)
				{
					DisplayPreference userPref2 = (DisplayPreference)userPrefs2.elementAt(i);

					// display all custom fields
					log("\t Display all custom fields");
					Iterator it = userPref2.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = userPref2.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}

					// display specific custom fields
					System.out.println("specific custom fields");
					if (userPref2.containsCustomFieldKey(BACKOFFICEID_KEY))
					{
						String backOfficeId = (String)userPref2.getCustomFieldValue(BACKOFFICEID_KEY);
						log("\t\tback office ID = " + backOfficeId);
					}
					if (userPref2.containsCustomFieldKey(RELATEDUSR_KEY))
					{
						Entity relatedUser = (Entity)userPref2.getCustomFieldValue(RELATEDUSR_KEY);
						log("\t\trelated user = #" + relatedUser.getObjectID());
					}

				}
			}
			uow2.release();

			//
			// Phase 3: Select a DisplayPreference and update the custom fields
			//

			UnitOfWork uow3 = getPersistenceSession().acquireUnitOfWork();

			Organization org = (Organization)uow3.readObject(OrganizationC.class);
			ReadAllQuery query3 = new ReadAllQuery();
			query3.setReferenceClass(DisplayPreference.class);
			query3.setSelectionCriteria(new ExpressionBuilder().get("dateFormat").equal(dateFormat));
			query3.addJoinedAttribute(query3.getExpressionBuilder().anyOf("customFields"));
			Vector userPrefs3 = (Vector)uow3.executeQuery(query3);

			if (userPrefs3 != null)
			{
				for (i=0; i<userPrefs3.size(); i++)
				{
					DisplayPreference userPref3 = (DisplayPreference)userPrefs3.elementAt(i);

					userPref3.putCustomField(BACKOFFICEID_KEY,"XYZ987");
					userPref3.putCustomField(RELATEDUSR_KEY, org);
				}
			}

			uow3.commit();

			//
			// Phase 4: fetch DisplayPreference and display custom fields
			//

			UnitOfWork uow4 = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query4 = new ReadAllQuery();
			query4.setReferenceClass(DisplayPreference.class);
			query4.setSelectionCriteria(new ExpressionBuilder().get("dateFormat").equal(dateFormat));
			query4.addJoinedAttribute(query4.getExpressionBuilder().anyOf("customFields"));
			Vector userPrefs4 = (Vector)uow4.executeQuery(query4);

			if (userPrefs4 != null)
			{
				for (i=0; i<userPrefs4.size(); i++)
				{
					DisplayPreference userPref4 = (DisplayPreference)userPrefs4.elementAt(i);

					// display all custom fields
					log("\tall custom fields");
					Iterator it = userPref4.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = userPref4.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}

					// display specific custom fields
					System.out.println("\tspecific custom fields");
					if (userPref4.containsCustomFieldKey(BACKOFFICEID_KEY))
					{
						String backOfficeId = (String)userPref4.getCustomFieldValue(BACKOFFICEID_KEY);
						System.out.println("\t\tback office ID = " + backOfficeId);
					}
					if (userPref4.containsCustomFieldKey(RELATEDUSR_KEY))
					{
						Entity relatedOrg = (Entity)userPref4.getCustomFieldValue(RELATEDUSR_KEY);
						System.out.println("\t\trelated Organization = #" + relatedOrg.getObjectID());
					}

				}
			}

			uow4.release();

			//
			// Phase 5: fetch UsePreference based on custom fields
			//

			UnitOfWork uow5 = getPersistenceSession().acquireUnitOfWork();
			ReadAllQuery query5 = new ReadAllQuery();
			query5.setReferenceClass(DisplayPreference.class);
			query5.setSelectionCriteria(new ExpressionBuilder().get("dateFormat").equal(dateFormat));
			query5.addJoinedAttribute(query5.getExpressionBuilder().anyOf("customFields"));
			Vector userPrefs5 = (Vector)uow5.executeQuery(query5);

			if (userPrefs5 != null)
			{
				for (i=0; i<userPrefs5.size(); i++)
				{
					DisplayPreference userPref5 = (DisplayPreference)userPrefs5.elementAt(i);

					// display all custom fields
					log("\t Display all custom fields");
					Iterator it = userPref5.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = userPref5.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}

					// display specific custom fields
					log("\t Specific custom fields");
					if (userPref5.containsCustomFieldKey(BACKOFFICEID_KEY))
					{
						String backOfficeId = (String)userPref5.getCustomFieldValue(BACKOFFICEID_KEY);
						log("\t\tback office ID = " + backOfficeId);
					}
					if (userPref5.containsCustomFieldKey(RELATEDUSR_KEY))
					{
						Entity relatedOrg = (Entity)userPref5.getCustomFieldValue(RELATEDUSR_KEY);
						log("\t\trelated Organization = #" + relatedOrg.getObjectID());
					}

				}
			}

			uow5.release();

			//Phase 6 Fetch DisplayPreference and delete the custom fields

			UnitOfWork uow6 = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query6 = new ReadAllQuery();
			query6.setReferenceClass(DisplayPreference.class);
			query6.setSelectionCriteria(new ExpressionBuilder().get("dateFormat").equal(dateFormat));
			query6.addJoinedAttribute(query6.getExpressionBuilder().anyOf("customFields"));
			Vector userPrefs6 = (Vector)uow6.executeQuery(query6);

			if (userPrefs6 != null && userPrefs6.size() !=0 )
			{
				for (i=0; i<userPrefs6.size(); i++)
				{
					DisplayPreference userPref6 = (DisplayPreference)userPrefs6.elementAt(i);
					Iterator it = userPref6.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						if (key.equals(BACKOFFICEID_KEY))
						{
							userPref6.removeCustomField(BACKOFFICEID_KEY);
						}
					}
				}
			}
			uow6.commit();

			//	Phase 7: fetch user based on custom fields and check if the entry has been deleted

			UnitOfWork uow7 = getPersistenceSession().acquireUnitOfWork();

			// query User

			ReadAllQuery query7 = new ReadAllQuery();
			query7.setReferenceClass(DisplayPreference.class);
			query7.setSelectionCriteria(new ExpressionBuilder().get("dateFormat").equal(dateFormat));
			query7.addJoinedAttribute(query7.getExpressionBuilder().anyOf("customFields"));
			Vector userPrefs7 = (Vector)uow7.executeQuery(query7);

			if (userPrefs7 != null && userPrefs7.size() != 0)
			{
				for (i=0; i<userPrefs7.size(); i++)
				{
					DisplayPreference userPref7 = (DisplayPreference)userPrefs7.elementAt(i);
					log("\tDisplay all the custom fields");
					Iterator it = userPref7.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = userPref7.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}
					log("\tSpecific custom field test");
					if (userPref7.containsCustomFieldKey(BACKOFFICEID_KEY))
					{
						String backOfficeId = (String)userPref7.getCustomFieldValue(BACKOFFICEID_KEY);
						log("\t\tback office ID = " + backOfficeId);
					}else
					{
						log("\tThe required customfield has been deleted successfully");
					}
					if (userPref7.containsCustomFieldKey(RELATEDUSR_KEY))
					{
						Entity relatedOrg = (Entity)userPref7.getCustomFieldValue(RELATEDUSR_KEY);
						System.out.println("related Organization = #" + relatedOrg.getObjectID());
					}
				}
			}
			uow7.release();

			//Phase 8 select a User and delete all the customFields

			UnitOfWork uow8 = getPersistenceSession().acquireUnitOfWork();
			ReadAllQuery query8 = new ReadAllQuery();
			query8.setReferenceClass(DisplayPreference.class);
			query8.setSelectionCriteria(new ExpressionBuilder().get("dateFormat").equal(dateFormat));
			query8.addJoinedAttribute(query8.getExpressionBuilder().anyOf("customFields"));
			Vector userPrefs8 = (Vector)uow8.executeQuery(query8);
			if (userPrefs8 != null && userPrefs8.size() != 0)
			{
				for (i=0; i<userPrefs8.size(); i++)
				{
					DisplayPreference userPref8 = (DisplayPreference)userPrefs8.elementAt(i);
					userPref8.clearCustomFields();
				}
			}
			uow8.commit();

			//Phase 9 Select a User and display the customfields.
			UnitOfWork uow9 = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query9 = new ReadAllQuery();
			query9.setReferenceClass(DisplayPreference.class);
			query9.setSelectionCriteria(new ExpressionBuilder().get("dateFormat").equal(dateFormat));
			query9.addJoinedAttribute(query9.getExpressionBuilder().anyOf("customFields"));
			Vector userPrefs9 = (Vector)uow9.executeQuery(query9);

			// display custom fields
			if (userPrefs9 != null && userPrefs9.size() != 0)
			{
				for (i=0; i<userPrefs9.size(); i++)
				{
					DisplayPreference userPref9 = (DisplayPreference)userPrefs9.elementAt(i);
					log("\tDisplay all the custom fields");
					Iterator it = userPref9.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = userPref9.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}
				}
			}
			uow9.release();
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("DisplayPreference test");
		}
	}
*/
}
