package com.integral.user.test;

import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.persistence.Namespace;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.PriceProvision;
import com.integral.user.PriceProvisionC;
import com.integral.user.UserFactory;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

public class PriceProvisionPTestC extends PTestCaseC
{
    public static final String name = "PriceProvisionPTestC";

    public PriceProvisionPTestC( String name )
    {
        super( name );

    }

    public void testSpread()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            long time = System.currentTimeMillis();
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            Organization org = UserFactory.newOrganization();
            org = ( Organization ) uow.registerObject( org );
            Namespace ns = ( Namespace ) uow.registerObject( new NamespaceC() );
            ns.setShortName( "TestOrg" + time );
            org.setShortName( "TestOrg" + time );
            PriceProvision test = ( PriceProvision ) uow.registerObject( new PriceProvisionC() );
            ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();
            test.setOrganization( org );
            test.setOwner( org );
            test.setCurrency( CurrencyFactory.getCurrency( "USD" ) );

            double spreadVal = 1.0001;
            double fwdPointSpreadVal = 0.00023;
            test.setCurrencyPair( CurrencyFactory.newCurrencyPair( "EUR", "USD" ) );
            test.setBidOfferMode( 0 );
            test.setRateSpreadOnOff( 1 );
            test.setSpreadValue( spreadVal );
            test.setForwardPointSpread( fwdPointSpreadVal );
            test.setFXRateConvention( ( FXRateConvention ) namedEntityReader.execute( FXRateConventionC.class, "STDQOTCNV" ) );
            test.setTradingLimit( "TEST" );
            test.setSpotSpreadClsf(PriceProvision.SPREAD_CLSF_PIPS);
            uow.commit();
            test.setSpreadValue( 0.0 );
            test = ( PriceProvision ) session.refreshObject( test );
            log( "PriceProvision=" + test + ",test.spreadValue=" + test.getSpreadValue() + ",test.fwdPointSpread=" + test.getForwardPointSpread() );
            assertEquals( "getSpreadValue should be same.", test.getSpreadValue(), spreadVal );
            assertEquals( "getForwardPointsSpread should be same.", test.getForwardPointSpread(), fwdPointSpreadVal );
            assertEquals(test.getSpotSpreadClsf(), PriceProvision.SPREAD_CLSF_PIPS);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
