package com.integral.user.test;

import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.BrokerOrganizationFunctionC;
import com.integral.broker.model.Stream;
import com.integral.broker.model.StreamC;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.user.UserFactory;
import com.integral.user.UserInactivationFunctor;
import com.integral.user.config.UserMBean;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Date;

/**
 * Tests whether classes in the com.integral.user package persist properly
 */
public class UserInactivationFunctorPTestC
        extends PTestCaseC
{
    long milliSecondsPerDay = IdcDate.SECONDS_PER_DAY * 1000;
    long inactivationDays = Integer.parseInt( UserFactory.getUserMBean().getUserInactivationDays() );

    public UserInactivationFunctorPTestC( String aName )
    {
        super( aName );
    }

    //todo after user session is moved to mongodb, this test case need to be reworked.
    public void _testUserInactivationForInactivity()
    {
        User fiUser = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm2" );
        Date lastLogin = null;
        //boolean spacesPersistenceEnabled = UserFactory.getUserMBean().isSpacesPersistenceEnabled();
        try
        {
            //WatchPropertyC.update( UserMBean.USER_SPACES_PERSISTENCE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            lastLogin = fiUser.getLastLoginDate();
            Date newLastLoginTime = new Date( System.currentTimeMillis() - ( inactivationDays + 1 ) * milliSecondsPerDay );
            setUserAttributes( fiUser, true, false, newLastLoginTime );

            // invoke the user inactivation functor
            WorkflowMessage wsm = MessageFactory.newWorkflowMessage();
            wsm.setObject( IdcUtilC.getSessionContextUser() );
            new UserInactivationFunctor().execute( wsm );

            // verification
            fiUser = ( User ) getPersistenceSession().refreshObject( fiUser );
            assertEquals( fiUser.isActive(), false );
        }
        catch ( Exception e )
        {
            fail( "testUserInactivationForInactivity", e );
        }
        finally
        {
            setUserAttributes( fiUser, true, true, lastLogin );
            //WatchPropertyC.update( UserMBean.USER_SPACES_PERSISTENCE_ENABLED, String.valueOf( spacesPersistenceEnabled ), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUserInactivationForNoInactivity()
    {
        User fiUser = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm2" );
        Date lastLogin = null;
        try
        {
            lastLogin = fiUser.getLastLoginDate();
            Date newLastLoginTime = new Date( System.currentTimeMillis() - ( inactivationDays - 1 ) * milliSecondsPerDay );
            setUserAttributes( fiUser, true, false, newLastLoginTime );

            // invoke the user inactivation functor
            WorkflowMessage wsm = MessageFactory.newWorkflowMessage();
            wsm.setObject( IdcUtilC.getSessionContextUser() );
            new UserInactivationFunctor().execute( wsm );

            // verification
            fiUser = ( User ) getPersistenceSession().refreshObject( fiUser );
            assertEquals( fiUser.isActive(), true );
        }
        catch ( Exception e )
        {
            fail( "testUserInactivationForNoInactivity", e );
        }
        finally
        {
            setUserAttributes( fiUser, true, true, lastLogin );
        }
    }

    public void testUserInactivationExempt()
    {
        User fiUser = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm2" );
        Date lastLogin = null;
        try
        {
            lastLogin = fiUser.getLastLoginDate();
            Date newLastLoginTime = new Date( System.currentTimeMillis() - ( inactivationDays + 1 ) * milliSecondsPerDay );
            setUserAttributes( fiUser, true, true, newLastLoginTime );

            // invoke the user inactivation functor
            WorkflowMessage wsm = MessageFactory.newWorkflowMessage();
            wsm.setObject( IdcUtilC.getSessionContextUser() );
            new UserInactivationFunctor().execute( wsm );

            // verification
            fiUser = ( User ) getPersistenceSession().refreshObject( fiUser );
            assertEquals( fiUser.isActive(), true );
        }
        catch ( Exception e )
        {
            fail( "testUserInactivationExempt", e );
        }
        finally
        {
            setUserAttributes( fiUser, true, true, lastLogin );
        }
    }

    public void testUserInactivationExemptForDefaultUser()
    {
        User fiUser = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm2" );
        Date lastLogin = null;
        User defaultUser = fiUser.getOrganization().getDefaultDealingUser();
        try
        {
            lastLogin = fiUser.getLastLoginDate();
            Date newLastLoginTime = new Date( System.currentTimeMillis() - ( inactivationDays + 1 ) * milliSecondsPerDay );
            setUserAttributes( fiUser, true, false, newLastLoginTime );

            // set this user as org's default dealing user.
            Organization org = fiUser.getOrganization();
            org.setDefaultDealingUser( fiUser );
            ( ( OrganizationC ) org ).resetTransients();
            assertEquals( org.getDefaultDealingUser(), fiUser );

            // invoke the user inactivation functor
            WorkflowMessage wsm = MessageFactory.newWorkflowMessage();
            wsm.setObject( IdcUtilC.getSessionContextUser() );
            new UserInactivationFunctor().execute( wsm );

            // verification
            fiUser = ( User ) getPersistenceSession().refreshObject( fiUser );
            assertEquals( fiUser.isActive(), true );
        }
        catch ( Exception e )
        {
            fail( "testUserInactivationExemptForDefaultUser", e );
        }
        finally
        {
            setUserAttributes( fiUser, true, true, lastLogin );
            fiUser.getOrganization().setDefaultDealingUser( defaultUser );
            getPersistenceSession().refreshObject( fiUser.getOrganization() );
        }
    }

    public void testUserInactivationExemptForStreamUser()
    {
        User fiUser = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm2" );
        Date lastLogin = null;
        try
        {
            lastLogin = fiUser.getLastLoginDate();
            Date newLastLoginTime = new Date( System.currentTimeMillis() - ( inactivationDays + 1 ) * milliSecondsPerDay );
            setUserAttributes( fiUser, true, false, newLastLoginTime );

            // set this user as org's default dealing user.
            Organization org = fiUser.getOrganization();
            BrokerOrganizationFunction bof = org.getBrokerOrganizationFunction();
            if ( bof == null )
            {
                bof = new BrokerOrganizationFunctionC();
                bof.setNamespace( org.getNamespace() );
                org.getOrganizationFunctions().add( bof );
            }

            Collection<Stream> streams = bof.getStreams();
            if ( streams == null || streams.isEmpty() )
            {
                Stream stream = new StreamC();
                stream.setUser( fiUser );
                bof.getStreams().add( stream );
            }
            else
            {
                Stream aStream = streams.iterator().next();
                aStream.setUser( fiUser );
            }

            // invoke the user inactivation functor
            WorkflowMessage wsm = MessageFactory.newWorkflowMessage();
            wsm.setObject( IdcUtilC.getSessionContextUser() );
            new UserInactivationFunctor().execute( wsm );

            // verification
            fiUser = ( User ) getPersistenceSession().refreshObject( fiUser );
            assertEquals( fiUser.isActive(), true );
        }
        catch ( Exception e )
        {
            fail( "testUserInactivationExemptForStreamUser", e );
        }
        finally
        {
            setUserAttributes( fiUser, true, true, lastLogin );
            getPersistenceSession().refreshObject( fiUser.getOrganization() );
        }
    }

    private void setUserAttributes( User fiUser, boolean status, boolean exempt, Date lastLogin )
    {
        try
        {
            UnitOfWork aUow = this.getPersistenceSession().acquireUnitOfWork();
            aUow.removeReadOnlyClass( UserC.class );
            fiUser = ( User ) aUow.registerObject( fiUser );
            fiUser.setActive( status );
            fiUser.setAutoInactivationExempt( exempt );
            fiUser.setLastLoginDate( lastLogin );
            log( "last login time reset to " + lastLogin );
            User usr1 = fiUser.getStatusLastModifiedBy();
            if ( usr1 != null )
            {
                usr1 = ( User ) aUow.registerObject( usr1 );
                fiUser.setStatusLastModifiedBy( usr1 );
            }
            User usr2 = fiUser.getDigestLastModifiedBy();
            if ( usr2 != null )
            {
                usr2 = ( User ) aUow.registerObject( usr2 );
                fiUser.setDigestLastModifiedBy( usr2 );
            }
            User usr3 = fiUser.getLastModifiedBy();
            if ( usr3 != null )
            {
                usr3 = ( User ) aUow.registerObject( usr3 );
                fiUser.setLastModifiedBy( usr3 );
            }
            aUow.commit();
        }
        catch ( Exception e )
        {
            fail( "setUserAttributes", e );
        }
    }

}

