package com.integral.user.test;

import com.integral.persistence.EntityFactory;
import com.integral.persistence.Namespace;
import com.integral.test.PTestCaseC;
import com.integral.user.LegalAgreement;
import com.integral.user.Organization;
import com.integral.user.UserFactory;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.util.Collection;

public class LegalAgreementPTestC
        extends PTestCaseC
{
    static String name = "LegalAgreement Test";
    private String LA_SHORT_NAME = "BBB";

    public LegalAgreementPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new LegalAgreementPTestC( "testLegalAgreement" ) );
        suite.addTest( new LegalAgreementPTestC( "testOrgLegalAgreement" ) );
        return suite;
    }

    public void testOrgLegalAgreement()
    {
        Organization fiOrg = null;
        LegalAgreement originalDefaultLA = null;
        LegalAgreement originalDefaultCptyLA = null;

        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            fiOrg = UserFactory.newOrganization();
            Organization registeredOrg = ( Organization ) uow.registerObject( fiOrg );
            registeredOrg.setShortName( "NewOrg" + System.currentTimeMillis() );
            registeredOrg.setLongName( "LongName" );
            Namespace ns = ( Namespace ) uow.registerObject( EntityFactory.newNamespace());
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            registeredOrg.setNamespace( ns );
            uow.commit();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testOrgLegalAgreement() Exception 1 - " + exc );
        }

        // Insert few legal agreements to DB
        long t = System.nanoTime();
        addLegalAgreementToDB( "AAAA" + t, fiOrg, false );
        addLegalAgreementToDB( "BBBB" + t, fiOrg, false );
        addLegalAgreementToDB( "CCCC" + t, fiOrg, false );
        addLegalAgreementToDB( "DDDD" + t, fiOrg, true );
        addLegalAgreementToDB( "EEEE" + t, fiOrg, true );

        // Reloading the legal agreement from DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( fiOrg );

            originalDefaultLA = fiOrg.getDefaultLicenseAgreement();
            originalDefaultCptyLA = fiOrg.getDefaultCptyLicenseAgreement();

            assertEquals( fiOrg.getLicenseAgreements().size(), 3 );
            assertEquals( fiOrg.getCptyLicenseAgreements().size(), 2 );
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testOrgLegalAgreement() Exception 2 - " + exc );
        }

        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            fiOrg = ( Organization ) uow.registerObject( fiOrg );

            Collection<LegalAgreement> fiLicenseAgreement = fiOrg.getLicenseAgreements();
            Collection<LegalAgreement> fiCptyLicenseAgreement = fiOrg.getCptyLicenseAgreements();
            LegalAgreement defaultLA = null;
            LegalAgreement defaultCptyLA = null;

            // Set the default license agreement.
            for ( LegalAgreement la : fiLicenseAgreement )
            {
                defaultLA = la;
                log.info( "Default LA for CITI fi " + defaultLA.getShortName() );
            }

            for ( LegalAgreement la : fiCptyLicenseAgreement )
            {
                defaultCptyLA = la;
                log.info( "Default counterparty LA for CITI fi " + defaultCptyLA.getShortName() );
            }

            fiOrg.setDefaultLicenseAgreement( defaultLA );
            fiOrg.setDefaultCptyLicenseAgreement( defaultCptyLA );
            fiOrg.setLicenseAgreementStatus( "Optional" );
            fiOrg.setCptyLicenseAgreementStatus( "Mandatory" );

            // Add the new license agreement
            String shortName = "YYY" + System.nanoTime();
            LegalAgreement newLA = UserFactory.newLegalAgreement();
            newLA = ( LegalAgreement ) uow.registerObject( newLA );
            newLA.setShortName( shortName );
            newLA.setLongName( "YYY" );
            newLA.setDescription( "Legal Agreement" );
            newLA.setContent( "temp" );
            newLA.setLicenseVersion( "12345" );
            newLA.setStatus( 'T' );

            LegalAgreement cptyNewLA = UserFactory.newLegalAgreement();
            cptyNewLA = ( LegalAgreement ) uow.registerObject( cptyNewLA );
            cptyNewLA.setShortName( "ZZZ" + System.nanoTime() );
            cptyNewLA.setLongName( "ZZZ" );
            cptyNewLA.setDescription( "Legal Agreement" );
            cptyNewLA.setContent( "temp" );
            cptyNewLA.setLicenseVersion( "12345" );
            cptyNewLA.setStatus( 'T' );

            fiOrg.addLicenseAgreement( newLA );
            fiOrg.addCptyLicenseAgreement( cptyNewLA );
            uow.commit();
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testOrgLegalAgreement() Exception 3 - " + exc );
        }

        // Reloading the legal agreement from DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( fiOrg );

            log.info( "fiOrg.getDefaultLicenseAgreement() " + fiOrg.getDefaultLicenseAgreement().getShortName() );
            log.info( "fiOrg.getDefaultCptyLicenseAgreement() " + fiOrg.getDefaultCptyLicenseAgreement().getShortName() );

            assertNotNull( fiOrg.getDefaultLicenseAgreement() );
            assertNotNull( fiOrg.getDefaultCptyLicenseAgreement() );
            assertEquals( fiOrg.getLicenseAgreements().size(), 4 );
            assertEquals( fiOrg.getCptyLicenseAgreements().size(), 3 );
            assertEquals( fiOrg.getLicenseAgreementStatus(), "Optional" );
            assertEquals( fiOrg.getCptyLicenseAgreementStatus(), "Mandatory" );
            assertEquals( fiOrg.getDefaultLicenseAgreement().getReferenceId(), "BBB" );
            assertEquals( fiOrg.getDefaultCptyLicenseAgreement().getReferenceId(), "BBB" );

            Collection<LegalAgreement> fiLicenseAgreement = fiOrg.getLicenseAgreements();
            Collection<LegalAgreement> fiCptyLicenseAgreement = fiOrg.getCptyLicenseAgreements();

            // Set the default license agreement.
            for ( LegalAgreement la : fiLicenseAgreement )
            {
                log.info( "Default LA for CITI fi " + la.getShortName() );
            }

            for ( LegalAgreement la : fiCptyLicenseAgreement )
            {
                log.info( "Default counterparty LA for CITI fi " + la.getShortName() );
            }

            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testOrgLegalAgreement() Exception 4 - " + exc );
        }

        // Remove the legal agreement from DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            fiOrg = ( Organization ) uow.registerObject( fiOrg );


            Collection<LegalAgreement> fiLicenseAgreement = fiOrg.getLicenseAgreements();
            Collection<LegalAgreement> fiCptyLicenseAgreement = fiOrg.getCptyLicenseAgreements();

            LegalAgreement fiLA = null;
            LegalAgreement cptyLA = null;

            // Set the default license agreement.
            for ( LegalAgreement la : fiLicenseAgreement )
            {
                fiLA = la;
                log.info( "Default LA for CITI fi " + la.getShortName() );
            }

            for ( LegalAgreement la : fiCptyLicenseAgreement )
            {
                cptyLA = la;
                log.info( "Default counterparty LA for CITI fi " + la.getShortName() );
            }

            fiOrg.removeLicenseAgreement( fiLA );
            fiOrg.removeCptyLicenseAgreement( cptyLA );

            log.info( "Removed license agreement " + fiLA.getShortName() );
            log.info( "Removed Cpty license agreement " + cptyLA.getShortName() );

            fiOrg.setDefaultLicenseAgreement( originalDefaultLA );
            fiOrg.setDefaultCptyLicenseAgreement( originalDefaultCptyLA );

            uow.commit();

            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testOrgLegalAgreement() Exception 5 - " + exc );
        }

        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( fiOrg );
            assertNull( fiOrg.getDefaultLicenseAgreement() );
            assertNull( fiOrg.getDefaultCptyLicenseAgreement() );
            assertEquals( fiOrg.getLicenseAgreements().size(), 3 );
            assertEquals( fiOrg.getCptyLicenseAgreements().size(), 2 );
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testOrgLegalAgreement() Exception 6 - " + exc );
        }

        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            fiOrg = ( Organization ) uow.registerObject( fiOrg );
            fiOrg.clearLicenseAgreements();
            fiOrg.clearCptyLicenseAgreements();

            uow.commit();
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testOrgLegalAgreement() Exception 7 - " + exc );
        }

        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( fiOrg );
            assertNull( fiOrg.getDefaultLicenseAgreement() );
            assertNull( fiOrg.getDefaultCptyLicenseAgreement() );
            assertEquals( fiOrg.getLicenseAgreements().size(), 0 );
            assertEquals( fiOrg.getCptyLicenseAgreements().size(), 0 );
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testOrgLegalAgreement() Exception 8 - " + exc );
        }
    }

    public void testLegalAgreement()
    {
        String shortName = LA_SHORT_NAME + System.nanoTime();
        LegalAgreement legalAgreement = UserFactory.newLegalAgreement();

        // Inserting the new legal Agreement to DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            legalAgreement = ( LegalAgreement ) this.uow.registerNewObject( legalAgreement );
            legalAgreement.setShortName( shortName );
            legalAgreement.setLongName( shortName );
            legalAgreement.setDescription( "Legal Agreement" );
            legalAgreement.setContent( "At the Sun Tech Days conference in Atlanta, Georgia, " +
                    "Robert Eckstein interviews NetBeans evangelist Gregg Sporar to hear about " +
                    "the latest features in the new NetBeans IDE 6.0 release. Tired of putting together and managing all " +
                    "the pieces of a Ruby-on-Rails deployment? Simplify your life by developing and deploying with " +
                    "JRuby on GlassFish" );
            legalAgreement.setLicenseVersion( "12345" );
            legalAgreement.setStatus( 'T' );
            uow.commit();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testLegalAgreement() Exception 1 - " + exc );
        }

        // Reloading the legal agreement from DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( legalAgreement );
            assertEquals( legalAgreement.getShortName(), shortName );
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testLegalAgreement() Exception 2 - " + exc );
        }

        // Deleteing the entry.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.deleteObject( legalAgreement );
            uow.commit();
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testLegalAgreement() Exception 3 - " + exc );
        }

        // Retrieve the object again to make sure object is deleted.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder builder = new ExpressionBuilder();
            Expression expr = builder.get( "shortName" ).equal( LA_SHORT_NAME );
            LegalAgreement legalA = ( LegalAgreement ) uow.readObject( com.integral.user.LegalAgreement.class, expr );
            assertNull( legalA );
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.testLegalAgreement() Exception 4 - " + exc );
        }
    }

    private void addLegalAgreementToDB( String shortName, Organization org, boolean isCoutomerLA )
    {
        LegalAgreement legalAgreement = UserFactory.newLegalAgreement();

        // Inserting the new legal Agreement to DB.
        try
        {

            this.uow = this.getPersistenceSession().acquireUnitOfWork();
            this.uow.removeAllReadOnlyClasses();
            org = ( Organization ) this.uow.registerObject( org );
            legalAgreement = ( LegalAgreement ) this.uow.registerNewObject( legalAgreement );
            legalAgreement.setShortName( shortName );
            legalAgreement.setLongName( shortName );
            legalAgreement.setDescription( "Legal Agreement" );
            legalAgreement.setContent( "At the Sun Tech Days conference in Atlanta, Georgia, " +
                    "Robert Eckstein interviews NetBeans evangelist Gregg Sporar to hear about " +
                    "the latest features in the new NetBeans IDE 6.0 release. Tired of putting together and managing all " +
                    "the pieces of a Ruby-on-Rails deployment? Simplify your life by developing and deploying with " +
                    "JRuby on GlassFish" );
            legalAgreement.setLicenseVersion( "12345" );
            legalAgreement.setStatus( 'T' );
            legalAgreement.setReferenceId( "BBB" );
            if ( isCoutomerLA )
            {
                legalAgreement.setCptyOrganization( org );
            }
            else
            {
                legalAgreement.setOrganization( org );
            }
            this.uow.commit();
        }
        catch ( Exception exc )
        {
            log.error( "LegalAgreementPTestC.addLegalAgreementToDB() Exception  - " + exc );
        }
    }
}

