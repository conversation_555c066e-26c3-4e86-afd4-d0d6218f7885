package com.integral.user.test;

import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.*;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Vector;


public class UserLookupPTestC
        extends PTestCaseC
{
    static String name = "User Lookup Test";

    public UserLookupPTestC( String name )
    {
        super( name );
    }

    protected void setUpX() throws Exception
    {
        super.setUp();
        try
        {
            Session session = PersistenceFactory.newSession();
            session.readAllObjects( User.class );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "setUp" );
        }
    }


    public void testLookupUser()
    {
        try
        {
            Session session1 = getPersistenceSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "fi1mm1" );
            User user = ( User ) session1.readObject( User.class, expr );

            long elapsed = 0;
            int num = 3;
            for ( int i = 0; i < num; i++ )
            {
                Session session = PersistenceFactory.newSession();

                Vector pk = new Vector( 1 );
                pk.addElement( new BigDecimal( user.getObjectID() ) );

                UnitOfWork uow = session.acquireUnitOfWork();
                long start = System.currentTimeMillis();
                User myUser = ( User ) ( ( org.eclipse.persistence.sessions.Session ) session ).getIdentityMapAccessor().getFromIdentityMap( pk, UserC.class );
                if ( myUser == null )
                {
                    System.out.println( "# " + i + '\t'
                            + "user id = " + null );
                    continue;
                }

                myUser = ( User ) uow.registerObject( myUser );
                long stop = System.currentTimeMillis();
                elapsed += ( stop - start );
                long version = getObjectVersion( uow, myUser );

                System.out.println( "# " + i + '\t'
                        + "user id = " + myUser.getObjectID()
                        + ", version = " + version );

                myUser.setDescription( "version #" + version );
                uow.commit();
            }
            System.out.println( "time for " + num + " lookups: " + elapsed + "msec" );

            log( "lookup user" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookup user" );
        }
    }

    public static long getObjectVersion( Session aSession, Entity anEntity )
    {
        return anEntity.getVersion();
    }

    /**
     * Tests nested lookup
     */
    public void testLookupUserNestedSelect()
    {
        try
        {
            /*
                *	FIND ALL users
                *	   WHERE status = 'A'
                *       AND longName = (FIND ALL users.longName
                *                          WHERE id in (501, 507, 508)
                *						)
                */
            Session session = getPersistenceSession();

            //
            //
            //
            System.out.println( "all users with status \'A\'" );

            ExpressionBuilder eb1 = new ExpressionBuilder();
            Expression expr1 = eb1.get( "status" ).equal( 'A' );

            Vector users = session.readAllObjects( User.class, expr1 );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                System.out.println( "\t#" + i + ":\t" + user.getObjectID()
                        + '\t' + user.getShortName()
                        + '\t' + user.getLongName() );
            }

            //
            //
            //
            System.out.println( "all users with status \'A\' and longName in (LONG1, LONG2)" );

            String[] long_names = {"LONG1", "LONG2"};

            ExpressionBuilder eb2 = new ExpressionBuilder();
            Expression expr2a = eb2.get( "status" ).equal( 'A' );
            Expression expr2b = eb2.get( "longName" ).in( long_names );

            Expression expr2 = expr2a.and( expr2b );

            users = session.readAllObjects( User.class, expr2 );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                System.out.println( "\t#" + i + ":\t" + user.getObjectID()
                        + '\t' + user.getShortName()
                        + '\t' + user.getLongName() );
            }

            //
            //
            //
            System.out.println( "all users with status \'A\' and longName in (select longName where ID in (501,507,508))" );

            long object_ids[] = {501, 507, 508};

            ExpressionBuilder eb3 = new ExpressionBuilder();
            Expression expr3a = eb3.get( "status" ).equal( 'A' );

            Expression expr3b = eb3.get( "objectID" ).in( object_ids );

            Expression expr3 = expr3a.and( expr3b );

            users = session.readAllObjects( User.class, expr3 );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                System.out.println( "\t#" + i + ":\t" + user.getObjectID()
                        + '\t' + user.getShortName()
                        + '\t' + user.getLongName() );
            }

            log( "lookup nested user" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookup nested user" );
        }
    }

    public void testLookupUserAndRefresh( long oid )
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "member12" );
            UserC user = ( UserC ) uow.readObject( User.class, expr );

            System.out.println( " retrieving user groups...." );
            Vector group = ( Vector ) ( user.getUserGroups() );
            System.out.println( "   ... completed. groups=" + group );

            System.out.println( " user groups = " + user.getUserGroups() );
            System.out.println( " user groups hashCode = " + user.getUserGroups().hashCode() );

            System.out.println( " refreshing user ...." );
            uow.refreshObject( user );
            System.out.println( "   ... completed" );

            System.out.println( " retrieving user groups...." );
            user.getUserGroups();
            System.out.println( "   ... completed" );

            System.out.println( " user groups = " + user.getUserGroups() );
            System.out.println( " user groups hashCode = " + user.getUserGroups().hashCode() );

            log( "lookup user" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookup user" );
        }
    }


    public void testListAdminUsers()
    {
        try
        {
            Session session = getPersistenceSession();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).like( "jaim%" );
            Vector users = session.readAllObjects( User.class, expr );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                System.out.println( "\t#" + i + ":\t" + user.getFullName()
                        + ( user.isAdmin() ? " ADMIN" : " USER" ) );
            }

            log( "lookup user" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookup user" );
        }
    }

    public void testNamespaceGroupUserLookup()
    {
        try
        {
            User user = UserFactory.getUser( "fi1mm1@FI1" );
            log( "user=" + user );
            assertEquals( "user should not be null.", user != null, true );

            User user1 = UserFactory.getUser( "fi2mm1@FI2" );
            log( "user1=" + user1 );
            assertEquals( "user1 should not be null.", user1 != null, true );

            User user2 = UserFactory.getUser( "fi1mm1@FI2" );
            log( "user2=" + user2 );
            assertEquals( "user2 should be null.", user2 == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testUserFactoryLookup()
    {
        try
        {
            Organization org = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Collection<User> users = org.getUsers();
            for ( User user : users )
            {
                if ( user.isActive () )
                {
                    User userFromCache = UserFactory.getUser ( user.getFullName () );
                    assertNotNull ( "userFullName=" + user.getFullName (), userFromCache );
                    assertTrue ( "user=" + user + ",userFromUserFactory=" + UserFactory.getUser ( user.getFullName () ), UserFactory.getUser ( user.getFullName () ).isSameAs ( user ) );
                    assertTrue ( "user=" + user + ",userFromUserFactory=" + UserFactory.getUser ( user.getFullyQualifiedName () ), UserFactory.getUser ( user.getFullyQualifiedName () ).isSameAs ( user ) );
                }
            }

            // test a non-existing user and namespace.
            User usr = UserFactory.getUser( "abc@abc" );
            assertNull( usr );

            // check for a non-existing user in an existing org.
            User usr1 = UserFactory.getUser( "abc@FI1" );
            assertNull( usr1 );


            // check for a user without namespace specified. It will use MAIN namespace
            User usr2 = UserFactory.getUser( "Integral1" );
            assertNotNull( usr2 );

            // check for a user without namespace specified.
            User usr3 = UserFactory.getUser( "xxx" );
            assertNull( usr3 );
            
            User usr4 = UserFactory.getUser( "fi1mm1@MAIN.FI1" );
            assertNotNull( usr4 );

            User usr5 = UserFactory.getUser( "xxx@MAIN.FI1" );
            assertNull( usr5 );
        }
        catch ( Exception e )
        {
            fail( "testUserFactoryLookup", e );
        }
    }

    public void testLookupUsersWithPermission()
    {
        try
        {
            String testPermPattern = "GM_";
            Session session = getPersistenceSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.anyOf( "userGroups" ).anyOf( "roles" ).anyOf( "userPermissions" ).get(NamedEntity.ShortName ).like( testPermPattern + "%" );
            Vector<User> users = session.readAllObjects( User.class, expr );
            log( "users=" + users );
            assertNotNull( users );
            assertTrue( users.size() > 0 );

            for ( User user: users )
            {
                boolean permissionFound = false;
                Collection permissions = user.getUserPermissions();
                for ( Object obj: permissions )
                {
                    UserPermission userPermission = (UserPermission) obj;
                    if ( userPermission.getShortName().startsWith( testPermPattern ) )
                    {
                        permissionFound = true;
                    }
                }
                assertTrue( "permission found for user=" + user, permissionFound );
            }
        }
        catch ( Exception e )
        {
            fail ( "testLookupUsersWithPermission" );
        }
    }

}
