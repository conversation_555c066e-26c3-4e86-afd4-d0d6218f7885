//package com.integral.persistence.test;
package com.integral.user.test;

import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemId;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Iterator;
import java.util.Vector;

public class OrganizationListPTestC
        extends PTestCaseC
{
    public OrganizationListPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new OrganizationListPTestC( "testExternalSystem" ) );
        return suite;
    }

    static void ASSERT_OBJECTS_ARE_IN_SAME_TRANSACTION( Entity object1, Entity object2 )
            throws RuntimeException
    {
        return;

        /*
          System.out.println("\n\nASSERT\n\n");
          Session aSession = null;
          try
          {
              aSession = PersistenceFactory.newSession();
          }
          catch (Exception e)
          {
              throw new RuntimeException("session is null");
          }

          Object serverObject1 = aSession.readObject(object1);
          System.out.println("server object 1:" + serverObject1);
          if (object1.equals(serverObject1))
          {
              System.out.println("object 1 is original server session object");
              // throw new RuntimeException("object 1 is original server session object");
          }
          else
          {
              System.out.println("object 1 is NOT original server session object");
          }

          Object serverObject2 = aSession.readObject(object2);
          System.out.println("server object 2:" + serverObject2);
          if (object2.equals(serverObject2))
          {
              System.out.println("object 2 is original server session object");
              // throw new RuntimeException("object 2 is original server session object");
          }
          else
          {
              System.out.println("object 2 is NOT original server session object");
          }

          System.out.println("object 1&2 are in same UOW");
          */
    }

    public void addUser()
    {
        try
        {
            // UOW
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            User pUser = ( User ) getPersistenceSession().readObject( User.class );
            log( "persistent user: " + pUser.getShortName() );

            Vector users = new Vector();

            User ben = UserFactory.newUser( "ben" );
            User jim = UserFactory.newUser( "jim" );
            User joe = UserFactory.newUser( "joe" );

            users.add( joe );
            users.add( jim );
            users.add( ben );
            users.add( pUser );

            // test jim
            if ( !users.contains( jim ) )
            {
                fail( "jim not found" );
            }

            // test jim's clone
            User jimsClone = ( User ) uow.registerObject( jim );
            if ( !users.contains( jimsClone ) )
            {
                fail( "jim's clone found" );
            }

            // test pUser
            if ( !users.contains( pUser ) )
            {
                fail( "pUser not found" );
            }

            // test jim's clone
            User pUsersClone = ( User ) uow.registerObject( pUser );
            if ( !users.contains( pUsersClone ) )
            {
                fail( "pUser's clone found" );
            }


            try
            {
                ASSERT_OBJECTS_ARE_IN_SAME_TRANSACTION( jim, jim );
                ASSERT_OBJECTS_ARE_IN_SAME_TRANSACTION( jimsClone, jimsClone );
                ASSERT_OBJECTS_ARE_IN_SAME_TRANSACTION( jim, jimsClone );
                ASSERT_OBJECTS_ARE_IN_SAME_TRANSACTION( pUser, pUser );
                ASSERT_OBJECTS_ARE_IN_SAME_TRANSACTION( pUsersClone, pUsersClone );
                ASSERT_OBJECTS_ARE_IN_SAME_TRANSACTION( pUser, pUsersClone );
            }
            catch ( Exception e )
            {

            }

            uow.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Exception in the creation of Persistence" );
        }

    }

    public void testOrganization()
    {
        Iterator orgs = getPersistenceSession().readAllObjects( Organization.class ).iterator();
        while ( orgs.hasNext() )
        {
            Organization org = ( Organization ) orgs.next();
            log( "org = " + org );
            log( "  with ns = " + org.getNamespace() );
        }
    }


    /**
     * Tests saving a organization external system id
     */
    public void testExternalSystem()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();

            ExternalSystem extSys = ( ExternalSystem ) uow.readObject( ExternalSystem.class );

            Organization org = new OrganizationC();
            org.setShortName( "TEST" + System.currentTimeMillis() );
            org.setStatus( 'T' );

            ExternalSystemId extSysId = UserFactory.newOrganizationExternalSystemId();
            extSysId.setName( "Test" + System.currentTimeMillis() );
            extSysId.setStatus( 'T' );
            extSysId.setExternalSystem( extSys );
            org.addExternalSystemId( extSysId );

            uow.registerObject( org );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "test insert " );
        }

    }
}
