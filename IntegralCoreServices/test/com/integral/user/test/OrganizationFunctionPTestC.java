package com.integral.user.test;


import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.BrokerOrganizationFunctionC;
import com.integral.message.user.OrganizationReference;
import com.integral.message.user.OrganizationReferenceC;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.provider.ProviderOrgFunction;
import com.integral.provider.ProviderOrgFunctionC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.OrganizationFunction;
import com.integral.user.UserFactory;
import com.integral.xml.binding.JavaXMLBinderFactory;
import com.integral.xml.mapping.XMLMappingLoaderC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Vector;


public class OrganizationFunctionPTestC
        extends PTestCaseC
{
    static String name = "Organization Function Lookup Test";

    public OrganizationFunctionPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new OrganizationFunctionPTestC( "testOutputOfIsRFSEnabledOnOrganizationReferenceXml" ) );
        return suite;
    }

    /**
     * Test insert of org functions
     */
    public void testInsertOrgFunction()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            OrganizationFunction orgFunc = UserFactory.newOrganizationFunction( "TEST-" + System.currentTimeMillis() );
            orgFunc.setStatus( 'T' );

            uow.registerObject( orgFunc );

            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "test insert org function" );
            e.printStackTrace();
        }
    }

    /**
     * Test query of org functions
     */
    public void testQueryOrgFunction()
    {
        // query
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = ( Vector ) uow.readAllObjects( OrganizationFunction.class, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tretrieved " + entity );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            fail( "test query org function" );
            e.printStackTrace();
        }
    }

    /**
     * Test delete of org functions
     */
    public void testDeleteOrgFunction()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = ( Vector ) uow.readAllObjects( OrganizationFunction.class, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tdeleting " + entity );
                uow.deleteObject( entity );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "test delete org function" );
            e.printStackTrace();
        }
    }


    public void testOrganizationFunctionsOfAnOrg()
    {
        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) PersistenceFactory.newSession();
            Vector orgs = session1.readAllObjects( Organization.class );
            for ( int i = 0; i < orgs.size(); i++ )
            {
                Organization org = ( Organization ) orgs.elementAt( i );
                log( "\torg: " + org );
                Iterator orgFunctions = org.getOrganizationFunctions().iterator();
                while ( orgFunctions.hasNext() )
                {
                    OrganizationFunction orgfun = ( OrganizationFunction ) orgFunctions.next();
                    log( "\t\torg function " + orgfun );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "loadOrgFunction" );
        }
    }

    public void testOutputOfIsRFSEnabledOnOrganizationReferenceXml()
    {

        try
        {
            // Make the mappings ready - setup requires integral5/XMLMapping folder and integral5/properties folder to be in the classpath
            XMLMappingLoaderC mappingLoader = new XMLMappingLoaderC();
            mappingLoader.preload( this.getClass().getClassLoader() );

            //Current organization refernce = RFS is false by default
            OrganizationReference orgRef = new OrganizationReferenceC();
            orgRef.setShortName( "MAIN" );

            StringWriter sw = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw, orgRef, "IFS" );
            System.out.println( "XML OUTPUT BEFORE ISS-\n " + sw.getBuffer().toString() );

            Organization orga = orgRef.getOrganization();

            //Let us create an organization function - with provider as name and RFS enabled = true
            org.eclipse.persistence.sessions.Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            ProviderOrgFunctionC orgFunction = new ProviderOrgFunctionC();
            orgFunction = ( ProviderOrgFunctionC ) uow.registerObject( orgFunction );
            orgFunction.setOrganization( orga );
            ArrayList al = new ArrayList( 1 );
            al.add( orgFunction );
            orga.setOrganizationFunctions( al );
            uow.commit();

            // Let us print Organization reference now - It should show XML having value isRFSEnabled=true
            OrganizationReference orgRef1 = new OrganizationReferenceC();
            orgRef1.setShortName( "MAIN" );
            StringWriter sw1 = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw1, orgRef1, "IFS" );
            System.out.println( "XML OUTPUT ISS-\n " + sw1.getBuffer().toString() );

            // Let us alter provider org function to have RFS Enabled =false again
            uow = session.acquireUnitOfWork();
            ProviderOrgFunction prOF = orga.getProviderOrgFunction();
            prOF = ( ProviderOrgFunctionC ) uow.registerObject( prOF );
            uow.commit();

            //Not sure why refreshing object is required to refresh the owned entity collection's ProviderOrgFunction!!
            // without this version of provider org function is coming as old
            OrganizationC orgaC = ( OrganizationC ) orga;
            uow = session.acquireUnitOfWork();
            orgaC = ( OrganizationC ) uow.refreshObject( orgaC );

            // Now organization reference should again start showing isRFSEnabled as 'false'
            OrganizationReference orgRef2 = new OrganizationReferenceC();
            orgRef2.setShortName( "MAIN" );
            orgRef2.getRFSSuported();
            StringWriter sw2 = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw2, orgRef2, "IFS" );
            System.out.println( "XML OUTPUT ISS-\n " + sw2.getBuffer().toString() );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void testBrokerOrgFunctionPersistence()
    {
        try
        {
            org.eclipse.persistence.sessions.Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            BrokerOrganizationFunction testBrokerOrgFunc = new BrokerOrganizationFunctionC();
            BrokerOrganizationFunction registeredOrgFunc = ( BrokerOrganizationFunction ) uow.registerObject( testBrokerOrgFunc );
            Collection<Organization> list = new ArrayList<Organization>();
            list.add( ( Organization ) new ReadNamedEntityC().execute( Organization.class, "CITI" ) );
            list.add( ( Organization ) new ReadNamedEntityC().execute( Organization.class, "FI1" ) );
            registeredOrgFunc.setDisabledProviders( list );
            uow.commit();

            testBrokerOrgFunc.setDisabledProviders( null );
            // refresh the broker orgfunction and retrieve the disabled providers.
            testBrokerOrgFunc = ( BrokerOrganizationFunction ) session.refreshObject( testBrokerOrgFunc );
            Collection<Organization> provs = testBrokerOrgFunc.getDisabledProviders();
            log( "testBrokerOrgFunc=" + testBrokerOrgFunc + ",testBrokerOrgFunc.disabledProviders=" + provs );
            assertNotNull( provs );
            assertEquals( "disable providers list should be the same", provs.size(), list.size() );
        }
        catch ( Exception e )
        {
            fail( "Exception in testBrokerOrgFunctionPersistence :", e );
        }
    }


}
