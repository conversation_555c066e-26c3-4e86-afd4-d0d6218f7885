package com.integral.user.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.pii.PIIServiceStartup;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.DisplayPreference;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.user.UserContact;
import com.integral.user.UserContactC;
import com.integral.user.UserFactory;
import com.integral.user.UserMessage;
import com.integral.user.UserMessageC;
import com.integral.user.UserUtilC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Date;
import java.util.Vector;

/**
 * Tests whether classes in the com.integral.user package persist properly
 */
public class UserPersistencePTestC
        extends PTestCaseC
{
    public UserPersistencePTestC( String aName )
    {
        super( aName );
    }

    @Override
    protected void setUp() throws Exception
    {
        super.setUp();
        PIIServiceStartup.initializePIIServices();
    }

    /**
     * Tests persistence of com.integral.user.UserC
     */
    public void testUserPersistence()
    {
        Class clz = com.integral.user.UserC.class;

        log( "test persistence of " + clz.getName() );

        // insert
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            User user = new UserC();
            user.setShortName( "TEST" );
            user.setStatus( 'T' );

            uow.registerObject( user );

            uow.commit();
            log( "test insert of " + clz.getName() );
        }
        catch ( Exception e )
        {
            fail( "test insert " + clz.getName() );
            e.printStackTrace();
        }

        // query
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tretrieved " + entity );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            fail( "test query " + clz.getName() );
            e.printStackTrace();
        }

        // delete
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tdeleting " + entity );
                uow.deleteObject( entity );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "test delete " + clz.getName() );
            e.printStackTrace();
        }
    }

    /**
     * Tests persistence of com.integral.user.UserMessageC
     */
    public void testUserMessagePersistence()
    {
        Class clz = com.integral.user.UserMessageC.class;

        log( "test persistence of " + clz.getName() );

        // insert
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            UserMessage usermsg = new UserMessageC();
            usermsg.setStatus( 'T' );
            usermsg.setText( "TESTTEXT" );

            UserContact contact = new UserContactC();
            usermsg.setContact( contact );

            User user = new UserC();
            user.setShortName( "TEST" );
            user.setStatus( 'T' );
            usermsg.setUser( user );

            usermsg.setCrossReference( user );

            uow.registerObject( usermsg );

            uow.commit();
            log( "test insert of " + clz.getName() );
        }
        catch ( Exception e )
        {
            fail( "test insert " + clz.getName() );
            e.printStackTrace();
        }

        // query
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                UserMessage entity = ( UserMessage ) objects.get( i );
                log( "\tretrieved " + entity );
                log( "\t\ttext    = " + entity.getText() );
                log( "\t\tcontact = " + entity.getContact() );
                log( "\t\tuser    = " + entity.getUser() );
                log( "\t\txref    = " + entity.getCrossReference() );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            fail( "test query " + clz.getName() );
            e.printStackTrace();
        }

        // delete
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tdeleting " + entity );
                uow.deleteObject( entity );
            }

            objects = uow.readAllObjects( User.class, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tdeleting " + entity );
                uow.deleteObject( entity );
            }

            objects = uow.readAllObjects( UserContact.class, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tdeleting " + entity );
                uow.deleteObject( entity );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "test delete " + clz.getName() );
            e.printStackTrace();
        }
    }

    /**
     * Tests the UserFactory.getUser() method
     */
    public void testGetUser()
    {
        try
        {
            PersistenceFactory.newSession().readAllObjects( User.class );
        }
        catch ( Exception e )
        {
        }

        User user = UserFactory.getUser( "Integral" );
        assertNotNull( user );

        user = UserFactory.getUser( "Integral@MAIN" );
        assertNotNull( user );

        user = UserFactory.getUser( "IntegralX" );
        assertNull( user );

        user = UserFactory.getUser( "Integral@MAINX" );
        assertNull( user );

        user = UserFactory.getUser( "fi1mm1@FI1" );
        assertNotNull( user );

        user = UserFactory.getUser( "fi1mm1@MAIN" );
        assertNull( user );

        user = UserFactory.getUser( "fi1mm1@FI1.FI2-FICpty22" );
        assertNull( user );

        user = UserFactory.getUser( "ficpty11trdr1@FI2.FI1-FICpty11" );
        assertNull( user );

        user = UserFactory.getUser( "fi1mm1" );
        assertNull( user );
    }

    public void testFieldsPersistence()
    {
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );
            User sessionUser = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm2" );

            uow.removeAllReadOnlyClasses();
            user = ( User ) uow.registerObject( user );
            sessionUser = ( User ) uow.registerObject( sessionUser );

            Date lastLogin = new Date();
            Date lastLogout = new Date();
            int failedAttempts = user.getNumberOfFailedAuthorizationAttempts() + ( Math.random() > 0.5 ? 1 : -1 );
            user.setFailedLoginAttempts( failedAttempts );
            user.setDigestLastModifiedBy( sessionUser );
            user.setStatusLastModifiedBy( sessionUser );
            user.setLastLoginDate( lastLogin );
            user.setLastLogoutDate( lastLogout );
            user.setExternalAuth( true );
            user.setSTPMappingExternalName( "MASFXI_Id" );
            user.setSTPMappingExternalEmailAddress( "MASFXI_Name" );
            user.setSTPMappingExternalFullName( "MASFXI_FullName" );
            user.setROTriggeringEmailEnabled(true);
            //user.setMiFIDShortCode( "ShortCode" );
            user.setMiFIDExecutingUser( true );
            user.setMiFIDInvDecisionMaker( true );
            user.setNationalIDType(2);
            user.setNationalIdCountry ( "TestCountry" );
            user.setMultiIdentitySSOToken ( "TestSSOToken" );
            uow.commit();

            user = ( User ) getPersistenceSession().refreshObject( user );
            assertEquals( user.getFailedLoginAttempts(), failedAttempts );
            assertTrue( sessionUser.isSameAs( user.getStatusLastModifiedBy() ) );
            assertTrue( sessionUser.isSameAs( user.getDigestLastModifiedBy() ) );
            assertEquals( user.getLastLoginDate().getTime(), lastLogin.getTime() );
            assertEquals( user.getLastLogoutDate().getTime(), lastLogout.getTime() );
            assertEquals( user.isExternalAuth(), true );
            assertEquals( user.getSTPMappingExternalName(), "MASFXI_Id" );
            assertEquals( user.getSTPMappingExternalEmailAddress(), "MASFXI_Name" );
            assertEquals( user.getSTPMappingExternalFullName(), "MASFXI_FullName" );
            assertEquals ( "TestCountry", user.getNationalIdCountry () );

            assertEquals( user.isROTriggeringEmailEnabled(),true);
            assertTrue(user.isMiFIDInvDecisionMaker());
            assertTrue(user.isMiFIDExecutingUser());
            //assertEquals(user.getMiFIDShortCode(), "ShortCode" );
            assertEquals(user.getNationalIDType(), Integer.valueOf(2));
            assertEquals( user.getMultiIdentitySSOToken (), "TestSSOToken" );

            // retest again with a null last login and logout time.
            uow = this.getPersistenceSession().acquireUnitOfWork();

            uow.removeAllReadOnlyClasses();
            user = ( User ) uow.registerObject( user );
            sessionUser = ( User ) uow.registerObject( sessionUser );
            user.setLastLoginDate( null );
            user.setLastLogoutDate( null );
            uow.commit();

            user = ( User ) getPersistenceSession().refreshObject( user );
            assertTrue( user.getLastLoginDate() == null );
            assertTrue( user.getLastLogoutDate() == null );
            assertEquals( user.getMultiIdentitySSOToken (), "TestSSOToken" );
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }

    public void testUserRoleChecks()
    {
        try
        {
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );
            assertTrue( UserUtilC.isMarketMaker( user ) );
            assertFalse( UserUtilC.isSalesAdministrator( user ) );
            assertFalse( UserUtilC.isCustomerTrader( user ) );
            assertFalse( UserUtilC.isFIAdminChecker( user ) );
            assertFalse( UserUtilC.isFIAdminMakerChecker( user ) );
            assertFalse( UserUtilC.isLPUser( user ) );
            assertFalse( UserUtilC.isProfessionalCustomerTrader( user ) );
            assertFalse( UserUtilC.isSysAdmin( user ) );
            assertFalse( UserUtilC.isSysAdminChecker( user ) );
            assertFalse( UserUtilC.isSysAdminMaker( user ) );
            assertFalse( UserUtilC.isSysAdminMakerChecker( user ) );
            assertFalse( UserUtilC.isSysAdminChecker( user ) );
            assertFalse( UserUtilC.isSysAdminChecker( user ) );
        }
        catch ( Exception e )
        {
            fail( "testUserRoleChecks", e );
        }
    }

    public void testFXLegCustomFieldPersistence()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency aud = CurrencyFactory.getCurrency( "AUD" );
            FXRateConvention conv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );

            uow = this.getPersistenceSession().acquireUnitOfWork();
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );

            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            user = ( User ) uow.registerObject( user );
            DisplayPreference dp = user.getDisplayPreference();
            dp = ( DisplayPreference ) uow.registerObject( dp );
            FXLeg cfLeg = ( FXLeg ) dp.getCustomFieldValue( "testFXLeg" );
            if ( cfLeg == null )
            {
                cfLeg = FXFactory.newFXLeg();
                cfLeg = ( FXLeg ) uow.registerObject( cfLeg );
                FXPaymentParameters fxPmt = cfLeg.getFXPayment();
                fxPmt.setCurrency1( eur );
                fxPmt.setCurrency2( usd );
                FXRate fxRate = FXFactory.newFXRate();
                fxRate.setBaseCurrency( eur );
                fxRate.setVariableCurrency( usd );
                fxRate.setFXRateConvention( conv );
                fxPmt.setFXRate( fxRate );
                dp.putCustomField( "testFXLeg", cfLeg );
            }
            uow.commit();
            FXLeg leg = ( FXLeg ) user.getDisplayPreference().getCustomFieldValue( "testFXLeg" );
            assertNotNull( leg );
            assertEquals( leg.getFXPayment().getCurrency1(), eur );
            assertEquals( leg.getFXPayment().getCurrency2(), usd );
            assertEquals( leg.getFXPayment().getFXRate().getBaseCurrency(), eur );
            assertEquals( leg.getFXPayment().getFXRate().getVariableCurrency(), usd );
            assertEquals( leg.getFXPayment().getFXRate().getCurrencyPair().getBaseCurrency(), eur );
            assertEquals( leg.getFXPayment().getFXRate().getCurrencyPair().getVariableCurrency(), usd );

            // refresh the object and check again.
            // refresh all the objects and check again.
            User u1 = ( User ) getPersistenceSession().refreshObject( user );
            dp = ( DisplayPreference ) getPersistenceSession().refreshObject( u1.getDisplayPreference() );
            FXLeg fxLeg = ( FXLeg ) dp.getCustomFieldValue( "testFXLeg" );
            fxLeg = ( FXLeg ) getPersistenceSession().refreshObject( fxLeg );
            assertEquals( fxLeg.getFXPayment().getCurrency1(), eur );
            assertEquals( fxLeg.getFXPayment().getCurrency2(), usd );
            assertEquals( fxLeg.getFXPayment().getFXRate().getBaseCurrency(), eur );
            assertEquals( fxLeg.getFXPayment().getFXRate().getVariableCurrency(), usd );
            assertEquals( fxLeg.getFXPayment().getFXRate().getCurrencyPair().getBaseCurrency(), eur );
            assertEquals( fxLeg.getFXPayment().getFXRate().getCurrencyPair().getVariableCurrency(), usd );

            // now update the currencies to another.
            uow = this.getPersistenceSession().acquireUnitOfWork();
            user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            user = ( User ) uow.registerObject( user );
            dp = user.getDisplayPreference();
            dp = ( DisplayPreference ) uow.registerObject( dp );
            FXLeg cfLeg1 = ( FXLeg ) dp.getCustomFieldValue( "testFXLeg" );
            cfLeg1 = ( FXLeg ) uow.registerObject( cfLeg1 );
            FXPaymentParameters fxPmt1 = cfLeg1.getFXPayment();
            fxPmt1.setCurrency1( gbp );
            fxPmt1.setCurrency2( aud );
            FXRate fxRate1 = fxPmt1.getFXRate();
            fxRate1.setBaseCurrency( gbp );
            fxRate1.setVariableCurrency( aud );
            fxRate1.setFXRateConvention( conv );

            uow.commit();
            assertNotNull( cfLeg1 );

            assertEquals( cfLeg1.getFXPayment().getCurrency1(), gbp );
            assertEquals( cfLeg1.getFXPayment().getCurrency2(), aud );
            assertEquals( cfLeg1.getFXPayment().getFXRate().getBaseCurrency(), gbp );
            assertEquals( cfLeg1.getFXPayment().getFXRate().getVariableCurrency(), aud );
            assertEquals( cfLeg1.getFXPayment().getFXRate().getCurrencyPair().getBaseCurrency(), gbp );
            assertEquals( cfLeg1.getFXPayment().getFXRate().getCurrencyPair().getVariableCurrency(), aud );

            // refresh all the objects and check again.
            User aUser = ( User ) getPersistenceSession().refreshObject( user );
            dp = ( DisplayPreference ) getPersistenceSession().refreshObject( aUser.getDisplayPreference() );
            FXLeg leg1 = ( FXLeg ) dp.getCustomFieldValue( "testFXLeg" );
            leg1 = ( FXLeg ) getPersistenceSession().refreshObject( fxLeg );
            assertEquals( leg1.getFXPayment().getCurrency1(), gbp );
            assertEquals( leg1.getFXPayment().getCurrency2(), aud );
            assertEquals( leg1.getFXPayment().getFXRate().getBaseCurrency(), gbp );
            assertEquals( leg1.getFXPayment().getFXRate().getVariableCurrency(), aud );
            assertEquals( leg1.getFXPayment().getFXRate().getCurrencyPair().getBaseCurrency(), gbp );
            assertEquals( leg1.getFXPayment().getFXRate().getCurrencyPair().getVariableCurrency(), aud );
        }
        catch ( Exception e )
        {
            fail( "testFXLegCustomFieldPersistence", e );
        }
        finally
        {
            try
            {
                uow = this.getPersistenceSession().acquireUnitOfWork();
                User usr = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );
                uow.removeAllReadOnlyClasses();
                DisplayPreference dp = usr.getDisplayPreference();
                dp = ( DisplayPreference ) uow.registerObject( dp );
                FXLeg cfLeg2 = ( FXLeg ) dp.getCustomFieldValue( "testFXLeg" );
                if ( cfLeg2 != null )
                {
                    dp.removeCustomField( "testFXLeg" );
                }
                uow.commit();

                // refresh all the objects.
                usr = ( User ) getPersistenceSession().refreshObject( usr );
                dp = ( DisplayPreference ) getPersistenceSession().refreshObject( usr.getDisplayPreference() );
                FXLeg fxLeg = ( FXLeg ) dp.getCustomFieldValue( "testFXLeg" );
                assertNull( fxLeg );
            }
            catch ( Exception e )
            {
                log.error( "testFXLegCustomFieldPersistence", e );
            }
        }
    }

    public void testNationalIdFieldsPersistence()
    {
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );

            uow.removeAllReadOnlyClasses();
            user = ( User ) uow.registerObject( user );

            user.setNationalIDType(2);
            user.setNationalIdCountry ( "TestCountry" );
            uow.commit();

            user = ( User ) getPersistenceSession().refreshObject( user );
            assertEquals( Integer.valueOf(2), user.getNationalIDType () );
            assertEquals ( "TestCountry", user.getNationalIdCountry () );

        }
        catch ( Exception e )
        {
            fail( "testNationalIdFieldsPersistence", e );
        }
    }

}

