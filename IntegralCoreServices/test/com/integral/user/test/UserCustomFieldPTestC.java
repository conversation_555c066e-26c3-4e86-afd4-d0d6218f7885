//package com.integral.persistence.test;
package com.integral.user.test;


import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;

public class UserCustomFieldPTestC
        extends PTestCaseC
{
    public UserCustomFieldPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

/* TBD
		// suite.addTest(new UserCustomFieldPTestC("createSuccess"));
		// suite.addTest(new UserCustomFieldPTestC("createFailure"));
		// suite.addTest(new UserCustomFieldPTestC("createPersistence"));
		// suite.addTest(new UserCustomFieldPTestC("testEntity"));
		// suite.addTest(new UserCustomFieldPTestC("testUser"));
		// suite.addTest(new UserCustomFieldPTestC("testPersistentUserCF"));
		// suite.addTest(new UserCustomFieldPTestC("testXMLCustomField"));
		suite.addTest(new UserCustomFieldPTestC("addSortedCustomFields"));
		suite.addTest(new UserCustomFieldPTestC("sortedLookup"));
*/
        return suite;
    }

/*
	//This is the test for CustomFields
	public void createSuccess()
	{
		
		log("positive test");
		try
		{
			//
			// value test
			//

			// null
			CustomField o0 = UserFactory.newUserCustomField();
			o0.setKey("key0");
			assertNull(o0.getValue());

			// int
			CustomField o1 = UserFactory.newUserCustomField();
			o1.setKey("key1");
			int v1 = 1;
			o1.setInteger(v1);
			assertEquals(o1.getInteger(),v1);

			// long
			CustomField o2 = UserFactory.newUserCustomField();
			o2.setKey("key2");
			long v2 = 2;
			o2.setLong(v2);
			assertEquals(o2.getLong(),v2);

			// double
			CustomField o3 = UserFactory.newUserCustomField();
			o3.setKey("key3");
			double v3 = 3.3;
			o3.setDouble(v3);
			assertEquals(o3.getDouble(),v3,0);

			// char
			CustomField o4 = UserFactory.newUserCustomField();
			o4.setKey("key4");
			char v4 = 'a';
			o4.setChar(v4);
			assertEquals(o4.getChar(),v4);

			// boolean
			CustomField o5 = UserFactory.newUserCustomField();
			o5.setKey("key5");
			boolean v5 = false;
			o5.setBoolean(v5);
			assertEquals(o5.getBoolean(),v5);

			CustomField o6 = UserFactory.newUserCustomField();
			o6.setKey("key6");
			boolean v6 = true;
			o6.setBoolean(v6);
			assertEquals(o6.getBoolean(),v6);

			// String
			CustomField o7 = UserFactory.newUserCustomField();
			o7.setKey("key7");
			String v7 = "abc";
			o7.setValue(v7);
			assertEquals(o7.getValue(),v7);

			// IdcDate & IdcDateTime
			CustomField o8 = UserFactory.newUserCustomField();
			o8.setKey("key7b");
			IdcDate v8 = DateTimeFactory.timeNow();
			o8.setValue(v8);
			assertEquals(o8.getValue(),v8);


			CustomField o8b = UserFactory.newUserCustomField();
			o8b.setKey("key8");
			IdcDateTime v8b = DateTimeFactory.timeNowTime();
			o8b.setValue(v8b);
			assertEquals(o8b.getValue(),v8b);

			// Timestamp
			CustomField o9 = UserFactory.newUserCustomField();
			o9.setKey("key9");
			Timestamp v9 = new Timestamp(0);
			o9.setValue(v9);
			assertEquals(o9.getValue(),v9);

			// Entity
			CustomField o11 = UserFactory.newUserCustomField();
			o11.setKey("key11");
			Entity v11 = UserFactory.newUser("BB");
			o11.setValue(v11);
			assertEquals(o11.getValue(),v11);

			// Object
			CustomField o12 = UserFactory.newUserCustomField();
			o12.setKey("key12");
			ArrayList a12 = new ArrayList(100); 
			o12.setValue(a12);
			assertEquals(o12.getValue(),a12);

		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("Exception in Creation");
		}

	}

	public void createFailure()
	{
		log("negative test");
		try
		{
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("Exception in FailureCreation");
		}

	}

	// Checking if the customfields are storing to DMBS
	public void createPersistence()
	{
		log("persistence test");
		try
		{
			CustomField objs[] = new CustomField[100];
			int i=0;

			// UOW
			UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();


			// null
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertNull(objs[i].getValue());
			i++;

			// int
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			int v1 = 1;
			objs[i].setInteger(v1);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getInteger(),v1);
			i++;

			// long
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			long v2 = 2;
			objs[i].setLong(v2);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getLong(),v2);
			i++;

			// double
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			double v3 = 3.3;
			objs[i].setDouble(v3);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getDouble(),v3,0);
			i++;

			// char
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			char v4 = 'a';
			objs[i].setChar(v4);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getChar(),v4);
			i++;

			// boolean
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			boolean v5 = false;
			objs[i].setBoolean(v5);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getBoolean(),v5);
			i++;

			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			boolean v6 = true;
			objs[i].setBoolean(v6);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getBoolean(),v6);

			i++;
			// String
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			String v7 = "abc";
			objs[i].setValue(v7);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),v7);
			i++;

			// IdcDate
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			IdcDate v8 = DateTimeFactory.timeNow();
			objs[i].setValue(v8);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),v8);
			i++;

			// IdcDateTime
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			IdcDateTime v8b = DateTimeFactory.timeNowTime();
			objs[i].setValue(v8b);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),v8b);
			i++;

			// Timestamp
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			Timestamp v9 = new Timestamp(0);
			objs[i].setValue(v9);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),v9);
			i++;

			// Entity
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			Entity v11 = (Entity)uow.readObject(UserC.class);
			objs[i].setValue(v11);

			objs[i] = (CustomField)uow.registerObject(objs[i]);
			if (objs[i].getValue() instanceof Entity)
			{
				Entity v11_p = (Entity)objs[i].getValue();
				assertTrue(((v11_p instanceof User)
						 && (v11.getClass() == v11_p.getClass())
						 && (v11.getObjectID() == v11_p.getObjectID())));
			}
			else
			{
				fail("Entity");
			}
			i++;

			// Object
			objs[i] = UserFactory.newUserCustomField();
			objs[i].setKey("key" + i);
			ArrayList a1 = new ArrayList(100);
			objs[i].setValue(a1);
			objs[i] = (CustomField)uow.registerObject(objs[i]);
			assertEquals(objs[i].getValue(),a1);
			i++;

			uow.commit();
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("Exception in the creation of Persistence");
		}

	}


	public void testEntity()
	{
		log("entity test");
		try
		{
			String BACKOFFICEID_KEY = "backOfficeId";
			String RELATEDCRNC_KEY = "relatedCrnc";

			CustomField objs[] = new CustomField[100];
			int i=0;

			try
			{
				// UnsupportedOperationException
				UserC crnc = new UserC();
				crnc.putCustomField("try","abc");
				fail("Unsupported Operation Exception");
			}
			catch (Exception e)
			{
			}
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("User test");
		}

	}

	public void testPersistentUserCF()
	{
		log("check persistent user custom fields");
		try
		{
			UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

			ExpressionBuilder eb = new ExpressionBuilder();
			Expression expr = eb.get("shortName").equal("Integral");
			User integral = (User)uow.readObject(UserC.class,expr);

			// first read existing custom fields
			Iterator it = integral.customFieldKeySet().iterator();
			while (it.hasNext())
			{
				String key   = (String)it.next();
				Object value = integral.getCustomField(key);
				log("\t\tUser Integral CF: key = " + key + ", value = " + value);
			}
			
			// then add custom fields to the user "Integral"
			Entity entity = (Entity)uow.readObject(UserC.class);

			integral.putCustomField("test-int", 1);
			integral.putCustomField("test-long", 2L);
			integral.putCustomField("test-double", 3.0);
			integral.putCustomField("test-char", 'x');
			integral.putCustomField("test-boolean", true);
			integral.putCustomField("test-String", "abc");
			integral.putCustomField("test-IdcDate", DateTimeFactory.timeNow());
			integral.putCustomField("test-IdcDateTime", DateTimeFactory.timeNowTime());
			integral.putCustomField("test-Timestamp", new Timestamp(0));
			integral.putCustomField("test-entity", entity);

			uow.commit();
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("Exception in the creation of Persistence");
		}

	}


	//Checking for User cases
	public void testUser()
	{
		log("user test");
		try
		{
			String BACKOFFICEID_KEY = "backOfficeId";
			String RELATEDCRNC_KEY = "relatedCrnc";
			String ISABSOLUTE_KEY = "isAbsolute";

			int i=0;
			String shortName = "A" + String.valueOf(System.currentTimeMillis()).substring(6,12);

			// Phase 1: add objects and save them to DBMS

			UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
			User crnc = (User)uow1.readObject(UserC.class);
			User usr = UserFactory.newUser(shortName);
			usr = (User)uow1.registerObject(usr);
			usr.putCustomField(BACKOFFICEID_KEY,"abc123");
			usr.putCustomField(RELATEDCRNC_KEY, crnc);
			usr.putCustomField(ISABSOLUTE_KEY, Boolean.TRUE);
			uow1.commit();

			// Phase 2: Select and retrieve the custom fields

			UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
			ReadAllQuery query2 = new ReadAllQuery();
			query2.setReferenceClass(User.class);
			query2.setSelectionCriteria(new ExpressionBuilder().get("shortName").equal(shortName));
			query2.addJoinedAttribute(query2.getExpressionBuilder().anyOf("customFields"));
			Vector users2 = (Vector)uow2.executeQuery(query2);

			// display custom fields
			if (users2 != null && users2.size() !=0)
			{
				for (i=0; i<users2.size(); i++)
				{
					User usr2 = (User)users2.elementAt(i);
					Iterator it = usr2.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = usr2.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}
					// display specific custom fields
					log("\tspecific custom fields");
					if (usr2.containsCustomFieldKey(BACKOFFICEID_KEY))
					{
						String backOfficeId = (String)usr2.getCustomFieldValue(BACKOFFICEID_KEY);
						log("\t\tback office ID = " + backOfficeId);
					}
					if (usr2.containsCustomFieldKey(RELATEDCRNC_KEY))
					{
						Entity relatedCrnc = (Entity)usr2.getCustomFieldValue(RELATEDCRNC_KEY);
						log("\t\trelated crnc = #" + relatedCrnc.getObjectID());
					}
					if (usr2.containsCustomFieldKey(ISABSOLUTE_KEY))
					{
						Boolean isAbsolute = (Boolean)usr2.getCustomFieldValue(ISABSOLUTE_KEY);
						log("\t\tis absolute = " + isAbsolute);
					}

				}
			}
			uow2.release();

			// Phase 3: select the User and update custom fields

			UnitOfWork uow3 = getPersistenceSession().acquireUnitOfWork();
			Organization org = (Organization)uow3.readObject(OrganizationC.class);
			ReadAllQuery query3 = new ReadAllQuery();
			query3.setReferenceClass(User.class);
			query3.setSelectionCriteria( new ExpressionBuilder().get("shortName").equal(shortName));
			query3.addJoinedAttribute(query3.getExpressionBuilder().anyOf("customFields"));
			Vector users3 = (Vector)uow3.executeQuery(query3);

			if (users3 != null && users3.size() != 0)
			{
				for (i=0; i<users3.size(); i++)
				{
					User usr3 = (User)users3.elementAt(i);

					usr3.putCustomField(BACKOFFICEID_KEY,"XYZ987");
					usr3.putCustomField(RELATEDCRNC_KEY, org);
					usr3.putCustomField(ISABSOLUTE_KEY, Boolean.FALSE);
				}
			}
			uow3.commit();

			// Phase 4: fetch User and display custom fields

			UnitOfWork uow4 = getPersistenceSession().acquireUnitOfWork();
			ReadAllQuery query4 = new ReadAllQuery();
			query4.setReferenceClass(User.class);
			query4.setSelectionCriteria(new ExpressionBuilder().get("shortName").equal(shortName));
			query4.addJoinedAttribute(query4.getExpressionBuilder().anyOf("customFields"));
			Vector users4 = (Vector)uow4.executeQuery(query4);

			if (users4 != null && users4.size() !=0)
			{
				for (i=0; i<users4.size(); i++)
				{
					User usr4 = (User)users4.elementAt(i);
					Iterator it = usr4.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = usr4.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}

					// display specific custom fields
					log("\tspecific custom fields");
					if (usr4.containsCustomFieldKey(BACKOFFICEID_KEY))
					{
						String backOfficeId = (String)usr4.getCustomFieldValue(BACKOFFICEID_KEY);
						log("\t\tback office ID = " + backOfficeId);
					}
					if (usr4.containsCustomFieldKey(RELATEDCRNC_KEY))
					{
						Entity relatedOrg = (Entity)usr4.getCustomFieldValue(RELATEDCRNC_KEY);
						log("\t\trelated Organization = #" + relatedOrg.getObjectID());
					}
					if (usr4.containsCustomFieldKey(ISABSOLUTE_KEY))
					{
						Boolean isAbsolute = (Boolean)usr4.getCustomFieldValue(ISABSOLUTE_KEY);
						log("\t\tis absolute = " + isAbsolute);
					}
				}
			}
			uow4.release();

			// Phase 5: fetch User ased on custom fields

			UnitOfWork uow5 = getPersistenceSession().acquireUnitOfWork();
			ReadAllQuery query5 = new ReadAllQuery();
			query5.setReferenceClass(User.class);
			query5.setSelectionCriteria(new ExpressionBuilder().get("shortName").equal(shortName));
			query5.addJoinedAttribute(query5.getExpressionBuilder().anyOf("customFields"));
			Vector users5 = (Vector)uow5.executeQuery(query5);

			if (users5 != null && users5.size() !=0)
			{
				for (i=0; i<users5.size(); i++)
				{
					User usr5 = (User)users5.elementAt(i);
					log("\t\tDisplay all the custom fields");
					Iterator it = usr5.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = usr5.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}

					log("\tspecific custom fields");
					if (usr5.containsCustomFieldKey(BACKOFFICEID_KEY))
					{
						String backOfficeId = (String)usr5.getCustomFieldValue(BACKOFFICEID_KEY);
						log("\t\tback office ID = " + backOfficeId);
					}
					if (usr5.containsCustomFieldKey(RELATEDCRNC_KEY))
					{
						Entity relatedOrg = (Entity)usr5.getCustomFieldValue(RELATEDCRNC_KEY);
						log("\t\trelated Organization = #" + relatedOrg.getObjectID());
					}
					if (usr5.containsCustomFieldKey(ISABSOLUTE_KEY))
					{
						Boolean isAbsolute = (Boolean)usr5.getCustomFieldValue(ISABSOLUTE_KEY);
						log("\t\tis absolute = " + isAbsolute);
					}

				}
			}
			uow5.release();

			//Phase 6 Fetch user and delete the custom fields

			UnitOfWork uow6 = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query6 = new ReadAllQuery();
			query6.setReferenceClass(User.class);
			query6.setSelectionCriteria(new ExpressionBuilder().get("shortName").equal(shortName));
			query6.addJoinedAttribute(query6.getExpressionBuilder().anyOf("customFields"));
			Vector users6 = (Vector)uow6.executeQuery(query6);

			if (users6 != null && users6.size() !=0 )
			{
				for (i=0; i<users6.size(); i++)
				{
					User usr6 = (User)users6.elementAt(i);
					Iterator it = usr6.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						if (key.equals(BACKOFFICEID_KEY))
						usr6.removeCustomField(BACKOFFICEID_KEY);
					}
				}
			}
			uow6.commit();

 		//	Phase 7: fetch user based on custom fields and check if the entry has been deleted

			UnitOfWork uow7 = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query7 = new ReadAllQuery();
			query7.setReferenceClass(User.class);
			query7.setSelectionCriteria(new ExpressionBuilder().get("shortName").equal(shortName));
			query7.addJoinedAttribute(query7.getExpressionBuilder().anyOf("customFields"));
			Vector users7 = (Vector)uow7.executeQuery(query7);

			if (users7 != null && users7.size() != 0)
			{
				for (i=0; i<users7.size(); i++)
				{
					User usr7 = (User)users7.elementAt(i);
					log("\tDisplay all the custom fields");
					Iterator it = usr7.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = usr7.getCustomField(key);
						log("\t\tkey = " + key + ", value = " + value);
					}

					log("\tSpecific custom field test");
					if (usr7.containsCustomFieldKey(BACKOFFICEID_KEY))
					{
						String backOfficeId = (String)usr7.getCustomFieldValue(BACKOFFICEID_KEY);
						log("\t\tback office ID = " + backOfficeId);
					}
					else
					{
						log("\t\tThe selectec custom field has being successfully deleted");
					}
					if (usr7.containsCustomFieldKey(RELATEDCRNC_KEY))
					{
						Entity relatedOrg = (Entity)usr7.getCustomFieldValue(RELATEDCRNC_KEY);
						log("\t\trelated Organization#" + relatedOrg.getObjectID());
					}
					if (usr7.containsCustomFieldKey(ISABSOLUTE_KEY))
					{
						Boolean isAbsolute = (Boolean)usr7.getCustomFieldValue(ISABSOLUTE_KEY);
						log("\t\tis absolute = " + isAbsolute);
					}

				}
			}
			uow7.release();

		   //Phase 8 Select a User and delete all the customFields

			UnitOfWork uow8 = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query8 = new ReadAllQuery();
			query8.setReferenceClass(User.class);
			query8.setSelectionCriteria(new ExpressionBuilder().get("shortName").equal(shortName));
			query8.addJoinedAttribute(query8.getExpressionBuilder().anyOf("customFields"));
			Vector users8 = (Vector)uow8.executeQuery(query8);
			if (users8 != null && users8.size() != 0)
			{
				for (i=0; i<users8.size(); i++)
				{
					User usr8 = (User)users8.elementAt(i);
					usr8.clearCustomFields();
				}
			}
			uow8.commit();

			//Phase 9 Select a User and display the customfields.
			UnitOfWork uow9 = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query9 = new ReadAllQuery();
			query9.setReferenceClass(User.class);
			query9.setSelectionCriteria(new ExpressionBuilder().get("shortName").equal(shortName));
			query9.addJoinedAttribute(query9.getExpressionBuilder().anyOf("customFields"));
			Vector users9 = (Vector)uow9.executeQuery(query9);

			if (users9 != null && users9.size() != 0)
			{
				for (i=0; i<users9.size(); i++)
				{
					User usr9 = (User)users9.elementAt(i);
					log("\tDisplay all the custom fields");
					Iterator it = usr9.customFieldKeySet().iterator();
					while (it.hasNext())
					{
						String key   = (String)it.next();
						Object value = usr9.getCustomField(key);
						log("\tkey = " + key + ", value = " + value);
					}
				}
			}
			uow9.release();
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("Excpetion in User Test");
		}

	}


	public void testXMLCustomField()
	{
		log("check XMl custom fields");
		try
		{
			CustomFieldC cf = (CustomFieldC)UserFactory.newUserCustomField();

			cf.setObjectType("java.lang.Integer");
			cf.setObjectValue("1");
			log("\tInteger: " + cf);

			cf.setObjectType("java.lang.Long");
			cf.setObjectValue("2");
			log("\tLong: " + cf);

			cf.setObjectType("java.lang.Double");
			cf.setObjectValue("3.567");
			log("\tDouble: " + cf);

			cf.setObjectType("java.lang.Character");
			cf.setObjectValue("C");
			log("\tCharacter: " + cf);

			cf.setObjectType("java.lang.String");
			cf.setObjectValue("abcd");
			log("\tString: " + cf);

			cf.setObjectType("java.sql.Timestamp");
			Timestamp ts = new Timestamp(0);
			cf.setObjectValue(ts.toString());
			log("\tTimestamp: " + cf);

			cf.setObjectType("com.integral.time.IdcDate");
			IdcDate dt = DateTimeFactory.timeNow();
			cf.setObjectValue(dt.toString());
			log("\tIdcDate: " + cf);

			cf.setObjectType("com.integral.time.IdcDateTime");
			IdcDateTime dtt = DateTimeFactory.timeNowTime();
			cf.setObjectValue(dtt.toString());
			log("\tidcDateTime: " + cf);

			ExpressionBuilder eb = new ExpressionBuilder();
			Expression expr = eb.get("shortName").equal("Integral");
			User integral = (User)getPersistenceSession().readObject(UserC.class,expr);

			cf.setObjectType("com.integral.user.UserC");
			cf.setObjectValue(""+integral.getObjectID());
			log("\tUser Entity: " + cf);
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("Exception in the creation of Persistence");
		}

	}

	public void addSortedCustomFields()
	{
		log("add user custom fields for sort expression");
		try
		{
			UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

			ReadAllQuery query = new ReadAllQuery();
			query.setReferenceClass(User.class);
			Vector users = (Vector)uow.executeQuery(query);

			if (users != null && users.size() !=0)
			{
				for (int i=0; i<users.size(); i++)
				{
					User user = (User)users.elementAt(i);
					// user.putCustomField("test2sort","aaa_sort" + i);
					// user.putCustomField("testsort","sort" + i);
					user.putCustomField("name","name-" + i);
				}
			}

			uow.commit();
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("Exception in addSortedCustomFields");
		}

	}

	public void sortedLookup()
	{
		log("lookup user custom fields with sort expression");
		try
		{
			ExpressionBuilder eb = new ExpressionBuilder();

			Expression cfExpr1 = eb.anyOf("customFields");
			Expression searchExpr1 = cfExpr1.get("key").equal("testsort");
			Expression orderExpr1  = cfExpr1.get("stringValue");

			Expression cfExpr2 = eb.anyOf("customFields");
			Expression searchExpr2_1 = cfExpr2.get("key").equal("name");
			Expression searchExpr2_2 = cfExpr2.get("stringValue").like("name-1%");
			Expression searchExpr2 = searchExpr2_1.and(searchExpr2_2);

			Expression searchExpr = searchExpr1.and(searchExpr2);

			ReadAllQuery query = new ReadAllQuery();
			query.setReferenceClass(User.class);
			query.setSelectionCriteria(searchExpr);
			query.addOrdering(orderExpr1);
			Vector users = (Vector)getPersistenceSession().executeQuery(query);

			if (users != null && users.size() !=0)
			{
				for (int i=0; i<users.size(); i++)
				{
					User user = (User)users.elementAt(i);
					log("\tuser #" + i + ":\t" + user.getShortName()
						+ "\tCF[testsort]:\t" + user.getCustomFieldValue("testsort"));
				}
			}
		}
		catch(Exception e)
		{
			e.printStackTrace();
			fail("Exception in sortedLookup: " + e);
		}
	}
*/

}
