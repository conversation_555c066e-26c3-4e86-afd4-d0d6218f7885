package com.integral.user.test;

import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.user.Organization;
import com.integral.user.UserFactory;
import com.integral.user.UserUtilC;
import com.integral.user.config.User;
import com.integral.user.config.UserMBean;

/**
 * <AUTHOR> Development Corporation.
 */
public class UserMBeanTestC extends MBeanTestCaseC
{
    User userMBean = ( User ) UserFactory.getUserMBean();

    public UserMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( userMBean, "userInactivationDays", UserMBean.USERINACTIVATION_DAYS_KEY, MBeanTestCaseC.STRING );
        testProperty( userMBean, "notificationEmailFromAddr", UserMBean.USERINACTIVATION_MAIL_FROM_KEY, MBeanTestCaseC.STRING );
        testProperty( userMBean, "excludedUsers", UserMBean.EXCLUDEDUSERS_KEY, MBeanTestCaseC.STRING );
        testProperty( userMBean, "notificationEmailToAddr", UserMBean.MAS_NOTIFICATION_MAIL_TO_KEY, MBeanTestCaseC.STRING );
//        testProperty( userMBean, "userPasswordExpiryDays", UserMBean.PASSWORD_EXPIRY_DAYS_KEY, MBeanTestCaseC.INTEGER );
        testProperty( userMBean, "maxUserLoginPasswordFailedAttempts", UserMBean.MAX_USER_PASSWORD_FAILED_LOGIN_ATTEMPTS_KEY, MBeanTestCaseC.INTEGER );
        testProperty( userMBean, "maxUserPasswordChangePasswordFailedAttempts", UserMBean.MAX_USER_PASSWORD_FAILED_PASSWORD_CHANGE_ATTEMPTS_KEY, MBeanTestCaseC.INTEGER );
        testProperty( userMBean, "passwordGeneratorMinPasswordLength", UserMBean.MIN_PASSWORD_GENERATOR_PASSWORD_LENGTH_KEY, MBeanTestCaseC.INTEGER );
        testProperty( userMBean, "passwordGeneratorMaxPasswordLength", UserMBean.MAX_PASSWORD_GENERATOR_PASSWORD_LENGTH_KEY, MBeanTestCaseC.INTEGER );
        testProperty( userMBean, "userForgotPasswordFromEmailId", UserMBean.USER_PASSWORD_CHANGE_FROM_EMAIL_ADDRESS, MBeanTestCaseC.STRING );
        testProperty( userMBean, "userForgotPasswordEmailSubject", UserMBean.USER_PASSWORD_CHANGE_EMAIL_SUBJECT, MBeanTestCaseC.STRING );
        testProperty( userMBean, "userForgotPasswordEmailContent", UserMBean.USER_PASSWORD_CHANGE_EMAIL_CONTENT, MBeanTestCaseC.STRING );
        testProperty( userMBean, "userPasswordQuestionMaxLength", UserMBean.MAX_PASSWORD_QUESTION_LENGTH_KEY, MBeanTestCaseC.INTEGER );
        testProperty( userMBean, "userPasswordAnswerMaxLength", UserMBean.MAX_PASSWORD_ANSWER_LENGTH_KEY, MBeanTestCaseC.INTEGER );
        testProperty( userMBean, "productNameExternalID", UserMBean.ORGANIZATION_BRAND_NAME_EXTERNAL_ID, MBeanTestCaseC.STRING );
        testProperty( userMBean, "defaultProductName", UserMBean.USER_PASSWORD_EMAIL_CONTENT_PRODUCT_NAME, MBeanTestCaseC.STRING );
        testProperty( userMBean, "userInactivationEmailSubject", UserMBean.USER_INACTIVATION_EMAIL_SUBJECT, MBeanTestCaseC.STRING );
        testProperty( userMBean, "userInactivationEmailContent", UserMBean.USER_INACTIVATION_EMAIL_CONTENT, MBeanTestCaseC.STRING );
        testProperty( userMBean, "userDatabaseQueryEnabled", UserMBean.USER_DATABASE_QUERY_ENABLED, MBeanTestCaseC.BOOLEAN );
    }

    public void testProperties1()
    {
        testProperty( userMBean, "userAuthenticationValidationMaxShortNameLength", UserMBean.USER_AUTHENTICATION_USERNAME_VALIDATION_MAXLENGTH, MBeanTestCaseC.INTEGER );
        testProperty( userMBean, "userAuthenticationValidationMaxNamespaceLength", UserMBean.USER_AUTHENTICATION_NAMESPACE_VALIDATION_MAXLENGTH, MBeanTestCaseC.INTEGER );
    }

    public void testExternalAuthProperties()
    {
        testProperty( userMBean, "FXIContactEMail", UserMBean.IDC_IS_FXI_CONTACT_EMAIL, MBeanTestCaseC.STRING );
        testProperty( userMBean, "externalAuthServerURL", UserMBean.EXTERNAL_AUTH_SERVER_URL_KEY, MBeanTestCaseC.STRING, true, true );
        userMBean.setProperty( UserMBean.EXTERNAL_AUTH_FAILURE_EMAIL_SUBJECT_KEY, "authentication failure code:$ERRORCODE", ConfigurationProperty.DYNAMIC_SCOPE, null );
        userMBean.setProperty( UserMBean.EXTERNAL_AUTH_FAILURE_EMAIL_CONTENT_KEY, "authentication failure code:$ERRORCODE$USERNAME", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( userMBean.getExternalAuthFailureEmailSubject( "10010" ), "authentication failure code:10010" );
        assertEquals( userMBean.getExternalAuthFailureEmailContent( "10010", "user" ), "authentication failure code:10010user" );
    }

    public void testAllowedUserGroupsPropertyDuplicateEntries()
    {
        Organization org1 = ReferenceDataCacheC.getInstance ().getOrganization ( "FI1" );
        String propertyName = UserUtilC.MARKETMAKER_PERMISSION_PROPERTY_SPECIFIC + "." + org1.getShortName ();
        try
        {
            userMBean.setProperty( propertyName, "XXX,YYY,YYY,ZZZ", ConfigurationProperty.DYNAMIC_SCOPE, null );
            String[] propValue = userMBean.getWorkflowUserPermission ( org1 );
            assertNotNull ( propValue );
            assertEquals ( 3, propValue.length );

            userMBean.setProperty( propertyName, "DEF,YYY,YYY,ABC ", ConfigurationProperty.DYNAMIC_SCOPE, null );
            String[] propValue1 = userMBean.getWorkflowUserPermission ( org1 );
            assertNotNull ( propValue1 );
            assertEquals ( 3, propValue1.length );
            assertEquals( "DEF", propValue1[0] );
            assertEquals( "YYY", propValue1[1] );
            assertEquals( "ABC", propValue1[2] );

            userMBean.setProperty( propertyName, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            String[] propValue2 = userMBean.getWorkflowUserPermission ( org1 );
            assertNotNull ( propValue2 );
        }
        catch ( Exception e )
        {
            fail ( "testAllowedUserGroupsPropertyDuplicateEntries" );
            e.printStackTrace ();
        }
        finally
        {
            userMBean.removeProperty ( propertyName, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }
}
