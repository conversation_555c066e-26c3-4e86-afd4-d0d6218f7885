package com.integral.user.test;

// Copyright (c) 1999-2006 Integral Development Corp. All rights reserved.


import com.integral.test.PTestCaseC;
import com.integral.user.UserC;
import org.eclipse.persistence.sessions.Session;

import java.util.Collection;


/**
 */
public class UserInfoPTestC extends PTestCaseC
{
    private Session session = null;

    private String userName;
    private long userId;
    private boolean showPassword = false;

    public UserInfoPTestC()
    {
        super( "UserInfoPTestC" );
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName( String name )
    {
        userName = name;
    }

    public long getUserId()
    {
        return userId;
    }

    public void setUserId( long id )
    {
        userId = id;
    }

    public boolean getShowPassword()
    {
        return showPassword;
    }

    public void setShowPassword( boolean doShow )
    {
        showPassword = doShow;
    }


    public void testUserInfo()
    {
        try
        {
            StringBuilder buffer = null;
            for ( UserC user : getUsers() )
            {
                if ( user != null )
                {
                    buffer = new StringBuilder( 50 ).append( "user=" ).append( user.getFullName() );
                    log.info( buffer.toString() );
                }
                else
                {
                    fail( "Got Null user." );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "Exception in printing User Information.", e );
        }

    }

    private Collection<UserC> getUsers()
    {
        try
        {
            return ( Collection<UserC> ) getPersistenceSession().readAllObjects( UserC.class );
        }
        catch ( Exception e )
        {
            fail( "Error retriving users from DB.unknown user with name=" + getUserName() + " or id=" + getUserId(), e );
            return null;
        }
    }

}
