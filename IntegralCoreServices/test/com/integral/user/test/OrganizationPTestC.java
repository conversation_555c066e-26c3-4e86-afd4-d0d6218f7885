package com.integral.user.test;


import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.trade.Tenor;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.*;
import com.integral.util.IdcUtilC;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Vector;


public class OrganizationPTestC
        extends PTestCaseC
{
    static String name = "Organization Test";
    static String ddLeName = "DD1-le1";
    static String lpUserName = "Citigroup";

    public OrganizationPTestC ( String name )
    {
        super ( name );
    }

    public static void main ( String args[] )
    {
        junit.textui.TestRunner.run ( suite () );
    }

    public static Test suite ( )
    {
        TestSuite suite = new TestSuite ();

        suite.addTest ( new OrganizationPTestC ( "testInsertOrg" ) );
        // suite.addTest(new OrganizationPTestC("testSelectOrgRel"));
        suite.addTest ( new OrganizationPTestC ( "testSTPConfiguration" ) );
        suite.addTest ( new OrganizationPTestC ( "testOrgFieldsPersistence" ) );
        return suite;
    }

    /**
     * Test inserting trading limits for an org
     */
    public void testInsertOrg ( )
    {
        // insert org and trading limit
        try
        {
            UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();

            Organization org = new OrganizationC ();
            assertEquals ( org.getIntraFloorSTPFlag (), Organization.INTRAFLOOR_TRADE_TAKER_DOWNLOAD );
            org = ( Organization ) uow.registerObject ( org );
            org.setShortName ( "ORGTEST-" + System.currentTimeMillis () );
            org.setStatus ( 'T' );
            org.setShowLimitAsMatchedPrice ( true );
            org.setShowLimitAsMatchedPriceBroker ( false );
            org.setAllowCancelOfSettledTrades ( true );
            org.setDownloadEnabledForCancelTrade ( true );
            org.setShowOrigCptyUserInSTP ( false );
            org.setOrderExecutionEnabled ( false );
            org.setPriceStreamingEnabled ( false );
            org.setFmaPricingEnabled ( false );
            org.setIncludePortfolioIDInSTP ( false );
            org.setIncludeOriginatingPortfolioIDInSTP ( false );
            org.setPreTradeAllocation ( true );
            org.setPostTradeAllocation ( true );
            org.setFullInactivation ( true );
            org.setSTPOriginatingDetails ( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            LegalEntity ddLe = ( LegalEntity ) namedEntityReader.execute ( LegalEntity.class, ddLeName );
            ddLe = ( LegalEntity ) uow.registerObject ( ddLe );
            org.setDefaultDealingEntity ( ddLe );
            User lpUser = ( User ) namedEntityReader.execute ( User.class, lpUserName );
            lpUser = ( User ) uow.registerObject ( lpUser );
            org.setDefaultDealingUser ( lpUser );
            org.setSendSpreadsInSTP ( true );
            org.setClearingHouse ( true );
            org.setSEF ( true );
            org.setSDR ( false );
            org.setTradingAllowed ( false );
            org.setNettingGroupByLE ( false );
            org.setNettingGroupByTP ( false );
            org.setAutoNettingCptyOrg ( true );
            org.setAutoNettingOrg ( true );
            org.setIntraFloorSTPFlag ( Organization.INTRAFLOOR_TRADE_BOTH_DOWNLOAD );
            org.setShowUTI ( true );
            org.setFullAllocationDownload ( true );
            org.setIncludeAllocationInSTP ( true );
            org.setExternalAuth ( 0 );
            org.setIncludedManualTradeChannelsForStp ( "DNET/MANUAL" );
            org.setShowSyntheticCrossInSTP ( true );
            org.setIncludeYMBookNameInSTP ( true );
            org.setSuperBank ( true );
            org.setMiFID ( true );
            org.setMTFVenue ( true );
            org.setMICCode ( "MICTest" );
            org.setShortCodeSource ( "scsource" );

            String lei = "TestLEI";
            org.setLEI ( lei );
            org.setInterimLEI ( true );
            uow.commit ();

            org = ( Organization ) getPersistenceSession ().refreshObject ( org );
            assertEquals ( org.isShowLimitAsMatchedPrice (), true );
            assertEquals ( org.isShowLimitAsMatchedPriceBroker (), false );
            assertEquals ( org.isAllowCancelOfSettledTrades (), true );
            assertEquals ( org.isAllowCancelOfSettledTrades (), true );
            assertEquals ( org.isDownloadEnabledForCancelTrade (), true );
            assertEquals ( org.getSTPOriginatingDetails (), Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            assertEquals ( org.isShowOrigCptyUserInSTP (), false );
            assertEquals ( org.getDefaultDealingEntity ().getShortName (), ddLeName );
            assertEquals ( org.getDefaultDealingUser ().getShortName (), lpUserName );
            assertEquals ( org.isSendSpreadsInSTP (), true );
            assertEquals ( org.isTradingAllowed (), false );
            assertEquals ( org.getExternalAuth (), 0 );
            assertEquals ( org.isNettingGroupByLE (), false );
            assertEquals ( org.isNettingGroupByTP (), false );
            assertEquals ( org.isOrderExecutionEnabled (), false );
            assertEquals ( org.isPriceStreamingEnabled (), false );
            assertEquals ( org.isFmaPricingEnabled (), false );
            assertEquals ( org.isAutoNettingCptyOrg (), true );
            assertEquals ( org.isAutoNettingOrg (), true );
            assertEquals ( org.isClearingHouse (), true );
            assertEquals ( org.isSEF (), true );
            assertEquals ( org.isSDR (), false );
            assertEquals ( org.isFullInactivation (), true );
            assertEquals ( org.getIntraFloorSTPFlag (), Organization.INTRAFLOOR_TRADE_BOTH_DOWNLOAD );
            assertEquals ( org.getLEI (), lei );
            assertEquals ( org.isInterimLEI (), true );
            assertEquals ( org.isPreTradeAllocation (), true );
            assertEquals ( org.isPostTradeAllocation (), true );
            assertEquals ( org.isShowUTI (), true );
            assertEquals ( org.isFullAllocationDownload (), true );
            assertEquals ( org.isIncludeAllocationInSTP (), true );
            assertEquals ( org.getIncludedManualTradeChannelsForStp (), "DNET/MANUAL" );
            assertEquals ( org.isShowSyntheticCrossInSTP (), true );
            assertEquals ( org.isIncludePortfolioIDInSTP (), false );
            assertEquals ( org.isIncludeOriginatingPortfolioIDInSTP (), false );
            assertEquals ( org.isSTPOverrideOnLE (), false );
            assertEquals ( org.isIncludeYMBookNameInSTP (), true );
            assertEquals ( org.isSuperBank (), true );
            assertEquals ( org.isMiFID (), true );
            assertEquals ( org.isMTFVenue (), true );
            assertEquals ( org.getMICCode (), "MICTest" );
            assertEquals ( org.getShortCodeSource (), "scsource" );


            log ( "new org index=" + org.getIndex () );
            assertEquals ( " new org index should be greater than or equal to 1", org.getIndex () >= 1, true );
        }
        catch ( Exception e )
        {
            fail ( "test insert org " );
            e.printStackTrace ();
        }
    }

    /**
     * Test inserting trading limits for an org
     */
    public void testInsertTradingLimit ( )
    {
        // insert org and trading limit
        try
        {
            UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();

            // get two different orgs
            Vector orgs = uow.readAllObjects ( Organization.class );
            TestCase.assertTrue ( orgs.size () >= 2 );
            Organization org1 = ( Organization ) orgs.elementAt ( 0 );
            Organization org2 = ( Organization ) orgs.elementAt ( 1 );

            // get currency
            Vector crncs = uow.readAllObjects ( Currency.class );
            TestCase.assertTrue ( orgs.size () >= 2 );
            Currency crnc1 = ( Currency ) crncs.elementAt ( 0 );
            Currency crnc2 = ( Currency ) crncs.elementAt ( 1 );

            // create currency pair
            CurrencyPair crncPair = CurrencyFactory.newCurrencyPair ();
            crncPair.setBaseCurrency ( crnc1 );
            crncPair.setVariableCurrency ( crnc2 );

            // print current trading limits
            printTradingLimits ( org1 );
            printTradingLimits ( org2 );

            // add a new trading limit
            TradingLimit limit = UserFactory.newTradingLimit ();
            limit.setBidOfferMode ( DealingPrice.MID );
            limit.setCurrency ( crnc1 );
            limit.setCurrencyPair ( crncPair );
            limit.setOrganization ( org2 );
            limit.setTradingLimit ( "1.4%" );

            // set trading limit mode
            org1.setTradingLimitRule ( org2, TradingLimit.CUSTOMER_SPECIFIC_TRADING_LIMIT_MODE );

            org1.getTradingLimits ().add ( limit );

            uow.commit ();
        }
        catch ( Exception e )
        {
            fail ( "test insert org function" );
            e.printStackTrace ();
        }
    }

    /**
     * Test displaying trading limits for an org
     */
    public void testQueryTradingLimit ( )
    {
        // query
        try
        {
            UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();

            ExpressionBuilder eb = new ExpressionBuilder ();
            Expression expr = eb.anyOf ( "tradingLimits" ).notNull ();

            Vector objects = ( Vector ) uow.readAllObjects ( Organization.class );
            for ( int i = 0; i < objects.size (); i++ )
            {
                Organization org = ( Organization ) objects.get ( i );
                printTradingLimits ( org );
            }

            uow.release ();
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            log.error ( "test query org function", e );
        }

    }

    private void printTradingLimits ( Organization org )
    {
        log.info ( "Organization: " + org );
        Collection limits = org.getTradingLimits ();
        Iterator it = limits.iterator ();
        while ( it.hasNext () )
        {
            TradingLimit limit = ( TradingLimit ) it.next ();
            log.info ( "\ttrading limit: " + limit );
            log.info ( "\t\tprovider org   = " + limit.getOwner () );
            log.info ( "\t\tcustomer org   = " + limit.getOrganization () );
            log.info ( "\t\ttrading limit mode for this org = " + org.getTradingLimitRule ( limit.getOrganization () ) );
            log.info ( "\t\tcurrency pair  = " + limit.getCurrency () );
            log.info ( "\t\tcurrency       = " + limit.getCurrencyPair () );
            if ( limit.getBidOfferMode () == DealingPrice.BID )
            {
                log.info ( "\t\tmode           = BID" );
            }
            if ( limit.getBidOfferMode () == DealingPrice.MID )
            {
                log.info ( "\t\tmode           = MID" );
            }
            if ( limit.getBidOfferMode () == DealingPrice.OFFER )
            {
                log.info ( "\t\tmode           = OFFER" );
            }
            log.info ( "\t\tlimit          = " + limit.getTradingLimit () );
        }
    }


    /**
     * Test inserting a new organization relationship
     */
    public void testInsertOrgRel ( )
    {
        try
        {
            UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();

            // get two different orgs
            Vector orgs = uow.readAllObjects ( Organization.class );
            Organization org1 = ( Organization ) orgs.elementAt ( 0 );
            Organization org2;
            if ( orgs.size () >= 2 )
            {
                org2 = ( Organization ) orgs.elementAt ( 1 );
            }
            else
            {
                org2 = new OrganizationC ();
                org2.setShortName ( "TESTORG" );
                org2 = ( Organization ) uow.registerObject ( org2 );
            }

            // get or create org rel clsf
            ExpressionBuilder eb = new ExpressionBuilder ();
            Expression expr = eb.get ( "shortName" ).equal ( "DD" );
            OrganizationRelationshipClassification orgClsf = ( OrganizationRelationshipClassification ) uow.readObject ( OrganizationRelationshipClassification.class, expr );
            if ( orgClsf == null )
            {
                log.info ( "\tcreated org classification DD" );
                orgClsf = new OrganizationRelationshipClassificationC ();
                orgClsf.setShortName ( "DD" );
                orgClsf = ( OrganizationRelationshipClassification ) uow.registerObject ( orgClsf );
            }

            // setup relationship
            Collection relatedOrgs = org1.getRelatedOrganizations ( "DD" );
            if ( relatedOrgs.contains ( org2 ) )
            {
                log.info ( "\torg1 is already related to org2" );
            }
            else
            {
                log.info ( "\tcreate org relationship from org1 to org2" );
                OrganizationRelationship orgRel = new OrganizationRelationshipC ();
                orgRel.setClassification ( orgClsf );
                orgRel.setRelatedOrganization ( org2 );

                relatedOrgs.add ( orgRel );
                org1.setOrganizationRelationships ( relatedOrgs );
            }

            uow.commit ();
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "test insert org rel" );
        }
    }

    public void testSTPConfiguration ( )
    {
        ReadNamedEntityC namedEntityReader = new ReadNamedEntityC ();
        UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
        uow.removeAllReadOnlyClasses ();
        Organization org = ( Organization ) uow.registerObject ( namedEntityReader.execute ( OrganizationC.class, "CITI" ) );
        log.warn ( "OrganizationPTestC:INFO:: Spot = " + org.isDownloadEnabledForSpot () + ", outright = " + org.isDownloadEnabledForOutright () + ", swap = " + org.isDownloadEnabledForSwap () +
                ", NDF = " + org.isDownloadEnabledForNDF () + ", NDFSwap = " + org.isDownloadEnabledForNDFSwap () );
        org.setDownloadEnabledForSpot ( false );
        org.setDownloadEnabledForSwap ( true );
        org.setDownloadEnabledForOutright ( true );
        org.setDownloadEnabledForNDF ( true );
        org.setDownloadEnabledForNDFSwap ( true );
        org.setSTPOverrideOnLE ( true );
        org.setIncludedManualTradeChannelsForStp ( "DNET/MANUAL,API/MANUAL" );
        uow.commitAndResume ();
        Organization refreshedOrg = ( Organization ) uow.refreshObject ( org );
        assertEquals ( "Spot ", refreshedOrg.isDownloadEnabledForSpot (), false );
        assertEquals ( "Outright  ", refreshedOrg.isDownloadEnabledForOutright (), true );
        assertEquals ( "Swap  ", refreshedOrg.isDownloadEnabledForSwap (), true );
        assertEquals ( "NDF ", refreshedOrg.isDownloadEnabledForNDF (), true );
        assertEquals ( "NDFSwap ", refreshedOrg.isDownloadEnabledForNDFSwap (), true );
        assertEquals ( "DNET/MANUAL,API/MANUAL", refreshedOrg.getIncludedManualTradeChannelsForStp () );

        log.warn ( "OrganizationPTestC:INFO:: Spot = " + refreshedOrg.isDownloadEnabledForSpot () + ", outright = " + refreshedOrg.isDownloadEnabledForOutright () + ", swap = " + refreshedOrg.isDownloadEnabledForSwap () +
                ", NDF = " + refreshedOrg.isDownloadEnabledForNDF () + ", NDFSwap = " + refreshedOrg.isDownloadEnabledForNDFSwap () );
    }

    /**
     * Test selecting an org based on a organization relationship
     */
    public void testSelectOrgRel ( )
    {
        try
        {
            UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();

            // get two different orgs
            Vector orgs = uow.readAllObjects ( Organization.class );
            Organization org2 = ( Organization ) orgs.elementAt ( 1 );

            // get org
            ExpressionBuilder eb = new ExpressionBuilder ();

            Expression expr1 = eb.anyOf ( "organizationRelationships" );
            Expression expr2 = expr1.get ( "classification" ).get ( "shortName" ).equal ( "DD" );
            Expression expr3 = expr1.get ( "relatedOrganization" ).equal ( org2 );
            Expression expr = expr2.and ( expr3 );

            Vector objects = uow.readAllObjects ( Organization.class, expr );
            for ( int i = 0; i < objects.size (); i++ )
            {
                Organization org = ( Organization ) objects.get ( i );
                log.info ( "organization " + org );
                Iterator it = org.getOrganizationRelationships ().iterator ();
                while ( it.hasNext () )
                {
                    OrganizationRelationship orgRel = ( OrganizationRelationship ) it.next ();
                    OrganizationRelationshipClassification clsf = orgRel.getClassification ();
                    Organization relatedOrg = orgRel.getRelatedOrganization ();
                    log.info ( "\t related as " + clsf + " to organization " + relatedOrg );
                }
            }


            uow.release ();
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "test insert org rel" );
        }
    }

    public void testOrgTenors ( )
    {
        // Print org tenors before starting
        printOrgTenors ();

        UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
        uow.removeAllReadOnlyClasses ();
        HashSet hs = new HashSet ( 2 );
        Tenor tenor1 = Tenor.SPOT_TENOR;
        Tenor tenor2 = Tenor.SPOT_NEXT_TENOR;
        hs.add ( tenor1 );
        hs.add ( tenor2 );
        Vector objects = uow.readAllObjects ( Organization.class );
        for ( int i = 0; i < objects.size (); i++ )
        {
            Organization org = ( Organization ) objects.get ( i );
            org = ( Organization ) uow.registerObject ( org );
            org.setSupportedTenors ( hs );
        }
        uow.commit ();

        // Read org tenors after insert operation ->
        printOrgTenors ();

        // REmove one of the tenor from HS and try to set again - to simulate modify operation
        hs.clear ();
        hs.add ( tenor1 );
        uow = getPersistenceSession ().acquireUnitOfWork ();
        uow.removeAllReadOnlyClasses ();
        objects = uow.readAllObjects ( Organization.class );
        for ( int i = 0; i < objects.size (); i++ )
        {
            Organization org = ( Organization ) objects.get ( i );
            Organization org1 = ( Organization ) uow.registerObject ( org );
            org1.setSupportedTenors ( hs );
        }
        uow.commit ();

        // Read org tenors after modify->
        printOrgTenors ();

    }

    public void testOrgBrokerOrganization ( )
    {
        try
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            ExpressionBuilder builder = new ExpressionBuilder ();
            Expression expr = builder.get ( "shortName" ).equal ( "CITI" );
            Organization brokerOrg = ( Organization ) uow.readObject ( com.integral.user.Organization.class, expr );

            builder = new ExpressionBuilder ();
            expr = builder.get ( "shortName" ).equal ( "FI1" );
            Organization fiOrg = ( Organization ) uow.readObject ( com.integral.user.Organization.class, expr );
            Organization originalBrokerOrg = fiOrg.getBrokerOrganization ();

            uow.removeAllReadOnlyClasses ();
            fiOrg = ( Organization ) uow.registerObject ( fiOrg );
            brokerOrg = ( Organization ) uow.registerObject ( brokerOrg );
            fiOrg.setBrokerOrganization ( brokerOrg );
            uow.commit ();
            uow.release ();

            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            uow.refreshObject ( fiOrg );
            assertEquals ( fiOrg.getBrokerOrganization (), brokerOrg );
            uow.release ();

            // Setting the original value after test.
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            fiOrg = ( Organization ) uow.registerObject ( fiOrg );
            originalBrokerOrg = ( Organization ) uow.registerObject ( originalBrokerOrg );
            fiOrg.setBrokerOrganization ( originalBrokerOrg );
            uow.commit ();
            uow.release ();
        }
        catch ( Exception exc )
        {
            log.error ( "OrganizationPTestC.testOrgBrokerOrganization() Exception  - " + exc );
        }
    }

    public void testOrgRealLP ( )
    {
        try
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            ExpressionBuilder builder = new ExpressionBuilder ();
            Expression expr = builder.get ( "shortName" ).equal ( "CITI" );
            Organization newRealLP = ( Organization ) uow.readObject ( com.integral.user.Organization.class, expr );

            builder = new ExpressionBuilder ();
            expr = builder.get ( "shortName" ).equal ( "FI1" );
            Organization fiOrg = ( Organization ) uow.readObject ( com.integral.user.Organization.class, expr );
            Organization originalRealLP = fiOrg.getBrokerOrganization ();

            uow.removeAllReadOnlyClasses ();
            fiOrg = ( Organization ) uow.registerObject ( fiOrg );
            newRealLP = ( Organization ) uow.registerObject ( newRealLP );
            fiOrg.setRealLP ( newRealLP );
            uow.commit ();
            uow.release ();

            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            uow.refreshObject ( fiOrg );
            uow.refreshObject ( newRealLP );
            assertEquals ( fiOrg.getRealLP (), newRealLP );
            uow.release ();

            // Setting the original value after test.
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            fiOrg = ( Organization ) uow.registerObject ( fiOrg );
            originalRealLP = ( Organization ) uow.registerObject ( originalRealLP );
            fiOrg.setRealLP ( originalRealLP );
            uow.commit ();
            uow.release ();
        }
        catch ( Exception exc )
        {
            log.error ( "OrganizationPTestC.testOrgRealLP() Exception  - " + exc );
        }
    }


    public void printOrgTenors ( )
    {

        UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
        Vector objects = uow.readAllObjects ( Organization.class );
        for ( int i = 0; i < objects.size (); i++ )
        {
            Organization org = ( Organization ) objects.get ( i );
            Collection coll = org.getSupportedTenors ();
            Iterator iter1 = coll.iterator ();
            while ( iter1.hasNext () )
            {
                Tenor ten1 = ( Tenor ) iter1.next ();
                System.out.println ( "tenor names are " + ten1 );
            }
        }
    }

    public void testRelatedOrganizationsLookup ( )
    {
        try
        {
            Organization testOrg = ( Organization ) new ReadNamedEntityC ().execute ( Organization.class, "FI1" );
            Collection<OrganizationRelationship> rels = testOrg.getOrganizationRelationships ();
            for ( OrganizationRelationship orgRel : rels )
            {
                Organization org = orgRel.getRelatedOrganization ();
                assertNotNull ( org );
                assertEquals ( "org should be part of collection", testOrg.getRelatedOrganizations ( orgRel.getClassification ().getShortName () ).contains ( org ), true );
            }

            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeReadOnlyClass ( OrganizationC.class );
            Collection<Organization> lpOrgs = testOrg.getRelatedOrganizations ( "FI_to_LP" );
            log ( "lpOrgs=" + lpOrgs );
            assertNotNull ( lpOrgs );
            assertEquals ( lpOrgs.size () > 0, true );
            Organization registeredTestOrg = ( Organization ) uow.registerObject ( testOrg );
            registeredTestOrg.update ();
            uow.commit ();

            lpOrgs = testOrg.getRelatedOrganizations ( "FI_to_LP" );
            assertNotNull ( lpOrgs );
            assertEquals ( lpOrgs.size () > 0, true );

            // refresh the organization.
            testOrg = ( Organization ) session.refreshObject ( testOrg );
            lpOrgs = testOrg.getRelatedOrganizations ( "FI_to_LP" );
            log ( "testOrg=" + testOrg + ",testOrg.lpOrgs=" + lpOrgs );
            assertNotNull ( lpOrgs );
            assertEquals ( lpOrgs.size () > 0, true );
            rels = testOrg.getOrganizationRelationships ();
            for ( OrganizationRelationship orgRel : rels )
            {
                Organization org = orgRel.getRelatedOrganization ();
                assertNotNull ( org );
                assertEquals ( "org should be part of collection", testOrg.getRelatedOrganizations ( orgRel.getClassification ().getShortName () ).contains ( org ), true );
            }
        }
        catch ( Exception e )
        {
            fail ( "Exception in related organizations lookup", e );
        }
    }

    public void testOrgFieldsPersistence ( )
    {
        Organization org = null;
        String grpNum = null;
        try
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            org = new OrganizationC ();
            assertTrue ( org.isTradingAllowed () );
            assertFalse ( org.isPersistentOrderEnabled () );
            assertTrue ( org.isCancelOrdersOnUserInactivation () );
            assertEquals ( org.getIntraFloorSTPFlag (), Organization.INTRAFLOOR_TRADE_TAKER_DOWNLOAD );
            assertFalse ( org.isMaskLPProvisioning () );
            assertFalse ( org.isMaskLPCurrencyPairs () );
            assertFalse ( org.isShowMaskLP () );
            assertFalse ( org.isShowSEF () );
            assertFalse ( org.isShowMiFID () );
            assertFalse ( org.isIncludeSDInfo () );
            assertFalse ( org.isIncludePortfolioIDInSTP () );
            assertFalse ( org.isIncludeOriginatingPortfolioIDInSTP () );

            org = ( Organization ) uow.registerObject ( org );
            org.setStatus ( 'T' );
            org.setShortName ( "Test" + System.nanoTime () );
            boolean showOrderId = org.isIncludeOrderIdInSTP ();
            boolean newShowOrderId = !showOrderId;
            boolean sendSpreads = org.isSendSpreadsInSTP ();
            boolean newSendSpreads = !sendSpreads;
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( VirtualServerC.class );
            org = ( Organization ) uow.registerObject ( org );
            boolean dailyEmail = !org.isDailyTradeEmailEnabled ();
            org.setDailyTradeEmailEnabled ( dailyEmail );
            boolean singleIntraFloorDownload = !org.isIntraFloorTradeSingleDownload ();
            org.setIntraFloorTradeSingleDownload ( singleIntraFloorDownload );
            org.setAllowCancelOfSettledTrades ( true );
            org.setIncludeOrderIdInSTP ( newShowOrderId );
            org.setSendSpreadsInSTP ( newSendSpreads );
            org.setTradingAllowed ( false );
            org.setPersistentOrderEnabled ( true );
            grpNum = org.getGroupNumber ();
            String newGrpNum = "GRP" + 20;
            org.setGroupNumber ( newGrpNum );
            VirtualServer vs = ( VirtualServer ) new ReadNamedEntityC ().execute ( VirtualServer.class, "GLOBALVS" );
            org.setVirtualServer ( vs );
            org.setExternalAuth ( 1 );
            org.setCancelOrdersOnUserInactivation ( false );
            org.setFIXSTPSession ( "TestFIXSession" );
            org.setIntraFloorSTPFlag ( Organization.INTRAFLOOR_TRADE_NO_DOWNLOAD );
            String lei = "TestLEI";
            org.setLEI ( lei );
            org.setInterimLEI ( true );
            org.setShowMaskLP ( true );
            org.setShowSEF ( true );
            org.setShowMiFID ( true );
            org.setIncludeSDInfo ( true );
            org.setMaskLPProvisioning ( true );
            org.setMaskLPCurrencyPairs ( true );
            org.setSEF ( true );
            org.setSDR ( true );
            org.setIncludePortfolioIDInSTP ( true );
            org.setIncludeOriginatingPortfolioIDInSTP ( true );
            org.setSTPOverrideOnLE ( true );
            uow.commit ();

            org = ( Organization ) getPersistenceSession ().refreshObject ( org );
            assertEquals ( org.isDailyTradeEmailEnabled (), dailyEmail );
            assertEquals ( org.isIntraFloorTradeSingleDownload (), singleIntraFloorDownload );
            assertEquals ( org.getGroupNumber (), newGrpNum );
            assertEquals ( org.isAllowCancelOfSettledTrades (), true );
            assertEquals ( org.getVirtualServer (), vs );
            assertEquals ( org.isIncludeOrderIdInSTP (), newShowOrderId );
            assertEquals ( org.isSendSpreadsInSTP (), newSendSpreads );
            assertEquals ( org.getExternalAuth (), 1 );
            assertEquals ( org.isIncludePortfolioIDInSTP (), true );
            assertEquals ( org.isIncludeOriginatingPortfolioIDInSTP (), true );

            // check the default values for a new org.
            Organization newOrg = UserFactory.newOrganization ();
            assertFalse ( newOrg.isIncludeOrderIdInSTP () );
            assertFalse ( newOrg.isSendSpreadsInSTP () );
            assertNull ( newOrg.getGroupNumber () );

            // refresh the virtual server and check for the organization in its list of orgs.
            vs = ( VirtualServer ) getPersistenceSession ().refreshObject ( vs );
            assertTrue ( vs.getOrganizations ().size () > 0 );
            assertTrue ( vs.getOrganizations ().contains ( org ) );
            assertTrue ( org.isPersistentOrderEnabled () );
            assertFalse ( org.isTradingAllowed () );
            assertFalse ( org.isCancelOrdersOnUserInactivation () );
            assertEquals ( "TestFIXSession", org.getFIXSTPSession () );
            assertEquals ( org.getIntraFloorSTPFlag (), Organization.INTRAFLOOR_TRADE_NO_DOWNLOAD );
            assertEquals ( org.getLEI (), lei );
            assertTrue ( org.isInterimLEI () );
            assertTrue ( org.isMaskLPProvisioning () );
            assertTrue ( org.isMaskLPCurrencyPairs () );
            assertTrue ( org.isShowMaskLP () );
            assertTrue ( org.isShowSEF () );
            assertTrue ( org.isShowMiFID () );
            assertTrue ( org.isIncludeSDInfo () );
            assertTrue ( org.isSEF () );
            assertTrue ( org.isSDR () );
            assertTrue ( org.isSTPOverrideOnLE () );

            log.info ( "Finished testOrgFieldsPersistence." );
        }
        catch ( Exception e )
        {
            fail ( "testOrgFieldsPersistence", e );
        }
        finally
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( VirtualServerC.class );
            org = ( Organization ) uow.registerObject ( org );
            org.setGroupNumber ( grpNum );
            uow.commit ();
        }
    }

    public void testOrgIndex ( )
    {
        try
        {
            int index1 = createOrganization ().getIndex ();
            int index2 = createOrganization ().getIndex ();
            log ( "org1Index=" + index1 + ",org2Index=" + index2 );
            assertEquals ( "index should be incremented by one.", index2 - index1, 1 );
            assertEquals ( "index should be greater than or equal to 1", index1 >= 1, true );
            assertEquals ( "index should be greater than or equal to 1", index2 >= 1, true );
        }
        catch ( Exception e )
        {
            fail ( "testOrgIndex", e );
        }
    }

    protected Organization createOrganization ( )
    {
        UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
        uow.removeAllReadOnlyClasses ();

        Organization org = new OrganizationC ();
        org = ( Organization ) uow.registerObject ( org );
        org.setShortName ( "ORGTEST-" + System.currentTimeMillis () );
        org.setStatus ( 'T' );
        uow.commit ();
        return ( Organization ) getPersistenceSession ().refreshObject ( org );
    }

    public void testSTPOriginatingDetails ( )
    {
        Organization organization = createOrganization ();
        assertTrue ( organization.getSTPOriginatingDetails () == Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );

        UnitOfWork uow = getPersistenceSession ().acquireUnitOfWork ();
        uow.removeAllReadOnlyClasses ();
        organization = ( Organization ) uow.registerObject ( organization );
        organization.setSTPOriginatingDetails ( Counterparty.COVERED_TRADE_COUNTERPARTY_DETAILS );
        uow.commit ();

        organization = ( Organization ) getPersistenceSession ().refreshObject ( organization );
        assertTrue ( organization.getSTPOriginatingDetails () == Counterparty.COVERED_TRADE_COUNTERPARTY_DETAILS );
    }

    public void testIdentityMap ( )
    {
        try
        {
            Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( Organization.class, "FI1" );
            System.out.println ( "fi1.hashcode=" + fi1.hashCode () );
            Organization fi1cache = ( Organization ) getPersistenceSession ().getIdentityMapAccessor ().getFromIdentityMap ( fi1 );
            System.out.println ( "fi1cache.hashcode=" + fi1cache.hashCode () );
            Organization refreshedOrg = ( Organization ) getPersistenceSession ().refreshObject ( fi1 );
            System.out.println ( "refreshedOrg.hashcode=" + refreshedOrg.hashCode () );
            Organization fi2cache = ( Organization ) getPersistenceSession ().getIdentityMapAccessor ().getFromIdentityMap ( fi1 );
            System.out.println ( "fi2cache.hashcode=" + fi2cache.hashCode () );
        }
        catch ( Exception e )
        {
            fail ( "testIdentityMap", e );
        }
    }

    public void testOrgRelMapLookup ( )
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( Organization.class, "FI1" );
            for ( Object or : org1.getOrganizationRelationships () )
            {
                OrganizationRelationship orgRel = ( OrganizationRelationship ) or;
                OrganizationRelationship orgRelLookup = org1.getOrgRelationship ( orgRel.getRelatedOrganization (), orgRel.getClassification ().getShortName () );
                assertEquals ( orgRel, orgRelLookup );
            }

            // now do a refresh of org and test again.
            org1 = ( Organization ) IdcUtilC.refreshObject ( org1 );
            for ( Object or : org1.getOrganizationRelationships () )
            {
                OrganizationRelationship orgRel = ( OrganizationRelationship ) or;
                OrganizationRelationship orgRelLookup = org1.getOrgRelationship ( orgRel.getRelatedOrganization (), orgRel.getClassification ().getShortName () );
                assertEquals ( orgRel, orgRelLookup );
            }
        }
        catch ( Exception e )
        {
            fail ( "testOrgRelMapLookup", e );
        }
    }

    public void testLegalEntityLookup ( )
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( Organization.class, "FI1" );
            for ( Object obj : org1.getLegalEntities () )
            {
                LegalEntity le = ( LegalEntity ) obj;
                LegalEntity leLookup = org1.getLegalEntity ( le.getShortName () );
                assertNotNull ( leLookup );
                assertTrue ( le.isSameAs ( leLookup ) );
            }

            // now do a refresh of org and test again.
            org1 = ( Organization ) IdcUtilC.refreshObject ( org1 );
            for ( Object obj : org1.getLegalEntities () )
            {
                LegalEntity le = ( LegalEntity ) obj;
                LegalEntity leLookup = org1.getLegalEntity ( le.getShortName () );
                assertNotNull ( leLookup );
                assertTrue ( le.isSameAs ( leLookup ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testLegalEntityLookup", e );
        }
    }
}
