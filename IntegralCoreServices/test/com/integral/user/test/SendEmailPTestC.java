package com.integral.user.test;

import com.integral.system.mail.SendEmailC;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDateTime;
import com.integral.time.IdcSimpleDateFormat;

public class SendEmailPTestC
        extends PTestCaseC
{
    static String name = "SendEmailPTestC Test";
    int number = 5;

    public SendEmailPTestC( String name )
    {
        super( name );
    }

    public void testsendEmail()
    {
        try
        {
            log( "Total email number to be sent =" + number );
            IdcDateTime startTime = DateTimeFactory.timeNowTime();
            IdcSimpleDateFormat timeFormat = DateTimeFactory.newIdcSimpleDateFormat( "yyyy.MM.dd hh:mm:ss a zzz" );
            String startTimeStr = timeFormat.format( startTime );
            try
            {

            log( "Start time: " + startTimeStr );
            for ( int i = 0; i < number; i++ )
            {
                String fromEmailAddress = "<EMAIL>";
                String toEmailAddress = "<EMAIL>";
                String subject = "Test mail number: " + i;
                String body = subject;
                SendEmailC.sendEmail( from<PERSON>mailAddress, toEmailAddress, null, subject, body );
                log( "Finish sending email number:" + i );
            }

                Thread.sleep( 20000 );
            }
            catch ( Exception e )
            {
                fail( "Failed to send email Exception : ", e );
            }


            IdcDateTime endTime = DateTimeFactory.timeNowTime();
            String endTimeStr = timeFormat.format( endTime );

            log( "End time: " + endTimeStr );

            log( "Total time elapsed in seconds= " + startTime.secondsTo( endTime ) );

        }
        catch ( Exception e )
        {
            fail( "SendEmail method : ", e );
        }
    }
}
