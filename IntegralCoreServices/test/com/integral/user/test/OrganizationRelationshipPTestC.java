package com.integral.user.test;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.dealing.TradeChannelSupport;
import com.integral.finance.dealing.TradeChannelSupportC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.OrganizationRelationship;
import com.integral.user.OrganizationRelationshipC;
import org.eclipse.persistence.sessions.Session;

public class OrganizationRelationshipPTestC extends PTestCaseC
{
    public static final String name = "OrganizationRelationshipPTestC";

    public OrganizationRelationshipPTestC( String name )
    {
        super( name );
    }

    public void testPersistenceFields()
    {
        try
        {
            Session session = getPersistenceSession();
            org.eclipse.persistence.sessions.UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( OrganizationRelationshipC.class );
            Organization fi1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );

            OrganizationRelationship testOrgRel = ( OrganizationRelationship ) fi1.getOrganizationRelationships().toArray()[0];
            OrganizationRelationship regOrgRel = ( OrganizationRelationship ) uow.registerObject( testOrgRel );
            TradeChannelSupport tcs = new TradeChannelSupportC();
            tcs.setESPSpotSupported(true);
            tcs.setESPOutrightSupported(false);
            tcs.setESPSwapSupported(true);
            tcs.setRFSSpotSupported(false);
            tcs.setRFSOutrightSupported(true);
            tcs.setRFSSwapSupported(false);
            tcs.setNDFSupported(true);
            tcs.setFSRSupported(false);
            tcs.setSSPSupported(true);
            regOrgRel.setTradeChannelSupport(tcs);
            regOrgRel.getCustomerPriceProvision().setAllowSpreadImprovement(true);
            regOrgRel.getCustomerPriceProvision().setSpreadImprovementPercent(new Double(10.22));
            regOrgRel.setCounterpartyEnabled( false );
            CurrencyPairGroup oneClickCcyPairGrp = CurrencyFactory.newCurrencyPairGroup();
            CurrencyPairGroup regOneClickCcyPairGrp = ( CurrencyPairGroup ) uow.registerObject( oneClickCcyPairGrp );
            regOneClickCcyPairGrp.setShortName( "TOneCcy" + System.currentTimeMillis() );
            regOrgRel.setOneClickCurrencyPairGroup(regOneClickCcyPairGrp);
            uow.commit();

            testOrgRel.setTradeChannelSupport( null );
            // refresh the trade and retrieve the maker request.
            testOrgRel = ( OrganizationRelationship ) session.refreshObject( testOrgRel );
            log( "testOrgRel=" + testOrgRel + ",testOrgRel.tradeChannelSupport=" + testOrgRel.getTradeChannelSupport() );
            log( "testOrgRel - Spread Improvement Enabled? =" + testOrgRel.getCustomerPriceProvision().isAllowSpreadImprovement() 
                   + ". Spread Improvement Enabled %age=" + testOrgRel.getCustomerPriceProvision().getSpreadImprovementPercent() );
            log("oneClickCcyPairGrp=" + testOrgRel.getOneClickCurrencyPairGroup() );
            assertEquals("orig trade channel support should not be null.", testOrgRel.getTradeChannelSupport() != null, true);
            assertTrue(testOrgRel.getCustomerPriceProvision().isAllowSpreadImprovement());
            assertFalse(testOrgRel.isCounterpartyEnabled());
            assertEquals( testOrgRel.getCustomerPriceProvision().getSpreadImprovementPercent(), new Double( 10.22 ) );
            TradeChannelSupport refreshedTrdChnlSupport = testOrgRel.getTradeChannelSupport();
            assertTrue(refreshedTrdChnlSupport.isESPSpotSupported());
            assertFalse(refreshedTrdChnlSupport.isESPOutrightSupported());
            assertTrue(refreshedTrdChnlSupport.isESPSwapSupported());
            assertFalse(refreshedTrdChnlSupport.isRFSSpotSupported());
            assertTrue(refreshedTrdChnlSupport.isRFSOutrightSupported());
            assertFalse(refreshedTrdChnlSupport.isRFSSwapSupported());
            assertTrue(refreshedTrdChnlSupport.isNDFSupported());
            assertFalse(refreshedTrdChnlSupport.isFSRSupported());
            assertTrue(refreshedTrdChnlSupport.isSSPSupported());
            assertEquals( "one click ccy pair group should not be null.", testOrgRel.getOneClickCurrencyPairGroup() != null, true );
        }
        catch ( Exception e )
        {
            fail( "Exception in trade channel support flags", e );
        }
    }
}

