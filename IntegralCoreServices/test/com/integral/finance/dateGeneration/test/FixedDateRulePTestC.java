package com.integral.finance.dateGeneration.test;

import com.integral.finance.dateGeneration.FixedDateRuleC;
import com.integral.finance.dateGeneration.HolidayDateRuleC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

/**
 * Tests persistence of FixedDateRule and related classes
 */
public class FixedDateRulePTestC extends PTestCaseC
{
    public FixedDateRulePTestC( String aName )
    {
        super( aName );
    }


    /**
     * Tests querying of FixedDateRule
     */
    public void testFixedDateRule()
    {
        // query  FixedDateRule
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'A' );

            Vector objects = uow.readAllObjects( FixedDateRuleC.class, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                FixedDateRuleC fdr = ( FixedDateRuleC ) objects.get( i );
                System.out.println( "\tretrieved FixedDateRule " + fdr + "\t\t monthsBitSet=[" + fdr.getMonthsBitSet() + ']' );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "test retrieving holiday date rule" );
        }
    }

    /**
     * Tests querying of HolidayDateRuleC
     */
    public void testHolidayDateRule()
    {
        // query  HolidayDateRuleC
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'A' );

            Vector objects = uow.readAllObjects( HolidayDateRuleC.class, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                HolidayDateRuleC hdr = ( HolidayDateRuleC ) objects.get( i );
                System.out.println( "\tretrieved HolidayDateRule " + hdr + "\t\t rollAmounts=[" + hdr.getRollAmounts() + ']' );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "test retrieving holiday date rule" );
        }
    }

}
