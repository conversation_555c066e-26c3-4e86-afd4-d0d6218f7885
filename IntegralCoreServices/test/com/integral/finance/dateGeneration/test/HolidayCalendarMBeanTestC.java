package com.integral.finance.dateGeneration.test;

// Copyright (c) 2010 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.dateGeneration.mbean.HolidayCalendarMBean;
import com.integral.finance.dateGeneration.mbean.HolidayCalendarMBeanC;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateBasisC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;

import java.util.List;

/**
 * Tester for <code>OrderServiceMBeanC</code>
 *
 * <AUTHOR> Development Corporation.
 * @see com.integral.finance.order.configuration.OrderServiceMBeanC
 */
public class HolidayCalendarMBeanTestC extends MBeanTestCaseC
{
    private HolidayCalendarMBeanC holidayCalendarMBean;

    public HolidayCalendarMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( holidayCalendarMBean, "emailFrom", HolidayCalendarMBean.UPLOAD_EMAIL_FROM_KEY, MBeanTestCaseC.STRING, false, true );
        testProperty( holidayCalendarMBean, "emailTo", HolidayCalendarMBean.UPLOAD_EMAIL_TO_KEY, MBeanTestCaseC.STRING, false, true );
        testProperty( holidayCalendarMBean, "emailSubjectPrefix", HolidayCalendarMBean.UPLOAD_EMAIL_SUBJECTPREFIX_KEY, MBeanTestCaseC.STRING, false, true );
        testProperty( holidayCalendarMBean, "uploadFile", HolidayCalendarMBean.UPLOAD_FILE_KEY, MBeanTestCaseC.STRING, false, true );
        testProperty( holidayCalendarMBean, "ftpUserName", HolidayCalendarMBean.FTP_DOWNLOAD_USERNAME, MBeanTestCaseC.STRING, false, true );
        testProperty( holidayCalendarMBean, "ftpPassword", HolidayCalendarMBean.FTP_DOWNLOAD_PASSWORD, MBeanTestCaseC.STRING, false, true );
        testProperty( holidayCalendarMBean, "ftpURL", HolidayCalendarMBean.FTP_DOWNLOAD_URL, MBeanTestCaseC.STRING, false, true );

        testProperty( holidayCalendarMBean, "spanOfHolidayCalendarYears", HolidayCalendarMBean.SPAN_OF_HOLIDAY_CALENDAR_YEARS, MBeanTestCaseC.INTEGER, false, true );
        testProperty( holidayCalendarMBean, "deleteThreshold", HolidayCalendarMBean.DELETE_THRESHOLD, MBeanTestCaseC.INTEGER, false, true );
        testProperty( holidayCalendarMBean, "deleteUnwantedHolidays", HolidayCalendarMBean.DELETE_EXTRA_HOLIDAYS, MBeanTestCaseC.BOOLEAN, false, true );
        testProperty( holidayCalendarMBean, "ftpFileListFilter", HolidayCalendarMBean.FTP_DOWNLOAD_FILE_FILTER, MBeanTestCaseC.STRING, false, true );
        testProperty( holidayCalendarMBean, "removeOlderHolidayDateRules", HolidayCalendarMBean.REMOVE_OLDER_HOLIDAY_DATE_RULES, MBeanTestCaseC.BOOLEAN, false, true );
        testProperty( holidayCalendarMBean, "removeOlderHolidayDateRulesPeriod", HolidayCalendarMBean.REMOVE_OLDER_HOLIDAY_DATE_RULES_PERIOD, MBeanTestCaseC.STRING, false, true );
    }

    public void testOrderProvidersList() throws Exception
    {
        String list = "AED~ABU,BHD~BAH";
        holidayCalendarMBean.setProperty( HolidayCalendarMBean.UPLOAD_CALENDAR_MAP_KEY, list, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().size(), 2 );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "AED" ).contains( "ABU" ), true );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "BHD" ).contains( "BAH" ), true );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "BHD1" ), null );

        list = "";
        holidayCalendarMBean.setProperty( HolidayCalendarMBean.UPLOAD_CALENDAR_MAP_KEY, list, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().size(), 0 );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "AED" ), null );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "BHD" ), null );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "BHD1" ), null );

        list = "AED~ABU";
        holidayCalendarMBean.setProperty( HolidayCalendarMBean.UPLOAD_CALENDAR_MAP_KEY, list, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().size(), 1 );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "AED" ).contains( "ABU" ), true );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "BHD" ), null );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "BHD1" ), null );

        list = "AED~ABU,AED~TOR,CAD~TOR";
        holidayCalendarMBean.setProperty( HolidayCalendarMBean.UPLOAD_CALENDAR_MAP_KEY, list, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().size(), 2 );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "AED" ).contains( "ABU" ), true );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "AED" ).contains( "TOR" ), true );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "AED" ).contains( "NYC" ), false );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "CAD" ).contains( "TOR" ), true );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "BHD" ), null );
        assertEquals( holidayCalendarMBean.getCcyHolidayCalendarMap().get( "BHD1" ), null );

    }

    public void testReadOnlyClasses()
    {
        holidayCalendarMBean.setProperty( HolidayCalendarMBean.UPLOAD_READONLY_CLASSES, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
        List<Class> readOnlyClasses = holidayCalendarMBean.getReadOnlyClasses();
        assertEquals( readOnlyClasses.size(), 0 );

        holidayCalendarMBean.setProperty( HolidayCalendarMBean.UPLOAD_READONLY_CLASSES, "com.integral.finance.fx.FXRateBasis,com.integral.finance.fx.FXRateBasisC", ConfigurationProperty.DYNAMIC_SCOPE, null );
        readOnlyClasses = holidayCalendarMBean.getReadOnlyClasses();
        assertEquals( readOnlyClasses.size(), 2 );
        assertEquals( readOnlyClasses.contains( FXRateBasis.class ), true );
        assertEquals( readOnlyClasses.contains( FXRateBasisC.class ), true );

        holidayCalendarMBean.setProperty( HolidayCalendarMBean.UPLOAD_READONLY_CLASSES, "com.integral.finance.fx.FXRateBasis,com.integral.finance.fx.FXRateBasisC1,com.integral.finance.fx.FXRateBasisC", ConfigurationProperty.DYNAMIC_SCOPE, null );
        readOnlyClasses = holidayCalendarMBean.getReadOnlyClasses();
        assertEquals( readOnlyClasses.size(), 2 );
        assertEquals( readOnlyClasses.contains( FXRateBasis.class ), true );
        assertEquals( readOnlyClasses.contains( FXRateBasisC.class ), true );

    }

    protected void setUp() throws Exception
    {
        super.setUp();
        holidayCalendarMBean = HolidayCalendarMBeanC.getInstance();
    }
}
