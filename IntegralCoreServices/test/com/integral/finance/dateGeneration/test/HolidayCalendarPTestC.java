package com.integral.finance.dateGeneration.test;

import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.dateGeneration.HolidayDateRuleCalendar;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

/**
 * Tests persistence of HolidayCalendarC and related classes
 */
public class HolidayCalendarPTestC
        extends PTestCaseC
{
    public HolidayCalendarPTestC( String aName )
    {
        super( aName );
    }


    /**
     * Tests querying of HolidayCalendar
     */
    public void testQueryHolidayDateRule()
    {
        // query  HolidayCalendar
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'A' );

            Vector objects = ( Vector ) uow.readAllObjects( HolidayCalendar.class, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                HolidayCalendar hc = ( HolidayCalendar ) objects.get( i );
                System.out.println( "\tretrieved holiday calendar " + hc );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "test retrieving holiday date rule" );
        }

        // query  HolidayDateRuleCalendar
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'A' );

            Vector objects = ( Vector ) uow.readAllObjects( HolidayDateRuleCalendar.class, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                HolidayDateRuleCalendar hdrc = ( HolidayDateRuleCalendar ) objects.get( i );
                System.out.println( "\tretrieved holiday date-rule calendar " + hdrc );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "test retrieving holiday date rule" );
        }

    }

    /**
     * Tests changing of HolidayDateRule on a HolidayCalendar and persisting it
     */
    public void testModifyHolidayDateRule()
    {
        HolidayDateRuleCalendar hdrc = null;

        // query  HolidayDateRuleCalendar
        try
        {
            Session session = getPersistenceSession();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr1 = eb.get( "status" ).equal( 'A' );
            Expression expr2 = eb.get( "shortName" ).equal( "TAL" );
            Expression expr = expr1.and( expr2 );

            hdrc = ( HolidayDateRuleCalendar ) session.readObject( HolidayDateRuleCalendar.class, expr );
            log( "\tretrieved holiday date-rule calendar " + hdrc );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "failed retrieving holiday date rule TAL" );
        }

        // modify  HolidayDateRuleCalendar
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();

            HolidayDateRuleCalendar hdrc1 = ( HolidayDateRuleCalendar ) uow.registerObject( hdrc );
            hdrc1.update();

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "failed modifying holiday date rule TAL" );
        }

    }


}
