package com.integral.finance.financialEventParameters.test;


import com.integral.finance.financialEventParameters.FinancialEventParametersCustomFieldC;
import com.integral.finance.financialEventParameters.FinancialEventParametersFactory;
import com.integral.persistence.CustomField;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


public class FinancialEventParamtersCustomFieldPTestC
        extends PTestCaseC
{
    static String name = "FinancialEventParamtersCustomField Test";

    public FinancialEventParamtersCustomFieldPTestC( String name )
    {
        super( name );
    }

    public void testLookup()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );
            Vector actions = this.getPersistenceSession().readAllObjects( FinancialEventParametersCustomFieldC.class, expr );
            for ( int i = 0; i < actions.size(); i++ )
            {
                CustomField cf = ( CustomField ) actions.elementAt( i );
                log( "\t objectID = " + cf.getObjectID() );
                log( "\t object = " + cf );
                log( "\t key: " + cf.getKey() );
                log( "\t value: " + cf.getValue() );

            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTest" );
        }
    }

    public void testInsert()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            CustomField cf = FinancialEventParametersFactory.newFinancialEventParametersCustomField();
            cf.setKey( "TEST" );
            cf.setValue( "TESTTEXT" );
            cf.setStatus( 'T' );

            uow.registerObject( cf );
            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }
}
