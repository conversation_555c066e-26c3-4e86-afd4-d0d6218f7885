package com.integral.finance.positions.test;

import com.integral.finance.positions.configuration.PositionServiceConfigurationC;
import com.integral.finance.positions.configuration.PositionServiceConfigurationFactory;
import com.integral.finance.positions.configuration.PositionServiceConfigurationMBean;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

/**
 * <AUTHOR> Development Corporation.
 */
public class PositionServiceConfigurationMBeanTestC extends MBeanTestCaseC
{
    PositionServiceConfigurationC posSvcConfig = ( PositionServiceConfigurationC ) PositionServiceConfigurationFactory.getPositionServiceConfigurationMBean();

    public PositionServiceConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( posSvcConfig, "posServiceEnabled", PositionServiceConfigurationMBean.POS_SERVICE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( posSvcConfig, "multiAppPositionInfoEnabled", PositionServiceConfigurationMBean.MULTI_APP_POSITION_INFO_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( posSvcConfig, "postCommitJMSSendEnabled", PositionServiceConfigurationMBean.POSITION_SERVICE_POST_COMMIT_JMS_ENABLED, MBeanTestCaseC.BOOLEAN );
    }

    public void testPositionServiceEnabled()
    {
        try
        {
            Organization nullOrg = null;
            assertFalse( posSvcConfig.isOrgEnabledForPos( nullOrg ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "DFI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "AXLP" );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org1 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org2 ) );

            // now set the global property
            posSvcConfig.setProperty( PositionServiceConfigurationMBean.POS_SERVICE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org1 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org2 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( nullOrg ) );

            posSvcConfig.setProperty( PositionServiceConfigurationMBean.POS_SERVICE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org1 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org2 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( nullOrg ) );


            posSvcConfig.setProperty( PositionServiceConfigurationMBean.POS_SERVICE_ORG_POS + "DFI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            posSvcConfig.setProperty( PositionServiceConfigurationMBean.POS_SERVICE_ORG_POS + "AXLP", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( posSvcConfig.isOrgEnabledForPos( org1 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org2 ) );

            posSvcConfig.setProperty( PositionServiceConfigurationMBean.POS_SERVICE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( posSvcConfig.isOrgEnabledForPos( org1 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org2 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( nullOrg ) );

            posSvcConfig.setProperty( PositionServiceConfigurationMBean.POS_SERVICE_ORG_POS + "DFI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            posSvcConfig.setProperty( PositionServiceConfigurationMBean.POS_SERVICE_ORG_POS + "AXLP", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org1 ) );
            assertFalse( posSvcConfig.isOrgEnabledForPos( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testPositionServiceEnabled" );
        }
    }


}
