package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationMBean;
import com.integral.finance.creditLimit.handler.CreditNZDEndOfDayHandler;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import com.integral.workflow.dealing.DealingLimit;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Tests the credit limit workflow when currency pair exemption is used.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitTenorCoefficientPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Tenor Coefficient Test";

    public CreditLimitTenorCoefficientPTestC( String name )
    {
        super( name );
    }

    public void testCreditTenorCoefficientLookupAtCptyLevel()
    {
        try
        {
            init( lpUser );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            FXRateBasis rb = stdQuoteConv.getFXRateBasis( eur, usd );
            IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();

            String testProfile = "Test" + System.nanoTime();
            String[] tenors = new String[]{"SPOT", "1d", "1W", "2W", "3W", "1m", "2m", "3m", "4m", "5m", "6m", "9m", "1y", "2y"};
            double[] coefficients = new double[tenors.length];
            double percent = 0.0;

            Map<String, Double> coefficientsMap = new HashMap<String, Double>();
            Map<String, IdcDate> valueDateMap = new HashMap<String, IdcDate>();

            for ( int i = 0; i < tenors.length; i++ )
            {
                coefficients[i] = percent;
                coefficientsMap.put( tenors[i], coefficients[i] );
                valueDateMap.put( tenors[i], rb.getValueDate( tradeDate, new Tenor( tenors[i] ) ) );
                percent += 10.0;
            }

            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( testProfile, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            CreditLimitRule clr = CreditUtilC.getCreditLimitRule ( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );

            for ( int i = 0; i < tenors.length; i++ )
            {
                Double tenorCoefficient = CreditUtilC.getTenorCoefficient( cclr, clr, valueDateMap.get( tenors[i] ), eur, usd );
                assertEquals( tenorCoefficient, coefficients[i] / 100.0 );
            }

            // check for value date beyond max tenor in the tenor parameters.
            IdcDate date1 = rb.getValueDate( tradeDate, new Tenor( "3Y" ) );
            Double tc1 = CreditUtilC.getTenorCoefficient( cclr, clr, date1, eur, usd );
            assertEquals( tc1, CreditLimit.TENOR_COEFFICIENT_NA );

            // check for value date before the spot dates.
            IdcDate date2 = rb.getValueDate( tradeDate, new Tenor( "TOD" ) );
            Double tc2 = CreditUtilC.getTenorCoefficient( cclr, clr, date2, eur, usd );
            assertEquals( tc2, coefficientsMap.get( "SPOT" ) );

            // now remove the tenor profile.
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            for ( int i = 0; i < tenors.length; i++ )
            {
                Double tenorCoefficient = CreditUtilC.getTenorCoefficient( cclr, clr, valueDateMap.get( tenors[i] ), eur, usd );
                assertEquals( tenorCoefficient, CreditLimit.DEFAULT_TENOR_COEFFICIENT );
            }

        }
        catch ( Exception e )
        {
            fail( "testCreditTenorCoefficientLookupAtCptyLevel", e );
        }
        finally
        {
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
        }
    }

    public void testCreditTenorCoefficientLookupAtProviderLevel()
    {
        try
        {
            init( lpUser );

            creditAdminSvc.setCreditTenorProfile(lpOrg, lpTpForFi, null);
            creditAdminSvc.setUsePFEConfiguration( lpOrg, false );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, false );

            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            FXRateBasis rb = stdQuoteConv.getFXRateBasis( eur, usd );
            IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();

            String testProfile = "Test" + System.nanoTime();
            String[] tenors = new String[]{"SPOT", "1d", "1W", "2W", "3W", "1m", "2m", "3m", "4m", "5m", "6m", "9m", "1y", "2y"};
            double[] coefficients = new double[tenors.length];
            double percent = 0.0;

            Map<String, Double> coefficientsMap = new HashMap<String, Double>();
            Map<String, IdcDate> valueDateMap = new HashMap<String, IdcDate>();

            for ( int i = 0; i < tenors.length; i++ )
            {
                coefficients[i] = percent;
                coefficientsMap.put( tenors[i], coefficients[i] );
                valueDateMap.put( tenors[i], rb.getValueDate( tradeDate, new Tenor( tenors[i] ) ) );
                percent += 10.0;
            }

            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( testProfile, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile(lpOrg, tenorProfile);
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, tenorProfile);

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            CreditLimitRule clr = CreditUtilC.getCreditLimitRule ( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );

            for ( int i = 0; i < tenors.length; i++ )
            {
                Double tenorCoefficient = CreditUtilC.getTenorCoefficient( cclr, clr, valueDateMap.get( tenors[i] ), eur, usd );
                assertEquals( tenorCoefficient, coefficients[i] / 100.0 );
            }

            // check for value date beyond max tenor in the tenor parameters.
            IdcDate date1 = rb.getValueDate( tradeDate, new Tenor( "3Y" ) );
            Double tc1 = CreditUtilC.getTenorCoefficient( cclr, clr, date1, eur, usd );
            assertEquals( tc1, CreditLimit.TENOR_COEFFICIENT_NA );

            // check for value date before the spot dates.
            IdcDate date2 = rb.getValueDate( tradeDate, new Tenor( "TOD" ) );
            Double tc2 = CreditUtilC.getTenorCoefficient( cclr, clr, date2, eur, usd );
            assertEquals( tc2, coefficientsMap.get( "SPOT" ) );

            // now remove the tenor profile.
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, null);
            for ( int i = 0; i < tenors.length; i++ )
            {
                Double tenorCoefficient = CreditUtilC.getTenorCoefficient( cclr, clr, valueDateMap.get( tenors[i] ), eur, usd );
                assertEquals( tenorCoefficient, CreditLimit.DEFAULT_TENOR_COEFFICIENT );
            }

        }
        catch ( Exception e )
        {
            fail( "testCreditTenorCoefficientLookupAtProviderLevel", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, null);
            creditAdminSvc.setCreditTenorProfile(lpOrg, lpTpForFi, null);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, true);
            creditAdminSvc.setUsePFEConfiguration( lpOrg, true );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, true );
        }
    }

    public void testTenorCoefficientOnTakeCreditWithoutSubscription()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit(fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt / 10.0 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }

            IdcDate valueDate1 = stdQuoteConv.getFXRateBasis( "EUR", "USD" ).getValueDate(tradeDate, new Tenor("9m"));
            Trade trade1 = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate1);
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals("Message1 success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS);
            for ( CreditUtilizationEvent cue : cwm1.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.5 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testTenorCoefficientOnTakeCreditWithoutSubscription", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
        }
    }

    public void testTenorCoefficientOnTakeCreditWithSubscription()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, false);

            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));
            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("EUR"), CurrencyFactory.getCurrency("USD"));
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("GBP"), CurrencyFactory.getCurrency("JPY"));

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt / 10.0 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }

            IdcDate valueDate1 = stdQuoteConv.getFXRateBasis( "EUR", "USD" ).getValueDate( tradeDate, new Tenor( "9m" ) );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate1 );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message1 success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm1.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.5 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testTenorCoefficientOnTakeCreditWithSubscription", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
        }
    }

    public void testTenorCoefficientOnTakeCreditOnTodayTrade()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue(cue.getPrincipal() == 0.0);
                    assertTrue( cue.getPrice() == 0.0 );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }

            double[] coefficients1 = new double[]{1.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile1 = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients1 );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile1 );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile1 );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message1 success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm1.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.01 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testTenorCoefficientOnTakeCreditOnTodayTrade", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
        }
    }

    public void testTenorCoefficientOnUndoCreditWithSubscription()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, false );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, false );

            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );

            double availableLimit = getAvailableCreditLimit(lpTpForFi, spotDate);
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit(fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS);

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Undo Message success status=" + undoCwm.getStatus(), undoCwm.getStatus(), MessageStatus.SUCCESS );
            double availableLimit1 = getAvailableCreditLimit(lpTpForFi, spotDate);
            assertTrue(availableLimit == availableLimit1);
        }
        catch ( Exception e )
        {
            fail( "testTenorCoefficientOnTakeCreditWithSubscription", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, true );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, true );
        }
    }

    public void testTenorCoefficientOnTakeCreditOnSwapTrade()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade trade = prepareSwapTrade(tradeAmt, tradeAmt, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays(2));
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    if ( cue.getSettlementDate().isSameAs( spotDate ) )
                    {
                        assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt / 10.0 ) ) < CREDIT_CALCULATION_MINIMUM );
                    }
                    else
                    {
                        assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.2 ) ) < CREDIT_CALCULATION_MINIMUM );
                    }
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }

            IdcDate valueDate1 = stdQuoteConv.getFXRateBasis( "EUR", "USD" ).getValueDate(tradeDate, new Tenor("9m"));
            Trade trade1 = prepareSwapTrade(tradeAmt, tradeAmt, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], valueDate1, valueDate1.addDays(10));
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message1 success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS);
            for ( CreditUtilizationEvent cue : cwm1.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    if ( cue.getSettlementDate().isSameAs( valueDate1 ) )
                    {
                        assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.5 ) ) < CREDIT_CALCULATION_MINIMUM );
                    }
                    else
                    {
                        assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 1.0 ) ) < CREDIT_CALCULATION_MINIMUM );
                    }
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testTenorCoefficientOnTakeCreditOnSwapTrade", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
        }
    }

    public void testCreditRejectionBeyondMaxTenor()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));
            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));

            double tradeAmt = 1000;
            IdcDate valueDate = stdQuoteConv.getFXRateBasis( "EUR", "USD" ).getValueDate(tradeDate, new Tenor("13m"));
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit(fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message credit reject status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            Trade trade1 = prepareSwapTrade( tradeAmt, tradeAmt, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], valueDate, valueDate.addDays( 10 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message1 success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            fail( "testCreditRejectionBeyondMaxTenor", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
        }
    }

    public void testQueryOrgsSetWithTenorProfile()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            CreditTenorProfile tenorProfile1 = createCreditTenorProfileWithTenorParam( "Test1" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile(lpOrg, tenorProfile1);
            creditAdminSvc.setDefaultCreditTenorProfile(fiOrg, tenorProfile1);

            Collection<Organization> ctpOrgs = CreditUtilC.getOrganizationsWithPFESet();
            log( "orgs set with credit tenor profile=" + ctpOrgs );
            assertNotNull( ctpOrgs );
            assertTrue( !ctpOrgs.isEmpty() );
            assertTrue(ctpOrgs.size() >= 2);
            assertTrue( IdcUtilC.contains( ctpOrgs, lpOrg ) );
            assertTrue(IdcUtilC.contains(ctpOrgs, fiOrg));
        }
        catch ( Exception e )
        {
            fail( "testQueryOrgsSetWithTenorProfile", e );
        }
        finally
        {
            creditAdminSvc.setCreditTenorProfile(lpOrg, lpTpForFi, null);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, true);
            creditAdminSvc.setDefaultCreditTenorProfile(fiOrg, null);
        }
    }

    /**
     * This test case tests that updating of credit tenor profile coefficients is reflected in the next trade.
     */
    public void testTenorCoefficientOnTakeCreditWithCreditTenorProfileUpdate()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            String tenorProfileName = "Test" + System.nanoTime();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( cue.getPrincipal() == 0.0 );
                    assertTrue( cue.getPrice() == 0.0 );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }

            CreditTenorProfile tenorProfileCopy = tenorProfile.cloneCreditTenorProfile( tenorProfileName );
            for ( CreditTenorParameters ctp : tenorProfileCopy.getCreditTenorParameters() )
            {
                if ( ctp.getTenor().isToday() )
                {
                    ctp.setUtilizationPercent( 2.0 );
                    break;
                }
            }
            creditAdminSvc.updateCreditTenorProfile( lpOrg, tenorProfileCopy );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message1 success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm1.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.02 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testTenorCoefficientOnTakeCreditWithCreditTenorProfileUpdate", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, null);
            creditAdminSvc.setCreditTenorProfile(lpOrg, lpTpForFi, null);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, true);
        }
    }

    /**
     * This test case tests that credit tenor coefficients are correctly applied when updateCreditUtilizationAmount is called on verification.
     */
    public void testTenorCoefficientOnUpdateCreditUtilizationAmount()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            String tenorProfileName = "Test" + System.nanoTime();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000);

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEventsForNotification() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs(cue.getPrincipal() - tradeAmt * 0.1) < CREDIT_CALCULATION_MINIMUM );
                    assertTrue( Math.abs(cue.getPrice() - (tradeAmt / bidRates[0]) * 0.1) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }

            // change the trade amount
            double offset = -500;
            updateTradeAmount( trade, tradeAmt + offset, true, false );

            CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount(fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Update Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );

            for ( CreditUtilizationEvent cue : cwm1.getCreditUtilizationEventsForNotification() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "Updated cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue(Math.abs(cue.getPrincipal() - (tradeAmt + offset) * 0.1) < CREDIT_CALCULATION_MINIMUM);
                    assertTrue( Math.abs( cue.getPrice() - ( ( tradeAmt + offset ) / bidRates[0] ) * 0.1 ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt + offset ) ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testTenorCoefficientOnUpdateCreditUtilizationAmount", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
        }
    }

    public void testTenorCoefficientOnTakeBilateralCreditWithSubscription()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "TestLP" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, tenorProfile );

            double[] coefficients1 = new double[]{1.0, 20.0, 30.0, 40.0, 60.0, 160.0};
            CreditTenorProfile tenorProfile1 = createCreditTenorProfileWithTenorParam( "TestFI" + System.nanoTime(), tenors, coefficients1 );
            creditAdminSvc.addCreditTenorProfile( fiOrg, tenorProfile1 );
            creditAdminSvc.setDefaultCreditTenorProfile(fiOrg, tenorProfile1);

            creditAdminSvc.setUseDefaultTenorProfile(fiOrg, fiTpForLp, true);
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, false );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, false );


            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));
            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));
            creditAdminSvc.setCreditLimit(fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency(fiOrg, lpOrg, fiTpForLp));
            creditAdminSvc.setCreditLimit(fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency(fiOrg, lpOrg, fiTpForLp));

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("GBP"), CurrencyFactory.getCurrency("JPY"));

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit(fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log("cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice());
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.1 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.2 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testTenorCoefficientOnTakeBilateralCreditWithSubscription", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );

            creditAdminSvc.setDefaultCreditTenorProfile( fiOrg, null );
            creditAdminSvc.setCreditTenorProfile( fiOrg, fiTpForLp, null );
            creditAdminSvc.setUseDefaultTenorProfile( fiOrg, fiTpForLp, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, true );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, true );
        }
    }

    public void testFXSwapAvailableCreditCheckWithTenorCoefficients()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 10.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "TestLP" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, tenorProfile );

            double[] coefficients1 = new double[]{1.0, 10.0, 10.0, 40.0, 60.0, 160.0};
            CreditTenorProfile tenorProfile1 = createCreditTenorProfileWithTenorParam( "TestFI" + System.nanoTime(), tenors, coefficients1 );
            creditAdminSvc.addCreditTenorProfile(fiOrg, tenorProfile1);
            creditAdminSvc.setDefaultCreditTenorProfile(fiOrg, tenorProfile1);

            creditAdminSvc.setUseDefaultTenorProfile(fiOrg, fiTpForLp, true);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, true);
            creditAdminSvc.setUsePFEConfiguration( lpOrg, false );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, false );


            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double dailyLimit = 1000000;
            double aggLimit = dailyLimit;
            for ( CreditUtilizationCalculator aggCalc : CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies() )
            {
                setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, aggLimit );
                setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, aggLimit );

                for ( CreditUtilizationCalculator dailyCalc : CreditUtilC.getSupportedDailyNettingMethodologies() )
                {

                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, dailyLimit );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, dailyLimit );

                    double tradeAmt = dailyLimit / 10;
                    FXTrade trade = prepareSwapTrade( tradeAmt, tradeAmt, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
                    CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade, lpLe, fiLe );
                    log( "cwm=" + cwm );
                    assertNull( cwm );


                    double tradeAmt1 = dailyLimit * 100;
                    FXTrade trade1 = prepareSwapTrade( tradeAmt1, tradeAmt1, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
                    CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade1, lpLe, fiLe );
                    log( "cwm1=" + cwm1 );
                    assertNotNull( cwm1 );

                    // now do a trade in anticipation of tenor coefficients properly applied.
                    double tradeAmt2 = dailyLimit * 4;
                    if ( aggCalc.isSameAs( CreditLimitConstants.AGGREGATE_NPR_SETTLEMENT_CALCULATOR ) || aggCalc.isSameAs( CreditLimitConstants.NET_OPEN_POSITION_CALCULATOR ) )
                    {
                        tradeAmt2 = dailyLimit * 8;
                    }
                    FXTrade trade2 = prepareSwapTrade( tradeAmt2, tradeAmt2, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
                    CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade2, lpLe, fiLe );
                    log( "cwm2=" + cwm2 );
                    assertNull( cwm2 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testFXSwapAvailableCreditCheckWithTenorCoefficients", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );

            creditAdminSvc.setDefaultCreditTenorProfile( fiOrg, null );
            creditAdminSvc.setCreditTenorProfile( fiOrg, fiTpForLp, null );
            creditAdminSvc.setUseDefaultTenorProfile( fiOrg, fiTpForLp, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, true );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, true );
        }
    }

    public void testAvailableCreditLimitWithTenorCoefficients()
    {
        try
        {
            init( lpUser );

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 10.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "TestLP" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, tenorProfile);

            double[] coefficients1 = new double[]{0.0, 10.0, 10.0, 40.0, 60.0, 160.0};
            CreditTenorProfile tenorProfile1 = createCreditTenorProfileWithTenorParam( "TestFI" + System.nanoTime(), tenors, coefficients1 );
            creditAdminSvc.addCreditTenorProfile(fiOrg, tenorProfile1);
            creditAdminSvc.setDefaultCreditTenorProfile(fiOrg, tenorProfile1);

            creditAdminSvc.setUseDefaultTenorProfile(fiOrg, fiTpForLp, true);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, true);
            creditAdminSvc.setUsePFEConfiguration( lpOrg, false );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, false );


            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            double limit = 1000000;
            for ( CreditUtilizationCalculator aggCalc : CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies() )
            {
                setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, limit );
                setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, limit );

                for ( CreditUtilizationCalculator dailyCalc : CreditUtilC.getSupportedDailyNettingMethodologies() )
                {

                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, limit );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, limit );

                    DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, spotDate.addDays( 2 ), usd, jpy, true, true );
                    log( "dl=" + dl );
                    assertTrue(dl.getBidLimit() > limit * 9);
                    assertTrue(dl.getOfferLimit() > limit * 9);

                    DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, tradeDate, usd, jpy, true, true );
                    log( "dl1=" + dl1 );
                    assertNull( dl1 );

                    DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit(lpLe, fiLe, spotDate.addDays(500), usd, jpy, true, true);
                    log( "dl2=" + dl2 );
                    assertTrue(dl2.getBidLimit() == 0.0);
                    assertTrue(dl2.getOfferLimit() == 0.0);
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testAvailableCreditLimitWithTenorCoefficients", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );

            creditAdminSvc.setDefaultCreditTenorProfile( fiOrg, null );
            creditAdminSvc.setCreditTenorProfile( fiOrg, fiTpForLp, null );
            creditAdminSvc.setUseDefaultTenorProfile( fiOrg, fiTpForLp, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, true );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, true );
        }
    }

    public void testResetTenorCoefficientsAtEndOfDay()
    {
        Currency eur = CurrencyFactory.getCurrency("EUR");
        Currency usd = CurrencyFactory.getCurrency("USD");
        Currency gbp = CurrencyFactory.getCurrency("GBP");
        Currency jpy = CurrencyFactory.getCurrency("JPY");
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "TOM", "SPOT", "1d", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile(lpOrg, tenorProfile);
            creditAdminSvc.setCreditTenorProfile(lpOrg, lpTpForDd, tenorProfile);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForDd, false);

            removeCreditLimitSubscriptions(lpLe, lpTpForDd);


            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, ddLe, eur, usd);
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, ddLe, gbp, jpy);

            FXRateBasis rb = stdQuoteConv.getFXRateBasis(eur, usd);
            IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate spotDate = rb.getValueDate(tradeDate, Tenor.SPOT_TENOR);
            CreditUtilizationCacheEntry cce = CreditUtilizationManagerC.getInstance().getCreditUtilizationCacheEntry(lpLe, ddOrg, lpTpForDd, spotDate, SUBSCRIBE_EVENT);
            assertNotNull(cce);
            double tc0 = cce.getTenorCoefficient( (CreditLimitRule) cce.getCounterpartyCreditLimitRule().getChildRule( GROSS_NOTIONAL_RULE_SHORT_NAME ), rb.getCurrencyPair() );
            assertEquals(tc0, 0.2);

            rb.getFXBusinessCalendar().setLag(0);
            rb.resetTradeDateCache();
            IdcDate newSpotDate = rb.getValueDate( tradeDate, Tenor.SPOT_TENOR );

            // now reset the cache.
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateAllCreditTenorProfiles();
            double tc1 = cce.getTenorCoefficient( (CreditLimitRule) cce.getCounterpartyCreditLimitRule().getChildRule( GROSS_NOTIONAL_RULE_SHORT_NAME ), rb.getCurrencyPair() );
            log( "new tenor coefficient=" + tc1 + ",newSpotDate=" + newSpotDate + ",oldSpotDate=" + spotDate );
            assertTrue( "new tenor coefficient=" + tc1 + ",newSpotDate=" + newSpotDate + ",oldSpotDate=" + spotDate , tc1 > 0.2 );

        }
        catch ( Exception e )
        {
            fail( "testResetTenorCoefficientsAtEndOfDay", e );
        } finally {
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, null);
            creditAdminSvc.setCreditTenorProfile(lpOrg, lpTpForDd, null);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForDd, true);
            IdcUtilC.refreshObject(stdQuoteConv.getFXRateBasis(eur, usd));
        }
    }

    public void testNZDResetTenorCoefficientsAtEndOfDay()
    {
        Currency nzd = CurrencyFactory.getCurrency("EUR");
        Currency usd = CurrencyFactory.getCurrency("USD");
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "TOM", "SPOT", "1d", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile(lpOrg, tenorProfile);
            creditAdminSvc.setCreditTenorProfile(lpOrg, lpTpForDd, tenorProfile);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForDd, false);

            removeCreditLimitSubscriptions(lpLe, lpTpForDd);


            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, ddLe, nzd, usd );

            FXRateBasis rb = stdQuoteConv.getFXRateBasis(nzd, usd);
            IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate spotDate = rb.getValueDate(tradeDate, Tenor.SPOT_TENOR);
            CreditUtilizationCacheEntry cce = CreditUtilizationManagerC.getInstance().getCreditUtilizationCacheEntry(lpLe, ddOrg, lpTpForDd, spotDate, SUBSCRIBE_EVENT);
            assertNotNull(cce);
            double tc0 = cce.getTenorCoefficient( (CreditLimitRule) cce.getCounterpartyCreditLimitRule().getChildRule( GROSS_NOTIONAL_RULE_SHORT_NAME ), rb.getCurrencyPair() );
            assertEquals(tc0, 0.2);

            rb.getFXBusinessCalendar().setLag(0);
            rb.resetTradeDateCache();
            IdcDate newSpotDate = rb.getValueDate( tradeDate, Tenor.SPOT_TENOR );

            // now reset the cache.
            new CreditNZDEndOfDayHandler().handle( null );

            double tc1 = cce.getTenorCoefficient( (CreditLimitRule) cce.getCounterpartyCreditLimitRule().getChildRule( GROSS_NOTIONAL_RULE_SHORT_NAME ), rb.getCurrencyPair() );
            log( "new tenor coefficient=" + tc1 + ",newSpotDate=" + newSpotDate + ",oldSpotDate=" + spotDate );
            assertTrue( "new tenor coefficient=" + tc1 + ",newSpotDate=" + newSpotDate + ",oldSpotDate=" + spotDate , tc1 > 0.2 );

        }
        catch ( Exception e )
        {
            fail( "testNZDResetTenorCoefficientsAtEndOfDay", e );
        } finally {
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, null);
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, null);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForDd, true);
            IdcUtilC.refreshObject( stdQuoteConv.getFXRateBasis(nzd, usd) );
        }
    }

    public void testAllowTodaysTradeWithZeroUtilizationOnCreditBreach()
    {
        try
        {
            init( lpUser );

            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );


            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("EUR"), CurrencyFactory.getCurrency("USD"));
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("GBP"), CurrencyFactory.getCurrency("JPY"));

            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            String[] tenors = new String[]{"TOD", "TOM", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, false);

            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000);

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus()  );

            WatchPropertyC.update( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH, "true", ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + lpOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE);
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message status=" + cwm2.getStatus(), MessageStatus.SUCCESS, cwm2.getStatus() );

            WatchPropertyC.update( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + lpOrg.getShortName(), "false", ConfigurationProperty.DYNAMIC_SCOPE);
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message status=" + cwm3.getStatus(), MessageStatus.FAILURE, cwm3.getStatus() );

            WatchPropertyC.update( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + lpOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE);
            Trade trade4 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message status=" + cwm4.getStatus(), MessageStatus.SUCCESS, cwm4.getStatus() );

        }
        catch ( Exception e )
        {
            fail( "testAllowTodaysTradeWithZeroUtilizationOnCreditBreach", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000);
            WatchPropertyC.update(CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH, "false", ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update(CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + lpOrg.getShortName(), "false", ConfigurationProperty.DYNAMIC_SCOPE);
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
        }
    }

    public void testAllowPostTodaysTradeWithZeroUtilizationOnCreditBreach()
    {
        try
        {
            init( lpUser );

            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000000 );
            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("EUR"), CurrencyFactory.getCurrency("USD"));
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("GBP"), CurrencyFactory.getCurrency("JPY"));

            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            String[] tenors = new String[]{"TOD", "TOM", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{100.0, 100.0, 10.0, 20.0, 0.0, 0.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, false);

            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000);

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate.addMonths ( 7 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus()  );

            WatchPropertyC.update( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH, "true", ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update(CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + lpOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE);
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate.addMonths ( 7 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message status=" + cwm2.getStatus(), MessageStatus.SUCCESS, cwm2.getStatus() );

            WatchPropertyC.update( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + lpOrg.getShortName(), "false", ConfigurationProperty.DYNAMIC_SCOPE);
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate.addMonths ( 7 ) );
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message status=" + cwm3.getStatus(), MessageStatus.FAILURE, cwm3.getStatus() );

            WatchPropertyC.update( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + lpOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE);
            Trade trade4 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate.addMonths ( 7 ) );
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message status=" + cwm4.getStatus(), MessageStatus.SUCCESS, cwm4.getStatus() );
        }
        catch ( Exception e )
        {
            fail( "testAllowPostTodaysTradeWithZeroUtilizationOnCreditBreach", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000);
            WatchPropertyC.update(CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH, "false", ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update(CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + lpOrg.getShortName(), "false", ConfigurationProperty.DYNAMIC_SCOPE);
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
        }
    }

    public void testAvailableCreditLimitWithZeroTenorCoefficientsAtLPOnly()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 0.0, 0.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "TestLP" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, tenorProfile);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, true);
            creditAdminSvc.setUsePFEConfiguration( lpOrg, false );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            double limit = 1000000;
            for ( CreditUtilizationCalculator aggCalc : CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies() )
            {
                setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, limit );

                for ( CreditUtilizationCalculator dailyCalc : CreditUtilC.getSupportedDailyNettingMethodologies() )
                {

                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, limit );

                    DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, spotDate.addDays( 2 ), usd, jpy, true, true );
                    log( "dl=" + dl );
                    assertNull ( dl );

                    DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, tradeDate, usd, jpy, true, true );
                    log( "dl1=" + dl1 );
                    assertNull( dl1 );

                    DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit(lpLe, fiLe, spotDate.addDays(500), usd, jpy, true, true);
                    log( "dl2=" + dl2 );
                    assertNotNull ( dl2 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testAvailableCreditLimitWithZeroTenorCoefficientsAtLPOnly", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, true );
        }
    }


    public void testAvailableCreditLimitWithZeroTenorCoefficientsOnOneSide()
    {
        try
        {
            init( lpUser );

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 0.0, 0.0, 30.0, 50.0, 100.0};
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( "TestLP" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, tenorProfile);

            double[] coefficients1 = new double[]{0.0, 10.0, 10.0, 40.0, 60.0, 160.0};
            CreditTenorProfile tenorProfile1 = createCreditTenorProfileWithTenorParam( "TestFI" + System.nanoTime(), tenors, coefficients1 );
            creditAdminSvc.addCreditTenorProfile(fiOrg, tenorProfile1);
            creditAdminSvc.setDefaultCreditTenorProfile(fiOrg, tenorProfile1);

            creditAdminSvc.setUseDefaultTenorProfile(fiOrg, fiTpForLp, true);
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, true);
            creditAdminSvc.setUsePFEConfiguration( lpOrg, false );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, false );


            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            double limit = 1000000;
            for ( CreditUtilizationCalculator aggCalc : CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies() )
            {
                setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, limit );
                setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, limit );

                for ( CreditUtilizationCalculator dailyCalc : CreditUtilC.getSupportedDailyNettingMethodologies() )
                {

                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, limit );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, limit );

                    DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, spotDate.addDays( 2 ), usd, jpy, true, true );
                    log( "dl=" + dl );
                    assertTrue(dl.getBidLimit() > limit * 9);
                    assertTrue(dl.getOfferLimit() > limit * 9);

                    DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, tradeDate, usd, jpy, true, true );
                    log( "dl1=" + dl1 );
                    assertNull( dl1 );

                    DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit(lpLe, fiLe, spotDate.addDays(500), usd, jpy, true, true);
                    log( "dl2=" + dl2 );
                    assertTrue(dl2.getBidLimit() == 0.0);
                    assertTrue(dl2.getOfferLimit() == 0.0);
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testAvailableCreditLimitWithZeroTenorCoefficientsOnOneSide", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );

            creditAdminSvc.setDefaultCreditTenorProfile( fiOrg, null );
            creditAdminSvc.setCreditTenorProfile( fiOrg, fiTpForLp, null );
            creditAdminSvc.setUseDefaultTenorProfile( fiOrg, fiTpForLp, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, true );
            creditAdminSvc.setUsePFEConfiguration( fiOrg, true );
        }
    }

    public void testCreditTenorCoefficientLookupInterpolationEnabled()
    {
        try
        {
            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            init( lpUser );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            FXRateBasis rb = stdQuoteConv.getFXRateBasis( eur, usd );
            IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();

            String testProfile = "Test" + System.nanoTime();
            String[] tenors = new String[]{"SPOT", "1W", "3W", "1m", "3m", "4m", "5m", "6m", "9m", "1y", "2y"};
            double[] coefficients = new double[]{ 100.0, 80.0, 70.0, 60.0, 50.0, 40.0, 30.0, 25.0, 15.0, 10.0, 5.0 };

            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( testProfile, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            CreditLimitRule clr = CreditUtilC.getCreditLimitRule ( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );

            double tenorCoefficient = CreditUtilC.getTenorCoefficient( cclr, clr, rb.getValueDate( tradeDate, new Tenor( "2W" ) ), eur, usd );
            assertTrue ( "tenorCoefficient=" + tenorCoefficient, Math.abs ( tenorCoefficient - 0.75 ) < 0.1 );

            double tenorCoefficient1 = CreditUtilC.getTenorCoefficient( cclr, clr, rb.getValueDate( tradeDate, new Tenor( "2m" ) ), eur, usd );
            assertTrue ( "tenorCoefficient1=" + tenorCoefficient1, Math.abs ( tenorCoefficient1 - 0.55 ) < 0.1 );

            double tenorCoefficient2 = CreditUtilC.getTenorCoefficient( cclr, clr, rb.getValueDate( tradeDate, new Tenor( "18m" ) ), eur, usd );
            assertTrue ( "tenorCoefficient2=" + tenorCoefficient2, Math.abs ( tenorCoefficient2 - 0.075 ) < 0.01 );

            double tenorCoefficient3 = CreditUtilC.getTenorCoefficient( cclr, clr, rb.getValueDate( tradeDate, new Tenor( "2y" ) ), eur, usd );
            assertTrue ( "tenorCoefficient3=" + tenorCoefficient3, Math.abs ( tenorCoefficient3 - 0.05 ) < 0.01 );

            double tenorCoefficient4 = CreditUtilC.getTenorCoefficient( cclr, clr, rb.getValueDate( tradeDate, new Tenor( "SPOT" ) ), eur, usd );
            assertTrue ( "tenorCoefficient4=" + tenorCoefficient4, Math.abs ( tenorCoefficient4 - 1.0 ) < 0.1 );

            double tenorCoefficient5 = CreditUtilC.getTenorCoefficient( cclr, clr, rb.getValueDate( tradeDate, new Tenor( "TOD" ) ), eur, usd );
            assertEquals ( "tenorCoefficient5=" + tenorCoefficient5, 1.0, tenorCoefficient5 );

            double tenorCoefficient6 = CreditUtilC.getTenorCoefficient( cclr, clr, rb.getValueDate( tradeDate, new Tenor( "3y" ) ), eur, usd );
            assertEquals ( "tenorCoefficient6=" + tenorCoefficient6, -1.0, tenorCoefficient6 );
        }
        catch ( Exception e )
        {
            fail( "testCreditTenorCoefficientLookupInterpolationEnabled", e );
        }
        finally
        {
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testCreditTenorCoefficientLookupAtCptyCreditRuleLevel()
    {
        try
        {
            init( lpUser );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            FXRateBasis rb = stdQuoteConv.getFXRateBasis( eur, usd );
            IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForFi, true );

            String testProfile = "Test" + System.currentTimeMillis();
            String[] tenors = new String[]{"SPOT", "1d", "1W", "2W", "3W", "1m", "2m", "3m", "4m", "5m", "6m", "9m", "1y", "2y"};
            double[] coefficients = new double[tenors.length];
            double percent = 0.0;

            Map<String, Double> coefficientsMap = new HashMap<String, Double>();
            Map<String, IdcDate> valueDateMap = new HashMap<String, IdcDate>();

            for ( int i = 0; i < tenors.length; i++ )
            {
                coefficients[i] = percent;
                coefficientsMap.put( tenors[i], coefficients[i] );
                valueDateMap.put( tenors[i], rb.getValueDate( tradeDate, new Tenor( tenors[i] ) ) );
                percent += 10.0;
            }

            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( testProfile, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, false );

            String testAggProfile = "TestAgg" + System.currentTimeMillis();
            double[] coefficients1 = new double[tenors.length];
            double percent1 = 0.0;

            Map<String, Double> coefficientsMap1 = new HashMap<String, Double>();

            for ( int i = 0; i < tenors.length; i++ )
            {
                coefficients1[i] = percent1;
                coefficientsMap1.put( tenors[i], coefficients1[i] );
                valueDateMap.put( tenors[i], rb.getValueDate( tradeDate, new Tenor( tenors[i] ) ) );
                percent1 += 5.0;
            }

            CreditTenorProfile aggTenorProfile = createCreditTenorProfileWithTenorParam( testAggProfile, tenors, coefficients1 );
            creditAdminSvc.addCreditTenorProfile( lpOrg, aggTenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggTenorProfile );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, false );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, false );

            String testDailyProfile = "TestDaily" + System.currentTimeMillis();
            double[] coefficients2 = new double[tenors.length];
            double percent2 = 0.0;

            Map<String, Double> coefficientsMap2 = new HashMap<String, Double>();

            for ( int i = 0; i < tenors.length; i++ )
            {
                coefficients2[i] = percent2;
                coefficientsMap2.put( tenors[i], coefficients2[i] );
                valueDateMap.put( tenors[i], rb.getValueDate( tradeDate, new Tenor( tenors[i] ) ) );
                percent2 += 3.0;
            }

            CreditTenorProfile dailyTenorProfile = createCreditTenorProfileWithTenorParam( testDailyProfile, tenors, coefficients2 );
            creditAdminSvc.addCreditTenorProfile( lpOrg, dailyTenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyTenorProfile );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, false );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            CreditLimitRule aggClr = CreditUtilC.getCreditLimitRule ( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule ( cclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );

            for ( int i = 0; i < tenors.length; i++ )
            {
                double aggTc = CreditUtilC.getTenorCoefficient( cclr, aggClr, valueDateMap.get( tenors[i] ), eur, usd );
                assertEquals( aggTc, coefficients1[i] / 100.0 );
                double dailyTc = CreditUtilC.getTenorCoefficient( cclr, dailyClr, valueDateMap.get( tenors[i] ), eur, usd );
                assertEquals( dailyTc, coefficients2[i] / 100.0 );
            }

            // check for value date beyond max tenor in the tenor parameters.
            IdcDate date1 = rb.getValueDate( tradeDate, new Tenor( "3Y" ) );
            double aggTc = CreditUtilC.getTenorCoefficient( cclr, aggClr, date1, eur, usd );
            assertEquals( aggTc, CreditLimit.TENOR_COEFFICIENT_NA );
            double dailyTc = CreditUtilC.getTenorCoefficient( cclr, dailyClr, date1, eur, usd );
            assertEquals( dailyTc, CreditLimit.TENOR_COEFFICIENT_NA );

            // check for value date before the spot dates.
            IdcDate date2 = rb.getValueDate( tradeDate, new Tenor( "TOD" ) );
            aggTc = CreditUtilC.getTenorCoefficient( cclr, aggClr, date2, eur, usd );
            assertEquals( aggTc, coefficientsMap1.get( "SPOT" ) );
            dailyTc = CreditUtilC.getTenorCoefficient( cclr, dailyClr, date2, eur, usd );
            assertEquals( dailyTc, coefficientsMap2.get( "SPOT" ) );
       }
        catch ( Exception e )
        {
            fail( "testCreditTenorCoefficientLookupAtCptyCreditRuleLevel", e );
        }
        finally
        {
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForFi, false );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, true );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null );
        }
    }

    public void testCreditLimitRuleLevelTenorCoefficientOnTakeCreditWithSubscription()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{4.0, 14.0, 24.0, 34.0, 54.0, 140.0};
            double[] aggCoefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            double[] dailyCoefficients = new double[]{7.0, 17.0, 27.0, 37.0, 57.0, 170.0};
            CreditTenorProfile cptyTenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, cptyTenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, cptyTenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, false);

            CreditTenorProfile aggTenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, aggCoefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, aggTenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggTenorProfile );

            CreditTenorProfile dailyTenorProfile = createCreditTenorProfileWithTenorParam( "Test" + System.nanoTime(), tenors, dailyCoefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, dailyTenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyTenorProfile );
            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForFi, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, false );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, false );

            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);

            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));
            creditAdminSvc.setCreditLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi));

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("EUR"), CurrencyFactory.getCurrency("USD"));
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("GBP"), CurrencyFactory.getCurrency("JPY"));

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                if ( cue.getCreditUtilization().getCreditLimitRule() instanceof SingleCreditLimitRule )
                {
                    if (cue.getNamespace().isSameAs(lpOrg.getNamespace()))
                    {
                        log("cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice());
                        assertTrue(Math.abs(cue.getPrincipal() - (tradeAmt / 10.0)) < CREDIT_CALCULATION_MINIMUM);
                    }
                    else
                    {
                        log("cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice());
                        assertTrue(Math.abs(cue.getPrincipal() - tradeAmt) < CREDIT_CALCULATION_MINIMUM);
                    }
                }
                else
                {
                    if (cue.getNamespace().isSameAs(lpOrg.getNamespace()))
                    {
                        log("cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice());
                        assertTrue(Math.abs(cue.getPrincipal() - (tradeAmt / 17.0)) < CREDIT_CALCULATION_MINIMUM);
                    }
                    else
                    {
                        log("cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice());
                        assertTrue(Math.abs(cue.getPrincipal() - tradeAmt) < CREDIT_CALCULATION_MINIMUM);
                    }
                }
            }

            IdcDate valueDate1 = stdQuoteConv.getFXRateBasis( "EUR", "USD" ).getValueDate( tradeDate, new Tenor( "9m" ) );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate1 );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message1 success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );
            for ( CreditUtilizationEvent cue : cwm1.getCreditUtilizationEvents() )
            {
                if ( cue.getNamespace().isSameAs( lpOrg.getNamespace() ) )
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - ( tradeAmt * 0.5 ) ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "cue.principal=" + cue.getPrincipal() + ",cue.price=" + cue.getPrice() );
                    assertTrue( Math.abs( cue.getPrincipal() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditLimitRuleLevelTenorCoefficientOnTakeCreditWithSubscription", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForFi, false );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, true );
        }
    }
}
