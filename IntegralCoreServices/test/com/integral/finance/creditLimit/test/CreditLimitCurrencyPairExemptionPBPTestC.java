package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.creditLimit.CreditWorkflowRidersDefault;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.time.IdcDate;
import org.eclipse.persistence.sessions.UnitOfWork;
import org.junit.Assert;

/**
 * Tests the credit limit workflow when currency pair exemption is used.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitCurrencyPairExemptionPBPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Currency Pair exemption PB Test";

    public CreditLimitCurrencyPairExemptionPBPTestC( String name )
    {
        super( name );
    }

    public void testCurrencyPairExemptionWithBothPBOnCreditPreQualification()
    {
        Currency USD = CurrencyFactory.getCurrency( "USD" );
        Currency JPY = CurrencyFactory.getCurrency( "JPY" );
        final int SLEEP_TIME = 100;

        try
        {
            init( lpUser );

            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );

            setupBothPBs();

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );

            sleepFor( 3000 );

            IdcDate eurUSDSpotDate = CreditUtilC.getSpotDate( USD, JPY ) == null ? spotDate : CreditUtilC.getSpotDate( USD, JPY );

            //Case 1: Currency Pair Not Exempted for LP & FI-- Failure Case
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( makerLe, takerLe, USD, JPY, true, true, null );
            Thread.sleep( SLEEP_TIME );
            double[] bidLimit;
            double[] offerLimit;
            double origBidLimit, origOfferLimit;

            bidLimit = new double[]{10000};
            offerLimit = new double[]{10000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( makerLe, takerLe, eurUSDSpotDate, USD, JPY, true, bidLimit, offerLimit, true );

            Assert.assertEquals( "The available bid limit for USD/JPY should be less than quote limits. provisionedBidLimit=" + bidLimit[0] + ",origBidLimit=" + origBidLimit, origBidLimit != bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be less than quote limits. provisionedOfferLimit=" + offerLimit[0] + ",origOfferLimit=" + origOfferLimit, origOfferLimit != offerLimit[0], true );

            //Case 2: Currency Pair Exempted for LP & for FI-- Success Case
            CurrencyPairGroup g7 = getCurrencyPairGroup( "G7" );
            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, g7 );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( makerLe, takerLe, USD, JPY, true, true, null );
            Thread.sleep( SLEEP_TIME );

            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( makerLe, takerLe, eurUSDSpotDate, USD, JPY, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for USD/JPY should be same as quote limits.", origBidLimit == bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for USD/JPY should be same as quote limits.", origOfferLimit == offerLimit[0], true );

        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairExemptionWithBothPBOnCreditPreQualification", e );
        }
        finally
        {
            //CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, takerPbLe, EUR, USD, true, true );
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( makerLe, takerLe, USD, JPY, true, true );
            removeCreditLimitSubscriptions( makerLe, makerTpForTaker );
            removeCreditLimitSubscriptions( takerLe, takerTpForMaker );

            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, null );
        }
    }

    public void testCurrencyPairExemptionWithBothPBOnTrade()
    {
        Currency USD = CurrencyFactory.getCurrency( "USD" );
        Currency JPY = CurrencyFactory.getCurrency( "JPY" );
        final int SLEEP_TIME = 100;

        try
        {
            init( lpUser );

            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );

            setupBothPBs();

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );

            sleepFor( 3000 );

            final CurrencyPairGroup g7 = getCurrencyPairGroup( "G7" );
            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, g7 );

            double limit0 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimit( takerLe, makerLe, spotDate );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            sleepFor( 3000 );
            double limit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimit( takerLe, makerLe, spotDate );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "no credit amount was taken :", true, Math.abs( limit0 - limit1 ) < MINIMUM );


            Thread.sleep( SLEEP_TIME );


            //Case 4: Currency Pair Exempted for FI & Not for LP -- Failure Case
            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, null );
            Thread.sleep( SLEEP_TIME );

            double limit2 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimit( takerLe, makerLe, spotDate );
            log( "credit limit  before take Credit : " + limit2 );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            sleepFor( 3000 );
            double limit3 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimit( takerLe, makerLe, spotDate );
            log( "credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() );
            assertEquals( "success=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "credit amount was taken :", true, Math.abs( limit2 - limit3 ) > MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairExemptionWithBothPBOnTrade", e );
        }
        finally
        {
            //CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, takerPbLe, EUR, USD, true, true );
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( makerLe, takerLe, USD, JPY, true, true );
            removeCreditLimitSubscriptions( makerLe, makerTpForTaker );
            removeCreditLimitSubscriptions( takerLe, takerTpForMaker );

            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, null );
        }
    }

    public void testCurrencyPairExemptionWithTakerPBOnlyOnCreditPreQualification()
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" );
        Currency USD = CurrencyFactory.getCurrency( "USD" );
        final int SLEEP_TIME = 100;

        try
        {
            init( lpUser );

            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );

            setupTakerPBOnly();

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );

            sleepFor( 3000 );

            IdcDate eurUSDSpotDate = CreditUtilC.getSpotDate( EUR, USD ) == null ? spotDate : CreditUtilC.getSpotDate( EUR, USD );

            //Case 1: Currency Pair Not Exempted for LP & FI-- Failure Case
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( makerLe, takerLe, EUR, USD, true, true, null );
            Thread.sleep( SLEEP_TIME );
            double[] bidLimit;
            double[] offerLimit;
            double origBidLimit, origOfferLimit;

            bidLimit = new double[]{1000000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( makerLe, takerLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );

            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits. provisionedBidLimit=" + bidLimit[0] + ",origBidLimit=" + origBidLimit, origBidLimit != bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits. provisionedOfferLimit=" + offerLimit[0] + ",origOfferLimit=" + origOfferLimit, origOfferLimit != offerLimit[0], true );

            //Case 2: Currency Pair Exempted for LP & for FI-- Success Case
            CurrencyPairGroup g7 = getCurrencyPairGroup( "G7" );
            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, g7 );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( makerLe, takerLe, EUR, USD, true, true, null );
            Thread.sleep( SLEEP_TIME );

            bidLimit = new double[]{10000};
            offerLimit = new double[]{10000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( makerLe, takerLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit == bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit == offerLimit[0], true );

        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairExemptionWithTakerPBOnlyOnCreditPreQualification", e );
        }
        finally
        {
            //CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, takerPbLe, EUR, USD, true, true );
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( makerLe, takerLe, EUR, USD, true, true );
            removeCreditLimitSubscriptions( makerLe, makerTpForTaker );
            removeCreditLimitSubscriptions( takerLe, takerTpForMaker );

            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            setupBothPBs();

            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, null );
        }
    }

    public void testCurrencyPairExemptionWithMakerPBOnly()
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" );
        Currency USD = CurrencyFactory.getCurrency( "USD" );
        final int SLEEP_TIME = 1000;

        try
        {
            init( lpUser );

            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );

            setupMakerPBOnly();

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000 );

            sleepFor( 3000 );

            IdcDate eurUSDSpotDate = CreditUtilC.getSpotDate( EUR, USD ) == null ? spotDate : CreditUtilC.getSpotDate( EUR, USD );

            //Case 1: Currency Pair Not Exempted for LP & FI-- Failure Case
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( makerLe, takerLe, EUR, USD, true, true, null );
            Thread.sleep( SLEEP_TIME );
            double[] bidLimit;
            double[] offerLimit;
            double origBidLimit, origOfferLimit;

            bidLimit = new double[]{10000};
            offerLimit = new double[]{10000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( makerLe, takerLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );

            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits. provisionedBidLimit=" + bidLimit[0] + ",origBidLimit=" + origBidLimit, origBidLimit != bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits. provisionedOfferLimit=" + offerLimit[0] + ",origOfferLimit=" + origOfferLimit, origOfferLimit != offerLimit[0], true );

            //Case 2: Currency Pair Exempted for LP & for FI-- Success Case
            CurrencyPairGroup g7 = getCurrencyPairGroup( "G7" );
            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, g7 );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( makerLe, takerLe, EUR, USD, true, true, null );
            Thread.sleep( SLEEP_TIME );

            bidLimit = new double[]{10000};
            offerLimit = new double[]{10000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( makerLe, takerLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit == bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit == offerLimit[0], true );

        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairExemptionWithMakerPBOnlyOnCreditPreQualification", e );
        }
        finally
        {
            //CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, takerPbLe, EUR, USD, true, true );
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( makerLe, takerLe, EUR, USD, true, true );
            removeCreditLimitSubscriptions( makerLe, makerTpForTaker );
            removeCreditLimitSubscriptions( takerLe, takerTpForMaker );

            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            setupBothPBs();

            creditAdminSvc.setCurrencyPairGroupExemption( makerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( takerPbOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( makerPbOrg, null );
        }
    }

    protected void setupBothPBs()
    {
        if ( CreditUtilC.getCreditPrimeBrokerTradingparty( takerTpForMaker ) != null && CreditUtilC.getCreditPrimeBrokerTradingparty( makerTpForTaker ) != null )
        {
            return;
        }

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
        registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
        TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
        registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
        uow.commit();
    }

    protected void setupTakerPBOnly()
    {
        if ( CreditUtilC.getCreditPrimeBrokerTradingparty( takerTpForMaker ) != null && CreditUtilC.getCreditPrimeBrokerTradingparty( makerTpForTaker ) == null )
        {
            return;
        }

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
        registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
        TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
        registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
        uow.commit();
    }

    protected void setupMakerPBOnly()
    {
        if ( CreditUtilC.getCreditPrimeBrokerTradingparty( takerTpForMaker ) == null && CreditUtilC.getCreditPrimeBrokerTradingparty( makerTpForTaker ) != null )
        {
            return;
        }

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
        registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( false );
        TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
        registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
        uow.commit();
    }

    protected CurrencyPairGroup getCurrencyPairGroup( String name )
    {
        return ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( name, CurrencyPairGroupC.class, null, null );
    }
}
