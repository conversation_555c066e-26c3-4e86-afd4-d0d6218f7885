package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

/**
 * Tests various credit netting methodologies.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitDailyPLNettingPTestC extends CreditLimitNettingPTestC
{
    static String name = "Credit Limit DailyPL Netting Test";

    public CreditLimitDailyPLNettingPTestC( String name )
    {
        super( name );
    }

    protected void setUp() throws Exception
    {
        super.setUp();

        // set the daily PL enabled
        creditAdminSvc.setDefaultDailyPL( fiOrg, true );
        creditAdminSvc.setDailyPL( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, true );
        creditAdminSvc.setDailyPL( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, true );

        creditAdminSvc.setDefaultDailyPL( lpOrg, true );
        creditAdminSvc.setDailyPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, true );
        creditAdminSvc.setDailyPL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, true );
    }

    protected void tearDown()
    {
        // reset the daily PL enabled flag
        // set the daily PL enabled
        creditAdminSvc.setDefaultDailyPL( fiOrg, false );
        creditAdminSvc.setDailyPL( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, false );
        creditAdminSvc.setDailyPL( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, false );

        creditAdminSvc.setDefaultDailyPL( lpOrg, false );
        creditAdminSvc.setDailyPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, false );
        creditAdminSvc.setDailyPL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, false );

        super.tearDown();
    }
}
