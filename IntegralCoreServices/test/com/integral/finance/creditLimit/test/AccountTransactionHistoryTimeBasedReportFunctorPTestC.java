package com.integral.finance.creditLimit.test;

// Copyright (c) 2019 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.creditLimit.admin.AccountTransactionHistoryReportFunctor;
import com.integral.finance.creditLimit.admin.AccountTransactionHistoryTimeBasedReportFunctor;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.trade.Trade;
import com.integral.is.message.MessageFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.rule.SendEmailAction;
import com.integral.scheduler.ScheduleFunctor;
import com.integral.session.IdcTransaction;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcSimpleDateFormat;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import java.text.SimpleDateFormat;


/**
 * Tests Transaction history report schedule event functor.
 *
 * <AUTHOR> Development Corp.
 */
public class AccountTransactionHistoryTimeBasedReportFunctorPTestC extends CreditLimitServicePTestC
{
    static String name = "AccountTransactionHistoryTimeBasedReportFunctorPTestC";
    String[] START_DATE_VALUES = new String[] { AccountTransactionHistoryReportFunctor.MTD, AccountTransactionHistoryReportFunctor.TODAY, AccountTransactionHistoryReportFunctor.YESTERDAY, AccountTransactionHistoryReportFunctor.YTD, AccountTransactionHistoryReportFunctor.WTD};

    public AccountTransactionHistoryTimeBasedReportFunctorPTestC( String name )
    {
        super( name );
    }

    public void testReportFunctorSimpleValidation()
    {
        try
        {
            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor ();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartTime ( "00:00:30" );
            functor.setEndTime ( "23:33:45" );
            for ( String startDate: START_DATE_VALUES )
            {
                functor.setStartDate ( startDate );
                String validationError = functor.validate ();
                assertEquals ( ScheduleFunctor.SUCCESS, validationError );
            }
        }
        catch ( Exception e )
        {
            fail( "testReportFunctorSimpleValidation", e );
        }
    }

    public void testReportFunctorDateValidation()
    {
        try
        {
            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( "xxx" );
            functor.setStartTime ( "00:00:30" );
            functor.setEndTime ( "23:33:45" );
            String validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setStartDate ( "MTD" );
            functor.setEndDate ( "xxx" );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            User loggedInUser = IdcUtilC.getSessionContextUser ();
            assertNotNull ( loggedInUser );
            SimpleDateFormat sdf = loggedInUser.getDisplayPreference ().getDateFormat ();
            assertNotNull ( sdf );

            IdcDate startDate = DateTimeFactory.newDate ();
            IdcDate endDate = DateTimeFactory.newDate ().addDays ( 100 );
            functor.setEndDate ( sdf.format ( startDate.asJdkDate () ) );
            functor.setStartDate ( sdf.format ( endDate.asJdkDate () ) );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setStartDate ( sdf.format ( startDate.asJdkDate () ) );
            functor.setEndDate ( sdf.format ( endDate.asJdkDate () ) );
            validationError = functor.validate ();
            assertTrue ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            if ( !sdf.toPattern ().equals ( IdcSimpleDateFormat.FORMAT_DATE_ISO ) )
            {
                SimpleDateFormat sdf1 = new SimpleDateFormat ( IdcSimpleDateFormat.FORMAT_DATE_ISO );
                functor.setStartDate ( sdf1.format ( startDate.asJdkDate () ) );
                functor.setEndDate ( sdf1.format ( endDate.asJdkDate () ) );
                validationError = functor.validate ();
                assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );
            }
        }
        catch ( Exception e )
        {
            fail( "testReportFunctorDateValidation", e );
        }
    }

    public void testReportFunctorDateTimeValidation()
    {
        try
        {
            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            User loggedInUser = IdcUtilC.getSessionContextUser ();
            assertNotNull ( loggedInUser );
            SimpleDateFormat sdf = loggedInUser.getDisplayPreference ().getDateFormat ();
            assertNotNull ( sdf );
            IdcDate startDate = DateTimeFactory.newDate ();
            IdcDate endDate = DateTimeFactory.newDate ().addDays ( 100 );
            functor.setEndDate ( sdf.format ( startDate.asJdkDate () ) );
            functor.setStartDate ( sdf.format ( endDate.asJdkDate () ) );
            functor.setStartTime ( "00:00:30" );
            functor.setEndTime ( "23:33:45" );
            String validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setStartTime ( "00:00:30" );
            functor.setEndTime ( "xxx" );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setEndDate ( sdf.format ( DateTimeFactory.newDate ().asJdkDate () ) );
            functor.setStartDate ( sdf.format ( DateTimeFactory.newDate ().asJdkDate () ) );
            functor.setStartTime ( "23:00:34" );
            functor.setEndTime ( "01:34:45" );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setStartDate ( sdf.format ( startDate.asJdkDate () ) );
            functor.setEndDate ( sdf.format ( endDate.asJdkDate () ) );
            validationError = functor.validate ();
            assertTrue ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setStartTime ( null );
            functor.setEndDate ( sdf.format ( endDate.asJdkDate () ) );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "23:62:90" );
            functor.setEndDate ( sdf.format ( endDate.asJdkDate () ) );
            validationError = functor.validate ();
            assertTrue ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setStartTime ( "23:62:90" );
            functor.setEndTime ( "04:00:00" );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.YESTERDAY );
            functor.setEndDate ( AccountTransactionHistoryReportFunctor.TODAY );
            validationError = functor.validate ();
            assertTrue ( ScheduleFunctor.SUCCESS.equals ( validationError ) );
        }
        catch ( Exception e )
        {
            fail( "testReportFunctorDateTimeValidation", e );
        }
    }

    public void testReportFunctorEmailValidation()
    {
        try
        {
            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.MTD );
            functor.setStartTime ( "00:45:56" );
            functor.setEndTime ( "23:00:05" );
            String validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setFromEmailAddress ( "<EMAIL>" );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setToEmailAddresses ( "nobodyintegral.com" );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setFromEmailAddress ( "xx" );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>,<EMAIL>" );
            validationError = functor.validate ();
            assertTrue ( ScheduleFunctor.SUCCESS.equals ( validationError ) );
        }
        catch ( Exception e )
        {
            fail( "testReportFunctorEmailValidation", e );
        }
    }

    public void testReportFunctorAccountPartiesValidation()
    {
        try
        {
            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.MTD );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartTime ( "00:45:56" );
            functor.setEndTime ( "23:00:05" );

            String validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setAccountProviderOrg ( "xxxx" );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setAccountProviderOrg ( ddOrg.getShortName () );
            functor.setAccountCptyOrg ( null );
            functor.setAccountCpty ( ddLe.getShortName () );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setAccountCptyOrg ( "xxxx" );
            functor.setAccountCpty ( ddLe.getShortName () );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( null );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setAccountCpty ( "xxxx" );
            validationError = functor.validate ();
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            validationError = functor.validate ();
            assertTrue ( ScheduleFunctor.SUCCESS.equals ( validationError ) );
        }
        catch ( Exception e )
        {
            fail( "testReportFunctorAccountPartiesValidation", e );
        }
    }

    public void testMiscValidations ()
    {
        AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
        functor.setFromEmailAddress ( "<EMAIL>" );
        functor.setToEmailAddresses ( "<EMAIL>" );
        functor.setStartDate ( AccountTransactionHistoryReportFunctor.MTD );
        functor.setAccountProviderOrg ( lpOrg.getShortName () );
        functor.setAccountCptyOrg ( ddOrg.getShortName () );
        functor.setAccountCpty ( ddLe.getShortName () );
        functor.setStartTime ( "00:45:56" );
        functor.setEndTime ( "23:00:05" );

        String validationError = functor.validate ();
        assertTrue ( ScheduleFunctor.SUCCESS.equals ( validationError ) );

    }

    public void testSimpleTransactionHistoryTodayReport()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.TODAY );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "23:59:59" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertTrue ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertTrue ( emailMsg.contains ( trade.getOrderId () ) );

            functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.YESTERDAY );
            wm = MessageFactory.newWorkflowMessage ();

            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ); ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );

        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryTodayReport", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSimpleTransactionHistoryTodayReportWithTradeExclusion()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.TODAY );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "00:00:01" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );
        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryTodayReportWithTradeExclusion", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSimpleTransactionHistoryYesterdayReport()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.YESTERDAY );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "23:59:59" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );
        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryYesterdayReport", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSimpleTransactionHistoryReportCustomDateRange()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            IdcDate startDate = DateTimeFactory.newDate ();
            functor.setStartDate ( lpUser.getDisplayPreference ().getDateFormat ().format ( startDate.asJdkDate ()  ) );
            functor.setEndDate ( lpUser.getDisplayPreference ().getDateFormat ().format ( startDate.asJdkDate () ) );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "23:59:59" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertTrue ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertTrue ( emailMsg.contains ( trade.getOrderId () ) );

            functor.setStartDate ( lpUser.getDisplayPreference ().getDateFormat ().format ( startDate.subtractDays ( 1 ).asJdkDate ()  ) );
            functor.setEndDate ( lpUser.getDisplayPreference ().getDateFormat ().format ( startDate.subtractDays ( 1 ).asJdkDate () ) );
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );

        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryReportCustomDateRange", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSimpleTransactionHistoryMTDReport()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.MTD );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "23:59:59" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertTrue ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertTrue ( emailMsg.contains ( trade.getOrderId () ) );

            functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.YESTERDAY );
            wm = MessageFactory.newWorkflowMessage ();

            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ); ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );

        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryMTDReport", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSimpleTransactionHistoryMTDReportWithTradeExclusion()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.MTD );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "00:00:01" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );
        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryTodayReportWithTradeExclusion", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSimpleTransactionHistoryWTDReport()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.WTD );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "23:59:59" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertTrue ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertTrue ( emailMsg.contains ( trade.getOrderId () ) );

            functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.YESTERDAY );
            wm = MessageFactory.newWorkflowMessage ();

            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ); ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );

        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryWTDReport", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSimpleTransactionHistoryWTDReportWithTradeExclusion()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.WTD );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "00:00:01" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );
        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryWTDReportWithTradeExclusion", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSimpleTransactionHistoryEndDateTodayReport()
    {
        try
        {
            TestTransactionHistoryReportHandler handler = new TestTransactionHistoryReportHandler();
            AccountTransactionHistoryTimeBasedReportFunctor.setNotificationHandler ( handler );
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );


            double tradeAmt = 1000;
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );

            IdcTransaction tx = initTransaction ( true );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setOrderId ( "*********" );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) );
            validateCreditUtilizationEvents( cwm );
            commitTransaction ( tx );

            AccountTransactionHistoryTimeBasedReportFunctor functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.YESTERDAY );
            functor.setEndDate ( AccountTransactionHistoryReportFunctor.TODAY );
            functor.setStartTime ( "00:00:00" );
            functor.setEndTime ( "23:59:59" );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            String emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertTrue ( emailMsg.contains ( trade.getTransactionID () ) ) ;
            assertTrue ( emailMsg.contains ( trade.getOrderId () ) );

            functor = new AccountTransactionHistoryTimeBasedReportFunctor();
            functor.setFromEmailAddress ( "<EMAIL>" );
            functor.setToEmailAddresses ( "<EMAIL>" );
            functor.setAccountProviderOrg ( lpOrg.getShortName () );
            functor.setAccountCptyOrg ( ddOrg.getShortName () );
            functor.setAccountCpty ( ddLe.getShortName () );
            functor.setStartDate ( AccountTransactionHistoryReportFunctor.YESTERDAY );
            wm = MessageFactory.newWorkflowMessage ();

            functor.execute ( wm );
            assertNotNull ( handler.getSendEmailAction () );
            emailMsg = handler.getSendEmailAction ().getBody ();
            log( "email=" + emailMsg );
            assertFalse ( emailMsg.contains ( trade.getTransactionID () ) ); ;
            assertFalse ( emailMsg.contains ( trade.getOrderId () ) );

        }
        catch ( Exception e )
        {
            fail ( "testSimpleTransactionHistoryEndDateTodayReport", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    private class TestTransactionHistoryReportHandler implements MessageHandler
    {
        SendEmailAction sendEmailAction;


        private SendEmailAction getSendEmailAction()
        {
            return sendEmailAction;
        }

        public Message handle ( Message message )
        {
            WorkflowMessage wm = ( WorkflowMessage ) message;
            sendEmailAction = ( SendEmailAction ) wm.getParameterValue ( "SendEmailAction" );
            return null;
        }
    }
}
