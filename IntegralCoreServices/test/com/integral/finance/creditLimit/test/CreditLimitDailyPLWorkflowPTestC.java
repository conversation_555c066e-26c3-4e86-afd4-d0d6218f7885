package com.integral.finance.creditLimit.test;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationCalculator;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.session.IdcTransaction;

import java.util.Collection;


/**
 * Tests the credit limit service workflows.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitDailyPLWorkflowPTestC extends CreditLimitServiceWorkflowPTestC
{
    static String name = "Credit Limit DailyPL Service workflow Test";

    protected void setUp() throws Exception
    {
        super.setUp();

        // set the daily PL enabled
        creditAdminSvc.setDefaultDailyPL( fiOrg, true );
        creditAdminSvc.setDailyPL( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, true );
        creditAdminSvc.setDailyPL( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, true );

        creditAdminSvc.setDefaultDailyPL( lpOrg, true );
        creditAdminSvc.setDailyPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, true );
        creditAdminSvc.setDailyPL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, true );
    }

    protected void tearDown()
    {
        // reset the daily PL enabled flag
        // set the daily PL enabled
        creditAdminSvc.setDefaultDailyPL( fiOrg, false );
        creditAdminSvc.setDailyPL( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, false );
        creditAdminSvc.setDailyPL( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, false );

        creditAdminSvc.setDefaultDailyPL( lpOrg, false );
        creditAdminSvc.setDailyPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, false );
        creditAdminSvc.setDailyPL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, false );

        super.tearDown();
    }

    public CreditLimitDailyPLWorkflowPTestC( String name )
    {
        super( name );
    }

    /**
     * This test will make sure that currency positions are aggregated on the trade date instead of the value date.
     * In this case, trades will be done in two different value dates and netting will take place at the trade date level.
     */
    public void testTradeDateBasedPositions()
    {
        try
        {
            init( lpUser );
            double limit = ********;   // 10M
            Collection<CreditUtilizationCalculator> supportedCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, limit * 100 );  // disable the daily credit limits by putting a large limit
            for ( CreditUtilizationCalculator calculator : supportedCalcs )
            {
                log( "$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ Test trade date based positions for " + calculator );
                removeExistingCreditUtilizationEvents( lpOrg );
                removeExistingCreditUtilizationEvents( fiOrg );

                limit = ********;
                setCalcAndLimit( lpOrg, lpTpForFi, CreditUtilC.getCreditLimitClassification( calculator ), calculator, limit );
                creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForFi, 100 );

                double availableAmt0 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "****************** availableAmt0=" + availableAmt0 + " for calc=" + calculator );

                double trade1Amt = 1000;
                Trade trade = prepareSingleLegTrade( trade1Amt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                double availableAmt1 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmt1=" + availableAmt1 + " for calc=" + calculator );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                //do another trade which is with a different value date
                double trade2Amt = 1000;
                trade = prepareSingleLegTrade( trade2Amt, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays( 10 ) );
                cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                double availableAmt2 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmt2=" + availableAmt2 + " for calc=" + calculator );

                if ( CreditUtilC.isNettingCalculator( calculator ) )
                {
                    log( "Nettting calculator used is " + calculator );
                    assertTrue( Math.abs( availableAmt0 - availableAmt2 ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "No Nettting calculator used. calc=" + calculator );
                    assertTrue( Math.abs( availableAmt0 - availableAmt2 - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testTradeDateBasedPositions", e );
        }
    }

    /**
     * This test will make sure that currency positions are aggregated on the trade date instead of the value date even
     * when there are positions created from different trade dates.
     * In this case, trades will be done in two different value dates and netting will take place at the trade date level
     * and the trades done on an earlier trade date will be ignored.
     */
    public void testDifferentTradeDateBasedPositions()
    {
        try
        {
            init( lpUser );
            double limit = ********;   // 10M
            Collection<CreditUtilizationCalculator> supportedCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, limit * 100 );  // disable the daily credit limits by putting a large limit
            for ( CreditUtilizationCalculator calculator : supportedCalcs )
            {
                log( "$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ Test trade date based positions for " + calculator );
                removeExistingCreditUtilizationEvents( lpOrg );
                removeExistingCreditUtilizationEvents( fiOrg );

                setCalcAndLimit( lpOrg, lpTpForFi, CreditUtilC.getCreditLimitClassification( calculator ), calculator, limit );
                creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForFi, 100 );

                double availableAmt0 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "****************** availableAmt0=" + availableAmt0 + " for calc=" + calculator );

                double trade1Amt = limit * 10;
                Trade trade = prepareSingleLegTrade( trade1Amt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
                trade.setTradeDate( tradeDate.subtractDays( 1 ) );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                double availableAmt1 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmt1=" + availableAmt1 + " for calc=" + calculator );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                // after this trade, reset the caches so that trade date will be set correctly
                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetAllCreditUtilizations();
                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();

                //do another trade which is with a different value date
                double trade2Amt = 1300;
                trade = prepareSingleLegTrade( trade2Amt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
                cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                double availableAmt2 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmt0=" + availableAmt0 + ",availableAmt2=" + availableAmt2 + " for calc=" + calculator );
                assertTrue( Math.abs( availableAmt0 - availableAmt2 - trade2Amt ) < 100.0 );
            }
        }
        catch ( Exception e )
        {
            fail( "testDifferentTradeDateBasedPositions", e );
        }
    }

    /**
     * This test will make sure that once the dailyPL is set in admin at org level and utilizations are recalculated. First start with no setting
     * on cpty level and org level is set to false. Once trades are done in different spot dates, admin change is done and used
     * amount and available amounts are checked.
     */
    public void testDailyPLAdminChangeAtOrgLevel()
    {
        try
        {
            init( lpUser );
            double limit = ********;   // 10M
            Collection<CreditUtilizationCalculator> supportedCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, limit * 100 );  // disable the daily credit limits by putting a large limit
            for ( CreditUtilizationCalculator calculator : supportedCalcs )
            {
                log( "$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ Test trade date based positions for " + calculator );
                removeExistingCreditUtilizationEvents( lpOrg );
                removeExistingCreditUtilizationEvents( fiOrg );

                // start with no daily PL
                creditAdminSvc.setDefaultDailyPL( lpOrg, false );
                creditAdminSvc.setDailyPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null );

                limit = ********;
                setCalcAndLimit( lpOrg, lpTpForFi, CreditUtilC.getCreditLimitClassification( calculator ), calculator, limit );
                creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForFi, 100 );

                double availableAmt0 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "****************** availableAmt0=" + availableAmt0 + " for calc=" + calculator );

                IdcTransaction tx = initTransaction( true );
                double trade1Amt = 1000;
                Trade trade = prepareSingleLegTrade( trade1Amt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                commitTransaction( tx );
                double availableAmt1 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmt1=" + availableAmt1 + " for calc=" + calculator );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                //do another trade which is with a different value date
                tx = initTransaction( true );
                double trade2Amt = 1000;
                trade = prepareSingleLegTrade( trade2Amt, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays( 10 ) );
                cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                commitTransaction( tx );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                double availableAmt2 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmt2=" + availableAmt2 + " for calc=" + calculator );

                if ( CreditUtilC.isNettingCalculator( calculator ) )
                {
                    log( "Nettting calculator used is " + calculator );
                    if ( AGGREGATE_LIMIT_CALCULATOR.isSameAs( calculator ) )
                    {
                        // when daily PL is false,
                        assertTrue( Math.abs( availableAmt0 - availableAmt2 - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                    }
                    else
                    {
                        assertTrue( Math.abs( availableAmt0 - availableAmt2 ) < CREDIT_CALCULATION_MINIMUM );
                    }
                }
                else
                {
                    log( "No Nettting calculator used. calc=" + calculator );
                    assertTrue( Math.abs( availableAmt0 - availableAmt2 - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                }

                // now set the admin setting to make daily PL to false.
                sleepFor( 2000 );
                creditAdminSvc.setDefaultDailyPL( lpOrg, true );
                double availableAmt3 = getAvailableCreditLimit( lpTpForFi, spotDate );
                CreditUtilization aggCu = getAggregateCreditUtilization( lpLe, lpTpForFi, spotDate );
                log( "****************** availableAmt3=" + availableAmt3 + " for calc=" + calculator + ",aggCu.usedAmt=" + aggCu.getUsedAmount() );

                if ( CreditUtilC.isNettingCalculator( calculator ) )
                {
                    log( "Nettting calculator used is " + calculator );
                    assertTrue( Math.abs( availableAmt0 - availableAmt3 ) < CREDIT_CALCULATION_MINIMUM );
                    assertTrue( Math.abs( aggCu.getUsedAmount() ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "No Nettting calculator used. calc=" + calculator );
                    assertTrue( Math.abs( availableAmt0 - availableAmt3 - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                    assertTrue( Math.abs( aggCu.getUsedAmount() - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testDailyPLAdminChangeAtOrgLevel", e );
        }
    }

    /**
     * This test will make sure that once the dailyPL is set in admin at counterparty level and utilizations are recalculated.
     */
    public void testDailyPLAdminChangeAtCptyLevel()
    {
        try
        {
            init( lpUser );
            double limit = ********;   // 10M
            Collection<CreditUtilizationCalculator> supportedCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, limit * 100 );  // disable the daily credit limits by putting a large limit
            for ( CreditUtilizationCalculator calculator : supportedCalcs )
            {
                log( "$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ Test trade date based positions for " + calculator );
                removeExistingCreditUtilizationEvents( lpOrg );
                removeExistingCreditUtilizationEvents( fiOrg );

                // start with no daily PL
                creditAdminSvc.setDefaultDailyPL( lpOrg, true );
                creditAdminSvc.setDailyPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, false );

                limit = ********;
                setCalcAndLimit( lpOrg, lpTpForFi, CreditUtilC.getCreditLimitClassification( calculator ), calculator, limit );
                creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForFi, 100 );

                double availableAmt0 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "****************** availableAmt0=" + availableAmt0 + " for calc=" + calculator );

                IdcTransaction tx = initTransaction( true );
                double trade1Amt = 1000;
                Trade trade = prepareSingleLegTrade( trade1Amt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                commitTransaction( tx );
                double availableAmt1 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmt1=" + availableAmt1 + " for calc=" + calculator );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                //do another trade which is with a different value date
                tx = initTransaction( true );
                double trade2Amt = 1000;
                trade = prepareSingleLegTrade( trade2Amt, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays( 10 ) );
                cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                commitTransaction( tx );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                double availableAmt2 = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmt2=" + availableAmt2 + " for calc=" + calculator );

                if ( CreditUtilC.isNettingCalculator( calculator ) )
                {
                    log( "Nettting calculator used is " + calculator );
                    if ( AGGREGATE_LIMIT_CALCULATOR.isSameAs( calculator ) )
                    {
                        // when daily PL is false,
                        assertTrue( Math.abs( availableAmt0 - availableAmt2 - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                    }
                    else
                    {
                        assertTrue( Math.abs( availableAmt0 - availableAmt2 ) < CREDIT_CALCULATION_MINIMUM );
                    }
                }
                else
                {
                    log( "No Nettting calculator used. calc=" + calculator );
                    assertTrue( Math.abs( availableAmt0 - availableAmt2 - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                }

                // now set the admin setting to make daily PL to false.
                sleepFor( 2000 );
                creditAdminSvc.setDailyPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, true );
                double availableAmt3 = getAvailableCreditLimit( lpTpForFi, spotDate );
                CreditUtilization aggCu = getAggregateCreditUtilization( lpLe, lpTpForFi, spotDate );
                log( "****************** availableAmt3=" + availableAmt3 + " for calc=" + calculator + ",aggCu.usedAmt=" + aggCu.getUsedAmount() );

                if ( CreditUtilC.isNettingCalculator( calculator ) )
                {
                    log( "Nettting calculator used is " + calculator );
                    assertTrue( Math.abs( availableAmt0 - availableAmt3 ) < CREDIT_CALCULATION_MINIMUM );
                    assertTrue( Math.abs( aggCu.getUsedAmount() ) < CREDIT_CALCULATION_MINIMUM );
                }
                else
                {
                    log( "No Nettting calculator used. calc=" + calculator );
                    assertTrue( Math.abs( availableAmt0 - availableAmt3 - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                    assertTrue( Math.abs( aggCu.getUsedAmount() - trade1Amt - trade2Amt ) < CREDIT_CALCULATION_MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testDailyPLAdminChangeAtCptyLevel", e );
        }
    }
}
