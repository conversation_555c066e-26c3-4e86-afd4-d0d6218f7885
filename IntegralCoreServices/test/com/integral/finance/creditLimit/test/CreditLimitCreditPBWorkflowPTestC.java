package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.creditLimit.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.cache.TradeCacheC;
import com.integral.message.MessageStatus;
import com.integral.session.IdcTransaction;
import com.integral.user.OrganizationC;
import com.integral.workflow.dealing.DealingLimit;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;

/**
 * Tests the credit limit workflow when prime broker is involved. This is for the use case where
 * Credit PB is set - the credit should be taken from Credit PB instead of Regular PB
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitCreditPBWorkflowPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Credit PB Workflow  Test";
    TradingParty takerTpForMakerPB = takerTpForMaker.getPrimeBrokerTradingParty();
    TradingParty makerTpForTakerPB = makerTpForTaker.getPrimeBrokerTradingParty();
    TradingParty takerTpForLpPB = takerTpForLp.getPrimeBrokerTradingParty();
    TradingParty lpTpForTakerPB = lpTpForTaker.getPrimeBrokerTradingParty();
    TradingParty makerTpForLpPB = makerTpForLp.getPrimeBrokerTradingParty();
    TradingParty lpTpForMakerPB = lpTpForMaker.getPrimeBrokerTradingParty();

    public CreditLimitCreditPBWorkflowPTestC( String name )
    {
        super( name );
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit.
     */
    public void testSimplePrimeBrokerageCredit()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );

            // disable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, false );
            creditAdminSvc.setCreditEnabled( makerOrg, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, false );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, false );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, false );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, false );

            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be false. enabled1=" + enabled1, enabled1, false );

            // enable only one relationship
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            // Set the PBs back to the original PBs
            setPrimeBrokerSettings();
        }
    }

    /**
     * Sets the PB settings back to the original settings
     */
    private void setPrimeBrokerSettings()
    {
        try
        {
            IdcTransaction tx = initTransaction();
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );

            TradingParty registerdTakerTpForMaker = ( TradingParty ) takerTpForMaker.getRegisteredObject();
            registerdTakerTpForMaker.setPrimeBrokerTradingParty( ( TradingParty ) takerTpForMakerPB.getRegisteredObject() );

            TradingParty registeredMakerTpForTaker = ( TradingParty ) makerTpForTaker.getRegisteredObject();
            registeredMakerTpForTaker.setPrimeBrokerTradingParty( ( TradingParty ) makerTpForTakerPB.getRegisteredObject() );

            TradingParty registerdTakerTpForLp = ( TradingParty ) takerTpForLp.getRegisteredObject();
            registerdTakerTpForLp.setPrimeBrokerTradingParty( ( TradingParty ) takerTpForLpPB.getRegisteredObject() );

            TradingParty registeredLpTpForTaker = ( TradingParty ) lpTpForTaker.getRegisteredObject();
            registeredLpTpForTaker.setPrimeBrokerTradingParty( ( TradingParty ) lpTpForTakerPB.getRegisteredObject() );

            TradingParty registeredMakerTpForLp = ( TradingParty ) makerTpForLp.getRegisteredObject();
            registeredMakerTpForLp.setPrimeBrokerTradingParty( ( TradingParty ) makerTpForLpPB.getRegisteredObject() );

            TradingParty registeredLpTpForMaker = ( TradingParty ) lpTpForMaker.getRegisteredObject();
            registeredLpTpForMaker.setPrimeBrokerTradingParty( ( TradingParty ) lpTpForMakerPB.getRegisteredObject() );

            commitTransaction( tx );

        }
        catch ( Exception e )
        {
            log.error( "CreditLimitCreditPBWorkflowPTestC.setPrimeBrokerSettings.ERROR", e );
        }

    }

    /**
     * Removes the existing PB settings. This is to test PB credit enhancement.
     */
    private void removePrimeBrokerSettings()
    {
        try
        {
            IdcTransaction tx = initTransaction();
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );

            TradingParty registerdTakerTpForMaker = ( TradingParty ) takerTpForMaker.getRegisteredObject();
            registerdTakerTpForMaker.setPrimeBrokerTradingParty( null );

            TradingParty registeredMakerTpForTaker = ( TradingParty ) makerTpForTaker.getRegisteredObject();
            registeredMakerTpForTaker.setPrimeBrokerTradingParty( null );

            TradingParty registerdTakerTpForLp = ( TradingParty ) takerTpForLp.getRegisteredObject();
            registerdTakerTpForLp.setPrimeBrokerTradingParty( null );

            TradingParty registeredLpTpForTaker = ( TradingParty ) lpTpForTaker.getRegisteredObject();
            registeredLpTpForTaker.setPrimeBrokerTradingParty( null );

            TradingParty registeredMakerTpForLp = ( TradingParty ) makerTpForLp.getRegisteredObject();
            registeredMakerTpForLp.setPrimeBrokerTradingParty( null );

            TradingParty registeredLpTpForMaker = ( TradingParty ) lpTpForMaker.getRegisteredObject();
            registeredLpTpForMaker.setPrimeBrokerTradingParty( null );

            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "CreditLimitCreditPBWorkflowPTestC.removePrimeBrokerSettings.ERROR", e );
        }

    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditBothSidesEnabledWithSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setPrimeBrokerSettings();
        }
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditBothSidesEnabledWithNettingAndSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, "USD/CHF", makerOrg, makerTpForTaker, bidRates[8], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt + ",bidOfferRates=" + bidRates[8] + "/" + offerRates[8], true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "USD/CHF", makerOrg, makerTpForTaker, bidRates[8], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm1.status=" + cwm1.getStatus()  );
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfterOppositeTrade0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterOppositeTrade0=" + aggregateLimitAfterOppositeTrade0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterOppositeTrade0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterOppositeTrade1=" + aggregateLimitAfterOppositeTrade1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterOppositeTrade1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterOppositeTrade2=" + aggregateLimitAfterOppositeTrade2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterOppositeTrade2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterOppositeTrade3=" + aggregateLimitAfterOppositeTrade3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterOppositeTrade3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterOppositeTrade4=" + aggregateLimitAfterOppositeTrade4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterOppositeTrade4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterOppositeTrade5=" + aggregateLimitAfterOppositeTrade5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterOppositeTrade5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterOppositeTrade0=" + dailyLimitAfterOppositeTrade0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterOppositeTrade0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterOppositeTrade1=" + dailyLimitAfterOppositeTrade1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterOppositeTrade1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterOppositeTrade2=" + dailyLimitAfterOppositeTrade2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterOppositeTrade2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterOppositeTrade3=" + dailyLimitAfterOppositeTrade3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterOppositeTrade3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterOppositeTrade4=" + dailyLimitAfterOppositeTrade4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterOppositeTrade4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterOppositeTrade5=" + dailyLimitAfterOppositeTrade5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterOppositeTrade5 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setPrimeBrokerSettings();
        }
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditBothSidesEnabledFailureWithNettingAndSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 6000000;
            double aggregateLimit = 6000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - ( aggregateLimitOffset * 5 ) );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 2000000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setPrimeBrokerSettings();
        }
    }

    /**
     * Tests the FI -> FI Pb, FI Pb -> LP, LP -> FI credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditLPSideDisabledWithSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "undoCwm.status=" + undoCwm.getStatus()  );
            assertEquals( "Message success status=" + undoCwm.getStatus(), undoCwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();
            setPrimeBrokerSettings();
        }
    }

    /**
     * Tests the LP -> LP Pb, LP Pb -> FI, FI -> LP credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditFISideDisabledWithSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( false );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, aggregateLimit );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "undoCwm.status=" + undoCwm.getStatus() );
            assertEquals( "Message success status=" + undoCwm.getStatus(), undoCwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            setPrimeBrokerSettings();
        }
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit after disabling the credit for prime brokerage.
     */
    public void testPrimeBrokerageCreditBothDisabled()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );

            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // disable the prime brokerage credit.
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( false );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            removePrimeBrokerSettings();

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            setPrimeBrokerSettings();
        }
    }

    /**
     * Tests the FI -> FI Pb, FI Pb -> LP, LP -> FI credit lines with a take credit and then undo credit after disabling the credit for prime brokerage.
     */
    public void testSimplePrimeBrokerageCreditLPSideDisabled()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 3 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 3 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 2 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 2 );

            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // disable the prime brokerage credit.
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            // check the available credit limit for USD and CHF. This dealing limit will be available credit limit between taker's credit for maker and bilateral available between maker pb and maker.
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            DealingLimit dealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, usd, chf, false );
            DealingLimit takerCreditForTakerPb = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( takerLe, takerTpForTakerPb, takerPbOrg, spotDate, usd, chf, false );
            DealingLimit takerPbCreditForMaker = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( takerPbLe, takerPbTpForMaker, makerOrg, spotDate, usd, chf, false );
            DealingLimit makerCreditForTaker = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( makerLe, makerTpForTaker, takerOrg, spotDate, usd, chf, false );
            log( "dealingLimit=" + dealingLimit + ",takerCreditForTakerPb=" + takerCreditForTakerPb + ",takerPbCreditForMaker=" + takerPbCreditForMaker + ",makerCreditForTaker" + makerCreditForTaker );

            double minBidLimit1 = takerCreditForTakerPb.getBidLimit() > takerPbCreditForMaker.getBidLimit() ? takerPbCreditForMaker.getBidLimit() : takerCreditForTakerPb.getBidLimit();
            double minBidLimit = minBidLimit1 > makerCreditForTaker.getBidLimit() ? makerCreditForTaker.getBidLimit() : minBidLimit1;
            double minOfferLimit1 = takerCreditForTakerPb.getOfferLimit() > takerPbCreditForMaker.getOfferLimit() ? takerPbCreditForMaker.getOfferLimit() : takerCreditForTakerPb.getOfferLimit();
            double minOfferLimit = minOfferLimit1 > makerCreditForTaker.getOfferLimit() ? makerCreditForTaker.getOfferLimit() : minBidLimit1;

            assertEquals( "bid limit should be equal to min of three limits.", dealingLimit.getBidLimit(), minBidLimit );
            assertEquals( "offer limit should be equal to min of three limits.", dealingLimit.getOfferLimit(), minOfferLimit );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            assertEquals( "Message status after undo credit should be success.", undoCwm.getStatus() == MessageStatus.SUCCESS, true );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );

            // check bilateral credit enabled flags
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, false );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, false );
            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be true. enabled1=" + enabled1, enabled1, false );

            // now mark one of them enabled
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            setPrimeBrokerSettings();
        }
    }

    /**
     * Tests the FI -> FI Pb, FI Pb -> LP, LP -> FI credit lines with a take credit and then undo credit after disabling the credit for prime brokerage.
     */
    public void testSimplePrimeBrokerageUndoCreditLPSideDisabled()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 3 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 3 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 2 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 2 );

            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // now take a simple trade
            double tradeAmt = 1000;

            // disable the prime brokerage credit.
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            assertEquals( "Message status after undo credit should be success.", undoCwm.getStatus() == MessageStatus.SUCCESS, true );

            // check bilateral credit enabled flags
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, false );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, false );
            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be true. enabled1=" + enabled1, enabled1, false );

            // now mark one of them enabled
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            setPrimeBrokerSettings();
        }
    }


    /**
     * Tests the LP -> LP Pb, LP Pb -> FI, FI -> LP credit lines with a take credit and then undo credit after disabling the credit for prime brokerage.
     */
    public void testSimplePrimeBrokerageCreditFISideDisabled()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            removePrimeBrokerSettings();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 3 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 3 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 2 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 2 );

            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // disable the prime brokerage credit.
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( false );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            // check the available credit limit for USD and CHF. This dealing limit will be available credit limit between taker's credit for maker and bilateral available between maker pb and maker.
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            DealingLimit dealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, usd, chf, false );
            DealingLimit makerCreditForMakerPb = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( makerLe, makerTpForMakerPb, makerPbOrg, spotDate, usd, chf, false );
            DealingLimit makerPbCreditForTaker = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( makerPbLe, makerPbTpForTaker, takerOrg, spotDate, usd, chf, false );
            DealingLimit takerCreditForMaker = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( takerLe, takerTpForMaker, makerOrg, spotDate, usd, chf, false );
            log( "dealingLimit=" + dealingLimit + ",makerCreditForMakerPb=" + makerCreditForMakerPb + ",makerPbCreditForTaker=" + makerPbCreditForTaker + ",takerCreditForMaker" + takerCreditForMaker );

            double minBidLimit1 = makerCreditForMakerPb.getBidLimit() > makerPbCreditForTaker.getBidLimit() ? makerPbCreditForTaker.getBidLimit() : makerCreditForMakerPb.getBidLimit();
            double minBidLimit = minBidLimit1 > takerCreditForMaker.getBidLimit() ? takerCreditForMaker.getBidLimit() : minBidLimit1;
            double minOfferLimit1 = makerCreditForMakerPb.getOfferLimit() > makerPbCreditForTaker.getOfferLimit() ? makerPbCreditForTaker.getOfferLimit() : makerCreditForMakerPb.getOfferLimit();
            double minOfferLimit = minOfferLimit1 > takerCreditForMaker.getOfferLimit() ? takerCreditForMaker.getOfferLimit() : minOfferLimit1;

            assertEquals( "bid limit should be equal to min of three limits.", dealingLimit.getBidLimit(), minBidLimit );
            assertEquals( "offer limit should be equal to min of three limits.", dealingLimit.getOfferLimit(), minOfferLimit );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );

            // check bilateral credit enabled flags
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, false );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, false );
            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be true. enabled1=" + enabled1, enabled1, false );

            // now mark one of them enabled
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            setPrimeBrokerSettings();
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForTransparentPBExternalTx()
    {
        try
        {
            testPBCoverCreditUtilizationAmountUpdateForTransparentPB( true );
        }
        catch ( Exception e )
        {
            log.error( "testPBCoverCreditUtilizationAmountUpdateForTransparentPBExternalTx", e );
            fail();
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForTransparentPBNoExternalTx()
    {
        try
        {
            testPBCoverCreditUtilizationAmountUpdateForTransparentPB( false );
        }
        catch ( Exception e )
        {
            log.error( "testPBCoverCreditUtilizationAmountUpdateForTransparentPBNoExternalTx", e );
            fail();
        }
    }

    public void testCreditRelationsForTransparentPB()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();
            removePrimeBrokerSettings();

            //Scenario 1: transparent PB set for both sides enabled
            ArrayList<CreditEntity> creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, makerLe );
            assertEquals( creditRelations.size(), 6 );
            assertEquals( creditRelations.get( 0 ).getLegalEntity(), takerLe );
            assertEquals( creditRelations.get( 0 ).getTradingParty(), takerTpForTakerPb );
            assertEquals( creditRelations.get( 1 ).getLegalEntity(), takerPbLe );
            assertEquals( creditRelations.get( 1 ).getTradingParty(), takerPbTpForTaker );
            assertEquals( creditRelations.get( 2 ).getLegalEntity(), makerLe );
            assertEquals( creditRelations.get( 2 ).getTradingParty(), makerTpForMakerPb );
            assertEquals( creditRelations.get( 3 ).getLegalEntity(), makerPbLe );
            assertEquals( creditRelations.get( 3 ).getTradingParty(), makerPbTpForMaker );
            assertEquals( creditRelations.get( 4 ).getLegalEntity(), takerPbLe );
            assertEquals( creditRelations.get( 4 ).getTradingParty(), takerPbTpForMakerPb );
            assertEquals( creditRelations.get( 5 ).getLegalEntity(), makerPbLe );
            assertEquals( creditRelations.get( 5 ).getTradingParty(), makerPbTpForTakerPb );

            //scenario 2: transparent PB with PB enabled for org1 side only
            uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();
            creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, makerLe );
            assertEquals( creditRelations.size(), 3 );
            assertEquals( creditRelations.get( 0 ).getLegalEntity(), takerLe );
            assertEquals( creditRelations.get( 0 ).getTradingParty(), takerTpForTakerPb );
            assertEquals( creditRelations.get( 1 ).getLegalEntity(), takerPbLe );
            assertEquals( creditRelations.get( 1 ).getTradingParty(), takerPbTpForMaker );
            assertEquals( creditRelations.get( 2 ).getLegalEntity(), makerLe );
            assertEquals( creditRelations.get( 2 ).getTradingParty(), makerTpForTaker );

            //scenario 3: transparent PB with PB enabled for org2 side only
            uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( false );
            registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();
            creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, makerLe );
            assertEquals( creditRelations.size(), 3 );
            assertEquals( creditRelations.get( 0 ).getLegalEntity(), makerLe );
            assertEquals( creditRelations.get( 0 ).getTradingParty(), makerTpForMakerPb );
            assertEquals( creditRelations.get( 1 ).getLegalEntity(), makerPbLe );
            assertEquals( creditRelations.get( 1 ).getTradingParty(), makerPbTpForTaker );
            assertEquals( creditRelations.get( 2 ).getLegalEntity(), takerLe );
            assertEquals( creditRelations.get( 2 ).getTradingParty(), takerTpForMaker );

            //scenario 4: transparent PB with both PB disabled
            uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( false );
            registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();
            creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, makerLe );
            assertEquals( creditRelations.size(), 2 );
            assertEquals( creditRelations.get( 0 ).getLegalEntity(), takerLe );
            assertEquals( creditRelations.get( 0 ).getTradingParty(), takerTpForMaker );
            assertEquals( creditRelations.get( 1 ).getLegalEntity(), makerLe );
            assertEquals( creditRelations.get( 1 ).getTradingParty(), makerTpForTaker );
        }
        catch ( Exception e )
        {
            fail( "testCreditRelationsForTransparentPB", e );
        }
        finally
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();
            setPrimeBrokerSettings();
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForTransparentPB( boolean createExternalTx )
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            removePrimeBrokerSettings();

            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            double tradeAmt = 1000;
            Trade takerMakerTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, takerMakerTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );

            IdcTransaction tx = null;
            if ( createExternalTx )
            {
                tx = initTransaction( true );
            }
            // now update the counterpartyB of taker maker trade.
            takerMakerTrade.setCounterpartyB( takerPbLe );
            double pbTradeAmt = 900;
            Trade pbMakerTrade = prepareSingleLegTrade( pbTradeAmt, true, false, EURUSD, takerPbOrg, takerPbTpForMaker, bidRates[0], spotDate );
            pbMakerTrade.setTransactionID( takerMakerTrade.getTransactionID() + 'C' );
            takerMakerTrade.setCoverTradeTxIds( pbMakerTrade.getTransactionID() );
            TradeCacheC.getInstance().add( pbMakerTrade );
            TradeCacheC.getInstance().add( takerMakerTrade );

            // now update the taker maker trade.
            CreditWorkflowMessage updateCwm = creditMgr.updateBilateralCreditUtilizationAmount( makerLe, takerLe, takerMakerTrade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Message success status=" + updateCwm.getStatus(), updateCwm.getStatus(), MessageStatus.SUCCESS );
            if ( createExternalTx )
            {
                commitTransaction( tx );
            }


            double aggregateLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfterUpdate0=" + aggregateLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUpdate0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfterUpdate1=" + aggregateLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUpdate1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfterUpdate2=" + aggregateLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfterUpdate3=" + aggregateLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUpdate3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfterUpdate4=" + aggregateLimitAfterUpdate4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUpdate4 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfterUpdate5=" + aggregateLimitAfterUpdate5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUpdate5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfterUpdate0=" + dailyLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUpdate0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfterUpdate1=" + dailyLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUpdate1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfterUpdate2=" + dailyLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfterUpdate3=" + dailyLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUpdate3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfterUpdate4=" + dailyLimitAfterUpdate4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUpdate4 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfterUpdate5=" + dailyLimitAfterUpdate5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUpdate5 ) < CREDIT_CALCULATION_MINIMUM );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();

            setPrimeBrokerSettings();
        }
    }

    public void testPBCoverAmountUpdateForTransparentPBValueDateChange()
    {
        try
        {
            boolean createExternalTx = true;
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
            registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            removePrimeBrokerSettings();

            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            double tradeAmt = 1000;
            Trade takerMakerTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, takerMakerTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );

            IdcTransaction tx = null;
            if ( createExternalTx )
            {
                tx = initTransaction( true );
            }
            // now update the counterpartyB of taker maker trade.
            takerMakerTrade.setCounterpartyB( takerPbLe );
            //update the value date of trade - updated by the provider
            updateTradeValueDate( takerMakerTrade, 2 );

            double pbTradeAmt = 900;
            Trade pbMakerTrade = prepareSingleLegTrade( pbTradeAmt, true, false, EURUSD, takerPbOrg, takerPbTpForMaker, bidRates[0], spotDate );
            pbMakerTrade.setTransactionID( takerMakerTrade.getTransactionID() + 'C' );
            takerMakerTrade.setCoverTradeTxIds( pbMakerTrade.getTransactionID() );
            TradeCacheC.getInstance().add( pbMakerTrade );
            TradeCacheC.getInstance().add( takerMakerTrade );

            // now update the taker maker trade.
            CreditWorkflowMessage updateCwm = creditMgr.updateBilateralCreditUtilizationAmount( makerLe, takerLe, takerMakerTrade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Message success status=" + updateCwm.getStatus(), updateCwm.getStatus(), MessageStatus.SUCCESS );
            if ( createExternalTx )
            {
                commitTransaction( tx );
            }


            double aggregateLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdateForNextDay0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfterUpdate0=" + aggregateLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUpdate0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfterUpdate1=" + aggregateLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUpdate1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfterUpdate2=" + aggregateLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfterUpdate3=" + aggregateLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUpdate3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfterUpdate4=" + aggregateLimitAfterUpdate4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUpdate4 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfterUpdate5=" + aggregateLimitAfterUpdate5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUpdate5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfterUpdate0=" + dailyLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUpdate0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfterUpdateForNextDay0=" + dailyLimitAfterUpdateForNextDay0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUpdateForNextDay0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfterUpdate1=" + dailyLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUpdate1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfterUpdate2=" + dailyLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfterUpdate3=" + dailyLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUpdate3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfterUpdate4=" + dailyLimitAfterUpdate4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUpdate4 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfterUpdate5=" + dailyLimitAfterUpdate5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUpdate5 ) < CREDIT_CALCULATION_MINIMUM );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();

        }
        finally
        {
            setPrimeBrokerSettings();
        }
    }
}
