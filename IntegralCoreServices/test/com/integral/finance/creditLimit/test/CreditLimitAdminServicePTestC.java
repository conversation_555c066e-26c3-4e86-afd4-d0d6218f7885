package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.businessCenter.BusinessCenter;
import com.integral.businessCenter.BusinessCenterC;
import com.integral.businessCenter.BusinessCenterFactory;
import com.integral.exception.IdcDatabaseException;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.*;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.CreditTenorProfile;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.model.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.persistence.*;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rule.Rule;
import com.integral.session.IdcTransaction;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * Tests the credit limit admin service API
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAdminServicePTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Admin Service Test";

    public CreditLimitAdminServicePTestC( String name )
    {
        super( name );
    }

    //validating constraint rulesetid, cptyid from idcrule where type='IdcCptyCLRC' and cptyid is not null
    public void testCreditConstraintsIdcRuleCptyIdNotNull()
    {
        IdcTransaction tx = null;
        try
        {
            tx = initTransaction( false );
            ExpressionBuilder eb = new ExpressionBuilder();
            ReadAllQuery query = new ReadAllQuery( eb );
            query.setReferenceClass( CounterpartyCreditLimitRuleC.class );
            Expression expr = eb.get( "tradingParty" ).notNull();
            query.setSelectionCriteria( expr );
            List clrs = ( List ) tx.getUOW().executeQuery( query );
            CounterpartyCreditLimitRule rule = ( CounterpartyCreditLimitRule ) clrs.get( 0 );
            CounterpartyCreditLimitRule rule2 = new CounterpartyCreditLimitRuleC();
            rule2 = ( CounterpartyCreditLimitRule ) rule2.getRegisteredObject();
            rule2.setRuleSet( rule.getRuleSet() );
            rule2.setTradingParty( rule.getTradingParty() );
            commitTransaction( tx );
        }
        catch ( IdcDatabaseException e )
        {
            releaseTransaction( tx );
            return;
        }
        catch ( Exception e )
        {
            fail();
        }
        fail();
    }

    // validating rulesetid, cptyid from idcrule where type='IdcCptyCLRC' and cptyid is  null
    public void testCreditConstraintsIdcRuleCptyIdNull()
    {
        IdcTransaction tx = null;
        try
        {
            tx = initTransaction( false );
            ExpressionBuilder eb = new ExpressionBuilder();
            ReadAllQuery query = new ReadAllQuery( eb );
            query.setReferenceClass( CounterpartyCreditLimitRuleC.class );
            Expression expr = eb.get( "tradingParty" ).isNull();
            query.setSelectionCriteria( expr );
            List clrs = ( List ) tx.getUOW().executeQuery( query );
            CounterpartyCreditLimitRule rule = ( CounterpartyCreditLimitRule ) clrs.get( 0 );
            CounterpartyCreditLimitRule rule2 = new CounterpartyCreditLimitRuleC();
            rule2 = ( CounterpartyCreditLimitRule ) rule2.getRegisteredObject();
            rule2.setRuleSet( rule.getRuleSet() );
            rule2.setTradingPartyOrganization(rule.getTradingPartyOrganization());
            commitTransaction( tx );
        }
        catch ( IdcDatabaseException e )
        {
            releaseTransaction( tx );
            return;
        }
        catch ( Exception e )
        {
            fail();
        }
        fail();
    }

    //validating constraint type, ruleparentid from idcrule where type in ('IdcDlyCLRC',)
    public void testCreditConstraintsIdcRuleTypeDaily()
    {
        IdcTransaction tx = null;
        try
        {
            tx = initTransaction( false );
            ExpressionBuilder eb = new ExpressionBuilder();
            ReadAllQuery query = new ReadAllQuery( eb );
            query.setReferenceClass(DailyCreditLimitRuleC.class);
            List clrs = ( List ) tx.getUOW().executeQuery( query );
            DailyCreditLimitRule rule = ( DailyCreditLimitRuleC ) clrs.get( 0 );
            DailyCreditLimitRuleC rule2 = new DailyCreditLimitRuleC();
            rule2 = ( DailyCreditLimitRuleC ) rule2.getRegisteredObject();
            rule2.setParentRule(rule.getParentRule());
            commitTransaction( tx );
        }
        catch ( IdcDatabaseException e )
        {
            releaseTransaction( tx );
            return;
        }
        catch ( Exception e )
        {
            fail();
        }
        fail();
    }

    //validating constraint type, ruleparentid from idcrule where type in ( 'IdcSingleCLRC')
    public void testCreditConstraintsIdcRuleTypeSingle()
    {
        IdcTransaction tx = null;
        try
        {
            tx = initTransaction( false );
            ExpressionBuilder eb = new ExpressionBuilder();
            ReadAllQuery query = new ReadAllQuery( eb );
            query.setReferenceClass( SingleCreditLimitRule.class );
            List clrs = ( List ) tx.getUOW().executeQuery( query );
            SingleCreditLimitRule sclr1 = ( SingleCreditLimitRule ) clrs.get( 0 );
            SingleCreditLimitRule sclr2 = new SingleCreditLimitRuleC();
            sclr2 = ( SingleCreditLimitRule ) sclr2.getRegisteredObject();
            sclr2.setParentRule( sclr1.getParentRule() );
            commitTransaction( tx );
        }
        catch ( IdcDatabaseException e )
        {
            releaseTransaction( tx );
            return;
        }
        catch ( Exception e )
        {
            fail();
        }
        fail();
    }

    //validating constraint  on IdcCreditUtil   type, cudate, creditlimitruleid where type='IdcDailyCredUtilC'
    public void testCreditConstraintsIdcDailyCreditUtil()
    {
        IdcTransaction tx = null;
        try
        {
            tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( DailyCreditUtilizationC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            tx.getUOW().removeReadOnlyClass(CreditLimitRule.class);
            DailyCreditUtilization dcu = null;
            Collection<CreditUtilization> cus = CreditUtilizationManagerC.getInstance().getCreditUtilizations( lpLe, fiOrg, lpTpForFi, spotDate, CreditLimitConstants.SUBSCRIBE_EVENT );
            for ( CreditUtilization cu : cus )
            {
                if ( cu instanceof DailyCreditUtilization )
                {
                    dcu = ( DailyCreditUtilization ) cu;
                    break;
                }
            }
            DailyCreditUtilizationC dcu2 = new DailyCreditUtilizationC();
            dcu2 = ( DailyCreditUtilizationC ) dcu2.getRegisteredObject();
            CreditLimitRule clr = ( CreditLimitRule ) dcu.getCreditLimitRule().getRegisteredObject();
            dcu2.setDate( dcu.getDate() );
            dcu2.setCreditLimitRule(clr);
            commitTransaction( tx );
        }
        catch ( IdcDatabaseException e )
        {
            releaseTransaction( tx );
            return;
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        fail();
    }

    //validating constraint  on IdcCreditUtil   type, creditlimitruleid where type='IdcCrdtUtilC'
    public void testCreditConstraintsIdcCreditUtil()
    {
        IdcTransaction tx = null;
        try
        {
            tx = initTransaction( false );
            ExpressionBuilder eb = new ExpressionBuilder();
            ReadAllQuery query = new ReadAllQuery( eb );
            query.setReferenceClass(CreditUtilizationC.class);
            Expression expr = eb.get( "date" ).isNull().and( eb.get( "creditLimitRule" ).notNull() );
            query.setSelectionCriteria(expr);
            List clrs = ( List ) tx.getUOW().executeQuery( query );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            CreditUtilizationC cu = ( CreditUtilizationC ) clrs.get( 0 );
            tx.getUOW().removeReadOnlyClass( DailyCreditUtilizationC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            tx.getUOW().removeReadOnlyClass( CreditLimitRule.class );
            CreditLimitRule clr = ( CreditLimitRule ) cu.getCreditLimitRule().getRegisteredObject();
            CreditUtilizationC cu2 = new CreditUtilizationC();
            cu2 = ( CreditUtilizationC ) cu2.getRegisteredObject();
            cu2.setCreditLimitRule( clr );
            commitTransaction( tx );
        }
        catch ( IdcDatabaseException e )
        {
            releaseTransaction( tx );
            return;
        }
        catch ( Exception e )
        {
            fail( "testCreditConstraintsIdcCreditUtil", e );
        }
        fail();
    }

    public void sanityCheckAllOrganizations()
    {
        try
        {
            Collection<Organization> orgs = ( Collection<Organization> ) PersistenceFactory.newSession().readAllObjects( Organization.class );
            for ( Organization org : orgs )
            {
                boolean sanityCheck = sanityCheck( org );
                assertEquals( "org=" + org, sanityCheck, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testChangeCreditExposureWithEvents()
    {
        try
        {
            init( fiUser );

            removeExistingCreditUtilizationEvents( fiOrg );

            // now do a trade.
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 );
            double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
            double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < 1 );
            validateCreditUtilizationEvents( cwm );
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) cwm.getCreditUtilizationEvents().toArray()[0];
            log( "CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );

            // execute various credit workflows here.
            executeCreditWorkflows( fiOrg, fiLe, cptyOrg, fiTpforCpty );
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( fiOrg, cptyOrg );
            if ( !isOrgLevel )
            {
                log( "setting org level credit exposure. org=" + fiOrg );
                testSetOrgCreditExposure();
            }
            else
            {
                log( "setting le level credit exposure. org=" + fiOrg );
                testSetLegalEntityCreditExposure();
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 );
        }
    }

    public void testCreditExposure()
    {
        try
        {
            init(lpUser);
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, ddOrg );
            if ( !isOrgLevel )
            {
                CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
                assertEquals( "Active rule should not be null=" + activeCclr, activeCclr != null, true );
                assertEquals( "Active rule status should be org level=" + activeCclr.isOrgLevel(), activeCclr.isOrgLevel(), false );
                assertEquals( "Active rule status should have a tporg=" + activeCclr.getTradingPartyOrganization(), activeCclr.getTradingPartyOrganization() != null, true );

                CounterpartyCreditLimitRule orgCclr = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( lpOrg, lpTpForDd );
                assertEquals( "orgCclr should have null trading party=" + orgCclr.getTradingParty(), orgCclr.getTradingParty() == null, true );
                assertEquals( "orgCclr should be inactive=" + orgCclr.isActive(), orgCclr.isActive(), false );

                CounterpartyCreditLimitRule tpCclr = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( lpOrg, lpTpForDd );
                assertEquals( "cptyRule should have a tp=" + tpCclr.getTradingParty(), tpCclr.getTradingParty() != null, true );
                assertEquals( "cptyRule should be active=" + tpCclr.isActive(), tpCclr.isActive(), true );
                assertEquals( "cptyRule status should be tp level=" + tpCclr.isOrgLevel(), tpCclr.isOrgLevel(), false );
            }
            else
            {
                CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
                assertEquals( "Active rule should not be null=" + activeCclr, activeCclr != null, true );
                assertEquals( "Active rule status should be org level=" + activeCclr.isOrgLevel(), activeCclr.isOrgLevel(), true );
                assertEquals( "Active rule status should have a tporg=" + activeCclr.getTradingPartyOrganization(), activeCclr.getTradingPartyOrganization() != null, true );

                CounterpartyCreditLimitRule orgCclr = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( lpOrg, lpTpForDd );
                assertEquals( "orgCclr should have null trading party=" + orgCclr.getTradingParty(), orgCclr.getTradingParty() == null, true );
                assertEquals( "orgCclr should be active=" + orgCclr.isActive(), orgCclr.isActive(), true );

                CounterpartyCreditLimitRule tpCclr = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( lpOrg, lpTpForDd );
                assertEquals( "cptyRule should have a tp=" + tpCclr.getTradingParty(), tpCclr.getTradingParty() != null, true );
                assertEquals( "cptyRule should be inactive=" + tpCclr.isActive(), tpCclr.isActive(), false );
                assertEquals( "cptyRule status should be org level=" + tpCclr.isOrgLevel(), tpCclr.isOrgLevel(), true );
            }
            assertEquals("checkSanity", sanityCheck(lpOrg), true);
        }
        catch ( Exception e )
        {
            log.error( "testCreditExposure", e );
            fail();
        }
    }


    public void testSetOrgCreditExposure()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, ddOrg );
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, ddOrg );
            assertEquals( "isOrglevel : " + isOrgLevel, isOrgLevel, true );
            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testSetOrgCreditExposure", e );
            fail();
        }
    }

    public void testSetLegalEntityCreditExposure()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, ddOrg);
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, ddOrg );
            assertEquals( "isOrglevel : " + isOrgLevel, isOrgLevel, false );
            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testSetLegalEntityCreditExposure", e );
            fail();
        }
    }

    public void testNettingCalculatorChanges()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setNettingMethodology(lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR);
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            Iterator iter = cclr.getChildrenRules().iterator();
            while ( iter.hasNext() )
            {
                CreditLimitRule clr = ( CreditLimitRule ) iter.next();
                if ( clr instanceof SingleCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), AGGREGATE_LIMIT_CALCULATOR );
                }
                else if ( clr instanceof DailyCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), GROSS_DAILY_LIMIT_CALCULATOR );
                }
            }

            // now change the single type to NOP and daily type to Daily settlement.
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR );

            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            Iterator iter1 = cclr1.getChildrenRules().iterator();
            while ( iter1.hasNext() )
            {
                CreditLimitRule clr = ( CreditLimitRule ) iter1.next();
                if ( clr instanceof SingleCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), NET_OPEN_POSITION_CALCULATOR );
                }
                else if ( clr instanceof DailyCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), DAILY_SETTLEMENT_LIMIT_CALCULATOR );
                }
            }

            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testNettingCalculatorChanges", e );
            fail();
        }
    }

    public void testNettingMethodologyChangeWithEvents()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            IdcTransaction tx = initTransaction( true );

            Currency ccy = CreditUtilC.getLimitCurrency( lpOrg );
            if ( !ccy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                return;
            }

            // do a trade
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            commitTransaction( tx );
            sleepFor( 4000 );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < MINIMUM );
            validateCreditUtilizationEvents( cwm );
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) cwm.getCreditUtilizationEvents().toArray()[0];
            log( "CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );

            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            log( "cclr children rule size=" + cclr.getChildrenRules().size() );
            Iterator iter = cclr.getChildrenRules().iterator();
            while ( iter.hasNext() )
            {
                CreditLimitRule clr = ( CreditLimitRule ) iter.next();
                log( "clr=" + clr + "clsf=" + clr.getClassification() + ",calc=" + clr.getCreditUtilizationCalculator() );
                if ( clr instanceof DailyCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator().getShortName(), DAILY_SETTLEMENT_LIMIT_CALCULATOR.getShortName() );
                }
                else if ( clr instanceof SingleCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator().getShortName(), AGGREGATE_LIMIT_CALCULATOR.getShortName() );
                }
            }

            // inspect the credit utilization event again.
            log( "After netting methodology changes. CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );
            cue = ( CreditUtilizationEvent ) PersistenceFactory.newSession().refreshObject( cue );
            log( "After refreshing. CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );

            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testNettingMethodologyChangeWithEvents", e );
            fail();
        }
    }

    public void testCreditEnableOrgLevel()
    {
        try
        {
            init( lpUser );
            boolean enable = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( lpOrg, lpTpForDd );
            log( "Credit enabled for org=" + lpOrg + ",enabled=" + enable );
            if ( enable )
            {
                double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate );
                AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForDd, ddOrg, spotDate );
                assertEquals( "aoi should not be null. aoi=" + aoi, aoi != null, true );

                Double availableLimit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( lpLe, lpTpForDd, ddOrg, spotDate );
                assertEquals( "availableLimit should not be null. availableLimit=" + aoi, availableLimit != null, true );

                // now disable the credit for provider.
                creditAdminSvc.setCreditEnabled( lpOrg, false );

                double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate );
                double tradeAmt = 1000;
                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
                double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate );
                log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );

                boolean enable1 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( lpOrg, lpTpForDd );

                // put it back to enabled for other test cases.
                creditAdminSvc.setCreditEnabled( lpOrg, true );

                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit2, true, Math.abs( limit2 - limit1 ) < MINIMUM );
                assertEquals( "Credit should be success. status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
                assertEquals( "Should be disabled. enable=" + enable1, enable1, false );

            }
            else
            {
                double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate );
                AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForDd, ddOrg, spotDate );
                assertEquals( "aoi should be null. aoi=" + aoi, aoi == null, true );

                Double availableLimit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( lpLe, lpTpForDd, ddOrg, spotDate );
                assertEquals( "availableLimit should be null. availableLimit=" + aoi, availableLimit == null, true );

                // now enable the credit for provider.
                creditAdminSvc.setCreditEnabled( lpOrg, true );

                double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate );
                double tradeAmt = 1000;
                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
                double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate );
                log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < MINIMUM );
                assertEquals( "Credit should be success. status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                boolean enable1 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( lpOrg, lpTpForDd );
                assertEquals( "Should be enabled. enable=" + enable1, enable1, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditEnableTradingPartyLevel()
    {
        try
        {
            init( fiUser );
            boolean enable = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( fiOrg, fiTpforCpty );
            log( "Credit enabled for org=" + fiOrg + ",enabled=" + enable );
            if ( enable )
            {
                double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate );
                AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpforCpty, cptyOrg, spotDate );
                assertEquals( "aoi should not be null. aoi=" + aoi, aoi != null, true );

                Double availableLimit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( fiLe, fiTpforCpty, cptyOrg, spotDate );
                assertEquals( "availableLimit should not be null. availableLimit=" + aoi, availableLimit != null, true );

                // now disable the credit for provider.
                creditAdminSvc.setCreditEnabled( fiOrg, fiTpforCpty, false );

                boolean isCclrEnabled = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(fiOrg, fiTpforCpty).isEnabled();
                log( "cclr enabled=" + isCclrEnabled );
                assertEquals( "cpty limit rule should be disabled=" + isCclrEnabled, isCclrEnabled, false );

                boolean isEnabled = CreditUtilizationManagerC.getInstance().isCreditEnabled(fiOrg, fiTpforCpty);
                log("enable cache value=" + isEnabled);
                assertEquals("credit should be disabled=" + isEnabled, isEnabled, false);

                double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate );
                double tradeAmt = 1000;
                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
                double limit2 = getAvailableCreditLimit( fiTpforCpty, spotDate );
                log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
                boolean enable1 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( fiOrg, fiTpforCpty );

                // put it back to enabled for other test cases.
                creditAdminSvc.setCreditEnabled( fiOrg, fiTpforCpty, true );

                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit2, true, Math.abs( limit2 - limit1 ) < MINIMUM );
                assertEquals( "Credit should be success. status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                assertEquals( "Should be disabled. enable=" + enable1, enable1, false );

            }
            else
            {
                double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate );
                AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpforCpty, cptyOrg, spotDate );
                assertEquals( "aoi should be null. aoi=" + aoi, aoi == null, true );

                Double availableLimit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( fiLe, fiTpforCpty, cptyOrg, spotDate );
                assertEquals( "availableLimit should be null. availableLimit=" + aoi, availableLimit == null, true );

                // now enable the credit for provider.
                creditAdminSvc.setCreditEnabled( fiOrg, fiTpforCpty, true );
                boolean isEnabled = CreditUtilizationManagerC.getInstance().isCreditEnabled( fiOrg, fiTpforCpty );
                assertEquals("credit should be disabled=" + isEnabled, isEnabled, true);

                double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate );
                double tradeAmt = 1000;
                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
                double limit2 = getAvailableCreditLimit( fiTpforCpty, spotDate );
                log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < MINIMUM );
                assertEquals( "Credit should be success. status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                boolean enable1 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( fiOrg, fiTpforCpty );
                assertEquals( "Should be enabled. enable=" + enable1, enable1, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetupCreditProvider()
    {
        try
        {
            init( adminUser );

            if ( CreditUtilC.getCreditLimitRuleSet( ddOrg ) != null )
            {
                return;
            }

            creditAdminSvc.setupCreditProvider( ddOrg, staticMds );

            // check whether we have a valid credit limit org function and credit limit ruleset.
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( ddOrg );
            log( "orgFunc=" + orgFunc );
            assertEquals( "credit limit org funciton=" + orgFunc, orgFunc != null, true );
            log( "supported credit types=" + orgFunc.getSupportedCreditLimitClassifications() );
            assertEquals( "org function supported credit types=" + orgFunc.getSupportedCreditLimitClassifications(), orgFunc.getSupportedCreditLimitClassifications().isEmpty(), false );
            log( "org func credit utilization period=" + orgFunc.getCreditUtilizationPeriod() );
            assertEquals( "org function credit utilization period=" + orgFunc.getCreditUtilizationPeriod(), orgFunc.getCreditUtilizationPeriod() != null, true );
            CreditLimitRuleSet clrs = orgFunc.getCreditLimitRuleSet();
            log( "clrs=" + clrs );
            assertEquals( "credit limit rule set=" + clrs, clrs != null, true );
            assertEquals( "credit limit ruleset is indexed=" + clrs.isRulesIndexed(), clrs.isRulesIndexed(), true );
            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditProviderEnabled() )
            {
                assertEquals( "credit limit ruleset is enabled=" + clrs.isEnabled(), clrs.isEnabled(), true );
            }
            else
            {
                assertEquals( "credit limit ruleset is enabled=" + clrs.isEnabled(), clrs.isEnabled(), false );
            }
            assertEquals( "namespace should match. org ns=" + ddOrg.getNamespace() + ",clrs.ns=" + clrs.getNamespace(), ddOrg.getNamespace().isSameAs( clrs.getNamespace() ), true );
            log( "ns=" + ddOrg.getNamespace() + ",clrs.mds=" + clrs.getMarketDataSet() + ",orgFunc.mds=" + orgFunc.getMarketDataSet() );
            MarketDataSet mds = clrs.getMarketDataSet();
            assertEquals( "market data set=" + mds, mds != null, true );
            assertEquals( "org func has same mds=" + orgFunc.getMarketDataSet(), orgFunc.getMarketDataSet() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetupCreditProviderExternalTx()
    {
        try
        {
            init( adminUser );
            IdcTransaction tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( BusinessCenterC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );
            Organization org = UserFactory.newOrganization();
            Organization registeredOrg = ( Organization ) org.getRegisteredObject();
            registeredOrg.setShortName( "NewOrg" + System.currentTimeMillis() );
            registeredOrg.setLongName( "LongName" );
            Namespace ns = ( Namespace ) EntityFactory.newNamespace().getRegisteredObject();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            registeredOrg.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = ( LegalEntity ) CounterpartyFactory.newLegalEntity().getRegisteredObject();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( registeredOrg );
            registeredOrg.getLegalEntities().add( le );
            registeredOrg.setDefaultDealingEntity( le );

            // set the business center reporting currency.
            BusinessCenter bs = ( BusinessCenter ) BusinessCenterFactory.newBusinessCenter().getRegisteredObject();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            creditAdminSvc.setupCreditProvider( registeredOrg, staticMds );
            commitTransaction( tx );

            // check whether we have a valid credit limit org function and credit limit ruleset.
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( org );
            log( "orgFunc=" + orgFunc );
            assertEquals( "credit limit org funciton=" + orgFunc, orgFunc != null, true );
            log( "supported credit types=" + orgFunc.getSupportedCreditLimitClassifications() );
            assertEquals( "org function supported credit types=" + orgFunc.getSupportedCreditLimitClassifications(), orgFunc.getSupportedCreditLimitClassifications().isEmpty(), false );
            assertEquals( "org function should have limit currency=" + orgFunc.getCreditLimitCurrency(), orgFunc.getCreditLimitCurrency() != null, true );
            log( "org func credit utilization period=" + orgFunc.getCreditUtilizationPeriod() );
            assertEquals( "org function credit utilization period=" + orgFunc.getCreditUtilizationPeriod(), orgFunc.getCreditUtilizationPeriod() != null, true );
            CreditLimitRuleSet clrs = orgFunc.getCreditLimitRuleSet();
            log( "clrs=" + clrs );
            assertEquals( "credit limit rule set=" + clrs, clrs != null, true );
            assertEquals( "credit limit ruleset is indexed=" + clrs.isRulesIndexed(), clrs.isRulesIndexed(), true );
            assertEquals( "credit limit ruleset is matchfirst=" + clrs.isMatchFirst(), clrs.isMatchFirst(), true );
            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditProviderEnabled() )
            {
                assertEquals( "credit limit ruleset is enabled=" + clrs.isEnabled(), clrs.isEnabled(), true );
            }
            else
            {
                assertEquals( "credit limit ruleset is enabled=" + clrs.isEnabled(), clrs.isEnabled(), false );
            }
            assertEquals( "namespace should match. org ns=" + ns + ",clrs.ns=" + clrs.getNamespace(), ns.isSameAs( clrs.getNamespace() ), true );
            log( "ns=" + ns + ",clrs.mds=" + clrs.getMarketDataSet() + ",orgFunc.mds=" + orgFunc.getMarketDataSet() );
            MarketDataSet mds = clrs.getMarketDataSet();
            assertEquals( "market data set=" + mds, mds != null, true );
            assertEquals( "org func has same mds=" + orgFunc.getMarketDataSet(), orgFunc.getMarketDataSet() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditRelationshipAtOrgLevel()
    {
        try
        {
            init( adminUser );

            // tweak the credit limit org function to set at org level.
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            boolean exposureLevel = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( true );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            orgFunc.setOrgLevelCreditExposure( exposureLevel );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertEquals( "cclr is not null", cclr != null, true );
            if ( cclr != null )
            {
                log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
                assertEquals( "cclr active ", cclr.isActive(), true );
                log( "children rules=" + cclr.getChildrenRules() );
                assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

                // check the spot date credit utilization.
                Double limit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( lpLe, lpTpForFi, fiOrg, spotDate );
                log( "spot date limit=" + limit );
                assertEquals( "limit is not null. ", limit != null, true );

                // now remove the trading relationship.
                boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, fiOrg );
                log( "exposure level at org=" + isOrgLevel );
                creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
                CounterpartyCreditLimitRule cclr1 = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( lpOrg, lpTpForFi );
                log( "cclr1=" + cclr1 + ",cclr1.enabled=" + cclr1.isEnabled() + ",status=" + cclr1.getStatus() );
                assertEquals( "cclr1 is not null", cclr1 != null, true );
                if ( isOrgLevel )
                {
                    assertEquals( "cclr1 active ", cclr1.isActive(), true );
                }
                else
                {
                    assertEquals( "cclr1 active ", cclr1.isActive(), false );
                }
                log( "children rules=" + cclr1.getChildrenRules() );
                assertEquals( "cclr1 children rules not empty", cclr1.getChildrenRules().isEmpty(), false );

                // now re-establish credit so that other test cases can work fine.
                creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
                CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
                log( "cclr=" + activeCclr + ",cclr.enabled=" + activeCclr.isEnabled() + ",status=" + activeCclr.getStatus() );
                assertEquals( "cclr is not null", activeCclr != null, true );
                assertEquals( "cclr active ", activeCclr.isActive(), true );
                log( "children rules=" + activeCclr.getChildrenRules() );
                assertEquals( "cclr children rules not empty", activeCclr.getChildrenRules().isEmpty(), false );
                assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditRelationshipAtTradingPartyLevel()
    {
        try
        {
            init( adminUser );

            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            boolean exposure = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( false );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            orgFunc.setOrgLevelCreditExposure( exposure );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            // check the spot date credit utilization.
            Double limit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( lpLe, lpTpForFi, fiOrg, spotDate );
            log( "spot date limit=" + limit );
            assertEquals( "limit is not null. ", limit != null, true );

            // now remove the trading relationship.
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( lpOrg, lpTpForFi );
            log( "cclr1=" + cclr1 + ",cclr1.enabled=" + cclr1.isEnabled() + ",status=" + cclr1.getStatus() );
            assertEquals( "cclr1 is not null", cclr1 != null, true );
            assertEquals( "cclr1 active ", cclr1.isActive(), false );
            log( "children rules=" + cclr1.getChildrenRules() );
            assertEquals( "cclr1 children rules not empty", cclr1.getChildrenRules().isEmpty(), false );

            // now re-establish credit so that other test cases can work fine.
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            log( "cclr=" + activeCclr + ",cclr.enabled=" + activeCclr.isEnabled() + ",status=" + activeCclr.getStatus() );
            assertEquals( "cclr is not null", activeCclr != null, true );
            assertEquals( "cclr active ", activeCclr.isActive(), true );
            log( "children rules=" + activeCclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", activeCclr.getChildrenRules().isEmpty(), false );
            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditLimitForDate()
    {
        try
        {
            init( lpUser );
            double limit0 = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate );
            log( "limit on spot date=" + limit0 );
            double newLimitOnSpotPlusTwo = limit0 + 100000000;
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, newLimitOnSpotPlusTwo, limitCcy, spotDate.addDays( 2 ) );
            double limitOnSpotPlusTwo = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate.addDays( 2 ) );
            log( "limit on spot date=" + limit0 + ",limit on spot+2=" + limitOnSpotPlusTwo );
            assertEquals( "new limit", Math.abs( newLimitOnSpotPlusTwo - limitOnSpotPlusTwo ) < MINIMUM, true );

            // now the set the limit globally
            double newLimit = limit0 + 1000000;
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, newLimit, limitCcy );
            double limitOnSpot = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate );
            double spotPlusTwoLimit = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate.addDays( 2 ) );
            log( "limitOnSpot=" + limitOnSpot + ",spotPlusTwoLimit=" + spotPlusTwoLimit );
            assertEquals( "limit should be same on all days.", spotPlusTwoLimit, limitOnSpotPlusTwo );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testResetCreditLimitForDate()
    {
        try
        {
            init( lpUser );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, ddOrg, lpTpForDd);
            creditAdminSvc.setCreditLimit(lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, limitCcy, spotDate);
            double limit0 = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate );
            log( "limit on spot date=" + limit0 );
            double newLimitOnSpotPlusTwo = limit0 + 100000000;
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, newLimitOnSpotPlusTwo, limitCcy, spotDate.addDays( 2 ) );
            double limitOnSpotPlusTwo = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate.addDays( 2 ) );
            log( "limit on spot date=" + limit0 + ",limit on spot+2=" + limitOnSpotPlusTwo );
            assertEquals( "new limit", Math.abs( newLimitOnSpotPlusTwo - limitOnSpotPlusTwo ) < MINIMUM, true );

            // now reset the date specific limit to null.
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, limitCcy, spotDate.addDays( 2 ) );
            double limitOnSpot = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate );
            double spotPlusTwoLimit = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate.addDays( 2 ) );
            log("limitOnSpot=" + limitOnSpot + ",spotPlusTwoLimit=" + spotPlusTwoLimit);
            assertEquals( "limit should be same on all days.", Math.abs( limitOnSpot - spotPlusTwoLimit ) < MINIMUM, true );
            assertEquals("limit should be same as new limit.", Math.abs(limitOnSpot - limit0) < MINIMUM, true);
            CreditUtilizationCacheEntry cce = CreditUtilizationManagerC.getInstance().getCreditUtilizationCacheEntry(lpLe, ddOrg, lpTpForDd, spotDate.addDays(2), CreditLimitConstants.SUBSCRIBE_EVENT);
            Collection<CreditUtilization> creditUtils = cce.getCreditUtilizations();
            for ( CreditUtilization cu : creditUtils )
            {
                assertEquals( "limit on cu should be null. cu.limit=" + cu.getLimitAmount(), cu.getLimitAmount() == null, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetDefaultAggregateNettingMethodology()
    {
        try
        {
            init( lpUser );
            CreditUtilizationCalculator existingCalc = creditAdminSvc.getDefaultAggregateNettingMethodology( lpOrg );
            CreditUtilizationCalculator newCalc;
            if ( existingCalc != null && existingCalc.isSameAs( AGGREGATE_LIMIT_CALCULATOR ) )
            {
                newCalc = NET_OPEN_POSITION_CALCULATOR;
            }
            else
            {
                newCalc = AGGREGATE_LIMIT_CALCULATOR;
            }
            log( "existing calc=" + existingCalc + ",new calc=" + newCalc );
            creditAdminSvc.setDefaultAggregateNettingMethodology( lpOrg, newCalc );
            CreditUtilizationCalculator checkCalc = creditAdminSvc.getDefaultAggregateNettingMethodology( lpOrg );
            log( "newly set calc=" + checkCalc + ",new calc=" + newCalc );
            assertEquals( "check newly set calc=" + checkCalc, checkCalc.isSameAs( newCalc ), true );
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            assertEquals( "check new default aggregate calc=" + orgFunc.getDefaultAggregateCreditUtilizationCalculator(), checkCalc.isSameAs( orgFunc.getDefaultAggregateCreditUtilizationCalculator() ), true );

            // now create a new org and setup credit relationship.
            IdcTransaction tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );
            tx.getUOW().removeReadOnlyClass( NamespaceC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            tx.getUOW().removeReadOnlyClass( BusinessCenterC.class );
            Organization org = UserFactory.newOrganization();
            Organization registeredOrg = ( Organization ) org.getRegisteredObject();
            Organization registeredLpOrg = ( Organization ) lpOrg.getRegisteredObject();
            registeredOrg.setShortName( "NewOrg" + System.currentTimeMillis() );
            registeredOrg.setLongName( "LongName" );
            Namespace ns = ( Namespace ) EntityFactory.newNamespace().getRegisteredObject();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            registeredOrg.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = ( LegalEntity ) CounterpartyFactory.newLegalEntity().getRegisteredObject();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( registeredOrg );
            registeredOrg.getLegalEntities().add( le );
            registeredOrg.setDefaultDealingEntity( le );

            // set the business center reporting currency.
            BusinessCenter bs = ( BusinessCenter ) BusinessCenterFactory.newBusinessCenter().getRegisteredObject();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the new org trading party.
            TradingParty orgTp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            LegalEntity registeredLpLe = ( LegalEntity ) lpLe.getRegisteredObject();
            orgTp.setLegalEntity( registeredLpLe );
            orgTp.setShortName( lpLe.getShortName() );
            orgTp.setOrganization( registeredOrg );
            orgTp.setNamespace( ( Namespace ) registeredOrg.getNamespace().getRegisteredObject() );
            registeredOrg.getTradingParties().add( orgTp );

            // set the new lp trading party.
            TradingParty lpTp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            lpTp.setLegalEntity( ( LegalEntity ) le.getRegisteredObject() );
            lpTp.setShortName( le.getShortName() );
            lpTp.setOrganization( registeredLpOrg );
            lpTp.setNamespace( ( Namespace ) registeredLpOrg.getNamespace().getRegisteredObject() );
            registeredLpOrg.getTradingParties().add( lpTp );

            commitTransaction( tx );

            log( "le=" + le );
            log( "org=" + org );
            log( "org.tp=" + orgTp );
            log( "lp.tp=" + lpTp );
            log( "lp.tp.le.org=" + lpTp.getLegalEntityOrganization() );
            log( "registeredOrg=" + registeredOrg );
            boolean exposure = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( true );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTp );
            orgFunc.setOrgLevelCreditExposure( exposure );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTp );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals( "clr calc should be new calc=" + clr.getCreditUtilizationCalculator(), clr.getCreditUtilizationCalculator().isSameAs( newCalc ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetDefaultDailyNettingMethodology()
    {
        try
        {
            init( lpUser );
            CreditUtilizationCalculator existingCalc = creditAdminSvc.getDefaultDailyNettingMethodology(lpOrg);
            CreditUtilizationCalculator newCalc;
            if ( existingCalc != null && existingCalc.isSameAs( GROSS_DAILY_LIMIT_CALCULATOR ) )
            {
                newCalc = DAILY_SETTLEMENT_LIMIT_CALCULATOR;
            }
            else
            {
                newCalc = GROSS_DAILY_LIMIT_CALCULATOR;
            }
            log( "existing calc=" + existingCalc + ",new calc=" + newCalc );
            creditAdminSvc.setDefaultDailyNettingMethodology(lpOrg, newCalc);
            CreditUtilizationCalculator checkCalc = creditAdminSvc.getDefaultDailyNettingMethodology(lpOrg);
            log( "newly set calc=" + checkCalc + ",new calc=" + newCalc );
            assertEquals( "check newly set calc=" + checkCalc, checkCalc.isSameAs( newCalc ), true );
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            assertEquals( "check new default aggregate calc=" + orgFunc.getDefaultDailyCreditUtilizationCalculator(), checkCalc.isSameAs( orgFunc.getDefaultDailyCreditUtilizationCalculator() ), true );

            // now create a new org and setup credit relationship.
            IdcTransaction tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( BusinessCenterC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );
            tx.getUOW().removeReadOnlyClass( NamespaceC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            Organization org = UserFactory.newOrganization();
            Organization registeredOrg = ( Organization ) org.getRegisteredObject();
            Organization registeredLpOrg = ( Organization ) lpOrg.getRegisteredObject();
            registeredOrg.setShortName( "NewOrg" + System.currentTimeMillis() );
            registeredOrg.setLongName( "LongName" );
            Namespace ns = ( Namespace ) EntityFactory.newNamespace().getRegisteredObject();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            registeredOrg.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = ( LegalEntity ) CounterpartyFactory.newLegalEntity().getRegisteredObject();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( registeredOrg );
            registeredOrg.getLegalEntities().add( le );
            registeredOrg.putCustomField(DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME, le);

            // set the business center reporting currency.
            BusinessCenter bs = ( BusinessCenter ) BusinessCenterFactory.newBusinessCenter().getRegisteredObject();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the new org trading party.
            TradingParty orgTp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            LegalEntity registeredLpLe = ( LegalEntity ) lpLe.getRegisteredObject();
            orgTp.setLegalEntity( registeredLpLe );
            orgTp.setShortName( lpLe.getShortName() );
            orgTp.setOrganization( registeredOrg );
            orgTp.setNamespace( ( Namespace ) registeredOrg.getNamespace().getRegisteredObject() );
            registeredOrg.getTradingParties().add( orgTp );

            // set the new lp trading party.
            TradingParty lpTp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            lpTp.setLegalEntity( ( LegalEntity ) le.getRegisteredObject() );
            lpTp.setShortName( le.getShortName() );
            lpTp.setOrganization( registeredLpOrg );
            lpTp.setNamespace( ( Namespace ) registeredLpOrg.getNamespace().getRegisteredObject() );
            registeredLpOrg.getTradingParties().add( lpTp );

            commitTransaction( tx );

            log( "le=" + le );
            log( "org=" + org );
            log( "org.tp=" + orgTp );
            log( "lp.tp=" + lpTp );
            log( "lp.tp.le.org=" + lpTp.getLegalEntityOrganization() );
            log( "registeredOrg=" + registeredOrg );
            boolean exposure = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( true );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTp );
            orgFunc.setOrgLevelCreditExposure( exposure );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTp );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "clr calc should be new calc=" + clr.getCreditUtilizationCalculator(), clr.getCreditUtilizationCalculator().isSameAs( newCalc ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetDefaultAggregateMethodology_AggregateNetSettlementReceivable()
    {
        try
        {
            init( lpUser );
            CreditUtilizationCalculator existingCalc = creditAdminSvc.getDefaultAggregateNettingMethodology( lpOrg );
            CreditUtilizationCalculator newCalc = AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR;
            log( "existing calc=" + existingCalc + ",new calc=" + newCalc );
            creditAdminSvc.setDefaultAggregateNettingMethodology( lpOrg, newCalc );

            // now create a new org and setup credit relationship.
            IdcTransaction tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );
            tx.getUOW().removeReadOnlyClass( NamespaceC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            tx.getUOW().removeReadOnlyClass( BusinessCenterC.class );
            Organization org = UserFactory.newOrganization();
            Organization registeredOrg = ( Organization ) org.getRegisteredObject();
            Organization registeredLpOrg = ( Organization ) lpOrg.getRegisteredObject();
            registeredOrg.setShortName( "NewOrg" + System.currentTimeMillis() );
            registeredOrg.setLongName( "LongName" );
            Namespace ns = ( Namespace ) EntityFactory.newNamespace().getRegisteredObject();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            registeredOrg.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = ( LegalEntity ) CounterpartyFactory.newLegalEntity().getRegisteredObject();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( registeredOrg );
            registeredOrg.getLegalEntities().add( le );
            registeredOrg.setDefaultDealingEntity( le );

            // set the business center reporting currency.
            BusinessCenter bs = ( BusinessCenter ) BusinessCenterFactory.newBusinessCenter().getRegisteredObject();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the new org trading party.
            TradingParty orgTp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            LegalEntity registeredLpLe = ( LegalEntity ) lpLe.getRegisteredObject();
            orgTp.setLegalEntity( registeredLpLe );
            orgTp.setShortName( lpLe.getShortName() );
            orgTp.setOrganization( registeredOrg );
            orgTp.setNamespace( ( Namespace ) registeredOrg.getNamespace().getRegisteredObject() );
            registeredOrg.getTradingParties().add( orgTp );

            // set the new lp trading party.
            TradingParty lpTp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            lpTp.setLegalEntity( ( LegalEntity ) le.getRegisteredObject() );
            lpTp.setShortName( le.getShortName() );
            lpTp.setOrganization( registeredLpOrg );
            lpTp.setNamespace( ( Namespace ) registeredLpOrg.getNamespace().getRegisteredObject() );
            registeredLpOrg.getTradingParties().add( lpTp );

            commitTransaction( tx );

            log( "le=" + le );
            log( "org=" + org );
            log( "org.tp=" + orgTp );
            log( "lp.tp=" + lpTp );
            log( "lp.tp.le.org=" + lpTp.getLegalEntityOrganization() );
            log( "registeredOrg=" + registeredOrg );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTp );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTp );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals( "clr calc should be new calc=" + clr.getCreditUtilizationCalculator(), clr.getCreditUtilizationCalculator().isSameAs( newCalc ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultAggregateNettingMethodology( lpOrg, creditAdminSvc.getDefaultAggregateNettingMethodology( lpOrg ) );
        }
    }

    public void testSetDefaultLimitCurrency()
    {
        try
        {
            init( lpUser );
            Currency existingCcy = creditAdminSvc.getDefaultLimitCurrency( lpOrg );
            Currency newCcy;
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            if ( existingCcy != null && existingCcy.isSameAs( eur ) )
            {
                newCcy = usd;
            }
            else
            {
                newCcy = eur;
            }
            log( "existing ccy=" + existingCcy + ",new ccy=" + newCcy );
            creditAdminSvc.setDefaultLimitCurrency( lpOrg, newCcy );
            Currency checkCcy = creditAdminSvc.getDefaultLimitCurrency( lpOrg );
            log( "newly set ccy=" + checkCcy + ",new ccy=" + newCcy );
            assertEquals( "check newly set ccy=" + checkCcy, checkCcy.isSameAs( newCcy ), true );
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            assertEquals( "check new default ccy=" + orgFunc.getCreditLimitCurrency(), checkCcy.isSameAs( orgFunc.getCreditLimitCurrency() ), true );

            // now create a new org and setup credit relationship.
            IdcTransaction tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( BusinessCenterC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );
            tx.getUOW().removeReadOnlyClass( NamespaceC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            Organization org = UserFactory.newOrganization();
            Organization registeredOrg = ( Organization ) org.getRegisteredObject();
            Organization registeredLpOrg = ( Organization ) lpOrg.getRegisteredObject();
            registeredOrg.setShortName( "NewOrg" + System.currentTimeMillis() );
            registeredOrg.setLongName( "LongName" );
            Namespace ns = ( Namespace ) EntityFactory.newNamespace().getRegisteredObject();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            registeredOrg.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = ( LegalEntity ) CounterpartyFactory.newLegalEntity().getRegisteredObject();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( registeredOrg );
            registeredOrg.getLegalEntities().add( le );
            registeredOrg.putCustomField( DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME, le );

            // set the business center reporting currency.
            BusinessCenter bs = ( BusinessCenter ) BusinessCenterFactory.newBusinessCenter().getRegisteredObject();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the new org trading party.
            TradingParty orgTp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            LegalEntity registeredLpLe = ( LegalEntity ) lpLe.getRegisteredObject();
            orgTp.setLegalEntity( registeredLpLe );
            orgTp.setShortName( lpLe.getShortName() );
            orgTp.setOrganization( registeredOrg );
            orgTp.setNamespace( ( Namespace ) registeredOrg.getNamespace().getRegisteredObject() );
            registeredOrg.getTradingParties().add( orgTp );

            // set the new lp trading party.
            TradingParty lpTp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            lpTp.setLegalEntity( ( LegalEntity ) le.getRegisteredObject() );
            lpTp.setShortName( le.getShortName() );
            lpTp.setOrganization( registeredLpOrg );
            lpTp.setNamespace( ( Namespace ) registeredLpOrg.getNamespace().getRegisteredObject() );
            registeredLpOrg.getTradingParties().add( lpTp );

            commitTransaction( tx );

            log( "le=" + le );
            log( "org=" + org );
            log( "org.tp=" + orgTp );
            log( "lp.tp=" + lpTp );
            log( "lp.tp.le.org=" + lpTp.getLegalEntityOrganization() );
            log( "registeredOrg=" + registeredOrg );
            boolean exposure = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( true );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTp );
            orgFunc.setOrgLevelCreditExposure( exposure );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTp );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "clr ccy should be new ccy=" + clr.getCurrency(), clr.getCurrency().isSameAs( newCcy ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
            creditAdminSvc.reinitialize( lpOrg );
        }
    }

    public void testQueryAllCounterpartyOrganizations()
    {
        try
        {
            Collection<Organization> cptyOrgs = CreditUtilC.getCounterpartyOrganizations( lpOrg );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            Set<Organization> tpOrgs = new HashSet<Organization>();
            Collection<Rule> rules = clrs.getRules();
            for ( Rule rule : rules )
            {
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) rule;
                tpOrgs.add( cclr.getTradingPartyOrganization() );
            }
            log( "cptyOrgs=" + cptyOrgs + ",tpOrgs=" + tpOrgs );
            assertEquals("cptyrOrgs size=" + cptyOrgs.size(), cptyOrgs.size() > 0, true);
            assertEquals("tpOrgs and cptyOrgs size should be the same.", cptyOrgs.size(), tpOrgs.size());
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testReinitializeCreditProvider()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            IdcTransaction tx = initTransaction( true );
            creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            commitTransaction( tx );
            Collection<CreditUtilizationEvent> cues = CreditUtilC.getCreditUtilizationEvents(lpOrg, fiOrg);
            log( "cues.size=" + cues.size() );
            assertEquals( "cues size should be greater than zero.", cues.size() > 0, true );

            creditAdminSvc.reinitializeCreditCounterpartyOrganization( lpOrg, fiOrg );
            Collection<CreditUtilizationEvent> cuesAfter = CreditUtilC.getCreditUtilizationEvents(lpOrg, fiOrg);
            log( "cuesAfter=" + cuesAfter );
            assertEquals( "cuesAfter either should be null or empty", cuesAfter == null || cuesAfter.isEmpty(), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testReinitializeCreditProviderExternalTx()
    {
        try
        {
            init(lpUser);

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            IdcTransaction tx = initTransaction(true);
            creditMgr.takeCredit(trade, lpLe, fiOrg, lpTpForFi);
            commitTransaction(tx);
            Collection<CreditUtilizationEvent> cues = CreditUtilC.getCreditUtilizationEvents(lpOrg, fiOrg);
            log( "cues.size=" + cues.size() );
            assertEquals("cues size should be greater than zero.", cues.size() > 0, true);
            creditAdminSvc.reinitializeCreditCounterpartyOrganization(lpOrg, fiOrg);

            Collection<CreditUtilizationEvent> cuesAfter = CreditUtilC.getCreditUtilizationEvents( lpOrg, fiOrg );
            log( "cuesAfter=" + cuesAfter );
            assertEquals( "cuesAfter either should be null or empty", cuesAfter == null || cuesAfter.isEmpty(), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );

        }
        catch ( Exception e )
        {
            log.error("testReinitializeCreditProviderExternalTx", e);
            fail();
        }
    }

    public void testReinitialize()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            IdcTransaction tx = initTransaction( true );
            creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            commitTransaction( tx );
            Collection<CreditUtilizationEvent> cues = CreditUtilC.getCreditUtilizationEvents( lpOrg, fiOrg );
            log( "cues.size for fiOrg=" + cues.size() );
            assertEquals( "cues size after taking trade with FI should be greater than zero.", cues.size() > 0, true );

            IdcTransaction tx1 = initTransaction( true );
            creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            commitTransaction( tx1 );

            cues = CreditUtilC.getCreditUtilizationEvents( lpOrg, ddOrg );
            log("cues.size for ddOrg=" + cues.size());
            assertEquals("cues size after taking trade with DD should be greater than zero.", cues.size() > 0, true);

            creditAdminSvc.reinitialize(lpOrg);

            Collection<CreditUtilizationEvent> cuesAfter = CreditUtilC.getCreditUtilizationEvents(lpOrg, fiOrg);
            log( "cuesAfter for FiOrg=" + cuesAfter );
            assertEquals("cuesAfter for FiOrg either should be null or empty", cuesAfter == null || cuesAfter.isEmpty(), true);

            cuesAfter = CreditUtilC.getCreditUtilizationEvents( lpOrg, ddOrg );
            log( "cuesAfter for DdOrg=" + cuesAfter );
            assertEquals( "cuesAfter for DdOrg either should be null or empty", cuesAfter == null || cuesAfter.isEmpty(), true );

            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testReinitializeExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            creditMgr.takeCredit(trade, lpLe, fiOrg, lpTpForFi);
            commitTransaction(tx);
            Collection<CreditUtilizationEvent> cues = CreditUtilC.getCreditUtilizationEvents(lpOrg, fiOrg);
            log( "cues.size for fiOrg=" + cues.size() );
            assertEquals("cues size after taking trade with FI should be greater than zero.", cues.size() > 0, true);

            IdcTransaction tx1 = initTransaction( true );
            creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            commitTransaction(tx1);
            cues = CreditUtilC.getCreditUtilizationEvents( lpOrg, ddOrg );
            log( "cues.size for ddOrg=" + cues.size() );
            assertEquals("cues size after taking trade with DD should be greater than zero.", cues.size() > 0, true);

            creditAdminSvc.reinitialize(lpOrg);

            Collection<CreditUtilizationEvent> cuesAfter = CreditUtilC.getCreditUtilizationEvents( lpOrg, fiOrg );
            log( "cuesAfter for FiOrg=" + cuesAfter );
            assertEquals( "cuesAfter for FiOrg either should be null or empty", cuesAfter == null || cuesAfter.isEmpty(), true );

            cuesAfter = CreditUtilC.getCreditUtilizationEvents( lpOrg, ddOrg );
            log( "cuesAfter for DdOrg=" + cuesAfter );
            assertEquals( "cuesAfter for DdOrg either should be null or empty", cuesAfter == null || cuesAfter.isEmpty(), true );

            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }


    public void testSetRejectionEmailConfig()
    {
        try
        {
            init( lpUser );
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            boolean isEnable = orgFunc.isEnableRejectionEmail();
            String emailId = "support" + System.currentTimeMillis() + "@integral.com";
            creditAdminSvc.setEnableRejectionEmail( lpOrg, !isEnable );
            creditAdminSvc.setSenderEmailAddress( lpOrg, emailId );

            Session session = PersistenceFactory.newSession();
            orgFunc = ( CreditLimitOrgFunction ) session.refreshObject( orgFunc );

            assertEquals( "sender email address is=" + orgFunc.getSenderEmailAddress(), orgFunc.getSenderEmailAddress(), emailId );
            assertEquals( "enable rejection email is=" + orgFunc.isEnableRejectionEmail(), orgFunc.isEnableRejectionEmail(), !isEnable );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testResetCreditUtitilizationOnNoCreditUtilizationCalc()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );

            IdcTransaction tx = initTransaction( true );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            commitTransaction( tx );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            if ( CreditUtilC.getLimitCurrency( lpOrg ).isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < MINIMUM );
            }
            validateCreditUtilizationEvents( cwm );

            // now set the credit calculator to null for daily.
            sleepFor( 5000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );
            Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
            for ( CreditUtilizationEvent cue : cues )
            {
                CreditUtilization cu = cue.getCreditUtilization();
                if ( cu.getCreditLimitRule() instanceof DailyCreditLimitRule )
                {
                    PersistenceFactory.newSession().refreshObject( cu );
                    log( " cu used amount=" + cu.getUsedAmount() + ",cu.reserveAmt=" + cu.getReservedAmount() + ",cu=" + cu );
                    assertEquals( "cu.usedAmt=" + cu.getUsedAmount(), true, Math.abs( cu.getUsedAmount() - 0.0 ) < MINIMUM );
                    assertEquals( "cu.reserveAmt=" + cu.getReservedAmount(), true, Math.abs( cu.getReservedAmount() - 0.0 ) < MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setCalcAndLimit(lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000);
            setCalcAndLimit(lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000);
        }
    }

    public void testDefaultCreditUtilizationCalculatorAndCurrencyOnReinitialize()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setDefaultAggregateNettingMethodology(lpOrg, null);
            creditAdminSvc.setDefaultDailyNettingMethodology(lpOrg, null);
            creditAdminSvc.setDefaultLimitCurrency(lpOrg, CurrencyFactory.getCurrency("GBP"));
            creditAdminSvc.reinitialize( lpOrg );

            CreditUtilizationCalculator aggregateNettingCalc = creditAdminSvc.getNettingMethodology( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION );
            CreditUtilizationCalculator dailyNettingCalc = creditAdminSvc.getNettingMethodology( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION );
            Currency limitCcy = creditAdminSvc.getCreditLimitCurrency( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION );
            log( "aggregateNettingCalc=" + aggregateNettingCalc + ",dailyNettingCalc=" + dailyNettingCalc + ",limitCcy=" + limitCcy );
            assertEquals( "aggregateNettingCalc should be null.", aggregateNettingCalc, null );
            assertEquals( "limitCcy should not be null.", CurrencyFactory.getCurrency( "GBP" ).isSameAs( limitCcy ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );

            creditAdminSvc.setDefaultAggregateNettingMethodology( lpOrg, GROSS_AGGREGATE_LIMIT_CALCULATOR );
            creditAdminSvc.setDefaultDailyNettingMethodology( lpOrg, GROSS_DAILY_LIMIT_CALCULATOR );
            creditAdminSvc.setDefaultLimitCurrency(lpOrg, CurrencyFactory.getCurrency("USD"));
            creditAdminSvc.reinitialize( lpOrg );

            CreditUtilizationCalculator aggregateNettingCalc1 = creditAdminSvc.getNettingMethodology(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION);
            CreditUtilizationCalculator dailyNettingCalc1 = creditAdminSvc.getNettingMethodology(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION);
            Currency limitCcy1 = creditAdminSvc.getCreditLimitCurrency(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION);
            log( "aggregateNettingCalc1=" + aggregateNettingCalc1 + ",dailyNettingCalc1=" + dailyNettingCalc1 + ",limitCcy1=" + limitCcy1 );
            assertEquals("aggregateNettingCalc1 should not be null.", GROSS_AGGREGATE_LIMIT_CALCULATOR.isSameAs(aggregateNettingCalc1), true);
            assertEquals("dailyNettingCalc should not be null.", GROSS_DAILY_LIMIT_CALCULATOR.isSameAs(dailyNettingCalc1), true);
            assertEquals("limitCcy should not be null.", CurrencyFactory.getCurrency("USD").isSameAs(limitCcy1), true);
            boolean sanityCheck1 = sanityCheck(lpOrg);
            assertEquals( "sanityCheck1 on lpOrg=" + sanityCheck1, sanityCheck1, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultLimitCurrency(lpOrg, CurrencyFactory.getCurrency("USD"));
            creditAdminSvc.reinitialize(lpOrg);
        }
    }

    public void testCreditRuleActivation()
    {
        try
        {
            //For LE exposure
            init( lpUser );
            creditAdminSvc.establishCreditRelationship(lpOrg, lpTpForFi);
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, fiOrg);
            CounterpartyCreditLimitRule leCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertEquals("The status should be active", leCclr.getStatus(), 'A');

            creditAdminSvc.removeCreditRelationship(lpOrg, lpTpForFi);
            assertEquals( "The status should be inactive", leCclr.getStatus(), 'P' );

            Collection<CounterpartyCreditLimitRule> cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add(leCclr);
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, 'A');
            assertEquals( "The status should be active", leCclr.getStatus(), 'A' );

            creditAdminSvc.setCreditEnabled(lpOrg, lpTpForFi, false);
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, 'P');
            assertEquals("The status should be inactive", leCclr.getStatus(), 'P');

            //For Org exposure
            creditAdminSvc.establishCreditRelationship(lpOrg, lpTpForFi);
            creditAdminSvc.setOrganizationExposureLevel(lpOrg, fiOrg);
            CounterpartyCreditLimitRule orgCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi);
            assertEquals( "The status should be active", orgCclr.getStatus(), 'A' );

            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            assertEquals("The status should still be active", orgCclr.getStatus(), 'A');

            cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add(orgCclr);
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, 'A');
            assertEquals( "The status should be active", orgCclr.getStatus(), 'A' );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, fiOrg);
            creditAdminSvc.setCreditEnabled(lpOrg, lpTpForFi, true);
        }
    }

    public void testCreditRuleInactivationValidation()
    {
        try
        {
            //set trading relation and try to inactivate -- should not allow to inactivate
            init( lpUser );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            lpTpForFi.setStatus('A');
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            CounterpartyCreditLimitRule leCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertEquals( "The status should be active", leCclr.getStatus(), 'A' );

            Collection<CounterpartyCreditLimitRule> cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add( leCclr );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, 'P' );
            assertEquals( "Due to validation failure the status should not change", leCclr.getStatus(), 'A' );

            // remove trading relation and try to inactivate -- status should change
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            lpTpForFi.setStatus( 'P' );
            cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add(leCclr);
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, 'P');
            assertEquals("The validation should succeed and the status should be inactive", leCclr.getStatus(), 'P');

            // pass empty collection also
            cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, 'A');
            assertEquals("For the empty collection the status should remain inactive", leCclr.getStatus(), 'P');

            cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add(leCclr);
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, 'A');
            assertEquals("The status should change to active for non-empty collection", leCclr.getStatus(), 'A');

            // now establish the relationship back and set the credit disabled and try inactivating.
            creditAdminSvc.establishCreditRelationship(lpOrg, lpTpForFi);
            lpTpForFi.setStatus('A');

            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, 'A');
            assertEquals( "The status should change to active for non-empty collection", leCclr.getStatus(), 'A' );

            creditAdminSvc.setCreditEnabled(lpOrg, lpTpForFi, false);
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, 'P' );
            assertEquals( "The status should change to active for non-empty collection", leCclr.getStatus(), 'P' );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            lpTpForFi.setStatus( 'A' );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
        }
    }

    public void testRemoveCreditRelationshipOnOrgLevelExposure()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );
            CounterpartyCreditLimitRule orgCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            log( "orgCclr=" + orgCclr );
            assertEquals( "orgCclr should be active and at org level", orgCclr.isActive() && orgCclr.isOrgLevel(), true );

            // now remove the credit relationship.
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            CounterpartyCreditLimitRule orgCclr1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            log( "orgCclr1=" + orgCclr1 );
            assertEquals( "orgCclr1 should be active and at org level", orgCclr1.isActive() && orgCclr1.isOrgLevel(), true );

            // now re-establish the credit relationship.
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            boolean sanityCheck = CreditUtilC.sanityCheck( lpOrg, fiOrg, true );
            log( "sanityCheck=" + sanityCheck );
            assertEquals( "sanityCheck should be true", sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetOrgLevelLeverageFactor()
    {
        try
        {
            init( lpUser );
            Double existingLeverageFactor = creditAdminSvc.getDefaultLeverageFactor(lpOrg);
            Double newLeverageFactor;
            if ( existingLeverageFactor == null || existingLeverageFactor.equals( 1.0 ) )
            {
                newLeverageFactor = 100.0;
            }
            else
            {
                newLeverageFactor = existingLeverageFactor + ( Math.random() > 0.5 ? 10 : -10 );
            }
            creditAdminSvc.setDefaultLeverageFactor(lpOrg, newLeverageFactor);
            assertEquals("check credit admin service getDefaultLeverageFactor", newLeverageFactor == creditAdminSvc.getDefaultLeverageFactor(lpOrg), true);
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(lpOrg);
            PersistenceFactory.newSession().refreshObject( clrs );
            log( "clrs.getLeverageFactor=" + clrs.getLeverageFactor() + ",newLeverageFactor=" + newLeverageFactor );
            assertEquals( "credit limit rule set value should be same. clrs.getLeverageFactor=" + clrs.getLeverageFactor() + ",newLeverageFactor=" + newLeverageFactor, clrs.getLeverageFactor().equals( newLeverageFactor ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetOrgLevelApplyPandL()
    {
        try
        {
            init( lpUser );
            Boolean existingApplyPandL = creditAdminSvc.isDefaultApplyPandL(lpOrg);
            Boolean newApplyPandL;
            if ( existingApplyPandL == null )
            {
                newApplyPandL = true;
            }
            else
            {
                newApplyPandL = !existingApplyPandL;
            }
            creditAdminSvc.setDefaultApplyPandL(lpOrg, newApplyPandL);
            assertEquals( "check credit admin service getDefaultApplyPandL", newApplyPandL == creditAdminSvc.isDefaultApplyPandL(lpOrg), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject( clrs );
            log( "clrs.existingApplyPandL=" + existingApplyPandL + ",newApplyPandL=" + newApplyPandL );
            assertEquals( "credit limit rule set value should be same. clrs.applyPandL=" + clrs.isApplyPandL() + ",newApplyPandL=" + newApplyPandL, clrs.isApplyPandL().equals( newApplyPandL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetOrgLevelIgnoreCurrDatePositions()
    {
        try
        {
            init( lpUser );
            Boolean existingIgnoreCurrDatePositions = creditAdminSvc.isDefaultIgnoreCurrDatePositions(lpOrg);
            Boolean newIgnoreCurrDatePositions;
            if ( existingIgnoreCurrDatePositions == null )
            {
                newIgnoreCurrDatePositions = true;
            }
            else
            {
                newIgnoreCurrDatePositions = !existingIgnoreCurrDatePositions;
            }
            creditAdminSvc.setDefaultIgnoreCurrDatePositions(lpOrg, newIgnoreCurrDatePositions);
            assertEquals( "check credit admin service getDefaultIgnoreCurrDatePositions", newIgnoreCurrDatePositions == creditAdminSvc.isDefaultIgnoreCurrDatePositions(lpOrg), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject( clrs );
            log( "clrs.existingIgnoreCurrDatePositions=" + existingIgnoreCurrDatePositions + ",newIgnoreCurrDatePositions=" + newIgnoreCurrDatePositions );
            assertEquals( "credit limit rule set value should be same. clrs.ignoreCurrentDatePositions=" + clrs.ignoreCurrentDatePositions() + ",newIgnoreCurrDatePositions=" + newIgnoreCurrDatePositions, clrs.ignoreCurrentDatePositions().equals( newIgnoreCurrDatePositions ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }


    public void testSetCptyLevelLeverageFactor()
    {
        try
        {
            init( lpUser );
            Double existingLeverageFactor = creditAdminSvc.getLeverageFactor( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );

            Double newLeverageFactor;
            if ( existingLeverageFactor == null || existingLeverageFactor.equals( 1.0 ) )
            {
                newLeverageFactor = 100.0;
            }
            else
            {
                newLeverageFactor = existingLeverageFactor + ( Math.random() > 0.5 ? 10 : -10 );
            }
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, newLeverageFactor );
            assertEquals( "check credit admin service getLeverageFactor", newLeverageFactor == creditAdminSvc.getLeverageFactor( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION ), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            PersistenceFactory.newSession().refreshObject( sclr );
            log( "clrs.getLeverageFactor=" + sclr.getLeverageFactor() + ",newLeverageFactor=" + newLeverageFactor );
            assertEquals( "credit limit rule set value should be same. clrs.getLeverageFactor=" + sclr.getLeverageFactor() + ",newLeverageFactor=" + newLeverageFactor, sclr.getLeverageFactor().equals( newLeverageFactor ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setLeverageFactor(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1.0);
        }
    }

    public void testSetCptyLevelApplyPandL()
    {
        try
        {
            init( lpUser );
            Boolean existingApplyPandL = creditAdminSvc.isApplyPandL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            Boolean newApplyPandL;
            if ( existingApplyPandL == null )
            {
                newApplyPandL = true;
            }
            else
            {
                newApplyPandL = !existingApplyPandL;
            }
            creditAdminSvc.setApplyPandL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, newApplyPandL );
            assertEquals( "check credit admin service getApplyPandL", newApplyPandL == creditAdminSvc.isApplyPandL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION ), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            PersistenceFactory.newSession().refreshObject( sclr );
            log( "clrs.existingApplyPandL=" + existingApplyPandL + ",newApplyPandL=" + newApplyPandL );
            assertEquals("credit limit rule set value should be same. clrs.applyPandL=" + sclr.isApplyPandL() + ",newApplyPandL=" + newApplyPandL, sclr.isApplyPandL().equals(newApplyPandL), true);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCptyLevelIgnoreCurrDatePositions()
    {
        try
        {
            init( lpUser );
            Boolean existingIgnoreCurrDatePositions = creditAdminSvc.isIgnoreCurrDatePositions(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION);
            Boolean newIgnoreCurrDatePositions;
            if ( existingIgnoreCurrDatePositions == null )
            {
                newIgnoreCurrDatePositions = true;
            }
            else
            {
                newIgnoreCurrDatePositions = !existingIgnoreCurrDatePositions;
            }
            creditAdminSvc.setIgnoreCurrDatePositions(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, newIgnoreCurrDatePositions);
            assertEquals( "check credit admin service getIgnoreCurrDatePositions", newIgnoreCurrDatePositions == creditAdminSvc.isIgnoreCurrDatePositions(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            PersistenceFactory.newSession().refreshObject( sclr );
            log( "clrs.existingIgnoreCurrDatePositions=" + existingIgnoreCurrDatePositions + ",newIgnoreCurrDatePositions=" + newIgnoreCurrDatePositions );
            assertEquals( "credit limit rule set value should be same. clrs.IgnoreCurrDatePositions=" + sclr.ignoreCurrentDatePositions() + ",newIgnoreCurrDatePositions=" + newIgnoreCurrDatePositions, sclr.ignoreCurrentDatePositions().equals( newIgnoreCurrDatePositions ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetOrgLevelMaximumTenor()
    {
        try
        {
            init( lpUser );
            Tenor existingMaximumTenor = creditAdminSvc.getDefaultMaximumTenor( lpOrg );
            Tenor newMaximumTenor;
            if ( existingMaximumTenor == null )
            {
                newMaximumTenor = new Tenor( "5D" );
            }
            else
            {
                DatePeriod datePeriodFor5D = DateTimeFactory.newDatePeriod( 0, 0, 5 );
                newMaximumTenor = new Tenor( existingMaximumTenor.getDatePeriod().add( datePeriodFor5D ).getToString () );
            }
            creditAdminSvc.setDefaultMaximumTenor( lpOrg, newMaximumTenor );
            assertEquals( "check credit admin service getDefaultMaximumTenor", newMaximumTenor.compareTo( creditAdminSvc.getDefaultMaximumTenor( lpOrg ) ), 0 );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject( clrs );
            log( "clrs.getMaximumTenor=" + clrs.getMaximumTenor() + ",newMaximumTenor=" + newMaximumTenor );
            assertEquals( "credit limit rule set value should be same. clrs.getMaximumTenor=" + clrs.getMaximumTenor() + ",newMaximumTenor=" + newMaximumTenor, clrs.getMaximumTenor().compareTo( newMaximumTenor ), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultMaximumTenor(lpOrg, null);
        }
    }

    public void testSetCptyLevelMaximumTenor()
    {
        try
        {
            init( lpUser );
            Tenor existingMaximumTenor = creditAdminSvc.getMaximumTenor(lpOrg, lpTpForDd);

            Tenor newMaximumTenor;
            if ( existingMaximumTenor == null )
            {
                newMaximumTenor = new Tenor( "5D" );
            }
            else
            {
                DatePeriod datePeriodFor5D = DateTimeFactory.newDatePeriod( 0, 0, 5 );
                newMaximumTenor = new Tenor( existingMaximumTenor.getDatePeriod().add( datePeriodFor5D ).getToString () );
            }
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForDd, newMaximumTenor);
            assertEquals( "check credit admin service getMaximumTenor", newMaximumTenor.compareTo( creditAdminSvc.getMaximumTenor(lpOrg, lpTpForDd) ), 0 );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForDd);
            PersistenceFactory.newSession().refreshObject( cclr );
            log( "cclr.getMaximumTenor=" + cclr.getMaximumTenor() + ",newMaximumTenor=" + newMaximumTenor );
            assertEquals( "cpty credit limit rule value should be same. cclr.getMaximumTenor=" + cclr.getMaximumTenor() + ",newMaximumTenor=" + newMaximumTenor, cclr.getMaximumTenor().compareTo( newMaximumTenor ), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForDd, null);
        }
    }

    public void testSetOrgLevelMinimumTenor()
    {
        try
        {
            init( lpUser );
            Tenor existingMinimumTenor = creditAdminSvc.getDefaultMinimumTenor(lpOrg);
            Tenor newMinimumTenor;
            if ( existingMinimumTenor == null )
            {
                newMinimumTenor = new Tenor( "5D" );
            }
            else
            {
                DatePeriod datePeriodFor5D = DateTimeFactory.newDatePeriod( 0, 0, 5 );
                newMinimumTenor = new Tenor( existingMinimumTenor.getDatePeriod().add( datePeriodFor5D ).getToString () );
            }
            creditAdminSvc.setDefaultMinimumTenor(lpOrg, newMinimumTenor);
            assertEquals( "check credit admin service getDefaultMinimumTenor", newMinimumTenor.compareTo( creditAdminSvc.getDefaultMinimumTenor(lpOrg) ), 0 );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(lpOrg);
            PersistenceFactory.newSession().refreshObject( clrs );
            log( "clrs.getMinimumTenor=" + clrs.getMinimumTenor() + ",newMinimumTenor=" + newMinimumTenor );
            assertEquals( "credit limit rule set value should be same. clrs.getMinimumTenor=" + clrs.getMinimumTenor() + ",newMinimumTenor=" + newMinimumTenor, clrs.getMinimumTenor().compareTo( newMinimumTenor ), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultMinimumTenor(lpOrg, null);
        }
    }

    public void testSetCptyLevelMinimumTenor()
    {
        try
        {
            init( lpUser );
            Tenor existingMinimumTenor = creditAdminSvc.getMinimumTenor(lpOrg, lpTpForDd);

            Tenor newMinimumTenor;
            if ( existingMinimumTenor == null )
            {
                newMinimumTenor = new Tenor( "5D" );
            }
            else
            {
                DatePeriod datePeriodFor5D = DateTimeFactory.newDatePeriod( 0, 0, 5 );
                newMinimumTenor = new Tenor( existingMinimumTenor.getDatePeriod().add( datePeriodFor5D ).getToString () );
            }
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForDd, newMinimumTenor);
            assertEquals( "check credit admin service getMinimumTenor", newMinimumTenor.compareTo( creditAdminSvc.getMinimumTenor(lpOrg, lpTpForDd) ), 0 );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForDd);
            PersistenceFactory.newSession().refreshObject( cclr );
            log( "cclr.getMinimumTenor=" + cclr.getMinimumTenor() + ",newMinimumTenor=" + newMinimumTenor );
            assertEquals( "cpty credit limit rule value should be same. cclr.getMinimumTenor=" + cclr.getMinimumTenor() + ",newMinimumTenor=" + newMinimumTenor, cclr.getMinimumTenor().compareTo( newMinimumTenor ), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForDd, null);
        }
    }

    public void testSetOrgLevelUpdateBalanceWithPL()
    {
        try
        {
            init( lpUser );
            Boolean existingUpdateBalanceWithPL = creditAdminSvc.isDefaultUpdateBalanceWithPL( lpOrg );
            Boolean newUpdateBalanceWithPL;
            if ( existingUpdateBalanceWithPL == null )
            {
                newUpdateBalanceWithPL = true;
            }
            else
            {
                newUpdateBalanceWithPL = !existingUpdateBalanceWithPL;
            }
            creditAdminSvc.setDefaultUpdateBalanceWithPL( lpOrg, newUpdateBalanceWithPL );
            assertEquals( "check credit admin service getDefaultUpdateBalanceWithPL", newUpdateBalanceWithPL == creditAdminSvc.isDefaultUpdateBalanceWithPL( lpOrg ), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject( clrs );
            log( "clrs.existingUpdateBalanceWithPL=" + existingUpdateBalanceWithPL + ",newUpdateBalanceWithPL=" + newUpdateBalanceWithPL );
            assertEquals("credit limit rule set value should be same. clrs.updateBalanceWithPL=" + clrs.isUpdateBalanceWithPL() + ",newUpdateBalanceWithPL=" + newUpdateBalanceWithPL, clrs.isUpdateBalanceWithPL().equals(newUpdateBalanceWithPL), true);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCptyLevelUpdateBalanceWithPL()
    {
        try
        {
            init( lpUser );
            Boolean existingUpdateBalanceWithPL = creditAdminSvc.isUpdateBalanceWithPL(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION);
            Boolean newUpdateBalanceWithPL;
            newUpdateBalanceWithPL = existingUpdateBalanceWithPL == null || !existingUpdateBalanceWithPL;
            creditAdminSvc.setUpdateBalanceWithPL(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, newUpdateBalanceWithPL);
            assertEquals( "check credit admin service getUpdateBalanceWithPL", newUpdateBalanceWithPL == creditAdminSvc.isUpdateBalanceWithPL(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule(cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION);
            PersistenceFactory.newSession().refreshObject( sclr );
            log( "clrs.existingUpdateBalanceWithPL=" + existingUpdateBalanceWithPL + ",newUpdateBalanceWithPL=" + newUpdateBalanceWithPL );
            assertEquals( "credit limit rule set value should be same. clrs.newUpdateBalanceWithPL=" + sclr.isUpdateBalanceWithPL() + ",newUpdateBalanceWithPL=" + newUpdateBalanceWithPL, sclr.isUpdateBalanceWithPL().equals( newUpdateBalanceWithPL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testEndOfDayUpdateBalanceWithPL()
    {
        try
        {
            IdcDate businessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            CreditUtilizationManagerC.getInstance().recalculateAllAggregateCreditUtilizations(businessDate, null);
        }
        catch ( Exception e )
        {
            fail( "testEndOfDayUpdateBalanceWithPL", e );
        }
    }

    public void testSetOrgLevelDailyPL()
    {
        try
        {
            init( lpUser );
            Boolean existingDailyPL = creditAdminSvc.isDefaultDailyPL( lpOrg );
            Boolean newDailyPL;
            newDailyPL = existingDailyPL == null || !existingDailyPL;
            creditAdminSvc.setDefaultDailyPL( lpOrg, newDailyPL );
            assertEquals( "check credit admin service isDefaultDailyPL", newDailyPL == creditAdminSvc.isDefaultDailyPL( lpOrg ), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject( clrs );
            log( "clrs.existingDailyPL=" + existingDailyPL + ",newDailyPL=" + newDailyPL );
            assertEquals( "credit limit rule set value should be same. clrs.dailyPL=" + clrs.isDailyPL() + ",newDailyPL=" + newDailyPL, clrs.isDailyPL().equals( newDailyPL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultDailyPL(lpOrg, false);
        }
    }

    public void testSetCptyLevelDailyPL()
    {
        try
        {
            init( lpUser );
            Boolean existingDailyPL = creditAdminSvc.isDailyPL(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION);
            Boolean newDailyPL;
            newDailyPL = existingDailyPL == null || !existingDailyPL;
            creditAdminSvc.setDailyPL(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, newDailyPL);
            assertEquals( "check credit admin service isDailyPL", newDailyPL == creditAdminSvc.isDailyPL(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule(cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION);
            PersistenceFactory.newSession().refreshObject( sclr );
            log( "clr.existingDailyPL=" + existingDailyPL + ",newDailyPL=" + newDailyPL );
            assertEquals( "credit limit rule set value should be same. clr.newDailyPL=" + sclr.isDailyPL() + ",newDailyPL=" + newDailyPL, sclr.isDailyPL().equals( newDailyPL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDailyPL(lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false);
        }
    }

    public void testSetOrgCurrencyPairGroupExemption()
    {
        try
        {
            init(lpUser);
            ExpressionBuilder builder = new ExpressionBuilder();
            Expression exp = builder.get( "shortName" ).equal("G7");
            CurrencyPairGroup currencyPairGroup = ( CurrencyPairGroup ) PersistenceFactory.newSession().readObject( CurrencyPairGroupC.class, exp );
            creditAdminSvc.setCurrencyPairGroupExemption(lpOrg, currencyPairGroup);
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject(clrs);
            assertTrue(clrs.getExemptCurrencyPairGroup().isSameAs(currencyPairGroup));
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCurrencyPairGroupExemption(lpOrg, null);
        }
    }

    public void testSetCounterpartyLevelCurrencyPairGroupExemption()
    {
        try
        {
            init( lpUser );
            ExpressionBuilder builder = new ExpressionBuilder();
            Expression exp = builder.get( "shortName" ).equal( "G7" );
            CurrencyPairGroup currencyPairGroup = ( CurrencyPairGroup ) PersistenceFactory.newSession().readObject( CurrencyPairGroupC.class, exp );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForDd, currencyPairGroup );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForDd);
            cclr = ( CounterpartyCreditLimitRule ) PersistenceFactory.newSession().refreshObject( cclr );
            assertTrue( cclr.getExemptCurrencyPairGroup().isSameAs( currencyPairGroup ) );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelCurrencyPairGroupExemption", e );
        }
        finally
        {
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForDd, null );
        }
    }

    public void testSetCounterpartyLevelUseDefaultExemptCurrencyPairGroup()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultExemptCurrencyPairGroup( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultExemptCurrencyPairGroup() );

            // now set different exempt groups at provider level and cpty level.
            CurrencyPairGroup g7 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( "G7", CurrencyPairGroup.class, null, null );
            CurrencyPairGroup usdAll = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName("USD/All", CurrencyPairGroup.class, null, null);

            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForDd, usdAll );
            assertTrue( CreditUtilC.getExemptCurrencyPairGroup( cclr ).isSameAs( usdAll ) );

            creditAdminSvc.setUseDefaultExemptCurrencyPairGroup( lpOrg, lpTpForDd, true );
            assertTrue( CreditUtilC.getExemptCurrencyPairGroup( cclr ).isSameAs( g7 ) );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelUseDefaultExemptCurrencyPairGroup", e );
        }
        finally
        {
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForDd, null );
            creditAdminSvc.setUseDefaultExemptCurrencyPairGroup(lpOrg, lpTpForDd, true);
        }
    }


    public void testSetDefaultCreditTenorProfile()
    {
        try
        {
            init( lpUser );

            String testProfile = "Test" + System.nanoTime();
            CreditTenorProfile tenorProfile = createCreditTenorProfile( testProfile );
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, tenorProfile);
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject(clrs);
            assertTrue( clrs.getCreditTenorProfile().isSameAs( tenorProfile ) );
        }
        catch ( Exception e )
        {
            fail( "testSetDefaultCreditTenorProfile", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile(lpOrg, null);
        }
    }

    public void testSetCounterpartyLevelCreditTenorProfile()
    {
        try
        {
            init(lpUser);

            String testProfile = "Test" + System.nanoTime();
            CreditTenorProfile tenorProfile = createCreditTenorProfile( testProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(lpOrg);
            clrs = ( CreditLimitRuleSet ) IdcUtilC.refreshObject( clrs );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi);
            cclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject( cclr );
            assertTrue( cclr.getCreditTenorProfile().isSameAs( tenorProfile ) );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelCreditTenorProfile", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForFi, true);
        }
    }

    public void testSetCounterpartyLevelUseDefaultTenorProfile()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultTenorProfile() );

            // now set different tenor profiles at provider level and cpty level.
            String orgLevelCtpName = "TestCPO" + System.nanoTime();
            CreditTenorProfile orgLevelCtp = createCreditTenorProfile( orgLevelCtpName );

            String cptyLevelCtpName = "TestCC" + System.nanoTime();
            CreditTenorProfile cptyLevelCtp = createCreditTenorProfile(cptyLevelCtpName);

            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, orgLevelCtp );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, cptyLevelCtp );
            assertTrue(cclr.getCreditTenorProfile().isSameAs( cptyLevelCtp ) );

            creditAdminSvc.setUseDefaultTenorProfile(lpOrg, lpTpForDd, true);
            assertTrue( cclr.isUseDefaultTenorProfile() );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelUseDefaultTenorProfile", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
        }
    }

    public void testExternalCreditLimitProviderChanges()
    {
        try
        {
            String externalCreditLimitProvider = "TraianaPing";
            init( lpUser );
            creditAdminSvc.setExternalCreditLimitProvider( lpOrg, lpTpForDd, externalCreditLimitProvider );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals("externalCreditLimitProvider=" + cclr.getExternalCreditLimitProvider(), cclr.getExternalCreditLimitProvider(), externalCreditLimitProvider);

            String creditLimitProviderFromDB = creditAdminSvc.getExternalCreditLimitProvider( lpOrg, lpTpForDd );
            assertEquals("creditLimitProviderFromDB=" + creditLimitProviderFromDB, creditLimitProviderFromDB, externalCreditLimitProvider);

            assertEquals("checkSanity", sanityCheck(lpOrg), true);
        }
        catch ( Exception e )
        {
            log.error( "testExternalCreditLimitProviderChanges", e );
            fail();
        }
    }

    public void testSetDefaultMode()
    {
        try
        {
            init( lpUser );

            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_INTEGRATION_MODE );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject( clrs );
            assertTrue( clrs.getMode() == CreditLimit.CREDIT_INTEGRATION_MODE );
        }
        catch ( Exception e )
        {
            fail( "testSetDefaultMode", e );
        }
        finally
        {
            creditAdminSvc.setDefaultMode(lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE);
        }
    }

    public void testSetCounterpartyLevelMode()
    {
        try
        {
            init( lpUser );

            creditAdminSvc.setMode(lpOrg, lpTpForFi, CreditLimit.CREDIT_INTEGRATION_MODE);
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            IdcUtilC.refreshObject(clrs);

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi);
            cclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject( cclr );
            assertTrue( cclr.getMode() == CreditLimit.CREDIT_INTEGRATION_MODE );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelMode", e );
        }
        finally
        {
            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setMode(lpOrg, lpTpForFi, CreditLimit.CREDIT_CARVE_OUT_MODE);
        }
    }

    public void testSetExcludePFEMode()
    {
        CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
        int defaultVal =  clrs.getPFEExcludeForDailyExposure();
        try
        {
            init( lpUser );

            creditAdminSvc.setPFEExcludeForDailyExposure(lpOrg, CreditLimit.PFE_MODE_YES);
            clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            IdcUtilC.refreshObject(clrs);

            clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            clrs = ( CreditLimitRuleSet ) IdcUtilC.refreshObject( clrs );
            assertTrue( clrs.getPFEExcludeForDailyExposure() == CreditLimit.PFE_MODE_YES );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelExcludePFEMode", e );
        }
        finally
        {
            creditAdminSvc.setPFEExcludeForDailyExposure(lpOrg, defaultVal);
        }
    }

    public void testSetCounterpartyLevelExcludePFEMode()
    {
        try
        {
            init( lpUser );

            creditAdminSvc.setPFEExcludeForDailyExposure(lpOrg, lpTpForFi, CreditLimit.PFE_MODE_YES);
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            IdcUtilC.refreshObject( clrs );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            cclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject( cclr );
            assertTrue( cclr.getPFEExcludeForDailyExposure() == CreditLimit.PFE_MODE_YES );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelExcludePFEMode", e );
        }
        finally
        {
            creditAdminSvc.setPFEExcludeForDailyExposure(lpOrg, lpTpForFi, CreditLimit.PFE_MODE_DEFAULT);
        }
    }
     public void testSetCounterpartyLevelUseDefaultMode()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultMode() );

            // now set different modes at provider level and cpty level.
            creditAdminSvc.setDefaultMode(lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE);
            creditAdminSvc.setMode(lpOrg, lpTpForDd, CreditLimit.CREDIT_INTEGRATION_MODE);
            assertTrue(CreditUtilC.getCreditMode(cclr) == CreditLimit.CREDIT_INTEGRATION_MODE);

            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, true );
            assertTrue( CreditUtilC.getCreditMode(cclr) == CreditLimit.CREDIT_CARVE_OUT_MODE );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelUseDefaultMode", e );
        }
        finally
        {
            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setMode( lpOrg, lpTpForDd, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setUseDefaultMode(lpOrg, lpTpForDd, true);
        }
    }




    public void testUsePFEProvider()
    {
        init( lpUser );
        CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
        boolean defaultValue = clrs.isUsePFEConfiguration();

        try
        {

            creditAdminSvc.setUsePFEConfiguration(lpOrg, true);
            PersistenceFactory.newSession().refreshObject(clrs);
            assertTrue(clrs.isUsePFEConfiguration());

            creditAdminSvc.setUsePFEConfiguration(lpOrg, false);
            clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            PersistenceFactory.newSession().refreshObject(clrs);
            assertFalse(clrs.isUsePFEConfiguration());

        }
        catch ( Exception e )
        {
            fail( "testUsePFEProvider", e );
        }
        finally
        {
            clrs = ( CreditLimitRuleSet ) IdcUtilC.refreshObject( clrs );
            creditAdminSvc.setUsePFEConfiguration(lpOrg, defaultValue);
        }
    }

    public void testUsePFECounterparty()
    {
        init( lpUser );
        CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi);
        boolean defaultValue =cclr.isUsePFEConfiguration();
        try
        {
            creditAdminSvc.setUsePFEConfiguration(lpOrg, lpTpForFi, true);
            cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            cclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject( cclr );
            assertTrue(cclr.isUsePFEConfiguration());


            creditAdminSvc.setUsePFEConfiguration(lpOrg, lpTpForFi, false);
            cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi);
            cclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject( cclr );
            assertFalse(cclr.isUsePFEConfiguration());

        }
        catch ( Exception e )
        {
            fail( "testUsePFECounterparty", e );
        }
        finally
        {
            cclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject( cclr );
            cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            creditAdminSvc.setUsePFEConfiguration(lpOrg, lpTpForFi, defaultValue);

        }
    }


    public void testPersistPFE()
    {
        init( lpUser );
        try
        {
            String name = "TestPFE" + System.nanoTime();
            String testProfile = "Test" + System.nanoTime();

            PfeConfiguration pfeConfiguration = create(name,testProfile);
            creditAdminSvc.persist(lpOrg,pfeConfiguration);

            PFEConfiguration pfeConfiguration1 = creditAdminSvc.getPfeConfiguration(lpOrg,name);
            assertNotNull(pfeConfiguration1);
            assertEquals(pfeConfiguration1.getName(), name);

            assertTrue(pfeConfiguration1.getPfeConfigurationProfiles().size() == 2);
            Collection<PFEConfigurationProfile> collection = pfeConfiguration1.getPfeConfigurationProfiles();
            int index = 0;
            String[] grp= {"G7","EUR/All"};
            for(PFEConfigurationProfile temp:collection)
            {
                assertTrue(temp.getCurrencyPairGroup().getName().equals(grp[index]));
                index++;
            }
        }
        catch ( Exception e )
        {
            fail( "testPersistPFE", e );
        }
        finally
        {

        }
    }

    private PfeConfiguration create(String name,String testProfile)
    {
        CreditTenorProfile tenorProfile = createCreditTenorProfile( testProfile );
        creditAdminSvc.addCreditTenorProfile(lpOrg,tenorProfile);

        PfeConfiguration pfeConfiguration = new PfeConfiguration();
        String defaultTenorProfile = testProfile;
        pfeConfiguration.setName( name);
        pfeConfiguration.setDefaultTenorProfile(defaultTenorProfile);
        pfeConfiguration.setDescription(name);

        PfeConfigurationProfile pfeConfigurationProfile = new PfeConfigurationProfile();
        pfeConfigurationProfile.setSortOrder(10);
        pfeConfigurationProfile.setCurrencyPairGroup("G7");

        com.integral.finance.creditLimit.model.CreditTenorProfile creditTenorProfile = createCreditTenorProfileModel("TestPFEProf"+ System.nanoTime());
        pfeConfigurationProfile.setTenorProfile(creditTenorProfile);
        pfeConfiguration.getPfeConfigurationProfiles().add(pfeConfigurationProfile);

        PfeConfigurationProfile pfeConfigurationProfile2 = new PfeConfigurationProfile();
        pfeConfigurationProfile2.setSortOrder(20);
        pfeConfigurationProfile2.setCurrencyPairGroup("EUR/All");
        com.integral.finance.creditLimit.model.CreditTenorProfile creditTenorProfile2 = createCreditTenorProfileModel("TestPFEProf2"+ System.nanoTime());
        pfeConfigurationProfile2.setTenorProfile(creditTenorProfile2);

        pfeConfiguration.getPfeConfigurationProfiles().add(pfeConfigurationProfile2);
        return pfeConfiguration;
    }

    private PfeConfiguration create( String name,String defaultTestProfile, String testProfile )
    {
        CreditTenorProfile tenorProfile = createCreditTenorProfile( defaultTestProfile );
        creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );

        PfeConfiguration pfeConfiguration = new PfeConfiguration();
        pfeConfiguration.setName( name);
        pfeConfiguration.setDefaultTenorProfile( defaultTestProfile );
        pfeConfiguration.setDescription( name );

        PfeConfigurationProfile pfeConfigurationProfile = new PfeConfigurationProfile();
        pfeConfigurationProfile.setSortOrder(10);
        pfeConfigurationProfile.setCurrencyPairGroup("G7");

        com.integral.finance.creditLimit.model.CreditTenorProfile creditTenorProfile = createCreditTenorProfileModel(testProfile );
        pfeConfigurationProfile.setTenorProfile(creditTenorProfile);
        pfeConfiguration.getPfeConfigurationProfiles().add(pfeConfigurationProfile);

        return pfeConfiguration;
    }


    /**
     *
     */
    public void testSetPFEProvider()
    {
        init( lpUser );
        String name = "TPFE2" + System.nanoTime();
        String testProfile = "T2" + System.nanoTime();
        CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
        PFEConfiguration configuredPFE = clrs.getPfeConfiguration();
        try
        {
            PfeConfiguration pfeConfiguration = create(name, testProfile);
            creditAdminSvc.persist(lpOrg,pfeConfiguration);

            PFEConfiguration temp = creditAdminSvc.getPfeConfiguration(lpOrg,name);
            creditAdminSvc.setPFEConfiguration(lpOrg, temp);

            clrs = (CreditLimitRuleSet)IdcUtilC.refreshObject(clrs);
            assertNotNull(clrs.getPfeConfiguration());
            assertEquals(clrs.getPfeConfiguration().getName(),name);
        }
        catch ( Exception e )
        {
            fail( "testSetPFEProvider", e );
        }
        finally
        {
            creditAdminSvc.setPFEConfiguration(lpOrg, configuredPFE);

        }

    }

    public void testSetPFECounterparty()
    {
        init( lpUser );
        String name = "TestPFE3" + System.nanoTime();
        String testProfile = "Test3" + System.nanoTime();
        CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi);
        PFEConfiguration configuredPFE = cclr.getPfeConfiguration();
        try
        {
            PfeConfiguration pfeConfiguration = create(name, testProfile);
            creditAdminSvc.persist(lpOrg,pfeConfiguration);

            PFEConfiguration temp = creditAdminSvc.getPfeConfiguration(lpOrg,name);
            creditAdminSvc.setPFEConfiguration(lpOrg, lpTpForFi,temp);

            cclr = (CounterpartyCreditLimitRule)IdcUtilC.refreshObject(cclr);
            assertNotNull(cclr.getPfeConfiguration());
            assertEquals(cclr.getPfeConfiguration().getName(),name);
        }
        catch ( Exception e )
        {
            fail( "testSetPFECounterparty", e );
        }
        finally
        {
            creditAdminSvc.setPFEConfiguration(lpOrg, lpTpForFi,configuredPFE);

        }

    }

    public void testSetLETenorOverride()
    {
        init( lpUser );
        try
        {
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, true);
            assertTrue(CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi).isLeOverride());
        }
        catch (Exception e)
        {
            fail("testSetLETenorOverride", e);
        }
        finally
        {
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false);
        }
    }

    public void testSetLETenorOrgDefault()
    {
        init( lpUser );
        try
        {
            creditAdminSvc.setOrganizationExposureLevel(lpOrg, fiOrg);
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false);
            assertFalse(CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule(lpOrg, lpTpForFi).isOrgDefault());
        }
        catch (Exception e)
        {
            fail("testSetLETenorOrgDefault", e);
        }
        finally
        {
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true);
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, fiOrg);
        }
    }

    public void testInactivateCreditRelationshipAtLELevelExposure()
    {
        creditAdminSvc.setLegalEntityExposureLevel(lpOrg, ddOrg);
        Collection<CounterpartyCreditLimitRule> cclrs = CreditUtilC.queryAllCounterpartyCreditLimitRulesForCounterpartyOrganization( lpOrg, ddOrg, true );
        try
        {
            Collection<TradingParty> tps = CounterpartyUtilC.getTradingParties( lpOrg, ddOrg );
            for ( TradingParty tp: tps )
            {
                creditAdminSvc.setCreditEnabled( lpOrg, tp, false);
            }
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, Entity.PASSIVE_STATUS);
            Collection<CounterpartyCreditLimitRule> inactiveTpCclrs = CreditUtilC.getInactiveCounterpartyTradingParyRules( lpOrg, ddOrg );
            Collection<CounterpartyCreditLimitRule> allCclrs = CreditUtilC.queryAllCounterpartyCreditLimitRulesForCounterpartyOrganization(lpOrg, ddOrg, false);
            assertNotNull( inactiveTpCclrs );
            assertFalse(inactiveTpCclrs.isEmpty());
            assertSame(inactiveTpCclrs.size() + 1, allCclrs.size());
            assertNull(CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForDd));
            for ( CounterpartyCreditLimitRule cclr: inactiveTpCclrs )
            {
                assertTrue( cclr.isPassive() );
            }
        }
        catch ( Exception e )
        {
            fail ( "testInactivateCreditRelationshipAtLELevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, ddOrg);
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, Entity.ACTIVE_STATUS);
            Collection<TradingParty> tps = CounterpartyUtilC.getTradingParties( lpOrg, ddOrg );
            for ( TradingParty tp: tps )
            {
                creditAdminSvc.setCreditEnabled( lpOrg, tp, true );
            }
        }
    }

    public void testInactivateCreditRelationshipAtOrgLevelExposure()
    {
        creditAdminSvc.setOrganizationExposureLevel(lpOrg, ddOrg);
        Collection<CounterpartyCreditLimitRule> cclrs = CreditUtilC.queryAllCounterpartyCreditLimitRulesForCounterpartyOrganization( lpOrg, ddOrg, true );
        try
        {
            Collection<TradingParty> tps = CounterpartyUtilC.getTradingParties( lpOrg, ddOrg );
            for ( TradingParty tp: tps )
            {
                creditAdminSvc.setCreditEnabled( lpOrg, tp, false);
            }
            creditAdminSvc.setCounterpartyCreditLimitRulesActive(cclrs, Entity.PASSIVE_STATUS);
            Collection<CounterpartyCreditLimitRule> inactiveTpCclrs = CreditUtilC.getInactiveCounterpartyTradingParyRules( lpOrg, ddOrg );
            Collection<CounterpartyCreditLimitRule> allCclrs = CreditUtilC.queryAllCounterpartyCreditLimitRulesForCounterpartyOrganization(lpOrg, ddOrg, false);
            assertNotNull( inactiveTpCclrs );
            assertFalse(inactiveTpCclrs.isEmpty());
            assertSame( inactiveTpCclrs.size() + 1, allCclrs.size() );
            assertNull(CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForDd));
            for ( CounterpartyCreditLimitRule cclr: inactiveTpCclrs )
            {
                assertTrue( cclr.isPassive() );
            }
        }
        catch ( Exception e )
        {
            fail ( "testInactivateCreditRelationshipAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, ddOrg );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.ACTIVE_STATUS );
            Collection<TradingParty> tps = CounterpartyUtilC.getTradingParties( lpOrg, ddOrg );
            for ( TradingParty tp: tps )
            {
                creditAdminSvc.setCreditEnabled( lpOrg, tp, true );
            }
        }
    }

    public void testSetTenorRestrictionInBusinessDays()
    {
        init( lpUser );
        try
        {
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true);
            assertTrue(CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi).isTenorRestrictionInBusinessDays() );
            assertTrue( creditAdminSvc.isTenorRestrictionInBusinessDays( lpOrg, lpTpForFi ));
        }
        catch (Exception e)
        {
            fail("testSetTenorRestrictionInBusinessDays", e);
        }
        finally
        {
            creditAdminSvc.setTenorRestrictionInBusinessDays(lpOrg, lpTpForFi, false);
        }
    }

    public void testTenorRestrictionInBusinessDaysAtOrgLevelExposure()
    {
        creditAdminSvc.setOrganizationExposureLevel(lpOrg, ddOrg);
        creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForDd, true );
        assertTrue( CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd ).isTenorRestrictionInBusinessDays() );
        assertTrue( creditAdminSvc.isTenorRestrictionInBusinessDays( lpOrg, lpTpForDd ));

        try
        {
            Collection<TradingParty> tps = CounterpartyUtilC.getTradingParties( lpOrg, ddOrg );
            for ( TradingParty tp: tps )
            {
                creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, tp, true );
                assertTrue( creditAdminSvc.isCptyLETenorRestrictionInBusinessDays( lpOrg, tp ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testTenorRestrictionInBusinessDaysAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, ddOrg );
            Collection<TradingParty> tps = CounterpartyUtilC.getTradingParties( lpOrg, ddOrg );
            for ( TradingParty tp: tps )
            {
                creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, tp, false );
            }
        }
    }

    public void testDeposit()
    {
        try
        {
            init ( lpUser );
            removeExistingCreditUtilizationEvents ( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );

            CreditWorkflowMessage cwm = creditAdminSvc.deposit ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "GBP" ), -10000, "DEP" + System.currentTimeMillis (), null, null, "1DEP" + System.currentTimeMillis () );
            assertNotNull ( cwm );
            assertNotNull ( cwm.getErrorCode () );
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) );

            CreditWorkflowMessage cwm1 = creditAdminSvc.deposit ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "GBP" ), 10000.0, "DEP" + System.currentTimeMillis (), null, null, "2DEP" + System.currentTimeMillis () );
            assertNotNull ( cwm1 );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );

            CreditUtilization cu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            assertNotNull ( cu );
            double nav = cu.getUsedAmount ();
            assertTrue ( nav > 0.0 );

            CreditWorkflowMessage cwm2 = creditAdminSvc.deposit ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "USD" ), 10000.0, "DEP" + System.currentTimeMillis (), null, null, "3DEP" + System.currentTimeMillis ());
            assertNotNull ( cwm2 );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) );
            assertTrue ( cu.getUsedAmount () > nav );


            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );

            CreditWorkflowMessage cwm3 = creditAdminSvc.deposit ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "USD" ), 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () );
            assertNotNull ( cwm3 );
            assertNotNull ( cwm3.getErrorCode () );
            assertTrue ( MessageStatus.FAILURE.equals ( cwm3.getStatus () ) );

        }
        catch ( Exception e )
        {
            fail ( "testDeposit", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }


    public void testWithdraw()
    {
        try
        {
            init ( lpUser );
            removeExistingCreditUtilizationEvents ( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );

            CreditWorkflowMessage cwm = creditAdminSvc.deposit ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "GBP" ), -10000, "DEP" + System.currentTimeMillis (), null, null, "1DEP" + System.currentTimeMillis () );
            assertNotNull ( cwm );
            assertNotNull ( cwm.getErrorCode () );
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) );

            CreditWorkflowMessage cwm1 = creditAdminSvc.deposit ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "GBP" ), 10000.0, "DEP" + System.currentTimeMillis (), null, null, "2DEP" + System.currentTimeMillis () );
            assertNotNull ( cwm1 );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );

            CreditUtilization cu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            assertNotNull ( cu );
            double nav = cu.getUsedAmount ();
            assertTrue ( nav > 0.0 );

            CreditWorkflowMessage cwm2 = creditAdminSvc.withdraw ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "USD" ), 10000.0, "WITHDR" + System.currentTimeMillis (), null, null, "1WITHDR" + System.currentTimeMillis () );
            assertNotNull ( cwm2 );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) );

            CreditWorkflowMessage cwm3 = creditAdminSvc.withdraw ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "GBP" ), 10000.4, "WITHDR" + System.currentTimeMillis (), null, null, "2WITHDR" + System.currentTimeMillis () );
            assertNotNull ( cwm3 );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm3.getStatus () ) );

            CreditWorkflowMessage cwm4 = creditAdminSvc.withdraw ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "GBP" ), 9000.00, "WITHDR" + System.currentTimeMillis (), null, null, "3WITHDR" + System.currentTimeMillis () );
            assertNotNull ( cwm4 );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm4.getStatus () ) );
            assertTrue ( cu.getUsedAmount () < nav );

            CreditWorkflowMessage cwm5 = creditAdminSvc.withdraw ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "GBP" ), 5000, "WITHDR" + System.currentTimeMillis (), null, null, "4WITHDR" + System.currentTimeMillis () );
            assertNotNull ( cwm5 );
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm5.getStatus () ) );

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );

            CreditWorkflowMessage cwm6 = creditAdminSvc.withdraw ( lpOrg, lpTpForDd, CurrencyFactory.getCurrency ( "USD" ), 10000, "WITHDR" + System.currentTimeMillis (), null, null, "5WITHDR" + System.currentTimeMillis () );
            assertNotNull ( cwm6 );
            assertTrue ( MessageStatus.FAILURE.equals ( cwm6.getStatus () ) );

        }
        catch ( Exception e )
        {
            fail ( "testWithdraw", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testSetCounterpartyLevelCreditLimitRuleLevelTenorCoefficients()
    {
        try
        {
            init( lpUser );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForDd, true );
            assertEquals( "cclr.isCreditLimitRuleLevelTenorCoefficients()=" + cclr.isCreditLimitRuleLevelTenorCoefficients(), true, cclr.isCreditLimitRuleLevelTenorCoefficients() );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelCreditLimitRuleLevelTenorCoefficients", e );
        }
        finally
        {
            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForDd, false );
        }
    }

    public void testSetCounterpartyCreditLimitRuleLevelTenorProfiles()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultTenorProfile() );

            // now set different tenor profiles at counterparty rule level and credit limit rule level.
            String cptyLevelCtpName = "TestCC" + System.currentTimeMillis();
            CreditTenorProfile cptyLevelCtp = createCreditTenorProfile( cptyLevelCtpName );

            String aggLevelCtpName = "TestAgg" + System.currentTimeMillis();
            CreditTenorProfile aggLevelCtp = createCreditTenorProfile( aggLevelCtpName );

            String dailyLevelCtpName = "TestAgg" + System.currentTimeMillis();
            CreditTenorProfile dailyLevelCtp = createCreditTenorProfile( dailyLevelCtpName );

            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, cptyLevelCtp );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, aggLevelCtp );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, dailyLevelCtp );
            assertTrue( cclr.getCreditTenorProfile().isSameAs( cptyLevelCtp ) );

            CreditLimitRule aggRule = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            CreditLimitRule dailyRule = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.DAILY_SETTLEMENT_RULE_SHORT_NAME );

            assertEquals( aggLevelCtp.getShortName(), aggRule.getCreditTenorProfile().getShortName() );
            assertEquals( dailyLevelCtp.getShortName(), dailyRule.getCreditTenorProfile().getShortName() );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyCreditLimitRuleLevelTenorProfiles", e );
        }
        finally
        {
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
        }
    }

    public void testSetCounterpartyCreditLimitRuleLevelPFEConfiguration()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultTenorProfile() );

            // now set different tenor profiles at counterparty rule level and credit limit rule level.
            String cptyLevelPFEName = "TestCCPFE" + System.currentTimeMillis();
            String cptyLevelCTPName = "TestCCCTP" + System.currentTimeMillis();
            PfeConfiguration cptyLevelPFE = create( cptyLevelPFEName, cptyLevelCTPName );

            String aggLevelPFEName = "TestAggPFE" + System.currentTimeMillis();
            String aggLevelCTPName = "TestAggCTP" + System.currentTimeMillis();
            PfeConfiguration aggLevelPFE = create( aggLevelPFEName, aggLevelCTPName );

            String dailyLevelPFEName = "TestDailyPFE" + System.currentTimeMillis();
            String dailyLevelCTPName = "TestDailyCTP" + System.currentTimeMillis();
            PfeConfiguration dailyLevelPFE = create( dailyLevelPFEName, dailyLevelCTPName );

            creditAdminSvc.persist( lpOrg, cptyLevelPFE );
            creditAdminSvc.persist( lpOrg, aggLevelPFE );
            creditAdminSvc.persist( lpOrg, dailyLevelPFE );

            PFEConfiguration cptyPFE = creditAdminSvc.getPfeConfiguration( lpOrg, cptyLevelPFEName );
            PFEConfiguration aggPFE = creditAdminSvc.getPfeConfiguration( lpOrg, aggLevelPFEName );
            PFEConfiguration dailyPFE = creditAdminSvc.getPfeConfiguration( lpOrg, dailyLevelPFEName );
            assertNotNull( cptyPFE );
            assertNotNull( aggPFE );
            assertNotNull( dailyPFE );

            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, cptyPFE );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, aggPFE );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, dailyPFE );
            assertTrue( cclr.getPfeConfiguration().isSameAs( cptyPFE ) );

            CreditLimitRule aggRule = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            CreditLimitRule dailyRule = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.DAILY_SETTLEMENT_RULE_SHORT_NAME );

            assertEquals( aggPFE.getShortName(), aggRule.getPfeConfiguration().getShortName() );
            assertEquals( dailyPFE.getShortName(), dailyRule.getPfeConfiguration().getShortName() );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyCreditLimitRuleLevelPFEConfiguration", e );
        }
        finally
        {
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, null );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
        }
    }

    public void testSetCounterpartyCreditLimitRuleLevelUsePFEConfig()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultTenorProfile() );

            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, false );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, false );
            assertFalse( cclr.isUsePFEConfiguration() );

            CreditLimitRule aggRule = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            CreditLimitRule dailyRule = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.DAILY_SETTLEMENT_RULE_SHORT_NAME );

            assertFalse( aggRule.isUsePFEConfiguration() );
            assertFalse( dailyRule.isUsePFEConfiguration() );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyCreditLimitRuleLevelUsePFEConfig", e );
        }
        finally
        {
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, true );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
        }
    }

    public void testTenorProfilePrecedence()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultTenorProfile() );

            String orgLevelCTPName = "TestCPO" + System.currentTimeMillis();
            CreditTenorProfile orgLevelCtp = createCreditTenorProfile( orgLevelCTPName );

            String cptyLevelCTPName = "TestCC" + System.currentTimeMillis();
            CreditTenorProfile cptyLevelCtp = createCreditTenorProfile( cptyLevelCTPName );

            String aggLevelCTPName = "TestAgg" + System.currentTimeMillis();
            CreditTenorProfile aggLevelCTP = createCreditTenorProfile( aggLevelCTPName );

            String dailyLevelCTPName = "TestAgg" + System.currentTimeMillis();
            CreditTenorProfile dailyLevelCTP = createCreditTenorProfile( dailyLevelCTPName );

            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, orgLevelCtp );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, cptyLevelCtp );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, aggLevelCTP );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, dailyLevelCTP );
            assertTrue( cclr.getCreditTenorProfile().isSameAs( cptyLevelCtp ) );

            CreditLimitRule aggRule = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            CreditLimitRule dailyRule = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.DAILY_SETTLEMENT_RULE_SHORT_NAME );

            assertEquals( aggLevelCTP.getShortName(), aggRule.getCreditTenorProfile().getShortName() );
            assertEquals( dailyLevelCTP.getShortName(), dailyRule.getCreditTenorProfile().getShortName() );

            String orgLevelPFEName = "TestCPOPFE" + System.currentTimeMillis();
            String orgLevelDefaultPFECTPName = "TCPOPFEDEFCTP" + System.currentTimeMillis();
            String orgLevelPFECTPName = "TestCPOPFECTP" + System.currentTimeMillis();
            PfeConfiguration orgLevelPFE = create( orgLevelPFEName, orgLevelDefaultPFECTPName, orgLevelPFECTPName );

            String cptyLevelPFEName = "TestCCPFE" + System.currentTimeMillis();
            String cptyLevelPFEDEFCTPName = "TCCPFEDEFCTP" + System.currentTimeMillis();
            String cptyLevelPFECTPName = "TestCCPFECTP" + System.currentTimeMillis();
            PfeConfiguration cptyLevelPFE = create( cptyLevelPFEName, cptyLevelPFEDEFCTPName, cptyLevelPFECTPName );

            String aggLevelPFEName = "TestAggPFE" + System.currentTimeMillis();
            String aggLevelPFEDEFCTPName = "TAPFEDEFCTP" + System.currentTimeMillis();
            String aggLevelPFECTPName = "TestAggPFECTP" + System.currentTimeMillis();
            PfeConfiguration aggLevelPFE = create( aggLevelPFEName, aggLevelPFEDEFCTPName, aggLevelPFECTPName );

            String dailyLevelPFEName = "TestDailyPFE" + System.currentTimeMillis();
            String dailyLevelPFEDEFCTPName = "TPFEDEFCTP" + System.currentTimeMillis();
            String dailyLevelPFECTPName = "TestDailyPFECTP" + System.currentTimeMillis();
            PfeConfiguration dailyLevelPFE = create( dailyLevelPFEName, dailyLevelPFEDEFCTPName, dailyLevelPFECTPName );

            creditAdminSvc.persist( lpOrg, orgLevelPFE );
            creditAdminSvc.persist( lpOrg, cptyLevelPFE );
            creditAdminSvc.persist( lpOrg, aggLevelPFE );
            creditAdminSvc.persist( lpOrg, dailyLevelPFE );

            PFEConfiguration orgPFE = creditAdminSvc.getPfeConfiguration( lpOrg, orgLevelPFEName );
            PFEConfiguration cptyPFE = creditAdminSvc.getPfeConfiguration( lpOrg, cptyLevelPFEName );
            PFEConfiguration aggPFE = creditAdminSvc.getPfeConfiguration( lpOrg, aggLevelPFEName );
            PFEConfiguration dailyPFE = creditAdminSvc.getPfeConfiguration( lpOrg, dailyLevelPFEName );

            creditAdminSvc.setPFEConfiguration( lpOrg, orgPFE );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, cptyPFE );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, aggPFE );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, dailyPFE );

            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForDd, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, true );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            CreditTenorProfile aggCTP = CreditUtilC.getCreditTenorProfile( cclr, aggRule, eur, usd );
            assertEquals( aggLevelPFECTPName, aggCTP.getShortName() );
            CreditTenorProfile dailyCTP = CreditUtilC.getCreditTenorProfile( cclr, dailyRule, eur, usd );
            assertEquals( dailyLevelPFECTPName, dailyCTP.getShortName() );

            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
            aggCTP = CreditUtilC.getCreditTenorProfile( cclr, aggRule, eur, usd );
            assertEquals( aggLevelCTPName, aggCTP.getShortName() );
            dailyCTP = CreditUtilC.getCreditTenorProfile( cclr, dailyRule, eur, usd );
            assertEquals( dailyLevelPFECTPName, dailyCTP.getShortName() );

            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, false );
            aggCTP = CreditUtilC.getCreditTenorProfile( cclr, aggRule, eur, usd );
            assertEquals( aggLevelCTPName, aggCTP.getShortName() );
            dailyCTP = CreditUtilC.getCreditTenorProfile( cclr, dailyRule, eur, usd );
            assertEquals( dailyLevelCTPName, dailyCTP.getShortName() );

            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForDd, false );
            aggCTP = CreditUtilC.getCreditTenorProfile( cclr, aggRule, eur, usd );
            assertEquals( cptyLevelPFECTPName, aggCTP.getShortName() );
            dailyCTP = CreditUtilC.getCreditTenorProfile( cclr, dailyRule, eur, usd );
            assertEquals( cptyLevelPFECTPName, dailyCTP.getShortName() );

            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, false );
            aggCTP = CreditUtilC.getCreditTenorProfile( cclr, aggRule, eur, usd );
            assertEquals( cptyLevelCTPName, aggCTP.getShortName() );
            dailyCTP = CreditUtilC.getCreditTenorProfile( cclr, dailyRule, eur, usd );
            assertEquals( cptyLevelCTPName, dailyCTP.getShortName() );

            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
            aggCTP = CreditUtilC.getCreditTenorProfile( cclr, aggRule, eur, usd );
            assertEquals( orgLevelPFECTPName, aggCTP.getShortName() );
            dailyCTP = CreditUtilC.getCreditTenorProfile( cclr, dailyRule, eur, usd );
            assertEquals( orgLevelPFECTPName, dailyCTP.getShortName() );

            creditAdminSvc.setUsePFEConfiguration( lpOrg, false );
            aggCTP = CreditUtilC.getCreditTenorProfile( cclr, aggRule, eur, usd );
            assertEquals( orgLevelCTPName, aggCTP.getShortName() );
            dailyCTP = CreditUtilC.getCreditTenorProfile( cclr, dailyRule, eur, usd );
            assertEquals( orgLevelCTPName, dailyCTP.getShortName() );

        }
        catch ( Exception e )
        {
            fail( "testTenorProfilePrecedence", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );

            creditAdminSvc.setPFEConfiguration( lpOrg, null );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, null );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setPFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, null );

            creditAdminSvc.setUsePFEConfiguration( lpOrg, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            creditAdminSvc.setUsePFEConfiguration( lpOrg, lpTpForDd, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, true );
            creditAdminSvc.setUseCreditLimitRuleLevelTenorCoefficients( lpOrg, lpTpForDd, false );
        }
    }

    public void testSetStopOutPercentageAtProviderLevel()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setStopOutPercentage ( lpOrg, 99.0 );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet ( lpOrg );
            assertNotNull ( clrs );
            assertNotNull ( clrs.getStopOutPercentage () );
            assertEquals ( 99.0, clrs.getStopOutPercentage () );
            for ( Object obj: clrs.getRules () )
            {
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) obj;
                if ( cclr.isActive () )
                {
                    assertEquals ( 99.0, CreditUtilC.getStopOutPercentage ( cclr ) );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "testSetStopOutPercentageAtProviderLevel", e );
            fail();
        }
        finally
        {
            creditAdminSvc.setStopOutPercentage ( lpOrg, null );
        }
    }

    public void testSetStopOutPercentageAtCptyLevel()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setStopOutPercentage ( lpOrg, null );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet ( lpOrg );
            assertNotNull ( clrs );
            assertNull ( clrs.getStopOutPercentage () );
            assertNull ( clrs.getStopOutPercentage () );
            creditAdminSvc.setStopOutPercentage ( lpOrg, lpTpForFi, 99.0 );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( lpOrg, lpTpForFi );
            assertNotNull ( cclr );
            assertEquals ( 99.0, cclr.getStopOutPercentage () );
            assertNull ( clrs.getStopOutPercentage () );
            assertEquals ( 99.0, CreditUtilC.getStopOutPercentage ( cclr ) );
        }
        catch ( Exception e )
        {
            log.error( "testSetStopOutPercentageAtCptyLevel", e );
            fail();
        }
        finally
        {
            creditAdminSvc.setStopOutPercentage ( lpOrg,lpTpForFi, null );
        }
    }

    public void testAutoStopOutEnabledRulesQueryAtProviderLevel()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setStopOutPercentage ( lpOrg, 99.0 );
            Collection<CounterpartyCreditLimitRule> enabledRules = CreditUtilC.getAutoStopOutEnabledRules ( lpOrg );
            assertNotNull ( enabledRules );
            assertFalse ( enabledRules.isEmpty () );

            creditAdminSvc.setStopOutPercentage ( lpOrg, null );
            enabledRules = CreditUtilC.getAutoStopOutEnabledRules ( lpOrg );
            assertNotNull ( enabledRules );
            assertTrue ( enabledRules.isEmpty () );
        }
        catch ( Exception e )
        {
            log.error( "testAutoStopOutEnabledRulesQueryAtProviderLevel", e );
            fail();
        }
        finally
        {
            creditAdminSvc.setStopOutPercentage ( lpOrg, null );
        }
    }

    public void testAutoStopOutEnabledRulesQueryAtCptyLevel()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setStopOutPercentage ( lpOrg, lpTpForFi, 99.0 );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( lpOrg, lpTpForFi );
            assertNotNull ( cclr );
            Collection<CounterpartyCreditLimitRule> enabledRules = CreditUtilC.getAutoStopOutEnabledRules ( lpOrg );
            assertNotNull ( enabledRules );
            assertTrue ( enabledRules.contains ( cclr ) );

            creditAdminSvc.setStopOutPercentage ( lpOrg, lpTpForFi, null );
            enabledRules = CreditUtilC.getAutoStopOutEnabledRules ( lpOrg );
            assertNotNull ( enabledRules );
            assertFalse ( enabledRules.contains ( cclr ) );
        }
        catch ( Exception e )
        {
            log.error( "testSetStopOutPercentageAtCptyLevel", e );
            fail();
        }
        finally
        {
            creditAdminSvc.setStopOutPercentage ( lpOrg,lpTpForFi, null );
        }
    }
}


