package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.time.IdcDate;
import org.junit.Assert;

/**
 * Tests the credit limit workflow when currency pair exemption is used.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitCurrencyPairExemptionPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Currency Pair exemption  Test";

    public CreditLimitCurrencyPairExemptionPTestC( String name )
    {
        super( name );
    }

    public void testCurrencyPairExemptionFILPOnCreditPreQualificationWithSubscription()
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" );
        Currency USD = CurrencyFactory.getCurrency( "USD" );
        final int SLEEP_TIME = 3000;

        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 200 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 200 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            IdcDate eurUSDSpotDate = CreditUtilC.getSpotDate( EUR, USD ) == null ? spotDate : CreditUtilC.getSpotDate( EUR, USD );

            //Case 1: Currency Pair Not Exempted for LP & FI -- Failure Case
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true, null );
            Thread.sleep( SLEEP_TIME );
            double[] bidLimit;
            double[] offerLimit;
            double origBidLimit, origOfferLimit;

            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            assertEquals( "The available bid limit for EUR/USD should be same as quote limits. bidLimit=" + bidLimit[0] + ",offerLimit=" + offerLimit[0], origBidLimit != bidLimit[0], true );
            assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit != offerLimit[0], true );

            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, getCurrencyPairGroup( "G7" ) );

            //Case 3: Currency Pair Exempted for LP & for FI -- Success Case
            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, getCurrencyPairGroup( "G7" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true, null );
            Thread.sleep( SLEEP_TIME );

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit == bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit == offerLimit[0], true );

            //Case 4: Currency Pair Exempted for FI & Not for LP -- Failure Case
            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true, null );
            Thread.sleep( SLEEP_TIME );

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit != bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit != offerLimit[0], true );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairExemptionFILPOnCreditPreQualificationWithSubscription", e );
        }
        finally
        {
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForFi, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, fiTpForLp, null );
        }
    }

    public void testCurrencyPairExemptionFILPOnCreditPreQualificationWithoutSubscription()
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" );
        Currency USD = CurrencyFactory.getCurrency( "USD" );

        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 200 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 200 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            IdcDate eurUSDSpotDate = CreditUtilC.getSpotDate( EUR, USD ) == null ? spotDate : CreditUtilC.getSpotDate( EUR, USD );

            //Case 1: Currency Pair Not Exempted for LP & FI -- Failure Case
            double[] bidLimit;
            double[] offerLimit;
            double origBidLimit, origOfferLimit;

            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits. bidLimit=" + bidLimit[0] + ",offerLimit=" + offerLimit[0], origBidLimit != bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit != offerLimit[0], true );

            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, getCurrencyPairGroup( "G7" ) );

            //Case 3: Currency Pair Exempted for LP & for FI -- Success Case
            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];

            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, getCurrencyPairGroup( "G7" ) );

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit == bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit == offerLimit[0], true );

            //Case 4: Currency Pair Exempted for FI & Not for LP -- Failure Case
            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit != bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit != offerLimit[0], true );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairExemptionFILPOnCreditPreQualificationWithoutSubscription", e );
        }
        finally
        {
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForFi, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, fiTpForLp, null );
        }
    }

    public void testCurrencyPairExemptionFILPOnCreditPreQualificationForMultiLegTrade()
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" );
        Currency USD = CurrencyFactory.getCurrency( "USD" );

        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 200 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 200 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            IdcDate eurUSDSpotDate = CreditUtilC.getSpotDate( EUR, USD ) == null ? spotDate : CreditUtilC.getSpotDate( EUR, USD );

            //Case 1: Currency Pair Not Exempted for LP & FI -- Failure Case
            double[] bidLimit;
            double[] offerLimit;
            double origBidLimit, origOfferLimit;

            FXSwap trade = prepareSwapTrade( 1000, 1000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade, lpLe, fiLe );
            assertNotNull( cwm );
            assertTrue( cwm.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm.getErrorCode() );

            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, getCurrencyPairGroup( "G7" ) );

            //Case 3: Currency Pair Exempted for LP & for FI -- Success Case
            FXSwap trade1 = prepareSwapTrade( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade1, lpLe, fiLe );
            log( "cwm1=" + cwm1 );
            assertNull( cwm1 );

            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, getCurrencyPairGroup( "G7" ) );

            FXSwap trade2 = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade2, lpLe, fiLe );
            log( "cwm2=" + cwm2 );
            assertNull( cwm2 );

            //Case 4: Currency Pair Exempted for FI & Not for LP -- Failure Case
            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );

            CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            Assert.assertEquals( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit != bidLimit[0], true );
            Assert.assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit != offerLimit[0], true );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairExemptionFILPOnCreditPreQualificationForMultiLegTrade", e );
        }
        finally
        {
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForFi, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, fiTpForLp, null );
        }
    }

    public void testCurrencyPairExemptionFILPOnTrade()
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" );
        Currency USD = CurrencyFactory.getCurrency( "USD" );
        final int SLEEP_TIME = 100;

        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 200 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 200 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );


            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, getCurrencyPairGroup( "G7" ) );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, getCurrencyPairGroup( "G7" ) );

            double limit0 = getAvailableCreditLimit( fiTpForLp, spotDate );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForLp, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, lpOrg, fiTpForLp );
            sleepFor( SLEEP_TIME );
            double limit1 = getAvailableCreditLimit( fiTpForLp, spotDate );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "no credit amount was taken :", true, Math.abs( limit0 - limit1 ) < MINIMUM );


            Thread.sleep( SLEEP_TIME );


            //Case 4: Currency Pair Exempted for FI & Not for LP -- Failure Case
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, null );
            Thread.sleep( SLEEP_TIME );

            double limit2 = getAvailableCreditLimit( fiTpForLp, spotDate );
            log( "credit limit  before take Credit : " + limit2 );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForLp, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, fiLe, lpOrg, fiTpForLp );
            sleepFor( SLEEP_TIME );
            double limit3 = getAvailableCreditLimit( fiTpForLp, spotDate );
            log( "credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() );
            assertEquals( "success=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "credit amount was taken :", true, Math.abs( limit2 - limit3 - tradeAmt ) < MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairExemptionFILPOnTrade", e );
        }
        finally
        {
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForFi, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( fiOrg, fiTpForLp, null );
        }
    }

    protected CurrencyPairGroup getCurrencyPairGroup( String name )
    {
        return ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName( name, CurrencyPairGroupC.class, null, null );
    }
}
