package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.handler.CreditUtilizationCacheSynchronizationHandlerC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.cache.TradeCacheC;
import com.integral.message.MessageStatus;
import com.integral.session.IdcTransaction;
import com.integral.time.IdcDate;
import com.integral.util.IdcUtilC;
import com.integral.util.Tuple;
import com.integral.workflow.dealing.DealingLimit;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Tests the credit limit workflow when prime broker is involved.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitPrimeBrokerageWorkflowPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Prime Brokerage Workflow  Test";

    public CreditLimitPrimeBrokerageWorkflowPTestC( String name )
    {
        super( name );
    }

    public void testPBCoverAmountUpdateForTransparentPBValueDateChange()
    {
        try
        {
            boolean createExternalTx = true;
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setupTakerMakerCreditLimits( ********, ******** );

            setupTakerPBOnly();

            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            double tradeAmt = 1000;
            Trade takerMakerTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, takerMakerTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );

            IdcTransaction tx = null;
            if ( createExternalTx )
            {
                tx = initTransaction( true );
            }
            // now update the counterpartyB of taker maker trade.
            takerMakerTrade.setCounterpartyB( takerPbLe );
            //update the value date of trade - updated by the provider
            updateTradeValueDate( takerMakerTrade, 2 );

            double pbTradeAmt = 900;
            Trade pbMakerTrade = prepareSingleLegTrade( pbTradeAmt, true, false, EURUSD, makerPbOrg, makerTpForTakerPb, bidRates[0], spotDate );
            pbMakerTrade.setTransactionID( takerMakerTrade.getTransactionID() + 'C' );
            takerMakerTrade.setCoverTradeTxIds( pbMakerTrade.getTransactionID() );
            TradeCacheC.getInstance().add( pbMakerTrade );
            TradeCacheC.getInstance().add( takerMakerTrade );

            // now update the taker maker trade.
            CreditWorkflowMessage updateCwm = creditMgr.updateBilateralCreditUtilizationAmount( makerLe, takerLe, takerMakerTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + updateCwm.getStatus(), updateCwm.getStatus(), MessageStatus.SUCCESS );
            if ( createExternalTx )
            {
                commitTransaction( tx );
            }


            double aggregateLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdateForNextDay0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfterUpdate0=" + aggregateLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUpdate0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfterUpdate1=" + aggregateLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUpdate1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfterUpdate2=" + aggregateLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfterUpdate3=" + aggregateLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUpdate3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfterUpdate4=" + aggregateLimitAfterUpdate4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUpdate4 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfterUpdate5=" + aggregateLimitAfterUpdate5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUpdate5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfterUpdate0=" + dailyLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUpdate0 ) < CREDIT_CALCULATION_MINIMUM );
            // assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfterUpdateForNextDay0=" + dailyLimitAfterUpdateForNextDay0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUpdateForNextDay0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfterUpdate1=" + dailyLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUpdate1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfterUpdate2=" + dailyLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfterUpdate3=" + dailyLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUpdate3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfterUpdate4=" + dailyLimitAfterUpdate4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUpdate4 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfterUpdate5=" + dailyLimitAfterUpdate5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUpdate5 ) < CREDIT_CALCULATION_MINIMUM );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit.
     */
    public void testSimplePrimeBrokerageCredit()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setupTakerMakerCreditLimits( aggregateLimit, dailyLimit );

            setupBothPBs();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );

            // disable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, false );
            creditAdminSvc.setCreditEnabled( makerOrg, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, false );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, false );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, false );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, false );

            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be false. enabled1=" + enabled1, enabled1, false );

            // enable only one relationship
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditBothSidesEnabledWithSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            setupTakerMakerCreditEnabled();

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );


            setupBothPBs();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + undoCwm.getStatus(), undoCwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditBothSidesEnabledWithNettingAndSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            setupBothPBs();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm1.status=" + cwm1.getStatus()  );
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfterOppositeTrade0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterOppositeTrade5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterOppositeTrade5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterOppositeTrade0=" + aggregateLimitAfterOppositeTrade0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterOppositeTrade0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterOppositeTrade1=" + aggregateLimitAfterOppositeTrade1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterOppositeTrade1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterOppositeTrade2=" + aggregateLimitAfterOppositeTrade2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterOppositeTrade2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterOppositeTrade3=" + aggregateLimitAfterOppositeTrade3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterOppositeTrade3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterOppositeTrade4=" + aggregateLimitAfterOppositeTrade4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterOppositeTrade4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterOppositeTrade5=" + aggregateLimitAfterOppositeTrade5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterOppositeTrade5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterOppositeTrade0=" + dailyLimitAfterOppositeTrade0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterOppositeTrade0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterOppositeTrade1=" + dailyLimitAfterOppositeTrade1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterOppositeTrade1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterOppositeTrade2=" + dailyLimitAfterOppositeTrade2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterOppositeTrade2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterOppositeTrade3=" + dailyLimitAfterOppositeTrade3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterOppositeTrade3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterOppositeTrade4=" + dailyLimitAfterOppositeTrade4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterOppositeTrade4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterOppositeTrade5=" + dailyLimitAfterOppositeTrade5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterOppositeTrade5 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditBothSidesEnabledFailureWithNettingAndSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 6000000;
            double aggregateLimit = 6000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            setupBothPBs();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit - ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit - ( aggregateLimitOffset * 5 ) );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 2000000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the FI -> FI Pb, FI Pb -> LP, LP -> FI credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditLPSideDisabledWithSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            setupTakerPBOnly();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "undoCwm.status=" + undoCwm.getStatus() );
            assertEquals( "Message success status=" + undoCwm.getStatus(), undoCwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the LP -> LP Pb, LP Pb -> FI, FI -> LP credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditFISideDisabledWithSubscriptions()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            setupMakerPBOnly();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, aggregateLimit );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( takerLe, makerLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "undoCwm.status=" + undoCwm.getStatus() );
            assertEquals( "Message success status=" + undoCwm.getStatus(), undoCwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the FI <-> FI Pb, FI Pb <-> LP Pb, LP Pb <-> LP credit lines with a take credit and then undo credit after disabling the credit for prime brokerage.
     */
    public void testPrimeBrokerageCreditBothDisabled()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 0.0 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 0.0 );

            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // disable the prime brokerage credit.
            disableBothPBs();

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the FI -> FI Pb, FI Pb -> LP, LP -> FI credit lines with a take credit and then undo credit after disabling the credit for prime brokerage.
     */
    public void testSimplePrimeBrokerageCreditLPSideDisabled()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 3 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 3 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 2 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 2 );

            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // disable the prime brokerage credit.
            setupTakerPBOnly();

            // check the available credit limit for USD and CHF. This dealing limit will be available credit limit between taker's credit for maker and bilateral available between maker pb and maker.
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            DealingLimit dealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, usd, chf, false );
            DealingLimit takerCreditForTakerPb = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( takerLe, takerTpForTakerPb, takerPbOrg, spotDate, usd, chf, false );
            DealingLimit takerPbCreditForMaker = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( takerPbLe, takerPbTpForMaker, makerOrg, spotDate, usd, chf, false );
            DealingLimit makerCreditForTaker = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( makerLe, makerTpForTaker, takerOrg, spotDate, usd, chf, false );
            log( "dealingLimit=" + dealingLimit + ",takerCreditForTakerPb=" + takerCreditForTakerPb + ",takerPbCreditForMaker=" + takerPbCreditForMaker + ",makerCreditForTaker" + makerCreditForTaker );

            double minBidLimit1 = takerCreditForTakerPb.getBidLimit() > takerPbCreditForMaker.getBidLimit() ? takerPbCreditForMaker.getBidLimit() : takerCreditForTakerPb.getBidLimit();
            double minBidLimit = minBidLimit1 > makerCreditForTaker.getBidLimit() ? makerCreditForTaker.getBidLimit() : minBidLimit1;
            double minOfferLimit1 = takerCreditForTakerPb.getOfferLimit() > takerPbCreditForMaker.getOfferLimit() ? takerPbCreditForMaker.getOfferLimit() : takerCreditForTakerPb.getOfferLimit();
            double minOfferLimit = minOfferLimit1 > makerCreditForTaker.getOfferLimit() ? makerCreditForTaker.getOfferLimit() : minBidLimit1;

            assertEquals( "bid limit should be equal to min of three limits.", dealingLimit.getBidLimit(), minBidLimit );
            assertEquals( "offer limit should be equal to min of three limits.", dealingLimit.getOfferLimit(), minOfferLimit );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            assertEquals( "Message status after undo credit should be success.", undoCwm.getStatus() == MessageStatus.SUCCESS, true );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );

            // check bilateral credit enabled flags
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, false );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, false );
            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be true. enabled1=" + enabled1, enabled1, false );

            // now mark one of them enabled
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    /**
     * Tests the FI -> FI Pb, FI Pb -> LP, LP -> FI credit lines with a take credit and then undo credit after disabling the credit for prime brokerage.
     */
    public void testSimplePrimeBrokerageUndoCreditLPSideDisabled()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 3 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 3 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 2 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 2 );

            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // now take a simple trade
            double tradeAmt = 1000;

            // disable the prime brokerage credit.
            setupTakerPBOnly();

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            assertEquals( "Message status after undo credit should be success.", undoCwm.getStatus() == MessageStatus.SUCCESS, true );

            // check bilateral credit enabled flags
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, false );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, false );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, false );
            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be true. enabled1=" + enabled1, enabled1, false );

            // now mark one of them enabled
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }


    /**
     * Tests the LP -> LP Pb, LP Pb -> FI, FI -> LP credit lines with a take credit and then undo credit after disabling the credit for prime brokerage.
     */
    public void testSimplePrimeBrokerageCreditFISideDisabled()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 3 );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 3 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit * 2 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit * 2 );

            setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // disable the prime brokerage credit.
            setupMakerPBOnly();

            // check the available credit limit for USD and CHF. This dealing limit will be available credit limit between taker's credit for maker and bilateral available between maker pb and maker.
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            DealingLimit dealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, usd, chf, false );
            DealingLimit makerCreditForMakerPb = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( makerLe, makerTpForMakerPb, makerPbOrg, spotDate, usd, chf, false );
            DealingLimit makerPbCreditForTaker = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( makerPbLe, makerPbTpForTaker, takerOrg, spotDate, usd, chf, false );
            DealingLimit takerCreditForMaker = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( takerLe, takerTpForMaker, makerOrg, spotDate, usd, chf, false );
            log( "dealingLimit=" + dealingLimit + ",makerCreditForMakerPb=" + makerCreditForMakerPb + ",makerPbCreditForTaker=" + makerPbCreditForTaker + ",takerCreditForMaker" + takerCreditForMaker );

            double minBidLimit1 = makerCreditForMakerPb.getBidLimit() > makerPbCreditForTaker.getBidLimit() ? makerPbCreditForTaker.getBidLimit() : makerCreditForMakerPb.getBidLimit();
            double minBidLimit = minBidLimit1 > takerCreditForMaker.getBidLimit() ? takerCreditForMaker.getBidLimit() : minBidLimit1;
            double minOfferLimit1 = makerCreditForMakerPb.getOfferLimit() > makerPbCreditForTaker.getOfferLimit() ? makerPbCreditForTaker.getOfferLimit() : makerCreditForMakerPb.getOfferLimit();
            double minOfferLimit = minOfferLimit1 > takerCreditForMaker.getOfferLimit() ? takerCreditForMaker.getOfferLimit() : minOfferLimit1;

            assertEquals( "bid limit should be equal to min of three limits.", dealingLimit.getBidLimit(), minBidLimit );
            assertEquals( "offer limit should be equal to min of three limits.", dealingLimit.getOfferLimit(), minOfferLimit );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( takerLe, makerLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );

            // check bilateral credit enabled flags
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, false );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, false );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, false );
            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be true. enabled1=" + enabled1, enabled1, false );

            // now mark one of them enabled
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( takerLe, makerLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForTransparentPBExternalTx()
    {
        try
        {
            testPBCoverCreditUtilizationAmountUpdateForTransparentPB( true );
        }
        catch ( Exception e )
        {
            log.error( "testPBCoverCreditUtilizationAmountUpdateForTransparentPBExternalTx", e );
            fail();
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForTransparentPBNoExternalTx()
    {
        try
        {
            testPBCoverCreditUtilizationAmountUpdateForTransparentPB( false );
        }
        catch ( Exception e )
        {
            log.error( "testPBCoverCreditUtilizationAmountUpdateForTransparentPBNoExternalTx", e );
            fail();
        }
    }

    public void testCreditRelationsForTransparentPB()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            setupBothPBs();

            //Scenario 1: transparent PB set for both sides enabled
            ArrayList<CreditEntity> creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, makerLe );
            assertEquals( creditRelations.size(), 6 );
            assertEquals( creditRelations.get( 0 ).getLegalEntity(), takerLe );
            assertEquals( creditRelations.get( 0 ).getTradingParty(), takerTpForTakerPb );
            assertEquals( creditRelations.get( 1 ).getLegalEntity(), takerPbLe );
            assertEquals( creditRelations.get( 1 ).getTradingParty(), takerPbTpForTaker );
            assertEquals( creditRelations.get( 2 ).getLegalEntity(), makerLe );
            assertEquals( creditRelations.get( 2 ).getTradingParty(), makerTpForMakerPb );
            assertEquals( creditRelations.get( 3 ).getLegalEntity(), makerPbLe );
            assertEquals( creditRelations.get( 3 ).getTradingParty(), makerPbTpForMaker );
            assertEquals( creditRelations.get( 4 ).getLegalEntity(), takerPbLe );
            assertEquals( creditRelations.get( 4 ).getTradingParty(), takerPbTpForMakerPb );
            assertEquals( creditRelations.get( 5 ).getLegalEntity(), makerPbLe );
            assertEquals( creditRelations.get( 5 ).getTradingParty(), makerPbTpForTakerPb );

            //scenario 2: transparent PB with PB enabled for org1 side only
            setupTakerPBOnly();

            creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, makerLe );
            assertEquals( creditRelations.size(), 3 );
            assertEquals( creditRelations.get( 0 ).getLegalEntity(), takerLe );
            assertEquals( creditRelations.get( 0 ).getTradingParty(), takerTpForTakerPb );
            assertEquals( creditRelations.get( 1 ).getLegalEntity(), takerPbLe );
            assertEquals( creditRelations.get( 1 ).getTradingParty(), takerPbTpForMaker );
            assertEquals( creditRelations.get( 2 ).getLegalEntity(), makerLe );
            assertEquals( creditRelations.get( 2 ).getTradingParty(), makerTpForTaker );

            //scenario 3: transparent PB with PB enabled for org2 side only
            setupMakerPBOnly();

            creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, makerLe );
            assertEquals( creditRelations.size(), 3 );
            assertEquals( creditRelations.get( 0 ).getLegalEntity(), makerLe );
            assertEquals( creditRelations.get( 0 ).getTradingParty(), makerTpForMakerPb );
            assertEquals( creditRelations.get( 1 ).getLegalEntity(), makerPbLe );
            assertEquals( creditRelations.get( 1 ).getTradingParty(), makerPbTpForTaker );
            assertEquals( creditRelations.get( 2 ).getLegalEntity(), takerLe );
            assertEquals( creditRelations.get( 2 ).getTradingParty(), takerTpForMaker );

            //scenario 4: transparent PB with both PB disabled
            disableBothPBs();

            creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, makerLe );
            assertEquals( creditRelations.size(), 2 );
            assertEquals( creditRelations.get( 0 ).getLegalEntity(), takerLe );
            assertEquals( creditRelations.get( 0 ).getTradingParty(), takerTpForMaker );
            assertEquals( creditRelations.get( 1 ).getLegalEntity(), makerLe );
            assertEquals( creditRelations.get( 1 ).getTradingParty(), makerTpForTaker );
        }
        catch ( Exception e )
        {
            fail( "testCreditRelationsForTransparentPB", e );
        }
        finally
        {
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForTransparentPB( boolean createExternalTx )
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );

            setupTakerPBOnly();

            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            double tradeAmt = 1000;
            Trade takerMakerTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, takerMakerTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );

            IdcTransaction tx = null;
            if ( createExternalTx )
            {
                tx = initTransaction( true );
            }
            // now update the counterpartyB of taker maker trade.
            takerMakerTrade.setCounterpartyB( takerPbLe );
            double pbTradeAmt = 900;
            Trade pbMakerTrade = prepareSingleLegTrade( pbTradeAmt, true, false, EURUSD, makerOrg, makerTpForTakerPb, bidRates[0], spotDate );
            pbMakerTrade.setTransactionID( takerMakerTrade.getTransactionID() + 'C' );
            takerMakerTrade.setCoverTradeTxIds( pbMakerTrade.getTransactionID() );
            TradeCacheC.getInstance().add( pbMakerTrade );
            TradeCacheC.getInstance().add( takerMakerTrade );

            // now update the taker maker trade.
            CreditWorkflowMessage updateCwm = creditMgr.updateBilateralCreditUtilizationAmount( makerLe, takerLe, takerMakerTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + updateCwm.getStatus(), updateCwm.getStatus(), MessageStatus.SUCCESS );
            if ( createExternalTx )
            {
                commitTransaction( tx );
            }


            double aggregateLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate4 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate5 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate4 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate5 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfterUpdate0=" + aggregateLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUpdate0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfterUpdate1=" + aggregateLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUpdate1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfterUpdate2=" + aggregateLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfterUpdate3=" + aggregateLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUpdate3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfterUpdate4=" + aggregateLimitAfterUpdate4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUpdate4 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfterUpdate5=" + aggregateLimitAfterUpdate5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUpdate5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfterUpdate0=" + dailyLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUpdate0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfterUpdate1=" + dailyLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUpdate1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfterUpdate2=" + dailyLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfterUpdate3=" + dailyLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUpdate3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfterUpdate4=" + dailyLimitAfterUpdate4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUpdate4 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfterUpdate5=" + dailyLimitAfterUpdate5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUpdate5 ) < CREDIT_CALCULATION_MINIMUM );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testTakerMakerCreditOrg()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            // enable the credit for all orgs and credit cpty
            setupTakerMakerCreditEnabled();

            // check the regular FI<=>LP
            takerTpForMaker.setPrimeBrokerageCreditUsed( false );
            makerTpForTaker.setPrimeBrokerageCreditUsed( false );
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, eur, usd, false );
            assertNotNull( dl );
            assertEquals( dl.getTakerPBLegalEntity(), makerLe );
            assertEquals( dl.getMakerPBLegalEntity(), takerLe );

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, eur, usd, true );
            assertNotNull( dl1 );
            assertEquals( dl1.getTakerPBLegalEntity(), takerLe );
            assertEquals( dl1.getMakerPBLegalEntity(), makerLe );

            // check one sided pb.
            takerTpForMaker.setPrimeBrokerageCreditUsed( true );
            makerTpForTaker.setPrimeBrokerageCreditUsed( false );
            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, eur, usd, false );
            assertNotNull( dl2 );
            assertEquals( dl2.getTakerPBLegalEntity(), makerLe );
            assertEquals( dl2.getMakerPBLegalEntity(), takerPbLe );

            DealingLimit dl3 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, eur, usd, true );
            assertNotNull( dl3 );
            assertEquals( dl3.getTakerPBLegalEntity(), takerPbLe );
            assertEquals( dl3.getMakerPBLegalEntity(), makerLe );

            // check other sided pb.
            takerTpForMaker.setPrimeBrokerageCreditUsed( false );
            makerTpForTaker.setPrimeBrokerageCreditUsed( true );
            DealingLimit dl4 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, eur, usd, false );
            assertNotNull( dl4 );
            assertEquals( dl4.getTakerPBLegalEntity(), makerPbLe );
            assertEquals( dl4.getMakerPBLegalEntity(), takerLe );

            DealingLimit dl5 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, eur, usd, true );
            assertNotNull( dl5 );
            assertEquals( dl5.getTakerPBLegalEntity(), takerLe );
            assertEquals( dl5.getMakerPBLegalEntity(), makerPbLe );

            // have both sides pb
            takerTpForMaker.setPrimeBrokerageCreditUsed( true );
            makerTpForTaker.setPrimeBrokerageCreditUsed( true );
            DealingLimit dl6 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, eur, usd, false );
            assertNotNull( dl6 );
            assertEquals( dl6.getTakerPBLegalEntity(), takerPbLe );
            assertEquals( dl6.getMakerPBLegalEntity(), makerPbLe );

            DealingLimit dl7 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( takerLe, makerLe, spotDate, eur, usd, true );
            assertNotNull( dl7 );
            assertEquals( dl7.getTakerPBLegalEntity(), makerPbLe );
            assertEquals( dl7.getMakerPBLegalEntity(), takerPbLe );
        }
        catch ( Exception e )
        {
            fail( "testTakerMakerCreditOrg", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject( takerTpForMaker );
            IdcUtilC.refreshObject( makerTpForTaker );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUndoFIPB1PB2LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );
            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUndoFIPB1PB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUpdateAmountFIPB1PB2LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );
            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now update credit.
                    FXPaymentParameters fxPmt = ( ( FXSingleLeg ) trade1 ).getFXLeg().getFXPayment();
                    fxPmt.setCurrency1Amount( fxPmt.getCurrency1Amount() / 2.0 );
                    fxPmt.setCurrency2Amount( fxPmt.getCurrency2Amount() / 2.0 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount( takerLe, makerLe, trade1, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUpdateAmountFIPB1PB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void _testCreditUtilizationUpdatesOnBilateralTakeAmendCptyFIPB1PB2LP()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( makerLe, takerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now book the trade in a different cpty
                    LegalEntity newMakerLe = lpLe;
                    trade1.setCounterpartyA( newMakerLe );
                    CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( takerLe, newMakerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg2 = ( CreditWorkflowMessage ) cwm2.getReplyMessage();
                    String eventSnapshot2 = ( String ) replyMsg2.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event2 = replyMsg2.getEventName();
                    IdcDate tradeDate2 = replyMsg2.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event2, eventSnapshot2, trade1.getTransactionID(), tradeDate2 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeAmendCptyFIPB1PB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillFIPB1PB2LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );
            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 500;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( takerLe, makerLe, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillFIPB1PB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFillFIPB1PB2LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );
            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 100;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( takerLe, makerLe, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFillFIPB1PB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUndoFIPB1LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupTakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUndoFIPB1LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUpdateAmountFIPB1LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupTakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, takerOrg, takerTpForMaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now update credit.
                    FXPaymentParameters fxPmt = ( ( FXSingleLeg ) trade1 ).getFXLeg().getFXPayment();
                    fxPmt.setCurrency1Amount( fxPmt.getCurrency1Amount() / 2.0 );
                    fxPmt.setCurrency2Amount( fxPmt.getCurrency2Amount() / 2.0 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount( takerLe, makerLe, trade1, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUpdateAmountFIPB1LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void _testCreditUtilizationUpdatesOnBilateralTakeAmendCptyFIPB1LP()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupTakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( makerLe, takerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now book the trade in a different cpty
                    LegalEntity newMakerLe = lpLe;
                    trade1.setCounterpartyA( newMakerLe );
                    CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( takerLe, newMakerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg2 = ( CreditWorkflowMessage ) cwm2.getReplyMessage();
                    String eventSnapshot2 = ( String ) replyMsg2.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event2 = replyMsg2.getEventName();
                    IdcDate tradeDate2 = replyMsg2.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event2, eventSnapshot2, trade1.getTransactionID(), tradeDate2 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeAmendCptyFIPB1LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillFIPB1LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupTakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 500;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( takerLe, makerLe, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillFIPB1LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFillFIPB1LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupTakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();
            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 100;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( takerLe, makerLe, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFillFIPB1LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUndoFIPB2LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupMakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUndoFIPB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUpdateAmountFIPB2LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupMakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, takerOrg, takerTpForMaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now update credit.
                    FXPaymentParameters fxPmt = ( ( FXSingleLeg ) trade1 ).getFXLeg().getFXPayment();
                    fxPmt.setCurrency1Amount( fxPmt.getCurrency1Amount() / 2.0 );
                    fxPmt.setCurrency2Amount( fxPmt.getCurrency2Amount() / 2.0 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount( takerLe, makerLe, trade1, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUpdateAmountFIPB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void _testCreditUtilizationUpdatesOnBilateralTakeAmendCptyFIPB2LP()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupMakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( makerLe, takerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now book the trade in a different cpty
                    LegalEntity newMakerLe = lpLe;
                    trade1.setCounterpartyA( newMakerLe );
                    CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( takerLe, newMakerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg2 = ( CreditWorkflowMessage ) cwm2.getReplyMessage();
                    String eventSnapshot2 = ( String ) replyMsg2.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event2 = replyMsg2.getEventName();
                    IdcDate tradeDate2 = replyMsg2.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event2, eventSnapshot2, trade1.getTransactionID(), tradeDate2 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeAmendCptyFIPB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillFIPB2LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupMakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 500;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( takerLe, makerLe, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillFIPB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFillFIPB2LP()
    {
        try
        {
            init( takerUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            setupMakerPBOnly();

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();
            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( takerOrg );
                    removeExistingCreditUtilizationEvents( makerOrg );
                    removeExistingCreditUtilizationEvents( takerPbOrg );
                    removeExistingCreditUtilizationEvents( makerPbOrg );

                    // enable the credit for all orgs and credit cpty
                    setupTakerMakerCreditEnabled();

                    // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000 );
                    setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( takerLe, makerLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 100;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( takerLe, makerLe, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFillFIPB2LP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            setupBothPBs();
            setupTakerMakerCreditEnabled();
            setupTakerMakerCreditLimits( ********, ******** );
        }
    }

    protected void setupBothPBs()
    {
        if ( CreditUtilC.getCreditPrimeBrokerTradingparty( takerTpForMaker ) != null && CreditUtilC.getCreditPrimeBrokerTradingparty( makerTpForTaker ) != null )
        {
            return;
        }

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
        registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
        TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
        registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
        uow.commit();
    }

    protected void setupTakerPBOnly()
    {
        if ( CreditUtilC.getCreditPrimeBrokerTradingparty( takerTpForMaker ) != null && CreditUtilC.getCreditPrimeBrokerTradingparty( makerTpForTaker ) == null )
        {
            return;
        }

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
        registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( true );
        TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
        registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
        uow.commit();
    }

    protected void setupMakerPBOnly()
    {
        if ( CreditUtilC.getCreditPrimeBrokerTradingparty( takerTpForMaker ) == null && CreditUtilC.getCreditPrimeBrokerTradingparty( makerTpForTaker ) != null )
        {
            return;
        }

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
        registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( false );
        TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
        registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
        uow.commit();
    }

    protected void disableBothPBs()
    {
        if ( CreditUtilC.getCreditPrimeBrokerTradingparty( takerTpForMaker ) == null && CreditUtilC.getCreditPrimeBrokerTradingparty( makerTpForTaker ) == null )
        {
            return;
        }

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        TradingParty registeredTakerTpForMaker = ( TradingParty ) uow.registerObject( takerTpForMaker );
        registeredTakerTpForMaker.setPrimeBrokerageCreditUsed( false );
        TradingParty registeredMakerTpForTaker = ( TradingParty ) uow.registerObject( makerTpForTaker );
        registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( false );
        uow.commit();
    }

    protected void setupTakerMakerCreditEnabled()
    {
        creditAdminSvc.setCreditEnabled( takerOrg, true );
        creditAdminSvc.setCreditEnabled( makerOrg, true );
        creditAdminSvc.setCreditEnabled( takerPbOrg, true );
        creditAdminSvc.setCreditEnabled( makerPbOrg, true );

        creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
        creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
        creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMakerPb, true );

        creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
        creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
        creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );

        creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
        creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
        creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );

        creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
        creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTakerPb, true );
        creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );
    }

    protected void setupTakerMakerCreditLimits ( double aggLimit, double dailyLimit )
    {
        setCalcAndLimit( takerOrg, takerTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( takerOrg, takerTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( takerOrg, takerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( takerOrg, takerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( makerOrg, makerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( makerOrg, makerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );

        setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
        setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
    }


}
