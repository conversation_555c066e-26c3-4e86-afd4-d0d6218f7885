package com.integral.finance.creditLimit.test;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.enums.StopOutState;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.user.Organization;
import com.integral.util.GUIDFactory;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Date;
import java.util.concurrent.locks.Lock;


public class CounterpartyCreditLimitRulePTestC extends PTestCaseC
{
    static String name = "CounterpartyCreditLimitRule Test";

    public CounterpartyCreditLimitRulePTestC( String name )
    {
        super( name );
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CounterpartyCreditLimitRule testCclr = CreditLimitFactory.newCounterpartyCreditLimitRule();
            Lock lock = testCclr.getLock();
            int lockHashCode = lock.hashCode();
            log.info("Lock =" + lock + ",lockHashCode=" + lockHashCode);
            testCclr.setShortName("Test_" + System.nanoTime());
            assertFalse(testCclr.isOrgLevel());
            assertTrue(testCclr.isEnabled());
            assertTrue(testCclr.isActive());
            assertFalse(testCclr.isTradingSuspended());
            assertTrue(testCclr.isUseDefaultExemptCurrencyPairGroup());
            assertTrue(testCclr.isUseDefaultTenorProfile());
            assertTrue( testCclr.getMode() == null );
            assertTrue(testCclr.isUseDefaultMode());
            assertFalse(testCclr.isLeOverride());
            assertTrue(testCclr.isOrgDefault());
            assertFalse( testCclr.isTenorRestrictionInBusinessDays() );
            assertFalse ( testCclr.isCreditLimitRuleLevelTenorCoefficients () );
            assertNull ( testCclr.getStopOutPercentage () );
            assertNull ( testCclr.getStopOutDate () );
            assertNull ( testCclr.getStopOutId () );
            assertEquals ( StopOutState.INITIAL, testCclr.getStopOutState () );

            CounterpartyCreditLimitRule registeredTestClr = ( CounterpartyCreditLimitRule ) uow.registerObject( testCclr );
            assertFalse( testCclr.isTradingSuspended() );
            assertTrue(registeredTestClr.isUseDefaultExemptCurrencyPairGroup());
            assertTrue( registeredTestClr.isUseDefaultTenorProfile() );
            assertTrue( registeredTestClr.isUseDefaultMode() );
            registeredTestClr.setEnabled(false);
            registeredTestClr.setActive(false);
            registeredTestClr.setOrgLevel(true);
            registeredTestClr.setTradingSuspended( true );
            registeredTestClr.setUseDefaultExemptCurrencyPairGroup( false );
            registeredTestClr.setUseDefaultTenorProfile( false );
            registeredTestClr.setUseDefaultMode( false );
            registeredTestClr.setTenorRestrictionInBusinessDays( true );
            Lock lock1 = testCclr.getLock();
            int lockHashCode1 = lock1.hashCode();
            log.info( "Lock1 =" + lock1 + ",lockHashCode1=" + lockHashCode1 );
            assertEquals( lockHashCode, lockHashCode1 );
            assertFalse(registeredTestClr.isLeOverride());
            assertTrue(registeredTestClr.isOrgDefault());
            assertTrue( registeredTestClr.isTenorRestrictionInBusinessDays() );
            assertFalse ( registeredTestClr.isCreditLimitRuleLevelTenorCoefficients () );
            assertNull ( registeredTestClr.getStopOutPercentage () );
            assertNull ( registeredTestClr.getStopOutDate () );
            assertNull ( registeredTestClr.getStopOutId () );
            assertEquals ( StopOutState.INITIAL, registeredTestClr.getStopOutState () );

            registeredTestClr.setStopOutPercentage ( 99.0 );
            Date date = DateTimeFactory.newDate ().asJdkDate ();
            registeredTestClr.setStopOutDate ( date );
            String guid = GUIDFactory.createGUID ();
            registeredTestClr.setStopOutId ( guid );
            registeredTestClr.setStopOutState ( StopOutState.EXECUTED );

            CreditTenorProfile profile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regProfile = ( CreditTenorProfile ) uow.registerObject( profile );
            regProfile.setStatus( 'T' );
            String name = "Test" + System.nanoTime();
            regProfile.setShortName( name );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );

            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent(20.0);
            regCnp2.setTenor(new Tenor("1W"));

            regProfile.getCreditTenorParameters().add(regCnp1);
            regProfile.getCreditTenorParameters().add(regCnp2);
            registeredTestClr.setCreditTenorProfile(regProfile);

            registeredTestClr.setLeOverride( true );
            registeredTestClr.setOrgDefault( false );

            // assign an exemption currency pair group.
            CurrencyPairGroup cpg = ( CurrencyPairGroup ) namedEntityReader.execute( CurrencyPairGroupC.class, "G7", IdcUtilC.MAIN_NAMESPACE );
            assertNotNull( cpg );
            CurrencyPairGroup regCpg = ( CurrencyPairGroup ) uow.registerObject( cpg );
            registeredTestClr.setExemptCurrencyPairGroup( regCpg );

            registeredTestClr.setMode( 1 );
            registeredTestClr.setCreditLimitRuleLevelTenorCoefficients ( true );
            uow.commit();

            // refresh the trade and verify the fields
            testCclr.setExemptCurrencyPairGroup( null );
            testCclr.setCreditTenorProfile(null);
            testCclr = ( CounterpartyCreditLimitRule ) session.refreshObject( testCclr );
            log( "testClr=" + testCclr + ",testClr.active=" + testCclr.isActive() + ",testCclr.enabled="
                    + testCclr.isEnabled() + ",orgLevel=" + testCclr.isOrgLevel() );
            assertTrue(testCclr.isOrgLevel());
            assertFalse(testCclr.isEnabled());
            assertFalse( testCclr.isActive() );
            assertTrue(testCclr.isLeOverride());
            Lock lock2 = testCclr.getLock();
            int lockHashCode2 = lock2.hashCode();
            log.info( "Lock2 =" + lock2 + ",lockHashCode2=" + lockHashCode2 );
            assertEquals( lockHashCode, lockHashCode2 );
            assertTrue( testCclr.isTradingSuspended() );
            assertNotNull( testCclr.getCreditTenorProfile() );
            assertNotNull( testCclr.getExemptCurrencyPairGroup() );
            assertTrue( testCclr.getExemptCurrencyPairGroup().isSameAs( cpg ) );
            assertFalse( testCclr.isUseDefaultExemptCurrencyPairGroup() );
            assertFalse( testCclr.isUseDefaultTenorProfile() );
            assertTrue( testCclr.getMode() == 1 );
            assertFalse( testCclr.isUseDefaultMode() );
            assertTrue(testCclr.isLeOverride());
            assertFalse(testCclr.isOrgDefault() );
            assertTrue( testCclr.isTenorRestrictionInBusinessDays() );
            assertTrue ( testCclr.isCreditLimitRuleLevelTenorCoefficients () );
            assertEquals ( 99.0, testCclr.getStopOutPercentage () );
            assertEquals ( date, testCclr.getStopOutDate () );
            assertEquals ( guid, testCclr.getStopOutId () );
            assertEquals ( StopOutState.EXECUTED, testCclr.getStopOutState () );
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence : ", e );
        }
    }

    public void testCounterpartyCreditLimitRuleTenorPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CounterpartyCreditLimitRule testCclr = CreditLimitFactory.newCounterpartyCreditLimitRule();
            testCclr.setShortName("Test_" + System.nanoTime());

            CounterpartyCreditLimitRule registeredTestClr = ( CounterpartyCreditLimitRule ) uow.registerObject( testCclr );
            registeredTestClr.setEnabled(false);
            registeredTestClr.setActive(false);
            registeredTestClr.setOrgLevel(true);
            registeredTestClr.setTradingSuspended( true );
            registeredTestClr.setUseDefaultExemptCurrencyPairGroup( false );
            registeredTestClr.setUseDefaultTenorProfile( false );
            registeredTestClr.setUseDefaultMode( false );
            registeredTestClr.setTenorRestrictionInBusinessDays( true );


            registeredTestClr.setMode( 1 );
            Tenor spTenor = Tenor.SPOT_TENOR;
            int spTenorHashCode = spTenor.hashCode ();
            Tenor todTenor = Tenor.TODAY_TENOR;
            int todTenorHashCode = todTenor.hashCode ();
            registeredTestClr.setMinimumTenor ( Tenor.TODAY_TENOR );
            registeredTestClr.setMaximumTenor ( Tenor.SPOT_TENOR );
            uow.commit();

            testCclr = ( CounterpartyCreditLimitRuleC ) IdcUtilC.refreshObject ( testCclr );
            assertNotNull ( testCclr );
            assertEquals ( testCclr.getMinimumTenor (), Tenor.TODAY_TENOR );
            assertEquals ( testCclr.getMaximumTenor (), Tenor.SPOT_TENOR );

            uow = session.acquireUnitOfWork();
            CounterpartyCreditLimitRule registeredTestCclr1 = ( CounterpartyCreditLimitRule ) uow.registerObject( testCclr );
            registeredTestCclr1.setMinimumTenor ( Tenor.TOMORROW_TENOR );
            registeredTestCclr1.setMaximumTenor ( Tenor.SPOT_NEXT_TENOR );
            uow.commit ();

            // refresh the trade and verify the fields
            testCclr = ( CounterpartyCreditLimitRuleC ) IdcUtilC.refreshObject ( testCclr );
            assertNotNull ( testCclr );
            assertEquals ( testCclr.getMinimumTenor (), Tenor.TOMORROW_TENOR );
            assertEquals ( testCclr.getMaximumTenor (), Tenor.SPOT_NEXT_TENOR );
            assertEquals( Tenor.TODAY_TENOR.getDatePeriodString (), new Tenor( "TOD").getDatePeriodString () );
            assertEquals( Tenor.TODAY_TENOR.getDatePeriod(), new Tenor( "TOD").getDatePeriod () );
            assertEquals( Tenor.SPOT_TENOR.getDatePeriodString (), new Tenor( "SPOT").getDatePeriodString () );
            assertEquals( Tenor.SPOT_TENOR.getDatePeriod(), new Tenor( "SPOT").getDatePeriod () );
         }
        catch ( Exception e )
        {
            fail( "testCounterpartyCreditLimitRuleTenorPersistence", e );
        }
    }

    public void testSetPFEConfiguration()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CounterpartyCreditLimitRule testCclr = CreditLimitFactory.newCounterpartyCreditLimitRule();
            CounterpartyCreditLimitRule registeredTestClr = ( CounterpartyCreditLimitRule ) uow.registerObject( testCclr );
            registeredTestClr.setShortName( "Test_" + System.nanoTime() );

            PFEConfiguration pfe = CreditLimitFactory.newPFEConfiguration();
            PFEConfiguration regConf = ( PFEConfiguration ) uow.registerObject( pfe );
            String pfeShortName = "PFE" + System.nanoTime();
            regConf.setShortName( pfeShortName );
            regConf.setLongName( "PFETestProfileLongName" );
            regConf.setDescription( "PFE ProfileTest Desc" );

            PFEConfigurationProfile pfeProfile = CreditLimitFactory.newPFEConfigurationProfile();
            PFEConfigurationProfile regPfeProfile = ( PFEConfigurationProfile ) uow.registerObject( pfeProfile );
            regPfeProfile.setStatus( 'T' );

            //Setting Owners
            regPfeProfile.setOwner( regConf );

            regPfeProfile.setSortOrder( 4 );

            CurrencyPairGroup g7 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName(
                    "G7", CurrencyPairGroup.class, null, null );

            CurrencyPairGroup ccyPairGrp = ( CurrencyPairGroup ) uow.registerObject( g7 );
            regPfeProfile.setCurrencyPairGroup( ccyPairGrp );

            // Credit Tenor Profile
            CreditTenorProfile creditTenorProfile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regCreditTenorProfile = ( CreditTenorProfile ) uow.registerObject( creditTenorProfile );
            regCreditTenorProfile.setStatus( 'T' );
            String ctpShortName = "CTP" + System.nanoTime();
            regCreditTenorProfile.setShortName( ctpShortName );
            regCreditTenorProfile.setLongName( "TestLongName" );
            regCreditTenorProfile.setDescription( "testDesc" );


            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regCreditTenorProfile.getCreditTenorParameters().add( regCnp1 );
            regCreditTenorProfile.getCreditTenorParameters().add( regCnp2 );
            Organization creditProviderOrg = ReferenceDataCacheC.getInstance().getOrganization( "CITI" );
            CreditLimitOrgFunction orgFunc = creditProviderOrg.getCreditLimitOrgFunction();

            if ( orgFunc != null )
            {
                CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) uow.registerObject( orgFunc );
                regCreditTenorProfile.setOwner( registeredOrgFunc );
            }

            regPfeProfile.setCreditTenorProfile( regCreditTenorProfile );
            regConf.getPfeConfigurationProfiles().add( regPfeProfile );
            registeredTestClr.setPfeConfiguration( regConf );
            uow.commit();

            // refresh the rule set.
            testCclr.setPfeConfiguration( null );
            testCclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject( testCclr );
            assertNotNull( testCclr.getPfeConfiguration() );
        }
        catch ( Exception e )
        {
            fail( "Exception in testSetPFEConfiguration : ", e );
        }
    }

    public void testNestedLocking()
    {
        try
        {
            CounterpartyCreditLimitRule testCclr = CreditLimitFactory.newCounterpartyCreditLimitRule();
            Lock lock = testCclr.getLock();
            int lockHashCode = lock.hashCode();
            log.info( "Lock =" + lock + ",lockHashCode=" + lockHashCode );
            lock.lock();
            testCclr.getLock().lock();
            System.out.println( "################ successful" );
            testCclr.getLock().unlock();
            lock.unlock();
        }
        catch ( Exception e )
        {
            fail( "testNestedLocking", e );
        }
    }

    public void testSettlementCode()
    {
        try
        {
            Collection<CounterpartyCreditLimitRule> cptyRules = getPersistenceSession().readAllObjects(CounterpartyCreditLimitRuleC.class);
            for ( CounterpartyCreditLimitRule cclr: cptyRules )
            {
                if ( cclr.isActive() || cclr.isPassive() )
                {
                    if ( cclr.getTradingPartyOrganization() == null )
                    {
                        continue;
                    }
                    String settlementCode = cclr.getSettlementCode();
                    if ( cclr.getTradingParty() != null )
                    {
                        assertEquals(settlementCode, IdcUtilC.getExternalSystemValue( cclr.getTradingParty(), TradeServiceConstants.SETTLEMENT_CODE));
                    }
                    else
                    {
                        Organization cpo = ((CreditLimitRuleSet)cclr.getRuleSet()).getOrganization();
                        Organization cptyOrg = cclr.getTradingPartyOrganization();
                        final LegalEntity cptyOrgDefLE = cptyOrg.getDefaultDealingEntity();
                        if ( cptyOrgDefLE == null )
                        {
                            continue;
                        }
                        TradingParty tp = cptyOrgDefLE.getTradingParty(cpo);
                        assertEquals(settlementCode, IdcUtilC.getExternalSystemValue( tp, TradeServiceConstants.SETTLEMENT_CODE));
                    }
                }
            }

        }
        catch (Exception e)
        {
            fail("testSettlementCode", e);
        }
    }

    public void testLEI()
    {
        try
        {
            Collection<CounterpartyCreditLimitRule> cptyRules = getPersistenceSession().readAllObjects( CounterpartyCreditLimitRuleC.class );
            for ( CounterpartyCreditLimitRule cclr: cptyRules )
            {
                if ( cclr.isActive() )
                {
                    if ( cclr.getTradingPartyOrganization() == null  )
                    {
                        continue;
                    }

                    if ( (cclr.isOrgLevel() && cclr.getTradingParty() != null ) || (!cclr.isOrgLevel() && cclr.getTradingParty() == null) )
                    {
                        continue;
                    }

                    String lei = cclr.getLEI();
                    if ( cclr.getTradingParty() != null )
                    {
                        assertEquals(cclr.getTradingParty().getLEI(), lei);
                    }
                    else
                    {
                        assertEquals(cclr.getTradingPartyOrganization().getLEI(), lei);
                    }
                }
            }
        }
        catch (Exception e)
        {
            fail("testLEI", e);
        }
    }

}
