##################################################################################
#																   
# Messages.properties: Message Resouce Bundle
#																   
# This files defines key/message templates used for exception messages and other
# locale specific messages for exceptions in the current package.
#
# For more details about resource bundle property, please refer to JDK 
# documentations.											   
#																   
# The key of the default exception message is the fully scoped exception				   
# class name. Exception classes have to be inherited from IdcException or 			   
# IdcRuntimeException. 												   
#																   
# For example:													   
#																   	
#	  com.integral.system.ExceptionMessageLogTest.TestFileException:\					   
#	  File {0} created at {1} by {2} is not found.								   
#																   
# The package and class names are case-sensitive and used by exception				   
# subsystem for message lookup. 										   
#
# IdcRuntimeException and its subclasses can have multiple message templates.			   
# The key of the message template can be any string. User specifies the string			   
# key while creaing a IdcRuntimeException. 			
#																   
##################################################################################

com.integral.finance.creditLimit.test.CreditLimitExceptionTestC.java
