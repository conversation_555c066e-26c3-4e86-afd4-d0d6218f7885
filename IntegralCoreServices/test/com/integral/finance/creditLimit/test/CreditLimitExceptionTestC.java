package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditLimitException;
import com.integral.finance.creditLimit.CreditUtilizationEventC;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;

/**
 * Test for credit limit exception.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitExceptionTestC
        extends TestCase
{
    static String name = "CreditLimitExceptionTest";

    public CreditLimitExceptionTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new CreditLimitExceptionTestC( "event_exception" ) );
        suite.addTest( new CreditLimitExceptionTestC( "creditutilevent_exception" ) );
        return suite;
    }

    public void event_exception()
    {
        Exception e = new CreditLimitException( CreditLimit.ERROR_INVALID_EVENT, "TEST" );
        ErrorMessage emsg = new ErrorMessageC();
        emsg.setException( e );
        try
        {
            System.out.println( emsg.getUserMessage() );
        }
        catch ( Exception ee )
        {
            fail( "event_exception" );
        }
    }

    public void creditutilevent_exception()
    {
        Exception e = new CreditLimitException( CreditLimit.ERROR_INVALID_AMOUNT_TYPE,
                new CreditUtilizationEventC() );
        ErrorMessage emsg = new ErrorMessageC();
        emsg.setException( e );
        try
        {
            System.out.println( emsg.getUserMessage() );
        }
        catch ( Exception ee )
        {
            fail( "creditutilevent_exception" );
        }
    }

}
