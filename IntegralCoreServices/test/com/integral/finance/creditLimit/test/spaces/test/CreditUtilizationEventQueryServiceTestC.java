package com.integral.finance.creditLimit.test.spaces.test;

import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.creditLimit.CurrencyPositionCollection;
import com.integral.finance.creditLimit.spaces.CreditUtilizationEventQueryService;
import com.integral.test.DealingPTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.util.Iterator;
import java.util.Vector;

public class CreditUtilizationEventQueryServiceTestC extends DealingPTestCaseC
{

    public CreditUtilizationEventQueryServiceTestC( String aName )
    {
        super( aName );
    }

    public void setUp() throws Exception
    {
        super.setUp();
    }

    public void testBuildCurrencyPosition()
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "creditLimitRule" ).get( "ruleParent" ).get( "tradingPartyOrganization" ).get( "shortName" ).equal( "XLP" );

        Vector objs = getPersistenceSession().readAllObjects( CreditUtilization.class, expr );
        for ( int i = 0; i < objs.size(); i++ )
        {
            CreditUtilization cclr = ( CreditUtilization ) objs.elementAt( i );
            log( "CreditUtilization = " + cclr );
            Iterator<CreditUtilizationEvent> cues = CreditUtilizationEventQueryService.getSpaceCreditUtilizationEvents( cclr );
            assertNotNull( cues );

            IdcDate baseDate = DateTimeFactory.newDate().addDays( -3 );
            try
            {
                CurrencyPositionCollection cpc = CreditUtilizationEventQueryService.buildCurrencyPositions( cclr, baseDate, false, true, false, true, true, true, null );
            }
            catch ( Exception ex )
            {
                fail();
            }

        }
    }
}
