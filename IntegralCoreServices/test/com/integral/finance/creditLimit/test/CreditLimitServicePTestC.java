package com.integral.finance.creditLimit.test;

import com.integral.audit.AuditEvent;
import com.integral.facade.FacadeFactory;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.calculator.CalculatorFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.admin.CreditLimitAdminService;
import com.integral.finance.creditLimit.admin.CreditLimitAdminServiceFactory;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationMBean;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditEventFacade;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditEventFacadeC;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationMBean;
import com.integral.finance.creditLimit.enums.StopOutState;
import com.integral.finance.creditLimit.quickcheck.CreditLineManagerC;
import com.integral.finance.creditLimit.spaces.CreditUtilizationEventQueryService;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.facade.RequestStateFacade;
import com.integral.finance.dealing.facade.RequestStateFacadeC;
import com.integral.finance.fx.*;
import com.integral.finance.marketData.MarketDataSetC;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataSetC;
import com.integral.finance.marketData.fx.FXMarketDataSetUpdateHandlerC;
import com.integral.finance.marketData.fx.calculator.ExternalMarketDataExtractionCalculator;
import com.integral.finance.marketData.fx.calculator.ExternalMarketDataExtractionCalculatorDelegator;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeClassificationC;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.messaging.config.MessagingConfiguration;
import com.integral.persistence.*;
import com.integral.rule.Rule;
import com.integral.rule.RuleSet;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.DealingPTestCaseC;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.util.IdcUtilC;
import com.integral.workflow.StateC;
import com.integral.workflow.dealing.DealingLimit;
import com.integral.workflow.dealing.DealingLimitCollection;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import com.mongodb.CommandResult;
import com.mongodb.DB;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.*;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

/**
 * Tests the credit limit service
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitServicePTestC
        extends DealingPTestCaseC implements CreditLimitConstants
{
    static String name = "Credit Limit Service Test";
    protected static CreditWorkflowManagerC creditMgr = new CreditWorkflowManagerC();
    protected CreditConfigurationMBean configMBean = CreditConfigurationFactory.getCreditConfigurationMBean();
    protected static CreditLimitAdminService creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService();
    protected static double CREDIT_CALCULATION_MINIMUM = 2.0;
    protected static CreditUtilizationEventQueryService cueq = new CreditUtilizationEventQueryService();
    protected static MessagingConfiguration configuration = MessagingConfiguration
            .getInstance();

    public CreditLimitServicePTestC( String name )
    {
        super( name );
        initializeData ();
    }

    protected static void initializeData()
    {
        try
        {
            FacadeFactory.setFacade ( CreditLimitAdminAuditEventFacade.FACADE_NAME, AuditEvent.class, CreditLimitAdminAuditEventFacadeC.class );
            tradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();

            // initialise the credit workflow manager.
            creditMgr.setSendCreditUtilizationUpdates ( false );

            // init the FI and LP relationship
            initFILPCreditRelationship ();

            // initialize credit relationship between PB
            initPrimeBrokerCreditRelationship ();

            initMarketData ();
            initCurrencyPairRates ( false );
            noFXRateCurrencyPairs.clear ();
            initNoFXRateCurrencyPairs ();
        }
        catch ( Exception e )
        {
            log.error ( "CLSP.initializeData - Exception while initializing the data.", e );
        }
    }

    protected void initialize( User user )
    {
        try
        {
            init( user );
            FacadeFactory.setFacade( RequestStateFacade.REQUEST_STATE_FACADE, Request.class, RequestStateFacadeC.class );
            FacadeFactory.setFacade( CreditLimitWorkflowStateFacadeC.class.getName(), CreditLimitWorkflowState.class, CreditLimitWorkflowStateFacadeC.class );
            FacadeFactory.setFacade( CreditLimitAdminAuditEventFacade.FACADE_NAME, AuditEvent.class, CreditLimitAdminAuditEventFacadeC.class );
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
        }
        catch ( Exception e )
        {
            log.error( "CreditLimitServicePTestC.init.ERROR : Error initializing the credit test.", e );
        }
    }

    protected void setUp() throws Exception
    {
        super.setUp();
        CreditLineManagerC.getInstance().disableReloadTasks( true );
        CreditLineManagerC.getInstance().removeAll();

        FacadeFactory.setFacade( CreditLimitWorkflowStateFacadeC.class.getName(), CreditLimitWorkflowState.class, CreditLimitWorkflowStateFacadeC.class );
        FacadeFactory.setFacade(CreditLimitAdminAuditEventFacade.FACADE_NAME, AuditEvent.class, CreditLimitAdminAuditEventFacadeC.class);
        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().clearAllSubscriptionData();
        WatchPropertyC.update(CreditLimitConfigurationMBean.CREDIT_METASPACE_QUERY_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE);
        WatchPropertyC.update(CreditConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_PERIOD, "10", ConfigurationProperty.DYNAMIC_SCOPE);
        WatchPropertyC.update( CreditAdminConfigurationMBean.CREDIT_REMOTE_NOTIFCATION_PROCESSING_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
    }

    protected void tearDown()
    {
        log( "Special sleep after every test" );
        sleepFor( 2000 );
        CreditUtilizationManagerC.getInstance().getCreditUtilizationPersistenceTask().clear();
        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
        CreditLineManagerC.getInstance().disableReloadTasks( true );
        CreditLineManagerC.getInstance().removeAll();
        super.tearDown();
    }

    protected static void initMarketData()
    {
        try
        {
            // initialize the market data set if not updated.
            if ( staticMds.getBaseDate() == null || staticMds.getBaseDate().isEarlierThan( tradeDate ) || staticMds.getReadingsName() == null )
            {
                // register calculator for market data extraction.
                init( lpUser );
                CalculatorFactory.putCalculator( ExternalMarketDataExtractionCalculator.CALCULATOR_NAME, FXMarketDataSet.class, ExternalMarketDataExtractionCalculatorDelegator.class );
                new FXMarketDataSetUpdateHandlerC().handle( MessageFactory.newWorkflowMessage() );
                Session session = PersistenceFactory.newSession();
                UnitOfWork uow = session.acquireUnitOfWork();
                uow.removeReadOnlyClass( FXMarketDataSetC.class );
                FXMarketDataSet regMds = ( FXMarketDataSet ) uow.registerObject( staticMds );
                regMds.setReadingsName( "Test" );
                uow.commit();
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    protected static IdcTransaction initTransaction( boolean isCreditWorkflow ) throws Exception
    {
        initTransaction();
        IdcTransaction tx = IdcSessionManager.getInstance().getTransaction();
        // add legal entity as read only class
        tx.getUOW().addReadOnlyClass( OrganizationC.class );
        tx.getUOW().addReadOnlyClass( LegalEntityC.class );
        tx.getUOW().addReadOnlyClass( CreditUtilizationCalculatorC.class );
        tx.getUOW().addReadOnlyClass( CreditLimitClassificationC.class );
        tx.getUOW().addReadOnlyClass( UserC.class );
        tx.getUOW().addReadOnlyClass( StateC.class );
        tx.getUOW().addReadOnlyClass( CreditLimitWorkflowStateC.class );
        tx.getUOW().addReadOnlyClass( FXRateConventionC.class );
        tx.getUOW().addReadOnlyClass( FXRateBasisC.class );
        tx.getUOW().addReadOnlyClass( FXSettlementDateRuleC.class );
        tx.getUOW().addReadOnlyClass( TradeClassificationC.class );
        tx.getUOW().addReadOnlyClass( TradingPartyC.class );
        if ( isCreditWorkflow )
        {
            tx.getUOW().addReadOnlyClass( CreditLimitRuleSetC.class );
            tx.getUOW().addReadOnlyClass( MarketDataSetC.class );
            tx.getUOW().addReadOnlyClass( CreditLimitRuleC.class );
            tx.getUOW().addReadOnlyClass( CounterpartyCreditLimitRuleC.class );
            tx.getUOW().addReadOnlyClass( NamespaceC.class );
            tx.getUOW().addReadOnlyClass( NamespaceGroupC.class );
        }
        tx.getUOW().addReadOnlyClass( CreditUtilizationC.class );
        tx.getUOW().addReadOnlyClass( CurrencyC.class );
        return tx;
    }

    protected static boolean isInTransaction()
    {
        return IdcSessionManager.getInstance().getTransaction() != null;
    }

    protected double getAvailableCreditLimit( TradingParty tp, IdcDate valueDate )
    {
        LegalEntity creditProviderLe = CreditUtilC.getDefaultlegalEntity( tp.getOrganization() );
        Double limit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit(creditProviderLe, tp, tp.getLegalEntityOrganization(), valueDate);
        return limit != null ? limit : 0.0;
    }

    protected double getTotalDailyCreditLimit( TradingParty tp, IdcDate valueDate )
    {
        LegalEntity creditProviderLe = CreditUtilC.getDefaultlegalEntity( tp.getOrganization() );
        Collection<CreditUtilization> col = CreditUtilizationManagerC.getInstance().getCreditUtilizations( creditProviderLe, tp.getLegalEntityOrganization(), tp, valueDate, CreditLimitConstants.TRADE_EVENT );
        for ( CreditUtilization cu : col )
        {
            if ( cu.getCreditLimitRule() instanceof DailyCreditLimitRule )
            {
                return cu.getLimit();
            }
        }
        return 0.0;
    }


    protected double getTotalCreditLimit( Organization creditProviderOrg, TradingParty creditCpty )
    {
        CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditCpty);
        Collection<Rule> rules = cclr.getChildrenRules();
        double limitAmt = 0.0;
        boolean isInitial = true;
        for ( Rule rule : rules )
        {
            CreditLimitRule clr = ( CreditLimitRule ) rule;
            Collection<CreditUtilization> creditUtils = clr.getCreditUtilizations();
            for ( CreditUtilization cu : creditUtils )
            {
                double cuLimit = cu.getLimit();
                if ( isInitial )
                {
                    limitAmt = cuLimit;
                    isInitial = false;
                }
                else if ( cuLimit < limitAmt )
                {
                    limitAmt = cuLimit;
                }
            }
        }
        return limitAmt;
    }

    protected double getAvailableCreditLimit( TradingParty tp, IdcDate valueDate, CreditLimitClassification clsf )
    {
        LegalEntity creditProviderLe = CreditUtilC.getDefaultlegalEntity( tp.getOrganization() );
        Collection<CreditUtilization> creditUtils = CreditUtilizationManagerC.getInstance().getCreditUtilizations(creditProviderLe, tp.getLegalEntityOrganization(), tp, valueDate, CreditLimitConstants.RATE_QUALIFICATION_EVENT);
        if ( creditUtils != null )
        {
            for ( CreditUtilization cu : creditUtils )
            {
                CreditLimitRule clr = cu.getCreditLimitRule();
                if ( clr.isEnabled() && clsf.isSameAs( clr.getClassification() ) )
                {
                    CreditUtilizationCalculator cuc = clr.getCreditUtilizationCalculator();
                    return cuc != null ? cuc.getRealtimeAvailableAmount( cu ) : cu.getAvailableMarginReserveAmount();
                }
            }
        }
        return 0.0;
    }

    protected void resetNotificationDate( Organization org )
    {
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( org );
            if ( clrs != null )
            {
                Session session = PersistenceFactory.newSession();
                UnitOfWork uow = session.acquireUnitOfWork();
                CreditLimitRuleSet regClrs = ( CreditLimitRuleSet ) uow.registerObject( clrs );
                regClrs.setNotificationDate( null );
                regClrs.setWarningDate( null );
                regClrs.setSuspensionDate( null );

                Collection<CounterpartyCreditLimitRule> cclrs = ( Collection<CounterpartyCreditLimitRule> ) regClrs.getRules();
                for ( CounterpartyCreditLimitRule cclr : cclrs )
                {
                    CounterpartyCreditLimitRule regCclr = ( CounterpartyCreditLimitRule ) uow.registerObject( cclr );
                    regCclr.setNotificationDate( null );
                    regCclr.setWarningDate( null );
                    regCclr.setSuspensionDate( null );
                    regCclr.setStopOutState ( StopOutState.INITIAL );
                    regCclr.setStopOutDate ( null );
                }

                uow.commit();
            }
        }
        catch ( Exception e )
        {
            fail( "resetNoticationDate", e );
        }
    }

    protected CreditUtilization getAggregateCreditUtilization( LegalEntity creditProviderLe, TradingParty creditCpty, IdcDate settlementDate )
    {
        Collection col = CreditUtilizationManagerC.getInstance().getCreditUtilizations( creditProviderLe, creditCpty.getLegalEntityOrganization(), creditCpty, settlementDate, CreditLimitConstants.SUBSCRIBE_EVENT );
        Iterator iter = col.iterator();
        CreditUtilization cu = null;
        while ( iter.hasNext() )
        {
            cu = ( CreditUtilization ) iter.next();
            if ( cu.getCreditLimitRule() instanceof SingleCreditLimitRule )
            {
                break;
            }
        }
        return cu;
    }

    protected CreditUtilization getDailyCreditUtilization( LegalEntity creditProviderLe, TradingParty creditCpty, IdcDate settlementDate )
    {
        Collection col = CreditUtilizationManagerC.getInstance().getCreditUtilizations( creditProviderLe, creditCpty.getLegalEntityOrganization(), creditCpty, settlementDate, CreditLimitConstants.SUBSCRIBE_EVENT );
        Iterator iter = col.iterator();
        CreditUtilization cu = null;
        while ( iter.hasNext() )
        {
            cu = ( CreditUtilization ) iter.next();
            if ( cu.getCreditLimitRule() instanceof DailyCreditLimitRule )
            {
                break;
            }
        }
        return cu;
    }

    // TODO : Enhance to remove entries from collection for given criteria.
    protected void dropCreditCollectionFromSpaces()
    {
        log.info( "Dropping CREDIT collection from spaces " );
        DB db = mongo.getDB( CreditLimit.CREDIT_SPACE );
        db.dropDatabase();
        CommandResult cr = db.getLastError();
    }

    protected void removeExistingCreditUtilizationEvents( Organization org )
    {
        try
        {
            log.info( "Removing  CUEs for org " + org );
            CreditUtilizationManagerC.getInstance().getCreditUtilizationPersistenceTask().clear();
            if ( CreditUtilC.getCreditLimitRuleSet( org ) == null )
            {
                return;
            }
            long t0 = System.currentTimeMillis();
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( CreditLimitRuleSetC.class );
            uow.removeReadOnlyClass( CreditUtilizationC.class );
            uow.removeReadOnlyClass( CreditUtilizationEventC.class );
            uow.removeReadOnlyClass( CreditLimitRuleC.class );

            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( CreditUtilizationEvent.class );
            raq.setSelectionCriteria( getCreditUtilizationEventsExpression( CreditUtilC.getCreditLimitRuleSet( org ) ) );
            Collection<CreditUtilizationEvent> col = ( Collection<CreditUtilizationEvent> ) session.executeQuery( raq );
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( col.size() );
            for ( CreditUtilizationEvent cue : col )
            {
                if ( cue.getCreditUtilization() != null )
                {
                    creditUtils.add( cue.getCreditUtilization() );
                }
                CreditUtilizationEvent registeredCue = ( CreditUtilizationEvent ) uow.registerObject( cue );
                registeredCue.setCreditUtilization( null );
                uow.deleteObject( registeredCue );
            }
            for ( CreditUtilization cu : creditUtils )
            {
                log.info( "Resestting " + cu.getObjectId() );
                cu = ( CreditUtilization ) IdcUtilC.refreshObject( cu );
                cu.resetCurrencyPositions( "Test", false );
                cu.setPositions( "TEST" );
                cu.markPositionsDirty();
                cu.markNextDatePositionsDirty ();
                CreditUtilization registeredCu = ( CreditUtilization ) uow.registerObject( cu );
                registeredCu.setReservedAmount( 0.0 );
                registeredCu.setUsedAmount( 0.0 );
                registeredCu.resetCurrencyPositions( "Test", false );
                registeredCu.setPositions( null );
                CreditLimitRule clr = registeredCu.getCreditLimitRule();
                CreditLimitRule regClr = ( CreditLimitRule ) uow.registerObject( clr );
                regClr.update();
            }

            uow.commit();

            Iterator cuRefreshIter = creditUtils.iterator();
            while ( cuRefreshIter.hasNext() )
            {
                CreditUtilization cu = ( CreditUtilization ) cuRefreshIter.next();
                cu = ( CreditUtilization ) getPersistenceSession().refreshObject( cu );
                cu.resetCurrencyPositions( "Test", false );
                Collection events = CreditUtilC.getCreditUtilizationEvents( cu );
                if ( events != null && !events.isEmpty() )
                {
                    log( "***************STILL EVENTS!!!!" );
                }
            }

            // check whether any credit utilizations are there with non-zero reserve or used amount
            UnitOfWork uow1 = session.acquireUnitOfWork();
            uow1.addReadOnlyClass( CreditLimitRuleC.class );
            uow1.addReadOnlyClass( DailyCreditLimitRuleC.class );
            uow1.addReadOnlyClass( SingleCreditLimitRuleC.class );
            uow1.addReadOnlyClass( CreditLimitRuleSetC.class );
            uow1.removeReadOnlyClass( CreditUtilizationC.class );
            uow1.removeReadOnlyClass( CreditUtilizationEventC.class );

            ReadAllQuery raq1 = new ReadAllQuery();
            raq1.setReferenceClass( CreditUtilization.class );
            raq1.setSelectionCriteria( getAllCreditUtilizationExpression( CreditUtilC.getCreditLimitRuleSet( org ) ) );
            Collection<CreditUtilization> col1 = ( Collection<CreditUtilization> ) session.executeQuery( raq1 );
            if ( col1 != null && !col1.isEmpty() )
            {
                for ( CreditUtilization cu : col1 )
                {
                    if ( cu.getCurrencyPositions( false ) == null && cu.getPositions() == null && cu.getUsedAmount() == 0.0 && cu.getReservedAmount() == 0.0 )
                    {
                        continue;
                    }
                    cu.setPositions( "TEST" );
                    cu.markPositionsDirty();
                    cu.markNextDatePositionsDirty ();
                    cu.resetCurrencyPositions( "Test", false );
                    cu = ( CreditUtilization ) IdcUtilC.refreshObject( cu );
                    cu.markPositionsDirty();
                    cu.markNextDatePositionsDirty ();
                    CreditUtilization regCu = ( CreditUtilization ) uow1.registerObject( cu );
                    regCu.setUsedAmount( 0.0 );
                    regCu.setReservedAmount( 0.0 );
                    regCu.setLimitAmount( null );
                    regCu.setPositions( null );
                    regCu.resetCurrencyPositions( "Test", false );
                }
            }

            uow1.commit();

            if ( ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() && CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditMetaSpaceQueryEnabled() )
            {
                resetCreditUtilizationForSpaces( org );
            }

            long t1 = System.currentTimeMillis();
            log( "CreditLimitServicePTestC.removeExistingCreditUtilizationEvents took : " + ( t1 - t0 ) );
        }
        catch ( Exception e )
        {
            log.error( "CreditLimitServicePTestC.removeExistingCreditUtilizationEvents", e );
        }
    }

    protected void resetCreditUtilizationForSpaces( Organization org ) throws Exception
    {
        Iterator<CreditUtilizationEvent> eventItr = cueq.getCreditUtilizationEvents( org.getNamespace() );

        if ( eventItr.hasNext() )
        {
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( CreditLimitRuleC.class );
            uow.addReadOnlyClass( DailyCreditLimitRuleC.class );
            uow.addReadOnlyClass( SingleCreditLimitRuleC.class );
            uow.addReadOnlyClass( CreditLimitRuleSetC.class );

            while ( eventItr.hasNext() )
            {
                CreditUtilization cu = eventItr.next().getCreditUtilization();
                log.info( "Resestting " + cu.getObjectId() );
                cu.resetCurrencyPositions( "Test", false );
                cu.setPositions( " " );
                CreditUtilization registeredCu = ( CreditUtilization ) uow.registerObject( cu );
                registeredCu.setReservedAmount( 0.0 );
                registeredCu.setUsedAmount( 0.0 );
                registeredCu.resetCurrencyPositions( "Test", false );
                registeredCu.setPositions( null );
            }

            uow.commit();
        }
    }

    protected double getDailyAvailableCreditLimit( TradingParty tp, IdcDate valueDate )
    {
        LegalEntity creditProviderLe = CreditUtilC.getDefaultlegalEntity( tp.getOrganization() );
        Collection col = CreditUtilizationManagerC.getInstance().getCreditUtilizations( creditProviderLe, tp.getLegalEntityOrganization(), tp, valueDate, CreditLimitConstants.RATE_QUALIFICATION_EVENT );
        Iterator iter = col.iterator();
        while ( iter.hasNext() )
        {
            CreditUtilization cu = ( CreditUtilization ) iter.next();
            if ( cu.getCreditLimitRule() instanceof DailyCreditLimitRule )
            {
                return cu.getAvailableMarginReserveAmount();
            }
        }
        return 0.0;
    }

    protected double getRealtimeDailyUtilizedAmount( TradingParty tp, IdcDate valueDate )
    {
        LegalEntity creditProviderLe = CreditUtilC.getDefaultlegalEntity( tp.getOrganization() );
        Collection<CreditUtilization> col = CreditUtilizationManagerC.getInstance().getCreditUtilizations( creditProviderLe, tp.getLegalEntityOrganization(), tp, valueDate, CreditLimitConstants.SUBSCRIBE_EVENT );
        for ( CreditUtilization cu : col )
        {
            if ( cu.getCreditLimitRule() instanceof DailyCreditLimitRule )
            {
                return cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( cu );
            }
        }
        return 0.0;
    }

    protected double getRealtimeAggregateUtilizedAmount( TradingParty tp, IdcDate valueDate )
    {
        LegalEntity creditProviderLe = CreditUtilC.getDefaultlegalEntity( tp.getOrganization() );
        Collection<CreditUtilization> col = CreditUtilizationManagerC.getInstance().getCreditUtilizations( creditProviderLe, tp.getLegalEntityOrganization(), tp, valueDate, CreditLimitConstants.TRADE_EVENT );
        for ( CreditUtilization cu : col )
        {
            if ( cu.getCreditLimitRule() instanceof SingleCreditLimitRule )
            {
                return cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( cu );
            }
        }
        return 0.0;
    }

    protected double getGrossNotionalAvailableCreditLimit( TradingParty tp, IdcDate valueDate )
    {
        LegalEntity creditProviderLe = CreditUtilC.getDefaultlegalEntity( tp.getOrganization() );
        Collection col = CreditUtilizationManagerC.getInstance().getCreditUtilizations( creditProviderLe, tp.getLegalEntityOrganization(), tp, valueDate, CreditLimitConstants.SUBSCRIBE_EVENT );
        Iterator iter = col.iterator();
        while ( iter.hasNext() )
        {
            CreditUtilization cu = ( CreditUtilization ) iter.next();
            if ( cu.getCreditLimitRule() instanceof SingleCreditLimitRule )
            {
                return cu.getAvailableMarginReserveAmount();
            }
        }
        return 0.0;
    }

    protected void validateCreditUtilizationEvents( CreditWorkflowMessage cwm )
    {
        Collection cues = cwm.getCreditUtilizationEvents();
        Iterator iter = cues.iterator();
        Map<IdcDate, Boolean> cptyRuleAttach = new HashMap<IdcDate, Boolean>( 2 );
        while ( iter.hasNext() )
        {
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) iter.next();
            IdcDate posDate = cue.getSettlementDate();
            assertEquals( "validateCreditUtilizationEvents. cue namespace : " + cue.getNamespace(), cue.getNamespace(), cwm.getOrganization().getNamespace() );
            if ( cwm.getStatus().equals( MessageStatus.SUCCESS ) )
            {
                if ( cwm.getEvent().equals( CreditMessageEvent.RESERVE ) || cwm.getEventName().equals( CreditMessageEvent.USE.getName() ) )
                {
                    assertEquals( "validateCreditUtilizationEvents. cue status : " + cue.getStatus(), cue.getStatus(), NamedEntity.ACTIVE_STATUS );
                    char lastAction = cwm.getEvent().equals( CreditMessageEvent.RESERVE ) ? CreditLimit.ACTION_EARMARK : CreditLimit.ACTION_APPLY;
                    assertEquals( "validateCreditUtilizationEvents. cue lastAction : " + cue.getLastAction(), cue.getLastAction(), new Character( lastAction ) );
                }
                else if ( cwm.getEventName().equals( CreditMessageEvent.REMOVE.getName() ) )
                {
                    assertEquals( "validateCreditUtilizationEvents. cue status : " + cue.getStatus(), cue.getStatus(), NamedEntity.PASSIVE_STATUS );
                    assertEquals( "validateCreditUtilizationEvents. cue lastAction : " + cue.getLastAction(), cue.getLastAction(), new Character( CreditLimit.ACTION_REMOVE ) );
                }

                // check the credit utilization events have the trading party organization and counterparty credit limit rule populated.
                assertEquals( "cue. tpOrg=" + cue.getTradingPartyOrganization(), cue.getTradingPartyOrganization() != null, true );
                if ( cue.getCounterpartyCreditLimitRule() != null )
                {
                    if ( cptyRuleAttach.get( posDate ) != null )
                    {
                        fail( "counterparty rule attached mulitple times." );
                    }
                    cptyRuleAttach.put( posDate, Boolean.TRUE );
                }
            }
            else
            {
                assertEquals( "validateCreditUtilizationEvents. cue reserved amt : " + cue.getReservedAmount(), cue.getReservedAmount() < MINIMUM, true );
                assertEquals( "validateCreditUtilizationEvents. cue used amt : " + cue.getUsedAmount(), Math.abs( cue.getUsedAmount() ) < MINIMUM, true );
                assertEquals( "validateCreditUtilizationEvents. cue namespace : " + cue.getNamespace(), cue.getNamespace(), cwm.getOrganization().getNamespace() );
                if ( cwm.getEvent().equals( CreditMessageEvent.RESERVE ) || cwm.getEventName().equals( CreditMessageEvent.USE.getName() ) )
                {
                    assertEquals( "validateCreditUtilizationEvents. cue status : " + cue.getStatus(), cue.getStatus(), NamedEntity.PASSIVE_STATUS );
                    assertEquals( "validateCreditUtilizationEvents. cue lastAction : " + cue.getLastAction(), cue.getLastAction().equals( new Character( CreditLimit.ACTION_APPLY ) ), false );
                }
            }
        }
        if ( cwm.getStatus().equals( MessageStatus.SUCCESS ) && cptyRuleAttach.isEmpty() )
        {
            fail( "No counterparty credit limit rule attached." );
        }
    }

    protected void checkCreditUtilizationPersistence( CreditUtilizationEvent cue )
    {
        if ( cue.getCreditUtilization() != null && IdcSessionManager.getInstance().getTransaction() == null )
        {
            CreditUtilization cu = cue.getCreditUtilization();
            double usedAmt = cu.getUsedAmount();
            double reserveAmt = cu.getReservedAmount();
            try
            {
                CreditUtilization refreshedCu = ( CreditUtilization ) PersistenceFactory.newSession().refreshObject( cu );
                assertEquals( "Check persistence of cu used amount=" + usedAmt + ",refreshCuAmt=" + refreshedCu.getUsedAmount(), Math.abs( refreshedCu.getUsedAmount() - usedAmt ) < MINIMUM, true );
                assertEquals( "Check persistence of cu reserved amount=" + reserveAmt + ",refreshCuAmt=" + refreshedCu.getReservedAmount(), Math.abs( refreshedCu.getReservedAmount() - reserveAmt ) < MINIMUM, true );
            }
            catch ( Exception e )
            {
                e.printStackTrace();
            }
        }
    }

    protected Expression getCreditUtilizationEventsExpression( RuleSet clrs )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        return eb.get( Entity.Namespace ).equal( clrs.getNamespace() );
    }

    protected Expression getUtilizedCreditUtilizationExpression( CreditLimitRuleSet clrs )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression clrExpr = eb.get( "creditLimitRule" ).get( "ruleParent" ).get( "ruleSet" ).equal( clrs );
        Expression usedExpr = eb.get( "usedAmount" ).notEqual( 0 ).or( eb.get( "reservedAmount" ).notEqual( 0 ) );
        Expression overriddenLimitExpr = eb.get( "limitAmount" ).notNull();
        return clrExpr.and( usedExpr ).or( overriddenLimitExpr );
    }

    protected Expression getAllCreditUtilizationExpression( CreditLimitRuleSet clrs )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression clrExpr = eb.get( "creditLimitRule" ).get( "ruleParent" ).get( "ruleSet" ).equal( clrs );
        return clrExpr;
    }

    protected void printCurrencyPositions( CreditWorkflowMessage cwm )
    {
        Collection cues = cwm.getCreditUtilizationEvents();
        Iterator iter = cues.iterator();
        while ( iter.hasNext() )
        {
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) iter.next();
            if ( cue.getCreditUtilization() != null )
            {
                log( "Cu=" + cue.getCreditUtilization() + ",ccyPos=" + cue.getCreditUtilization().getCurrencyPositions() );
            }
        }
    }

    protected void setNotificationSettings( Organization creditProviderOrg, TradingParty creditCpty, String email, boolean isOrgLevel, Double notification, Double warning, Double suspension )
    {
        setNotificationSettings( creditProviderOrg, creditCpty, email, isOrgLevel, notification, warning, suspension, null, false );
    }

    protected void setNotificationSettings( Organization creditProviderOrg, TradingParty creditCpty, String email, boolean isOrgLevel, Double notification, Double warning, Double suspension, Double stopOut, boolean stopOutEnabled )
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) uow.registerObject( clrs );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) uow.registerObject( cclr );
            registeredCclr.setNotificationDate( null );
            registeredCclr.setWarningDate( null );
            registeredCclr.setSuspensionDate( null );
            registeredCclr.setStopOutDate ( null );
            if ( isOrgLevel )
            {
                registeredClrs.setNotificationPercentage( notification );
                registeredClrs.setWarningPercentage( warning );
                registeredClrs.setSuspensionPercentage( suspension );
                registeredClrs.setNotificationEmailAddress( email );
                if ( stopOutEnabled )
                {
                    registeredCclr.setStopOutPercentage ( stopOut );
                }
            }
            else
            {
                registeredCclr.setNotificationPercentage( notification );
                registeredCclr.setWarningPercentage( warning );
                registeredCclr.setSuspensionPercentage( suspension );
                registeredCclr.setNotificationEmailAddress( email );
                if ( stopOutEnabled )
                {
                    registeredCclr.setStopOutPercentage ( stopOut );
                }
            }
            uow.commit();
        }
        catch ( Exception e )
        {
            log.error( "CreditLimitServicePTestC.setNotificationSettings : creditProviderOrg=" + creditProviderOrg + ",creditCpty=" + creditCpty, e );
        }
    }

    protected void setCalcAndLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, CreditUtilizationCalculator calc, double limit )
    {
        creditAdminSvc.setCreditEnabled( creditProviderOrg, true );
        creditAdminSvc.setCreditEnabled( creditProviderOrg, creditCpty, true );
        creditAdminSvc.setNettingMethodology( creditProviderOrg, creditCpty, clsf, calc );
        creditAdminSvc.setCreditLimit( creditProviderOrg, creditCpty, clsf, limit, CreditUtilC.getCounterpartyCreditLimitCurrency( creditProviderOrg, creditCpty.getLegalEntityOrganization(), creditCpty ) );
        creditAdminSvc.setLeverageFactor( creditProviderOrg, creditCpty, clsf, 1.0 );
        creditAdminSvc.setApplyPandL( creditProviderOrg, creditCpty, clsf, false );
        if ( calc != null && calc.isSameAs( AGGREGATE_NPR_SETTLEMENT_CALCULATOR ) )
        {
            creditAdminSvc.setIgnoreCurrDatePositions( creditProviderOrg, creditCpty, clsf, true );
        }
        else
        {
            creditAdminSvc.setIgnoreCurrDatePositions( creditProviderOrg, creditCpty, clsf, false );
        }
        creditAdminSvc.setCreditEnabled( creditProviderOrg, creditCpty, true );
        creditAdminSvc.setSuspensionPercentage( creditProviderOrg, 98.0 );
        creditAdminSvc.setSuspensionPercentage( creditProviderOrg, creditCpty, 98.0 );
    }

    protected double getLimitCurrencyAmount( Organization creditProviderOrg, TradingParty creditTp, String ccy, double amt, boolean isBuy )
    {
        Currency otherCcy = CurrencyFactory.getCurrency( ccy );
        Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( creditProviderOrg, creditTp.getLegalEntityOrganization(), creditTp );
        CurrencyPair ccyPair = CurrencyFactory.newCurrencyPair( limitCcy, otherCcy );
        FXMarketDataElement fxMde = ( ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( creditProviderOrg ) ).findFXRate( ccyPair, true, Tenor.SPOT_TENOR );
        FXRateBasis fxRateBasis = fxMde.getFXRate().getFXRateBasis();
        boolean isAmtCcyBase = CurrencyFactory.getCurrency( ccy ).isSameAs( fxRateBasis.getBaseCurrency() );
        FXRate fxRate = ( isBuy && isAmtCcyBase ) || ( !isBuy && !isAmtCcyBase ) ? fxMde.getFXPrice().getOfferFXRate() : fxMde.getFXPrice().getBidFXRate();
        return fxRate.getAmount( amt, otherCcy );
    }

    protected double getTradeAmount( Trade trade, String ccyStr )
    {
        FXSingleLeg fxTrade = ( FXSingleLeg ) trade;
        FXPaymentParameters fxPmt = fxTrade.getFXLeg().getFXPayment();
        Currency ccy = CurrencyFactory.getCurrency( ccyStr );
        return ccy.isSameAs( fxPmt.getCurrency1() ) ? fxPmt.getCurrency1Amount() : fxPmt.getCurrency2Amount();
    }

    protected Collection<CreditLimitClassification> getCreditLimitClassifications( Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg )
    {
        Set<CreditLimitClassification> clsfs = new HashSet<CreditLimitClassification>( 2 );
        CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
        if ( cptyRule != null )
        {
            Collection<CreditLimitRule> rules = cptyRule.getChildrenRules();
            log( "CreditLimitServicePTestC.getCreditLimitClassifications - Number of credit limit rules=" + rules.size() );
            for ( Rule rule : rules )
            {
                CreditLimitRule clr = ( CreditLimitRule ) rule;
                log( "CreditLimitServicePTestC.getCreditLimitClassifications - Rule=" + clr );
                clsfs.add( clr.getClassification() );
            }
        }
        return clsfs;
    }

    protected void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, double limit )
    {
        Currency limitCcy = CreditUtilC.getLimitCurrency( creditProviderOrg );
        creditAdminSvc.setCreditLimit( creditProviderOrg, creditCpty, clsf, limit, limitCcy );
    }

    protected boolean sanityCheck( Organization creditProviderOrg )
    {
        return CreditUtilC.sanityCheck( creditProviderOrg, null, false );
    }

    protected void executeCreditWorkflows( Organization creditProviderOrg, LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty )
    {
        // do a regular limit currency trade.
        double tradeAmt = 1000;
        Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, creditProviderOrg, creditCpty, bidRates[0], spotDate );
        CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, creditProviderLe, creditCptyOrg, creditCpty );
        double limit1 = getAvailableCreditLimit( creditCpty, spotDate );
        log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm1.getStatus() + ",events=" + cwm1.getCreditUtilizationEvents() );
        validateCreditUtilizationEvents( cwm1 );

        // do another trade in non-limit currency
        Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, creditProviderOrg, creditCpty, bidRates[0], spotDate );
        CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, creditProviderLe, creditCptyOrg, creditCpty );
        double limit2 = getAvailableCreditLimit( creditCpty, spotDate );
        log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",events=" + cwm2.getCreditUtilizationEvents() );
        validateCreditUtilizationEvents( cwm2 );

        // do another trade in non-limit currency trade with opposite trade action.
        Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, creditProviderOrg, creditCpty, bidRates[0], spotDate );
        CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, creditProviderLe, creditCptyOrg, creditCpty );
        double limit3 = getAvailableCreditLimit( creditCpty, spotDate );
        log( "Credit limit after first bid trade take Credit : " + limit3 + " success : " + cwm3.getStatus() + ",events=" + cwm3.getCreditUtilizationEvents() );
        validateCreditUtilizationEvents( cwm3 );

        // undo the last trade.
        CreditWorkflowMessage cwm4 = creditMgr.undoCredit( trade3, creditProviderLe, creditCptyOrg, creditCpty );
        validateCreditUtilizationEvents( cwm4 );

        // make credit failed due to lack of credit utilization.
        Trade trade5 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, creditProviderOrg, creditCpty, bidRates[0], spotDate.addDays( 1000 ) );
        CreditWorkflowMessage cwm5 = creditMgr.takeCredit( trade5, creditProviderLe, creditCptyOrg, creditCpty );
        double limit5 = getAvailableCreditLimit( creditCpty, spotDate );
        log( "Credit limit after first bid trade take Credit : " + limit5 + " success : " + cwm5.getStatus() + ",events=" + cwm5.getCreditUtilizationEvents() );
        validateCreditUtilizationEvents( cwm5 );

        // make credit failed due to insufficient credit limit
        Trade trade6 = prepareSingleLegTrade( tradeAmt * **********, true, false, EURGBP, creditProviderOrg, creditCpty, bidRates[0], spotDate );
        CreditWorkflowMessage cwm6 = creditMgr.takeCredit( trade6, creditProviderLe, creditCptyOrg, creditCpty );
        double limit6 = getAvailableCreditLimit( creditCpty, spotDate );
        log( "Credit limit after first bid trade take Credit : " + limit6 + " success : " + cwm6.getStatus() + ",events=" + cwm6.getCreditUtilizationEvents() );
        validateCreditUtilizationEvents( cwm6 );

        // make credit failed due to lack of fx rate.
        Trade trade7 = prepareSingleLegTrade( tradeAmt, true, false, "AUD/NZD", creditProviderOrg, creditCpty, bidRates[0], spotDate );
        CreditWorkflowMessage cwm7 = creditMgr.takeCredit( trade7, creditProviderLe, creditCptyOrg, creditCpty );
        double limit7 = getAvailableCreditLimit( creditCpty, spotDate );
        log( "Credit limit after first bid trade take Credit : " + limit7 + " success : " + cwm7.getStatus() + ",events=" + cwm7.getCreditUtilizationEvents() );
        validateCreditUtilizationEvents( cwm7 );

        // make credit failed due to matured trade date.
        Trade trade8 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, creditProviderOrg, creditCpty, bidRates[0], spotDate.subtractDays( 10 ) );
        CreditWorkflowMessage cwm8 = creditMgr.takeCredit( trade8, creditProviderLe, creditCptyOrg, creditCpty );
        double limit8 = getAvailableCreditLimit( creditCpty, spotDate );
        log( "Credit limit after first bid trade take Credit : " + limit8 + " success : " + cwm8.getStatus() + ",events=" + cwm8.getCreditUtilizationEvents() );
        validateCreditUtilizationEvents( cwm8 );

        // make credit failed due to lack of trading party.
        Trade trade9 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, creditProviderOrg, creditCpty, bidRates[0], spotDate.subtractDays( 10 ) );
        trade9.setCounterpartyA( null );
        CreditWorkflowMessage cwm9 = creditMgr.takeCredit( trade9, creditProviderLe, creditCptyOrg, creditCpty );
        double limit9 = getAvailableCreditLimit( creditCpty, spotDate );
        log( "Credit limit after first bid trade take Credit : " + limit9 + " success : " + cwm9.getStatus() + ",events=" + cwm9.getCreditUtilizationEvents() );
        validateCreditUtilizationEvents( cwm9 );

        // make a reserve request.
        Request request10 = DealingTestUtilC.createTestSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, creditProviderOrg, creditCpty, bidRates[0], spotDate ) );
        CreditWorkflowMessage cwm10 = creditMgr.reserveCredit( request10, creditProviderLe, creditCptyOrg, creditCpty );
        validateCreditUtilizationEvents( cwm10 );
    }

    public void testASanityCheck()
    {
        try
        {
            boolean sanityResult = true;
            init( lpUser );
            Collection<Organization> orgs = ( Collection<Organization> ) PersistenceFactory.newSession().readAllObjects( Organization.class );
            Collection<Organization> failedOrgs = new HashSet<Organization>();
            for ( Organization org : orgs )
            {
                boolean result = CreditUtilC.sanityCheck( org, null, true );
                log( "sanityCheck for org=" + org + ",result=" + result );
                if ( !result )
                {
                    failedOrgs.add( org );
                    sanityResult = result;
                }
            }
            log.error( "Sanity check failed orgs=" + failedOrgs );
            assertEquals( "All org sanity check should be true. result=" + sanityResult, sanityResult, true );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testZSanityCheck()
    {
        testASanityCheck();
    }

    protected static void initFILPCreditRelationship()
    {
        try
        {
            // set up fi and lp orgs
            creditAdminSvc.setupCreditProvider( lpOrg, staticMds );
            creditAdminSvc.setupCreditProvider( fiOrg, staticMds );

            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForDd );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.establishCreditRelationship( fiOrg, fiTpForLp );
            creditAdminSvc.establishCreditRelationship ( fiOrg, fiTpForLpLe2 );
            creditAdminSvc.establishCreditRelationship( fiOrg, fiTpforCpty );
            creditAdminSvc.establishCreditRelationship ( lpOrg, lpTpForFiLe2 );

            // now set the credit enabled to true for all cases.
            creditAdminSvc.setCreditEnabled( fiOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpforCpty, true );
        }
        catch ( Exception e )
        {
            log.error( "CreditLimitServicePTestC.initFILPCreditRelationship.ERROR", e );
        }
    }

    protected static void initPrimeBrokerCreditRelationship()
    {
        try
        {
            // set up pbLP and pbFI as credit providers.
            creditAdminSvc.setupCreditProvider( makerPbOrg, staticMds );
            creditAdminSvc.setupCreditProvider( takerPbOrg, staticMds );

            // establish credit relationship between FI and fi pb
            creditAdminSvc.establishCreditRelationship( takerOrg, takerTpForTakerPb );
            creditAdminSvc.establishCreditRelationship( takerPbOrg, takerPbTpForTaker );

            // establish credit relationship between LP and LP pb
            creditAdminSvc.establishCreditRelationship( makerOrg, makerTpForMakerPb );
            creditAdminSvc.establishCreditRelationship( makerPbOrg, makerPbTpForMaker );

            IdcTransaction tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );

            TradingParty registerdTakerTpForMaker = ( TradingParty ) takerTpForMaker.getRegisteredObject();
            registerdTakerTpForMaker.setPrimeBrokerTradingParty( ( TradingParty ) takerTpForTakerPb.getRegisteredObject() );
            registerdTakerTpForMaker.setPrimeBrokerCreditTradingParty( ( TradingParty ) takerTpForTakerPb.getRegisteredObject() );
            registerdTakerTpForMaker.setPrimeBrokerageCreditUsed( true );

            TradingParty registeredMakerTpForTaker = ( TradingParty ) makerTpForTaker.getRegisteredObject();
            registeredMakerTpForTaker.setPrimeBrokerTradingParty( ( TradingParty ) makerTpForMakerPb.getRegisteredObject() );
            registeredMakerTpForTaker.setPrimeBrokerCreditTradingParty( ( TradingParty ) makerTpForMakerPb.getRegisteredObject() );
            registeredMakerTpForTaker.setPrimeBrokerageCreditUsed( true );
            commitTransaction( tx );

            if ( CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( takerOrg, takerTpForMaker ) == null )
            {
                creditAdminSvc.establishCreditRelationship( takerOrg, takerTpForMaker );
            }

            if ( CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( makerOrg, makerTpForTaker ) == null )
            {
                creditAdminSvc.establishCreditRelationship( makerOrg, makerTpForTaker );
            }

            if ( CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( makerPbOrg, makerPbTpForTaker ) == null )
            {
                creditAdminSvc.establishCreditRelationship( makerPbOrg, makerPbTpForTaker );
            }

            // check the pbFI trading party for pbLP
            if ( takerPbTpForMakerPb != null )
            {
                creditAdminSvc.establishCreditRelationship( takerPbOrg, takerPbTpForMakerPb );
            }

            if ( makerPbTpForTakerPb != null )
            {
                creditAdminSvc.establishCreditRelationship( makerPbOrg, makerPbTpForTakerPb );
            }

            // check pb trading parties for the other side.
            if ( takerPbTpForMaker != null )
            {
                creditAdminSvc.establishCreditRelationship( takerPbOrg, takerPbTpForMaker );
            }

            if ( makerPbTpForTaker != null )
            {
                creditAdminSvc.establishCreditRelationship( makerPbOrg, makerPbTpForTaker );
            }

            if ( takerTpForMakerPb != null )
            {
                creditAdminSvc.establishCreditRelationship( takerOrg, takerTpForMakerPb );
            }

            if ( makerTpForTakerPb != null )
            {
                creditAdminSvc.establishCreditRelationship( makerOrg, makerTpForTakerPb );
            }

            // now set the credit enabled to true for all cases.
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
        }
        catch ( Exception e )
        {
            log.error( "CreditLimitServicePTestC.initPrimeBrokerCreditRelationship.ERROR", e );
        }
    }

    protected void removeCreditLimitSubscriptions( LegalEntity creditProviderLe, TradingParty creditTp )
    {
        Collection<String> ccyPairs = new HashSet<String>();
        DealingLimitCollection dlc = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( creditProviderLe, creditTp );
        if ( dlc != null )
        {
            Collection<DealingLimit> dealingLimits = dlc.getDealingLimits();
            if ( dealingLimits != null )
            {
                for ( DealingLimit dl : dealingLimits )
                {
                    FXDealingLimit fxDl = ( FXDealingLimit ) dl;
                    ccyPairs.add( CurrencyFactory.getCurrencyPairName( fxDl.getBaseCurrency(), fxDl.getVariableCurrency() ) );
                }
            }
            CreditUtilizationManagerC.getInstance().unsubscribe( creditProviderLe, creditTp, creditTp.getLegalEntityOrganization(), ccyPairs );
        }
    }

    protected CreditUtilizationEvent createCreditUtilizationEvent( CreditUtilization cu, Currency prinCcy, double princAmt, Currency priceCcy, double priceAmt, IdcDate posDate, boolean buy )
    {
        CreditUtilizationEvent cue = CreditLimitFactory.newCreditUtilizationEvent();
        cue.setPrincipalCurrency( prinCcy );
        cue.setPrincipal( princAmt );
        cue.setPriceCurrency( priceCcy );
        cue.setPrice( priceAmt );
        cue.setSettlementDate( posDate );
        cue.setBuySell( buy ? CreditLimit.BUY : CreditLimit.SELL );
        cue.setNamespace( cu.getNamespace() );
        cue.setCreditUtilization( cu );
        return cue;
    }

    protected CreditTenorProfile createCreditTenorProfile( String name )
    {
        String[] tenors = new String[]{"SPOT", "1m"};
        double[] coefficients = new double[]{10.0, 20.0};
        return createCreditTenorProfileWithTenorParam( name, tenors, coefficients );
    }

    protected CreditTenorProfile createCreditTenorProfileWithTenorParam( String name, String[] tenors, double[] coefficients )
    {
        CreditTenorProfile profile = null;
        try
        {
            profile = CreditLimitFactory.newCreditTenorProfile();
            profile.setStatus( 'T' );
            profile.setShortName( name );
            profile.setLongName( "TestLongName" );
            profile.setDescription( "testDesc" );

            for ( int i = 0; i < tenors.length; i++ )
            {
                CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
                cnp1.setUtilizationPercent( coefficients[i] );
                cnp1.setTenor( new Tenor( tenors[i] ) );
                profile.getCreditTenorParameters().add( cnp1 );
            }
        }
        catch ( Exception e )
        {
            fail( "createCreditTenorProfileWithTenorParam", e );
        }
        return profile;
    }

    protected com.integral.finance.creditLimit.model.CreditTenorProfile createCreditTenorProfileModel( String name )
    {
        String[] tenors = new String[]{"SPOT", "1m"};
        double[] coefficients = new double[]{10.0, 20.0};
        return createCreditTenorProfileModelWithTenorParam( name, tenors, coefficients );
    }

    protected com.integral.finance.creditLimit.model.CreditTenorProfile createCreditTenorProfileModelWithTenorParam( String name, String[] tenors, double[] coefficients )
    {
        com.integral.finance.creditLimit.model.CreditTenorProfile profile = null;
        try
        {
            profile = new com.integral.finance.creditLimit.model.CreditTenorProfile();
            profile.setName( name );
            profile.setDescription( "TestLongName" );

            for ( int i = 0; i < tenors.length; i++ )
            {
                com.integral.finance.creditLimit.model.CreditTenorParameters cnp1 = new com.integral.finance.creditLimit.model.CreditTenorParameters();
                cnp1.setUtilizationPercent( coefficients[i] );
                cnp1.setTenor( tenors[i] );
                profile.getTenorParameters().add( cnp1 );
            }
        }
        catch ( Exception e )
        {
            fail( "createCreditTenorProfileWithTenorParam", e );
        }
        return profile;
    }
}
