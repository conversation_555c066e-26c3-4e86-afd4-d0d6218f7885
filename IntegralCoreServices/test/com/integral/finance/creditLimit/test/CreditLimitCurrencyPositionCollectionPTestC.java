package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import java.util.HashMap;
import java.util.Map;

import com.integral.finance.creditLimit.CounterpartyCreditLimitRule;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditLimitFactory;
import com.integral.finance.creditLimit.CreditLimitRule;
import com.integral.finance.creditLimit.CreditLimitRuleSet;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationCalculator;
import com.integral.finance.creditLimit.CurrencyPosition;
import com.integral.finance.creditLimit.CurrencyPositionCollection;
import com.integral.finance.creditLimit.CurrencyPositionCollectionC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.price.fx.FXPrice;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.time.DateTimeFactory;
import com.integral.workflow.dealing.DealingLimit;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import junit.framework.Test;
import junit.framework.TestSuite;


/**
 * Tests the credit limit currency position collections and calculation of utilization and available limits at the
 * currency pair level.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitCurrencyPositionCollectionPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Currency Position Collection Test";
    static double leverageFactor = 1.0;

    public CreditLimitCurrencyPositionCollectionPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testNetReceivableAvailableLimitCalculation"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testCurrencyPositionCollectionForDailySettlement"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testCurrencyPositionCollectionForAggregateLimit"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testCurrencyPositionCollectionForAggregateGrossLimit"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testCurrencyPositionCollectionForNOP"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testCurrencyPositionCollectionForAggregateNetPRSettlement"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testCurrencyPositionCollectionForNoNetting"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testBuildPositionsFromSnapshot"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testBuildPositionsFromSnapshotBackwardCompatibility"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testCurrencyPositionCollectionComparison"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testDailyNetReceivableAvailableCalc"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testANRPositionSnapshotDeserialization"));
        suite.addTest(new CreditLimitCurrencyPositionCollectionPTestC("testNonANRPositionSnapshotDeserialization"));
        return suite;
    }

    public void testNetReceivableAvailableLimitCalculation()
    {
        try
        {
            init( lpUser );
            double limitAmt = 100000;
            double amt[] = new double[]{-9000, -19000, 10000, -45450};
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            FXRate eurUsdRate = staticMds.findSpotConversionMarketDataElement( eur, usd, true ).getFXRate();
            FXRate eurGbpRate = staticMds.findSpotConversionMarketDataElement( eur, gbp, true ).getFXRate();
            FXRate gbpUsdRate = staticMds.findSpotConversionMarketDataElement( gbp, usd, true ).getFXRate();

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true,null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[1], amt[1], gbp, leverageFactor, true,null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true,null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true,null);

            Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
            double usedAmt = pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            double availableAmt = limitAmt - usedAmt;
            log( "used amt =" + usedAmt + ",pos=" + pos );
            log( "availableAmt =" + availableAmt + ",pos=" + pos );

            CurrencyPosition eurPos = pos.getCurrencyPosition( eur, spotDate );
            CurrencyPosition gbpPos = pos.getCurrencyPosition( gbp, spotDate );

            double eurPosAmtInUSD = eurPos != null ? eurUsdRate.getAmount( eurPos.getNetAmount(), eur ) : 0.0;
            double gbpPosAmtInUSD = gbpPos != null ? gbpUsdRate.getAmount( gbpPos.getNetAmount(), gbp ) : 0.0;

            log( "eurPosAmtInUSD=" + eurPosAmtInUSD + ",gbpPosAmtInUSD=" + gbpPosAmtInUSD );

            double otherAvailableAmt = availableAmt;
            if ( eurPosAmtInUSD > 0.0 )
            {
                otherAvailableAmt += eurPosAmtInUSD;
            }
            if ( gbpPosAmtInUSD > 0.0 )
            {
                otherAvailableAmt += gbpPosAmtInUSD;
            }

            double maxAmtInUsd = otherAvailableAmt - gbpPosAmtInUSD;


            double newEurAmt = eurUsdRate.getAmount( maxAmtInUsd, usd );
            double newGbpAmt = eurGbpRate.getAmount( newEurAmt, eur );
            log( "new EUR amt=" + newEurAmt + ",new GBP amt=" + newGbpAmt );
            pos.addCurrencyAmount( tradeDate, spotDate, -newEurAmt, -newEurAmt, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, newGbpAmt, newGbpAmt, gbp, leverageFactor, true, null );
            log( "After adding the new amounts pos=" + pos );
            double newUsedAmt = pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            double newAvailable = limitAmt - newUsedAmt;
            log( "newUsedAmt =" + newUsedAmt + ",pos=" + pos );
            log( "Should be zero. newAvailable =" + newAvailable + ",pos=" + pos );

            // now to validate this. add a bigger amount than max available
            pos.addCurrencyAmount( tradeDate, spotDate, newEurAmt, newEurAmt, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, eurGbpRate.getAmount( -newEurAmt, eur ), eurGbpRate.getAmount( -newEurAmt, eur ), gbp, leverageFactor, true, null );

            double newTestMax = maxAmtInUsd + 1000;
            double newTestEurAmt = eurUsdRate.getAmount( newTestMax, usd );
            pos.addCurrencyAmount( tradeDate, spotDate, -newTestEurAmt, -newTestEurAmt, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, eurGbpRate.getAmount( newTestEurAmt, eur ), eurGbpRate.getAmount( newTestEurAmt, eur ), gbp, leverageFactor, true, null );

            double newTestUsedAmt = pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            double newTestAvailable = limitAmt - newTestUsedAmt;
            log( "newTestUsedAmt =" + newTestUsedAmt + ",pos=" + pos );
            log( "newTestAvailable =" + newTestAvailable + ",pos=" + pos );


        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCurrencyPositionCollectionForDailySettlement()
    {
        try
        {
            init( lpUser );
            double limitAmt = 100000;
            double amt[] = new double[]{9000, -19000, -10000, 45450, -2300};
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            boolean isMaker = true;
            int multiple;
            if ( isMaker )
            {
                multiple = 1;
            }
            else
            {
                multiple = -1;
            }

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.setNetting( true );
            pos.setDailyAggregate( false );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[1], amt[1], usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[4], amt[4], gbp, leverageFactor, true, null );

            Map rateMap = new HashMap();
            double[] netAmounts = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimit = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimit=" + eurGbpLimit + ",pos=" + pos );

            // test the bid limit
            double eurGbpBidLimit = eurGbpLimit.getBidLimit();
            double gbpAmt = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpBidLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * eurGbpBidLimit, multiple * eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -gbpAmt, multiple * -gbpAmt, gbp, leverageFactor, true, null );

            double[] netAmountsAfter = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimitAfter = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmountsAfter, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfter=" + eurGbpLimitAfter + ",pos=" + pos );
            double availableLimitAfterBid = limitAmt - pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            log( "Available amount after bid=" + availableLimitAfterBid );
            assertEquals( "Available should be close to zero. availableLimitAfterBid=" + availableLimitAfterBid, Math.abs( availableLimitAfterBid ) < 200, true );

            // now add the previous amount back and do an offer trade.
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpBidLimit, multiple * -eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt, multiple * gbpAmt, gbp, leverageFactor, true, null );

            double[] netAmounts1 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimit1 = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts1, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            double eurGbpOfferLimit = eurGbpLimit1.getOfferLimit();
            double gbpAmt1 = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpOfferLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpOfferLimit, multiple * -eurGbpOfferLimit, eur, leverageFactor, true , null);
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt1, multiple * gbpAmt1, gbp, leverageFactor, true, null );

            double[] netAmounts2 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimitAfterOffer = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts2, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfterOffer=" + eurGbpLimitAfterOffer + ",pos=" + pos );
            double availableLimitAfterOffer = limitAmt - pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            log( "Available amount after offer=" + availableLimitAfterOffer );
            assertEquals( "Available should be close to zero. availableLimitAfterOffer=" + availableLimitAfterOffer, Math.abs( availableLimitAfterOffer ) < 500, true );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    protected void setUp() throws Exception
    {
        super.setUp();
        removeExistingCreditUtilizationEvents( lpOrg );
        setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
    }

    public void testCurrencyPositionCollectionForAggregateLimit()
    {
        try
        {
            init( lpUser );
            double limitAmt = 100000;
            double amt[] = new double[]{9000, -19000, -10000, 45450, -2300};
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.setDailyAggregate( true );
            pos.setNetting( true );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true , null);
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[1], amt[1], usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[4], amt[4], gbp, leverageFactor, true, null );

            Map rateMap = new HashMap();
            boolean isMaker = true;
            int multiple;
            if ( isMaker )
            {
                multiple = 1;
            }
            else
            {
                multiple = -1;
            }
            double[] netAmounts = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimit = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimit=" + eurGbpLimit + ",pos=" + pos );

            // test the bid limit
            double eurGbpBidLimit = eurGbpLimit.getBidLimit();
            double gbpAmt = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpBidLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * eurGbpBidLimit, multiple * eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -gbpAmt, multiple * -gbpAmt, gbp, leverageFactor, true, null );

            double[] netAmounts1 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimitAfter = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts1, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfter=" + eurGbpLimitAfter + ",pos=" + pos );
            double availableLimitAfterBid = limitAmt - pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            log( "Available amount after bid=" + availableLimitAfterBid );
            assertEquals( "Available should be close to zero. availableLimitAfterBid=" + availableLimitAfterBid, Math.abs( availableLimitAfterBid ) < 200, true );

            // now add the previous amount back and do an offer trade.
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpBidLimit, multiple * -eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt, multiple * gbpAmt, gbp, leverageFactor, true, null );

            double[] netAmounts2 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimit1 = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts2, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            double eurGbpOfferLimit = eurGbpLimit1.getOfferLimit();
            double gbpAmt1 = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpOfferLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpOfferLimit, multiple * -eurGbpOfferLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt1, multiple * gbpAmt1, gbp, leverageFactor, true , null);

            double[] netAmounts3 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimitAfterOffer = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts3, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfterOffer=" + eurGbpLimitAfterOffer + ",pos=" + pos );
            double availableLimitAfterOffer = limitAmt - pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            log( "Available amount after offer=" + availableLimitAfterOffer );
            assertEquals( "Available should be close to zero. availableLimitAfterOffer=" + availableLimitAfterOffer, Math.abs( availableLimitAfterOffer ) < 500, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCurrencyPositionCollectionForAggregateGrossLimit()
    {
        try
        {
            init( lpUser );
            double limitAmt = 100000;
            double amt[] = new double[]{9000, -19000, -10000, 45450, -2300};
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
//            pos.setApplyPandL( true );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[1], amt[1], usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[4], amt[4], gbp, leverageFactor, true, null );

            Map rateMap = new HashMap();
            boolean isMaker = true;
            int multiple;
            if ( isMaker )
            {
                multiple = 1;
            }
            else
            {
                multiple = -1;
            }
            double[] netAmounts = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimit = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimit=" + eurGbpLimit + ",pos=" + pos );

            // test the bid limit
            double eurGbpBidLimit = eurGbpLimit.getBidLimit();
            double gbpAmt = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpBidLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * eurGbpBidLimit, multiple * eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -gbpAmt, multiple * -gbpAmt, gbp, leverageFactor, false, null );

            double[] netAmounts1 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimitAfter = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts1, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfter=" + eurGbpLimitAfter + ",pos=" + pos );
            double availableLimitAfterBid = limitAmt - pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            log( "Available amount after bid=" + availableLimitAfterBid );
            assertEquals( "Available should be close to zero. availableLimitAfterBid=" + availableLimitAfterBid, Math.abs( availableLimitAfterBid ) < 200, true );

            // Re-initialize new currency pos collection. 
            pos = new CurrencyPositionCollectionC();
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[1], amt[1], usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[4], amt[4], gbp, leverageFactor, true, null );

            double[] netAmounts2 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimit1 = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts2, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            double eurGbpOfferLimit = eurGbpLimit1.getOfferLimit();
            double gbpAmt1 = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpOfferLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpOfferLimit, multiple * -eurGbpOfferLimit, eur, leverageFactor, false, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt1, multiple * gbpAmt1, gbp, leverageFactor, true, null );

            double[] netAmounts3 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimitAfterOffer = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts3, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfterOffer=" + eurGbpLimitAfterOffer + ",pos=" + pos );
            double availableLimitAfterOffer = limitAmt - pos.getNetReceivableAmount( usd, staticMds, rateMap, true, false );
            log( "Available amount after offer=" + availableLimitAfterOffer );
            assertEquals( "Available should be close to zero. availableLimitAfterOffer=" + availableLimitAfterOffer, Math.abs( availableLimitAfterOffer ) < 200, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCurrencyPositionCollectionForNOP()
    {
        try
        {
            init( lpUser );
            double limitAmt = 100000;
            double amt[] = new double[]{9000, 19000, 10000, -45450, 2300};
            //double amt[] = new double[]{-4000, -99000, 8999900, -45450, 12300};
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            boolean isMaker = true;
            int multiple;
            if ( isMaker )
            {
                multiple = 1;
            }
            else
            {
                multiple = -1;
            }

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.setDailyAggregate( false );
            pos.setNetting( true );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[1], amt[1], usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true, null);
            pos.addCurrencyAmount( tradeDate, spotDate, amt[4], amt[4], gbp, leverageFactor, true, null );

            Map rateMap = new HashMap();
            FXDealingLimit eurGbpLimit = ( FXDealingLimit ) pos.getNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimit=" + eurGbpLimit + ",pos=" + pos );

            // test the bid limit
            double eurGbpBidLimit = eurGbpLimit.getBidLimit();

            //todo: verify what EUR/GBP rate (bid/offer/mid) should be used to derive the variable currency amount
            double gbpAmt = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpBidLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * eurGbpBidLimit, multiple * eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -gbpAmt, multiple * -gbpAmt, gbp, leverageFactor, true, null );

            FXDealingLimit eurGbpLimitAfter = ( FXDealingLimit ) pos.getNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfter=" + eurGbpLimitAfter + ",pos=" + pos );
            double availableLimitAfterBid = limitAmt - pos.getNetAmount( usd, staticMds, rateMap, false, false );
            log( "Available amount after bid=" + availableLimitAfterBid );
            assertEquals( "Available should be close to zero. availableLimitAfterBid=" + availableLimitAfterBid, Math.abs( availableLimitAfterBid ) < 500, true );

            // now add the previous amount back and do an offer trade.
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpBidLimit, multiple * -eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt, multiple * gbpAmt, gbp, leverageFactor, true, null );
            FXDealingLimit eurGbpLimit1 = ( FXDealingLimit ) pos.getNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            double eurGbpOfferLimit = eurGbpLimit1.getOfferLimit();
            double gbpAmt1 = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpOfferLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpOfferLimit, multiple * -eurGbpOfferLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt1, multiple * gbpAmt1, gbp, leverageFactor, true, null );

            FXDealingLimit eurGbpLimitAfterOffer = ( FXDealingLimit ) pos.getNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfterOffer=" + eurGbpLimitAfterOffer + ",pos=" + pos );
            double availableLimitAfterOffer = limitAmt - pos.getNetAmount( usd, staticMds, rateMap, false, false );
            log( "Available amount after offer=" + availableLimitAfterOffer );
            assertEquals( "Available should be close to zero. availableLimitAfterOffer=" + availableLimitAfterOffer, Math.abs( availableLimitAfterOffer ) < 500, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCurrencyPositionCollectionForAggregateNetPRSettlement()
    {
        try
        {
            init( lpUser );
            double limitAmt = 100000;
            double amt[] = new double[]{-9000, -19000, -10000, 45450, -2300};
            //double amt[] = new double[]{-19000, -5000, 20000, 30450, -8300};
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            boolean isMaker = false;
            int multiple;
            if ( isMaker )
            {
                multiple = 1;
            }
            else
            {
                multiple = -1;
            }

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.setDailyAggregate( false );
            pos.setNetting( true );
            pos.setIgnoreCurrentDatePositions( true );
            pos.setIgnoreLimitCurrencyPositions( true );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true , null);
            pos.addCurrencyAmount( tradeDate, spotDate, amt[1], amt[1], usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true , null);
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[4], amt[4], gbp, leverageFactor, true, null );

            // Test1: Test the bid/ offer limits for EUR/GBP currency pair
            Map rateMap = new HashMap();
            FXDealingLimit eurGbpLimit = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimit=" + eurGbpLimit + ",pos=" + pos );

            // test the bid limit
            double eurGbpBidLimit = eurGbpLimit.getBidLimit();
            double gbpAmt = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpBidLimit, eur );
            // For testing bid limits, buy base and sell variable
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * eurGbpBidLimit, multiple * eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -gbpAmt, multiple * -gbpAmt, gbp, leverageFactor, true, null );

            FXDealingLimit eurGbpLimitAfterBid = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfterBid=" + eurGbpLimitAfterBid + ",pos=" + pos );
            double availableLimitAfterBid = limitAmt - pos.getAggregateNetAmount( usd, staticMds, rateMap, false, false );
            log( "Available amount after bid=" + availableLimitAfterBid );
            assertEquals( "Available should be close to zero. availableLimitAfterBid=" + availableLimitAfterBid, Math.abs( availableLimitAfterBid ) < 200, true );

            // now add the previous amount back and do an offer trade.
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpBidLimit, multiple * -eurGbpBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt, multiple * gbpAmt, gbp, leverageFactor, true, null );
            FXDealingLimit eurGbpLimit1 = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            double eurGbpOfferLimit = eurGbpLimit1.getOfferLimit();
            double gbpAmt1 = staticMds.getSpotMarketDataElement( eur, gbp, true ).getFXRate().getAmount( eurGbpOfferLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurGbpOfferLimit, multiple * -eurGbpOfferLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * gbpAmt1, multiple * gbpAmt1, gbp, leverageFactor, true, null );

            FXDealingLimit eurGbpLimitAfterOffer = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimitAfterOffer=" + eurGbpLimitAfterOffer + ",pos=" + pos );
            double availableLimitAfterOffer = limitAmt - pos.getAggregateNetAmount( usd, staticMds, rateMap, false, false );
            log( "Available amount after offer=" + availableLimitAfterOffer );
            assertEquals( "Available should be close to zero. availableLimitAfterOffer=" + availableLimitAfterOffer, Math.abs( availableLimitAfterOffer ) < 500, true );
            // adding the previous amount back
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * eurGbpOfferLimit, multiple * eurGbpOfferLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -gbpAmt1, multiple * -gbpAmt1, gbp, leverageFactor, true, null );

            // Test2: Test the bid/ offer limits for EUR/USD currency pair
            FXDealingLimit eurUsdLimit = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, usd, rateMap, isMaker );
            log( "eurUsdLimit=" + eurUsdLimit + ",pos=" + pos );

            // test the bid limit
            double eurUsdBidLimit = eurUsdLimit.getBidLimit();
            double usdAmt = staticMds.getSpotMarketDataElement( eur, usd, true ).getFXRate().getAmount( eurUsdBidLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * eurUsdBidLimit, multiple * eurUsdBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -usdAmt, multiple * -usdAmt, usd, leverageFactor, true, null );

            FXDealingLimit eurUsdLimitAfterBid = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, usd, rateMap, isMaker );
            log( "eurUsdLimitAfterBid=" + eurUsdLimitAfterBid + ",pos=" + pos );
            availableLimitAfterBid = limitAmt - pos.getAggregateNetAmount( usd, staticMds, rateMap, false, false );
            log( "Available amount after bid=" + availableLimitAfterBid );
            assertEquals( "Available should be close to zero. availableLimitAfterBid=" + availableLimitAfterBid, Math.abs( availableLimitAfterBid ) < 500, true );

            // now add the previous amount back and do an offer trade.
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurUsdBidLimit, multiple * -eurUsdBidLimit, eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * usdAmt, multiple * usdAmt, usd, leverageFactor, true, null );
            FXDealingLimit eurUsdLimit1 = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, usd, rateMap, isMaker );
            double eurUsdOfferLimit = eurUsdLimit1.getOfferLimit();
            double usdAmt1 = staticMds.getSpotMarketDataElement( eur, usd, true ).getFXRate().getAmount( eurUsdOfferLimit, eur );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -eurUsdOfferLimit, multiple * -eurUsdOfferLimit, eur, leverageFactor, true, null);
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * usdAmt1, multiple * usdAmt1, usd, leverageFactor, true, null );

            FXDealingLimit eurUsdLimitAfterOffer = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, eur, usd, rateMap, isMaker );
            log( "eurUsdLimitAfterOffer=" + eurUsdLimitAfterOffer + ",pos=" + pos );
            availableLimitAfterOffer = limitAmt - pos.getAggregateNetAmount( usd, staticMds, rateMap, false, false );
            log( "Available amount after offer=" + availableLimitAfterOffer );
            assertEquals( "Available should be close to zero. availableLimitAfterOffer=" + availableLimitAfterOffer, Math.abs( availableLimitAfterOffer ) < 500, true );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * eurUsdOfferLimit, multiple * eurUsdOfferLimit,eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -usdAmt1, multiple * -usdAmt1, usd, leverageFactor, true, null );

            // Test3: Test the bid/ offer limits for USD/JPY currency pair
            FXDealingLimit usdJpyLimit = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, usd, jpy, rateMap, isMaker );
            log( "usdJpyLimit=" + usdJpyLimit + ",pos=" + pos );

            // test the bid limit
            double usdJpyBidLimit = usdJpyLimit.getBidLimit();
            double jpyAmt = staticMds.getSpotMarketDataElement( usd, jpy, true ).getFXRate().getAmount( usdJpyBidLimit, usd );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * usdJpyBidLimit, multiple * usdJpyBidLimit, usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -jpyAmt, multiple * -jpyAmt, jpy, leverageFactor, true, null );

            FXDealingLimit usdJpyLimitAfterBid = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, usd, jpy, rateMap, isMaker );
            log( "usdJpyLimitAfterBid=" + usdJpyLimitAfterBid + ",pos=" + pos );
            availableLimitAfterBid = limitAmt - pos.getAggregateNetAmount( usd, staticMds, rateMap, false, false );
            log( "Available amount after bid=" + availableLimitAfterBid );
            assertEquals( "Available should be close to zero. availableLimitAfterBid=" + availableLimitAfterBid, Math.abs( availableLimitAfterBid ) < 500, true );

            // now add the previous amount back and do an offer trade.
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -usdJpyBidLimit, multiple * -usdJpyBidLimit, usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * jpyAmt, multiple * jpyAmt, jpy, leverageFactor, true, null );
            FXDealingLimit usdJpyLimit1 = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, usd, jpy, rateMap, isMaker );
            double usdJpyOfferLimit = usdJpyLimit1.getOfferLimit();
            double jpyAmt1 = staticMds.getSpotMarketDataElement( usd, jpy, true ).getFXRate().getAmount( usdJpyOfferLimit, usd );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -usdJpyOfferLimit, multiple * -usdJpyOfferLimit, usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * jpyAmt1, multiple * jpyAmt1, jpy, leverageFactor, true, null );

            FXDealingLimit usdJpyLimitAfterOffer = ( FXDealingLimit ) pos.getAggregateNetAmountDealingLimit( limitAmt, usd, staticMds, spotDate, usd, jpy, rateMap, isMaker );
            log( "usdJpyLimitAfterOffer=" + usdJpyLimitAfterOffer + ",pos=" + pos );
            availableLimitAfterOffer = limitAmt - pos.getAggregateNetAmount( usd, staticMds, rateMap, false, false );
            log( "Available amount after offer=" + availableLimitAfterOffer );
            assertEquals( "Available should be close to zero. availableLimitAfterOffer=" + availableLimitAfterOffer, Math.abs( availableLimitAfterOffer ) < 500, true );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * usdJpyOfferLimit, multiple * usdJpyOfferLimit, usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, multiple * -jpyAmt1, multiple * -jpyAmt1, jpy, leverageFactor, true, null );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCurrencyPositionCollectionForNoNetting()
    {
        try
        {
            init( lpUser );
            double limitAmt = 100000;
            double amt[] = new double[]{9000, -19000, -10000, 45450, -2300};
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.setDailyAggregate( false );
            pos.setNetting( false );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[1], amt[1], usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[4], amt[4], gbp, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[0], amt[0], gbp, leverageFactor, true, null );

            Map rateMap = new HashMap();
            boolean isMaker = true;
            double[] netAmounts0 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurGbpLimit = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts0, usd, staticMds, spotDate, eur, gbp, rateMap, isMaker );
            log( "eurGbpLimit=" + eurGbpLimit + ",pos=" + pos );

            double[] netAmounts1 = pos.getNetAmounts( usd, staticMds, rateMap );
            FXDealingLimit eurChfLimit = ( FXDealingLimit ) pos.getNetReceivableDealingLimit( limitAmt, netAmounts1, usd, staticMds, spotDate, eur, chf, rateMap, isMaker );
            log( "eurChfLimit=" + eurChfLimit + ",pos=" + pos );

            assertEquals( "eurGbp bid and offer limits in EUR should be same.", eurGbpLimit.getBidLimit(), eurGbpLimit.getOfferLimit() );
            assertEquals( "eurChf bid and offer limits in EUR should be same.", eurChfLimit.getBidLimit(), eurChfLimit.getOfferLimit() );

            assertEquals( "eurGbp limit in EUR and eurChf bid limit in EUR should be same.", eurGbpLimit.getBidLimit(), eurChfLimit.getBidLimit() );
            assertEquals( "eurGbp limit in EUR and eurChf offer limit in EUR should be same.", eurGbpLimit.getOfferLimit(), eurChfLimit.getOfferLimit() );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBuildPositionsFromSnapshot()
    {
    	reallyTestBuildPositionsFromSnapshot(false);
    }
    
    public void testBuildPositionsFromSnapshotBackwardCompatibility()
    {
    	reallyTestBuildPositionsFromSnapshot(true);
    }
    
    private void reallyTestBuildPositionsFromSnapshot(boolean testPosSerializationBackwardCompatibility)
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            init( lpUser );
            for ( CreditUtilizationCalculator aggCalc : CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies() )
            {
                setCalcAndLimit( lpOrg, lpTpForFi, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 100000.0 );
                CreditUtilization cu = getAggregateCreditUtilization( lpLe, lpTpForFi, tradeDate );
                cu.resetCurrencyPositions( "Test", false );

                CurrencyPositionCollection cps = cu.getCurrencyPositions();
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, eur, 10000.00, jpy, 2345676.77, tradeDate.addDays( 1 ), true ) );
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, gbp, 10000.00, usd, 1234.55, tradeDate.addDays( 2 ), false ) );
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, chf, 56453.98, eur, 5676.77, tradeDate.addDays( 3 ), true ) );
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, eur, 10000.00, jpy, 3456.09, tradeDate.addDays( 1 ), false ) );
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, eur, 987.45, jpy, 45000.00, tradeDate.addDays( 2 ), true ) );

                String ps;
                if(testPosSerializationBackwardCompatibility)
            	{
                	ps = ((CurrencyPositionCollectionC)cps).getPositions(false);            
            	}
                else{
                	ps = cps.getPositions();
                }
                log( "position snapshot=" + ps + ",calc=" + aggCalc );

                synchronized ( cu )
                {
                    cu.resetCurrencyPositions( "Test", false );
                    cu.setPositions( ps );
                    cu.rebuildCurrencyPositionsFromSnapshot( "Test", false );
                }
                CurrencyPositionCollection cpsNew = cu.getCurrencyPositions();
                String psNew;
                if(testPosSerializationBackwardCompatibility)
            	{
                	psNew = ((CurrencyPositionCollectionC)cpsNew).getPositions(false);            
            	}
                else{
                	psNew = cpsNew.getPositions();
                }
                assertEquals( "snapshots should be same for calc=" + aggCalc, psNew, ps );
            }

            for ( CreditUtilizationCalculator dailyCalc : CreditUtilC.getSupportedDailyNettingMethodologies() )
            {
                setCalcAndLimit( lpOrg, lpTpForFi, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 100000.0 );
                CreditUtilization cu = getDailyCreditUtilization( lpLe, lpTpForFi, tradeDate );
                cu.resetCurrencyPositions( "Test", false );

                CurrencyPositionCollection cps = cu.getCurrencyPositions();
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, eur, 10000.00, jpy, 2345676.77, tradeDate.addDays( 1 ), true ) );
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, gbp, 10000.00, usd, 1234.55, tradeDate.addDays( 2 ), false ) );
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, chf, 56453.98, eur, 5676.77, tradeDate.addDays( 3 ), true ) );
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, eur, 10000.00, jpy, 3456.09, tradeDate.addDays( 1 ), false ) );
                cps.addCreditUtilizationEvent( createCreditUtilizationEvent( cu, eur, 987.45, jpy, 45000.00, tradeDate.addDays( 2 ), true ) );

                String ps;
                if(testPosSerializationBackwardCompatibility)
            	{
                	ps = ((CurrencyPositionCollectionC)cps).getPositions(false);            
            	}
                else{
                	ps = cps.getPositions();
                }
                
                log( "position snapshot=" + ps + ",calc=" + dailyCalc );

                synchronized ( cu )
                {
                    cu.resetCurrencyPositions( "Test", false );
                    cu.setPositions( ps );
                    cu.rebuildCurrencyPositionsFromSnapshot( "Test", false );
                }
                CurrencyPositionCollection cpsNew = cu.getCurrencyPositions();
                String psNew;
                if(testPosSerializationBackwardCompatibility)
            	{
                	psNew = ((CurrencyPositionCollectionC)cpsNew).getPositions(false);            
            	}
                else{
                	psNew = cpsNew.getPositions();
                }
                assertEquals( "snapshots should be same for calc=" + dailyCalc, psNew, ps );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCurrencyPositionCollectionComparison()
    {
        try
        {
            init( lpUser );
            double limitAmt = 100000;
            double amt[] = new double[]{9000, -19000, -10000, 45450, -2300};
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency chf = CurrencyFactory.getCurrency( "CHF" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setDailyAggregate( true );
            pos.setCreditUtilization( getAggregateCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[0], amt[0], eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[1], amt[1], usd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[2], amt[2], jpy, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate, amt[3], amt[3], chf, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 1 ), amt[4], amt[4], gbp, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 2 ), amt[4], amt[4], gbp, leverageFactor, true, null );

            assertTrue( pos.isCurrencyPositionsMatch( pos ) );
            CurrencyPositionCollection trueCopy = pos.copy( true );
            CurrencyPositionCollection copy2 = pos.copy( false );
            CurrencyPositionCollection copy3 = pos.copy( false );
            assertTrue( pos.isCurrencyPositionsMatch( trueCopy ) );
            assertFalse( pos.isCurrencyPositionsMatch( copy2 ) );
            assertTrue( copy2.isCurrencyPositionsMatch( copy3 ) );

            //now add another currency amount to the position.
            pos.addCurrencyAmount( tradeDate, spotDate.addDays( 2 ), amt[4], amt[4], gbp, leverageFactor, true, null );

            assertFalse( pos.isCurrencyPositionsMatch( trueCopy ) );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPositionCollectionComparison", e );
        }
    }

    public void testDailyNetReceivableAvailableCalc()
    {
        try
        {
            //JPY=-1.506351577E9;CAD=-1993500.0;NZD=-2808961.0;AUD=2.3567922E7;GBP=-1800000.0;EUR=-1930693.0;USD=3187291.94,
            init( lpUser );
            double[] amt = new double[]{-1506351577.0, -1993500.0, -2808961.0, 23567922.0, -1800000.0, -1930693.0, 3187291.94};
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency cad = CurrencyFactory.getCurrency( "CAD" );
            Currency nzd = CurrencyFactory.getCurrency( "NZD" );
            Currency aud = CurrencyFactory.getCurrency( "AUD" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            CurrencyPositionCollection pos = new CurrencyPositionCollectionC();
            pos.setDailyAggregate( false );
            pos.setNetting( true );
            CreditUtilization cu = getDailyCreditUtilization( lpLe, lpTpForDd, tradeDate );
            cu.getCreditLimitRule().setAllowNetting( true );
            cu.getCreditLimitRule().setCreditUtilizationCalculator( DAILY_SETTLEMENT_LIMIT_CALCULATOR );
            cu.getCreditLimitRule().setLimitAmount( 25000000.0 );
            pos.setCreditUtilization( getDailyCreditUtilization( lpLe, lpTpForDd, tradeDate ) );
            pos.setBaseDate( tradeDate );
            pos.addCurrencyAmount( tradeDate, tradeDate, amt[0], amt[0], jpy, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, tradeDate, amt[1], amt[1], cad, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, tradeDate, amt[2], amt[2], nzd, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, tradeDate, amt[3], amt[3], aud, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, tradeDate, amt[4], amt[4], gbp, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, tradeDate, amt[5], amt[5], eur, leverageFactor, true, null );
            pos.addCurrencyAmount( tradeDate, tradeDate, amt[6], amt[6], usd, leverageFactor, true, null );
            Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
            double[] netAmounts = pos.getNetAmounts( cu.getCurrency(), staticMds, rateMap );
            double excludeUsedAmt = cu.getAdjustedLimit() - cu.getReservedAmount();
            DealingLimit dl = pos.getNetReceivableDealingLimit( excludeUsedAmt, netAmounts, cu.getCurrency(), staticMds, tradeDate, eur, gbp, rateMap, true );
            System.out.println( "############################ dl=" + dl );

        }
        catch ( Exception e )
        {
            fail( "testDailyNetReceivableAvailableCalc", e );
        }
    }

    public void testANRPositionSnapshotDeserialization()
    {
        try
        {
            String anrPos = "11-Sep-2015:EUR=182888.91,914444.57;USD=-200000.0,-1000000.0~14-Sep-2015:EUR=-182888.91,-914444.57;USD=200000.0,1000000.0~28-Apr-2015:AUD=100000.0,1000000.0;USD=-118830.0,-1188300.0~16-Sep-2015:EUR=-182888.91,-914444.57;USD=200000.0,1000000.0~05-May-2015:AUD=-100000.0,-1000000.0;USD=118860.0,1188600.0~15-Sep-2015:EUR=182907.31,914536.56;USD=-200000.0,-1000000.0~18-Sep-2015:EUR=-365759.42,-1828797.15;USD=400000.0,2000000.0~17-Sep-2015:EUR=182907.31,914536.56;USD=-200000.0,-1000000.0~23-Apr-2015:EUR=150000.0,1000000.0;USD=-164017.5,-1093450.0~22-Apr-2015:AUD=-50000.0,-1000000.0;USD=59435.0,1188700.0~22-Sep-2015:EUR=-182857.14,-914285.71;USD=200000.0,1000000.0~21-Apr-2015:AUD=50000.0,1000000.0;USD=-59415.0,-1188300.0~20-Apr-2015:GBP=50000.0,1000000.0;USD=-89416.5,-1788330.0~21-Sep-2015:EUR=182888.91,914444.57;USD=-200000.0,-1000000.0~24-Sep-2015:EUR=-182857.14,-914285.71;USD=200000.0,1000000.0~23-Sep-2015:EUR=182888.91,914444.57;USD=-200000.0,-1000000.0~25-Sep-2015:EUR=182888.91,914444.57;USD=-200000.0,-1000000.0~26-Aug-2015:JPY=-1.181542E7,-1.181542E8;EUR=100000.0,1000000.0~27-Aug-2015:JPY=1.180024E7,1.180024E8;EUR=-100000.0,-1000000.0~28-Aug-2015:JPY=-1.181542E7,-1.181542E8;EUR=100000.0,1000000.0~20-May-2015:CHF=121809.0,1218090.0;EUR=-400000.0,-2000000.0;USD=337500.0,1187500.0~21-May-2015:CHF=121829.0,1218290.0;USD=-100000.0,-1000000.0~31-Aug-2015:JPY=-1.181542E7,-1.181542E8;EUR=-82888.91,85555.43;USD=200000.0,1000000.0~01-Sep-2015:EUR=182907.31,914536.56;USD=-200000.0,-1000000.0~02-Sep-2015:EUR=-182888.91,-914444.57;USD=200000.0,1000000.0~03-Sep-2015:EUR=182907.31,914536.56;USD=-200000.0,-1000000.0~04-Sep-2015:EUR=-182888.91,-914444.57;USD=200000.0,1000000.0~07-May-2015:EUR=-400000.0,-2000000.0;USD=437500.0,2187500.0~12-May-2015:CHF=-121907.0,-1219070.0;USD=100000.0,1000000.0~13-May-2015:AUD=-100000.0,-1000000.0;USD=118860.0,1188600.0~08-Sep-2015:EUR=31.77,158.86;USD=0.0,0.0~09-Sep-2015:EUR=182888.91,914444.57;USD=-200000.0,-1000000.0~11-May-2015:GBP=-100000.0,-1000000.0;USD=178863.0,1788630.0~10-Sep-2015:EUR=-182857.14,-914285.71;USD=200000.0,1000000.0~02-Jun-2015:JPY=-1.854002E7,-9.27001E7;USD=200000.0,1000000.0~04-Jun-2015:EUR=200000.0,1000000.0;USD=-218690.0,-1093450.0~27-May-2015:AUD=100000.0,1000000.0;USD=-118870.0,-1188700.0~26-May-2015:AUD=-100000.0,-1000000.0;USD=118860.0,1188600.0~29-Sep-2015:EUR=-228571.43,-914285.71;USD=250000.0,1000000.0~30-Sep-2015:EUR=228611.14,914444.57;USD=-250000.0,-1000000.0~16-Jun-2015:JPY=-1.854002E7,-9.27001E7;EUR=200000.0,1000000.0;USD=-18690.0,-93450.0~28-Sep-2015:EUR=182888.91,914444.57;USD=-200000.0,-1000000.0~18-Jun-2015:EUR=200000.0,1000000.0;USD=-218690.0,-1093450.0~09-Jun-2015:JPY=-1.854002E7,-9.27001E7;USD=200000.0,1000000.0~11-Jun-2015:EUR=200000.0,1000000.0;USD=-218690.0,-1093450.0~02-Jul-2015:CHF=-121799.0,-1217990.0;USD=100000.0,1000000.0~30-Jun-2015:JPY=-1.854002E7,-9.27001E7;USD=200000.0,1000000.0~25-Jun-2015:EUR=200000.0,1000000.0;USD=-218690.0,-1093450.0~23-Jun-2015:JPY=-1.854002E7,-9.27001E7;USD=200000.0,1000000.0~16-Jul-2015:CHF=-121799.0,-1217990.0;USD=100000.0,1000000.0~09-Jul-2015:CHF=-121799.0,-1217990.0;USD=100000.0,1000000.0~07-Aug-2015:JPY=1.181542E7,1.181542E8;EUR=-100000.0,-1000000.0~06-Aug-2015:JPY=-1.180321E7,-1.180321E8;EUR=100000.0,1000000.0~26-Mar-2015:CAD=-9527.0,-952700.0;USD=6884.16,688416.37~03-Aug-2015:JPY=1.181542E7,1.181542E8;EUR=-100000.0,-1000000.0~05-Aug-2015:JPY=1.181242E7,1.181242E8;EUR=-100000.0,-1000000.0~04-Aug-2015:JPY=-1.180021E7,-1.180021E8;EUR=100000.0,1000000.0~27-Mar-2015:JPY=-1.059002E7,-5.89402E8;AUD=93668.6,9366861.52;GBP=101184.33,1.011843375E7;CHF=-89521.7,-8952170.0;EUR=130416.72,6358540.17;USD=-238834.81,-2.253377021E7~24-Aug-2015:JPY=-1.181542E7,-1.181542E8;EUR=100000.0,1000000.0~25-Aug-2015:JPY=1.180024E7,1.180024E8;EUR=-100000.0,-1000000.0~18-Aug-2015:JPY=-1.180021E7,-1.180021E8;EUR=100000.0,1000000.0~19-Aug-2015:JPY=1.181542E7,1.181542E8;EUR=-100000.0,-1000000.0~07-Apr-2015:CHF=60953.5,1219070.0;USD=-50000.0,-1000000.0~20-A";
            CreditUtilization cu = CreditLimitFactory.newCreditUtilization();
            CreditLimitRule clr = CreditLimitFactory.newCreditLimitRule();
            CounterpartyCreditLimitRule cclr = CreditLimitFactory.newCounterpartyCreditLimitRule();
            clr.setParentRule( cclr );
            clr.setCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
            CreditLimitRuleSet clrs = CreditLimitFactory.newCreditLimitRuleSet();
            cclr.setRuleSet( clrs );
            cu.setCreditLimitRule( clr );
            cu.setPositions( anrPos );
            CurrencyPositionCollection cps = new CurrencyPositionCollectionC();
            cps.setCreditUtilization( cu );
            cps.setBaseDate( DateTimeFactory.newDate() );
            cps.setDailyAggregate( true );
            cps.setNetting( true );
            for ( int i = anrPos.length(); i > 100; i-- )
            {
                String newPos = anrPos.substring( 0, i );
                cu.setPositions( newPos );
                cps.deserializePositionMap( cu );
                log( "rebuild newPos=" + newPos );
                log( "rebuild newPos.positions=" + cps.getPositions() );
                assertTrue( "newPos.length=" + newPos.length() + ",cps.pos.length=" + cps.getPositions().length(), newPos.length() - cps.getPositions().length() < 50 );
            }

        }
        catch ( Exception e )
        {
            fail( "testANRPositionSnapshotDeserialization", e );
        }
    }

    public void testNonANRPositionSnapshotDeserialization()
    {
        try
        {
            CreditUtilization cu = CreditLimitFactory.newCreditUtilization();
            CreditLimitRule clr = CreditLimitFactory.newCreditLimitRule();
            CounterpartyCreditLimitRule cclr = CreditLimitFactory.newCounterpartyCreditLimitRule();
            clr.setParentRule( cclr );
            CreditLimitRuleSet clrs = CreditLimitFactory.newCreditLimitRuleSet();
            cclr.setRuleSet( clrs );
            cu.setCreditLimitRule( clr );
            clr.setCurrency ( CurrencyFactory.getCurrency ( "USD" ) );

            Currency[] currencies = ReferenceDataCacheC.getInstance().getCurrencies();
            CurrencyPositionCollection testCps = new CurrencyPositionCollectionC();
            testCps.setBaseDate( tradeDate );
            testCps.setNetting( true );
            testCps.setCreditUtilization( cu );
            for ( Currency ccy : currencies )
            {
                if ( ccy != null )
                {
                    testCps.addCurrencyAmount( tradeDate, spotDate, (Integer.MAX_VALUE/10.0), (Integer.MAX_VALUE / 5.0), ccy, 1.0, true,null );
                }
            }

            log( "testCps.pos=" + testCps.getPositions() );
            String pos = testCps.getPositions().substring( 0, 3000 );
            log( "trimmed testCps.pos=" + pos );
            cu.setPositions( pos );
            CurrencyPositionCollection cps = new CurrencyPositionCollectionC();
            cps.setCreditUtilization( cu );
            cps.setBaseDate( tradeDate );
            cps.setNetting( true );
            for ( int i = pos.length(); i > 100; i-- )
            {
                String newPos = pos.substring( 0, i );
                cu.setPositions( newPos );
                cps.deserializePositionMap( cu );
                log( "rebuild newPos=" + newPos );
                log( "rebuild newPos.positions=" + cps.getPositions() );
                assertTrue( "newPos.length=" + newPos.length() + ",cps.pos.length=" + cps.getPositions().length(), newPos.length() - cps.getPositions().length() < 50 );
            }

        }
        catch ( Exception e )
        {
            fail( "testNonANRPositionSnapshotDeserialization", e );
        }
    }

}
