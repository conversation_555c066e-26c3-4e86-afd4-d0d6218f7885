package com.integral.finance.creditLimit.test;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.functor.CreditUtilizationCacheSynchronizeNotificationFunctorC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.log.LogFactory;
import com.integral.log.LogLevel;
import com.integral.message.ErrorMessage;
import com.integral.message.MessageStatus;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcTransaction;
import com.integral.session.RemoteTransactionNotification;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.time.IdcDate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


/**
 * Tests the credit limit service workflow.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitServiceWorkflowPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Service workflow Test";

    public CreditLimitServiceWorkflowPTestC( String name )
    {
        super( name );
    }

    public void testMinTenorValidationSuccess()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the minTenor limit to 5 days and value date 10 days
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, new Tenor( "5D" ) );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 10 ) );
            log( "credit limit before take Credit: " + limitBefore );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 10 ) );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testMinTenorValidationSuccess", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
        }
    }

    public void testMaxTenorValidationSuccess()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the maxTenor limit to 10 days and value date 10 days
            Tenor maxTenor = new Tenor( "10D" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor );

            // calculate the value date based on the maximum tenor.
            IdcDate maxDate = stdQuoteConv.getFXRateBasis( "EUR", "USD" ).getValueDate( tradeDate, maxTenor );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, maxDate );
            log( "credit limit before take Credit: " + limitBefore );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], maxDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, maxDate );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorValidationSuccess", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null);
            creditAdminSvc.setMaximumTenor(fiOrg, fiTpForLp, null);
        }
    }

    public void testMultiCreditRuleTakeCredit()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( cptyOrg );

            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCredit : credit limit  before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
            double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCredit : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "testMultiCreditRuleTakeCredit : 1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt ) < MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCredit", e );
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTakeCreditOnWarmupMode()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents(fiOrg);
            removeExistingCreditUtilizationEvents( cptyOrg );

            RuntimeFactory.getServerRuntimeMBean().setServerWarmingUp ( true );

            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCredit : credit limit  before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
            double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCredit : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals("testMultiCreditRuleTakeCredit : nothing was taken :", true, Math.abs(limit0 - limit1) < MINIMUM);

            // there should not be any utilizations created.
            sleepFor( 3000 );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpforCpty );
            assertNotNull(cclr);
            Collection rules = cclr.getChildrenRules();
            for ( Object r: rules )
            {
                Collection<CreditUtilization> cus = CreditUtilC.getUsedCreditUtilizations( (CreditLimitRule) r );
                assertTrue( cus == null || cus.isEmpty() );
            }
        }
        catch ( Exception e )
        {
            log.error( "testTakeCreditOnWarmupMode", e );
            fail();
        }
        finally
        {
            RuntimeFactory.getServerRuntimeMBean().setServerWarmingUp( false );
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testMultiCreditRuleTakeCreditFailure_Insufficient_Credit()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( cptyOrg );

            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : credit limit  before take Credit : " + limit0 );
            Double suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpforCpty ).getSuspensionPercentage();
            if ( suspensionPercent == null )
            {
                creditAdminSvc.setSuspensionPercentage( fiOrg, fiTpforCpty, 98.0 );
                suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpforCpty ).getSuspensionPercentage();
            }
            Double totalLimit = getTotalCreditLimit( fiOrg, fiTpforCpty );
            Double bufferLimit = totalLimit * ( 1 - suspensionPercent / 100 );
            double tradeAmt = limit0 + ( bufferLimit + 200 );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
            double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : 1000 was taken :", true, Math.abs( limit0 - limit1 ) < MINIMUM );
            assertEquals( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : status :  ", cwm.getStatus(), MessageStatus.FAILURE );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit", e );
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( cptyOrg );

            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : credit limit  before take Credit : " + limit0 );
            Double suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpforCpty ).getSuspensionPercentage();
            if ( suspensionPercent == null )
            {
                creditAdminSvc.setSuspensionPercentage( fiOrg, fiTpforCpty, 98.0 );
                suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpforCpty ).getSuspensionPercentage();
            }
            double totalLimit = getTotalCreditLimit( fiOrg, fiTpforCpty );
            Double bufferLimit = totalLimit * ( 1 - suspensionPercent / 100 );
            double tradeAmt = limit0 + ( bufferLimit );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
            double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent : 1000 was taken :", true, Math.abs( limit0 - limit1 - totalLimit ) < MINIMUM );
            assertEquals( "testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent : status :  ", cwm.getStatus(), MessageStatus.SUCCESS );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent", e );
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testMultiCreditRuleTakeCreditFailure_No_FXRate()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( cptyOrg );
            if ( noFXRateCurrencyPairs.isEmpty() )
            {
                return;
            }
            CurrencyPair ccyPair = ( CurrencyPair ) noFXRateCurrencyPairs.toArray()[0];
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_No_FXRate : credit limit  before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, ccyPair.getName(), fiOrg, fiTpforCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
            double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log("testMultiCreditRuleTakeCreditFailure_No_FXRate : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus());
            FXMarketDataElement fxMde = staticMds.findSpotConversionMarketDataElement( ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(), true );
            if ( fxMde == null )
            {
                assertEquals( "testMultiCreditRuleTakeCreditFailure_No_FXRate : 1000 was taken :", true, Math.abs( limit0 - limit1 ) < MINIMUM );
                assertEquals( "testMultiCreditRuleTakeCreditFailure_No_FXRate : status :  ", cwm.getStatus(), MessageStatus.FAILURE );
            }
            else
            {
                assertEquals( "testMultiCreditRuleTakeCreditFailure_No_FXRate : status :  ", cwm.getStatus(), MessageStatus.SUCCESS );
            }
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCreditFailure_No_FXRate", e );
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testMultiCreditRuleTakeCreditFailure_No_CreditUtilization()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( cptyOrg );

            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization : credit limit  before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate.addDays( 740 ) );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
            double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals("testMultiCreditRuleTakeCreditFailure_No_CreditUtilization : 1000 was taken :", true, Math.abs(limit0 - limit1) < MINIMUM);
            assertEquals( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization : status :  ", cwm.getStatus(), MessageStatus.FAILURE );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization", e );
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testBilateralTakeCreditFailure_No_CreditUtilization()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            double dailyFILimit0 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLPLimit0 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double aggregateFILimit0 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLPLimit0 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "daily FI credit limit  before take Credit : " + dailyFILimit0 );
            log( "daily LP credit limit  before take Credit : " + dailyLPLimit0 );
            log( "aggregate FI credit limit  before take Credit : " + aggregateFILimit0 );
            log( "aggregate LP credit limit  before take Credit : " + aggregateLPLimit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays( 740 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, cptyLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            double dailyFILimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLPLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double aggregateFILimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLPLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "daily FI credit limit  after take Credit : " + dailyFILimit1 + " success : " + cwm.getStatus() );
            log( "daily LP credit limit  after take Credit : " + dailyLPLimit1 + " success : " + cwm.getStatus() );
            log( "aggregate FI credit limit  after take Credit : " + aggregateFILimit1 + " success : " + cwm.getStatus() );
            log( "aggregate LP credit limit  after take Credit : " + aggregateLPLimit1 + " success : " + cwm.getStatus() );
            assertEquals( "Limit should not be different", true, Math.abs( dailyFILimit0 - dailyFILimit1 ) < MINIMUM );
            assertEquals( "Limit should not be different", true, Math.abs( dailyLPLimit0 - dailyLPLimit1 ) < MINIMUM );
            assertEquals( "Limit should not be different", true, Math.abs( aggregateFILimit0 - aggregateFILimit1 ) < MINIMUM );
            assertEquals( "Limit should not be different", true, Math.abs( aggregateLPLimit0 - aggregateLPLimit1 ) < MINIMUM );
            assertEquals( "staus should be failure=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( cptyOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            double dailyLimit = getDailyAvailableCreditLimit( fiTpforCpty, spotDate );
            double grossLimit = getGrossNotionalAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit :  daily credit limit  before take Credit : " + dailyLimit + " gross limit : " + grossLimit );
            Double suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpforCpty ).getSuspensionPercentage();
            if ( suspensionPercent == null )
            {
                creditAdminSvc.setSuspensionPercentage( fiOrg, fiTpforCpty, 98.0 );
            }
            Double totalLimit = getTotalDailyCreditLimit( fiTpforCpty, spotDate );
            Double bufferLimit = totalLimit * ( 1 - suspensionPercent / 100 );
            double tradeAmt = dailyLimit + ( bufferLimit + 200 );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
            double dailylimit1 = getDailyAvailableCreditLimit( fiTpforCpty, spotDate );
            double grossLimit1 = getGrossNotionalAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit : daily credit limit  after take Credit : " + dailylimit1 + " gross limit : " + grossLimit1 + " success : " + cwm.getStatus() );
            assertEquals( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit : gross limit is unchanged :", true, Math.abs( grossLimit1 - grossLimit ) < MINIMUM );
            assertEquals( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit : daily limit is unchanged :", true, Math.abs( dailylimit1 - dailyLimit ) < MINIMUM );
            assertEquals( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit : status :  ", cwm.getStatus(), MessageStatus.FAILURE );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            log.error("testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit", e);
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    private void setTemplate()
    {
        removeExistingCreditUtilizationEvents( lpOrg );
        removeExistingCreditUtilizationEvents( fiOrg );

        setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, ********0 );
        setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );

    }

    public void testCreditLimits()
    {
        try
        {
            setTemplate();
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( ddOrg );
            double grossLimit = getGrossNotionalAvailableCreditLimit( lpTpForDd, spotDate );
            log( "******** grossLimit before any trade=" + grossLimit );

            double tradeAmt = 2000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, 1.3028, spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );

            CreditUtilization cu = cwm.getCreditUtilizationEvents().iterator().next().getCreditUtilization();
            CurrencyPositionCollection positions = cu.getCurrencyPositions();
            double[] netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() );
            double pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE];
            double pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE];
            double profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true );
            log( "************ Used Amount=" + cu.getUsedAmount() );
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() );
            log( "************ P/L=" + profitAndLoss );
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() );

            tradeAmt = 1000000;
            trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, 1.3032, spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );

            cu = cwm.getCreditUtilizationEvents().iterator().next().getCreditUtilization();
            positions = cu.getCurrencyPositions();
            netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() );
            pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE];
            pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE];
            profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true );
            log( "************ Used Amount=" + cu.getUsedAmount() );
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() );
            log( "************ P/L=" + profitAndLoss );
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() );

            tradeAmt = 2000000;
            trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, 1.3028, spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );

            cu = cwm2.getCreditUtilizationEvents().iterator().next().getCreditUtilization();
            positions = cu.getCurrencyPositions();
            netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() );
            pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE];
            pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE];
            profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true );
            log( "************ Used Amount=" + cu.getUsedAmount() );
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() );
            log( "************ P/L=" + profitAndLoss );
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() );


            tradeAmt = 4000000;
            trade = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, 0.6977, spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );

            cu = cwm3.getCreditUtilizationEvents().iterator().next().getCreditUtilization();
            positions = cu.getCurrencyPositions();
            netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() );
            pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE];
            pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE];
            profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true );
            log( "************ Used Amount=" + cu.getUsedAmount() );
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() );
            log( "************ P/L=" + profitAndLoss );
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() );

            tradeAmt = 4000000;
            trade = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, 0.698, spotDate );
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );

            cu = cwm4.getCreditUtilizationEvents().iterator().next().getCreditUtilization();
            positions = cu.getCurrencyPositions();
            netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() );
            pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE];
            pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE];
            profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true );
            log( "************ Used Amount=" + cu.getUsedAmount() );
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() );
            log( "************ P/L=" + profitAndLoss );
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() );
        }
        catch ( Exception e )
        {
            fail( "testCreditLimits", e );
        }
    }


    public void testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( cptyOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );

            double dailyLimit = getDailyAvailableCreditLimit( fiTpforCpty, spotDate );
            double grossLimit = getGrossNotionalAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit :  daily credit limit  before take Credit : " + dailyLimit + " gross limit : " + grossLimit );

            // remove the daily limit for spot date.
            Trade trade1 = prepareSingleLegTrade( dailyLimit, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, fiLe, cptyOrg, fiTpforCpty );
            double dailylimit1 = getDailyAvailableCreditLimit( fiTpforCpty, spotDate );
            double grossLimit1 = getGrossNotionalAvailableCreditLimit( fiTpforCpty, spotDate );
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : daily credit limit after take Credit : " + dailylimit1 + " gross limit : " + grossLimit1 + " success : " + cwm1.getStatus() );

            // remove the daily limit for spot next date.
            double spotNextDailyAvailable = getDailyAvailableCreditLimit( fiTpforCpty, spotDate.addDays( 1 ) );
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit :  on spot+1 day daily credit limit  before take Credit : " + spotNextDailyAvailable + " gross limit : " + grossLimit1 );
            Trade trade2 = prepareSingleLegTrade( spotNextDailyAvailable, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, fiLe, cptyOrg, fiTpforCpty );
            double dailylimit2 = getDailyAvailableCreditLimit( fiTpforCpty, spotDate.addDays( 1 ) );
            double grossLimit2 = getGrossNotionalAvailableCreditLimit( fiTpforCpty, spotDate.addDays( 1 ) );
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : spot + 1 daily credit limit  after take Credit : " + dailylimit2 + " gross limit : " + grossLimit2 + " success : " + cwm2.getStatus() );

            // try to do a trade with spot date + 2
            double spot2DailyLimit = getDailyAvailableCreditLimit( fiTpforCpty, spotDate.addDays( 2 ) );
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit :  on spot+2 day daily credit limit  before take Credit : " + spot2DailyLimit + " gross limit : " + grossLimit2 );
            Trade trade3 = prepareSingleLegTrade( spot2DailyLimit, true, false, EURUSD, cptyOrg, fiTpforCpty, bidRates[0], spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, fiLe, cptyOrg, fiTpforCpty );
            double dailylimit3 = getDailyAvailableCreditLimit( fiTpforCpty, spotDate.addDays( 2 ) );
            double grossLimit3 = getGrossNotionalAvailableCreditLimit( fiTpforCpty, spotDate.addDays( 2 ) );
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : spot + 2 daily credit limit  after take Credit : " + dailylimit3 + " gross limit : " + grossLimit3 + " success : " + cwm3.getStatus() );

            assertEquals( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : gross limit is unchanged :", true, Math.abs( grossLimit3 - grossLimit2 ) < MINIMUM );
            assertEquals( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : daily limit is unchanged :", true, Math.abs( dailylimit3 - spot2DailyLimit ) < MINIMUM );
            assertEquals( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : status :  ", cwm3.getStatus(), MessageStatus.FAILURE );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit", e );
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( fiOrg, fiTpforCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testMultiCreditUndoCredit()
    {

    }

    public void testMultiCreditUndoCreditFailure()
    {

    }

    public void testMultiCreditUpdateCredit()
    {

    }

    public void testMultiCreditCheckCredit()
    {

    }

    public void testMultiCreditCheckCreditFailure()
    {

    }

    public void testMultiCreditReserve()
    {

    }

    public void testMultiCreditReserveFailure()
    {

    }

    public void testMultiCreditUndoReserve()
    {

    }

    public void testMultiCreditRuleTakeCreditExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditRuleTakeCredit();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCredit", e );
            fail();
        }
    }

    public void testMultiCreditRuleTakeCreditFailure_No_FXRateExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditRuleTakeCreditFailure_No_FXRate();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCreditFailure_No_FXRate", e );
            fail();
        }
    }

    public void testMultiCreditRuleTakeCreditFailure_No_CreditUtilizationExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditRuleTakeCreditFailure_No_CreditUtilization();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization", e );
            fail();
        }
    }

    public void testMultiCreditRuleTakeCreditFailure_DailyInsufficientCreditExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit", e );
            fail();
        }
    }

    public void testMultiCreditUndoCreditExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditUndoCredit();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditUndoCreditExternalTx", e );
            fail();
        }
    }

    public void testMultiCreditUndoCreditFailureExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditUndoCreditFailure();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditUndoCreditFailureExternalTx", e );
            fail();
        }
    }

    public void testMultiCreditUpdateCreditExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditUpdateCredit();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditUpdateCreditExternalTx", e );
            fail();
        }
    }

    public void testMultiCreditCheckCreditExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditCheckCredit();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditCheckCreditExternalTx", e );
            fail();
        }
    }

    public void testMultiCreditCheckCreditFailureExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditCheckCreditFailure();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditCheckCreditFailureExternalTx", e );
            fail();
        }
    }

    public void testMultiCreditReserveExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditReserve();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditReserveExternalTx", e );
            fail();
        }
    }

    public void testMultiCreditReserveFailureExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditReserveFailure();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditReserveFailureExternalTx", e );
            fail();
        }
    }

    public void testMultiCreditUndoReserveExternalTx()
    {
        try
        {
            init( fiUser );
            IdcTransaction tx = initTransaction( true );
            testMultiCreditUndoReserve();
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "testMultiCreditUndoReserveExternalTx", e );
            fail();
        }
    }

    public void testSingleRulePublishCreditLimits()
    {
        try
        {
            init( lpUser );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribe( lpLe, lpTpForDd, ddOrg, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribe( lpLe, lpTpForDd, ddOrg, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertEquals("Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions(lpLe, lpTpForDd);
        }
    }

    public void testBilateralCreditTakeWithBothSuccess()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("EUR"), CurrencyFactory.getCurrency("USD"));
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate(lpLe, fiLe, CurrencyFactory.getCurrency("GBP"), CurrencyFactory.getCurrency("JPY"));

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit(fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

        }
    }

    public void testBilateralCreditTakeWitBothSuccessExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            testBilateralCreditTakeWithBothSuccess();
            commitTransaction(tx);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditTakeWithSecondFailure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );
            double lowerLimit = 1000000;
            double higherLimit = 2000000;

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, higherLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, higherLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, lowerLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, lowerLimit );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            Double suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi ).getSuspensionPercentage();
            Double totalLimit = getTotalCreditLimit( lpOrg, lpTpForFi );
            Double bufferLimitForLPCreditProvider = totalLimit * ( 1 - suspensionPercent / 100 );
            suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForLp ).getSuspensionPercentage();
            totalLimit = getTotalCreditLimit( fiOrg, fiTpForLp );
            Double bufferLimitForFICreditProvider = totalLimit * ( 1 - suspensionPercent / 100 );
            Double bufferLimit = availableLpLimit.getAmount() < availableFiLimit.getAmount() ? bufferLimitForLPCreditProvider : bufferLimitForFICreditProvider;

            double tradeAmt = availableLimit.getAmount() + ( bufferLimit + 200 );
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit + ",tradeAmt=" + tradeAmt );
            Trade trade = prepareSingleLegTrade( tradeAmt, false, CurrencyFactory.getCurrency( "EUR" ).isSameAs( availableLimit.getInstrument() ) ? true : false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message should have failure status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 );
            assertEquals( "availableLpLimit should be same.", availableLpLimit.getAmount(), availableLpLimit1.getAmount() );
            assertEquals( "availableFiLimit should be same.", availableFiLimit.getAmount(), availableFiLimit1.getAmount() );
            assertEquals( "availableLimit should be same.", availableLimit.getAmount(), availableLimit1.getAmount() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testBilateralCreditTakeWithSecondFailureExernalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            testBilateralCreditTakeWithSecondFailure();
            commitTransaction(tx);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditUndo()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Undo Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );

            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 );
            assertEquals( "availableLpLimit should be same.", availableLpLimit.getAmount(), availableLpLimit1.getAmount() );
            assertEquals( "availableFiLimit should be same.", availableFiLimit.getAmount(), availableFiLimit1.getAmount() );
            assertEquals( "availableLimit should be same.", availableLimit.getAmount(), availableLimit1.getAmount() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testBilateralCreditUndoNew()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            CreditWorkflowMessage cwm1 = creditMgr.undoCreditNew( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Undo Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );

            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 );
            assertEquals( "availableLpLimit should be same.", availableLpLimit.getAmount(), availableLpLimit1.getAmount() );
            assertEquals( "availableFiLimit should be same.", availableFiLimit.getAmount(), availableFiLimit1.getAmount() );
            assertEquals( "availableLimit should be same.", availableLimit.getAmount(), availableLimit1.getAmount() );
        }
        catch ( Exception e )
        {
            fail ("testBilateralCreditUndoNew", e );
        }
        finally
        {
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }


    public void testBilateralCreditUndoExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            testBilateralCreditUndo();
            commitTransaction(tx);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditTakeWithBothSuccessOnExternalTxFailure()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            testBilateralCreditTakeWithBothSuccess();

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            double fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate );
            double fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate );
            double lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate );
            double lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate );
            log( "After credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt );
            assertTrue( fiDailyUsedAmt > 0.0 );
            assertTrue( fiAggregateUsedAmt > 0.0 );
            assertTrue( lpDailyUsedAmt > 0.0 );
            assertTrue( lpAggregateUsedAmt > 0.0 );

            // now release the transaction so that utilizations has to be removed.
            releaseTransaction( tx );
            sleepFor( 2000 );

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate );
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate );
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate );
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate );
            log( "After transaction release. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt );
            assertEquals( fiDailyUsedAmt, 0.0 );
            assertEquals( fiAggregateUsedAmt, 0.0 );
            assertEquals( lpDailyUsedAmt, 0.0 );
            assertEquals( lpAggregateUsedAmt, 0.0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditTakeWithSecondFailureOnExternalTxFailure()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            testBilateralCreditTakeWithSecondFailure();

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            double fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate );
            double fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate );
            double lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate );
            double lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate );
            log( "After credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt );
            assertEquals(fiDailyUsedAmt, 0.0);
            assertEquals(fiAggregateUsedAmt, 0.0);
            assertEquals(lpDailyUsedAmt, 0.0);
            assertEquals(lpAggregateUsedAmt, 0.0);

            // now release the transaction so that utilizations has to be removed.
            releaseTransaction( tx );
            sleepFor( 2000 );

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate );
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate );
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate );
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate );
            log( "After transaction release. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt );
            assertEquals( fiDailyUsedAmt, 0.0 );
            assertEquals( fiAggregateUsedAmt, 0.0 );
            assertEquals( lpDailyUsedAmt, 0.0 );
            assertEquals( lpAggregateUsedAmt, 0.0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditUndoOnExternalTxFailure()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            testBilateralCreditUndo();

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            double fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate );
            double fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate );
            double lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate );
            double lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate );
            log( "After credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt );
            assertEquals( fiDailyUsedAmt, 0.0 );
            assertEquals( fiAggregateUsedAmt, 0.0 );
            assertEquals( lpDailyUsedAmt, 0.0 );
            assertEquals( lpAggregateUsedAmt, 0.0 );

            // now release the transaction so that utilizations has to be removed.
            releaseTransaction( tx );
            sleepFor( 2000 );

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate );
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate );
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate );
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate );
            log( "After transaction release. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt );
            assertEquals( fiDailyUsedAmt, 0.0 );
            assertEquals( fiAggregateUsedAmt, 0.0 );
            assertEquals( lpDailyUsedAmt, 0.0 );
            assertEquals( lpAggregateUsedAmt, 0.0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditUpdate()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit );

            // change the trade amount
            double offset = 500;
            updateTradeAmount( trade, tradeAmt + offset, true, false );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testBilateralCreditUpdateExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            testBilateralCreditUpdate();
            commitTransaction(tx);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditUtilizationAmountUpdate()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );

            //update Credit utilization amount before take credit should fail
            CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, trade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Undo Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );

            //do update Credit utilization amount after take credit for single-leg trade should pass
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit );
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit );

            // change the trade amount
            double offset = -500;
            updateTradeAmount( trade, tradeAmt + offset, true, false );

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, trade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 );
            log( "avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1 );
            assertEquals( "aggregate LP limits should be sum of offset", Math.abs( avaliableLpAggLimit - avaliableLpAggLimit1 - offset ) < CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "aggregate FI limits should be sum of offset", Math.abs( avaliableFiAggLimit - avaliableFiAggLimit1 - offset ) < CREDIT_CALCULATION_MINIMUM, true );

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false );
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false );

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() - offset ) < CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() - offset ) < CREDIT_CALCULATION_MINIMUM, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testUndoBilateralCreditWithBothCreditDisabled()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            creditAdminSvc.setCreditEnabled( lpOrg, false );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false );
            creditAdminSvc.setCreditEnabled( fiOrg, false );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, false );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Undo Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Undo Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );
            assertEquals( "Message error.", ( ( ErrorMessage ) cwm1.getErrors().toArray()[0] ).getCode(), CreditLimit.ERROR_CREDIT_LIMIT_WORKFLOW_STATE_UNAVAILABLE );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            creditAdminSvc.setCreditEnabled(fiOrg, true);
            creditAdminSvc.setCreditEnabled(fiOrg, fiTpForLp, true);

        }
    }


    public void testCreditUtilizationAmountUpdateOnSwap()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            creditAdminSvc.setCreditEnabled( fiOrg, true );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double nearTradeAmt = 1000;
            double farTradeAmt = 2000;
            Trade trade = prepareSwapTrade( nearTradeAmt, farTradeAmt, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );

            // take credit for swap trade should pass
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            // available amount on both near and far spot dates before Update of util amount
            double availableLpNearLimit = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double availableFiNearLimit = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpNearLimit=" + availableLpNearLimit + ",availableFiNearLimit=" + availableFiNearLimit + ",availableLimit=" + availableLimit );

            double availableLpFarLimit = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION );
            double availableFiFarLimit = getAvailableCreditLimit( fiTpForLp, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "availableLpFarLimit=" + availableLpFarLimit + ",availableFiFarLimit=" + availableFiFarLimit );

            // change the trade amount
            double nearLegOffset = 1000;
            double farLegOffset = 2000;
            updateSwapTradeAmount( trade, nearTradeAmt + nearLegOffset, farTradeAmt + farLegOffset, true, false );
            FXMarketDataElement fxMde = staticMds.findSpotConversionMarketDataElement( CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ), true );
            double spread = fxMde.getFXPrice().getOfferFXRate().getSpotRate() - fxMde.getFXPrice().getBidFXRate().getSpotRate();
            double spreadOffset = ( farTradeAmt + farLegOffset ) * spread;
            log( "spreadOffset=" + spreadOffset );

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, trade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS );

            // available amount on both near and far spot dates after Update of util amount
            double availableLpNearLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double availableFiNearLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpNearLimit1=" + availableLpNearLimit1 + ",availableFiNearLimit1=" + availableFiNearLimit1 + ",availableLimit1=" + availableLimit1 );

            assertEquals( "availableLpNearLimit should not be same.", Math.abs( availableLpNearLimit - availableLpNearLimit1 - nearLegOffset ) < CREDIT_CALCULATION_MINIMUM + spreadOffset, true );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiNearLimit - availableFiNearLimit1 - nearLegOffset ) < CREDIT_CALCULATION_MINIMUM + spreadOffset, true );

            double availableLpFarLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION );
            double availableFiFarLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "availableLpFarLimit1=" + availableLpFarLimit1 + ",availableFiFarLimit1=" + availableFiFarLimit1 );

            assertEquals( "availableLpFarLimit should not be same.", Math.abs( availableLpFarLimit - availableLpFarLimit1 - farLegOffset ) < CREDIT_CALCULATION_MINIMUM + spreadOffset, true );
            assertEquals( "availableFiFarLimit should not be same.", Math.abs( availableFiFarLimit - availableFiFarLimit1 - farLegOffset ) < CREDIT_CALCULATION_MINIMUM + spreadOffset, true );

            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);
        }
    }

    public void testMultiFillCreditUtilizationExclTradeInDealCollection()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade originalTrade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);

            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, originalTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument(lpLe, lpTpForFi, fiOrg, spotDate);
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument(fiLe, fiTpForLp, lpOrg, spotDate);
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument(lpLe, fiLe, spotDate);
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit );
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit );

            // create fill trades
            double fillAmt1 = 200;
            double fillAmt2 = 200;
            Trade fillTrade1 = prepareSingleLegTrade(fillAmt1, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );

            Collection<Trade> fillTradeColl = new ArrayList<Trade>();
            fillTradeColl.add( fillTrade1 );
            fillTradeColl.add(fillTrade2);

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit(fiLe, lpLe, originalTrade, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument(lpLe, lpTpForFi, fiOrg, spotDate);
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument(fiLe, fiTpForLp, lpOrg, spotDate);
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            log("availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1);
            log("avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1);
            /*assertEquals( "aggregate LP limits should be sum of offset", avaliableLpAggLimit == avaliableLpAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true );
            assertEquals( "aggregate FI limits should be sum of offset", avaliableFiAggLimit == avaliableFiAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true );*/

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );
            assertEquals("availableFiLimit should not be same.", Math.abs(availableFiLimit.getAmount() - availableFiLimit1.getAmount()) > CREDIT_CALCULATION_MINIMUM, true);
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testMultiFillCreditUtilizationInclTradeInDealCollection()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade originalTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );

            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, originalTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit );
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit );

            // create fill trades
            double fillAmt1 = 200;
            double fillAmt2 = 200;
            updateTradeAmount( originalTrade, fillAmt1, true, false );
            Trade fillTrade1 = prepareSingleLegTrade( fillAmt2, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );

            Collection<Trade> fillTradeColl = new ArrayList<Trade>();
            // add original trade also to the collection
            fillTradeColl.add( originalTrade );
            fillTradeColl.add( fillTrade1 );

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit( fiLe, lpLe, originalTrade, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 );
            log( "avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1 );
            /*assertEquals( "aggregate LP limits should be sum of offset", avaliableLpAggLimit == avaliableLpAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true );
            assertEquals( "aggregate FI limits should be sum of offset", avaliableFiAggLimit == avaliableFiAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true );*/

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testMultiFillCreditUtilizationInclTradeInDealCollectionExternalTx()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade originalTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );

            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, originalTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit );
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit );

            // create fill trades
            double fillAmt1 = 200;
            double fillAmt2 = 200;
            updateTradeAmount( originalTrade, fillAmt1, true, false );
            Trade fillTrade1 = prepareSingleLegTrade( fillAmt2, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );

            Collection<Trade> fillTradeColl = new ArrayList<Trade>();
            // add original trade also to the collection
            fillTradeColl.add( originalTrade );
            fillTradeColl.add( fillTrade1 );

            IdcTransaction tx = initTransaction( true );
            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit( fiLe, lpLe, originalTrade, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            commitTransaction( tx );

            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 );
            log( "avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1 );
            /*assertEquals( "aggregate LP limits should be sum of offset", avaliableLpAggLimit == avaliableLpAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true );
            assertEquals( "aggregate FI limits should be sum of offset", avaliableFiAggLimit == avaliableFiAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true );*/

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true );

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testInMemoryCreditUtilizationUpdate()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );


            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, fiOrg, lpTpForFi );
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSimpleCreditWorkflow()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            Collection<CreditUtilization> creditUtils = CreditUtilizationManagerC.getInstance().getCreditUtilizations( lpLe, fiOrg, lpTpForFi, spotDate, CreditLimitConstants.TRADE_EVENT );
            for ( CreditUtilization cu : creditUtils )
            {
                log( "credit util before credit take=" + cu + ",usedAmt=" + cu.getUsedAmount() + ",availableAmt=" + cu.getAvailableMarginReserveAmount() );
            }
            double availableAmtBeforeCreditTake = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "creditUtils=" + creditUtils + ",availableAmtBeforeCreditTake=" + availableAmtBeforeCreditTake );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                log( "credit util from the workflow message=" + cue.getCreditUtilization() + ",usedAmt=" + cue.getCreditUtilization().getUsedAmount() + ",availableAmt=" + cue.getCreditUtilization().getAvailableMarginReserveAmount() );
            }
            Collection<CreditUtilization> creditUtilsAfter = CreditUtilizationManagerC.getInstance().getCreditUtilizations( lpLe, fiOrg, lpTpForFi, spotDate, CreditLimitConstants.TRADE_EVENT );
            double availableAmtAfterCreditTake = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "creditUtilsAfter=" + creditUtilsAfter + ",availableAmtAfterCreditTake=" + availableAmtAfterCreditTake );
            for ( CreditUtilization cu : creditUtilsAfter )
            {
                log( "credit util after credit take=" + cu + ",usedAmt=" + cu.getUsedAmount() + ",availableAmt=" + cu.getAvailableMarginReserveAmount() );
            }
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            sleepFor(5000);
            Collection<CreditUtilization> creditUtilsAfterPeriodicUpdate = CreditUtilizationManagerC.getInstance().getCreditUtilizations(lpLe, fiOrg, lpTpForFi, spotDate, CreditLimitConstants.RATE_QUALIFICATION_EVENT);
            for ( CreditUtilization cu : creditUtilsAfterPeriodicUpdate )
            {
                log( "credit util after periodic update=" + cu + ",usedAmt=" + cu.getUsedAmount() + ",availableAmt=" + cu.getAvailableMarginReserveAmount() );
            }
            double availableAmtAfterPeriodicUpdate = getAvailableCreditLimit(lpTpForFi, spotDate);
            log("creditUtilsAfterPeriodicUpdate=" + creditUtilsAfterPeriodicUpdate + ",availableAmtAfterPeriodicUpdate=" + availableAmtAfterPeriodicUpdate);
        }
        catch ( Exception e )
        {
            fail();
            e.printStackTrace();
        }
    }

    public void testTradingSuspension()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            double newDailyLimit = 1000000;
            double newGrossLimit = 2000000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, newDailyLimit );
            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, newGrossLimit);

            double newSuspensionPercentage = 90;
            creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForFi, newSuspensionPercentage );
            double tradeAmt = newDailyLimit + ( 200 );
            Trade trade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            double newTradeAmt = newDailyLimit;
            Trade trade1 = prepareSingleLegTrade( newTradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit(trade1, lpLe, fiOrg, lpTpForFi);
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS );

            // release the limit
            creditMgr.undoCredit(trade1, lpLe, fiOrg, lpTpForFi);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditRelationshipChanges()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            // initialize the credit enabled flag
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            creditAdminSvc.setCreditEnabled( fiOrg, true );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            //remove the trading relation
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.removeCreditRelationship( fiOrg, fiTpForLp );

            //populate enable credit
            CreditUtilizationCache creditCache = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache();
            boolean isCreditEnabled = creditCache.isCreditEnabled( lpOrg, lpTpForFi );
            assertEquals( "With no trading relation creditEnable should be false.", false, isCreditEnabled );

            //establish the trading relationship
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );

            //reinit should set enable credit to true but the map should still contain false
            boolean isCreditEnabledAfterReinit = creditCache.isCreditEnabled( lpOrg, lpTpForFi );
            assertEquals( "After trading relation set and reinit creditEnable should be true.", true, isCreditEnabledAfterReinit );
        }
        catch ( Exception e )
        {
            fail( "testCreditRelationshipChanges", e );
        }
        finally
        {
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.establishCreditRelationship( fiOrg, fiTpForLp );
        }
    }


    public void testCreditSuccessWIthLessSevereBreach()
    {
        try
        {
            init(lpUser);
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            double limit = ********;   // 10M

            Collection<CreditUtilizationCalculator> supportedCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, limit);  // disable the daily credit limits
            for ( CreditUtilizationCalculator calculator : supportedCalcs )
            {
                log( "$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ Test credit limit breach for " + calculator );
                removeExistingCreditUtilizationEvents( lpOrg );
                removeExistingCreditUtilizationEvents( fiOrg );

                limit = ********;
                setCalcAndLimit( lpOrg, lpTpForFi, CreditUtilC.getCreditLimitClassification( calculator ), calculator, limit );
                creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForFi, 100 );

                double availableAmtBeforeCreditTake = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "****************** availableAmtBeforeCreditTake=" + availableAmtBeforeCreditTake );

                // do a trade to exhaust almost entire credit limit
                double tradeAmt = limit;
                Trade trade = prepareSingleLegTrade( tradeAmt * 0.95, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                // the new available limit should be very close to zero limits
                double availableAmtAfterCreditTake = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmtAfterCreditTake=" + availableAmtAfterCreditTake );

                //now set the limit to lower limits
                limit = 8000000;   // 8M
                setCalcAndLimit( lpOrg, lpTpForFi, CreditUtilC.getCreditLimitClassification( calculator ), calculator, limit );

                // with the new reduded limtis the available limit should be -ve
                double availableAmtAfterLimitChangeBeforeCredit = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmtAfterLimitChangeBeforeCredit=" + availableAmtAfterLimitChangeBeforeCredit );
                assertEquals( "After the limit change, the available should be negative.", availableAmtAfterLimitChangeBeforeCredit < 0, true );

                //do another trade which redues the utilization amount based on the netting
                tradeAmt = 1000000; // 1M
                trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
                cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );

                double availableAmtAfterLimitChangeAfterCredit = getAvailableCreditLimit( lpTpForFi, spotDate );
                log( "availableAmtAfterLimitChangeAfterCredit=" + availableAmtAfterLimitChangeAfterCredit );

                if ( CreditUtilC.isNettingCalculator( calculator ) )
                {
                    log( "Nettting calculator used." );
                    if ( availableAmtAfterLimitChangeAfterCredit > availableAmtAfterLimitChangeBeforeCredit )
                    {
                        assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
                    }
                    else
                    {
                        assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );
                    }
                }
                else
                {
                    log( "No Nettting calculator used. The trade should always reject" );
                    assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDisabledCreditLimitRules()
    {
        double newDailyLimit = 1000000;
        double newGrossLimit = 2000000;
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, newDailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, newGrossLimit );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            assertEquals( "Message failure status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, newDailyLimit);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, newGrossLimit );
        }
    }

    public void testObsoleteMaturityDates()
    {
        try
        {
            init(lpUser);
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            double newDailyLimit = 1000000;
            double newGrossLimit = 2000000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, newDailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, newGrossLimit );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.subtractDays( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            assertEquals("Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testMultiCreditRuleTakeCreditOnSwapTrade()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            double limitOnNearDate = getAvailableCreditLimit( lpTpForFi, spotDate );
            double limitOnFarDate = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 2 ) );
            log( "near date credit limit before take Credit: " + limitOnNearDate );
            log( "far date credit limit before take Credit : " + limitOnFarDate );

            IdcTransaction tx = initTransaction( true );
            double tradeAmt = 1000;
            Trade trade = prepareSwapTrade( tradeAmt, tradeAmt, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            commitTransaction( tx );
            double limit1OnNearDate = getAvailableCreditLimit( lpTpForFi, spotDate );
            double limit1OnFarDate = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 2 ) );
            log( "near date credit limit  after take Credit : " + limit1OnNearDate + " success : " + cwm.getStatus() );
            log( "far date credit limit  after take Credit : " + limit1OnFarDate + " success : " + cwm.getStatus() );
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "1000 was taken :", true, Math.abs( limitOnNearDate - limit1OnNearDate - tradeAmt - tradeAmt ) < MINIMUM );
            assertEquals( "1000 was taken :", true, Math.abs( limitOnFarDate - limit1OnFarDate - tradeAmt - tradeAmt ) < MINIMUM );
            log( "credit utilization events=" + cwm.getCreditUtilizationEvents() );
            assertEquals( "There should be 4 credit utilization events.", cwm.getCreditUtilizationEvents().size(), 4 );
            validateCreditUtilizationEvents( cwm );
            sleepFor( 3000 );

            // check credit utilizations.
            Set<CreditUtilization> utils = new HashSet<CreditUtilization>( 4 );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                utils.add( cue.getCreditUtilization() );
            }

            for ( CreditUtilization cu : utils )
            {
                log( "before refresh cu=" + cu + ",ccyPos=" + cu.getCurrencyPositions() + ",cu.usedAmt=" + cu.getUsedAmount() );
                double realtimeUsed = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( cu );
                log( "before refresh, realtime used amt=" + realtimeUsed + ",usedAmt=" + cu.getUsedAmount() );
                assertEquals( "before refresh, realtime used amt and used amt should be equal. realTimeUsed=" + realtimeUsed + ",cu.usedAmt=" + cu.getUsedAmount(), Math.abs( realtimeUsed - cu.getUsedAmount() ) < CREDIT_CALCULATION_MINIMUM, true );

                cu.resetCurrencyPositions( "Test", true );
                cu = ( CreditUtilization ) PersistenceFactory.newSession().refreshObject( cu );
                log( "after refresh cu=" + cu + ",ccyPos=" + cu.getCurrencyPositions() + ",cu.usedAmt=" + cu.getUsedAmount() );
                double realtimeUsed1 = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( cu );
                log( "after refresh, realtime used amt1=" + realtimeUsed1 + ",usedAmt=" + cu.getUsedAmount() );
                assertEquals( "after refresh, realtime used amt and used amt should be equal. realTimeUsed=" + realtimeUsed + ",cu.usedAmt=" + cu.getUsedAmount(), Math.abs( realtimeUsed1 - cu.getUsedAmount() ) < CREDIT_CALCULATION_MINIMUM, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditUtilizationEventReadObject()
    {
        Collection cues = new ArrayList();
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) CreditLimitFactory.newCreditUtilizationEvent().getRegisteredObject();
            cue.setNamespace( lpUser.getNamespace() );
            cue.setUsedAmount( 1000 );
            cues.add( cue );
            commitTransaction( tx );

            // get the credit utilization event.
            CreditUtilizationEvent cue1 = ( CreditUtilizationEvent ) cues.toArray()[0];
            log( "cue from cache=" + cue1 );

            CreditUtilizationEvent cue2 = ( CreditUtilizationEvent ) CreditUtilC.readObject( cue1 );
            log( "cue after read object=" + cue2 );

            IdcTransaction tx1 = initTransaction( true );
            CreditUtilizationEvent registeredCue = ( CreditUtilizationEvent ) cue2.getRegisteredObject();
            registeredCue.setUsedAmount( 2000 );
            commitTransaction( tx1 );
            log( "cue after commit=" + cue2 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditTakeWithExcessCreditUtilizationAllowed()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            Trade trade = prepareSingleLegTrade( 30000000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testBilateralCreditTradingPartyValidation()
    {
        try
        {
            init( fiUser );

            Trade trade = prepareSingleLegTrade( 30000000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, ddLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );
            assertEquals("Message error.", ((ErrorMessage) cwm.getErrors().toArray()[0]).getCode(), CreditLimit.ERROR_INVALID_TRADING_PARTY);
            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( fiLe, ddLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals("Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE);
        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditTradingPartyValidation", e );
        }
    }

    public void testBilateralCreditMaxTenorValidationFailure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // set the maxTenor limit to 5 days and value date 10 days
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, new Tenor( "5D" ) );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "credit limit before take Credit: " + limitBefore );

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );
            //todo: cwm sets the error code from first cwm - not aggregate results
            //assertEquals( "Message error.", ( ( ErrorMessage ) cwm.getErrors().toArray()[0] ).getCode(), CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR );
            assertEquals( "The credit limit should not be changed.", true, Math.abs( limitBefore - limitAfter ) < MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditMaxTenorValidationFailure", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testBilateralCreditMinTenorValidationFailure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // set the minTenor limit to 5 days and value date as spot
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("5D"));

            double limitBefore = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "credit limit before take Credit: " + limitBefore );

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );
            assertEquals( "The credit limit should not be changed.", true, Math.abs( limitBefore - limitAfter ) < MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditMaxTenorValidationFailure", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, null);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testBilateralCreditTakeSubscriptionsWithOptimization()
    {
        try
        {
            testBilateralCreditTakeSubscriptionsWithOptimization( true );
            Thread.sleep( 5000 );
            testBilateralCreditTakeSubscriptionsWithOptimization(false);
        }
        catch ( Exception e )
        {

        }
    }

    private void testBilateralCreditTakeSubscriptionsWithOptimization( boolean creditEnabled )
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" );
        Currency USD = CurrencyFactory.getCurrency( "USD" );
        Currency CAD = CurrencyFactory.getCurrency( "CAD" );
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 200000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, ********0 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled( lpOrg, creditEnabled );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, creditEnabled );
            creditAdminSvc.setCreditEnabled( fiOrg, creditEnabled );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, creditEnabled );

            IdcDate eurUSDSpotDate = CreditUtilC.getSpotDate( EUR, USD ) == null ? spotDate : CreditUtilC.getSpotDate( EUR, USD );
            IdcDate usdCADSpotDate = CreditUtilC.getSpotDate( USD, CAD ) == null ? spotDate : CreditUtilC.getSpotDate( USD, CAD );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true, null );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, USD, CAD, true, true, null );
            Thread.sleep( 10000 );

            double[] bidLimit = new double[]{100000}, offerLimit = new double[]{100000};
            double origBidLimit = bidLimit[0], origOfferLimit = offerLimit[0];
            boolean enable;
            enable = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            assertEquals( "The bilateral credit enabled should match.", enable, creditEnabled );
            if ( creditEnabled )
            {
                assertEquals ( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit < bidLimit[ 0 ], true );
                assertEquals ( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit < offerLimit[ 0 ], true );
            }
            else
            {
                assertEquals ( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit == bidLimit[ 0 ], true );
                assertEquals ( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit == offerLimit[ 0 ], true );
            }

            bidLimit = new double[]{2000000};
            offerLimit = new double[]{2000000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];
            enable = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true );
            assertEquals( "The bilateral credit enabled should match.", enable, creditEnabled );
            if ( creditEnabled )
            {
                assertEquals ( "The available bid limit for EUR/USD should not be same as quote limits.", origBidLimit > bidLimit[ 0 ], true );
                assertEquals ( "The available offer limit for EUR/USD should not be same as quote limits.", origOfferLimit > offerLimit[ 0 ], true );
            }
            else
            {
                assertEquals ( "The available bid limit for EUR/USD should not be same as quote limits.", origBidLimit == bidLimit[ 0 ], true );
                assertEquals ( "The available offer limit for EUR/USD should not be same as quote limits.", origOfferLimit == offerLimit[ 0 ], true );
            }

            //repeat the same test for USD/CAD ccy pair
            bidLimit = new double[]{100000};
            offerLimit = new double[]{100000};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];
            enable = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, usdCADSpotDate, USD, CAD, true, bidLimit, offerLimit, true );
            assertEquals( "The bilateral credit enabled should match.", enable, creditEnabled );
            if ( creditEnabled )
            {
                assertEquals ( "The available bid limit for USD/CAD should be same as quote limits.", origBidLimit < bidLimit[ 0 ], true );
                assertEquals ( "The available offer limit for USD/CAD should be same as quote limits.", origOfferLimit <  offerLimit[ 0 ], true );
            }
            else
            {
                assertEquals ( "The available bid limit for USD/CAD should be same as quote limits.", origBidLimit == bidLimit[ 0 ], true );
                assertEquals ( "The available offer limit for USD/CAD should be same as quote limits.", origOfferLimit == offerLimit[ 0 ], true );
            }

            bidLimit = new double[]{********00};
            offerLimit = new double[]{********00};
            origBidLimit = bidLimit[0];
            origOfferLimit = offerLimit[0];
            enable = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, usdCADSpotDate, USD, CAD, true, bidLimit, offerLimit, true );
            assertEquals( "The bilateral credit enabled should match.", enable, creditEnabled );
            if ( creditEnabled )
            {
                assertEquals ( "The available bid limit for USD/CAD should not be same as quote limits.", origBidLimit >  bidLimit[ 0 ], true );
                assertEquals ( "The available offer limit for USD/CAD should not be same as quote limits.", origOfferLimit > offerLimit[ 0 ], true );
            }
            else
            {
                assertEquals ( "The available bid limit for USD/CAD should not be same as quote limits.", origBidLimit == bidLimit[ 0 ], true );
                assertEquals ( "The available offer limit for USD/CAD should not be same as quote limits.", origOfferLimit == offerLimit[ 0 ], true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true );
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, USD, CAD, true, true );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled(lpOrg, lpTpForFi, true);
            creditAdminSvc.setCreditEnabled(fiOrg, true);
            creditAdminSvc.setCreditEnabled(fiOrg, fiTpForLp, true);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testCreditSubscriptionCleanupEOD()
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" );
        Currency USD = CurrencyFactory.getCurrency( "USD" );
        Currency CAD = CurrencyFactory.getCurrency( "CAD" );
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            // 1. add credit limit subscriptions for a few currency pairs across diff value dates
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true, null );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, USD, CAD, true, true, null );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate(lpLe, fiLe, EUR, USD, true, true);
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate(lpLe, fiLe, USD, CAD, true, true);
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testRebookWithOrgLevelExposure()
    {
        try
        {
            //set org level exposure
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled( lpOrg, false );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false );
            creditAdminSvc.setCreditEnabled( fiOrg, false );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, false );


            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // do trade and check credit
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double dailyFILimit0 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLPLimit0 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double aggregateFILimit0 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLPLimit0 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            IdcTransaction tx = initTransaction( true );
            Trade regTrade = ( Trade ) trade.getRegisteredObject();
            // rebook the trade with a diff le
            creditMgr.undoBilateralCredit( fiLe, lpLe, regTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            FXSingleLeg sLeg = ( FXSingleLeg ) trade;
            sLeg.setCounterpartyB( lp2Le );
            cwm = creditMgr.takeBilateralCredit( fiLe, lp2Le, regTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            // check credit and it shouldnt change
            double dailyFILimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLPLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double aggregateFILimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLPLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals( "dailyFILimit0=" + dailyFILimit0 + ",dailyFILimit1=" + dailyFILimit1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyFILimit0 - dailyFILimit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLPLimit0=" + dailyLPLimit0 + ",dailyLPLimit1=" + dailyLPLimit1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLPLimit0 - dailyLPLimit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateFILimit0=" + aggregateFILimit0 + ",aggregateFILimit1=" + aggregateFILimit1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateFILimit0 - aggregateFILimit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLPLimit0=" + aggregateLPLimit0 + ",aggregateLPLimit1=" + aggregateLPLimit1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLPLimit0 - aggregateLPLimit1 ) < CREDIT_CALCULATION_MINIMUM );
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error("testMultiCreditRuleTakeCredit", e);
            fail();
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled( lpOrg, false );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false );
            creditAdminSvc.setCreditEnabled( fiOrg, false );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, false );
            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000);
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000);
        }
    }

    public void testCreditUtilCacheSynchronizeNotificationWithStaleCcy()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            assertTrue( CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( lpOrg, lpTpForFi ) );
            assertTrue( CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( fiOrg, fiTpForLp ) );

            IdcTransaction tx = initTransaction( true );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            commitTransaction( tx );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( cwm.getStatus(), MessageStatus.SUCCESS );

            Set<CreditUtilization> utils = new HashSet<CreditUtilization>( 4 );


            // invoke remote notification functor to sync up the currency positions.
            HashMap<String, Object> propertiesMap = new HashMap<String, Object>( 4 );
            propertiesMap.put( RemoteTransactionNotification.REMOTE_ONLY_KEY, RemoteTransactionNotification.REMOTE_ONLY_VALUE );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cwm.getOrganization().getShortName() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cwm.getTradingParty().getShortName() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, cwm.getEventName() );
            setCreditUtilizationEventGuid( cwm, propertiesMap );

            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                CreditUtilization cu = cue.getCreditUtilization();
                if ( cu.getCreditLimitRule().getClassification().isSameAs( GROSS_NOTIONAL_CLASSIFICATION ) )
                {
                    cu.getCurrencyPositions().removeCreditUtilizationEvent( cue );
                }
                else if ( cu.getCreditLimitRule().getClassification().isSameAs( DAILY_SETTLEMENT_CLASSIFICATION ) )
                {
                    cu.getCurrencyPositions().removeCreditUtilizationEvent( cue );
                }
            }

            CreditUtilizationCacheSynchronizeNotificationFunctorC functor = new CreditUtilizationCacheSynchronizeNotificationFunctorC();
            functor.onCommit( propertiesMap );

        }
        catch ( Exception e )
        {
            fail( "testCreditUtilCacheSynchronizeNotificationWithStaleCcy", e );
        }
    }

    public void testLeLevelOverrideMinTenor()
    {
        try
        {
            init(lpUser);
            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);


            creditAdminSvc.setOrganizationExposureLevel(lpOrg, fiOrg);

            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit(fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled( fiOrg, false );

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            assertEquals("Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS);


            // set the minTenor limit to 5 days and value date as spot
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("5D"));

            Trade trade1 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);

            assertEquals("Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );

            // now set the legal entity level override.
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true );
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("TOD"), true);
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false );
            Trade trade2 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS);

            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("1Y"), true);
            Trade trade3 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE);

            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, null, true);
            Trade trade4 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm4.getStatus(), cwm4.getStatus(), MessageStatus.SUCCESS);


            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true);
            Trade trade5 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm5 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade5, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message status=" + cwm5.getStatus(), cwm5.getStatus(), MessageStatus.FAILURE);

            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false);
            Trade trade6 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade6, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message status=" + cwm6.getStatus(), cwm6.getStatus(), MessageStatus.FAILURE);

            // verify the null override behavior.
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, true);
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false);
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, null, true);
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("1W") );
            Trade trade7 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm7 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade7, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm7.getStatus(), cwm7.getStatus(), MessageStatus.SUCCESS);
        }
        catch ( Exception e )
        {
            fail( "testLeLevelOverrideMinTenor", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( fiOrg, true );
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, null, true);
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false);
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null);
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, fiOrg);
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
        }
    }

    public void testLeLevelOverrideMaxTenor()
    {
        try
        {
            init(lpUser);
            removeExistingCreditUtilizationEvents(lpOrg);
            removeExistingCreditUtilizationEvents(fiOrg);


            creditAdminSvc.setOrganizationExposureLevel(lpOrg, fiOrg);

            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit(fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled(fiOrg, false);

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            assertEquals("Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS);


            // set the minTenor limit to 5 days and value date as spot
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("TOD"));

            Trade trade1 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);

            assertEquals("Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );

            // now set the legal entity level override.
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true );
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("1Y"), true);
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false );
            Trade trade2 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS);

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("TOM"), true);
            Trade trade3 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE);

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null, true);
            Trade trade4 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm4.getStatus(), cwm4.getStatus(), MessageStatus.SUCCESS);


            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true);
            Trade trade5 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm5 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade5, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message status=" + cwm5.getStatus(), cwm5.getStatus(), MessageStatus.FAILURE);

            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false);
            Trade trade6 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade6, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message status=" + cwm6.getStatus(), cwm6.getStatus(), MessageStatus.FAILURE);

            // verify the null override behavior.
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, true);
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false);
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null, true);
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("TOD"));
            Trade trade7 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);
            CreditWorkflowMessage cwm7 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade7, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals("Message success status=" + cwm7.getStatus(), cwm7.getStatus(), MessageStatus.SUCCESS);
        }
        catch ( Exception e )
        {
            fail( "testLeLevelOverrideMaxTenor", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( fiOrg, true );
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null, true);
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false);
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null);
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, fiOrg);
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null);
        }
    }

    public void testBilateralCreditNoMethodology()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            // set all the credit methodologies to null.
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "cwm.status should be failure", MessageStatus.FAILURE, cwm.getStatus() );

            // unilateral credit also should fail.
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            log( "cwm1.status=" + cwm1.getStatus() );
            assertEquals( "cwm1.status should be failure", MessageStatus.FAILURE, cwm1.getStatus() );

            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade, fiLe, lpOrg, fiTpForLp );
            log( "cwm2.status=" + cwm2.getStatus() );
            assertEquals( "cwm2.status should be failure", MessageStatus.FAILURE, cwm2.getStatus() );
        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditNoMethodology", e );
        }
        finally
        {
            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000);
        }
    }

    public void testMinTenorInBusinessDaysAtLELevelExposure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit before take Credit: " + limitBefore );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() );
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtLELevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
        }
    }

    public void testMinTenorInBusinessDaysAtOrgLevelExposure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit before take Credit: " + limitBefore );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() );
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }

    public void testMinTenorInBusinessDaysAtOrgLevelExposureWithLEOverride()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, false );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor, true );
            creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit before take Credit: " + limitBefore );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() );
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtOrgLevelExposureWithLEOverride", e );
        }
        finally
        {
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, false );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null, true );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }

    public void testMaxTenorInBusinessDaysAtLELevelExposure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit before take Credit: " + limitBefore );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() );
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorInBusinessDaysAtLELevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
        }
    }

    public void testMaxTenorInBusinessDaysAtOrgLevelExposure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit before take Credit: " + limitBefore );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() );
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorInBusinessDaysAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }

    public void testMaxTenorInBusinessDaysAtOrgLevelExposureWithLEOverride()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, false );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor, true );
            creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true );

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit before take Credit: " + limitBefore );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) );
            log( "credit limit after take Credit: " + limitAfter );

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() );
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorInBusinessDaysAtOrgLevelExposureWithLEOverride", e );
        }
        finally
        {
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, false );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null, true );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }

    public void testCreditWorkflowsInDebugMode()
    {
        final int logLevel =  CreditLimit.creditCalcLog.getLevel();

        try
        {
            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000);
            setCalcAndLimit(fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000);

            sleepFor(2000);
            CreditLimit.creditCalcLog.setLevel(LogLevel.DEBUG);
            CreditLimit.creditCalcLog.updateFlags();
            LogFactory.getLog( CreditWorkflowManagerC.class ).setLevel(LogLevel.DEBUG);
            LogFactory.getLog(CreditWorkflowManagerC.class).updateFlags();
            init(fiUser);
            removeExistingCreditUtilizationEvents(fiOrg);
            removeExistingCreditUtilizationEvents(lpOrg);
            // execute various credit workflows here.
            executeCreditWorkflows(lpOrg, lpLe, fiOrg, lpTpForFi);
        }
        catch ( Exception e )
        {
            log.error( "testCreditWorkflowsInDebugMode", e );
            fail();
        }
        finally
        {
            CreditLimit.creditCalcLog.setLevel(logLevel );
            CreditLimit.creditCalcLog.updateFlags();
            LogFactory.getLog( CreditWorkflowManagerC.class ).setLevel(logLevel);
            LogFactory.getLog(CreditWorkflowManagerC.class).updateFlags();
        }
    }

    public void testSkipSettledTradesOnUndoCredit()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents ( lpOrg );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, ddLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "success=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );
            validateCreditUtilizationEvents( cwm );

            //update the event with old value date
            for ( CreditUtilizationEvent cue: cwm.getCreditUtilizationEvents () )
            {
                cue.setSettlementDate ( tradeDate.subtractDays ( 5 ) );
            }

            CreditWorkflowRiders riders = new CreditWorkflowRiders ();
            riders.setSkipSettledTradeUndoCredit ( true );
            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit ( lpLe, ddLe, trade, riders );
            assertEquals( "success=" + cwm1.getStatus(), MessageStatus.SUCCESS, cwm1.getStatus() );
        }
        catch ( Exception e )
        {
            fail ( "testSkipSettledTradesOnUndoCredit", e );
        }
    }

    public void testSkipSettledTradesOnUndoCredit_Account()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents ( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, ddLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "success=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );
            validateCreditUtilizationEvents( cwm );

            //update the event with old value date
            for ( CreditUtilizationEvent cue: cwm.getCreditUtilizationEvents () )
            {
                cue.setSettlementDate ( tradeDate.subtractDays ( 5 ) );
            }

            CreditWorkflowRiders riders = new CreditWorkflowRiders ();
            riders.setSkipSettledTradeUndoCredit ( true );
            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit ( lpLe, ddLe, trade, riders );
            assertEquals( "success=" + cwm1.getStatus(), MessageStatus.SUCCESS, cwm1.getStatus() );

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, tradeDate );
            CurrencyPosition eurPos = aggCu.getCurrencyPositions ().getCurrencyPosition ( CurrencyFactory.getCurrency ( "EUR" ) );
            assertTrue( eurPos.getNetAmount () == 0.0 );

            CurrencyPosition usdPos = aggCu.getCurrencyPositions ().getCurrencyPosition ( CurrencyFactory.getCurrency ( "USD" ) );
            assertTrue( usdPos.getNetAmount () == 0.0 );

        }
        catch ( Exception e )
        {
            fail ( "testSkipSettledTradesOnUndoCredit_Account", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
        }
    }

    private void setCreditUtilizationEventGuid( CreditWorkflowMessage cwm, Map<String, Object> propertiesMap )
    {
        int size = cwm.getCreditUtilizationEvents().size();
        long[] cuObjectIds = new long[size];
        String[] guids = new String[size];
        double ccySums[] = new double[size];
        double ccyAmtSums[] = new double[size];
        int index = 0;
        for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
        {
            cuObjectIds[index] = cue.getCreditUtilization().getObjectID();
            guids[index] = cue.getGUID();
            index++;
        }
        propertiesMap.put( CreditLimit.CREDIT_UTILIZATION, cuObjectIds );
        propertiesMap.put( CreditLimit.CREDIT_UTILIZATION_EVENT, guids );
    }

}
