package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationCalculator;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.Request;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.persistence.PersistenceFactory;
import junit.framework.Test;
import junit.framework.TestSuite;

import java.util.Collection;


/**
 * Tests various credit netting methodologies.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitCurrencyMarginNettingPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Currency Margin Test";

    public CreditLimitCurrencyMarginNettingPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testSimpleDailySettlementNetting" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementNettingWithDifferentValueDates" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementNettingWithFXRateConversion" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementNettingWithNoCreditLimitCurrency" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementReserve" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementReserveFailure" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementUndoCredit" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementMultipleTradesWithDifferentCurrenciesBuyTerm" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementMultipleTradesWithDifferentCurrenciesSellTerm" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementMultipleTradesWithDifferentCurrenciesBuyBase" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testDailySettlementMultipleTradesWithDifferentCurrenciesSellBase" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testSimpleNOPNetting" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testNOPNettingWithDifferentValueDates" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testNOPNettingWithFXRateConversion" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testNOPNettingWithNoCreditLimitCurrency" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testNOPReserve" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testNOPReserveFailure" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testNOPUndoCredit" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testSimpleAggregateNetPRSettlementNetting" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateNetPRSettlementWithDifferentValueDates" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateNetPRSettlementWithFXRateConversion" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateNetPRSettlementWithCreditLimitCurrency" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateNetPRSettlementReserve" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateNetPRSettlementReserveFailure" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateNetPRSettlementUndoCredit" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateLimitWithDifferentValueDates" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateLimitWithFXRateConversion" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateLimitWithNoCreditLimitCurrency" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateLimitReserve" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateLimitReserveFailure" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testIgnoreCurrentValueDatePositions" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testAggregateLimitUndoCredit" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testSimpleGrossDaily" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testGrossDailyWithDifferentValueDates" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testGrossDailyWithFXRateConversion" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testGrossDailyWithNoCreditLimitCurrency" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testGrossDailyReserve" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testGrossDailyReserveFailure" ) );
        suite.addTest( new CreditLimitCurrencyMarginNettingPTestC( "testGrossDailyUndoCredit" ) );
        return suite;
    }


    public void testSimpleDailySettlementNetting()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, 100.0 );
            creditAdminSvc.setApplyPandL( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, false );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 100000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );

            // now apply PandL
            initCurrencyPairRates( true );
            creditAdminSvc.setApplyPandL( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, true );

            // lp org sells EUR and buys USD.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            double eurAmt = tradeAmt / bidRates[0];
            double netPayable = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", eurAmt, false );
            double pandLFromCptyPerspective = netPayable - tradeAmt;
            log( "netReceivable=" + tradeAmt + ",netPayable=" + netPayable + ",pandL from counterparty perspective=" + pandLFromCptyPerspective );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit3 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm );
            sleepFor( 2000 );
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                CreditUtilization cu = ( CreditUtilization ) PersistenceFactory.newSession().refreshObject( cue.getCreditUtilization() );
                double sum = ( tradeAmt / 100 ) - cu.getUsedAmount() - pandLFromCptyPerspective;
                log( "cu.usedAmt=" + cu.getUsedAmount() + ",availableAmt=" + cu.getAvailableMarginReserveAmount() + ",sum=" + sum );
                assertEquals( "In this case counterparty had profit.", sum < CREDIT_CALCULATION_MINIMUM, true );
            }

            // Now take an offer side trade.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit4 + " success : " + cwm3.getStatus() );
            validateCreditUtilizationEvents( cwm3 );
            assertEquals( "before limit=" + limit3 + ",after limit=" + limit4, true, Math.abs( limit4 - limit2 ) < CREDIT_CALCULATION_MINIMUM );

            // lp org buys EUR and sells USD.
            Trade trade4 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            double eurAmt1 = tradeAmt / bidRates[0];
            double netPayable1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", eurAmt1, true );
            double pandLFromCptyPerspective1 = netPayable1 - tradeAmt;
            log( "netReceivable1=" + tradeAmt + ",netPayable1=" + netPayable1 + ",pandL1 from counterparty perspective=" + pandLFromCptyPerspective1 );
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd );
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit5 + " success : " + cwm4.getStatus() );
            validateCreditUtilizationEvents( cwm );
            sleepFor( 2000 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, 1.0 );
            initCurrencyPairRates( false );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testDailySettlementNettingWithDifferentValueDates()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "On Spot date, credit limit before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "On spot date, credit limit after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now do a trade on spot next date
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "On spot/next date, credit limit before take Credit : " + limit2 );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "On spot/next date, credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit2 - limit3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementNettingWithFXRateConversion()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementNettingWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            printCurrencyPositions( cwm1 );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementReserve()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = 1000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm );

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() );
            printCurrencyPositions( cwm1 );
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementReserveFailure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = limit0 + 100000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementUndoCredit()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );

            // first do a trade to buy 1k USD vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do another trade to sell 1k USD vs EUR. Now the net utilization should be zero.
            log( "Credit limit before second offer trade take Credit : " + limit2 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit1 - limit4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementMultipleTradesWithDifferentCurrenciesBuyTerm()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );

            // first do a trade to buy 1k GBP vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );

            // in this case net receivable would be EURO 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", getTradeAmount( trade1, "GBP" ), true );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do a trade to buy 1k JPY vs CHF
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit3 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be two net receivables GBP xk and JPY xk.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", getTradeAmount( trade2, "JPY" ), true );
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );

            // do a trade to buy 1k DKK vs AUD
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit5 );
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, false, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be 3 receivable amounts GBP xk, JPY xk, and DKK xk
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "DKK", getTradeAmount( trade3, "DKK" ), true );
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementMultipleTradesWithDifferentCurrenciesSellTerm()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // first do a trade to sell 1k  GBP vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 10000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );

            // in this case net receivable would be EUR 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", getTradeAmount( trade1, "EUR" ), true );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do a trade to sell 1k JPY vs CHF
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit3 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", getTradeAmount( trade2, "CHF" ), true );
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );

            // do a trade to sell 1k DKK vs AUD
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit5 );
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be 3 receivable amounts EUR 1k, CHF 1k, and AUD 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "AUD", getTradeAmount( trade3, "AUD" ), true );
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementMultipleTradesWithDifferentCurrenciesBuyBase()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );

            // first do a trade to buy 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );

            // in this case net receivable would be EUR 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do a trade to buy 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit3 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true );
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );

            // do a trade to buy 1k AUD vs DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit5 );
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementMultipleTradesWithDifferentCurrenciesSellBase()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // first do a trade to sell 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );

            // in this case net receivable would be USD 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", getTradeAmount( trade1, "GBP" ), true );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do a trade to sell 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit3 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be two net receivables GBP 1k and JPY 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", getTradeAmount( trade2, "JPY" ), true );
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );

            // do a trade to sell 1k AUD vs DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit5 );
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be 3 receivable amounts GBP 1k, JPY 1k, and DKK 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "DKK", getTradeAmount( trade3, "DKK" ), true );
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testDailySettlementMultipleTradesCancelsOut()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // first do a trade to buy 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );

            // in this case net receivable would be EURO 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do a trade to buy 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit3 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true );
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );

            // do a trade to buy 1k AUD/DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit5 );
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be 3 receivable amounts EUR 1k, CHF 1k, and AUD 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "AUD", tradeAmt, true );
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm3 );

            // first do a trade to sell 1k EUR vs GBP
            double limit7 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit7 );
            Trade trade4 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd );
            double limit8 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit8 + " success : " + cwm4.getStatus() );

            // in this case net receivable would be EURO 1k
            double usedAmt4 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            assertEquals( "before limit=" + limit7 + ",limit after=" + limit8 + ",usedAmt=" + usedAmt4, true, Math.abs( limit8 - limit7 - usedAmt4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm4.getStatusName(), cwm4.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm4 );

            // do a trade to sell 1k CHF vs JPY
            double limit9 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit9 );
            Trade trade5 = prepareSingleLegTrade( tradeAmt, false, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm5 = creditMgr.takeCredit( trade5, lpLe, ddOrg, lpTpForDd );
            double limit10 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt5 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true );
            log( "Credit limit after second bid trade take Credit : " + limit10 + " success : " + cwm5.getStatus() );
            assertEquals( "before limit=" + limit9 + ",limit after=" + limit10 + ",usedAmt=" + usedAmt5, true, Math.abs( limit10 - limit9 - usedAmt5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm5.getStatusName(), cwm5.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm5 );

            // do a trade to sell 1k AUD vs DKK
            double limit11 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before second bid trade take Credit : " + limit11 );
            Trade trade6 = prepareSingleLegTrade( tradeAmt, false, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm6 = creditMgr.takeCredit( trade6, lpLe, ddOrg, lpTpForDd );
            double limit12 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            // in this case, all the net receivables got cancelled due to opposite direction trades.
            log( "Credit limit after second bid trade take Credit : " + limit12 + " success : " + cwm6.getStatus() );
            assertEquals( "before limit=" + limit11 + ",limit after=" + limit12, true, Math.abs( limit12 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm6.getStatusName(), cwm6.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm6 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSimpleNOPNetting()
    {
        try
        {
            init( lpUser );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            removeExistingCreditUtilizationEvents( lpOrg );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            // in this case, check the net receivable and net payable.
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( tradeAmt ) ? Math.abs( eurAmt1 ) : Math.abs( tradeAmt );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNOPNettingWithDifferentValueDates()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "On Spot date, credit limit before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            // in this case, check the net receivable and net payable.
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( tradeAmt ) ? Math.abs( eurAmt1 ) : Math.abs( tradeAmt );

            log( "On spot date, credit limit after take Credit : " + limit1 + " success : " + cwm.getStatus() + "eurAmt=" + eurAmt1 + ",utilizedAmt=" + utilizedAmt );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now do an offer trade on spot next date
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), GROSS_NOTIONAL_CLASSIFICATION );
            log( "On spot/next date, credit limit before take Credit : " + limit2 );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), GROSS_NOTIONAL_CLASSIFICATION );
            log( "On spot/next date, credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNOPNettingWithFXRateConversion()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            // in this case, check the net receivable and net payable.
            double usdAmt1 = tradeAmt * bidRates[0];
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( usdAmt1 ) ? Math.abs( eurAmt1 ) : Math.abs( usdAmt1 );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNOPNettingWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            // in this case, check the net receivable and net payable.
            double gbpAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt * bidRates[0], false );
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( gbpAmt1 ) ? Math.abs( eurAmt1 ) : Math.abs( gbpAmt1 );
            log( "gbpAmt=" + gbpAmt1 + ",eurAmt=" + eurAmt1 + ",utilizedAmt=" + utilizedAmt );

            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            printCurrencyPositions( cwm1 );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNOPReserve()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = 1000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm );

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() );
            printCurrencyPositions( cwm1 );
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNOPReserveFailure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = limit0 + 100000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNOPUndoCredit()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );

            // first do a trade to buy 1k USD vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            // in this case, check the net receivable and net payable.
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( tradeAmt ) ? Math.abs( eurAmt1 ) : Math.abs( tradeAmt );

            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm1.getStatus() );
            printCurrencyPositions( cwm1 );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit1 - limit3 ) < CREDIT_CALCULATION_MINIMUM );

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSimpleAggregateNetPRSettlementNetting()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
                creditAdminSvc.reinitialize( lpOrg );
            }
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 );
            creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, true );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            double tradeDateLimit0 = getAvailableCreditLimit( lpTpForDd, tradeDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + tradeDateLimit0 );
            double tradeAmt = 1000;

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], tradeDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double tradeDateLimit1 = getAvailableCreditLimit( lpTpForDd, tradeDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit on trade date : " + tradeDateLimit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + tradeDateLimit0 + ",limitAfter=" + tradeDateLimit1, true, Math.abs( tradeDateLimit0 - tradeDateLimit1 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // now do a trade in spot date and it should have only non-limit currency positions taken for utilization.
            double spotDateLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );

            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double spotDateLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + spotDateLimit1 + " success : " + cwm1.getStatus() );
            assertEquals( "before limit=" + spotDateLimit0 + ",limitAfter=" + spotDateLimit1, true, Math.abs( spotDateLimit0 - spotDateLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm1 );

            // Now take an offer side trade. This should put back the earlier EUR amount and result in no utilization at all.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double spotDateLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + spotDateLimit2 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm2 );
            assertEquals( "before limit=" + spotDateLimit1 + ",after limit=" + spotDateLimit2, true, Math.abs( spotDateLimit0 - spotDateLimit2 ) < CREDIT_CALCULATION_MINIMUM );

            // for spot date and non-limit currency trade it would just act same as NOP.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );

            // in this case, check the net receivable and net payable.
            double eurAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            double gbpAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true );
            double utilizedAmt3 = Math.abs( eurAmt3 ) + Math.abs( gbpAmt3 );

            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double spotDateLimit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + spotDateLimit3 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + spotDateLimit2 + ",limitAfter=" + spotDateLimit3, true, Math.abs( spotDateLimit2 - spotDateLimit3 - utilizedAmt3 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm3 );

            // Now take an offer side trade.
            Trade trade4 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd );
            double spotDateLimit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + spotDateLimit4 + " success : " + cwm4.getStatus() );
            validateCreditUtilizationEvents( cwm4 );
            assertEquals( "before limit=" + spotDateLimit3 + ",after limit=" + spotDateLimit4, true, Math.abs( spotDateLimit2 - spotDateLimit4 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateNetPRSettlementWithDifferentValueDates()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
                creditAdminSvc.reinitialize( lpOrg );
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 );

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "On Spot date, credit limit before take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            // in this case, check the net receivable and net payable.
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );

            log( "On spot date, credit limit after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now do an offer trade on spot next date
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), GROSS_NOTIONAL_CLASSIFICATION );
            log( "On spot/next date, credit limit before take Credit : " + limit2 );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), GROSS_NOTIONAL_CLASSIFICATION );
            log( "On spot/next date, credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateNetPRSettlementWithFXRateConversion()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
                creditAdminSvc.reinitialize( lpOrg );
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 );

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            // in this case, check the net receivable and net payable.
            double gbpAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true );
            double jpyAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt * bidRates[4], false );
            double utilizedAmt = Math.abs( jpyAmt1 ) + Math.abs( gbpAmt1 );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );

            // Now take trade date trade.
            double tradeDateLimit0 = getAvailableCreditLimit( lpTpForDd, tradeDate, GROSS_NOTIONAL_CLASSIFICATION );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], tradeDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double tradeDateLimit1 = getAvailableCreditLimit( lpTpForDd, tradeDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + tradeDateLimit1 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm2 );
            assertEquals( "before limit=" + tradeDateLimit0 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateNetPRSettlementWithCreditLimitCurrency()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
                creditAdminSvc.reinitialize( lpOrg );
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 100000 );

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            // in this case, check the net receivable and net payable.
            double utilizedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt1 );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt1, true, Math.abs( limit0 - limit1 - utilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take another trade on GBP/USD. In this case GBP amount falls on to net payable.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "GBP/USD", lpOrg, lpTpForDd, bidRates[1], spotDate );
            double gbpAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, false );
            double utilizedAmt2 = Math.abs( utilizedAmt1 ) + Math.abs( gbpAmt );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            validateCreditUtilizationEvents( cwm1 );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",gbpAmt=" + gbpAmt + ",eurAmt=" + utilizedAmt1, true, Math.abs( limit0 - limit2 - utilizedAmt2 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateNetPRSettlementReserve()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
                creditAdminSvc.reinitialize( lpOrg );
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 );

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = 1000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm );

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() );
            printCurrencyPositions( cwm1 );
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateNetPRSettlementReserveFailure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
                creditAdminSvc.reinitialize( lpOrg );
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 );

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = limit0 + 100000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateNetPRSettlementUndoCredit()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
                creditAdminSvc.reinitialize( lpOrg );
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 );

            // first do a GBP/JPY trade.
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            double gbpAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true );
            double jpyAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt * bidRates[4], false );
            double utilizedAmt = Math.abs( jpyAmt1 ) + Math.abs( gbpAmt1 );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // do the GBP/JPY trade in the opposite direction.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm2 = creditMgr.undoCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade undo Credit : " + limit3 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSimpleAggregateLimitNetting()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            // in this case, check the net receivable and net payable.
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateLimitWithDifferentValueDates()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            // in this case, check the net receivable and net payable.
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take another bid trade on the spot next day.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot next day. This will remove the net receivable position in spot/next day.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after third offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm2 );
            assertEquals( "before limit=" + limit2 + ",after limit=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot day. This will remove the net receivable position in spot day also.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after fourth offer trade take Credit : " + limit4 + " success : " + cwm3.getStatus() );
            validateCreditUtilizationEvents( cwm3 );
            assertEquals( "before limit=" + limit3 + ",after limit=" + limit4, true, Math.abs( limit4 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateLimitWithFXRateConversion()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            // in this case, check the net receivable and net payable.
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take another bid trade on the spot next day.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot next day. This will remove the net receivable position in spot/next day.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after third offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm2 );
            assertEquals( "before limit=" + limit2 + ",after limit=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot day. This will remove the net receivable position in spot day also.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after fourth offer trade take Credit : " + limit4 + " success : " + cwm3.getStatus() );
            validateCreditUtilizationEvents( cwm3 );
            assertEquals( "before limit=" + limit3 + ",after limit=" + limit4, true, Math.abs( limit4 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateLimitWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );

            // in this case, check the net receivable and net payable.
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take another bid trade on the spot next day.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot next day. This will remove the net receivable position in spot/next day.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after third offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm2 );
            assertEquals( "before limit=" + limit2 + ",after limit=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot day. This will remove the net receivable position in spot day also.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after fourth offer trade take Credit : " + limit4 + " success : " + cwm3.getStatus() );
            validateCreditUtilizationEvents( cwm3 );
            assertEquals( "before limit=" + limit3 + ",after limit=" + limit4, true, Math.abs( limit4 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateLimitReserve()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = 1000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm );

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() );
            printCurrencyPositions( cwm1 );
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateLimitReserveFailure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = limit0 + 1000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testIgnoreCurrentValueDatePositions()
    {
        try
        {
            init( fiUser );

            Collection<CreditUtilizationCalculator> supportedCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();

            // initialise trade settling today itself
            double tradeAmt = 1000;


            for ( CreditUtilizationCalculator calculator : supportedCalcs )
            {
                removeExistingCreditUtilizationEvents( fiOrg );
                setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, calculator, 10000 );
                creditAdminSvc.setLeverageFactor( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, 100.0 );
                //set exclude trades settling today flag to true
                creditAdminSvc.setIgnoreCurrDatePositions( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, true );

                double limit0 = getAvailableCreditLimit( fiTpforCpty, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
                log( "Credit limit before first trade take Credit for calculator type " + calculator.getShortName() + " is : " + limit0 );

                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], tradeDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpforCpty );
                double limit1 = getAvailableCreditLimit( fiTpforCpty, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
                log( "Credit limit after first trade take Credit for calculator type " + calculator.getShortName() + " is : " + limit1 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );

                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 ) < MINIMUM );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setCalcAndLimit( fiOrg, fiTpforCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testAggregateLimitUndoCredit()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );

            // first do a trade to buy 1k USD vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do another trade to sell 1k USD vs EUR. Now the net utilization should be zero.
            log( "Credit limit before second offer trade take Credit : " + limit2 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit1 - limit4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSimpleGrossDaily()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade. In this case buy currency is EUR. but, if sell currency is credit limit currency, then that amount will be used.
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testGrossDailyWithDifferentValueDates()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limitSpot0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limitSpot0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limitSpot1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limitSpot1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + limitSpot0 + ",limitAfter=" + limitSpot1, true, Math.abs( limitSpot0 - limitSpot1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take a bid trade on the spot next day.
            double limitSpotNext0 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limitSpotNext1 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second bid trade take Credit : " + limitSpotNext1 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limitSpotNext0 + ",after limit=" + limitSpotNext1, true, Math.abs( limitSpotNext0 - limitSpotNext1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot next day. This will add to the utilization on spot/next day.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limitSpotNext2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after third offer trade take Credit : " + limitSpotNext2 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm2 );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            assertEquals( "before limit=" + limitSpotNext1 + ",after limit=" + limitSpotNext2, true, Math.abs( limitSpotNext1 - limitSpotNext2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot day. This will remove the net receivable position in spot day also.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limitSpot2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after fourth offer trade take Credit : " + limitSpot2 + " success : " + cwm3.getStatus() );
            validateCreditUtilizationEvents( cwm3 );
            assertEquals( "before limit=" + limitSpot1 + ",after limit=" + limitSpot2, true, Math.abs( limitSpot1 - limitSpot2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testGrossDailyWithFXRateConversion()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double spotLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + spotLimit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            // in this case, EUR is the buying currency. but, USD if the limit currency, then USD amount is used.
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt * bidRates[0] : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double spotLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + spotLimit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertEquals( "before limit=" + spotLimit0 + ",limitAfter=" + spotLimit1, true, Math.abs( spotLimit0 - spotLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take another bid trade on the spot next day.
            double spotNextLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double spotNextLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second bid trade take Credit : " + spotNextLimit1 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + spotNextLimit0 + ",after limit=" + spotNextLimit1, true, Math.abs( spotNextLimit0 - spotNextLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot next day. This will add to the utilization. But, buying currency is now USD.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double spotNextLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after third offer trade take Credit : " + spotNextLimit2 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm2 );
            assertEquals( "before limit=" + spotNextLimit1 + ",after limit=" + spotNextLimit2, true, Math.abs( spotNextLimit1 - spotNextLimit2 - ( tradeAmt * bidRates[0] ) ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot day. This will add to the spot utilization. But buying currency is USD.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            validateCreditUtilizationEvents( cwm3 );
            assertTrue( MessageStatus.SUCCESS.equals( cwm3.getStatus() ) );
            sleepFor( 2000 );
            double spotLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after fourth offer trade take Credit : " + spotLimit2 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + spotLimit1 + ",after limit=" + spotLimit2, true, Math.abs( spotLimit1 - spotLimit2 - ( tradeAmt * bidRates[0] ) ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testGrossDailyWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double spotLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + spotLimit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );

            // in this case, buying currency is GBP.
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double spotLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + spotLimit1 + " success : " + cwm.getStatus() );
            assertEquals( "before limit=" + spotLimit0 + ",limitAfter=" + spotLimit1, true, Math.abs( spotLimit0 - spotLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take another bid trade on the spot next day. buying currency is GBP
            double spotNextLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double spotNextLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second bid trade take Credit : " + spotNextLimit1 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + spotNextLimit0 + ",after limit=" + spotNextLimit1, true, Math.abs( spotNextLimit0 - spotNextLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot next day. In this case, the buying currency is EUR.
            double utilizedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double spotNextLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after third offer trade take Credit : " + spotNextLimit2 + " success : " + cwm2.getStatus() );
            validateCreditUtilizationEvents( cwm2 );
            assertEquals( "before limit=" + spotNextLimit1 + ",after limit=" + spotNextLimit2, true, Math.abs( spotNextLimit1 - spotNextLimit2 - utilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM );

            // Now take an offer trade on the spot day. GBP is the buying currency.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double spotLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after fourth offer trade take Credit : " + spotLimit2 + " success : " + cwm3.getStatus() );
            validateCreditUtilizationEvents( cwm3 );
            assertEquals( "before limit=" + spotLimit1 + ",after limit=" + spotLimit2, true, Math.abs( spotLimit1 - spotLimit2 - utilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testGrossDailyReserve()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = 1000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm );

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() );
            printCurrencyPositions( cwm1 );
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testGrossDailyReserveFailure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid reserve Credit : " + limit0 );
            double tradeAmt = limit0 + 1000000;
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 );
            printCurrencyPositions( cwm );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testGrossDailyUndoCredit()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );

            // first do a trade to buy 1k USD vs EUR. Buying currency is USD
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do another trade to sell 1k USD vs EUR. buying currency is EUR. but, USD amount will be considered if USD is the limit ccy
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizationAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            log( "Credit limit before second offer trade take Credit : " + limit2 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit2 - limit3 - utilizationAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );

            // undo the last trade so that it goes back to the previous utilization.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() );
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit2 - limit4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
