package com.integral.finance.creditLimit.test;

import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.test.util.DealingTestUtil;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.is.ISCommonConstants;
import com.integral.message.MessageStatus;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

/**
 * Tests the credit qualification of netting portfolio.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitNettingPortfolioPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Netting Portfolio Test";

    public CreditLimitNettingPortfolioPTestC ( String name )
    {
        super ( name );
    }

    protected void setUp ( ) throws Exception
    {
        super.setUp ();
        lpTpForFi.putCustomField ( CounterpartyUtilC.TRADING_PARTY_DEFAULT_DEALING_ENTITY, lpLe );
        List<String> relatedLes = new ArrayList<String> ();
        relatedLes.add ( fiLe.getShortName () );
        relatedLes.add ( fiLe2.getShortName () );
        fiTpForLp.putCustomField ( CounterpartyUtilC.RELATED_LEGAL_ENTITY, relatedLes );
        fiTpForLpLe2.putCustomField ( CounterpartyUtilC.RELATED_LEGAL_ENTITY, relatedLes );
        lpTpForFiLe2.putCustomField ( CounterpartyUtilC.TRADING_PARTY_DEFAULT_DEALING_ENTITY, lpLe );

        fiTpForLp.putCustomField ( CounterpartyUtilC.TRADING_PARTY_DEFAULT_DEALING_ENTITY, fiLe );
        List<String> relatedLes1 = new ArrayList<String> ();
        relatedLes1.add ( lpLe.getShortName () );
        relatedLes1.add ( lp2Le.getShortName () );
        lpTpForFi.putCustomField ( CounterpartyUtilC.RELATED_LEGAL_ENTITY, relatedLes1 );
        lpTpForFiLe2.putCustomField ( CounterpartyUtilC.RELATED_LEGAL_ENTITY, relatedLes1 );
        fiTpForLpLe2.putCustomField ( CounterpartyUtilC.TRADING_PARTY_DEFAULT_DEALING_ENTITY, fiLe );
    }

    public void testPortfolioCreditCheckWithDailyNettingAtLeExposure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 1200000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );

            // test one buy and sell trade with net amount below the limit in the test portfolio. credit check should pass.
            NettingPortfolio np2 = new NettingPortfolioC ();
            np2.setName ( "test2" );
            np2.setPortfolioID ( "FXI10002" );
            np2.getToOrganizations ().add ( lpOrg );

            double dealtAmt2x = 1200000;
            double dealtAmt2y = 900000;
            NettingTradeRequest ntr2x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2y, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np2.getInputRequestPojos ().add ( ntr2x );
            np2.getInputRequestPojos ().add ( ntr2y );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2 );
            assertNull ( cwm2 );

            // test two sell trade with total amount above the limit in the test portfolio. credit check should fail.
            NettingPortfolio np3 = new NettingPortfolioC ();
            np3.setName ( "test3" );
            np3.setPortfolioID ( "FXI10003" );
            np3.getToOrganizations ().add ( lpOrg );

            double dealtAmt3x = 600000;
            double dealtAmt3y = 600000;
            NettingTradeRequest ntr3x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3y, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np3.getInputRequestPojos ().add ( ntr3x );
            np3.getInputRequestPojos ().add ( ntr3y );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np3 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithDailyNettingAtLeExposure", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithDailyNettingAtOrgExposure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 1200000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );

            // test one buy and sell trade against different LEs with net amount below the limit in the test portfolio. credit check should pass.
            NettingPortfolio np2 = new NettingPortfolioC ();
            np2.setName ( "test2" );
            np2.setPortfolioID ( "FXI10002" );
            np2.getToOrganizations ().add ( lpOrg );

            double dealtAmt2x = 1200000;
            double dealtAmt2y = 900000;
            NettingTradeRequest ntr2x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt2y, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np2.getInputRequestPojos ().add ( ntr2x );
            np2.getInputRequestPojos ().add ( ntr2y );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2 );
            assertNull ( cwm2 );

            // test two sell trade against two LEs with total amount above the limit in the test portfolio. credit check should fail.
            NettingPortfolio np3 = new NettingPortfolioC ();
            np3.setName ( "test3" );
            np3.setPortfolioID ( "FXI10003" );
            np3.getToOrganizations ().add ( lpOrg );

            double dealtAmt3x = 600000;
            double dealtAmt3y = 600000;
            NettingTradeRequest ntr3x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt3y, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np3.getInputRequestPojos ().add ( ntr3x );
            np3.getInputRequestPojos ().add ( ntr3y );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np3 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithDailyNettingAtOrgExposure", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );


            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithAggregateNettingAtLeExposure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 1200000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );

            // test one buy and sell trade with net amount below the limit in the test portfolio. credit check should pass.
            NettingPortfolio np2 = new NettingPortfolioC ();
            np2.setName ( "test2" );
            np2.setPortfolioID ( "FXI10002" );
            np2.getToOrganizations ().add ( lpOrg );

            double dealtAmt2x = 1200000;
            double dealtAmt2y = 900000;
            NettingTradeRequest ntr2x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2y, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np2.getInputRequestPojos ().add ( ntr2x );
            np2.getInputRequestPojos ().add ( ntr2y );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2 );
            assertNull ( cwm2 );

            // test two sell trade with total amount above the limit in the test portfolio. credit check should fail.
            NettingPortfolio np3 = new NettingPortfolioC ();
            np3.setName ( "test3" );
            np3.setPortfolioID ( "FXI10003" );
            np3.getToOrganizations ().add ( lpOrg );

            double dealtAmt3x = 600000;
            double dealtAmt3y = 600000;
            NettingTradeRequest ntr3x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3y, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np3.getInputRequestPojos ().add ( ntr3x );
            np3.getInputRequestPojos ().add ( ntr3y );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np3 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithAggregateNettingAtLeExposure", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithAggregateNettingAtOrgExposure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 1200000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );

            // test one buy and sell trade with net amount below the limit in the test portfolio. credit check should pass.
            NettingPortfolio np2 = new NettingPortfolioC ();
            np2.setName ( "test2" );
            np2.setPortfolioID ( "FXI10002" );
            np2.getToOrganizations ().add ( lpOrg );

            double dealtAmt2x = 1200000;
            double dealtAmt2y = 900000;
            NettingTradeRequest ntr2x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt2y, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np2.getInputRequestPojos ().add ( ntr2x );
            np2.getInputRequestPojos ().add ( ntr2y );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2 );
            assertNull ( cwm2 );

            // test two sell trade with total amount above the limit in the test portfolio. credit check should fail.
            NettingPortfolio np3 = new NettingPortfolioC ();
            np3.setName ( "test3" );
            np3.setPortfolioID ( "FXI10003" );
            np3.getToOrganizations ().add ( lpOrg );

            double dealtAmt3x = 600000;
            double dealtAmt3y = 600000;
            NettingTradeRequest ntr3x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt3y, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np3.getInputRequestPojos ().add ( ntr3x );
            np3.getInputRequestPojos ().add ( ntr3y );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np3 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithAggregateNettingAtOrgExposure", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );


            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithDailyNoNettingAtLeExposure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 1200000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );

            // test one buy and sell trade with net amount below the limit in the test portfolio. credit check should fail.
            NettingPortfolio np2 = new NettingPortfolioC ();
            np2.setName ( "test2" );
            np2.setPortfolioID ( "FXI10002" );
            np2.getToOrganizations ().add ( lpOrg );

            double dealtAmt2x = 1200000;
            double dealtAmt2y = 900000;
            NettingTradeRequest ntr2x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2y, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np2.getInputRequestPojos ().add ( ntr2x );
            np2.getInputRequestPojos ().add ( ntr2y );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2 );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );

            // test two sell trade with total amount above the limit in the test portfolio. credit check should fail.
            NettingPortfolio np3 = new NettingPortfolioC ();
            np3.setName ( "test3" );
            np3.setPortfolioID ( "FXI10003" );
            np3.getToOrganizations ().add ( lpOrg );

            double dealtAmt3x = 600000;
            double dealtAmt3y = 600000;
            NettingTradeRequest ntr3x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3y, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np3.getInputRequestPojos ().add ( ntr3x );
            np3.getInputRequestPojos ().add ( ntr3y );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np3 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithDailyNoNettingAtLeExposure", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithDailyNoNettingAtOrgExposure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 1200000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );

            // test one buy and sell trade against different LEs with net amount below the limit in the test portfolio. credit check should fail.
            NettingPortfolio np2 = new NettingPortfolioC ();
            np2.setName ( "test2" );
            np2.setPortfolioID ( "FXI10002" );
            np2.getToOrganizations ().add ( lpOrg );

            double dealtAmt2x = 1200000;
            double dealtAmt2y = 900000;
            NettingTradeRequest ntr2x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt2y, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np2.getInputRequestPojos ().add ( ntr2x );
            np2.getInputRequestPojos ().add ( ntr2y );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2 );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );

            // test two sell trade against two LEs with total amount above the limit in the test portfolio. credit check should fail.
            NettingPortfolio np3 = new NettingPortfolioC ();
            np3.setName ( "test3" );
            np3.setPortfolioID ( "FXI10003" );
            np3.getToOrganizations ().add ( lpOrg );

            double dealtAmt3x = 600000;
            double dealtAmt3y = 600000;
            NettingTradeRequest ntr3x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt3y, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np3.getInputRequestPojos ().add ( ntr3x );
            np3.getInputRequestPojos ().add ( ntr3y );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np3 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithDailyNoNettingAtOrgExposure", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );


            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithAggregateNoNettingAtLeExposure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 1200000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );

            // test one buy and sell trade with net amount below the limit in the test portfolio. credit check should fail.
            NettingPortfolio np2 = new NettingPortfolioC ();
            np2.setName ( "test2" );
            np2.setPortfolioID ( "FXI10002" );
            np2.getToOrganizations ().add ( lpOrg );

            double dealtAmt2x = 1200000;
            double dealtAmt2y = 900000;
            NettingTradeRequest ntr2x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2y, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np2.getInputRequestPojos ().add ( ntr2x );
            np2.getInputRequestPojos ().add ( ntr2y );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2 );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );

            // test two sell trade with total amount above the limit in the test portfolio. credit check should fail.
            NettingPortfolio np3 = new NettingPortfolioC ();
            np3.setName ( "test3" );
            np3.setPortfolioID ( "FXI10003" );
            np3.getToOrganizations ().add ( lpOrg );

            double dealtAmt3x = 600000;
            double dealtAmt3y = 600000;
            NettingTradeRequest ntr3x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3y, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np3.getInputRequestPojos ().add ( ntr3x );
            np3.getInputRequestPojos ().add ( ntr3y );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np3 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithAggregateNoNettingAtLeExposure", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithAggregateNoNettingAtOrgExposure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 1200000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );

            // test one buy and sell trade with net amount below the limit in the test portfolio. credit check should fail.
            NettingPortfolio np2 = new NettingPortfolioC ();
            np2.setName ( "test2" );
            np2.setPortfolioID ( "FXI10002" );
            np2.getToOrganizations ().add ( lpOrg );

            double dealtAmt2x = 1200000;
            double dealtAmt2y = 900000;
            NettingTradeRequest ntr2x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt2x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt2y, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np2.getInputRequestPojos ().add ( ntr2x );
            np2.getInputRequestPojos ().add ( ntr2y );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2 );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );

            // test two sell trade with total amount above the limit in the test portfolio. credit check should fail.
            NettingPortfolio np3 = new NettingPortfolioC ();
            np3.setName ( "test3" );
            np3.setPortfolioID ( "FXI10003" );
            np3.getToOrganizations ().add ( lpOrg );

            double dealtAmt3x = 600000;
            double dealtAmt3y = 600000;
            NettingTradeRequest ntr3x = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt3x, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3y = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt3y, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np3.getInputRequestPojos ().add ( ntr3x );
            np3.getInputRequestPojos ().add ( ntr3y );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np3 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithAggregateNoNettingAtOrgExposure", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );


            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckSanityTest ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000000 );
            Collection<Organization> toOrgs = new ArrayList<Organization> ();
            toOrgs.add ( lpOrg );

            // check against all customer legal entities a mix of spot, outright and swap trades.
            for ( int i = 1; i < 6; i++ )
            {
                for ( int j = 0; j < 5; j++ )
                {
                    for ( int k = 0; k < 5; k++ )
                    {
                        NettingPortfolio np = DealingTestUtil.createTestNettingPortfolio ( "test", "FXI1000", fiOrg, toOrgs, i, j, k );
                        CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
                        if ( cwm != null )
                        {
                            log ( "cwm.errorcode=" + cwm.getErrorCode () );
                        }
                    }
                }
            }

            // reverse loop
            for ( int i = 1; i < 6; i++ )
            {
                for ( int j = 0; j < 5; j++ )
                {
                    for ( int k = 0; k < 5; k++ )
                    {
                        NettingPortfolio np = DealingTestUtil.createTestNettingPortfolio ( "test", "FXI1000", fiOrg, toOrgs, k, j, i );
                        CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
                        if ( cwm != null )
                        {
                            log ( "cwm.errorcode=" + cwm.getErrorCode () );
                        }
                    }
                }
            }

            // now check against a particular customer legal entity
            Collection<LegalEntity> les = new ArrayList<LegalEntity> ();
            les.add ( fiLe );
            for ( int i = 1; i < 6; i++ )
            {
                for ( int j = 0; j < 5; j++ )
                {
                    for ( int k = 0; k < 5; k++ )
                    {
                        NettingPortfolio np = DealingTestUtil.createTestNettingPortfolio ( "test", "FXI1000", les, toOrgs, i, j, k );
                        CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
                        if ( cwm != null )
                        {
                            log ( "cwm.errorcode=" + cwm.getErrorCode () );
                        }
                        assertNull ( cwm );
                    }
                }
            }

            // reverse loop
            for ( int i = 1; i < 6; i++ )
            {
                for ( int j = 0; j < 5; j++ )
                {
                    for ( int k = 0; k < 5; k++ )
                    {
                        NettingPortfolio np = DealingTestUtil.createTestNettingPortfolio ( "test", "FXI1000", les, toOrgs, k, j, i );
                        CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
                        if ( cwm != null )
                        {
                            log ( "cwm.errorcode=" + cwm.getErrorCode () );
                        }
                        assertNull ( cwm );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail ( "testPortfolioCreditCheck", e );
        }
    }

    public void testPortfolioCreditCheckOnSuspended ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( lpOrg, lpTpForFi );
            if ( null == cptyRule )
            {
                creditAdminSvc.establishCreditRelationship ( lpOrg, lpTpForFi, false );
            }
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000000 );

            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000000 );

            Collection<Organization> toOrgs = new ArrayList<Organization> ();
            toOrgs.add ( lpOrg );

            // set the credit status to suspended.
            creditAdminSvc.setCreditStatus ( lpOrg, CREDIT_SUSPEND );
            Collection<LegalEntity> fiLes = new ArrayList<LegalEntity> ();
            fiLes.add ( fiLe );
            fiLes.add ( fiLe2 );

            NettingPortfolio np = DealingTestUtil.createTestNettingPortfolio ( "test", "FXI1000", fiLes, toOrgs, 1, 1, 1 );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np, false, true );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );

            // set the credit status to active.
            creditAdminSvc.setCreditStatus ( lpOrg, CREDIT_ACTIVE );

            NettingPortfolio np1 = DealingTestUtil.createTestNettingPortfolio ( "test", "FXI1000", fiLes, toOrgs, 1, 1, 1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1, false, true );
            assertNull ( cwm1 );


            creditAdminSvc.setCreditStatus ( lpOrg, lpTpForFi, CREDIT_SUSPEND );

            NettingPortfolio np2 = DealingTestUtil.createTestNettingPortfolio ( "test", "FXI1000", fiLes, toOrgs, 1, 1, 1 );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np2, false, true );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            fail ( "testPortfolioCreditCheckOnSuspended", e );
        }
        finally
        {
            creditAdminSvc.setCreditStatus ( lpOrg, CREDIT_ACTIVE );
            creditAdminSvc.setCreditStatus ( lpOrg, lpTpForFi, CREDIT_ACTIVE );
        }
    }

    public void testFXSwapAvailableCreditCheck ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade ( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade, lpLe, fiLe );
            assertNotNull ( cwm );
            assertTrue ( cwm.getErrors ().size () > 0 );
            log ( "cwm.errorCode=" + cwm.getErrorCode () );

            FXSwap trade1 = prepareSwapTrade ( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade1, lpLe, fiLe );
            assertNull ( cwm1 );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies ();
            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 1000000 );
                setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 1000000 );

                FXSwap trade2 = prepareSwapTrade ( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
                CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade2, lpLe, fiLe );

                if ( aggCalc.isSameAs ( AGGREGATE_LIMIT_CALCULATOR ) || aggCalc.isSameAs ( GROSS_AGGREGATE_LIMIT_CALCULATOR ) )
                {
                    assertNotNull ( cwm2 );
                    assertTrue ( cwm2.getErrors ().size () > 0 );
                    log ( "cwm.errorCode=" + cwm2.getErrorCode () );
                }
                else
                {
                    assertNull ( cwm2 );
                }

                FXSwap trade3 = prepareSwapTrade ( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
                CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade3, lpLe, fiLe );
                assertNull ( cwm3 );
            }

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 1000000 );
                setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 1000000 );

                FXSwap trade2 = prepareSwapTrade ( 600000, 600000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
                CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade2, lpLe, fiLe );

                if ( aggCalc.isSameAs ( AGGREGATE_LIMIT_CALCULATOR ) || aggCalc.isSameAs ( GROSS_AGGREGATE_LIMIT_CALCULATOR ) )
                {
                    assertNotNull ( cwm2 );
                    assertTrue ( cwm2.getErrors ().size () > 0 );
                    log ( "cwm.errorCode=" + cwm2.getErrorCode () );
                }
                else
                {
                    assertNull ( cwm2 );
                }

                FXSwap trade3 = prepareSwapTrade ( 300000, 300000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
                CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade3, lpLe, fiLe );
                assertNull ( cwm3 );
            }

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 1000000 );
                setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 1000000 );

                FXSwap trade2 = prepareSwapTrade ( 600000, 600000, true, false, true, true, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
                CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade2, lpLe, fiLe );

                if ( aggCalc.isSameAs ( AGGREGATE_LIMIT_CALCULATOR ) || aggCalc.isSameAs ( GROSS_AGGREGATE_LIMIT_CALCULATOR ) )
                {
                    assertNotNull ( cwm2 );
                    assertTrue ( cwm2.getErrors ().size () > 0 );
                    log ( "cwm.errorCode=" + cwm2.getErrorCode () );
                }
                else
                {
                    assertNull ( cwm2 );
                }

                FXSwap trade3 = prepareSwapTrade ( 300000, 300000, true, false, true, true, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
                CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade3, lpLe, fiLe );
                assertNull ( cwm3 );
            }

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 1000000 );
                setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 1000000 );

                FXSwap trade2 = prepareSwapTrade ( 600000, 600000, false, true, true, true, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
                CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade2, lpLe, fiLe );

                if ( aggCalc.isSameAs ( AGGREGATE_LIMIT_CALCULATOR ) || aggCalc.isSameAs ( GROSS_AGGREGATE_LIMIT_CALCULATOR ) )
                {
                    assertNotNull ( cwm2 );
                    assertTrue ( cwm2.getErrors ().size () > 0 );
                    log ( "cwm.errorCode=" + cwm2.getErrorCode () );
                }
                else
                {
                    assertNull ( cwm2 );
                }

                FXSwap trade3 = prepareSwapTrade ( 300000, 300000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
                CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( trade3, lpLe, fiLe );
                assertNull ( cwm3 );
            }
        }
        catch ( Exception e )
        {
            fail ( "testFXSwapAvailableCreditCheck", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testFXSwapRequestAvailableCreditCheckNoNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade ( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request = DealingTestUtil.createTestSwapRequest ( trade );
            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm );
            assertTrue ( cwm.getErrors ().size () > 0 );
            log ( "cwm.errorCode=" + cwm.getErrorCode () );

            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getErrors ().size () > 0 );
            log ( "cwm1.errorCode=" + cwm1.getErrorCode () );

            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getErrors ().size () > 0 );
            log ( "cwm2.errorCode=" + cwm2.getErrorCode () );

            FXSwap trade1 = prepareSwapTrade ( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request1 = DealingTestUtil.createTestSwapRequest ( trade1 );
            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm3 );

            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm4 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm4 );

            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm5 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm5 );

            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue ( cwm6.getStatus () == MessageStatus.SUCCESS );

            FXSwap trade2 = prepareSwapTrade ( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request2 = DealingTestUtil.createTestSwapRequest ( trade2 );
            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm7 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNotNull ( cwm7 );
            assertTrue ( cwm7.getErrors ().size () > 0 );
            log ( "cwm7.errorCode=" + cwm7.getErrorCode () );

            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm8 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNotNull ( cwm8 );
            assertTrue ( cwm8.getErrors ().size () > 0 );
            log ( "cwm8.errorCode=" + cwm8.getErrorCode () );

            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm9 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNotNull ( cwm9 );
            assertTrue ( cwm9.getErrors ().size () > 0 );
            log ( "cwm9.errorCode=" + cwm9.getErrorCode () );

            FXSwap trade3 = prepareSwapTrade ( 50000, 50000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request3 = DealingTestUtil.createTestSwapRequest ( trade3 );
            request3.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request3.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm10 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request3, lpLe, fiLe );
            assertNull ( cwm10 );

            request3.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request3.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm11 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request3, lpLe, fiLe );
            assertNull ( cwm11 );

            request3.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request3.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm12 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request3, lpLe, fiLe );
            assertNull ( cwm12 );

            CreditWorkflowMessage cwm13 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue ( cwm13.getStatus () == MessageStatus.SUCCESS );

        }
        catch ( Exception e )
        {
            fail ( "testFXSwapRequestAvailableCreditCheckNoNetting", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testFXSwapRequestAvailableCreditCheckNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade ( 1200000, 1200000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request = DealingTestUtil.createTestSwapRequest ( trade );
            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm );
            assertTrue ( cwm.getErrors ().size () > 0 );
            log ( "cwm.errorCode=" + cwm.getErrorCode () );

            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getErrors ().size () > 0 );
            log ( "cwm1.errorCode=" + cwm1.getErrorCode () );

            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getErrors ().size () > 0 );
            log ( "cwm2.errorCode=" + cwm2.getErrorCode () );

            FXSwap trade1 = prepareSwapTrade ( 900000, 900000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request1 = DealingTestUtil.createTestSwapRequest ( trade1 );
            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm3 );

            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm4 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm4 );

            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm5 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm5 );

            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue ( cwm6.getStatus () == MessageStatus.SUCCESS );

            FXSwap trade2 = prepareSwapTrade ( 1300000, 1300000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request2 = DealingTestUtil.createTestSwapRequest ( trade2 );
            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm7 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNotNull ( cwm7 );
            assertTrue ( cwm7.getErrors ().size () > 0 );
            log ( "cwm7.errorCode=" + cwm7.getErrorCode () );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm7a = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNull ( cwm7a );

            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm8 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNotNull ( cwm8 );
            assertTrue ( cwm8.getErrors ().size () > 0 );
            log ( "cwm8.errorCode=" + cwm8.getErrorCode () );

            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm9 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNull ( cwm9 );

        }
        catch ( Exception e )
        {
            fail ( "testFXSwapRequestAvailableCreditCheckNetting", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testFXSwapRequestAvailableCreditCheckDailyAggregateNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade ( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request = DealingTestUtil.createTestSwapRequest ( trade );
            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm );
            assertTrue ( cwm.getErrors ().size () > 0 );
            log ( "cwm.errorCode=" + cwm.getErrorCode () );

            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getErrors ().size () > 0 );
            log ( "cwm1.errorCode=" + cwm1.getErrorCode () );

            request.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request, lpLe, fiLe );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getErrors ().size () > 0 );
            log ( "cwm2.errorCode=" + cwm2.getErrorCode () );

            FXSwap trade1 = prepareSwapTrade ( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request1 = DealingTestUtil.createTestSwapRequest ( trade1 );
            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm3 );

            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm4 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm4 );

            request1.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request1.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );

            CreditWorkflowMessage cwm5 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request1, lpLe, fiLe );
            assertNull ( cwm5 );

            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue ( cwm6.getStatus () == MessageStatus.SUCCESS );

            FXSwap trade2 = prepareSwapTrade ( 1300000, 1300000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 2 ) );
            Request request2 = DealingTestUtil.createTestSwapRequest ( trade2 );
            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.TWO_WAY );

            CreditWorkflowMessage cwm7 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNotNull ( cwm7 );
            assertTrue ( cwm7.getErrors ().size () > 0 );
            log ( "cwm7.errorCode=" + cwm7.getErrorCode () );

            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.BID );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );

            CreditWorkflowMessage cwm8 = CreditUtilizationManagerC.getInstance ().checkMultiLegFXTradeCredit ( request2, lpLe, fiLe );
            assertNotNull ( cwm8 );
            assertTrue ( cwm8.getErrors ().size () > 0 );
            log ( "cwm8.errorCode=" + cwm8.getErrorCode () );

            request2.getRequestPrice ( ISCommonConstants.NEAR_LEG ).setBidOfferMode ( DealingPrice.OFFER );
            request2.getRequestPrice ( ISCommonConstants.FAR_LEG ).setBidOfferMode ( DealingPrice.BID );
        }
        catch ( Exception e )
        {
            fail ( "testFXSwapRequestAvailableCreditCheckDailyAggregateNetting", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithMinMaxTenorAtProviderLevel ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );

            creditAdminSvc.setDefaultMinimumTenor ( lpOrg, new Tenor ( "2W" ) );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            FXRateBasis rb = CreditUtilC.getStdQuoteConvention ().getFXRateBasis ( ccyPair.getBaseCurrency (), ccyPair.getVariableCurrency () );
            IdcDate spDate = rb.getSpotDate ( tradeDate );
            IdcDate oneWeekDate = rb.getValueDate ( tradeDate, new Tenor ( "1W" ) );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr1 );

            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), oneWeekDate, new Tenor ( "1W" ) );
            np.getInputRequestPojos ().add ( ntr2 );

            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR, cwm.getErrorCode () );

            creditAdminSvc.setDefaultMinimumTenor ( lpOrg, null );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm1 );


            creditAdminSvc.setDefaultMaximumTenor ( lpOrg, Tenor.SPOT_TENOR );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR, cwm2.getErrorCode () );

            creditAdminSvc.setDefaultMinimumTenor ( lpOrg, new Tenor ( "TOD" ) );
            creditAdminSvc.setDefaultMaximumTenor ( lpOrg, new Tenor ( "1D" ) );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR, cwm3.getErrorCode () );

            creditAdminSvc.setDefaultMinimumTenor ( lpOrg, null );
            creditAdminSvc.setDefaultMaximumTenor ( lpOrg, null );
            CreditWorkflowMessage cwm4 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm4 );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithMinMaxTenorAtProviderLevel", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            creditAdminSvc.setDefaultMinimumTenor ( lpOrg, null );
            creditAdminSvc.setDefaultMaximumTenor ( lpOrg, null );
        }
    }

    public void testPortfolioCreditCheckWithMinMaxTenorAtCptyLevel ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );

            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "2W" ) );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            FXRateBasis rb = CreditUtilC.getStdQuoteConvention ().getFXRateBasis ( ccyPair.getBaseCurrency (), ccyPair.getVariableCurrency () );
            IdcDate spDate = rb.getSpotDate ( tradeDate );
            IdcDate oneWeekDate = rb.getValueDate ( tradeDate, new Tenor ( "1W" ) );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr1 );

            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), oneWeekDate, new Tenor ( "1W" ) );
            np.getInputRequestPojos ().add ( ntr2 );

            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR, cwm.getErrorCode () );

            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm1 );


            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, Tenor.SPOT_TENOR );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR, cwm2.getErrorCode () );

            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "TOD" ) );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, new Tenor ( "1D" ) );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR, cwm3.getErrorCode () );

            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, null );
            CreditWorkflowMessage cwm4 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm4 );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithMinMaxTenorAtCptyLevel", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            creditAdminSvc.setDefaultMinimumTenor ( lpOrg, null );
            creditAdminSvc.setDefaultMaximumTenor ( lpOrg, null );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, null );

        }
    }

    public void testPortfolioCreditCheckDailyNettingLeExposureForBidOfferLimits ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            double tradeAmt = 700000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, true, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log ( "cwm.status=" + cwm1.getStatus () );
            assertEquals ( cwm1.getStatus (), MessageStatus.SUCCESS );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 700000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm2 );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 700000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckDailyNettingLeExposureForBidOfferLimits", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckAggregateNettingLeExposureForBidOfferLimits ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );

            double tradeAmt = 700000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, true, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log ( "cwm.status=" + cwm1.getStatus () );
            assertEquals ( cwm1.getStatus (), MessageStatus.SUCCESS );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 700000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm2 );

            // test a single buy trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np1 = new NettingPortfolioC ();
            np1.setName ( "test1" );
            np1.setPortfolioID ( "FXI10001" );
            np1.getToOrganizations ().add ( lpOrg );

            double dealtAmt1 = 700000;
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np1.getInputRequestPojos ().add ( ntr1 );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np1 );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckAggregateNettingLeExposureForBidOfferLimits", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    /**
     * This test is used to make sure legal entity override is respected in a batch trade.
     */
    public void testPortfolioCreditCheckMinMaxTenorWithOrgExposureAndLELevelOverride ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            // now set the legal entity level override.
            creditAdminSvc.setLEOverride ( lpOrg, fiOrg, true );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "2W" ), true );
            creditAdminSvc.setOrgDefault ( lpOrg, fiOrg, lpTpForFi, false );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            FXRateBasis rb = CreditUtilC.getStdQuoteConvention ().getFXRateBasis ( ccyPair.getBaseCurrency (), ccyPair.getVariableCurrency () );
            IdcDate spDate = rb.getSpotDate ( tradeDate );
            IdcDate oneWeekDate = rb.getValueDate ( tradeDate, new Tenor ( "1W" ) );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr1 );

            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), oneWeekDate, new Tenor ( "1W" ) );
            np.getInputRequestPojos ().add ( ntr2 );

            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm );
            assertTrue ( cwm.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR, cwm.getErrorCode () );

            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "TOD" ), true );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm1 );


            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, Tenor.SPOT_TENOR, true );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR, cwm2.getErrorCode () );

            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "TOD" ), true );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, new Tenor ( "1D" ), true );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm3 );
            assertTrue ( cwm3.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR, cwm3.getErrorCode () );

            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "TOD" ), true );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, new Tenor ( "1Y" ), true );
            CreditWorkflowMessage cwm4 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm4 );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckMinMaxTenorWithOrgExposureAndLELevelOverride", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setOrgDefault ( lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setLEOverride ( lpOrg, fiOrg, false );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, null );
        }
    }

    /**
     * This test is used to make sure legal entity override is respected in a batch trade.
     */
    public void testMinMaxTenorWithOrgExposureAndLELevelOverrideWithMultipleLEs ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            // now set the legal entity level override.
            creditAdminSvc.setLEOverride ( lpOrg, fiOrg, true );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "TOD" ), true );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, new Tenor ( "8D" ), true );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFiLe2, new Tenor ( "SPOT" ), true );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFiLe2, new Tenor ( "2W" ), true );
            creditAdminSvc.setOrgDefault ( lpOrg, fiOrg, lpTpForFi, false );
            creditAdminSvc.setOrgDefault ( lpOrg, fiOrg, lpTpForFiLe2, false );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            FXRateBasis rb = CreditUtilC.getStdQuoteConvention ().getFXRateBasis ( ccyPair.getBaseCurrency (), ccyPair.getVariableCurrency () );
            IdcDate spDate = rb.getSpotDate ( tradeDate );
            IdcDate oneWeekDate = rb.getValueDate ( tradeDate, new Tenor ( "1W" ) );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr1 );

            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt, false, "USD", ccyPair.getName (), oneWeekDate, new Tenor ( "1W" ) );
            np.getInputRequestPojos ().add ( ntr2 );

            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm );

            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "10D" ), true );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm1 );
            assertTrue ( cwm1.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR, cwm1.getErrorCode () );


            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, new Tenor ( "TOD" ), true );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFiLe2, Tenor.SPOT_TENOR, true );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNotNull ( cwm2 );
            assertTrue ( cwm2.getStatus () == MessageStatus.FAILURE );
            assertEquals ( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR, cwm2.getErrorCode () );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMinMaxTenorWithOrgExposureAndLELevelOverrideWithMultipleLEs", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFiLe2, null, true );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFiLe2, null, true );
            creditAdminSvc.setOrgDefault ( lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setOrgDefault ( lpOrg, fiOrg, lpTpForFiLe2, true );
            creditAdminSvc.setLEOverride ( lpOrg, fiOrg, false );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFiLe2, null );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFiLe2, null );
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForFiLe2, null );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForFiLe2, null );
        }
    }

    public void testPortfolioCreditCheckWithDailyNettingAtLeExposure_CreditDisabled ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            creditAdminSvc.setCreditEnabled ( lpOrg, false );
            creditAdminSvc.setCreditEnabled ( fiOrg, false );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 1200000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithDailyNettingAtLeExposure_CreditDisabled", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testPortfolioCreditCheckWithDailyNettingAtOrgExposure_CreditDisabled ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, false );

            // test a single sell trade in the test portfolio going above the limit. Credit check should fail.
            NettingPortfolio np = new NettingPortfolioC ();
            np.setName ( "test" );
            np.setPortfolioID ( "FXI1000" );
            np.getToOrganizations ().add ( lpOrg );

            double dealtAmt = 120000000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "USD", "JPY" );
            NettingTradeRequest ntr = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            np.getInputRequestPojos ().add ( ntr );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance ().checkPortfolioCredit ( np );
            assertNull ( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPortfolioCreditCheckWithDailyNettingAtOrgExposure_CreditDisabled", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheck ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 5000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message success status=" + cwm1, cwm1 == null || MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheck", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckFailure ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 50000000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message failure status=" + cwm1, MessageStatus.FAILURE.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckFailure", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }


    public void testTradeAllocationCreditCheckSuccessWithNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 900000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 450000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message success status=" + cwm1, cwm1 == null || MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckSuccessWithNetting", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckFailureWithNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 900000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 550000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message failure status=" + cwm1, MessageStatus.FAILURE.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckFailureWithNetting", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckSuccessWithReverseTradesNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 900000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 900000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message success status=" + cwm1, cwm1 == null || MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckSuccessWithReverseTradesNetting", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckFailureWithReverseTradesNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setOrganizationExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setOrganizationExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 900000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 5550000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message failure status=" + cwm1, MessageStatus.FAILURE.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckFailureWithReverseTradesNetting", e );
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckFailureMultipleLE_Aggregate ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 50000000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message failure status=" + cwm1, MessageStatus.FAILURE.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckFailureMultipleLE_Aggregate", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckFailureMultipleLE_Daily ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 10000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 50000000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message failure status=" + cwm1, MessageStatus.FAILURE.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckFailureMultipleLE_Daily", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckMultipleLE_Aggregate ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 800000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 900000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message success status=" + cwm1, cwm1 == null || MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckMultipleLE_Aggregate", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckMultipleLE_Daily ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 800000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 900000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message success status=" + cwm1, cwm1 == null || MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckMultipleLE_Daily", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckFailureMultipleLE_AggregateNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 900000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt1 = 1900000;
            double dealtAmt2 = 900000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt2, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message failure status=" + cwm1, MessageStatus.FAILURE.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckFailureMultipleLE_AggregateNetting", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckFailureMultipleLE_DailyNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, 5000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 900000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt1 = 1900000;
            double dealtAmt2 = 900000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt1, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt2, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message failure status=" + cwm1, MessageStatus.FAILURE.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckFailureMultipleLE_DailyNetting", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckMultipleLE_AggregateNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 900000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 900000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt * 2, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            nettingTradeRequests.add ( ntr3 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message success status=" + cwm1, cwm1 == null || MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckMultipleLE_AggregateNetting", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckMultipleLE_DailyNetting ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            double tradeAmt = 800000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 800000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt * 2, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            nettingTradeRequests.add ( ntr3 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message success status=" + cwm1, cwm1 == null || MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckMultipleLE_DailyNetting", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testTradeAllocationCreditCheckMultipleLE_SuspendedStatus ( )
    {
        try
        {
            init ( fiUser );
            removeExistingCreditUtilizationEvents ( fiOrg );
            removeExistingCreditUtilizationEvents ( lpOrg );

            creditAdminSvc.setLegalEntityExposureLevel ( lpOrg, fiOrg );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setCreditEnabled ( fiOrg, false );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );
            creditAdminSvc.setCreditStatus ( lpOrg, lpTpForFiLe2, CreditLimitConstants.CREDIT_SUSPEND );

            double tradeAmt = 900000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays ( 10 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals ( "Message success status=" + cwm.getStatus (), cwm.getStatus (), MessageStatus.SUCCESS );

            double dealtAmt = 9000;
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( "EUR", "USD" );
            NettingTradeRequest ntr1 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, false, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr2 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe, dealtAmt, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            NettingTradeRequest ntr3 = DealingTestUtil.createTestSingleLegNettingTradeRequest ( fiLe2, dealtAmt, true, "USD", ccyPair.getName (), spotDate, Tenor.SPOT_TENOR );
            Collection<NettingTradeRequest> nettingTradeRequests = new ArrayList<NettingTradeRequest> ();
            nettingTradeRequests.add ( ntr1 );
            nettingTradeRequests.add ( ntr2 );
            nettingTradeRequests.add ( ntr3 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance ().checkTradeAllocationsCredit ( trade, nettingTradeRequests );
            assertTrue ( "Message failure status=" + cwm1, cwm1 != null && MessageStatus.FAILURE.equals ( cwm1.getStatus () ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeAllocationCreditCheckMultipleLE_SuspendedStatus", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( fiOrg, true );
            creditAdminSvc.setCreditEnabled ( lpOrg, true );
            creditAdminSvc.setCreditStatus ( lpOrg, lpTpForFiLe2, CreditLimitConstants.CREDIT_ACTIVE );

            setCalcAndLimit ( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit ( lpOrg, lpTpForFiLe2, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
        }
    }
}
