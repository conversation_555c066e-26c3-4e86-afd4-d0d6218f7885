package com.integral.finance.creditLimit.test;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.finance.creditLimit.CreditLimitFactory;
import com.integral.finance.creditLimit.CreditLimitRule;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


public class CreditLimitRulePTestC extends PTestCaseC
{
    static String name = "CreditLimitRule Test";
    Vector users = null;

    public CreditLimitRulePTestC( String name )
    {
        super( name );
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CreditLimitRule testClr = CreditLimitFactory.newCreditLimitRule();
            testClr.setShortName( "Test_" + System.nanoTime() );
            assertNull( testClr.isDailyPL() );
            assertNull( testClr.isApplyPandL() );
            assertNull( testClr.isUpdateBalanceWithPL() );
            assertEquals( testClr.getLimitLastUpdatedTime(), 0 );
            assertTrue ( testClr.isUsePFEConfiguration () );

            CreditLimitRule registeredTestClr = ( CreditLimitRule ) uow.registerObject( testClr );
            assertNull( registeredTestClr.isDailyPL() );
            assertNull( registeredTestClr.isApplyPandL() );
            assertNull( registeredTestClr.isUpdateBalanceWithPL() );
            assertTrue ( registeredTestClr.isUsePFEConfiguration () );

            registeredTestClr.setDailyPL( true );
            registeredTestClr.setApplyPandL( true );
            registeredTestClr.setUpdateBalanceWithPL( true );
            registeredTestClr.setUsePFEConfiguration ( false );
            long now = System.currentTimeMillis();
            registeredTestClr.setLimitLastUpdatedTime( now );
            uow.commit();

            // refresh the trade and verify the fields
            testClr = ( CreditLimitRule ) session.refreshObject( testClr );
            log( "testClr=" + testClr + ",testClr.dailyPL=" + testClr.isDailyPL() + ",testClr.applyPL="
                    + testClr.isApplyPandL() + ",updateBalanceWithPL=" + testClr.isUpdateBalanceWithPL() );
            assertTrue( testClr.isApplyPandL() );
            assertTrue( testClr.isUpdateBalanceWithPL() );
            assertTrue( testClr.isDailyPL() );
            assertTrue( testClr.getLimitLastUpdatedTime() == now );
            assertFalse ( testClr.isUsePFEConfiguration () );
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence : ", e );
        }
    }
}
