package com.integral.finance.creditLimit.test;

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.creditLimit.CreditWorkflowRidersDefault;
import com.integral.finance.dealing.Request;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

/**
 * Tests the credit limit service API for validation of parameters.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitServiceValidationPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Service Validation Test";

    public CreditLimitServiceValidationPTestC( String name )
    {
        super( name );
    }

    public void testTakeCreditInsufficientParameters()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            CreditWorkflowMessage cwm = creditMgr.takeCredit( null, lpLe, ddOrg, lpTpForDd );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade, null, ddOrg, lpTpForDd );
            log( "success : " + cwm1.getStatus() );
            assertEquals( "status=" + cwm1.getStatus(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm1 );

            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade, lpLe, null, lpTpForDd );
            log( "success : " + cwm2.getStatus() );
            assertEquals( "status=" + cwm2.getStatus(), cwm2.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm2 );

            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade, lpLe, ddOrg, null );
            log( "success : " + cwm3.getStatus() );
            assertEquals( "status=" + cwm3.getStatus(), cwm3.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm3 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testReserveCreditInsufficientParameters()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( null, lpLe, ddOrg, lpTpForDd );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            Request request = prepareSingleLegRequest( ( FXSingleLeg ) trade );
            CreditWorkflowMessage cwm1 = creditMgr.reserveCredit( request, null, ddOrg, lpTpForDd );
            log( "success : " + cwm1.getStatus() );
            assertEquals( "status=" + cwm1.getStatus(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm1 );

            CreditWorkflowMessage cwm2 = creditMgr.reserveCredit( request, lpLe, null, lpTpForDd );
            log( "success : " + cwm2.getStatus() );
            assertEquals( "status=" + cwm2.getStatus(), cwm2.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm2 );

            CreditWorkflowMessage cwm3 = creditMgr.reserveCredit( request, lpLe, ddOrg, null );
            log( "success : " + cwm3.getStatus() );
            assertEquals( "status=" + cwm3.getStatus(), cwm3.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm3 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testUndoCreditInsufficientParameters()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            CreditWorkflowMessage cwm = creditMgr.undoCredit( null, lpLe, ddOrg, lpTpForDd );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.undoCredit( trade, null, ddOrg, lpTpForDd );
            log( "success : " + cwm1.getStatus() );
            assertEquals( "status=" + cwm1.getStatus(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm1 );

            CreditWorkflowMessage cwm2 = creditMgr.undoCredit( trade, lpLe, null, lpTpForDd );
            log( "success : " + cwm2.getStatus() );
            assertEquals( "status=" + cwm2.getStatus(), cwm2.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm2 );

            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade, lpLe, ddOrg, null );
            log( "success : " + cwm3.getStatus() );
            assertEquals( "status=" + cwm3.getStatus(), cwm3.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm3 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testUndoReserveCreditInsufficientParameters()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            CreditWorkflowMessage cwm = creditMgr.undoReserve( null, lpLe, ddOrg, lpTpForDd );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            Request request = prepareSingleLegRequest( ( FXSingleLeg ) trade );
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, null, ddOrg, lpTpForDd );
            log( "success : " + cwm1.getStatus() );
            assertEquals( "status=" + cwm1.getStatus(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm1 );

            CreditWorkflowMessage cwm2 = creditMgr.undoReserve( request, lpLe, null, lpTpForDd );
            log( "success : " + cwm2.getStatus() );
            assertEquals( "status=" + cwm2.getStatus(), cwm2.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm2 );

            CreditWorkflowMessage cwm3 = creditMgr.undoReserve( request, lpLe, ddOrg, null );
            log( "success : " + cwm3.getStatus() );
            assertEquals( "status=" + cwm3.getStatus(), cwm3.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm3 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testTakeCreditNullCounterparty()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade.setCounterpartyA( null );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            trade1.setCounterpartyB( null );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            log( "success : " + cwm1.getStatus() );
            assertEquals( "status=" + cwm1.getStatus(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm1 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testTakeCreditMaturedTrade()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.subtractDays( 20 ) );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSameProviderAndCounterpartyOrg()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.subtractDays( 20 ) );
            Request request = prepareSingleLegRequest( ( FXSingleLeg ) trade );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, lpOrg, lpTpForDd );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );

            CreditWorkflowMessage cwm1 = creditMgr.reserveCredit( request, lpLe, lpOrg, lpTpForDd );
            log( "success : " + cwm1.getStatus() );
            assertEquals( "status=" + cwm1.getStatus(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm1 );

            CreditWorkflowMessage cwm2 = creditMgr.undoReserve( request, lpLe, lpOrg, lpTpForDd );
            log( "success : " + cwm2.getStatus() );
            assertEquals( "status=" + cwm2.getStatus(), cwm2.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm2 );

            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade, lpLe, lpOrg, lpTpForDd );
            log( "success : " + cwm3.getStatus() );
            assertEquals( "status=" + cwm3.getStatus(), cwm3.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm3 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSameProviderAndCounterparty()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.subtractDays( 20 ) );
            Request request = prepareSingleLegRequest( ( FXSingleLeg ) trade );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, ( TradingParty ) lpLe );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );

            CreditWorkflowMessage cwm1 = creditMgr.reserveCredit( request, lpLe, ddOrg, ( TradingParty ) lpLe );
            log( "success : " + cwm1.getStatus() );
            assertEquals( "status=" + cwm1.getStatus(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm1 );

            CreditWorkflowMessage cwm2 = creditMgr.undoReserve( request, lpLe, ddOrg, ( TradingParty ) lpLe );
            log( "success : " + cwm2.getStatus() );
            assertEquals( "status=" + cwm2.getStatus(), cwm2.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm2 );

            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade, lpLe, ddOrg, ( TradingParty ) lpLe );
            log( "success : " + cwm3.getStatus() );
            assertEquals( "status=" + cwm3.getStatus(), cwm3.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditCounterpartyDifferent()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.subtractDays( 20 ) );
            Request request = prepareSingleLegRequest( ( FXSingleLeg ) trade );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, ( TradingParty ) fiLe );
            log( "success : " + cwm.getStatus() );
            assertEquals( "status=" + cwm.getStatus(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm );

            CreditWorkflowMessage cwm1 = creditMgr.reserveCredit( request, lpLe, ddOrg, ( TradingParty ) fiLe );
            log( "success : " + cwm1.getStatus() );
            assertEquals( "status=" + cwm1.getStatus(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm1 );

            CreditWorkflowMessage cwm2 = creditMgr.undoReserve( request, lpLe, ddOrg, ( TradingParty ) fiLe );
            log( "success : " + cwm2.getStatus() );
            assertEquals( "status=" + cwm2.getStatus(), cwm2.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm2 );

            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade, lpLe, ddOrg, ( TradingParty ) fiLe );
            log( "success : " + cwm3.getStatus() );
            assertEquals( "status=" + cwm3.getStatus(), cwm3.getStatus().equals( MessageStatus.FAILURE ), true );
            validateCreditUtilizationEvents( cwm3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testBilateralCreditWorkflowValidation()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.subtractDays( 20 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( null, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "failure status", MessageStatus.FAILURE, cwm.getStatus() );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( cptyLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "failure status", MessageStatus.FAILURE, cwm1.getStatus() );
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( cptyLe, cptyLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "failure status", MessageStatus.FAILURE, cwm2.getStatus() );

            CreditWorkflowMessage cwm3 = creditMgr.undoBilateralCredit( null, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "failure status", MessageStatus.FAILURE, cwm3.getStatus() );
            CreditWorkflowMessage cwm4 = creditMgr.undoBilateralCredit( cptyLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "failure status", MessageStatus.FAILURE, cwm4.getStatus() );
            CreditWorkflowMessage cwm5 = creditMgr.undoBilateralCredit( cptyLe, cptyLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "failure status", MessageStatus.FAILURE, cwm5.getStatus() );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
