package com.integral.finance.creditLimit.test;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationMBean;
import com.integral.finance.creditLimit.notification.CreditLimitInfo;
import com.integral.finance.creditLimit.notification.CreditStatus;
import com.integral.finance.creditLimit.notification.CreditLimitInfoNotifier;
import com.integral.finance.creditLimit.notification.multicast.CreditMulticastMessageSender;
import com.integral.message.UnSafeBufferMessageSender;
import org.junit.Assert;
import org.junit.Test;


import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;

public class CreditLimitInfoNotifierTest
{
    long fiLe = 1000000;
    long lpLe = 2000000;
    int limitCcy = 1000;
    long aggLimit = 10000000;
    long aggAvailable = 9000000;
    long dailyLimit = 20000000;
    long dailyAvailable = 19000000;
    short valDate = 5000;

	@Test
	public void testCreditLimitNotification()
	{
        try
        {
            TestMessageSender msgSender = new TestMessageSender( );
            CreditLimitInfoNotifier notifier = new CreditLimitInfoNotifier( msgSender );
            Collection<CreditLimitInfo> lines = new ArrayList<CreditLimitInfo>();
            for ( int i=0; i < 1000; i++ )
            {
                for (int j = 0; j < 1000; j++)
                {
                    CreditStatus status = Math.random() > 0.5 ? CreditStatus.ACTIVE : CreditStatus.NO_CHECK;
                    CreditLimitInfo cli = createCreditLimitInfo(fiLe + j, lpLe + j, status, (short) (limitCcy + j), aggLimit + j, aggAvailable + j, dailyLimit + j, dailyAvailable + j, (short) (valDate + j));
                    lines.add(cli);
                    if ( i ==  j )
                    {
                        System.out.println( " Sending lines count=" + lines.size() + ",i=" + i + ",j=" + j);
                        notifier.sendCreditLimitNotification(lines);
                        lines.clear();
                        break;
                    }
                }
            }

            msgSender.inspect();
        }
        catch( Exception e )
        {
            e.printStackTrace();
            Assert.fail();
        }
	}

    @Test
    public void testCreditLimitNotificationViaMulticast()
    {
        try
        {
            CreditLimitConfigurationMBean mbean = CreditLimitConfigurationFactory.getCreditConfigurationMBean();
            CreditMulticastMessageSender msgSender = new CreditMulticastMessageSender( InetAddress.getByName(mbean.getMulticastAddress()),mbean.getMulticastPort()  );
            CreditLimitInfoNotifier notifier = new CreditLimitInfoNotifier( msgSender );
            Collection<CreditLimitInfo> lines = new ArrayList<CreditLimitInfo>();
            CreditLimitInfo cli = createCreditLimitInfo(fiLe, lpLe, CreditStatus.ACTIVE, (short)limitCcy, aggLimit, aggAvailable, dailyLimit, dailyAvailable, valDate);
            lines.add(cli);
            notifier.sendCreditLimitNotification( lines );
        }
        catch( Exception e )
        {
            e.printStackTrace();
            Assert.fail();
        }
    }


    private CreditLimitInfo createCreditLimitInfo( long fiLe, long lpLe, CreditStatus status, short limitCcy, long aggLimit, long aggAvailable, long dailyLimit, long dailyAvailable, short valueDate)
    {
        CreditLimitInfo info = new CreditLimitInfo( fiLe, lpLe, valueDate );
        info.setCreditStatus(status);
        info.setLimitCcy(limitCcy);
        info.setAggregateLimit(aggLimit);
        info.setAggregateAvailable(aggAvailable);
        info.setDailyLimit(dailyLimit);
        info.setDailyAvailable(dailyAvailable);
		return info;
	}

    private class TestMessageSender implements UnSafeBufferMessageSender
    {
        private int counter = 0;
        private Map<Integer, UnSafeBuffer> bufferMap = new LinkedHashMap<Integer, UnSafeBuffer>();

        public void send ( UnSafeBuffer buff )
        {
            bufferMap.put( counter, buff );
            ++counter;
        }

        public void inspect()
        {
            for ( UnSafeBuffer buff: bufferMap.values() )
            {
                // remove the msgtype and version.
                byte version = buff.get();
                short msgType = buff.getShort();
                double totalSize = buff.limit();
                int linesSize = (int) totalSize / CreditLimitInfo.MSG_SIZE;
                System.out.println( "byteSize=" + totalSize + ",lineSize=" + linesSize + ",msgType=" + msgType + ",version=" + version );

                for (int i = 0; i < linesSize; i++)
                {
                    CreditLimitInfo cli = new CreditLimitInfo( 0, 0, (short) 0);
                    cli.readFrom( buff );
                    System.out.println("cli=" + cli);
                    Assert.assertTrue(cli.getFiLe() >= fiLe);
                    Assert.assertTrue( cli.getLpLe() >= lpLe );
                    Assert.assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE || cli.getCreditStatus() == CreditStatus.NO_CHECK );
                    Assert.assertTrue( cli.getLimitCcy() >= limitCcy );
                    Assert.assertTrue( cli.getAggregateLimit() >= aggLimit );
                    Assert.assertTrue( cli.getAggregateAvailable() >= aggAvailable );
                    Assert.assertTrue( cli.getDailyLimit() >= dailyLimit );
                    Assert.assertTrue( cli.getDailyAvailable() >= dailyAvailable );
                    Assert.assertTrue( cli.getValueDate() >= valDate );
                }
            }
        }

    }
}
