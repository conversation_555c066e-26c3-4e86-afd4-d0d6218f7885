package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.audit.AuditEvent;
import com.integral.audit.AuditFactory;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.audit.CreditAuditEventParameters;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditEventFacade;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditEventFacadeC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcTransaction;
import com.integral.user.UserC;
import com.integral.util.MessageFormat;

import java.util.Enumeration;
import java.util.ResourceBundle;


/**
 * Tests the credit limit admin audit events storing and display.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAdminAuditPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Admin Audit Test";

    public CreditLimitAdminAuditPTestC( String name )
    {
        super( name );
    }

    public void testCreditLimitAdminAudit()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( false );
            tx.getUOW().addReadOnlyClass( UserC.class );
            AuditEvent newAuditEvent = AuditFactory.newAuditEvent( "test", "test", "" );
            AuditEvent registeredEvent = ( AuditEvent ) newAuditEvent.getRegisteredObject();

            CreditLimitAdminAuditEventFacade auditFacade = ( CreditLimitAdminAuditEventFacade ) registeredEvent.getFacade( CreditLimitAdminAuditEventFacade.FACADE_NAME );
            auditFacade.setUser( lpUser );
            auditFacade.setCreditProviderOrganization( lpOrg );
            auditFacade.setCreditProviderLegalEntity( lpLe );
            auditFacade.setCreditCounterpartyOrganization( fiOrg );
            auditFacade.setCreditCounterparty( lpTpForDd );
            auditFacade.setLimitCurrency( CreditUtilC.getLimitCurrency( lpOrg ) );
            auditFacade.setExistingNettingMethodology( CreditLimitConstants.GROSS_DAILY_LIMIT_CALCULATOR );
            auditFacade.setNewNettingMethodology( CreditLimitConstants.GROSS_AGGREGATE_LIMIT_CALCULATOR );
            auditFacade.setSettlementDate( spotDate );
            auditFacade.setExistingLimitAmount( 100.00 );
            auditFacade.setNewLimitAmount( 101.00 );
            auditFacade.setExistingNotificationPercentage( new Double( 90 ) );
            auditFacade.setNewNotificationPercentage( new Double( 91 ) );
            auditFacade.setExistingWarningPercentage( new Double( 95 ) );
            auditFacade.setNewWarningPercentage( new Double( 96 ) );
            auditFacade.setExistingSuspensionPercentage( new Double( 98 ) );
            auditFacade.setNewSuspensionPercentage( new Double( 99 ) );
            auditFacade.setExistingEmailAddress( "<EMAIL>" );
            auditFacade.setNewEmailAddress( "<EMAIL>" );
            auditFacade.setUtilizationPercentage( new Double( 80 ) );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setCreditCounterpartyShortName( lpTpForDd.getShortName() );
            commitTransaction( tx );

            AuditEvent refreshedAuditEvent = ( AuditEvent ) PersistenceFactory.newSession().refreshObject( newAuditEvent );
            CreditLimitAdminAuditEventFacade refreshedAuditFacade = ( CreditLimitAdminAuditEventFacade ) refreshedAuditEvent.getFacade( CreditLimitAdminAuditEventFacade.FACADE_NAME );
            log( "RefreshedAuditEvent description=" + refreshedAuditFacade.getDescription() );

            CreditLimitAdminAuditEventFacadeC fcd = ( CreditLimitAdminAuditEventFacadeC ) refreshedAuditFacade;
            ResourceBundle rb = ResourceBundle.getBundle( fcd.getResourceBundleName(), adminUser.getDisplayPreference().getLocale(), fcd.getClass().getClassLoader() );
            Enumeration keys = rb.getKeys();
            while ( keys.hasMoreElements() )
            {
                String key = ( String ) keys.nextElement();
                log( "key=" + key + ",description=" + MessageFormat.format( rb.getString( key ), fcd.getMessageArgs() ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
