package com.integral.finance.creditLimit.test;

import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationMBean;
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean;
import com.integral.finance.creditLimit.enums.StopOutState;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Request;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;

import java.util.Date;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

/**
 * Tests the credit utilization notification upon utilization levels going above notification, warning and suspension levels.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationNotificationPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Utilization Notification Test";
    private String emailIds = "<EMAIL>";
    private double notificationPercent = 70;
    private double warningPercent = 80;
    private double suspensionPercent = 90;
    private double stopOutPercent = 90.1;

    private Double notificationPercentage = notificationPercent;
    private Double warningPercentage = warningPercent;
    private Double suspensionPercentage = suspensionPercent;
    private Double stopOutPercentage = stopOutPercent;

    public CreditUtilizationNotificationPTestC( String name )
    {
        super( name );
    }

    public void testSimpleProviderNotificationEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );

            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm1.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt1 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do another trade where the amount is slightly greater than notification percentage.
            double tradeAmt2 = ( dailyLimit * notificationPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // now do another trade which exceeds notification percentage again.
            Date notifiedDate = cclr.getNotificationDate();
            double tradeAmt3 = 1000;
            Trade trade3 = prepareSingleLegTrade( tradeAmt3, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit3 + " success : " + cwm3.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt3, true, Math.abs( limit2 - limit3 - tradeAmt3 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm3 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
            assertEquals( "notified date should be the earlier one", cclr.getNotificationDate(), notifiedDate );

            // now do another trade to breach the warning level.
            double tradeAmt4 = ( dailyLimit * ( warningPercent - notificationPercent ) ) / 100;
            Trade trade4 = prepareSingleLegTrade( tradeAmt4, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit4 + " success : " + cwm4.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() + ",tradeAmt=" + tradeAmt4 );
            assertEquals( "taken trade amt=" + tradeAmt4, true, Math.abs( limit3 - limit4 - tradeAmt4 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm4 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
            assertEquals( "notified date should be the earlier one", cclr.getNotificationDate(), notifiedDate );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testProviderDailyWarningAggregateNotificationEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );

            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double aggLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "aggregate limit0=" + aggLimit0 );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double aggLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "aggregate limit1=" + aggLimit1 );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm1.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt1 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do another trade where the amount is equivalent to the notification percentage for aggregate but, on warning level on daily.
            double tradeAmt2 = ( ( aggregateLimit * notificationPercent ) / 100 ) + 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double aggLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "aggregate limit2=" + aggLimit2 );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testProviderAggregateWarningDailyNotificationEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1200000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );

            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm1.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt1 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do another trade where the amount is equivalent to the warning percentage for aggregate but, on notification level on daily.
            double tradeAmt2 = ( ( dailyLimit * notificationPercent ) / 100 ) + 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testProviderDailySuspensionAggregateWarningEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1125000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for daily but, on warning level on aggregate.
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double tradeAmt2 = ( aggregateLimit * warningPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testProviderAggregateSuspensionDailyWarningEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1125000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for aggregate but, on warning level on daily.
            double tradeAmt2 = ( dailyLimit * warningPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testProviderDailySuspensionAggregateNormalEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 20000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for daily but, on normal level on aggregate.
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double tradeAmt2 = ( dailyLimit * suspensionPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testProviderAggregateSuspensionDailyNormalEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 125000000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for aggregate but, on normal level on daily.
            double tradeAmt2 = ( aggregateLimit * suspensionPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSimpleCounterpartyNotificationEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );

            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm1.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt1 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do another trade where the amount is slightly greater than notification percentage.
            double tradeAmt2 = ( dailyLimit * notificationPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // now do another trade which exceeds notification percentage again.
            Date notifiedDate = cclr.getNotificationDate();
            double tradeAmt3 = 1000;
            Trade trade3 = prepareSingleLegTrade( tradeAmt3, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit3 + " success : " + cwm3.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt3, true, Math.abs( limit2 - limit3 - tradeAmt3 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm3 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
            assertEquals( "notified date should be the earlier one", cclr.getNotificationDate(), notifiedDate );

            // now do another trade to breach the warning level.
            double tradeAmt4 = ( dailyLimit * ( warningPercent - notificationPercent ) ) / 100;
            Trade trade4 = prepareSingleLegTrade( tradeAmt4, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit4 + " success : " + cwm4.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() + ",tradeAmt=" + tradeAmt4 );
            assertEquals( "taken trade amt=" + tradeAmt4, true, Math.abs( limit3 - limit4 - tradeAmt4 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm4 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
            assertEquals( "notified date should be the earlier one", cclr.getNotificationDate(), notifiedDate );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCounterpartyDailyWarningAggregateNotificationEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );

            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm1.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt1 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do another trade where the amount is equivalent to the notification percentage for aggregate but, on warning level on daily.
            double tradeAmt2 = ( aggregateLimit * notificationPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCounterpartyAggregateWarningDailyNotificationEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1200000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );

            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm1.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt1 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do another trade where the amount is equivalent to the warning percentage for aggregate but, on notification level on daily.
            double tradeAmt2 = ( dailyLimit * notificationPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCounterpartyDailySuspensionAggregateWarningEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1125000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for daily but, on warning level on aggregate.
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double tradeAmt2 = ( aggregateLimit * warningPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCounterpartyAggregateSuspensionDailyWarningEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1125000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for aggregate but, on warning level on daily.
            double tradeAmt2 = ( dailyLimit * warningPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should not be null.", cclr.getWarningDate() != null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCounterpartyDailySuspensionAggregateNormalEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 20000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for daily but, on normal level on aggregate.
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double tradeAmt2 = ( dailyLimit * suspensionPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCounterpartyAggregateSuspensionDailyNormalEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 125000000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for aggregate but, on normal level on daily.
            double tradeAmt2 = ( aggregateLimit * suspensionPercent ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            sleepFor( 2000 );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testRoundedTradingSuspensionPercentageEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 8163266;
            double aggregateLimit = 90000000;
            Double suspensionPercent = ( double ) 98;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercent );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do trade where the amount is equivalent to the suspension percentage for aggregate but, on normal level on daily.
            double tradeAmt2 = 8000000;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should not be null.", cclr.getSuspensionDate() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCounterpartyNotificationSettings()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double orgLevelNotification = 10;
            double orgLevelWarning = 12;
            double orgLevelSuspension = 14;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, orgLevelNotification, orgLevelWarning, orgLevelSuspension );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );

            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = dailyLimit * orgLevelSuspension / 100;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm1.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt1 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm1 );
            sleepFor( 3000 );
            assertEquals( "notified time should be null.", cclr.getNotificationDate() == null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );

            // do another trade where the amount is slightly greater than notification percentage.
            double tradeAmt2 = ( dailyLimit * ( notificationPercent - orgLevelSuspension ) ) / 100;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm2.getStatus() + ",notifiedDate=" + cclr.getNotificationDate() );
            assertEquals( "taken trade amt=" + tradeAmt2, true, Math.abs( limit1 - limit2 - tradeAmt2 ) < MINIMUM );
            validateCreditUtilizationEvents( cwm2 );
            sleepFor( 3000 );
            assertEquals( "notified time should not be null.", cclr.getNotificationDate() != null, true );
            assertEquals( "warning time should be null.", cclr.getWarningDate() == null, true );
            assertEquals( "suspension time should be null.", cclr.getSuspensionDate() == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testObsoleteValueDateRejectionEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000000 );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            creditAdminSvc.setEnableRejectionEmail( lpOrg, true );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.subtractDays( 15 ) );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.subtractDays( 15 ) ) );
            CreditWorkflowMessage cwm1 = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );

            Trade swapTrade = prepareSwapTrade( tradeAmt, tradeAmt, true, true, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate.subtractDays( 15 ), spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( swapTrade, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.FAILURE );

            Request swapRequest = prepareSwapRequest( prepareSwapTrade( tradeAmt, tradeAmt, false, false, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate.subtractDays( 15 ) ) );
            CreditWorkflowMessage cwm3 = creditMgr.reserveCredit( swapRequest, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNoMarketConversionRejectionEmail()
    {
        try
        {
            init( lpUser );
            if ( noFXRateCurrencyPairs.isEmpty() )
            {
                return;
            }
            CurrencyPair ccyPair = ( CurrencyPair ) noFXRateCurrencyPairs.toArray()[0];
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000000 );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            creditAdminSvc.setEnableRejectionEmail( lpOrg, true );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, ccyPair.getName(), lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, false, false, ccyPair.getName(), lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm1 = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );

            Trade swapTrade = prepareSwapTrade( tradeAmt, tradeAmt, true, true, false, false, ccyPair.getName(), lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( swapTrade, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.FAILURE );

            Request swapRequest = prepareSwapRequest( prepareSwapTrade( tradeAmt, tradeAmt, false, false, false, false, ccyPair.getName(), lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate ) );
            CreditWorkflowMessage cwm3 = creditMgr.reserveCredit( swapRequest, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testInsufficientCreditRejectionEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000000 );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            creditAdminSvc.setEnableRejectionEmail( lpOrg, true );

            double tradeAmt = 100000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate ) );
            CreditWorkflowMessage cwm1 = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );

            Trade swapTrade = prepareSwapTrade( tradeAmt, tradeAmt, true, true, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( swapTrade, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.FAILURE );

            Request swapRequest = prepareSwapRequest( prepareSwapTrade( tradeAmt, tradeAmt, false, false, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate ) );
            CreditWorkflowMessage cwm3 = creditMgr.reserveCredit( swapRequest, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNoCreditUtilizationRejectionEmail()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000000 );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, false, notificationPercentage, warningPercentage, suspensionPercentage );
            creditAdminSvc.setEnableRejectionEmail( lpOrg, true );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1000 ) );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE );

            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1000 ) ) );
            CreditWorkflowMessage cwm1 = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );

            Trade swapTrade = prepareSwapTrade( tradeAmt, tradeAmt, true, true, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate.addDays( 1000 ), spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( swapTrade, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.FAILURE );

            Request swapRequest = prepareSwapRequest( prepareSwapTrade( tradeAmt, tradeAmt, false, false, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 1000 ) ) );
            CreditWorkflowMessage cwm3 = creditMgr.reserveCredit( swapRequest, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSimpleStopOutNotification()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_PERIOD, "1000", ConfigurationProperty.DYNAMIC_SCOPE );
            TestMarginCallService marginSvc = new TestMarginCallService ();
            CreditMarginLiquidationServiceC.getInstance ().registerMarginCallService ( marginSvc );
            ConfigurationFactory.getServerMBean().setApplicationStarted ( true );

            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage, stopOutPercentage, true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );


            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = ( aggregateLimit * suspensionPercent * 0.01 ) - 100;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.SUCCESS, cwm1.getStatus() );

            //now set the credit limit lower
            creditAdminSvc.setCreditLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, aggregateLimit * 0.2, creditAdminSvc.getCreditLimitCurrency ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION ) );

            sleepFor ( 15000 );

            int state = marginSvc.getState ();
            assertEquals ( StopOutState.TRIGGERED.ordinal (), state );
            assertEquals ( StopOutState.TRIGGERED.ordinal (), cclr.getStopOutState ().ordinal () );
            CreditWorkflowMessage cwm = CreditLimitPOJOFactory.newCreditWorkflowMessage ();
            cwm.setLegalEntity ( lpLe );
            cwm.setOrganization ( lpOrg );
            cwm.setTradingParty ( lpTpForDd );
            cwm.setTradingPartyOrganization ( ddOrg );
            marginSvc.onClosePositionsStatusUpdate ( cwm );
            state = marginSvc.getState ();
            assertEquals ( StopOutState.EXECUTED.ordinal (), state );
            assertEquals ( StopOutState.EXECUTED.ordinal (), cclr.getStopOutState ().ordinal () );
        }
        catch ( Exception e )
        {
            fail( "testSimpleStopOutNotification", e);
        }
        finally
        {
            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_PERIOD, "60000", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testStopOutNotificationIncomplete()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_PERIOD, "1000", ConfigurationProperty.DYNAMIC_SCOPE );
            TestMarginCallService marginSvc = new TestMarginCallService ();
            CreditMarginLiquidationServiceC.getInstance ().registerMarginCallService ( marginSvc );
            ConfigurationFactory.getServerMBean().setApplicationStarted ( true );

            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage, stopOutPercentage, true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );


            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = ( aggregateLimit * suspensionPercent * 0.01 ) - 100;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.SUCCESS, cwm1.getStatus() );

            //now set the credit limit lower
            creditAdminSvc.setCreditLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, aggregateLimit * 0.2, creditAdminSvc.getCreditLimitCurrency ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION ) );

            sleepFor ( 15000 );

            int state = marginSvc.getState ();
            assertEquals ( StopOutState.TRIGGERED.ordinal (), state );
            assertEquals ( StopOutState.TRIGGERED.ordinal (), cclr.getStopOutState ().ordinal () );

            sleepFor ( 10000 );
            state = marginSvc.getState ();
            assertEquals ( StopOutState.TRIGGERED.ordinal (), state );
            assertEquals ( StopOutState.TRIGGERED.ordinal (), cclr.getStopOutState ().ordinal () );
        }
        catch ( Exception e )
        {
            fail( "testStopOutNotificationIncomplete", e);
        }
        finally
        {
            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_PERIOD, "60000", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testRepeatStopOutNotification()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            resetNotificationDate( lpOrg );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_PERIOD, "1000", ConfigurationProperty.DYNAMIC_SCOPE );
            TestMarginCallService marginSvc = new TestMarginCallService ();
            CreditMarginLiquidationServiceC.getInstance ().registerMarginCallService ( marginSvc );
            ConfigurationFactory.getServerMBean().setApplicationStarted ( true );

            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, aggregateLimit );
            setNotificationSettings( lpOrg, lpTpForDd, null, false, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, null, true, null, null, null );
            setNotificationSettings( lpOrg, lpTpForDd, emailIds, true, notificationPercentage, warningPercentage, suspensionPercentage, stopOutPercentage, true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );


            // now do a trade with an amount which is less than the notification percentage
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt1 = ( aggregateLimit * suspensionPercent * 0.01 ) - 100;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.SUCCESS, cwm1.getStatus() );

            //now set the credit limit lower
            creditAdminSvc.setCreditLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, aggregateLimit * 0.2, creditAdminSvc.getCreditLimitCurrency ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION ) );

            sleepFor ( 15000 );

            int state = marginSvc.getState ();
            assertEquals ( StopOutState.TRIGGERED.ordinal (), state );
            assertEquals ( StopOutState.TRIGGERED.ordinal (), cclr.getStopOutState ().ordinal () );
            CreditWorkflowMessage cwm = CreditLimitPOJOFactory.newCreditWorkflowMessage ();
            cwm.setLegalEntity ( lpLe );
            cwm.setOrganization ( lpOrg );
            cwm.setTradingParty ( lpTpForDd );
            cwm.setTradingPartyOrganization ( ddOrg );
            marginSvc.onClosePositionsStatusUpdate ( cwm );
            state = marginSvc.getState ();
            assertEquals ( StopOutState.EXECUTED.ordinal (), state );
            assertEquals ( StopOutState.EXECUTED.ordinal (), cclr.getStopOutState ().ordinal () );

            creditAdminSvc.setCreditLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, aggregateLimit, creditAdminSvc.getCreditLimitCurrency ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION ) );

            // do a reverse trade
            Trade trade1x = prepareSingleLegTrade( tradeAmt1, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1x = creditMgr.takeCredit( trade1x, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm1x.getStatus(), MessageStatus.SUCCESS, cwm1x.getStatus() );


            Trade trade2 = prepareSingleLegTrade( tradeAmt1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            assertEquals( "Message success status=" + cwm2.getStatus(), MessageStatus.SUCCESS, cwm2.getStatus() );

            //now set the credit limit lower
            creditAdminSvc.setCreditLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, aggregateLimit * 0.2, creditAdminSvc.getCreditLimitCurrency ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION ) );

            sleepFor ( 15000 );

            state = marginSvc.getState ();
            assertEquals ( StopOutState.TRIGGERED.ordinal (), state );
            assertEquals ( StopOutState.TRIGGERED.ordinal (), cclr.getStopOutState ().ordinal () );
            CreditWorkflowMessage cwm3 = CreditLimitPOJOFactory.newCreditWorkflowMessage ();
            cwm3.setLegalEntity ( lpLe );
            cwm3.setOrganization ( lpOrg );
            cwm3.setTradingParty ( lpTpForDd );
            cwm3.setTradingPartyOrganization ( ddOrg );
            marginSvc.onClosePositionsStatusUpdate ( cwm3 );
            state = marginSvc.getState ();
            assertEquals ( StopOutState.EXECUTED.ordinal (), state );
            assertEquals ( StopOutState.EXECUTED.ordinal (), cclr.getStopOutState ().ordinal () );
        }
        catch ( Exception e )
        {
            fail( "testRepeatStopOutNotification", e);
        }
        finally
        {
            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_PERIOD, "60000", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    private class TestMarginCallService implements MarginCallService
    {
        int state = StopOutState.INITIAL.ordinal ();

        public int getState()
        {
            return state;
        }

        public void initialize()
        {

        }

        public void closePositions( CreditWorkflowMessage cwm )
        {
            log( "TMCS.closePositions - received instruction to close out positions." );
            state = StopOutState.TRIGGERED.ordinal ();
            cwm.setStatus( MessageStatus.SUCCESS );
        }

        public void onClosePositionsStatusUpdate( CreditWorkflowMessage cwm )
        {
            log( "TMCS.onClosePositionsStatusUpdate - received update on close out positions status." );
            CreditMarginLiquidationServiceC.getInstance ().onClosePositionsStatusUpdate ( cwm );
            state = StopOutState.EXECUTED.ordinal ();
            cwm.setStatus( MessageStatus.SUCCESS );
        }
    }
}
