package com.integral.finance.creditLimit.test;

// Copyright (c) 2019 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.configuration.*;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class CreditLimitServiceConfigurationMBeanTestC extends MBeanTestCaseC
{
    private CreditLimitServiceConfiguration _config = ( CreditLimitServiceConfiguration ) CreditLimitServiceConfigurationFactory.getCreditConfigurationMBean();

    public CreditLimitServiceConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( _config, "creditGridAPIResponse400UponValidationError", CreditLimitServiceConfigurationMBean.CREDIT_API_RESPONSE_400_UPON_VALIDATION_ERROR, MBeanTestCaseC.BOOLEAN );
    }
}
