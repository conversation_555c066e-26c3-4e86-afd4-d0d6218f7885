package com.integral.finance.creditLimit.test;

import com.integral.config.util.ConfigUtil;
import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.creditLimit.CreditWorkflowRidersDefault;
import com.integral.finance.creditLimit.server.CreditLimitSubscriptionInfo;
import com.integral.finance.creditLimit.server.CreditServerManagerC;
import com.integral.finance.creditLimit.server.config.CreditServerConfigMBean;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerType;
import com.integral.util.CompositeKeys;

import java.util.Collection;
import java.util.HashSet;
import java.util.Map;

public class CreditServerManagerPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Server Manager Test";

    public CreditServerManagerPTestC( String name )
    {
        super( name );
    }

    //////////////////////////////// Test cases for single active credit relationship between two legal entities.////////////////////////


    public void testCreditLimitsPublish()
    {
        try
        {
            CreditServerManagerC.getInstance().clearCreditLimitSubscriptionMap();

            // setup credit server
            WatchPropertyC.update( CreditServerConfigMBean.CREDIT_LINES_RELOAD_PERIOD, "1000", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update(CreditServerConfigMBean.CREDIT_LIMITS_PUBLISH_PERIOD, "100", ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update(CreditServerConfigMBean.LIMIT_NOTIFICATION_ENABLED_VS_TYPES, VirtualServerType.MakerGateWay + "," + VirtualServerType.OrderAdaptor, ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update(CreditServerConfigMBean.CREDIT_SERVER_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE);

            sleepFor(60000);
            Map<CompositeKeys, CreditLimitSubscriptionInfo> clsiMap = CreditServerManagerC.getInstance().getCreditLimitSubscriptionInfoMap();
            log( "clsiMap=" + clsiMap );
            assertNotNull(clsiMap);

            Collection<VirtualServer> oaServers = ConfigUtil.getVirtualServers( VirtualServerType.OrderAdaptor );
            Collection<VirtualServer> sogServers = ConfigUtil.getVirtualServers( VirtualServerType.MakerGateWay );
            if ( ( oaServers != null && !oaServers.isEmpty() ) || ( sogServers != null && !sogServers.isEmpty() ) )
            {
                assertTrue(clsiMap.size() > 0);

                Collection<CreditLimitSubscriptionInfo> clsis = CreditServerManagerC.getInstance().getActiveCreditLimitSubscriptionInfos();
                if (!clsis.isEmpty())
                {
                    CreditLimitSubscriptionInfo clsi = (CreditLimitSubscriptionInfo) clsis.toArray()[0];
                    init(clsi.getFiLe().getOrganization().getDefaultDealingUser());
                    removeExistingCreditUtilizationEvents(clsi.getFiLe().getOrganization());
                    removeExistingCreditUtilizationEvents(clsi.getLpLe().getOrganization());

                    sleepFor(1000);

                    long dailyLimit = clsi.getCreditLimitInfo().getDailyLimit();
                    long dailyAvailable = clsi.getCreditLimitInfo().getDailyAvailable();
                    long aggLimit = clsi.getCreditLimitInfo().getAggregateLimit();
                    long aggAvailable = clsi.getCreditLimitInfo().getAggregateAvailable();
                    log("clsi=" + clsi);

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, clsi.getLpLe().getOrganization(), clsi.getFiLe().getTradingParty(clsi.getLpLe().getOrganization()), bidRates[0], spotDate);
                    CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(clsi.getFiLe(), clsi.getLpLe(), trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    assertEquals("Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals(MessageStatus.SUCCESS), true);

                    sleepFor(1000);
                    log("clsi=" + clsi);
                    assertTrue(clsi.getCreditLimitInfo().getDailyAvailable() < dailyAvailable || clsi.getCreditLimitInfo().getAggregateAvailable() < aggAvailable);
                    assertTrue(clsi.getCreditLimitInfo().getDailyLimit() == dailyLimit);
                    assertTrue(clsi.getCreditLimitInfo().getAggregateLimit() == aggLimit);
                }
            }
        }
        catch ( Exception e )
        {
            fail ( "testCreditLimitsPublish", e );
        }
        finally
        {
            WatchPropertyC.update( CreditServerConfigMBean.CREDIT_SERVER_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testAdminConfigReload()
    {
        try
        {
            CreditServerManagerC.getInstance().clearCreditLimitSubscriptionMap();

            // setup credit server
            WatchPropertyC.update(CreditServerConfigMBean.CREDIT_LINES_RELOAD_PERIOD, "2000", ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update(CreditServerConfigMBean.CREDIT_LIMITS_PUBLISH_PERIOD, "100", ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update(CreditServerConfigMBean.LIMIT_NOTIFICATION_ENABLED_VS_TYPES, VirtualServerType.MakerGateWay + "," + VirtualServerType.OrderAdaptor, ConfigurationProperty.DYNAMIC_SCOPE);
            WatchPropertyC.update(CreditServerConfigMBean.CREDIT_SERVER_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE);

            sleepFor(10000);
            Map<CompositeKeys, CreditLimitSubscriptionInfo> clsiMap = CreditServerManagerC.getInstance().getCreditLimitSubscriptionInfoMap();
            log("clsiMap=" + clsiMap);
            assertNotNull(clsiMap);
            Collection<String> vsTypes = new HashSet<String>();
            vsTypes.add( VirtualServerType.OrderAdaptor );
            vsTypes.add( VirtualServerType.MakerGateWay );
            Collection<VirtualServer> virtualServers = ConfigUtil.getVirtualServers( vsTypes );
            if ( virtualServers != null && !virtualServers.isEmpty() )
            {
                assertTrue(clsiMap.size() > 0);

                CreditServerManagerC.getInstance().clearCreditLimitSubscriptionMap();
                clsiMap = CreditServerManagerC.getInstance().getCreditLimitSubscriptionInfoMap();
                assertTrue(clsiMap.size() == 0);

                sleepFor(5000);
                clsiMap = CreditServerManagerC.getInstance().getCreditLimitSubscriptionInfoMap();
                assertTrue(clsiMap.size() > 0);
            }
        }
        catch ( Exception e )
        {
            fail ( "testAdminConfigReload", e );
        }
        finally
        {
            WatchPropertyC.update(CreditServerConfigMBean.CREDIT_SERVER_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE);
        }
    }

}
