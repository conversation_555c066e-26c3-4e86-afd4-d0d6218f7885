package com.integral.finance.creditLimit.test;


import com.integral.finance.creditLimit.CreditLimitFactory;
import com.integral.finance.creditLimit.CreditLimitOrgFunction;
import com.integral.finance.creditLimit.CreditTenorParameters;
import com.integral.finance.creditLimit.CreditTenorProfile;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

/**
 * Tests the persistence of credit tenor profile.
 */
public class CreditLimitOrgFunctionPTestC
        extends PTestCaseC
{
    static String name = "Credit Limit Org Function Test";

    public CreditLimitOrgFunctionPTestC( String name )
    {
        super(name);
    }

    public void testInsertCreditTenorProfile()
    {
        try
        {
            Session session = getPersistenceSession();

            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            CreditTenorProfile profile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regProfile = ( CreditTenorProfile ) uow.registerObject( profile );
            regProfile.setStatus( 'T' );
            String name = "Test" + System.nanoTime();
            regProfile.setShortName( name );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );

            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regProfile.getCreditTenorParameters().add( regCnp1 );
            regProfile.getCreditTenorParameters().add( regCnp2 );
            Organization org = ( Organization ) namedEntityReader.execute( Organization.class, "CITI" );
            CreditLimitOrgFunction regPof = (CreditLimitOrgFunction) uow.registerObject( org.getCreditLimitOrgFunction() );
            regProfile.setOwner(regPof);

            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal( name );
            CreditTenorProfile profileFromDB = ( CreditTenorProfile ) getPersistenceSession().readObject( CreditTenorProfile.class, expr );
            assertNotNull( profileFromDB );
            log.info( "profileFromDB=" + profileFromDB );
            assertTrue( profileFromDB.getCreditTenorParameters().size() == 2 );
        }
        catch ( Exception e )
        {
            fail( "testInsertCreditTenorProfile", e );
        }
    }
}

