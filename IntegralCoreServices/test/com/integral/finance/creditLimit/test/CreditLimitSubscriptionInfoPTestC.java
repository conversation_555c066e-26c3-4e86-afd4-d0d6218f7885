package com.integral.finance.creditLimit.test;

import com.integral.finance.creditLimit.CreditLimitSubscriptionManagerC;
import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.creditLimit.CreditWorkflowRidersDefault;
import com.integral.finance.creditLimit.notification.CreditLimitInfo;
import com.integral.finance.creditLimit.notification.CreditStatus;
import com.integral.finance.creditLimit.server.CreditLimitSubscriptionInfo;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.time.IdcDateConstants;

public class CreditLimitSubscriptionInfoPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Subscription Info Test";

    public CreditLimitSubscriptionInfoPTestC( String name )
    {
        super( name );
    }

    //////////////////////////////// Test cases for single active credit relationship between two legal entities.////////////////////////


    public void testSingleLineLimits()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( ddLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( ddLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(ddLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLineLimits", e );
        }
    }

    public void testSingleLineLimitsNoCheck()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, false );

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( ddLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( ddLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == dailyLimit );
            assertTrue( cli.getAggregateAvailable() == aggLimit  );
            assertTrue( cli.getCreditStatus() == CreditStatus.NO_CHECK );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(ddLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == dailyLimit  );
            assertTrue( cli.getAggregateAvailable() == aggLimit  );
            assertTrue( cli.getCreditStatus() == CreditStatus.NO_CHECK );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLineLimitsNoCheck", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, true );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000.0 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000.0 );
        }
    }

    public void testSingleLineLimitsSuspended()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setCreditStatus( lpOrg, lpTpForDd, CREDIT_SUSPEND );

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( ddLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( ddLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs((dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0)) - cli.getDailyAvailable()) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs((aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0)) - cli.getAggregateAvailable()) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(ddLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs((dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0)) - cli.getDailyAvailable()) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs((aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0)) - cli.getAggregateAvailable()) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLineLimitsSuspended", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, true );
            creditAdminSvc.setCreditStatus( lpOrg, lpTpForDd, CREDIT_ACTIVE );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000.0 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000.0 );
        }
    }

    public void testSingleLineLimitsNoMethodology()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null );


            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( ddLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( ddLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( CreditLimitInfo.NO_LIMIT , cli.getDailyLimit() );
            assertEquals( CreditLimitInfo.NO_LIMIT , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == CreditLimitInfo.NO_LIMIT  );
            assertTrue( cli.getAggregateAvailable() == CreditLimitInfo.NO_LIMIT  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(ddLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( CreditLimitInfo.NO_LIMIT , cli.getDailyLimit() );
            assertEquals( CreditLimitInfo.NO_LIMIT , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == CreditLimitInfo.NO_LIMIT  );
            assertTrue( cli.getAggregateAvailable() == CreditLimitInfo.NO_LIMIT  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLineLimitsNoMethodology", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, true );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000.0 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000.0 );
        }
    }

    public void testSingleLineLimitsMinTenor()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForDd, new Tenor("1W"));

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( ddLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( ddLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == 0  );
            assertTrue( cli.getAggregateAvailable() == 0 );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(ddLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == 0  );
            assertTrue( cli.getAggregateAvailable() == 0  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForDd, new Tenor("TOD"));

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );


            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForDd, new Tenor( "SP" ) );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits(clsi, null);
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLineLimitsMinTenor", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForDd, null );
            setCalcAndLimit(lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000.0);
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000.0 );
        }
    }

    public void testSingleLineLimitsMaxTenor()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForDd, new Tenor("TOD"));

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( ddLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( ddLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == 0  );
            assertTrue( cli.getAggregateAvailable() == 0 );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(ddLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == 0  );
            assertTrue( cli.getAggregateAvailable() == 0  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForDd, new Tenor("1Y"));

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForDd, new Tenor("SP"));

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLineLimitsMaxTenor", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForDd, null);
            setCalcAndLimit(lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000.0);
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000.0 );
        }
    }

    public void testSingleLineLimitsZeroLimits()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 0;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( ddLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( ddLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( ddLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable() ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLineLimitsZeroLimits", e );
        }
        finally
        {
            setCalcAndLimit(lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000.0);
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000.0 );
        }
    }

    //////////////////////////////// Test cases for multiple active credit relationship between two legal entities.////////////////////////


    public void testMultiLineLimits()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents(fiOrg);
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 600000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 700000000 );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( fiLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( fiLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() - tradeAmt ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testMultiLineLimits", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
        }
    }

    public void testMultiLineLimitsNoCheck()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 600000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 700000000 );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, false );

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( fiLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( fiLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == dailyLimit  );
            assertTrue( cli.getAggregateAvailable() == aggLimit  );
            assertTrue( cli.getCreditStatus() == CreditStatus.NO_CHECK );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == dailyLimit  );
            assertTrue( cli.getAggregateAvailable() == aggLimit  );
            assertTrue( cli.getCreditStatus() == CreditStatus.NO_CHECK );
        }
        catch ( Exception e )
        {
            fail ( "testMultiLineLimitsNoCheck", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
        }
    }

    public void testMultiLineLimitsSuspended()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 600000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 700000000 );

            creditAdminSvc.setCreditStatus( lpOrg, lpTpForFi, CREDIT_SUSPEND );

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( fiLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( fiLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs((dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0)) - cli.getDailyAvailable()) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs((aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0)) - cli.getAggregateAvailable()) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs((dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0)) - cli.getDailyAvailable()) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs((aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0)) - cli.getAggregateAvailable()) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );
        }
        catch ( Exception e )
        {
            fail ( "testMultiLineLimitsSuspended", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            creditAdminSvc.setCreditStatus( lpOrg, lpTpForFi, CREDIT_ACTIVE );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
        }
    }

    public void testMultiLineLimitsNoMethodology()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 600000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 700000000 );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null );
            creditAdminSvc.setNettingMethodology( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, null );
            creditAdminSvc.setNettingMethodology( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null );


            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( fiLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( fiLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( CreditLimitInfo.NO_LIMIT , cli.getDailyLimit() );
            assertEquals( CreditLimitInfo.NO_LIMIT , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == CreditLimitInfo.NO_LIMIT  );
            assertTrue( cli.getAggregateAvailable() == CreditLimitInfo.NO_LIMIT  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( CreditLimitInfo.NO_LIMIT , cli.getDailyLimit() );
            assertEquals( CreditLimitInfo.NO_LIMIT , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == CreditLimitInfo.NO_LIMIT  );
            assertTrue( cli.getAggregateAvailable() == CreditLimitInfo.NO_LIMIT  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testMultiLineLimitsNoMethodology", e );
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
        }
    }

    public void testMultiLineLimitsMinTenor()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents(fiOrg);
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 600000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 700000000 );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("1W"));

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( fiLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( fiLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == 0 );
            assertTrue( cli.getAggregateAvailable() == 0  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == 0 );
            assertTrue( cli.getAggregateAvailable() == 0  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("TOD"));

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );


            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor( "SP" ) );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits(clsi, null);
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testMultiLineLimitsMinTenor", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, null );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
        }
    }

    public void testMultiLineLimitsMaxTenor()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents(fiOrg);
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 100000;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 600000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 700000000 );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("TOD"));

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( fiLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( fiLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == 0  );
            assertTrue( cli.getAggregateAvailable() == 0  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit , cli.getDailyLimit() );
            assertEquals( aggLimit , cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( cli.getDailyAvailable() == 0  );
            assertTrue( cli.getAggregateAvailable() == 0  );
            assertTrue( cli.getCreditStatus() == CreditStatus.SUSPEND );

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("1Y"));

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("SP"));

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

        }
        catch ( Exception e )
        {
            fail ( "testMultiLineLimitsMaxTenor", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null);
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
        }
    }

    public void testMultiLineLimitsZeroLimits()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents(fiOrg);
            removeExistingCreditUtilizationEvents( lpOrg );
            long dailyLimit = 0;
            long aggLimit = 200000;
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggLimit );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 600000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 700000000 );
            Currency limitCcy = CurrencyFactory.getCurrency( "USD" );

            CreditLimitSubscriptionInfo clsi = new CreditLimitSubscriptionInfo( fiLe, lpLe, spotDate );
            CreditLimitInfo cli = new CreditLimitInfo( fiLe.getObjectID(), lpLe.getObjectID(), (short) ( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY ) );
            clsi.setCreditLimitInfo( cli );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable()  ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditLimitSubscriptionManagerC.getInstance().recalculateCreditLimits( clsi, null );
            assertEquals( spotDate, clsi.getValueDate() );
            assertEquals( limitCcy, clsi.getLimitCcy() );
            cli = clsi.getCreditLimitInfo();
            assertEquals( dailyLimit, cli.getDailyLimit() );
            assertEquals( aggLimit, cli.getAggregateLimit() );
            assertEquals( spotDate.asJdkDate().getTime() / IdcDateConstants.MILLISECONDS_PER_DAY, cli.getValueDate() );
            assertTrue( Math.abs ( ( dailyLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getDailyAvailable() ) < CREDIT_CALCULATION_MINIMUM );
            assertTrue( Math.abs( ( aggLimit * (DEFAULT_SUSPENSION_PERCENTAGE / 100.0) ) - cli.getAggregateAvailable() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( cli.getCreditStatus() == CreditStatus.ACTIVE );
        }
        catch ( Exception e )
        {
            fail ( "testMultiLineLimitsZeroLimits", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );
        }
    }

}
