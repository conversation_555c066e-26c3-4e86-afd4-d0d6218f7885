package com.integral.finance.creditLimit.test;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.finance.creditLimit.*;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Vector;


public class CreditLimitRuleSetPTestC extends PTestCaseC
{
    static String name = "CreditLimitRuleSet Test";
    Vector users = null;

    public CreditLimitRuleSetPTestC( String name )
    {
        super( name );
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CreditLimitRuleSet testClrs = CreditLimitFactory.newCreditLimitRuleSet();
            testClrs.setShortName( "Test_" + System.nanoTime() );
            assertFalse( testClrs.isDailyPL() );
            assertFalse( testClrs.isApplyPandL() );
            assertFalse( testClrs.isUpdateBalanceWithPL() );
            assertFalse( testClrs.isTradingSuspended() );
            assertTrue( testClrs.getMode() == 0 );
            assertNull ( testClrs.getStopOutPercentage () );
            CreditLimitRuleSet registeredTestClrs = ( CreditLimitRuleSet ) uow.registerObject( testClrs );
            assertFalse( registeredTestClrs.isDailyPL() );
            assertFalse( registeredTestClrs.isApplyPandL() );
            assertFalse( registeredTestClrs.isUpdateBalanceWithPL() );
            assertNull ( registeredTestClrs.getStopOutPercentage () );
            registeredTestClrs.setDailyPL( true );
            registeredTestClrs.setApplyPandL( true );
            registeredTestClrs.setUpdateBalanceWithPL( true );
            registeredTestClrs.setTradingSuspended( true );
            registeredTestClrs.setMode( 1 );
            double stopOutPercent = 95.34;
            registeredTestClrs.setStopOutPercentage ( stopOutPercent );

            CreditTenorProfile profile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regProfile = ( CreditTenorProfile ) uow.registerObject( profile );
            regProfile.setStatus( 'T' );
            String name = "Test" + System.nanoTime();
            regProfile.setShortName( name );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );

            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regProfile.getCreditTenorParameters().add( regCnp1 );
            regProfile.getCreditTenorParameters().add( regCnp2 );
            registeredTestClrs.setCreditTenorProfile( regProfile );

            // assign an exemption currency pair group.
            CurrencyPairGroup cpg = ( CurrencyPairGroup ) namedEntityReader.execute( CurrencyPairGroupC.class, "G7", IdcUtilC.MAIN_NAMESPACE );
            assertNotNull( cpg );
            CurrencyPairGroup regCpg = ( CurrencyPairGroup ) uow.registerObject( cpg );
            registeredTestClrs.setExemptCurrencyPairGroup( regCpg );

            uow.commit();

            // refresh the trade and verify the fields
            testClrs.setExemptCurrencyPairGroup( null );
            testClrs.setCreditTenorProfile( null );
            testClrs = ( CreditLimitRuleSet ) session.refreshObject( testClrs );
            log( "testClrs=" + testClrs + ",testClrs.dailyPL=" + testClrs.isDailyPL() + ",testClrs.applyPL="
                    + testClrs.isApplyPandL() + ",updateBalanceWithPL=" + testClrs.isUpdateBalanceWithPL()
                    + ",tradingSuspended=" + testClrs.isTradingSuspended() );
            assertTrue( testClrs.isDailyPL() );
            assertTrue( testClrs.isApplyPandL() );
            assertTrue( testClrs.isUpdateBalanceWithPL() );
            assertTrue( testClrs.isTradingSuspended() );
            assertNotNull( testClrs.getCreditTenorProfile() );
            assertNotNull( testClrs.getExemptCurrencyPairGroup() );
            assertTrue( testClrs.getExemptCurrencyPairGroup().isSameAs( cpg ) );
            assertTrue( testClrs.getMode() == 1 );
            assertNotNull ( testClrs.getStopOutPercentage () );
            assertEquals ( stopOutPercent, testClrs.getStopOutPercentage () );
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence : ", e );
        }
    }

    public void testSetPFEConfiguration()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CreditLimitRuleSet testClrs = CreditLimitFactory.newCreditLimitRuleSet();
            testClrs.setShortName( "Test_" + System.nanoTime() );
            CreditLimitRuleSet registeredTestClrs = ( CreditLimitRuleSet ) uow.registerObject( testClrs );

            PFEConfiguration pfe = CreditLimitFactory.newPFEConfiguration();
            PFEConfiguration regConf = ( PFEConfiguration ) uow.registerObject( pfe );
            String pfeShortName = "PFE" + System.nanoTime();
            regConf.setShortName( pfeShortName );
            regConf.setLongName( "PFETestProfileLongName" );
            regConf.setDescription( "PFE ProfileTest Desc" );

            PFEConfigurationProfile pfeProfile = CreditLimitFactory.newPFEConfigurationProfile();
            PFEConfigurationProfile regPfeProfile = ( PFEConfigurationProfile ) uow.registerObject( pfeProfile );
            regPfeProfile.setStatus( 'T' );

            //Setting Owners
            regPfeProfile.setOwner( regConf );

            regPfeProfile.setSortOrder( 4 );

            CurrencyPairGroup g7 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName(
                    "G7", CurrencyPairGroup.class, null, null );

            CurrencyPairGroup ccyPairGrp = ( CurrencyPairGroup ) uow.registerObject( g7 );
            regPfeProfile.setCurrencyPairGroup( ccyPairGrp );

            // Credit Tenor Profile
            CreditTenorProfile creditTenorProfile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regCreditTenorProfile = ( CreditTenorProfile ) uow.registerObject( creditTenorProfile );
            regCreditTenorProfile.setStatus( 'T' );
            String ctpShortName = "CTP" + System.nanoTime();
            regCreditTenorProfile.setShortName( ctpShortName );
            regCreditTenorProfile.setLongName( "TestLongName" );
            regCreditTenorProfile.setDescription( "testDesc" );


            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regCreditTenorProfile.getCreditTenorParameters().add( regCnp1 );
            regCreditTenorProfile.getCreditTenorParameters().add( regCnp2 );
            Organization creditProviderOrg = ReferenceDataCacheC.getInstance().getOrganization( "CITI" );
            CreditLimitOrgFunction orgFunc = creditProviderOrg.getCreditLimitOrgFunction();

            if ( orgFunc != null )
            {
                CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) uow.registerObject( orgFunc );
                regCreditTenorProfile.setOwner( registeredOrgFunc );
            }

            regPfeProfile.setCreditTenorProfile( regCreditTenorProfile );
            regConf.getPfeConfigurationProfiles().add( regPfeProfile );
            registeredTestClrs.setPfeConfiguration( regConf );
            uow.commit();

            // refresh the rule set.
            testClrs.setPfeConfiguration( null );
            testClrs = (CreditLimitRuleSet) IdcUtilC.refreshObject( testClrs );
            assertNotNull( testClrs.getPfeConfiguration() );
        }
        catch ( Exception e )
        {
            fail( "Exception in testSetPFEConfiguration : ", e );
        }
    }

}
