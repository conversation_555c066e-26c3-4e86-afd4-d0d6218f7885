package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitRule;
import com.integral.finance.creditLimit.CreditLimitRuleSet;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.dealing.DealingPrice;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;

import java.util.ArrayList;
import java.util.Vector;


public class CreditLimitQueryPTestC
        extends PTestCaseC
{
    static String name = "CreditLimitQuery Test";
    Vector users = null;

    public CreditLimitQueryPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new CreditLimitQueryPTestC( "testCounterpartyCreditLimitRuleQuery" ) );
        suite.addTest( new CreditLimitQueryPTestC( "testCreditLimitRuleCcyLimitQuery" ) );
        return suite;
    }

    public void testCounterpartyCreditLimitRuleQuery()
    {
        log( "testCounterpartyCreditLimitRuleQuery" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "creditLimitRule" ).get( "ruleParent" ).get( "tradingPartyOrganization" ).get( "shortName" ).equal( "DDOrg" );

            Vector objs = getPersistenceSession().readAllObjects( CreditUtilization.class, expr );
            for ( int i = 0; i < objs.size(); i++ )
            {
                CreditUtilization cclr = ( CreditUtilization ) objs.elementAt( i );
                log( "CreditUtilization = " + cclr );
                if ( i > 100 )
                {
                    break;
                }
            }
            log( "testCounterpartyCreditLimitRuleQuery" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCounterpartyCreditLimitRuleQuery" );
        }
    }

    public void testCreditLimitRuleCcyLimitQuery()
    {
        log( "testCreditLimitRuleCcyLimitQuery" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            ReportQuery reportQuery = new ReportQuery( eb );

            reportQuery.setReferenceClass( com.integral.finance.creditLimit.CreditUtilizationC.class );
            reportQuery.useCollectionClass( ArrayList.class );
            reportQuery.addAttribute( "orgShortName", eb.get( "creditLimitRule" ).get( "ruleParent" ).get( "tradingPartyOrganization" ).get( "shortName" ) );
            reportQuery.addAttribute( "limit", eb.get( "creditLimitRule" ).get( "limitAmount" ) );
            reportQuery.addAttribute( "limitCcy", eb.get( "creditLimitRule" ).get( "currency" ).get( "shortName" ) );

            ArrayList<ReportQueryResult> objs = ( ArrayList<ReportQueryResult> ) getPersistenceSession().executeQuery( reportQuery );
            int i = 0;
            for ( ReportQueryResult object : objs )
            {
                i++;
                if ( i > 100 )
                {
                    break;
                }
                log( "CreditUtilization = " + object.get( "orgShortName" ) + "Limit Amt : " + object.get( "limit" ) + " Limit Ccy : " + object.get( "limitCcy" ) );
            }
            log( "testCreditLimitRuleCcyLimitQuery" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            log.error( "Got Exception : ", e );
            fail( "testCreditLimitRuleCcyLimitQuery" );
        }
    }

    public void testLimitedQuery()
    {
        log( "testLimitedQuery" );
        try
        {
            TradingParty tp = getTradingParty();
            CreditLimitRuleSet clrs = getCreditLimitRuleSet();

            ExpressionBuilder eb = new ExpressionBuilder();

            Expression activeExpr = eb.get( "status" ).equal( 'A' );
            Expression clsrExpr = eb.get( "ruleParent" ).get( "ruleSet" ).equal( clrs );
            Expression tpExpr = eb.get( "ruleParent" ).get( "counterparty" ).equal( tp.getObjectID() );

            Expression expr = activeExpr.and( clsrExpr ).and( tpExpr );


            ReadAllQuery raq = new ReadAllQuery( eb );
            raq.setReferenceClass( CreditLimitRule.class );
            // raq.useCursoredStream(INITIALREADSIZE, PAGESIZE);
            raq.setSelectionCriteria( expr );

            Vector objs = ( Vector ) getPersistenceSession().executeQuery( raq );
            for ( int i = 0; i < objs.size(); i++ )
            {
                CreditLimitRule clr = ( CreditLimitRule ) objs.elementAt( i );
                log( "CreditLimitRule = " + clr );
                if ( i > 100 )
                {
                    break;
                }
            }
            log( "testLimitedQuery" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testLimitedQuery" );
        }
    }

    TradingParty getTradingParty()
    {
        return ( TradingParty ) getPersistenceSession().readObject( TradingParty.class );
    }

    CreditLimitRuleSet getCreditLimitRuleSet()
    {
        return ( CreditLimitRuleSet ) getPersistenceSession().readObject( CreditLimitRuleSet.class );
    }

    public void testAllQuery()
    {
        log( "lookupTest" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( DealingPrice.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                CreditLimitRule clr = ( CreditLimitRule ) objs.elementAt( i );
                log( "CreditLimitRule = " + clr );
                if ( i > 100 )
                {
                    break;
                }
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTest" );
        }
    }

}
