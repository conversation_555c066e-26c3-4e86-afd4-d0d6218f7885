package com.integral.finance.creditLimit.test;

import com.integral.finance.creditLimit.CreditLimitFactory;
import com.integral.finance.creditLimit.CreditLimitOrgFunction;
import com.integral.finance.creditLimit.CreditTenorParameters;
import com.integral.finance.creditLimit.CreditTenorProfile;
import com.integral.finance.creditLimit.PFEConfiguration;
import com.integral.finance.creditLimit.PFEConfigurationProfile;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;
import java.util.Vector;

/**
 * Created by saxenap on 5/28/2014.
 */
public class PFEConfigurationPTestC extends PTestCaseC
{

    static String name = "PFE Configuration Profile Test";

    public PFEConfigurationPTestC( String name )
    {
        super( name );
    }

    public void testInsertPfeConfigurationProfile()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            PFEConfiguration pfe = CreditLimitFactory.newPFEConfiguration();
            PFEConfiguration regConf = ( PFEConfiguration ) uow.registerObject( pfe );
            String pfeShortName = "PFE" + System.nanoTime();
            regConf.setShortName( pfeShortName );
            regConf.setLongName( "PFETestProfileLongName" );
            regConf.setDescription( "PFE ProfileTest Desc" );

            PFEConfigurationProfile pfeProfile = CreditLimitFactory.newPFEConfigurationProfile();
            PFEConfigurationProfile regPfeProfile = ( PFEConfigurationProfile ) uow.registerObject( pfeProfile );
            regPfeProfile.setStatus( 'T' );

            //Setting Owners
            regPfeProfile.setOwner( regConf );

            regPfeProfile.setSortOrder( 4 );

            CurrencyPairGroup g7 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName(
                    "G7", CurrencyPairGroup.class, null, null );

            CurrencyPairGroup ccyPairGrp = ( CurrencyPairGroup ) uow.registerObject( g7 );
            regPfeProfile.setCurrencyPairGroup( ccyPairGrp );

            // Credit Tenor Profile
            CreditTenorProfile creditTenorProfile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regCreditTenorProfile = ( CreditTenorProfile ) uow.registerObject( creditTenorProfile );
            regCreditTenorProfile.setStatus( 'T' );
            String ctpShortName = "CTP" + System.nanoTime();
            regCreditTenorProfile.setShortName( ctpShortName );
            regCreditTenorProfile.setLongName( "TestLongName" );
            regCreditTenorProfile.setDescription( "testDesc" );


            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regCreditTenorProfile.getCreditTenorParameters().add( regCnp1 );
            regCreditTenorProfile.getCreditTenorParameters().add( regCnp2 );
            Organization creditProviderOrg = ReferenceDataCacheC.getInstance().getOrganization( "CITI" );
            CreditLimitOrgFunction orgFunc = creditProviderOrg.getCreditLimitOrgFunction();

            if ( orgFunc != null )
            {
                CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) uow.registerObject( orgFunc );
                regCreditTenorProfile.setOwner( registeredOrgFunc );
            }

            regPfeProfile.setCreditTenorProfile( regCreditTenorProfile );

            regConf.getPfeConfigurationProfiles().add( regPfeProfile );
            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal( pfeShortName );
            PFEConfiguration pfeConfFromDB = ( PFEConfiguration ) getPersistenceSession().readObject( PFEConfiguration.class, expr );
            assertNotNull( pfeConfFromDB );
            log.info( "pfeConfFromDB=" + pfeConfFromDB );
            assertNotNull( pfeConfFromDB.getPfeConfigurationProfiles() );
            assertTrue( pfeConfFromDB.getPfeConfigurationProfiles().size() == 1 );

            for ( PFEConfigurationProfile pfeConfigurationProfile : pfeConfFromDB.getPfeConfigurationProfiles() )
            {
                assertEquals( ctpShortName, pfeConfigurationProfile.getCreditTenorProfile().getShortName() );
                assertEquals( g7.getShortName(), pfeConfigurationProfile.getCurrencyPairGroup().getShortName() );
            }

        }
        catch ( Exception e )
        {
            fail( "testInsertCreditTenorProfile", e );
        }
    }


    public void testUpdatePfeConfigurationProfile()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            PFEConfiguration pfe = CreditLimitFactory.newPFEConfiguration();
            PFEConfiguration regConf = ( PFEConfiguration ) uow.registerObject( pfe );
            String pfeShortName = "PFE" + System.nanoTime();
            regConf.setShortName( pfeShortName );
            regConf.setLongName( "PFETestProfileLongName" );
            regConf.setDescription( "PFE ProfileTest Desc" );

            PFEConfigurationProfile pfeProfile = CreditLimitFactory.newPFEConfigurationProfile();
            PFEConfigurationProfile regPfeProfile = ( PFEConfigurationProfile ) uow.registerObject( pfeProfile );
            regPfeProfile.setStatus( 'T' );

            //Setting Owners
            regPfeProfile.setOwner( regConf );

            regPfeProfile.setSortOrder( 4 );

            CurrencyPairGroup g7 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName(
                    "G7", CurrencyPairGroup.class, null, null );

            CurrencyPairGroup ccyPairGrp = ( CurrencyPairGroup ) uow.registerObject( g7 );
            regPfeProfile.setCurrencyPairGroup( ccyPairGrp );

            // Credit Tenor Profile
            CreditTenorProfile creditTenorProfile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regCreditTenorProfile = ( CreditTenorProfile ) uow.registerObject( creditTenorProfile );
            regCreditTenorProfile.setStatus( 'T' );
            String ctpShortName = "CTP" + System.nanoTime();
            regCreditTenorProfile.setShortName( ctpShortName );
            regCreditTenorProfile.setLongName( "TestLongName" );
            regCreditTenorProfile.setDescription( "testDesc" );


            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regCreditTenorProfile.getCreditTenorParameters().add( regCnp1 );
            regCreditTenorProfile.getCreditTenorParameters().add( regCnp2 );
            Organization creditProviderOrg = ReferenceDataCacheC.getInstance().getOrganization( "CITI" );
            CreditLimitOrgFunction orgFunc = creditProviderOrg.getCreditLimitOrgFunction();

            if ( orgFunc != null )
            {
                CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) uow.registerObject( orgFunc );
                regCreditTenorProfile.setOwner( registeredOrgFunc );
            }

            regPfeProfile.setCreditTenorProfile( regCreditTenorProfile );

            regConf.getPfeConfigurationProfiles().add( regPfeProfile );
            uow.commit();

            long pfeObjectId = pfe.getObjectId();

            // now update the configuration.
            uow = getPersistenceSession().acquireUnitOfWork();
            PFEConfiguration registeredPFEConfig = ( PFEConfiguration ) uow.registerObject( pfe );
            PFEConfigurationProfile pfeProfile1 = CreditLimitFactory.newPFEConfigurationProfile();
            PFEConfigurationProfile regPfeProfile1 = ( PFEConfigurationProfile ) uow.registerObject( pfeProfile1 );
            regPfeProfile1.setStatus( 'T' );

            regPfeProfile1.setOwner( registeredPFEConfig );

            regPfeProfile1.setSortOrder( 5 );

            CurrencyPairGroup usdAll = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName(
                    "USD/All", CurrencyPairGroup.class, null, null );

            CurrencyPairGroup registeredUsdAll = ( CurrencyPairGroup ) uow.registerObject( usdAll );
            regPfeProfile1.setCurrencyPairGroup( registeredUsdAll );

            // Credit Tenor Profile
            CreditTenorProfile creditTenorProfile1 = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regCreditTenorProfile1 = ( CreditTenorProfile ) uow.registerObject( creditTenorProfile1 );
            regCreditTenorProfile1.setStatus( 'T' );
            String ctpShortName1 = "CTP" + System.nanoTime();
            regCreditTenorProfile1.setShortName( ctpShortName1 );
            regCreditTenorProfile1.setLongName( "TestLongName" );
            regCreditTenorProfile1.setDescription( "testDesc" );


            CreditTenorParameters cnp11 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp11 = ( CreditTenorParameters ) uow.registerObject( cnp11 );
            regCnp11.setUtilizationPercent( 10.0 );
            regCnp11.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp21 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp21 = ( CreditTenorParameters ) uow.registerObject( cnp21 );
            regCnp21.setUtilizationPercent( 20.0 );
            regCnp21.setTenor( new Tenor( "1W" ) );

            regCreditTenorProfile1.getCreditTenorParameters().add( regCnp11 );
            regCreditTenorProfile1.getCreditTenorParameters().add( regCnp21 );

            if ( orgFunc != null )
            {
                CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) uow.registerObject( orgFunc );
                regCreditTenorProfile1.setOwner( registeredOrgFunc );
            }

            regPfeProfile1.setCreditTenorProfile( regCreditTenorProfile1 );

            registeredPFEConfig.getPfeConfigurationProfiles().add( regPfeProfile1 );
            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal( pfeShortName );
            PFEConfiguration pfeConfFromDB = ( PFEConfiguration ) getPersistenceSession().readObject( PFEConfiguration.class, expr );
            assertNotNull( pfeConfFromDB );

            // there should not be any config profiles with ownerid
            ExpressionBuilder eb2 = new ExpressionBuilder();
            Expression expr2 = eb2.get( "owner" ).get( Entity.ObjectID ).equal( pfeObjectId );
            Vector pfeProfiles = ( Vector ) getPersistenceSession().readAllObjects( PFEConfigurationProfile.class, expr2 );
            assertNotNull( pfeProfiles );
            assertTrue( pfeProfiles.size() == 2 );
        }
        catch ( Exception e )
        {
            fail( "testUpdatePfeConfigurationProfile", e );
        }
    }


    public void testDeletePfeConfigurationProfile()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            PFEConfiguration pfe = CreditLimitFactory.newPFEConfiguration();
            PFEConfiguration regConf = ( PFEConfiguration ) uow.registerObject( pfe );
            String pfeShortName = "PFE" + System.nanoTime();
            regConf.setShortName( pfeShortName );
            regConf.setLongName( "PFETestProfileLongName" );
            regConf.setDescription( "PFE ProfileTest Desc" );

            PFEConfigurationProfile pfeProfile = CreditLimitFactory.newPFEConfigurationProfile();
            PFEConfigurationProfile regPfeProfile = ( PFEConfigurationProfile ) uow.registerObject( pfeProfile );
            regPfeProfile.setStatus( 'T' );

            //Setting Owners
            regPfeProfile.setOwner( regConf );

            regPfeProfile.setSortOrder( 4 );

            CurrencyPairGroup g7 = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByShortName(
                    "G7", CurrencyPairGroup.class, null, null );

            CurrencyPairGroup ccyPairGrp = ( CurrencyPairGroup ) uow.registerObject( g7 );
            regPfeProfile.setCurrencyPairGroup( ccyPairGrp );

            // Credit Tenor Profile
            CreditTenorProfile creditTenorProfile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regCreditTenorProfile = ( CreditTenorProfile ) uow.registerObject( creditTenorProfile );
            regCreditTenorProfile.setStatus( 'T' );
            String ctpShortName = "CTP" + System.nanoTime();
            regCreditTenorProfile.setShortName( ctpShortName );
            regCreditTenorProfile.setLongName( "TestLongName" );
            regCreditTenorProfile.setDescription( "testDesc" );


            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regCreditTenorProfile.getCreditTenorParameters().add( regCnp1 );
            regCreditTenorProfile.getCreditTenorParameters().add( regCnp2 );
            Organization creditProviderOrg = ReferenceDataCacheC.getInstance().getOrganization( "CITI" );
            CreditLimitOrgFunction orgFunc = creditProviderOrg.getCreditLimitOrgFunction();

            if ( orgFunc != null )
            {
                CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) uow.registerObject( orgFunc );
                regCreditTenorProfile.setOwner( registeredOrgFunc );
            }

            regPfeProfile.setCreditTenorProfile( regCreditTenorProfile );

            regConf.getPfeConfigurationProfiles().add( regPfeProfile );
            uow.commit();

            long pfeObjectId = pfe.getObjectId();

            // now delete the configuration.
            uow = getPersistenceSession().acquireUnitOfWork();
            PFEConfiguration registeredPFEConfig = ( PFEConfiguration ) uow.registerObject( pfe );
            uow.deleteObject( registeredPFEConfig );
            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal( pfeShortName );
            PFEConfiguration pfeConfFromDB = ( PFEConfiguration ) getPersistenceSession().readObject( PFEConfiguration.class, expr );
            assertNull( pfeConfFromDB );

            ExpressionBuilder eb1 = new ExpressionBuilder();
            Expression expr1 = eb1.get( NamedEntity.ShortName ).equal( ctpShortName );
            CreditTenorProfile ctp = ( CreditTenorProfile ) getPersistenceSession().readObject( CreditTenorProfile.class, expr1 );
            assertNotNull( ctp );

            // there should not be any config profiles with ownerid
            ExpressionBuilder eb2 = new ExpressionBuilder();
            Expression expr2 = eb2.get( "owner" ).get( Entity.ObjectID ).equal( pfeObjectId );
            Vector pfeProfiles = ( Vector ) getPersistenceSession().readAllObjects( PFEConfigurationProfile.class, expr2 );
            assertNotNull( pfeProfiles );
            assertTrue( pfeProfiles.size() == 0 );
        }
        catch ( Exception e )
        {
            fail( "testDeletePfeConfigurationProfile", e );
        }
    }

    public void testPFEConfigurationLookup()
    {
        try
        {
            Vector configs = getPersistenceSession().readAllObjects( PFEConfiguration.class );
            for ( int i = 0; i < configs.size(); i++ )
            {
                PFEConfiguration pfeConfig = ( PFEConfiguration ) configs.elementAt( i );
                log.info( ",configs=" + pfeConfig );
            }
        }
        catch ( Exception e )
        {
            fail( "testPFEConfigurationLookup", e );
        }
    }

}
