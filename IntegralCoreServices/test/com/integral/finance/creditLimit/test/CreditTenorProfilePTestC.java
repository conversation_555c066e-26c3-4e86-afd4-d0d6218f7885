package com.integral.finance.creditLimit.test;

// Copyright (c) 2013 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.creditLimit.CreditLimitFactory;
import com.integral.finance.creditLimit.CreditLimitOrgFunction;
import com.integral.finance.creditLimit.CreditTenorParameters;
import com.integral.finance.creditLimit.CreditTenorProfile;
import com.integral.finance.creditLimit.CreditTenorProfileC;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


/**
 * Tests the persistence of credit tenor profile.
 */
public class CreditTenorProfilePTestC
        extends PTestCaseC
{
    static String name = "Credit Tenor Profile Test";

    public CreditTenorProfilePTestC( String name )
    {
        super( name );
    }

    public void testInsertCreditTenorProfile()
    {
        try
        {
            Session session = getPersistenceSession();

            UnitOfWork uow = session.acquireUnitOfWork();
            CreditTenorProfile profile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regProfile = ( CreditTenorProfile ) uow.registerObject( profile );
            regProfile.setStatus( 'T' );
            String name = "Test" + System.nanoTime();
            regProfile.setShortName( name );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );


            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regProfile.getCreditTenorParameters().add( regCnp1 );
            regProfile.getCreditTenorParameters().add( regCnp2 );
            Organization creditProviderOrg = ReferenceDataCacheC.getInstance().getOrganization( "DBNA" );
            CreditLimitOrgFunction orgFunc = creditProviderOrg.getCreditLimitOrgFunction();
            if ( orgFunc != null )
            {
                CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) uow.registerObject( orgFunc );
                regProfile.setOwner( registeredOrgFunc );
            }

            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal( name );
            CreditTenorProfile profileFromDB = ( CreditTenorProfile ) getPersistenceSession().readObject( CreditTenorProfile.class, expr );
            assertNotNull( profileFromDB );
            log.info( "profileFromDB=" + profileFromDB );
            assertTrue( profileFromDB.getCreditTenorParameters().size() == 2 );
        }
        catch ( Exception e )
        {
            fail( "testInsertCreditTenorProfile", e );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( UserC.class );
            uow.addReadOnlyClass( OrganizationC.class );
            CreditTenorProfile profile = CreditLimitFactory.newCreditTenorProfile();
            assertFalse( profile.isInterpolate() );
            assertEquals ( 0.0, profile.getGrossPositionSpreadMargin () );
            CreditTenorProfile regProfile = ( CreditTenorProfile ) uow.registerObject( profile );
            assertEquals ( 0.0, regProfile.getGrossPositionSpreadMargin () );
            regProfile.setStatus( 'T' );
            regProfile.setShortName( "Test" + System.nanoTime() );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );
            regProfile.setInterpolate( true );
            regProfile.setGrossPositionSpreadMargin ( 3.87 );

            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regProfile.getCreditTenorParameters().add( regCnp1 );
            regProfile.getCreditTenorParameters().add( regCnp2 );

            uow.commit();

            profile = ( CreditTenorProfile ) session.refreshObject( profile );
            assertTrue( profile.isInterpolate() );
            assertTrue( profile.getCreditTenorParameters().size() == 2 );
            assertEquals ( 3.87, profile.getGrossPositionSpreadMargin () );
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }


    public void testCreditTenorProfileLookup()
    {
        try
        {
            Session session = getPersistenceSession();
            Vector profiles = session.readAllObjects( CreditTenorProfileC.class );
            for ( int i = 0; i < profiles.size(); i++ )
            {
                CreditTenorProfile profile = ( CreditTenorProfile ) profiles.elementAt( i );
                log.info( ",profile=" + profile );
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditTenorProfileLookup", e );
        }
    }

    public void testDeleteCreditTenorProfile()
    {
        try
        {
            Session session = getPersistenceSession();

            UnitOfWork uow = session.acquireUnitOfWork();
            CreditTenorProfile profile = CreditLimitFactory.newCreditTenorProfile();
            CreditTenorProfile regProfile = ( CreditTenorProfile ) uow.registerObject( profile );
            regProfile.setStatus( 'T' );
            String name = "Test" + System.nanoTime();
            regProfile.setShortName( name );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );

            CreditTenorParameters cnp1 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp1 = ( CreditTenorParameters ) uow.registerObject( cnp1 );
            regCnp1.setUtilizationPercent( 10.0 );
            regCnp1.setTenor( Tenor.SPOT_TENOR );

            CreditTenorParameters cnp2 = CreditLimitFactory.newCreditTenorParameters();
            CreditTenorParameters regCnp2 = ( CreditTenorParameters ) uow.registerObject( cnp2 );
            regCnp2.setUtilizationPercent( 20.0 );
            regCnp2.setTenor( new Tenor( "1W" ) );

            regProfile.getCreditTenorParameters().add( regCnp1 );
            regProfile.getCreditTenorParameters().add( regCnp2 );

            uow.commit();

            uow = session.acquireUnitOfWork();
            profile = ( CreditTenorProfile ) getPersistenceSession().refreshObject( profile );
            CreditTenorProfile regProfile1 = ( CreditTenorProfile ) uow.registerObject( profile );
            regProfile1.getCreditTenorParameters().clear();
            uow.commit();

            profile = ( CreditTenorProfile ) getPersistenceSession().refreshObject( profile );
            assertTrue( profile.getCreditTenorParameters().isEmpty() );

            uow = session.acquireUnitOfWork();
            profile = ( CreditTenorProfile ) getPersistenceSession().refreshObject( profile );
            CreditTenorProfile regProfile2 = ( CreditTenorProfile ) uow.registerObject( profile );
            uow.deleteObject( regProfile2 );
            uow.commit();


            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal( name );
            CreditTenorProfile profileFromDB = ( CreditTenorProfile ) getPersistenceSession().readObject( CreditTenorProfile.class, expr );
            assertNull( profileFromDB );
        }
        catch ( Exception e )
        {
            fail( "testDeleteCreditTenorProfile", e );
        }
    }

}
