package com.integral.finance.creditLimit.test;

// Copyright (c) 2023 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.trade.Trade;
import com.integral.session.IdcTransaction;
import com.integral.workflow.dealing.DealingLimit;

/**
 * Tests the currency pair position based multi-factor netting methodology.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditMultiFactorNettingPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit MultiFactor Netting Test";

    public CreditMultiFactorNettingPTestC( String name )
    {
        super( name );
    }

    public void setUp() throws Exception
    {
        super.setUp ();

        creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
        creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, null );
        creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
        creditAdminSvc.setUsePFEConfiguration ( lpOrg, false );
        creditAdminSvc.setUsePFEConfiguration ( lpOrg, lpTpForDd, false );
    }

    public void tearDown()
    {
        super.tearDown ();
        setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
        setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );

        creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
        creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, null );
        creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
        creditAdminSvc.setUsePFEConfiguration ( lpOrg, false );
        creditAdminSvc.setUsePFEConfiguration ( lpOrg, lpTpForDd, false );
    }

    // base currency as limit currency and dealt currency buy single trade.
    public void testAggregateMultiFactorSettlement1()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + tradeAmt, Math.abs ( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement1", e );
        }
    }

    // base currency = limit currency and base currency = dealt currency - sell single trade.
    public void testAggregateMultiFactorSettlement2()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + tradeAmt, Math.abs ( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement2", e );
        }
    }

    // base currency = limit currency and term currency = dealt currency - sell single trade.
    public void testAggregateMultiFactorSettlement3()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "JPY" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt, true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement3", e );
        }
    }

    // base currency = limit currency and term currency = dealt currency - buy single trade.
    public void testAggregateMultiFactorSettlement4()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 10000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "JPY" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt, true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement4", e );
        }
    }

    // term currency = limit currency and base currency = dealt currency - buy single trade.
    public void testAggregateMultiFactorSettlement5()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, false );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement5", e );
        }
    }

    // term currency = limit currency and base currency = dealt currency - sell single trade.
    public void testAggregateMultiFactorSettlement6()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, false );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement6", e );
        }
    }

    // term currency = limit currency and term currency = dealt currency - buy single trade.
    public void testAggregateMultiFactorSettlement7()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt/bidRates[0], false );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement7", e );
        }
    }

    // term currency = limit currency and term currency = dealt currency - sell single trade.
    public void testAggregateMultiFactorSettlement8()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt/bidRates[0], false );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement8", e );
        }
    }

    // base currency as limit currency and dealt currency - spot trades nets out.
    public void testAggregateMultiFactorSettlement9()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + tradeAmt, Math.abs ( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade. In this case buy currency is EUR. but, if sell currency is credit limit currency, then that amount will be used.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertTrue ( "before limit=" + limit1 + ",after limit=" + limit2 + ",utilizedAmt=0.0", Math.abs ( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement9", e );
        }
    }

    // base currency as limit currency and term currency = dealt currency - spot trades nets out.
    public void testAggregateMultiFactorSettlement10()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt, true );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade. In this case buy currency is EUR. but, if sell currency is credit limit currency, then that amount will be used.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertTrue ( "before limit=" + limit1 + ",after limit=" + limit2 + ",utilizedAmt=0.0", Math.abs ( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement10", e );
        }
    }

    // base currency as limit currency and dealt currency - two trades net out across value dates.
    public void testAggregateMultiFactorSettlement11()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + tradeAmt, Math.abs ( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade. In this case buy currency is EUR. but, if sell currency is credit limit currency, then that amount will be used.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate.addMonths(1) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertTrue ( "before limit=" + limit1 + ",after limit=" + limit2 + ",utilizedAmt=0.0", Math.abs ( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement11", e );
        }
    }

    // base currency as limit currency and term currency = dealt currency - two trades net out across value dates.
    public void testAggregateMultiFactorSettlement12()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt, true );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade. In this case buy currency is EUR. but, if sell currency is credit limit currency, then that amount will be used.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate.addMonths( 1 ));
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertTrue ( "before limit=" + limit1 + ",after limit=" + limit2 + ",utilizedAmt=0.0", Math.abs ( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement12", e );
        }
    }

    // base currency as limit currency and term currency = dealt currency - trade cancels after done.
    public void testAggregateMultiFactorSettlement13()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            CreditWorkflowMessage cwm1 = creditMgr.undoCredit( trade, lpLe, ddOrg, lpTpForDd  );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after undo Credit : " + limit2 + " success : " + cwm1.getStatus() );
            assertTrue ( "before limit=" + limit1 + ",limitAfter=" + limit2, Math.abs ( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement13", e );
        }
    }

    // multiple currency pairs trades.
    public void testAggregateMultiFactorSettlement14()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // Now take an offer side trade. In this case buy currency is EUR. but, if sell currency is credit limit currency, then that amount will be used.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertTrue ( "before limit=" + limit1 + ",after limit=" + limit2, Math.abs ( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement14", e );
        }
    }

    // base currency as limit currency and dealt currency buy single trade. Use tenor coefficient from a tenor profile.
    public void testAggregateMultiFactorSettlement15()
    {
        try
        {
            init ( lpUser );
            removeExistingCreditUtilizationEvents ( lpOrg );
            setCalcAndLimit ( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );

            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            if ( !isInTransaction () )
            {
                String tenorProfileName = "Test" + System.nanoTime ();
                CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
                creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
                creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
                creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
            }

            double limit0 = getAvailableCreditLimit ( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log ( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade ( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit ( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit ( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log ( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus () );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + tradeAmt / 10, Math.abs ( limit0 - limit1 - tradeAmt / 10 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents ( cwm );
        }
        catch ( Exception e )
        {
            fail ( "testAggregateMultiFactorSettlement15", e );
        }
    }

    // base currency as limit currency and dealt currency buy single trade. Use tenor coefficient from a tenor profile and gross margin.
    public void testAggregateMultiFactorSettlement16()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );

            String[] tenors = new String[] {"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[] {0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            double grossMargin = 15.0;
            if ( !isInTransaction () )
            {
                String tenorProfileName = "Test" + System.nanoTime ();
                CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
                creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
                creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
                creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
                creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );
            }

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            double grossMarginAmt = 0.0;
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double utilizedAmt = ( tradeAmt * ( 10.0 / 100.0 ) ) + ( tradeAmt * ( grossMarginAmt/100.0 ) );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement16", e );
        }
    }

    // base currency as limit currency and dealt currency buy single trade. Use tenor coefficient from a tenor profile and gross margin with fully net out.
    public void testAggregateMultiFactorSettlement17()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );

            String[] tenors = new String[] {"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[] {0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            double grossMargin = 15.0;
            if ( !isInTransaction () )
            {
                String tenorProfileName = "Test" + System.nanoTime ();
                CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
                creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
                creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
                creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
                creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );
            }

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            double grossMarginAmt = 0.0;
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double utilizedAmt = ( tradeAmt * ( 10.0 / 100.0 ) ) + ( tradeAmt * ( grossMarginAmt/100.0 ) );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double netAmt = 0.0;
            grossMarginAmt = 0.0;
            double utilizedAmt1 = ( netAmt * ( 10.0 / 100.0 ) ) + ( grossMarginAmt * ( grossMargin/100.0 ) );
            log( "Credit limit after second trade take Credit : " + limit2 + " success : " + cwm1.getStatus() + ",utilizedAmt1=" + utilizedAmt1 );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit2 + ",utilizedAmt1=" + utilizedAmt1, Math.abs ( limit0 - limit2 - utilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement17", e );
        }
    }

    // base/var currency as non-limit currency. Use tenor coefficient from a tenor profile and gross margin with fully net out.
    public void testAggregateMultiFactorSettlement18()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );

            String[] tenors = new String[] {"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[] {0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            double grossMargin = 15.0;
            if ( !isInTransaction () )
            {
                String tenorProfileName = "Test" + System.nanoTime ();
                CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
                creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
                creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
                creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
                creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );
            }

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, "EUR/JPY", lpOrg, lpTpForDd, offerRates[4], spotDate );

            double grossMarginAmt = 0.0;
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double baseCcyUtilizedAmt = ( tradeAmt/offerRates[4] * ( 10.0 / 100.0 ) ) + ( tradeAmt/offerRates[4] * ( grossMarginAmt/100.0 ) );
            double limitCcyUtilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", baseCcyUtilizedAmt, true );
            log( "Credit limit after first trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",baseCcyUtilizedAmt=" + baseCcyUtilizedAmt + ",limitCcyUtilizedAmt=" + limitCcyUtilizedAmt );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",baseCcyUtilizedAmt=" + baseCcyUtilizedAmt + ",limitCcyUtilizedAmt=" + limitCcyUtilizedAmt, Math.abs ( limit0 - limit1 - limitCcyUtilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, "EUR/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double netAmt = Math.abs( tradeAmt/bidRates[4]- tradeAmt/offerRates[4]);
            grossMarginAmt = 0.0;
            double baseCcyUtilizedAmt1 = ( netAmt * ( 10.0 / 100.0 ) ) + ( grossMarginAmt * ( grossMargin/100.0 ) );
            double limitCcyUtilizedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", baseCcyUtilizedAmt1, true );
            log( "Credit limit after second trade take Credit : " + limit2 + " success : " + cwm1.getStatus() + ",baseCcyUtilizedAmt1=" + baseCcyUtilizedAmt1 + ",limitCcyUtilizedAmt1=" + limitCcyUtilizedAmt1 );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit2 + ",baseCcyUtilizedAmt1=" + baseCcyUtilizedAmt1 + ",limitCcyUtilizedAmt1=" + limitCcyUtilizedAmt1, Math.abs ( limit0 - limit2 - limitCcyUtilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement18", e );
        }
    }

    // base/var currency as non-limit currency. Use tenor coefficient from a tenor profile and gross margin with not fully net out.
    public void testAggregateMultiFactorSettlement19()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );

            double grossMargin = 15.0;
            if ( !isInTransaction () )
            {
                String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
                double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
                String tenorProfileName = "Test" + System.nanoTime ();
                CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
                creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
                creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
                creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
                creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );
            }

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, "EUR/JPY", lpOrg, lpTpForDd, offerRates[4], spotDate );

            double grossMarginAmt = 0.0;
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double baseCcyUtilizedAmt = ( tradeAmt/offerRates[4] * ( 10.0 / 100.0 ) ) + ( tradeAmt/offerRates[4] * ( grossMarginAmt/100.0 ) );
            double limitCcyUtilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", baseCcyUtilizedAmt, true );
            log( "Credit limit after first trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",baseCcyUtilizedAmt=" + baseCcyUtilizedAmt + ",limitCcyUtilizedAmt=" + limitCcyUtilizedAmt );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",baseCcyUtilizedAmt=" + baseCcyUtilizedAmt + ",limitCcyUtilizedAmt=" + limitCcyUtilizedAmt, Math.abs ( limit0 - limit1 - limitCcyUtilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            double tradeAmt1 = 400000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, true, false, "EUR/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double netAmt = Math.abs( tradeAmt1/bidRates[4]- tradeAmt/offerRates[4]);
            grossMarginAmt = 0.0;
            double baseCcyUtilizedAmt1 = ( netAmt * ( 10.0 / 100.0 ) ) + ( grossMarginAmt * ( grossMargin/100.0 ) );
            double limitCcyUtilizedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", baseCcyUtilizedAmt1, true );
            log( "Credit limit after second trade take Credit : " + limit2 + " success : " + cwm1.getStatus() + ",baseCcyUtilizedAmt1=" + baseCcyUtilizedAmt1 + ",limitCcyUtilizedAmt1=" + limitCcyUtilizedAmt1 );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit2 + ",baseCcyUtilizedAmt1=" + baseCcyUtilizedAmt1 + ",limitCcyUtilizedAmt1=" + limitCcyUtilizedAmt1, Math.abs ( limit0 - limit2 - limitCcyUtilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement19", e );
        }
    }

    // base currency as limit currency and dealt currency buy single trade. Use tenor coefficient from a tenor profile and gross margin with fully net out across different value dates.
    public void testAggregateMultiFactorSettlement20()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );

            String[] tenors = new String[] {"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[] {0.0, 10.0, 4.0, 40.0, 40.0, 100.0};
            double grossMargin = 15.0;
            String tenorProfileName = "Test" + System.nanoTime();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, false );
            creditAdminSvc.setGrossPositionSpreadMargin(lpOrg, tenorProfile, grossMargin);

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            double grossMarginAmt = 0.0;
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double utilizedAmt = ( tradeAmt * ( 10.0 / 100.0 ) ) + ( tradeAmt * ( grossMarginAmt/100.0 ) );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            tradeAmt = tradeAmt / 4.0;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate.addMonths( 6 ) );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double netAmt = 0.0;
            grossMarginAmt = tradeAmt;
            double utilizedAmt1 = ( netAmt * ( 10.0 / 100.0 ) ) + ( grossMarginAmt * ( grossMargin/100.0 ) );
            log( "Credit limit after second trade take Credit : " + limit2 + " success : " + cwm1.getStatus() + ",utilizedAmt1=" + utilizedAmt1 );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit2 + ",utilizedAmt1=" + utilizedAmt1, Math.abs ( limit0 - limit2 - utilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement20", e );
        }
    }

    public void testAggregateMultiFactorSettlement1ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement1();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement1ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement2ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement2();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement2ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement3ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement3();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement3ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement4ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement4();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement4ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement5ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement5();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement5ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement6ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement6();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement6ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement7ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement7();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement7ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement8ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement8();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement8ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement9ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement9();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement9ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement10ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement10();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement10ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement11ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement11();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement11ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement12ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement12();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement12ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement13ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement13();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement13ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement14ExternalTx()
    {
        try
        {
            init( lpUser );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement14();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement14ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement15ExternalTx()
    {
        try
        {
            init( lpUser );
            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            String tenorProfileName = "Test" + System.nanoTime ();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );

            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement15();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement15ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement16ExternalTx()
    {
        try
        {
            init( lpUser );
            String[] tenors = new String[] {"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[] {0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            double grossMargin = 15.0;
            String tenorProfileName = "Test" + System.nanoTime ();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
            creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );

            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement16();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement16ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement17ExternalTx()
    {
        try
        {
            init( lpUser );
            String[] tenors = new String[] {"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[] {0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            double grossMargin = 15.0;
            String tenorProfileName = "Test" + System.nanoTime ();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
            creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );

            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement17();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement17ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement18ExternalTx()
    {
        try
        {
            init( lpUser );

            String[] tenors = new String[] {"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[] {0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            double grossMargin = 15.0;
            String tenorProfileName = "Test" + System.nanoTime ();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
            creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );
            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement18();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement18ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement19ExternalTx()
    {
        try
        {
            init( lpUser );

            double grossMargin = 15.0;
            String[] tenors = new String[]{"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[]{0.0, 10.0, 20.0, 30.0, 50.0, 100.0};
            String tenorProfileName = "Test" + System.nanoTime ();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
            creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );

            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement19();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement19ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlement20ExternalTx()
    {
        try
        {
            init( lpUser );

            double grossMargin = 15.0;
            String[] tenors = new String[] {"TOD", "SPOT", "1m", "6m", "9m", "1y"};
            double[] coefficients = new double[] {0.0, 10.0, 4.0, 40.0, 40.0, 100.0};
            String tenorProfileName = "Test" + System.nanoTime ();
            CreditTenorProfile tenorProfile = createCreditTenorProfileWithTenorParam ( tenorProfileName, tenors, coefficients );
            creditAdminSvc.addCreditTenorProfile ( lpOrg, tenorProfile );
            creditAdminSvc.setCreditTenorProfile ( lpOrg, lpTpForDd, tenorProfile );
            creditAdminSvc.setUseDefaultTenorProfile ( lpOrg, lpTpForDd, false );
            creditAdminSvc.setGrossPositionSpreadMargin ( lpOrg, tenorProfile, grossMargin );

            IdcTransaction tx = initTransaction( true );

            testAggregateMultiFactorSettlement20();
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            commitTransaction(tx);

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate );
            aggCu.resetCurrencyPositions ( "Test", true );
            aggCu.getCurrencyPositions ( true );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals ( "rebuild cu should have the same limits", limit0, limit1 );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlement20ExternalTx", e );
        }
    }

    public void testAggregateMultiFactorSettlementWithLeverageFactor()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, 100.0 );


            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double utilizedAmt = tradeAmt / 100.0;
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertTrue ( "before limit=" + limit0 + ",limitAfter=" + limit1 + ",utilizedAmt=" + utilizedAmt, Math.abs ( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlementWithLeverageFactor", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, 1.0 );
        }
    }

    public void testAggregateMultiFactorSettlementDealingLimitWithLeverageFactor()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            double aggregateLimit = 1000000;
            double leverageFactor = 100;
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, aggregateLimit );
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, leverageFactor );
            CreditUtilization aggCu = getAggregateCreditUtilization( lpLe, lpTpForDd, spotDate );

            CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString( "USD/JPY" );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, ddLe, currencyPair.getBaseCurrency(), currencyPair.getVariableCurrency(), true, true, spotDate );
            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, ddLe, spotDate, currencyPair.getBaseCurrency(), currencyPair.getVariableCurrency(), true );
            log( "bilateral credit between dd and lp. bilateralCreditDealingLimit=" + dl );
            double availableAmt = aggCu.getAdjustedLimit();
            double leverageAdjustedAvailableAmount = availableAmt * leverageFactor;
            assertTrue( "BilateralCredit bid limit should be adjusted for leverage factor. BilateralCredit=" + dl + ",availableLimit=" + availableAmt, Math.abs ( leverageAdjustedAvailableAmount - dl.getBidLimit() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( "BilateralCredit offer limit should be adjusted for leverage factor. BilateralCredit=" + dl + ",availableLimit=" + availableAmt, Math.abs ( leverageAdjustedAvailableAmount - dl.getOfferLimit() ) < CREDIT_CALCULATION_MINIMUM );

            double tradeAmt = 1000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[7], spotDate );

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double utilizedAmt = tradeAmt / leverageFactor;
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            validateCreditUtilizationEvents( cwm );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, ddLe, spotDate, currencyPair.getBaseCurrency(), currencyPair.getVariableCurrency(), true );
            log( "bilateral credit between dd and lp. bilateralCreditDealingLimit=" + dl1 );
            double availableAmt1 = aggCu.getAdjustedLimit() - utilizedAmt;
            double leverageAdjustedAvailableAmount1 = availableAmt1 * leverageFactor;
            assertTrue( "BilateralCredit bid limit should be adjusted for leverage factor. BilateralCredit=" + dl1 + ",availableLimit1=" + availableAmt1, Math.abs ( leverageAdjustedAvailableAmount1 - dl1.getBidLimit() ) < CREDIT_CALCULATION_MINIMUM  );
            assertTrue( "BilateralCredit offer limit should be adjusted for leverage factor. BilateralCredit=" + dl1 + ",availableLimit1=" + availableAmt1, Math.abs ( leverageAdjustedAvailableAmount1 - dl1.getOfferLimit() ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testAggregateMultiFactorSettlementDealingLimitWithLeverageFactor", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR, 10000000 );
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, 1.0 );
        }
    }
}
