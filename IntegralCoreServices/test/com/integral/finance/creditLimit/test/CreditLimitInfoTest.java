package com.integral.finance.creditLimit.test;

import com.integral.finance.creditLimit.notification.CreditLimitInfo;
import com.integral.finance.creditLimit.notification.CreditStatus;
import org.junit.Assert;
import org.junit.Test;

import com.integral.commons.buffers.UnSafeBuffer;

public class CreditLimitInfoTest  {

	@Test
	public void testSerialization()
	{
		CreditLimitInfo creditLimitInfo = createCreditLimitInfo();
		UnSafeBuffer safeBuf = new UnSafeBuffer();
        byte[] buffer = new byte[ creditLimitInfo.getEstimatedSize() ];
        safeBuf.init( buffer );
        creditLimitInfo.writeTo(safeBuf);
        safeBuf.flip();
		CreditLimitInfo creditLimitInfo1 = new CreditLimitInfo( 0, 0, (short) 0);
        creditLimitInfo1.readFrom(safeBuf);
		Assert.assertTrue( isSameAs( creditLimitInfo, creditLimitInfo1));
	}
	
	private CreditLimitInfo createCreditLimitInfo()
    {

        CreditLimitInfo info = new CreditLimitInfo(1000, 2000, (short) 32550 );
        info.setCreditStatus(CreditStatus.ACTIVE);
        info.setLimitCcy((short) 12);
        info.setAggregateLimit(1000000);
        info.setAggregateAvailable(5000);
        info.setDailyLimit(800000);
        info.setDailyAvailable(4000);
		return info;
	}

    private boolean isSameAs( CreditLimitInfo cli1, CreditLimitInfo cli2 )
    {
        return cli1.getFiLe() == cli2.getFiLe()
                && cli1.getLpLe() == cli2.getLpLe()
                && cli1.getCreditStatus() == cli2.getCreditStatus()
                && cli1.getAggregateLimit() == cli2.getAggregateLimit()
                && cli1.getAggregateAvailable() == cli2.getAggregateAvailable()
                && cli1.getDailyLimit() == cli2.getDailyLimit()
                && cli1.getDailyAvailable() == cli2.getDailyAvailable()
                && cli1.getValueDate() == cli2.getValueDate();
    }
}
