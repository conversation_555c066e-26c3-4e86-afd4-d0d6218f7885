package com.integral.finance.creditLimit.test;

// Copyright (c) 2014 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.*;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;


/**
 * Tests the credit limit service mode tests.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitServiceModePTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Service mode Test";

    public CreditLimitServiceModePTestC( String name )
    {
        super( name );
    }

    public void testCreditIntegrationModeAtCreditProviderLevel()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( ddOrg );

            // set all the credit methodologies to null.
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 2000000 );

            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_INTEGRATION_MODE );
            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, true );

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "credit limit  before take Credit : " + limit0 );
            double tradeAmt = 2000000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() );
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "was taken :", true, Math.abs( limit0 - limit1 - tradeAmt ) < MINIMUM );

            // now another trade even it is going in the opposite direction, should not be allowed.
            tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "credit limit  after take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            assertEquals( "success=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE );
            assertEquals( "was taken :", true, Math.abs( limit2 - limit1 ) < 2 );

            // set the limits to a lower value. We should be able to execute the trade up to this limit again.
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 500000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 500000 );

            tradeAmt = 500000;
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "credit limit  after take Credit : " + limit4 + " success : " + cwm2.getStatus() );
            assertEquals( "success=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS );
            assertEquals( "was taken :", true, Math.abs( limit3 - limit4 - tradeAmt ) < MINIMUM );

            // now another trade even it is going in the opposite direction, should not be allowed.
            tradeAmt = 1000;
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd );
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "credit limit  after take Credit : " + limit5 + " success : " + cwm3.getStatus() );
            assertEquals( "success=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE );
            assertEquals( "was taken limit5=" + limit5 + ",limit4=" + limit4, true, Math.abs( limit5 - limit4 ) < MINIMUM );
        }
        catch ( Exception e )
        {
            log.error( "testCreditIntegrationModeAtCreditProviderLevel", e );
            fail();
        }
        finally
        {
            creditAdminSvc.setMode( lpOrg, lpTpForDd, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, true );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testFXSwapAvailableCreditCheckWithIntegrationMode()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_INTEGRATION_MODE );
            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, true );

            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade, lpLe, ddLe );
            assertNotNull( cwm );
            assertTrue( cwm.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm.getErrorCode() );

            FXSwap trade1 = prepareSwapTrade( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade1, lpLe, ddLe );
            assertNull( cwm1 );

            // do a trade and consume the credit.
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( lpLe, ddLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue( cwm2.getStatus() == MessageStatus.SUCCESS );

            FXSwap trade2 = prepareSwapTrade( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade2, lpLe, ddLe );
            assertNotNull( cwm3 );
            assertTrue( cwm3.getErrors().size() > 0 );
            log( "cwm3.errorCode=" + cwm3.getErrorCode() );


            // after the setting the new limit, earlier used amounts should not be used in the calculation.
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 900000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 900000 );

            FXSwap trade3 = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm4 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( trade3, lpLe, ddLe );
            assertNull( cwm4 );
        }
        catch ( Exception e )
        {
            fail( "testFXSwapAvailableCreditCheckWithIntegrationMode", e );
        }
        finally
        {
            creditAdminSvc.setMode( lpOrg, lpTpForDd, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, true );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 2000000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 );
        }
    }

    public void testBilateralCreditUtilizationAmountUpdateWithIntegrationMode()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents(fiOrg);

            creditAdminSvc.setDefaultMode(lpOrg, CreditLimit.CREDIT_INTEGRATION_MODE);
            creditAdminSvc.setUseDefaultMode(lpOrg, lpTpForDd, true);

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, 10000000, CreditUtilC.getCounterpartyCreditLimitCurrency(lpOrg, fiOrg, lpTpForFi) );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency("EUR"), CurrencyFactory.getCurrency( "USD" ) );
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate);

            //do update Credit utilization amount after take credit for single-leg trade should pass
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit );
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit );

            // change the trade amount
            double offset = -500;
            updateTradeAmount( trade, tradeAmt + offset, true, false );

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, trade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS );

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 );
            log( "avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1 );
            assertEquals( "aggregate LP limits should be sum of offset", Math.abs( avaliableLpAggLimit - avaliableLpAggLimit1 - offset ) < CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "aggregate FI limits should be sum of offset", Math.abs( avaliableFiAggLimit - avaliableFiAggLimit1 - offset ) < CREDIT_CALCULATION_MINIMUM, true );

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false );
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false );

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() - offset ) < CREDIT_CALCULATION_MINIMUM, true );
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() - offset ) < CREDIT_CALCULATION_MINIMUM, true );
        }
        catch ( Exception e )
        {
            fail("testBilateralCreditUtilizationAmountUpdateWithIntegrationMode", e);
        }
        finally
        {
            removeCreditLimitSubscriptions(lpLe, lpTpForFi);
            removeCreditLimitSubscriptions(fiLe, fiTpForLp);
            creditAdminSvc.setMode( lpOrg, lpTpForDd, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setDefaultMode(lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE);
            creditAdminSvc.setUseDefaultMode(lpOrg, lpTpForDd, true);
        }
    }
}
