package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.creditLimit.CreditLimitFactory;
import com.integral.finance.creditLimit.CreditLimitRule;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Vector;


public class CreditUtilizationPTestC
        extends PTestCaseC
{
    static String name = "CreditUtilization Test";
    Vector users = null;

    public CreditUtilizationPTestC( String name )
    {
        super( name );
    }

    public void testInsertCreditUtilization()
    {
        log( "testInsertCreditUtilization" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            CreditUtilization cu = new CreditUtilizationC();
            cu.setStatus( 'T' );
            CreditUtilization registeredCu = ( CreditUtilization ) uow.registerObject( cu );
            registeredCu.setPositions( "TestPositions" );
            CreditLimitRule clr = CreditLimitFactory.newCreditLimitRule();
            clr.setShortName( "Test" + System.nanoTime() );
            clr.setCurrency( CurrencyFactory.getCurrency( "USD" ) );
            registeredCu.setCreditLimitRule( clr );
            registeredCu.setLimitAmount( 1000.0 );
            registeredCu.setMarketRates( "MarketRates" );
            registeredCu.setUsedAmount( 100.0 );
            registeredCu.setEarmarkedUsedAmount( 10.0 );

            uow.commit();

            cu = ( CreditUtilization ) IdcUtilC.refreshObject( cu );
            assertNotNull( cu );

            log( "testInsertCreditUtilization" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsertCreditUtilization" );
        }
    }


    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CreditUtilization testCu = new CreditUtilizationC();
            assertTrue( testCu.getLimitLastUpdatedTime() == 0 );
            CreditUtilization registeredTestCu = ( CreditUtilization ) uow.registerObject( testCu );
            registeredTestCu.setStatus( Entity.DELETED_STATUS );
            registeredTestCu.setLimitAmount( 1000.0 );
            CreditLimitRule clr = CreditLimitFactory.newCreditLimitRule();
            clr.setShortName( "Test" + System.nanoTime() );
            clr.setCurrency( CurrencyFactory.getCurrency( "USD" ) );
            registeredTestCu.setCreditLimitRule( clr );
            registeredTestCu.setUsedAmount( 100.0 );
            registeredTestCu.setEarmarkedUsedAmount( 10.0 );
            long now = System.currentTimeMillis();
            registeredTestCu.setLimitLastUpdatedTime( now );
            registeredTestCu.setPositionLastUpdatedTime ( now );
            long checksum = 12345;
            registeredTestCu.setCurrencyPositionChecksum( checksum );
            uow.commit();

            // refresh the trade and verify the fields
            testCu = ( CreditUtilization ) session.refreshObject( testCu );
            assertEquals( "limit amount", testCu.getLimitAmount(), 1000.0 );
            assertEquals( "used amount", testCu.getUsedAmount(), 100.0 );
            assertEquals( "earmarked used amount", testCu.getEarmarkedUsedAmount(), 10.0 );
            assertTrue( testCu.getPositionLastUpdatedTime() == now );
            assertTrue( testCu.getCurrencyPositionChecksum() == checksum );
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence : ", e );
        }
    }

    public void testCreditUtilizationLookupByObjectId()
    {
        try
        {
            Collection<CreditUtilization> creditUtils = getPersistenceSession().readAllObjects( CreditUtilizationC.class );
            assertTrue( creditUtils.size() > 0 );
            for ( CreditUtilization cu: creditUtils )
            {
                long oid = cu.getObjectID();
                CreditUtilization cu1 = ( CreditUtilization ) ReferenceDataCacheC.getInstance().getEntityByObjectId( oid, CreditUtilization.class );
                assertNotNull( cu1 );
                assertTrue( cu1.getObjectID() == oid );

                CreditUtilization cu2 = ( CreditUtilization ) ReferenceDataCacheC.getInstance().getEntityByObjectId( oid, CreditUtilizationC.class );
                assertNotNull( cu2 );
                assertTrue( cu2.getObjectID() == oid );
            }
        }
        catch ( Exception e )
        {
            fail ( "testCreditUtilizationLookupByObjectId", e );
        }
    }

}
