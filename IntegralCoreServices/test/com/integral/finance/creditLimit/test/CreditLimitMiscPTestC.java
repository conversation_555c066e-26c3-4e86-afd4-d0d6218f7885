package com.integral.finance.creditLimit.test;

// Copyright (c) 2013 Integral Development Corporation.  All Rights Reserved.

import com.integral.businessCenter.BusinessCenter;
import com.integral.businessCenter.BusinessCenterC;
import com.integral.businessCenter.BusinessCenterFactory;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.*;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.functor.CreditUtilizationEventRecalculationNotificationFunctorC;
import com.integral.finance.creditLimit.handler.CreditUtilizationCacheSynchronizationHandlerC;
import com.integral.finance.creditLimit.quickcheck.LP_FI;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.message.MessageStatus;
import com.integral.netting.model.NettingPortfolio;
import com.integral.persistence.EntityFactory;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;
import com.integral.util.Tuple;
import com.integral.workflow.dealing.DealingLimit;

import java.util.*;

/**
 * Tests misc. credit workflow test cases
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitMiscPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Misc. Tests";

    public CreditLimitMiscPTestC( String name )
    {
        super( name );
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUndo()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );
            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( fiOrg );
                    removeExistingCreditUtilizationEvents( lpOrg );
                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUndo", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testEndOfDayPLUpdate()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setApplyPandL( lpOrg, lpTpForFi, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            creditAdminSvc.setApplyPandL( fiOrg, fiTpForLp, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            creditAdminSvc.setUpdateBalanceWithPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, true );
            creditAdminSvc.setUpdateBalanceWithPL( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, true );

            // now execute credit workflows
            executeCreditWorkflows( lpOrg, lpLe, fiOrg, lpTpForFi );

            // recalculate and end of day p/l will be updated.
            CreditUtilizationManagerC.getInstance().recalculateAllAggregateCreditUtilizations( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            // reset all the values back to previous
            creditAdminSvc.setApplyPandL( lpOrg, lpTpForFi, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
            creditAdminSvc.setApplyPandL( fiOrg, fiTpForLp, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
            creditAdminSvc.setUpdateBalanceWithPL( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, false );
            creditAdminSvc.setUpdateBalanceWithPL( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, false );
        }
    }

    public void testUpdateCreditAmountsResultingBreach()
    {
        try
        {
            //set org level exposure
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // do trade and check credit
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            double tradeAmt = 900;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            double availableLimit = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "availableLimit=" + availableLimit );
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( fiLe, lpLe, spotDate, eur, usd, false );
            log( "availableDL=" + dl );
            assertEquals( "success=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() );

            // do an opposite trade to reverse the position
            double tradeAmt1 = 900;
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            double availableLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "availableLimit1=" + availableLimit1 );
            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( fiLe, lpLe, spotDate, eur, usd, false );
            log( "availableDL1=" + dl1 );
            assertEquals( "success=" + cwm1.getStatus(), MessageStatus.SUCCESS, cwm1.getStatus() );

            // do an opposite trade to reverse the position
            double tradeAmt2 = 900;
            Trade trade2 = prepareSingleLegTrade( tradeAmt2, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
            double availableLimit2 = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "availableLimit2=" + availableLimit2 );
            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( fiLe, lpLe, spotDate, eur, usd, false );
            log( "availableDL2=" + dl2 );
            assertEquals( "success=" + cwm2.getStatus(), MessageStatus.SUCCESS, cwm2.getStatus() );

            // now amend the original trade with reduced amount and update credit.
            FXLeg trdLeg = ( ( FXSingleLeg ) trade ).getFXLeg();
            trdLeg.getFXPayment().setCurrency1Amount( trdLeg.getFXPayment().getCurrency1Amount() / 2 );
            trdLeg.getFXPayment().setCurrency2Amount( trdLeg.getFXPayment().getCurrency1Amount() / 2 );

            CreditWorkflowMessage cwm3 = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, trade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            double availableLimit3 = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "availableLimit3=" + availableLimit3 );
            DealingLimit dl3 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( fiLe, lpLe, spotDate, eur, usd, false );
            log( "availableDL3=" + dl3 );
            assertEquals( MessageStatus.SUCCESS, cwm3.getStatus() );
            assertEquals( dl3.getBidLimit(), 0.0 );
            assertTrue( dl3.getOfferLimit() > 0.0 );

            // now do a trade using the offer limit as bid limit will be zero.
            double tradeAmt4 = dl3.getOfferLimit();
            Trade trade4 = prepareSingleLegTrade( tradeAmt4, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            double availableLimit4 = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "availableLimit4=" + availableLimit4 );
            DealingLimit dl4 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( fiLe, lpLe, spotDate, eur, usd, false );
            log( "availableDL4=" + dl4 );
            assertEquals( "success=" + cwm4.getStatus(), MessageStatus.SUCCESS, cwm4.getStatus() );

            // now do a trade for a big amount to make sure that credit check will be failed.
            double tradeAmt5 = dl4.getBidLimit() * 10;
            Trade trade5 = prepareSingleLegTrade( tradeAmt5, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm5 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade5, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            double availableLimit5 = getAvailableCreditLimit( lpTpForFi, spotDate );
            log( "availableLimit5=" + availableLimit5 );
            DealingLimit dl5 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( fiLe, lpLe, spotDate, eur, usd, false );
            log( "availableDL5=" + dl5 );
            assertEquals( "success=" + cwm5.getStatus(), MessageStatus.FAILURE, cwm5.getStatus() );
        }
        catch ( Exception e )
        {
            log.error( "testUpdateCreditAmountsResultingBreach", e );
            fail();
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testSetCreditRelationshipAtOrgLevelExternalTx()
    {
        try
        {
            init( adminUser );
            IdcTransaction tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            tx.getUOW().removeReadOnlyClass( BusinessCenterC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );
            tx.getUOW().removeReadOnlyClass( CounterpartyCreditLimitRuleC.class );
            tx.getUOW().removeReadOnlyClass( CreditLimitRuleC.class );
            tx.getUOW().removeReadOnlyClass( CreditLimitRuleSetC.class );
            tx.getUOW().removeReadOnlyClass( CreditLimitOrgFunctionC.class );
            Organization org = UserFactory.newOrganization();
            Organization registeredOrg = ( Organization ) org.getRegisteredObject();
            Organization registeredDdOrg = ( Organization ) ddOrg.getRegisteredObject();
            registeredOrg.setShortName( "NewOrg" + System.currentTimeMillis() );
            registeredOrg.setLongName( "LongName" );
            Namespace ns = ( Namespace ) EntityFactory.newNamespace().getRegisteredObject();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            registeredOrg.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = ( LegalEntity ) CounterpartyFactory.newLegalEntity().getRegisteredObject();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( registeredOrg.getNamespace() );
            le.setOrganization( registeredOrg );
            registeredOrg.getLegalEntities().add( le );
            registeredOrg.putCustomField( DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME, le );
            registeredOrg.setDefaultDealingEntity( le );

            // set the business center reporting currency.
            BusinessCenter bs = ( BusinessCenter ) BusinessCenterFactory.newBusinessCenter().getRegisteredObject();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the trading party.
            TradingParty tp = CounterpartyFactory.newTradingParty();
            TradingParty registeredTp = ( TradingParty ) tp.getRegisteredObject();
            LegalEntity registeredDdLe = ( LegalEntity ) ddLe.getRegisteredObject();
            registeredTp.setLegalEntity( registeredDdLe );
            registeredTp.setShortName( ddLe.getShortName() );
            registeredTp.setOrganization( registeredOrg );
            registeredTp.setNamespace( registeredOrg.getNamespace() );
            registeredOrg.getTradingParties().add( registeredTp );

            creditAdminSvc.setupCreditProvider( registeredOrg, staticMds );
            // tweak the credit limit org function to set at org level.
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( registeredOrg );
            orgFunc.setOrgLevelCreditExposure( true );

            commitTransaction( tx );

            creditAdminSvc.establishCreditRelationship( org, tp );


            log( "le=" + le );
            log( "org=" + org );
            log( "tp=" + tp );
            tp = (TradingParty) IdcUtilC.refreshObject( tp );
            assertNotNull( "tp after refresh=" + tp, tp );
            org = (Organization) IdcUtilC.refreshObject( org );
            assertNotNull( "org after refresh=" + org, org );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( org, tp );
            assertNotNull( cclr );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            // check the spot date credit utilization.
            Double limit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( le, tp, tp.getLegalEntityOrganization(), spotDate );
            log( "spot date limit=" + limit );
            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditProviderEnabled() )
            {
                assertEquals( "limit is not null. ", limit != null, true );
            }
            else
            {
                assertEquals( "limit will be null.", limit == null, true );
            }

            // now remove the trading relationship.
            creditAdminSvc.removeCreditRelationship( org, tp );
            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( org, tp );
            log( "cclr1=" + cclr1 + ",cclr1.enabled=" + cclr1.isEnabled() + ",status=" + cclr1.getStatus() );
            assertEquals( "cclr1 is not null", cclr1 != null, true );
            assertEquals( "cclr1 active ", cclr.isActive(), true );
            log( "children rules=" + cclr1.getChildrenRules() );
            assertEquals( "cclr1 children rules not empty", cclr1.getChildrenRules().isEmpty(), false );
            assertEquals( "checkSanity", sanityCheck( org ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditRelationshipAtTradingPartyLevelExternalTx()
    {
        try
        {
            init( adminUser );
            IdcTransaction tx = initTransaction( false );
            tx.getUOW().removeReadOnlyClass( LegalEntityC.class );
            tx.getUOW().removeReadOnlyClass( CreditUtilizationC.class );
            tx.getUOW().removeReadOnlyClass( OrganizationC.class );
            tx.getUOW().removeReadOnlyClass( TradingPartyC.class );
            tx.getUOW().removeReadOnlyClass( BusinessCenterC.class );
            tx.getUOW().addReadOnlyClass( CurrencyC.class );
            Organization org = UserFactory.newOrganization();
            Organization registeredOrg = ( Organization ) org.getRegisteredObject();
            Organization registeredDdOrg = ( Organization ) ddOrg.getRegisteredObject();
            registeredOrg.setShortName( "NewOrg" + System.currentTimeMillis() );
            registeredOrg.setLongName( "LongName" );
            Namespace ns = ( Namespace ) EntityFactory.newNamespace().getRegisteredObject();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            registeredOrg.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = ( LegalEntity ) CounterpartyFactory.newLegalEntity().getRegisteredObject();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( registeredOrg.getNamespace() );
            le.setOrganization( registeredOrg );
            registeredOrg.getLegalEntities().add( le );
            registeredOrg.putCustomField( DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME, le );
            registeredOrg.setDefaultDealingEntity( le );

            // set the business center reporting currency.
            BusinessCenter bs = ( BusinessCenter ) BusinessCenterFactory.newBusinessCenter().getRegisteredObject();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the trading party.
            TradingParty tp = ( TradingParty ) CounterpartyFactory.newTradingParty().getRegisteredObject();
            LegalEntity registeredDdLe = ( LegalEntity ) ddLe.getRegisteredObject();
            tp.setLegalEntity( registeredDdLe );
            tp.setShortName( ddLe.getShortName() );
            tp.setOrganization( registeredOrg );
            tp.setNamespace( registeredOrg.getNamespace() );
            registeredOrg.getTradingParties().add( tp );

            creditAdminSvc.setupCreditProvider( registeredOrg, staticMds );

            commitTransaction( tx );

            creditAdminSvc.establishCreditRelationship( registeredOrg, tp );

            log( "le=" + le );
            log( "org=" + org );
            log( "tp=" + tp );
            tp = (TradingParty) IdcUtilC.refreshObject( tp );
            assertNotNull( "tp after refresh=" + tp, tp );
            org = (Organization) IdcUtilC.refreshObject( org );
            assertNotNull( "org after refresh=" + org, org );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( org, tp );
            assertNotNull( cclr );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            // check the spot date credit utilization.
            Double limit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( le, tp, tp.getLegalEntityOrganization(), spotDate );
            log( "spot date limit=" + limit );
            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditProviderEnabled() )
            {
                assertEquals( "limit is not null. ", limit != null, true );
            }
            else
            {
                assertEquals( "limit is null. ", limit != null, false );
            }

            // now remove the trading relationship.
            creditAdminSvc.removeCreditRelationship( org, tp );
            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( org, tp );
            log( "cclr1=" + cclr1 + ",cclr1.enabled=" + cclr1.isEnabled() + ",status=" + cclr1.getStatus() );
            assertEquals( "cclr1 is not null", cclr1 != null, true );
            assertEquals( "cclr1 active ", cclr1.isActive(), false );
            log( "children rules=" + cclr1.getChildrenRules() );
            assertEquals( "cclr1 children rules not empty", cclr1.getChildrenRules().isEmpty(), false );
            assertEquals( "checkSanity", sanityCheck( org ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testAggregateNetPRSettlementUndoCredit()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
                creditAdminSvc.reinitialize( lpOrg );
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 );

            // first do a GBP/JPY trade.
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );

            double gbpAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true );
            double jpyAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt * bidRates[4], false );
            double utilizedAmt = Math.abs( jpyAmt1 ) + Math.abs( gbpAmt1 );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt );
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM );
            validateCreditUtilizationEvents( cwm );

            // do the GBP/JPY trade in the opposite direction.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );
            validateCreditUtilizationEvents( cwm1 );
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM );

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm2 = creditMgr.undoCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after second offer trade undo Credit : " + limit3 + " success : " + cwm2.getStatus() );
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm2 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditUtilizationEventRecalculationNotificationFunctor()
    {
        try
        {
            String[] orgGuids = new String[2];
            orgGuids[0] = takerOrg.getGUID();
            orgGuids[1] = fiOrg.getGUID();
            HashMap  propertiesMap = new HashMap( 2 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGS, orgGuids );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.RECALCULATE_UTILIZATION_EVENTS.getName() );
            new CreditUtilizationEventRecalculationNotificationFunctorC().onCommit( propertiesMap );

            // change the org guids to an array list.
            propertiesMap.clear();
            List list = new ArrayList();
            list.add( takerOrg.getGUID() );
            list.add( fiOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGS, list );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.RECALCULATE_UTILIZATION_EVENTS.getName() );
            new CreditUtilizationEventRecalculationNotificationFunctorC().onCommit( propertiesMap );

        }
        catch ( Exception e )
        {
            fail("testCreditUtilizationEventRecalculationNotificationFunctor");
        }
    }

    public void testLPFI()
    {
        try
        {
            Map<String, LP_FI> lpFiMap = new HashMap<String, LP_FI>();
            Organization[] orgs = ReferenceDataCacheC.getInstance().getOrgs();
            for ( Organization org : orgs )
            {
                for ( Organization otherOrg: orgs )
                {
                    if ( org.isSameAs( otherOrg ) )
                    {
                        continue;
                    }
                    LP_FI lp_fi = new LP_FI( org, otherOrg );
                    String index = lp_fi.getIndex();
                    assertFalse( lpFiMap.containsKey( index ) );
                    lpFiMap.put( index, lp_fi );
                }
            }

            log( "lpFiMap=" + lpFiMap );

            Set<LP_FI> lpFiSet = new HashSet<LP_FI>( lpFiMap.values() );
            int size = lpFiSet.size();
            for ( LP_FI lpfi: lpFiMap.values() )
            {
                lpFiSet.add( lpfi );
                int newSize = lpFiSet.size();
                assertEquals( "size before=" + size + ",size after=" + newSize , size, newSize );
            }
        }
        catch ( Exception e )
        {
            fail ( "testLPFI" );
        }
    }

    public void testPortfolioCreditCheckOnSuspended()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            if ( null == cptyRule )
            {
                creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi, false );
            }
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, ********* );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, ********* );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, ********* );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, ********* );
            Collection<Organization> toOrgs = new ArrayList<Organization>();
            toOrgs.add( lpOrg );

            Collection<LegalEntity> custLes =new HashSet<LegalEntity>();

            for ( LegalEntity custLe: (Collection<LegalEntity>) fiOrg.getLegalEntities() )
            {
                TradingParty lpTpForCustLe = custLe.getTradingParty( lpOrg );
                if ( lpTpForCustLe == null )
                {
                    log.warn( " no trading party found for custLe=" + custLe + " in lpOrg=" + lpOrg );
                    continue;
                }
                assertNotNull( lpOrg.getDefaultDealingEntity() );
                if ( CounterpartyUtilC.isValidRelationShip( custLe, lpOrg.getDefaultDealingEntity()) )
                {
                    int status = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditStatus( lpOrg, lpTpForCustLe ) ;
                    if ( !( CREDIT_SUSPEND == status)  )
                    {
                        double limit = getAvailableCreditLimit( lpTpForCustLe, spotDate );
                        if ( CREDIT_NO_CHECK == status || limit > 0.0 )
                        {
                            List<CreditEntity> creditEntities = CreditUtilC.getAllEnabledCreditEntitiesBetween(lpOrg.getDefaultDealingEntity(), custLe );
                            boolean activeCreditLines = true;
                            for ( CreditEntity creditEntity: creditEntities )
                            {
                                final Organization cpo = creditEntity.getLegalEntity().getOrganization();
                                final TradingParty cc = creditEntity.getTradingParty();
                                if ( CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled(cpo, cc) )
                                {
                                    CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, cc, true );
                                    if ( cclr == null )
                                    {
                                        activeCreditLines = false;
                                    }
                                }
                            }
                            if ( activeCreditLines )
                            {
                                custLes.add(custLe);
                            }
                        }
                    }
                }
            }

            // set the credit status to suspended.
            creditAdminSvc.setCreditStatus( lpOrg, CreditLimitConstants.CREDIT_SUSPEND );

            NettingPortfolio np = DealingTestUtilC.createTestNettingPortfolio( "test", "FXI1000", custLes, toOrgs, 1, 1, 1 );
            assertNotNull( np );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance().checkPortfolioCredit( np );
            assertNull( cwm );

            CreditWorkflowMessage cwmx = CreditUtilizationManagerC.getInstance().checkPortfolioCredit( np, false, true );
            assertNotNull( cwmx );

            assertTrue( cwmx.getStatus() == MessageStatus.FAILURE );


            // set the credit status to active.
            creditAdminSvc.setCreditStatus( lpOrg, CreditLimitConstants.CREDIT_ACTIVE );

            NettingPortfolio np1 = DealingTestUtilC.createTestNettingPortfolio( "test", "FXI1000", custLes, toOrgs, 1, 1, 1 );
            assertNotNull( np1 );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance().checkPortfolioCredit( np1 );
            assertNull( cwm1 );


            creditAdminSvc.setCreditStatus( lpOrg, lpTpForFi, CreditLimitConstants.CREDIT_SUSPEND );

            NettingPortfolio np2 = DealingTestUtilC.createTestNettingPortfolio( "test", "FXI1000", custLes, toOrgs, 1, 1, 1 );
            assertNotNull( np2 );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance().checkPortfolioCredit( np2 );
            assertNull ( cwm2 );

            CreditWorkflowMessage cwm3 = CreditUtilizationManagerC.getInstance().checkPortfolioCredit( np2, false, true );
            assertNotNull ( cwm3 );
            assertTrue( MessageStatus.FAILURE.equals( cwm3.getStatus() ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testPortfolioCreditCheckOnSuspended", e );
        }
        finally
        {
            creditAdminSvc.setCreditStatus( lpOrg, CreditLimitConstants.CREDIT_ACTIVE );
            creditAdminSvc.setCreditStatus( lpOrg, lpTpForFi, CreditLimitConstants.CREDIT_ACTIVE );
        }
    }

    public void testCreditUtilizationDateComparator()
    {
        Set<CreditUtilization> creditUtils = new TreeSet<CreditUtilization>( new CreditUtilizationComparatorC() );
        CreditUtilization aggCu = getAggregateCreditUtilization( lpLe, lpTpForFi, tradeDate );
        CreditUtilization dailyCu1 = getDailyCreditUtilization( lpLe, lpTpForFi, tradeDate );
        CreditUtilization dailyCu2 = getDailyCreditUtilization( lpLe, lpTpForFi, spotDate );
        creditUtils.add( dailyCu1 );
        creditUtils.add( aggCu );
        creditUtils.add( dailyCu2 );
        assertTrue( creditUtils.toArray()[0] == aggCu );
        assertTrue( creditUtils.toArray()[1] == dailyCu1 );
        assertTrue( creditUtils.toArray()[2] == dailyCu2 );
    }

    public void testCptyRuleComparator1()
    {
        try
        {
            Set<CounterpartyCreditLimitRule> cptyRules = new TreeSet<CounterpartyCreditLimitRule> ( new CptyRuleComparatorC () );
            creditAdminSvc.setLegalEntityExposureLevel ( fiOrg, lpOrg );
            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( fiOrg, fiTpforCpty );
            CounterpartyCreditLimitRule cclr2 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( fiOrg, fiTpForLpLe2 );
            CounterpartyCreditLimitRule cclr3 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( fiOrg, fiTpForLp );

            cptyRules.add ( cclr1 );
            cptyRules.add ( cclr2 );
            cptyRules.add ( cclr3 );

            assertTrue ( cptyRules.toArray ()[ 0 ] == cclr3 );
            assertTrue ( cptyRules.toArray ()[ 1 ] == cclr2 );
            assertTrue ( cptyRules.toArray ()[ 2 ] == cclr1 );
        }
        catch ( Exception e )
        {
            fail ( "testCptyRuleComparator1", e );
        }
    }

    public void testCptyRuleComparator2()
    {
        try
        {
            Set<CounterpartyCreditLimitRule> cptyRules = new TreeSet<CounterpartyCreditLimitRule> ( new CptyRuleComparatorC () );
            creditAdminSvc.setLegalEntityExposureLevel ( takerOrg, lpOrg );
            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( takerOrg, takerTpForMaker );
            CounterpartyCreditLimitRule cclr2 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( takerOrg, takerTpForMakerPb );
            CounterpartyCreditLimitRule cclr3 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( takerOrg, takerTpForTakerPb );
            CounterpartyCreditLimitRule cclr4 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( takerOrg, takerTpForLpLe2 );
            CounterpartyCreditLimitRule cclr5 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( takerOrg, takerTpForLp );

            cptyRules.add ( cclr1 );
            cptyRules.add ( cclr2 );
            cptyRules.add ( cclr3 );
            cptyRules.add (  cclr4 );
            cptyRules.add (  cclr5 );

            assertTrue ( cptyRules.toArray ()[ 0 ] == cclr5 );
            assertTrue ( cptyRules.toArray ()[ 1 ] == cclr4 );
            assertTrue ( cptyRules.toArray ()[ 2 ] == cclr1 );
            assertTrue ( cptyRules.toArray ()[ 3 ] == cclr3 );
            assertTrue ( cptyRules.toArray ()[ 4 ] == cclr2 );
        }
        catch ( Exception e )
        {
            fail ( "testCptyRuleComparator2", e );
        }
    }

    public void testAggregateCashNetSettlementLimitCheckFlag()
    {
        try
        {
            init ( lpUser );
            removeExistingCreditUtilizationEvents ( lpOrg );

            setCalcAndLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 );
            setCalcAndLimit ( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );

            assertTrue ( CreditUtilC.isCashSettlementEnabled ( lpOrg, lpTpForDd ) );
            assertFalse ( CreditUtilC.isCashSettlementEnabledWithLimitCheck ( lpOrg, lpTpForDd ) );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( lpOrg, lpTpForDd );
            CreditLimitRule clr = ( CreditLimitRule ) cclr.getChildRule ( GROSS_NOTIONAL_RULE_SHORT_NAME );

            assertTrue ( CreditUtilC.isCashSettlement ( clr ) );
            assertFalse ( CreditUtilC.isCashSettlementWithLimitCheckEnabled ( clr) );

            setCalcAndLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 );

            assertTrue ( CreditUtilC.isCashSettlementEnabled ( lpOrg, lpTpForDd ) );
            assertTrue ( CreditUtilC.isCashSettlementEnabledWithLimitCheck ( lpOrg, lpTpForDd ) );
            assertTrue ( CreditUtilC.isCashSettlement ( clr ) );
            assertTrue ( CreditUtilC.isCashSettlementWithLimitCheckEnabled ( clr) );
        }
        catch ( Exception e )
        {
            fail ( "testAggregateCashNetSettlementLimitCheckFlag", e );
        }
        finally
        {
            setCalcAndLimit ( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit ( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 );
        }
    }
}
