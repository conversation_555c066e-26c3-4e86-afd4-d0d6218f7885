package com.integral.finance.creditLimit.test;

// Copyright (c) 2015 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.server.config.CreditServerConfig;
import com.integral.finance.creditLimit.server.config.CreditServerConfigFactory;
import com.integral.finance.creditLimit.server.config.CreditServerConfigMBean;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;

import java.util.Collection;

/**
 * <AUTHOR> Development Corporation.
 */
public class CreditServerConfigMBeanTest extends MBeanTestCaseC
{
    CreditServerConfig creditServerConfig = ( CreditServerConfig ) CreditServerConfigFactory.getCreditServerConfigMBean();

    public CreditServerConfigMBeanTest( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( creditServerConfig, "creditServerEnabled", CreditServerConfigMBean.CREDIT_SERVER_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditServerConfig, "ccyPositionNotificationEnabled", CreditServerConfigMBean.CCY_POSITION_NOTIFICATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditServerConfig, "creditLimitsPublishPeriod", CreditServerConfigMBean.CREDIT_LIMITS_PUBLISH_PERIOD, MBeanTestCaseC.LONG );
        testProperty( creditServerConfig, "creditLinesReloadPeriod", CreditServerConfigMBean.CREDIT_LINES_RELOAD_PERIOD, MBeanTestCaseC.LONG );
    }

    public void testLimitNotificationEnabledVSTypes()
    {
        try
        {
            creditServerConfig.setProperty( CreditServerConfigMBean.LIMIT_NOTIFICATION_ENABLED_VS_TYPES, "MakerOrderGateway,OrderAdaptor", ConfigurationProperty.DYNAMIC_SCOPE, null );
            Collection<String> vsTypes = creditServerConfig.getLimitNotificationEnabledVSTypes();
            log( "vsTypes=" + vsTypes );
            assertTrue( vsTypes.contains( "MakerOrderGateway" ) );
            assertTrue( vsTypes.contains( "OrderAdaptor" ) );
        }
        catch ( Exception e )
        {
            fail( "testLimitNotificationEnabledVSTypes" );
            e.printStackTrace();
        }
    }
}
