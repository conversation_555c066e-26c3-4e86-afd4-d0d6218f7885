package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationC;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.creditLimit.CreditUtilizationEventC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.trade.Trade;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.user.Organization;
import com.integral.user.User;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.math.BigDecimal;


public class CreditUtilizationEventPTestC
        extends PTestCaseC
{
    static String name = "CreditUtilizationEvent Test";

    public CreditUtilizationEventPTestC( String name )
    {
        super( name );
    }

    public void testInsertCreditUtilizationEvent()
    {
        log( "testInsertCreditUtilizationEvent" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            // register only event and then point it to trade
            CreditUtilizationEvent event1 = new CreditUtilizationEventC();
            event1.setStatus( 'T' );
            event1 = ( CreditUtilizationEvent ) uow.registerObject( event1 );
            Trade trade1 = new FXSingleLegC();

            // point it to trade and then register only event
            CreditUtilizationEvent event2 = new CreditUtilizationEventC();
            event2.setStatus( 'T' );
            Trade trade2 = new FXSingleLegC();
            event2 = ( CreditUtilizationEvent ) uow.registerObject( event2 );

            // register event and point it to registered trade
            CreditUtilizationEvent event3 = new CreditUtilizationEventC();
            event3.setStatus( 'T' );
            event3 = ( CreditUtilizationEvent ) uow.registerObject( event3 );
            Trade trade3 = new FXSingleLegC();
            trade3 = ( Trade ) uow.registerObject( trade3 );

            uow.commit();

            log( "testInsertCreditUtilizationEvent" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsertCreditUtilizationEvent" );
        }
    }

    public void testInsertCreditUtilizationEvent1()
    {
        log( "testInsertCreditUtilizationEvent1" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            // register only event and then point it to trade
            CreditUtilizationEvent event1 = new CreditUtilizationEventC();
            event1 = ( CreditUtilizationEvent ) uow.registerObject( event1 );
            event1.setStatus( 'T' );
            Trade trade1 = new FXSingleLegC();

            uow.commit();

            log( "testInsertFXCreditUtilizationEvent" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsertFXCreditUtilizationEvent" );
        }
    }

    public void testTransactionId()
    {
        try
        {
            String txId = "Test" + System.currentTimeMillis();
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CreditUtilizationEvent cue = new CreditUtilizationEventC();
            CreditUtilizationEvent registeredCue = ( CreditUtilizationEvent ) uow.registerObject( cue );
            registeredCue.setTransactionId( txId );
            uow.commit();

            // refresh the trade and retrieve the maker request.
            cue = ( CreditUtilizationEvent ) session.refreshObject( cue );
            log( "cue=" + cue + ",cue.transactionId=" + cue.getTransactionId() );
            assertEquals( "transaction id should be same.", cue.getTransactionId(), txId );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditUtilizationEventMaximumSizeSnapshots()
    {
        log( "testCreditUtilizationEventMaximumSizeSnapshots" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            // register only event and then point it to trade
            CreditUtilizationEvent event1 = new CreditUtilizationEventC();
            event1 = ( CreditUtilizationEvent ) uow.registerObject( event1 );
            event1.setStatus( 'T' );
            StringBuilder snapshot = new StringBuilder( 5000 ).append( "0123456789" );
            for ( int i = 0; i < 400; i++ )
            {
                snapshot.append( "0123456789" );
            }

            assertTrue( snapshot.length() > 4000 );

            event1.setPositionSnapshot( snapshot.toString() );
            event1.setMarketSnapshot( snapshot.toString() );

            uow.commit();

            event1 = ( CreditUtilizationEvent ) getPersistenceSession().refreshObject( event1 );
            assertEquals( event1.getPositionSnapshot(), snapshot.toString());
            assertEquals( event1.getMarketSnapshot().length(), 4000 );

            log( "testCreditUtilizationEventMaximumSizeSnapshots" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCreditUtilizationEventMaximumSizeSnapshots" );
        }
    }

    public void testCreditUtilizationMaximumSizeSnapshots()
    {
        log( "testCreditUtilizationMaximumSizeSnapshots" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            // register only event and then point it to trade
            CreditUtilization testCu = new CreditUtilizationC();
            testCu = ( CreditUtilization ) uow.registerObject( testCu );
            testCu.setStatus( 'T' );
            StringBuilder snapshot = new StringBuilder( 5000 ).append( "0123456789" );
            for ( int i = 0; i < 400; i++ )
            {
                snapshot.append( "0123456789" );
            }

            assertTrue( snapshot.length() > 4000 );

            testCu.setPositions( snapshot.toString() );
            testCu.setMarketRates( snapshot.toString() );

            uow.commit();

            testCu = ( CreditUtilization ) getPersistenceSession().refreshObject( testCu );
            assertEquals( testCu.getPositions(), snapshot.toString() );
            assertEquals( testCu.getMarketRates().length(), 4000 );

            log( "testCreditUtilizationMaximumSizeSnapshots" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCreditUtilizationMaximumSizeSnapshots" );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            CreditUtilizationEvent testCue = new CreditUtilizationEventC();
            CreditUtilizationEvent registeredTestCue = ( CreditUtilizationEvent ) uow.registerObject( testCue );
            registeredTestCue.setStatus( Entity.DELETED_STATUS );
            double origPrincipalAmt = 100000d;
            double origPriceAmt = 200000d;
            registeredTestCue.setOriginalPrincipalAmount( origPrincipalAmt );
            registeredTestCue.setOriginalPriceAmount( origPriceAmt );
            //registeredTestCue.setOriginalTradePrincipalAmount( origPrincipalAmt );
            //registeredTestCue.setOriginalTradePriceAmount( origPriceAmt );
            registeredTestCue.setTradingParty( ( TradingParty ) uow.readObject( TradingParty.class ) );
            registeredTestCue.setLegalEntity( ( LegalEntity ) uow.readObject( LegalEntity.class ) );
            registeredTestCue.setTradingPartyOrganization( ( Organization ) uow.readObject( Organization.class ) );
            registeredTestCue.setPrincipal( 1000d );
            registeredTestCue.setPrincipalCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            registeredTestCue.setPrice( 1000d );
            registeredTestCue.setPriceCurrency( CurrencyFactory.getCurrency( "USD" ) );
            registeredTestCue.setBuySell( 'S' );
            registeredTestCue.setTradeDate( DateTimeFactory.newDate() );
            registeredTestCue.setSettlementDate( DateTimeFactory.newDate() );
            registeredTestCue.setInstrument( CurrencyFactory.newCurrency( "EUR" ) );
            registeredTestCue.setTradeRate( 7.2345 );
            registeredTestCue.setTradeSpotRate( 7.2346 );
            registeredTestCue.setTradeFwdPoints( 0.23456 );
            registeredTestCue.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            registeredTestCue.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            registeredTestCue.setFxRateBasis( ( FXRateBasis ) uow.readObject( FXRateBasis.class ) );
            registeredTestCue.setFxRateConvention( ( FXRateConvention ) uow.readObject( FXRateConvention.class ) );
            registeredTestCue.setTradePrincipalAmount( 2000.0 );
            registeredTestCue.setTradePriceAmount( 3000.00 );
            registeredTestCue.setTenorCoefficient( 50.00 );
            registeredTestCue.setSpacesCreditUtilizationKey("TestSpaceCUKey");
            registeredTestCue.setPrincipalBalance (  new BigDecimal (100.00 ) );
            registeredTestCue.setPriceBalance ( new BigDecimal( 200.00 ) );
            registeredTestCue.setUser ( ( User ) uow.readObject( User.class )  );
            registeredTestCue.setNotes ( "testNote" );
            registeredTestCue.setTicket ( "testTicketId" );
            registeredTestCue.setOrderId ( "testOrderId" );
            registeredTestCue.setExternalTransactionId ( "testExtTid" );
            User user = registeredTestCue.getUser ();

            registeredTestCue.setTradeLegName( "singleLeg" );
            uow.commit();

            testCue.setTradeDate( null );
            testCue.setSettlementDate( null );

            // refresh the trade and verify the fields
            testCue = ( CreditUtilizationEvent ) session.refreshObject( testCue );
            assertEquals( "Orig principal amt should not be null.", testCue.getOriginalPrincipalAmount(), origPrincipalAmt );
            assertEquals( "Orig price amt should not be null.", testCue.getOriginalPriceAmount(), origPriceAmt );
            //assertEquals( "Orig trade principal amt should not be null.", testCue.getOriginalTradePrincipalAmount(), origPrincipalAmt );
            //assertEquals( "Orig trade price amt should not be null.", testCue.getOriginalTradePriceAmount(), origPriceAmt );
            assertNotNull( "TradingParty should not be null.", testCue.getTradingParty() );
            assertNotNull( "LegalEntity should not be null.", testCue.getLegalEntity() );
            assertNotNull( "TradingPartyOrganization should not be null.", testCue.getTradingPartyOrganization() );
            assertEquals( "Principal amt should not be null.", testCue.getPrincipal(), 1000d );
            assertEquals( "Price amt should not be null.", testCue.getPrice(), 1000d );
            assertNotNull( "TradeDate should not be null.", testCue.getTradeDate() );
            assertNotNull( "SettlementDate should not be null.", testCue.getSettlementDate() );
            assertNotNull( "TradingPartyOrganization should not be null.", testCue.getTradingPartyOrganization() );
            assertNotNull( "TradeLeg should not be null.", testCue.getTradeLegName() );
            assertEquals( "TradeRate should not be null.", testCue.getTradeRate(), 7.2345d );
            assertEquals( "TradeSpotRate should not be null.", testCue.getTradeSpotRate(), 7.2346d );
            assertEquals( "TradeFwdPoints should not be null.", testCue.getTradeFwdPoints(), 0.23456 );
            assertNotNull( "BaseCurrency should not be null.", testCue.getBaseCurrency() );
            assertNotNull( "VariableCurrency should not be null.", testCue.getVariableCurrency() );
            assertNotNull( "FXRateBasis should not be null.", testCue.getFxRateBasis() );
            assertEquals( "Trade principal amount should be 2000.", testCue.getTradePrincipalAmount(), 2000.00 );
            assertEquals( "Trade price amount should be 3000.", testCue.getTradePriceAmount(), 3000.00 );
            assertEquals( "Tenor Coefficient should be 2000.", testCue.getTenorCoefficient(), 50.00 );
            assertEquals( "SpacesCreditUtilizationKey should be TestSpaceCUKey.", testCue.getSpacesCreditUtilizationKey(), "TestSpaceCUKey" );
            assertEquals( "principal balance should be.", 100.00 , testCue.getPrincipalBalance ().doubleValue () );
            assertEquals( "principal balance should be.", 200.00 , testCue.getPriceBalance ().doubleValue () );
            assertEquals ( "user should be", user.getFullName (), testCue.getUser ().getFullName () );
            assertEquals ( "Notes should be", "testNote", testCue.getNotes () );
            assertEquals ( "Ticket should be", "testTicketId", testCue.getTicket () );
            assertEquals ( "order id should be", "testOrderId", testCue.getOrderId () );
            assertEquals ( "external transaction id should be", "testExtTid", testCue.getExternalTransactionId () );
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence : ", e );
        }
    }
}
