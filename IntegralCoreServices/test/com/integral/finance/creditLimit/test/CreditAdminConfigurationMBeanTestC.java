package com.integral.finance.creditLimit.test;

import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfiguration;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationMBean;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

/**
 * <AUTHOR> Development Corporation.
 */
public class CreditAdminConfigurationMBeanTestC extends MBeanTestCaseC
{
    CreditAdminConfiguration creditAdminConfig = ( CreditAdminConfiguration ) CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean();

    public CreditAdminConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( creditAdminConfig, "creditAdminRegenerationEnabled", CreditAdminConfigurationMBean.REGENERATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditAdminConfig, "createUtilizationOrgType", CreditAdminConfigurationMBean.CREATE_UTILIZATION_ORG_TYPE, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "FICreditUtilizationPeriod", CreditAdminConfigurationMBean.FI_UTILIZATION_PERIOD, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "removeCreditOrgType", CreditAdminConfigurationMBean.REMOVE_UTILIZATION_ORG_TYPE, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "creditAdminRegenerationEmailFromAddress", CreditAdminConfigurationMBean.CREDIT_ADMIN_REGENERATION_EMAIL_FROM_ADDRESS, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "creditAdminRegenerationEmailToAddresses", CreditAdminConfigurationMBean.CREDIT_ADMIN_REGENERATION_EMAIL_TO_ADDRESSES, MBeanTestCaseC.COLLECTION_STRING );
        testProperty( creditAdminConfig, "creditRegenerationStartEmailSubject", CreditAdminConfigurationMBean.CREDIT_EMAIL_REGENERATION_START_SUBJECT, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "creditRegenerationStartEmailContent", CreditAdminConfigurationMBean.CREDIT_EMAIL_REGENERATION_START_CONTENT, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "creditRegenerationEndEmailSubject", CreditAdminConfigurationMBean.CREDIT_EMAIL_REGENERATION_END_SUBJECT, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "creditRegenerationEndEmailContent", CreditAdminConfigurationMBean.CREDIT_EMAIL_REGENERATION_END_CONTENT, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "creditRegenerationInitialSleep", CreditAdminConfigurationMBean.CREDIT_ADMIN_REGENERATION_SLEEP_PERIOD, MBeanTestCaseC.LONG );
        testProperty( creditAdminConfig, "creditAdminDailyExposureHorizon", CreditAdminConfigurationMBean.CREDIT_ADMIN_DAILY_EXPOSURE_HORIZON, MBeanTestCaseC.INTEGER );
        testProperty( creditAdminConfig, "creditAdminDailyHorizon", CreditAdminConfigurationMBean.CREDIT_ADMIN_DAILY_HORIZON, MBeanTestCaseC.INTEGER );
        testProperty( creditAdminConfig, "endOfDayAggregateCreditUtilizationRevaluationEnabled", CreditAdminConfigurationMBean.END_OF_DAY_AGGREGATE_CREDIT_UTILIZATION_REVALUATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditAdminConfig, "periodicRealtimeCreditUtilizationCacheRevaluationEnabled", CreditAdminConfigurationMBean.PERIODIC_REALTIME_CREDIT_UTILIZATION_CACHE_REVALUATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditAdminConfig, "realtimeCreditUtilizationCacheRevaluationPeriod", CreditAdminConfigurationMBean.REALTIME_CREDIT_UTILIZATION_CACHE_REVALUATION_PERIOD, MBeanTestCaseC.LONG );
        testProperty( creditAdminConfig, "excludedCreditOperationOrganizationList", CreditAdminConfigurationMBean.EXCLUDE_CREDIT_OPERATIONS_ORGANIZATION_LIST, MBeanTestCaseC.COLLECTION_STRING );
        testProperty( creditAdminConfig, "creditInitializeEnabled", CreditAdminConfigurationMBean.CREDIT_INITIALIZE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditAdminConfig, "creditRemoteNotificationProcessingEnabled", CreditAdminConfigurationMBean.CREDIT_REMOTE_NOTIFCATION_PROCESSING_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditAdminConfig, "asyncCreditFunctorInvocationEnabled", CreditAdminConfigurationMBean.CREDIT_ASYNC_FUNCTOR_INVOCATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditAdminConfig, "creditEndOfDayPLUpdateUser", CreditAdminConfigurationMBean.CREDIT_ENDOFDAY_PL_UPDATE_USER, MBeanTestCaseC.STRING );
        testProperty( creditAdminConfig, "creditUtilizationCacheEntryUpdateStalePeriod", CreditAdminConfigurationMBean.CREDIT_UTILIZATION_CACHE_ENTRY_UPDATE_STALE_PERIOD, MBeanTestCaseC.LONG );
        testProperty( creditAdminConfig, "creditUtilizationEventRevaluationEnabled", CreditAdminConfigurationMBean.CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditAdminConfig, "endOfDayCreditUtilizationEventRevaluationEnabled", CreditAdminConfigurationMBean.END_OF_DAY_CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditAdminConfig, "creditUtilizationStopOutRevaluationPeriod", CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_PERIOD, MBeanTestCaseC.LONG );
        testProperty( creditAdminConfig, "creditUtilizationStopOutRevaluationEnabled", CreditAdminConfigurationMBean.CREDIT_UTILIZATION_STOPOUT_REVALUATION_ENABLED, MBeanTestCaseC.BOOLEAN );
    }

    public void testFICreditUtilizationPeriod()
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertEquals( creditAdminConfig.getFICreditUtilizationPeriod( org1 ), "13M" );
            assertEquals( creditAdminConfig.getFICreditUtilizationPeriod( org2 ), "13M" );

            // set the order match delay for org1
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.FI_UTILIZATION_PERIOD_PREFIX + org1.getShortName(), "3Y", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals( creditAdminConfig.getFICreditUtilizationPeriod( org1 ), "3Y" );
            assertEquals( creditAdminConfig.getFICreditUtilizationPeriod( org2 ), "13M" );

            // now set 100 for org2
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.FI_UTILIZATION_PERIOD_PREFIX + org2.getShortName(), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals( creditAdminConfig.getFICreditUtilizationPeriod( org1 ), "3Y" );
            assertEquals( creditAdminConfig.getFICreditUtilizationPeriod( org2 ), "13M" );
        }
        catch ( Exception e )
        {
            fail( "testFICreditUtilizationPeriod" );
            e.printStackTrace();
        }
    }

    public void testEndOfDayCreditUtilizationEventRevaluationEnabled()
    {
        try
        {
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org2 ) );

            // now set the global property
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org1 ) );
            assertTrue( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org2 ) );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org2 ) );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( null ) );


            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org2 ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org2 ) );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED_PREFIX + "FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_CREDIT_UTILIZATION_EVENT_REVALUATION_ENABLED_PREFIX + "FI2", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayCreditUtilizationEventRevaluationEnabled( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testEndOfDayCreditUtilizationEventRevaluationEnabled" );
        }
    }

    public void testEndOfDayRevaluationBreachAlertEnabled()
    {
        try
        {
            assertFalse ( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertTrue( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org1 ) );
            assertTrue( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org2 ) );

            // now set the global property
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_REVALUATION_BREACH_ALERT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org1 ) );
            assertTrue( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org2 ) );
            assertFalse ( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_REVALUATION_BREACH_ALERT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org2 ) );
            assertFalse( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( null ) );


            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_REVALUATION_BREACH_ALERT_ENABLED_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_REVALUATION_BREACH_ALERT_ENABLED_PREFIX + org2.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org2 ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_REVALUATION_BREACH_ALERT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org2 ) );
            assertFalse( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_REVALUATION_BREACH_ALERT_ENABLED_PREFIX + org1.getShortName (), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.END_OF_DAY_REVALUATION_BREACH_ALERT_ENABLED_PREFIX + org2.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org1 ) );
            assertFalse( creditAdminConfig.isEndOfDayRevaluationBreachAlertEnabled( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testEndOfDayRevaluationBreachAlertEnabled" );
        }
    }

    public void testUnchangedLimitUpdateAuditSuppressEnabled()
    {
        try
        {
            assertFalse ( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org1 ) );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org2 ) );

            // now set the global property
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_UNCHANGED_LIMIT_UPDATE_AUDIT_SUPPRESS_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org1 ) );
            assertTrue( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org2 ) );
            assertFalse ( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_UNCHANGED_LIMIT_UPDATE_AUDIT_SUPPRESS_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org1 ) );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org2 ) );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( null ) );


            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_UNCHANGED_LIMIT_UPDATE_AUDIT_SUPPRESS_ENABLED_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_UNCHANGED_LIMIT_UPDATE_AUDIT_SUPPRESS_ENABLED_PREFIX + org2.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org1 ) );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org2 ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_UNCHANGED_LIMIT_UPDATE_AUDIT_SUPPRESS_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org1 ) );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org2 ) );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_UNCHANGED_LIMIT_UPDATE_AUDIT_SUPPRESS_ENABLED_PREFIX + org1.getShortName (), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_UNCHANGED_LIMIT_UPDATE_AUDIT_SUPPRESS_ENABLED_PREFIX + org2.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org1 ) );
            assertFalse( creditAdminConfig.isUnchangedLimitUpdateAuditSuppressEnabled( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testUnchangedLimitUpdateAuditSuppressEnabled" );
        }
    }

    public void testAggregateCashNetSettlementEnabled()
    {
        try
        {
            assertFalse ( creditAdminConfig.isAggregateNetCashSettlementEnabled( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( org2 ) );

            // now set the global property
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_CASH_SETTLEMENT_METHODOLOGY_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateNetCashSettlementEnabled( org1 ) );
            assertTrue( creditAdminConfig.isAggregateNetCashSettlementEnabled( org2 ) );
            assertFalse ( creditAdminConfig.isAggregateNetCashSettlementEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_CASH_SETTLEMENT_METHODOLOGY_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( org2 ) );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( null ) );


            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_CASH_SETTLEMENT_METHODOLOGY_ENABLED_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_CASH_SETTLEMENT_METHODOLOGY_ENABLED_PREFIX + org2.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateNetCashSettlementEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( org2 ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_CASH_SETTLEMENT_METHODOLOGY_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateNetCashSettlementEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( org2 ) );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_CASH_SETTLEMENT_METHODOLOGY_ENABLED_PREFIX + org1.getShortName (), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_CASH_SETTLEMENT_METHODOLOGY_ENABLED_PREFIX + org2.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetCashSettlementEnabled( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAggregateCashNetSettlementEnabled" );
        }
    }

    public void testAggregateCashSettlementReceivableMethodologyEnabled()
    {
        try
        {
            assertFalse ( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org2 ) );

            // now set the global property
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_SETTLEMENT_RECEIVABLE_METHODOLOGY_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org1 ) );
            assertTrue( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org2 ) );
            assertFalse ( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_SETTLEMENT_RECEIVABLE_METHODOLOGY_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org2 ) );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( null ) );


            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_SETTLEMENT_RECEIVABLE_METHODOLOGY_ENABLED_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_SETTLEMENT_RECEIVABLE_METHODOLOGY_ENABLED_PREFIX + org2.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org2 ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_SETTLEMENT_RECEIVABLE_METHODOLOGY_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org2 ) );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_SETTLEMENT_RECEIVABLE_METHODOLOGY_ENABLED_PREFIX + org1.getShortName (), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_NET_SETTLEMENT_RECEIVABLE_METHODOLOGY_ENABLED_PREFIX + org2.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateNetSettlementReceivableMethodologyEnabled( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAggregateCashSettlementReceivableMethodologyEnabled" );
        }
    }

    public void testCreditAdminReadOnlyPermissionCheckEnabled()
    {
        try
        {
            assertFalse ( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org1 ) );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org2 ) );

            // now set the global property
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_READONLY_PERMISSION_CHECK_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org1 ) );
            assertTrue( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org2 ) );
            assertFalse ( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_READONLY_PERMISSION_CHECK_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org1 ) );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org2 ) );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( null ) );


            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_READONLY_PERMISSION_CHECK_ENABLED_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_READONLY_PERMISSION_CHECK_ENABLED_PREFIX + org2.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org1 ) );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org2 ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_READONLY_PERMISSION_CHECK_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org1 ) );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org2 ) );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_READONLY_PERMISSION_CHECK_ENABLED_PREFIX + org1.getShortName (), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_READONLY_PERMISSION_CHECK_ENABLED_PREFIX + org2.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org1 ) );
            assertFalse( creditAdminConfig.isCreditAdminReadOnlyPermissionCheckEnabled( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCreditAdminReadOnlyPermissionCheckEnabled" );
        }
    }

    public void testAggregateMultiFactorSettlementMethodologyEnabled()
    {
        try
        {
            assertFalse ( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org2 ) );

            // now set the global property
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_MULTIFACTOR_SETTLEMENT_METHODOLOGY_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org1 ) );
            assertTrue( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org2 ) );
            assertFalse ( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_MULTIFACTOR_SETTLEMENT_METHODOLOGY_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org2 ) );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( null ) );


            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_MULTIFACTOR_SETTLEMENT_METHODOLOGY_ENABLED_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_MULTIFACTOR_SETTLEMENT_METHODOLOGY_ENABLED_PREFIX + org2.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org2 ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_MULTIFACTOR_SETTLEMENT_METHODOLOGY_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org2 ) );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( null ) );

            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_MULTIFACTOR_SETTLEMENT_METHODOLOGY_ENABLED_PREFIX + org1.getShortName (), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditAdminConfig.setProperty( CreditAdminConfigurationMBean.CREDIT_ADMIN_AGGREGATE_MULTIFACTOR_SETTLEMENT_METHODOLOGY_ENABLED_PREFIX + org2.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org1 ) );
            assertFalse( creditAdminConfig.isAggregateMultiFactorSettlementMethodologyEnabled( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAggregateMultiFactorSettlementMethodologyEnabled" );
        }
    }

    public void testExternalCreditProviderNewFlowEnabled() {
        try {
            assertTrue(creditAdminConfig.isExternalCreditProviderNewFlowEnabled(null));
            Organization org1 = (Organization) new ReadNamedEntityC().execute(OrganizationC.class, "FI1");
            assertTrue(creditAdminConfig.isExternalCreditProviderNewFlowEnabled(org1));

            // now set the global property
            creditAdminConfig.setProperty(CreditAdminConfigurationMBean.EXTERNAL_CREDIT_PROVIDER_NEW_FLOW_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertFalse(creditAdminConfig.isExternalCreditProviderNewFlowEnabled(org1));
            assertFalse(creditAdminConfig.isExternalCreditProviderNewFlowEnabled(null));

            creditAdminConfig.setProperty(CreditAdminConfigurationMBean.EXTERNAL_CREDIT_PROVIDER_NEW_FLOW_ENABLED_PREFIX + org1.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertTrue(creditAdminConfig.isExternalCreditProviderNewFlowEnabled(org1));
            assertFalse(creditAdminConfig.isExternalCreditProviderNewFlowEnabled(null));

        } catch (Exception e) {
            e.printStackTrace();
            fail("testExternalCreditProviderNewFlowEnabled");
        }
    }
}
