package com.integral.finance.creditLimit.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.handler.CreditUtilizationCacheSynchronizationHandlerC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.cache.TradeCacheC;
import com.integral.time.IdcDate;
import com.integral.util.IdcUtilC;
import com.integral.util.Tuple;
import com.integral.workflow.dealing.DealingLimit;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Tests the credit limit workflow when prime broker is involved.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAnonymousLPWorkflowPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit Anonymous LP Workflow  Test";

    public CreditLimitAnonymousLPWorkflowPTestC( String name )
    {
        super( name );
    }

    public void testTakerMakerCreditOrgForAnonymousWorkflow()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            AXLPOrg.setRealLP( XLPOrg );

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( DFI1Org, true );
            creditAdminSvc.setCreditEnabled( XLPOrg, true );
            creditAdminSvc.setCreditEnabled( XPBOrg, true );
            creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
            creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
            creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
            creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

            setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );


            // check the regular FI<=>LP
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( true );
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( DFI1Le, AXLPLe, spotDate, eur, usd, false );
            assertNotNull( dl );
            //assertEquals( dl.getTakerPBLegalEntity(), XPBLe );
            //assertEquals( dl.getMakerPBLegalEntity(), XPBLe );

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( AXLPLe, DFI1Le, spotDate, eur, usd, true );
            assertNotNull( dl1 );
            //assertEquals( dl1.getTakerPBLegalEntity(), XPBLe );
            //assertEquals( dl1.getMakerPBLegalEntity(), XPBLe );

            // check one sided pb.
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( false );
            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( DFI1Le, AXLPLe, spotDate, eur, usd, false );
            assertNull( dl2 );

            DealingLimit dl3 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( AXLPLe, DFI1Le, spotDate, eur, usd, true );
            assertNull( dl3 );

            // check other sided pb.
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( false );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( true );
            DealingLimit dl4 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( DFI1Le, AXLPLe, spotDate, eur, usd, false );
            assertNotNull( dl4 );
            //assertEquals( dl4.getTakerPBLegalEntity(), XPBLe );
            //assertEquals( dl4.getMakerPBLegalEntity(), XPBLe );

            DealingLimit dl5 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( AXLPLe, DFI1Le, spotDate, eur, usd, true );
            assertNotNull( dl5 );
            //assertEquals( dl5.getTakerPBLegalEntity(), XPBLe );
            //assertEquals( dl5.getMakerPBLegalEntity(), XPBLe );

            // have both sides pb
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( false );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( false );
            DealingLimit dl6 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( DFI1Le, AXLPLe, spotDate, eur, usd, false );
            assertNull( dl6 );

            DealingLimit dl7 = CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( AXLPLe, DFI1Le, spotDate, eur, usd, true );
            assertNull( dl7 );
        }
        catch ( Exception e )
        {
            fail( "testTakerMakerCreditOrgForAnonymousWorkflow", e );
        }
        finally
        {
            IdcUtilC.refreshObject( XLPOrg );
            IdcUtilC.refreshObject( XPBOrg );
            IdcUtilC.refreshObject( DFI1Org );
            IdcUtilC.refreshObject( DFI1TpForAXLP );
            IdcUtilC.refreshObject( AXLPTpForDFI1 );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUndoMaskLP()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            AXLPOrg.setRealLP( XLPOrg );
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( DFI1Le, AXLPLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( DFI1Le, AXLPLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUndoMaskLP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            IdcUtilC.refreshObject( XLPOrg );
            IdcUtilC.refreshObject( XPBOrg );
            IdcUtilC.refreshObject( DFI1Org );
            IdcUtilC.refreshObject( DFI1TpForAXLP );
            IdcUtilC.refreshObject( AXLPTpForDFI1 );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUpdateAmountMaskLP()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            AXLPOrg.setRealLP( XLPOrg );
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( DFI1Le, AXLPLe, trade1 , CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS);
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now update credit.
                    FXPaymentParameters fxPmt = ( ( FXSingleLeg ) trade1 ).getFXLeg().getFXPayment();
                    fxPmt.setCurrency1Amount( fxPmt.getCurrency1Amount() / 2.0 );
                    fxPmt.setCurrency2Amount( fxPmt.getCurrency2Amount() / 2.0 );

                    Trade pbCoverTrade = prepareSingleLegTrade( fxPmt.getCurrency1Amount() / 2.0, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade.setTransactionID( trade1.getTransactionID() + 'C' );
                    trade1.setCoverTradeTxIds( pbCoverTrade.getTransactionID() );
                    TradeCacheC.getInstance().add( trade1 );
                    TradeCacheC.getInstance().add( pbCoverTrade );
                    FXPaymentParameters fxPmt1 = ( ( FXSingleLeg ) pbCoverTrade ).getFXLeg().getFXPayment();
                    fxPmt1.setCurrency1Amount( fxPmt.getCurrency1Amount() );
                    fxPmt1.setCurrency2Amount( fxPmt.getCurrency2Amount() );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount( AXLPLe, DFI1Le, trade1, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUpdateAmountMaskLP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeAmendCptyMaskLP()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            AXLPOrg.setRealLP( XLPOrg );
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( AXLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( AXLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now book the trade in a different cpty
                    LegalEntity newLPLe = XPBLe;
                    trade1.setCounterpartyB( newLPLe );
                    CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( DFI1Le, newLPLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg2 = ( CreditWorkflowMessage ) cwm2.getReplyMessage();
                    String eventSnapshot2 = ( String ) replyMsg2.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event2 = replyMsg2.getEventName();
                    IdcDate tradeDate2 = replyMsg2.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event2, eventSnapshot2, trade1.getTransactionID(), tradeDate2 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeAmendCptyMaskLP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillMaskLP()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            AXLPOrg.setRealLP( XLPOrg );
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( AXLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 500;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    fillTrade1.setTransactionID( "FXI" + System.nanoTime() );
                    fillTrade2.setTransactionID( "FXI" + System.nanoTime() );

                    Trade pbCoverTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade1.setTransactionID( fillTrade1.getTransactionID() + 'C' );
                    fillTrade1.setCoverTradeTxIds( pbCoverTrade1.getTransactionID() );
                    TradeCacheC.getInstance().add( fillTrade1 );
                    TradeCacheC.getInstance().add( pbCoverTrade1 );

                    Trade pbCoverTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade2.setTransactionID( fillTrade2.getTransactionID() + 'C' );
                    fillTrade2.setCoverTradeTxIds( pbCoverTrade2.getTransactionID() );
                    TradeCacheC.getInstance().add( fillTrade2 );
                    TradeCacheC.getInstance().add( pbCoverTrade2 );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( AXLPLe, DFI1Le, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillMaskLP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillMaskLPWithSameTrade()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            AXLPOrg.setRealLP( XLPOrg );
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( AXLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades

                    Trade pbCoverTrade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade1.setTransactionID( trade1.getTransactionID() + 'C' );
                    trade1.setCoverTradeTxIds( pbCoverTrade1.getTransactionID() );
                    TradeCacheC.getInstance().add( trade1 );
                    TradeCacheC.getInstance().add( pbCoverTrade1 );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( trade1 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( AXLPLe, DFI1Le, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillMaskLPWithSameTrade", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFillMaskLP()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            AXLPOrg.setRealLP( XLPOrg );
            AXLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForAXLP.setPrimeBrokerageCreditUsed( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( AXLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 100;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, AXLPOrg, AXLPTpForDFI1, bidRates[0], spotDate );
                    fillTrade1.setTransactionID( "FXI" + System.nanoTime() );
                    fillTrade2.setTransactionID( "FXI" + System.nanoTime() );

                    Trade pbCoverTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade1.setTransactionID( fillTrade1.getTransactionID() + 'C' );
                    fillTrade1.setCoverTradeTxIds( pbCoverTrade1.getTransactionID() );
                    TradeCacheC.getInstance().add( fillTrade1 );
                    TradeCacheC.getInstance().add( pbCoverTrade1 );

                    Trade pbCoverTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade2.setTransactionID( fillTrade2.getTransactionID() + 'C' );
                    fillTrade2.setCoverTradeTxIds( pbCoverTrade2.getTransactionID() );
                    TradeCacheC.getInstance().add( fillTrade2 );
                    TradeCacheC.getInstance().add( pbCoverTrade2 );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( AXLPLe, DFI1Le, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFillMaskLP", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    protected void setUp() throws Exception
    {
        super.setUp ();
        initMaskLPCreditRelationship ();
    }

    protected static void initMaskLPCreditRelationship()
    {
        try
        {
            // set up pbLP and pbFI as credit providers.
            creditAdminSvc.setupCreditProvider( DFI1Org, staticMds );
            creditAdminSvc.setupCreditProvider( XPBOrg, staticMds );
            creditAdminSvc.setupCreditProvider( XLPOrg, staticMds );

            // establish credit relationship between FI and fi pb
            creditAdminSvc.establishCreditRelationship( DFI1Org, DFI1TpForXPB );
            creditAdminSvc.establishCreditRelationship( XPBOrg, XPBTpForDFI1 );
            creditAdminSvc.establishCreditRelationship( XLPOrg, XLPTpForXPB );
            creditAdminSvc.establishCreditRelationship( XPBOrg, XPBTpForXLP );

        }
        catch ( Exception e )
        {
            log.error( "CreditLimitServicePTestC.initMaskLPCreditRelationship.ERROR", e );
        }
    }
}
