package com.integral.finance.creditLimit.test;

// Copyright (c) 2013 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.configuration.CreditLimitConfiguration;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationMBean;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

/**
 * <AUTHOR> Development Corporation.
 */
public class CreditLimitConfigurationMBeanTestC extends MBeanTestCaseC
{
    CreditLimitConfiguration creditLimitConfig = ( CreditLimitConfiguration ) CreditLimitConfigurationFactory.getCreditConfigurationMBean();

    public CreditLimitConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( creditLimitConfig, "creditMetaSpaceQueryEnabled", CreditLimitConfigurationMBean.CREDIT_METASPACE_QUERY_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( creditLimitConfig, "multicastAddress", CreditLimitConfigurationMBean.CREDIT_MULTICAST_ADDRESS, MBeanTestCaseC.STRING );
        testProperty( creditLimitConfig, "multicastPort", CreditLimitConfigurationMBean.CREDIT_MULTICAST_PORT, MBeanTestCaseC.INTEGER );
        testProperty( creditLimitConfig, "multicastPublishPeriod", CreditLimitConfigurationMBean.CREDIT_MULTICAST_PUBLISH_PERIOD, MBeanTestCaseC.LONG );
    }

    public void testPrefixedProperties()
    {
        String org1 = "FI1";
        String org2 = "FI2";

        boolean creditUtilizationSpacesEnabledDefault = creditLimitConfig.isCreditUtilizationLookupSpacesEnabled(null);
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_LOOKUP_SPACES_ENABLED + ".FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationLookupSpacesEnabled( org1 ), true );
        assertEquals( creditLimitConfig.isCreditUtilizationLookupSpacesEnabled( org2 ), creditUtilizationSpacesEnabledDefault );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_LOOKUP_SPACES_ENABLED + ".FI2", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationLookupSpacesEnabled( org1 ), true );
        assertEquals( creditLimitConfig.isCreditUtilizationLookupSpacesEnabled( org2 ), true );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_LOOKUP_SPACES_ENABLED + ".FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_LOOKUP_SPACES_ENABLED + ".FI2", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationLookupSpacesEnabled( org1 ), creditUtilizationSpacesEnabledDefault );
        assertEquals( creditLimitConfig.isCreditUtilizationLookupSpacesEnabled( org2 ), creditUtilizationSpacesEnabledDefault );
        assertEquals( creditLimitConfig.isCreditUtilizationLookupSpacesEnabled(null), creditUtilizationSpacesEnabledDefault );

        boolean creditUtilizationPersistenceOracleEnabledDefault = creditLimitConfig.isCreditUtilizationPersistenceOracleEnabled( null );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_ORACLE_ENABLED + ".FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceOracleEnabled( org1 ), true );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceOracleEnabled( org2 ), creditUtilizationPersistenceOracleEnabledDefault );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_ORACLE_ENABLED + ".FI2", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceOracleEnabled( org1 ), true );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceOracleEnabled( org2 ), true );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_ORACLE_ENABLED + ".FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_ORACLE_ENABLED + ".FI2", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceOracleEnabled( org1 ), creditUtilizationPersistenceOracleEnabledDefault );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceOracleEnabled( org2 ), creditUtilizationPersistenceOracleEnabledDefault );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceOracleEnabled( null ), creditUtilizationPersistenceOracleEnabledDefault );

        boolean creditUtilizationPersistenceSpacesEnabledDefault = creditLimitConfig.isCreditUtilizationPersistenceSpacesEnabled(null);
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_SPACES_ENABLED + ".FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceSpacesEnabled( org1 ), true );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceSpacesEnabled( org2 ), creditUtilizationPersistenceSpacesEnabledDefault );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_SPACES_ENABLED + ".FI2", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceSpacesEnabled( org1 ), true );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceSpacesEnabled( org2 ), true );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_SPACES_ENABLED + ".FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_SPACES_ENABLED + ".FI2", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceSpacesEnabled( org1 ), creditUtilizationPersistenceSpacesEnabledDefault );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceSpacesEnabled( org2 ), creditUtilizationPersistenceSpacesEnabledDefault );
        assertEquals( creditLimitConfig.isCreditUtilizationPersistenceSpacesEnabled(null), creditUtilizationPersistenceSpacesEnabledDefault );
    }

    public void testTodayTradesWithZeroUtilizationAllowedOnBreach()
    {
        try
        {
            assertFalse( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(null));
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse(creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org1));
            assertFalse(creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org2));

            // now set the global property
            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org1) );
            assertTrue( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org2) );
            assertTrue( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach( null ) );

            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org1) );
            assertFalse( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org2) );
            assertFalse( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(null) );


            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org1) );
            assertFalse( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org2) );

            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_TODAY_TRADES_WITH_ZERO_UTILIZATION_ALLOWED_ON_BREACH_PREFIX, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org1) );
            assertFalse( creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(org2) );
            assertFalse(creditLimitConfig.isTodayTradesWithZeroUtilizationAllowedOnBreach(null));
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testTodayTradesWithZeroUtilizationAllowedOnBreach" );
        }
    }

    public void testCreditLineBasedNettingPortfolioCheckEnabled()
    {
        try
        {
            assertTrue( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled (null));
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertTrue(creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org1));
            assertTrue(creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org2));

            // now set the global property
            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_LINE_BASED_NETTING_PORTFOLIO_CHECK_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org1) );
            assertFalse( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org2) );
            assertFalse( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled( null ) );

            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_LINE_BASED_NETTING_PORTFOLIO_CHECK_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org1) );
            assertTrue( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org2) );
            assertTrue( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(null) );


            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_LINE_BASED_NETTING_PORTFOLIO_CHECK_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_LINE_BASED_NETTING_PORTFOLIO_CHECK_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org1) );
            assertFalse( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org2) );

            creditLimitConfig.setProperty( CreditLimitConfigurationMBean.CREDIT_LINE_BASED_NETTING_PORTFOLIO_CHECK_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org1) );
            assertFalse( creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(org2) );
            assertTrue(creditLimitConfig.isCreditLineBasedNettingPortfolioCheckEnabled(null));
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCreditLineBasedNettingPortfolioCheckEnabled" );
        }
    }

}
