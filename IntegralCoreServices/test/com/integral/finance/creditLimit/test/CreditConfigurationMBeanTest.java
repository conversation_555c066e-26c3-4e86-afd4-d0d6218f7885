package com.integral.finance.creditLimit.test;

// Copyright (c) 2022 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.configuration.CreditConfiguration;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.util.IdcUtilC;

import java.util.Collection;

/**
 * <AUTHOR> Development Corporation.
 */
public class CreditConfigurationMBeanTest extends MBeanTestCaseC
{
    private final CreditConfiguration creditConfig = ( CreditConfiguration ) CreditConfigurationFactory.getCreditConfigurationMBean ();

    public CreditConfigurationMBeanTest ( String aName )
    {
        super ( aName );
    }

    public void testProperties ( )
    {
        testProperty ( creditConfig, "creditEnabled", CreditConfigurationMBean.CREDIT_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditProviderEnabled", CreditConfigurationMBean.CREDIT_PROVIDER_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditUtilizationPersistencePeriod", CreditConfigurationMBean.CREDIT_UTILIZATION_PERSISTENCE_PERIOD, MBeanTestCaseC.LONG );
        testProperty ( creditConfig, "defaultNotificationPercentage", CreditConfigurationMBean.DEFAULT_NOTIFICATION_PERCENTAGE, MBeanTestCaseC.DOUBLE );
        testProperty ( creditConfig, "defaultWarningPercentage", CreditConfigurationMBean.DEFAULT_WARNING_PERCENTAGE, MBeanTestCaseC.DOUBLE );
        testProperty ( creditConfig, "defaultSuspensionPercentage", CreditConfigurationMBean.DEFAULT_SUSPENSION_PERCENTAGE, MBeanTestCaseC.DOUBLE );
        testProperty ( creditConfig, "creditNotificationSenderEmailAddress", CreditConfigurationMBean.CREDIT_EMAIL_SENDER, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "creditNotificationCCEmailAddress", CreditConfigurationMBean.CREDIT_EMAIL_CC, MBeanTestCaseC.COLLECTION_STRING );
        testProperty ( creditConfig, "creditNotificationEmailSubject", CreditConfigurationMBean.CREDIT_EMAIL_SUBJECT, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "creditNotificationEmailSubjectWithSettlementCode", CreditConfigurationMBean.CREDIT_EMAIL_SUBJECT_WITH_SETTLMENT_CODE, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "creditNotificationEmailContent", CreditConfigurationMBean.CREDIT_EMAIL_CONTENT, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "creditNotificationEmailContentWithSettlementCode", CreditConfigurationMBean.CREDIT_EMAIL_CONTENT_WITH_SETTLMENT_CODE, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "creditNotificationRuleEmailContent", CreditConfigurationMBean.CREDIT_EMAIL_CONTENT_RULE, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "creditRejectNotificationEmailSubject", CreditConfigurationMBean.CREDIT_EMAIL_REJECT_SUBJECT, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "creditRejectNotificationEmailSubjectWithSettlementCode", CreditConfigurationMBean.CREDIT_EMAIL_REJECT_SUBJECT_WITH_SETTLMENT_CODE, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "creditRejectNotificationEmailContent", CreditConfigurationMBean.CREDIT_EMAIL_REJECT_CONTENT, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "supportedCreditTypes", CreditConfigurationMBean.SUPPORTED_CREDIT_TYPES, MBeanTestCaseC.COLLECTION_STRING );
        testProperty ( creditConfig, "creditEmailPoolsize", CreditConfigurationMBean.CREDIT_EMAIL_POOLSIZE, MBeanTestCaseC.INTEGER );
        testProperty ( creditConfig, "defaultAggregateCreditUtilizationCalculator", CreditConfigurationMBean.DEFAULT_AGGREGATE_NETTING_CALCULATOR, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "defaultDailyCreditUtilizationCalculator", CreditConfigurationMBean.DEFAULT_DAILY_NETTING_CALCULATOR, MBeanTestCaseC.STRING );
        testProperty ( creditConfig, "defaultOrgLevelExposure", CreditConfigurationMBean.DEFAULT_ORG_LEVEL_CREDIT_EXPOSURE, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "allowExcessCreditUtilization", CreditConfigurationMBean.ALLOW_EXCESS_CREDIT_UTILIZATION, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditUtilizationPeriodicMemoryFetchEnabled", CreditConfigurationMBean.CREDIT_UTILIZATION_PERIODIC_MEMORY_FETCH_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditRecalculationEnabled", CreditConfigurationMBean.CREDIT_PERIODIC_RECALCULATIONS_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditWorkflowWarmupEnabled", CreditConfigurationMBean.CREDIT_WORKFLOW_WARMUP_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditEndOfDayCacheEntryStaleEnabled", CreditConfigurationMBean.CREDIT_ENDOFDAY_CACHE_ENTRY_STALE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditEndOfDayCacheEntryRebuildDelay", CreditConfigurationMBean.CREDIT_ENDOFDAY_CACHE_ENTRY_REBUILD_DELAY, MBeanTestCaseC.LONG );
        testProperty ( creditConfig, "customerOrgsCreditUtilizationFetchEnabled", CreditConfigurationMBean.CREDIT_CUSTOMERORGS_UTILIZATION_FETCH_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditCacheContainerRotationPeriod", CreditConfigurationMBean.CREDIT_CACHE_CONTAINER_ROTATION_PERIOD, MBeanTestCaseC.LONG );
        testProperty ( creditConfig, "eventCodesUsingSnapshotBasedPositions", CreditConfigurationMBean.CREDIT_EVENT_CODES_USING_SNAPSHOT_BASED_POSITIONS, MBeanTestCaseC.COLLECTION_INTEGER );
        testProperty ( creditConfig, "currencyPositionMismatchStalePeriod", CreditConfigurationMBean.CURRENCY_POSITION_MISMATCH_STALE_PERIOD, MBeanTestCaseC.LONG );
        testProperty ( creditConfig, "twoWayRFSWithOneSideOnlyAvailableEnabled", CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditEndOfDayPreCalculationEnabled", CreditConfigurationMBean.ENDOFDAY_PRECALCULATION_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "firstQuoteOnlyRFSCreditQualificationEnabled", CreditConfigurationMBean.CREDIT_CHECK_ONLY_FOR_FIRST_RFS_QUOTE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( creditConfig, "creditMarginCallServiceClassName", CreditConfigurationMBean.CREDIT_MARGIN_CALL_SERVICE_CLASS_NAME, MBeanTestCaseC.STRING );
    }

    public void testNotificationEmailAddressList ( )
    {
        try
        {
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_EMAIL_CC, "<EMAIL> ; <EMAIL> ; <EMAIL> ", ConfigurationProperty.DYNAMIC_SCOPE, null );
            Collection<String> emails = creditConfig.getCreditNotificationCCEmailAddress ();
            log ( "emails=" + emails );
            assertTrue ( emails.contains ( "<EMAIL>" ) );
            assertTrue ( emails.contains ( "<EMAIL>" ) );
            assertTrue ( emails.contains ( "<EMAIL>" ) );

            assertFalse ( emails.contains ( "<EMAIL> " ) );
            assertFalse ( emails.contains ( " <EMAIL> " ) );
            assertFalse ( emails.contains ( " <EMAIL>" ) );

        }
        catch ( Exception e )
        {
            fail ( "testNotificationEmailAddressList" );
            e.printStackTrace ();
        }
    }

    public void testSwapOptimizedCreditTakeEnabled ( )
    {
        try
        {
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( null ) );
            String org1 = "FI1";
            String org2 = "FI2";
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org1 ) );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org2 ) );

            // now set the global property
            creditConfig.setProperty ( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org1 ) );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org2 ) );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org1 ) );
            assertFalse ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org2 ) );
            assertFalse ( creditConfig.isSwapOptimizedCreditTakeEnabled ( null ) );


            creditConfig.setProperty ( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED_PREFIX + org1, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED_PREFIX + org2, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org1 ) );
            assertFalse ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org2 ) );

            creditConfig.setProperty ( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org1 ) );
            assertFalse ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org2 ) );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED_PREFIX + org1, null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED_PREFIX + org2, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org1 ) );
            assertTrue ( creditConfig.isSwapOptimizedCreditTakeEnabled ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSwapOptimizedCreditTakeEnabled" );
        }
    }

    public void testShowSettlementCode ( )
    {
        try
        {
            assertFalse ( creditConfig.isShowSettlementCode ( null ) );
            String org1 = "FI1";
            String org2 = "FI2";
            assertFalse ( creditConfig.isShowSettlementCode ( org1 ) );
            assertFalse ( creditConfig.isShowSettlementCode ( org2 ) );

            // now set the global property
            creditConfig.setProperty ( CreditConfigurationMBean.SHOW_SETTLEMENT_CODE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isShowSettlementCode ( org1 ) );
            assertTrue ( creditConfig.isShowSettlementCode ( org2 ) );
            assertTrue ( creditConfig.isShowSettlementCode ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.SHOW_SETTLEMENT_CODE, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( creditConfig.isShowSettlementCode ( org1 ) );
            assertFalse ( creditConfig.isShowSettlementCode ( org2 ) );
            assertFalse ( creditConfig.isShowSettlementCode ( null ) );


            creditConfig.setProperty ( CreditConfigurationMBean.SHOW_SETTLEMENT_CODE_PREFIX + org1, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.SHOW_SETTLEMENT_CODE_PREFIX + org2, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isShowSettlementCode ( org1 ) );
            assertFalse ( creditConfig.isShowSettlementCode ( org2 ) );

            creditConfig.setProperty ( CreditConfigurationMBean.SHOW_SETTLEMENT_CODE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isShowSettlementCode ( org1 ) );
            assertFalse ( creditConfig.isShowSettlementCode ( org2 ) );
            assertTrue ( creditConfig.isShowSettlementCode ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.SHOW_SETTLEMENT_CODE_PREFIX + org1, null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.SHOW_SETTLEMENT_CODE_PREFIX + org2, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isShowSettlementCode ( org1 ) );
            assertTrue ( creditConfig.isShowSettlementCode ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testShowSettlementCode" );
        }
    }

    public void testMultiPrimeNewLookupEnabled ( )
    {
        try
        {
            assertFalse ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( null ) );
            String org1 = "FI1";
            String org2 = "FI2";
            assertFalse ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org1 ) );
            assertFalse ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org2 ) );

            // now set the global property
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_MULTI_PRIME_NEW_CREDIT_LOOKUP_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org1 ) );
            assertTrue ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org2 ) );
            assertTrue ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_MULTI_PRIME_NEW_CREDIT_LOOKUP_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org1 ) );
            assertFalse ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org2 ) );
            assertFalse ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( null ) );


            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_MULTI_PRIME_NEW_CREDIT_LOOKUP_ENABLED_PREFIX + org1, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_MULTI_PRIME_NEW_CREDIT_LOOKUP_ENABLED_PREFIX + org2, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org1 ) );
            assertFalse ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org2 ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_MULTI_PRIME_NEW_CREDIT_LOOKUP_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org1 ) );
            assertFalse ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org2 ) );
            assertTrue ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_MULTI_PRIME_NEW_CREDIT_LOOKUP_ENABLED_PREFIX + org1, null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_MULTI_PRIME_NEW_CREDIT_LOOKUP_ENABLED_PREFIX + org2, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org1 ) );
            assertTrue ( creditConfig.isMultiPrimeNewCreditLookupEnabled ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMultiPrimeNewLookupEnabled" );
        }
    }

    public void testCreditPFEInterpolationEnabled ( )
    {
        try
        {
            assertFalse ( creditConfig.isCreditPFEInterpolationEnabled ( null ) );
            String org1 = "FI1";
            String org2 = "FI2";
            assertFalse ( creditConfig.isCreditPFEInterpolationEnabled ( org1 ) );
            assertFalse ( creditConfig.isCreditPFEInterpolationEnabled ( org2 ) );

            // now set the global property
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isCreditPFEInterpolationEnabled ( org1 ) );
            assertTrue ( creditConfig.isCreditPFEInterpolationEnabled ( org2 ) );
            assertTrue ( creditConfig.isCreditPFEInterpolationEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( creditConfig.isCreditPFEInterpolationEnabled ( org1 ) );
            assertFalse ( creditConfig.isCreditPFEInterpolationEnabled ( org2 ) );
            assertFalse ( creditConfig.isCreditPFEInterpolationEnabled ( null ) );


            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED_PREFIX + org1, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED_PREFIX + org2, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isCreditPFEInterpolationEnabled ( org1 ) );
            assertFalse ( creditConfig.isCreditPFEInterpolationEnabled ( org2 ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isCreditPFEInterpolationEnabled ( org1 ) );
            assertFalse ( creditConfig.isCreditPFEInterpolationEnabled ( org2 ) );
            assertTrue ( creditConfig.isCreditPFEInterpolationEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED_PREFIX + org1, null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_PFE_INTERPOLATION_ENABLED_PREFIX + org2, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isCreditPFEInterpolationEnabled ( org1 ) );
            assertTrue ( creditConfig.isCreditPFEInterpolationEnabled ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testCreditPFEInterpolationEnabled" );
        }
    }

    public void testCreditRetailModeEnabled ( )
    {
        try
        {
            assertFalse ( creditConfig.isCreditRetailModeEnabled ( null ) );
            String org1 = "FI1";
            String org2 = "FI2";
            assertFalse ( creditConfig.isCreditRetailModeEnabled ( org1 ) );
            assertFalse ( creditConfig.isCreditRetailModeEnabled ( org2 ) );

            // now set the global property
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isCreditRetailModeEnabled ( org1 ) );
            assertTrue ( creditConfig.isCreditRetailModeEnabled ( org2 ) );
            assertTrue ( creditConfig.isCreditRetailModeEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( creditConfig.isCreditRetailModeEnabled ( org1 ) );
            assertFalse ( creditConfig.isCreditRetailModeEnabled ( org2 ) );
            assertFalse ( creditConfig.isCreditRetailModeEnabled ( null ) );


            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED_PREFIX + org1, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED_PREFIX + org2, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isCreditRetailModeEnabled ( org1 ) );
            assertFalse ( creditConfig.isCreditRetailModeEnabled ( org2 ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isCreditRetailModeEnabled ( org1 ) );
            assertFalse ( creditConfig.isCreditRetailModeEnabled ( org2 ) );
            assertTrue ( creditConfig.isCreditRetailModeEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED_PREFIX + org1, null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_RETAIL_MODE_ENABLED_PREFIX + org2, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isCreditRetailModeEnabled ( org1 ) );
            assertTrue ( creditConfig.isCreditRetailModeEnabled ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testCreditRetailModeEnabled" );
        }
    }
    public void testOrderReserveWorkflowEnabled ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker1 );
        org2.setBrokerOrganization ( broker1 );
        try
        {
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( null ) );
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( org1 ) );
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( org2 ) );

            // now set the global property
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_ORDER_RESERVE_WORKFLOW_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isOrderReserveWorkflowEnabled ( org1 ) );
            assertTrue ( creditConfig.isOrderReserveWorkflowEnabled ( org2 ) );
            assertTrue ( creditConfig.isOrderReserveWorkflowEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_ORDER_RESERVE_WORKFLOW_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( org1 ) );
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( org2 ) );
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( null ) );

            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_ORDER_RESERVE_WORKFLOW_ENABLED_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isOrderReserveWorkflowEnabled ( org1 ) );
            assertTrue ( creditConfig.isOrderReserveWorkflowEnabled ( org2 ) );
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( null ) );

            creditConfig.removeProperty ( CreditConfigurationMBean.CREDIT_ORDER_RESERVE_WORKFLOW_ENABLED_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_ORDER_RESERVE_WORKFLOW_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            creditConfig.setProperty ( CreditConfigurationMBean.CREDIT_ORDER_RESERVE_WORKFLOW_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isOrderReserveWorkflowEnabled ( org1 ) );
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( org2 ) );

            creditConfig.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_SUPPORT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( creditConfig.isOrderReserveWorkflowEnabled ( org1 ) );
            assertFalse ( creditConfig.isOrderReserveWorkflowEnabled ( org2 ) );
            assertTrue ( creditConfig.isOrderReserveWorkflowEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testOrderReserveWorkflowEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker1 );
        }
    }
}
