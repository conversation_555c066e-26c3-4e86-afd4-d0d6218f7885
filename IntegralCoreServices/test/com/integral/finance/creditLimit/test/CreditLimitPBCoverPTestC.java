package com.integral.finance.creditLimit.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.handler.CreditUtilizationCacheSynchronizationHandlerC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.cache.TradeCacheC;
import com.integral.message.MessageStatus;
import com.integral.session.IdcTransaction;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.time.IdcDate;
import com.integral.util.IdcUtilC;
import com.integral.util.Tuple;
import com.integral.workflow.dealing.DealingLimit;
import com.integral.workflow.dealing.DealingLimitCollection;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import com.integral.workflow.dealing.fx.FXDealingLimitCollection;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

/**
 * Tests the credit limit admin audit events storing and display.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitPBCoverPTestC extends CreditLimitServicePTestC
{
    static String name = "Credit Limit PB Cover Test";

    public CreditLimitPBCoverPTestC( String name )
    {
        super( name );
    }

    public void testCreditRelationsForMaskedPB()
    {
        initMaskedOrg();
        init( fiUser );
        removeExistingCreditUtilizationEvents( takerOrg );
        removeExistingCreditUtilizationEvents( makerOrg );
        removeExistingCreditUtilizationEvents( takerPbOrg );
        removeExistingCreditUtilizationEvents( makerPbOrg );

        // enable the credit for all orgs and credit cpty
        creditAdminSvc.setCreditEnabled( takerOrg, true );
        creditAdminSvc.setCreditEnabled( makerOrg, true );
        creditAdminSvc.setCreditEnabled( takerPbOrg, true );
        creditAdminSvc.setCreditEnabled( makerPbOrg, true );
        creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
        creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
        creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
        creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
        creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
        creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
        creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
        creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
        creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );
        creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTakerPb, true );

        //Scenario 1: Masked LP set for both sides enabled
        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        TradingParty registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
        registeredTakerTpForLp.setPrimeBrokerageCreditUsed( true );
        TradingParty registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
        registeredLpTpForTaker.setPrimeBrokerageCreditUsed( true );
        uow.commit();
        ArrayList<CreditEntity> creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, lpLe );
        assertEquals( creditRelations.size(), 6 );
        assertEquals( creditRelations.get( 0 ).getLegalEntity(), takerLe );
        assertEquals( creditRelations.get( 0 ).getTradingParty(), takerTpForTakerPb );
        assertEquals( creditRelations.get( 1 ).getLegalEntity(), takerPbLe );
        assertEquals( creditRelations.get( 1 ).getTradingParty(), takerPbTpForTaker );
        assertEquals( creditRelations.get( 2 ).getLegalEntity(), makerLe );
        assertEquals( creditRelations.get( 2 ).getTradingParty(), makerTpForMakerPb );
        assertEquals( creditRelations.get( 3 ).getLegalEntity(), makerPbLe );
        assertEquals( creditRelations.get( 3 ).getTradingParty(), makerPbTpForMaker );
        assertEquals( creditRelations.get( 4 ).getLegalEntity(), takerPbLe );
        assertEquals( creditRelations.get( 4 ).getTradingParty(), takerPbTpForMakerPb );
        assertEquals( creditRelations.get( 5 ).getLegalEntity(), makerPbLe );
        assertEquals( creditRelations.get( 5 ).getTradingParty(), makerPbTpForTakerPb );

        //scenario 2: Masked LP with PB enabled for org1 side only
        uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
        registeredTakerTpForLp.setPrimeBrokerageCreditUsed( true );
        registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
        registeredLpTpForTaker.setPrimeBrokerageCreditUsed( false );
        uow.commit();
        creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, lpLe );
        assertEquals( creditRelations.size(), 4 );
        assertEquals( creditRelations.get( 0 ).getLegalEntity(), takerLe );
        assertEquals( creditRelations.get( 0 ).getTradingParty(), takerTpForTakerPb );
        assertEquals( creditRelations.get( 1 ).getLegalEntity(), takerPbLe );
        assertEquals( creditRelations.get( 1 ).getTradingParty(), takerPbTpForMaker );
        assertEquals( creditRelations.get( 2 ).getLegalEntity(), takerPbLe );
        assertEquals( creditRelations.get( 2 ).getTradingParty(), takerPbTpForTaker );
        assertEquals( creditRelations.get( 3 ).getLegalEntity(), makerLe );
        assertEquals( creditRelations.get( 3 ).getTradingParty(), makerTpForTakerPb );


        //scenario 3: Masked LP with PB enabled for org2 side only
        uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
        registeredTakerTpForLp.setPrimeBrokerageCreditUsed( false );
        registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
        registeredLpTpForTaker.setPrimeBrokerageCreditUsed( true );
        uow.commit();
        creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, lpLe );
        assertEquals( creditRelations.size(), 4 );
        assertEquals( creditRelations.get( 0 ).getLegalEntity(), makerLe );
        assertEquals( creditRelations.get( 0 ).getTradingParty(), makerTpForMakerPb );
        assertEquals( creditRelations.get( 1 ).getLegalEntity(), makerPbLe );
        assertEquals( creditRelations.get( 1 ).getTradingParty(), makerPbTpForTaker );
        assertEquals( creditRelations.get( 2 ).getLegalEntity(), makerPbLe );
        assertEquals( creditRelations.get( 2 ).getTradingParty(), makerPbTpForMaker );
        assertEquals( creditRelations.get( 3 ).getLegalEntity(), takerLe );
        assertEquals( creditRelations.get( 3 ).getTradingParty(), takerTpForMakerPb );

        //scenario 4: Masked LP with both PB disabled
        uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( TradingPartyC.class );
        registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
        registeredTakerTpForLp.setPrimeBrokerageCreditUsed( false );
        registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
        registeredLpTpForTaker.setPrimeBrokerageCreditUsed( false );
        uow.commit();
        creditRelations = ( ArrayList<CreditEntity> ) CreditLimitSubscriptionManagerC.getInstance().getCreditRelationsBetween2Les( takerLe, lpLe );
        log( "creditRelations=" + creditRelations );
        assertEquals( 2, creditRelations.size() );
    }

    public void testMaskedLPTakeCreditWithBothPBEnabled()
    {
        try
        {
            initMaskedOrg();
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTakerPb, true );

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
            registeredTakerTpForLp.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
            registeredLpTpForTaker.setPrimeBrokerageCreditUsed( true );
            uow.commit();


            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, "USD/CHF", lpOrg, lpTpForFi, bidRates[8], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            sleepFor( 2000 );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            sleepFor( 2000 );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testMaskedLPTakeCreditWithTakerPBOnlyEnabled()
    {
        try
        {
            initMaskedOrg();
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerOrg, makerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( makerOrg, makerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerOrg, takerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
            registeredTakerTpForLp.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
            registeredLpTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();


            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore10=" + aggregateLimitBefore10 + ",aggregateLimitAfter10=" + aggregateLimitAfter10 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore10 - aggregateLimitAfter10 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore11=" + aggregateLimitBefore11 + ",aggregateLimitAfter11=" + aggregateLimitAfter11 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore11 - aggregateLimitAfter11 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore10=" + dailyLimitBefore10 + ",dailyLimitAfter10=" + dailyLimitAfter10 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore10 - dailyLimitAfter10 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore11=" + dailyLimitBefore11 + ",dailyLimitAfter11=" + dailyLimitAfter11 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore11 - dailyLimitAfter11 ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "Message status after undo credit=" + undoCwm.getStatus() );
            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter10=" + aggregateLimitAfter10 + ",aggregateLimitAfterUndo10=" + aggregateLimitAfterUndo10 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore10 - aggregateLimitAfterUndo10 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter11=" + aggregateLimitAfter11 + ",aggregateLimitAfterUndo11=" + aggregateLimitAfterUndo11 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore11 - aggregateLimitAfterUndo11 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter10=" + dailyLimitAfter10 + ",dailyLimitAfterUndo10=" + dailyLimitAfterUndo10 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore10 - dailyLimitAfterUndo10 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter11=" + dailyLimitAfter11 + ",dailyLimitAfterUndo11=" + dailyLimitAfterUndo11 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore11 - dailyLimitAfterUndo11 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testMultiFillCreditUpdate()
    {
        try
        {
            initMaskedOrg();
            init( makerUser );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
            registeredTakerTpForLp.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
            registeredLpTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            double tradeAmt = 1000;
            Trade originalTrade = prepareSingleLegTrade( tradeAmt, true, true, "USD/CHF", lpOrg, lpTpForFi, bidRates[8], spotDate );

            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, originalTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            // create fill trades
            double fillAmt1 = 200;
            double fillAmt2 = 300;
            // now update the counterpartyB of original trade.
            updateTradeAmount( originalTrade, fillAmt1, true, true );
            originalTrade.setCounterpartyB( takerPbLe );

            double pbTradeAmt1 = 250;
            double pbTradeAmt2 = 350;

            Trade pbOigTrade = prepareSingleLegTrade( pbTradeAmt1, true, true, "USD/CHF", takerPbOrg, takerPbTpForMaker, bidRates[8], spotDate );
            pbOigTrade.setTransactionID( originalTrade.getTransactionID() + 'C' );
            originalTrade.setCoverTradeTxIds( pbOigTrade.getTransactionID() );
            TradeCacheC.getInstance().add( pbOigTrade );
            TradeCacheC.getInstance().add( originalTrade );

            Trade fillTrade1 = prepareSingleLegTrade( fillAmt2, true, true, "USD/CHF", takerPbOrg, takerPbTpForTaker, bidRates[8], spotDate );
            Trade fillPBCoverTrade1 = prepareSingleLegTrade( pbTradeAmt2, true, true, "USD/CHF", takerPbOrg, takerPbTpForMaker, bidRates[8], spotDate );
            fillPBCoverTrade1.setTransactionID( fillTrade1.getTransactionID() + 'C' );
            fillTrade1.setCoverTradeTxIds( fillPBCoverTrade1.getTransactionID() );
            TradeCacheC.getInstance().add( fillTrade1 );
            TradeCacheC.getInstance().add( fillPBCoverTrade1 );

            Collection<Trade> fillTradeColl = new ArrayList<Trade>();
            // add original trade also to the collection
            fillTradeColl.add( originalTrade );
            fillTradeColl.add( fillTrade1 );

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit( lpLe, fiLe, originalTrade, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfterMultiFill0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterMultiFill1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterMultiFill2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterMultiFill3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterMultiFill0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterMultiFill1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterMultiFill2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterMultiFill3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfterMultiFill0=" + aggregateLimitAfterMultiFill0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterMultiFill0 - ( fillAmt1 + fillAmt2 ) ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfterMultiFill1=" + aggregateLimitAfterMultiFill1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterMultiFill1 - ( fillAmt1 + fillAmt2 ) ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfterMultiFill2=" + aggregateLimitAfterMultiFill2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterMultiFill2 - ( pbTradeAmt1 + pbTradeAmt2 ) ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfterMultiFill3=" + aggregateLimitAfterMultiFill3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterMultiFill3 - ( pbTradeAmt1 + pbTradeAmt2 ) ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfterMultiFill0=" + dailyLimitAfterMultiFill0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterMultiFill0 - ( fillAmt1 + fillAmt2 ) ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfterMultiFill1=" + dailyLimitAfterMultiFill1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterMultiFill1 - ( fillAmt1 + fillAmt2 ) ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfterMultiFill2=" + dailyLimitAfterMultiFill2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterMultiFill2 - ( pbTradeAmt1 + pbTradeAmt2 ) ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfterMultiFill3=" + dailyLimitAfterMultiFill3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterMultiFill3 - ( pbTradeAmt1 + pbTradeAmt2 ) ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForMaskedOrgExternalTx()
    {
        try
        {
            testPBCoverCreditUtilizationAmountUpdateForMaskedOrg( true );
        }
        catch ( Exception e )
        {
            log.error( "testPBCoverCreditUtilizationAmountUpdateForMaskedOrgExternalTx", e );
            fail();
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForMaskedOrgNoExternalTx()
    {
        try
        {
            testPBCoverCreditUtilizationAmountUpdateForMaskedOrg( false );
        }
        catch ( Exception e )
        {
            log.error( "testPBCoverCreditUtilizationAmountUpdateForMaskedOrgNoExternalTx", e );
            fail();
        }
    }

    public void testPBCoverCreditUtilizationAmountUpdateForMaskedOrg( boolean createExternalTx )
    {
        try
        {
            initMaskedOrg();
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( makerOrg, makerTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 20000000 );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
            registeredTakerTpForLp.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
            registeredLpTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            double tradeAmt = 1000;
            Trade takerMakerTrade = prepareSingleLegTrade( tradeAmt, true, true, "USD/CHF", lpOrg, lpTpForFi, bidRates[8], spotDate );
            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, takerMakerTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );

            IdcTransaction tx = null;
            if ( createExternalTx )
            {
                tx = initTransaction( true );
            }
            // now update the counterpartyB of taker maker trade.
            takerMakerTrade.setCounterpartyB( takerPbLe );
            double pbTradeAmt = 900;
            Trade pbMakerTrade = prepareSingleLegTrade( pbTradeAmt, true, true, "USD/CHF", takerPbOrg, takerPbTpForMaker, bidRates[8], spotDate );
            pbMakerTrade.setTransactionID( takerMakerTrade.getTransactionID() + 'C' );
            takerMakerTrade.setCoverTradeTxIds( pbMakerTrade.getTransactionID() );
            TradeCacheC.getInstance().add( pbMakerTrade );
            TradeCacheC.getInstance().add( takerMakerTrade );

            // now update the taker maker trade.
            CreditWorkflowMessage updateCwm = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, takerMakerTrade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );
            assertEquals( "Message success status=" + updateCwm.getStatus(), updateCwm.getStatus(), MessageStatus.SUCCESS );
            if ( createExternalTx )
            {
                commitTransaction( tx );
            }

            double aggregateLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUpdate0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate2 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUpdate3 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfterUpdate0=" + aggregateLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUpdate0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfterUpdate1=" + aggregateLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUpdate1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfterUpdate2=" + aggregateLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfterUpdate3=" + aggregateLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUpdate3 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfterUpdate0=" + dailyLimitAfterUpdate0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUpdate0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfterUpdate1=" + dailyLimitAfterUpdate1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUpdate1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfterUpdate2=" + dailyLimitAfterUpdate2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUpdate2 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfterUpdate3=" + dailyLimitAfterUpdate3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUpdate3 - pbTradeAmt ) < CREDIT_CALCULATION_MINIMUM );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    /**
     * Tests the FI -> FI Pb, FI Pb -> LP, LP -> FI credit lines with a take credit and then undo credit. In this case, also checks the available limit subscriptions.
     */
    public void testPrimeBrokerageCreditLPSideDisabledWithSubscriptions()
    {
        try
        {
            initMaskedOrg();
            init( fiUser );
            removeExistingCreditUtilizationEvents( takerOrg );
            removeExistingCreditUtilizationEvents( makerOrg );
            removeExistingCreditUtilizationEvents( takerPbOrg );
            removeExistingCreditUtilizationEvents( makerPbOrg );
            double dailyLimit = 1000000;
            double aggregateLimit = 1000000;
            double dailyLimitOffset = 1000000;
            double aggregateLimitOffset = 1000000;

            // establish credit relationship between FI pb and anonymous lp
            creditAdminSvc.establishCreditRelationship( takerPbOrg, takerPbTpForLp );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForTakerPb );

            // enable the credit for all orgs and credit cpty
            creditAdminSvc.setCreditEnabled( takerOrg, true );
            creditAdminSvc.setCreditEnabled( makerOrg, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerOrg, takerTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForMakerPb, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( takerPbOrg, takerPbTpForLp, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForMaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerPbOrg, makerPbTpForTakerPb, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForTaker, true );
            creditAdminSvc.setCreditEnabled( makerOrg, makerTpForMakerPb, true );

            // set the netting calcs and limits for all credit relationships between FI1-FICPTY12-FICPTY22-FI2
            setCalcAndLimit( takerOrg, takerTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + dailyLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + aggregateLimitOffset );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 2 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerPbOrg, takerPbTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 3 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 4 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 4 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 5 ) );
            setCalcAndLimit( makerOrg, makerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 5 ) );

            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( takerPbOrg, takerPbTpForMaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit + ( dailyLimitOffset * 6 ) );
            setCalcAndLimit( makerPbOrg, makerPbTpForTaker, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit + ( aggregateLimitOffset * 6 ) );
            setCalcAndLimit( takerOrg, takerTpForMakerPb, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, dailyLimit );
            setCalcAndLimit( takerOrg, takerTpForMakerPb, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, aggregateLimit );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty registeredTakerTpForLp = ( TradingParty ) uow.registerObject( takerTpForLp );
            registeredTakerTpForLp.setPrimeBrokerageCreditUsed( true );
            TradingParty registeredLpTpForTaker = ( TradingParty ) uow.registerObject( lpTpForTaker );
            registeredLpTpForTaker.setPrimeBrokerageCreditUsed( false );
            uow.commit();

            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency limitCcy0 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerOrg, takerPbOrg, takerTpForTakerPb );
            if ( !limitCcy0.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerOrg, usd );
                creditAdminSvc.reinitialize( takerOrg );
            }
            Currency limitCcy1 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, takerOrg, takerPbTpForTaker );
            if ( !limitCcy1.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy2 = CreditUtilC.getCounterpartyCreditLimitCurrency( takerPbOrg, makerPbOrg, takerPbTpForMakerPb );
            if ( !limitCcy2.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( takerPbOrg, usd );
                creditAdminSvc.reinitialize( takerPbOrg );
            }
            Currency limitCcy3 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerPbOrg, takerPbOrg, makerPbTpForTakerPb );
            if ( !limitCcy3.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerPbOrg, usd );
                creditAdminSvc.reinitialize( makerPbOrg );
            }
            Currency limitCcy4 = CreditUtilC.getCounterpartyCreditLimitCurrency( makerOrg, makerPbOrg, makerTpForMakerPb );
            if ( !limitCcy4.isSameAs( usd ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( makerOrg, usd );
                creditAdminSvc.reinitialize( makerOrg );
            }

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( fiLe, lpLe, ccyPairs );

            // now take a simple trade
            double tradeAmt = 1000;
            double aggregateLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitBefore12 = getAvailableCreditLimit( takerPbTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitBefore0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitBefore12 = getAvailableCreditLimit( takerPbTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus()  );
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

            double aggregateLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfter12 = getAvailableCreditLimit( takerPbTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfter0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfter12 = getAvailableCreditLimit( takerPbTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitBefore0=" + aggregateLimitBefore0 + ",aggregateLimitAfter0=" + aggregateLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore1=" + aggregateLimitBefore1 + ",aggregateLimitAfter1=" + aggregateLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore2=" + aggregateLimitBefore2 + ",aggregateLimitAfter2=" + aggregateLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore3=" + aggregateLimitBefore3 + ",aggregateLimitAfter3=" + aggregateLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore4=" + aggregateLimitBefore4 + ",aggregateLimitAfter4=" + aggregateLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore5=" + aggregateLimitBefore5 + ",aggregateLimitAfter5=" + aggregateLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore6=" + aggregateLimitBefore6 + ",aggregateLimitAfter6=" + aggregateLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore7=" + aggregateLimitBefore7 + ",aggregateLimitAfter7=" + aggregateLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore8=" + aggregateLimitBefore8 + ",aggregateLimitAfter8=" + aggregateLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore9=" + aggregateLimitBefore9 + ",aggregateLimitAfter9=" + aggregateLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore10=" + aggregateLimitBefore10 + ",aggregateLimitAfter10=" + aggregateLimitAfter10 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore10 - aggregateLimitAfter10 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore11=" + aggregateLimitBefore11 + ",aggregateLimitAfter11=" + aggregateLimitAfter11 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore11 - aggregateLimitAfter11 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitBefore12=" + aggregateLimitBefore12 + ",aggregateLimitAfter12=" + aggregateLimitAfter12 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore12 - aggregateLimitAfter12 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore0=" + dailyLimitBefore0 + ",dailyLimitAfter0=" + dailyLimitAfter0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfter0 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore1=" + dailyLimitBefore1 + ",dailyLimitAfter1=" + dailyLimitAfter1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfter1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore2=" + dailyLimitBefore2 + ",dailyLimitAfter2=" + dailyLimitAfter2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfter2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore3=" + dailyLimitBefore3 + ",dailyLimitAfter3=" + dailyLimitAfter3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfter3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore4=" + dailyLimitBefore4 + ",dailyLimitAfter4=" + dailyLimitAfter4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfter4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore5=" + dailyLimitBefore5 + ",dailyLimitAfter5=" + dailyLimitAfter5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfter5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore6=" + dailyLimitBefore6 + ",dailyLimitAfter6=" + dailyLimitAfter6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfter6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore7=" + dailyLimitBefore7 + ",dailyLimitAfter7=" + dailyLimitAfter7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfter7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore8=" + dailyLimitBefore8 + ",dailyLimitAfter8=" + dailyLimitAfter8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfter8 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore9=" + dailyLimitBefore9 + ",dailyLimitAfter9=" + dailyLimitAfter9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfter9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore10=" + dailyLimitBefore10 + ",dailyLimitAfter10=" + dailyLimitAfter10 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore10 - dailyLimitAfter10 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore11=" + dailyLimitBefore11 + ",dailyLimitAfter11=" + dailyLimitAfter11 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore11 - dailyLimitAfter11 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitBefore12=" + dailyLimitBefore12 + ",dailyLimitAfter12=" + dailyLimitAfter12 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore12 - dailyLimitAfter12 ) < CREDIT_CALCULATION_MINIMUM );

            CreditWorkflowMessage undoCwm = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "undoCwm.status=" + undoCwm.getStatus() );
            log( "Message status after undo credit=" + undoCwm.getStatus() );

            double aggregateLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double aggregateLimitAfterUndo12 = getAvailableCreditLimit( takerPbTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double dailyLimitAfterUndo0 = getAvailableCreditLimit( takerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo1 = getAvailableCreditLimit( takerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo2 = getAvailableCreditLimit( takerPbTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo3 = getAvailableCreditLimit( makerPbTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo4 = getAvailableCreditLimit( makerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo5 = getAvailableCreditLimit( makerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo6 = getAvailableCreditLimit( takerTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo7 = getAvailableCreditLimit( makerTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo8 = getAvailableCreditLimit( takerPbTpForMaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo9 = getAvailableCreditLimit( makerPbTpForTaker, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo10 = getAvailableCreditLimit( makerTpForTakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo11 = getAvailableCreditLimit( takerTpForMakerPb, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            double dailyLimitAfterUndo12 = getAvailableCreditLimit( takerPbTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "aggregateLimitAfter0=" + aggregateLimitAfter0 + ",aggregateLimitAfterUndo0=" + aggregateLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore0 - aggregateLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter1=" + aggregateLimitAfter1 + ",aggregateLimitAfterUndo1=" + aggregateLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore1 - aggregateLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter2=" + aggregateLimitAfter2 + ",aggregateLimitAfterUndo2=" + aggregateLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore2 - aggregateLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter3=" + aggregateLimitAfter3 + ",aggregateLimitAfterUndo3=" + aggregateLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore3 - aggregateLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter4=" + aggregateLimitAfter4 + ",aggregateLimitAfterUndo4=" + aggregateLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore4 - aggregateLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter5=" + aggregateLimitAfter5 + ",aggregateLimitAfterUndo5=" + aggregateLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore5 - aggregateLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter6=" + aggregateLimitAfter6 + ",aggregateLimitAfterUndo6=" + aggregateLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore6 - aggregateLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter7=" + aggregateLimitAfter7 + ",aggregateLimitAfterUndo7=" + aggregateLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore7 - aggregateLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter8=" + aggregateLimitAfter8 + ",aggregateLimitAfterUndo8=" + aggregateLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore8 - aggregateLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter9=" + aggregateLimitAfter9 + ",aggregateLimitAfterUndo9=" + aggregateLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore9 - aggregateLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter10=" + aggregateLimitAfter10 + ",aggregateLimitAfterUndo10=" + aggregateLimitAfterUndo10 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore10 - aggregateLimitAfterUndo10 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter11=" + aggregateLimitAfter11 + ",aggregateLimitAfterUndo11=" + aggregateLimitAfterUndo11 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore11 - aggregateLimitAfterUndo11 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "aggregateLimitAfter12=" + aggregateLimitAfter12 + ",aggregateLimitAfterUndo12=" + aggregateLimitAfterUndo12 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLimitBefore12 - aggregateLimitAfterUndo12 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter0=" + dailyLimitAfter0 + ",dailyLimitAfterUndo0=" + dailyLimitAfterUndo0 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore0 - dailyLimitAfterUndo0 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter1=" + dailyLimitAfter1 + ",dailyLimitAfterUndo1=" + dailyLimitAfterUndo1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore1 - dailyLimitAfterUndo1 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter2=" + dailyLimitAfter2 + ",dailyLimitAfterUndo2=" + dailyLimitAfterUndo2 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore2 - dailyLimitAfterUndo2 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter3=" + dailyLimitAfter3 + ",dailyLimitAfterUndo3=" + dailyLimitAfterUndo3 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore3 - dailyLimitAfterUndo3 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter4=" + dailyLimitAfter4 + ",dailyLimitAfterUndo4=" + dailyLimitAfterUndo4 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore4 - dailyLimitAfterUndo4 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter5=" + dailyLimitAfter5 + ",dailyLimitAfterUndo5=" + dailyLimitAfterUndo5 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore5 - dailyLimitAfterUndo5 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter6=" + dailyLimitAfter6 + ",dailyLimitAfterUndo6=" + dailyLimitAfterUndo6 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore6 - dailyLimitAfterUndo6 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter7=" + dailyLimitAfter7 + ",dailyLimitAfterUndo7=" + dailyLimitAfterUndo7 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore7 - dailyLimitAfterUndo7 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter8=" + dailyLimitAfter8 + ",dailyLimitAfterUndo8=" + dailyLimitAfterUndo8 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore8 - dailyLimitAfterUndo8 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter9=" + dailyLimitAfter9 + ",dailyLimitAfterUndo9=" + dailyLimitAfterUndo9 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore9 - dailyLimitAfterUndo9 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter10=" + dailyLimitAfter10 + ",dailyLimitAfterUndo10=" + dailyLimitAfterUndo10 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore10 - dailyLimitAfterUndo10 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter11=" + dailyLimitAfter11 + ",dailyLimitAfterUndo11=" + dailyLimitAfterUndo11 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore11 - dailyLimitAfterUndo11 ) < CREDIT_CALCULATION_MINIMUM );
            assertEquals( "dailyLimitAfter12=" + dailyLimitAfter12 + ",dailyLimitAfterUndo12=" + dailyLimitAfterUndo12 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLimitBefore12 - dailyLimitAfterUndo12 ) < CREDIT_CALCULATION_MINIMUM );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            // remove credit relationship between FI pb and anonymous lp
            creditAdminSvc.removeCreditRelationship( takerPbOrg, takerPbTpForLp );
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForTakerPb );
        }
    }

    public void testCreditUtilizationUpdatesOnPBCoverBilateralTakeUndo()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            XLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerCoverTradeEnabled( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( DFI1Le, XLPLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( DFI1Le, XLPLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnPBCoverBilateralTakeUndo", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            IdcUtilC.refreshObject( XLPOrg );
            IdcUtilC.refreshObject( XPBOrg );
            IdcUtilC.refreshObject( DFI1Org );
            IdcUtilC.refreshObject( DFI1TpForXLP );
            IdcUtilC.refreshObject( XLPTpForDFI1 );
        }
    }

    public void testCreditUtilizationUpdatesOnPBCoverBilateralTakeUpdateAmount()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            XLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerCoverTradeEnabled( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( DFI1Le, XLPLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now update credit.
                    FXPaymentParameters fxPmt = ( ( FXSingleLeg ) trade1 ).getFXLeg().getFXPayment();
                    fxPmt.setCurrency1Amount( fxPmt.getCurrency1Amount() / 2.0 );
                    fxPmt.setCurrency2Amount( fxPmt.getCurrency2Amount() / 2.0 );

                    Trade pbCoverTrade = prepareSingleLegTrade( fxPmt.getCurrency1Amount() / 2.0, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade.setTransactionID( trade1.getTransactionID() + 'C' );
                    trade1.setCoverTradeTxIds( pbCoverTrade.getTransactionID() );
                    TradeCacheC.getInstance().add( trade1 );
                    TradeCacheC.getInstance().add( pbCoverTrade );
                    FXPaymentParameters fxPmt1 = ( ( FXSingleLeg ) pbCoverTrade ).getFXLeg().getFXPayment();
                    fxPmt1.setCurrency1Amount( fxPmt.getCurrency1Amount() );
                    fxPmt1.setCurrency2Amount( fxPmt.getCurrency2Amount() );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount( XLPLe, DFI1Le, trade1, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnPBCoverBilateralTakeUpdateAmount", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            IdcUtilC.refreshObject( XLPTpForDFI1 );
            IdcUtilC.refreshObject( DFI1TpForXLP );
        }
    }

    public void testCreditUtilizationUpdatesOnPBCoverBilateralTakeAmendCpty()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            XLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerCoverTradeEnabled( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( XLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( XLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now book the trade in a different cpty
                    LegalEntity newLPLe = XPBLe;
                    trade1.setCounterpartyB( newLPLe );
                    CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( DFI1Le, newLPLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg2 = ( CreditWorkflowMessage ) cwm2.getReplyMessage();
                    String eventSnapshot2 = ( String ) replyMsg2.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event2 = replyMsg2.getEventName();
                    IdcDate tradeDate2 = replyMsg2.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event2, eventSnapshot2, trade1.getTransactionID(), tradeDate2 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnPBCoverBilateralTakeAmendCpty", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            IdcUtilC.refreshObject( XLPTpForDFI1 );
            IdcUtilC.refreshObject( DFI1TpForXLP );
        }
    }

    public void testCreditUtilizationUpdatesOnPBCoverBilateralTakeMultiFillFullFill()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            XLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerCoverTradeEnabled( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( XLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 500;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    fillTrade1.setTransactionID( "FXI" + System.nanoTime() );
                    fillTrade2.setTransactionID( "FXI" + System.nanoTime() );

                    Trade pbCoverTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade1.setTransactionID( fillTrade1.getTransactionID() + 'C' );
                    fillTrade1.setCoverTradeTxIds( pbCoverTrade1.getTransactionID() );
                    TradeCacheC.getInstance().add( fillTrade1 );
                    TradeCacheC.getInstance().add( pbCoverTrade1 );

                    Trade pbCoverTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade2.setTransactionID( fillTrade2.getTransactionID() + 'C' );
                    fillTrade2.setCoverTradeTxIds( pbCoverTrade2.getTransactionID() );
                    TradeCacheC.getInstance().add( fillTrade2 );
                    TradeCacheC.getInstance().add( pbCoverTrade2 );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( XLPLe, DFI1Le, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnPBCoverBilateralTakeMultiFillFullFill", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            IdcUtilC.refreshObject( XLPTpForDFI1 );
            IdcUtilC.refreshObject( DFI1TpForXLP );
        }
    }

    public void testCreditUtilizationUpdatesOnPBCoverBilateralTakeMultiFillFullFillWithSameTrade()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            XLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerCoverTradeEnabled( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( XLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades

                    Trade pbCoverTrade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade1.setTransactionID( trade1.getTransactionID() + 'C' );
                    trade1.setCoverTradeTxIds( pbCoverTrade1.getTransactionID() );
                    TradeCacheC.getInstance().add( trade1 );
                    TradeCacheC.getInstance().add( pbCoverTrade1 );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( trade1 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( XLPLe, DFI1Le, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnPBCoverBilateralTakeMultiFillFullFillWithSameTrade", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            IdcUtilC.refreshObject( XLPTpForDFI1 );
            IdcUtilC.refreshObject( DFI1TpForXLP );
        }
    }

    public void testCreditUtilizationUpdatesOnPBCoverBilateralTakeMultiFillPartialFill()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            XLPTpForDFI1.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerageCreditUsed( true );
            DFI1TpForXLP.setPrimeBrokerCoverTradeEnabled( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( DFI1Org );
                    removeExistingCreditUtilizationEvents( XLPOrg );
                    removeExistingCreditUtilizationEvents( XPBOrg );

                    // enable the credit for all orgs and credit cpty
                    creditAdminSvc.setCreditEnabled( DFI1Org, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForDFI1, true );
                    creditAdminSvc.setCreditEnabled( XPBOrg, XPBTpForXLP, true );
                    creditAdminSvc.setCreditEnabled( DFI1Org, DFI1TpForXPB, true );
                    creditAdminSvc.setCreditEnabled( XLPOrg, XLPTpForXPB, true );

                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( DFI1Org, DFI1TpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForDFI1, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );
                    setCalcAndLimit( XLPOrg, XLPTpForXPB, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, 20000000 );
                    setCalcAndLimit( XPBOrg, XPBTpForXLP, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, 20000000 );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( XLPLe, DFI1Le, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 100;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, XLPOrg, XLPTpForDFI1, bidRates[0], spotDate );
                    fillTrade1.setTransactionID( "FXI" + System.nanoTime() );
                    fillTrade2.setTransactionID( "FXI" + System.nanoTime() );

                    Trade pbCoverTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade1.setTransactionID( fillTrade1.getTransactionID() + 'C' );
                    fillTrade1.setCoverTradeTxIds( pbCoverTrade1.getTransactionID() );
                    TradeCacheC.getInstance().add( fillTrade1 );
                    TradeCacheC.getInstance().add( pbCoverTrade1 );

                    Trade pbCoverTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, XLPOrg, XLPTpForXPB, bidRates[0], spotDate );
                    pbCoverTrade2.setTransactionID( fillTrade2.getTransactionID() + 'C' );
                    fillTrade2.setCoverTradeTxIds( pbCoverTrade2.getTransactionID() );
                    TradeCacheC.getInstance().add( fillTrade2 );
                    TradeCacheC.getInstance().add( pbCoverTrade2 );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( XLPLe, DFI1Le, trade1, fillTradeColl, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1 );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        log.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    log.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnPBCoverBilateralTakeMultiFillPartialFill", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
            IdcUtilC.refreshObject( XLPTpForDFI1 );
            IdcUtilC.refreshObject( DFI1TpForXLP );
        }
    }


    private void initMaskedOrg()
    {
        tradeConfigMBean.setProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow.FICpty12", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        tradeConfigMBean.setProperty( "IDC.LP.FI2.Masked", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        tradeConfigMBean.setProperty( "IDC.LP.FI2.Masked.Name", "CITI", ConfigurationProperty.DYNAMIC_SCOPE, null );

        // set the real lp flag on the masked org.
        lpOrg.setRealLP( makerOrg );
    }

    protected void setUp() throws Exception
    {
        super.setUp();
    }

    protected void tearDown()
    {
        super.tearDown();
        tradeConfigMBean.removeProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow.FICpty12", ConfigurationProperty.DYNAMIC_SCOPE );
        tradeConfigMBean.removeProperty( "IDC.LP.FI2.Masked", ConfigurationProperty.DYNAMIC_SCOPE );
        tradeConfigMBean.removeProperty( "IDC.LP.FI2.Masked.Name", ConfigurationProperty.DYNAMIC_SCOPE );

        // reset the real lp flag on the masked org.
        lpOrg.setRealLP( null );
    }
}
