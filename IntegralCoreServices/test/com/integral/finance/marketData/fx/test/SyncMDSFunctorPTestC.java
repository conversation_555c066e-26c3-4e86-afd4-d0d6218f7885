package com.integral.finance.marketData.fx.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.marketData.fx.*;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.finance.trade.Tenor;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.util.IdcUtilC;

import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;


/**
 * Tests market data set lookups and market data set updating as an end of day process.
 *
 * <AUTHOR> Development Corp.
 */
public class SyncMDSFunctorPTestC extends PTestCaseC
{
    static String name = "SyncMDSFunctorPTestC";
    private FXRateConvention stdConv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );

    public SyncMDSFunctorPTestC( String name )
    {
        super( name );
    }

    public void testStaticMDSSimpleSync()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );

            addFXMarketDataElement( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "1w" ) );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            assertFalse(testTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, testTargetMds.getMarketDataElements().size() );
            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde );
            assertNull( fxMde.getBrokenDate() );

            FXMask mask2 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde1 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask2 );
            assertNotNull( fxMde1 );
            assertNull( fxMde1.getBrokenDate() );
        }
        catch ( Exception e )
        {
            fail( "testStaticMDSSimpleSync", e );
        }
    }

    public void testStaticMDSSimpleSyncWithBrokenDates()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );

            IdcDate brokenDate = DateTimeFactory.newDate().addDays( 15 );
            addFXMarketDataElement( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, brokenDate );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "1w" ) );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            assertFalse(testTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, testTargetMds.getMarketDataElements().size() );
            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, brokenDate );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde );
            assertNotNull( fxMde.getBrokenDate() );
            assertEquals ( brokenDate, fxMde.getBrokenDate () );

            FXMask mask2 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde1 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask2 );
            assertNotNull( fxMde1 );
            assertNull( fxMde1.getBrokenDate() );
        }
        catch ( Exception e )
        {
            fail( "testStaticMDSSimpleSyncWithBrokenDates", e );
        }
    }

    public void testStaticMDSSyncTargetUpdateMde()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );

            addFXMarketDataElement( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, eur, jpy, 121.11, 0.0, 122.12, 0.0, false, Tenor.SPOT_TENOR );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );

            addFXMarketDataElement( registeredTestTargetMds, eur, usd, 2.2345, 0.0, 2.2367, 0.0, false, Tenor.SPOT_TENOR );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            assertFalse(testTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, testTargetMds.getMarketDataElements().size() );

            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = (FXMarketDataElement) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde );
            assertTrue( fxMde.getFXPrice().getBidFXRate().getRate() < 2.0 );
            assertTrue( fxMde.getFXPrice().getOfferFXRate().getRate() < 2.0 );
        }
        catch ( Exception e )
        {
            fail( "testStaticMDSSyncTargetUpdateMde", e );
        }
    }

    public void testStaticMDSSyncAdditionalTargetMdes()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );

            addFXMarketDataElement( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );

            addFXMarketDataElement( registeredTestTargetMds, eur, jpy, 121.11, 0.0, 122.22, 0.0, false, Tenor.SPOT_TENOR );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            assertFalse(testTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 2, testTargetMds.getMarketDataElements().size() );

            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = (FXMarketDataElement) testTargetMds.getMarketDataElement( mask1 );
            assertNull( fxMde );
        }
        catch ( Exception e )
        {
            fail( "testStaticMDSSyncAdditionalTargetMdes", e );
        }
    }

    public void testRealtimeToStaticMDSSync()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );
            registeredTestSourceMds.setRealtime( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            addFXMarketDataElement((FXMarketDataSet) testSourceMds.getSessionInstance(), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "1w" ) );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            assertFalse(testTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, testTargetMds.getMarketDataElements().size() );
            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde );
            assertNull( fxMde.getBrokenDate() );

            FXMask mask2 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde1 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask2 );
            assertNotNull( fxMde1 );
            assertNull( fxMde1.getBrokenDate() );
        }
        catch ( Exception e )
        {
            fail( "testRealtimeToStaticMDSSync", e );
        }
    }

    public void testRealtimeToStaticMDSSyncWithBrokenDates()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );
            registeredTestSourceMds.setRealtime( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            addFXMarketDataElement((FXMarketDataSet) testSourceMds.getSessionInstance(), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, DateTimeFactory.newDate().addDays( 14 ) );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "1w" ) );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            assertFalse(testTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, testTargetMds.getMarketDataElements().size() );
            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde );
            assertNotNull( fxMde.getBrokenDate() );

            FXMask mask2 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde1 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask2 );
            assertNotNull( fxMde1 );
            assertNull( fxMde1.getBrokenDate() );
        }
        catch ( Exception e )
        {
            fail( "testRealtimeToStaticMDSSyncWithBrokenDates", e );
        }
    }

    public void testStaticToRealtimeMDSSync()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );

            addFXMarketDataElement( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "1w" ) );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );
            registeredTestTargetMds.setRealtime( true );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            FXMarketDataSet realtimeTargetMds = (FXMarketDataSet) testTargetMds.getSessionInstance();
            assertFalse(realtimeTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, realtimeTargetMds.getMarketDataElements().size() );
            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde);
            assertNull( fxMde.getBrokenDate() );

            FXMask mask2 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde1 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask2 );
            assertNotNull( fxMde1 );
            assertNull( fxMde1.getBrokenDate() );
        }
        catch ( Exception e )
        {
            fail( "testStaticToRealtimeMDSSync", e );
        }
    }

    public void testStaticToRealtimeMDSSyncWithBrokenDates()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );

            addFXMarketDataElement( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, DateTimeFactory.newDate().addDays( 13 ) );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "1w" ) );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );
            registeredTestTargetMds.setRealtime( true );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            FXMarketDataSet realtimeTargetMds = (FXMarketDataSet) testTargetMds.getSessionInstance();
            assertFalse(realtimeTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, realtimeTargetMds.getMarketDataElements().size() );
            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde);
            assertNotNull( fxMde.getBrokenDate() );

            FXMask mask2 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde1 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask2 );
            assertNotNull( fxMde1 );
            assertNull( fxMde1.getBrokenDate() );
        }
        catch ( Exception e )
        {
            fail( "testStaticToRealtimeMDSSyncWithBrokenDates", e );
        }
    }

    public void testRealtimeToRealtimeMDSSync()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );
            registeredTestSourceMds.setRealtime( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );
            registeredTestTargetMds.setRealtime( true );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "1w" ) );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            FXMarketDataSet realtimeTargetMds = (FXMarketDataSet) testTargetMds.getSessionInstance();
            assertFalse(realtimeTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, realtimeTargetMds.getMarketDataElements().size() );
            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde );
            assertNull( fxMde.getBrokenDate() );

            FXMask mask2 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde1 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask2 );
            assertNotNull( fxMde1 );
            assertNull( fxMde1.getBrokenDate() );
        }
        catch ( Exception e )
        {
            fail( "testRealtimeToRealtimeMDSSync", e );
        }
    }

    public void testRealtimeToRealtimeMDSSyncWithBrokenDates()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );
            registeredTestSourceMds.setRealtime( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );
            registeredTestTargetMds.setRealtime( true );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, DateTimeFactory.newDate().addDays( 12 ) );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "10d" ) );


            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );
            functor.execute( null );

            FXMarketDataSet realtimeTargetMds = (FXMarketDataSet) testTargetMds.getSessionInstance();
            assertFalse(realtimeTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 3, realtimeTargetMds.getMarketDataElements().size() );
            FXMask mask1 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask1 );
            assertNotNull( fxMde );
            assertNotNull( fxMde.getBrokenDate() );

            FXMask mask2 = FXMarketDataFactory.newFXMask( CurrencyFactory.getCurrencyPair( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde1 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement( mask2 );
            assertNotNull( fxMde1 );
            assertNull( fxMde1.getBrokenDate() );
        }
        catch ( Exception e )
        {
            fail( "testRealtimeToRealtimeMDSSyncWithBrokenDates", e );
        }
    }

    public void testRealtimeToRealtimeMDSSyncWithSynonymousTenors()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );
            registeredTestSourceMds.setRealtime( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );
            registeredTestTargetMds.setRealtime( true );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            // add synonymous tenors
            addFXMarketDataElement( (FXMarketDataSet) testTargetMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testTargetMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "ON" ) );

            // add elements in the source mds
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testSourceMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "TOD" ) );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );

            functor.execute( null );
            functor.execute ( null );

            Thread.sleep ( 1000 );

            FXMarketDataSet realtimeTargetMds = (FXMarketDataSet) testTargetMds.getSessionInstance();
            assertFalse(realtimeTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 2, realtimeTargetMds.getMarketDataElements().size() );
        }
        catch ( Exception e )
        {
            fail( "testRealtimeToRealtimeMDSSyncWithSynonymousTenors", e );
        }
    }

    public void testRealtimeToRealtimeMDSSyncMidOnly()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency("EUR");
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis();
            registeredTestSourceMds.setShortName( sourceMdsName );
            registeredTestSourceMds.setStatus( 'T' );
            registeredTestSourceMds.setRealtime( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis();
            registeredTestTargetMds.setShortName( targetMdsName );
            registeredTestTargetMds.setStatus( 'T' );
            registeredTestTargetMds.setRealtime( true );

            uow.commit();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject( testSourceMds );
            assertNotNull( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject(testTargetMds);
            assertNotNull( testTargetMds );

            // add synonymous tenors
            FXMarketDataElement mde1 =  createFXMarketDataElement ( usd, jpy, 111.11, 0.0, 112.12, 0.0, Tenor.SPOT_TENOR, true, null );
            mde1.getFXPrice ().setBidFXRate ( null );
            mde1.getFXPrice ().setOfferFXRate ( null );
            testSourceMds.getSessionInstance().addMarketDataElement ( mde1 );

            FXMarketDataElement mde2 =  createFXMarketDataElement ( usd, jpy, 111.11, 0.0, 112.12, 0.0, new Tenor( "ON" ), true, null );
            mde2.getFXPrice ().setBidFXRate ( null );
            mde2.getFXPrice ().setOfferFXRate ( null );
            testSourceMds.getSessionInstance().addMarketDataElement ( mde2 );

            // add elements in the source mds
            addFXMarketDataElement( (FXMarketDataSet) testTargetMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement( (FXMarketDataSet) testTargetMds.getSessionInstance(), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor( "TOD" ) );

            SyncMDSFunctorC functor = new SyncMDSFunctorC();
            functor.setSourceMDSName( sourceMdsName );
            functor.setSinkMDSName( targetMdsName );

            functor.execute( null );
            functor.execute ( null );

            Thread.sleep ( 1000 );

            FXMarketDataSet realtimeTargetMds = (FXMarketDataSet) testTargetMds.getSessionInstance();
            assertFalse(realtimeTargetMds.getMarketDataElements().isEmpty());
            assertEquals( 2, realtimeTargetMds.getMarketDataElements().size() );
        }
        catch ( Exception e )
        {
            fail( "testRealtimeToRealtimeMDSSyncMidOnly", e );
        }
    }

    private void addFXMarketDataElement( FXMarketDataSet mds, FXMarketDataElement mde )
    {
        if ( mds.isRealtime() )
        {
            mds.getSessionInstance().addMarketDataElement( mde );
        }
        else
        {
            mds.addMarketDataElement( mde );
        }
        mde.setMarketDataSet( mds );
    }

    private void addFXMarketDataElement( FXMarketDataSet mds, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, boolean createMid, Tenor tenor )
    {
        FXMarketDataElement mde1 = createFXMarketDataElement( base, var, bidSpotRate, bidFwdPoints, offerSpotRate, offerFwdPoints, tenor, createMid, null );
        addFXMarketDataElement( mds, mde1 );
    }

    private void addFXMarketDataElement( FXMarketDataSet mds, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, boolean createMid, IdcDate valueDate )
    {
        FXMarketDataElement mde1 = createFXMarketDataElement( base, var, bidSpotRate, bidFwdPoints, offerSpotRate, offerFwdPoints, null, createMid, valueDate );
        addFXMarketDataElement( mds, mde1 );
    }

    private FXMarketDataElement createFXMarketDataElement( Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, Tenor tenor, boolean createMid, IdcDate valueDate )
    {
        FXMarketDataElement mde1 = FXMarketDataFactory.newFXMarketDataElement();
        FXPrice fxPrice = FXPriceFactory.newFXPrice();
        mde1.setFXPrice( fxPrice );
        mde1.setTenor( tenor );
        mde1.setValueDate( valueDate );
        FXRate bid = FXFactory.newFXRate();
        bid.setFXRateConvention( stdConv );
        bid.setBaseCurrency( base );
        bid.setVariableCurrency( var );
        bid.setSpotRate( bidSpotRate );
        bid.setForwardPoints( bidFwdPoints );
        fxPrice.setBidFXRate( bid );
        FXRate offer = FXFactory.newFXRate();
        offer.setFXRateConvention( stdConv );
        offer.setBaseCurrency( base );
        offer.setVariableCurrency( var );
        offer.setSpotRate( offerSpotRate );
        offer.setForwardPoints( offerFwdPoints );
        fxPrice.setOfferFXRate( offer );
        if ( createMid )
        {
            FXRate computedMid = fxPrice.getComputedMidFXRate();
            FXRate mid = FXFactory.newFXRate();
            mid.setFXRateConvention( stdConv );
            mid.setBaseCurrency( base );
            mid.setVariableCurrency( var );
            mid.setSpotRate( computedMid.getSpotRate() );
            mid.setForwardPoints( computedMid.getForwardPoints() );
            fxPrice.setMidFXRate( mid );
        }
        String shortName = new StringBuffer( base.getShortName() ).append( '&' ).append( var.getShortName() ).toString();
        mde1.setShortName( shortName );
        return mde1;
    }

}
