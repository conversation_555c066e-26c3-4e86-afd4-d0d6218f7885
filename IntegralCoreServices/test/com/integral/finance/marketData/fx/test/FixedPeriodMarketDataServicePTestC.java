package com.integral.finance.marketData.fx.test;

// Copyright (c) 2021 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.marketData.configuration.MarketDataConfigurationMBean;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.marketData.fx.FixedPeriodMarketDataServiceC;
import com.integral.finance.marketData.model.FXMDSUpdateEvent;
import com.integral.finance.marketData.model.MDSUpdateEvent;
import com.integral.finance.marketData.service.MDSUpdateHelper;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.TimeZone;

/**
 * Tests fixed period market data creation and lookup.
 *
 * <AUTHOR> Development Corp.
 */
public class FixedPeriodMarketDataServicePTestC extends PTestCaseC
{
    static String name = "Fixed Period market data service Test";
    FXRateConvention stdConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
    protected String adminUserName = "Integral";

    public FixedPeriodMarketDataServicePTestC ( String name )
    {
        super ( name );
    }

    public void testCreateFixedPeriodMarketDataSet ( )
    {
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            String mdsName = "Test-" + brokerOrg.getShortName () + System.currentTimeMillis ();
            String mdsLongName = mdsName + " mds";
            IdcDate date = DateTimeFactory.newDate ();
            long now = System.currentTimeMillis ();
            Timestamp start = new Timestamp ( now - 60000 );
            Timestamp end = new Timestamp ( now + 1200000 );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "USD/JPY", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "GBP/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/JPY", "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );

            FXMarketDataSet mds = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents, start, end );
            assertNotNull ( mds );
            assertEquals ( mdsName, mds.getShortName () );
            assertEquals ( mdsLongName, mds.getLongName () );
            assertEquals ( date, mds.getBaseDate () );
            assertEquals ( start, mds.getStartTime () );
            assertEquals ( end, mds.getEndTime () );
            assertEquals ( brokerOrg.getNamespace (), mds.getNamespace () );
            assertEquals ( 4, mds.getMarketDataElements ().size () );
            Collection<FXMarketDataElement> mdes = ( Collection<FXMarketDataElement> ) mds.getMarketDataElements ();
            for ( FXMarketDataElement fxMarketDataElement : mdes )
            {
                assertNotNull ( fxMarketDataElement.getRateID () );
                assertTrue ( fxMarketDataElement.getRateID ().contains ( fxMarketDataElement.getGUID () ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testCreateFixedPeriodMarketDataSet", e );
        }
    }

    public void testMDSLookup ( )
    {
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            String mdsName = "Test-" + brokerOrg.getShortName () + System.currentTimeMillis ();
            String mdsLongName = mdsName + " mds";
            IdcDate date = DateTimeFactory.newDate ();
            long now = System.currentTimeMillis ();
            Timestamp start = new Timestamp ( now - 60000 );
            Timestamp end = new Timestamp ( now + 1200000 );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "USD/JPY", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "GBP/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/JPY", "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );

            FXMarketDataSet mds = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents, start, end );
            assertNotNull ( mds );
            FXMarketDataSet cachedMDS = ReferenceDataCacheC.getInstance ().getFixedPeriodMarketDataSetByGUID ( mds.getGUID () );
            assertNotNull ( cachedMDS );
            Collection<FXMarketDataElement> mdes = ( Collection<FXMarketDataElement> ) mds.getMarketDataElements ();
            for ( FXMarketDataElement fxMarketDataElement : mdes )
            {
                String rateId = fxMarketDataElement.getRateID ();
                FXMarketDataSet marketDataSet = FixedPeriodMarketDataServiceC.getInstance ().getMarketDataSetByRateId ( rateId );
                assertNotNull ( marketDataSet );
                assertTrue ( marketDataSet.isSameAs ( mds ) );
                FXMarketDataElement fxMde = FixedPeriodMarketDataServiceC.getInstance ().getMarketDataElementByRateId ( rateId );
                assertNotNull ( fxMde );
                assertTrue ( fxMarketDataElement.isSameAs ( fxMde ) );

                String marketDataSetName = FixedPeriodMarketDataServiceC.getInstance ().getMarketDataSetName ( rateId );
                assertEquals ( mds.getShortName (), marketDataSetName );
            }
        }
        catch ( Exception e )
        {
            fail ( "testMDSLookup", e );
        }
    }

    public void testValidateRateId ( )
    {
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            String mdsName = "Test-" + brokerOrg.getShortName () + System.currentTimeMillis ();
            String mdsLongName = mdsName + " mds";
            IdcDate date = DateTimeFactory.newDate ();
            long now = System.currentTimeMillis ();
            Timestamp start = new Timestamp ( System.currentTimeMillis () - 120000 );
            Timestamp end = new Timestamp ( System.currentTimeMillis () - 120000 );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "USD/JPY", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "GBP/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/JPY", "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );

            FXMarketDataSet mds = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents, start, end );
            assertNotNull ( mds );
            FXMarketDataSet cachedMDS = ReferenceDataCacheC.getInstance ().getFixedPeriodMarketDataSetByGUID ( mds.getGUID () );
            assertNotNull ( cachedMDS );
            Collection<FXMarketDataElement> mdes = ( Collection<FXMarketDataElement> ) mds.getMarketDataElements ();
            for ( FXMarketDataElement fxMarketDataElement : mdes )
            {
                String rateId = fxMarketDataElement.getRateID ();
                FXMarketDataSet marketDataSet = FixedPeriodMarketDataServiceC.getInstance ().getMarketDataSetByRateId ( rateId );
                assertNotNull ( marketDataSet );
                assertTrue ( marketDataSet.isSameAs ( mds ) );
                FXMarketDataElement fxMde = FixedPeriodMarketDataServiceC.getInstance ().getMarketDataElementByRateId ( rateId );
                assertNotNull ( fxMde );
                assertTrue ( fxMarketDataElement.isSameAs ( fxMde ) );

                Timestamp newStartTime = new Timestamp ( System.currentTimeMillis () - 50000 );
                mds.setStartTime ( newStartTime );

                Timestamp newEndTime = new Timestamp ( System.currentTimeMillis () + 50000 );
                mds.setEndTime ( newEndTime );

                assertNotNull ( FixedPeriodMarketDataServiceC.getInstance ().validateRateId ( rateId ) );
                assertNotNull ( FixedPeriodMarketDataServiceC.getInstance ().validateRateId ( rateId ) );

                newEndTime = new Timestamp ( System.currentTimeMillis () - 50000 );
                mds.setEndTime ( newEndTime );

                assertNull ( FixedPeriodMarketDataServiceC.getInstance ().validateRateId ( rateId ) );
                assertNull ( FixedPeriodMarketDataServiceC.getInstance ().validateRateId ( rateId ) );

                Timestamp newEndTime1 = new Timestamp ( System.currentTimeMillis () + 150000 );
                mds.setEndTime ( newEndTime1 );

                newStartTime = new Timestamp ( System.currentTimeMillis () + 50000 );
                mds.setStartTime ( newStartTime );

                assertNull ( FixedPeriodMarketDataServiceC.getInstance ().validateRateId ( rateId ) );
                assertNull ( FixedPeriodMarketDataServiceC.getInstance ().validateRateId ( rateId ) );

                newStartTime = new Timestamp ( System.currentTimeMillis () - 50000 );
                mds.setStartTime ( newStartTime );

                assertNotNull ( FixedPeriodMarketDataServiceC.getInstance ().validateRateId ( rateId ) );
                assertNotNull ( FixedPeriodMarketDataServiceC.getInstance ().validateRateId ( rateId ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testValidateRateId", e );
        }
    }

    public void testCurrentMDSLookup ( )
    {
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            String mdsName = "Test2-" + brokerOrg.getShortName () + System.currentTimeMillis ();
            String mdsLongName = mdsName + " mds";
            IdcDate date = DateTimeFactory.newDate ();
            long now = System.currentTimeMillis ();
            Timestamp start1 = new Timestamp ( now - 150000 );
            Timestamp start2 = new Timestamp ( now - 150000 );
            Timestamp endTime1 = new Timestamp ( now - 1000 );
            Timestamp endTime2 = new Timestamp ( now + 250000 );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "USD/JPY", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "GBP/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/JPY", "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );

            FXMarketDataSet mds2 = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents, start2, endTime2 );
            assertNotNull ( mds2 );
            FXMarketDataSet mds1 = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents, start1, endTime1 );
            assertNotNull ( mds1 );
            Thread.sleep ( 1000 );
            FXMarketDataSet cachedMDS = ReferenceDataCacheC.getInstance ().getCurrentFixedPeriodMarketDataSet ( brokerOrg, date, mdsName );
            assertNotNull ( cachedMDS );
            assertTrue ( mds2.isSameAs ( cachedMDS ) );
        }
        catch ( Exception e )
        {
            fail ( "testCurrentMDSLookup", e );
        }
    }

    public void testUpdateFixedPeriodMarketDataSet ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.FIXED_PERIOD_MARKET_DATASET_CACHE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            String mdsName = "Test-" + brokerOrg.getShortName () + System.currentTimeMillis ();
            String mdsLongName = mdsName + " mds";
            IdcDate date = DateTimeFactory.newDate ();
            long now = System.currentTimeMillis ();
            Timestamp start = new Timestamp ( now - 600000 );
            Timestamp end = new Timestamp ( now + 600000 );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "USD/JPY", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "GBP/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/JPY", "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );

            FXMarketDataSet mds = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents, start, end );
            assertNotNull ( mds );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/USD", "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );


            FXMarketDataSet mds1 = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents1, start, end );
            assertNotNull ( mds1 );
            assertEquals ( 4, mds1.getMarketDataElements ().size () );
        }
        catch ( Exception e )
        {
            fail ( "testUpdateFixedPeriodMarketDataSet", e );
        }
    }

    public void testMDSLookupForDatePeriod ( )
    {
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            String mdsName = "Test-" + brokerOrg.getShortName () + System.currentTimeMillis ();
            String mdsLongName = mdsName + " mds";
            IdcDate date = DateTimeFactory.newDate ();
            long now = System.currentTimeMillis ();
            Timestamp start1 = new Timestamp ( now - 60000 );
            Timestamp end1 = new Timestamp ( now + 120000 );
            Timestamp start2 = new Timestamp ( now + 130000 );
            Timestamp end2 = new Timestamp ( now + 260000 );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "USD/JPY", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "GBP/USD", "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), "EUR/JPY", "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );

            FXMarketDataSet mds1 = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents, start1, end1 );
            assertNotNull ( mds1 );
            FXMarketDataSet mds2 = FixedPeriodMarketDataServiceC.getInstance ().createFixedPeriodMarketDataSet ( brokerOrg, stdConv, mdsName, mdsLongName, date, fxmdsUpdateEvents, start2, end2 );
            assertNotNull ( mds2 );

            Timestamp ts1 = new Timestamp ( start1.getTime () - 1000 );
            Timestamp ts2 = new Timestamp ( end2.getTime ()  + 1000 );
            Collection<FXMarketDataSet> mdsCol1 = ReferenceDataCacheC.getInstance ().getFixedPeriodMarketDataSetsForDatePeriod ( brokerOrg, mdsName, ts1, ts2 );
            assertNotNull ( mdsCol1 );
            assertFalse ( mdsCol1.isEmpty () );
            assertEquals ( 2, mdsCol1.size () );

            Timestamp ts3 = new Timestamp ( start1.getTime () - 20000 );
            Timestamp ts4 = new Timestamp ( start1.getTime () - 10000 );
            Collection<FXMarketDataSet> mdsCol2 = ReferenceDataCacheC.getInstance ().getFixedPeriodMarketDataSetsForDatePeriod ( brokerOrg, mdsName, ts3, ts4 );
            assertNotNull ( mdsCol2 );
            assertTrue ( mdsCol2.isEmpty () );

            Timestamp ts5 = new Timestamp ( end2.getTime () + 10000 );
            Timestamp ts6 = new Timestamp ( end2.getTime () + 20000 );
            Collection<FXMarketDataSet> mdsCol3 = ReferenceDataCacheC.getInstance ().getFixedPeriodMarketDataSetsForDatePeriod ( brokerOrg, mdsName, ts5, ts6 );
            assertNotNull ( mdsCol3 );
            assertTrue ( mdsCol3.isEmpty () );
        }
        catch ( Exception e )
        {
            fail ( "testMDSLookupForDatePeriod", e );
        }
    }
}
