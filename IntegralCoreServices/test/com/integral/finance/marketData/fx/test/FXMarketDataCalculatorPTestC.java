package com.integral.finance.marketData.fx.test;

// Copyright (c) 2008 Integral Development Corp.  All rights reserved.

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.marketData.MarketDataElement;
import com.integral.finance.marketData.configuration.MarketDataConfigurationMBean;
import com.integral.finance.marketData.fx.*;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.PersistenceFactory;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.DealingPTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;

/**
 * Tests persistence of FX trades and related objects
 */
public class FXMarketDataCalculatorPTestC
        extends DealingPTestCaseC
{
    static String name = "FX Market Data Calculator Test";

    public FXMarketDataCalculatorPTestC( String name )
    {
        super( name );
    }

    public void testCrossCurrencyComputation()
    {
        try
        {
            Currency omr = CurrencyFactory.getCurrency( "OMR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency inr = CurrencyFactory.getCurrency( "INR" );

            FXMarketDataElement usdOmrMde = staticMds.findSpotConversionMarketDataElement( usd, omr, true );
            FXMarketDataElement usdInrMde = staticMds.findSpotConversionMarketDataElement( usd, inr, true );
            if ( usdOmrMde != null && usdInrMde != null )
            {
                log( "gbpUsdMde.bidRate=" + usdOmrMde.getFXPrice().getBidFXRate().getRate() + ",offerRate=" + usdOmrMde.getFXPrice().getOfferFXRate().getRate() );
                log( "audUsdMde.bidRate=" + usdInrMde.getFXPrice().getBidFXRate().getRate() + ",offerRate=" + usdInrMde.getFXPrice().getOfferFXRate().getRate() );
                assertNotNull( usdOmrMde );
                assertNotNull( usdInrMde );
                assertTrue( doSanityCheck( usdOmrMde, usd.getIndex(), omr.getIndex() ) );
                assertTrue( doSanityCheck( usdInrMde, usd.getIndex(), inr.getIndex() ) );
                FXMarketDataElement omrInrMde = staticMds.getFXRate( CurrencyFactory.newCurrencyPair( omr, inr ), true, Tenor.SPOT_TENOR );
                assertNull( omrInrMde );

                omrInrMde = staticMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertNotNull( omrInrMde );
                FXPrice fxPrc = omrInrMde.getFXPrice();
                log( "gbpAudMde.bidRate=" + fxPrc.getBidFXRate().getRate() + ",offerRate=" + fxPrc.getOfferFXRate().getRate() );
                assertTrue( doSanityCheck( omrInrMde, omr.getIndex(), inr.getIndex() ) );

                // set the following rate
                usdOmrMde.getFXPrice().getBidFXRate().getFXRateBasis().setSpotPrecision( 5 );
                usdOmrMde.getFXPrice().getOfferFXRate().getFXRateBasis().setInverseSpotPrecision( 5 );
                usdOmrMde.getFXPrice().getBidFXRate().setSpotRate( 1.95960 );
                usdOmrMde.getFXPrice().getBidFXRate().setRate( 1.95960 );
                usdOmrMde.getFXPrice().getBidFXRate().setForwardPoints( 0.0 );
                usdOmrMde.getFXPrice().getOfferFXRate().setSpotRate( 1.96002 );
                usdOmrMde.getFXPrice().getOfferFXRate().setRate( 1.96002 );
                usdOmrMde.getFXPrice().getOfferFXRate().setForwardPoints( 0.0 );

                usdInrMde.getFXPrice().getBidFXRate().getFXRateBasis().setSpotPrecision( 5 );
                usdInrMde.getFXPrice().getOfferFXRate().getFXRateBasis().setInverseSpotPrecision( 5 );
                usdInrMde.getFXPrice().getBidFXRate().setSpotRate( 0.89293 );
                usdInrMde.getFXPrice().getBidFXRate().setRate( 0.89293 );
                usdInrMde.getFXPrice().getBidFXRate().setForwardPoints( 0.0 );
                usdInrMde.getFXPrice().getOfferFXRate().setSpotRate( 0.89318 );
                usdInrMde.getFXPrice().getOfferFXRate().setRate( 0.89318 );
                usdInrMde.getFXPrice().getOfferFXRate().setForwardPoints( 0.0 );
                log( "after reset gbpUsdMde.bidRate=" + usdOmrMde.getFXPrice().getBidFXRate().getRate() + ",offerRate=" + usdOmrMde.getFXPrice().getOfferFXRate().getRate() );
                log( "after reset audUsdMde.bidRate=" + usdInrMde.getFXPrice().getBidFXRate().getRate() + ",offerRate=" + usdInrMde.getFXPrice().getOfferFXRate().getRate() );

                // now find the cross rate.
                omrInrMde = staticMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertNotNull( omrInrMde );
                IdcDateTime effDate = omrInrMde.getEffDateTime();
                assertNotNull( effDate );
                fxPrc = omrInrMde.getFXPrice();
                double bidRate = fxPrc.getBidFXRate().getRate();
                double offerRate = fxPrc.getOfferFXRate().getRate();
                log( "omrInrMde.bidRate=" + bidRate + ",offerRate=" + offerRate );
                assertEquals( "bidRate should be less than offer rate", bidRate < offerRate, true );
                assertTrue( doSanityCheck( omrInrMde, omr.getIndex(), inr.getIndex() ) );

                // find the spot rate again after setting a different AUD/USD rate.
                usdInrMde.getFXPrice().getBidFXRate().setSpotRate( 0.99293 );
                usdInrMde.getFXPrice().getBidFXRate().setRate( 0.99293 );
                usdInrMde.getFXPrice().getBidFXRate().setForwardPoints( 0.0 );
                usdInrMde.getFXPrice().getOfferFXRate().setSpotRate( 0.99318 );
                usdInrMde.getFXPrice().getOfferFXRate().setRate( 0.99318 );
                usdInrMde.getFXPrice().getOfferFXRate().setForwardPoints( 0.0 );

                omrInrMde = staticMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertEquals( "Effective date time should be equal.", effDate, omrInrMde.getEffDateTime() );
                FXPrice newFXPrc = omrInrMde.getFXPrice();
                double newBidRate = newFXPrc.getBidFXRate().getRate();
                double newOfferRate = newFXPrc.getOfferFXRate().getRate();
                log( "omrInrMde.newBidRate=" + newBidRate + ",newOfferRate=" + newOfferRate );
                assertEquals( "new bidRate should be less than new offer rate", newBidRate < newOfferRate, true );
                assertEquals( "new bid rate should be different from old bid rate", newBidRate != bidRate, true );
                assertEquals( "new offer rate should be different from old offer rate", newOfferRate != offerRate, true );
                IdcDateTime newEffDate = omrInrMde.getEffectiveDateTime();
                assertEquals( "Effective date should be same.", effDate, newEffDate );
                assertTrue( doSanityCheck( omrInrMde, omr.getIndex(), inr.getIndex() ) );

                // now refresh teh market data set.
                sleepFor( 2000 );
                staticMds = ( FXMarketDataSet ) PersistenceFactory.newSession().refreshObject( staticMds );
                omrInrMde = staticMds.findSpotConversionMarketDataElement( omr, inr, true );
                FXPrice refreshedFXPrice = omrInrMde.getFXPrice();
                double refreshedBidRate = refreshedFXPrice.getBidFXRate().getRate();
                double refreshedOfferRate = refreshedFXPrice.getOfferFXRate().getRate();
                log( "omrInrMde.refreshedBidRate=" + refreshedBidRate + ",refreshedOfferRate=" + refreshedOfferRate );
                assertEquals( "refreshed bidRate should be less than refreshed offer rate", refreshedBidRate < refreshedOfferRate, true );
                assertEquals( "refreshed bid rate should be different from new bid rate", newBidRate != refreshedBidRate, true );
                assertEquals( "refreshed offer rate should be different from new offer rate", refreshedOfferRate != newOfferRate, true );
                IdcDateTime refreshedEffDate = omrInrMde.getEffectiveDateTime();
                assertEquals( "Effective date should be later than old", refreshedEffDate.isLaterThan( newEffDate ), true );
            }
        }
        catch ( Exception e )
        {
            fail( "testCrossCurrencyComputation", e );
        }
    }

    public void testCrossCurrencyComputationWithoutConvention()
    {
        try
        {
            Currency omr = CurrencyFactory.getCurrency( "OMR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency inr = CurrencyFactory.getCurrency( "INR" );

            FXMarketDataElement usdOmrMde = staticMds.findSpotConversionMarketDataElement( usd, omr, true );
            FXMarketDataElement usdInrMde = staticMds.findSpotConversionMarketDataElement( usd, inr, true );
            if ( usdOmrMde != null && usdInrMde != null )
            {
                log( "usdOmrMde.bidRate=" + usdOmrMde.getFXPrice().getBidFXRate().getRate() + ",offerRate=" + usdOmrMde.getFXPrice().getOfferFXRate().getRate() );
                log( "usdInrMde.bidRate=" + usdInrMde.getFXPrice().getBidFXRate().getRate() + ",offerRate=" + usdInrMde.getFXPrice().getOfferFXRate().getRate() );
                assertNotNull( usdOmrMde );
                assertNotNull( usdInrMde );
                assertTrue( doSanityCheck( usdOmrMde, usd.getIndex(), omr.getIndex() ) );
                assertTrue( doSanityCheck( usdInrMde, usd.getIndex(), inr.getIndex() ) );
                FXMarketDataElement omrInrMde = staticMds.getFXRate( CurrencyFactory.newCurrencyPair( omr, inr ), true, Tenor.SPOT_TENOR );
                assertNull( omrInrMde );

                omrInrMde = staticMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertNotNull( omrInrMde );
                FXPrice fxPrc = omrInrMde.getFXPrice();
                log( "omrInrMde.bidRate=" + fxPrc.getBidFXRate().getRate() + ",offerRate=" + fxPrc.getOfferFXRate().getRate() );
                assertTrue( doSanityCheck( omrInrMde, omr.getIndex(), inr.getIndex() ) );

                // the basic test is to remove the OMR/INR quote convention and see if it still gets an OMR/INR cross computed rate.
                FXRateBasis rb = stdQuoteConv.getFXRateBasis( "OMR/INR" );
                stdQuoteConv.getFXRateBasis().remove( rb );
                ( ( FXRateConventionC ) stdQuoteConv ).resetTransients();
                ( ( FXMarketDataSetC ) staticMds ).resetTransients();
                omrInrMde = staticMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertNull( omrInrMde );

                // now refresh teh market data set and convention
                sleepFor( 2000 );
                stdQuoteConv = ( FXRateConvention ) PersistenceFactory.newSession().refreshObject( stdQuoteConv );
                staticMds = ( FXMarketDataSet ) PersistenceFactory.newSession().refreshObject( staticMds );
                omrInrMde = staticMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertNotNull( omrInrMde );
            }
        }
        catch ( Exception e )
        {
            fail( "testCrossCurrencyComputationWithoutConvention", e );
        }
    }

    public void testMarketDataLookup()
    {
        try
        {
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency aud = CurrencyFactory.getCurrency( "AUD" );

            FXMarketDataElement gbpAudMde = staticMds.findSpotConversionMarketDataElement( gbp, aud, true );
            assertTrue( doSanityCheck( gbpAudMde, gbp.getIndex(), aud.getIndex() ) );
            FXPrice fxPrc = gbpAudMde.getFXPrice();
            double bidRate = fxPrc.getBidFXRate().getRate();
            double offerRate = fxPrc.getOfferFXRate().getRate();
            log( "gbpAudMde.bidRate=" + bidRate + ",offerRate=" + offerRate + ",gbpAud.base="
                    + fxPrc.getBidFXRate().getBaseCurrency() + ",inverted=" + fxPrc.getBidFXRate().isRateInverted() );
            assertEquals( "gbpAudMde should be direct rate.", fxPrc.getBidFXRate().isRateInverted(), false );

            FXMarketDataElement audGbpMde = staticMds.findSpotConversionMarketDataElement( aud, gbp, true );
            assertTrue( doSanityCheck( audGbpMde, gbp.getIndex(), aud.getIndex() ) );
            FXPrice fxPrc1 = audGbpMde.getFXPrice();
            double bidRate1 = fxPrc1.getBidFXRate().getRate();
            double offerRate1 = fxPrc1.getOfferFXRate().getRate();
            log( "audGbpMde.bidRate1=" + bidRate1 + ",offerRate1=" + offerRate1 + ",audGbp.base="
                    + fxPrc1.getBidFXRate().getBaseCurrency() + ",inverted=" + fxPrc1.getBidFXRate().isRateInverted() );
            assertEquals( "audGbpMde should be direct rate.", fxPrc1.getBidFXRate().isRateInverted(), false );


            FXMarketDataElement gbpUsdMde = staticMds.findSpotConversionMarketDataElement( gbp, usd, true );
            assertTrue( doSanityCheck( gbpUsdMde, gbp.getIndex(), usd.getIndex() ) );
            FXPrice fxPrc2 = gbpUsdMde.getFXPrice();
            double bidRate2 = fxPrc2.getBidFXRate().getRate();
            double offerRate2 = fxPrc2.getOfferFXRate().getRate();
            log( "gbpUsdMde.bidRate2=" + bidRate2 + ",offerRate2=" + offerRate2 );
            assertEquals( "gbpUsdMde should be direct rate.", fxPrc2.getBidFXRate().isRateInverted(), false );

            FXMarketDataElement usdGbpMde = staticMds.findSpotConversionMarketDataElement( usd, gbp, true );
            assertTrue( doSanityCheck( usdGbpMde, gbp.getIndex(), usd.getIndex() ) );
            FXPrice fxPrc3 = usdGbpMde.getFXPrice();
            double bidRate3 = fxPrc3.getBidFXRate().getRate();
            double offerRate3 = fxPrc3.getOfferFXRate().getRate();
            log( "usdGbpMde.bidRate3=" + bidRate3 + ",offerRate3=" + offerRate3 );
            assertEquals( "usdGbpMde should be direct rate.", fxPrc3.getBidFXRate().isRateInverted(), false );

            FXMarketDataElement gbpAudMde1 = staticMds.findSpotConversionMarketDataElement( gbp, aud, false );
            assertTrue( doSanityCheck( gbpAudMde1, gbp.getIndex(), aud.getIndex() ) );
            FXPrice fxPrc4 = gbpAudMde1.getFXPrice();
            double bidRate4 = fxPrc4.getBidFXRate().getRate();
            double offerRate4 = fxPrc4.getOfferFXRate().getRate();
            log( "gbpAudMde1.bidRate4=" + bidRate4 + ",offerRate4=" + offerRate4 + ",gbpAud.base="
                    + fxPrc4.getBidFXRate().getBaseCurrency() + ",inverted=" + fxPrc4.getBidFXRate().isRateInverted() );
            assertEquals( "gbpAudMde1should be direct rate.", fxPrc4.getBidFXRate().isRateInverted(), false );

            FXMarketDataElement audGbpMde1 = staticMds.findSpotConversionMarketDataElement( aud, gbp, false );
            assertTrue( doSanityCheck( audGbpMde1, gbp.getIndex(), aud.getIndex() ) );
            FXPrice fxPrc5 = audGbpMde1.getFXPrice();
            double bidRate5 = fxPrc5.getBidFXRate().getRate();
            double offerRate5 = fxPrc5.getOfferFXRate().getRate();
            log( "audGbpMde1.bidRate5=" + bidRate5 + ",offerRate5=" + offerRate5 + ",audGbp.base="
                    + fxPrc5.getBidFXRate().getBaseCurrency() + ",inverted=" + fxPrc5.getBidFXRate().isRateInverted() );
            assertEquals( "audGbpMde1 should be direct rate.", fxPrc5.getBidFXRate().isRateInverted(), false );


            FXMarketDataElement gbpUsdMde1 = staticMds.findSpotConversionMarketDataElement( gbp, usd, false );
            assertTrue( doSanityCheck( gbpUsdMde1, gbp.getIndex(), usd.getIndex() ) );
            FXPrice fxPrc6 = gbpUsdMde1.getFXPrice();
            double bidRate6 = fxPrc6.getBidFXRate().getRate();
            double offerRate6 = fxPrc6.getOfferFXRate().getRate();
            log( "gbpUsdMde1.bidRate6=" + bidRate6 + ",offerRate6=" + offerRate6 );
            assertEquals( "gbpUsdMde1 should be direct rate.", fxPrc6.getBidFXRate().isRateInverted(), false );

            FXMarketDataElement usdGbpMde1 = staticMds.findSpotConversionMarketDataElement( usd, gbp, false );
            assertNull( usdGbpMde1 );

            usdGbpMde1 = staticMds.findSpotConversionMarketDataElement( usd, gbp, true );
            assertTrue( doSanityCheck( usdGbpMde1, gbp.getIndex(), usd.getIndex() ) );
            FXPrice fxPrc7 = usdGbpMde1.getFXPrice();
            double bidRate7 = fxPrc7.getBidFXRate().getRate();
            double offerRate7 = fxPrc7.getOfferFXRate().getRate();
            log( "usdGbpMde1.bidRate7=" + bidRate7 + ",offerRate7=" + offerRate7 );
            assertEquals( "usdGbpMde1 should be direct rate.", fxPrc7.getBidFXRate().isRateInverted(), false );
        }
        catch ( Exception e )
        {
            fail( "testMarketDataLookup", e );
        }
    }

    public void testSpotRateLookupFromExtendedMds()
    {
        try
        {
            Currency omr = CurrencyFactory.getCurrency( "OMR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency inr = CurrencyFactory.getCurrency( "INR" );

            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet();
            fxMds.setCrossCurrency( usd );
            fxMds.setAutoInterpolate( true );
            fxMds.setShortName( "TEST" + System.currentTimeMillis() );
            fxMds.setExtendedMarketDataSet ( staticMds );

            FXMarketDataElement usdOmrMde = fxMds.findSpotConversionMarketDataElement( usd, omr, true );
            FXMarketDataElement usdInrMde = fxMds.findSpotConversionMarketDataElement( usd, inr, true );
            if ( usdOmrMde != null && usdInrMde != null )
            {
                log( "usdOmrMde.bidRate=" + usdOmrMde.getFXPrice().getBidFXRate().getRate() + ",offerRate=" + usdOmrMde.getFXPrice().getOfferFXRate().getRate() );
                log( "usdInrMde.bidRate=" + usdInrMde.getFXPrice().getBidFXRate().getRate() + ",offerRate=" + usdInrMde.getFXPrice().getOfferFXRate().getRate() );
                assertNotNull( usdOmrMde );
                assertNotNull( usdInrMde );
                assertTrue( doSanityCheck( usdOmrMde, usd.getIndex(), omr.getIndex() ) );
                assertTrue( doSanityCheck( usdInrMde, usd.getIndex(), inr.getIndex() ) );
                FXMarketDataElement omrInrMde = fxMds.getFXRate( CurrencyFactory.newCurrencyPair( omr, inr ), true, Tenor.SPOT_TENOR );
                assertNull( omrInrMde );

                omrInrMde = fxMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertNotNull( omrInrMde );
                FXPrice fxPrc = omrInrMde.getFXPrice();
                log( "omrInrMde.bidRate=" + fxPrc.getBidFXRate().getRate() + ",offerRate=" + fxPrc.getOfferFXRate().getRate() );
                assertTrue( doSanityCheck( omrInrMde, omr.getIndex(), inr.getIndex() ) );

                // the basic test is to remove the OMR/INR quote convention and see if it still gets an OMR/INR cross computed rate.
                FXRateBasis rb = stdQuoteConv.getFXRateBasis( "OMR/INR" );
                stdQuoteConv.getFXRateBasis().remove( rb );
                ( ( FXRateConventionC ) stdQuoteConv ).resetTransients();
                ( ( FXMarketDataSetC ) fxMds ).resetTransients();
                omrInrMde = fxMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertNull( omrInrMde );

                // now refresh teh market data set and convention
                sleepFor( 2000 );
                stdQuoteConv = ( FXRateConvention ) PersistenceFactory.newSession().refreshObject( stdQuoteConv );
                staticMds = ( FXMarketDataSet ) PersistenceFactory.newSession().refreshObject( staticMds );
                omrInrMde = fxMds.findSpotConversionMarketDataElement( omr, inr, true );
                assertNotNull( omrInrMde );
            }
        }
        catch ( Exception e )
        {
            fail ( "testSpotRateLookupFromExtendedMds", e );
        }
    }

    private boolean doSanityCheck( FXMarketDataElement fxMde, int index1, int index2 )
    {
        try
        {
            Currency base = fxMde.getCurrencyPair().getBaseCurrency();
            Currency var = fxMde.getCurrencyPair().getVariableCurrency();
            if ( base.getIndex() != index1 || var.getIndex() != index2 )
            {
                log.error( "Index mismatch on base=" + base + ",var=" + var );
                return false;
            }
            FXRate mid = fxMde.getFXPrice().getMidFXRate();
            if ( mid != null )
            {
                if ( !mid.getBaseCurrency().isSameAs( base ) || !mid.getVariableCurrency().isSameAs( var ) )
                {
                    log.error( "mid base/var mismatch. base=" + base + ",var=" + var + ",mid.base=" + mid.getBaseCurrency() + ",mid.var=" + mid.getVariableCurrency() );
                    return false;
                }
            }
            FXRate bid = fxMde.getFXPrice().getBidFXRate();
            if ( bid != null )
            {
                if ( !bid.getBaseCurrency().isSameAs( base ) || !bid.getVariableCurrency().isSameAs( var ) )
                {
                    log.error( "bid base/var mismatch. base=" + base + ",var=" + var + ",bid.base=" + bid.getBaseCurrency() + ",bid.var=" + bid.getVariableCurrency() );
                    return false;
                }
            }
            FXRate offer = fxMde.getFXPrice().getOfferFXRate();
            if ( offer != null )
            {
                if ( !offer.getBaseCurrency().isSameAs( base ) || !offer.getVariableCurrency().isSameAs( var ) )
                {
                    log.error( "offer base/var mismatch. base=" + base + ",var=" + var + ",offer.base=" + offer.getBaseCurrency() + ",offer.var=" + offer.getVariableCurrency() );
                    return false;
                }
            }

            FXRate computedMid = fxMde.getFXPrice().getComputedMidFXRate();
            if ( computedMid != null )
            {
                if ( !computedMid.getBaseCurrency().isSameAs( base ) || !computedMid.getVariableCurrency().isSameAs( var ) )
                {
                    log.error( "computed mid base/var mismatch. base=" + base + ",var=" + var + ",computedMid.base=" + computedMid.getBaseCurrency() + ",computedMid.var=" + computedMid.getVariableCurrency() );
                    return false;
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "FXMarketDataCalculatorPTestC.doSanityCheck.ERROR : Error while sanity check. ", e );
            return false;
        }
        return true;
    }
}
