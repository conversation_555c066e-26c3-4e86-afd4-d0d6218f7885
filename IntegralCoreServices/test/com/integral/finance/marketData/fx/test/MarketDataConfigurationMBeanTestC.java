package com.integral.finance.marketData.fx.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.marketData.configuration.MarketDataConfiguration;
import com.integral.finance.marketData.configuration.MarketDataConfigurationFactory;
import com.integral.finance.marketData.configuration.MarketDataConfigurationMBean;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

/**
 * <AUTHOR> Development Corporation.
 */
public class MarketDataConfigurationMBeanTestC extends MBeanTestCaseC
{
    MarketDataConfiguration mktDataMBean = ( MarketDataConfiguration ) MarketDataConfigurationFactory.getMarketDataConfigurationMBean();

    public MarketDataConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( mktDataMBean, "endOfDayStaticMarketDataSetUpdateEnabled", MarketDataConfigurationMBean.END_OF_DAY_STATIC_MARKET_DATA_UPDATE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( mktDataMBean, "externalRealTimeMarketData", MarketDataConfigurationMBean.EXTERNAL_REAL_TIME_MARKET_DATA, MBeanTestCaseC.BOOLEAN );
        testProperty( mktDataMBean, "staticMarketDataSet", MarketDataConfigurationMBean.STATIC_MARKET_DATASET, MBeanTestCaseC.STRING );
        testProperty( mktDataMBean, "sourceMarketDataSetForStaticMarketDataUpdate", MarketDataConfigurationMBean.SOURCE_MARKET_DATASET, MBeanTestCaseC.STRING );
        testProperty( mktDataMBean, "externalMarketDataUrl", MarketDataConfigurationMBean.EXTERNAL_MARKET_DATA_URL, MBeanTestCaseC.STRING );
        testProperty( mktDataMBean, "endOfDayMarketDataSet", MarketDataConfigurationMBean.EOD_MARKET_DATASET, MBeanTestCaseC.STRING );
        testProperty( mktDataMBean, "endOfDayMarketDataSetBusinessCetner", MarketDataConfigurationMBean.EOD_MARKET_DATASET_BUSINESS_CENTER, MBeanTestCaseC.STRING );
        testProperty( mktDataMBean, "endOfDayMarketDataSetRateConvention", MarketDataConfigurationMBean.EOD_MARKET_DATASET_RATE_CONVENTION, MBeanTestCaseC.STRING );
        testProperty( mktDataMBean, "endOfDayMarketDataSetUpdateEnabled", MarketDataConfigurationMBean.END_OF_DAY_MARKET_DATA_UPDATE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( mktDataMBean, "referenceForwardRateAggregationStandardTenors", MarketDataConfigurationMBean.REFERENCE_FORWARD_RATE_AGGREGATION_STANDARD_TENORS, MBeanTestCaseC.STRING );
        testProperty( mktDataMBean, "referenceForwardRateAggregationStalePeriod", MarketDataConfigurationMBean.REFERENCE_FORWARD_RATE_AGGREGATION_STALE_PERIOD, MBeanTestCaseC.LONG );
        testProperty( mktDataMBean, "externalMarketDataHttpConnectionUserAgentPropertyEnabled", MarketDataConfigurationMBean.EXTERNAL_MARKET_DATA_HTTP_CONNECTION_USER_AGENT_PROPERTY_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( mktDataMBean, "realtimeMDSListenerThreadPoolEnabled", MarketDataConfigurationMBean.REALTIME_MDS_LISTENER_THREADPOOL_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( mktDataMBean, "realtimeMDSListenerThreadPoolSize", MarketDataConfigurationMBean.REALTIME_MDS_LISTENER_THREADPOOL_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( mktDataMBean, "allRealtimeMDSListenerLoggingEnabled", MarketDataConfigurationMBean.ALL_REALTIME_MDS_LISTENER_LOGGING_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( mktDataMBean, "monitoringEnabledMarketDataSets", MarketDataConfigurationMBean.MONITORING_ENABLED_MDS_LIST, MBeanTestCaseC.COLLECTION_CLASS );
        testProperty( mktDataMBean, "resetSpotElementForwardPointsEnabled", MarketDataConfigurationMBean.MARKET_DATASET_RESET_SPOT_ELEMENT_FORWARD_POINTS_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( mktDataMBean, "fixedPeriodMarketDataSetCacheEnabled", MarketDataConfigurationMBean.FIXED_PERIOD_MARKET_DATASET_CACHE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( mktDataMBean, "endOfDayMarketDataFetcherConnectionTimeout", MarketDataConfigurationMBean.EOD_MARKET_DATASET_EXTRACTION_FETCHER_CONNECTION_TIMEOUT, MBeanTestCaseC.INTEGER );
        testProperty( mktDataMBean, "endOfDayMarketDataFetcherReadTimeout", MarketDataConfigurationMBean.EOD_MARKET_DATASET_EXTRACTION_FETCHER_READ_TIMEOUT, MBeanTestCaseC.INTEGER );
    }

    public void testRealtimeMDSStaleElementsRemoveEnabled()
    {
        try
        {
            assertTrue(mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(null));
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertTrue(mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org1));
            assertTrue(mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org2));

            // now set the global property
            mktDataMBean.setProperty( MarketDataConfigurationMBean.REALTIME_STALE_ELEMENTS_REMOVE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse(mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org1));
            assertFalse(mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org2));
            assertFalse(mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(null));

            mktDataMBean.setProperty( MarketDataConfigurationMBean.REALTIME_STALE_ELEMENTS_REMOVE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue(mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org1));
            assertTrue(mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org2));
            assertTrue( mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(null) );


            mktDataMBean.setProperty( MarketDataConfigurationMBean.REALTIME_STALE_ELEMENTS_REMOVE_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            mktDataMBean.setProperty( MarketDataConfigurationMBean.REALTIME_STALE_ELEMENTS_REMOVE_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org1) );
            assertFalse( mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org2) );

            mktDataMBean.setProperty( MarketDataConfigurationMBean.REALTIME_STALE_ELEMENTS_REMOVE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org1) );
            assertFalse( mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(org2) );
            assertFalse( mktDataMBean.isRealtimeMDSStaleElementsRemoveEnabled(null) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testRealtimeMDSStaleElementsRemoveEnabled" );
        }
    }

    public void testForwardPointsDefaultVehicleCurrency()
    {
        try
        {
            Currency ccy = CurrencyFactory.getCurrency ( "USD" );
            Currency ccy1 = CurrencyFactory.getCurrency ( "GBP" );
            Currency ccy2 = CurrencyFactory.getCurrency ( "CAD" );
            Currency ccy3 = CurrencyFactory.getCurrency ( "CHF" );
            assertNotNull ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency (null ) );
            assertTrue ( ccy.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency (null ) ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertTrue ( ccy.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency (org1 ) ) );
            assertTrue ( ccy.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency (org2 ) ) );

            // now set the global property
            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( ccy.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( null ) ) );
            assertTrue ( ccy.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( org1 ) ) );
            assertTrue ( ccy.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( org2 ) ) );

            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "CHF", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( ccy3.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( null ) ) );
            assertTrue ( ccy3.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( org1 ) ) );
            assertTrue ( ccy3.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( org2 ) ) );


            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY_PREFIX+ "FI1", "GBP", ConfigurationProperty.DYNAMIC_SCOPE, null );
            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY_PREFIX + "FI2", "CAD", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( ccy3.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( null ) ) );
            assertTrue ( ccy1.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( org1 ) ) );
            assertTrue ( ccy2.isSameAs ( mktDataMBean.getMarketDataSetForwardPointsDefaultVehicleCurrency ( org2 ) ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testForwardPointsDefaultVehicleCurrency" );
        }
    }

    public void testMarketDataForwardPointsExtrapolationEnabled()
    {
        try
        {
            assertFalse(mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled (null));
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse(mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org1));
            assertFalse(mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org2));

            // now set the global property
            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse(mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org1));
            assertFalse(mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org2));
            assertFalse(mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(null));

            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue(mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org1));
            assertTrue(mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org2));
            assertTrue( mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(null) );


            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org1) );
            assertFalse( mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org2) );

            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org1) );
            assertFalse( mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(org2) );
            assertFalse( mktDataMBean.isMarketDataForwardPointsExtrapolationEnabled(null) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testMarketDataForwardPointsExtrapolationEnabled" );
        }
    }

    public void testMarketDataSetAPIBrokenDateFormat()
    {
        try
        {
            String df = MarketDataConfigurationMBean.MARKET_DATA_SET_API_BROKEN_DATE_FORMAT_DEFAULT;
            assertNotNull ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat (null ) );
            assertTrue ( df.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat (null ) ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertTrue ( df.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat (org1.getDefaultDealingUser () ) ) );
            assertTrue ( df.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat (org2.getDefaultDealingUser () ) ) );

            // now set the global property
            String df1 = "dd/MM/yyyy";
            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_API_BROKEN_DATE_FORMAT, df1, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( df1.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat ( null ) ) );
            assertTrue ( df1.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat ( org1.getDefaultDealingUser () ) ) );
            assertTrue ( df1.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat ( org2.getDefaultDealingUser () ) ) );

            String df2 = "yyyy/MM/dd";
            String df3 = "dd-MMM-yyyy";
            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_API_BROKEN_DATE_FORMAT_PREFIX + org1.getDefaultDealingUser ().getFullyQualifiedName (), df2, ConfigurationProperty.DYNAMIC_SCOPE, null );
            mktDataMBean.setProperty( MarketDataConfigurationMBean.MARKET_DATA_SET_API_BROKEN_DATE_FORMAT_PREFIX + org2.getDefaultDealingUser ().getFullyQualifiedName (), df3, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( df1.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat ( null ) ) );
            assertTrue ( df2.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat ( org1.getDefaultDealingUser () ) ) );
            assertTrue ( df3.equals ( mktDataMBean.getMarketDataSetAPIBrokenDateFormat ( org2.getDefaultDealingUser () ) ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testMarketDataSetAPIBrokenDateFormat" );
        }
    }
}
