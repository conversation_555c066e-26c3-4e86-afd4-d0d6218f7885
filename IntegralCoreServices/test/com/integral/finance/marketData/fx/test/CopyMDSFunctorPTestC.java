package com.integral.finance.marketData.fx.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.marketData.fx.*;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;


/**
 * Tests market data set lookups and market data set updating as an end of day process.
 *
 * <AUTHOR> Development Corp.
 */
public class CopyMDSFunctorPTestC extends PTestCaseC
{
    static String name = "CopyMDSFunctorPTestC";
    FXRateConvention stdConv = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();

    public CopyMDSFunctorPTestC ( String name )
    {
        super ( name );
    }

    public void testStaticMDSSimpleCopy ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "1w" ) );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, testTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask1 );
            assertNotNull ( fxMde );
            assertNull ( fxMde.getBrokenDate () );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde2 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask2 );
            assertNotNull ( fxMde2 );
            assertNull ( fxMde2.getBrokenDate () );
        }
        catch ( Exception e )
        {
            fail ( "testStaticMDSSimpleCopy", e );
        }
    }

    public void testStaticMDSSimpleCopyWithBrokenDates ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "1w" ) );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, DateTimeFactory.newDate ().addDays ( 5 ) );
            createAndAddMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "2w" ) );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 4, testTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask1 );
            assertNotNull ( fxMde );
            assertNull ( fxMde.getBrokenDate () );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde2 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask2 );
            assertNotNull ( fxMde2 );
            assertNotNull ( fxMde2.getBrokenDate () );
        }
        catch ( Exception e )
        {
            fail ( "testStaticMDSSimpleCopyWithBrokenDates", e );
        }
    }

    public void testStaticMDSSimpleCcyPairCopy ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "1W" ) );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.setCurrencyPairNames ( "EUR/USD" );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 1, testTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask1 ) );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            assertNull ( testTargetMds.getMarketDataElement ( mask2 ) );
        }
        catch ( Exception e )
        {
            fail ( "testStaticMDSSimpleCcyPairCopy", e );
        }
    }

    public void testStaticMDSSimpleCcyPairsCopy ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "1w" ) );
            addFXMarketDataElement ( registeredTestSourceMds, eur, jpy, 121.11, 0.0, 122.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, eur, jpy, 121.11, 0.0, 122.12, 0.0, false, new Tenor ( "1Y" ) );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.setCurrencyPairNames ( "EUR/USD,USD/JPY" );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, testTargetMds.getMarketDataElements ().size () );

            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask1 ) );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask2 ) );

            FXMask mask3 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, jpy ), true, Tenor.SPOT_TENOR );
            assertNull ( testTargetMds.getMarketDataElement ( mask3 ) );
        }
        catch ( Exception e )
        {
            fail ( "testStaticMDSSimpleCcyPairsCopy", e );
        }
    }

    public void testStaticMDSCopyTargetUpdateMde ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, eur, jpy, 121.11, 0.0, 122.12, 0.0, false, Tenor.SPOT_TENOR );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestTargetMds, eur, usd, 2.2345, 0.0, 2.2367, 0.0, false, Tenor.SPOT_TENOR );

            uow.commit ();

            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.setCurrencyPairNames ( "EUR/USD,USD/JPY" );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 2, testTargetMds.getMarketDataElements ().size () );

            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask1 );
            assertNotNull ( fxMde );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getRate () < 2.0 );
            assertTrue ( fxMde.getFXPrice ().getOfferFXRate ().getRate () < 2.0 );
        }
        catch ( Exception e )
        {
            fail ( "testStaticMDSCopyTargetUpdateMde", e );
        }
    }

    public void testStaticMDSCopyAdditionalTargetMdes ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestTargetMds, eur, jpy, 121.11, 0.0, 122.22, 0.0, false, Tenor.SPOT_TENOR );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, testTargetMds.getMarketDataElements ().size () );

            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask1 );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testStaticMDSCopyAdditionalTargetMdes", e );
        }
    }

    public void testRealtimeToStaticMDSCopy ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );
            registeredTestSourceMds.setRealtime ( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "1w" ) );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, testTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask1 ) );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask2 ) );
        }
        catch ( Exception e )
        {
            fail ( "testRealtimeToStaticMDSCopy", e );
        }
    }

    public void testStaticToRealtimeMDSCopy ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "1w" ) );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );
            registeredTestTargetMds.setRealtime ( true );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            FXMarketDataSet realtimeTargetMds = ( FXMarketDataSet ) testTargetMds.getSessionInstance ();
            assertFalse ( realtimeTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, realtimeTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask1 ) );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask2 ) );
        }
        catch ( Exception e )
        {
            fail ( "testStaticToRealtimeMDSCopy", e );
        }
    }

    public void testRealtimeToStaticMDSCopyWithBrokenDates ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );
            registeredTestSourceMds.setRealtime ( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setStatus ( 'T' );

            uow.commit ();

            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, DateTimeFactory.newDate ().addDays ( 15 ) );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, DateTimeFactory.newDate ().addDays ( 15 ) );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, testTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask1 );
            assertNotNull ( fxMde );
            assertNotNull ( fxMde.getBrokenDate () );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde2 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask2 );
            assertNotNull ( fxMde2 );
            assertNull ( fxMde2.getBrokenDate () );
        }
        catch ( Exception e )
        {
            fail ( "testRealtimeToStaticMDSCopyWithBrokenDates", e );
        }
    }


    public void testRealtimeToRealtimeMDSCopy ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );
            registeredTestSourceMds.setRealtime ( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );
            registeredTestTargetMds.setRealtime ( true );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "1w" ) );


            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            FXMarketDataSet realtimeTargetMds = ( FXMarketDataSet ) testTargetMds.getSessionInstance ();
            assertFalse ( realtimeTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, realtimeTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask1 ) );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask2 ) );
        }
        catch ( Exception e )
        {
            fail ( "testRealtimeToRealtimeMDSCopy", e );
        }
    }

    public void testRealtimeToRealtimeMDSCopyWithBrokenDates ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );
            registeredTestSourceMds.setRealtime ( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );
            registeredTestTargetMds.setRealtime ( true );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, DateTimeFactory.newDate ().addDays ( 5 ) );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, DateTimeFactory.newDate ().addDays ( 15 ) );


            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            FXMarketDataSet realtimeTargetMds = ( FXMarketDataSet ) testTargetMds.getSessionInstance ();
            assertFalse ( realtimeTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, realtimeTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask1 );
            assertNotNull ( fxMde );
            assertNotNull ( fxMde.getBrokenDate () );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde2 = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask2 );
            assertNotNull ( fxMde2 );
            assertNull ( fxMde2.getBrokenDate () );
        }
        catch ( Exception e )
        {
            fail ( "testRealtimeToRealtimeMDSCopyWithBrokenDates", e );
        }
    }

    public void testRealtimeToStaticMDSCopyWithNonZeroFwdPoints ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );
            registeredTestSourceMds.setRealtime ( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.6, 112.12, 0.7, false, new Tenor ( "2w" ) );
            addFXMarketDataElement ( ( FXMarketDataSet ) testSourceMds.getSessionInstance (), usd, jpy, 111.11, 0.6, 112.12, 0.7, false, new Tenor ( "1w" ) );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 4, testTargetMds.getMarketDataElements ().size () );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask1 ) );

            FXMask mask2 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( usd, jpy ), true, Tenor.SPOT_TENOR );
            assertNotNull ( testTargetMds.getMarketDataElement ( mask2 ) );
            for ( FXMarketDataElement fxMde : ( Collection<FXMarketDataElement> ) testTargetMds.getMarketDataElements () )
            {
                if ( fxMde.getTenor ().isSpot () )
                {
                    assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () == 0.0 );
                    assertTrue ( fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () == 0.0 );
                }
                else
                {
                    assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () != 0.0 );
                    assertTrue ( fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () != 0.0 );
                }
                assertNotNull ( fxMde.getFXPrice ().getBidFXRate ().getFXRateConvention () );
                assertNotNull ( fxMde.getFXPrice ().getOfferFXRate ().getFXRateConvention () );
            }
        }
        catch ( Exception e )
        {
            fail ( "testRealtimeToStaticMDSCopyWithNonZeroFwdPoints", e );
        }
    }

    public void testRealtimeToRealtimeMDSCopyMidOnly ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setStatus ( 'T' );
            registeredTestSourceMds.setRealtime ( true );

            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setStatus ( 'T' );
            registeredTestTargetMds.setRealtime ( true );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            // add synonymous tenors
            FXMarketDataElement mde1 = createFXMarketDataElement ( usd, jpy, 111.11, 0.0, 112.12, 0.0, Tenor.SPOT_TENOR, true, null );
            mde1.getFXPrice ().setBidFXRate ( null );
            mde1.getFXPrice ().setOfferFXRate ( null );
            testSourceMds.getSessionInstance ().addMarketDataElement ( mde1 );

            FXMarketDataElement mde2 = createFXMarketDataElement ( usd, jpy, 111.11, 0.0, 112.12, 0.0, new Tenor ( "ON" ), true, null );
            mde2.getFXPrice ().setBidFXRate ( null );
            mde2.getFXPrice ().setOfferFXRate ( null );
            testSourceMds.getSessionInstance ().addMarketDataElement ( mde2 );

            // add elements in the source mds
            addFXMarketDataElement ( ( FXMarketDataSet ) testTargetMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( ( FXMarketDataSet ) testTargetMds.getSessionInstance (), usd, jpy, 111.11, 0.0, 112.12, 0.0, false, new Tenor ( "TOD" ) );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );

            functor.execute ( null );
            functor.execute ( null );

            Thread.sleep ( 1000 );

            FXMarketDataSet realtimeTargetMds = ( FXMarketDataSet ) testTargetMds.getSessionInstance ();
            assertFalse ( realtimeTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 2, realtimeTargetMds.getMarketDataElements ().size () );
        }
        catch ( Exception e )
        {
            fail ( "testRealtimeToRealtimeMDSCopyMidOnly", e );
        }
    }

    private void addFXMarketDataElement ( FXMarketDataSet mds, FXMarketDataElement mde )
    {
        if ( mds.isRealtime () )
        {
            mds.getSessionInstance ().addMarketDataElement ( mde );
        }
        else
        {
            mds.addMarketDataElement ( mde );
        }
        mde.setMarketDataSet ( mds );
    }

    private void addFXMarketDataElement ( FXMarketDataSet mds, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, boolean createMid, Tenor tenor )
    {
        CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair ( base, var );
        if ( tenor.isSpot () )
        {
            mds.updateFXMarketDataElement ( ccyPair, tenor.getName (), null, bidSpotRate, offerSpotRate, null, System.currentTimeMillis () );
        }
        else
        {
            mds.updateFXMarketDataElement ( ccyPair, tenor.getName (), null, bidFwdPoints, offerFwdPoints, null, System.currentTimeMillis () );
        }
    }

    private void createAndAddMarketDataElement ( FXMarketDataSet mds, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, boolean createMid, Tenor tenor )
    {
        FXMarketDataElement mde1 = createFXMarketDataElement ( base, var, bidSpotRate, bidFwdPoints, offerSpotRate, offerFwdPoints, tenor, createMid, null );
        addFXMarketDataElement ( mds, mde1 );
    }

    private void addFXMarketDataElement ( FXMarketDataSet mds, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, boolean createMid, IdcDate valueDate )
    {
        FXMarketDataElement mde1 = createFXMarketDataElement ( base, var, bidSpotRate, bidFwdPoints, offerSpotRate, offerFwdPoints, null, createMid, valueDate );
        addFXMarketDataElement ( mds, mde1 );
    }


    private FXMarketDataElement createFXMarketDataElement ( Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, Tenor tenor, boolean createMid, IdcDate valueDate )
    {
        FXMarketDataElement mde1 = FXMarketDataFactory.newFXMarketDataElement ();
        FXPrice fxPrice = FXPriceFactory.newFXPrice ();
        mde1.setFXPrice ( fxPrice );
        mde1.setTenor ( tenor );
        mde1.setValueDate ( valueDate );
        FXRate bid = FXFactory.newFXRate ();
        bid.setFXRateConvention ( stdConv );
        bid.setBaseCurrency ( base );
        bid.setVariableCurrency ( var );
        bid.setSpotRate ( bidSpotRate );
        bid.setForwardPoints ( bidFwdPoints );
        fxPrice.setBidFXRate ( bid );
        FXRate offer = FXFactory.newFXRate ();
        offer.setFXRateConvention ( stdConv );
        offer.setBaseCurrency ( base );
        offer.setVariableCurrency ( var );
        offer.setSpotRate ( offerSpotRate );
        offer.setForwardPoints ( offerFwdPoints );
        fxPrice.setOfferFXRate ( offer );
        if ( createMid )
        {
            FXRate computedMid = fxPrice.getComputedMidFXRate ();
            FXRate mid = FXFactory.newFXRate ();
            mid.setFXRateConvention ( stdConv );
            mid.setBaseCurrency ( base );
            mid.setVariableCurrency ( var );
            mid.setSpotRate ( computedMid.getSpotRate () );
            mid.setForwardPoints ( computedMid.getForwardPoints () );
            fxPrice.setMidFXRate ( mid );
        }
        String shortName = new StringBuffer ( base.getShortName () ).append ( '&' ).append ( var.getShortName () ).toString ();
        mde1.setShortName ( shortName );
        return mde1;
    }

    public void testStaticMDSCopySkipUpdateExistingCurrencyPairs ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXMarketDataSet testSourceMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestSourceMds = ( FXMarketDataSet ) uow.registerObject ( testSourceMds );
            String sourceMdsName = "TestSource" + System.currentTimeMillis ();
            registeredTestSourceMds.setShortName ( sourceMdsName );
            registeredTestSourceMds.setFXRateConvention ( stdConv );
            registeredTestSourceMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestSourceMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestSourceMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false, Tenor.SPOT_TENOR );


            FXMarketDataSet testTargetMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestTargetMds = ( FXMarketDataSet ) uow.registerObject ( testTargetMds );
            String targetMdsName = "TestTarget" + System.currentTimeMillis ();
            registeredTestTargetMds.setShortName ( targetMdsName );
            registeredTestTargetMds.setFXRateConvention ( stdConv );
            registeredTestTargetMds.setStatus ( 'T' );

            addFXMarketDataElement ( registeredTestTargetMds, eur, usd, 1.1111, 0.0, 1.2222, 0.0, false, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( registeredTestTargetMds, eur, jpy, 121.11, 0.0, 122.22, 0.0, false, Tenor.SPOT_TENOR );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testSourceMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testSourceMds );
            assertNotNull ( testSourceMds );
            testTargetMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testTargetMds );
            assertNotNull ( testTargetMds );

            CopyMDSFunctorC functor = new CopyMDSFunctorC ();
            functor.setSourceMDSName ( sourceMdsName );
            functor.setSinkMDSName ( targetMdsName );
            functor.setSkipUpdate ( "T" );
            functor.execute ( null );

            assertFalse ( testTargetMds.getMarketDataElements ().isEmpty () );
            assertEquals ( 3, testTargetMds.getMarketDataElements ().size () );

            FXMask mask1 = FXMarketDataFactory.newFXMask ( CurrencyFactory.getCurrencyPair ( eur, usd ), true, Tenor.SPOT_TENOR );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) testTargetMds.getMarketDataElement ( mask1 );
            assertNotNull ( fxMde );
            assertEquals ( "EUR/USD bid rate unmodified. bidRate=" + fxMde.getFXPrice ().getBidFXRate ().getSpotRate (), 1.1111, fxMde.getFXPrice ().getBidFXRate ().getSpotRate () );
            assertEquals ( "EUR/USD offer rate unmodified. offerRate=" + fxMde.getFXPrice ().getOfferFXRate ().getSpotRate (), 1.2222, fxMde.getFXPrice ().getOfferFXRate ().getSpotRate () );
        }
        catch ( Exception e )
        {
            fail ( "testStaticMDSCopySkipUpdateExistingCurrencyPairs", e );
        }
    }
}
