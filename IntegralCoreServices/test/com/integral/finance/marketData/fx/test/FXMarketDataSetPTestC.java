package com.integral.finance.marketData.fx.test;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.alert.AlertLoggerFactory;
import com.integral.alert.impl.ScreenAlertLogger;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.calculator.CalculatorFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateBasisC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.marketData.MarketDataElement;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.marketData.configuration.MarketDataConfigurationFactory;
import com.integral.finance.marketData.configuration.MarketDataConfigurationMBean;
import com.integral.finance.marketData.fx.*;
import com.integral.finance.marketData.fx.calculator.ExternalMarketDataExtractionCalculator;
import com.integral.finance.marketData.fx.calculator.ExternalMarketDataExtractionCalculatorDelegator;
import com.integral.finance.marketData.model.FXMDSUpdateEvent;
import com.integral.finance.marketData.model.MDSUpdateEvent;
import com.integral.finance.marketData.service.MDSUpdateHelper;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TenorFactory;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.persistence.EntityServiceFactory;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.DataPreloaderC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ObjectLevelReadQuery;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Time;
import java.sql.Timestamp;
import java.util.*;

/**
 * Tests market data set lookups and market data set updating as an end of day process.
 *
 * <AUTHOR> Development Corp.
 */
public class FXMarketDataSetPTestC extends PTestCaseC
{
    static String name = "FX market data set Test";
    MessageHandler mktDataHandler = new FXMarketDataSetUpdateHandlerC ();
    FXMarketDataSet staticMds = ( FXMarketDataSet ) new ReadNamedEntityC ().execute ( FXMarketDataSet.class, MarketDataConfigurationFactory.getMarketDataConfigurationMBean ().getStaticMarketDataSet () );
    FXMarketDataSet realtimeMds = ( FXMarketDataSet ) new ReadNamedEntityC ().execute ( FXMarketDataSet.class, MarketDataConfigurationFactory.getMarketDataConfigurationMBean ().getSourceMarketDataSetForStaticMarketDataUpdate () );
    FXRateConvention stdConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
    protected String adminUserName = "Integral";
    User adminUser = ( User ) new ReadNamedEntityC ().execute ( User.class, adminUserName );


    public FXMarketDataSetPTestC ( String name )
    {
        super ( name );
    }

    public void testFieldsPersistence ( )
    {
        try
        {
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            FXMarketDataSet testMds = new FXMarketDataSetC ();
            assertTrue ( testMds.isInterpolate () );
            assertFalse ( testMds.isAbsoluteBaseDate () );
            assertFalse ( testMds.isFixedPeriod () );

            FXMarketDataSet registeredTestMds = ( FXMarketDataSet ) uow.registerObject ( testMds );
            registeredTestMds.setShortName ( "Test" + System.currentTimeMillis () );
            registeredTestMds.setStatus ( 'T' );
            assertTrue ( registeredTestMds.isInterpolate () );
            assertFalse ( registeredTestMds.isAbsoluteBaseDate () );

            registeredTestMds.setInterpolate ( false );
            registeredTestMds.setAbsoluteBaseDate ( true );
            registeredTestMds.setFixedPeriod ( true );

            long now = System.currentTimeMillis ();
            Timestamp start = new Timestamp ( now - 50000  );
            Timestamp end = new Timestamp ( now + 50000  );
            registeredTestMds.setStartTime ( start );
            registeredTestMds.setEndTime ( end );
            registeredTestMds.setMarketDataType("Deposits");
            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testMds = ( FXMarketDataSet ) session.refreshObject ( testMds );
            log ( "testMds=" + testMds + ",interpolate=" + testMds.isInterpolate () + ",absoluteBaseDate=" + testMds.isAbsoluteBaseDate () );
            assertFalse ( testMds.isInterpolate () );
            assertTrue ( testMds.isAbsoluteBaseDate () );
            assertTrue ( testMds.isFixedPeriod () );
            assertNotNull ( testMds.getStartTime () );
            assertNotNull ( testMds.getEndTime () );
            assertEquals ( start.getTime (), testMds.getStartTime ().getTime () );
            assertEquals ( end.getTime (), testMds.getEndTime ().getTime () );
            assertEquals ("Deposits", testMds.getMarketDataType());
        }
        catch ( Exception e )
        {
            fail ( "Exception in testFieldsPersistence : ", e );
        }
    }

    public void testStaticMarketDataSetUpdate ( )
    {
        try
        {
            // this test case is only valid if it is realtime market data set update.
            if ( MarketDataConfigurationFactory.getMarketDataConfigurationMBean ().isExternalRealTimeMarketData () )
            {
                return;
            }
            init ( adminUser );
            double bidRate = 1.2345;
            double offerRate = 1.3456;
            // check whether a EUR/USD spot rate is present in the live market data set.
            CurrencyPair ccyPair = CurrencyFactory.newCurrencyPair ( "EUR", "USD" );
            FXMarketDataElement mde1 = realtimeMds.getFXRate ( ccyPair, false, Tenor.SPOT_TENOR );
            log ( "EUR/USD spot rate in real time mds=" + mde1 );
            if ( mde1 == null )
            {
                mde1 = FXMarketDataFactory.newFXMarketDataElement ();
                mde1.setFXPrice ( FXPriceFactory.newFXPrice () );
                mde1.setTenor ( Tenor.SPOT_TENOR );
                FXRate bid = FXFactory.newFXRate ();
                bid.setCurrencyPair ( ccyPair );
                bid.setRate ( bidRate );
                bid.setFXRateConvention ( stdConv );
                FXRate offer = FXFactory.newFXRate ();
                offer.setCurrencyPair ( ccyPair );
                offer.setRate ( offerRate );
                offer.setFXRateConvention ( stdConv );
                mde1.setEffectiveDateTime ( DateTimeFactory.newDateTime () );
                realtimeMds.getSessionInstance ().getMarketDataElements ().add ( mde1 );
            }
            else
            {
                mde1.getFXPrice ().getBidFXRate ().setRate ( bidRate );
                mde1.getFXPrice ().getOfferFXRate ().setRate ( offerRate );
            }
            log ( "realtime mds bid rate=" + bidRate + ",offer rate=" + offerRate );

            // check whether static mds has EUR/USD rate.
            FXMarketDataElement mde2 = staticMds.getFXRate ( ccyPair, false, Tenor.SPOT_TENOR );
            assertNotNull ( mde2.getFXPrice () );
            assertNotNull ( "Static mds will have EUR/USD spot rate.", mde2 );
            double staticBidRate = mde2.getFXPrice ().getBidFXRate ().getRate ();
            double staticOfferRate = mde2.getFXPrice ().getOfferFXRate ().getRate ();
            log ( "static mds bid rate=" + staticBidRate + ",offer rate=" + staticOfferRate );

            // now invoke the update handler
            mktDataHandler.handle ( MessageFactory.newWorkflowMessage () );

            // now verify the market data.
            FXMarketDataElement mde3 = staticMds.getFXRate ( ccyPair, false, Tenor.SPOT_TENOR );
            log ( "Static mds bid rate=" + mde3.getFXPrice ().getBidFXRate ().getRate () + ",offer rate=" + mde3.getFXPrice ().getOfferFXRate ().getRate () );

            assertTrue ( "Static mds bid rate", mde3.getFXPrice ().getBidFXRate ().getRate () - bidRate == 0.0 );
            assertTrue ( "Static mds offer rate", mde3.getFXPrice ().getOfferFXRate ().getRate () - offerRate == 0.0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ();
        }
    }

    public void testEndOfDayMarketDataSetCreate ( )
    {
        try
        {
            init ( adminUser );
            IdcDate baseDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ().previousDate ();
            log ( "basedate = " + baseDate );
            new FXEODMarketDataSetUpdateHandlerC ().handle ( MessageFactory.newWorkflowMessage () );
            FXMarketDataSet eodmds = getEODMarketDataSetForDate ( "EODMDS", baseDate );
            log ( "eodmds=" + eodmds + ",date=" + baseDate );
            //assertEquals( "eod mds should not be null. mds=" + eodmds, eodmds != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ();
        }
    }

    public void testEndOfDayMarketDataSetUpdate ( )
    {
        String url = MarketDataConfigurationFactory.getMarketDataConfigurationMBean ().getExternalMarketDataUrl ();
        try
        {
            CalculatorFactory.putCalculator ( ExternalMarketDataExtractionCalculator.CALCULATOR_NAME, FXMarketDataSet.class, ExternalMarketDataExtractionCalculatorDelegator.class );

            // try with a previous date to get the data.
            if ( !url.contains ( "date" ) )
            {
                String newUrl = url + "&date=" + EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ().previousDate ().getFormattedDate ( IdcDate.MM_DD_YYYY_FORWARD_SLASH );
                WatchPropertyC.update ( MarketDataConfigurationMBean.EXTERNAL_MARKET_DATA_URL, newUrl, ConfigurationProperty.DYNAMIC_SCOPE, null, null );
                new FXMarketDataSetUpdateHandlerC ().handle ( MessageFactory.newWorkflowMessage () );
            }

            WatchPropertyC.update ( MarketDataConfigurationMBean.EXTERNAL_MARKET_DATA_URL, url, ConfigurationProperty.DYNAMIC_SCOPE, null, null );
            new FXMarketDataSetUpdateHandlerC ().handle ( MessageFactory.newWorkflowMessage () );

            assertEquals ( staticMds.getBaseDate (), EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate () );

            Map<Integer, FXMarketDataElement> mdesMap = (( FXMarketDataSetC ) staticMds).getSpotElements ();
            if ( mdesMap != null )
            {
                for ( FXMarketDataElement mde : mdesMap.values () )
                {
                    if ( mde != null )
                    {
                        boolean sanityCheck = doSanityCheck ( mde );
                        assertTrue ( sanityCheck );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail ( "testEndOfDayMarketDataSetUpdate", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.EXTERNAL_MARKET_DATA_URL, url, ConfigurationProperty.DYNAMIC_SCOPE, null, null );
        }
    }

    public void testEndOfDayMarketDataSetUpdateWithDoubleConventions ( )
    {
        try
        {
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
            staticMds.setBaseDate ( null );
            // add two inverse fx rate basis in std quote convention.
            createAndAddFXRateBasisIfNotPresent ( stdConv, jpy, usd, 6, 2, 2, 3, 2, 2, 10000, 100 );
            createAndAddFXRateBasisIfNotPresent ( stdConv, usd, gbp, 4, 2, 2, 4, 2, 2, 10000, 10000 );
            FXRateBasis jpyUsdRb = stdConv.getFXRateBasis ( jpy, usd );
            assertNotNull ( jpyUsdRb );
            assertTrue ( jpy.isSameAs ( jpyUsdRb.getBaseCurrency () ) );
            FXRateBasis usdGbpRb = stdConv.getFXRateBasis ( usd, gbp );
            assertNotNull ( usdGbpRb );
            assertTrue ( usd.isSameAs ( usdGbpRb.getBaseCurrency () ) );

            CalculatorFactory.putCalculator ( ExternalMarketDataExtractionCalculator.CALCULATOR_NAME, FXMarketDataSet.class, ExternalMarketDataExtractionCalculatorDelegator.class );
            new FXMarketDataSetUpdateHandlerC ().handle ( MessageFactory.newWorkflowMessage () );
            assertEquals ( staticMds.getBaseDate (), EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate () );

            Map<Integer, FXMarketDataElement> mdesMap = (( FXMarketDataSetC ) staticMds).getSpotElements ();
            if ( mdesMap != null )
            {
                for ( FXMarketDataElement mde : mdesMap.values () )
                {
                    if ( mde != null )
                    {
                        boolean sanityCheck = doSanityCheck ( mde );
                        assertTrue ( sanityCheck );
                    }
                }
            }

            // direct conventions rates for USD/JPY and GBP/USD should be present in the mds.
            FXMarketDataElement usdJpyMde = staticMds.findSpotConversionMarketDataElement ( usd, jpy, false );
            assertNotNull ( usdJpyMde );
            FXPrice usdJpyPrice = usdJpyMde.getFXPrice ();
            assertTrue ( usdJpyPrice.getBidFXRate ().getRate () > 50.0 );
            assertTrue ( usdJpyPrice.getBidFXRate ().getRate () < 250.0 );
            assertTrue ( usdJpyPrice.getBidFXRate ().getSpotRate () > 50.0 );
            assertTrue ( usdJpyPrice.getBidFXRate ().getSpotRate () < 250.0 );
            assertEquals ( "usdJpyPrice.bid.fwdPoints=" + usdJpyPrice.getBidFXRate ().getForwardPoints (), 0.0, usdJpyPrice.getBidFXRate ().getForwardPoints () );
            assertNull ( staticMds.findSpotConversionMarketDataElement ( jpy, usd, false ) );

            FXMarketDataElement gbpUsdMde = staticMds.findSpotConversionMarketDataElement ( gbp, usd, false );
            FXPrice gbpUsdPrice = gbpUsdMde.getFXPrice ();
            assertTrue ( gbpUsdPrice.getBidFXRate ().getRate () > 1.0 );
            assertTrue ( gbpUsdPrice.getBidFXRate ().getRate () < 3.0 );
            assertTrue ( gbpUsdPrice.getBidFXRate ().getSpotRate () > 1.0 );
            assertTrue ( gbpUsdPrice.getBidFXRate ().getSpotRate () < 3.0 );
            assertEquals ( 0.0, gbpUsdPrice.getBidFXRate ().getForwardPoints () );
            assertNull ( staticMds.findSpotConversionMarketDataElement ( usd, gbp, false ) );
        }
        catch ( Exception e )
        {
            fail ( "testEndOfDayMarketDataSetUpdateWithDoubleConventions", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( staticMds );
            IdcUtilC.refreshObject ( stdConv );
        }
    }

    public void testMarketDataSetUpdate ( )
    {
        try
        {
            init ( adminUser );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
            FXMarketDataElement mde = staticMds.findSpotConversionMarketDataElement ( eur, usd, true );
            assertNotNull ( mde );
            mde = ( FXMarketDataElement ) uow.registerObject ( mde );
            mde.getFXPrice ().getBidFXRate ().setCurrencyPair ( CurrencyFactory.newCurrencyPair ( usd, eur ) );
            mde.getFXPrice ().getBidFXRate ().setRate ( mde.getFXPrice ().getBidFXRate ().getRate () + 0.0001 );
            mde.getFXPrice ().getOfferFXRate ().setCurrencyPair ( CurrencyFactory.newCurrencyPair ( usd, eur ) );
            mde.getFXPrice ().getOfferFXRate ().setRate ( mde.getFXPrice ().getOfferFXRate ().getRate () + 0.0001 );
            FXRate mid = mde.getFXPrice ().getMidFXRate ();
            if ( mid != null )
            {
                mid.setCurrencyPair ( CurrencyFactory.newCurrencyPair ( usd, eur ) );
                mid.setRate ( mde.getFXPrice ().getMidFXRate ().getRate () + 0.0001 );
            }
            uow.commit ();

            // revert back the currency setting.
            UnitOfWork uow1 = PersistenceFactory.newSession ().acquireUnitOfWork ();
            assertNotNull ( mde );
            mde = ( FXMarketDataElement ) uow1.registerObject ( mde );
            mde.getFXPrice ().getBidFXRate ().setBaseCurrency ( eur );
            mde.getFXPrice ().getBidFXRate ().setVariableCurrency ( usd );
            mde.getFXPrice ().getBidFXRate ().setRate ( mde.getFXPrice ().getBidFXRate ().getRate () - 0.0001 );
            mde.getFXPrice ().getOfferFXRate ().setBaseCurrency ( eur );
            mde.getFXPrice ().getOfferFXRate ().setVariableCurrency ( usd );
            mde.getFXPrice ().getOfferFXRate ().setRate ( mde.getFXPrice ().getOfferFXRate ().getRate () - 0.0001 );
            FXRate mid1 = mde.getFXPrice ().getMidFXRate ();
            if ( mid1 != null )
            {
                mid1.setBaseCurrency ( eur );
                mid1.setVariableCurrency ( usd );
                mid1.setRate ( mde.getFXPrice ().getMidFXRate ().getRate () - 0.0001 );
            }
            uow1.commit ();
        }
        catch ( Exception e )
        {
            fail ( "testMarketDataSetUpdate", e );
        }
    }

    public void testStaticMarketDataSetInsert ( )
    {
        try
        {
            init ( adminUser );
            // this test case is only valid if it is realtime market data set update.
            if ( MarketDataConfigurationFactory.getMarketDataConfigurationMBean ().isExternalRealTimeMarketData () )
            {
                return;
            }
            double bidRate = 46.56;
            double offerRate = 47.34;
            // check whether a USD/INR spot rate is present in the live market data set.
            CurrencyPair ccyPair = CurrencyFactory.newCurrencyPair ( "USD", "BHD" );
            FXMarketDataElement mde1 = realtimeMds.getFXRate ( ccyPair, false, Tenor.SPOT_TENOR );
            if ( mde1 == null )
            {
                mde1 = FXMarketDataFactory.newFXMarketDataElement ();
                mde1.setFXPrice ( FXPriceFactory.newFXPrice () );
                mde1.setTenor ( Tenor.SPOT_TENOR );
                FXRate bid = FXFactory.newFXRate ();
                bid.setCurrencyPair ( ccyPair );
                bid.setRate ( bidRate );
                bid.setFXRateConvention ( stdConv );
                mde1.getFXPrice ().setBidFXRate ( bid );
                FXRate offer = FXFactory.newFXRate ();
                offer.setCurrencyPair ( ccyPair );
                offer.setRate ( offerRate );
                offer.setFXRateConvention ( stdConv );
                mde1.getFXPrice ().setOfferFXRate ( offer );
                realtimeMds.getSessionInstance ().getMarketDataElements ().add ( mde1 );
                mde1.setEffectiveDateTime ( DateTimeFactory.newDateTime () );
                mde1.setValueDate ( offer.getFXRateBasis ().getValueDate ( DateTimeFactory.newDate (), Tenor.SPOT_TENOR ) );
            }
            else
            {
                mde1.getFXPrice ().getBidFXRate ().setRate ( bidRate );
                mde1.getFXPrice ().getOfferFXRate ().setRate ( offerRate );
            }

            // check whether static mds has USD/INR rate.
            FXMarketDataElement mde2 = staticMds.getFXRate ( ccyPair, false, Tenor.SPOT_TENOR );
            if ( mde2 != null )
            {
                double staticBidRate = mde2.getFXPrice ().getBidFXRate ().getRate ();
                double staticOfferRate = mde2.getFXPrice ().getOfferFXRate ().getRate ();
                log ( "static mds bid rate=" + staticBidRate + ",offer=" + staticOfferRate );
            }
            log ( "Before updating, static mds mdes size=" + staticMds.getMarketDataElements ().size () );

            // now invoke the update handler
            mktDataHandler.handle ( MessageFactory.newWorkflowMessage () );

            // now verify the market data.
            log ( "After updating, static mds mdes size=" + staticMds.getMarketDataElements ().size () );
            FXMarketDataElement mde3 = staticMds.getFXRate ( ccyPair, false, Tenor.SPOT_TENOR );
            log ( "Static mds bid rate=" + mde3.getFXPrice ().getBidFXRate ().getRate () + ",offer rate=" + mde3.getFXPrice ().getOfferFXRate ().getRate () );

            assertTrue ( "Static mds bid rate", mde3.getFXPrice ().getBidFXRate ().getRate () - bidRate == 0.0 );
            assertTrue ( "Static mds offer rate", mde3.getFXPrice ().getOfferFXRate ().getRate () - offerRate == 0.0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ();
        }
    }

    protected void init ( User user )
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance ().getSessionContext ( user );
            IdcSessionManager.getInstance ().setSessionContext ( sessContext );
            AlertLoggerFactory.setLogger ( new ScreenAlertLogger () );
        }
        catch ( Exception e )
        {
            log.error ( "FXMarketDataSetPTestC.init.ERROR : Error initilising the credit test.", e );
        }
    }

    private FXMarketDataSet getEODMarketDataSetForDate ( String eodMdsName, IdcDate date )
    {
        FXMarketDataSet eodMarketDataSet = null;
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder ();
            Expression expr = eb.get ( NamedEntity.ShortName ).equal ( eodMdsName ).and ( eb.get ( "baseDate" ).equal ( date.asSqlDate () ) );
            eodMarketDataSet = ( FXMarketDataSet ) PersistenceFactory.newSession ().readObject ( FXMarketDataSetC.class, expr );
        }
        catch ( Exception e )
        {
            log.error ( "FXEODMarketDataSetUpdateHandlerC.getEODMarketDataSetForDate.ERROR : Error while getting EOD mds for date=" + date, e );
        }
        return eodMarketDataSet;
    }

    private FXMarketDataElement createMarketDataElement ( FXMarketDataSet fxMds, Currency baseCcy, Currency varCcy, Tenor tenor )
    {
        FXRate bidFXRate = FXFactory.newFXRate ( baseCcy, varCcy, stdConv );
        FXRate offerFXRate = FXFactory.newFXRate ( baseCcy, varCcy, stdConv );
        bidFXRate.setRate ( 1.2345 );
        offerFXRate.setRate ( 1.2346 );

        FXPrice fxPrice = FXPriceFactory.newFXPrice ();
        fxPrice.setBidFXRate ( bidFXRate );
        fxPrice.setOfferFXRate ( offerFXRate );

        // Child Elements initialized, now create the MDE and set them in it.
        FXMarketDataElement fxmde = FXMarketDataFactory.newFXMarketDataElement ();
        fxmde.setTenor ( tenor );
        fxmde.setFXPrice ( fxPrice );

        String shortName = new StringBuffer ( baseCcy.getShortName () ).append ( '&' ).append ( varCcy.getShortName () ).toString ();
        fxmde.setShortName ( shortName );
        fxMds.addMarketDataElement ( fxmde );
        return fxmde;
    }

    public void testMarketDataLookup ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setRealtime ( true );
            addFXMarketDataElement ( fxMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false );
            addFXMarketDataElement ( fxMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false );

            FXMarketDataElement spotMde = fxMds.findSpotConversionMarketDataElement ( eur, usd, false );
            assertNotNull ( spotMde );
            assertTrue ( doSanityCheck ( spotMde ) );
            // now do a inverted lookup
            spotMde = fxMds.findSpotConversionMarketDataElement ( eur, usd, true );
            assertNotNull ( spotMde );
            assertTrue ( doSanityCheck ( spotMde ) );
            spotMde = fxMds.findSpotConversionMarketDataElement ( usd, eur, false );
            assertNull ( spotMde );
            spotMde = fxMds.findSpotConversionMarketDataElement ( usd, eur, true );
            assertNotNull ( spotMde );
            assertTrue ( doSanityCheck ( spotMde ) );
        }
        catch ( Exception e )
        {
            fail ( "testMarketDataLookup", e );
        }
    }

    public void testMarketDataInterpolation ( )
    {
        try
        {
            IdcDate trdDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setBaseDate ( trdDate );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            FXRateBasis rb = stdConv.getFXRateBasis ( eur, usd );
            IdcDate spotDate = rb.getSpotDate ( trdDate );
            double spotBid = 1.11;
            double spotOffer = 1.22;
            double bid1FwdPoints = 0.001;
            double bid2FwdPoints = 0.003;
            double offer1FwdPoints = 0.003;
            double offer2FwdPoints = 0.005;

            FXMarketDataElement mdeSpot = createFXMarketDataElement ( stdConv, eur, usd, spotBid, 0.0, spotOffer, 0.0, Tenor.SPOT_TENOR, false );
            FXMarketDataElement mde1Week = createFXMarketDataElement ( stdConv, eur, usd, spotBid, bid1FwdPoints, spotOffer, offer1FwdPoints, new Tenor ( "1W" ), false );
            FXMarketDataElement mde2Week = createFXMarketDataElement ( stdConv, eur, usd, spotBid, bid2FwdPoints, spotOffer, offer2FwdPoints, new Tenor ( "2W" ), false );
            addFXMarketDataElement ( fxMds, mdeSpot );
            addFXMarketDataElement ( fxMds, mde1Week );
            addFXMarketDataElement ( fxMds, mde2Week );
            IdcDate valueDate1Week = rb.getValueDate ( trdDate, new Tenor ( "1W" ) );
            IdcDate valueDate2Week = rb.getValueDate ( trdDate, new Tenor ( "2W" ) );

            double bid1Rate = mde1Week.getFXPrice ().getBidFXRate ().getRate ();
            double bid2Rate = mde2Week.getFXPrice ().getBidFXRate ().getRate ();
            double offer1Rate = mde1Week.getFXPrice ().getOfferFXRate ().getRate ();
            double offer2Rate = mde2Week.getFXPrice ().getOfferFXRate ().getRate ();
            log ( "bid1Rate=" + bid1Rate + ",offer1Rate=" + offer1Rate + ",bid2Rate=" + bid2Rate + ",offer2Rate=" + offer2Rate );
            // test interpolation
            fxMds.setInterpolate ( true );
            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = valueDate1Week.addDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () + ",i=" + i );
                System.out.println ( "offer=" + offer.getRate () + ",i=" + i );
                assertTrue ( bid.getRate () > bid1Rate );
                assertTrue ( bid.getRate () < bid2Rate );
                assertTrue ( offer.getRate () > offer1Rate );
                assertTrue ( offer.getRate () < offer2Rate );
            }

            // special handling if the requested tenor is more than the max available tenor. Then, interpolation should not be done and
            // maximum available should be returned as per bug no.47879 clarification
            // for each day after 2 week, 2 week values should be returned. This should be done even if there is no spot element.
            for ( int i = 1; i < 15; i++ )
            {
                IdcDate testDate = valueDate2Week.addDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == bid2Rate );
                assertTrue ( offer.getRate () == offer2Rate );
            }

            // test value dates earlier than the minimum tenor available.
            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = spotDate.subtractDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == spotBid );
                assertTrue ( offer.getRate () == spotOffer );
            }

            //#########################################################################
            // Use the following tenor tests.
            //#########################################################################

            // test use following tenor feature
            // for each day in between 1 week and 2 week, tenor 2w values should be retrieved.
            fxMds.setInterpolate ( false );
            (( FXMarketDataSetC ) fxMds).resetTransients ();
            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = valueDate1Week.addDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == bid2Rate );
                assertTrue ( offer.getRate () == offer2Rate );
            }

            // for each day between spot and 1 week, 1 week values should be returned. This should be done even if there is no spot element.
            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = valueDate1Week.subtractDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == bid1Rate );
                assertTrue ( offer.getRate () == offer1Rate );
            }

            // for each day after 2 week, 2 week values should be returned. This should be done even if there is no spot element.
            for ( int i = 1; i < 15; i++ )
            {
                IdcDate testDate = valueDate2Week.addDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == bid2Rate );
                assertTrue ( offer.getRate () == offer2Rate );
            }

            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = spotDate.subtractDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == spotBid );
                assertTrue ( offer.getRate () == spotOffer );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMarketDataInterpolation" );
        }
    }

    public void testMarketDataInterpolationWithOnlySpotElement ( )
    {
        try
        {
            IdcDate trdDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setBaseDate ( trdDate );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            FXRateBasis rb = stdConv.getFXRateBasis ( eur, usd );
            IdcDate spotDate = rb.getSpotDate ( trdDate );
            double spotBid = 0.98;
            double spotOffer = 1.0;

            FXMarketDataElement mdeSpot = createFXMarketDataElement ( stdConv, eur, usd, spotBid, 0.0, spotOffer, 0.0, Tenor.SPOT_TENOR, false );
            addFXMarketDataElement ( fxMds, mdeSpot );

            // test interpolation
            fxMds.setInterpolate ( true );

            assertNotNull ( fxMds.findSpotConversionMarketDataElement ( eur, usd, true ) );

            // test value dates earlier than the spot tenor
            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = spotDate.subtractDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == spotBid );
                assertTrue ( offer.getRate () == spotOffer );
            }

            // test value dates later than the spot tenor
            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = spotDate.addDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == spotBid );
                assertTrue ( offer.getRate () == spotOffer );
            }

            //#########################################################################
            // Use the following tenor tests.
            //#########################################################################

            // test use following tenor feature
            // for each day in between 1 week and 2 week, tenor 2w values should be retrieved.
            fxMds.setInterpolate ( false );
            (( FXMarketDataSetC ) fxMds).resetTransients ();

            assertNotNull ( fxMds.findSpotConversionMarketDataElement ( eur, usd, true ) );
            // test value dates earlier than the spot tenor
            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = spotDate.subtractDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == spotBid );
                assertTrue ( offer.getRate () == spotOffer );
            }

            // test value dates later than the spot tenor
            for ( int i = 1; i < 5; i++ )
            {
                IdcDate testDate = spotDate.addDays ( i );
                FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, testDate );
                FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
                FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
                FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
                System.out.println ( "bid=" + bid.getRate () );
                System.out.println ( "offer=" + offer.getRate () );
                assertTrue ( bid.getRate () == spotBid );
                assertTrue ( offer.getRate () == spotOffer );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMarketDataInterpolationWithOnlySpotElement" );
        }
    }

    public void testMarketDataInterpolationWithTenorAndBrokenDateWithDifferentEffectiveDateTime ( )
    {
        try
        {
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setBaseDate ( null );
            fxMds.setAbsoluteBaseDate ( false );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setEffectiveDateTime ( DateTimeFactory.newDateTime () );
            fxMds.setInterpolate ( true );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            FXRateBasis rb = stdConv.getFXRateBasis ( eur, usd );
            double spotBid = 0.98;
            double spotOffer = 1.0;
            double bid1Rate = 1.1;
            double bid1FwdPoints = bid1Rate - spotBid;
            double bid2Rate = 1.3;
            double bid2FwdPoints = bid2Rate - spotBid;
            double offer1Rate = 1.2;
            double offer1FwdPoints = offer1Rate - spotOffer;
            double offer2Rate = 1.4;
            double offer2FwdPoints = offer2Rate - spotOffer;

            FXMarketDataElement mdeSpot = createFXMarketDataElement ( stdConv, eur, usd, spotBid, 0.0, spotOffer, 0.0, Tenor.SPOT_TENOR, false );
            FXMarketDataElement mde1Week = createFXMarketDataElement ( stdConv, eur, usd, spotBid, bid1FwdPoints, spotOffer, offer1FwdPoints, new Tenor ( "1W" ), false );
            mde1Week.setEffectiveDateTime ( fxMds.getEffectiveDateTime () );

            addFXMarketDataElement ( fxMds, mdeSpot );
            addFXMarketDataElement ( fxMds, mde1Week );

            // add an mde for the 1 week's broken date.
            IdcDate week1ValDate = mde1Week.getValueDate ();
            FXMarketDataElement mde1WeekBrokenDate = createFXMarketDataElement ( eur, usd, spotBid, bid2FwdPoints, spotOffer, offer2FwdPoints, null, week1ValDate );
            mde1WeekBrokenDate.setEffectiveDateTime ( fxMds.getEffectiveDateTime ().addSeconds ( 1 ) );
            addFXMarketDataElement ( fxMds, mde1WeekBrokenDate );

            FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, week1ValDate );
            FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
            FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
            FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
            System.out.println ( "bid=" + bid.getRate () );
            System.out.println ( "offer=" + offer.getRate () );
            assertTrue ( bid.getRate () == bid2Rate );
            assertTrue ( offer.getRate () == offer2Rate );

            // now change the effective date time of the broken date mde 1 seconds earlier than 1Week tenor element.
            // it should select 1Week tenor as it is the latest.
            mde1WeekBrokenDate.setEffectiveDateTime ( fxMds.getEffectiveDateTime ().addSeconds ( -1 ) );
            FXMask fxMask1 = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, week1ValDate );
            FXMarketDataElement fxMktDtElement1 = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask1 );
            FXRate bid0 = fxMktDtElement1.getFXPrice ().getBidFXRate ();
            FXRate offer0 = fxMktDtElement1.getFXPrice ().getOfferFXRate ();
            System.out.println ( "bid=" + bid0.getRate () );
            System.out.println ( "offer=" + offer0.getRate () );
            assertTrue ( bid0.getRate () == bid1Rate );
            assertTrue ( offer0.getRate () == offer1Rate );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMarketDataInterpolationWithTenorAndBrokenDateWithDifferentEffectiveDateTime" );
        }
    }

    // in this case, broken date should be given priority.
    public void testMarketDataInterpolationWithTenorAndBrokenDateWithSameEffectiveDateTime ( )
    {
        try
        {
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setBaseDate ( null );
            fxMds.setAbsoluteBaseDate ( false );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setEffectiveDateTime ( DateTimeFactory.newDateTime () );
            fxMds.setInterpolate ( true );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            FXRateBasis rb = stdConv.getFXRateBasis ( eur, usd );
            IdcDate week1ValDate = rb.getValueDate ( fxMds.getCurrentBaseDate (), new Tenor ( "1W" ) );
            double spotBid = 0.98;
            double spotOffer = 1.0;
            double bid1Rate = 1.1;
            double bid1FwdPoints = bid1Rate - spotBid;
            double bid2Rate = 1.3;
            double bid2FwdPoints = bid2Rate - spotBid;
            double offer1Rate = 1.2;
            double offer1FwdPoints = offer1Rate - spotOffer;
            double offer2Rate = 1.4;
            double offer2FwdPoints = offer2Rate - spotOffer;

            FXMarketDataElement mdeSpot = createFXMarketDataElement ( stdConv, eur, usd, spotBid, 0.0, spotOffer, 0.0, Tenor.SPOT_TENOR, false );
            FXMarketDataElement mde1Week = createFXMarketDataElement ( stdConv, eur, usd, spotBid, bid1FwdPoints, spotOffer, offer1FwdPoints, new Tenor ( "1W" ), false );
            mde1Week.setEffectiveDateTime ( fxMds.getEffectiveDateTime () );

            // add an mde for the 1 week's broken date.
            FXMarketDataElement mde1WeekBrokenDate = createFXMarketDataElement ( eur, usd, spotBid, bid2FwdPoints, spotOffer, offer2FwdPoints, null, week1ValDate );
            mde1WeekBrokenDate.setEffectiveDateTime ( fxMds.getEffectiveDateTime () );

            // add all market data elements
            addFXMarketDataElement ( fxMds, mde1WeekBrokenDate );
            addFXMarketDataElement ( fxMds, mdeSpot );
            addFXMarketDataElement ( fxMds, mde1Week );

            FXMask fxMask = FXMarketDataFactory.newFXMask ( rb.getCurrencyPair (), true, week1ValDate );
            FXMarketDataElement fxMktDtElement = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
            FXRate bid = fxMktDtElement.getFXPrice ().getBidFXRate ();
            FXRate offer = fxMktDtElement.getFXPrice ().getOfferFXRate ();
            System.out.println ( "bid=" + bid.getRate () );
            System.out.println ( "offer=" + offer.getRate () );
            assertTrue ( fxMktDtElement == mde1WeekBrokenDate );
            assertTrue ( bid.getRate () == bid2Rate );
            assertTrue ( offer.getRate () == offer2Rate );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMarketDataInterpolationWithTenorAndBrokenDateWithSameEffectiveDateTime" );
        }
    }


    private void addFXMarketDataElement ( FXMarketDataSet mds, FXMarketDataElement mde )
    {
        if ( mds.isRealtime () )
        {
            mds.getSessionInstance ().addMarketDataElement ( mde );
        }
        else
        {
            mds.addMarketDataElement ( mde );
        }
        mde.setMarketDataSet ( mds );
    }

    private void addFXMarketDataElement ( FXMarketDataSet mds, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, boolean createMid )
    {
        FXMarketDataElement mde1 = createFXMarketDataElement ( stdConv, base, var, bidSpotRate, bidFwdPoints, offerSpotRate, offerFwdPoints, Tenor.SPOT_TENOR, createMid );
        addFXMarketDataElement ( mds, mde1 );
    }

    private void addFXMarketDataElement ( FXMarketDataSet mds, FXRateConvention conv, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, boolean createMid )
    {
        FXMarketDataElement mde1 = createFXMarketDataElement ( conv, base, var, bidSpotRate, bidFwdPoints, offerSpotRate, offerFwdPoints, Tenor.SPOT_TENOR, createMid );
        addFXMarketDataElement ( mds, mde1 );
    }

    private void addFXMarketDataElement ( FXMarketDataSet mds, FXRateConvention conv, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, boolean createMid, Tenor tenor )
    {
        FXMarketDataElement mde1 = createFXMarketDataElement ( conv, base, var, bidSpotRate, bidFwdPoints, offerSpotRate, offerFwdPoints, tenor, createMid );
        addFXMarketDataElement ( mds, mde1 );
    }

    private FXMarketDataElement createFXMarketDataElement ( FXRateConvention conv, Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, Tenor tenor, boolean createMid )
    {
        FXMarketDataElement mde1 = FXMarketDataFactory.newFXMarketDataElement ();
        CurrencyPair cp = CurrencyFactory.getCurrencyPair ( base, var );
        String mdeName = new StringBuilder ( 32 ).append ( cp.getName () ).append ( '_' ).append ( tenor.getName () ).toString ();
        mde1.setShortName ( mdeName );
        FXPrice fxPrice = FXPriceFactory.newFXPrice ();
        mde1.setFXPrice ( fxPrice );
        mde1.setTenor ( tenor );
        FXRate bid = FXFactory.newFXRate ();
        bid.setFXRateConvention ( conv != null ? conv : stdConv );
        bid.setBaseCurrency ( base );
        bid.setVariableCurrency ( var );
        bid.setSpotRate ( bidSpotRate );
        bid.setForwardPoints ( bidFwdPoints );
        fxPrice.setBidFXRate ( bid );
        FXRate offer = FXFactory.newFXRate ();
        offer.setFXRateConvention ( conv != null ? conv : stdConv );
        offer.setBaseCurrency ( base );
        offer.setVariableCurrency ( var );
        offer.setSpotRate ( offerSpotRate );
        offer.setForwardPoints ( offerFwdPoints );
        fxPrice.setOfferFXRate ( offer );
        if ( createMid )
        {
            FXRate computedMid = fxPrice.getComputedMidFXRate ();
            FXRate mid = FXFactory.newFXRate ();
            mid.setFXRateConvention ( conv != null ? conv : stdConv );
            mid.setBaseCurrency ( base );
            mid.setVariableCurrency ( var );
            mid.setSpotRate ( computedMid.getSpotRate () );
            mid.setForwardPoints ( computedMid.getForwardPoints () );
            fxPrice.setMidFXRate ( mid );
        }
        return mde1;
    }

    private FXMarketDataElement createFXMarketDataElement ( Currency base, Currency var, double bidSpotRate, double bidFwdPoints, double offerSpotRate, double offerFwdPoints, Tenor tenor, IdcDate valDate )
    {
        FXMarketDataElement mde1 = FXMarketDataFactory.newFXMarketDataElement ();
        mde1.setFXPrice ( FXPriceFactory.newFXPrice () );
        mde1.setValueDate ( valDate );
        mde1.setTenor ( tenor );
        FXRate bid = FXFactory.newFXRate ();
        bid.setFXRateConvention ( stdConv );
        bid.setBaseCurrency ( base );
        bid.setVariableCurrency ( var );
        bid.setSpotRate ( bidSpotRate );
        bid.setForwardPoints ( bidFwdPoints );
        mde1.getFXPrice ().setBidFXRate ( bid );
        FXRate offer = FXFactory.newFXRate ();
        offer.setFXRateConvention ( stdConv );
        offer.setBaseCurrency ( base );
        offer.setVariableCurrency ( var );
        offer.setSpotRate ( offerSpotRate );
        offer.setForwardPoints ( offerFwdPoints );
        mde1.getFXPrice ().setOfferFXRate ( offer );
        return mde1;
    }

    private boolean doSanityCheck ( FXMarketDataElement fxMde )
    {
        return doSanityCheck ( fxMde, false );
    }

    private boolean doSanityCheck ( FXMarketDataElement fxMde, boolean checkMidRate )
    {
        try
        {
            FXPrice fxPrice = fxMde.getFXPrice ();
            FXRate bid = fxPrice.getBidFXRate ();
            FXRate offer = fxPrice.getOfferFXRate ();
            FXRate mid = fxMde.getFXPrice ().getMidFXRate ();
            Currency base = fxMde.getCurrencyPair ().getBaseCurrency ();
            Currency var = fxMde.getCurrencyPair ().getVariableCurrency ();
            if ( mid != null )
            {
                if ( !mid.getBaseCurrency ().isSameAs ( base ) || !mid.getVariableCurrency ().isSameAs ( var ) )
                {
                    return false;
                }

                // check the values of mid
                if ( checkMidRate )
                {
                    double midSpotRate = mid.getSpotRate ();
                    double midRate = mid.getRate ();
                    if ( bid != null && offer != null )
                    {
                        double calcMidSpotRate = (bid.getSpotRate () + offer.getSpotRate ()) / 2.0;
                        double calcMidRate = (bid.getRate () + offer.getRate ()) / 2.0;
                        if ( Math.abs ( midSpotRate - calcMidSpotRate ) > 0.01 )
                        {
                            log.warn ( "mismatch in mid spotrate=" + midSpotRate + " and bidSpotRate=" + bid.getSpotRate () + " and offerSpotRate=" + offer.getSpotRate () );
                            return false;
                        }
                        if ( Math.abs ( midRate - calcMidRate ) > 0.01 )
                        {
                            log.warn ( "mismatch in mid rate=" + midRate + " and bidRate=" + bid.getRate () + " and offerRate=" + offer.getRate () );
                            return false;
                        }
                    }
                }
            }
            if ( bid != null )
            {
                if ( !bid.getBaseCurrency ().isSameAs ( base ) || !bid.getVariableCurrency ().isSameAs ( var ) )
                {
                    return false;
                }
            }
            if ( offer != null )
            {
                if ( !offer.getBaseCurrency ().isSameAs ( base ) || !offer.getVariableCurrency ().isSameAs ( var ) )
                {
                    return false;
                }
            }

            FXRate computedMid = fxMde.getFXPrice ().getComputedMidFXRate ();
            if ( computedMid != null )
            {
                if ( !computedMid.getBaseCurrency ().isSameAs ( base ) || !computedMid.getVariableCurrency ().isSameAs ( var ) )
                {
                    return false;
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "FXMarketDataSetPTestC.doSanityCheck.ERROR : Error while sanity check. ", e );
            return false;
        }
        return true;
    }

    public void testCurrentBaseDate ( )
    {
        try
        {
            FXMarketDataSet mds = new FXMarketDataSetC ();
            mds.setAbsoluteBaseDate ( true );
            assertNotNull ( mds.getRollTimeBusinessCenter () );
            IdcDate rollTimeDate = mds.getRollTimeBusinessCenter ().getCurrentRolledDate ();
            IdcDate mdsCurrentBusinessDate = mds.getCurrentBaseDate ();
            assertEquals ( rollTimeDate, mdsCurrentBusinessDate );
            mds.setAbsoluteBaseDate ( false );
            mdsCurrentBusinessDate = mds.getCurrentBaseDate ();
            assertEquals ( rollTimeDate, mdsCurrentBusinessDate );

            // now set a base date
            IdcDate newDate = rollTimeDate.addDays ( 10 );
            mds.setBaseDate ( newDate );
            mdsCurrentBusinessDate = mds.getCurrentBaseDate ();
            assertEquals ( rollTimeDate, mdsCurrentBusinessDate );
            mds.setAbsoluteBaseDate ( true );
            mdsCurrentBusinessDate = mds.getCurrentBaseDate ();
            assertEquals ( newDate, mdsCurrentBusinessDate );
        }
        catch ( Exception e )
        {
            fail ( "testCurrentBaseDate", e );
        }
    }

    public void testMarketDataLookupWithAlias ( )
    {
        Currency eur = CurrencyFactory.getCurrency ( "EUR" );
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
        Currency krw = CurrencyFactory.getCurrency ( "KRW" );
        Currency rub = CurrencyFactory.getCurrency ( "RUB" );
        Currency ru2 = CurrencyFactory.getCurrency ( "RU2" );
        if ( ru2 != null )
        {
            ru2.setAlias ( rub.getShortName () );
        }
        krw.setAlias ( jpy.getShortName () );
        rub.setAlias ( "TestAlias" );
        try
        {
            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setRealtime ( true );
            addFXMarketDataElement ( fxMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false );
            addFXMarketDataElement ( fxMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, false );
            addFXMarketDataElement ( fxMds, usd, rub, 36.11, 0.0, 36.22, 0.0, false );

            FXMarketDataElement spotMde = fxMds.findSpotConversionMarketDataElement ( eur, usd, true );
            assertNotNull ( spotMde );
            assertTrue ( doSanityCheck ( spotMde ) );
            // now do a inverted lookup
            spotMde = fxMds.findSpotConversionMarketDataElement ( eur, usd, true );
            assertNotNull ( spotMde );
            assertTrue ( doSanityCheck ( spotMde ) );
            spotMde = fxMds.findSpotConversionMarketDataElement ( usd, eur, false );
            assertNull ( spotMde );
            spotMde = fxMds.findSpotConversionMarketDataElement ( usd, eur, true );
            assertNotNull ( spotMde );
            assertTrue ( doSanityCheck ( spotMde ) );

            //test alias which have only inverted price in MDS
            //test to validate fix EN-676 [PROBLEM] [CASE: 331268] GM Shows Incorrect USD Amount for USD/RU0 Order
            if ( ru2 != null )
            {
                spotMde = fxMds.findSpotConversionMarketDataElement ( ru2, usd, true );
                assertNotNull ( spotMde );

                FXRate fxRate = spotMde.getFXRate ();
                double amount = fxRate.getAmount ( 1000, ru2 );
                assertEquals ( amount, 27.65 );
                assertTrue ( doSanityCheck ( spotMde ) );
            }
            spotMde = fxMds.findSpotConversionMarketDataElement ( rub, usd, true );
            assertNotNull ( spotMde );
            FXRate fxRate = spotMde.getFXRate ();
            double amount = fxRate.getAmount ( 1000, rub );
            assertEquals ( amount, 27.65 );
            assertTrue ( doSanityCheck ( spotMde ) );


            // check USD/KRW, it should return usdjpy mde.
            FXMarketDataElement usdKrwMde = fxMds.findSpotConversionMarketDataElement ( usd, krw, true );
            assertNotNull ( usdKrwMde );
            assertTrue ( doSanityCheck ( usdKrwMde ) );

            FXMarketDataElement usdRubMde = fxMds.findSpotConversionMarketDataElement ( usd, rub, true );
            assertNotNull ( usdRubMde );

            // now do a circular alias.
            rub.setAlias ( krw.getShortName () );
            krw.setAlias ( rub.getShortName () );
        }
        catch ( Exception e )
        {
            fail ( "testMarketDataLookupWithAlias", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( eur );
            IdcUtilC.refreshObject ( usd );
            IdcUtilC.refreshObject ( jpy );
            IdcUtilC.refreshObject ( krw );
            IdcUtilC.refreshObject ( rub );
            if ( ru2 != null )
            {
                IdcUtilC.refreshObject ( ru2 );
            }
        }
    }

    public void testMarketDataElementComparator ( )
    {
        try
        {
            Comparator c = new FXMarketDataElementComparator ();
            FXMarketDataElement mde1 = FXMarketDataFactory.newFXMarketDataElement ();
            FXMarketDataElement mde2 = FXMarketDataFactory.newFXMarketDataElement ();
            List<FXMarketDataElement> mdes = new ArrayList<FXMarketDataElement> ( 2 );
            mdes.add ( mde1 );
            mdes.add ( mde2 );
            IdcDateTime dt1 = DateTimeFactory.newDateTime ();
            IdcDateTime dt2 = dt1.subtractSeconds ( 3 );
            IdcDateTime dt3 = dt1.addSeconds ( 5 );
            Tenor tenor1 = new Tenor ( "1W" );
            Tenor tenor2 = new Tenor ( "2W" );

            mde1.setEffectiveDateTime ( dt1 );
            mde2.setEffectiveDateTime ( dt2 );
            assertTrue ( c.compare ( mde1, mde2 ) == -1 );
            Collections.sort ( mdes, c );
            assertTrue ( mdes.get ( 0 ).equals ( mde1 ) );

            mde2.setEffectiveDateTime ( dt3 );
            assertTrue ( c.compare ( mde1, mde2 ) == 1 );
            Collections.sort ( mdes, c );
            assertTrue ( mdes.get ( 0 ).equals ( mde2 ) );

            // now set the effective date time same and set one as broken date.
            mde2.setEffectiveDateTime ( dt1 );
            mde1.setTenor ( tenor1 );
            assertTrue ( c.compare ( mde1, mde2 ) == 1 );
            Collections.sort ( mdes, c );
            assertTrue ( mdes.get ( 0 ).equals ( mde2 ) );

            // set the tenor on both.
            mde2.setTenor ( tenor2 );
            assertTrue ( c.compare ( mde1, mde2 ) == 1 );
            Collections.sort ( mdes, c );
            assertTrue ( mdes.get ( 0 ).equals ( mde2 ) );
        }
        catch ( Exception e )
        {
            fail ( "testMarketDataElementComparator", e );
        }
    }

    private FXRateBasis createAndAddFXRateBasisIfNotPresent ( FXRateConvention conv, Currency base, Currency var, int spotPrecision, int forwardPrecision,
                                                              int spotPointsPrecision, int inverseSpotPrecision, int inverseForwardPrecision, int inverseSpotPointsPrecision,
                                                              int pipsFactor, int inversePipsFactor )
    {
        FXRateBasis rb = conv.getFXRateBasis ( base, var );
        if ( rb == null || !rb.getBaseCurrency ().isSameAs ( base ) )
        {
            rb = FXFactory.newFXRateBasis ();
            rb.setBaseCurrency ( base );
            rb.setVariableCurrency ( var );
            rb.setName ( CurrencyFactory.getCurrencyPairName ( base, var ) );
            rb.setSpotPrecision ( spotPrecision );
            rb.setForwardPointsPrecision ( forwardPrecision );
            rb.setSpotPointsPrecision ( spotPointsPrecision );
            rb.setAllowInverse ( false );
            rb.setPipsFactor ( pipsFactor );
            rb.setInverseSpotPrecision ( inverseSpotPrecision );
            rb.setInverseForwardPointsPrecision ( inverseForwardPrecision );
            rb.setInverseSpotPointsPrecision ( inverseSpotPointsPrecision );
            rb.setFXRateConvention ( conv );
            conv.addFXRateBasis ( rb );
        }
        return rb;
    }


    public void testConventionCrossCurrencyComputationForIndexTypeInstrument ( )
    {
        FXRateBasis eurJpyRb = null;
        Currency eur = CurrencyFactory.getCurrency ( "EUR" );
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
        Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
        try
        {
            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setRealtime ( true );
            addFXMarketDataElement ( fxMds, eur, gbp, 1.2345, 0.0, 1.2367, 0.0, false );
            addFXMarketDataElement ( fxMds, gbp, jpy, 111.11, 0.0, 112.12, 0.0, false );

            // add GBP as cross currency to the EUR/JPY convention.
            FXRateConvention stdQuoteConv = ( FXRateConvention ) namedEntityReader.execute ( FXRateConvention.class, "STDQOTCNV" );
            eurJpyRb = stdQuoteConv.getFXRateBasis ( "EUR/JPY" );
            eurJpyRb.setCrossCurrency ( gbp );

            // also change classification to INDEX for eur.
            InstrumentClassification indexClsf = ( InstrumentClassification ) namedEntityReader.execute ( InstrumentClassification.class, "INDEX" );
            eur.setInstrumentClassification ( indexClsf );

            // check for EUR/JPY rate, it should return an Mde which is cross computed from
            FXMarketDataElement eurJpyMde = fxMds.findSpotConversionMarketDataElement ( eur, jpy, true );
            assertNotNull ( eurJpyMde );
            assertTrue ( doSanityCheck ( eurJpyMde ) );

            // with mds cross currency being null also, it should return the same.
            fxMds.setCrossCurrency ( null );
            FXMarketDataElement eurJpyMde1 = fxMds.findSpotConversionMarketDataElement ( eur, jpy, true );
            assertNotNull ( eurJpyMde1 );
            assertTrue ( doSanityCheck ( eurJpyMde1 ) );

            // set eur to be regular type which is 'Currency'
            InstrumentClassification ccyClsf = ( InstrumentClassification ) namedEntityReader.execute ( InstrumentClassification.class, "CURRENCY" );
            eur.setInstrumentClassification ( ccyClsf );
            FXMarketDataElement eurJpyMde2 = fxMds.findSpotConversionMarketDataElement ( eur, jpy, true );
            assertNull ( eurJpyMde2 );

            // even if the currency is not set with a classification, it should return null without throwing exception.
            eur.setInstrumentClassification ( null );
            FXMarketDataElement eurJpyMde3 = fxMds.findSpotConversionMarketDataElement ( eur, jpy, true );
            assertNull ( eurJpyMde3 );
        }
        catch ( Exception e )
        {
            fail ( "testConventionCrossCurrencyComputationForIndexTypeInstrument", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( eur );
            if ( eurJpyRb != null )
            {
                IdcUtilC.refreshObject ( eurJpyRb );
            }
        }
    }

    public void testMixedDirectAndInverseElementsForInterpolationWithNoSpotElement ( )
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
            CurrencyPair directCcyPair = CurrencyFactory.getCurrencyPair ( gbp, usd );
            CurrencyPair inverseCcyPair = CurrencyFactory.getCurrencyPair ( usd, gbp );
            // add two inverse fx rate basis in std quote convention.
            createAndAddFXRateBasisIfNotPresent ( stdConv, usd, gbp, 4, 2, 2, 4, 2, 2, 10000, 10000 );

            // add direct 1w tenor and inverse 3 week tenor on a test mds.
            double spotBidRate = 1.8888;
            double spotOfferRate = 1.9999;
            double inverseBidSpotRate = 1 / spotOfferRate;
            double inverseOfferSpotRate = 1 / spotBidRate;
            double fwdBidPoints = 0.0005;
            double fwdOfferPoints = 0.0006;
            double inverseFwdBidPoints = 0.0006;
            double inverseFwdOfferPoints = 0.00007;
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setShortName ( "Test" );
            fxMds.setAbsoluteBaseDate ( true );
            fxMds.setBaseDate ( DateTimeFactory.newDate ( "2016-03-14" ) );

            FXMarketDataElement direct1WMde = createMarketDataElement ( fxMds, gbp, usd, new Tenor ( "6m" ) );
            FXRate directBid = direct1WMde.getFXPrice ().getBidFXRate ();
            directBid.setSpotRate ( spotBidRate );
            directBid.setForwardPoints ( fwdBidPoints );
            FXRate directOffer = direct1WMde.getFXPrice ().getOfferFXRate ();
            directOffer.setSpotRate ( spotOfferRate );
            directOffer.setForwardPoints ( fwdOfferPoints );

            FXMarketDataElement inverse3WMde = createMarketDataElement ( fxMds, usd, gbp, new Tenor ( "12m" ) );
            FXRate inverseBid = inverse3WMde.getFXPrice ().getBidFXRate ();
            inverseBid.setSpotRate ( inverseBidSpotRate );
            inverseBid.setForwardPoints ( inverseFwdBidPoints );
            FXRate inverseOffer = inverse3WMde.getFXPrice ().getOfferFXRate ();
            inverseOffer.setSpotRate ( inverseOfferSpotRate );
            inverseOffer.setForwardPoints ( inverseFwdOfferPoints );

            FXPrice convertedInverseToDirect = inverse3WMde.getFXPrice ().getInverted ();
            FXPrice convertedDirectToInverse = direct1WMde.getFXPrice ().getInverted ();
            double avgBidSpotRateDirect = (convertedInverseToDirect.getBidFXRate ().getSpotRate () + direct1WMde.getFXPrice ().getBidFXRate ().getSpotRate ()) / 2.0;
            double avgOfferSpotRateDirect = (convertedInverseToDirect.getOfferFXRate ().getSpotRate () + direct1WMde.getFXPrice ().getOfferFXRate ().getSpotRate ()) / 2.0;
            double avgBidFwdPointsDirect = (convertedInverseToDirect.getBidFXRate ().getScaledForwardPoints () + direct1WMde.getFXPrice ().getBidFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgOfferFwdPointsDirect = (convertedInverseToDirect.getOfferFXRate ().getScaledForwardPoints () + direct1WMde.getFXPrice ().getOfferFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgBidRateDirect = (convertedInverseToDirect.getBidFXRate ().getRate () + direct1WMde.getFXPrice ().getBidFXRate ().getRate ()) / 2.0;
            double avgOfferRateDirect = (convertedInverseToDirect.getOfferFXRate ().getRate () + direct1WMde.getFXPrice ().getOfferFXRate ().getRate ()) / 2.0;

            double avgBidSpotRateInverse = (convertedDirectToInverse.getBidFXRate ().getSpotRate () + inverse3WMde.getFXPrice ().getBidFXRate ().getSpotRate ()) / 2.0;
            double avgOfferSpotRateInverse = (convertedDirectToInverse.getOfferFXRate ().getSpotRate () + inverse3WMde.getFXPrice ().getOfferFXRate ().getSpotRate ()) / 2.0;
            double avgBidFwdPointsInverse = (convertedDirectToInverse.getBidFXRate ().getScaledForwardPoints () + inverse3WMde.getFXPrice ().getBidFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgOfferFwdPointsInverse = (convertedDirectToInverse.getOfferFXRate ().getScaledForwardPoints () + inverse3WMde.getFXPrice ().getOfferFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgBidRateInverse = (convertedDirectToInverse.getBidFXRate ().getRate () + inverse3WMde.getFXPrice ().getBidFXRate ().getRate ()) / 2.0;
            double avgOfferRateInverse = (convertedDirectToInverse.getOfferFXRate ().getRate () + inverse3WMde.getFXPrice ().getOfferFXRate ().getRate ()) / 2.0;

            // look for inverted=true 2w rates.
            Tenor tenor2W = new Tenor ( "9m" );
            FXMask fxMask = FXMarketDataFactory.newFXMask ( directCcyPair, true, tenor2W );
            fxMask.setCalculate ( true );
            FXMarketDataElement mde1 = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
            assertNotNull ( mde1 );
            FXRate bid1 = mde1.getFXPrice ().getBidFXRate ();
            log ( "bid1=" + bid1.getToString () );
            assertTrue ( bid1.getCurrencyPair ().getBaseCurrency ().isSameAs ( gbp ) );
            assertTrue ( Math.abs ( bid1.getSpotRate () - avgBidSpotRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( bid1.getRate () - avgBidRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( bid1.getScaledForwardPoints () - avgBidFwdPointsDirect ) < 0.2 );

            FXRate offer1 = mde1.getFXPrice ().getOfferFXRate ();
            log ( "offer1=" + offer1.getToString () );
            assertTrue ( offer1.getCurrencyPair ().getBaseCurrency ().isSameAs ( gbp ) );
            assertTrue ( Math.abs ( offer1.getSpotRate () - avgOfferSpotRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( offer1.getRate () - avgOfferRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( offer1.getScaledForwardPoints () - avgOfferFwdPointsDirect ) < 0.5 );

            FXMask fxMask1 = FXMarketDataFactory.newFXMask ( inverseCcyPair, true, tenor2W );
            fxMask1.setCalculate ( true );
            FXMarketDataElement mde2 = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask1 );
            assertNotNull ( mde2 );
            FXRate bid2 = mde2.getFXPrice ().getBidFXRate ();
            log ( "bid2=" + bid2.getToString () );
            assertTrue ( bid2.getCurrencyPair ().getBaseCurrency ().isSameAs ( usd ) );
            assertTrue ( Math.abs ( bid2.getSpotRate () - avgBidSpotRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( bid2.getRate () - avgBidRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( bid2.getScaledForwardPoints () - avgBidFwdPointsInverse ) < 0.2 );

            FXRate offer2 = mde2.getFXPrice ().getOfferFXRate ();
            log ( "offer2=" + offer2.getToString () );
            assertTrue ( offer2.getCurrencyPair ().getBaseCurrency ().isSameAs ( usd ) );
            assertTrue ( Math.abs ( offer2.getSpotRate () - avgOfferSpotRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( offer2.getRate () - avgOfferRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( offer2.getScaledForwardPoints () - avgOfferFwdPointsInverse ) < 0.2 );
        }
        catch ( Exception e )
        {
            fail ( "testMixedDirectAndInverseElementsForInterpolationWithNoSpotElement", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( stdConv );
        }
    }

    public void testMixedDirectAndInverseElementsForInterpolationWithDirectSpotElement ( )
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
            CurrencyPair directCcyPair = CurrencyFactory.getCurrencyPair ( gbp, usd );
            CurrencyPair inverseCcyPair = CurrencyFactory.getCurrencyPair ( usd, gbp );
            // add two inverse fx rate basis in std quote convention.
            createAndAddFXRateBasisIfNotPresent ( stdConv, usd, gbp, 4, 2, 2, 4, 2, 2, 10000, 10000 );

            // add direct 1w tenor and inverse 3 week tenor on a test mds.
            double spotBidRate = 1.82;
            double spotOfferRate = 1.92;
            double inverseBidSpotRate = 1 / spotOfferRate;
            double inverseOfferSpotRate = 1 / spotBidRate;
            double fwdBidPoints = 0.0005;
            double fwdOfferPoints = 0.0006;
            double inverseFwdBidPoints = 0.0006;
            double inverseFwdOfferPoints = 0.00007;
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setShortName ( "Test" );
            fxMds.setAbsoluteBaseDate ( true );
            fxMds.setBaseDate ( DateTimeFactory.newDate ( "2016-03-14" ) );

            // add a direct spot rate.
            FXMarketDataElement spotMde = createMarketDataElement ( fxMds, gbp, usd, Tenor.SPOT_TENOR );
            FXRate spotBid = spotMde.getFXPrice ().getBidFXRate ();
            spotBid.setSpotRate ( spotBidRate );
            FXRate spotOffer = spotMde.getFXPrice ().getOfferFXRate ();
            spotOffer.setSpotRate ( spotOfferRate );

            FXMarketDataElement direct1WMde = createMarketDataElement ( fxMds, gbp, usd, new Tenor ( "6m" ) );
            FXRate directBid = direct1WMde.getFXPrice ().getBidFXRate ();
            directBid.setSpotRate ( spotBidRate );
            directBid.setForwardPoints ( fwdBidPoints );
            FXRate directOffer = direct1WMde.getFXPrice ().getOfferFXRate ();
            directOffer.setSpotRate ( spotOfferRate );
            directOffer.setForwardPoints ( fwdOfferPoints );

            FXMarketDataElement inverse3WMde = createMarketDataElement ( fxMds, usd, gbp, new Tenor ( "12m" ) );
            FXRate inverseBid = inverse3WMde.getFXPrice ().getBidFXRate ();
            inverseBid.setSpotRate ( inverseBidSpotRate );
            inverseBid.setForwardPoints ( inverseFwdBidPoints );
            FXRate inverseOffer = inverse3WMde.getFXPrice ().getOfferFXRate ();
            inverseOffer.setSpotRate ( inverseOfferSpotRate );
            inverseOffer.setForwardPoints ( inverseFwdOfferPoints );

            FXPrice convertedInverseToDirect = inverse3WMde.getFXPrice ().getInverted ();
            FXPrice convertedDirectToInverse = direct1WMde.getFXPrice ().getInverted ();
            double avgBidSpotRateDirect = (convertedInverseToDirect.getBidFXRate ().getSpotRate () + direct1WMde.getFXPrice ().getBidFXRate ().getSpotRate ()) / 2.0;
            double avgOfferSpotRateDirect = (convertedInverseToDirect.getOfferFXRate ().getSpotRate () + direct1WMde.getFXPrice ().getOfferFXRate ().getSpotRate ()) / 2.0;
            double avgBidFwdPointsDirect = (convertedInverseToDirect.getBidFXRate ().getScaledForwardPoints () + direct1WMde.getFXPrice ().getBidFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgOfferFwdPointsDirect = (convertedInverseToDirect.getOfferFXRate ().getScaledForwardPoints () + direct1WMde.getFXPrice ().getOfferFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgBidRateDirect = (convertedInverseToDirect.getBidFXRate ().getRate () + direct1WMde.getFXPrice ().getBidFXRate ().getRate ()) / 2.0;
            double avgOfferRateDirect = (convertedInverseToDirect.getOfferFXRate ().getRate () + direct1WMde.getFXPrice ().getOfferFXRate ().getRate ()) / 2.0;

            double avgBidSpotRateInverse = (convertedDirectToInverse.getBidFXRate ().getSpotRate () + inverse3WMde.getFXPrice ().getBidFXRate ().getSpotRate ()) / 2.0;
            double avgOfferSpotRateInverse = (convertedDirectToInverse.getOfferFXRate ().getSpotRate () + inverse3WMde.getFXPrice ().getOfferFXRate ().getSpotRate ()) / 2.0;
            double avgBidFwdPointsInverse = (convertedDirectToInverse.getBidFXRate ().getScaledForwardPoints () + inverse3WMde.getFXPrice ().getBidFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgOfferFwdPointsInverse = (convertedDirectToInverse.getOfferFXRate ().getScaledForwardPoints () + inverse3WMde.getFXPrice ().getOfferFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgBidRateInverse = (convertedDirectToInverse.getBidFXRate ().getRate () + inverse3WMde.getFXPrice ().getBidFXRate ().getRate ()) / 2.0;
            double avgOfferRateInverse = (convertedDirectToInverse.getOfferFXRate ().getRate () + inverse3WMde.getFXPrice ().getOfferFXRate ().getRate ()) / 2.0;

            // look for inverted=true 2w rates.
            Tenor tenor2W = new Tenor ( "9m" );
            FXMask fxMask = FXMarketDataFactory.newFXMask ( directCcyPair, true, tenor2W );
            fxMask.setCalculate ( true );
            FXMarketDataElement mde1 = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
            assertNotNull ( mde1 );
            FXRate bid1 = mde1.getFXPrice ().getBidFXRate ();
            log ( "bid1=" + bid1.getToString () );
            assertTrue ( bid1.getCurrencyPair ().getBaseCurrency ().isSameAs ( gbp ) );
            assertTrue ( Math.abs ( bid1.getSpotRate () - avgBidSpotRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( bid1.getRate () - avgBidRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( bid1.getScaledForwardPoints () - avgBidFwdPointsDirect ) < 0.2 );

            FXRate offer1 = mde1.getFXPrice ().getOfferFXRate ();
            log ( "offer1=" + offer1.getToString () );
            assertTrue ( offer1.getCurrencyPair ().getBaseCurrency ().isSameAs ( gbp ) );
            assertTrue ( Math.abs ( offer1.getSpotRate () - avgOfferSpotRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( offer1.getRate () - avgOfferRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( offer1.getScaledForwardPoints () - avgOfferFwdPointsDirect ) < 0.5 );

            FXMask fxMask1 = FXMarketDataFactory.newFXMask ( inverseCcyPair, true, tenor2W );
            fxMask1.setCalculate ( true );
            FXMarketDataElement mde2 = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask1 );
            assertNotNull ( mde2 );
            FXRate bid2 = mde2.getFXPrice ().getBidFXRate ();
            log ( "bid2=" + bid2.getToString () );
            assertTrue ( bid2.getCurrencyPair ().getBaseCurrency ().isSameAs ( usd ) );
            assertTrue ( Math.abs ( bid2.getSpotRate () - avgBidSpotRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( bid2.getRate () - avgBidRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( bid2.getScaledForwardPoints () - avgBidFwdPointsInverse ) < 0.2 );

            FXRate offer2 = mde2.getFXPrice ().getOfferFXRate ();
            log ( "offer2=" + offer2.getToString () );
            assertTrue ( offer2.getCurrencyPair ().getBaseCurrency ().isSameAs ( usd ) );
            assertTrue ( Math.abs ( offer2.getSpotRate () - avgOfferSpotRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( offer2.getRate () - avgOfferRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( offer2.getScaledForwardPoints () - avgOfferFwdPointsInverse ) < 0.2 );
        }
        catch ( Exception e )
        {
            fail ( "testMixedDirectAndInverseElementsForInterpolationWithDirectSpotElement", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( stdConv );
        }
    }

    public void testMixedDirectAndInverseElementsForInterpolationWithInverseSpotElement ( )
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
            CurrencyPair directCcyPair = CurrencyFactory.getCurrencyPair ( gbp, usd );
            CurrencyPair inverseCcyPair = CurrencyFactory.getCurrencyPair ( usd, gbp );
            // add two inverse fx rate basis in std quote convention.
            createAndAddFXRateBasisIfNotPresent ( stdConv, usd, gbp, 4, 2, 2, 4, 2, 2, 10000, 10000 );

            // add direct 1w tenor and inverse 3 week tenor on a test mds.
            double spotBidRate = 1.25;
            double spotOfferRate = 2.5;
            double inverseBidSpotRate = 1 / spotOfferRate;
            double inverseOfferSpotRate = 1 / spotBidRate;
            double fwdBidPoints = 0.0005;
            double fwdOfferPoints = 0.0006;
            double inverseFwdBidPoints = 0.0006;
            double inverseFwdOfferPoints = 0.00007;
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setShortName ( "Test" );
            fxMds.setAbsoluteBaseDate ( true );
            fxMds.setBaseDate ( DateTimeFactory.newDate ( "2016-03-14" ) );

            // add a direct spot rate.
            FXMarketDataElement spotMde = createMarketDataElement ( fxMds, usd, gbp, Tenor.SPOT_TENOR );
            FXRate spotBid = spotMde.getFXPrice ().getBidFXRate ();
            spotBid.setSpotRate ( inverseBidSpotRate );
            FXRate spotOffer = spotMde.getFXPrice ().getOfferFXRate ();
            spotOffer.setSpotRate ( inverseOfferSpotRate );

            FXMarketDataElement direct1WMde = createMarketDataElement ( fxMds, gbp, usd, new Tenor ( "6m" ) );
            FXRate directBid = direct1WMde.getFXPrice ().getBidFXRate ();
            directBid.setSpotRate ( spotBidRate );
            directBid.setForwardPoints ( fwdBidPoints );
            FXRate directOffer = direct1WMde.getFXPrice ().getOfferFXRate ();
            directOffer.setSpotRate ( spotOfferRate );
            directOffer.setForwardPoints ( fwdOfferPoints );

            FXMarketDataElement inverse3WMde = createMarketDataElement ( fxMds, usd, gbp, new Tenor ( "12m" ) );
            FXRate inverseBid = inverse3WMde.getFXPrice ().getBidFXRate ();
            inverseBid.setSpotRate ( inverseBidSpotRate );
            inverseBid.setForwardPoints ( inverseFwdBidPoints );
            FXRate inverseOffer = inverse3WMde.getFXPrice ().getOfferFXRate ();
            inverseOffer.setSpotRate ( inverseOfferSpotRate );
            inverseOffer.setForwardPoints ( inverseFwdOfferPoints );

            FXPrice convertedInverseToDirect = inverse3WMde.getFXPrice ().getInverted ();
            FXPrice convertedDirectToInverse = direct1WMde.getFXPrice ().getInverted ();
            double avgBidSpotRateDirect = (convertedInverseToDirect.getBidFXRate ().getSpotRate () + direct1WMde.getFXPrice ().getBidFXRate ().getSpotRate ()) / 2.0;
            double avgOfferSpotRateDirect = (convertedInverseToDirect.getOfferFXRate ().getSpotRate () + direct1WMde.getFXPrice ().getOfferFXRate ().getSpotRate ()) / 2.0;
            double avgBidFwdPointsDirect = (convertedInverseToDirect.getBidFXRate ().getScaledForwardPoints () + direct1WMde.getFXPrice ().getBidFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgOfferFwdPointsDirect = (convertedInverseToDirect.getOfferFXRate ().getScaledForwardPoints () + direct1WMde.getFXPrice ().getOfferFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgBidRateDirect = (convertedInverseToDirect.getBidFXRate ().getRate () + direct1WMde.getFXPrice ().getBidFXRate ().getRate ()) / 2.0;
            double avgOfferRateDirect = (convertedInverseToDirect.getOfferFXRate ().getRate () + direct1WMde.getFXPrice ().getOfferFXRate ().getRate ()) / 2.0;

            double avgBidSpotRateInverse = (convertedDirectToInverse.getBidFXRate ().getSpotRate () + inverse3WMde.getFXPrice ().getBidFXRate ().getSpotRate ()) / 2.0;
            double avgOfferSpotRateInverse = (convertedDirectToInverse.getOfferFXRate ().getSpotRate () + inverse3WMde.getFXPrice ().getOfferFXRate ().getSpotRate ()) / 2.0;
            double avgBidFwdPointsInverse = (convertedDirectToInverse.getBidFXRate ().getScaledForwardPoints () + inverse3WMde.getFXPrice ().getBidFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgOfferFwdPointsInverse = (convertedDirectToInverse.getOfferFXRate ().getScaledForwardPoints () + inverse3WMde.getFXPrice ().getOfferFXRate ().getScaledForwardPoints ()) / 2.0;
            double avgBidRateInverse = (convertedDirectToInverse.getBidFXRate ().getRate () + inverse3WMde.getFXPrice ().getBidFXRate ().getRate ()) / 2.0;
            double avgOfferRateInverse = (convertedDirectToInverse.getOfferFXRate ().getRate () + inverse3WMde.getFXPrice ().getOfferFXRate ().getRate ()) / 2.0;

            // look for inverted=true 2w rates.
            IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
            IdcDate week1Date = stdConv.getFXRateBasis ( gbp, usd ).getValueDate ( tradeDate, new Tenor ( "6m" ) );
            IdcDate week3Date = stdConv.getFXRateBasis ( gbp, usd ).getValueDate ( tradeDate, new Tenor ( "12m" ) );
            log ( "6m=" + week1Date + ",12m=" + week3Date );
            FXMask fxMask = FXMarketDataFactory.newFXMask ( directCcyPair, true, new Tenor ( "9m" ) );
            fxMask.setCalculate ( true );
            FXMarketDataElement mde1 = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask );
            assertNotNull ( mde1 );
            FXRate bid1 = mde1.getFXPrice ().getBidFXRate ();
            log ( "bid1=" + bid1.getToString () );
            assertTrue ( bid1.getCurrencyPair ().getBaseCurrency ().isSameAs ( gbp ) );
            assertTrue ( Math.abs ( bid1.getSpotRate () - avgBidSpotRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( bid1.getRate () - avgBidRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( bid1.getScaledForwardPoints () - avgBidFwdPointsDirect ) < 0.2 );

            FXRate offer1 = mde1.getFXPrice ().getOfferFXRate ();
            log ( "offer1=" + offer1.getToString () );
            assertTrue ( offer1.getCurrencyPair ().getBaseCurrency ().isSameAs ( gbp ) );
            assertTrue ( Math.abs ( offer1.getSpotRate () - avgOfferSpotRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( offer1.getRate () - avgOfferRateDirect ) < 0.2 );
            assertTrue ( Math.abs ( offer1.getScaledForwardPoints () - avgOfferFwdPointsDirect ) < 0.5 );

            FXMask fxMask1 = FXMarketDataFactory.newFXMask ( inverseCcyPair, true, new Tenor ( "9m" ) );
            fxMask1.setCalculate ( true );
            FXMarketDataElement mde2 = ( FXMarketDataElement ) fxMds.getMarketDataElement ( fxMask1 );
            assertNotNull ( mde2 );
            FXRate bid2 = mde2.getFXPrice ().getBidFXRate ();
            log ( "bid2=" + bid2.getToString () );
            assertTrue ( bid2.getCurrencyPair ().getBaseCurrency ().isSameAs ( usd ) );
            assertTrue ( Math.abs ( bid2.getSpotRate () - avgBidSpotRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( bid2.getRate () - avgBidRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( bid2.getScaledForwardPoints () - avgBidFwdPointsInverse ) < 0.2 );

            FXRate offer2 = mde2.getFXPrice ().getOfferFXRate ();
            log ( "offer2=" + offer2.getToString () );
            assertTrue ( offer2.getCurrencyPair ().getBaseCurrency ().isSameAs ( usd ) );
            assertTrue ( Math.abs ( offer2.getSpotRate () - avgOfferSpotRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( offer2.getRate () - avgOfferRateInverse ) < 0.2 );
            assertTrue ( Math.abs ( offer2.getScaledForwardPoints () - avgOfferFwdPointsInverse ) < 0.2 );
        }
        catch ( Exception e )
        {
            fail ( "testMixedDirectAndInverseElementsForInterpolationWithInverseSpotElement", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( stdConv );
        }
    }

    public void testRealtimeMDSCrossCurrencyComputation ( )
    {
        Currency eur = CurrencyFactory.getCurrency ( "EUR" );
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
        Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
        Currency cad = CurrencyFactory.getCurrency ( "CAD" );
        try
        {
            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet liveMds = ReferenceDataCacheC.getInstance ().getLiveFXMds ();
            FXRateConvention conv = liveMds.getFXRateConvention ();
            if ( conv == null )
            {
                conv = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();
                liveMds.setFXRateConvention ( conv );
            }
            FXMarketDataSet fxMds = ( FXMarketDataSet ) liveMds.getSessionInstance ();
            conv = fxMds.getFXRateConvention ();

            addFXMarketDataElement ( fxMds, conv, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false );
            addFXMarketDataElement ( fxMds, conv, usd, jpy, 111.11, 0.0, 112.12, 0.0, false );
            addFXMarketDataElement ( fxMds, gbp, usd, 1.6785, 0.0, 1.6788, 0.0, false );
            addFXMarketDataElement ( fxMds, usd, cad, 1.3185, 0.0, 1.3188, 0.0, false );

            // check for EUR/JPY rate, it should return an Mde which is cross computed from
            FXMarketDataElement eurJpyMde = fxMds.findSpotConversionMarketDataElement ( eur, jpy, false );
            assertNotNull ( eurJpyMde );
            assertTrue ( doSanityCheck ( eurJpyMde, true ) );

            FXRate eurJpyBid = eurJpyMde.getFXPrice ().getBidFXRate ();
            FXRate eurJpyOffer = eurJpyMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( eurJpyBid.getRate () - 137 ) < 1.0 );
            assertTrue ( Math.abs ( eurJpyOffer.getRate () - 138 ) < 1.0 );

            // check for EUR/GBP rate, it should return an Mde which is cross computed from
            FXMarketDataElement eurGbpMde = fxMds.findSpotConversionMarketDataElement ( eur, gbp, false );
            assertNotNull ( eurGbpMde );
            assertTrue ( doSanityCheck ( eurGbpMde, true ) );

            FXRate eurGbpBid = eurGbpMde.getFXPrice ().getBidFXRate ();
            FXRate eurGbpOffer = eurGbpMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( eurGbpBid.getRate () - 0.735 ) < 0.0005 );
            assertTrue ( Math.abs ( eurGbpOffer.getRate () - 0.737 ) < 0.0005 );

            // check for CAD/JPY rate, it should return an Mde which is cross computed from
            FXMarketDataElement cadJpyMde = fxMds.findSpotConversionMarketDataElement ( cad, jpy, false );
            assertNotNull ( cadJpyMde );
            assertTrue ( doSanityCheck ( cadJpyMde, true ) );

            FXRate cadJpyBid = cadJpyMde.getFXPrice ().getBidFXRate ();
            FXRate cadJpyOffer = cadJpyMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( cadJpyBid.getRate () - 84 ) < 0.5 );
            assertTrue ( Math.abs ( cadJpyOffer.getRate () - 85 ) < 0.5 );
        }
        catch ( Exception e )
        {
            fail ( "testRealtimeMDSCrossCurrencyComputation", e );
        }
    }

    public void testRealtimeMDSCrossCurrencyComputationWithMidRate ( )
    {
        Currency eur = CurrencyFactory.getCurrency ( "EUR" );
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
        Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
        Currency cad = CurrencyFactory.getCurrency ( "CAD" );
        try
        {
            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet liveMds = ReferenceDataCacheC.getInstance ().getLiveFXMds ();
            FXRateConvention conv = liveMds.getFXRateConvention ();
            if ( conv == null )
            {
                liveMds.setFXRateConvention ( ReferenceDataCacheC.getInstance ().getStandardFXRateConvention () );
            }
            FXMarketDataSet fxMds = ( FXMarketDataSet ) liveMds.getSessionInstance ();
            conv = fxMds.getFXRateConvention ();
            addFXMarketDataElement ( fxMds, conv, eur, usd, 1.2345, 0.0, 1.2367, 0.0, true );
            addFXMarketDataElement ( fxMds, conv, usd, jpy, 111.11, 0.0, 112.12, 0.0, true );
            addFXMarketDataElement ( fxMds, conv, gbp, usd, 1.6785, 0.0, 1.6788, 0.0, true );
            addFXMarketDataElement ( fxMds, usd, cad, 1.3185, 0.0, 1.3188, 0.0, false );

            // check for EUR/JPY rate, it should return an Mde which is cross computed from
            FXMarketDataElement eurJpyMde = fxMds.findSpotConversionMarketDataElement ( eur, jpy, false );
            assertNotNull ( eurJpyMde );
            assertTrue ( doSanityCheck ( eurJpyMde, true ) );

            FXRate eurJpyBid = eurJpyMde.getFXPrice ().getBidFXRate ();
            FXRate eurJpyOffer = eurJpyMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( eurJpyBid.getRate () - 137 ) < 1.0 );
            assertTrue ( Math.abs ( eurJpyOffer.getRate () - 138 ) < 1.0 );

            // check for EUR/GBP rate, it should return an Mde which is cross computed from
            FXMarketDataElement eurGbpMde = fxMds.findSpotConversionMarketDataElement ( eur, gbp, false );
            assertNotNull ( eurGbpMde );
            assertTrue ( doSanityCheck ( eurGbpMde, true ) );

            FXRate eurGbpBid = eurGbpMde.getFXPrice ().getBidFXRate ();
            FXRate eurGbpOffer = eurGbpMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( eurGbpBid.getRate () - 0.735 ) < 0.0005 );
            assertTrue ( Math.abs ( eurGbpOffer.getRate () - 0.737 ) < 0.0005 );

            // check for CAD/JPY rate, it should return an Mde which is cross computed from
            FXMarketDataElement cadJpyMde = fxMds.findSpotConversionMarketDataElement ( cad, jpy, false );
            assertNotNull ( cadJpyMde );
            assertTrue ( doSanityCheck ( cadJpyMde, true ) );

            FXRate cadJpyBid = cadJpyMde.getFXPrice ().getBidFXRate ();
            FXRate cadJpyOffer = cadJpyMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( cadJpyBid.getRate () - 84 ) < 0.5 );
            assertTrue ( Math.abs ( cadJpyOffer.getRate () - 85 ) < 0.5 );
        }
        catch ( Exception e )
        {
            fail ( "testRealtimeMDSCrossCurrencyComputationWithMidRate", e );
        }
    }


    public void testCrossCurrencyComputationWithMidRate ( )
    {
        Currency eur = CurrencyFactory.getCurrency ( "EUR" );
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
        Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
        try
        {
            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setRealtime ( true );
            addFXMarketDataElement ( fxMds, eur, usd, 1.2345, 0.0, 1.2367, 0.0, true );
            addFXMarketDataElement ( fxMds, usd, jpy, 111.11, 0.0, 112.12, 0.0, true );
            addFXMarketDataElement ( fxMds, gbp, usd, 1.6785, 0.0, 1.6788, 0.0, true );

            // check for EUR/JPY rate, it should return an Mde which is cross computed from
            FXMarketDataElement eurJpyMde = fxMds.findSpotConversionMarketDataElement ( eur, jpy, false );
            assertNotNull ( eurJpyMde );
            assertTrue ( doSanityCheck ( eurJpyMde, true ) );

            // check for EUR/GBP rate, it should return an Mde which is cross computed from
            FXMarketDataElement eurGbpMde = fxMds.findSpotConversionMarketDataElement ( eur, gbp, false );
            assertNotNull ( eurGbpMde );
            assertTrue ( doSanityCheck ( eurGbpMde, true ) );
        }
        catch ( Exception e )
        {
            fail ( "testCrossCurrencyComputationWithMidRate", e );
        }
    }

    public void testRealtimeMDSSessionInstance ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            testMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestMds = ( FXMarketDataSet ) uow.registerObject ( testMds );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            registeredTestMds.setShortName ( mdsName );
            registeredTestMds.setRealtime ( true );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testMds );
            int origMdsHashCode = testMds.hashCode ();
            assertNotNull ( testMds );
            FXMarketDataSet mdsSessionInstance1 = ( FXMarketDataSet ) testMds.getSessionInstance ();
            assertNotNull ( mdsSessionInstance1 );
            int mdsHashCode1 = mdsSessionInstance1.hashCode ();
            log ( "session instance1=" + mdsSessionInstance1 + ",hashcode1=" + mdsHashCode1 + ",origMdsHashcode=" + origMdsHashCode );
            assertTrue ( origMdsHashCode != mdsHashCode1 );

            addFXMarketDataElement ( mdsSessionInstance1, eur, usd, 1.2345, 0.0, 1.2367, 0.0, false );
            addFXMarketDataElement ( mdsSessionInstance1, usd, jpy, 111.11, 0.0, 112.12, 0.0, false );
            assertEquals ( "mds.size", 2, mdsSessionInstance1.getMarketDataElements ().size () );


            uow = session.acquireUnitOfWork ();
            registeredTestMds = ( FXMarketDataSet ) uow.registerObject ( testMds );
            registeredTestMds.update ();
            uow.commit ();

            MarketDataSet mdsSessionInstance2 = testMds.getSessionInstance ();
            int mdsHashCode2 = mdsSessionInstance2.hashCode ();
            log ( "session instance2=" + mdsSessionInstance2 + ",hashcode=" + mdsHashCode2 );
            assertNotNull ( mdsSessionInstance2 );
            assertEquals ( mdsHashCode1, mdsHashCode2 );
            assertEquals ( "mds.size", 2, mdsSessionInstance2.getMarketDataElements ().size () );

            testMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testMds );
            assertNotNull ( testMds );
            MarketDataSet mdsSessionInstance3 = testMds.getSessionInstance ();
            int mdsHashCode3 = mdsSessionInstance3.hashCode ();
            log ( "session instance3=" + mdsSessionInstance3 + ",hashcode=" + mdsHashCode3 );
            assertNotNull ( mdsSessionInstance3 );
            assertEquals ( mdsHashCode1, mdsHashCode3 );
            assertEquals ( "mds.size", 2, mdsSessionInstance3.getMarketDataElements ().size () );

        }
        catch ( Exception e )
        {
            fail ( "testRealtimeMDSSessionInstance", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDELookupWithAdditionalTenor ( )
    {
        Currency eur = CurrencyFactory.getCurrency ( "EUR" );
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
        Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
        try
        {
            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.2345, 0.0, 1.2367, 0.0, true );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 111.11, 0.0, 112.12, 0.0, true );
            addFXMarketDataElement ( fxMds, stdConv, gbp, usd, 1.6785, 0.0, 1.6788, 0.0, true );

            // add elements for 500 days.
            for ( int i = 1; i < 500; i++ )
            {
                Tenor tenor = new Tenor ( i + "d" );
                addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.2345, 0.0, 1.2367, 0.0, true, tenor );
                addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 111.11, 0.0, 112.12, 0.0, true, tenor );
                addFXMarketDataElement ( fxMds, stdConv, gbp, usd, 1.6785, 0.0, 1.6788, 0.0, true, tenor );
            }

            Map<CurrencyPair, Map<IdcDate, Collection<FXMarketDataElement>>> cpDateMdeMap = new HashMap<CurrencyPair, Map<IdcDate, Collection<FXMarketDataElement>>> ();
            Collection<FXMarketDataElement> mdes = ( Collection<FXMarketDataElement> ) fxMds.getMarketDataElements ();
            for ( FXMarketDataElement fxMde : mdes )
            {
                CurrencyPair cp = fxMde.getCurrencyPair ();
                IdcDate mdeDate = fxMde.getValueDate ();
                Map<IdcDate, Collection<FXMarketDataElement>> dateMdeMap = cpDateMdeMap.get ( cp );
                if ( dateMdeMap == null )
                {
                    dateMdeMap = new HashMap<IdcDate, Collection<FXMarketDataElement>> ();
                    cpDateMdeMap.put ( cp, dateMdeMap );
                }

                Collection<FXMarketDataElement> fxMdes = dateMdeMap.get ( mdeDate );
                if ( fxMdes == null )
                {
                    fxMdes = new ArrayList<FXMarketDataElement> ();
                    dateMdeMap.put ( mdeDate, fxMdes );
                }
                fxMdes.add ( fxMde );
            }

            for ( CurrencyPair cp : cpDateMdeMap.keySet () )
            {
                Map<IdcDate, Collection<FXMarketDataElement>> dateMap = cpDateMdeMap.get ( cp );
                for ( IdcDate dt : dateMap.keySet () )
                {
                    Collection<FXMarketDataElement> elements = dateMap.get ( dt );
                    if ( elements.size () > 1 )
                    {
                        for ( FXMarketDataElement fxMarketDataElement : elements )
                        {
                            FXMask mask = FXMarketDataFactory.newFXMask ( cp, true, dt, fxMarketDataElement.getTenor () );
                            mask.setCalculate ( true );
                            FXMarketDataElement calculatedFXMde = ( FXMarketDataElement ) fxMds.getMarketDataElement ( mask );
                            assertEquals ( "tenor should be equal", calculatedFXMde.getTenor (), fxMarketDataElement.getTenor () );
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            fail ( "testCrossCurrencyComputationWithMidRate", e );
        }
    }

    public void testQueryMdsInMemory ( )
    {
        try
        {
            DataPreloaderC loader = new DataPreloaderC ();
            loader.setVerboseMode ( true );
            loader.run ( null );


            Session tlSession = PersistenceFactory.newSession ();

            ReadAllQuery raq = new ReadAllQuery ();
            raq.setReferenceClass ( FXMarketDataSetC.class );
            raq.setCacheUsage ( ObjectLevelReadQuery.CheckCacheOnly );// only retrieve what is in toplink cache.

            Vector objects = ( Vector ) tlSession.executeQuery ( raq );
            for ( Object object : objects )
            {
                System.out.println ( "mds=" + object );
            }

            ReadAllQuery raq1 = new ReadAllQuery ();
            raq1.setReferenceClass ( FXMarketDataSetC.class );
            raq1.setCacheUsage ( ObjectLevelReadQuery.DoNotCheckCache );

            Vector objects1 = ( Vector ) tlSession.executeQuery ( raq1 );

            assertTrue ( objects.size () > 0 );
            assertTrue ( objects1.size () > 0 );
            assertTrue ( objects1.size () > objects.size () );

        }
        catch ( Exception e )
        {
            fail ( "testQueryMdsInMemory", e );
        }
    }

    public void testMDSSimpleForwardPointsNoCrossCalculation ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.2345, 0.0003, 1.2367, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 111.11, 0.67, 112.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );

            fxMds.setCrossCurrency ( null );
            mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );

            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            fxMds.setCrossCurrency ( usd );
            mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsNoCrossCalculation", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculation ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency aud = CurrencyFactory.getCurrency ( "AUD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.2345, 0.0003, 1.2367, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 111.11, 0.67, 112.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );
            mask.setForwardPointsVehicleCurrency ( aud );
            mde = fxMds.getMarketDataElement ( mask );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculation", e );
        }
    }

    public void testMDSForwardPointsCrossInterpolationCalculation ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency chf = CurrencyFactory.getCurrency ( "CHF" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.2345, 0.0, 1.2367, 0.0, true, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.2345, 0.0003, 1.2367, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 111.11, 0.0, 112.12, 0.0, true, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 111.11, 0.67, 112.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1w = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1w" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1w, new Tenor ( "1w" ) );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask ); // cross calculation is allowed only when vehicle currency is specified.
            assertNull ( mde );
            mask.setForwardPointsVehicleCurrency ( chf );
            mde = fxMds.getMarketDataElement ( mask );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );

        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculation", e );
        }
    }


    public void testMDSSimpleForwardPointsCrossCalculationUsingFwdCurrency ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, null, ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, gbp, 0.9123, 0.0003, 0.9212, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, gbp, jpy, 131.11, 0.67, 132.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            IdcDate valueDate1_1m = stdConv.getFXRateBasis ( eur, gbp ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            IdcDate valueDate2_1m = stdConv.getFXRateBasis ( gbp, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( gbp );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationUsingFwdCurrency", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationUsingCrossCurrency ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, null, ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency chf = CurrencyFactory.getCurrency ( "CHF" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( gbp );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, gbp, 0.9123, 0.0003, 0.9212, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, gbp, jpy, 131.11, 0.67, 132.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );
            mask.setForwardPointsVehicleCurrency ( chf );
            mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );

            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationUsingCrossCurrency", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationUsingDefaultCrossCurrency ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "GBP", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, gbp, 0.9123, 0.0003, 0.9212, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, gbp, jpy, 131.11, 0.67, 132.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationUsingDefaultCrossCurrency", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationUsingDefaultCrossCurrencyWithFwdCurrencyRatesNotAvailable ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "GBP", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, gbp, 0.9123, 0.0003, 0.9212, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, gbp, jpy, 131.11, 0.67, 132.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurUsd = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationUsingDefaultCrossCurrencyWithFwdCurrencyRatesNotAvailable", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testExtrapolationEnabledFlag ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 0.9123, 0.0, 0.9212, 0.0, true, Tenor.SPOT_TENOR );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, usd ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurUsd = CurrencyFactory.newCurrencyPair ( eur, usd );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_1m, new Tenor ( "1m" ) );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );

            fxMds.setInterpolate ( false );
            IdcDate valueDate_TOD = stdConv.getFXRateBasis ( eur, usd ).getValueDate ( DateTimeFactory.newDate (), Tenor.TODAY_TENOR );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_TOD, Tenor.TODAY_TENOR );
            MarketDataElement mde1 = fxMds.getMarketDataElement ( mask1 );
            assertNotNull ( mde1 );

            fxMds.setInterpolate ( true );
            MarketDataElement mde2 = fxMds.getMarketDataElement ( mask1 );
            assertNotNull ( mde2 );

            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 0.9123, 0.0023, 0.9212, 0.0034, true, new Tenor ( "1m" ) );
            IdcDate valueDate_3m = stdConv.getFXRateBasis ( eur, usd ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            FXMask mask3 = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_3m, new Tenor ( "3m" ) );
            MarketDataElement mde3 = fxMds.getMarketDataElement ( mask3 );
            assertNotNull ( mde3 );

            FXMask mask4 = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_TOD, Tenor.TODAY_TENOR );
            MarketDataElement mde4 = fxMds.getMarketDataElement ( mask4 );
            assertNotNull ( mde4 );
        }
        catch ( Exception e )
        {
            fail ( "testExtrapolationEnabledFlag", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testExtrapolationDisabledFlag ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 0.9123, 0.0, 0.9212, 0.0, true, Tenor.SPOT_TENOR );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, usd ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurUsd = CurrencyFactory.newCurrencyPair ( eur, usd );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_1m, new Tenor ( "1m" ) );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );

            fxMds.setInterpolate ( false );
            IdcDate valueDate_TOD = stdConv.getFXRateBasis ( eur, usd ).getValueDate ( DateTimeFactory.newDate (), Tenor.TODAY_TENOR );
            FXMask mask1 = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_TOD, Tenor.TODAY_TENOR );
            MarketDataElement mde1 = fxMds.getMarketDataElement ( mask1 );
            assertNotNull ( mde1 );

            fxMds.setInterpolate ( true );
            MarketDataElement mde2 = fxMds.getMarketDataElement ( mask1 );
            assertNotNull ( mde2 );

            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 0.9123, 0.0023, 0.9212, 0.0034, true, new Tenor ( "1m" ) );
            (( FXMarketDataSetC ) fxMds).resetTransients ();
            IdcDate valueDate_3m = stdConv.getFXRateBasis ( eur, usd ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "3m" ) );
            FXMask mask3 = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_3m, new Tenor ( "3m" ) );
            MarketDataElement mde3 = fxMds.getMarketDataElement ( mask3 );
            assertNull ( mde3 );

            FXMask mask4 = FXMarketDataFactory.newFXMask ( eurUsd, true, valueDate_TOD, Tenor.TODAY_TENOR );
            MarketDataElement mde4 = fxMds.getMarketDataElement ( mask4 );
            assertNotNull ( mde4 );
        }
        catch ( Exception e )
        {
            fail ( "testExtrapolationDisabledFlag", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testWithMDSSimpleForwardPointsCrossCalculationDoubleConventions ( )
    {
        try
        {
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
            staticMds.setBaseDate ( null );

            // add two inverse fx rate basis in std quote convention.
            createAndAddFXRateBasisIfNotPresent ( stdConv, jpy, usd, 6, 2, 2, 3, 2, 2, 10000, 100 );
            createAndAddFXRateBasisIfNotPresent ( stdConv, usd, gbp, 4, 2, 2, 4, 2, 2, 10000, 10000 );
            createAndAddFXRateBasisIfNotPresent ( stdConv, usd, eur, 4, 2, 2, 4, 2, 2, 10000, 10000 );

            FXRateBasis jpyUsdRb = stdConv.getFXRateBasis ( jpy, usd );
            assertNotNull ( jpyUsdRb );
            assertTrue ( jpy.isSameAs ( jpyUsdRb.getBaseCurrency () ) );
            FXRateBasis usdGbpRb = stdConv.getFXRateBasis ( usd, gbp );
            assertNotNull ( usdGbpRb );
            assertTrue ( usd.isSameAs ( usdGbpRb.getBaseCurrency () ) );
            FXRateBasis usdEurRb = stdConv.getFXRateBasis ( usd, eur );
            assertNotNull ( usdEurRb );
            assertTrue ( usd.isSameAs ( usdEurRb.getBaseCurrency () ) );

            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, usd, eur, 0.9123, 0.0, 0.9212, 0.0, true, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( fxMds, stdConv, usd, eur, 0.9123, -0.0023, 0.9212, -0.0034, true, new Tenor ( "1m" ) );

            addFXMarketDataElement ( fxMds, stdConv, jpy, usd, 0.009134, 0.0, 0.009156, 0.0, true, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( fxMds, stdConv, jpy, usd, 0.009134, -0.00023, 0.009156, -0.00034, true, new Tenor ( "3m" ) );

            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            FXMarketDataElement mde = ( FXMarketDataElement ) fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            assertTrue ( eur.isSameAs ( mde.getFXPrice ().getBaseCurrency () ) );
            assertTrue ( jpy.isSameAs ( mde.getFXPrice ().getVariableCurrency () ) );
            assertTrue ( mde.getFXPrice ().getBidFXRate ().getSpotRate () > 90 );
            assertTrue ( mde.getFXPrice ().getBidFXRate ().getRate () > 90 );
            assertTrue ( mde.getFXPrice ().getOfferFXRate ().getSpotRate () > 90 );
            assertTrue ( mde.getFXPrice ().getOfferFXRate ().getRate () > 90 );
        }
        catch ( Exception e )
        {
            fail ( "testWithMDSSimpleForwardPointsCrossCalculationDoubleConventions", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( staticMds );
            IdcUtilC.refreshObject ( stdConv );
        }
    }

    public void testWithMDSSimpleForwardPointsCrossCalculationInverted ( )
    {
        try
        {
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            staticMds.setBaseDate ( null );

            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.1224, 0.0, 1.1245, 0.0, true, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.1224, 0.0023, 1.1245, 0.0034, true, new Tenor ( "1m" ) );

            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 110.34, 0.0, 112.67, 0.0, true, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 110.34, 1.23, 112.67, 1.34, true, new Tenor ( "3m" ) );

            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair jpyEur = CurrencyFactory.newCurrencyPair ( jpy, eur );
            FXMask mask = FXMarketDataFactory.newFXMask ( jpyEur, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            FXMarketDataElement mde = ( FXMarketDataElement ) fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            assertTrue ( jpy.isSameAs ( mde.getFXPrice ().getBaseCurrency () ) );
            assertTrue ( eur.isSameAs ( mde.getFXPrice ().getVariableCurrency () ) );
            assertTrue ( mde.getFXPrice ().getBidFXRate ().getSpotRate () < 0.1 );
            assertTrue ( mde.getFXPrice ().getBidFXRate ().getRate () < 0.1 );
            assertTrue ( mde.getFXPrice ().getOfferFXRate ().getSpotRate () < 0.1 );
            assertTrue ( mde.getFXPrice ().getOfferFXRate ().getRate () < 0.1 );
        }
        catch ( Exception e )
        {
            fail ( "testWithMDSSimpleForwardPointsCrossCalculationInverted", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( staticMds );
            IdcUtilC.refreshObject ( stdConv );
        }
    }

    public void testMDSSimpleForwardPointsNoCrossCalculationFromExtendedMds ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setFXRateConvention ( stdConv );

            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.2345, 0.0003, 1.2367, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 111.11, 0.67, 112.12, 0.78, true, new Tenor ( "1m" ) );

            FXMarketDataSet childMds = FXMarketDataFactory.newFXMarketDataSet ();
            childMds.setCrossCurrency ( null );
            childMds.setAutoInterpolate ( true );
            childMds.setExtendedMarketDataSet ( fxMds );

            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            MarketDataElement mde = childMds.getMarketDataElement ( mask );
            assertNull ( mde );

            childMds.setCrossCurrency ( usd );
            mde = childMds.getMarketDataElement ( mask );
            assertNull ( mde );

            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            childMds.setCrossCurrency ( usd );
            mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsNoCrossCalculationFromExtendedMds", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculation1 ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency aud = CurrencyFactory.getCurrency ( "AUD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.128, 0.00064, 1.12802, 0.000644, true, new Tenor ( "1d" ) );
            addFXMarketDataElement ( fxMds, stdConv, aud, usd, 0.68501, 0.00015, 0.68505, 0.00016, true, new Tenor ( "1d" ) );

            FXRateBasis eurUsd_rb = stdConv.getFXRateBasis ( eur, usd );
            eurUsd_rb.setSpotPrecision ( 5 );
            FXRateBasis audUsd_rb = stdConv.getFXRateBasis ( aud, usd );
            audUsd_rb.setSpotPrecision ( 5 );

            FXRateBasis eurAud_rb = stdConv.getFXRateBasis ( eur, aud );
            eurAud_rb.setSpotPrecision ( 5 );

            IdcDate valueDate_1d = stdConv.getFXRateBasis ( eur, aud ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1d" ) );
            CurrencyPair eurAud = CurrencyFactory.newCurrencyPair ( eur, aud );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurAud, true, valueDate_1d, new Tenor ( "1d" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( aud ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );
            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculation1", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculation2 ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency cad = CurrencyFactory.getCurrency ( "CAD" );
            Currency aud = CurrencyFactory.getCurrency ( "AUD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( cad );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis eurCad_rb = stdConv.getFXRateBasis ( eur, cad );
            eurCad_rb.setSpotPrecision ( 5 );
            FXRateBasis audCad_rb = stdConv.getFXRateBasis ( aud, cad );
            audCad_rb.setSpotPrecision ( 5 );

            FXRateBasis eurAud_rb = stdConv.getFXRateBasis ( eur, aud );
            eurAud_rb.setSpotPrecision ( 5 );

            addFXMarketDataElement ( fxMds, stdConv, eur, cad, 1.12885, 0.000689, 1.12887, 0.000694, true, new Tenor ( "1d" ) );
            addFXMarketDataElement ( fxMds, stdConv, aud, cad, 0.68501, 0.000203, 0.68505, 0.000219, true, new Tenor ( "1d" ) );


            IdcDate valueDate_5d = stdConv.getFXRateBasis ( eur, aud ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "5d" ) );
            CurrencyPair eurAud = CurrencyFactory.newCurrencyPair ( eur, aud );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurAud, true, valueDate_5d, new Tenor ( "5d" ) );
            mask.setForwardPointsVehicleCurrency ( cad );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( aud ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );
            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculation2", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculation3 ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis eurUsd_rb = stdConv.getFXRateBasis ( eur, usd );
            eurUsd_rb.setSpotPrecision ( 5 );

            FXRateBasis usdJpy_rb = stdConv.getFXRateBasis ( usd, jpy );
            usdJpy_rb.setSpotPrecision ( 3 );
            usdJpy_rb.setForwardPointsPrecision ( 2 );

            FXRateBasis eurJpy_rb = stdConv.getFXRateBasis ( eur, jpy );
            eurJpy_rb.setSpotPrecision ( 3 );
            eurJpy_rb.setForwardPointsPrecision ( 2 );

            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.18815, 0.0030, 1.18855, 0.0040, true, new Tenor ( "1d" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 108.35, 0.35, 108.351, 0.45, true, new Tenor ( "1d" ) );


            IdcDate valueDate_1d = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1d" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1d, new Tenor ( "1d" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );
            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculation2", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationWithInterpolation4 ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency cad = CurrencyFactory.getCurrency ( "CAD" );
            Currency aud = CurrencyFactory.getCurrency ( "AUD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( cad );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis eurCad_rb = stdConv.getFXRateBasis ( eur, cad );
            eurCad_rb.setSpotPrecision ( 5 );
            eurCad_rb.setInverseSpotPrecision ( 5 );
            FXRateBasis audCad_rb = stdConv.getFXRateBasis ( aud, cad );
            audCad_rb.setSpotPrecision ( 5 );
            eurCad_rb.setInverseSpotPrecision ( 5 );

            FXRateBasis eurAud_rb = stdConv.getFXRateBasis ( eur, aud );
            eurAud_rb.setSpotPrecision ( 5 );
            eurAud_rb.setInverseSpotPrecision ( 5 );

            addFXMarketDataElement ( fxMds, stdConv, cad, eur, 1.12885, 0.0, 1.12887, 0.0, true, new Tenor ( "SPOT" ) );
            addFXMarketDataElement ( fxMds, stdConv, cad, eur, 1.12885, 0.000639, 1.12887, 0.000644, true, new Tenor ( "4W" ) );
            addFXMarketDataElement ( fxMds, stdConv, cad, aud, 1.15337, 0.0, 1.15339, 0.0, true, new Tenor ( "SPOT" ) );
            addFXMarketDataElement ( fxMds, stdConv, cad, aud, 1.15337, 0.000153, 1.15339, 0.000169, true, new Tenor ( "4W" ) );


            IdcDate valueDate_2w = stdConv.getFXRateBasis ( eur, aud ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "2W" ) );
            CurrencyPair eurAud = CurrencyFactory.newCurrencyPair ( eur, aud );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurAud, true, valueDate_2w, new Tenor ( "2W" ) );
            mask.setForwardPointsVehicleCurrency ( cad );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( aud ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );
            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationWithInterpolation4", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationWithMalformedElement ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency cad = CurrencyFactory.getCurrency ( "CAD" );
            Currency aud = CurrencyFactory.getCurrency ( "AUD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( cad );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis eurCad_rb = stdConv.getFXRateBasis ( eur, cad );
            eurCad_rb.setSpotPrecision ( 5 );
            eurCad_rb.setInverseSpotPrecision ( 5 );
            FXRateBasis audCad_rb = stdConv.getFXRateBasis ( aud, cad );
            audCad_rb.setSpotPrecision ( 5 );
            eurCad_rb.setInverseSpotPrecision ( 5 );

            FXRateBasis eurAud_rb = stdConv.getFXRateBasis ( eur, aud );
            eurAud_rb.setSpotPrecision ( 5 );
            eurAud_rb.setInverseSpotPrecision ( 5 );

            addFXMarketDataElement ( fxMds, stdConv, eur, cad, 1.12885, 0.0, 1.12887, 0.0, true, new Tenor ( "SPOT" ) );
            addFXMarketDataElement ( fxMds, stdConv, eur, cad, 1.12885, 0.000639, 1.12887, 0.000644, true, new Tenor ( "1W" ) );
            addFXMarketDataElement ( fxMds, stdConv, aud, cad, 1.15337, 0.0, 1.15339, 0.0, true, new Tenor ( "SPOT" ) );
            addFXMarketDataElement ( fxMds, stdConv, aud, cad, 1.15337, 0.000153, 1.15339, 0.000169, true, new Tenor ( "1W" ) );

            Collection mdes = fxMds.getMarketDataElements ();
            for ( Object mde : mdes )
            {
                FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
                FXPrice fxPrice = fxMde.getFXPrice ();
                if ( fxMde.getTenor ().equals ( new Tenor ( "1D" ) ) && fxPrice.getBidFXRate ().getBaseCurrency ().isSameAs ( aud ) )
                {
                    fxPrice.setBidFXRate ( null );
                }
            }

            IdcDate valueDate_1d = stdConv.getFXRateBasis ( eur, aud ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1D" ) );
            CurrencyPair eurAud = CurrencyFactory.newCurrencyPair ( eur, aud );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurAud, true, valueDate_1d, new Tenor ( "1D" ) );
            mask.setForwardPointsVehicleCurrency ( cad );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );


            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcDate valueDate_2w = stdConv.getFXRateBasis ( eur, aud ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "2W" ) );
            mask = FXMarketDataFactory.newFXMask ( eurAud, true, valueDate_2w, new Tenor ( "2W" ) );
            mask.setForwardPointsVehicleCurrency ( cad );
            mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( aud ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );
            log ( "*************** fxPrice=" + fxPriceStr );

            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );

            mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );
            mask = FXMarketDataFactory.newFXMask ( eurAud, true, eurAud_rb.getSpotDate ( DateTimeFactory.newDate () ), new Tenor ( "SPOT" ) );
            mask.setForwardPointsVehicleCurrency ( cad );
            mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationWithMalformedElement", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_EXTRAPOLATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }


    public void testMDSSimpleForwardPointsCrossCalculationExample1 ( )
    {
        try
        {
            Currency aud = CurrencyFactory.getCurrency ( "AUD" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis audUsd_rb = stdConv.getFXRateBasis ( aud, usd );
            audUsd_rb.setSpotPrecision ( 5 );

            FXRateBasis usdJpy_rb = stdConv.getFXRateBasis ( usd, jpy );
            usdJpy_rb.setSpotPrecision ( 3 );
            usdJpy_rb.setForwardPointsPrecision ( 2 );

            FXRateBasis eurJpy_rb = stdConv.getFXRateBasis ( aud, jpy );
            eurJpy_rb.setSpotPrecision ( 3 );
            eurJpy_rb.setForwardPointsPrecision ( 2 );

            addFXMarketDataElement ( fxMds, stdConv, aud, usd, 0.68501, 0.00015, 0.68505, 0.00016, true, new Tenor ( "1d" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 108.35, -0.0575, 108.351, -0.057, true, new Tenor ( "1d" ) );


            IdcDate valueDate_1d = stdConv.getFXRateBasis ( aud, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1d" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( aud, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1d, new Tenor ( "1d" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( aud ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );

            FXRate bid = fxMde.getFXPrice ().getBidFXRate ();
            assertNotNull ( bid );
            assertTrue ( Math.abs ( bid.getSpotRate () - 74.221 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getForwardPoints () + 0.02331 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getRate () - 74.19769 ) < 0.00000001 );
            FXRate offer = fxMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( offer.getSpotRate () - 74.226 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getForwardPoints () + 0.02187 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getRate () - 74.20413 ) < 0.00000001 );

            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculation2", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationExample2 ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency cad = CurrencyFactory.getCurrency ( "CAD" );
            Currency aud = CurrencyFactory.getCurrency ( "AUD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis eurCad_rb = stdConv.getFXRateBasis ( eur, cad );
            eurCad_rb.setSpotPrecision ( 5 );

            FXRateBasis audCad_rb = stdConv.getFXRateBasis ( aud, cad );
            audCad_rb.setSpotPrecision ( 5 );
            audCad_rb.setForwardPointsPrecision ( 2 );

            FXRateBasis eurAud_rb = stdConv.getFXRateBasis ( eur, aud );
            eurAud_rb.setSpotPrecision ( 5 );
            eurAud_rb.setForwardPointsPrecision ( 2 );

            addFXMarketDataElement ( fxMds, stdConv, eur, cad, 1.128, 0.00064, 1.12802, 0.000644, true, new Tenor ( "1d" ) );
            addFXMarketDataElement ( fxMds, stdConv, aud, cad, 0.68501, 0.00015, 0.68505, 0.00016, true, new Tenor ( "1d" ) );


            IdcDate valueDate_1d = stdConv.getFXRateBasis ( eur, aud ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1d" ) );
            CurrencyPair eurAud = CurrencyFactory.newCurrencyPair ( eur, aud );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurAud, true, valueDate_1d, new Tenor ( "1d" ) );
            mask.setForwardPointsVehicleCurrency ( cad );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( aud ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );

            FXRate bid = fxMde.getFXPrice ().getBidFXRate ();
            assertNotNull ( bid );
            assertTrue ( Math.abs ( bid.getSpotRate () - 1.6466 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getForwardPoints () - 0.0005447 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getRate () - 1.6471447 ) < 0.00000001 );
            FXRate offer = fxMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( offer.getSpotRate () - 1.64672 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getForwardPoints () - 0.0005799 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getRate () - 1.6472999 ) < 0.00000001 );

            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationExample2", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationExample3 ( )
    {
        try
        {
            Currency chf = CurrencyFactory.getCurrency ( "CHF" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis usdChf_rb = stdConv.getFXRateBasis ( chf, usd );
            usdChf_rb.setSpotPrecision ( 5 );

            FXRateBasis usdJpy_rb = stdConv.getFXRateBasis ( usd, jpy );
            usdJpy_rb.setSpotPrecision ( 3 );
            usdJpy_rb.setForwardPointsPrecision ( 3 );

            FXRateBasis chfJpy_rb = stdConv.getFXRateBasis ( chf, jpy );
            chfJpy_rb.setSpotPrecision ( 3 );
            chfJpy_rb.setForwardPointsPrecision ( 3 );

            addFXMarketDataElement ( fxMds, stdConv, usd, chf, 0.998, -0.000642, 0.99803, -0.000638, true, new Tenor ( "1d" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 108.35, -0.0575, 108.351, -0.057, true, new Tenor ( "1d" ) );


            IdcDate valueDate_1d = stdConv.getFXRateBasis ( chf, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1d" ) );
            CurrencyPair chfJpy = CurrencyFactory.newCurrencyPair ( chf, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( chfJpy, true, valueDate_1d, new Tenor ( "1d" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( chf ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );

            FXRate bid = fxMde.getFXPrice ().getBidFXRate ();
            assertNotNull ( bid );
            assertTrue ( Math.abs ( bid.getSpotRate () - 108.564 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getForwardPoints () - 0.011665 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getRate () - 108.575665 ) < 0.00000001 );
            FXRate offer = fxMde.getFXPrice ().getOfferFXRate ();
            assertTrue ( Math.abs ( offer.getSpotRate () - 108.568 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getForwardPoints () - 0.012871 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getRate () - 108.580871 ) < 0.00000001 );

            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationExample3", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationExample2_Inverse ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency cad = CurrencyFactory.getCurrency ( "CAD" );
            Currency aud = CurrencyFactory.getCurrency ( "AUD" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis eurCad_rb = stdConv.getFXRateBasis ( eur, cad );
            eurCad_rb.setSpotPrecision ( 5 );
            eurCad_rb.setInverseSpotPrecision ( 5 );

            FXRateBasis audCad_rb = stdConv.getFXRateBasis ( aud, cad );
            audCad_rb.setSpotPrecision ( 5 );
            audCad_rb.setInverseSpotPrecision ( 5 );
            audCad_rb.setForwardPointsPrecision ( 2 );

            FXRateBasis eurAud_rb = stdConv.getFXRateBasis ( eur, aud );
            eurAud_rb.setSpotPrecision ( 5 );
            eurAud_rb.setInverseSpotPrecision ( 5 );
            eurAud_rb.setForwardPointsPrecision ( 2 );

            addFXMarketDataElement ( fxMds, stdConv, cad, eur, 0.685, 0.0, 0.688, 0.0, true, new Tenor ( "1d" ) );
            addFXMarketDataElement ( fxMds, stdConv, aud, cad, 0.893, 0.0, 0.898, 0.0, true, new Tenor ( "1d" ) );


            IdcDate valueDate_1d = stdConv.getFXRateBasis ( eur, aud ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1d" ) );
            CurrencyPair eurAud = CurrencyFactory.newCurrencyPair ( eur, aud );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurAud, true, valueDate_1d, new Tenor ( "1d" ) );
            mask.setForwardPointsVehicleCurrency ( cad );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( aud ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );

            FXRate bid = fxMde.getFXPrice ().getBidFXRate ();
            FXRate offer = fxMde.getFXPrice ().getOfferFXRate ();
            assertNotNull ( bid );

            assertTrue ( Math.abs ( bid.getSpotRate () - 1.61858 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getForwardPoints () - 0.0 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getRate () - 1.61858 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getSpotRate () - 1.63477 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getForwardPoints () - 0.0 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getRate () - 1.63477 ) < 0.00000001 );

            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationExample2_Inverse", e );
        }
    }

    public void testMDSSimpleForwardPointsCrossCalculationExample2_Inverse1 ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            fxMds.setFXRateConvention ( stdConv );

            FXRateBasis eurJpy_rb = stdConv.getFXRateBasis ( eur, jpy );
            eurJpy_rb.setSpotPrecision ( 3 );
            eurJpy_rb.setInverseSpotPrecision ( 6 );
            eurJpy_rb.setForwardPointsPrecision ( 3 );
            eurJpy_rb.setInverseForwardPointsPrecision ( 3 );

            FXRateBasis eurUsd_rb = stdConv.getFXRateBasis ( eur, usd );
            eurUsd_rb.setSpotPrecision ( 5 );
            eurUsd_rb.setInverseSpotPrecision ( 5 );
            eurUsd_rb.setForwardPointsPrecision ( 2 );

            FXRateBasis usdJpy_rb = stdConv.getFXRateBasis ( usd, jpy );
            usdJpy_rb.setSpotPrecision ( 3 );
            usdJpy_rb.setInverseSpotPrecision ( 6 );
            usdJpy_rb.setForwardPointsPrecision ( 3 );
            usdJpy_rb.setInverseForwardPointsPrecision ( 3 );

            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 108.12, 0.0, 109.98, 0.0, true, new Tenor ( "1d" ) );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.2345, 0.0, 1.2678, 0.0, true, new Tenor ( "1d" ) );


            IdcDate valueDate_1d = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1d" ) );
            CurrencyPair jpyEur = CurrencyFactory.newCurrencyPair ( jpy, eur );
            FXMask mask = FXMarketDataFactory.newFXMask ( jpyEur, true, valueDate_1d, new Tenor ( "1d" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( jpy ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( eur ) );
            String fxPriceStr = getToString ( fxMde.getFXPrice () );

            FXRate bid = fxMde.getFXPrice ().getBidFXRate ();
            FXRate offer = fxMde.getFXPrice ().getOfferFXRate ();
            assertNotNull ( bid );

            assertTrue ( Math.abs ( bid.getSpotRate () - 0.007172 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getForwardPoints () - 0.0 ) < 0.00000001 );
            assertTrue ( Math.abs ( bid.getRate () - 0.007172 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getSpotRate () - 0.007492 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getForwardPoints () - 0.0 ) < 0.00000001 );
            assertTrue ( Math.abs ( offer.getRate () - 0.007492 ) < 0.00000001 );

            log ( "*************** fxPrice=" + fxPriceStr );
        }
        catch ( Exception e )
        {
            fail ( "testMDSSimpleForwardPointsCrossCalculationExample2_Inverse1", e );
        }
    }

    private void deleteMDS ( MarketDataSet mds )
    {
        try
        {
            if ( mds != null )
            {
                EntityServiceFactory.getEntityService ().deleteObject ( mds );
            }
        }
        catch ( Exception e )
        {
            log.error ( "deleteMds : Exception ", e );
        }
    }

    public String getToString ( FXPrice fxPrice )
    {
        StringBuilder sb = new StringBuilder ( 200 );
        if ( fxPrice.getBidFXRate () != null )
        {
            sb.append ( "bid=" ).append ( getToString ( fxPrice.getBidFXRate () ) );
        }
        if ( fxPrice.getOfferFXRate () != null )
        {
            sb.append ( ",offer=" ).append ( getToString ( fxPrice.getOfferFXRate () ) );
        }
        if ( fxPrice.getMidFXRate () != null )
        {
            sb.append ( ",mid=" ).append ( getToString ( fxPrice.getMidFXRate () ) );
        }
        return sb.toString ();
    }

    public String getToString ( FXRate fxRate )
    {
        return new StringBuilder ( 100 ).append ( fxRate.getCurrencyPair () ).append ( ':' ).append ( fxRate.getSpotRateFormat ( null ).format ( fxRate.getSpotRate () ) ).append ( ':' )
                .append ( fxRate.getForwardPointsFormat ( null ).format ( fxRate.getForwardPoints () ) ).append ( ':' ).append ( fxRate.getRateFormat ( null ).format ( fxRate.getRate () ) ).toString ();
    }

    public void testFXMarketDataElementTenorCopy ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            testMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestMds = ( FXMarketDataSet ) uow.registerObject ( testMds );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            registeredTestMds.setShortName ( mdsName );

            Tenor tenor1 = TenorFactory.getTenor ( "1m" );
            Tenor tenor2 = TenorFactory.getTenor ( "2m" );
            FXMarketDataElement mde1 = createFXMarketDataElement ( stdConv, eur, usd, 1.11, 0.0033, 2.22, 0.0034, tenor1, false );
            addFXMarketDataElement ( registeredTestMds, mde1 );

            FXMarketDataElement mde2 = createFXMarketDataElement ( stdConv, usd, jpy, 100.11, 0.33, 102.22, 0.34, tenor2, false );
            mde2.setTenor ( tenor1 );
            addFXMarketDataElement ( registeredTestMds, mde2 );

            uow.commit ();

            // refresh the trade and retrieve the maker request.
            testMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testMds );
            Collection<FXMarketDataElement> mdes = testMds.getMarketDataElements ();
            assertTrue ( mdes.size () == 2 );
            for ( FXMarketDataElement mde : mdes )
            {
                assertTrue ( mde.getTenor ().equals ( tenor1 ) );
                FXMarketDataElement refreshedMde = ( FXMarketDataElement ) IdcUtilC.refreshObject ( mde );
                assertNotNull ( refreshedMde );
                assertTrue ( refreshedMde.getTenor ().equals ( tenor1 ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testFXMarketDataElementTenorCopy", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventSimpleAdd ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            //verify the new elements are there.
            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSimpleAdd", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventSimpleModify ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent1 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSimpleModify", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventAdd ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            //verify the new elements are there.
            assertEquals ( 2, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventAdd", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventModify ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.MODIFY, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 2, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventModify", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairAdd ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            //verify the new elements are there.
            assertEquals ( 4, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairAdd", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairModify ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.MODIFY, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 4, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairModify", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventSimpleDelete ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            //verify the new elements are there.
            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );

            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.DELETE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );

            assertEquals ( 0, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSimpleDelete", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventDelete ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.DELETE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventDelete", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventSpotDelete ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.DELETE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 0, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSpotDelete", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairDelete ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.DELETE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 3, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairDelete", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairSpotDelete ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.DELETE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 2, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairSpotDelete", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventSimpleStale ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            //verify the new elements are there.
            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );

            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.STALE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );

            assertEquals ( 0, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSimpleStale", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventStale ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.STALE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventStale", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }


    public void testMDSUpdateEventSpotStale ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.STALE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 0, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSpotStale", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairStale ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.STALE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 3, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairStale", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairSpotStale ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.STALE, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 2, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairSpotStale", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventSimpleLogout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            //verify the new elements are there.
            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );

            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.LOGOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );

            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSimpleLogout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventLogout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.LOGOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 2, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventLogout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }


    public void testMDSUpdateEventSpotLogout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.LOGOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 2, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSpotLogout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairLogout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.LOGOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 4, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairLogout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairSpotLogout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.LOGOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 4, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairSpotLogout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventSimpleTimeout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            //verify the new elements are there.
            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );

            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.TIMEOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );

            assertEquals ( 1, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSimpleTimeout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventTimeout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.TIMEOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 2, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventTimeout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }


    public void testMDSUpdateEventSpotTimeout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.TIMEOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent3 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 2, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventSpotTimeout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairTimeout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.TIMEOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 4, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairTimeout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSUpdateEventMultiCcyPairSpotTimeout ( )
    {
        FXMarketDataSet testMds = null;
        try
        {
            TimeZone.setDefault ( TimeZone.getTimeZone ( "GMT" ) );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair ( eur, usd );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair ( usd, jpy );
            String mdsName = "TestMDS" + System.currentTimeMillis ();
            testMds = createMDSInDatabase ( mdsName, IdcUtilC.MAIN_NAMESPACE, true );
            assertNotNull ( testMds );

            FXMDSUpdateEvent mdsUpdateEvent1 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent2 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "1M", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent3 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "SPOT", null, 1.1111, 1.2222, null );
            FXMDSUpdateEvent mdsUpdateEvent4 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.ADD, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), usdJpy.getName (), "1M", null, 1.1111, 1.2222, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents.add ( mdsUpdateEvent1 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent2 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent3 );
            fxmdsUpdateEvents.add ( mdsUpdateEvent4 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents );

            FXMDSUpdateEvent mdsUpdateEvent5 = MDSUpdateHelper.getInstance ().getFXMDSUpdateEvent ( MDSUpdateEvent.Type.TIMEOUT, mdsName, IdcUtilC.MAIN_NAMESPACE, MDSUpdateEvent.Source.EXCEL.getId (), eurUsd.getName (), "SPOT", null, 1.3333, 1.4444, null );
            List<FXMDSUpdateEvent> fxmdsUpdateEvents1 = new ArrayList<FXMDSUpdateEvent> ();
            fxmdsUpdateEvents1.add ( mdsUpdateEvent5 );
            MDSUtil.updateMDSElements ( testMds, fxmdsUpdateEvents1 );


            //verify the new elements are there.
            assertEquals ( 4, testMds.getSessionInstance ().getMarketDataElements ().size () );
            FXMarketDataElement fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( usd, eur ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, false );
            assertNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( CurrencyFactory.getCurrencyPair ( jpy, usd ), Tenor.SPOT_TENOR, true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), false );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( eurUsd, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
            fxMde = testMds.getFXMarketDataElement ( usdJpy, TenorFactory.getTenor ( "1M" ), true );
            assertNotNull ( fxMde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSUpdateEventMultiCcyPairSpotTimeout", e );
        }
        finally
        {
            deleteMDS ( testMds );
        }
    }

    public void testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate1 ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, gbp, 0.0, 0.0003, 0.0, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, gbp, jpy, 131.11, 0.67, 132.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( gbp );
            mask.setReplaceZeroSpotRatesWithBenchmarkRate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            log ( "crossMde=" + fxMde.getToString () );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            assertTrue ( "bidFwdPoints=" + fxMde.getFXPrice ().getBidFXRate ().getForwardPoints (), fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () < 1 );
            assertTrue ( "offerFwdPoints=" + fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints (), fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () < 1 );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate1", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate2 ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.1, 0.0003, 0.0, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 0.0, 0.0067, 132.12, 0.0078, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            mask.setReplaceZeroSpotRatesWithBenchmarkRate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            log ( "crossMde=" + fxMde.getToString () );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            assertTrue ( "bidFwdPoints=" + fxMde.getFXPrice ().getBidFXRate ().getForwardPoints (), fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () < 1 );
            assertTrue ( "offerFwdPoints=" + fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints (), fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () < 1 );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate2", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate3 ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency cad = CurrencyFactory.getCurrency ( "CAD" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, usd, cad, 1.1, 0.0003, 0.0, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, jpy, 0.0, 0.67, 132.12, 0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( cad, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair cadJpy = CurrencyFactory.newCurrencyPair ( cad, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( cadJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            mask.setReplaceZeroSpotRatesWithBenchmarkRate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            log ( "crossMde=" + fxMde.getToString () );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( cad ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            assertTrue ( "bidFwdPoints=" + fxMde.getFXPrice ().getBidFXRate ().getForwardPoints (), fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () < 1 );
            assertTrue ( "offerFwdPoints=" + fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints (), fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () < 1 );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate3", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate4 ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 0.0, 0.0003, 0.0, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, gbp, usd, 1.52, 0.0067, 1.53, 0.0078, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, gbp ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurGbp = CurrencyFactory.newCurrencyPair ( eur, gbp );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurGbp, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            mask.setReplaceZeroSpotRatesWithBenchmarkRate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            log ( "crossMde=" + fxMde.getToString () );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( gbp ) );
            assertTrue ( "bidFwdPoints=" + fxMde.getFXPrice ().getBidFXRate ().getForwardPoints (), fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () < 1 );
            assertTrue ( "offerFwdPoints=" + fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints (), fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () < 1 );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate4", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate1_Inverse ( )
    {
        try
        {
            IdcUtilC.refreshObject ( staticMds );
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, gbp, eur, 0.0, -0.0004, 0.0, -0.0003, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, jpy, gbp, 131.11, -0.000018, 132.12, -0.000016, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( gbp );
            mask.setReplaceZeroSpotRatesWithBenchmarkRate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            log ( "crossMde=" + fxMde.getToString () );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            assertTrue ( "bidFwdPoints=" + fxMde.getFXPrice ().getBidFXRate ().getForwardPoints (), fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () < 1 );
            assertTrue ( "offerFwdPoints=" + fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints (), fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () < 1 );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate1_Inverse", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate2_Inverse ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, usd, eur, 1.1, -0.0003, 0.0, -0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, jpy, usd, 0.0, -0.000012, 132.12, -0.000014, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurJpy = CurrencyFactory.newCurrencyPair ( eur, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            mask.setReplaceZeroSpotRatesWithBenchmarkRate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            log ( "crossMde=" + fxMde.getToString () );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            assertTrue ( "bidFwdPoints=" + fxMde.getFXPrice ().getBidFXRate ().getForwardPoints (), fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () < 1 );
            assertTrue ( "offerFwdPoints=" + fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints (), fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () < 1 );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate2_Inverse", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcUtilC.refreshObject ( stdConv );
        }
    }

    public void testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate3_Inverse ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency cad = CurrencyFactory.getCurrency ( "CAD" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, cad, usd, 1.1, 0.0003, 0.0, 0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, jpy, usd, 0.0, -0.67, 132.12, -0.78, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( cad, jpy ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair cadJpy = CurrencyFactory.newCurrencyPair ( cad, jpy );
            FXMask mask = FXMarketDataFactory.newFXMask ( cadJpy, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            mask.setReplaceZeroSpotRatesWithBenchmarkRate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            log ( "crossMde=" + fxMde.getToString () );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( cad ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( jpy ) );
            assertTrue ( "bidFwdPoints=" + fxMde.getFXPrice ().getBidFXRate ().getForwardPoints (), fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () < 1 );
            assertTrue ( "offerFwdPoints=" + fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints (), fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () < 1 );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate3_Inverse", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcUtilC.refreshObject ( staticMds );
        }
    }

    public void testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate4_Inverse ( )
    {
        try
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( null );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, usd, eur, 0.0, -0.0003, 0.0, -0.0004, true, new Tenor ( "1m" ) );
            addFXMarketDataElement ( fxMds, stdConv, usd, gbp, 1.52, -0.0067, 1.53, -0.0078, true, new Tenor ( "1m" ) );


            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, gbp ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurGbp = CurrencyFactory.newCurrencyPair ( eur, gbp );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurGbp, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setForwardPointsVehicleCurrency ( usd );
            mask.setReplaceZeroSpotRatesWithBenchmarkRate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNotNull ( mde );
            FXMarketDataElement fxMde = ( FXMarketDataElement ) mde;
            log ( "crossMde=" + fxMde.getToString () );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getBaseCurrency ().isSameAs ( eur ) );
            assertTrue ( fxMde.getFXPrice ().getBidFXRate ().getVariableCurrency ().isSameAs ( gbp ) );
            assertTrue ( "bidFwdPoints=" + fxMde.getFXPrice ().getBidFXRate ().getForwardPoints (), fxMde.getFXPrice ().getBidFXRate ().getForwardPoints () < 1 );
            assertTrue ( "offerFwdPoints=" + fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints (), fxMde.getFXPrice ().getOfferFXRate ().getForwardPoints () < 1 );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsCrossCalculationUsingFwdCurrency_ZeroSpotRate4_Inverse", e );
        }
        finally
        {
            WatchPropertyC.update ( MarketDataConfigurationMBean.MARKET_DATA_SET_FORWARD_POINTS_DEFAULT_VEHICLE_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcUtilC.refreshObject ( staticMds );
        }
    }

    public void testMDSForwardPointsInterpolateOnly ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );

            // Add EUR/USD, USD/JPY elements
            FXMarketDataSet fxMds = FXMarketDataFactory.newFXMarketDataSet ();
            fxMds.setCrossCurrency ( usd );
            fxMds.setAutoInterpolate ( true );
            fxMds.setShortName ( "TEST" + System.currentTimeMillis () );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 1.1212, 0.0, 1.1313, 0.00, true, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( fxMds, stdConv, gbp, usd, 1.5224, 0.0, 1.5336, 0.0, true, Tenor.SPOT_TENOR );
            addFXMarketDataElement ( fxMds, stdConv, eur, usd, 0.0, 0.0003, 0.0, 0.0004, true, new Tenor ( "6m" ) );
            addFXMarketDataElement ( fxMds, stdConv, gbp, usd, 1.52, 0.0067, 1.53, 0.0078, true, new Tenor ( "6m" ) );

            IdcDate valueDate_1m = stdConv.getFXRateBasis ( eur, gbp ).getValueDate ( DateTimeFactory.newDate (), new Tenor ( "1m" ) );
            CurrencyPair eurGbp = CurrencyFactory.newCurrencyPair ( eur, gbp );
            FXMask mask = FXMarketDataFactory.newFXMask ( eurGbp, true, valueDate_1m, new Tenor ( "1m" ) );
            mask.setCalculate ( true );
            MarketDataElement mde = fxMds.getMarketDataElement ( mask );
            assertNull ( mde );
        }
        catch ( Exception e )
        {
            fail ( "testMDSForwardPointsInterpolateOnly", e );
        }
    }

    private FXMarketDataSet createMDSInDatabase ( String ns, String sn, boolean realtime )
    {
        FXMarketDataSet testMds = null;
        try
        {
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( CurrencyC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            uow.addReadOnlyClass ( NamespaceC.class );
            testMds = new FXMarketDataSetC ();
            FXMarketDataSet registeredTestMds = ( FXMarketDataSet ) uow.registerObject ( testMds );
            registeredTestMds.setShortName ( sn );
            registeredTestMds.setNamespace ( ReferenceDataCacheC.getInstance ().getNamespace ( ns ) );
            registeredTestMds.setRealtime ( realtime );

            uow.commit ();
            testMds = ( FXMarketDataSet ) IdcUtilC.refreshObject ( testMds );
            assertNotNull ( testMds );
        }
        catch ( Exception e )
        {
            fail ( "createMDSInDatabase", e );
        }
        return testMds;
    }

    public void testFixedPeriodMDSUniqueness ( )
    {
        try
        {
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( NamespaceC.class );
            FXMarketDataSet testMds = new FXMarketDataSetC ();

            String mdsName = "Test" + System.currentTimeMillis ();
            IdcDate baseDate = DateTimeFactory.newDate ();
            FXMarketDataSet registeredTestMds = ( FXMarketDataSet ) uow.registerObject ( testMds );
            registeredTestMds.setShortName ( mdsName );
            registeredTestMds.setStatus ( 'T' );
            registeredTestMds.setFixedPeriod ( true );
            registeredTestMds.setBaseDate ( baseDate );
            registeredTestMds.setNamespace ( ReferenceDataCacheC.getInstance ().getMAINNamespace () );

            long now = System.currentTimeMillis ();
            Timestamp start = new Timestamp ( now - 60000 );
            Timestamp end = new Timestamp ( now + 60000 );
            registeredTestMds.setStartTime ( start );
            registeredTestMds.setEndTime ( end );
            uow.commit ();

            testMds = ( FXMarketDataSet ) session.refreshObject ( testMds );
            assertNotNull ( testMds );

            //now create another mds with the same attributes.
            uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( NamespaceC.class );
            FXMarketDataSet testMds1 = new FXMarketDataSetC ();

            FXMarketDataSet registeredTestMds1 = ( FXMarketDataSet ) uow.registerObject ( testMds1 );
            registeredTestMds1.setShortName ( mdsName );
            registeredTestMds1.setStatus ( 'T' );
            registeredTestMds1.setFixedPeriod ( true );
            registeredTestMds1.setBaseDate ( baseDate );
            registeredTestMds1.setNamespace ( ReferenceDataCacheC.getInstance ().getMAINNamespace () );

            registeredTestMds1.setStartTime ( start );
            registeredTestMds1.setEndTime ( end );
            try
            {
                uow.commit ();
                fail ( "testFixedPeriodMDSUniqueness - should not have committed successfully." );
            }
            catch ( Exception ex )
            {

            }

            // now create a new mds with a different start time.
            uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( NamespaceC.class );
            FXMarketDataSet testMds2 = new FXMarketDataSetC ();

            FXMarketDataSet registeredTestMds2 = ( FXMarketDataSet ) uow.registerObject ( testMds2 );
            registeredTestMds2.setShortName ( mdsName );
            registeredTestMds2.setStatus ( 'T' );
            registeredTestMds2.setFixedPeriod ( true );
            registeredTestMds2.setBaseDate ( baseDate );
            registeredTestMds2.setNamespace ( ReferenceDataCacheC.getInstance ().getMAINNamespace () );

            registeredTestMds2.setStartTime ( new Timestamp ( now - 1000 )  );
            registeredTestMds2.setEndTime ( end );
            uow.commit ();
            testMds2 = ( FXMarketDataSet ) session.refreshObject ( testMds2 );
            assertNotNull ( testMds2 );
        }
        catch ( Exception e )
        {
            fail ( "Exception in testFixedPeriodMDSUniqueness : ", e );
        }
    }

    public void testNonFixedPeriodMDSUniqueness ( )
    {
        try
        {
            Session session = getPersistenceSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( NamespaceC.class );
            FXMarketDataSet testMds = new FXMarketDataSetC ();

            String mdsName = "Test" + System.currentTimeMillis ();
            IdcDate baseDate = DateTimeFactory.newDate ();
            FXMarketDataSet registeredTestMds = ( FXMarketDataSet ) uow.registerObject ( testMds );
            registeredTestMds.setShortName ( mdsName );
            registeredTestMds.setStatus ( 'T' );
            registeredTestMds.setBaseDate ( baseDate );
            registeredTestMds.setNamespace ( ReferenceDataCacheC.getInstance ().getMAINNamespace () );

            uow.commit ();

            testMds = ( FXMarketDataSet ) session.refreshObject ( testMds );
            assertNotNull ( testMds );

            //now create another mds with the same attributes.
            uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( NamespaceC.class );
            FXMarketDataSet testMds1 = new FXMarketDataSetC ();

            FXMarketDataSet registeredTestMds1 = ( FXMarketDataSet ) uow.registerObject ( testMds1 );
            registeredTestMds1.setShortName ( mdsName );
            registeredTestMds1.setStatus ( 'T' );
            registeredTestMds1.setBaseDate ( baseDate );
            registeredTestMds1.setNamespace ( ReferenceDataCacheC.getInstance ().getMAINNamespace () );

            try
            {
                uow.commit ();
                fail ( "testNonFixedPeriodMDSUniqueness - should not have committed successfully." );
            }
            catch ( Exception ex )
            {

            }

            // now create a new mds with a different start time.
            uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( NamespaceC.class );
            FXMarketDataSet testMds2 = new FXMarketDataSetC ();

            FXMarketDataSet registeredTestMds2 = ( FXMarketDataSet ) uow.registerObject ( testMds2 );
            registeredTestMds2.setShortName ( mdsName );
            registeredTestMds2.setStatus ( 'T' );
            registeredTestMds2.setFixedPeriod ( true );
            registeredTestMds2.setBaseDate ( baseDate.addDays ( 1 ) );
            registeredTestMds2.setNamespace ( ReferenceDataCacheC.getInstance ().getMAINNamespace () );

            uow.commit ();
            testMds2 = ( FXMarketDataSet ) session.refreshObject ( testMds2 );
            assertNotNull ( testMds2 );
        }
        catch ( Exception e )
        {
            fail ( "Exception in testNonFixedPeriodMDSUniqueness : ", e );
        }
    }
}
