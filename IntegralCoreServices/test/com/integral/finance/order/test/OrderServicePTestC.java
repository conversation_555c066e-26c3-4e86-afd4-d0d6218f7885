package com.integral.finance.order.test;

import com.integral.finance.businessCenter.EndOfDayService;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.order.*;
import com.integral.message.MessageStatus;
import com.integral.message.query.*;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.test.DealingPTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDateTime;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import com.integral.workflow.State;
import com.integral.workflow.StateC;
import com.integral.workflow.WorkflowFactory;
import com.integral.workflow.WorkflowStateMap;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.List;

/**
 * Tester for OrderService.
 *
 * @see OrderService
 * @since 2009-10-04
 */
@SuppressWarnings( {"deprecation"} )
public final class OrderServicePTestC extends DealingPTestCaseC
{

    private OrderService orderService = null;
    private static final int TRADE_AMT = 1000;

    /**
     * Creates tester.
     *
     * @param aName name
     */
    public OrderServicePTestC( final String aName )
    {
        super(aName);
    }


    /**
     * Tests {@link OrderService#getInstance()}.
     */
    public void testGetInstance()
    {
        assertNotNull( orderService );
    }


    /**
     * Tests {@link OrderService#queryOrdersByExample(QueryMessage)}
     */
    public void testQueryOrdersByExample() throws Exception
    {
        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );
        final WorkflowStateMap map = request.getWorkflowStateMap();
        map.setTimeOfLastStateChange( DateTimeFactory.newDateTime() );

        uow.refreshObject( request );
        tx.commit();

        // Create date range as one day before today to one day after today
        final Calendar startDate = Calendar.getInstance();
        startDate.add(Calendar.DAY_OF_MONTH, -1);

        final Calendar endDate = Calendar.getInstance();
        endDate.add(Calendar.DAY_OF_MONTH, 1);

        // Prepare method parameters
        final SimpleDateFormat dateRangeFormat = OrderServiceUtil.createDateRangeFormat();
        final Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        params.add( createDateParameter( OrderQueryParameters.PARAM_FROM_LAST_STATE_CHANGE_DATE, startDate, dateRangeFormat ) );
        params.add(createDateParameter(OrderQueryParameters.PARAM_TO_LAST_STATE_CHANGE_DATE, endDate, dateRangeFormat));
        params.add(createParameter(OrderQueryParameters.PARAM_ORG_SHORT_NAME_LIST, request.getOrganization().getShortName()));
        params.add( createParameter( OrderQueryParameters.PARAM_ORG_SHORT_NAME, request.getOrganization().getShortName() ) );
        params.add( createParameter( OrderQueryParameters.PARAM_ACCT_SHORT_NAME_LIST, request.getCounterparty().getShortName() ) );
        final QueryMessage msg = OrderService.getInstance().createQueryMessage( OrderService.GET_ORDERS_BY_ACCOUNT, params );

        // //
        // //
        final QueryMessage queryMessage = orderService.queryOrdersByExample(msg);
        // //
        // //

        assertEquals( MessageStatus.SUCCESS, queryMessage.getStatus() );

        // Check if there is result
        final QueryResult result = queryMessage.getQueryResult();
        final List items = result.getResultItems();
        assertTrue("Number of items should be greater than zero", items.size() > 0);

        // Check result is of expected type
        final Class<ResultItem> expectedItemClass = ResultItem.class;
        for ( final Object item : items )
        {
            assertTrue( "Item should be of type " + expectedItemClass, expectedItemClass.isAssignableFrom( item.getClass() ) );
        }
    }

    /**
     * Tests {@link OrderService#getOrdersByAccount(QueryMessage)}.
     */
    public void testGetOrdersByAccount() throws Exception
    {
        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );

        uow.refreshObject(request);

        tx.commit();

        // Query for active orders
        final String orgShortName = request.getOrganization().getShortName();
        assertTrue( "Org's short name should not be blank: " + orgShortName, orgShortName != null && orgShortName.trim().length() > 0 );
        final String acctShortName = request.getCounterparty().getShortName();
        assertTrue( "Acct's short name should not be blank: " + acctShortName, acctShortName != null && acctShortName.trim().length() > 0 );

        final Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        params.add( createParameter( OrderQueryParameters.PARAM_ORG_SHORT_NAME, orgShortName ) );
        params.add( createParameter( OrderQueryParameters.PARAM_ACCT_SHORT_NAME_LIST, acctShortName ) );
        final QueryMessage msg = OrderService.getInstance().createQueryMessage( OrderService.GET_ORDERS_BY_ACCOUNT, params );

        // //
        // //
        final QueryMessage queryMessage = orderService.getOrdersByAccount(msg);
        // //
        // //

        assertEquals( MessageStatus.SUCCESS, queryMessage.getStatus() );

        // Check if there is result
        final QueryResult result = queryMessage.getQueryResult();
        final List items = result.getResultItems();
        assertTrue("Number of items should be greater than zero", items.size() > 0);

        // Check result is of expected type
        final Class<ResultItem> expectedItemClass = ResultItem.class;
        for ( final Object item : items )
        {
            assertTrue( "Item should be of type " + expectedItemClass, expectedItemClass.isAssignableFrom( item.getClass() ) );
        }
    }

    /**
     * Tests {@link OrderService#getOrdersForAllBrokerCustomers(QueryMessage)}.
     */
    public void testGetOrdersForAllBrokerCustomers() throws Exception
    {
        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses(getDealingTransactionReadOnlyClasses());

        final Request request = createOrder();
        request.setStatus(Entity.ACTIVE_STATUS);
        request.setPersistentOrder(true);

        tx.commit();

        // Query
        final QueryMessage msg = new QueryMessageC();
        final Organization brokerOrg = ReferenceDataCacheC.getInstance().getOrganization("Broker2");
        msg.setSender( brokerOrg.getDefaultDealingUser() );

        // //
        // //
        final QueryMessage queryMessage = orderService.getOrdersForAllBrokerCustomers( msg );
        // //
        // //

        assertEquals( MessageStatus.SUCCESS, queryMessage.getStatus() );

        // Check if there is result
        final QueryResult result = queryMessage.getQueryResult();
        final List items = result.getResultItems();

        if ( brokerOrg.isSameAs(ddOrg.getBrokerOrganization()))
        {
            assertTrue("Number of items should be greater than zero", items.size() == 0);
        }
        else
        {
            assertTrue("Number of items should be equal to zero", items.size() == 0);
        }
    }

    /**
     * Tests {@link OrderService#getRecentyCancelledOrders(QueryMessage)}.
     */
    public void testGetOrdersByOrganization() throws Exception
    {
        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );

        uow.refreshObject( request );

        tx.commit();

        // Query for active orders
        final String orgShortName = request.getOrganization().getShortName();
        assertTrue( "Org's short name should not be blank: " + orgShortName, orgShortName != null && orgShortName.trim().length() > 0 );

        final Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        params.add( createParameter( OrderQueryParameters.PARAM_ORG_SHORT_NAME_LIST, orgShortName ) );
        final QueryMessage msg = OrderService.getInstance().createQueryMessage( OrderService.GET_ORDERS_BY_ORGANIZATION, params );

        // //
        // //
        final QueryMessage queryMessage = orderService.getOrdersByOrganization( msg );
        // //
        // //

        assertEquals( MessageStatus.SUCCESS, queryMessage.getStatus() );

        // Check if there is result
        final QueryResult result = queryMessage.getQueryResult();
        final List items = result.getResultItems();
        assertTrue( "Number of items should be greater than zero", items.size() > 0 );

        // Check result is of expected type
        final Class<ResultItem> expectedItemClass = ResultItem.class;
        for ( final Object item : items )
        {
            assertTrue( "Item should be of type " + expectedItemClass, expectedItemClass.isAssignableFrom( item.getClass() ) );
        }

    }

    /**
     * Tests {@link OrderService#getRecentyCancelledOrders(QueryMessage)}.
     */
    public void testRecentyExecutedOrders() throws Exception
    {
        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );

        // Mark request as active
        final WorkflowStateMap map = request.getWorkflowStateMap();

        // Set executedstate
        map.setState( ( State ) namedEntityReader.execute( StateC.class, "RSEXECUTED" ) );

        // Set canceled date to current trade date
        final EndOfDayService dayService = EndOfDayServiceFactory.getEndOfDayService();
        final IdcDateTime currentTradeDate = dayService.getCurrentTradeDate().toIdcDateTime();
        map.setTimeOfLastStateChange( currentTradeDate );

        uow.refreshObject( request );
        tx.commit();

        // Query for active orders
        final QueryMessage msg = new QueryMessageC();
        final QueryMessage queryMessage = orderService.getRecentyExecutedOrders( msg );
        assertEquals( MessageStatus.SUCCESS, queryMessage.getStatus() );

        // Check if there is result
        final QueryResult result = queryMessage.getQueryResult();
        final List items = result.getResultItems();
        assertTrue( "Number of items should be greater than zero", items.size() > 0 );

        // Check result is of expected type
        final Class<ResultItem> expectedItemClass = ResultItem.class;
        for ( final Object item : items )
        {
            assertTrue( "Item should be of type " + expectedItemClass, expectedItemClass.isAssignableFrom( item.getClass() ) );
        }

    }

    /**
     * Tests {@link OrderService#getRecentyCancelledOrders(QueryMessage)}.
     */
    public void testRecentyCancelledOrders() throws Exception
    {
        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );

        // Mark request as active
        final WorkflowStateMap map = request.getWorkflowStateMap();

        // Set canceled state
        map.setState( ( State ) namedEntityReader.execute( StateC.class, "RSCANCELED" ) );

        // Set canceled date to current trade date
        final EndOfDayService dayService = EndOfDayServiceFactory.getEndOfDayService();
        final IdcDateTime currentTradeDate = dayService.getCurrentTradeDate().toIdcDateTime();
        map.setTimeOfLastStateChange( currentTradeDate );

        uow.refreshObject( request );
        tx.commit();

        // Query for active orders
        final QueryMessage msg = new QueryMessageC();
        final QueryMessage queryMessage = orderService.getRecentyCancelledOrders( msg );
        assertEquals( MessageStatus.SUCCESS, queryMessage.getStatus() );

        // Check if there is result
        final QueryResult result = queryMessage.getQueryResult();
        final List items = result.getResultItems();
        assertTrue( "Number of items should be greater than zero", items.size() > 0 );

        // Check result is of expected type
        final Class<ResultItem> expectedItemClass = ResultItem.class;
        for ( final Object item : items )
        {
            assertTrue( "Item should be of type " + expectedItemClass, expectedItemClass.isAssignableFrom( item.getClass() ) );
        }

    }

    /**
     * Tests {@link OrderService#getActiveOrders(QueryMessage)}.
     */
    public void testGetActiveOrders() throws Exception
    {
        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        final Request registeredRequest = (Request) request.getRegisteredObject();
        registeredRequest.setStatus( Entity.ACTIVE_STATUS );
        registeredRequest.setPersistentOrder( true );

        // Mark request as active
        registeredRequest.setWorkflowStateMap(WorkflowFactory.newRequestWorkflowStateMapDependent());
        final WorkflowStateMap map = registeredRequest.getWorkflowStateMap();
        final Entity activeState = namedEntityReader.execute( StateC.class, "RSINIT" );
        map.setState( ( State ) activeState );

        tx.commit();

        Request refreshedRequest = (Request)IdcUtilC.refreshObject( request );
        assertNotNull(refreshedRequest);

        // Query for active orders
        final QueryMessage msg = new QueryMessageC();
        final QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        queryCriteria.setClassName(RequestC.class.getName());
        msg.setQueryCriteria(queryCriteria);
        final QueryMessage queryMessage = orderService.getActiveOrders( msg );
        assertEquals( MessageStatus.SUCCESS, queryMessage.getStatus() );

        // Check if there is result
        final QueryResult result = queryMessage.getQueryResult();
        final List items = result.getResultItems();
        assertTrue( "Number of items should be greater than zero", items.size() > 0 );

        // Check result is of expected type
        final Class<ResultItem> expectedItemClass = ResultItem.class;
        for ( final Object item : items )
        {
            assertTrue( "Item should be of type " + expectedItemClass, expectedItemClass.isAssignableFrom( item.getClass() ) );
        }

    }

    /**
     * Tests {@link OrderService#getOrdersByState(QueryMessage)} (QueryMessage)}.
     */
    public void testGetOrdersByState() throws Exception
    {

        // Create msg
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );

        // Mark msg as active
        final String stateShortName = "RSACTIVE";
        final Entity activeState = namedEntityReader.execute( StateC.class, stateShortName );
        final WorkflowStateMap map = request.getWorkflowStateMap();
        map.setState( ( State ) activeState );

        uow.refreshObject( request );
        tx.commit();

        // Query for active orders
        final Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        params.add( createParameter( OrderQueryParameters.PARAM_STATE, stateShortName ) );
        final QueryMessage msg = OrderService.getInstance().createQueryMessage( OrderService.GET_ORDERS_BY_LAST_STATE_CHANGE, params );

        // //
        // //
        final QueryMessage response = orderService.getOrdersByState( msg );
        // //
        // //

        assertEquals( MessageStatus.SUCCESS, response.getStatus() );

        // Check if there is result
        final QueryResult result = response.getQueryResult();
        final List items = result.getResultItems();
        assertTrue( "Number of items should be greater than zero", items.size() > 0 );

        // Check result is of expected type
        final Class<ResultItem> expectedItemClass = ResultItem.class;
        for ( final Object item : items )
        {
            assertTrue( "Item should be of type " + expectedItemClass, expectedItemClass.isAssignableFrom( item.getClass() ) );
        }

    }

    /**
     * Tests {@link OrderService#getOrdersByDateOfLastStateChange(QueryMessage)}.
     */
    public void testGetOrdersByDateOfLastStateChange() throws Exception
    {

        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );

        // Mark request as active
        final WorkflowStateMap map = request.getWorkflowStateMap();
        map.setTimeOfLastStateChange( DateTimeFactory.newDateTime() );

        uow.refreshObject( request );
        tx.commit();

        // Create date range as one day before today to one day after today
        final Calendar startDate = Calendar.getInstance();
        startDate.add( Calendar.DAY_OF_MONTH, -1 );

        final Calendar endDate = Calendar.getInstance();
        endDate.add( Calendar.DAY_OF_MONTH, 1 );

        // Prepare method parameters
        final SimpleDateFormat dateRangeFormat = OrderServiceUtil.createDateRangeFormat();
        final Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        params.add( createDateParameter( OrderQueryParameters.PARAM_FROM_LAST_STATE_CHANGE_DATE, startDate, dateRangeFormat ) );
        params.add( createDateParameter( OrderQueryParameters.PARAM_TO_LAST_STATE_CHANGE_DATE, endDate, dateRangeFormat ) );

        // Execute and assert
        final QueryMessage queryMessage = OrderService.getInstance().createQueryMessage( OrderService.GET_ORDERS_BY_LAST_STATE_CHANGE, params );
        final QueryMessage resultMessage = orderService.getOrdersByDateOfLastStateChange( queryMessage );
        final List resultItems = resultMessage.getQueryResult().getResultItems();
        assertNotNull( "Result items cannot be null", resultItems );
        assertTrue( "Result items cannot be empty", !resultItems.isEmpty() );
    }

    /**
     * Tests {@link OrderService#getOrdersByExecutionDate(QueryMessage)}.
     */
    public void testGetOrdersByExecutionDate() throws Exception
    {

        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );

        // Mark request as active
        final WorkflowStateMap map = request.getWorkflowStateMap();
        map.setTimeOfLastStateChange( DateTimeFactory.newDateTime() );
        map.setState( ( State ) namedEntityReader.execute( StateC.class, "RSEXECUTED" ) );

        uow.refreshObject( request );
        tx.commit();

        // Create date range as one day before today to one day after today
        final Calendar startDate = Calendar.getInstance();
        startDate.add( Calendar.DAY_OF_MONTH, -1 );

        final Calendar endDate = Calendar.getInstance();
        endDate.add( Calendar.DAY_OF_MONTH, 1 );

        // Prepare method parameters
        final SimpleDateFormat dateRangeFormat = OrderServiceUtil.createDateRangeFormat();
        final Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        params.add( createDateParameter( OrderQueryParameters.PARAM_FROM_EXECUTION_DATE, startDate, dateRangeFormat ) );
        params.add( createDateParameter( OrderQueryParameters.PARAM_TO_EXECUTION_DATE, endDate, dateRangeFormat ) );

        // Execute and assert
        final QueryMessage queryMessage = OrderService.getInstance().createQueryMessage( OrderService.GET_ORDERS_BY_EXECUTION_DATE, params );

        ////
        final QueryMessage resultMessage = orderService.getOrdersByExecutionDate( queryMessage );
        ////

        final List resultItems = resultMessage.getQueryResult().getResultItems();
        assertNotNull( "Result items cannot be null", resultItems );
        assertTrue( "Result items cannot be empty", !resultItems.isEmpty() );
    }

    /**
     * Tests {@link OrderService#getOrdersByExecutionDate(QueryMessage)}.
     */
    public void testGetOrdersByCreateDate() throws Exception
    {

        // Create request
        final IdcTransaction tx = initTransaction();
        final UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        final Request request = createOrder();
        request.setStatus( Entity.ACTIVE_STATUS );

        uow.refreshObject( request );
        tx.commit();

        // Create date range as one day before today to one day after today
        final Calendar startDate = Calendar.getInstance();
        startDate.add( Calendar.DAY_OF_MONTH, -1 );

        final Calendar endDate = Calendar.getInstance();
        endDate.add( Calendar.DAY_OF_MONTH, 1 );

        // Prepare method parameters
        final SimpleDateFormat dateRangeFormat = OrderServiceUtil.createDateRangeFormat();
        final Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        params.add( createDateParameter( OrderQueryParameters.PARAM_FROM_CREATED_DATE, startDate, dateRangeFormat ) );
        params.add( createDateParameter( OrderQueryParameters.PARAM_TO_CREATED_DATE, endDate, dateRangeFormat ) );

        // Execute and assert
        final QueryMessage queryMessage = OrderService.getInstance().createQueryMessage( OrderService.GET_ORDERS_BY_CREATED_DATE, params );

        ////
        ////
        final QueryMessage resultMessage = orderService.getOrdersByCreateDate( queryMessage );
        ////
        ////

        final List resultItems = resultMessage.getQueryResult().getResultItems();
        assertNotNull( "Result items cannot be null", resultItems );
        assertTrue( "Result items cannot be empty", !resultItems.isEmpty() );
    }

    /**
     * Helper method to create single leg request.
     *
     * @return single leg request.
     */
    private Request createOrder()
    {
        final FXSingleLeg fxSingleLeg = prepareSingleLegTrade( ( double ) TRADE_AMT, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
        return prepareSingleLegRequest( fxSingleLeg );
    }

    /**
     * Helper method.
     */
    private QueryParameter createDateParameter( final String paramName, final Calendar date, final SimpleDateFormat dateFormat )
    {
        final String value = dateFormat.format( date.getTime() );
        return createParameter( paramName, value );
    }

    private QueryParameter createParameter( final String paramName, final String value )
    {
        final QueryParameter result = QueryMessageFactory.newQueryParameter();
        result.setKey( paramName );
        result.setValue( value );
        return result;
    }

    @Override
    protected void setUp() throws Exception
    {
        super.setUp();
        orderService = OrderService.getInstance();
    }
}
