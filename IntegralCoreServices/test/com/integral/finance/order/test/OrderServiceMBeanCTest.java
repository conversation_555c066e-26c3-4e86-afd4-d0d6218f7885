package com.integral.finance.order.test;

// Copyright (c) 2010 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.order.configuration.OrderServiceMBean;
import com.integral.finance.order.configuration.OrderServiceMBeanC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.util.IdcUtilC;

/**
 * Tester for <code>OrderServiceMBeanC</code>
 *
 * <AUTHOR> Development Corporation.
 * @see com.integral.finance.order.configuration.OrderServiceMBeanC
 */
public class OrderServiceMBeanCTest extends MBeanTestCaseC
{
    private OrderServiceMBeanC orderServiceMBean;

    public OrderServiceMBeanCTest( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( orderServiceMBean, "dayOrderEnabled", OrderServiceMBean.DAY_ORDER_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( orderServiceMBean, "persistentOrderEnabled", OrderServiceMBean.PERSISTENT_ORDER_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( orderServiceMBean, "persistentOrderRequestClassifications", OrderServiceMBean.PERSISTENT_ORDER_LOAD_REQUEST_CLASSIFICATIONS, MBeanTestCaseC.STRING );
        testProperty( orderServiceMBean, "orderAdaptorAutoRateSubscriptionsEnabled", OrderServiceMBean.ORDER_ADAPTOR_AUTOSUBSCRIPTIONS, MBeanTestCaseC.BOOLEAN );
        testProperty( orderServiceMBean, "orderAdaptorVWAPEnabled", OrderServiceMBean.ORDER_ADAPTOR_VWAP_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( orderServiceMBean, "displayOrderProvidersAutoInclusionEnabled", OrderServiceMBean.DISPLAY_ORDER_PROVIDERS_AUTO_INCLUSION, MBeanTestCaseC.BOOLEAN );
    }

    public void testIsDayOrderDisabledByDefault() throws Exception
    {
        assertFalse( orderServiceMBean.isDayOrderEnabled() );
    }

    public void testIsPersistentOrderEnabledByDefault() throws Exception
    {
        assertFalse( orderServiceMBean.isPersistentOrderEnabled() );
    }
    
    public void testIsDisplayOrderProviderAutoInclusionEnabledByDefault() throws Exception
    {
        assertFalse( orderServiceMBean.isDisplayOrderProvidersAutoInclusionEnabled() );
    }
    

    protected void setUp() throws Exception
    {
        super.setUp();
        orderServiceMBean = OrderServiceMBeanC.getInstance();
    }

    public void testCancelOrdersOnDisconnect()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        Organization broker1 =  ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker1 );
        org2.setBrokerOrganization ( broker1 );
        try
        {
            assertFalse(orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled (null));
            assertFalse(orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled (org1));
            assertFalse(orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org2));

            // now set the global property
            orderServiceMBean.setProperty( OrderServiceMBean.PERSISTENT_ORDER_CANCELLATION_DISCONNECT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org1) );
            assertTrue( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org2) );
            assertTrue( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(null) );

            orderServiceMBean.setProperty( OrderServiceMBean.PERSISTENT_ORDER_CANCELLATION_DISCONNECT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org1) );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org2) );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(null) );

            orderServiceMBean.setProperty( OrderServiceMBean.PERSISTENT_ORDER_CANCELLATION_DISCONNECT_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org1) );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org2) );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(null) );

            orderServiceMBean.removeProperty ( OrderServiceMBean.PERSISTENT_ORDER_CANCELLATION_DISCONNECT_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            orderServiceMBean.setProperty ( OrderServiceMBean.BROKER_CUSTOMERS_PERSISTENT_ORDER_CANCELLATION_DISCONNECT_ENABLED_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org1) );
            assertTrue( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org2) );
            assertFalse(orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(null));

            orderServiceMBean.setProperty ( OrderServiceMBean.BROKER_CUSTOMERS_PERSISTENT_ORDER_CANCELLATION_DISCONNECT_ENABLED_PREFIX + "Broker1", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org1) );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org2) );
            assertFalse(orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(null));

            orderServiceMBean.setProperty( OrderServiceMBean.PERSISTENT_ORDER_CANCELLATION_DISCONNECT_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org1) );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(org2) );
            assertFalse( orderServiceMBean.isPersistentOrderCancellationOnDisconnectEnabled(null) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCancelOrdersOnDisconnect" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker1 );
        }
    }
}
