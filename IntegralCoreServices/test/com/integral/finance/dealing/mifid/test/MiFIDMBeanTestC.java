package com.integral.finance.dealing.mifid.test;

// Copyright (c) 2017 Integral Development Corporation.  All Rights Reserved.

import com.integral.mifid.MiFIDMBean;
import com.integral.mifid.MiFIDMBeanC;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;

import java.util.Collection;

/**
 * <AUTHOR> Development Corporation.
 */
public class MiFIDMBeanTestC extends MBeanTestCaseC
{
    MiFIDMBeanC mifidMBean = ( MiFIDMBeanC ) MiFIDMBeanC.getInstance();

    public MiFIDMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( mifidMBean, "postDeferralIndicatorValue", MiFIDMBean.IDC_MIFID_POSTTRADE_DEFERRAL, MBeanTestCaseC.STRING );
    }

    public void testNationalIdTypeMapping()
    {
        try
        {
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_NATIONAL_ID_TYPE_MAPPING, "SSN|1,Passport|2,DrivingLicence|3", ConfigurationProperty.DYNAMIC_SCOPE );

            assertEquals ( "SSN", mifidMBean.getNationalIdTypeName ( 1 ) );
            assertEquals ( "Passport", mifidMBean.getNationalIdTypeName ( 2 ) );
            assertEquals ( "DrivingLicence", mifidMBean.getNationalIdTypeName ( 3 ) );
            assertEquals ( null, mifidMBean.getNationalIdTypeName ( 6 ) );

            assertEquals ( 1, mifidMBean.getNationalIdTypeCode ( "SSN" ) );
            assertEquals ( 2, mifidMBean.getNationalIdTypeCode ( "Passport" ) );
            assertEquals ( 3, mifidMBean.getNationalIdTypeCode ( "DrivingLicence" ) );
            assertEquals ( -1, mifidMBean.getNationalIdTypeCode ( "XXX" ) );

            assertEquals ( 3, mifidMBean.getNationalIdTypeValue2Name ().size () );
            assertTrue ( mifidMBean.getNationalIdTypeName2Value().containsKey("SSN"));
            assertTrue ( mifidMBean.getNationalIdTypeName2Value().containsKey( "Passport" ) );
            assertTrue ( mifidMBean.getNationalIdTypeName2Value().containsKey( "DrivingLicence" ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testNationalIdTypeMapping" );
        }
    }

    public void testMiFIDValidationProperties1()
    {
        try
        {
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL, "A,B,C", ConfigurationProperty.DYNAMIC_SCOPE );

            Collection<String> values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, "FI1" );
            assertNotNull ( values1 );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values1.toArray ()[0].equals ( "A" ) );
            assertTrue ( values1.toArray ()[1].equals ( "B" ) );
            assertTrue ( values1.toArray ()[2].equals ( "C" ) );

            Collection<String> values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, null );
            assertNotNull ( values2 );
            assertTrue ( values2.size () == 3 );
            assertTrue ( values2.toArray ()[0].equals ( "A" ) );
            assertTrue ( values2.toArray ()[1].equals ( "B" ) );
            assertTrue ( values2.toArray ()[2].equals ( "C" ) );

            Collection<String> values3 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values3.isEmpty () );
            Collection<String> values4 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, null );
            assertTrue ( values4.isEmpty () );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST, "D,E,F", ConfigurationProperty.DYNAMIC_SCOPE );
            values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, "FI1" );
            assertNotNull ( values1 );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values1.toArray ()[0].equals ( "D" ) );
            assertTrue ( values1.toArray ()[1].equals ( "E" ) );
            assertTrue ( values1.toArray ()[2].equals ( "F" ) );

            values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, null );
            assertNotNull ( values2 );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values2.toArray ()[0].equals ( "D" ) );
            assertTrue ( values2.toArray ()[1].equals ( "E" ) );
            assertTrue ( values2.toArray ()[2].equals ( "F" ) );

            values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.TRADE, "FI1" );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values1.toArray ()[0].equals ( "A" ) );
            assertTrue ( values1.toArray ()[1].equals ( "B" ) );
            assertTrue ( values1.toArray ()[2].equals ( "C" ) );

            values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.TRADE, null );
            assertTrue ( values2.size () == 3 );
            assertTrue ( values2.toArray ()[0].equals ( "A" ) );
            assertTrue ( values2.toArray ()[1].equals ( "B" ) );
            assertTrue ( values2.toArray ()[2].equals ( "C" ) );

            values3 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values3.isEmpty () );
            values4 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, null );
            assertTrue ( values4.isEmpty () );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST + ".FI1", "G,H,I", ConfigurationProperty.DYNAMIC_SCOPE );
            values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values1.toArray ()[0].equals ( "G" ) );
            assertTrue ( values1.toArray ()[1].equals ( "H" ) );
            assertTrue ( values1.toArray ()[2].equals ( "I" ) );

            values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, null );
            assertTrue ( values2.size () == 3 );
            assertTrue ( values2.toArray ()[0].equals ( "D" ) );
            assertTrue ( values2.toArray ()[1].equals ( "E" ) );
            assertTrue ( values2.toArray ()[2].equals ( "F" ) );

            values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.TRADE, "FI1" );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values1.toArray ()[0].equals ( "A" ) );
            assertTrue ( values1.toArray ()[1].equals ( "B" ) );
            assertTrue ( values1.toArray ()[2].equals ( "C" ) );

            values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.TRADE, null );
            assertTrue ( values2.size () == 3 );
            assertTrue ( values2.toArray ()[0].equals ( "A" ) );
            assertTrue ( values2.toArray ()[1].equals ( "B" ) );
            assertTrue ( values2.toArray ()[2].equals ( "C" ) );

            values3 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values3.isEmpty () );
            values4 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, null );
            assertTrue ( values4.isEmpty () );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMiFIDValidationProperties1" );
        }
        finally
        {
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL, ConfigurationProperty.DYNAMIC_SCOPE );
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST, ConfigurationProperty.DYNAMIC_SCOPE );
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST + ".FI1", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMiFIDValidationProperties2()
    {
        try
        {
            Collection<String> values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values1 == null || values1.size () == 0 );

            Collection<String> values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, null );
            assertTrue ( values2 == null || values2.size () == 0 );

            Collection<String> values3 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values3.isEmpty () );
            Collection<String> values4 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, null );
            assertTrue ( values4.isEmpty () );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST, "D,E,F", ConfigurationProperty.DYNAMIC_SCOPE );
            values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, "FI1" );
            assertNotNull ( values1 );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values1.toArray ()[0].equals ( "D" ) );
            assertTrue ( values1.toArray ()[1].equals ( "E" ) );
            assertTrue ( values1.toArray ()[2].equals ( "F" ) );

            values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, null );
            assertNotNull ( values2 );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values2.toArray ()[0].equals ( "D" ) );
            assertTrue ( values2.toArray ()[1].equals ( "E" ) );
            assertTrue ( values2.toArray ()[2].equals ( "F" ) );

            values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.TRADE, "FI1" );
            assertTrue ( values1 == null || values1.size () == 0 );

            values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.TRADE, null );
            assertTrue ( values1 == null || values1.size () == 0 );

            values3 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values3.isEmpty () );
            values4 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, null );
            assertTrue ( values4.isEmpty () );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST + ".FI1", "G,H,I", ConfigurationProperty.DYNAMIC_SCOPE );
            values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values1.size () == 3 );
            assertTrue ( values1.toArray ()[0].equals ( "G" ) );
            assertTrue ( values1.toArray ()[1].equals ( "H" ) );
            assertTrue ( values1.toArray ()[2].equals ( "I" ) );

            values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.REQUEST, null );
            assertTrue ( values2.size () == 3 );
            assertTrue ( values2.toArray ()[0].equals ( "D" ) );
            assertTrue ( values2.toArray ()[1].equals ( "E" ) );
            assertTrue ( values2.toArray ()[2].equals ( "F" ) );

            values1 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.TRADE, "FI1" );
            assertTrue ( values1 == null || values1.size () == 0 );

            values2 = mifidMBean.getMiFIDFValidationsList ( true, MiFIDMBean.TRADE, null );
            assertTrue ( values2 == null || values2.size () == 0 );

            values3 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, "FI1" );
            assertTrue ( values3.isEmpty () );
            values4 = mifidMBean.getMiFIDFValidationsList ( false, MiFIDMBean.REQUEST, null );
            assertTrue ( values4.isEmpty () );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMiFIDValidationProperties2" );
        }
        finally
        {
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL, ConfigurationProperty.DYNAMIC_SCOPE );
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST, ConfigurationProperty.DYNAMIC_SCOPE );
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST + ".FI1", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }


    public void testMiFIDStrictValidationProperties1()
    {
        try
        {
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL, "A,B,C", ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "A" ) );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "B" ) );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "C" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "D" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "E" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "F" ) );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST, "D,E,F", ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "D" ) );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "A" ) );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "B" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "A" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "E" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "F" ) );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST + ".FI1", "G,H,I", ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "G" ) );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "B" ) );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "C" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "A" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "E" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "F" ) );

            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.QUOTE, "FI1", "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.REQUEST, "FI1", "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.TRADE, "FI1", "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.QUOTE, null, "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.REQUEST, null, "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.TRADE, null, "F" ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMiFIDStrictValidationProperties1" );
        }
        finally
        {
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL, ConfigurationProperty.DYNAMIC_SCOPE );
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST, ConfigurationProperty.DYNAMIC_SCOPE );
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST + ".FI1", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMiFIDStrictValidationProperties2()
    {
        try
        {
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "A" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "B" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "C" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "D" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "E" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "F" ) );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST, "D,E,F", ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "D" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "A" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "B" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "A" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "E" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "F" ) );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST + ".FI1", "G,H,I", ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "G" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "B" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "C" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.REQUEST, "FI1", "A" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.TRADE, "FI1", "E" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( true, MiFIDMBean.QUOTE, "FI1", "F" ) );

            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.QUOTE, "FI1", "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.REQUEST, "FI1", "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.TRADE, "FI1", "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.QUOTE, null, "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.REQUEST, null, "F" ) );
            assertFalse ( mifidMBean.rejectOnMiFiDValidationFailure( false, MiFIDMBean.TRADE, null, "F" ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMiFIDStrictValidationProperties2" );
        }
        finally
        {
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL, ConfigurationProperty.DYNAMIC_SCOPE );
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST, ConfigurationProperty.DYNAMIC_SCOPE );
            mifidMBean.removeProperty ( MiFIDMBean.IDC_MIFID_STRICT_VALIDATIONS + MiFIDMBean.INTERNAL + "." + MiFIDMBean.REQUEST + ".FI1", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }
}
