package com.integral.finance.dealing.fx.test;

import java.io.StringWriter;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXLegDealingPriceC;
import com.integral.finance.trade.Tenor;
import com.integral.test.PTestCaseC;
import com.integral.test.TestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.xml.binding.JavaXMLBinderFactory;
import com.integral.xml.mapping.XMLException;

import electric.xml.Document;
import electric.xml.Element;
import electric.xml.ParseException;
import electric.xml.XPath;

public class FXLegDealingPriceNDFMappingPTestC extends PTestCaseC
{
    static String name = "NDF Mapping test for FXLegDealingPrice";
    
    public FXLegDealingPriceNDFMappingPTestC()
    {
        super( name );
    }
    
    public void testNDFRequest()
    {
        Request request = new RequestC();
        FXLegDealingPrice dp = new FXLegDealingPriceC();
        dp.setDealtCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
        request.setRequestPrice( "test", dp );
        StringWriter writer = new StringWriter( 1024 );
        try
        {
            // Test Non-NDF
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( writer, request, "TradeDownload" );
            String out = writer.toString();
            Document doc;
            try
            {
                doc = new Document( out );
                Element fixingDateElement = doc.getElement( new XPath( "/request/fxLegPrice/fixingDate" ) );
                assertNull( fixingDateElement );
                Element fixingTenorElement = doc.getElement( new XPath( "/request/fxLegPrice/fixingTenor" ) );
                assertNull( fixingTenorElement );
            }
            catch ( ParseException e )
            {
                fail( "XML parsing failed. ", e );
            }
        }
        catch ( XMLException e )
        {
            fail( "XML conversion failed. ", e );
        }

        IdcDate date = DateTimeFactory.newDate();
        dp.setFixingDate( date );
        dp.setFixingTenor( Tenor.SPOT_TENOR );
        request.setRequestPrice( "test", dp );

        try
        {
            // Test NDF
            writer = new StringWriter( 1024 );
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( writer, request, "TradeDownload" );
            String out = writer.toString();
            Document doc;
            try
            {
                doc = new Document( out );
                Element fixingDateElement = doc.getElement( new XPath( "/request/fxLegPrice/fixingDate" ) );
                String formattedDate = date.getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN );
                assertEquals( formattedDate, fixingDateElement.getText().getString() );
                Element fixingTenorElement = doc.getElement( new XPath( "/request/fxLegPrice/fixingTenor" ) );
                assertEquals( Tenor.SPOT_TENOR, new Tenor( fixingTenorElement.getText().getString() ) );
            }
            catch ( ParseException e )
            {
                fail( "XML parsing failed for NDF. ", e );
            }
        }
        catch ( XMLException e )
        {
            fail( "XML conversion failed for NDF. ", e );
        }

    }
    
}
