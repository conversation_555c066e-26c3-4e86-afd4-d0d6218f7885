package com.integral.finance.dealing.fx.test;

import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXDealingPriceElementC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSideRates;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceFactory;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

class TestFXDealingPriceElementCreatorC implements Runnable
{
    private int num = 0;
    Log log = LogFactory.getLog( getClass() );

    public TestFXDealingPriceElementCreatorC( int i )
    {
        num = i;
    }

    public void run()
    {
        for ( int i = 0; i < 10; i++ )
        {
            log.info( "insertTest " + num + '/' + i );
            try
            {
                // correct way of creating a session since it it used
                // in multiple threads
                Session session = PersistenceFactory.newSession();
                UnitOfWork uow = session.acquireUnitOfWork();

                // don't do this since the session is not thread safe
                // getPersistenceSession() is a singleton so all threads
                // would use the same session
                // UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

                FXRate offerRate = FXFactory.newFXRate();
                offerRate.setForwardPips( 1.234 );

                FXSideRates newSideRate = FXFactory.newFXSideRates();
                offerRate.setSideRates( newSideRate );

                FXPrice price = FXPriceFactory.newFXPrice();
                price.setOfferFXRate( offerRate );

                FXDealingPriceElement dpe = new FXDealingPriceElementC();
                dpe.setPrice( price );
                dpe.setStatus( 'T' );
                uow.registerObject( dpe );

                uow.commit();
                Thread.sleep( 100 );
                log.info( "insertTest completed " + num + '/' + i );
            }
            catch ( Exception e )
            {
                e.printStackTrace();
            }
        }
    }
}
