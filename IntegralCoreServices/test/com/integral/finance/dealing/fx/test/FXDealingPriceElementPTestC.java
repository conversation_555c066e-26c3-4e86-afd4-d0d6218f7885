package com.integral.finance.dealing.fx.test;


import com.integral.exception.IdcDatabaseException;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXDealingPriceElementC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSideRates;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Vector;


/**
 * Unit test for fx dealing price element
 */
public class FXDealingPriceElementPTestC extends PTestCaseC
{
    static String name = "FXDealingPriceElement Test";

    public FXDealingPriceElementPTestC( String name )
    {
        super( name );
    }

    /**
     * Inserts a new fx dealing price element with a fx price that
     * has the offer fx rate pointing to a fx side rates
     */
    public void testInsert()
    {
        log( "insertTest" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            FXRate offerRate = FXFactory.newFXRate();

            FXSideRates newSideRate = FXFactory.newFXSideRates();
            offerRate.setSideRates( newSideRate );

            FXPrice price = FXPriceFactory.newFXPrice();
            price.setOfferFXRate( offerRate );

            FXDealingPriceElement dpe = new FXDealingPriceElementC();
            dpe.setPrice( price );
            dpe.setStatus( 'T' );
            uow.registerObject( dpe );

            uow.commit();
            log( "insertTest" );
        }
        catch ( Exception e )
        {
            fail( "Exception in insertTest", e );
        }
    }

    /**
     * Inserts a new fx dealing price element with a fx price that
     * has the offer fx rate pointing to a fx side rates that is also
     * registered in a second transaction
     */
    public void testInsertWithDoubleRegistration()
    {
        log( "testInsertWithDoubleRegistration" );
        try
        {
            FXSideRates newSideRate = FXFactory.newFXSideRates();

            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            FXSideRates newSideRate1 = ( FXSideRates ) uow1.registerObject( newSideRate );
            FXRate offerRate1 = FXFactory.newFXRate();
            offerRate1.setSideRates( newSideRate1 );
            FXPrice price1 = FXPriceFactory.newFXPrice();
            price1.setOfferFXRate( offerRate1 );
            FXDealingPriceElement dpe1 = new FXDealingPriceElementC();
            dpe1 = ( FXDealingPriceElement ) uow1.registerObject( dpe1 );
            dpe1.setPrice( price1 );
            dpe1.setStatus( 'T' );

            //uow1.printRegisteredObjects();
            uow1.commit();

            UnitOfWork uow2 = PersistenceFactory.newSession().acquireUnitOfWork();
            FXSideRates newSideRate2 = ( FXSideRates ) uow2.registerObject( newSideRate );
            FXRate offerRate2 = FXFactory.newFXRate();
            offerRate2.setSideRates( newSideRate2 );
            FXPrice price2 = FXPriceFactory.newFXPrice();
            price2.setOfferFXRate( offerRate2 );
            FXDealingPriceElement dpe2 = new FXDealingPriceElementC();
            dpe2 = ( FXDealingPriceElement ) uow2.registerObject( dpe2 );
            dpe2.setPrice( price2 );
            dpe2.setStatus( 'T' );

            boolean exception = false;
            try
            {
                uow2.commit();
            }
            catch ( IdcDatabaseException ex )
            {
                exception = true;
            }

            assertEquals( "The second db transaction should not fail since fx side rates is not privately owned. exception=" + exception, exception, false );
            log( "testInsertWithDoubleRegistration" );
        }
        catch ( Exception e )
        {
            fail( "Failed test testInsertWithDoubleRegistration", e );
        }
    }

    public void testParallelInsert()
    {
        log( "parallelInsertTest" );
        try
        {
            for ( int i = 0; i < 5; i++ )
            {
                TestFXDealingPriceElementCreatorC creator = new TestFXDealingPriceElementCreatorC( i );
                //FXSideRatesCreator creator = new FXSideRatesCreator(i);
                Thread thread = new Thread( creator );
                thread.setDaemon( true );
                thread.start();
            }
            log( "parallelInsertTest" );

            Thread.sleep( 20000 );
        }
        catch ( Exception e )
        {
            fail( "Error in parallelInsertTest", e );
        }
    }

    public void lookupTest()
    {
        log( "lookupTest" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( FXDealingPriceElement.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXDealingPriceElement pp = ( FXDealingPriceElement ) objs.elementAt( i );
                printFXDealingPriceElement( pp, i );
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            fail( "Exception in lookupTest", e );
        }
    }

    private void printFXDealingPriceElement( FXDealingPriceElement pp, int i )
    {
        log( "FXDealingPriceElement #" + i );
        log( "\t objectID = " + pp.getObjectID() );
        log( "\t fx price  = " + pp.getFXPrice() );
        if ( pp.getFXPrice() != null )
        {
            log( "\t\t fx offer rate  = " + pp.getFXPrice().getOfferFXRate() );
            if ( pp.getFXPrice().getOfferFXRate() != null )
            {
                log( "\t\t fx offer side rates  = " + pp.getFXPrice().getOfferFXRate().getSideRates() );
            }
        }
    }

    public void testFXDealingPriceElementFXPriceCurrencies()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            FXRate offerRate = FXFactory.newFXRate();
            offerRate.setBaseCurrency( eur );
            offerRate.setVariableCurrency( usd );

            FXRate bidRate = FXFactory.newFXRate();
            bidRate.setBaseCurrency( eur );
            bidRate.setVariableCurrency( usd );

            FXRate midRate = FXFactory.newFXRate();
            midRate.setBaseCurrency( eur );
            midRate.setVariableCurrency( usd );

            FXPrice price = FXPriceFactory.newFXPrice();
            price.setOfferFXRate( offerRate );
            price.setBidFXRate( bidRate );
            price.setMidFXRate( midRate );

            FXDealingPriceElement dpe = new FXDealingPriceElementC();
            dpe = ( FXDealingPriceElement ) uow.registerObject( dpe );
            dpe.setPrice( price );
            dpe.setStatus( 'T' );

            uow.commit();

            dpe = ( FXDealingPriceElement ) getPersistenceSession().refreshObject( dpe );
            FXPrice fxPrc = dpe.getFXPrice();
            log( "fxPrc.bidBaseccy=" + fxPrc.getBidFXRate().getBaseCurrency() );
            log( "fxPrc.bidVarccy=" + fxPrc.getBidFXRate().getVariableCurrency() );
            log( "fxPrc.midBaseccy=" + fxPrc.getMidFXRate().getBaseCurrency() );
            log( "fxPrc.midBaseccy=" + fxPrc.getMidFXRate().getVariableCurrency() );
            log( "fxPrc.offerBaseccy=" + fxPrc.getOfferFXRate().getBaseCurrency() );
            log( "fxPrc.offerbaseccy=" + fxPrc.getOfferFXRate().getVariableCurrency() );

            assertEquals( "fxPrc.bidBaseccy", fxPrc.getBidFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.bidVarccy", fxPrc.getBidFXRate().getVariableCurrency(), usd );
            assertEquals( "fxPrc.midBaseccy", fxPrc.getMidFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.midVarccy", fxPrc.getMidFXRate().getVariableCurrency(), usd );
            assertEquals( "fxPrc.offerBaseccy", fxPrc.getOfferFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.offerBaseccy", fxPrc.getOfferFXRate().getVariableCurrency(), usd );

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( Entity.ObjectID ).equal( dpe.getObjectID() );
            ReportQuery rq = new ReportQuery( eb );
            rq.setReferenceClass( FXDealingPriceElementC.class );
            rq.setSelectionCriteria( expr );
            rq.addAttribute( "baseCcy", eb.getField( "baseCrncId" ) );
            rq.addAttribute( "varCcy", eb.getField( "varCrncId" ) );
            Collection<ReportQueryResult> results = ( Collection<ReportQueryResult> ) getPersistenceSession().executeQuery( rq );
            for ( ReportQueryResult rqr : results )
            {
                long baseCcyId = ( ( Number ) rqr.get( "baseCcy" ) ).longValue();
                long varCcyId = ( ( Number ) rqr.get( "varCcy" ) ).longValue();
                log( "baseCcyId=" + baseCcyId + ",varCcyId=" + varCcyId );
                assertEquals( "baseCcy should be not null.", CurrencyFactory.getCurrency( baseCcyId ), eur );
                assertEquals( "varCcy should be not null.", CurrencyFactory.getCurrency( varCcyId ), usd );
            }
        }
        catch ( Exception e )
        {
            fail( "Exception in insertTest", e );
        }
    }
}
