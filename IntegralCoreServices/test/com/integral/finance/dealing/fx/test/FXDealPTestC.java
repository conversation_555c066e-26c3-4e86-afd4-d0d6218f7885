package com.integral.finance.dealing.fx.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.DealRequest;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Order;
import com.integral.finance.dealing.RequestClassification;
import com.integral.finance.dealing.facade.DealingFacadeFactory;
import com.integral.finance.dealing.fx.FXDealLeg;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXQuoteLeg;
import com.integral.finance.dealing.fx.FXSingleLegDeal;
import com.integral.finance.dealing.fx.FXSingleLegDealRequest;
import com.integral.finance.dealing.fx.FXSingleLegOrder;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TradeLegClassification;
import com.integral.persistence.CustomField;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.NamespaceC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.workflow.State;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Vector;


public class FXDealPTestC extends PTestCaseC
{
    static String name = "Deal Test";
    private User usr = ( User ) new ReadNamedEntityC().execute( User.class, "Integral" );
    private Currency usd = ( Currency ) new ReadNamedEntityC().execute( Currency.class, "USD" );
    private Currency eur = ( Currency ) new ReadNamedEntityC().execute( Currency.class, "EUR" );
    private LegalEntity tpA = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntity.class, "NYSE" );
    private Organization org = ( Organization ) new ReadNamedEntityC().execute( Organization.class, "MAIN" );
    private RequestClassification limitClsf = ( RequestClassification ) new ReadNamedEntityC().execute( RequestClassification.class, "REQUESTFORQUOTE" );
    private TradeLegClassification legClsf = ( TradeLegClassification ) new ReadNamedEntityC().execute( TradeLegClassification.class, "FXSPOTLEG" );
    private FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );
    private ExternalSystem extSysId = ( ExternalSystem ) new ReadNamedEntityC().execute( ExternalSystem.class, "SWIFTBIC" );
    private State verifiedState = DealingFacadeFactory.getDealStateMBean().getVerifiedState();
    private State pendingVerificationState = DealingFacadeFactory.getDealStateMBean().getPendingVerificationState();
    private State rejectedState = DealingFacadeFactory.getDealStateMBean().getRejectedState();
    private State filledState = DealingFacadeFactory.getOrderStateMBean().getFilledState();

    public FXDealPTestC( String name )
    {
        super( name );
    }

    public void testLookup()
    {
        log( "testLookup" );
        try
        {
            int lookupCount = 100;
            Vector objs = getPersistenceSession().readAllObjects( Deal.class );

            if ( objs.size() < lookupCount )
            {
                lookupCount = objs.size();
            }

            for ( int i = 0; i < lookupCount; i++ )
            {
                Deal deal = ( Deal ) objs.elementAt( i );
                printDealObjects( deal, i );
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testLookup" );
        }
    }

    public void testUpdate()
    {
        log( "testUpdate" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( Deal.class );
            if ( objs != null && !objs.isEmpty() )
            {
                Deal deal = ( Deal ) objs.elementAt( 0 );
                UnitOfWork aUow = getPersistenceSession().acquireUnitOfWork();
                aUow.addReadOnlyClasses( getReadOnlyClasses() );
                deal = ( Deal ) aUow.registerObject( deal );
                deal.setAcceptedDate( new Date() );
                deal.setCounterpartyBTradeId("FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135");
                aUow.commit();
                assertEquals(deal.getCounterpartyBTradeId(), "FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135"  );
            }
            log( "testUpdate" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testUpdate" );
        }
    }

    public void testUpdateForCounterpartyBTradeId()
    {
        log( "testUpdateForCounterpartyBTradeId" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( Deal.class );
            if ( objs != null && !objs.isEmpty() )
            {
                Deal deal = ( Deal ) objs.elementAt( 0 );
                UnitOfWork aUow = getPersistenceSession().acquireUnitOfWork();
                aUow.addReadOnlyClasses( getReadOnlyClasses() );
                deal = ( Deal ) aUow.registerObject( deal );
                deal.setAcceptedDate( new Date() );
                deal.setCounterpartyBTradeId("FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135-FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135-FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135");
                aUow.commit();
                assertEquals(deal.getCounterpartyBTradeId(),"FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135-FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135-FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139"  );
            }
            log( "testUpdateForCounterpartyBTradeId" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testUpdateForCounterpartyBTradeId" );
        }
    }

    public void testUpdateForCounterpartyBTradeId1()
    {
        log( "testUpdateForCounterpartyBTradeId1" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( Deal.class );
            if ( objs != null && !objs.isEmpty() )
            {
                Deal deal = ( Deal ) objs.elementAt( 0 );
                UnitOfWork aUow = getPersistenceSession().acquireUnitOfWork();
                aUow.addReadOnlyClasses( getReadOnlyClasses() );
                deal = ( Deal ) aUow.registerObject( deal );
                deal.setAcceptedDate( new Date() );
                deal.setCounterpartyBTradeId("FXI140690175");
                aUow.commit();
                assertEquals(deal.getCounterpartyBTradeId(),"FXI140690175"  );
            }
            log( "testUpdateForCounterpartyBTradeId1" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testUpdateForCounterpartyBTradeId1" );
        }
    }

    public void testInsert()
    {
        log( "testInsert" );
        try
        {
            UnitOfWork aUow = getPersistenceSession().acquireUnitOfWork();
            aUow.addReadOnlyClasses( getReadOnlyClasses() );
            createTestObjects( aUow );
            aUow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsert" );
        }
    }

    public void testMultipleInserts()
    {
        log( "testMultipleInserts" );
        try
        {
            int count = 100;
            long t0 = System.currentTimeMillis();
            UnitOfWork aUow = getPersistenceSession().acquireUnitOfWork();
            aUow.addReadOnlyClasses( getReadOnlyClasses() );

            // create requests
            for ( int i = 0; i < count; i++ )
            {
                createTestObjects( aUow );
            }

            aUow.commit();
            long t1 = System.currentTimeMillis();
            log( "Time taken for count : " + count + " is : " + ( t1 - t0 ) + " avg : " + ( ( t1 - t0 ) / count ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testMultipleInserts" );
        }
    }

    public void testDeletion()
    {
        log( "testDeletion" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr;

            String id = System.getProperty( "DEALID" );
            if ( id == null )
            {
                expr = eb.get( "objectID" ).lessThan( 10000 );
            }
            else
            {
                expr = eb.get( "objectID" ).equal( id );
            }
            Deal deal = ( Deal ) getPersistenceSession().readObject( Deal.class, expr );
            UnitOfWork aUow = getPersistenceSession().acquireUnitOfWork();

            log( "\tdelete deal " + deal );
            if ( deal != null )
            {
                if ( deal.getDealRequest() != null )
                {
                    aUow.deleteObject( deal.getDealRequest() );
                }
                aUow.deleteObject( deal );
            }

            aUow.commit();

            log( "testDeletion" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testDeletion" );
        }
    }

    private void createTestObjects( UnitOfWork aUow )
    {
        FXSingleLegDeal deal = FXDealingFactory.newFXSingleLegDeal();
        FXSingleLegDealRequest dealRfq = FXDealingFactory.newFXSingleLegDealRequest();
        FXSingleLegOrder order = FXDealingFactory.newFXSingleLegOrder();
        order.setNamespace(new NamespaceC());
        deal = ( FXSingleLegDeal ) aUow.registerObject( deal );
        dealRfq = ( FXSingleLegDealRequest ) aUow.registerObject( dealRfq );
        order = ( FXSingleLegOrder ) aUow.registerObject( order );

        // set the relation ships between deal, dealRequest and order.
        dealRfq.setDeal( deal );
        deal.setDealRequest( dealRfq );
        List dealsColl = new ArrayList();
        dealsColl.add( deal );
        order.setDeals( dealsColl );
        deal.setOrder( order );

        // set various deal values.
        populateDeal( deal, aUow );
        deal.setFXDealLeg( createFXDealLeg() );

        // set various deal request values.
        populateDealRequest( dealRfq, aUow );
        dealRfq.setFXQuoteLeg( createFXQuoteLeg() );

        // set various order values.
        populateOrder( order, aUow );
        order.setFXDealLeg( createFXDealLeg() );
    }

    private void populateDeal( Deal deal, UnitOfWork aUow )
    {
        deal.setAcceptedDate( new Date() );
        deal.setRejectReason( "testInsert" );
        deal.setTransactionId( String.valueOf( System.currentTimeMillis() ) );
        deal.setPortfolioId("123");
        deal.setAllocation(true);
        tpA = ( LegalEntity ) aUow.registerObject( tpA );
        deal.setCounterpartyA( tpA );
        deal.setCounterpartyB( tpA );

        deal.setCounterpartyATradeId( "sdf" );
        deal.setCounterpartyBTradeId( "sdfd" );
        deal.setTradeDate( DateTimeFactory.newDate() );
        deal.setExecutionDate( new Date() );
        int randomState = ( int ) ( Math.random() * 10 );
        if ( randomState > 7 )
        {
            deal.setState( rejectedState );
        }
        else if ( randomState < 7 && randomState > 5 )
        {
            deal.setState( pendingVerificationState );
        }
        else
        {
            deal.setState( verifiedState );
        }

        deal.setReportingCurrency( usd );
        deal.setUser( usr );
        deal.setPricingType("Manual");
        deal.setNote("Note");
        deal.putCustomField( "TestDealCFKey", "TestDealCFValue" );
        CustomField cf = deal.getCustomField( "TestDealCFKey" );
        log( "cf=" + cf );
    }

    private void populateDealRequest( DealRequest dealRfq, UnitOfWork aUow )
    {
        dealRfq.setMessageId( "Msg-98789" );
        dealRfq.setNotes( "testNotes" );
        dealRfq.setPriceProvisionLimitType( 0 );
        dealRfq.setPriceProvisionSpreadType( 3 );
        dealRfq.setProviderQuoteId( "Q12121" );
        dealRfq.setQuoteId( "G23232323" );
        dealRfq.setExpiryDate( new Date() );
        dealRfq.setRequestId( "R048390483" );
        dealRfq.setTradeId( "T098503800" );
        dealRfq.setChannel( extSysId );
        dealRfq.putCustomField( "TestDealRequestCFKey", "TestDealRequestCFValue" );
        CustomField cf = dealRfq.getCustomField( "TestDealRequestCFKey" );
    }

    private void populateOrder( Order order, UnitOfWork aUow )
    {
        User user = ( User ) aUow.registerObject( usr );
        order.setUser( user );

        order.setAmount( 50000 );
        order.setCounterpartyAOrg( org );
        order.setCreatedTime( DateTimeFactory.newDateTime() );
        order.setExpirationClassification( "TimeInForce" );
        order.setFilledAmount( 2000 );
        order.setOrderClassification( limitClsf );
        order.setState( filledState );
        order.setOrderId( String.valueOf( System.currentTimeMillis() ) );
        order.setContextInfo( "testContextInfo" );
        order.setMessageId( "MSG3403-403" );
        order.putCustomField( "TestOrderCFKey", "TestOrderCFValue" );
        CustomField cf = order.getCustomField( "TestOrderCFKey" );
        log( "cf=" + cf );
    }

    private FXDealLeg createFXDealLeg()
    {
        FXDealLeg fxDealLeg = FXDealingFactory.newFXDealLeg();
        fxDealLeg.setDealtCurrency1( false );
        fxDealLeg.setBuyingCurrency1( true );
        fxDealLeg.setFxRateConvention( fxConv );
        CurrencyPair ccyPair = CurrencyFactory.newCurrenyPair( eur, usd );
        fxDealLeg.setCurrencyPair( ccyPair.toString() );
        fxDealLeg.setCurrency1( eur );
        fxDealLeg.setCurrency1Amount( 1000 );
        fxDealLeg.setAcceptedBidOfferMode( DealingPrice.OFFER );
        fxDealLeg.setLegClassification( legClsf );
        fxDealLeg.setLegName( FXSingleLegDeal.SINGLE_LEG );
        fxDealLeg.setReportingCurrencyAmount( 150 );
        fxDealLeg.setCurrency2Amount( 2000 );
        fxDealLeg.setCurrency2( usd );
        fxDealLeg.setRate( 1.23655654 );
        fxDealLeg.setSpotRate( 1.2356 );
        Tenor spot = Tenor.SPOT_TENOR;
        fxDealLeg.setTenor( spot.getName() );
        fxDealLeg.setValueDate( DateTimeFactory.newDate().addDays( 2 ) );
        Tenor tom = Tenor.TOMORROW_TENOR;
        fxDealLeg.setVariableTenor(tom.getName());
        fxDealLeg.setVariableValueDate(DateTimeFactory.newDate().addDays( 1 ));
        fxDealLeg.setSubTradeType("WindowForward");
        fxDealLeg.setFixingDate( DateTimeFactory.newDate().addDays( 2 ) );
        fxDealLeg.setFixingTenor( Tenor.SPOT_TENOR );
        return fxDealLeg;

    }

    private FXQuoteLeg createFXQuoteLeg()
    {
        FXQuoteLeg fxQotLeg = FXDealingFactory.newFXQuoteLeg();
        fxQotLeg.setBaseRate( 1.2365 );
        fxQotLeg.setLegName( "singleLeg" );
        fxQotLeg.setPointsSpread( 1.23 );
        fxQotLeg.setSpotSpread( 0.024 );
        fxQotLeg.setClassification( legClsf );
        fxQotLeg.setQuoteLimit( 100000 );
        return fxQotLeg;
    }

    private void printDealObjects( Deal deal, int i )
    {
        log( "deal #" + i );
        printDeal( deal );
        if ( deal.getDealRequest() != null )
        {
            printDealRequest( deal.getDealRequest() );
        }
        if ( deal.getOrder() != null )
        {
            printOrder( deal.getOrder() );
        }
    }

    private void printDeal( Deal deal )
    {
        log( "\t deal object   = " + deal );
        log( "\t deal objectID = " + deal.getObjectID() );
        log( "\t order = " + deal.getOrder() );
        log( "\t deal request id = " + deal.getDealRequest() );
        log( "\t deal cptyA = " + deal.getCounterpartyA() );
        log( "\t deal cptyB = " + deal.getCounterpartyB() );
        log( "\t deal cptyA org = " + deal.getCounterpartyAOrg() );
        log( "\t deal cptyB org = " + deal.getCounterpartyBOrg() );
        log( "\t deal cptyB org = " + deal.getCounterpartyBOrg() );
        log( "\t deal accepted time = " + deal.getAcceptedDate() );
        log( "\t deal reporting currency = " + deal.getReportingCurrency() );
        log( "\t deal transaction id = " + deal.getTransactionId() );
        log( "\t deal trade date = " + deal.getTradeDate() );
        log( "\t deal state = " + deal.getState() );
        log( "\t deal trade clsf = " + deal.getTradeClassification() );
        if ( deal.getDealRequest() != null )
        {
            log( "\t deal request id = " + deal.getDealRequest().getObjectID() );
        }

        if ( deal instanceof FXSingleLegDeal )
        {
            FXSingleLegDeal fxDeal = ( FXSingleLegDeal ) deal;
            FXDealLeg fxDealLeg = fxDeal.getFXDealLeg();
            log( "\t fx deal leg currency1   = " + fxDealLeg.getCurrency1() );
            log( "\t fx deal leg currency2 = " + fxDealLeg.getCurrency2() );
            log( "\t fx deal leg dlt ccy   = " + fxDealLeg.getDealtCurrency() );
            log( "\t fx deal leg stld ccy = " + fxDealLeg.getSettledCurrency() );
            log( "\t fx deal leg base ccy   = " + fxDealLeg.getBaseCurrency() );
            log( "\t fx deal leg var ccy = " + fxDealLeg.getVariableCurrency() );
            log( "\t fx deal leg buy ccy   = " + fxDealLeg.getBuyCurrency() );
            log( "\t fx deal leg sell ccy = " + fxDealLeg.getSellCurrency() );
            log( "\t fx deal leg currency1 amt  = " + fxDealLeg.getCurrency1Amount() );
            log( "\t fx deal leg currency2 amt = " + fxDealLeg.getCurrency2Amount() );
            log( "\t fx deal leg dlt ccy amt  = " + fxDealLeg.getDealtAmount() );
            log( "\t fx deal leg stld ccy amt = " + fxDealLeg.getSettledAmount() );
            log( "\t fx deal leg base ccy amt  = " + fxDealLeg.getBaseAmount() );
            log( "\t fx deal leg var ccy amt = " + fxDealLeg.getVariableAmount() );
            log( "\t fx deal leg buy ccy amt   = " + fxDealLeg.getBuyAmount() );
            log( "\t fx deal leg sell amt = " + fxDealLeg.getSellAmount() );
        }
    }

    private void printDealRequest( DealRequest dealRequest )
    {
        log( "\t dealRequest object id   = " + dealRequest.getObjectID() );
        log( "\t dealRequest message id = " + dealRequest.getMessageId() );
        log( "\t dealRequest price provision limit type = " + dealRequest.getPriceProvisionLimitType() );
        log( "\t dealRequest notes = " + dealRequest.getNotes() );
        log( "\t dealRequest price provision spread type = " + dealRequest.getPriceProvisionSpreadType() );
        log( "\t dealRequest provider quote id = " + dealRequest.getProviderQuoteId() );
        log( "\t dealRequest quote id = " + dealRequest.getQuoteId() );
        log( "\t dealRequest request id = " + dealRequest.getRequestId() );
        log( "\t dealRequest trade id = " + dealRequest.getTradeId() );
    }

    private void printOrder( Order order )
    {
        log( "\t order object id   = " + order.getObjectID() );
        log( "\t order created time = " + order.getCreatedDate() );
        log( "\t order context info = " + order.getContextInfo() );
        log( "\t order cptyA org = " + order.getCounterpartyAOrg() );
        log( "\t order amount = " + order.getAmount() );
        log( "\t order average order rate = " + order.getAverageRate() );
        log( "\t order filled amount = " + order.getFilledAmount() );
        log( "\t order exp clsf = " + order.getExpirationClassification() );
        log( "\t order clsf = " + order.getOrderClassification() );
        log( "\t order order id = " + order.getOrderId() );
        log( "\t order rate = " + order.getOrderRate() );
        log( "\t order state = " + order.getState() );
        log( "\t order user = " + order.getUser() );
    }

    private Vector getReadOnlyClasses()
    {
        Vector roClasses = new Vector( 10 );
        roClasses.add( com.integral.finance.currency.CurrencyC.class );
        roClasses.add( com.integral.finance.counterparty.CounterpartyC.class );
        roClasses.add( com.integral.finance.counterparty.LegalEntityC.class );
        roClasses.add( com.integral.finance.counterparty.TradingPartyC.class );
        roClasses.add( com.integral.user.UserC.class );
        roClasses.add( com.integral.user.OrganizationC.class );
        roClasses.add( com.integral.finance.trade.TradeClassificationC.class );
        roClasses.add( com.integral.finance.trade.TradeLegClassificationC.class );
        roClasses.add( com.integral.finance.dealing.RequestClassificationC.class );
        roClasses.add( com.integral.workflow.StateC.class );
        return roClasses;
    }
}
