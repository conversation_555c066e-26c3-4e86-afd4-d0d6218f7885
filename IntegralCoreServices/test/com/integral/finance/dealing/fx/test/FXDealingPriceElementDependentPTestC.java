package com.integral.finance.dealing.fx.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.DealingPriceElement;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXDealingPriceElementDependentC;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXLegDealingPriceC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.price.Price;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.workgroup.WorkGroup;
import com.integral.xml.binding.JavaXMLBinderFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

/**
 *
 */
public class FXDealingPriceElementDependentPTestC extends PTestCaseC
{
    static String standardFXConventionName = "STDQOTCNV";
    public FXRateConvention aTestConvention = null;
    public Currency usd, jpy, cad, eur;
    User lpUser;

    private static int PRICE_ELEMENT_FROM_FACTORY = 0;
    private static int DEPENDENT_PRICE_ELEMENT_FROM_FACTORY = 1;
    private static int REGISTERED_DEPENDENT_PRICE_ELEMENT = 3;

    public FXDealingPriceElementDependentPTestC( String name )
    {
        super( name );
        eur = CurrencyFactory.getCurrency( "EUR" );
        usd = CurrencyFactory.getCurrency( "USD" );
        cad = CurrencyFactory.getCurrency( "CAD" );
        jpy = CurrencyFactory.getCurrency( "JPY" );
        lpUser = UserFactory.getUser( "Integral1" );
    }

    public void testFXLegInsert()
    {
        log( "testFXLegInsert" );
        try
        {
            persistFXLeg( PRICE_ELEMENT_FROM_FACTORY );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXLegInsert" );
        }
    }

    public void testFXLegInsert1()
    {
        log( "testFXLegInsert" );
        try
        {
            persistFXLeg( DEPENDENT_PRICE_ELEMENT_FROM_FACTORY );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXLegInsert" );
        }
    }

    public void testFXLegInsert2()
    {
        log( "testFXLegInsert" );
        try
        {
            persistFXLeg( REGISTERED_DEPENDENT_PRICE_ELEMENT );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXLegInsert" );
        }
    }

    public void testFXDealingPriceElementDependentFXPriceCurrencies()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            FXRate offerRate = FXFactory.newFXRate();
            offerRate.setBaseCurrency( eur );
            offerRate.setVariableCurrency( usd );

            FXRate bidRate = FXFactory.newFXRate();
            bidRate.setBaseCurrency( eur );
            bidRate.setVariableCurrency( usd );

            FXRate midRate = FXFactory.newFXRate();
            midRate.setBaseCurrency( eur );
            midRate.setVariableCurrency( usd );

            FXPrice price = FXPriceFactory.newFXPrice();
            price.setOfferFXRate( offerRate );
            price.setBidFXRate( bidRate );
            price.setMidFXRate( midRate );

            FXLegDealingPrice fxDp = new FXLegDealingPriceC();
            FXDealingPriceElementDependentC fxDpeDependent = new FXDealingPriceElementDependentC();
            fxDpeDependent.setPrice( price );
            fxDp.setPriceElement( fxDpeDependent );
            fxDp.setStatus( 'T' );
            uow.registerObject( fxDp );

            uow.commit();

            fxDp = ( FXLegDealingPrice ) getPersistenceSession().refreshObject( fxDp );
            FXPrice fxPrc = ( ( FXDealingPriceElementDependentC ) fxDp.getPriceElement() ).getFXPrice();
            log( "fxPrc.bidBaseccy=" + fxPrc.getBidFXRate().getBaseCurrency() );
            log( "fxPrc.bidVarccy=" + fxPrc.getBidFXRate().getVariableCurrency() );
            log( "fxPrc.midBaseccy=" + fxPrc.getMidFXRate().getBaseCurrency() );
            log( "fxPrc.midBaseccy=" + fxPrc.getMidFXRate().getVariableCurrency() );
            log( "fxPrc.offerBaseccy=" + fxPrc.getOfferFXRate().getBaseCurrency() );
            log( "fxPrc.offerbaseccy=" + fxPrc.getOfferFXRate().getVariableCurrency() );

            assertEquals( "fxPrc.bidBaseccy", fxPrc.getBidFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.bidVarccy", fxPrc.getBidFXRate().getVariableCurrency(), usd );
            assertEquals( "fxPrc.midBaseccy", fxPrc.getMidFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.midVarccy", fxPrc.getMidFXRate().getVariableCurrency(), usd );
            assertEquals( "fxPrc.offerBaseccy", fxPrc.getOfferFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.offerBaseccy", fxPrc.getOfferFXRate().getVariableCurrency(), usd );

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( Entity.ObjectID ).equal( fxDp.getObjectID() );
            ReportQuery rq = new ReportQuery( eb );
            rq.setReferenceClass( FXLegDealingPriceC.class );
            rq.setSelectionCriteria( expr );
            rq.addAttribute( "baseCcy", eb.getField( "baseCrncId" ) );
            rq.addAttribute( "varCcy", eb.getField( "varCrncId" ) );
            Collection<ReportQueryResult> results = ( Collection<ReportQueryResult> ) getPersistenceSession().executeQuery( rq );
            for ( ReportQueryResult rqr : results )
            {
                long baseCcyId = ( ( Number ) rqr.get( "baseCcy" ) ).longValue();
                long varCcyId = ( ( Number ) rqr.get( "varCcy" ) ).longValue();
                log( "baseCcyId=" + baseCcyId + ",varCcyId=" + varCcyId );
                assertEquals( "baseCcy should be not null.", CurrencyFactory.getCurrency( baseCcyId ), eur );
                assertEquals( "varCcy should be not null.", CurrencyFactory.getCurrency( varCcyId ), usd );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }


    private void persistFXLeg( int paymentParamType )
    {
        UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();

        DealingPrice dealingPrice = DealingFactory.newDealingPrice();
        dealingPrice = ( DealingPrice ) uow.registerObject( dealingPrice );

        dealingPrice.setName( "test" );
        dealingPrice.setAcceptedDateTime( new Date() );
        dealingPrice.setAcceptedPriceBidOfferMode( DealingPrice.BID );
        DealingPriceElement priceElement = null;

        if ( paymentParamType == REGISTERED_DEPENDENT_PRICE_ELEMENT )
        {
            priceElement = FXDealingFactory.newFXDealingPriceElementDependent();
            priceElement = ( FXDealingPriceElement ) priceElement.getRegisteredObject();
        }
        else if ( paymentParamType == DEPENDENT_PRICE_ELEMENT_FROM_FACTORY )
        {
            priceElement = FXDealingFactory.newFXDealingPriceElementDependent();
        }
        else if ( paymentParamType == PRICE_ELEMENT_FROM_FACTORY )
        {
            priceElement = FXDealingFactory.newFXDealingPriceElement();
        }

        populateDealingPriceElement( priceElement, lpUser );

        WorkGroup wg = ( WorkGroup ) getNamedEntity( WorkGroup.class, "DefaultCUG" );
        wg = ( WorkGroup ) uow.registerObject( wg );
        priceElement.setWorkGroup( wg );


        dealingPrice.setPriceElement( priceElement );
        uow.commit();
        printDealingPriceElementDetails( "Persisting to DB", dealingPrice );

        uow = this.getPersistenceSession().acquireUnitOfWork();
        uow.refreshObject( dealingPrice );
        printDealingPriceElementDetails( "Reading from DB", dealingPrice );
        checkXMLMapping( dealingPrice );
    }

    private void populateDealingPriceElement( DealingPriceElement dealingPriceEle, User user )
    {
        dealingPriceEle.setBidNotionalAmount( 10000.00 );
        dealingPriceEle.setCalculated( Boolean.TRUE );
        dealingPriceEle.setCalculationRequired( Boolean.TRUE );
        Request request = DealingFactory.newRequest();
        request.setTransactionID( "Test" + System.nanoTime() );
        dealingPriceEle.setForwardRequest( request );
        try
        {
            dealingPriceEle.setName( "test" );
        }
        catch ( Exception exc )
        {
            log( "Exception.setName - " + exc );
        }
        dealingPriceEle.setOfferNotionalAmount( 15000.00 );
        dealingPriceEle.setOrganization( user.getOrganization() );

        try
        {
            DealingPriceElement parentPriceElement = FXDealingFactory.newFXDealingPriceElement();
            parentPriceElement.setName( "test_P" );
            dealingPriceEle.setParent( parentPriceElement );
        }
        catch ( Exception exc )
        {
            log( "Exception.setParent - " + exc );
        }


        dealingPriceEle.setPrice( getPrice() );
        try
        {
            dealingPriceEle.setPriceElements( new ArrayList() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setPriceElements - " + exc );
        }

        try
        {
            dealingPriceEle.setSortOrder( 1 );
        }
        catch ( Exception exc )
        {
            log( "Exception.setSortOrder - " + exc );
        }
    }

    private Price getPrice()
    {
        FXPrice usd_cad = FXPriceFactory.newFXPrice();

        usd_cad.setBid( fxRate( usd, cad, 2.0, 2.0 ) );
        usd_cad.setOffer( fxRate( usd, cad, 3.0, 3.0 ) );
        usd_cad.setMid( fxRate( usd, cad, 2.5, 2.5 ) );
        return usd_cad;
    }

    private FXRate fxRate( Currency base, Currency var, double spotRate, double fwdPoints )
    {
        FXRate fxRate = new FXRateC();
        fxRate.setBaseCurrency( base );
        fxRate.setVariableCurrency( var );
        fxRate.setSpotRate( spotRate );
        fxRate.setForwardPoints( fwdPoints );
        return fxRate;
    }

    private void printDealingPriceElementDetails( String comment, DealingPrice dealingPrice )
    {
        log( "\t ********************** " + comment + " ****************************" );
        log( "\t dealingPrice   = " + dealingPrice );
        log( "\t dealingPrice.getName   = " + dealingPrice.getName() );
        log( "\t dealingPrice.getAcceptedDateTime   = " + dealingPrice.getAcceptedDateTime() );
        log( "\t dealingPrice.getAcceptedPriceBidOfferMode   = " + dealingPrice.getAcceptedPriceBidOfferMode() );

        DealingPriceElement priceElement = dealingPrice.getPriceElement();

        log( "\t FXDealingPriceElement = " + priceElement );
        log( "\t FXDealingPriceElement.getBidNotionalAmount = " + priceElement.getBidNotionalAmount() );
        log( "\t FXDealingPriceElement.isCalculated = " + priceElement.isCalculated() );
        log( "\t FXDealingPriceElement.isCalculationRequired = " + priceElement.isCalculationRequired() );
        log( "\t FXDealingPriceElement.getForwardRequest = " + priceElement.getForwardRequest() );
        log( "\t FXDealingPriceElement.getName = " + priceElement.getName() );
        log( "\t FXDealingPriceElement.getOfferNotionalAmount = " + priceElement.getOfferNotionalAmount() );
        log( "\t FXDealingPriceElement.getOrganization = " + priceElement.getOrganization() );
        log( "\t FXDealingPriceElement.getParent = " + priceElement.getParent() );

        Price price = priceElement.getPrice();

        if ( null != price )
        {
            log( "\t FXDealingPriceElement.getPrice = " + price );
            log( "\t FXDealingPriceElement.getPrice.getBid = " + ( ( FXRate ) price.getBid() ).getRate() );
            log( "\t FXDealingPriceElement.getPrice.getOffer = " + ( ( FXRate ) price.getOffer() ).getRate() );
        }

        log( "\t FXDealingPriceElement.getFXRate.getPriceElements = " + priceElement.getPriceElements() );
        log( "\t FXDealingPriceElement.getFXRate.getSortOrder = " + priceElement.getSortOrder() );
    }

    private void checkXMLMapping( DealingPrice dealingPrice )
    {
        try
        {
            StringWriter sw = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw, dealingPrice, "Integral" );

            log( "******************* FXLeg to XML ************************" );
            log( sw.toString() );

            StringReader sr = new StringReader( sw.toString() );
            Object dPrice = JavaXMLBinderFactory.newJavaXMLBinder().convertFromXML( sr, "Integral" );

            printDealingPriceElementDetails( "Reading from XML ", ( DealingPrice ) dPrice );
        }
        catch ( Exception exc )
        {
            log( "Exception.checkXMLMapping() - " + exc );
        }
    }

    protected NamedEntity getNamedEntity( Class aClass, String shortName )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).equal( shortName );
        Object obj;
        if ( this.uow == null )
        {
            obj = this.getPersistenceSession().readObject( aClass, expr );
        }
        else
        {
            obj = this.uow.readObject( aClass, expr );
        }
        return ( NamedEntity ) obj;
    }
}
