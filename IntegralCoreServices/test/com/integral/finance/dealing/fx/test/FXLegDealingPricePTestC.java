package com.integral.finance.dealing.fx.test;

import com.integral.exception.IdcDatabaseException;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXDealingPriceElementC;
import com.integral.finance.dealing.fx.FXDealingPriceElementDependentC;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXLegDealingPriceC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSideRates;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;

/**
 * Unit test for fx dealing price element
 */
public class FXLegDealingPricePTestC extends PTestCaseC
{
    static String name = "FXLegDealingPrice Test";

    public FXLegDealingPricePTestC( String name )
    {
        super( name );
    }

    /**
     * Inserts a new fx dealing price with a fx price that
     * has the offer fx rate pointing to a fx side rates
     */
    public void testInsert()
    {
        log( "insertTest" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            FXRate offerRate = FXFactory.newFXRate();
            offerRate.setBaseCurrency( CurrencyFactory.getCurrency( "EUR " ) );
            offerRate.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );

            FXSideRates newSideRate = FXFactory.newFXSideRates();
            offerRate.setSideRates( newSideRate );

            FXPrice price = FXPriceFactory.newFXPrice();
            price.setOfferFXRate( offerRate );

            FXDealingPriceElement dpe = new FXDealingPriceElementC();
            dpe = ( FXDealingPriceElement ) uow.registerObject( dpe );
            FXLegDealingPrice dp = new FXLegDealingPriceC();
            dp = ( FXLegDealingPrice ) uow.registerObject( dp );
            dp.setPriceElement( dpe );
            dpe.setPrice( price );
            dpe.setStatus( 'T' );

            uow.commit();

            // refresh the dealing price.
            dp = ( FXLegDealingPrice ) getPersistenceSession().refreshObject( dp );
            assertNotNull( dp );
            FXDealingPriceElement fxDpe = ( FXDealingPriceElement ) dp.getPriceElement();
            log( "fxDpe=" + fxDpe );
            assertNotNull( fxDpe );
            assertEquals( "Should not be dependent class" + fxDpe, fxDpe instanceof FXDealingPriceElementDependentC, false );
            log( "insertTest" );
        }
        catch ( Exception e )
        {
            fail( "Exception in insertTest", e );
        }
    }

    /**
     * Inserts a new fx dealing price with a fx price that
     * has the offer fx rate pointing to a fx side rates that is also
     * registered in a second transaction
     */
    public void testInsertWithDoubleRegistration()
    {
        log( "testInsertWithDoubleRegistration" );
        try
        {
            FXSideRates newSideRate = FXFactory.newFXSideRates();

            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            FXSideRates newSideRate1 = ( FXSideRates ) uow1.registerObject( newSideRate );
            FXRate offerRate1 = FXFactory.newFXRate();
            offerRate1.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            offerRate1.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            offerRate1.setSideRates( newSideRate1 );
            FXPrice price1 = FXPriceFactory.newFXPrice();
            price1.setOfferFXRate( offerRate1 );
            FXDealingPriceElement dpe1 = new FXDealingPriceElementC();
            dpe1 = ( FXDealingPriceElement ) uow1.registerObject( dpe1 );
            FXLegDealingPrice dp1 = new FXLegDealingPriceC();
            dp1 = ( FXLegDealingPrice ) uow1.registerObject( dp1 );
            dp1.setPriceElement( dpe1 );
            dpe1.setPrice( price1 );
            dpe1.setStatus( 'T' );

            //uow1.printRegisteredObjects();
            uow1.commit();

            UnitOfWork uow2 = PersistenceFactory.newSession().acquireUnitOfWork();
            FXSideRates newSideRate2 = ( FXSideRates ) uow2.registerObject( newSideRate );
            FXRate offerRate2 = FXFactory.newFXRate();
            offerRate2.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            offerRate2.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            offerRate2.setSideRates( newSideRate2 );
            FXPrice price2 = FXPriceFactory.newFXPrice();
            price2.setOfferFXRate( offerRate2 );
            FXDealingPriceElement dpe2 = new FXDealingPriceElementC();
            dpe2 = ( FXDealingPriceElement ) uow2.registerObject( dpe2 );
            FXLegDealingPrice dp2 = new FXLegDealingPriceC();
            dp2 = ( FXLegDealingPrice ) uow2.registerObject( dp2 );
            dp2.setPriceElement( dpe2 );
            dpe2.setPrice( price2 );
            dpe2.setStatus( 'T' );

            boolean exception = false;
            try
            {
                uow2.commit();
            }
            catch ( IdcDatabaseException ex )
            {
                exception = true;
            }

            assertEquals( "The second db transaction should not fail since fx side rates is not privately owned. exception=" + exception, exception, false );
            log( "testInsertWithDoubleRegistration" );
        }
        catch ( Exception e )
        {
            fail( "Failed test testInsertWithDoubleRegistration", e );
        }
    }

    public void testFXLegDealingPriceFXPriceCurrencies()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            FXRate offerRate = FXFactory.newFXRate();
            offerRate.setBaseCurrency( eur );
            offerRate.setVariableCurrency( usd );

            FXRate bidRate = FXFactory.newFXRate();
            bidRate.setBaseCurrency( eur );
            bidRate.setVariableCurrency( usd );

            FXRate midRate = FXFactory.newFXRate();
            midRate.setBaseCurrency( eur );
            midRate.setVariableCurrency( usd );

            FXPrice price = FXPriceFactory.newFXPrice();
            price.setOfferFXRate( offerRate );
            price.setBidFXRate( bidRate );
            price.setMidFXRate( midRate );

            FXLegDealingPrice dp = new FXLegDealingPriceC();
            dp = ( FXLegDealingPrice ) uow.registerObject( dp );
            FXDealingPriceElement dpe = new FXDealingPriceElementDependentC();
            dp.setPriceElement( dpe );
            dpe.setPrice( price );
            dpe.setStatus( 'T' );

            uow.commit();

            dp = ( FXLegDealingPrice ) getPersistenceSession().refreshObject( dp );
            FXPrice fxPrc = ( FXPrice ) dp.getPriceElement().getPrice();
            log( "fxPrc.bidBaseccy=" + fxPrc.getBidFXRate().getBaseCurrency() );
            log( "fxPrc.bidVarccy=" + fxPrc.getBidFXRate().getVariableCurrency() );
            log( "fxPrc.midBaseccy=" + fxPrc.getMidFXRate().getBaseCurrency() );
            log( "fxPrc.midBaseccy=" + fxPrc.getMidFXRate().getVariableCurrency() );
            log( "fxPrc.offerBaseccy=" + fxPrc.getOfferFXRate().getBaseCurrency() );
            log( "fxPrc.offerbaseccy=" + fxPrc.getOfferFXRate().getVariableCurrency() );

            assertEquals( "fxPrc.bidBaseccy", fxPrc.getBidFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.bidVarccy", fxPrc.getBidFXRate().getVariableCurrency(), usd );
            assertEquals( "fxPrc.midBaseccy", fxPrc.getMidFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.midVarccy", fxPrc.getMidFXRate().getVariableCurrency(), usd );
            assertEquals( "fxPrc.offerBaseccy", fxPrc.getOfferFXRate().getBaseCurrency(), eur );
            assertEquals( "fxPrc.offerBaseccy", fxPrc.getOfferFXRate().getVariableCurrency(), usd );

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( Entity.ObjectID ).equal( dp.getObjectID() );
            ReportQuery rq = new ReportQuery( eb );
            rq.setReferenceClass( FXLegDealingPriceC.class );
            rq.setSelectionCriteria( expr );
            rq.addAttribute( "baseCcy", eb.getField( "baseCrncId" ) );
            rq.addAttribute( "varCcy", eb.getField( "varCrncId" ) );
            Collection<ReportQueryResult> results = ( Collection<ReportQueryResult> ) getPersistenceSession().executeQuery( rq );
            for ( ReportQueryResult rqr : results )
            {
                long baseCcyId = ( ( Number ) rqr.get( "baseCcy" ) ).longValue();
                long varCcyId = ( ( Number ) rqr.get( "varCcy" ) ).longValue();
                log( "baseCcyId=" + baseCcyId + ",varCcyId=" + varCcyId );
                assertEquals( "baseCcy should be not null.", CurrencyFactory.getCurrency( baseCcyId ), eur );
                assertEquals( "varCcy should be not null.", CurrencyFactory.getCurrency( varCcyId ), usd );
            }
        }
        catch ( Exception e )
        {
            fail( "Exception in insertTest", e );
        }
    }

    public void testFieldsPersistence()
    {
        log( "testFieldsPersistence" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            FXRate offerRate = FXFactory.newFXRate();
            offerRate.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            offerRate.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );

            FXRate bidRate = FXFactory.newFXRate();
            bidRate.setBaseCurrency( CurrencyFactory.getCurrency( "USD" ) );
            bidRate.setVariableCurrency( CurrencyFactory.getCurrency( "EUR" ) );


            FXPrice price = FXPriceFactory.newFXPrice();
            price.setOfferFXRate( offerRate );
            price.setBidFXRate( bidRate );

            FXLegDealingPrice dp = new FXLegDealingPriceC();
            dp = ( FXLegDealingPrice ) uow.registerObject( dp );
            FXDealingPriceElement dpe = new FXDealingPriceElementC();
            dpe = ( FXDealingPriceElement ) uow.registerObject( dpe );
            dpe.setPrice( price );
            dp.setPriceElement( dpe );
            assertNull( dp.getVerifiedAmount() );
            double verifiedAmt = 100;
            double verifiedStldAmt = 120;
            double origOrderAmt = 1000.00;
            double origOrderRate = 1.34556;
            double TOBBidRate = 1.2343;
            double TOBOfferRate = 1.3534;

            dp.setMarketRange( null );
            dp.setMarketRange( 5.0 );
            dp.setBestMarketPrice( 3.4 );

            dp.setStopLossInitialTriggerRate( null );
            dp.setStopLossInitialTriggerRate( 10.24 );

            dp.setStopLossTriggerRate( null );
            dp.setStopLossTriggerRate( 10.20 );

            dp.setStopLossTriggerPoints( null );
            dp.setStopLossTriggerPoints( 0.12 );
            dp.setStopLossTriggered( true );

            dp.setAverageRate( null );
            dp.setAverageRate( 1.89 );
            dp.setFilledAmount( null );
            dp.setFilledAmount( ( double ) 30000000 );
            dp.setPriceImprovement( null );
            dp.setPriceImprovement( 0.00056 );
            dp.setVerifiedAmount( verifiedAmt );
            dp.addAcceptedAmounts( "100", verifiedAmt, verifiedStldAmt );
            dp.addVerifiedAmounts( "100", verifiedAmt, verifiedStldAmt );
            dp.setOriginalOrderAmount( origOrderAmt );
            dp.setOriginalOrderRate( origOrderRate );
            dp.setTOBBidRateAtTerminalState( TOBBidRate );
            dp.setTOBOfferRateAtTerminalState( TOBOfferRate );
            
            IdcDate fixingDate = DateTimeFactory.newDate().addDays( 2 );
            dp.setFixingDate( fixingDate );
            dp.setFixingTenor( Tenor.SPOT_TENOR );

            IdcDate variableValueDate = DateTimeFactory.newDate ().addDays ( 5 );
            dp.setVariableValueDate ( variableValueDate );
            dp.setVariableTenor ( new Tenor ( "5D" ) );
            uow.commit();

            // refresh the dealing price.
            dp.setVerifiedAmount( null );
            dp = ( FXLegDealingPrice ) getPersistenceSession().refreshObject( dp );
            assertNotNull( dp );
            assertEquals( dp.getMarketRange(), ( double ) 5 );
            assertEquals( dp.getStopLossInitialTriggerRate(), 10.24 );
            assertEquals( dp.getStopLossTriggerRate(), 10.20 );
            assertEquals( dp.getStopLossTriggerPoints(), 0.12 );
            assertEquals( dp.isStopLossTriggered(), true );
            assertEquals( dp.getBestMarketPrice(), 3.4 );
            assertEquals( dp.getAverageRate(), 1.89 );
            assertEquals( dp.getFilledAmount(), ( double ) 30000000 );
            assertEquals( dp.getPriceImprovement(), 0.00056 );
            assertEquals( dp.getVerifiedAmount(), verifiedAmt );
            assertEquals( dp.getVerifiedDealtCurrencyAmount(), verifiedAmt );
            assertEquals( dp.getVerifiedSettledCurrencyAmount(), verifiedStldAmt );
            assertEquals( dp.getAcceptedDealtCurrencyAmount(), verifiedAmt );
            assertEquals( dp.getAcceptedSettledCurrencyAmount(), verifiedStldAmt );
            assertEquals( dp.getOriginalOrderAmount(), origOrderAmt );
            assertEquals( dp.getOriginalOrderRate(), origOrderRate );
            assertEquals( dp.getTOBBidRateAtTerminalState(), TOBBidRate );
            assertEquals( dp.getTOBOfferRateAtTerminalState(), TOBOfferRate );
            assertEquals( dp.getFixingDate(), fixingDate );
            assertEquals( dp.getFixingTenor(), Tenor.SPOT_TENOR );

            // to check if the field values are allowed as null
            uow = getPersistenceSession().acquireUnitOfWork();
            dp = ( FXLegDealingPrice ) uow.registerObject( dp );
            dp.setStopLossTriggerPoints( null );
            dp.setBestMarketPrice( null );
            dp.setAverageRate( null );
            dp.setFilledAmount( null );
            dp.setPriceImprovement( null );
            dp.setVerifiedAmount( null );

            uow.commit();

            dp = ( FXLegDealingPrice ) getPersistenceSession().refreshObject( dp );
            assertNotNull( dp );
            assertEquals( dp.getStopLossTriggerPoints(), null );
            assertEquals( dp.getBestMarketPrice(), null );
            assertEquals( dp.getAverageRate(), null );
            assertEquals( dp.getFilledAmount(), null );
            assertEquals( dp.getPriceImprovement(), null );
            assertNull( dp.getVerifiedAmount() );
            assertTrue ( "variable tenor", new Tenor( "5D" ).equals ( dp.getVariableTenor () ) );
            assertTrue ( "variable value date", variableValueDate.isSameAs ( dp.getVariableValueDate () ) );

            log( "testFieldsPersistence test finished" );
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence", e );
        }
    }

    public void testAcceptedAmounts()
    {
        try
        {
            double dlt1 = 1000;
            double dlt2 = 3000;
            double dlt3 = 1000000;
            double stld1 = 1200;
            double stld2 = 3600;
            double stld3 = 1300000;

            FXLegDealingPrice dp = FXDealingFactory.newFXLegDealingPrice();
            dp.addAcceptedAmounts( "FXI100", dlt1, stld1 );
            assertEquals( dp.getAcceptedDealtCurrencyAmount(), dlt1 );
            assertEquals( dp.getAcceptedSettledCurrencyAmount(), stld1 );

            // now reset values to zero for same transaction id
            dp.addAcceptedAmounts( "FXI100", 0.0, 0.0 );
            assertEquals( dp.getAcceptedDealtCurrencyAmount(), 0.0 );
            assertEquals( dp.getAcceptedSettledCurrencyAmount(), 0.0 );

            dp.addAcceptedAmounts( "FXI100", dlt2, stld2 );
            assertEquals( dp.getAcceptedDealtCurrencyAmount(), dlt2 );
            assertEquals( dp.getAcceptedSettledCurrencyAmount(), stld2 );

            dp.addAcceptedAmounts( "FXI101", dlt3, stld3 );
            assertEquals( dp.getAcceptedDealtCurrencyAmount(), dlt2 + dlt3 );
            assertEquals( dp.getAcceptedSettledCurrencyAmount(), stld2 + stld3 );

            // now reduce the amount for another transaction
            dp.addAcceptedAmounts( "FXI102", -dlt3, -stld3 );
            assertEquals( dp.getAcceptedDealtCurrencyAmount(), dlt2 );
            assertEquals( dp.getAcceptedSettledCurrencyAmount(), stld2 );

            // register negative amount for an existing transaction.
            dp.addAcceptedAmounts( "FXI100", -dlt2, -stld2 );
            assertEquals( dp.getAcceptedDealtCurrencyAmount(), -dlt2 );
            assertEquals( dp.getAcceptedSettledCurrencyAmount(), -stld2 );
        }
        catch ( Exception e )
        {
            fail( "testAcceptedAmounts" );
        }
    }

    public void testVerifiedAmounts()
    {
        try
        {
            double dlt1 = 1000;
            double dlt2 = 3000;
            double dlt3 = 1000000;
            double stld1 = 1200;
            double stld2 = 3600;
            double stld3 = 1300000;

            FXLegDealingPrice dp = FXDealingFactory.newFXLegDealingPrice();
            dp.addVerifiedAmounts( "FXI100", dlt1, stld1 );
            assertEquals( dp.getVerifiedDealtCurrencyAmount(), dlt1 );
            assertEquals( dp.getVerifiedSettledCurrencyAmount(), stld1 );

            // now reset values to zero for same transaction id
            dp.addVerifiedAmounts( "FXI100", 0.0, 0.0 );
            assertEquals( dp.getVerifiedDealtCurrencyAmount(), 0.0 );
            assertEquals( dp.getVerifiedSettledCurrencyAmount(), 0.0 );

            dp.addAcceptedAmounts( "FXI100", dlt2, stld2 );
            assertEquals( dp.getVerifiedDealtCurrencyAmount(), 0.0 );
            assertEquals( dp.getVerifiedSettledCurrencyAmount(), 0.0 );

            dp.addVerifiedAmounts( "FXI100", dlt1, stld1 );
            dp.addVerifiedAmounts( "FXI101", dlt3, stld3 );
            assertEquals( dp.getVerifiedDealtCurrencyAmount(), dlt1 + dlt3 );
            assertEquals( dp.getVerifiedSettledCurrencyAmount(), stld1 + stld3 );

            // now reduce the amount for another transaction
            dp.addVerifiedAmounts( "FXI102", 0.0, 0.0 );
            assertEquals( dp.getVerifiedDealtCurrencyAmount(), dlt1 + dlt3 );
            assertEquals( dp.getVerifiedSettledCurrencyAmount(), stld1 + stld3 );

            // register negative amount for an existing transaction.
            dp.addVerifiedAmounts( "FXI100", 0.0, 0.0 );
            assertEquals( dp.getVerifiedDealtCurrencyAmount(), dlt3 );
            assertEquals( dp.getVerifiedSettledCurrencyAmount(), stld3 );
        }
        catch ( Exception e )
        {
            fail( "testVerifiedAmounts" );
        }
    }
}
