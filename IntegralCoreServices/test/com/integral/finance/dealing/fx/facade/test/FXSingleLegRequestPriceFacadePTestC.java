package com.integral.finance.dealing.fx.facade.test;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXLegDealingPriceC;
import com.integral.finance.dealing.fx.facade.FXSingleLegRequestPriceFacade;
import com.integral.finance.dealing.fx.facade.FXSingleLegRequestPriceFacadeC;
import com.integral.test.PTestCaseC;

/**
 * Unit test for fx dealing price element
 */
public class FXSingleLegRequestPriceFacadePTestC extends PTestCaseC
{
    static String name = "FXSingleLegRequestPriceFacade Test";

    public FXSingleLegRequestPriceFacadePTestC( String name )
    {
        super( name );
    }

    public void testAmounts()
    {
        try
        {
            Request request = new RequestC();
            FXLegDealingPrice fxDp = new FXLegDealingPriceC();
            request.setRequestPrice( FXLegDealingPrice.SINGLE_LEG, fxDp );
            fxDp.setDealtAmount( 100000000.00 );
            fxDp.setDealtCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            fxDp.setSettledCurrency( CurrencyFactory.getCurrency( "USD" ) );
            fxDp.setFilledAmount( 9.999999999999999E7 );

            FXSingleLegRequestPriceFacade prcFacade = new FXSingleLegRequestPriceFacadeC();
            prcFacade.setEntity( fxDp );

            double unfilledAmt = prcFacade.getUnfilledAmount();
            log( "unfilledAmt=" + unfilledAmt );
            assertEquals( "unfilled amt should be zero. unfilledAmt=" + unfilledAmt, unfilledAmt, 0.0 );

        }
        catch ( Exception e )
        {
            fail( "testAmounts", e );
        }
    }

}
