package com.integral.finance.dealing.fx.query.test;

import com.integral.exception.IdcException;
import com.integral.exception.IdcRuntimeException;
import com.integral.exception.IdcThrowable;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.QuotePortfolio;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestClassification;
import com.integral.finance.dealing.fx.query.FXQuoteQueryC;
import com.integral.finance.dealing.fx.query.FXRequestQueryC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.Trade;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.query.NamedQuery;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.workflow.State;
import com.integral.workflow.WorkflowFactory;
import com.integral.workflow.WorkflowStateMap;
import com.integral.workgroup.WorkGroup;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Vector;

/**
 * Tests request query
 */
public class FXDealingQueryPTestC
        extends PTestCaseC
{

    public FXDealingQueryPTestC( String name )
    {
        super( name );
    }

    public void testReadReferenceData()
    {
        try
        {
            Session session = getPersistenceSession();
            readAllObjects( session, RequestClassification.class );
            readAllObjects( session, State.class );
            readAllObjects( session, Currency.class );
            readAllObjects( session, Organization.class );
            readAllObjects( session, QuotePortfolio.class );
            readAllObjects( session, WorkGroup.class );
            readAllObjects( session, TradingParty.class );
        }
        catch ( Exception e )
        {
            fail( "Failed in testReadReferenceData : ", e );
        }
    }

    public void testReadRequests()
    {
        try
        {
            Session session = getPersistenceSession();
            readAllObjects( session, Request.class );
        }
        catch ( IdcRuntimeException ire )
        {
            printException( ire );
        }
        catch ( Exception e )
        {
            fail( "Failed in testReadRequests :  ", e );
        }
    }

    protected void printException( IdcThrowable t )
    {
        List nestedExceptions = t.getNestedExceptions();
        if ( nestedExceptions != null )
        {
            for ( Iterator iterator = nestedExceptions.iterator(); iterator.hasNext(); )
            {
                Exception exception = ( Exception ) iterator.next();
                log.info( "nested exception = " + exception );
            }
        }
        log.info( "parent exception = " + t );
    }

    protected void readAllObjects( Session session, Class clazz )
    {
        Vector results = session.readAllObjects( clazz );
        log.info( "\n\t" + clazz + " found: " + results.size() );
        for ( int i = 0; i < results.size(); i++ )
        {
            Entity result = ( Entity ) results.elementAt( i );
            log.info( result + " " + result.getObjectId() );
        }
    }

    public void testCreateRequests()
    {
        try
        {
            Session session = getPersistenceSession();

            String[] orgArray = {"FI1"};
            ExpressionBuilder orgEb = new ExpressionBuilder();
            Expression orgExpr = orgEb.get( NamedEntity.ShortName ).in( orgArray );
            Organization organization = ( Organization ) session.readObject( Organization.class, orgExpr );

            String[] reqClsfArray = {"PRICE", "INDICATE", "RFQ", "QUOTED"};
            ExpressionBuilder reqClsfEb = new ExpressionBuilder();
            Expression reqClsfExpr = reqClsfEb.get( NamedEntity.ShortName ).in( reqClsfArray );
            Vector requestClassifications = session.readAllObjects( RequestClassification.class, reqClsfExpr );

            String[] stateArray = {"Declined", "Approved"};
            ExpressionBuilder stateEb = new ExpressionBuilder();
            Expression stateExpr = stateEb.get( NamedEntity.ShortName ).in( stateArray );
            Vector states = session.readAllObjects( State.class, stateExpr );

            String[] ccyArray = {"EUR", "USD", "JPY", "GBP", "CAD"};
            ExpressionBuilder ccyEb = new ExpressionBuilder();
            Expression ccyExpr = ccyEb.get( NamedEntity.ShortName ).in( ccyArray );
            Vector currencies = session.readAllObjects( Currency.class, ccyExpr );

            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );

            for ( Iterator requestClassificationIterator = requestClassifications.iterator(); requestClassificationIterator.hasNext(); )
            {
                RequestClassification requestClassification = ( RequestClassification ) requestClassificationIterator.next();
                log.info( "requestClassification=" + requestClassification );
                for ( Iterator stateIterator = states.iterator(); stateIterator.hasNext(); )
                {
                    State state = ( State ) stateIterator.next();
                    log.info( "\t state=" + state );
                    for ( Iterator currency1Iterator = currencies.iterator(); currency1Iterator.hasNext(); )
                    {
                        Currency currency1 = ( Currency ) currency1Iterator.next();
                        log.info( "\t\t currency1=" + currency1 );

                        IdcTransaction tx = IdcSessionManager.getInstance().newTransaction( ctx );
                        for ( Iterator currency2Iterator = currencies.iterator(); currency2Iterator.hasNext(); )
                        {
                            Currency currency2 = ( Currency ) currency2Iterator.next();
                            if ( !currency2.equals( currency1 ) )
                            {
                                log.info( "\t\t\t currency2=" + currency2 );

                                Request fXSingleLegRequest = this.createFXSingleLegRequest( organization, requestClassification, state, currency1, currency2 );
                                log.info( "\t\t\t\t fXSingleLegRequest=" + fXSingleLegRequest );
                                Request fXSwapRequest = this.createFXSwapRequest( organization, requestClassification, state, currency1, currency2 );
                                log.info( "\t\t\t\t fXSwapRequest=" + fXSwapRequest );
                            }
                        }
                        tx.commit();

                    }
                }
            }

            log.info( "tx complete" );
        }
        catch ( Exception e )
        {
            IdcSessionManager.getInstance().setTransaction( null );
            fail( "Exception in testCreateRequests : ", e );
        }
    }

    protected Request createFXSingleLegRequest( Organization organization, RequestClassification requestClassification, State state, Currency currency1, Currency currency2 )
    {
        FXSingleLeg fxSingleLeg = this.createFXSingleLeg( currency1, currency2 );
        WorkflowStateMap workflowStateMap = createWorkflowStateMap( state );
        Request request = this.createRequest( requestClassification, workflowStateMap, fxSingleLeg );
        this.createQuote( request, organization, workflowStateMap );
        return request;
    }

    protected Request createFXSwapRequest( Organization organization, RequestClassification requestClassification, State state, Currency currency1, Currency currency2 )
    {
        FXSwap fxSwap = this.createFXSwap( currency1, currency2 );
        WorkflowStateMap workflowStateMap = createWorkflowStateMap( state );
        Request request = this.createRequest( requestClassification, workflowStateMap, fxSwap );
        Quote quote = this.createQuote( request, organization, workflowStateMap );
        return request;
    }

    protected FXSingleLeg createFXSingleLeg( Currency currency1, Currency currency2 )
    {
        FXSingleLeg fxSingleLeg = FXFactory.newFXSingleLeg();
        FXSingleLeg registeredFxSingleLeg = ( FXSingleLeg ) fxSingleLeg.getRegisteredObject();
        FXLeg fxLeg = registeredFxSingleLeg.getFXLeg();
        FXPaymentParameters fxPayment = fxLeg.getFXPayment();
        fxPayment.setCurrency1( currency1 );
        fxPayment.setCurrency2( currency2 );
        return registeredFxSingleLeg;
    }

    protected FXSwap createFXSwap( Currency currency1, Currency currency2 )
    {
        FXSwap fxSwap = FXFactory.newFXSwap();
        FXSwap registeredFxSwap = ( FXSwap ) fxSwap.getRegisteredObject();
        FXLeg nearLeg = registeredFxSwap.getNearLeg();
        FXLeg farLeg = registeredFxSwap.getFarLeg();
        FXPaymentParameters nearPayment = nearLeg.getFXPayment();
        nearPayment.setCurrency1( currency1 );
        nearPayment.setCurrency2( currency2 );
        FXPaymentParameters farPayment = farLeg.getFXPayment();
        farPayment.setCurrency1( currency1 );
        farPayment.setCurrency2( currency2 );
        return registeredFxSwap;
    }

    protected Request createRequest( RequestClassification requestClassification, WorkflowStateMap workflowStateMap, Trade trade )
    {
        Request request = DealingFactory.newRequest();
        Request registeredRequest = ( Request ) request.getRegisteredObject();
        registeredRequest.setWorkflowStateMap( workflowStateMap );
        registeredRequest.setRequestClassification( requestClassification );
        registeredRequest.setTrade( trade );
        registeredRequest.setTransactionID( "Test=" + System.nanoTime() );
        return registeredRequest;
    }

    protected Quote createQuote( Request request, Organization organization, WorkflowStateMap workflowStateMap )
    {
        Quote quote = DealingFactory.newQuote();
        Quote registeredQuote = ( Quote ) quote.getRegisteredObject();
        registeredQuote.setWorkflowStateMap( workflowStateMap );
        registeredQuote.setRequest( request );
        request.setQuote( organization, registeredQuote );
        return registeredQuote;
    }

    protected WorkflowStateMap createWorkflowStateMap( State state )
    {
        WorkflowStateMap workflowStateMap = WorkflowFactory.newWorkflowStateMap();
        WorkflowStateMap registeredWorkflowStateMap = ( WorkflowStateMap ) workflowStateMap.getRegisteredObject();
        registeredWorkflowStateMap.setState( state );
        return registeredWorkflowStateMap;
    }

    /**
     * Test the request query as a regular user. This user is not allowed
     * to see all request records
     */
    public void testRequestQueryAsUser()
    {
        try
        {
            Session session = getPersistenceSession();

            ExpressionBuilder clsf1Eb = new ExpressionBuilder();
            Expression clsf1Expr = clsf1Eb.get( NamedEntity.ShortName ).equal( "PRICE" );
            RequestClassification clsf1 = ( RequestClassification ) session.readObject( RequestClassification.class, clsf1Expr );

            ExpressionBuilder clsf2Eb = new ExpressionBuilder();
            Expression clsf2Expr = clsf2Eb.get( NamedEntity.ShortName ).equal( "RFQ" );
            RequestClassification clsf2 = ( RequestClassification ) session.readObject( RequestClassification.class, clsf2Expr );

            ExpressionBuilder stateEb = new ExpressionBuilder();
            Expression stateExpr = stateEb.get( NamedEntity.ShortName ).equal( "Approved" );
            State state = ( State ) session.readObject( State.class, stateExpr );

            ExpressionBuilder ccy1Eb = new ExpressionBuilder();
            Expression ccy1Expr = ccy1Eb.get( NamedEntity.ShortName ).equal( "EUR" );
            Currency ccy1 = ( Currency ) session.readObject( Currency.class, ccy1Expr );

            ExpressionBuilder ccy2Eb = new ExpressionBuilder();
            Expression ccy2Expr = ccy2Eb.get( NamedEntity.ShortName ).equal( "USD" );
            Currency ccy2 = ( Currency ) session.readObject( Currency.class, ccy2Expr );

            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );

            NamedQuery requestQuery = new FXRequestQueryC();
            List classificationIds = new ArrayList();
            classificationIds.add( clsf1.getObjectID() );
            classificationIds.add( clsf2.getObjectID() );
            requestQuery.setParameter( FXRequestQueryC.CLASSIFICATION_IDS, classificationIds );
            requestQuery.setParameter( FXRequestQueryC.STATE_ID, state.getObjectID() );
            requestQuery.setParameter( FXRequestQueryC.CURRENCY1_ID, ccy1.getObjectID() );
            requestQuery.setParameter( FXRequestQueryC.CURRENCY2_ID, ccy2.getObjectID() );

            QueryService qs = QueryFactory.getQueryService();
            Vector res = ( Vector ) qs.findAll( requestQuery );
            log.info( "requestQuery res.size()=" + res.size() );
        }
        catch ( IdcRuntimeException ire )
        {
            printException( ire );
        }
        catch ( IdcException ie )
        {
            printException( ie );
        }
        catch ( Exception e )
        {
            fail( "unhandled exception when executing testSampleNamedQuery ", e );
        }
    }

    /**
     * Test the quote query as a regular user. This user is not allowed
     * to see all quote records
     */
    public void testQuoteQueryAsUser()
    {
        try
        {
            Session session = getPersistenceSession();

            ExpressionBuilder clsf1Eb = new ExpressionBuilder();
            Expression clsf1Expr = clsf1Eb.get( NamedEntity.ShortName ).equal( "PRICE" );
            RequestClassification clsf1 = ( RequestClassification ) session.readObject( RequestClassification.class, clsf1Expr );

            ExpressionBuilder clsf2Eb = new ExpressionBuilder();
            Expression clsf2Expr = clsf2Eb.get( NamedEntity.ShortName ).equal( "RFQ" );
            RequestClassification clsf2 = ( RequestClassification ) session.readObject( RequestClassification.class, clsf2Expr );

            ExpressionBuilder stateEb = new ExpressionBuilder();
            Expression stateExpr = stateEb.get( NamedEntity.ShortName ).equal( "Approved" );
            State state = ( State ) session.readObject( State.class, stateExpr );

            ExpressionBuilder ccy1Eb = new ExpressionBuilder();
            Expression ccy1Expr = ccy1Eb.get( NamedEntity.ShortName ).equal( "EUR" );
            Currency ccy1 = ( Currency ) session.readObject( Currency.class, ccy1Expr );

            ExpressionBuilder ccy2Eb = new ExpressionBuilder();
            Expression ccy2Expr = ccy2Eb.get( NamedEntity.ShortName ).equal( "USD" );
            Currency ccy2 = ( Currency ) session.readObject( Currency.class, ccy2Expr );

            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );

            NamedQuery quoteQuery = new FXQuoteQueryC();
            List classificationIds = new ArrayList();
            classificationIds.add( clsf1.getObjectID() );
            classificationIds.add( clsf2.getObjectID() );
            quoteQuery.setParameter( FXQuoteQueryC.REQUEST_CLASSIFICATION_IDS, classificationIds );
            quoteQuery.setParameter( FXQuoteQueryC.STATE_ID, state.getObjectID() );
            quoteQuery.setParameter( FXQuoteQueryC.CURRENCY1_ID, ccy1.getObjectID() );
            quoteQuery.setParameter( FXQuoteQueryC.CURRENCY2_ID, ccy2.getObjectID() );
            log.info( "quoteQuery=" + quoteQuery );

            QueryService qs = QueryFactory.getQueryService();
            Vector res = ( Vector ) qs.findAll( quoteQuery );
            log.info( "quoteQuery res.size()=" + res.size() );
        }
        catch ( IdcRuntimeException ire )
        {
            printException( ire );
        }
        catch ( IdcException ie )
        {
            printException( ie );
        }
        catch ( Exception e )
        {
            fail( "unhandled exception when executing quoteQuery ", e );
        }
    }

}
