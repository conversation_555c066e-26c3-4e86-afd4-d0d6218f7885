package com.integral.finance.dealing.calculator.test;

import com.integral.businessCenter.BusinessCenter;
import com.integral.businessCenter.BusinessCenterFactory;
import com.integral.finance.businessCalendar.BusinessCalendar;
import com.integral.finance.businessCalendar.BusinessCalendarFactory;
import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.dealing.calculator.GoodTillEndOfWeekC;
import com.integral.finance.dealing.calculator.GoodTillTimeEndOfWeekC;
import com.integral.persistence.NamedEntity;
import com.integral.scheduler.Expiration;
import com.integral.scheduler.ScheduleFactory;
import com.integral.test.PTestCaseC;
import com.integral.time.IdcDateTime;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;

import java.sql.Time;
import java.util.ArrayList;
import java.util.Collection;
import java.util.TimeZone;

public class GoodTillEOWPTestC
        extends PTestCaseC
{
    static String name = "Good Till End Of Week Test";

    public GoodTillEOWPTestC( String name )
    {
        super( name );
    }

    public void testGTEOWExpiration()
    {
        try
        {

            //Load business calendar
            Expression expr = new ExpressionBuilder().get( NamedEntity.ShortName ).equalsIgnoreCase( "NYC" );
            Session session = this.getPersistenceSession();

            HolidayCalendar hCal = ( HolidayCalendar ) session.readObject( HolidayCalendar.class, expr );
            Collection hCals = new ArrayList();
            hCals.add( hCal );

            BusinessCalendar bCal = BusinessCalendarFactory.newBusinessCalendar();
            bCal.setHolidayCalendars( hCals );

            //Get TimeZone for IST
            TimeZone tz = TimeZone.getTimeZone( "IST" );

            //create BC
            BusinessCenter bc = BusinessCenterFactory.newBusinessCenter();
            bc.setBusinessCalendar( bCal );
            bc.setTimeZone( tz );

            Time time = new Time( 3600L * 17 * 1000L );
            bc.setRollTime( time );

            Expiration exp = ScheduleFactory.newExpiration();

            exp.setExpiryTimeCalculator( new GoodTillEndOfWeekC() );
            exp.setBusinessCenter( bc );

            IdcDateTime expTime = exp.getExpiryTimeCalculator().calcExpirationTime( exp );

            System.out.println( "GTEOW in BC's TZ: " + expTime.getDateTime( tz ) );
            System.out.println( "GTEOW in PDT TZ: " + expTime.getDateTime( TimeZone.getTimeZone( "PDT" ) ) );


        }
        catch ( Exception e )
        {
            log.error( "Error occurred", e );
            fail( "Exception occurred" );
        }
    }

    public void testGTTEOWExpiration()
    {
        try
        {

            //Load business calendar
            Expression expr = new ExpressionBuilder().get( NamedEntity.ShortName ).equalsIgnoreCase( "NYC" );
            Session session = this.getPersistenceSession();

            HolidayCalendar hCal = ( HolidayCalendar ) session.readObject( HolidayCalendar.class, expr );
            Collection hCals = new ArrayList();
            hCals.add( hCal );

            BusinessCalendar bCal = BusinessCalendarFactory.newBusinessCalendar();
            bCal.setHolidayCalendars( hCals );

            //Get TimeZone for IST
            TimeZone tz = TimeZone.getTimeZone( "IST" );

            //create BC
            BusinessCenter bc = BusinessCenterFactory.newBusinessCenter();
            bc.setBusinessCalendar( bCal );
            bc.setTimeZone( tz );

            Time time = new Time( 3600L * 17 * 1000L );
            bc.setRollTime( time );

            Expiration exp = ScheduleFactory.newExpiration();

            exp.setExpiryTimeCalculator( new GoodTillTimeEndOfWeekC() );
            exp.setBusinessCenter( bc );
            //set the timer for 1 hours from now
            exp.setSeconds( 1 * 3600 );

            IdcDateTime expTime = exp.getExpiryTimeCalculator().calcExpirationTime( exp );

            System.out.println( "GTTEOW in BC's TZ: " + expTime.getDateTime( tz ) );
            System.out.println( "GTTEOW in PDT TZ: " + expTime.getDateTime( TimeZone.getTimeZone( "PDT" ) ) );


        }
        catch ( Exception e )
        {
            log.error( "Error occurred", e );
            fail( "Exception occurred" );
        }
    }
}
