package com.integral.finance.dealing.priceRegeneration.test;

import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfiguration;
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfigurationFactory;
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfigurationMBean;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class PriceRegenerationConfigurationMBeanTestC extends MBeanTestCaseC
{
    PriceRegenerationConfiguration priceRegenMBean = ( PriceRegenerationConfiguration ) PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean();

    public PriceRegenerationConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {

        testProperty( priceRegenMBean, "priceRegenerationFrequency", PriceRegenerationConfigurationMBean.PRICE_REGENERATION_FREQUENCY, MBeanTestCaseC.INTEGER );
        testProperty( priceRegenMBean, "priceRegenerationLogCategory", PriceRegenerationConfigurationMBean.PRICE_REGENERATION_LOG_CATEGORY, MBeanTestCaseC.STRING );
        testProperty( priceRegenMBean, "tierLimitRoundingFactor", PriceRegenerationConfigurationMBean.PRICE_REGENERATION_ROUNDING_FACTOR, MBeanTestCaseC.INTEGER );
        testProperty( priceRegenMBean, "acceptanceToleranceFactor", PriceRegenerationConfigurationMBean.PRICE_REGENERATION_ACCEPTANCE_ROUNDING_FACTOR, MBeanTestCaseC.INTEGER );
        testProperty( priceRegenMBean, "priceRegenerationEventMonitoringEnabled", PriceRegenerationConfigurationMBean.PRICE_REGENERATION_EVENT_MONITORING_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( priceRegenMBean, "priceRegenerationEventMonitoringInterval", PriceRegenerationConfigurationMBean.PRICE_REGENERATION_EVENT_MONITORING_INTERVAL, MBeanTestCaseC.INTEGER );
        testProperty( priceRegenMBean, "acceptedQuoteListSize", PriceRegenerationConfigurationMBean.PRICE_REGENERATION_EVENT_ACCEPTED_QUOTE_LIST_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( priceRegenMBean, "tierLiquidityValidationEnabled", PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, MBeanTestCaseC.BOOLEAN );
    }
}
