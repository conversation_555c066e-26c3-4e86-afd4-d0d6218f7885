package com.integral.finance.dealing.priceRegeneration.test;

import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationFactory;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationParameters;
import com.integral.test.PTestCaseC;
import com.integral.user.OrganizationC;
import com.integral.user.UserC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


/**
 * Tests fees and fee schedules
 */
public class PriceRegenerationPTestC
        extends PTestCaseC
{
    static String name = "Price Regen Test";

    public PriceRegenerationPTestC( String name )
    {
        super( name );
    }

    public void testInsertPriceRegenerationParameters()
    {
        try
        {
            Session session = getPersistenceSession();

            UnitOfWork uow = session.acquireUnitOfWork();
            PriceRegenerationParameters priceRegenerationParam = PriceRegenerationFactory.newPriceRegenerationParameters();
            priceRegenerationParam.setStatus( 'T' );
            priceRegenerationParam.setAmount( 99.9 );
            priceRegenerationParam.setFrequency( 1 );
            priceRegenerationParam.setInterval( 2 );
            priceRegenerationParam.setLiquidityModel( 3 );
            priceRegenerationParam.setRegenerationModel( 4 );
            priceRegenerationParam.setType( 5 );

            uow.registerObject( priceRegenerationParam );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insert PriceRegenerationParameters" );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( UserC.class );
            uow.addReadOnlyClass( OrganizationC.class );
            PriceRegenerationParameters priceRegenerationParam = PriceRegenerationFactory.newPriceRegenerationParameters();
            assertFalse( priceRegenerationParam.isRapidFireEnabled() );
            assertFalse( priceRegenerationParam.isAllowOnlyOneMatchPerQuote() );
            PriceRegenerationParameters registeredPrcRegenParam = ( PriceRegenerationParameters ) uow.registerObject( priceRegenerationParam );
            registeredPrcRegenParam.setStatus( 'T' );
            registeredPrcRegenParam.setAmount( 99.9 );
            registeredPrcRegenParam.setFrequency( 1 );
            registeredPrcRegenParam.setInterval( 2 );
            registeredPrcRegenParam.setLiquidityModel( 3 );
            registeredPrcRegenParam.setRegenerationModel( 4 );
            registeredPrcRegenParam.setType( 5 );
            registeredPrcRegenParam.setRapidFireEnabled( true );
            registeredPrcRegenParam.setAllowOnlyOneMatchPerQuote( true );
            uow.commit();


            // refresh the trade and retrieve the maker request.
            priceRegenerationParam = ( PriceRegenerationParameters ) session.refreshObject( priceRegenerationParam );
            assertTrue( priceRegenerationParam.isAllowOnlyOneMatchPerQuote() );
            assertTrue( priceRegenerationParam.isRapidFireEnabled() );
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }


    public void testPriceRegenerationParametersLookup()
    {
        try
        {
            Session session = getPersistenceSession();
            Vector PriceRegenerationParams = session.readAllObjects( PriceRegenerationParameters.class );
            for ( int i = 0; i < PriceRegenerationParams.size(); i++ )
            {
                PriceRegenerationParameters prcRegenParam = ( PriceRegenerationParameters ) PriceRegenerationParams.elementAt( i );
                printPriceRegenerationParameters( prcRegenParam, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookup PriceRegeneration" );
        }
    }


    private void printPriceRegenerationParameters( PriceRegenerationParameters prcRegenParam, int i )
    {
        log.info( "PriceRegenerationParameters #" + i + ": " + prcRegenParam );
        log.info( "\tamount:             " + prcRegenParam.getAmount() );
        log.info( "\tfrequency:          " + prcRegenParam.getFrequency() );
        log.info( "\tinterval:           " + prcRegenParam.getInterval() );
        log.info( "\tliquidity model:    " + prcRegenParam.getLiquidityModel() );
        log.info( "\tregeneration model: " + prcRegenParam.getRegenerationModel() );
        log.info( "\ttype:               " + prcRegenParam.getType() );
    }

}
