package com.integral.finance.dealing.priceRegeneration.test;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

import com.integral.facade.FacadeFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.priceRegeneration.*;
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfigurationFactory;
import com.integral.finance.dealing.priceRegeneration.fx.PriceRegenerationEventCacheC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.facade.TradeStateFacadeC;
import com.integral.math.MathUtil;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.workflow.WorkflowFactory;
import com.integral.workflow.WorkflowStateMap;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Tests the price regeneration service.
 *
 * <AUTHOR> Development Corp.
 */
public class PriceRegenerationServicePTestC
        extends PTestCaseC
{
    static String name = "Price Regen Service Test";
    static final String BID = "BID";
    static final String OFFER = "OFFER";

    static final String EURUSD = "EUR/USD";
    static final String EURGBP = "EUR/GBP";
    static final int TIER_COUNT = 3;

    protected String[] currencyPairs = new String[]{"EUR/CHF", "EUR/CAD", "EUR/AUD", "EUR/NZD", "EUR/JPY", "EUR/NOK", "EUR/SEK", "USD/JPY", "USD/CHF", "USD/CAD",
            "USD/NOK", "USD/SEK", "AUD/USD", "USD/NZD", "GBP/JPY", "GBP/CHF", "GBP/NOK", "GBP/SEK", "GBP/AUD", "GBP/NZD",
            "GBP/USD", "JPY/CAD", "JPY/CHF", "JPY/NOK", "JPY/SEK", "CHF/CAD", "CHF/SEK", "CHF/NZD"};

    protected double acceptanceToleranceAmount = MathUtil.pow10( PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean().getAcceptanceToleranceFactor() );


    protected int noRegen = PriceRegenerationParameters.NO_REGENERATION;
    protected int replenish = PriceRegenerationParameters.REGENERATION_WITH_REPLENISHMENT;
    protected int noReplenish = PriceRegenerationParameters.REGENERATION_WITHOUT_REPLENISHMENT;
    protected int lowest = PriceRegenerationParameters.LOWEST_TO_HIGHEST_REGENERATION;
    protected int highest = PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION;
    protected int netting = PriceRegenerationParameters.COMBINED_LIQUIDITY_WITH_NETTING;
    protected int noNetting = PriceRegenerationParameters.COMBINED_LIQUIDITY_NO_NETTING;
    protected int indepedent = PriceRegenerationParameters.INDEPENDENT_LIQUIDITY;

    protected PriceRegenerationService svc = new PriceRegenerationServiceC();
    protected TestRegenerationHandler handler = new TestRegenerationHandler();

    protected PriceRegenerationParameters noPrcRegen = null;

    protected PriceRegenerationParameters noReplenishLowestNoNettingAmt = null;
    protected PriceRegenerationParameters noReplenishLowestNoNettingInterval = null;
    protected PriceRegenerationParameters noReplenishLowestNettingAmt = null;
    protected PriceRegenerationParameters noReplenishLowestNettingInterval = null;
    protected PriceRegenerationParameters noReplenishLowestIndependentAmt = null;
    protected PriceRegenerationParameters noReplenishLowestIndependentInterval = null;

    protected PriceRegenerationParameters noReplenishHighestNoNettingAmt = null;
    protected PriceRegenerationParameters noReplenishHighestNoNettingInterval = null;
    protected PriceRegenerationParameters noReplenishHighestNettingAmt = null;
    protected PriceRegenerationParameters noReplenishHighestNettingInterval = null;
    protected PriceRegenerationParameters noReplenishHighestIndependentAmt = null;
    protected PriceRegenerationParameters noReplenishHighestIndependentInterval = null;

    protected PriceRegenerationParameters replenishLowestNoNettingAmt = null;
    protected PriceRegenerationParameters replenishLowestNoNettingInterval = null;
    protected PriceRegenerationParameters replenishLowestNettingAmt = null;
    protected PriceRegenerationParameters replenishLowestNettingInterval = null;
    protected PriceRegenerationParameters replenishLowestIndependentAmt = null;
    protected PriceRegenerationParameters replenishLowestIndependentInterval = null;

    protected PriceRegenerationParameters replenishHighestNoNettingAmt = null;
    protected PriceRegenerationParameters replenishHighestNoNettingInterval = null;
    protected PriceRegenerationParameters replenishHighestNettingAmt = null;
    protected PriceRegenerationParameters replenishHighestNettingInterval = null;
    protected PriceRegenerationParameters replenishHighestIndependentAmt = null;
    protected PriceRegenerationParameters replenishHighestIndependentInterval = null;

    protected Set<PriceRegenerationParameters> regenParams = null;
    protected Set<PriceRegenerationParameters> replenishRegenParams = null;
    protected Set<PriceRegenerationParameters> noReplenishRegenParams = null;
    protected List<PriceRegenerationParameters> nettingRegenParams = null;
    protected List<PriceRegenerationParameters> noNettingRegenParams = null;
    protected List<PriceRegenerationParameters> independentRegenParams = null;
    protected List<PriceRegenerationParameters> nettingReplenishParams = null;
    protected List<PriceRegenerationParameters> noNettingReplenishParams = null;
    protected List<PriceRegenerationParameters> independentReplenishParams = null;
    protected List<PriceRegenerationParameters> nettingNoReplenishParams = null;
    protected List<PriceRegenerationParameters> noNettingNoReplenishParams = null;
    protected List<PriceRegenerationParameters> independentNoReplenishParams = null;
    protected List<PriceRegenerationParameters> lowestReplenishParams = null;
    protected List<PriceRegenerationParameters> lowestNoReplenishParams = null;
    protected List<PriceRegenerationParameters> highestReplenishParams = null;
    protected List<PriceRegenerationParameters> highestNoReplenishParams = null;
    protected List<PriceRegenerationParameters> lowestParams = null;
    protected List<PriceRegenerationParameters> highestParams = null;
    protected List<PriceRegenerationParameters> fixedAmountReplenishParams = null;
    protected List<PriceRegenerationParameters> fixedTimeReplenishParams = null;

    protected TradingParty org1Tp1 = null;
    protected TradingParty org1Tp2 = null;
    protected TradingParty org2Tp1 = null;
    protected TradingParty org2Tp2 = null;

    protected LegalEntity org1Le1 = null;
    protected LegalEntity org1Le2 = null;
    protected LegalEntity org2Le1 = null;
    protected LegalEntity org2Le2 = null;

    protected Organization org1 = null;
    protected Organization org2 = null;

    protected int interval = 10;
    protected int amount = 2000000;
    protected int frequency = 2;
    protected int numberOfTiers = TIER_COUNT;

    public PriceRegenerationServicePTestC( String name )
    {
        super( name );
        org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );

        org1Le1 = new LegalEntityC();
        org1Le1.setShortName( "Test1Le1" );
        org1Le1.setOrganization( org1 );

        org1Le2 = new LegalEntityC();
        org1Le2.setShortName( "Test1Le2" );
        org1Le2.setOrganization( org1 );

        org2Le1 = new LegalEntityC();
        org2Le1.setShortName( "Test2Le1" );
        org2Le1.setOrganization( org2 );

        org2Le2 = new LegalEntityC();
        org2Le2.setShortName( "Test2Le2" );
        org2Le2.setOrganization( org2 );

        org1Tp1 = new TradingPartyC();
        org1Tp1.setShortName( "Test1Tp1" );
        org1Tp1.setOrganization( org1 );
        org1Tp1.setLegalEntity( org1Le1 );

        org1Tp2 = new TradingPartyC();
        org1Tp2.setShortName( "Test1Tp2" );
        org1Tp2.setOrganization( org1 );
        org1Tp2.setLegalEntity( org1Le2 );

        org2Tp1 = new TradingPartyC();
        org2Tp1.setShortName( "Test2Tp1" );
        org2Tp1.setOrganization( org2 );
        org2Tp1.setLegalEntity( org2Le1 );

        org2Tp2 = new TradingPartyC();
        org2Tp2.setShortName( "Test2Tp2" );
        org2Tp2.setOrganization( org2 );
        org2Tp2.setLegalEntity( org2Le2 );

        FacadeFactory.setFacade( PriceRegenerationParametersFacade.FACADE_NAME, PriceRegenerationParameters.class, PriceRegenerationParametersFacadeC.class );
        FacadeFactory.setFacade( TradeStateFacade.TRADE_STATE_FACADE, Trade.class, TradeStateFacadeC.class );


        // set the default price regenerations.
        noPrcRegen = preparePriceRegenerationParameters( "noPrcRegen", noRegen, lowest, noNetting, interval, frequency, 0 );

        noReplenishLowestNoNettingAmt = preparePriceRegenerationParameters( "noReplenishLowestNoNettingAmt", noReplenish, lowest, noNetting, 0, frequency, amount );
        noReplenishLowestNoNettingInterval = preparePriceRegenerationParameters( "noReplenishLowestNoNettingInterval", noReplenish, lowest, noNetting, interval, 0, 0 );
        noReplenishLowestNettingAmt = preparePriceRegenerationParameters( "noReplenishLowestNettingAmt", noReplenish, lowest, netting, 0, frequency, amount );
        noReplenishLowestNettingInterval = preparePriceRegenerationParameters( "noReplenishLowestNettingInterval", noReplenish, lowest, netting, interval, 0, 0 );
        noReplenishLowestIndependentAmt = preparePriceRegenerationParameters( "noReplenishLowestIndependentAmt", noReplenish, lowest, indepedent, 0, frequency, amount );
        noReplenishLowestIndependentInterval = preparePriceRegenerationParameters( "noReplenishLowestIndependentInterval", noReplenish, lowest, indepedent, interval, 0, 0 );

        noReplenishHighestNoNettingAmt = preparePriceRegenerationParameters( "noReplenishHighestNoNettingAmt", noReplenish, highest, noNetting, 0, frequency, amount );
        noReplenishHighestNoNettingInterval = preparePriceRegenerationParameters( "noReplenishHighestNoNettingInterval", noReplenish, highest, noNetting, interval, 0, 0 );
        noReplenishHighestNettingAmt = preparePriceRegenerationParameters( "noReplenishHighestNettingAmt", noReplenish, highest, netting, 0, frequency, amount );
        noReplenishHighestNettingInterval = preparePriceRegenerationParameters( "noReplenishHighestNettingInterval", noReplenish, highest, netting, interval, 0, 0 );
        noReplenishHighestIndependentAmt = preparePriceRegenerationParameters( "noReplenishHighestIndependentAmt", noReplenish, highest, indepedent, 0, frequency, amount );
        noReplenishHighestIndependentInterval = preparePriceRegenerationParameters( "noReplenishHighestIndependentInterval", noReplenish, highest, indepedent, interval, 0, 0 );

        replenishLowestNoNettingAmt = preparePriceRegenerationParameters( "replenishLowestNoNettingAmt", replenish, lowest, noNetting, 0, frequency, amount );
        replenishLowestNoNettingInterval = preparePriceRegenerationParameters( "replenishLowestNoNettingInterval", replenish, lowest, noNetting, interval, 0, 0 );
        replenishLowestNettingAmt = preparePriceRegenerationParameters( "replenishLowestNettingAmt", replenish, lowest, netting, 0, frequency, amount );
        replenishLowestNettingInterval = preparePriceRegenerationParameters( "replenishLowestNettingInterval", replenish, lowest, netting, interval, 0, 0 );
        replenishLowestIndependentAmt = preparePriceRegenerationParameters( "replenishLowestIndependentAmt", replenish, lowest, indepedent, 0, frequency, amount );
        replenishLowestIndependentInterval = preparePriceRegenerationParameters( "replenishLowestIndependentInterval", replenish, lowest, indepedent, interval, 0, 0 );

        replenishHighestNoNettingAmt = preparePriceRegenerationParameters( "replenishHighestNoNettingAmt", replenish, highest, noNetting, 0, frequency, amount );
        replenishHighestNoNettingInterval = preparePriceRegenerationParameters( "replenishHighestNoNettingInterval", replenish, highest, noNetting, interval, 0, 0 );
        replenishHighestNettingAmt = preparePriceRegenerationParameters( "replenishHighestNettingAmt", replenish, highest, netting, 0, frequency, amount );
        replenishHighestNettingInterval = preparePriceRegenerationParameters( "replenishHighestNettingInterval", replenish, highest, netting, interval, 0, 0 );
        replenishHighestIndependentAmt = preparePriceRegenerationParameters( "replenishHighestIndependentAmt", replenish, highest, indepedent, 0, frequency, amount );
        replenishHighestIndependentInterval = preparePriceRegenerationParameters( "replenishHighestIndependentInterval", replenish, highest, indepedent, interval, 0, 0 );

        // add regen parameters to the list
        regenParams = new HashSet<PriceRegenerationParameters>( 20 );
        replenishRegenParams = new HashSet<PriceRegenerationParameters>( 12 );
        noReplenishRegenParams = new HashSet<PriceRegenerationParameters>( 12 );
        nettingRegenParams = new ArrayList<PriceRegenerationParameters>( 8 );
        noNettingRegenParams = new ArrayList<PriceRegenerationParameters>( 8 );
        independentRegenParams = new ArrayList<PriceRegenerationParameters>( 8 );
        nettingReplenishParams = new ArrayList<PriceRegenerationParameters>( 4 );
        noNettingReplenishParams = new ArrayList<PriceRegenerationParameters>( 4 );
        independentReplenishParams = new ArrayList<PriceRegenerationParameters>( 4 );
        nettingNoReplenishParams = new ArrayList<PriceRegenerationParameters>( 4 );
        noNettingNoReplenishParams = new ArrayList<PriceRegenerationParameters>( 4 );
        independentNoReplenishParams = new ArrayList<PriceRegenerationParameters>( 4 );
        lowestReplenishParams = new ArrayList<PriceRegenerationParameters>( 6 );
        lowestNoReplenishParams = new ArrayList<PriceRegenerationParameters>( 6 );
        highestReplenishParams = new ArrayList<PriceRegenerationParameters>( 6 );
        highestNoReplenishParams = new ArrayList<PriceRegenerationParameters>( 6 );
        lowestParams = new ArrayList<PriceRegenerationParameters>( 12 );
        highestParams = new ArrayList<PriceRegenerationParameters>( 12 );
        fixedAmountReplenishParams = new ArrayList<PriceRegenerationParameters>( 6 );
        fixedTimeReplenishParams = new ArrayList<PriceRegenerationParameters>( 6 );

        fixedAmountReplenishParams.add( replenishLowestNettingAmt );
        fixedAmountReplenishParams.add( replenishLowestNoNettingAmt );
        fixedAmountReplenishParams.add( replenishLowestIndependentAmt );
        fixedAmountReplenishParams.add( replenishHighestNettingAmt );
        fixedAmountReplenishParams.add( replenishHighestNoNettingAmt );
        fixedAmountReplenishParams.add( replenishHighestIndependentAmt );

        fixedTimeReplenishParams.add( replenishLowestNettingInterval );
        fixedTimeReplenishParams.add( replenishLowestNoNettingInterval );
        fixedTimeReplenishParams.add( replenishLowestIndependentInterval );
        fixedTimeReplenishParams.add( replenishHighestNettingInterval );
        fixedTimeReplenishParams.add( replenishHighestNoNettingInterval );
        fixedTimeReplenishParams.add( replenishHighestIndependentInterval );

        lowestReplenishParams.add( replenishLowestNettingAmt );
        lowestReplenishParams.add( replenishLowestNettingInterval );
        lowestReplenishParams.add( replenishLowestNoNettingAmt );
        lowestReplenishParams.add( replenishLowestNoNettingInterval );
        lowestReplenishParams.add( replenishLowestIndependentAmt );
        lowestReplenishParams.add( replenishLowestIndependentInterval );

        lowestNoReplenishParams.add( noReplenishLowestNettingAmt );
        lowestNoReplenishParams.add( noReplenishLowestNettingInterval );
        lowestNoReplenishParams.add( noReplenishLowestNoNettingAmt );
        lowestNoReplenishParams.add( noReplenishLowestNoNettingInterval );
        lowestNoReplenishParams.add( noReplenishLowestIndependentAmt );
        lowestNoReplenishParams.add( noReplenishLowestIndependentInterval );

        highestReplenishParams.add( replenishHighestNettingAmt );
        highestReplenishParams.add( replenishHighestNettingInterval );
        highestReplenishParams.add( replenishHighestNoNettingAmt );
        highestReplenishParams.add( replenishHighestNoNettingInterval );
        highestReplenishParams.add( replenishHighestIndependentAmt );
        highestReplenishParams.add( replenishHighestIndependentInterval );

        highestNoReplenishParams.add( noReplenishHighestNettingAmt );
        highestNoReplenishParams.add( noReplenishHighestNettingInterval );
        highestNoReplenishParams.add( noReplenishHighestNoNettingAmt );
        highestNoReplenishParams.add( noReplenishHighestNoNettingInterval );
        highestNoReplenishParams.add( noReplenishHighestIndependentAmt );
        highestNoReplenishParams.add( noReplenishHighestIndependentInterval );

        lowestParams.addAll( lowestReplenishParams );
        lowestParams.addAll( lowestNoReplenishParams );
        highestParams.addAll( highestReplenishParams );
        highestParams.addAll( highestNoReplenishParams );

        noReplenishRegenParams.add( noReplenishLowestNoNettingAmt );
        noReplenishRegenParams.add( noReplenishLowestNoNettingInterval );
        noReplenishRegenParams.add( noReplenishLowestNettingAmt );
        noReplenishRegenParams.add( noReplenishLowestNettingInterval );
        noReplenishRegenParams.add( noReplenishLowestIndependentAmt );
        noReplenishRegenParams.add( noReplenishLowestIndependentInterval );
        noReplenishRegenParams.add( noReplenishHighestNoNettingAmt );
        noReplenishRegenParams.add( noReplenishHighestNoNettingInterval );
        noReplenishRegenParams.add( noReplenishHighestNettingAmt );
        noReplenishRegenParams.add( noReplenishHighestNettingInterval );
        noReplenishRegenParams.add( noReplenishHighestIndependentAmt );
        noReplenishRegenParams.add( noReplenishHighestIndependentInterval );

        replenishRegenParams.add( replenishLowestNoNettingAmt );
        replenishRegenParams.add( replenishLowestNoNettingInterval );
        replenishRegenParams.add( replenishLowestNettingAmt );
        replenishRegenParams.add( replenishLowestNettingInterval );
        replenishRegenParams.add( replenishLowestIndependentAmt );
        replenishRegenParams.add( replenishLowestIndependentInterval );
        replenishRegenParams.add( replenishHighestNoNettingAmt );
        replenishRegenParams.add( replenishHighestNoNettingInterval );
        replenishRegenParams.add( replenishHighestNettingAmt );
        replenishRegenParams.add( replenishHighestNettingInterval );
        replenishRegenParams.add( replenishHighestIndependentAmt );
        replenishRegenParams.add( replenishHighestIndependentInterval );

        regenParams.add( noPrcRegen );
        regenParams.addAll( noReplenishRegenParams );
        regenParams.addAll( replenishRegenParams );

        // populate the liquidity model segregated sets.
        nettingReplenishParams.add( replenishLowestNettingAmt );
        nettingReplenishParams.add( replenishLowestNettingInterval );
        nettingReplenishParams.add( replenishHighestNettingAmt );
        nettingReplenishParams.add( replenishHighestNettingInterval );

        noNettingReplenishParams.add( replenishLowestNoNettingAmt );
        noNettingReplenishParams.add( replenishLowestNoNettingInterval );
        noNettingReplenishParams.add( replenishHighestNoNettingAmt );
        noNettingReplenishParams.add( replenishHighestNoNettingInterval );

        independentReplenishParams.add( replenishLowestIndependentAmt );
        independentReplenishParams.add( replenishLowestIndependentInterval );
        independentReplenishParams.add( replenishHighestIndependentAmt );
        independentReplenishParams.add( replenishHighestIndependentInterval );

        nettingNoReplenishParams.add( noReplenishLowestNettingAmt );
        nettingNoReplenishParams.add( noReplenishLowestNettingInterval );
        nettingNoReplenishParams.add( noReplenishHighestNettingAmt );
        nettingNoReplenishParams.add( noReplenishHighestNettingInterval );

        noNettingNoReplenishParams.add( noReplenishLowestNoNettingAmt );
        noNettingNoReplenishParams.add( noReplenishLowestNoNettingInterval );
        noNettingNoReplenishParams.add( noReplenishHighestNoNettingAmt );
        noNettingNoReplenishParams.add( noReplenishHighestNoNettingInterval );

        independentNoReplenishParams.add( noReplenishLowestIndependentAmt );
        independentNoReplenishParams.add( noReplenishLowestIndependentInterval );
        independentNoReplenishParams.add( noReplenishHighestIndependentAmt );
        independentNoReplenishParams.add( noReplenishHighestIndependentInterval );


        nettingRegenParams.addAll( nettingReplenishParams );
        nettingRegenParams.addAll( nettingNoReplenishParams );

        noNettingRegenParams.addAll( noNettingReplenishParams );
        noNettingRegenParams.addAll( noNettingNoReplenishParams );

        independentRegenParams.addAll( independentReplenishParams );
        independentRegenParams.addAll( independentNoReplenishParams );
    }

    protected Quote prepareQuote( Organization org, String ccyPair, double bidTier1Limit, double offerTier1Limit )
    {
        Quote quote = DealingFactory.newQuote();
        quote.setDisplayKey( ccyPair );
        quote.setOrganization( org );
        Collection<FXLegDealingPrice> col = new ArrayList<FXLegDealingPrice>( 10 );
        for ( int i = 0; i < numberOfTiers; i++ )
        {
            int multiplier = isMultiTier() ? i * 3 != 0 ? i * 3 : 1 : i + 1;
            double bidTierLimit = isMultiTier() ? bidTier1Limit * multiplier : bidTier1Limit * multiplier;
            FXLegDealingPrice bidDp = prepareQuoteDealingPrice( true, i, bidTierLimit, ccyPair );
            bidDp.setQuote( quote );
            bidDp.setGroupIndex( 0 );
            bidDp.setTier( i );
            col.add( bidDp );

            double offerTierLimit = isMultiTier() ? offerTier1Limit * multiplier : offerTier1Limit * multiplier;
            FXLegDealingPrice offerDp = prepareQuoteDealingPrice( false, i, offerTierLimit, ccyPair );
            offerDp.setQuote( quote );
            offerDp.setGroupIndex( 1 );
            offerDp.setTier( i );
            col.add( offerDp );
        }
        quote.setQuotePrices( col );
        return quote;
    }

    protected double[][] prepareQuoteLimits( double bidTier1Limit, double offerTier1Limit )
    {
        double[] bidLimits = new double[numberOfTiers];
        double[] offerLimits = new double[numberOfTiers];

        for ( int i = 0; i < numberOfTiers; i++ )
        {
            int multiplier = isMultiTier() ? i * 3 != 0 ? i * 3 : 1 : i + 1;
            double bidTierLimit = isMultiTier() ? bidTier1Limit * multiplier : bidTier1Limit * multiplier;
            bidLimits[i] = bidTierLimit;

            double offerTierLimit = isMultiTier() ? offerTier1Limit * multiplier : offerTier1Limit * multiplier;
            offerLimits[i] = offerTierLimit;
        }

        double[][] limits = new double[2][];
        limits[0] = bidLimits;
        limits[1] = offerLimits;
        return limits;
    }

    protected double[][] prepareQuoteLimits( double[] bidTier1Limits, double[] offerTier1Limits )
    {
        double[][] limits = new double[2][];
        limits[0] = bidTier1Limits;
        limits[1] = offerTier1Limits;
        return limits;
    }


    protected FXLegDealingPrice prepareQuoteDealingPrice( boolean isBid, int tier, double amt, String ccyPair )
    {
        FXLegDealingPrice dp = FXDealingFactory.newFXLegDealingPrice();
        FXDealingPriceElement fxDpe = FXDealingFactory.newFXDealingPriceElement();
        FXPrice fxPrc = FXPriceFactory.newFXPrice();
        fxPrc.setBidFXRate( prepareFXRate( ccyPair ) );
        fxPrc.setOfferFXRate( prepareFXRate( ccyPair ) );
        fxDpe.setPrice( fxPrc );
        dp.setPriceElement( fxDpe );
        if ( tier == 0 )
        {
            dp.setName( isBid ? BID : OFFER );
        }
        else
        {
            dp.setName( ( isBid ? BID : OFFER ) + tier );
        }
        dp.setDealtAmount( amt );
        return dp;
    }

    protected PriceRegenerationParameters preparePriceRegenerationParameters( String key, int type, int regenModel, int liqModel, int interval, int frequency, double amt )
    {
        PriceRegenerationParameters priceRegenParam = PriceRegenerationFactory.newPriceRegenerationParameters();
        priceRegenParam.setAmount( amt );
        priceRegenParam.setInterval( interval );
        priceRegenParam.setFrequency( frequency );
        priceRegenParam.setLiquidityModel( liqModel );
        priceRegenParam.setRegenerationModel( regenModel );
        priceRegenParam.setType( type );
        priceRegenParam.setDisplayKey( key );
        return priceRegenParam;
    }

    protected FXSingleLeg prepareTrade( double amt, boolean isBid, String ccyPair, Organization org, TradingParty tp )
    {
        FXSingleLeg singleLeg = FXFactory.newFXSingleLeg();
        singleLeg.setOrganization( org );
        singleLeg.setCounterpartyB( tp );
        singleLeg.setCounterpartyA( tp );
        FXLeg fxLeg = singleLeg.getFXLeg();
        FXPaymentParameters fxPmt = FXFactory.newFXPaymentParameters();
        fxPmt.setCurrency1Amount( amt );
        fxPmt.setBuyingCurrency1( !isBid );
        fxLeg.setFXPayment( fxPmt );
        fxPmt.setFXRate( prepareFXRate( ccyPair ) );
        fxPmt.setCurrency1( fxPmt.getFXRate().getBaseCurrency() );
        fxPmt.setCurrency2( fxPmt.getFXRate().getVariableCurrency() );
        WorkflowStateMap wm = WorkflowFactory.newWorkflowStateMap();
        singleLeg.setWorkflowStateMap( wm );
        return singleLeg;
    }

    protected FXSingleLeg prepareTrade( double amt, boolean isBid, String ccyPair, Organization org, TradingParty tp, boolean isDealtCcy1, double acceptedRate )
    {
       FXSingleLeg singleLeg = FXFactory.newFXSingleLeg();
       singleLeg.setOrganization( org );
       singleLeg.setCounterpartyB( tp );
       singleLeg.setCounterpartyA( tp );
       FXLeg fxLeg = singleLeg.getFXLeg();
       FXPaymentParameters fxPmt = FXFactory.newFXPaymentParameters();
       fxPmt.setCurrency1Amount( amt );
       fxPmt.setBuyingCurrency1(!isBid);
       fxLeg.setFXPayment(fxPmt);
       fxPmt.setFXRate(prepareFXRate(ccyPair));
       fxPmt.setCurrency1(isDealtCcy1 ? fxPmt.getFXRate().getBaseCurrency() : fxPmt.getFXRate().getVariableCurrency());
       fxPmt.setCurrency2(isDealtCcy1 ? fxPmt.getFXRate().getVariableCurrency() : fxPmt.getFXRate().getBaseCurrency());
       fxPmt.getFXRate().setRate(acceptedRate);
       fxPmt.setDealtCurrency1(isDealtCcy1);
       WorkflowStateMap wm = WorkflowFactory.newWorkflowStateMap();
       singleLeg.setWorkflowStateMap( wm );
       return singleLeg;
    }

    protected FXRate prepareFXRate( String ccyPair )
    {
        FXRate fxRate = FXFactory.newFXRate();
        CurrencyPair currencyPair = prepareCurrencyPair(ccyPair);
        fxRate.setCurrencyPair( currencyPair );
        return fxRate;
    }

    protected CurrencyPair prepareCurrencyPair(String ccyPair) {
        Currency baseCcy = CurrencyFactory.getCurrency(ccyPair.substring(0, ccyPair.indexOf('/')));
        Currency varCcy = CurrencyFactory.getCurrency( ccyPair.substring( ccyPair.indexOf( '/' ) + 1 ) );
        return CurrencyFactory.newCurrenyPair( baseCcy, varCcy );
    }

    protected void sleepFor( long interval )
    {
        try
        {
            log( "Sleeping for " + interval );
            Thread.sleep( interval );
            log( "Finished Sleeping for " + interval );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    protected PriceRegenerationParametersFacade getPriceRegenerationParametersFacade( PriceRegenerationParameters prcRegenParam )
    {
        return ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
    }

    protected void init()
    {
        Collection<PriceRegenerationEvent> allEvents = PriceRegenerationEventCacheC.getInstance().getAllPriceRegenerationEvents();
        if ( allEvents != null && !allEvents.isEmpty() )
        {
            for ( PriceRegenerationEvent event : allEvents )
            {
                PriceRegenerationTimerTask bidTimer = event.getBidPriceRegenerationTimerTask();
                if ( bidTimer != null )
                {
                    bidTimer.cancel();
                }
                PriceRegenerationTimerTask offerTimer = event.getOfferPriceRegenerationTimerTask();
                if ( offerTimer != null )
                {
                    offerTimer.cancel();
                }
                PriceRegenerationTimerTask rapidFireTimer = event.getRapidFireTimerTask();
                if ( rapidFireTimer != null )
                {
                    rapidFireTimer.cancel();
                }
            }
        }
        PriceRegenerationEventCacheC.getInstance().removeAllEvents();
    }

    protected void setNumberOfTiers( int tiers )
    {
        this.numberOfTiers = tiers;
    }

    protected boolean isMultiTier()
    {
        return false;
    }

    protected boolean checkQuoteAfterApplyingRegeneratedLimits( Quote quote, PriceRegeneration prcRegen )
    {
        boolean result = true;
        Collection quotePrices = quote.getQuotePrices();
        int tier;
        double bidQuoteLimit = 0.0;
        double totalBidQuoteLimit = 0.0;
        double offerQuoteLimit = 0.0;
        double totalOfferQuoteLimit = 0.0;
        for ( int index = 0; index < quotePrices.size(); index++ )
        {
            FXLegDealingPrice fxDp = ( FXLegDealingPrice ) quotePrices.toArray()[index];
            boolean isBid = fxDp.getGroupIndex() == 0;
            tier = getTier( fxDp, isBid );
            double tierLimit = isBid ? prcRegen.getBidTierLimit( tier ) : prcRegen.getOfferTierLimit( tier );
            if ( isBid )
            {
                bidQuoteLimit = fxDp.getDealtAmount();
                totalBidQuoteLimit += bidQuoteLimit;
            }
            else
            {
                offerQuoteLimit = fxDp.getDealtAmount();
                totalOfferQuoteLimit += offerQuoteLimit;
            }

            if ( Math.abs( tierLimit - fxDp.getDealtAmount() ) > PriceRegenerationConstants.MINIMUM )
            {
                result = false;
            }
        }
        // also check quote is of multi tier structure.
        if ( isMultiTier() )
        {
            if ( Math.abs( prcRegen.getBidLimit() - bidQuoteLimit ) > PriceRegenerationConstants.MINIMUM || Math.abs( prcRegen.getOfferLimit() - offerQuoteLimit ) > PriceRegenerationConstants.MINIMUM )
            {
                result = false;
            }
        }
        else
        {
            if ( Math.abs( prcRegen.getBidLimit() - totalBidQuoteLimit ) > PriceRegenerationConstants.MINIMUM || Math.abs( prcRegen.getOfferLimit() - totalOfferQuoteLimit ) > PriceRegenerationConstants.MINIMUM )
            {
                result = false;
            }
        }
        return result;
    }

    protected boolean checkQuoteLimitsAfterApplyingRegeneratedLimits( double[][] limits, PriceRegeneration prcRegen )
    {
        boolean result = true;
        double[] bidLimits = limits[0];
        double[] offerLimits = limits[1];
        int tier;
        double bidQuoteLimit = 0.0;
        double totalBidQuoteLimit = 0.0;
        double offerQuoteLimit = 0.0;
        double totalOfferQuoteLimit = 0.0;

        for ( int i = 0; i < bidLimits.length; i++ )
        {
            bidQuoteLimit = bidLimits[i];
            totalBidQuoteLimit += bidQuoteLimit;
            double tierLimit = prcRegen.getBidTierLimit( i );
            if ( Math.abs( tierLimit - bidLimits[i] ) > PriceRegenerationConstants.MINIMUM )
            {
                result = false;
            }
        }
        for ( int i = 0; i < offerLimits.length; i++ )
        {
            offerQuoteLimit = offerLimits[i];
            totalOfferQuoteLimit += offerQuoteLimit;
            double tierLimit = prcRegen.getOfferTierLimit( i );
            if ( Math.abs( tierLimit - offerLimits[i] ) > PriceRegenerationConstants.MINIMUM )
            {
                result = false;
            }
        }
        // also check quote is of multi tier structure.
        if ( isMultiTier() )
        {
            if ( Math.abs( prcRegen.getBidLimit() - bidQuoteLimit ) > PriceRegenerationConstants.MINIMUM || Math.abs( prcRegen.getOfferLimit() - offerQuoteLimit ) > PriceRegenerationConstants.MINIMUM )
            {
                result = false;
            }
        }
        else
        {
            if ( Math.abs( prcRegen.getBidLimit() - totalBidQuoteLimit ) > PriceRegenerationConstants.MINIMUM || Math.abs( prcRegen.getOfferLimit() - totalOfferQuoteLimit ) > PriceRegenerationConstants.MINIMUM )
            {
                result = false;
            }
        }
        return result;
    }

    protected int getTier( FXLegDealingPrice dp, boolean isBid )
    {
        int tier = 0;
        String dpName = dp.getName();
        if ( isBid )
        {
            if ( !dpName.equals( PriceRegenerationConstants.BID ) )
            {
                tier = Integer.parseInt( dpName.substring( PriceRegenerationConstants.BID.length() ) );
            }
        }
        else
        {
            if ( !dpName.equals( PriceRegenerationConstants.OFFER ) )
            {
                tier = Integer.parseInt( dpName.substring( PriceRegenerationConstants.OFFER.length() ) );
            }
        }
        return tier;
    }

    /**
     * Test regeneration handler class.
     */
    protected class TestRegenerationHandler implements MessageHandler
    {
        private PriceRegenerationParameters prcRegenParam = null;
        private PriceRegeneration prcRegen = null;
        private int invocations = 0;

        public PriceRegeneration getPriceRegeneration()
        {
            return prcRegen;
        }

        public int getHandlerInvocations()
        {
            return invocations;
        }

        public void setPriceRegenerationParameters( PriceRegenerationParameters aPrcRegenParam )
        {
            this.prcRegenParam = aPrcRegenParam;
        }

        public PriceRegenerationParameters getPriceRegenerationParameters()
        {
            return prcRegenParam;
        }

        public Message handle( Message msg )
        {
            WorkflowMessage wm = ( WorkflowMessage ) msg;
            PriceRegeneration priceRegen = ( PriceRegeneration ) wm.getObject();
            this.prcRegen = priceRegen;
            invocations++;
            if ( log.isInfoEnabled() )
            {
                log.info( "TestRegenerationHandler.handle.INFO : handler invocation. Invocation No : " + invocations + " PriceRegeneration : " + priceRegen + " PrcRegenParam : " + prcRegenParam.getDisplayKey() );
            }
            return null;
        }
    }

    public void testRemovePriceRegenerationEvent()
    {
        try
        {
            init();
            String ccyPair = currencyPairs[0];
            String ccyPair1 = currencyPairs[1];
            org1.setPriceRegenerationParameters( noReplenishHighestIndependentAmt );
            org2.setPriceRegenerationParameters( noReplenishHighestIndependentInterval );
            Quote newQuote0 = prepareQuote( org1, ccyPair, 1000, 5000 );
            svc.applyPriceRegenerationRules( newQuote0, org1Tp1, isMultiTier() );
            svc.applyPriceRegenerationRules( newQuote0, org2Tp1, isMultiTier() );
            Quote newQuote1 = prepareQuote( org1, ccyPair1, 1000, 5000 );
            svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );
            svc.applyPriceRegenerationRules( newQuote1, org2Tp1, isMultiTier() );

            Quote newQuote2 = prepareQuote( org2, ccyPair, 1000, 1000 );
            svc.applyPriceRegenerationRules( newQuote2, org1Tp1, isMultiTier() );
            svc.applyPriceRegenerationRules( newQuote2, org2Tp1, isMultiTier() );
            Quote newQuote3 = prepareQuote( org2, ccyPair1, 1000, 1000 );
            svc.applyPriceRegenerationRules( newQuote3, org1Tp1, isMultiTier() );
            svc.applyPriceRegenerationRules( newQuote3, org2Tp1, isMultiTier() );

            PriceRegenerationEvent event1 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
            PriceRegenerationEvent event2 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org2.getShortName(), org2Tp1.getShortName(), ccyPair1 );
            assertNotNull( event1 );
            assertNotNull( event2 );

            // remove all the org1 events.
            PriceRegenerationEventCacheC.getInstance().removeAllProviderEvents( org1.getShortName() );
            event1 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
            event2 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org2.getShortName(), org2Tp1.getShortName(), ccyPair1 );
            assertNull( event1 );
            assertNotNull( event2 );

            // now remove the tp1 events for org2
            PriceRegenerationEventCacheC.getInstance().removeAllTradingPartyEvents( org2.getShortName(), org1Tp1.getShortName() );
            event1 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org2.getShortName(), org2Tp1.getShortName(), ccyPair );
            event2 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org2.getShortName(), org1Tp1.getShortName(), ccyPair1 );
            assertNotNull( event1 );
            assertNull( event2 );

            // now remove all the ccyPair events from org2/tp2
            PriceRegenerationEventCacheC.getInstance().removeCurrencyPairEvent( org2.getShortName(), org2Tp1.getShortName(), ccyPair );
            event1 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org2.getShortName(), org2Tp1.getShortName(), ccyPair1 );
            event2 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org2.getShortName(), org2Tp1.getShortName(), ccyPair );
            assertNotNull( event1 );
            assertNull( event2 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    protected PriceRegeneration preparePriceRegeneration( PriceRegenerationEvent event )
    {
        PriceRegeneration prcRegen = event.getAvailableTradingLimits().copy();
        if ( event.isMultiTier() )
        {
            double bidLimit = 0.0;
            double[] bidTiers = event.getAvailableTradingLimits().getBidTiers();
            for ( int index = 0; index < bidTiers.length; index++ )
            {
                bidLimit += bidTiers[index];
                prcRegen.setTierLimit( index, bidLimit, true );
            }

            double offerLimit = 0.0;
            double[] offerTiers = event.getAvailableTradingLimits().getOfferTiers();
            for ( int index = 0; index < offerTiers.length; index++ )
            {
                offerLimit += offerTiers[index];
                prcRegen.setTierLimit( index, offerLimit, false );
            }
        }
        prcRegen.setPendingRegenerations( Math.max( event.getBidPriceRegenerationEventParameters().getPendingRegenerations(), event.getOfferPriceRegenerationEventParameters().getPendingRegenerations() ) );
        prcRegen.setRapidFirePeriod( event.isRapidFirePeriod() );
        return prcRegen;
    }

    public void log( String str )
    {
        System.out.println( "################### " + str );
    }

    public void testPriceRegenerationKeyExtraction()
    {
        String lp = "CITI";
        String cp = "EUR/USD";
        String tp = "FI2-le1";
        String key = "CITI:EUR/USD:FI2-le1";
        int delim1 = key.indexOf( PriceRegenerationConstants.KEY_DELIMITER );
        int delim2 = key.lastIndexOf( PriceRegenerationConstants.KEY_DELIMITER );
        String org = key.substring( 0, delim1 );
        String ccyPair = key.substring( delim1 + 1, delim2 );
        String tradingParty = key.substring( delim2 + 1, key.length() );
        log( "org=" + org + ",ccyPair=" + ccyPair + ",tp=" + tradingParty );
        assertEquals( lp, org );
        assertEquals( cp, ccyPair );
        assertEquals( tp, tradingParty );
    }
}
