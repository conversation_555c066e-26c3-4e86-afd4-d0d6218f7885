package com.integral.finance.dealing.priceRegeneration.test;

// Copyright (c) 2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationParameters;
import com.integral.finance.trade.Trade;
import com.integral.log.LogFactory;
import com.integral.log.LogLevel;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * This class is used to load test various price regeneration workflows.
 */
public class PriceRegenerationWorkflowLoadPTestC extends PriceRegenerationServicePTestC
{
    static String name = "PriceRegeneration Workflow Load Test";

    private int testOrgCount = 50;
    private int tpPerProvider = 4;
    private double processingTimeThreshold = 1;

    private Organization[] testProviders = new Organization[testOrgCount];
    private TradingParty[] testTradingParties = new TradingParty[testOrgCount * tpPerProvider];
    private LegalEntity[] testLegalEntities = new LegalEntity[testOrgCount * tpPerProvider];

    // workflow names
    static String APPLY_PRICE_REGEN = "ApplyPriceRegen";
    static String START_PRICE_REGEN = "StartPriceRegen";

    public PriceRegenerationWorkflowLoadPTestC( String name )
    {
        super( name );
    }

    protected void setUp()
    {
        LogFactory.getLog( "com.integral.finance.dealing.priceRegeneration" ).setLevel( LogLevel.WARN );
        log.setLevel( LogLevel.WARN );
    }

    protected void tearDown()
    {
        LogFactory.getLog( "com.integral.finance.dealing.priceRegeneration" ).setLevel( LogLevel.INFO );
        log.setLevel( LogLevel.WARN );
    }

    public void testApplyPriceRegeneration()
    {
        try
        {
            log( "Begin testApplyPriceRegeneration" );
            init();
            initTestData();
            PriceRegenerationThreadMonitor monitor = new PriceRegenerationThreadMonitor();
            Thread applyThread = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 50, 1000, 100, true, monitor ) );
            applyThread.setDaemon( true );
            applyThread.start();

            synchronized ( monitor )
            {
                monitor.wait();
            }

            checkProcessingTimes( monitor );
            log( "Finished testApplyPriceRegeneration" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Failed testApplyPriceRegeneration" );
        }
    }

    public void testStartPriceRegeneration()
    {
        try
        {
            log( "Begin testStartPriceRegeneration" );
            init();
            initTestData();
            PriceRegenerationThreadMonitor monitor = new PriceRegenerationThreadMonitor();
            Thread applyThread = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            applyThread.setDaemon( true );
            applyThread.start();
            synchronized ( monitor )
            {
                monitor.wait();
            }
            Thread startPriceThread = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            startPriceThread.setDaemon( true );
            startPriceThread.start();
            synchronized ( monitor )
            {
                monitor.wait();
            }

            checkProcessingTimes( monitor );
            log( "Finished testStartPriceRegeneration" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Failed testStartPriceRegeneration" );
        }
    }

    public void testApplyAndStartPriceRegenerationMultipleOrgAndCurrencyPairs()
    {
        try
        {
            log( "Begin testApplyAndStartPriceRegenerationMultipleOrgAndCurrencyPairs" );
            init();
            initTestData();
            PriceRegenerationThreadMonitor monitor = new PriceRegenerationThreadMonitor();
            Thread apply1 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            apply1.setDaemon( true );
            apply1.start();
            Thread apply2 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            apply2.setDaemon( true );
            apply2.start();
            Thread apply3 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            apply3.setDaemon( true );
            apply3.start();
            Thread apply4 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            apply4.setDaemon( true );
            apply4.start();
            Thread apply5 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            apply5.setDaemon( true );
            apply5.start();

            sleepFor( 2000 );

            Thread startPrice1 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            startPrice1.setDaemon( true );
            startPrice1.start();
            Thread startPrice2 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            startPrice2.setDaemon( true );
            startPrice2.start();
            Thread startPrice3 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            startPrice3.setDaemon( true );
            startPrice3.start();
            Thread startPrice4 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            startPrice4.setDaemon( true );
            startPrice4.start();
            Thread startPrice5 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            startPrice5.setDaemon( true );
            startPrice5.start();
            Thread startPrice6 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 50, 1000, 0, true, monitor ) );
            startPrice6.setDaemon( true );
            startPrice6.start();

            synchronized ( monitor )
            {
                monitor.wait();
            }

            checkProcessingTimes( monitor );
            log( "Finished testApplyAndStartPriceRegenerationMultipleOrgAndCurrencyPairs" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Failed testApplyAndStartPriceRegenerationMultipleOrgAndCurrencyPairs" );
        }
    }

    public void testApplyAndStartPriceRegenerationSingleOrgAndCurrencyPair()
    {
        try
        {
            log( "Begin testApplyAndStartPriceRegenerationSingleOrgAndCurrencyPair" );
            init();
            initTestData();
            PriceRegenerationThreadMonitor monitor = new PriceRegenerationThreadMonitor();
            Thread apply1 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            apply1.setDaemon( true );
            apply1.start();
            Thread apply2 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            apply2.setDaemon( true );
            apply2.start();
            Thread apply3 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            apply3.setDaemon( true );
            apply3.start();
            Thread apply4 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            apply4.setDaemon( true );
            apply4.start();
            Thread apply5 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            apply5.setDaemon( true );
            apply5.start();
            Thread apply6 = new Thread( new ApplyPriceRegenerationThread( APPLY_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            apply6.setDaemon( true );
            apply6.start();

            sleepFor( 2000 );

            Thread startPrice1 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            startPrice1.setDaemon( true );
            startPrice1.start();
            Thread startPrice2 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            startPrice2.setDaemon( true );
            startPrice2.start();
            Thread startPrice3 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            startPrice3.setDaemon( true );
            startPrice3.start();
            Thread startPrice4 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            startPrice4.setDaemon( true );
            startPrice4.start();
            Thread startPrice5 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            startPrice5.setDaemon( true );
            startPrice5.start();
            Thread startPrice6 = new Thread( new StartPriceRegenerationThread( START_PRICE_REGEN, 1, 1000, 0, false, monitor ) );
            startPrice6.setDaemon( true );
            startPrice6.start();

            synchronized ( monitor )
            {
                monitor.wait();
            }

            checkProcessingTimes( monitor );
            log( "Finished testApplyAndStartPriceRegenerationSingleOrgAndCurrencyPair" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Failed testApplyAndStartPriceRegenerationSingleOrgAndCurrencyPair" );
        }
    }

    private void initTestData()
    {
        for ( int orgIndex = 0; orgIndex < testOrgCount; orgIndex++ )
        {
            Organization org = new OrganizationC();
            org.setShortName( "TestOrg" + orgIndex );
            testProviders[orgIndex] = org;
            for ( int tpIndex = 0; tpIndex < tpPerProvider; tpIndex++ )
            {
                int tpNum = ( orgIndex * tpPerProvider ) + tpIndex;
                LegalEntity le = new LegalEntityC();
                le.setShortName( "TestLE" + tpNum );
                le.setOrganization( org );
                testLegalEntities[tpNum] = le;

                TradingParty tp = new TradingPartyC();
                tp.setShortName( "TestTP" + tpNum );
                tp.setLegalEntity( le );
                tp.setOrganization( org );
                testTradingParties[tpNum] = tp;
            }
        }

    }

    private void checkProcessingTimes( PriceRegenerationThreadMonitor monitor )
    {
        Map results = monitor.getAverageProcessingTimes();
        Iterator iter = results.entrySet().iterator();
        while ( iter.hasNext() )
        {
            Map.Entry result = ( Map.Entry ) iter.next();
            log( result.getKey() + " took : " + result.getValue() );
            assertEquals( "AverageProcessingTime : " + result.getValue(), ( ( Double ) result.getValue() ).doubleValue() < processingTimeThreshold, true );
        }
    }

    protected void sleepFor( long interval )
    {
        try
        {
            if ( interval > 0 )
            {
                Thread.sleep( interval );
            }
        }
        catch ( Exception e )
        {

        }
    }

    private class PriceRegenerationThreadMonitor
    {
        private Map averageProcessTimes = new HashMap();
        private Map processors = Collections.synchronizedMap( new HashMap() );

        public Map getAverageProcessingTimes()
        {
            return averageProcessTimes;
        }

        public void addAverageProcessingTime( String name, double processTime )
        {
            averageProcessTimes.put( name, new Double( processTime ) );
        }

        public void addProcessors( Object processor, String name )
        {
            processors.put( name, processor );
        }

        public void finishProcessing( String name, Object processor )
        {
            processors.remove( name );
            if ( processors.isEmpty() )
            {
                synchronized ( this )
                {
                    this.notifyAll();
                }
            }
        }
    }

    private class ApplyPriceRegenerationThread implements Runnable
    {
        private String workflowName = null;
        private int orgCount = 0;
        private int applyCount = 0;
        private long sleepInterval = 0;
        private boolean rotateCcyPair = true;
        private PriceRegenerationThreadMonitor monitor;

        public ApplyPriceRegenerationThread( String name, int orgsCount, int apply, long sleep, boolean rotate, PriceRegenerationThreadMonitor obj )
        {
            workflowName = name;
            orgCount = orgsCount;
            applyCount = apply;
            sleepInterval = sleep;
            rotateCcyPair = rotate;
            monitor = obj;
            monitor.addProcessors( this, name );
        }

        public void run()
        {
            int processingTime = 0;
            int ccyPairIndex = 0;
            int orgIndex = 0;
            int prcRegenIndex = 0;
            for ( int i = 0; i < applyCount; i++ )
            {
                if ( sleepInterval > 0 )
                {
                    sleepFor( sleepInterval );
                }

                Organization org = testProviders[orgIndex];
                org.setPriceRegenerationParameters( ( PriceRegenerationParameters ) regenParams.toArray()[prcRegenIndex] );
                Quote newQuote = prepareQuote( testProviders[orgIndex], currencyPairs[ccyPairIndex], 5000, 5000 );
                long t0 = System.currentTimeMillis();
                svc.applyPriceRegenerationRules( newQuote, testTradingParties[orgIndex * tpPerProvider + 1], isMultiTier() );
                processingTime += System.currentTimeMillis() - t0;

                if ( rotateCcyPair )
                {
                    ccyPairIndex = ccyPairIndex == currencyPairs.length - 1 ? 0 : ccyPairIndex + 1;
                }
                orgIndex = orgIndex == orgCount - 1 ? 0 : orgIndex + 1;
                prcRegenIndex = prcRegenIndex == regenParams.size() - 1 ? 0 : prcRegenIndex + 1;
            }
            double avgTime = ( double ) processingTime / ( double ) applyCount;
            log( "ApplyPriceRegenerationThread took : " + processingTime + " for apply count : " + applyCount + " average time for processing : " + avgTime );

            if ( monitor != null )
            {
                synchronized ( monitor )
                {
                    monitor.addAverageProcessingTime( workflowName, avgTime );
                    monitor.finishProcessing( workflowName, this );
                }
            }
        }
    }

    private class StartPriceRegenerationThread implements Runnable
    {
        private String workflowName = null;
        private int orgCount = 0;
        private int applyCount = 0;
        private long sleepInterval = 0;
        private boolean rotateCcyPair = true;
        private PriceRegenerationThreadMonitor monitor;

        public StartPriceRegenerationThread( String name, int orgsCount, int apply, long sleep, boolean rotate, PriceRegenerationThreadMonitor obj )
        {
            workflowName = name;
            orgCount = orgsCount;
            applyCount = apply;
            sleepInterval = sleep;
            rotateCcyPair = rotate;
            monitor = obj;
            monitor.addProcessors( this, name );
        }

        public void run()
        {
            int processingTime = 0;
            int ccyPairIndex = 0;
            int orgIndex = 0;
            int prcRegenIndex = 0;
            for ( int i = 0; i < applyCount; i++ )
            {
                if ( sleepInterval > 0 )
                {
                    sleepFor( sleepInterval );
                }

                Organization org = testProviders[orgIndex];
                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( org.getPriceRegenerationParameters() );

                // now start taking trades.
                Trade trade = prepareTrade( 1000, true, currencyPairs[ccyPairIndex], org, testTradingParties[orgIndex * tpPerProvider + 1] );
                long t0 = System.currentTimeMillis();
                svc.startPriceRegeneration( trade, testHandler );
                processingTime += System.currentTimeMillis() - t0;

                if ( rotateCcyPair )
                {
                    ccyPairIndex = ccyPairIndex == currencyPairs.length - 1 ? 0 : ccyPairIndex + 1;
                }
                orgIndex = orgIndex == orgCount - 1 ? 0 : orgIndex + 1;
                prcRegenIndex = prcRegenIndex == regenParams.size() - 1 ? 0 : prcRegenIndex + 1;
            }
            double avgTime = ( double ) processingTime / ( double ) applyCount;

            log( "StartPriceRegenerationThread took : " + processingTime + " for apply count : " + applyCount + " average time for processing : " + avgTime );

            if ( monitor != null )
            {
                synchronized ( monitor )
                {
                    monitor.addAverageProcessingTime( workflowName, avgTime );
                    monitor.finishProcessing( workflowName, this );
                }
            }
        }
    }
}
