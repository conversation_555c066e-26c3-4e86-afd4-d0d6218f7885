package com.integral.finance.dealing.priceRegeneration.test;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.priceRegeneration.PriceRegeneration;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationConstants;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationEvent;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationParameters;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationParametersFacade;
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfiguration;
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfigurationFactory;
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfigurationMBean;
import com.integral.finance.dealing.priceRegeneration.fx.PriceRegenerationEventCacheC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.math.MathUtil;
import com.integral.message.MessageHandler;
import com.integral.system.configuration.ConfigurationProperty;

import java.util.*;

/**
 * Tests the price regeneration service workflows.
 *
 * <AUTHOR> Development Corp.
 */
public class PriceRegenerationServiceWorkflowPTestC extends PriceRegenerationServicePTestC
{
    static String name = "Price Regen Service workflow Test";

    public PriceRegenerationServiceWorkflowPTestC( String name )
    {
        super( name );
    }

    public void testMultiQuotePriceRegenerationTakingTradeLimits()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean result1 = svc.startPriceRegeneration( prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp1 ), testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                if ( !getPriceRegenerationParametersFacade( prcRegenParam ).isNoPriceRegeneration() )
                {
                    boolean result2 = svc.startPriceRegeneration( prepareTrade( getBidLimit() + acceptanceToleranceAmount + 2000, true, ccyPair, org1, org1Tp1 ), testHandler );
                    assertEquals( "FailedToTakeTradingLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, false );
                }
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTakingTradeLimits" );
        }
    }


    public void testMultiQuoteMultiplePriceRegenerationTakingTradeLimits()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                Map<Integer, Double> tierAcceptedAmounts = new HashMap<Integer, Double>(numberOfTiers);
                // now take the complete liquidity of tiers.
                double amt = 0;
                for (int i = 0; i < numberOfTiers; i++) {
                    int multiplier = isMultiTier() ? i * 3 != 0 ? i * 3 : 1 : i + 1;
                    double tierAmt = getBidTierLimit() * multiplier;
                    amt = amt + tierAmt;
                    tierAcceptedAmounts.put(i, tierAmt);
                }

                // now start taking trades.
                FXSingleLeg trade = prepareTrade(amt, true, ccyPair, org1, org1Tp1);
                trade.setTierMatchedAmount(tierAcceptedAmounts);

                boolean result1 = svc.startPriceRegeneration(trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                if ( !getPriceRegenerationParametersFacade( prcRegenParam ).isNoPriceRegeneration() )
                {
                    boolean result2 = svc.startPriceRegeneration( prepareTrade( getBidLimit() + acceptanceToleranceAmount + 2000, true, ccyPair, org1, org1Tp1 ), testHandler );
                    assertEquals( "FailedToTakeTradingLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, false );
                }
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTakingTradeLimits" );
        }
    }

    public void testMultiQuotePriceRegenerationUndoTakingTradeLimits()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                svc.stopPriceRegeneration( trade, testHandler );
                assertEquals( "IsInPriceRegen. PrcRegenParam : " + prcRegenParam.getDisplayKey(), svc.isInPriceRegeneration( org1, org1Tp1, ccyPair ), false );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationUndoTakingTradeLimits" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment. First, trading limit
     * for tiers registered and full trading limit it taken by a trade. Then it is tested to see whether any subsequent trade
     * fails even with a small amount on the same(bid or offer) side earlier. Then, next quote arrives and replenishes the
     * trading limits. Then a trade is tested with the maximum trading limit and see whether it is taken successfully. Finally,
     * handlers are inspected to see whether any of them are invoked more than once.
     */
    public void testMultiQuotePriceRegenerationWithoutReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                sleepFor( 2000 );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( getBidLimit() - getTradingLimit() + 2000 + acceptanceToleranceAmount, true, ccyPair, org1, org1Tp1 ), testHandler );
                assertEquals( "Failed to take trading limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, false );

                Quote qot = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( qot, org1Tp1, isMultiTier() );

                boolean result3 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit after next quote replenishes the limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

            }

            sleepFor( 20000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "TestRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be less than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() < 3, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithoutReplenishment" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters with replenishment. First, trading limit
     * for tiers registered and full trading limit it taken by a trade. Then after waiting for 2 seconds, a small amount trade
     * is done to see whether regeneration took place. Then, a new quote with the same limits makes sure that
     * a trade with full amount will not go through. Another quote with different limits resets the trading limits and a
     * full amount trade will go through now.
     */
    public void testMultiQuotePriceRegenerationWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < replenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) replenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                sleepFor( 2100 );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( amount, true, ccyPair, org1, org1Tp1 ), testHandler );
                assertEquals( "Able take trading limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                Quote qot = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( qot, org1Tp1, isMultiTier() );

                boolean result3 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "Next quote comes with the same limits. Should fail now. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );
            }

            sleepFor( 20000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "testMultiQuotePriceRegenerationWithReplenishment - handler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "testMultiQuotePriceRegenerationWithReplenishment - Handler Invocation should be less than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 3, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationWithNetting()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "testMultiQuotePriceRegenerationWithNetting - nettingRegenParams size : " + nettingRegenParams.size() );
            for ( int index = 0; index < nettingRegenParams.size(); index++ )
            {
                prcRegenParam = nettingRegenParams.get( index );
                log( "testMultiQuotePriceRegenerationWithNetting - prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getBidLimit(), true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), prcRegen1.getBidLimit(), 0.0 );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), prcRegen1.getOfferLimit(), getOfferLimit() );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( getBidLimit(), false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), getBidLimit() );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), getOfferLimit() );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithNetting" );
        }
    }

    public void testMultiQuotePriceRegenerationWithoutNetting()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "testMultiQuotePriceRegenerationWithoutNetting - nettingRegenParams size : " + noNettingRegenParams.size() );
            for ( int index = 0; index < noNettingRegenParams.size(); index++ )
            {
                prcRegenParam = noNettingRegenParams.get( index );
                log( "testMultiQuotePriceRegenerationWithoutNetting - prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), prcRegen1.getBidLimit(), getBidLimit() - getTradingLimit() );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), prcRegen1.getOfferLimit(), getOfferLimit() - getTradingLimit() );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( getTradingLimit(), false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "Failed taking trading limit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, false );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), getBidLimit() - getTradingLimit() );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), getOfferLimit() - getTradingLimit() );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithoutNetting" );
        }
    }

    public void testMultiQuotePriceRegenerationIndependentBidOfferLiquidity()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "testMultiQuotePriceRegenerationIndependentBidOfferLiquidity - nettingRegenParams size : " + independentRegenParams.size() );
            for ( int index = 0; index < independentRegenParams.size(); index++ )
            {
                prcRegenParam = independentRegenParams.get( index );
                log( "testMultiQuotePriceRegenerationIndependentBidOfferLiquidity - prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getBidLimit(), true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), prcRegen1.getBidLimit(), 0.0 );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), prcRegen1.getOfferLimit(), getOfferLimit() );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( getOfferLimit(), false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradingLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), 0.0 );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), 0.0 );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationIndependentBidOfferLiquidity" );
        }
    }

    public void testMultiQuotePriceRegenerationNettingWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "nettingReplenishParams size : " + nettingReplenishParams.size() );
            for ( int index = 0; index < nettingReplenishParams.size(); index++ )
            {
                prcRegenParam = nettingReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                double amt = numberOfTiers > 1 ? getBidLimit() / 3 : getBidTierLimit() - 3000;
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), Math.abs( prcRegen1.getBidLimit() - ( getBidLimit() - amt ) ) < PriceRegenerationConstants.MINIMUM, true );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), Math.abs( prcRegen1.getOfferLimit() - getOfferLimit() ) < PriceRegenerationConstants.MINIMUM, true );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( amt, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), Math.abs( prcRegen2.getBidLimit() - getBidLimit() ) < PriceRegenerationConstants.MINIMUM, true );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), Math.abs( prcRegen2.getOfferLimit() - getOfferLimit() ) < PriceRegenerationConstants.MINIMUM, true );

                double amt1 = amt + 1000;
                boolean result3 = svc.startPriceRegeneration( prepareTrade( amt1, true, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen3 );
                assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );
                assertEquals( "Bid limit : " + prcRegen3.getBidLimit(), Math.abs( prcRegen3.getBidLimit() - ( getBidLimit() - amt1 ) ) < PriceRegenerationConstants.MINIMUM, true );
                assertEquals( "Offer limit : " + prcRegen3.getOfferLimit(), Math.abs( prcRegen3.getOfferLimit() - getOfferLimit() ) < PriceRegenerationConstants.MINIMUM, true );

                double amt2 = amt1 + 2000;
                boolean result4 = svc.startPriceRegeneration( prepareTrade( amt2, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen4 = testHandler.getPriceRegeneration();
                double offerLimit = prcRegen4.getOfferLimit();
                log( "After offer trade, prcRegen : " + prcRegen4 );
                assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
                assertEquals( "Bid limit : " + prcRegen4.getBidLimit(), Math.abs( prcRegen4.getBidLimit() - getBidLimit() ) < PriceRegenerationConstants.MINIMUM, true );
                assertEquals( "Offer limit : " + prcRegen4.getOfferLimit(), Math.abs( prcRegen4.getOfferLimit() - ( getOfferLimit() - ( amt2 - amt1 ) ) ) < PriceRegenerationConstants.MINIMUM, true );

                sleepFor( 2500 );
                PriceRegeneration prcRegen5 = testHandler.getPriceRegeneration();
                log( "Offer limit 2 seconds ago : " + offerLimit + " now : " + prcRegen5.getOfferLimit() );
                assertEquals( "Offer limit : " + prcRegen5.getOfferLimit(), prcRegen5.getOfferLimit() > offerLimit, true );

                sleepFor( 10000 );

                PriceRegeneration prcRegen = testHandler.getPriceRegeneration();
                log( "After sleeping for all regenerations, prcRegen : " + prcRegen );
                assertEquals( "Bid limit : " + prcRegen.getBidLimit(), prcRegen.getBidLimit(), getBidLimit() );
                assertEquals( "Offer limit : " + prcRegen.getOfferLimit(), prcRegen.getOfferLimit(), getOfferLimit() );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationNettingWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationNoNettingWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "noNettingReplenishParams size : " + noNettingReplenishParams.size() );
            for ( int index = 0; index < noNettingReplenishParams.size(); index++ )
            {
                prcRegenParam = noNettingReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                double amt1 = getTradingLimit() * 0.4;
                Trade trade = prepareTrade( amt1, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), prcRegen1.getBidLimit(), getBidLimit() - amt1 );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), prcRegen1.getOfferLimit(), getOfferLimit() - amt1 );

                double amt2 = getTradingLimit() * 0.5;
                boolean result2 = svc.startPriceRegeneration( prepareTrade( amt2, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "Successful taking trading limit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), getBidLimit() - ( amt1 + amt2 ) );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), getOfferLimit() - ( amt1 + amt2 ) );

                sleepFor( 3000 );

                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                log( "Price Regeneration after 3 seconds.  " + prcRegen3 );
                assertEquals( "bidLimit : " + prcRegen3.getBidLimit() + " offerLimit : " + prcRegen3.getOfferLimit(), prcRegen3.getOfferLimit() - prcRegen2.getOfferLimit(), prcRegen3.getBidLimit() - prcRegen2.getBidLimit() );

                sleepFor( 26000 );

                PriceRegeneration prcRegen4 = testHandler.getPriceRegeneration();
                log( "Price Regeneration after 16 seconds.  " + prcRegen4 );
                assertEquals( "Check bid and offer are equal. bidLimit : " + prcRegen4.getBidLimit() + " offerLimit : " + prcRegen4.getOfferLimit(), prcRegen4.getOfferLimit() - prcRegen3.getOfferLimit(), prcRegen4.getBidLimit() - prcRegen3.getBidLimit() );
                assertEquals( "Check bidLimit is trading limit are equal.", prcRegen4.getBidLimit(), getBidLimit() );
                assertEquals( "Check offerLimit is trading limit are equal.", prcRegen4.getOfferLimit(), getOfferLimit() );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationNoNettingWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationIndependentWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "independentReplenishParams size : " + independentReplenishParams.size() );
            for ( int index = 0; index < independentReplenishParams.size(); index++ )
            {
                prcRegenParam = independentReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                double amt1 = numberOfTiers > 1 ? getBidTierLimit() + 3000 : getBidTierLimit() - 2000;
                Trade trade = prepareTrade( amt1, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), prcRegen1.getBidLimit(), getBidLimit() - amt1 );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), prcRegen1.getOfferLimit(), getOfferLimit() );

                double amt2 = amt1 - 2000;
                boolean result2 = svc.startPriceRegeneration( prepareTrade( amt2, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradingLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), getBidLimit() - amt1 );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), getOfferLimit() - amt2 );

                sleepFor( 2500 );

                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                log( "After 2 seconds : " + prcRegen3 );
                assertEquals( "Bid limit : " + prcRegen3.getBidLimit(), prcRegen3.getBidLimit() > ( getBidLimit() - amt1 ), true );
                assertEquals( "Offer limit : " + prcRegen3.getOfferLimit(), prcRegen3.getOfferLimit() > ( getOfferLimit() - amt2 ), true );

                sleepFor( 12000 );

                PriceRegeneration prcRegen4 = testHandler.getPriceRegeneration();
                log( "Price Regeneration after 12 seconds.  " + prcRegen4 );
                assertEquals( "Check bidLimit is trading limit are equal.", prcRegen4.getBidLimit(), getBidLimit() );
                assertEquals( "Check offerLimit is trading limit are equal.", prcRegen4.getOfferLimit(), getOfferLimit() );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationIndependentWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationNettingWithoutReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "nettingNoReplenishParams size : " + nettingNoReplenishParams.size() );
            for ( int index = 0; index < nettingNoReplenishParams.size(); index++ )
            {
                prcRegenParam = nettingNoReplenishParams.get( index );
                log( "testPriceRegenerationNettingWithoutReplenishment - prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), prcRegen1.getBidLimit(), getBidLimit() - amt );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), prcRegen1.getOfferLimit(), getOfferLimit() );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( amt, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), getBidLimit() );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), getOfferLimit() );

                double amt1 = getBidLimit() * 0.5;
                boolean result3 = svc.startPriceRegeneration( prepareTrade( amt1, true, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen3 );
                assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );
                assertEquals( "Bid limit : " + prcRegen3.getBidLimit(), prcRegen3.getBidLimit(), getBidLimit() - amt1 );
                assertEquals( "Offer limit : " + prcRegen3.getOfferLimit(), prcRegen3.getOfferLimit(), getOfferLimit() );

                double amt2 = amt1 + ( getBidLimit() * 0.2 );
                boolean result4 = svc.startPriceRegeneration( prepareTrade( amt2, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen4 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen4 );
                assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
                assertEquals( "Bid limit : " + prcRegen4.getBidLimit(), prcRegen4.getBidLimit(), getBidLimit() );
                assertEquals( "Offer limit : " + prcRegen4.getOfferLimit(), prcRegen4.getOfferLimit(), getOfferLimit() - ( amt2 - amt1 ) );

                // now decline the first trade.
                svc.stopPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen5 = testHandler.getPriceRegeneration();
                log( "After declining first trade. prcRegen : " + prcRegen5 );
                assertEquals( "Bid limit : " + prcRegen5.getBidLimit(), prcRegen5.getBidLimit(), getBidLimit() );
                assertEquals( "Offer limit : " + prcRegen5.getOfferLimit(), prcRegen5.getOfferLimit(), getOfferLimit() - ( amt2 - amt1 ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationNettingWithoutReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationNoNettingWithoutReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "noNettingNoReplenishParams size : " + noNettingNoReplenishParams.size() );
            for ( int index = 0; index < noNettingNoReplenishParams.size(); index++ )
            {
                prcRegenParam = noNettingNoReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                double amt1 = getTradingLimit() * 0.4;
                Trade trade = prepareTrade( amt1, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), prcRegen1.getBidLimit(), getBidLimit() - amt1 );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), prcRegen1.getOfferLimit(), getOfferLimit() - amt1 );

                double amt2 = getTradingLimit() * 0.6;
                boolean result2 = svc.startPriceRegeneration( prepareTrade( amt2, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "Succeeded taking trading limit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), getBidLimit() - ( amt1 + amt2 ) );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), getOfferLimit() - ( amt1 + amt2 ) );

                // now decline the first trade.
                svc.stopPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                log( "After decline trade, prcRegen : " + prcRegen3 );
                assertEquals( "Bid limit : " + prcRegen3.getBidLimit(), prcRegen3.getBidLimit(), getBidLimit() - ( amt1 + amt2 ) );
                assertEquals( "Offer limit : " + prcRegen3.getOfferLimit(), prcRegen3.getOfferLimit(), getOfferLimit() - ( amt1 + amt2 ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationNoNettingWithoutReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationIndependentWithoutReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "independentNoReplenishParams size : " + independentNoReplenishParams.size() );
            for ( int index = 0; index < independentNoReplenishParams.size(); index++ )
            {
                prcRegenParam = independentNoReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                double amt1 = numberOfTiers > 1 ? getBidTierLimit() + 3000 : getBidTierLimit() - 1000;
                Trade trade = prepareTrade( amt1, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), Math.abs( prcRegen1.getBidLimit() - ( getBidLimit() - amt1 ) ) < PriceRegenerationConstants.MINIMUM, true );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), Math.abs( prcRegen1.getOfferLimit() - getOfferLimit() ) < PriceRegenerationConstants.MINIMUM, true );

                double amt2 = amt1 - 2000;
                boolean result2 = svc.startPriceRegeneration( prepareTrade( amt2, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradingLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), Math.abs( prcRegen2.getBidLimit() - ( getBidLimit() - amt1 ) ) < PriceRegenerationConstants.MINIMUM, true );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), Math.abs( prcRegen2.getOfferLimit() - ( getOfferLimit() - amt2 ) ) < PriceRegenerationConstants.MINIMUM, true );

                // decline the first trade.
                svc.stopPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                log( "After declining trade, prcRegen : " + prcRegen3 );
                assertEquals( "Bid limit : " + prcRegen3.getBidLimit(), Math.abs( prcRegen3.getBidLimit() - ( getBidLimit() - amt1 ) ) < PriceRegenerationConstants.MINIMUM, true );
                assertEquals( "Offer limit : " + prcRegen3.getOfferLimit(), Math.abs( prcRegen3.getOfferLimit() - ( getOfferLimit() - amt2 ) ) < PriceRegenerationConstants.MINIMUM, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationIndependentWithoutReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationFromLowestTier()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            if ( numberOfTiers != 3 )
            {
                return;
            }
            log( "lowestParams size : " + lowestParams.size() );
            if ( numberOfTiers != 3 )
            {
                return;
            }
            for ( int index = 0; index < lowestParams.size(); index++ )
            {
                prcRegenParam = lowestParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades. for some currency pairs take bid and for others take offer side.
                if ( index % 2 == 0 )
                {
                    double offset = 1000;
                    double amt = getBidTierLimit() * 3 + offset;
                    Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After bid trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 bid limit : " + prcRegen1.getBidTierLimit( 0 ), prcRegen1.getBidTierLimit( 0 ), getBidTierLimit() );
                    assertEquals( "Tier2 bid limit : " + prcRegen1.getBidTierLimit( 1 ), prcRegen1.getBidTierLimit( 1 ), getBidTierLimit() * 2 - offset );
                    assertEquals( "Tier3 bid limit : " + prcRegen1.getBidTierLimit( 2 ), prcRegen1.getBidTierLimit( 2 ), 0.0 );
                }
                else
                {
                    double offset = 1000;
                    double amt = getOfferTierLimit() * 3 + offset;
                    Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After offer trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 offer limit : " + prcRegen1.getOfferTierLimit( 0 ), prcRegen1.getOfferTierLimit( 0 ), getOfferTierLimit() );
                    assertEquals( "Tier2 offer limit : " + prcRegen1.getOfferTierLimit( 1 ), prcRegen1.getOfferTierLimit( 1 ), getOfferTierLimit() * 2 - offset );
                    assertEquals( "Tier3 offer limit : " + prcRegen1.getOfferTierLimit( 2 ), prcRegen1.getOfferTierLimit( 2 ), 0.0 );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFromLowestTier" );
        }
    }

    public void testMultiQuotePriceRegenerationFromHighestTier()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "highestParams size : " + highestParams.size() );
            if ( numberOfTiers != 3 )
            {
                return;
            }
            for ( int index = 0; index < highestParams.size(); index++ )
            {
                prcRegenParam = highestParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades. for some currency pairs take bid and for others take offer side.
                if ( index % 2 == 0 )
                {
                    double offset = 2000;
                    double amt = getBidTierLimit() * 3 + offset;
                    Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After bid trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 bid limit : " + prcRegen1.getBidTierLimit( 0 ), prcRegen1.getBidTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 bid limit : " + prcRegen1.getBidTierLimit( 1 ), prcRegen1.getBidTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 bid limit : " + prcRegen1.getBidTierLimit( 2 ), prcRegen1.getBidTierLimit( 2 ), getBidTierLimit() * 3 - offset );
                }
                else
                {
                    double offset = 2000;
                    double amt = getOfferTierLimit() * 3 + offset;
                    Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After offer trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 offer limit : " + prcRegen1.getOfferTierLimit( 0 ), prcRegen1.getOfferTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 offer limit : " + prcRegen1.getOfferTierLimit( 1 ), prcRegen1.getOfferTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 offer limit : " + prcRegen1.getOfferTierLimit( 2 ), prcRegen1.getOfferTierLimit( 2 ), getOfferTierLimit() * 3 - offset );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFromHighestTier" );
        }
    }

    public void testMultiQuotePriceRegenerationFromLowestTierWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "lowestReplenishParams size : " + lowestReplenishParams.size() );
            if ( numberOfTiers != 3 )
            {
                return;
            }
            for ( int index = 0; index < lowestReplenishParams.size(); index++ )
            {
                prcRegenParam = lowestReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades. for some currency pairs take bid and for others take offer side.
                double offset = 2000;
                if ( index % 2 == 0 )
                {
                    double amt = getBidTierLimit() * 4 + offset;
                    Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After bid trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 bid limit : " + prcRegen1.getBidTierLimit( 0 ), prcRegen1.getBidTierLimit( 0 ), getBidTierLimit() );
                    assertEquals( "Tier2 bid limit : " + prcRegen1.getBidTierLimit( 1 ), prcRegen1.getBidTierLimit( 1 ), getBidTierLimit() - offset );
                    assertEquals( "Tier3 bid limit : " + prcRegen1.getBidTierLimit( 2 ), prcRegen1.getBidTierLimit( 2 ), 0.0 );
                }
                else
                {
                    double amt = getOfferTierLimit() * 4 + offset;
                    Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After offer trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "offer limit : " + prcRegen1.getOfferTierLimit( 0 ), prcRegen1.getOfferTierLimit( 0 ), getOfferTierLimit() );
                    assertEquals( "Tier2 offer limit : " + prcRegen1.getOfferTierLimit( 1 ), prcRegen1.getOfferTierLimit( 1 ), getOfferTierLimit() - offset );
                    assertEquals( "Tier3 offer limit : " + prcRegen1.getOfferTierLimit( 2 ), prcRegen1.getOfferTierLimit( 2 ), 0.0 );
                }

                sleepFor( 2200 );

                if ( index % 2 == 0 )
                {
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After 2 seconds of regen, prcRegen : " + prcRegen1 );
                    assertEquals( "After first regen, Tier1 bid limit : " + prcRegen1.getBidTierLimit( 0 ), prcRegen1.getBidTierLimit( 0 ), getBidTierLimit() );
                    assertEquals( "After first regen, Tier2 bid limit : " + prcRegen1.getBidTierLimit( 1 ), prcRegen1.getBidTierLimit( 1 ) > ( getBidTierLimit() - offset ), true );
                    assertEquals( "After first regen, Tier3 bid limit : " + prcRegen1.getBidTierLimit( 2 ), prcRegen1.getBidTierLimit( 2 ), 0.0 );
                }
                else
                {
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After 2 seconds of regen, prcRegen : " + prcRegen1 );
                    assertEquals( "After first regen, Tier1 offer limit : " + prcRegen1.getOfferTierLimit( 0 ), prcRegen1.getOfferTierLimit( 0 ), getOfferTierLimit() );
                    assertEquals( "After first regen, Tier2 offer limit : " + prcRegen1.getOfferTierLimit( 1 ), prcRegen1.getOfferTierLimit( 1 ) > ( getOfferTierLimit() - offset ), true );
                    assertEquals( "After first regen, Tier3 offer limit : " + prcRegen1.getOfferTierLimit( 2 ), prcRegen1.getOfferTierLimit( 2 ), 0.0 );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFromLowestTierWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationFromHighestTierWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "highestReplenishParams size : " + highestReplenishParams.size() );
            if ( numberOfTiers != 3 )
            {
                return;
            }
            for ( int index = 0; index < highestReplenishParams.size(); index++ )
            {
                prcRegenParam = highestReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades. for some currency pairs take bid and for others take offer side.
                double offset = 2000;
                if ( index % 2 == 0 )
                {
                    Trade trade = prepareTrade( getBidTierLimit() * 4 + offset, true, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After bid trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 bid limit : " + prcRegen1.getBidTierLimit( 0 ), prcRegen1.getBidTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 bid limit : " + prcRegen1.getBidTierLimit( 1 ), prcRegen1.getBidTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 bid limit : " + prcRegen1.getBidTierLimit( 2 ), prcRegen1.getBidTierLimit( 2 ), getBidTierLimit() * 2 - offset );
                }
                else
                {
                    Trade trade = prepareTrade( getOfferTierLimit() * 4 + offset, false, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After offer trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 offer limit : " + prcRegen1.getOfferTierLimit( 0 ), prcRegen1.getOfferTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 offer limit : " + prcRegen1.getOfferTierLimit( 1 ), prcRegen1.getOfferTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 offer limit : " + prcRegen1.getOfferTierLimit( 2 ), prcRegen1.getOfferTierLimit( 2 ), getOfferTierLimit() * 2 - offset );
                }

                sleepFor( 2200 );

                if ( index % 2 == 0 )
                {
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After 2 seconds of regen, prcRegen : " + prcRegen1 );
                    assertEquals( "After first regen, Tier1 bid limit : " + prcRegen1.getBidTierLimit( 0 ), prcRegen1.getBidTierLimit( 0 ), 0.0 );
                    assertEquals( "After first regen, Tier2 bid limit : " + prcRegen1.getBidTierLimit( 1 ), prcRegen1.getBidTierLimit( 1 ), 0.0 );
                    assertEquals( "After first regen, Tier3 bid limit : " + prcRegen1.getBidTierLimit( 2 ), prcRegen1.getBidTierLimit( 2 ) > getBidTierLimit() * 2 - offset, true );
                }
                else
                {
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After 2 seconds of regen, prcRegen : " + prcRegen1 );
                    assertEquals( "After first regen, Tier1 offer limit : " + prcRegen1.getOfferTierLimit( 0 ), prcRegen1.getOfferTierLimit( 0 ), 0.0 );
                    assertEquals( "After first regen, Tier2 offer limit : " + prcRegen1.getOfferTierLimit( 1 ), prcRegen1.getOfferTierLimit( 1 ), 0.0 );
                    assertEquals( "After first regen, Tier3 offer limit : " + prcRegen1.getOfferTierLimit( 2 ), prcRegen1.getOfferTierLimit( 2 ) > getOfferTierLimit() * 2 - offset, true );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFromHighestTierWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationFromLowestTierWithoutReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "lowestNoReplenishParams size : " + lowestNoReplenishParams.size() );
            if ( numberOfTiers != 3 )
            {
                return;
            }
            for ( int index = 0; index < lowestNoReplenishParams.size(); index++ )
            {
                prcRegenParam = lowestNoReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades. for some currency pairs take bid and for others take offer side.
                double offset = 3000;
                if ( index % 2 == 0 )
                {
                    double amt = getBidTierLimit() * 3 + offset;
                    Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After bid trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 bid limit : " + prcRegen1.getBidTierLimit( 0 ), prcRegen1.getBidTierLimit( 0 ), getBidTierLimit() );
                    assertEquals( "Tier2 bid limit : " + prcRegen1.getBidTierLimit( 1 ), prcRegen1.getBidTierLimit( 1 ), getBidTierLimit() * 2 - offset );
                    assertEquals( "Tier3 bid limit : " + prcRegen1.getBidTierLimit( 2 ), prcRegen1.getBidTierLimit( 2 ), 0.0 );

                    Trade trade2 = prepareTrade( getBidTierLimit(), true, ccyPair, org1, org1Tp1 );
                    boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                    PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                    log( "After  second bid trade, prcRegen : " + prcRegen2 );
                    assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                    assertEquals( "Tier1 bid limit : " + prcRegen2.getBidTierLimit( 0 ), prcRegen2.getBidTierLimit( 0 ), getBidTierLimit() );
                    assertEquals( "Tier2 bid limit : " + prcRegen2.getBidTierLimit( 1 ), prcRegen2.getBidTierLimit( 1 ), getBidTierLimit() - offset );
                    assertEquals( "Tier3 bid limit : " + prcRegen2.getBidTierLimit( 2 ), prcRegen2.getBidTierLimit( 2 ), 0.0 );

                    // now decline the first trade.
                    svc.stopPriceRegeneration( trade1, testHandler );
                    PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                    log( "After declining first bid trade, prcRegen : " + prcRegen3 );
                    assertEquals( "Tier1 bid limit : " + prcRegen3.getBidTierLimit( 0 ), prcRegen3.getBidTierLimit( 0 ), getBidTierLimit() );
                    assertEquals( "Tier2 bid limit : " + prcRegen3.getBidTierLimit( 1 ), prcRegen3.getBidTierLimit( 1 ), getBidTierLimit() - offset );
                    assertEquals( "Tier3 bid limit : " + prcRegen3.getBidTierLimit( 2 ), prcRegen3.getBidTierLimit( 2 ), 0.0 );
                }
                else
                {
                    double amt = getOfferTierLimit() * 3 + offset;
                    Trade trade1 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After offer trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 offer limit : " + prcRegen1.getOfferTierLimit( 0 ), prcRegen1.getOfferTierLimit( 0 ), getOfferTierLimit() );
                    assertEquals( "Tier2 offer limit : " + prcRegen1.getOfferTierLimit( 1 ), prcRegen1.getOfferTierLimit( 1 ), getOfferTierLimit() * 2 - offset );
                    assertEquals( "Tier3 offer limit : " + prcRegen1.getOfferTierLimit( 2 ), prcRegen1.getOfferTierLimit( 2 ), 0.0 );

                    Trade trade2 = prepareTrade( getOfferTierLimit(), false, ccyPair, org1, org1Tp1 );
                    boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                    PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                    log( "After  second offer trade, prcRegen : " + prcRegen2 );
                    assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                    assertEquals( "Tier1 offer limit : " + prcRegen2.getOfferTierLimit( 0 ), prcRegen2.getOfferTierLimit( 0 ), getOfferTierLimit() );
                    assertEquals( "Tier2 offer limit : " + prcRegen2.getOfferTierLimit( 1 ), prcRegen2.getOfferTierLimit( 1 ), getOfferTierLimit() - offset );
                    assertEquals( "Tier3 offer limit : " + prcRegen2.getOfferTierLimit( 2 ), prcRegen2.getOfferTierLimit( 2 ), 0.0 );

                    // now decline the first trade.
                    svc.stopPriceRegeneration( trade1, testHandler );
                    PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                    log( "After declining first offer trade, prcRegen : " + prcRegen3 );
                    assertEquals( "Tier1 offer limit : " + prcRegen3.getOfferTierLimit( 0 ), prcRegen3.getOfferTierLimit( 0 ), getOfferTierLimit() );
                    assertEquals( "Tier2 offer limit : " + prcRegen3.getOfferTierLimit( 1 ), prcRegen3.getOfferTierLimit( 1 ), getOfferTierLimit() - offset  );
                    assertEquals( "Tier3 offer limit : " + prcRegen3.getOfferTierLimit( 2 ), prcRegen3.getOfferTierLimit( 2 ), 0.0 );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFromLowestTierWithoutReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationFromHighestTierWithoutReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "highestNoReplenishParams size : " + highestNoReplenishParams.size() );
            if ( numberOfTiers != 3 )
            {
                return;
            }
            for ( int index = 0; index < highestNoReplenishParams.size(); index++ )
            {
                prcRegenParam = highestNoReplenishParams.get( index );
                log( "testPriceRegenerationFromHighestTierWithoutReplenishment - prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades. for some currency pairs take bid and for others take offer side.
                double offset = getBidTierLimit() - 2000;
                if ( index % 2 == 0 )
                {
                    double amt = getBidTierLimit() * 3 + offset;
                    Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After bid trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 bid limit : " + prcRegen1.getBidTierLimit( 0 ), prcRegen1.getBidTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 bid limit : " + prcRegen1.getBidTierLimit( 1 ), prcRegen1.getBidTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 bid limit : " + prcRegen1.getBidTierLimit( 2 ), prcRegen1.getBidTierLimit( 2 ), getBidTierLimit() * 3 - offset );

                    Trade trade2 = prepareTrade( getBidTierLimit(), true, ccyPair, org1, org1Tp1 );
                    boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                    PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                    log( "After  second bid trade, prcRegen : " + prcRegen2 );
                    assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                    assertEquals( "Tier1 bid limit : " + prcRegen2.getBidTierLimit( 0 ), prcRegen2.getBidTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 bid limit : " + prcRegen2.getBidTierLimit( 1 ), prcRegen2.getBidTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 bid limit : " + prcRegen2.getBidTierLimit( 2 ), prcRegen2.getBidTierLimit( 2 ), getBidTierLimit() * 2 - offset );

                    // now decline the first trade.
                    svc.stopPriceRegeneration( trade1, testHandler );
                    PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                    log( "After declining first bid trade, prcRegen : " + prcRegen3 );
                    assertEquals( "Tier1 bid limit : " + prcRegen3.getBidTierLimit( 0 ), prcRegen3.getBidTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 bid limit : " + prcRegen3.getBidTierLimit( 1 ), prcRegen3.getBidTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 bid limit : " + prcRegen3.getBidTierLimit( 2 ), prcRegen3.getBidTierLimit( 2 ), getBidTierLimit() * 2 - offset );
                }
                else
                {
                    double amt = getOfferTierLimit() * 3 + offset;
                    Trade trade1 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                    boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                    PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                    log( "After offer trade, prcRegen : " + prcRegen1 );
                    assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                    assertEquals( "Tier1 offer limit : " + prcRegen1.getOfferTierLimit( 0 ), prcRegen1.getOfferTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 offer limit : " + prcRegen1.getOfferTierLimit( 1 ), prcRegen1.getOfferTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 offer limit : " + prcRegen1.getOfferTierLimit( 2 ), prcRegen1.getOfferTierLimit( 2 ), getOfferTierLimit() * 3 - offset );

                    Trade trade2 = prepareTrade( getOfferTierLimit(), false, ccyPair, org1, org1Tp1 );
                    boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                    PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                    log( "After  second offer trade, prcRegen : " + prcRegen2 );
                    assertEquals( "TakenTradeLimit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                    assertEquals( "Tier1 offer limit : " + prcRegen2.getOfferTierLimit( 0 ), prcRegen2.getOfferTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 offer limit : " + prcRegen2.getOfferTierLimit( 1 ), prcRegen2.getOfferTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 offer limit : " + prcRegen2.getOfferTierLimit( 2 ), prcRegen2.getOfferTierLimit( 2 ), getOfferTierLimit() * 2 - offset );

                    // now decline the first trade.
                    svc.stopPriceRegeneration( trade1, testHandler );
                    PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                    log( "After declining first offer trade, prcRegen : " + prcRegen3 );
                    assertEquals( "Tier1 offer limit : " + prcRegen3.getOfferTierLimit( 0 ), prcRegen3.getOfferTierLimit( 0 ), 0.0 );
                    assertEquals( "Tier2 offer limit : " + prcRegen3.getOfferTierLimit( 1 ), prcRegen3.getOfferTierLimit( 1 ), 0.0 );
                    assertEquals( "Tier3 offer limit : " + prcRegen3.getOfferTierLimit( 2 ), prcRegen3.getOfferTierLimit( 2 ), getOfferTierLimit() * 2 - offset );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFromHighestTierWithoutReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationFixedAmount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "testPriceRegenerationFixedAmount - nettingRegenParams size : " + fixedAmountReplenishParams.size() );
            for ( int index = 0; index < fixedAmountReplenishParams.size(); index++ )
            {
                prcRegenParam = fixedAmountReplenishParams.get( index );
                log( "testPriceRegenerationFixedAmount - prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double amt1 = getTradingLimit() - 1000;
                double tradingLimit = isBid ? getBidLimit() : getOfferLimit();
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );


                sleepFor( 2050 );

                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After 2 seconds : " + prcRegen2 );
                double limit = isBid ? prcRegen2.getBidLimit() : prcRegen2.getOfferLimit();
                assertEquals( "limit : " + limit, limit, tradingLimit + amount - amt1 );

                sleepFor( 2050 );

                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                limit = isBid ? prcRegen3.getBidLimit() : prcRegen3.getOfferLimit();
                log( "After 2 more seconds : " + prcRegen3 );
                assertEquals( "limit : " + limit, limit, tradingLimit + ( amount * 2 ) - amt1 );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFixedAmount" );
        }
    }

    public void testMultiQuotePriceRegenerationFixedTime()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "fixedTimeReplenishParams size : " + fixedTimeReplenishParams.size() );
            for ( int index = 0; index < fixedTimeReplenishParams.size(); index++ )
            {
                prcRegenParam = fixedTimeReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double tradingLimit = isBid ? getBidLimit() : getOfferLimit();
                double amt1 = getTradingLimit() - 1000;
                double replenishAmt = amt1 / ( prcRegenParam.getInterval() / facade.getPeriodicFixedTimeRegenerationFrequency() );
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );


                sleepFor( 2050 );

                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After 2 seconds : " + prcRegen2 );
                double limit = isBid ? prcRegen2.getBidLimit() : prcRegen2.getOfferLimit();
                assertEquals( "limit : " + limit + " prcRegenParam : " + prcRegenParam.getDisplayKey() + " replenishAmt : " + replenishAmt, limit, tradingLimit + replenishAmt - amt1 );

                sleepFor( 2050 );

                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                limit = isBid ? prcRegen3.getBidLimit() : prcRegen3.getOfferLimit();
                log( "After 2 more seconds : " + prcRegen3 );
                assertEquals( "limit : " + limit, limit, tradingLimit + ( replenishAmt * 2 ) - amt1 );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFixedTime" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitIncreaseNettingInterval()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "nettingReplenishParams size : " + nettingReplenishParams.size() );
            for ( int index = 0; index < nettingReplenishParams.size(); index++ )
            {
                prcRegenParam = nettingReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedAmountRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() + 2000;
                double newOfferTierLimit = getOfferTierLimit() + 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( ( regenCount - initialRegenDone ) * frequency * 1000 + 150 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitIncreaseNettingInterval" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitDecreaseNettingInterval()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "nettingReplenishParams size : " + nettingReplenishParams.size() );
            for ( int index = 0; index < nettingReplenishParams.size(); index++ )
            {
                prcRegenParam = nettingReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedAmountRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() - 2000;
                double newOfferTierLimit = getOfferTierLimit() - 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( ( regenCount - initialRegenDone ) * frequency * 1000 + 150 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitDecreaseNettingInterval" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitIncreaseNettingAmount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "nettingReplenishParams size : " + nettingReplenishParams.size() );
            for ( int index = 0; index < nettingReplenishParams.size(); index++ )
            {
                prcRegenParam = nettingReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedTimeRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() + 2000;
                double newOfferTierLimit = getOfferTierLimit() + 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( 20000 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitIncreaseNettingAmount" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitDecreaseNettingAmount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "nettingReplenishParams size : " + nettingReplenishParams.size() );
            for ( int index = 0; index < nettingReplenishParams.size(); index++ )
            {
                prcRegenParam = nettingReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedTimeRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() - 2000;
                double newOfferTierLimit = getOfferTierLimit() - 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( 20000 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitDecreaseNettingAmount" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitIncreaseNoNettingInterval()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "noNettingReplenishParams size : " + noNettingReplenishParams.size() );
            for ( int index = 0; index < noNettingReplenishParams.size(); index++ )
            {
                prcRegenParam = noNettingReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedAmountRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() + 2000;
                double newOfferTierLimit = getOfferTierLimit() + 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( ( regenCount - initialRegenDone ) * frequency * 1000 + 150 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitIncreaseNoNettingInterval" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitDecreaseNoNettingInterval()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "noNettingReplenishParams size : " + noNettingReplenishParams.size() );
            for ( int index = 0; index < noNettingReplenishParams.size(); index++ )
            {
                prcRegenParam = noNettingReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedAmountRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() - 2000;
                double newOfferTierLimit = getOfferTierLimit() - 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( ( regenCount - initialRegenDone ) * frequency * 1000 + 150 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitDecreaseNoNettingInterval" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitIncreaseNoNettingAmount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "noNettingReplenishParams size : " + noNettingReplenishParams.size() );
            for ( int index = 0; index < noNettingReplenishParams.size(); index++ )
            {
                prcRegenParam = noNettingReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedTimeRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() + 2000;
                double newOfferTierLimit = getOfferTierLimit() + 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( 20000 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitIncreaseNoNettingAmount" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitDecreaseNoNettingAmount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "noNettingReplenishParams size : " + noNettingReplenishParams.size() );
            for ( int index = 0; index < noNettingReplenishParams.size(); index++ )
            {
                prcRegenParam = noNettingReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedTimeRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() - 2000;
                double newOfferTierLimit = getOfferTierLimit() - 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( 20000 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitDecreaseNoNettingAmount" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitIncreaseIndependentInterval()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "independentReplenishParams size : " + independentReplenishParams.size() );
            for ( int index = 0; index < independentReplenishParams.size(); index++ )
            {
                prcRegenParam = independentReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedAmountRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() + 2000;
                double newOfferTierLimit = getOfferTierLimit() + 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( ( regenCount - initialRegenDone ) * frequency * 1000 + 150 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitIncreaseIndependentInterval" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitDecreaseIndependentInterval()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "independentReplenishParams size : " + independentReplenishParams.size() );
            for ( int index = 0; index < independentReplenishParams.size(); index++ )
            {
                prcRegenParam = independentReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedAmountRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() - 2000;
                double newOfferTierLimit = getOfferTierLimit() - 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( ( regenCount - initialRegenDone ) * frequency * 1000 + 150 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitDecreaseIndependentInterval" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitIncreaseIndependentAmount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "independentReplenishParams size : " + independentReplenishParams.size() );
            for ( int index = 0; index < independentReplenishParams.size(); index++ )
            {
                prcRegenParam = independentReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedTimeRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() + 2000;
                double newOfferTierLimit = getOfferTierLimit() + 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( 20000 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitIncreaseIndependentAmount" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitDecreaseIndependentAmount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "independentReplenishParams size : " + independentReplenishParams.size() );
            for ( int index = 0; index < independentReplenishParams.size(); index++ )
            {
                prcRegenParam = independentReplenishParams.get( index );
                PriceRegenerationParametersFacade facade = ( PriceRegenerationParametersFacade ) prcRegenParam.getFacade( PriceRegenerationParametersFacade.FACADE_NAME );
                if ( facade.isPeriodicFixedTimeRegeneration() )
                {
                    continue;
                }
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                double offset = 2000;
                double amt1 = numberOfTiers > 1 ? isBid ? getBidTierLimit() + offset : getOfferTierLimit() + offset : isBid ? getBidTierLimit() - offset : getOfferTierLimit() - offset;
                Trade trade = prepareTrade( amt1, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // allow two regenerations to happen.

                int regenCount = interval / frequency;
                int initialRegenDone = regenCount - ( regenCount / 2 );
                sleepFor( initialRegenDone * frequency * 1000 + 100 );

                double newBidTierLimit = getBidTierLimit() - 2000;
                double newOfferTierLimit = getOfferTierLimit() - 2000;
                svc.applyPriceRegenerationRules( prepareQuote( org1, ccyPair, newBidTierLimit, newOfferTierLimit ), org1Tp1, isMultiTier() );

                sleepFor( 20000 );

                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                double availBidLimit = prcRegenEvent.getAvailableTradingLimits().getBidLimit();
                double availOfferLimit = prcRegenEvent.getAvailableTradingLimits().getOfferLimit();
                log( "After full regeneration. event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );

                // now decline the original trade.
                svc.stopPriceRegeneration( trade, testHandler );
                log( "After declining the original trade. Event : " + prcRegenEvent );
                assertEquals( "availBidLimit " + availBidLimit, availBidLimit, getLimit( newBidTierLimit ) );
                assertEquals( "availOfferLimit " + availOfferLimit, availOfferLimit, getLimit( newOfferTierLimit ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitDecreaseIndependentAmount" );
        }
    }

    public void testMultiQuotePriceRegenerationWithGreaterAmount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "Inside testMultiQuotePriceRegenerationWithGreaterAmount" );
            prcRegenParam = preparePriceRegenerationParameters( "test", replenish, lowest, indepedent, 0, frequency, 10000000 );
            String ccyPair = currencyPairs[0];
            org1.setPriceRegenerationParameters( prcRegenParam );

            Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
            svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

            TestRegenerationHandler testHandler = new TestRegenerationHandler();
            testHandler.setPriceRegenerationParameters( prcRegenParam );

            // now start taking trades.
            double offset = 2000000;
            double amt1 = numberOfTiers > 1 ? getBidTierLimit() + offset : getBidTierLimit() - offset;
            Trade trade = prepareTrade( amt1, true, ccyPair, org1, org1Tp1 );
            boolean result1 = svc.startPriceRegeneration( trade, testHandler );
            assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
            PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
            assertEquals( "Check regen count. It should be at least one. " + prcRegen1.getPendingRegenerations(), new Integer( 1 ), new Integer( prcRegen1.getPendingRegenerations() ) );

            sleepFor( 20000 );

            PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
            log( "prcRegen after 20 seconds. " + prcRegen2 );
            assertEquals( "availBidLimit " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), getBidLimit() );
            assertEquals( "availOfferLimit " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), getOfferLimit() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithGreaterAmount" );
        }
    }

    public void testMultiQuotePriceRegenerationPendingRegenerationsCountWithoutReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "nettingRegenParams size : " + noNettingRegenParams.size() );
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getBidTierLimit(), true, ccyPair, org1, org1Tp1 );
                svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "Pending Regenerations count should be zero.  " + prcRegen1.getPendingRegenerations(), new Integer( 0 ), new Integer( prcRegen1.getPendingRegenerations() ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationPendingRegenerationsCountWithoutReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationFixedAmountPendingRegenerationsCount()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        double[] regenAmounts = new double[]{1122.34, 2300.56, 3000, 4000, 7878.98};
        double[] replenishAmounts = new double[]{3456.66, 4567.77, 3000.02, 4000.01, 4678.90};
        try
        {
            for ( int index = 0; index < regenAmounts.length; index++ )
            {
                log( "Inside testMultiQuotePriceRegenerationFixedAmountPendingRegenerationsCount" );
                double regenAmt = regenAmounts[index];
                double replenishAmt = replenishAmounts[index];
                prcRegenParam = preparePriceRegenerationParameters( "test", replenish, lowest, indepedent, 0, frequency, regenAmt );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                int regenCount = ( int ) Math.ceil( ( replenishAmt - PriceRegenerationConstants.MINIMUM ) / regenAmt );
                log( "Calculated regen count : " + regenCount + " replenishAmt : " + replenishAmt + " regenAmt : " + regenAmt );
                Trade trade = prepareTrade( replenishAmt, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                assertEquals( "Check regen count. " + prcRegen1.getPendingRegenerations(), new Integer( regenCount ), new Integer( prcRegen1.getPendingRegenerations() ) );

                sleepFor( regenCount * frequency * 1000 + 100 );

                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "prcRegen after 20 seconds. " + prcRegen2 );
                assertEquals( "availBidLimit " + prcRegen2.getBidLimit(), true, prcRegen2.getBidLimit() - getBidLimit() <= PriceRegenerationConstants.MINIMUM );
                assertEquals( "availOfferLimit " + prcRegen2.getOfferLimit(), true, prcRegen2.getOfferLimit() - getOfferLimit() <= PriceRegenerationConstants.MINIMUM );
                assertEquals( "Check regen count. " + prcRegen2.getPendingRegenerations(), new Integer( 0 ), new Integer( prcRegen2.getPendingRegenerations() ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFixedAmountPendingRegenerationsCount" );
        }
    }

    public void testMultiQuotePriceRegenerationEventCache()
    {
        init();
        try
        {
            int size = currencyPairs.length;

            for ( int index = 0; index < size; index++ )
            {

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( noReplenishHighestNettingInterval );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );
            }

            for ( int index = 0; index < size; index++ )
            {

                String ccyPair = currencyPairs[index];
                org2.setPriceRegenerationParameters( noReplenishHighestNettingAmt );

                Quote newQuote = prepareQuote( org2, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org2Tp1, isMultiTier() );
            }

            Collection providers = PriceRegenerationEventCacheC.getInstance().getProviders();
            log( "providers : " + providers );
            assertEquals( "Number of providers", new Integer( 2 ), new Integer( providers.size() ) );

            Collection tradingParties = PriceRegenerationEventCacheC.getInstance().getTradingParties( org1.getShortName() );
            log( "trading parties : " + tradingParties );
            assertEquals( "trading parties", new Integer( 1 ), new Integer( tradingParties.size() ) );

            Collection org1CcyPairs = PriceRegenerationEventCacheC.getInstance().getCurrencyPairs( org1.getShortName(), org1Tp1.getShortName() );
            Collection org2CcyPairs = PriceRegenerationEventCacheC.getInstance().getCurrencyPairs( org2.getShortName(), org2Tp1.getShortName() );
            log( "org1CcyPairs : " + org1CcyPairs + " org2CcyPairs : " + org2CcyPairs );
            assertEquals( "Number of org1CcyPairs", new Integer( size ), new Integer( org1CcyPairs.size() ) );
            assertEquals( "Number of org2CcyPairs", new Integer( size ), new Integer( org2CcyPairs.size() ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationEventCache" );
        }
    }

    public void testMultiQuotePriceRegenerationSingleTierNoNettingWithoutReplenishment()
    {
        init();
        setNumberOfTiers( 1 );
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "noNettingNoReplenishParams size : " + noNettingNoReplenishParams.size() );
            for ( int index = 0; index < noNettingNoReplenishParams.size(); index++ )
            {
                prcRegenParam = noNettingNoReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                double limit = 10000000;
                Quote newQuote = prepareQuote( org1, ccyPair, limit, limit );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                double amt1 = 4000000;
                Trade trade = prepareTrade( amt1, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Bid limit : " + prcRegen1.getBidLimit(), prcRegen1.getBidLimit(), limit - amt1 );
                assertEquals( "Offer limit : " + prcRegen1.getOfferLimit(), prcRegen1.getOfferLimit(), limit - amt1 );

                double amt2 = 2000000;
                boolean result2 = svc.startPriceRegeneration( prepareTrade( amt2, true, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradeLimit on bid side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "Bid limit : " + prcRegen2.getBidLimit(), prcRegen2.getBidLimit(), limit - amt1 - amt2 );
                assertEquals( "Offer limit : " + prcRegen2.getOfferLimit(), prcRegen2.getOfferLimit(), limit - amt1 - amt2 );

                double amt3 = 3000000;
                boolean result3 = svc.startPriceRegeneration( prepareTrade( amt3, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen3 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen3 );
                assertEquals( "Succeeded taking trading limit on offer side. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );
                assertEquals( "Bid limit : " + prcRegen3.getBidLimit(), prcRegen3.getBidLimit(), limit - ( amt1 + amt2 + amt3 ) );
                assertEquals( "Offer limit : " + prcRegen3.getOfferLimit(), prcRegen3.getOfferLimit(), limit - ( amt1 + amt2 + amt3 ) );

                // now decline the first trade.
                svc.stopPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen4 = testHandler.getPriceRegeneration();
                log( "After decline trade, prcRegen : " + prcRegen4 );
                assertEquals( "Bid limit : " + prcRegen4.getBidLimit(), prcRegen4.getBidLimit(), limit - ( amt1 + amt2 + amt3 ) );
                assertEquals( "Offer limit : " + prcRegen4.getOfferLimit(), prcRegen4.getOfferLimit(), limit - ( amt1 + amt2 + amt3 ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationSingleTierNoNettingWithoutReplenishment" );
        }
        setNumberOfTiers( TIER_COUNT );
    }

    public void testMultiQuotePriceRegenerationWithReplenishment_HandlerInvocationOnLowerNewTradingLimit()
    {
        init();
        setNumberOfTiers( 3 );
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < replenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) replenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 2000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "Handler invocations. " + testHandler.getHandlerInvocations(), new Integer( testHandler.getHandlerInvocations() ), new Integer( 1 ) );
                assertEquals( "Handler pending regens. " + testHandler.getPriceRegeneration().getPendingRegenerations(), testHandler.getPriceRegeneration().getPendingRegenerations() > 0, true );

                // now a new quote with lower trading limit arrives.
                Quote quote1 = prepareQuote( org1, ccyPair, 3000, 3000 );
                svc.applyPriceRegenerationRules( quote1, org1Tp1, isMultiTier() );
                assertEquals( "Handler pending regens. " + testHandler.getPriceRegeneration().getPendingRegenerations(), testHandler.getPriceRegeneration().getPendingRegenerations() > 0, true );
                assertEquals( "Handler invocations. " + testHandler.getHandlerInvocations(), new Integer( testHandler.getHandlerInvocations() ), new Integer( 1 ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithReplenishment_HandlerInvocationOnLowerNewTradingLimit" );
        }
        setNumberOfTiers( TIER_COUNT );
    }

    public void testMultiQuotePriceRegenerationWithReplenishment_TierChangeDuringPriceRegeneration()
    {
        init();
        setNumberOfTiers( 3 );
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < replenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) replenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // now a new quote with lower trading limit arrives. this time with 2 tiers.
                setNumberOfTiers( 2 );
                Quote quote1 = prepareQuote( org1, ccyPair, 4000, 4000 );
                svc.applyPriceRegenerationRules( quote1, org1Tp1, isMultiTier() );
                PriceRegenerationEvent prcRegenEvent1 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                assertEquals( "event should have exact number of bid tiers in trading and available.", prcRegenEvent1.getTradingLimits().getBidTiers().length, prcRegenEvent1.getAvailableTradingLimits().getBidTiers().length );
                assertEquals( "event should have exact number of offer tiers in trading and available.", prcRegenEvent1.getTradingLimits().getOfferTiers().length, prcRegenEvent1.getAvailableTradingLimits().getOfferTiers().length );

                // now a new quote with higher trading limit arrives. this time with 2 tiers.
                setNumberOfTiers( 4 );
                Quote quote2 = prepareQuote( org1, ccyPair, 3000, 3000 );
                svc.applyPriceRegenerationRules( quote2, org1Tp1, isMultiTier() );
                PriceRegenerationEvent prcRegenEvent2 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                assertEquals( "event should have exact number of bid tiers in trading and available.", prcRegenEvent2.getTradingLimits().getBidTiers().length, prcRegenEvent2.getAvailableTradingLimits().getBidTiers().length );
                assertEquals( "event should have exact number of offer tiers in trading and available.", prcRegenEvent2.getTradingLimits().getOfferTiers().length, prcRegenEvent2.getAvailableTradingLimits().getOfferTiers().length );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithReplenishment_TierChangeDuringPriceRegeneration" );
        }
        setNumberOfTiers( TIER_COUNT );
    }

    public void testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimit()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
        	Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
        	regenParams.addAll(replenishRegenParams);
        	regenParams.addAll(noReplenishRegenParams);
        	
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 5000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should not show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), true, prcRegen1.isRapidFirePeriod() );
                assertEquals( "PriceRegen service should not show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true, svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ));
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                sleepFor(200);
                // now try taking another trade. It should fail since rapidFirePeriod is on                
                Trade trade2 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler);
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), false, result2);
                
                
                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), true, testHandler.getHandlerInvocations() < 2);
                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should not show in rapid fire period. " + prcRegen2.isRapidFirePeriod(), false, prcRegen2.isRapidFirePeriod() );
                assertEquals( "PriceRegen service should not show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false, svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ) );
                assertEquals( "Handler invocations should be 2. " + testHandler.getHandlerInvocations(), 2, testHandler.getHandlerInvocations());
                
                // now try taking another trade. It should again go through fine, since rapidFirePeriod has ended                
                Trade trade3 = prepareTrade( 1000, true, ccyPair, org1, org1Tp1 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler);
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), true, result3);

            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimit" );
        }
    }

    public void testMultiQuotePriceRegenerationWithRapidFireUndoTakingTradingLimit()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
        	Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
        	regenParams.addAll(replenishRegenParams);
        	regenParams.addAll(noReplenishRegenParams);
        	
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 5000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000000, 5000000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service should show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now reject the trade.
                svc.stopPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "Undo Taking Trading limit. prcRegen=" + prcRegen2 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "PriceRegen should not show in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), false );
                assertEquals( "PriceRegen service should not show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );
                assertEquals( "Handler invocations should not be 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() == 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithRapidFireUndoTakingTradingLimit" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters with replenishment. First, trading limit
     * for tiers registered and full trading limit it taken by a trade. Then after waiting for 2 seconds, a small amount trade
     * is done to see whether regeneration took place. Then, a new quote with the changed limits makes sure that
     * the quote is modified for the new limits.
     */
    public void testMultiTierPriceRegenerationWithReplenishmentWithTradingLimitChange()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < replenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) replenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp1 );
                Quote quote = prepareQuote( org1, ccyPair, getBidTierLimit() + 10000, getOfferTierLimit() + 10000 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                svc.applyPriceRegenerationRules( quote, org1Tp1, isMultiTier() );
                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                PriceRegeneration prcRegen = preparePriceRegeneration( prcRegenEvent );
                assertEquals( "Check quote with price regeneration values.", checkQuoteAfterApplyingRegeneratedLimits( quote, prcRegen ), true );

                sleepFor( 2100 );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( 1000000, true, ccyPair, org1, org1Tp1 ), testHandler );
                assertEquals( "Able take trading limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                Quote qot = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( qot, org1Tp1, isMultiTier() );

                boolean result3 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "Next quote comes with the same limits. Should fail now. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );
            }

            sleepFor( 20000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "testMultiTierPriceRegenerationWithReplenishment - handler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "testMultiTierPriceRegenerationWithReplenishment - Handler Invocation should be less than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 3, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiTierPriceRegenerationWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationTradingLimitChangeWithRoundingDuringReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        setNumberOfTiers( 1 );
        try
        {
            log( "nettingReplenishParams size : " + nettingReplenishParams.size() );
            for ( int index = 0; index < nettingReplenishParams.size(); index++ )
            {
                prcRegenParam = nettingReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                double tierLimit = 5123434;
                int roundingFactor = PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean().getTierLimitRoundingFactor();
                double roundedTier = Math.round( tierLimit / MathUtil.pow10( roundingFactor ) ) * MathUtil.pow10( roundingFactor );
                log( "RoundedTier = " + roundedTier );
                double tierLimitChange = tierLimit - roundedTier - 1000;
                double newTierLimit = tierLimit + tierLimitChange;
                Quote newQuote = prepareQuote( org1, ccyPair, tierLimit, tierLimit );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                Trade trade = prepareTrade( tierLimit / 2, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                log( "new quote tier = " + newTierLimit );
                Quote newQuote1 = prepareQuote( org1, ccyPair, newTierLimit, newTierLimit );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );
                PriceRegenerationEvent event = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                log( "New quote limits within rounding limits event : " + event );
                assertEquals( "Event limits should be unchanged.", tierLimit, event.getTradingLimits().getBidTierLimit( 0 ) );

                double newTierLimit2 = tierLimit + MathUtil.pow10( roundingFactor );
                log( "new quote tier = " + newTierLimit2 );
                Quote newQuote2 = prepareQuote( org1, ccyPair, newTierLimit2, newTierLimit2 );
                svc.applyPriceRegenerationRules( newQuote2, org1Tp1, isMultiTier() );
                PriceRegenerationEvent event2 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                log( "New quote limits within rounding limits event : " + event2 );
                assertEquals( "Event limits should changed.", newTierLimit2, event2.getTradingLimits().getBidTierLimit( 0 ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitChangeWithRoundingDuringReplenishment" );
        }
        setNumberOfTiers( TIER_COUNT );
    }

    public void testMultiQuotePriceRegenerationFrequentTradingLimitChangeWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            prcRegenParam = preparePriceRegenerationParameters( "replenishLowestNoNettingAmt", replenish, lowest, noNetting, 0, frequency, 1000000 );
            log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
            String ccyPair = currencyPairs[0];
            org1.setPriceRegenerationParameters( prcRegenParam );

            double tierLimit = 5123434;
            Quote newQuote = prepareQuote( org1, ccyPair, tierLimit, tierLimit );
            svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

            TestRegenerationHandler testHandler = new TestRegenerationHandler();
            testHandler.setPriceRegenerationParameters( prcRegenParam );

            // now start taking trades.
            Trade trade = prepareTrade( tierLimit / 2, true, ccyPair, org1, org1Tp1 );
            boolean result1 = svc.startPriceRegeneration( trade, testHandler );
            PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
            log( "After trade, prcRegen : " + prcRegen1 );
            assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
            int roudingFactor = PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean().getTierLimitRoundingFactor();
            double newTierLimit = tierLimit + MathUtil.pow10( roudingFactor );

            for ( int i = 0; i < 10; i++ )
            {
                sleepFor( 300 );
                log( "new quote tier = " + newTierLimit );
                Quote newQuote1 = prepareQuote( org1, ccyPair, newTierLimit, newTierLimit );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );
                PriceRegenerationEvent event = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                log( "New quote limits within rounding limits event : " + event );
                assertEquals( "Event limits should changed.", newTierLimit, event.getTradingLimits().getBidTierLimit( 0 ) );
                newTierLimit += MathUtil.pow10( roudingFactor );
            }

            // check to see if price regeneration was carried out in the mean time by checking the handler invocations.
            assertEquals( "Handler invocations." + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() > 1, true );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFrequentTradingLimitChangeWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationUnsupportedTradingLimitChangeWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            prcRegenParam = preparePriceRegenerationParameters( "replenishLowestNoNettingAmt", replenish, lowest, noNetting, 0, frequency, 1000000 );
            log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
            String ccyPair = currencyPairs[0];
            org1.setPriceRegenerationParameters( prcRegenParam );

            double tierLimit = 5123434;
            Quote newQuote = prepareQuote( org1, ccyPair, tierLimit, tierLimit );
            svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

            TestRegenerationHandler testHandler = new TestRegenerationHandler();
            testHandler.setPriceRegenerationParameters( prcRegenParam );

            // now start taking trades.
            Trade trade = prepareTrade( tierLimit / 2, true, ccyPair, org1, org1Tp1 );
            boolean result1 = svc.startPriceRegeneration( trade, testHandler );
            PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
            log( "After trade, prcRegen : " + prcRegen1 );
            log( "handler.invocations after trade =" + testHandler.getHandlerInvocations() );
            assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

            // now send an unsupported rate.
            Quote newQuote1 = prepareQuote( org1, ccyPair, 0.0, 0.0 );
            svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

            sleepFor( 300 );
            PriceRegenerationEvent event = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
            log( "New quote limits with unsupported quote : " + event );
            assertEquals( "Event limits should changed.", 0.0, event.getTradingLimits().getBidTierLimit( 0 ) );
            log( "handler.invocations after unsupported rates=" + testHandler.getHandlerInvocations() );

            // check to see if price regeneration was carried out in the mean time by checking the handler invocations.
            assertEquals( "Handler invocations." + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() == 1, true );

            // now send another valid quote
            Quote newQuote2 = prepareQuote( org1, ccyPair, tierLimit, tierLimit );
            svc.applyPriceRegenerationRules( newQuote2, org1Tp1, isMultiTier() );
            event = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
            log( "New quote limits with supported quote : " + event );
            assertEquals( "Event limits should changed.", tierLimit, event.getTradingLimits().getBidTierLimit( 0 ) );
            log( "handler.invocations after supported rates=" + testHandler.getHandlerInvocations() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationUnsupportedTradingLimitChangeWithReplenishment" );
        }
    }


    public void testMultiQuoteAcceptanceTolerance()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "testMultiQuoteAcceptanceTolerance - regenParams size : " + regenParams.size() );
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                log( "testMultiQuoteAcceptanceTolerance - prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades. First with an amount which is greater than the acceptance tolerance amount.
                Trade trade = prepareTrade( getTradingLimit() + acceptanceToleranceAmount + 10, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit should fail. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, false );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( getTradingLimit() + acceptanceToleranceAmount - 10, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradeLimit should succeed. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuoteAcceptanceTolerance" );
        }
    }

    public void testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitOnSameOrder()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
        	Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
        	regenParams.addAll(replenishRegenParams);
        	regenParams.addAll(noReplenishRegenParams);
        	
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 5000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                quote0.setRateId( "G100" );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request = new RequestC();
                request.setAcceptedQuote( quote0 );
                request.getToOrganizations().add( org1 );
                request.setOrderId( "1000" );
                request.setTrade( trade );
                trade.setRequest( request );
                Request order = new RequestC();
                order.addChildRequest( request );
                request.setParentOCORequest( order );
                order.setOrderId( "1000" );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now create another trade with the same order id.
                Trade trade1 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request1 = new RequestC();
                request1.setAcceptedQuote( quote0 );
                request1.getToOrganizations().add( org1 );
                request1.setOrderId( "1000" );
                request1.setTrade( trade1 );
                trade1.setRequest( request1 );
                order.addChildRequest( request1 );
                request1.setParentRequest( order );
                boolean result2 = svc.startPriceRegeneration( trade1, testHandler );
                PriceRegeneration prcRegenx = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegenx );
                assertEquals( "Should allow the trade. result2=" + result2, result2, true );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegenx.isRapidFirePeriod(), prcRegenx.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                // now create another trade which does not originate from the same order. this should get rejected.
                Trade differentTrade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request differentRequest = new RequestC();
                differentRequest.setAcceptedQuote( quote0 );
                differentRequest.getToOrganizations().add( org1 );
                differentRequest.setOrderId( "1001" );
                differentRequest.setTrade( differentTrade );
                differentTrade.setRequest( differentRequest );
                Request differentOrder = new RequestC();
                differentOrder.addChildRequest( differentRequest );
                differentRequest.setParentOCORequest( differentOrder );
                differentOrder.setOrderId( "1001" );
                boolean result3 = svc.startPriceRegeneration( differentTrade, testHandler );
                PriceRegeneration prcRegeny = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegeny );
                assertEquals( "Should not allow the trade. result3=" + result3, result3, false );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegeny.isRapidFirePeriod(), prcRegeny.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );
                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should not show in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), false );
                assertEquals( "PriceRegen service not show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );
                assertEquals( "Handler invocations should be 3. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() == 3, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitOnSameOrder" );
        }
    }

    public void testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitOnSameOrderWithPendingTrade()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
        	Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
        	regenParams.addAll(replenishRegenParams);
        	regenParams.addAll(noReplenishRegenParams);
        	
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 5000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                quote0.setRateId( "G100" );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request = new RequestC();
                request.setAcceptedQuote( quote0 );
                request.getToOrganizations().add( org1 );
                request.setOrderId( "1000" );
                request.setTrade( trade );
                trade.setRequest( request );
                Request order = new RequestC();
                order.addChildRequest( request );
                request.setParentOCORequest( order );
                order.setOrderId( "1000" );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // reject the trade and call price regeneration and blackout period should be lifted.
                TradeStateFacade tsf = ( TradeStateFacade ) trade.getFacade( TradeStateFacade.TRADE_STATE_FACADE );
                tsf.setRejected();

                svc.stopPriceRegeneration( trade, testHandler );
                assertEquals( "PriceRegen service should lift blackout period." + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );

                // now create another trade with the same order id.
                Trade trade1 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request1 = new RequestC();
                request1.setAcceptedQuote( quote0 );
                request1.getToOrganizations().add( org1 );
                request1.setOrderId( "1000" );
                request1.setTrade( trade1 );
                trade1.setRequest( request1 );
                order.addChildRequest( request1 );
                request1.setParentRequest( order );
                boolean result2 = svc.startPriceRegeneration( trade1, testHandler );
                PriceRegeneration prcRegenx = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegenx );
                assertEquals( "Should allow the new trade. result2=" + result2, result2, true );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegenx.isRapidFirePeriod(), prcRegenx.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 4, true );

                // mark the trade as pending.
                TradeStateFacade tsf1 = ( TradeStateFacade ) trade1.getFacade( TradeStateFacade.TRADE_STATE_FACADE );
                tsf1.setNew();

                // now create another trade whose rejection should not lift the blackout period.
                Trade differentTrade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request differentRequest = new RequestC();
                differentRequest.setAcceptedQuote( quote0 );
                differentRequest.getToOrganizations().add( org1 );
                differentRequest.setOrderId( "1000" );
                differentRequest.setTrade( differentTrade );
                differentTrade.setRequest( differentRequest );
                order.addChildRequest( differentRequest );
                differentRequest.setParentOCORequest( order );
                order.setOrderId( "1000" );
                boolean result3 = svc.startPriceRegeneration( differentTrade, testHandler );
                PriceRegeneration prcRegeny = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegeny );
                assertEquals( "Should allow the trade. result3=" + result3, result3, true );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegeny.isRapidFirePeriod(), prcRegeny.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 5, true );

                // mark the trade as pending.
                TradeStateFacade tsf2 = ( TradeStateFacade ) differentTrade.getFacade( TradeStateFacade.TRADE_STATE_FACADE );
                tsf2.setRejected();

                svc.stopPriceRegeneration( differentTrade, testHandler );
                PriceRegeneration prcRegenz = testHandler.getPriceRegeneration();
                log( "put back trading limit. prcRegen=" + prcRegenz );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegenz.isRapidFirePeriod(), prcRegenz.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 6. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 6, true );
                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should not show in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), false );
                assertEquals( "PriceRegen service not show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );
                assertEquals( "Handler invocations should be 3. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() == 6, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitOnSameOrderWithPendingTrade" );
        }
    }

    public void testMultiQuotePriceRegenerationUndoTakingTradeLimitsWithTradeParametersChange()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // now change the trade parameters.
                trade.setCounterpartyB( org2Tp1 );
                trade.setCounterpartyA( org2Tp1 );
                trade.setOrganization( org2 );
                org2.setPriceRegenerationParameters( prcRegenParam );

                svc.stopPriceRegeneration( trade, testHandler );
                assertEquals( "IsInPriceRegen. PrcRegenParam : " + prcRegenParam.getDisplayKey(), svc.isInPriceRegeneration( org1, org1Tp1, ccyPair ), false );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationUndoTakingTradeLimits" );
        }
    }

    public void testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitWithSuspendedTimer()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
        	Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
        	regenParams.addAll(replenishRegenParams);
        	regenParams.addAll(noReplenishRegenParams);
        	
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 5000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now suspend the timer for rapid fire period.
                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                if ( prcRegenEvent.getBidPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getBidPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getOfferPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getOfferPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getRapidFireTimerTask() != null )
                {
                    prcRegenEvent.getRapidFireTimerTask().cancel();
                }

                // now sleep for more time than rapid fire period
                sleepFor( 200 + ( rapidFirePeriod ) );
                assertEquals( "PriceRegen should be still in rapid fire period. " + prcRegenEvent.isRapidFirePeriod(), prcRegenEvent.isRapidFirePeriod(), true );

                Trade trade1 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result2 = svc.startPriceRegeneration( trade1, testHandler );

                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should be in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service should be in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Trade should not be rejected.", result2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitWithSuspendedTimer" );
        }
    }

    public void testMultiQuotePriceRegenerationRapidFireWithMonitoringThread()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
        	Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
        	regenParams.addAll(replenishRegenParams);
        	regenParams.addAll(noReplenishRegenParams);
        	
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 5000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now suspend the timer for rapid fire period.
                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                if ( prcRegenEvent.getBidPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getBidPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getOfferPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getOfferPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getRapidFireTimerTask() != null )
                {
                    prcRegenEvent.getRapidFireTimerTask().cancel();
                }

                // now sleep for more time than rapid fire period
                sleepFor( 200 + ( rapidFirePeriod ) );
                assertEquals( "PriceRegen should be still in rapid fire period. " + prcRegenEvent.isRapidFirePeriod(), prcRegenEvent.isRapidFirePeriod(), true );

                // now sleep for the period of monitoring thread.
                long sleepDelay = PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean().getPriceRegenerationEventMonitoringInterval() * 2;
                sleepFor( sleepDelay + 1000 );

                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should not be in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), false );
                assertEquals( "PriceRegen service should be in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );
                break;
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationRapidFireWithMonitoringThread" );
        }
    }

    public void testMultiQuotePriceRegenerationWithMonitoringThread()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < replenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) replenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                int handlerInvocations = testHandler.getHandlerInvocations();
                assertEquals( "Handler invocations should not be more than 1. " + handlerInvocations, handlerInvocations < 2, true );

                // now suspend the timer for rapid fire period.
                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                if ( prcRegenEvent.getBidPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getBidPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getOfferPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getOfferPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getRapidFireTimerTask() != null )
                {
                    prcRegenEvent.getRapidFireTimerTask().cancel();
                }

                // now sleep for the period of monitoring thread.
                sleepFor( PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean().getPriceRegenerationEventMonitoringInterval() * 2 );

                assertEquals( "Handler invocations should be more than earlier handler invocations. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() > handlerInvocations, true );
                break;
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithMonitoringThread" );
        }
    }

    public void testMultiQuotePriceRegenerationTakingTradeLimitsForSpecificLegalEntity()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp2, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean result1 = svc.startPriceRegeneration( prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp2 ), testHandler, org1Tp2 );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                if ( !getPriceRegenerationParametersFacade( prcRegenParam ).isNoPriceRegeneration() )
                {
                    boolean result2 = svc.startPriceRegeneration( prepareTrade( getBidLimit() + acceptanceToleranceAmount + 2000, true, ccyPair, org1, org1Tp2 ), testHandler, org1Tp2 );
                    assertEquals( "FailedToTakeTradingLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, false );
                }
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTakingTradeLimitsForSpecificLegalEntity" );
        }
    }

    public void testMultiQuotePriceRegenerationUndoTakingTradeLimitsForSpecificLegalEntity()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp2, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp2 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                svc.stopPriceRegeneration( trade, testHandler );
                assertEquals( "IsInPriceRegen. PrcRegenParam : " + prcRegenParam.getDisplayKey(), svc.isInPriceRegeneration( org1, org1Tp2, ccyPair ), false );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationUndoTakingTradeLimitsForSpecificLegalEntity" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment. First, trading limit
     * for tiers registered and some part of the trading limit it taken by a trade. Then it is tested to see whether any subsequent trade
     * fails even with a small amount until next quote. Then, next quote arrives and replenishes the
     * trading limits. Then a trade is tested with the maximum trading limit and see whether it is taken successfully. Finally,
     * handlers are inspected to see whether any of them are invoked more than once.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentAndBlackoutUntilNextQuoteEnabled()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                prcRegenParam.setAllowOnlyOneMatchPerQuote( true );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );
                assertFalse( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now start taking trade with small portion of the available.
                double amt = getTradingLimit() / 10;
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                sleepFor( 2000 );

                // now create a new trade with same provider rate id. This trade should fail now.
                Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Failed due to blackout until next quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, false );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                // create another trade with a different provider rate id. This trade should go through
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                Trade trade2 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote1 );
                trade2.setRequest( req2 );

                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "trade success due to a different accepted quote id. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                // new send a new quote and new trade with new provider rate id should go through.
                Quote newQuote2 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId2 = "Q102";
                newQuote2.setRateId( provRateId2 );
                svc.applyPriceRegenerationRules( newQuote2, org1Tp1, isMultiTier() );
                assertFalse( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                Trade trade3 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote2 );
                trade3.setRequest( req3 );

                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "trade success due to newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                Trade trade4 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade failed due to re-attempt on already accepted quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, false );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "TestRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentAndBlackoutUntilNextQuoteEnabled" );
        }
        finally
        {
            // reset the black out
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                prcRegenParam.setAllowOnlyOneMatchPerQuote( false );
            }
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from lowest tier T1.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowBid()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                if ( prcRegenParam.getRegenerationModel() != PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION )
                {
                    continue;
                }
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now take first tier liquidity
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 2000 );

                // now create a new trade with same tier info. This trade should fail now.
                Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Failed due to insufficient liquidity at tier level. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, false );

                // an offer trade should go through
                Trade tradeOffer = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                tradeOffer.setAcceptedQuoteTier( 0 );
                Request reqOffer = DealingFactory.newRequest();
                reqOffer.setAcceptedQuote( newQuote );
                tradeOffer.setRequest( reqOffer );
                boolean resultOffer = svc.startPriceRegeneration( tradeOffer, testHandler );
                assertEquals( "offer trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultOffer, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a tier for total limit on tier2
                Trade trade3 = prepareTrade( amt * 2, true, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "Should fail. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "TestRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowBid" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from highest tier T3.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityLowToHighBid()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                if ( prcRegenParam.getRegenerationModel() == PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION )
                {
                    continue;
                }
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // take only first tier limit.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 2000 );

                // now create a new trade with same tier info. This trade should not fail.
                Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // an offer trade should go through
                Trade tradeOffer = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                tradeOffer.setAcceptedQuoteTier( 0 );
                Request reqOffer = DealingFactory.newRequest();
                reqOffer.setAcceptedQuote( newQuote );
                tradeOffer.setRequest( reqOffer );
                boolean resultOffer = svc.startPriceRegeneration( tradeOffer, testHandler );
                assertEquals( "offer trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultOffer, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( getBidTierLimit(), true, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a different tier.
                Trade trade3 = prepareTrade( getBidTierLimit() * 2, true, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( getBidLimit(), true, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "TestRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityLowToHighBid" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from highest tier T3.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowBidWithTierLevelTrackingDisabled()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        PriceRegenerationConfiguration config = ( PriceRegenerationConfiguration ) PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean();
        try
        {
            config.setProperty( PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // take only first tier limit.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 2000 );

                // now create a new trade with same tier info. This trade should not fail.
                Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // an offer trade should go through
                Trade tradeOffer = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                tradeOffer.setAcceptedQuoteTier( 0 );
                Request reqOffer = DealingFactory.newRequest();
                reqOffer.setAcceptedQuote( newQuote );
                tradeOffer.setRequest( reqOffer );
                boolean resultOffer = svc.startPriceRegeneration( tradeOffer, testHandler );
                assertEquals( "offer trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultOffer, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( getBidTierLimit(), true, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a different tier.
                Trade trade3 = prepareTrade( getBidTierLimit() * 2, true, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( getBidLimit(), true, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "TestRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowBidWithTierLevelTrackingDisabled" );
        }
        finally
        {
            config.setProperty( PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from lowest tier T1.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowOffer()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                if ( prcRegenParam.getRegenerationModel() != PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION )
                {
                    continue;
                }
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now take first tier liquidity
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 2000 );

                // now create a new trade with same tier info. This trade should fail now.
                Trade trade1 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Failed due to insufficient liquidity at tier level. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, false );

                // an offer trade should go through
                Trade tradeBid = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                tradeBid.setAcceptedQuoteTier( 0 );
                Request reqBid = DealingFactory.newRequest();
                reqBid.setAcceptedQuote( newQuote );
                tradeBid.setRequest( reqBid );
                boolean resultBid = svc.startPriceRegeneration( tradeBid, testHandler );
                assertEquals( "bid trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultBid, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a tier for total limit on tier2
                Trade trade3 = prepareTrade( amt * 2, false, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "Should fail. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "TestRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowOffer" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from highest tier T3.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityLowToHighOffer()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                if ( prcRegenParam.getRegenerationModel() == PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION )
                {
                    continue;
                }
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // take only first tier limit.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 2000 );

                // now create a new trade with same tier info. This trade should not fail.
                Trade trade1 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // an offer trade should go through
                Trade tradeBid = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                tradeBid.setAcceptedQuoteTier( 0 );
                Request reqBid = DealingFactory.newRequest();
                reqBid.setAcceptedQuote( newQuote );
                tradeBid.setRequest( reqBid );
                boolean resultBid = svc.startPriceRegeneration( tradeBid, testHandler );
                assertEquals( "bid trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultBid, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( getBidTierLimit(), false, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a different tier.
                Trade trade3 = prepareTrade( getBidTierLimit() * 2, false, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( getBidLimit(), false, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "TestRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityLowToHighOffer" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from highest tier T3.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowOfferWithTierLevelTrackingDisabled()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        PriceRegenerationConfiguration config = ( PriceRegenerationConfiguration ) PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean();
        try
        {
            config.setProperty( PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // take only first tier limit.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 2000 );

                // now create a new trade with same tier info. This trade should not fail.
                Trade trade1 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // an offer trade should go through
                Trade tradeBid = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                tradeBid.setAcceptedQuoteTier( 0 );
                Request reqBid = DealingFactory.newRequest();
                reqBid.setAcceptedQuote( newQuote );
                tradeBid.setRequest( reqBid );
                boolean resultBid = svc.startPriceRegeneration( tradeBid, testHandler );
                assertEquals( "bid trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultBid, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( getBidTierLimit(), false, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a different tier.
                Trade trade3 = prepareTrade( getBidTierLimit() * 2, false, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( getBidLimit(), false, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestRegenerationHandler hndler = ( TestRegenerationHandler ) handler1;
                log( "TestRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowOfferWithTierLevelTrackingDisabled" );
        }
        finally
        {
            config.setProperty( PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }

    public void testMultiQuoteWithRapidFireTakingTradingLimitOnSameOrderWithSweep()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
            regenParams.addAll(replenishRegenParams);
            regenParams.addAll(noReplenishRegenParams);

            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 5000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                quote0.setRateId( "G100" );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestRegenerationHandler testHandler = new TestRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request = new RequestC();
                request.setAcceptedQuote( quote0 );
                request.getToOrganizations().add( org1 );
                request.setOrderId( "1000" );
                request.setTrade( trade );
                trade.setRequest( request );
                Request order = new RequestC();
                order.addChildRequest( request );
                request.setParentOCORequest( order );
                order.setOrderId( "1000" );
                trade.setSweepNumber( 1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now create another trade with the same order id.
                Trade trade1 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request1 = new RequestC();
                request1.setAcceptedQuote( quote0 );
                request1.getToOrganizations().add( org1 );
                request1.setOrderId( "1000" );
                request1.setTrade( trade1 );
                trade1.setRequest( request1 );
                order.addChildRequest( request1 );
                request1.setParentRequest( order );
                trade1.setSweepNumber( 1 );
                boolean result2 = svc.startPriceRegeneration( trade1, testHandler );
                PriceRegeneration prcRegenx = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegenx );
                assertEquals( "Should allow the trade. result2=" + result2, result2, true );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegenx.isRapidFirePeriod(), prcRegenx.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                // now create another trade with the same order id, but different sweep number
                Trade trade2 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request2 = new RequestC();
                request2.setAcceptedQuote( quote0 );
                request2.getToOrganizations().add( org1 );
                request2.setOrderId( "1000" );
                request2.setTrade( trade2 );
                trade2.setRequest( request1 );
                order.addChildRequest( request2 );
                request2.setParentRequest( order );
                trade2.setSweepNumber( 2 );
                boolean result3x = svc.startPriceRegeneration( trade2, testHandler );
                PriceRegeneration prcRegenx1 = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegenx1 );
                assertEquals( "Should not allow the trade. result2=" + result3x, result3x, false );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3x, false );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegenx.isRapidFirePeriod(), prcRegenx.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                // now create another trade which does not originate from the same order. this should get rejected.
                Trade differentTrade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request differentRequest = new RequestC();
                differentRequest.setAcceptedQuote( quote0 );
                differentRequest.getToOrganizations().add( org1 );
                differentRequest.setOrderId( "1001" );
                differentRequest.setTrade( differentTrade );
                differentTrade.setRequest( differentRequest );
                Request differentOrder = new RequestC();
                differentOrder.addChildRequest( differentRequest );
                differentRequest.setParentOCORequest( differentOrder );
                differentOrder.setOrderId( "1001" );
                differentTrade.setSweepNumber( 1 );
                boolean result3 = svc.startPriceRegeneration( differentTrade, testHandler );
                PriceRegeneration prcRegeny = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegeny );
                assertEquals( "Should not allow the trade. result3=" + result3, result3, false );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegeny.isRapidFirePeriod(), prcRegeny.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );
                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should not show in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), false );
                assertEquals( "PriceRegen service not show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );
                assertEquals( "Handler invocations should be 3. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() == 3, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuoteWithRapidFireTakingTradingLimitOnSameOrderWithSweep" );
        }
    }

    private double getBidTierLimit()
    {
        return 5000000;
    }

    private double getOfferTierLimit()
    {
        return 6000000;
    }

    private double getBidLimit()
    {
        return getLimit( getBidTierLimit() );
    }

    private double getOfferLimit()
    {
        return getLimit( getOfferTierLimit() );
    }

    private double getLimit( double tierLimit )
    {
        double limit = 0.0;
        for ( int index = 0; index < numberOfTiers; index++ )
        {
            limit += ( index + 1 ) * tierLimit;
        }
        return limit;
    }

    private double getTradingLimit()
    {
        return getBidLimit() < getOfferLimit() ? getBidLimit() : getOfferLimit();
    }
}
