package com.integral.finance.dealing.priceProvision.test;

// Copyright (c) 2013 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.creditLimit.CreditTenorProfile;
import com.integral.finance.dealing.priceProvision.SpreadGroup;
import com.integral.finance.dealing.priceProvision.SpreadGroupC;
import com.integral.finance.dealing.priceProvision.SpreadTenorParameters;
import com.integral.finance.dealing.priceProvision.SpreadTenorParametersC;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.NamedEntity;
import com.integral.provider.ProviderOrgFunction;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


/**
 * Tests the persistence of credit tenor profile.
 */
public class SpreadGroupPTestC
        extends PTestCaseC
{
    static String name = "Spread Group Test";

    public SpreadGroupPTestC( String name )
    {
        super( name );
    }

    public void testInsertSpreadGroup()
    {
        try
        {
            Session session = getPersistenceSession();

            Organization org = ( Organization ) namedEntityReader.execute( Organization.class, "CITI" );
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            ProviderOrgFunction pof = org.getProviderOrgFunction();
            ProviderOrgFunction regPof = ( ProviderOrgFunction ) uow.registerObject( pof );
            SpreadGroup profile = new SpreadGroupC();
            SpreadGroup regProfile = ( SpreadGroup ) uow.registerObject( profile );
            regProfile.setStatus( 'T' );
            String name = "Test" + System.nanoTime();
            regProfile.setShortName( name );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );
            regProfile.setOwner( regPof );

            SpreadTenorParameters stp = new SpreadTenorParametersC();
            SpreadTenorParameters regStp = ( SpreadTenorParameters ) uow.registerObject( stp );
            regStp.setBidSpread( 10.0 );
            regStp.setOfferSpread( 20.0 );
            regStp.setTenor( Tenor.SPOT_TENOR );

            SpreadTenorParameters stp1 = new SpreadTenorParametersC();
            SpreadTenorParameters regStp1 = ( SpreadTenorParameters ) uow.registerObject( stp1 );
            regStp1.setBidSpread( 30.0 );
            regStp1.setOfferSpread( 40.0 );
            regStp1.setTenor( new Tenor( "1W" ) );

            regProfile.getSpreadTenorParameters().add( regStp );
            regProfile.getSpreadTenorParameters().add( regStp1 );

            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal( name );
            SpreadGroup profileFromDB = ( SpreadGroup ) getPersistenceSession().readObject( SpreadGroup.class, expr );
            assertNotNull( profileFromDB );
            log.info( "profileFromDB=" + profileFromDB );
            assertTrue( profileFromDB.getSpreadTenorParameters().size() == 2 );
        }
        catch ( Exception e )
        {
            fail( "testInsertSpreadGroup", e );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            Organization org = ( Organization ) namedEntityReader.execute( Organization.class, "CITI" );
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( UserC.class );
            uow.addReadOnlyClass( OrganizationC.class );
            ProviderOrgFunction pof = org.getProviderOrgFunction();
            ProviderOrgFunction regPof = ( ProviderOrgFunction ) uow.registerObject( pof );
            SpreadGroup profile = new SpreadGroupC();
            assertTrue( profile.isInterpolate() );
            SpreadGroup regProfile = ( SpreadGroup ) uow.registerObject( profile );
            regProfile.setStatus( 'T' );
            regProfile.setShortName( "Test" + System.nanoTime() );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );
            regProfile.setInterpolate( false );

            SpreadTenorParameters stp1 = new SpreadTenorParametersC();
            SpreadTenorParameters regStp1 = ( SpreadTenorParameters ) uow.registerObject( stp1 );
            regStp1.setBidSpread( 10.0 );
            regStp1.setOfferSpread( 11.0 );
            regStp1.setTenor( Tenor.SPOT_TENOR );

            SpreadTenorParameters stp2 = new SpreadTenorParametersC();
            SpreadTenorParameters regStp2 = ( SpreadTenorParameters ) uow.registerObject( stp2 );
            regStp2.setBidSpread( 20.0 );
            regStp2.setOfferSpread( 30.00 );
            regStp2.setTenor( new Tenor( "1W" ) );

            regProfile.getSpreadTenorParameters().add( regStp1 );
            regProfile.getSpreadTenorParameters().add( regStp2 );
            regProfile.setOwner( regPof );
            uow.commit();

            profile = ( SpreadGroup ) getPersistenceSession().refreshObject( profile );
            assertFalse( profile.isInterpolate() );
            assertTrue( profile.getSpreadTenorParameters().size() == 2 );
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }


    public void testSpreadGroupLookup()
    {
        try
        {
            Session session = getPersistenceSession();
            Vector profiles = session.readAllObjects( SpreadGroupC.class );
            for ( int i = 0; i < profiles.size(); i++ )
            {
                SpreadGroup profile = ( SpreadGroup ) profiles.elementAt( i );
                log.info( ",profile=" + profile );
            }
        }
        catch ( Exception e )
        {
            fail( "testSpreadGroupLookup", e );
        }
    }

    public void testDeleteSpreadGroup()
    {
        try
        {
            Session session = getPersistenceSession();

            Organization org = ( Organization ) namedEntityReader.execute( Organization.class, "CITI" );
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            ProviderOrgFunction pof = org.getProviderOrgFunction();
            ProviderOrgFunction regPof = ( ProviderOrgFunction ) uow.registerObject( pof );
            SpreadGroup profile = new SpreadGroupC();
            SpreadGroup regProfile = ( SpreadGroup ) uow.registerObject( profile );
            regProfile.setStatus( 'T' );
            String name = "Test" + System.nanoTime();
            regProfile.setShortName( name );
            regProfile.setLongName( "TestLongName" );
            regProfile.setDescription( "testDesc" );

            SpreadTenorParameters stp1 = new SpreadTenorParametersC();
            SpreadTenorParameters regStp1 = ( SpreadTenorParameters ) uow.registerObject( stp1 );
            regStp1.setBidSpread( 10.0 );
            regStp1.setOfferSpread( 11.0 );
            regStp1.setTenor( Tenor.SPOT_TENOR );

            SpreadTenorParameters stp2 = new SpreadTenorParametersC();
            SpreadTenorParameters regStp2 = ( SpreadTenorParameters ) uow.registerObject( stp2 );
            regStp2.setBidSpread( 20.0 );
            regStp2.setOfferSpread( 21.0 );
            regStp2.setTenor( new Tenor( "1W" ) );

            regProfile.getSpreadTenorParameters().add( regStp1 );
            regProfile.getSpreadTenorParameters().add( regStp2 );
            regProfile.setOwner( regPof );

            uow.commit();

            uow = session.acquireUnitOfWork();
            profile = ( SpreadGroup ) getPersistenceSession().refreshObject( profile );
            SpreadGroup regProfile1 = ( SpreadGroup ) uow.registerObject( profile );
            regProfile1.getSpreadTenorParameters().clear();
            uow.commit();

            profile = ( SpreadGroup ) getPersistenceSession().refreshObject( profile );
            assertTrue( profile.getSpreadTenorParameters().isEmpty() );

            uow = session.acquireUnitOfWork();
            profile = ( SpreadGroup ) getPersistenceSession().refreshObject( profile );
            SpreadGroup regProfile2 = ( SpreadGroup ) uow.registerObject( profile );
            uow.deleteObject( regProfile2 );
            uow.commit();


            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal( name );
            SpreadGroup profileFromDB = ( SpreadGroup ) getPersistenceSession().readObject( CreditTenorProfile.class, expr );
            assertNull( profileFromDB );
        }
        catch ( Exception e )
        {
            fail( "testDeleteSpreadGroup", e );
        }
    }

}
