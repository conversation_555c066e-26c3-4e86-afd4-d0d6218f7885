package com.integral.finance.dealing.skew.test;

import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.skew.SkewSpread;
import com.integral.finance.dealing.skew.SkewSpreadC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;


/**
 * <AUTHOR> jessy
 *         To change this template use File | Settings | File Templates.
 */
public class SkewSpreadPTestC extends PTestCaseC {
    static String name = "Spread Group Test";
    Organization citi = ( Organization ) namedEntityReader.execute( Organization.class, "CITI" );
    Organization fi1 = ( Organization ) namedEntityReader.execute( Organization.class, "FI1" );

    public SkewSpreadPTestC( String name )
    {
        super( name );
    }

    public void testInsertSkewSpread()
    {
        try
        {

            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            Organization lp = ( Organization ) uow.registerObject( citi );
            Organization fi = ( Organization ) uow.registerObject( fi1 );
            SkewSpread origSkewSpread = new SkewSpreadC();
            SkewSpread regSpread = ( SkewSpread ) uow.registerObject( origSkewSpread );
            regSpread.setOrganization( fi );
            regSpread.setOwner(lp);

            int clsf = 2;
            double spreadValue = 1.001;

            regSpread.setCurrencyPair(CurrencyFactory.newCurrencyPair("EUR", "USD"));
            regSpread.setEnabled(true);
            regSpread.setSpreadClsf( clsf );
            regSpread.setSpreadValue(spreadValue);
            ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();
            regSpread.setRateConvention((FXRateConvention) namedEntityReader.execute(FXRateConventionC.class, "STDQOTCNV"));
            uow.commit();


            SkewSpread skewSpreadFromDB = ( SkewSpread ) IdcUtilC.refreshObject( origSkewSpread );
            assertNotNull( skewSpreadFromDB );
            log.info( "Skew Spread from DB=" + skewSpreadFromDB.getToString() );
            assertEquals(skewSpreadFromDB.isEnabled(), regSpread.isEnabled());
            assertEquals( clsf, skewSpreadFromDB.getSpreadClsf() );
            assertEquals( spreadValue, skewSpreadFromDB.getSpreadValue());
        }
        catch ( Exception e )
        {
            fail( "testInsertSpreadGroup", e );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
//            Organization org = UserFactory.newOrganization();
//            org = ( Organization ) uow.registerObject( org );
//            Namespace ns = ( Namespace ) uow.registerObject( new NamespaceC() );
//            ns.setShortName( "TestOrg" + time );
//            org.setShortName( "TestOrg" + time );
            Organization lp = ( Organization ) uow.registerObject( citi );
            Organization fi = ( Organization ) uow.registerObject( fi1 );

            SkewSpread regSpread = ( SkewSpread ) uow.registerObject( new SkewSpreadC());

            regSpread.setOrganization( fi );
            regSpread.setOwner( lp );

            regSpread.setCurrencyPair( CurrencyFactory.newCurrencyPair( "EUR", "USD" ) );
            regSpread.setEnabled(true);
            regSpread.setSpreadClsf(1);
            regSpread.setSpreadValue(3.001);
            ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();
            regSpread.setRateConvention((FXRateConvention) namedEntityReader.execute(FXRateConventionC.class, "STDQOTCNV"));
            uow.commit();

            regSpread = ( SkewSpread ) session.refreshObject( regSpread );
            log( regSpread.getToString() );
            assertTrue(regSpread.isEnabled());
            assertEquals(regSpread.getSpreadClsf(), 1);
            assertEquals(regSpread.getSpreadValue(), 3.001);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

}
