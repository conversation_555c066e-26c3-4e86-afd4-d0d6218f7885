package com.integral.finance.dealing.skew.test;

import com.integral.finance.dealing.skew.SkewStrategy;
import com.integral.finance.dealing.skew.SkewStrategyC;
import com.integral.persistence.NamedEntity;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.OrganizationRelationship;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

/**
 * <AUTHOR> jessy
 *         To change this template use File | Settings | File Templates.
 */
public class SkewStrategyPTestC extends PTestCaseC {

    public void testInsertSkewStrategy()
    {
        try
        {
            Session session = getPersistenceSession();
            long time = System.currentTimeMillis();
            Organization lp = ( Organization ) namedEntityReader.execute( OrganizationC.class, "DBNB" );
            Organization fi = ( Organization ) namedEntityReader.execute( OrganizationC.class, "FI2" );
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            SkewStrategy strategy = new SkewStrategyC();
            SkewStrategy regStrategy = ( SkewStrategy ) uow.registerObject( strategy );
            regStrategy.setStatus('T');
            regStrategy.setName("Test" +time);
            OrganizationRelationship orgRel = lp.getOrgRelationship(fi, OrganizationRelationship.LP_FI_RELATIONSHIP);
            orgRel = (OrganizationRelationship)uow.registerObject(orgRel);
            regStrategy.setOwner(orgRel);
            regStrategy.setEnabled(true);
            regStrategy.setAmountThreshold(5000);
            regStrategy.setExpiryTime(10000);
            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal("Test" +time);
            SkewStrategy strFromDB = ( SkewStrategy ) getPersistenceSession().readObject( SkewStrategy.class, expr );
            assertNotNull( strFromDB );
            log.info( "Skew Spread from DB=" + strFromDB );
            assertEquals(strFromDB.getAmountThreshold(),regStrategy.getAmountThreshold());
            assertEquals(strFromDB.getExpiryTime(),regStrategy.getExpiryTime());
            assertEquals(strFromDB.isEnabled(),regStrategy.isEnabled());

        }
        catch ( Exception e )
        {
            fail( "testInsertSpreadGroup", e );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            long time = System.currentTimeMillis();
            Organization lp = ( Organization ) namedEntityReader.execute( OrganizationC.class, "DBNB" );
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            SkewStrategy strategy = new SkewStrategyC();
            SkewStrategy regStrategy = ( SkewStrategy ) uow.registerObject( strategy );
            regStrategy.setStatus('T');
            lp = (Organization)uow.registerObject(lp);
            regStrategy.setName("Test" + time);
            regStrategy.setOwner(lp);
            regStrategy.setAmountThreshold(5000);
            regStrategy.setExpiryTime(10000);
            regStrategy.setEnabled(true);
            uow.commit();

            strategy = ( SkewStrategy ) getPersistenceSession().refreshObject( strategy );
            assertNotNull( strategy );
            log.info( "Skew Spread from DB=" + strategy );
            assertEquals(strategy.getAmountThreshold(), regStrategy.getAmountThreshold());
            assertEquals(strategy.getExpiryTime(), regStrategy.getExpiryTime());
            assertEquals(strategy.isEnabled(), regStrategy.isEnabled());
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }


}
