package com.integral.finance.dealing.test;


import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.DealingPriceC;
import com.integral.finance.dealing.ExecutionType;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Vector;


public class DealingPricePTestC
        extends PTestCaseC
{
    static String name = "DealingPrice Test";

    public DealingPricePTestC( String name )
    {
        super( name );
    }

    public void testLookup()
    {
        log( "testLookup" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( DealingPrice.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                DealingPrice price = ( DealingPrice ) objs.elementAt( i );
                log( "Dealing Price " + price + ", bidOfferMode = " + price.getBidOfferMode() );
            }
            log( "testLookup" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testLookup" );
        }
    }

    public void testFilledPriceCalculation() throws Exception
    {
        Session sess = PersistenceFactory.newSession();
        UnitOfWork uow = sess.acquireUnitOfWork();

        FXLegDealingPrice mainDP = getTemplateDP( 0, 0, "EUR/USD", true, 1.182, uow );
        FXLegDealingPrice accDP1 = getTemplateDP( 1000, 1182, "EUR/USD", true, 1.182, uow );
        mainDP.addAcceptedPrice( accDP1 );
        FXLegDealingPrice fillMainDP = ( FXLegDealingPrice ) mainDP.getFillDealingPrice();
        assertEquals( "For a single accepted price filled Dealing Price dealt amount ", fillMainDP.getDealtAmount(), 1000.0 );
        assertEquals( "For a single accepted price filled Dealing Price Settlement amount ", fillMainDP.getSettledAmount(), 1182.0 );
        assertEquals( "For a single accepted price filled Dealing Price rate is ", fillMainDP.getFXRate().getRate(), 1.182 );

        mainDP = getTemplateDP( 0, 0, "EUR/USD", true, 1.182, uow );
        accDP1 = getTemplateDP( 1000, 1182, "EUR/USD", true, 1.1820, uow );
        accDP1.setIndex( 1 ); // just to make the hashcodes different so that when added to acceptedPrices SET ; they ramin
        FXLegDealingPrice accDP2 = getTemplateDP( 1000, 1183, "EUR/USD", true, 1.183, uow );
        accDP2.setIndex( 2 );
        FXLegDealingPrice accDP3 = getTemplateDP( 1000, 1186, "EUR/USD", true, 1.186, uow );
        accDP3.setIndex( 3 );
        Collection<DealingPrice> acceptedPrices = new ArrayList<DealingPrice>();
        acceptedPrices.add( accDP1 );
        acceptedPrices.add( accDP2 );
        acceptedPrices.add( accDP3 );
        mainDP.setAcceptedPrices( acceptedPrices );

        fillMainDP = ( FXLegDealingPrice ) mainDP.getFillDealingPrice();
        assertEquals( "For three accepted prices filled Dealing Price dealt amount ", fillMainDP.getDealtAmount(), 3000.0 );
        assertEquals( "For three accepted prices filled Dealing Price Settlement amount ", fillMainDP.getSettledAmount(), 3551.0 );
        assertEquals( "For three accepted prices filled Dealing Price rate is ", ( int ) ( fillMainDP.getFXRate().getRate() * 10000 ), 11836 );

    }

    public void testAcceptedAndFilledPricePersistence() throws Exception
    {
        Session sess = PersistenceFactory.newSession();
        UnitOfWork uow = sess.acquireUnitOfWork();

        FXLegDealingPrice mainDP = getTemplateDP( 0, 0, "EUR/USD", true, 1.182, uow );
        FXLegDealingPrice accDP1 = getTemplateDP( 1000, 1182, "EUR/USD", true, 1.182, uow );
        accDP1.setIndex( 1 ); // just to make the hashcodes different so that when added to acceptedPrices SET ; they ramin
        FXLegDealingPrice accDP2 = getTemplateDP( 1000, 1183, "EUR/USD", true, 1.183, uow );
        accDP2.setIndex( 2 );
        FXLegDealingPrice accDP3 = getTemplateDP( 1000, 1186, "EUR/USD", true, 1.186, uow );
        accDP3.setIndex( 3 );
        Collection<DealingPrice> acceptedPrices = new ArrayList<DealingPrice>();
        acceptedPrices.add( accDP1 );
        acceptedPrices.add( accDP2 );
        acceptedPrices.add( accDP3 );
        mainDP.setAcceptedPrices( acceptedPrices );
        uow.registerObject( mainDP );
        uow.commit();

        PersistenceFactory.refreshObject( sess, mainDP );
        FXLegDealingPrice fillMainDP = ( FXLegDealingPrice ) mainDP.getFillDealingPrice();
        if ( mainDP.getObjectID() == 0 )
        {
            fail( "Object Id should be greater than zero after persisting" );
        }
        assertEquals( "Accepted prices commited on dealing price", mainDP.getAcceptedPrices().size(), 3 );
        assertEquals( "After commit . For three accepted prices filled Dealing Price dealt amount ", fillMainDP.getDealtAmount(), 3000.0 );
        assertEquals( "After commit . For three accepted prices filled Dealing Price Settlement amount ", fillMainDP.getSettledAmount(), 3551.0 );
        assertEquals( "After commit . For three accepted prices filled Dealing Price rate is ", ( int ) ( fillMainDP.getFXRate().getRate() * 10000 ), 11836 );
    }


    private FXLegDealingPrice getTemplateDP( double dealtAmt, double settledAmt, String ccyPair, boolean dealtCcyIsBaseCcy, double currencyRate, UnitOfWork uow )
    {
        FXLegDealingPrice dp = ( FXLegDealingPrice ) uow.registerObject( FXDealingFactory.newFXLegDealingPrice() );
        FXRate rate = FXFactory.newFXRate();
        rate.setRate( currencyRate );
        dp.setDealtAmount( dealtAmt );
        dp.setSettledAmount( settledAmt );
        FXDealingPriceElement fxDpe = ( FXDealingPriceElement ) uow.registerObject( FXDealingFactory.newFXDealingPriceElement() );

        FXPrice fxPrc = FXPriceFactory.newFXPrice();
        fxPrc.setBidFXRate( rate );
        fxPrc.setOfferFXRate( rate );

        fxDpe.setPrice( fxPrc );


        dp.setPriceElement( fxDpe );
        Currency baseCcy = CurrencyFactory.getCurrency( ccyPair.substring( 0, ccyPair.indexOf( '/' ) ) );
        Currency varCcy = CurrencyFactory.getCurrency( ccyPair.substring( ccyPair.indexOf( '/' ) + 1 ) );
        CurrencyPair currencyPair = CurrencyFactory.newCurrencyPair( baseCcy, varCcy );
        if ( dealtCcyIsBaseCcy )
        {
            dp.setDealtCurrency( baseCcy );
            dp.setSettledCurrency( varCcy );
        }
        else
        {
            dp.setDealtCurrency( varCcy );
            dp.setSettledCurrency( baseCcy );
        }
        rate.setCurrencyPair( currencyPair );
        dp.getFXRate().setCurrencyPair( currencyPair );
        return dp;
    }

    public void testDealingPriceAndDPEPersistence() throws Exception
    {
        Session sess = PersistenceFactory.newSession();
        UnitOfWork uow = sess.acquireUnitOfWork();
        Request req = ( Request ) uow.registerObject( DealingFactory.newRequest() );
        req.setTransactionID( "Test" + System.nanoTime() );
        FXLegDealingPrice mainDP = ( FXLegDealingPrice ) uow.registerObject( FXDealingFactory.newFXLegDealingPrice() );
        //mainDP.setName( "mainDP");
        FXLegDealingPrice secondDP = getTemplateDP( 1000, 1186, "EUR/USD", true, 1.186, uow );
        secondDP.setName( "secondDP" );

        req.setRequestPrice( "singleLeg", mainDP );

        FXDealingPriceElement priceElement = ( FXDealingPriceElement ) uow.registerObject( FXDealingFactory.newFXDealingPriceElement() );
        priceElement.setName( "1" );
        mainDP.getPriceElements().add( priceElement );
        mainDP.addAcceptedPrice( secondDP );
        uow.commit();

        PersistenceFactory.refreshObject( sess, req );
        PersistenceFactory.refreshObject( sess, mainDP );
        uow = sess.acquireUnitOfWork();

        mainDP = ( FXLegDealingPrice ) uow.registerObject( mainDP );
        /*FXLegDealingPrice executeDP = (FXLegDealingPrice ) uow.registerObject( FXDealingFactory.newFXLegDealingPrice());
        executeDP.setName( "EXECUTE DP");
        FXDealingPriceElement executePE = ( FXDealingPriceElement ) uow.registerObject( FXDealingFactory.newFXDealingPriceElement() );
        executePE.setName( "EXECUTE");
        executeDP.setPriceElement(  executePE );*/
        FXLegDealingPrice executeDP = getTemplateDP( 1000, 1186, "EUR/USD", true, 1.186, uow );
        mainDP.addAcceptedPrice( executeDP );
        //mainDP.getPriceElements().add( priceElement2 );
        uow.commit();
    }

    public void testDealtAmount()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            int executionType = ExecutionType.LIFT;
            int prcType = Quote.PRICE_TYPE_MULTI_TIER;
            double maxAmt = 1000;
            double minAmt = 10;
            DealingPrice testDp = new DealingPriceC();
            DealingPrice registeredDp = ( DealingPrice ) uow.registerObject( testDp );
            registeredDp.setExecutionType( executionType );
            registeredDp.setMaxShowAmount( maxAmt );
            registeredDp.setMinDealtAmount( minAmt );
            registeredDp.setPriceType( prcType );
            uow.commit();

            // refresh the trade and retrieve the maker request.
            testDp = ( DealingPrice ) session.refreshObject( testDp );
            log( "testDp=" + testDp + ",testDp.maxAmt=" + testDp.getMaxShowAmount()
                    + ",minAmt=" + testDp.getMinDealtAmount() + ",executionType=" + testDp.getExecutionType() + ",priceType=" + testDp.getPriceType() );
            assertEquals( "min amount should not be null.", testDp.getMinDealtAmount(), minAmt );
            assertEquals( "max amt should be the same.", testDp.getMaxShowAmount(), maxAmt );
            assertEquals( "execution type should be same", testDp.getExecutionType(), executionType );
            assertEquals( "price type should be the same.", testDp.getPriceType(), prcType );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }


}

