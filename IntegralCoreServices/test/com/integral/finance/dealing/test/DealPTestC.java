package com.integral.finance.dealing.test;

import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXSingleLegDeal;
import com.integral.finance.dealing.fx.FXSingleLegDealC;
import com.integral.finance.dealing.fx.FXSwapDeal;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.Entity;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;

public class DealPTestC
        extends PTestCaseC
{
    static String name = "Deal Test";

    public DealPTestC( String name )
    {
        super( name );
    }

    public void testDealParentQuery()
    {
        try
        {
            System.out.println( "about to run deal test - for querying dealFXSingleLegParent - which is added in Integral5ProjectCMappingAmender" );
            Session session = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            ReportQuery query = new ReportQuery( eb );

            query.setReferenceClass( FXSingleLegDealC.class );
            query.useCollectionClass( ArrayList.class );
            query.setSelectionCriteria( eb.get( "objectID" ).equal( 249994 ) );
            query.addAttribute( "objectID", eb.get( "objectID" ) );
            query.addAttribute( "parentDealFXDealLegCurrency1Amount", eb.get( "dealFXSingleLegParent" ).get( "fxDealLeg" ).get( "currency1Amount" ) );
            query.addAttribute( "parentDealFXDealLegCurrency1Amount", eb.get( "dealFXSingleLegParent" ).get( "fxDealLeg" ).get( "currency2Amount" ) );
            ArrayList result = ( ArrayList ) session.executeQuery( query );

            System.out.println( "Query completed, result size is " + result.size() );
            for ( int i = 0; i < result.size(); i++ )
            {
                ReportQueryResult row = ( ReportQueryResult ) result.get( i );
                System.out.println( "row #" + i + ":\t" + row );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    /*
     * testing the persisting of fields in the deal object
     */
    public void testDealPersistence()
    {
        FXSingleLegDeal singleLegDeal = FXDealingFactory.newFXSingleLegDeal();
        double origOrderAmt1 = 100;
        double origOrderRate1 = 1.2345;
        IdcDate date = DateTimeFactory.newDate();

        // Inserting the new FXSingleLegDeal to DB.        
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            singleLegDeal = ( FXSingleLegDeal ) this.uow.registerNewObject( singleLegDeal );
            singleLegDeal.setInitialTriggerRate( 12.4567 );
            singleLegDeal.setCustomerLimit( 897 );
            assertTrue( singleLegDeal.isDisplayEnabled() );
            assertFalse( singleLegDeal.isServerManagedOrder() );
            singleLegDeal.setDisplayEnabled( false );
            singleLegDeal.setStatus( 'T' );
            singleLegDeal.setServerManagedOrder( true );
            System.out.println( "Setting MarketRate" );
            singleLegDeal.getFXDealLeg().setMarketSpotRate( 10.00 );
            singleLegDeal.getFXDealLeg().setMarketRate( 10.00 );
            singleLegDeal.setTransactionId( "FXI123" );
            singleLegDeal.setPortfolioId("123");
            singleLegDeal.setAllocation(true);
            singleLegDeal.getFXDealLeg().setOriginalOrderAmount( origOrderAmt1 );
            singleLegDeal.getFXDealLeg().setOriginalOrderRate( origOrderRate1 );
            singleLegDeal.getFXDealLeg().setFixingDate( date );
            singleLegDeal.getFXDealLeg().setFixingTenor( Tenor.SPOT_TENOR );
            singleLegDeal.setMaskedLP( "testMaskedLP" );
            singleLegDeal.setUPI( "testUPI" );
            singleLegDeal.getFXDealLeg().setUSI( "testUSI" );
            //singleLegDeal.getFXDealLeg().setUTI( "testUTI" );
            singleLegDeal.setSEF( false );
            singleLegDeal.setCounterpartyALEI( "testCptyALEI" );
            singleLegDeal.setCounterpartyBLEI( "testCptyBLEI" );
            singleLegDeal.setPricingType("Manual");
            singleLegDeal.setNote("Test");
            uow.commit();
        }
        catch ( Exception exc )
        {
            log.error( "DealPTestC.testDealPersistence() Exception 1 - " + exc );
        }

        // Reloading the FXSingleLegDeal from DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            singleLegDeal.getDealLegs().clear();
            singleLegDeal = ( FXSingleLegDeal ) uow.refreshObject( singleLegDeal );
            assertEquals( singleLegDeal.getInitialTriggerRate(), 12.4567 );
            assertFalse( singleLegDeal.isDisplayEnabled() );
            assertEquals( singleLegDeal.getCustomerLimit(), 897d );
            assertTrue( singleLegDeal.isServerManagedOrder() );
            assertEquals( singleLegDeal.getFXDealLeg().getMarketSpotRate(), 10.00 );
            assertEquals( singleLegDeal.getFXDealLeg().getMarketRate(), 10.00 );
            assertEquals( singleLegDeal.getFXDealLeg().getOriginalOrderAmount(), origOrderAmt1 );
            assertEquals( singleLegDeal.getFXDealLeg().getOriginalOrderRate(), origOrderRate1 );
            assertEquals( singleLegDeal.getFXDealLeg().getFixingDate(), date );
            assertEquals( singleLegDeal.getFXDealLeg().getFixingTenor(), Tenor.SPOT_TENOR );
            assertEquals( singleLegDeal.getUPI(), "testUPI" );
            assertEquals( singleLegDeal.getFXDealLeg().getUSI(), "testUSI" );
            //assertEquals( singleLegDeal.getFXDealLeg().getUTI(), "testUTI" );
            assertEquals( singleLegDeal.isSEF(), false );
            assertEquals( singleLegDeal.getMaskedLP(), "testMaskedLP" );
            assertEquals( "testCptyALEI", singleLegDeal.getCounterpartyALEI() );
            assertEquals( "testCptyBLEI", singleLegDeal.getCounterpartyBLEI() );
            assertEquals( singleLegDeal.getDealLegs().size(), 1 );
            assertEquals( singleLegDeal.getPortfolioId(),"123" );
            assertTrue(singleLegDeal.isAllocation());
            assertEquals( singleLegDeal.getPricingType(),"Manual" );
            assertEquals(singleLegDeal.getNote(), "Test");
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "DealPTestC.testDealPersistence() Exception 2 - " + exc );
        }

        long objectId = singleLegDeal.getObjectID();

        // Deleteing the entry.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.deleteObject( singleLegDeal );
            uow.commit();
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "DealPTestC.testDealPersistence() Exception 3 - " + exc );
        }

        // Retrieve the object again to make sure object is deleted.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder builder = new ExpressionBuilder();
            Expression expr = builder.get( "objectID" ).equal( objectId );
            uow = this.getPersistenceSession().acquireUnitOfWork();
            FXSingleLegDeal deal = ( FXSingleLegDeal ) uow.readObject( com.integral.finance.dealing.fx.FXSingleLegDealC.class, expr );
            assertEquals( deal, null );
        }
        catch ( Exception exc )
        {
            log.error( "DealPTestC.testDealPersistence() Exception 4 - " + exc );
        }
    }

    /*
     * testing the persisting of fields in the deal object
     */
    public void testSwapDealPersistence()
    {
        FXSwapDeal swapDeal = FXDealingFactory.newFXSwapDeal();
        double origOrderAmt1 = 100;
        double origOrderRate1 = 1.2345;
        double origOrderAmt2 = 200;
        double origOrderRate2 = 2.2345;

        // Inserting the new FXSingleLegDeal to DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            swapDeal = ( FXSwapDeal ) this.uow.registerNewObject( swapDeal );
            swapDeal.setInitialTriggerRate( 12.4567 );
            swapDeal.setCustomerLimit( 897 );
            swapDeal.setDisplayEnabled( false );
            swapDeal.setStatus( 'T' );
            swapDeal.setServerManagedOrder( true );
            System.out.println( "Setting MarketRate" );
            swapDeal.getNearDealLeg().setMarketSpotRate( 10.00 );
            swapDeal.getNearDealLeg().setMarketRate( 10.00 );
            swapDeal.getNearDealLeg().setOriginalOrderAmount( origOrderAmt1 );
            swapDeal.getNearDealLeg().setOriginalOrderRate( origOrderRate1 );
            swapDeal.getFarDealLeg().setMarketSpotRate( 10.00 );
            swapDeal.getFarDealLeg().setMarketRate( 10.00 );
            swapDeal.getFarDealLeg().setOriginalOrderAmount( origOrderAmt2 );
            swapDeal.getFarDealLeg().setOriginalOrderRate( origOrderRate2 );
            uow.commit();
        }
        catch ( Exception exc )
        {
            log.error( "DealPTestC.testDealPersistence() Exception 1 - " + exc );
        }

        // Reloading the FXSingleLegDeal from DB.
        try
        {
            swapDeal.getDealLegs().clear();
            uow = this.getPersistenceSession().acquireUnitOfWork();
            swapDeal = ( FXSwapDeal ) uow.refreshObject( swapDeal );
            assertEquals( swapDeal.getInitialTriggerRate(), 12.4567 );
            assertFalse( swapDeal.isDisplayEnabled() );
            assertEquals( swapDeal.getCustomerLimit(), 897d );
            assertTrue( swapDeal.isServerManagedOrder() );
            assertEquals( swapDeal.getNearDealLeg().getMarketSpotRate(), 10.00 );
            assertEquals( swapDeal.getNearDealLeg().getMarketRate(), 10.00 );
            assertEquals( swapDeal.getNearDealLeg().getOriginalOrderAmount(), origOrderAmt1 );
            assertEquals( swapDeal.getNearDealLeg().getOriginalOrderRate(), origOrderRate1 );
            assertEquals( swapDeal.getFarDealLeg().getOriginalOrderAmount(), origOrderAmt2 );
            assertEquals( swapDeal.getFarDealLeg().getOriginalOrderRate(), origOrderRate2 );
            assertEquals( swapDeal.getDealLegs().size(), 2 );
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "DealPTestC.testDealPersistence() Exception 2 - " + exc );
        }

    }

    public void testParentDealReportQuery()
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            String txId1 = "FXI1" + System.currentTimeMillis();
            String txId2 = "FXI2" + System.currentTimeMillis();
            FXSingleLegDeal origParentDeal = FXDealingFactory.newFXSingleLegDeal();
            origParentDeal.setNamespace( new NamespaceC() );
            FXSingleLegDeal parentDeal = ( FXSingleLegDeal ) uow.registerObject( origParentDeal );
            FXSingleLegDeal childDeal = ( FXSingleLegDeal ) uow.registerObject( FXDealingFactory.newFXSingleLegDeal() );
            childDeal.setParent( parentDeal );
            parentDeal.getDeals().add( childDeal );
            parentDeal.setTransactionId( txId1 );
            childDeal.setTransactionId( txId2 );
            uow.commit();

            // now do a report query for
            ReportQuery query = new ReportQuery();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "transactionId" ).equal( txId2 );
            query.setSelectionCriteria( expr );
            query.setReferenceClass( FXSingleLegDealC.class );
            query.addAttribute( "parentDealEntityID", eb.get( "dealFXSingleLegParent" ).get( Entity.ObjectID ) );
            query.addAttribute( "parentDealTransactionId", eb.get( "dealFXSingleLegParent" ).get( "transactionId" ) );
            query.useCollectionClass( ArrayList.class );

            // execute the query
            Collection result = ( ArrayList<ReportQueryResult> ) PersistenceFactory.newSession().executeQuery( query );
            log( "result=" + result );
            assertEquals( "result should not be empty.", result.isEmpty(), false );

        }
        catch ( Exception e )
        {
            fail( "testParentDealReportQuery", e );
        }
    }
}
