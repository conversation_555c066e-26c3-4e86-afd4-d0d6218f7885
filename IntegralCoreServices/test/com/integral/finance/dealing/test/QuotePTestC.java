package com.integral.finance.dealing.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.QuoteC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.test.util.DealingTestUtilC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


public class QuotePTestC
        extends PTestCaseC
{
    static String name = "Trade Test";
    Vector users = null;

    public QuotePTestC( String name )
    {
        super( name );
    }

    public void testFieldsPersistence()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            Quote testQuote = new QuoteC();
            assertFalse( testQuote.isSpreadsApplied() );
            Quote registeredQuote = ( Quote ) uow.registerObject( testQuote );
            registeredQuote.setPriceType( 10 );
            long t = System.currentTimeMillis();
            registeredQuote.setRateId( "Test+" + t );
            registeredQuote.setSpreadsApplied( true );
            uow.commit();

            testQuote.setPriceType( 0 );
            // refresh the trade and retrieve the maker request.
            testQuote = ( Quote ) session.refreshObject( testQuote );
            log( "testQuote=" + testQuote + ",testQuote.prcType=" + testQuote.getPriceType() );
            assertEquals( "price type check.", testQuote.getPriceType(), 10 );
            log( "testQuote=" + testQuote + ",testQuote.rateId=" + testQuote.getRateId() );
            assertEquals( "rate id check.", testQuote.getRateId(), "Test+" + t );
            assertTrue( testQuote.isSpreadsApplied() );
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }

    public void testBidOfferQuotePricesArray()
    {
        try
        {
            Quote emptyQuote = DealingFactory.newQuote();
            assertNull( emptyQuote.getBidQuotePrices() );
            assertNull( emptyQuote.getOfferQuotePrices() );

            Currency ccy1 = CurrencyFactory.getCurrency( "EUR" );
            Currency ccy2 = CurrencyFactory.getCurrency( "USD" );
            double[] bidRates = new double[]{1.2345, 1.2346, 1.2347};
            double[] offerRates = new double[]{1.2355, 1.2356};
            double[] bidLimits = new double[]{1000, 2000, 3000};
            double[] offerLimits = new double[]{1000, 2000};
            Quote quote = DealingTestUtilC.createTestMultipleTierSpotQuote( null, ccy1, ccy2, bidRates, offerRates, bidLimits, offerLimits, false );
            DealingPrice[] bidDps = quote.getBidQuotePrices();
            DealingPrice[] offerDps = quote.getOfferQuotePrices();
            assertEquals( "bid dealing prices should have three ", bidDps.length, 3 );
            assertEquals( "offer dealing prices should have two ", offerDps.length, 2 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
