package com.integral.finance.dealing.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.ExecutionFlags;
import com.integral.finance.dealing.PegTypes;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.QuoteC;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.TimeInForce;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.finance.trade.TradeClassification;
import com.integral.finance.trade.TradeClassificationC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import org.apache.commons.beanutils.PropertyUtils;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Vector;


public class RequestPTestC
        extends PTestCaseC
{
    static String name = "Trade Test";

    public RequestPTestC( String name )
    {
        super( name );
    }

    public void testChildrenRequests()
    {
        try
        {
            String portfolioRefID = "TESTID";
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            Request request = ( Request ) uow1.registerObject( new RequestC() );
            request.setTransactionID( "Test" + System.nanoTime() );
            Request childrenRequest = ( Request ) uow1.registerObject( new RequestC() );
            childrenRequest.setTransactionID( "Test" + System.nanoTime() );
            request.addChildRequest( childrenRequest );
            request.setPortfolioRefId( portfolioRefID );
            uow1.commit();


            Collection requests = request.getChildrenRequests();
            log( "children requests=" + requests );
            log( "children requests.size=" + requests.size() );
            assertEquals( "children requests should be 1", requests.size(), 1 );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            request = ( Request ) uow2.registerObject( request );
            Request childrenRequest1 = ( Request ) uow2.registerObject( new RequestC() );
            childrenRequest1.setTransactionID( "Test" + System.nanoTime() );
            request.addChildRequest( childrenRequest1 );
            uow2.commit();

            Collection requests2 = request.getChildrenRequests();
            log( "children requests2=" + requests2 );
            log( "children requests2.size=" + requests2.size() );
            assertEquals( "children requests2 should be 2", requests2.size(), 2 );

            request = ( Request ) getPersistenceSession().refreshObject( request );
            Collection requests3 = request.getChildrenRequests();
            log( "children requests3=" + requests3 );
            log( "children requests3.size=" + requests3.size() );
            assertEquals( "children requests3 should be 2", requests3.size(), 2 );

            UnitOfWork uow3 = getPersistenceSession().acquireUnitOfWork();
            Request request1 = ( Request ) uow1.registerObject( new RequestC() );
            request1.setTransactionID( "Test" + System.nanoTime() );
            int children = request1.getChildrenRequests().size();
            log( "children=" + children );
            uow3.commit();

            int children1 = request1.getChildrenRequests().size();
            log( "children1=" + children1 );
        }
        catch ( Exception e )
        {
            fail( "testChildrenRequests", e );
        }
    }

    public void testOCORequests()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( RequestC.class );

            //do basis sanity if data is getting persisted; testing addGroupOCORequest
            Request test1 = ( Request ) uow.registerObject( new RequestC() );
            Request test2 = ( Request ) uow.registerObject( new RequestC() );
            Request test3 = ( Request ) uow.registerObject( new RequestC() );

            test1.setTransactionID( "1Test" + System.nanoTime() );
            test2.setTransactionID( "2Test" + System.nanoTime() );
            test3.setTransactionID( "3Test" + System.nanoTime() );

            test1.addGroupOCORequest( test2 );
            test1.addGroupOCORequest( test3 );

            assertEquals( "OCO Group requests should be 3", test1.getGroupOCORequests().size(), 3 );
            assertEquals( "OCO Group requests should be 2", test1.getOCORequests().size(), 2 );
            assertEquals( "OCO Group requests should be 3", test2.getGroupOCORequests().size(), 3 );
            assertEquals( "OCO Group requests should be 2", test2.getOCORequests().size(), 2 );
            assertEquals( "OCO Group requests should be 3", test3.getGroupOCORequests().size(), 3 );
            assertEquals( "OCO Group requests should be 2", test3.getOCORequests().size(), 2 );

            uow.commit();
            session = PersistenceFactory.newSession();
            test1 = ( Request ) session.refreshObject( test1 );
            test2 = ( Request ) session.refreshObject( test2 );
            test3 = ( Request ) session.refreshObject( test3 );

            log( "Request=" + test1 + ",test1.getOCOGroupRequests.size=" + test1.getGroupOCORequests().size() );
            assertEquals( "OCO Group requests should be 3", test1.getGroupOCORequests().size(), 3 );
            assertEquals( "OCO Group requests should be 2", test1.getOCORequests().size(), 2 );
            assertEquals( "OCO Group requests should be 3", test2.getGroupOCORequests().size(), 3 );
            assertEquals( "OCO Group requests should be 2", test2.getOCORequests().size(), 2 );
            assertEquals( "OCO Group requests should be 3", test3.getGroupOCORequests().size(), 3 );
            assertEquals( "OCO Group requests should be 2", test3.getOCORequests().size(), 2 );

            session = PersistenceFactory.newSession();
            uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( RequestC.class );

            //do basis sanity   test setGroupOCORequests
            test1 = ( Request ) uow.registerObject( new RequestC() );
            test2 = ( Request ) uow.registerObject( new RequestC() );
            test1.setTransactionID( "Test" + System.nanoTime() );
            test2.setTransactionID( "Test" + System.nanoTime() );

            Collection<Request> col = new ArrayList<Request>();
            col.add( test1 );
            col.add( test2 );
            test1.setGroupOCORequests( col );
            uow.commit();
            session = PersistenceFactory.newSession();
            test1 = ( Request ) session.refreshObject( test1 );
            test2 = ( Request ) session.refreshObject( test2 );

            log( "Request=" + test1 + ",test1.getOCOGroupRequests.size=" + test1.getGroupOCORequests().size() );
            assertEquals( "OCO Group requests should be 2", test1.getGroupOCORequests().size(), 2 );
            assertEquals( "OCO Group requests should be 1", test1.getOCORequests().size(), 1 );
            assertEquals( "OCO Group requests should be 2", test2.getGroupOCORequests().size(), 2 );
            assertEquals( "OCO Group requests should be 1", test2.getOCORequests().size(), 1 );


            session = PersistenceFactory.newSession();
            uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( RequestC.class );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }

    }

    public void testLookup()
    {
        log( "testLookup" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( Request.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                Request req = ( Request ) objs.elementAt( i );
                printRequest( req, i );
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testLookup" );
        }
    }

    public void testCSV()
    {
        List<String> cols = new ArrayList<String>();
        cols.add( "objectID" );
        cols.add( "status" );
        cols.add( "acceptedQuote.objectID" );
        cols.add( "requestClassification.shortName" );
        cols.add( "organization.shortName" );
        cols.add( "user.shortName" );

        log( "testCSV" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( Request.class );

            String header = "NUM,";
            for ( int j = 0; j < cols.size(); j++ )
            {
                header += cols.get( j );
                if ( j < cols.size() - 1 )
                {
                    header += ",";
                }
            }
            log( "-----------------------------------------------" );
            log( header );

            for ( int i = 0; i < objs.size(); i++ )
            {
                Request req = ( Request ) objs.elementAt( i );
                String column = i + ",";
                for ( int j = 0; j < cols.size(); j++ )
                {
                    try
                    {
                        String attr = cols.get( j );
                        column += PropertyUtils.getProperty( req, attr );
                    }
                    catch ( Exception e )
                    {
                        column += "**ERROR**";
                    }
                    if ( j < cols.size() - 1 )
                    {
                        column += ",";
                    }
                }
                log( column );
            }
            log( "-----------------------------------------------" );
            log( "csvTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCSV" );
        }
    }

    public void testInsert()
    {
        log( "testInsert" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            Request r = new RequestC();
            r.setTransactionID( "Test" + System.nanoTime() );
            Quote q = new QuoteC();
            r.setAcceptedQuote( q );

            uow.registerObject( r );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsert" );
        }
    }

    public void testMultipleInserts()
    {
        log( "testMultipleInserts" );
        try
        {
            ArrayList<Request> list = new ArrayList<Request>( 10 );
            ArrayList<Request> regList = new ArrayList<Request>( 10 );
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            // create requests
            for ( int i = 0; i < 10; i++ )
            {
                Request r = new RequestC();
                r.setTransactionID( "Test" + System.nanoTime() );
                r.setStatus( 'T' );
                Quote q = new QuoteC();
                q.setStatus( 'T' );
                r.setAcceptedQuote( q );
                list.add( r );
                Request rr = ( Request ) uow.registerObject( r );
                regList.add( rr );
            }

            uow.commit();

            // print requests
            for ( int i = 0; i < 10; i++ )
            {
                Request r = list.get( i );
                log.info( "request #" + i + ": " + r );
            }
            for ( int i = 0; i < 10; i++ )
            {
                Request r = regList.get( i );
                log.info( "registered request #" + i + ": " + r );
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testMultipleInserts" );
        }
    }


    public void testArray()
    {
        log( "arrayTest" );
        try
        {
            Request r = new RequestC();
            r.setTransactionID( "Test" + System.nanoTime() );

            Quote q1 = new QuoteC();
            r.getQuotes().add( q1 );

            log( "--------- new request -----------" );
            log( "request #" + r.hashCode() );
            log( "request quote list #" + r.getQuotes().hashCode() );
            Iterator it = r.getQuotes().iterator();
            while ( it.hasNext() )
            {
                Quote qi = ( Quote ) it.next();
                log( "  quote #" + qi.hashCode() );
            }

            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.registerObject( r );
            uow1.commit();

            log( "--------- after first commit -----------" );
            log( "request #" + r.hashCode() );
            log( "request quote list #" + r.getQuotes().hashCode() );
            it = r.getQuotes().iterator();
            while ( it.hasNext() )
            {
                Quote qi = ( Quote ) it.next();
                log( "  quote #" + qi.hashCode() );
            }

            Quote q2 = new QuoteC();
            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            Request r2 = ( Request ) uow2.registerObject( r );
            r2.getQuotes().add( q2 );
            // ((RequestC)r2).myTrans = 99;
            // log("request before commit #" + ((RequestC)r2).myTrans);
            uow2.commit();

            log( "--------- after second commit -----------" );
            log( "request #" + r.hashCode() );
            log( "request quote list #" + r.getQuotes().hashCode() );
            // log("request after commit #" + ((RequestC)r).myTrans);
            it = r.getQuotes().iterator();
            while ( it.hasNext() )
            {
                Quote qi = ( Quote ) it.next();
                log( "  quote #" + qi.hashCode() );
            }


        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "arrayTest" );
        }
    }

    public void testDeletion()
    {
        log( "testDeletion" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr;

            String id = System.getProperty( "REQUESTID" );
            if ( id == null )
            {
                expr = eb.get( "objectID" ).lessThan( 10000 );
            }
            else
            {
                expr = eb.get( "objectID" ).equal( id );
            }
            Request request = ( Request ) getPersistenceSession().readObject( Request.class, expr );

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            log( "\tdelete request " + request );
            Iterator it = request.getQuotes().iterator();
            while ( it.hasNext() )
            {
                Quote quote = ( Quote ) it.next();
                log( "\t\tdelete quote " + quote );
                uow.deleteObject( quote );
            }
            uow.deleteObject( request );

            uow.commit();

            log( "testDeletion" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testDeletion" );
        }
    }


    private void printRequest( Request req, int i )
    {
        log( "request #" + i );
        log( "\t object   = " + req );
        log( "\t objectID = " + req.getObjectID() );
        log( "\t accepted quote ID = " + req.getAcceptedQuote() );
    }

    /**
     * Check sorting requests by ID
     */
    public void testSortById()
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression sortExpr = eb.get( "objectID" );

        lookupOrdered( "testSortById", eb, sortExpr );
    }

    /**
     * Check sorting requests by associated LE
     */
    public void testSortByLE()
    {
        // Request request = new RequestC();
        // request.getLegalEntity();

        ExpressionBuilder eb = new ExpressionBuilder();
        Expression sortExpr = eb.get( "legalEntity" ).get( "objectID" );

        lookupOrdered( "testSortByLE", eb, sortExpr );

        ExpressionBuilder eb2 = new ExpressionBuilder();
        Expression sortExpr2 = eb2.getField( "legalEntityId" );

        lookupOrdered( "testSortByLE", eb2, sortExpr2 );
    }

    /**
     * Check sorting requests by request prices
     */
    public void testSortByPrices()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();

            // Expression priceExpr = eb.anyOf("requestPrices");
            Expression priceExpr = eb.anyOf( "fxLegDealingPrices" );

            Expression sortExpr = priceExpr.get( "objectID" );

            Expression expr1 = priceExpr.get( "name" ).equal( "FOO" );
            Expression expr2 = priceExpr.get( "dealtAmount" ).equal( 10 );
            Expression expr = expr1.and( expr2 );

            ReadAllQuery query = new ReadAllQuery();
            query.setReferenceClass( RequestC.class );
            query.setSelectionCriteria( expr );
            query.addOrdering( sortExpr );

            Vector objs = ( Vector ) getPersistenceSession().executeQuery( query );
            for ( int i = 0; i < objs.size(); i++ )
            {
                Request req = ( Request ) objs.elementAt( i );
                printRequest( req, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testSortByPrices" );
        }
    }

    private void lookupOrdered( String testName, ExpressionBuilder eb, Expression sortExpr )
    {
        try
        {
            Expression expr = eb.get( "status" ).equal( 'A' );

            ReadAllQuery query = new ReadAllQuery();
            query.setReferenceClass( Request.class );
            query.setSelectionCriteria( expr );
            query.addOrdering( sortExpr );

            Vector objs = ( Vector ) getPersistenceSession().executeQuery( query );
            for ( int i = 0; i < objs.size(); i++ )
            {
                Request req = ( Request ) objs.elementAt( i );
                printRequest( req, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( testName );
        }
    }


    /**
     * Tests cloning of requests
     */
    public void testClone()
    {
        try
        {
            Request r = new RequestC();

            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();

            Request r1 = ( Request ) uow1.registerObject( r );
            Request r2 = ( Request ) uow1.registerObject( r );

            assertEquals( r1.hashCode(), r2.hashCode() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testClone" );
        }
    }

    /**
     * Check sorting requests by custom fields
     */
    public void testSortByCustomField()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();

            Expression cfExpr = eb.anyOf( "customFields" );
            Expression sortExpr = cfExpr.get( "longValue" );
            Expression expr = cfExpr.get( "key" ).equal( "FOO" );

            ReadAllQuery query = new ReadAllQuery();
            query.setReferenceClass( RequestC.class );
            query.setSelectionCriteria( expr );
            query.addOrdering( sortExpr );

            Session session = getPersistenceSession();
            Vector objs = ( Vector ) session.executeQuery( query );
            for ( int i = 0; i < objs.size(); i++ )
            {
                Request req = ( Request ) objs.elementAt( i );
                printRequest( req, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testSortByCustomField" );
        }
    }

    public void testTradeClassification()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( TradeClassificationC.class );
            TradeClassification spotTrdClsf = ( TradeClassification ) new ReadNamedEntityC().execute( TradeClassification.class, "FXSpot" );
            short executionFlags = ExecutionFlags.ALLOW_BROKER_CROSS;
            int prcType = Quote.PRICE_TYPE_MULTI_TIER;
            Date effDate = new Date();
            Request testRequest = new RequestC();
            testRequest.setTransactionID( "Test" + System.nanoTime() );

            Request registeredRequest = ( Request ) uow.registerObject( testRequest );
            registeredRequest.setTradeClassification( spotTrdClsf );
            registeredRequest.setEffectiveDate( effDate );
            registeredRequest.setExecutionFlags( executionFlags );
            registeredRequest.setPriceType( prcType );
            registeredRequest.setTriggeredBy( ( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() ).getOrganization() );
            long now2 = System.currentTimeMillis();
            registeredRequest.setTriggerReachedAt( new Timestamp( now2 + 89 ) );
            uow.commit();

            testRequest.setTradeClassification( null );
            // refresh the trade and retrieve the maker request.
            testRequest = ( Request ) session.refreshObject( testRequest );
            log( "testRequest=" + testRequest + ",testRequest.tradeClsf=" + testRequest.getTradeClassification()
                    + ",effectiveDate=" + effDate + ",executionFlags=" + executionFlags + ",priceType=" + prcType );
            assertEquals( "trade classification should not be null.", testRequest.getTradeClassification() != null, true );
            assertEquals( "execution flags should be the same.", testRequest.getExecutionFlags(), executionFlags );
            assertEquals( "effective date should be the same", testRequest.getEffectiveDate().getTime(), effDate.getTime() );
            assertEquals( "price type should be the same.", testRequest.getPriceType(), prcType );
            assertEquals( testRequest.getTriggeredBy(), ( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() ).getOrganization() );
            assertEquals( testRequest.getTriggerReachedAt().getTime(), now2 + 89 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testUnsolicitedCancelBy()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( UserC.class );
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi2mm1" );
            String snapshot = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
            Request testRequest = new RequestC();
            Request registeredRequest = ( Request ) uow.registerObject( testRequest );
            registeredRequest.setTransactionID( "Test" + System.nanoTime() );
            registeredRequest.setUnsolicitedCancelBy( user );
            registeredRequest.setMarketSnapshot( snapshot );
            uow.commit();

            testRequest.setUnsolicitedCancelBy( null );
            // refresh the trade and retrieve the maker request.
            testRequest = ( Request ) session.refreshObject( testRequest );
            log( "testRequest=" + testRequest + ",testRequest.unsolicitedCancelBy=" + testRequest.getUnsolicitedCancelBy()
                    + ",marketSnapshot=" + snapshot );
            assertEquals( "unsolicited cancel by user should not be null.", testRequest.getUnsolicitedCancelBy(), user );
            assertEquals( "execution flags should be the same.", testRequest.getMarketSnapshot(), snapshot );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testPriceTakingIntended()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( UserC.class );
            Request testRequest = new RequestC();
            Request registeredRequest = ( Request ) uow.registerObject( testRequest );
            registeredRequest.setTransactionID( "Test" + System.nanoTime() );
            boolean priceTake = true;
            log( "default value=" + registeredRequest.isPriceTakingIntended() );
            registeredRequest.setPriceTakingIntended( priceTake );
            uow.commit();

            // refresh the trade and retrieve the maker request.
            testRequest = ( Request ) session.refreshObject( testRequest );
            log( "testRequest=" + testRequest + ",testRequest.priceTakingIntended=" + testRequest.isPriceTakingIntended() );
            assertEquals( "price taking intended field should be false.", priceTake, testRequest.isPriceTakingIntended() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testRequestExecutionFlags()
    {
        try
        {
            Request request = new RequestC();
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be zero", request.getExecutionFlags(), 0 );
            request.setExecutionFlags( ( short ) ( request.getExecutionFlags() | ExecutionFlags.ALLOW_BROKER_CROSS ) );
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be equal to allow broker", ExecutionFlags.ALLOW_BROKER_CROSS, request.getExecutionFlags() );

            // check the execution flag is set correctly
            short val = ( short ) ( request.getExecutionFlags() & ExecutionFlags.ALLOW_BROKER_CROSS );
            log( "check execution flag=" + val );
            boolean isAllowBrokerCross = val > 0;
            log( "isAllowBrokerCross=" + isAllowBrokerCross );
            assertEquals( "AllowBrokerCross should be true", isAllowBrokerCross, true );
            short val1 = ( short ) ( request.getExecutionFlags() & ExecutionFlags.ALLOW_ORDER_MATCH );
            log( "check execution flag=" + val1 );
            assertEquals( "AllowBrokerCross should be true", isAllowBrokerCross, true );
            boolean isAllowOrderMatch = val1 > 0;
            log( "allowOrderMatch=" + isAllowOrderMatch );
            assertEquals( "allowOrderMatch should be false", isAllowOrderMatch, false );

            // now check double setting.
            request.setExecutionFlags( ( short ) ( request.getExecutionFlags() | ExecutionFlags.ALLOW_BROKER_CROSS ) );
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be equal to allow broker", ExecutionFlags.ALLOW_BROKER_CROSS, request.getExecutionFlags() );

            // now add order match as well.
            request.setExecutionFlags( ( short ) ( request.getExecutionFlags() | ExecutionFlags.ALLOW_ORDER_MATCH ) );
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be equal to allow broker + allow order", ExecutionFlags.ALLOW_BROKER_CROSS + ExecutionFlags.ALLOW_ORDER_MATCH, request.getExecutionFlags() );

            // remove the allow broker cross
            request.setExecutionFlags( ( short ) ( request.getExecutionFlags() & ~ExecutionFlags.ALLOW_BROKER_CROSS ) );
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be equal to allow order", ExecutionFlags.ALLOW_ORDER_MATCH, request.getExecutionFlags() );

            // now remove it again
            request.setExecutionFlags( ( short ) ( request.getExecutionFlags() & ~ExecutionFlags.ALLOW_BROKER_CROSS ) );
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be equal to allow order", ExecutionFlags.ALLOW_ORDER_MATCH, request.getExecutionFlags() );

            // set the at rate flag
            request.setExecutionFlags( ( short ) ( request.getExecutionFlags() | ExecutionFlags.AT_RATE ) );
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be equal to allow order match + at rate", ExecutionFlags.ALLOW_ORDER_MATCH + ExecutionFlags.AT_RATE, request.getExecutionFlags() );

            // remove the flag at rate
            request.setExecutionFlags( ( short ) ( request.getExecutionFlags() & ~ExecutionFlags.AT_RATE ) );
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be equal to allow order.", ExecutionFlags.ALLOW_ORDER_MATCH, request.getExecutionFlags() );

            // now remove it again
            request.setExecutionFlags( ( short ) ( request.getExecutionFlags() & ~ExecutionFlags.AT_RATE ) );
            log( "executionFlag=" + request.getExecutionFlags() );
            assertEquals( "execution flag should be equal to allow order", ExecutionFlags.ALLOW_ORDER_MATCH, request.getExecutionFlags() );
        }
        catch ( Exception e )
        {
            fail( "testExecutionFlags.exception", e );
        }
    }

    public void testOriginatingCptyUser()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            Request testRequest = new RequestC();
            Request registeredRequest = ( Request ) uow.registerObject( testRequest );
            registeredRequest.setTransactionID( "Test" + System.nanoTime() );
            assertNull( registeredRequest.getOriginatingCptyId() );
            assertNull( registeredRequest.getOriginatingUserId() );
            LegalEntity cpty = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntity.class, "FI1-le1" );
            long origCptyId = cpty.getObjectID();
            registeredRequest.setOriginatingCptyId( origCptyId );
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );
            long origUserId = user.getObjectID();
            registeredRequest.setOriginatingUserId( origUserId );
            uow.commit();

            testRequest.setOriginatingCptyId( null );
            testRequest.setOriginatingUserId( null );
            // refresh the trade and retrieve the maker request.
            testRequest = ( Request ) session.refreshObject( testRequest );
            log( "testRequest=" + testRequest + ",testTrade.origCptyId=" + testRequest.getOriginatingCptyId() );
            assertEquals( "orig cpty id should not be null.", testRequest.getOriginatingCptyId().longValue(), origCptyId );

            log( "testRequest=" + testRequest + ",testRequest.origUserId=" + testRequest.getOriginatingUserId() );
            assertEquals( "orig user id should not be null.", testRequest.getOriginatingUserId().longValue(), origUserId );
        }
        catch ( Exception e )
        {
            fail( "Exception in testOriginatingCptyUser : ", e );
        }
    }

    public void testDefaultTimeInForce()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            Request testRequest = DealingFactory.newRequest();
            assertEquals( "default time in force", testRequest.getTimeInForce(), TimeInForce.UNDEFINED );
            Request registeredRequest = ( Request ) uow.registerObject( testRequest );
            registeredRequest.setTransactionID( "Test" + System.nanoTime() );
            registeredRequest.setStatus( 'T' );
            assertEquals( "default time in force", registeredRequest.getTimeInForce(), TimeInForce.UNDEFINED );


            uow.commit();

            // refresh the trade


            testRequest = ( Request ) session.refreshObject( testRequest );
            log( "testRequest=" + testRequest + ",testRequest.timeInForce=" + testRequest.getTimeInForce() );
            assertEquals( "default time in force should be undefined.", testRequest.getTimeInForce(), TimeInForce.UNDEFINED );


        }
        catch ( Exception e )
        {
            fail( "testDefaultTimeInForce", e );
        }
    }

    //for 39259
    public void testGetTrade()
    {
        Trade trd = new TradeC();
        Request req = new RequestC();
        List trades = new ArrayList();
        trades.add( trd );
        req.setTrades( trades );
        System.out.println( req.getTrade() );  // if this does not throw ClassCastException that means test case is passed

    }


    public void testFieldsPersistence()
    {
        try
        {
            String portfolioRefID = "TESTREFID";
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( TradingPartyC.class );
            uow.addReadOnlyClass( UserC.class );
            Request testRequest = new RequestC();


            // validate default values
            assertNull( testRequest.getExternalSpread() );
            assertNull( testRequest.getPreferredProviders() );
            assertFalse( testRequest.isPersistentOrder() );
            assertFalse( testRequest.isServerManagedOrder() );
            assertNull( testRequest.getSecondarySortPriority() );
            assertNull( testRequest.getCoverOrderIds() );
            assertNull( testRequest.getCoverOrderIds() );
            assertNull( testRequest.getCreatedBusinessDate() );
            assertNull( testRequest.getCoveredRequestUser() );
            assertFalse( testRequest.isCancelReceived() );
            assertNull( testRequest.getCancelledBy() );
            assertNull( testRequest.getOrderExecutionEndTime() );
            assertNull( testRequest.getOrderExecutionStartTime() );
            assertEquals( 0, testRequest.getTwapSliceInterval() );
            assertFalse( testRequest.isRandomizeTwapSliceInterval() );
            assertEquals( 0.0, testRequest.getTwapSliceSize() );
            assertFalse( testRequest.isRandomizeTwapSliceSize() );
            assertFalse( testRequest.isTwapFOKSlice() );
            assertEquals( 0, testRequest.getActionOnOrderExpitation() );
            assertNull( testRequest.getLinkedOrderId() );
            assertNull( testRequest.getOrderExecutionStrategyName() );
            assertEquals( 0.0, testRequest.getTwapSliceRegularSize() );
            assertEquals( 0.0, testRequest.getPassiveTime() );
            assertEquals( 0.0, testRequest.getTwapSliceTopOfBookPercent() );
            assertEquals( 0, testRequest.getTwapMinimumSliceInterval() );
            assertEquals( 0, testRequest.getResponseType() );
            assertEquals( 0.0, testRequest.getPegOffset() );
            assertEquals( 0.0, testRequest.getPegOffsetIncrement() );
            assertEquals( 0, testRequest.getPegOffsetIncrementInterval() );
            assertFalse( testRequest.isRandomizePegOffsetIncrement() );
            assertFalse( testRequest.isAmended() );
            assertNull( testRequest.getClientVersion() );
            assertNull( testRequest.getTakerReferenceId() );

            assertFalse( testRequest.isSEF() );
            assertFalse( testRequest.isFSRRequest() );
            assertFalse( testRequest.isSpaces() );
            assertFalse( testRequest.isNetRequest() );
            assertNull( testRequest.getCustomerStreamId() );
            assertNull( testRequest.getExternalAccount() );
            assertNull ( testRequest.getCustomParameters () );

            Request registeredRequest = ( Request ) uow.registerObject( testRequest );
            registeredRequest.setTransactionID( "Test" + System.nanoTime() );
            registeredRequest.setPortfolioRefId( portfolioRefID );
            // validate registered request values
            assertNull( registeredRequest.getExpiryTime() );
            assertNull( registeredRequest.getVirtualServer() );
            assertNull( registeredRequest.getExternalSpread() );
            assertNull( registeredRequest.getPrimeBrokerTradingParty() );
            assertFalse( registeredRequest.isServerManagedOrder() );
            assertNull( registeredRequest.getCoveredRequestUser() );
            assertNull( registeredRequest.getCancelledBy() );
            assertNull( registeredRequest.getOrderExecutionEndTime() );
            assertNull( registeredRequest.getOrderExecutionStartTime() );
            assertEquals( 0, registeredRequest.getTwapSliceInterval() );
            assertFalse( registeredRequest.isRandomizeTwapSliceInterval() );
            assertEquals( 0.0, registeredRequest.getTwapSliceSize() );
            assertFalse( registeredRequest.isRandomizeTwapSliceSize() );
            assertFalse( registeredRequest.isTwapFOKSlice() );
            assertEquals( 0, registeredRequest.getActionOnOrderExpitation() );
            assertNull( registeredRequest.getLinkedOrderId() );
            assertNull( registeredRequest.getOrderExecutionStrategyName() );
            assertEquals( 0.0, registeredRequest.getTwapSliceRegularSize() );
            assertEquals( 0.0, registeredRequest.getPassiveTime() );
            assertEquals( 0.0, registeredRequest.getTwapSliceTopOfBookPercent() );
            assertEquals( 0, registeredRequest.getTwapMinimumSliceInterval() );
            assertEquals( 0, registeredRequest.getResponseType() );
            assertEquals( 0.0, registeredRequest.getPegOffset() );
            assertEquals( 0.0, registeredRequest.getPegOffsetIncrement() );
            assertEquals( 0, registeredRequest.getPegOffsetIncrementInterval() );
            assertFalse( registeredRequest.isRandomizePegOffsetIncrement() );
            assertFalse( registeredRequest.isAmended() );
            assertNull( registeredRequest.getTakerReferenceId() );
            assertFalse( registeredRequest.isFSRRequest() );
            assertFalse( registeredRequest.isNetRequest() );
            assertNull( registeredRequest.getExternalAccount() );
            assertNull ( registeredRequest.getCustomParameters () );

            //Populate request
            Timestamp ts = new Timestamp( System.currentTimeMillis() );
            registeredRequest.setExpiryTime( ts );
            registeredRequest.setVirtualServer( "TestServer" );
            registeredRequest.setOriginatingOrderId( "TestOrder" );
            registeredRequest.setCoverExecutionMethod( "TestEM" );
            registeredRequest.setExternalSpread( 0.0005 );
            registeredRequest.setPersistentOrder( true );
            registeredRequest.setPreferredProviders( "CITI:FI2" );
            registeredRequest.setSecondarySortPriority( "SPT" );
            registeredRequest.setCoverOrderIds( "FXI100-FXI101" );
            registeredRequest.setCoveredOrderId( "FXI99" );
            registeredRequest.setISExtSys( "setISExtSys" );
            registeredRequest.setCancelledBy( "Test@TestCase" );

            Timestamp twapexecEndTs = new Timestamp( System.currentTimeMillis() );
            Timestamp twapexecStartTs = new Timestamp( System.currentTimeMillis() );
            registeredRequest.setOrderExecutionEndTime( twapexecEndTs );
            registeredRequest.setOrderExecutionStartTime( twapexecStartTs );
            registeredRequest.setTwapSliceInterval( 5000 );
            registeredRequest.setRandomizeTwapSliceInterval( true );
            registeredRequest.setTwapFOKSlice( true );
            registeredRequest.setTwapSliceSize( 500000.0 );
            registeredRequest.setRandomizeTwapSliceSize( true );
            registeredRequest.setActionOnOrderExpitation( 10 );
            registeredRequest.setTwapSliceTopOfBookPercent( 0.5 );
            registeredRequest.setTwapMinimumSliceInterval( 10000 );
            registeredRequest.setResponseType( 1 );
            registeredRequest.setExternalForwardSpread( 0.0001 );
            registeredRequest.setExternalFarForwardSpread( 0.0002 );

            registeredRequest.setPegType( PegTypes.PRIMARY );
            registeredRequest.setPegOffset( 5.0 );
            registeredRequest.setPegOffsetIncrement( 0.5 );
            registeredRequest.setPegOffsetIncrementInterval( 30 );
            registeredRequest.setRandomizePegOffsetIncrement( true );

            Date execDate = new Date();
            Timestamp execDateTs = new Timestamp( execDate.getTime() );
            registeredRequest.setExecutionDateTime( execDate );
            IdcDate busDate = DateTimeFactory.newDate();
            registeredRequest.setCreatedBusinessDate( busDate );

            TradingParty fiTp = ( TradingParty ) new ReadNamedEntityC().execute( TradingPartyC.class, "FI1-le1" );
            registeredRequest.setPrimeBrokerTradingParty( fiTp );
            registeredRequest.setServerManagedOrder( true );

            User fiUser = ( User ) new ReadNamedEntityC().execute( UserC.class, "fi2mm1" );
            registeredRequest.setCoveredRequestUser( fiUser );

            String takerTradeIds = "Test100-Test101";
            registeredRequest.setTakerTradeIds( takerTradeIds );

            registeredRequest.setLinkedOrderId( "TestCaseLinkedOrderId" );
            registeredRequest.setOrderExecutionStrategyName( "TestCaseOrderExecutionStrategyName" );
            registeredRequest.setTwapSliceRegularSize( 100003.0 );
            registeredRequest.setPassiveTime( 1000019.0 );
            registeredRequest.setAmended( true );
            registeredRequest.setClientVersion( "1.0" );
            registeredRequest.setTakerReferenceId( "Taker Reference ID" );

            registeredRequest.setSEF( true );
            registeredRequest.setFSRRequest( true );
            registeredRequest.setSpaces( true );
            registeredRequest.setNetRequest( true );
            registeredRequest.setOutrightLimitOrder( true );
            final String contractId = "EUR_USD_130611";
            registeredRequest.setContractId( contractId );
            registeredRequest.setRFSBroadcast( true );
            registeredRequest.setOrderSession( true );
            registeredRequest.setAllocation( true );
            registeredRequest.setExternalAccount("testAcc1");

            String stream = "TestStream";
            registeredRequest.setCustomerStreamId( stream );
            registeredRequest.setCustomParameters ( "TestCustomParameters" );

            //Commit object now
            uow.commit();

            testRequest.setVirtualServer( null );
            testRequest.setExpiryTime( null );
            testRequest.setOriginatingOrderId( null );
            testRequest.setCoverExecutionMethod( null );
            testRequest.setExternalSpread( null );
            testRequest.setPrimeBrokerTradingParty( null );
            testRequest.setPersistentOrder( false );
            testRequest.setPreferredProviders( null );
            testRequest.setSecondarySortPriority( null );
            testRequest.setCoverOrderIds( null );
            testRequest.setCoveredOrderId( null );
            testRequest.setCreatedBusinessDate( null );
            testRequest.setCoveredRequestUser( null );
            testRequest.setTakerTradeIds( null );
            testRequest.setCancelledBy( null );
            testRequest.setPegType( Character.MIN_VALUE );
            testRequest.setPegOffset( 0.0 );
            testRequest.setPegOffsetIncrement( 0.0 );
            testRequest.setPegOffsetIncrementInterval( 0 );
            testRequest.setRandomizePegOffsetIncrement( false );
            testRequest.setPortfolioRefId( null );
            testRequest.setOutrightLimitOrder( false );
            testRequest.setContractId( null );
            testRequest.setExternalRequestId("abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefgh" +
                    "ijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnop" +
                    "qrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz");
            // refresh the trade and retrieve the maker request.
            testRequest = ( Request ) session.refreshObject( testRequest );
            log( "testRequest=" + testRequest + ",expiryTime=" + testRequest.getExpiryTime() + ",virtualServer="
                    + testRequest.getVirtualServer() + ",originatingOrderId=" + testRequest.getOriginatingOrderId()
                    + ",coverExecMethod=" + testRequest.getCoverExecutionMethod() + ",externalSpread=" + testRequest.getExternalSpread()
                    + ",primeBrokerTradingParty=" + testRequest.getPrimeBrokerTradingParty() + ",serverManagedOrder=" + testRequest.isServerManagedOrder() );

            log( "persistent=" + testRequest.isPersistentOrder() + ",preferredProviders=" + testRequest.getPreferredProviders()
                    + ",secSortPriority=" + testRequest.getSecondarySortPriority() + ",coverOrder=" + testRequest.getCoverOrderIds()
                    + ",coveredOrder=" + testRequest.getCoveredOrderId() + ",createdBusinessDate=" + testRequest.getCreatedBusinessDate()
                    + ",coveredRequestUser=" + testRequest.getCoveredRequestUser() + ",takerTradeIds=" + testRequest.getTakerTradeIds() );

            assertEquals( "Expiry should not be null.", testRequest.getExpiryTime(), ts );
            assertEquals( "Virtual Server should not be null.", testRequest.getVirtualServer(), "TestServer" );
            assertEquals( "Originating OrderId should not be null.", testRequest.getOriginatingOrderId(), "TestOrder" );
            assertEquals( "Cover execution method should not be null.", testRequest.getCoverExecutionMethod(), "TestEM" );
            assertEquals( "External Spread should not be null", testRequest.getExternalSpread(), 0.0005 );
            assertEquals( "Prime Broker trading party not be null", testRequest.getPrimeBrokerTradingParty(), fiTp );
            assertEquals( "persistent order should be true", testRequest.isPersistentOrder(), true );
            assertEquals( "preferred providers", testRequest.getPreferredProviders(), "CITI:FI2" );
            assertEquals( "secondary sort priority", testRequest.getSecondarySortPriority(), "SPT" );
            assertEquals( "cover order tx ids", testRequest.getCoverOrderIds(), "FXI100-FXI101" );
            assertEquals( "covered order tx id", testRequest.getCoveredOrderId(), "FXI99" );
            assertEquals( "created business date", testRequest.getCreatedBusinessDate(), busDate );
            assertEquals( testRequest.getISExtSys(), "setISExtSys" );
            assertEquals( testRequest.getExecutionDateTime(), execDateTs );
            assertTrue( testRequest.isServerManagedOrder() );
            assertEquals( testRequest.getCoveredRequestUser(), fiUser );
            assertEquals( testRequest.getTakerTradeIds(), takerTradeIds );
            assertEquals( "Cancelled By", testRequest.getCancelledBy(), "Test@TestCase" );
            assertEquals( twapexecEndTs, testRequest.getOrderExecutionEndTime() );
            assertEquals( twapexecStartTs, testRequest.getOrderExecutionStartTime() );
            assertEquals( 5000, testRequest.getTwapSliceInterval() );
            assertTrue( testRequest.isRandomizeTwapSliceInterval() );
            assertTrue( testRequest.isTwapFOKSlice() );
            assertEquals( 500000.0, testRequest.getTwapSliceSize() );
            assertTrue( testRequest.isRandomizeTwapSliceSize() );
            assertEquals( 10, testRequest.getActionOnOrderExpitation() );
            assertEquals( "TestCaseLinkedOrderId", testRequest.getLinkedOrderId() );
            assertEquals( "TestCaseOrderExecutionStrategyName", testRequest.getOrderExecutionStrategyName() );
            assertEquals( 100003.0, testRequest.getTwapSliceRegularSize() );
            assertEquals( 1000019.0, testRequest.getPassiveTime() );
            assertEquals( 0.5, testRequest.getTwapSliceTopOfBookPercent() );
            assertEquals( 10000, testRequest.getTwapMinimumSliceInterval() );
            assertEquals( 1, testRequest.getResponseType() );
            assertEquals( 0.0001, testRequest.getExternalForwardSpread() );
            assertEquals( 0.0002, testRequest.getExternalFarForwardSpread() );

            assertEquals( PegTypes.PRIMARY, testRequest.getPegType() );
            assertEquals( 5.0, testRequest.getPegOffset() );
            assertEquals( 0.5, testRequest.getPegOffsetIncrement() );
            assertEquals( 30, testRequest.getPegOffsetIncrementInterval() );
            assertTrue( testRequest.isRandomizePegOffsetIncrement() );
            assertTrue( testRequest.isAmended() );
            assertEquals( testRequest.getClientVersion(), "1.0" );
            assertEquals( testRequest.getTakerReferenceId(), "Taker Reference ID" );

            assertTrue( testRequest.isSEF() );
            assertTrue( testRequest.isFSRRequest() );
            assertTrue( testRequest.isSpaces() );
            assertTrue( testRequest.isNetRequest() );
            assertTrue( testRequest.isOutrightLimitOrder() );
            assertEquals( "Portfolio Reference ID", testRequest.getPortfolioRefId(), portfolioRefID );
            assertEquals( contractId, testRequest.getContractId() );
            assertTrue( testRequest.isRFSBroadcast() );
            assertTrue( testRequest.isOrderSession() );
            assertTrue( testRequest.isAllocation() );
            assertEquals("testAcc1" ,testRequest.getExternalAccount() );
            assertEquals( stream, testRequest.getCustomerStreamId() );
            assertEquals ( "TestCustomParameters", testRequest.getCustomParameters () );
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence : ", e );
        }
    }

    public void testMiFIDFieldsPersistence()
    {
        try
        {
            Organization mtfOrg = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "INTEGRALMTF" );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( TradingPartyC.class );
            uow.addReadOnlyClass( UserC.class );
            Request testRequest = new RequestC();


            // validate default values
            Request registeredRequest = ( Request ) uow.registerObject( testRequest );
            registeredRequest.setTransactionID( "Test" + System.nanoTime() );
            registeredRequest.setMiFIDExecutionVenue ( mtfOrg );


            //Commit object now
            uow.commit();

            // refresh the trade and retrieve the maker request.
            testRequest = ( Request ) session.refreshObject( testRequest );
            assertEquals ( mtfOrg, testRequest.getMiFIDExecutionVenue () );
            assertEquals ( mtfOrg.getShortName (), testRequest.getMTFVenueName () );
        }
        catch ( Exception e )
        {
            fail( "Exception in testMiFIDFieldsPersistence : ", e );
        }
    }

}
