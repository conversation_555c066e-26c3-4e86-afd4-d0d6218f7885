package com.integral.finance.dealing.test;

import com.integral.finance.dw.StagingEventTimes;
import com.integral.finance.dw.StagingEventTimesC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.Date;
import java.util.UUID;

/**
 * User: pulkit
 * Date: 11/21/12
 * Time: 10:36 AM
 */
public class StagingEventTimesPTestC extends PTestCaseC {
    static String name = StagingEventTimesC.class.getName();

    public StagingEventTimesPTestC(String name) {
        super(name);
    }

    public void testStagingEventTimes() {
        try {
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            StagingEventTimesC object = new StagingEventTimesC();

            updateObject(object);
            StagingEventTimes stagingEventTimes = (StagingEventTimes) uow1.registerObject(object);

            uow1.commit();


        } catch (Exception e) {
            fail("testStagingEventTimes", e);
        }
    }

    private void updateObject(StagingEventTimesC object) {
        Date date = new Date();
        Timestamp timestamp = new Timestamp(date.getTime());
        object.setOAOrderPublishedByAdaptor(timestamp);
        object.setOAOrderMatchedByAdaptor(timestamp);   //met.getOrderMatchedByServer()
        object.setRateEffective(timestamp);    //MatchEventPQ.QuoteDescriptor.QuoteEventTimes.getRateEffective()
        object.setRateSentByAdpt(timestamp);   //MatchEventPQ.QuoteDescriptor.QuoteEventTimes.getRateSentByAdapter()
        object.setQuoteCreatedByApp(timestamp); //MatchEventPQ.QuoteDescriptor.QuoteEventTimes.getQuoteCreated()
        object.setRateSentByApp(timestamp);   // ratesentbyIS // MatchEventPQ.QuoteEventTimes
        object.setRateQueriedByUser(timestamp); //met.clientQueriedRate
        object.setTransactionId(UUID.randomUUID().toString()); // trade id
        object.setRateRcvdByAdpt(timestamp); //MatchEventPQ.QuoteDescriptor.QuoteEventTimes.getRateReceivedByAdapter()
        object.setRateRcvdByApp(timestamp); //   rateReceivedbyis
        object.setRateRcvdByProxy(timestamp); // todo
        object.setRateSentByProxy(timestamp); // todo
        object.setRateRcvdByUser(timestamp);  // todo
        object.setRateDisplayedByUser(timestamp); // todo
        object.setRateAcceptedByUser(timestamp);  // todo
        object.setAcceptanceSentByUser(timestamp);    // todo
        object.setAcceptanceSentByApp(timestamp); //TradeEventTimes.getAcceptanceSentByAppTime()
        object.setAcceptanceRcvdByAdaptor(timestamp);// TradeEventTimes.getAcceptanceReceivedByAdaptorTime()
        object.setAcceptanceSentByAdaptor(timestamp);//TradeEventTimes.getAcceptanceSentByAdaptorTime()
        object.setVerificationRcvdFromProvider(timestamp); // TET.responseReceivedByAdaptorTime
        object.setVerificationRcvdByApp(timestamp);    //TET.responsereceived by App
        object.setVerificationSentByApp(timestamp);    //TET.responseSentByApp
        object.setRejectionRcvdFromProvider(timestamp); //??
        object.setRejectionRcvdByApp(timestamp);        //??
        object.setRejectionSentByApp(timestamp);        //??
        object.setRateAggregatedByApp(timestamp);        //MatchEventTimes.getRateAggregatedByServer()
        object.setOrderRcvdByApp(timestamp);  //MatchEventTimes.acceptanceReceivedByIS()
        object.setOrderMatchedByApp(timestamp); //MatchEventTimes.getOrderMatchedByServer()
        object.setOAOrderRcvdByAdaptor(timestamp); // ORET.receiveTime
        object.setConfirmedByUser(timestamp); //tradeConfirmationTime
        object.setNextRateRcvdByApp(timestamp);   //getNextRateReceivedByIS() - Method in class com.integral.model.dealing.MatchEventPQ.QuoteDescriptor.QuoteEventTimes

        //object.set

    }
}
