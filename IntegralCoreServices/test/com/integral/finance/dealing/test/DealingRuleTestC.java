package com.integral.finance.dealing.test;

import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.rule.RuleSet;
import com.integral.test.TestCaseC;

import java.util.Collection;
import java.util.Iterator;

public class DealingRuleTestC extends TestCaseC
{
    static String name = "Dealing Rule Test";

    public DealingRuleTestC( String aName )
    {
        super( aName );
    }

    public void testDealingRule()
    {
    }

    public static void dispatchQuotes( RuleSet ruleSet, WorkflowMessage requestMessage )
            throws Exception
    {
        Request request = ( Request ) requestMessage.getObject();
        Collection quotes = request.getQuotes();
        Iterator quoteIterator = quotes.iterator();
        while ( quoteIterator.hasNext() )
        {
            Quote quote = ( Quote ) quoteIterator.next();
            WorkflowMessage quoteMessage = copy( requestMessage );
            quoteMessage.setObject( quote );
            ruleSet.execute( quoteMessage );
        }
    }

    public static void dispatchRequest( RuleSet ruleSet, WorkflowMessage quoteMessage )
            throws Exception
    {
        Quote quote = ( Quote ) quoteMessage.getObject();
        Request request = quote.getRequest();
        WorkflowMessage requestMessage = copy( quoteMessage );
        requestMessage.setObject( request );
        ruleSet.execute( requestMessage );
    }

    public static WorkflowMessage copy( WorkflowMessage originalMessage )
    {
        WorkflowMessage newMessage = MessageFactory.newWorkflowMessage();
        newMessage.setEvent( originalMessage.getEvent() );
        newMessage.setSender( originalMessage.getSender() );
        newMessage.setObject( originalMessage.getObject() );
        newMessage.setTransactionMessage( originalMessage.getTransactionMessage() );
        return newMessage;
    }
}

