package com.integral.finance.dealing.test;

import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.dealing.ContingencyType;
import com.integral.finance.dealing.ContingentRequestGroup;
import com.integral.finance.dealing.ContingentRequestGroupC;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Vector;


public class ContingentRequestGroupPTestC
        extends PTestCaseC
{
    static String name = "Contingent Request Group Test";
    Vector users = null;

    public ContingentRequestGroupPTestC( String name )
    {
        super( name );
    }

    public void testInsert()
    {
        log( "testInsert" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            ContingentRequestGroup rcg = new ContingentRequestGroupC();
            rcg = ( ContingentRequestGroup ) uow.registerObject( rcg );
            Request r = new RequestC();
            r = ( Request ) uow.registerObject( r );
            r.setTransactionID( "Test" + System.nanoTime() );
            r.setOTORequestGroup( rcg );
            rcg.setPrimaryRequest( r );
            uow.commit();

            // refresh the group and request.
            rcg = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg );
            r = ( Request ) getPersistenceSession().refreshObject( r );
            assertEquals( rcg.getPrimaryRequest(), r );
            assertNotNull( rcg );
            assertNotNull( rcg.getCreatedDate() );
            assertNotNull( rcg.getModifiedDate() );
            assertNotNull( rcg.getGUID() );
            assertNotNull( rcg.getPrimaryRequest() );
            assertEquals( rcg.getPrimaryRequest(), r );

            assertNotNull( r );
            assertNotNull( r.getCreatedDate() );
            assertNotNull( r.getModifiedDate() );
            assertNotNull( r.getGUID() );
            assertNotNull( r.getOTORequestGroup() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsert" );
        }
    }


    public void testContingentRequests()
    {
        try
        {
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            ContingentRequestGroup rcg1 = new ContingentRequestGroupC();
            rcg1 = ( ContingentRequestGroup ) uow1.registerObject( rcg1 );
            ContingentRequestGroup rcg2 = new ContingentRequestGroupC();
            rcg2 = ( ContingentRequestGroup ) uow1.registerObject( rcg2 );

            Request request1 = ( Request ) uow1.registerObject( new RequestC() );
            Request request2 = ( Request ) uow1.registerObject( new RequestC() );
            request1.setTransactionID( "Test" + System.nanoTime() );
            request2.setTransactionID( "Test" + System.nanoTime() );
            List<Request> requests = new ArrayList<Request>();
            requests.add( request1 );
            requests.add( request2 );
            request1.setParentOCORequestGroup( rcg1 );
            request2.setParentOCORequestGroup( rcg1 );
            request2.setParentOTORequestGroup( rcg2 );
            rcg1.setOCORequests( requests );
            rcg2.getOTORequests().add( request2 );

            uow1.commit();

            // set the value holders to null.
            request1.setParentOCORequestGroup( null );
            request1.setParentOTORequestGroup( null );
            request2.setParentOCORequestGroup( null );
            request2.setParentOTORequestGroup( null );

            // refresh the groups.
            rcg1 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg1 );
            rcg2 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg2 );
            request1 = ( Request ) getPersistenceSession().refreshObject( request1 );
            request2 = ( Request ) getPersistenceSession().refreshObject( request2 );
            assertNotNull( rcg1 );
            assertNotNull( rcg2 );
            assertNotNull( request1 );
            assertNotNull( request2 );
            assertEquals( request1.getParentOCORequestGroup(), rcg1 );
            assertEquals( request2.getParentOTORequestGroup(), rcg2 );
            assertEquals( request2.getParentOCORequestGroup(), rcg1 );
            assertEquals( request2.getParentOTORequestGroup(), rcg2 );
            assertNull( request1.getParentOTORequestGroup() );


            Collection ocoRequests = rcg1.getOCORequests();
            log( "ocoRequests=" + ocoRequests );
            log( "ocoRequests.size=" + ocoRequests.size() );
            assertEquals( "ocoRequests should be 2", ocoRequests.size(), 2 );
            assertTrue( ocoRequests.contains( request1 ) );
            assertTrue( ocoRequests.contains( request2 ) );
            assertTrue( rcg1.getOTORequests().isEmpty() );

            Collection otoRequests = rcg2.getOTORequests();
            log( "otoRequests=" + otoRequests );
            log( "otoRequests.size=" + otoRequests.size() );
            assertEquals( "otoRequests should be 1", otoRequests.size(), 1 );
            assertFalse( otoRequests.contains( request1 ) );
            assertTrue( otoRequests.contains( request2 ) );
            assertTrue( rcg2.getOCORequests().isEmpty() );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            rcg2 = ( ContingentRequestGroup ) uow2.registerObject( rcg2 );
            Request request3 = ( Request ) uow2.registerObject( new RequestC() );
            request3.setTransactionID( "Test" + System.nanoTime() );
            request3.setParentOTORequestGroup( rcg2 );
            rcg2.getOTORequests().add( request3 );
            uow2.commit();

            rcg1 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg1 );
            rcg2 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg2 );
            request1 = ( Request ) getPersistenceSession().refreshObject( request1 );
            request2 = ( Request ) getPersistenceSession().refreshObject( request2 );
            request3 = ( Request ) getPersistenceSession().refreshObject( request3 );
            otoRequests = rcg2.getOTORequests();
            log( "otoRequests=" + otoRequests );
            log( "otoRequests.size=" + otoRequests.size() );
            assertEquals( "otoRequests should be 2", otoRequests.size(), 2 );
            assertFalse( otoRequests.contains( request1 ) );
            assertTrue( otoRequests.contains( request2 ) );
            assertTrue( otoRequests.contains( request3 ) );
            assertTrue( rcg2.getOCORequests().isEmpty() );

            UnitOfWork uow3 = getPersistenceSession().acquireUnitOfWork();
            rcg2 = ( ContingentRequestGroup ) uow3.registerObject( rcg2 );
            request3 = ( Request ) uow3.registerObject( request3 );
            request3.setParentOTORequestGroup( null );
            rcg2.getOTORequests().remove( request3 );
            uow3.commit();

            rcg1 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg1 );
            rcg2 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg2 );
            request1 = ( Request ) getPersistenceSession().refreshObject( request1 );
            request2 = ( Request ) getPersistenceSession().refreshObject( request2 );
            request3 = ( Request ) getPersistenceSession().refreshObject( request3 );
            otoRequests = rcg2.getOTORequests();
            log( "otoRequests=" + otoRequests );
            log( "otoRequests.size=" + otoRequests.size() );
            assertEquals( "otoRequests should be 1", otoRequests.size(), 1 );
            assertFalse( otoRequests.contains( request1 ) );
            assertTrue( otoRequests.contains( request2 ) );
            assertFalse( otoRequests.contains( request3 ) );
            assertTrue( rcg2.getOCORequests().isEmpty() );
        }
        catch ( Exception e )
        {
            fail( "testContingentRequests", e );
        }
    }

    public void testParentChildrenRelationship()
    {
        try
        {
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            ContingentRequestGroup rcg1 = new ContingentRequestGroupC();
            rcg1 = ( ContingentRequestGroup ) uow1.registerObject( rcg1 );
            ContingentRequestGroup rcg2 = new ContingentRequestGroupC();
            rcg2 = ( ContingentRequestGroup ) uow1.registerObject( rcg2 );

            Request request1 = ( Request ) uow1.registerObject( new RequestC() );
            Request request2 = ( Request ) uow1.registerObject( new RequestC() );
            request1.setTransactionID( "Test" + System.nanoTime() );
            request2.setTransactionID( "Test" + System.nanoTime() );
            List<Request> requests = new ArrayList<Request>();
            requests.add( request1 );
            requests.add( request2 );
            request1.setParentOCORequestGroup( rcg1 );
            request2.setParentOCORequestGroup( rcg1 );
            request2.setParentOTORequestGroup( rcg2 );
            rcg1.setOCORequests( requests );
            rcg2.getOTORequests().add( request2 );
            rcg1.getChildrenRequestGroups().add( rcg2 );
            rcg2.setParentRequestGroup( rcg1 );

            uow1.commit();

            // refresh the groups.
            rcg1 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg1 );
            rcg2 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg2 );
            request1 = ( Request ) getPersistenceSession().refreshObject( request1 );
            request2 = ( Request ) getPersistenceSession().refreshObject( request2 );
            assertNotNull( rcg1 );
            assertNotNull( rcg2 );
            assertNotNull( request1 );
            assertNotNull( request2 );
            assertEquals( request1.getParentOCORequestGroup(), rcg1 );
            assertEquals( request2.getParentOCORequestGroup(), rcg1 );
            assertEquals( request2.getParentOTORequestGroup(), rcg2 );
            assertNull( request1.getParentOTORequestGroup() );
            assertNotNull( rcg2.getParentRequestGroup() );
            assertNull( rcg1.getParentRequestGroup() );
            assertTrue( rcg1.getChildrenRequestGroups().contains( rcg2 ) );
            assertTrue( rcg2.getChildrenRequestGroups().isEmpty() );
            assertNotNull( rcg2.getParentRequestGroup() );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            ContingentRequestGroup rcg3 = ( ContingentRequestGroup ) uow2.registerObject( new ContingentRequestGroupC() );
            rcg1 = ( ContingentRequestGroup ) uow2.registerObject( rcg1 );
            Request request3 = ( Request ) uow2.registerObject( new RequestC() );
            request3.setTransactionID( "Test" + System.nanoTime() );
            request3.setParentOTORequestGroup( rcg3 );
            rcg3.getOTORequests().add( request3 );
            rcg1.getChildrenRequestGroups().add( rcg3 );
            uow2.commit();

            //refresh all the objects
            rcg1 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg1 );
            rcg2 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg2 );
            rcg2 = ( ContingentRequestGroup ) getPersistenceSession().refreshObject( rcg2 );
            request1 = ( Request ) getPersistenceSession().refreshObject( request1 );
            request2 = ( Request ) getPersistenceSession().refreshObject( request2 );
            assertNotNull( rcg1 );
            assertNotNull( rcg2 );
            assertNotNull( rcg3 );
            assertNotNull( request1 );
            assertNotNull( request2 );
            assertEquals( request1.getParentOCORequestGroup(), rcg1 );
            assertEquals( request2.getParentOCORequestGroup(), rcg1 );
            assertEquals( request2.getParentOTORequestGroup(), rcg2 );
            assertEquals( request3.getParentOTORequestGroup(), rcg3 );
            assertNull( request1.getParentOTORequestGroup() );
            assertNull( rcg1.getParentRequestGroup() );
            assertTrue( rcg1.getChildrenRequestGroups().contains( rcg2 ) );
            assertTrue( rcg2.getChildrenRequestGroups().isEmpty() );
            assertNotNull( rcg2.getParentRequestGroup() );
            assertNotNull( rcg3.getParentRequestGroup() );

        }
        catch ( Exception e )
        {
            fail( "testParentChildrenRelationship", e );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            ContingentRequestGroup testRcg = new ContingentRequestGroupC();
            ContingentRequestGroup registeredTestRcg = ( ContingentRequestGroup ) uow.registerObject( testRcg );
            assertTrue( registeredTestRcg.getState() == 0 );
            assertTrue( registeredTestRcg.getContingentType() == 0 );
            registeredTestRcg.setState( 100 );
            registeredTestRcg.setContingentType( ContingencyType.OTO );
            uow.commit();

            // refresh the trade and retrieve the maker request.
            testRcg = ( ContingentRequestGroup ) session.refreshObject( testRcg );
            log( "testRcg=" + testRcg + ",state=" + testRcg.getState() + ",type=" + testRcg.getContingentType() );
            assertEquals( "State should 100.", testRcg.getState(), 100 );
            assertEquals( "ContingentType should be OTO", testRcg.getContingentType(), ContingencyType.OTO );
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }
}
