package com.integral.finance.dealing.test;

import com.integral.persistence.Entity;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

/**
 * Created by IntelliJ IDEA.
 * User: pandit
 * Date: 12/9/11
 * Time: 1:49 PM
 * To change this template use File | Settings | File Templates.
 */
public class EntityPTestCaseC extends PTestCaseC
{
    public EntityPTestCaseC( final String aName )
    {
        super( aName );
    }

    public EntityPTestCaseC( final String aName, final boolean initJms )
    {
        super( aName, initJms );
    }

    protected void checkEntityDeletion( Entity entity )
    {
        // Deleteing the entry.
        long objectId = entity.getObjectID();
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.deleteObject( entity );
            uow.commit();
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "RequestEventPTestC.testRequestEventPersistence() Exception 3 - " + exc );
        }

        // Retrieve the object again to make sure object is deleted.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder builder = new ExpressionBuilder();
            Expression expr = builder.get( "objectID" ).equal( objectId );
            uow = this.getPersistenceSession().acquireUnitOfWork();
            Entity entity1 = ( Entity ) uow.readObject( entity.getClass(), expr );
            assertEquals( entity1, null );
        }
        catch ( Exception exc )
        {
            log.error( "RequestEventPTestC.testRequestEventPersistence() Exception 4 - " + exc );
        }
    }

}
