package com.integral.finance.dealing.test;

import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairC;
import com.integral.finance.dealing.RequestClassification;
import com.integral.finance.dealing.RequestClassificationC;
import com.integral.finance.dw.StagingOrdersCube;
import com.integral.finance.dw.StagingOrdersCubeC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.trade.TradeClassification;
import com.integral.finance.trade.TradeClassificationC;
import com.integral.query.QueryFactory;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.workflow.State;
import com.integral.workflow.StateC;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Timestamp;
import java.util.Date;
import java.util.UUID;

/**
 * Created with IntelliJ IDEA.
 * User: pulkit
 * Date: 11/26/12
 * Time: 10:17 AM
 */
public class StagingOrdersCubePTestC extends PTestCaseC {
    static String name = StagingOrdersCubeC.class.getName();

    public StagingOrdersCubePTestC(String name) {
        super(name);
    }

    public void testStagingOrdersCube() {
        try {
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            StagingOrdersCubeC object = new StagingOrdersCubeC();

            updateObject(object);
            StagingOrdersCube stagingEventTimes = (StagingOrdersCube) uow1.registerObject(object);

            uow1.commit();


        } catch (Exception e) {
            fail("testStagingOrdersCube", e);
        }
    }

    private void updateObject(StagingOrdersCubeC object) throws IdcNoSuchObjectException {

        Date date = new Date();
        Timestamp timestamp = new Timestamp(date.getTime());
        String ccyPair = "EUR/USD";
        Currency baseCcy = CurrencyFactory.getCurrency(ccyPair.substring(0, ccyPair.indexOf('/')));
        Currency varCcy = CurrencyFactory.getCurrency(ccyPair.substring(ccyPair.indexOf('/') + 1));
        CurrencyPair cp = new CurrencyPairC();
        cp.setBaseCurrency( baseCcy );
        cp.setVariableCurrency( varCcy );
        FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );
        assert   fxConv.getFXRateBasis( ccyPair) != null;
        Organization org1 = (Organization) namedEntityReader.execute(OrganizationC.class, "FI1");
        assert (LegalEntityC) new ReadNamedEntityC().execute(LegalEntityC.class, "FI1-le1") != null;

        object.setExpirationTime(timestamp);  //expirationTime - Variable in class com.integral.model.dealing.OrderRequestEventTimes
        object.setUser((User) new ReadNamedEntityC().execute(User.class, "fi2mm1")); //orderRequet.getUSer
        object.setVirtualServer("TestServer");  //orderRequest.getVirtualServer
        object.setCurrencyPair(fxConv.getFXRateBasis( ccyPair));//getCurrencyPair
        object.setBidOfferMode(1);  //bidOfferMode - Variable in class com.integral.model.dealing.MatchEventPQ.QuoteDescriptor
        object.setRequestClassification((RequestClassification) QueryFactory.getQueryService().find(RequestClassificationC.class, "QUOTED"));
        object.setChannel("DNET/PD");  //getChannel
        object.setTimeInForce(1); //getTimeInForce
        object.setOrderId("Test-orderId");  //get_id
        object.setTradeClassification((TradeClassification) namedEntityReader.execute(TradeClassificationC.class, "FXSpot"));   //classification - Variable in class com.integral.model.dealing.Trade
        object.setCoveredOrderId(UUID.randomUUID().toString()); //getCoveredOrderId() - Method in class com.integral.model.dealing.SingleLegOrder
        object.setOrderExecutionStartTime(timestamp);//getOrderExecutionStartTime() - Method in class com.integral.model.dealing.OrderStrategy
        object.setOrderExecutionEndTime(timestamp); //getOrderExecutionEndTime() - Method in class com.integral.model.dealing.OrderStrategy
        object.setTwapSliceInterval(1);   //orderStrategy
        object.setTwapSliceSize(1);
        object.setTwapSliceRegularSize(1);
        object.setOrderExecutionStrategyName("test-strategy");   //getStrategyName() - Method in class com.integral.model.dealing.OrderStrategy
        object.setPegType('C');  //orderTrigger
        object.setPegOffset(1.1);
        object.setPegOffsetIncrement(.0005);   //orderStrategy
        object.setPegOffsetIncrementInterval(1111111L);
        object.setStrategyExecutionSuspended(true);        //isExecutionSuspended() - Method in class com.integral.model.dealing.OrderRequest
        object.setOfferRate(2.00005);      //spotRate
        object.setMarketRange(.0000033);  //getMarketRange() - Method in class com.integral.model.dealing.OrderRequest
        object.setClientOrderId("test-client-id"); //clientReferenceId
        object.setBidRate(2.4443);  //derive based on bid/offer mode
        object.setBestMarketPrice(4.44444);  //setBestMarketPrice(double) - Method in class com.integral.model.dealing.OrderRequest.RequestLeg
        object.setValueDate(timestamp); //getValueDate() - Method in class com.integral.model.dealing.OrderRequest.RequestLeg

        object.setTriggerPriceType(1);
        object.setMaxShowAmount(2.33222);  //getMaxShowAmount() - Method in class com.integral.model.dealing.SingleLegOrder
        object.setAmount(2.33333);   //getAmount() - Method in class com.integral.model.dealing.OrderRequest.RequestLeg
        object.setFilledAmount(1000000);  //getFilledAmount() - Method in class com.integral.model.dealing.OrderRequest.RequestLeg
        object.setStopPrice(2.0000); //getStopPrice() - Method in class com.integral.model.dealing.OrderRequest.RequestLeg
        object.setCustomerOrg(org1); //getOrganization

        object.setStopLossTriggered(true);
        object.setFixingTenor("test-tenor"); //getTenor() - Method in class com.integral.model.dealing.OrderRequest.RequestLeg
        object.setDealtCurrency(baseCcy); //getDealtCurrency
        object.setStopLossTriggerRate(2.333333);     //getStopLossTriggerRate() - Method in class com.integral.model.dealing.SingleLegOrder

        object.setStopLossTriggerPoints(.00011);   //0 for now
        object.setPriceImprovement(2.333222);    // orderrate - average rate -
        object.setCustomerLE((LegalEntity) new ReadNamedEntityC().execute(LegalEntityC.class, "FI1-le1"));       //order get legalentity
        object.setCreatedBusDate(timestamp);      //created businessdate
        object.setWorkflowState((State) new ReadNamedEntityC().execute(StateC.class, "RSACTIVE"));  //get the state object for the state and set the states
        object.setLastEvent(timestamp);   //modified date
        object.setSubmittedTime(timestamp);              //ordersubmissiontime
        object.setAvgRate(2.22222); //getAverageRate() - Method in class com.integral.model.dealing.OrderRequest.RequestLeg
        object.setStopLossInitTriggerRate(3.2222222); //getStopLossInitialTriggerRate() - Method in class com.integral.model.dealing.SingleLegOrder
        object.setOrigOrderId(UUID.randomUUID().toString());   //originating Order descriptor
        object.setReqGroupId(2);   // not supported //order contingency type  id
        object.setPersistent(true); //persistence
        object.setContingencyType(222);//getOrderContigency
        object.setTwapSliceTobPercent(1);   //orderstrategy
        object.setExpirationTimeInSeconds(20);  //getExpirationTime() - Method in class com.integral.model.dealing.OrderRequestEventTimes
        object.setTwapMinSliceInterval(1);   //orderstrategy
        object.setTwapSliceIntervalRandom(true);  //orderstrategy
        object.setMatchedQuotesVwap(2.222222);   // todo
        object.setPegOffsetIncrementRandom(true); // orderstrategy
        object.setFixingDate(timestamp);   //todo
        object.setBrokerOrgId(org1.getObjectID());//organization.getBrokerOrg
        object.setTwapSliceSizeRandom(true);            //orderstrategy
        object.setMarketSnapshot("GDFGDFGDFGDFGD_DFGDFGDF_GDFG_DFG_D");
    }
}
