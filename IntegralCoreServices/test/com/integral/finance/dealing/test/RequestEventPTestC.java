package com.integral.finance.dealing.test;


import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.RequestEvent;
import com.integral.finance.dealing.RequestEventC;
import com.integral.persistence.NamespaceGroupC;
import com.integral.persistence.cache.ReferenceDataCacheC;

import java.util.Collection;

/*
TEST class For BUG 32802
 */
public class RequestEventPTestC
        extends EntityPTestCaseC
{
    static String name = "RequestEvent Test";
    private static long requestObjectID;

    public RequestEventPTestC( String name )
    {
        super( name );
    }

    /*
     * testing the persisting of market range field.
     */
    public void testFieldsPersistence()
    {
        RequestEvent requestEvent = new RequestEventC();
        RequestEvent requestEvent1 = new RequestEventC();

        // validate default fields
        assertNull( requestEvent.getName() );
        assertNull( requestEvent.getParam1() );
        assertNull( requestEvent.getParam2() );
        assertNull( requestEvent.getParam3() );
        assertNull( requestEvent.getParam4() );
        assertNull( requestEvent.getParam5() );
        assertNull( requestEvent.getParam6() );
        assertNull( requestEvent.getParam7() );
        assertNull( requestEvent.getParam8() );
        assertNull( requestEvent.getRequest() );
        assertEquals( 0L, requestEvent.getSeqID() );

        // Inserting the new RequestEvent to DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            requestEvent = ( RequestEvent ) this.uow.registerNewObject( requestEvent );
            requestEvent1 = ( RequestEvent ) this.uow.registerNewObject( requestEvent1 );
            Request request = ( Request ) uow.registerObject( new RequestC() );
            request = ( Request ) this.uow.registerNewObject( request );

            updateRequestEvent( requestEvent, request );

            updateRequestEvent( requestEvent1, request );

            uow.commit();

            // refresh the requestEvent from database.
            requestEvent = ( RequestEvent ) getPersistenceSession().refreshObject( requestEvent );
            request = ( Request ) getPersistenceSession().refreshObject( request );
            requestObjectID = request.getObjectID();
            assertEquals( "ABCD", requestEvent.getName() );
            assertEquals( "a", requestEvent.getParam1() );
            assertEquals( "b", requestEvent.getParam2() );
            assertEquals( "c", requestEvent.getParam3() );
            assertEquals( "d", requestEvent.getParam4() );
            assertEquals( "e", requestEvent.getParam5() );
            assertEquals( "f", requestEvent.getParam6() );
            assertEquals( "g", requestEvent.getParam7() );
            assertEquals( "h", requestEvent.getParam8() );
            assertEquals( 0L, requestEvent.getSeqID() );
            assertEquals( request.getObjectId(), requestEvent.getRequest().getObjectId() );

            requestEvent1 = ( RequestEvent ) getPersistenceSession().refreshObject( requestEvent1 );
            assertEquals( "ABCD", requestEvent1.getName() );
            assertEquals( "a", requestEvent1.getParam1() );
            assertEquals( "b", requestEvent1.getParam2() );
            assertEquals( "c", requestEvent1.getParam3() );
            assertEquals( "d", requestEvent1.getParam4() );
            assertEquals( "e", requestEvent1.getParam5() );
            assertEquals( "f", requestEvent1.getParam6() );
            assertEquals( "g", requestEvent1.getParam7() );
            assertEquals( "h", requestEvent1.getParam8() );
            assertEquals( 1L, requestEvent1.getSeqID() );
            assertEquals( request.getObjectId(), requestEvent1.getRequest().getObjectId() );

        }
        catch ( Exception exc )
        {
            log.error( "RequestEventPTestC.testRequestEventPersistence() Exception 1 - " + exc );
        }

        // checkEntityDeletion( requestEvent );
        // checkEntityDeletion( requestEvent1 );


    }

    private void updateRequestEvent( RequestEvent requestEvent, Request request )
    {
        requestEvent.setName( "ABCD" );
        requestEvent.setParam1( "a" );
        requestEvent.setParam2( "b" );
        requestEvent.setParam3( "c" );
        requestEvent.setParam4( "d" );
        requestEvent.setParam5( "e" );
        requestEvent.setParam6( "f" );
        requestEvent.setParam7( "g" );
        requestEvent.setParam8( "h" );
        requestEvent.setRequest( request );
    }


    public void test2()
    {
        // Inserting the new RequestEvent to DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( NamespaceGroupC.class );
            Request request1 = ( Request ) ReferenceDataCacheC.getInstance().getEntityByObjectId( requestObjectID, RequestC.class );
            request1 = ( Request ) this.uow.registerObject( request1 );
            Collection<RequestEvent> requestEvents = request1.getRequestEvents();
            System.out.println( requestEvents.size() );

            RequestEvent requestEvent = ( RequestEvent ) this.uow.registerNewObject( new RequestEventC() );
            updateRequestEvent( requestEvent, request1 );

            uow.commit();

            requestEvent = ( RequestEvent ) getPersistenceSession().refreshObject( requestEvent );
            assertEquals( "ABCD", requestEvent.getName() );
            assertEquals( "a", requestEvent.getParam1() );
            assertEquals( "b", requestEvent.getParam2() );
            assertEquals( 2L, requestEvent.getSeqID() );
            assertEquals( request1.getObjectId(), requestEvent.getRequest().getObjectId() );
        }
        catch ( Exception exc )
        {
            log.error( "RequestEventPTestC.testRequestEventPersistence() Exception 1 - " + exc );
        }
    }

}
