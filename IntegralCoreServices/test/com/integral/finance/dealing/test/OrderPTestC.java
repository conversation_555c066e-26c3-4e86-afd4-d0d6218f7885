package com.integral.finance.dealing.test;


import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXSingleLegOrder;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TradeClassification;
import com.integral.is.common.ISConstantsC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.sql.Timestamp;
import java.util.Date;

/*
TEST class For BUG 32802
 */
public class OrderPTestC
        extends PTestCaseC
{
    static String name = "Order Test";

    public OrderPTestC( String name )
    {
        super( name );
    }

    /*
     * testing the persisting of market range field.
     */
    public void testFieldsPersistence()
    {
        FXSingleLegOrder singleLegOrder = FXDealingFactory.newFXSingleLegOrder();

        // validate default fields
        assertNull( singleLegOrder.getOrderExecutionEndTime() );
        assertNull( singleLegOrder.getOrderExecutionStartTime() );
        assertEquals( 0, singleLegOrder.getTwapSliceInterval() );
        assertFalse( singleLegOrder.isRandomizeTwapSliceInterval() );
        assertEquals( 0.0, singleLegOrder.getTwapSliceSize() );
        assertFalse( singleLegOrder.isRandomizeTwapSliceSize() );
        assertEquals( 0, singleLegOrder.getActionOnOrderExpitation() );
        assertNull( singleLegOrder.getLinkedOrderId() );
        assertNull( singleLegOrder.getOrderExecutionStrategyName() );
        assertEquals( 0.0, singleLegOrder.getTwapSliceRegularSize() );
        assertEquals( 0.0, singleLegOrder.getPassiveTime() );
        assertEquals( 0.0, singleLegOrder.getTwapSliceTopOfBookPercent() );
        assertEquals( 0, singleLegOrder.getTwapMinimumSliceInterval() );
        assertFalse( singleLegOrder.isAmended() );
        assertNull( singleLegOrder.getCounterpartyAOrg() );
        assertNull( singleLegOrder.getCustomerOrg() );
        assertNull( singleLegOrder.getPlacedByOrg() );

        ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();
        LegalEntity takerLe = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "FI1-le1" );
        Organization org1 = ( Organization ) namedEntityReader.execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) namedEntityReader.execute( OrganizationC.class, "FI2" );
        Organization org3 = ( Organization ) namedEntityReader.execute( OrganizationC.class, "CITI" );
        TradeClassification spotClassification = ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISConstantsC.TRD_CLSF_SP );

        // Inserting the new FXSingleLegOrder to DB.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( OrganizationC.class );
            singleLegOrder = ( FXSingleLegOrder ) this.uow.registerNewObject( singleLegOrder );
            singleLegOrder.setMarketRange( "ABCDE" );
            assertFalse( singleLegOrder.isServerManagedOrder() );
            assertNull( singleLegOrder.getOrderExecutionEndTime() );
            assertNull( singleLegOrder.getOrderExecutionStartTime() );
            assertEquals( 0, singleLegOrder.getTwapSliceInterval() );
            assertFalse( singleLegOrder.isRandomizeTwapSliceInterval() );
            assertEquals( 0.0, singleLegOrder.getTwapSliceSize() );
            assertFalse( singleLegOrder.isRandomizeTwapSliceSize() );
            assertEquals( 0, singleLegOrder.getActionOnOrderExpitation() );
            assertNull( singleLegOrder.getLinkedOrderId() );
            assertNull( singleLegOrder.getOrderExecutionStrategyName() );
            assertEquals( 0.0, singleLegOrder.getTwapSliceRegularSize() );
            assertEquals( 0.0, singleLegOrder.getPassiveTime() );
            assertEquals( 0.0, singleLegOrder.getTwapSliceTopOfBookPercent() );
            assertEquals( 0, singleLegOrder.getTwapMinimumSliceInterval() );
            assertFalse( singleLegOrder.isAmended() );
            assertFalse( singleLegOrder.isSEF() );
            assertNull( singleLegOrder.getTradeClassification() );
            assertFalse( singleLegOrder.isAmended() );

            double averageRate = 1.2345;
            double stopPrice = 1.2335;
            double origOrderAmt = 1000.00;
            double origOrderRate = 1.34556;

            singleLegOrder.setAverageRate(averageRate);
            singleLegOrder.setStatus( 'T' );
            singleLegOrder.setlegalEntityCptyA( takerLe );
            Date execDate = new Date();
            Timestamp execDateTs = new Timestamp( execDate.getTime() );
            singleLegOrder.setStopPrice( stopPrice );
            singleLegOrder.setExecutionDate( execDate );
            singleLegOrder.setServerManagedOrder( true );
            singleLegOrder.setExpiryTime( execDate );
            singleLegOrder.setSubmissionTime( execDate );
            Timestamp now = new Timestamp( System.currentTimeMillis() );
            singleLegOrder.setTriggerReachedAt( now );
            singleLegOrder.setCancelledBy( "TestC@TestCase" );

            Timestamp twapexecEndTs = new Timestamp( System.currentTimeMillis() );
            Timestamp twapexecStartTs = new Timestamp( System.currentTimeMillis() );
            singleLegOrder.setOrderExecutionEndTime( twapexecEndTs );
            singleLegOrder.setOrderExecutionStartTime( twapexecStartTs );
            singleLegOrder.setTwapSliceInterval( 5000 );
            singleLegOrder.setRandomizeTwapSliceInterval( true );
            singleLegOrder.setTwapSliceSize( 500000.0 );
            singleLegOrder.setRandomizeTwapSliceSize( true );
            singleLegOrder.setActionOnOrderExpitation( 10 );
            singleLegOrder.setTwapSliceTopOfBookPercent( 0.5 );
            singleLegOrder.setTwapMinimumSliceInterval( 10000 );
            singleLegOrder.setOrderExecutionStrategyName( "TestCaseOrderExecutionStrategyName" );
            singleLegOrder.setTwapSliceRegularSize( 100003.0 );
            singleLegOrder.setPassiveTime( 1000019.0 );
            singleLegOrder.setAmended( true );
            singleLegOrder.getFXDealLeg().setOriginalOrderAmount( origOrderAmt );
            singleLegOrder.getFXDealLeg().setOriginalOrderRate( origOrderRate );

            IdcDate date = DateTimeFactory.newDate();
            singleLegOrder.getFXDealLeg().setFixingDate( date );
            singleLegOrder.getFXDealLeg().setFixingTenor( Tenor.SPOT_TENOR );
            singleLegOrder.setCounterpartyAOrg( org1 );
            singleLegOrder.setCustomerOrg( org2 );
            singleLegOrder.setPlacedByOrg( org3 );
            String contractId = "EUR_USD_130611";
            singleLegOrder.setContractId( contractId );
            singleLegOrder.setSEF(false);
            singleLegOrder.setTradeClassification(spotClassification);

            uow.commit();

            // refresh the order from database.
            singleLegOrder = ( FXSingleLegOrder ) getPersistenceSession().refreshObject( singleLegOrder );
            assertEquals( "averageRate should be the same. averageRate=" + averageRate + ",order.avgRate=" + singleLegOrder.getAverageRate(), averageRate, singleLegOrder.getAverageRate() );
            assertEquals( "stop price should be the same. stop price=" + stopPrice + ",order.stopPrice=" + singleLegOrder.getStopPrice(), stopPrice, singleLegOrder.getStopPrice() );
            assertEquals( singleLegOrder.getExecutionDate(), execDateTs );
            assertTrue( singleLegOrder.isServerManagedOrder() );
            assertEquals( singleLegOrder.getTriggerReachedAt().getTime(), now.getTime() );
            assertEquals( singleLegOrder.getExpiryTime().getTime(), execDate.getTime() );
            assertEquals( singleLegOrder.getSubmissionTime().getTime(), execDate.getTime() );
            assertEquals( singleLegOrder.getCancelledBy(), "TestC@TestCase" );
            assertEquals( twapexecEndTs, singleLegOrder.getOrderExecutionEndTime() );
            assertEquals( twapexecStartTs, singleLegOrder.getOrderExecutionStartTime() );
            assertEquals( 5000, singleLegOrder.getTwapSliceInterval() );
            assertTrue( singleLegOrder.isRandomizeTwapSliceInterval() );
            assertEquals( 500000.0, singleLegOrder.getTwapSliceSize() );
            assertTrue( singleLegOrder.isRandomizeTwapSliceSize() );
            assertEquals( 10, singleLegOrder.getActionOnOrderExpitation() );
            assertEquals( "TestCaseOrderExecutionStrategyName", singleLegOrder.getOrderExecutionStrategyName() );
            assertEquals( 100003.0, singleLegOrder.getTwapSliceRegularSize() );
            assertEquals( 1000019.0, singleLegOrder.getPassiveTime() );
            assertEquals( 0.5, singleLegOrder.getTwapSliceTopOfBookPercent() );
            assertEquals( 10000, singleLegOrder.getTwapMinimumSliceInterval() );
            assertTrue( singleLegOrder.isAmended() );
            assertEquals( singleLegOrder.getFXDealLeg().getOriginalOrderAmount(), origOrderAmt );
            assertEquals( singleLegOrder.getFXDealLeg().getOriginalOrderRate(), origOrderRate );
            assertEquals( singleLegOrder.getFXDealLeg().getFixingDate(), date );
            assertEquals( singleLegOrder.getFXDealLeg().getFixingTenor(), Tenor.SPOT_TENOR );
            assertEquals( singleLegOrder.getCounterpartyAOrg(), org1 );
            assertEquals( singleLegOrder.getCustomerOrg(), org2 );
            assertEquals( singleLegOrder.getPlacedByOrg(), org3 );
            assertEquals( contractId, singleLegOrder.getContractId() );
            assertEquals( false, singleLegOrder.isSEF() );
            assertEquals( spotClassification, singleLegOrder.getTradeClassification() );
        }
        catch ( Exception exc )
        {
            log.error( "OrderPTestC.testFieldsPersistence() Exception 1 - " + exc );
        }

        long objectId = singleLegOrder.getObjectID();

        // Deleteing the entry.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.deleteObject( singleLegOrder );
            uow.commit();
            uow.release();
        }
        catch ( Exception exc )
        {
            log.error( "OrderPTestC.testFieldsPersistence() Exception 3 - " + exc );
        }

        // Retrieve the object again to make sure object is deleted.
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder builder = new ExpressionBuilder();
            Expression expr = builder.get( "objectID" ).equal( objectId );
            uow = this.getPersistenceSession().acquireUnitOfWork();
            FXSingleLegOrder order = ( FXSingleLegOrder ) uow.readObject( com.integral.finance.dealing.fx.FXSingleLegOrderC.class, expr );
            assertEquals( order, null );
        }
        catch ( Exception exc )
        {
            log.error( "OrderPTestC.testFieldsPersistence() Exception 4 - " + exc );
        }
    }

}
