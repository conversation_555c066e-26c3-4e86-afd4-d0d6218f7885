package com.integral.finance.dealing.test;


import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.QuoteC;
import com.integral.finance.dealing.QuoteCustomFieldC;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.persistence.CustomField;
import com.integral.persistence.EntityFactory;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


public class QuoteCustomFieldPTestC
        extends PTestCaseC
{
    static String name = "Quote Custom Field Test";
    Vector users = null;

    public QuoteCustomFieldPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new QuoteCustomFieldPTestC( "deleteTest" ) );
        // suite.addTest(new QuoteCustomFieldPTestC("nullTest"));
        // suite.addTest(new QuoteCustomFieldPTestC("cfTest"));
        // suite.addTest(new QuoteCustomFieldPTestC("registrationTest"));
        // suite.addTest(new QuoteCustomFieldPTestC("cfListTest"));
        return suite;
    }

    public void deleteTest()
    {
        log( "deleteTest" );
        try
        {
            CustomField cf = new QuoteCustomFieldC();
            Quote q = new QuoteC();

            // create quote with no value
            //
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            CustomField cf_r1 = ( CustomField ) uow1.registerObject( cf );
            Quote q_r1 = ( Quote ) uow1.registerObject( q );
            cf_r1.setKey( "myquote" );
            cf_r1.setValue( q_r1 );
            cf_r1.setOwned( true );
            uow1.commit();

            // delete quote
            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            uow2.deleteObject( cf );
            uow2.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "deleteTest" );
        }
    }

    public void nullTest()
    {
        log( "nullTest" );
        try
        {
            Quote q = new QuoteC();

            // create quote
            //
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r1 = ( Quote ) uow1.registerObject( q );
            q_r1.putCustomField( "nullValue-1", null );
            log( "null CF 1: " + q_r1.getCustomFieldValue( "nullValue-1" ) );
            uow1.commit();
            log( "null CF 1: " + q.getCustomFieldValue( "nullValue-1" ) );

            // update quote
            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r2 = ( Quote ) uow2.registerObject( q );

            q_r2.putCustomField( "nullValue-2", null );
            log( "null CF 2: " + q_r2.getCustomFieldValue( "nullValue-2" ) );
            uow2.commit();
            log( "null CF 2: " + q.getCustomFieldValue( "nullValue-2" ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "nullTest" );
        }
    }

    public void registrationTest()
    {
        log( "insertTest" );
        try
        {
            Quote q = new QuoteC();

            // create quote
            //
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r1 = ( Quote ) uow1.registerObject( q );
            printQuote( q_r1, 1 );
            uow1.commit();
            printQuote( q, 11 );
            log( "---------------------------------------------" );

            // create requests and add them to quote
            //
            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r2 = ( Quote ) uow2.registerObject( q );

            Request r1 = new RequestC();
            log( "\t request 1 hash code: " + r1.hashCode() );
            Request r1_r2 = ( Request ) uow2.registerObject( r1 );
            log( "\t request 1r hash code: " + r1_r2.hashCode() );
            q_r2.putCustomField( "req-1", r1_r2 );

            Request r2 = new RequestC();
            log( "\t request 2 hash code: " + r2.hashCode() );
            Request r2_r2 = ( Request ) uow2.registerObject( r2 );
            log( "\t request 2r hash code: " + r2_r2.hashCode() );
            q_r2.putCustomField( "req-2", r2_r2 );

            printQuote( q_r2, 2 );
            uow2.commit();
            printQuote( q, 22 );
            log( "---------------------------------------------" );

            // register quote and look at CF
            //
            UnitOfWork uow3 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r3 = ( Quote ) uow3.registerObject( q );
            printQuote( q_r3, 3 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }

    public void cfListTest()
    {
        log( "cfListTest" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "objectID" ).equal( 4501 );
            Quote q = ( Quote ) getPersistenceSession().readObject( QuoteC.class, expr );

            q = ( Quote ) EntityFactory.newEntity( getPersistenceSession(), q, false );

            //
            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r2 = ( Quote ) uow2.registerObject( q );

            Request r1 = new RequestC();
            log( "\t request 1 hash code: " + r1.hashCode() );
            Request r1_r2 = ( Request ) uow2.registerObject( r1 );
            log( "\t request 1r hash code: " + r1_r2.hashCode() );
            q_r2.putCustomField( "req-1", r1_r2 );

            uow2.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "cfListTest" );
        }
    }

    public void registrationTest2()
    {
        log( "insertTest" );
        try
        {
            Quote q = new QuoteC();
            Request r1 = new RequestC();
            Request r2 = new RequestC();

            // create quote
            //
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r1 = ( Quote ) uow1.registerObject( q );
            uow1.registerObject( r1 );
            uow1.registerObject( r2 );
            printQuote( q_r1, 1 );
            uow1.commit();
            printQuote( q, 11 );
            log( "---------------------------------------------" );

            // create requests and add them to quote
            //
            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r2 = ( Quote ) uow2.registerObject( q );

            log( "\t request 1 hash code: " + r1.hashCode() );
            Request r1_r2 = ( Request ) uow2.registerObject( r1 );
            log( "\t request 1r hash code: " + r1_r2.hashCode() );
            q_r2.putCustomField( "req-1", r1_r2 );

            log( "\t request 2 hash code: " + r2.hashCode() );
            Request r2_r2 = ( Request ) uow2.registerObject( r2 );
            log( "\t request 2r hash code: " + r2_r2.hashCode() );
            q_r2.putCustomField( "req-2", r2_r2 );

            printQuote( q_r2, 2 );
            uow2.commit();
            printQuote( q, 22 );
            log( "---------------------------------------------" );

            // register quote and look at CF
            //
            UnitOfWork uow3 = getPersistenceSession().acquireUnitOfWork();
            Quote q_r3 = ( Quote ) uow3.registerObject( q );
            printQuote( q_r3, 3 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }

    public void cfTest()
    {
        log( "cfTest" );
        try
        {
            Quote q = new QuoteC();

            // log("SESSION: " + getPersistenceSession().hashCode());

            // create quote
            //
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            // log("UOW1: " + uow1.hashCode());
            Quote q_r1 = ( Quote ) uow1.registerObject( q );
            uow1.commit();
            log( "shared quote # = " + q.hashCode() );

            // create CF
            //
            QuoteCustomFieldC qcf = new QuoteCustomFieldC();
            log( "shared cf # = " + qcf.hashCode() );

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            // log("UOW2: " + uow2.hashCode());
            QuoteCustomFieldC qcf_r2 = ( QuoteCustomFieldC ) uow2.registerObject( qcf );

            log( "UOW2 cf # = " + qcf_r2.hashCode() );

            qcf_r2.setValue( uow2.registerObject( q ) );
            log( "UOW2 cf quote # = " + qcf_r2.getValue().hashCode() );

            uow2.commit();
            log( "shared cf # = " + qcf.hashCode() );
            log( "shared cf quote # = " + qcf.getValue().hashCode() );

            // register quote and look at CF
            //
            UnitOfWork uow3 = getPersistenceSession().acquireUnitOfWork();
            // log("UOW3: " + uow3.hashCode());
            QuoteCustomFieldC qcf_r3 = ( QuoteCustomFieldC ) uow3.registerObject( qcf );
            log( "UOW3 cf # = " + qcf_r3.hashCode() );
            log( "UOW3 cf quote # = " + qcf_r3.getValue().hashCode() );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }

    private void printQuote( Quote quote, int i )
    {
        log( "quote #" + i );
        log( "\t object    = " + quote );
        log( "\t objectID  = " + quote.getObjectID() );
        log( "\t hash code = " + quote.hashCode() );
        if ( quote.getCustomField( "req-1" ) != null )
        {
            // log("\t CFO req-1 " + quote.getCustomField("req-1"));
            // log("\t CFO req-1 hash code  = " + quote.getCustomField("req-1").hashCode());
        }
        if ( quote.getCustomFieldValue( "req-1" ) != null )
        {
            log( "\t CF req-1            = " + quote.getCustomFieldValue( "req-1" ) );
            log( "\t CF req-1 hash code  = " + quote.getCustomFieldValue( "req-1" ).hashCode() );
        }
        if ( quote.getCustomField( "req-2" ) != null )
        {
            // log("\t CFO req-2 " + quote.getCustomField("req-2"));
            // log("\t CFO req-2 hash code  = " + quote.getCustomField("req-2").hashCode());
        }
        if ( quote.getCustomFieldValue( "req-2" ) != null )
        {
            log( "\t CF req-2            = " + quote.getCustomFieldValue( "req-2" ) );
            log( "\t CF req-2 hash code  = " + quote.getCustomFieldValue( "req-2" ).hashCode() );
        }
    }
}
