package com.integral.finance.dealing.test;

import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyC;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairC;
import com.integral.finance.dw.StagingTradesCube;
import com.integral.finance.dw.StagingTradesCubeC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.trade.Tenor;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Timestamp;
import java.util.Date;
import java.util.UUID;

/**
 * Created with IntelliJ IDEA.
 * User: pulkit
 * Date: 11/26/12
 * Time: 5:00 PM
 */
public class StagingTradesCubePTestC extends PTestCaseC {
    static String name = StagingTradesCubeC.class.getName();

    public StagingTradesCubePTestC(String name) {
        super(name);
    }

    public void testStagingTradesCube() {
        try {
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            StagingTradesCubeC object = new StagingTradesCubeC();

            updateObject(object);
            StagingTradesCube stagingEventTimes = (StagingTradesCube) uow1.registerObject(object);

            uow1.commit();


        } catch (Exception e) {
            fail("testStagingOrdersCube", e);
        }
    }

    private void updateObject(StagingTradesCubeC object) throws IdcNoSuchObjectException {

        /*for (Method method : StagingTradesCubeC.class.getMethods()) {
            if (method.getName().startsWith("set")) {
                System.out.println(method.getName());
            }
        }*/
        Organization org1 = (Organization) namedEntityReader.execute(OrganizationC.class, "FI1");
        Counterparty cpty = (Counterparty) namedEntityReader.execute(CounterpartyC.class, "DBNA");
        String ccyPair = "EUR/USD";
        Currency baseCcy = CurrencyFactory.getCurrency(ccyPair.substring(0, ccyPair.indexOf('/')));
        Currency varCcy = CurrencyFactory.getCurrency(ccyPair.substring(ccyPair.indexOf('/') + 1));
        CurrencyPair cp = new CurrencyPairC();
        cp.setBaseCurrency(baseCcy);
        cp.setVariableCurrency(varCcy);
        FXRateConvention fxConv = (FXRateConvention) new ReadNamedEntityC().execute(FXRateConvention.class, "STDQOTCNV");
        assert fxConv.getFXRateBasis(ccyPair) != null;
        Date date = new Date();
        Timestamp timestamp = new Timestamp(date.getTime());
        User user = (User) new ReadNamedEntityC().execute(User.class, "fi2mm1");
        LegalEntityC le = (LegalEntityC) new ReadNamedEntityC().execute(LegalEntityC.class, "FI1-le1");
        assert fxConv.getFXRateBasis(ccyPair) != null;

        object.setPrimeBroker(org1); //  if organization.getPrimeBroker() is not null
        object.setVirtualServer("test-server");  //trade.getVirtualServer
        object.setOrganization(org1);  // trade.getLegalEntity.getOrganization
        object.setTenor(Tenor.SPOT_TENOR);  //matchEvent.getMatchEventLeg().getTenor()
        object.setCurrencyPair(fxConv.getFXRateBasis(ccyPair));   //matchEvent.getFxRateBasis
        object.setExecutionTime(timestamp);   //trade.getExecutionTime()
        object.setOrderId("Test-orderId");//trade.getOrderRequestId()
        object.setPriceType(1); // quotefortraderequest
        object.setMarketSnapshot("fdsahfkjad kjfkjdsfjadshflkjahsdf kjdasfhjkdshf kjadsfkjah"); // matchEvent.getMarketSnapshot
        object.setOriginatingOrderId(UUID.randomUUID().toString());//order.getOriginatingOrderId
        object.setTradeType(1);  //??
        object.setWorkflowCode(1);//TODO
        object.setTradeDate(timestamp); //trade.getTradeDate
        object.setSalesDealerUser(user);   //trade.getUSer ideally but dont set it as of now
        object.setStream("test-stream"); // matcheventpq.quotedescriptor.getStreamId
        object.setClientTag("test-client-tag"); // figure this thing
        object.setTaker(true);   //orderRequest.isMaker()
        object.setTransactionId(UUID.randomUUID().toString());//trade.get_id
        object.setSpreads(UUID.randomUUID().toString());// ?? check if the current db has it
        object.setRejectReason("fasfasddaaaaa"); //trade.getRejectionREason
        object.setSpotRate(2.223333); //trade.getTradeLeg.getSpotRate
        object.setBuyingCurrency1(true);  //trade.isBuyingbaseCurrency
        object.setRate(3.3333);   //trade.getSpotRate
        object.setFixingTenor("test-tenor");// todo keep it blank
        object.setDealtCurrency(baseCcy);  //trade.getDealtCurrency
        object.setSettledCurrency(varCcy); //trade.getSettledCurrency
        object.setAcceptedProviderPrice(2.32222);                    //   finalAcceptanceSpotRate
        object.setWorkflowStateId(12);    // calculate
        object.setFixingDate(timestamp);  //rfs
        object.setValueDate(timestamp);
        object.setBuyingCcy1(true);   //    dupicate
        object.setLastWorkflowStateId(1211);    //todo fill this in the app
        object.setOriginatingTradeId("Test-orig-trade-id");    // originatingtrade
        object.setTakerRefId(UUID.randomUUID().toString());    //takerrefid
        object.setMakerRefId(UUID.randomUUID().toString());                //makerrefid
        object.setCoverSpotRate(3.2222);   //todo
        object.setCoverRate(3.455555);     //todo
        object.setTradingLegalEntity(le);  //getLEgalEnitity
        object.setCptyLegalEntity(le);   //trade.getTradingPArty.getLE
        object.setTradingUser(user); //trade.getUser
        object.setCptyUser(user);   //trade.getMakerUSer
        object.setPrimeBrokerCpty(org1); //
        object.setOriginatingUser(user); //originating trade descriptor
        object.setOriginatingCpty(cpty);
        object.setOriginatingOrg(org1);
        object.setCoveredOrg(org1);   //    coveredtrade .get Organization
        object.setSalesDealerLE(le);  //
        object.setCreatedTimestamp(timestamp);
        object.setLastModifiedBy(user);
        object.setCreatedDateTime(timestamp);
        object.setNetTradeId(UUID.randomUUID().toString());
        object.setOrgUser(user);
        object.setCptyOrganization(org1);
        object.setSettledAmount(23.222);
        object.setDealtAmount(34.3333);
        object.setChannel("DNET/PD");
        object.setDealtBase(true);
    }
}
