package com.integral.finance.config.test;

import com.integral.finance.config.Finance;
import com.integral.finance.config.FinanceConfigurationFactory;
import com.integral.finance.config.FinanceMBean;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;

import java.sql.Time;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

/**
 * <AUTHOR> Development Corporation.
 */
public class FinanceMBeanTestC extends MBeanTestCaseC
{
    Finance financeMBean = ( Finance ) FinanceConfigurationFactory.getFinanceMBean();

    public FinanceMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( financeMBean, "dateGenerationHolidayCacheBeginYear", "IDC.DateGeneration.HolidayCalendar.HolidayCacheBeginYear", MBeanTestCaseC.INTEGER );
        testProperty( financeMBean, "dateGenerationHolidayCacheEndYear", "IDC.DateGeneration.HolidayCalendar.HolidayCacheEndYear", MBeanTestCaseC.INTEGER );
        testProperty( financeMBean, "globalBusinessDateCutOffTime", "IDC.GlobalBusinessDateCutOffTime", MBeanTestCaseC.STRING );
        testProperty( financeMBean, "globalBusinessDateCutOffTimeZone", "IDC.GlobalBusinessDateCutOffTimeZone", MBeanTestCaseC.STRING );
        testProperty( financeMBean, "externalSystemSwiftName", "IDC.ExternalSystem.Swift.Name", MBeanTestCaseC.STRING );
        testProperty( financeMBean, "monteCarloServer", "IDC.MonteCarlo.Server", MBeanTestCaseC.STRING );
        testProperty( financeMBean, "riskDataRoot", "IDC.Risk.DataRoot", MBeanTestCaseC.STRING );
        testProperty( financeMBean, "scenarioGenerationEnabled", "IDC.EnableScenarioGeneration", MBeanTestCaseC.BOOLEAN );
        testProperty( financeMBean, "riskMetricsReaderThreadEnabled", "IDC.EnableRiskMetricsReaderThread", MBeanTestCaseC.BOOLEAN );
        testProperty( financeMBean, "riskMetricsReaderPollingDelayInSeconds", "IDC.RiskMetricsReaderPollingDelayInSeconds", MBeanTestCaseC.INTEGER );
        testProperty( financeMBean, "NZDRollTimeCheckEnabled", FinanceMBean.NZD_ROLLTIME_CHECK_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( financeMBean, "NZDRollTimeInGMT", FinanceMBean.NZD_ROLLTIME_IN_GMT, MBeanTestCaseC.STRING );
        testProperty( financeMBean, "NZDRollTimeInNST", FinanceMBean.NZD_ROLLTIME_IN_NST, MBeanTestCaseC.STRING, true, false );
        testProperty( financeMBean, "NZDRollTimeRollTradeDateOnFridayDisabled", FinanceMBean.NZD_ROLLTIME_ROLLTRADEDATE_FRIDAY_DISABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( financeMBean, "forwardRatePrecisionCalculationUsingPipsFactorEnabled", FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( financeMBean, "precisionThresholdForCurrencyRoundingUsingBigDecimal", FinanceMBean.CURRENCY_PRECISION_THRESHOLD_FOR_ROUNDING_USING_BIGDECIMAL, MBeanTestCaseC.INTEGER );
        testProperty( financeMBean, "firstIMMDateAfterSpotDateEnabled", FinanceMBean.FIRST_IMM_DATE_AFTER_SPOT_DATE_ENABLED, MBeanTestCaseC.BOOLEAN );
    }

    public void testRollTime()
    {
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_GMT, "00:00:12", ConfigurationProperty.DYNAMIC_SCOPE );
        int[] gmtValues = financeMBean.getNZDRollTimeInGMTIntValue();
        assertEquals( gmtValues[0], 0 );
        assertEquals( gmtValues[1], 0 );
        assertEquals( gmtValues[2], 12 );

        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_GMT, "15:01:59", ConfigurationProperty.DYNAMIC_SCOPE );
        gmtValues = financeMBean.getNZDRollTimeInGMTIntValue();
        assertEquals( gmtValues[0], 15 );
        assertEquals( gmtValues[1], 1 );
        assertEquals( gmtValues[2], 59 );

    }

    public void _testNZDRollTime()
    {

        try
        {
            Currency nzd = CurrencyFactory.getCurrency( "NZD" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            checkNZDTradeDate( nzd, usd, "19:00:00", "07:00:00" );
            checkNZDTradeDate( nzd, usd, "21:00:00", "09:00:00" );
            checkNZDTradeDate( nzd, usd, "20:30:00", "08:30:00" );
            checkNZDTradeDate( nzd, usd, "20:30:30", "08:30:30" );
            checkNZDTradeDate( nzd, usd, "22:00:00", "10:00:00" );
            checkNZDTradeDate( nzd, usd, "23:00:00", "11:00:00" );
            checkNZDTradeDate( nzd, usd, "00:00:00", "12:00:00" );
            checkNZDTradeDate( nzd, usd, "01:00:00", "13:00:00" );
            checkNZDTradeDate( nzd, usd, "02:00:00", "14:00:00" );
            checkNZDTradeDate( nzd, usd, "04:00:00", "16:00:00" );
            checkNZDTradeDate( nzd, usd, "03:00:00", "15:00:00" );
            checkNZDTradeDate( nzd, usd, "05:00:00", "17:00:00" );
            checkNZDTradeDate( nzd, usd, "06:00:00", "18:00:00" );
            checkNZDTradeDate( nzd, usd, "07:00:00", "19:00:00" );
            checkNZDTradeDate( nzd, usd, "08:00:00", "20:00:00" );
            checkNZDTradeDate( nzd, usd, "09:00:00", "21:00:00" );
            checkNZDTradeDate( nzd, usd, "10:00:00", "22:00:00" );
            checkNZDTradeDate( nzd, usd, "11:00:00", "23:00:00" );
            checkNZDTradeDate( nzd, usd, "12:00:00", "00:00:00" );
            checkNZDTradeDate( nzd, usd, "13:00:00", "01:00:00" );
            checkNZDTradeDate( nzd, usd, "14:00:00", "02:00:00" );
            checkNZDTradeDate( nzd, usd, "15:00:00", "03:00:00" );
            checkNZDTradeDate( nzd, usd, "16:00:00", "04:00:00" );
            checkNZDTradeDate( nzd, usd, "17:00:00", "05:00:00" );
            checkNZDTradeDate( nzd, usd, "18:00:00", "06:00:00" );
            checkNZDTradeDate( nzd, usd, "19:00:00", "07:00:00" );
            checkNZDTradeDate( nzd, usd, "20:00:00", "08:00:00" );

        }
        catch ( Exception e )
        {
            log.error( "testNZDRollTime", e );
        }
    }

    private void checkNZDTradeDate( Currency nzd, Currency usd, String gmtTime, String nstTime )
    {
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_GMT, gmtTime, ConfigurationProperty.DYNAMIC_SCOPE );
        FXRateConvention stdQotConv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConventionC.class, "STDQOTCNV" );
        FXRateBasis rb = stdQotConv.getFXRateBasis( nzd, usd );
        IdcDate currentDate = DateTimeFactory.newDate();
        IdcDate td = rb.getNZDTradeDate( currentDate );
        log.info( "Trade Date : " + td );
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_NST, nstTime, ConfigurationProperty.DYNAMIC_SCOPE );
        IdcDate td1 = rb.getNZDTradeDate( currentDate );
        log.info( "Trade Date : " + td1 );
        assertEquals( td, td1 );
    }

    public void _testNZDRollTime1()
    {

        try
        {
            checkMockNZDTradeDate( "18:00:00", "06:00:00" );
            checkMockNZDTradeDate( "19:00:00", "07:00:00" );
            checkMockNZDTradeDate( "18:30:00", "06:30:00" );
            checkMockNZDTradeDate( "18:30:30", "06:30:30" );
            checkMockNZDTradeDate( "20:00:00", "08:00:00" );
            checkMockNZDTradeDate( "21:00:00", "09:00:00" );
            checkMockNZDTradeDate( "22:00:00", "10:00:00" );
            checkMockNZDTradeDate( "23:00:00", "11:00:00" );
            checkMockNZDTradeDate( "00:00:00", "12:00:00" );
            checkMockNZDTradeDate( "02:00:00", "14:00:00" );
            checkMockNZDTradeDate( "01:00:00", "13:00:00" );
            checkMockNZDTradeDate( "03:00:00", "15:00:00" );
            checkMockNZDTradeDate( "04:00:00", "16:00:00" );
            checkMockNZDTradeDate( "05:00:00", "17:00:00" );
            checkMockNZDTradeDate( "06:00:00", "18:00:00" );
            checkMockNZDTradeDate( "07:00:00", "19:00:00" );
            checkMockNZDTradeDate( "08:00:00", "20:00:00" );
            checkMockNZDTradeDate( "09:00:00", "21:00:00" );
            checkMockNZDTradeDate( "10:00:00", "22:00:00" );
            checkMockNZDTradeDate( "11:00:00", "23:00:00" );
            checkMockNZDTradeDate( "12:00:00", "00:00:00" );
            checkMockNZDTradeDate( "13:00:00", "01:00:00" );
            checkMockNZDTradeDate( "14:00:00", "02:00:00" );
            checkMockNZDTradeDate( "15:00:00", "03:00:00" );
            checkMockNZDTradeDate( "16:00:00", "04:00:00" );
            checkMockNZDTradeDate( "17:00:00", "05:00:00" );
            checkMockNZDTradeDate( "18:00:00", "06:00:00" );

        }
        catch ( Exception e )
        {
            log.error( "testNZDRollTime", e );
        }
    }

    private void checkMockNZDTradeDate( String gmtTime, String nstTime )
    {
        Calendar cal = new GregorianCalendar();
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_GMT, gmtTime, ConfigurationProperty.DYNAMIC_SCOPE );
        for ( int i = 0; i < 100; i++ )
        {
            cal.add( Calendar.HOUR_OF_DAY, 1 );
            Date checkAt = cal.getTime();
            log.info( "Manipulated Current Time :" + checkAt );
            IdcDate currentDate = DateTimeFactory.newDate();
            IdcDate td = mockCalculateTradeDate( currentDate, checkAt );
            log.info( "Trade Date : " + td );
            WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_NST, nstTime, ConfigurationProperty.DYNAMIC_SCOPE );
            IdcDate td1 = mockCalculateTradeDate( currentDate, checkAt );
            log.info( "Trade Date : " + td1 );
            assertEquals( td, td1 );
        }
    }

    public void testTimeZone()
    {
        TimeZone timeZone1 = TimeZone.getTimeZone( "UTC" );
        TimeZone timeZone2 = TimeZone.getTimeZone( "NST" );

        Calendar nzdCalendar = new GregorianCalendar( timeZone2 );
        nzdCalendar.set( Calendar.HOUR_OF_DAY, 13 );
        nzdCalendar.set( Calendar.MINUTE, 0 );
        nzdCalendar.set( Calendar.SECOND, 0 );
        nzdCalendar.get( Calendar.HOUR_OF_DAY );
        nzdCalendar.setTimeZone( timeZone1 );
        int[] tempTime = new int[3];
        tempTime[0] = nzdCalendar.get( Calendar.HOUR_OF_DAY );
        tempTime[1] = nzdCalendar.get( Calendar.MINUTE );
        tempTime[2] = nzdCalendar.get( Calendar.SECOND );

        System.out.println( "hour     = " + nzdCalendar.get( Calendar.HOUR_OF_DAY ) );
        System.out.println( "min     = " + nzdCalendar.get( Calendar.MINUTE ) );
        System.out.println( "sec     = " + nzdCalendar.get( Calendar.SECOND ) );

    }

    public void test10()
    {
        Calendar nzdCalendar = new GregorianCalendar();
        Calendar nzdCalendar1 = new GregorianCalendar();
        nzdCalendar.set( Calendar.HOUR, 10 );
        nzdCalendar.set( Calendar.MINUTE, 0 );
        nzdCalendar.set( Calendar.SECOND, 0 );
        //nzdCalendar.get( Calendar.SECOND );
        nzdCalendar.setTimeZone( TimeZone.getTimeZone( "GMT" ) );

        nzdCalendar1.set( Calendar.HOUR_OF_DAY, 10 );
        nzdCalendar1.set( Calendar.MINUTE, 0 );
        nzdCalendar1.set( Calendar.SECOND, 0 );
        //nzdCalendar1.get( Calendar.SECOND );
        nzdCalendar1.setTimeZone( TimeZone.getTimeZone( "GMT" ) );
        System.out.println( "Date : " + nzdCalendar.getTime() );
        System.out.println( "Date1 : " + nzdCalendar1.getTime() );
        System.out.println( "Diff : " + ( nzdCalendar1.getTime().getTime() - nzdCalendar.getTime().getTime() ) );
    }

    public IdcDate mockCalculateTradeDate( IdcDate tradeDate, Date date )
    {
        IdcDate currentDate = DateTimeFactory.newDate();

        if ( tradeDate.compare( currentDate ) == 0 )
        {
            String timeZoneStr;
            int[] nzdRollTime;
            timeZoneStr = "UTC";
            if ( financeMBean.getNZDRollTimeInNST() == null )
            {
                nzdRollTime = financeMBean.getNZDRollTimeInGMTIntValue();
            }
            else
            {
                nzdRollTime = financeMBean.getNZDRollTimeFromNSTInGMTIntValue();
            }

            Long currentTime = date.getTime();

            Calendar nzdCalendar = new GregorianCalendar();
            nzdCalendar.set( Calendar.HOUR, nzdRollTime[0] );
            nzdCalendar.set( Calendar.MINUTE, nzdRollTime[1] );
            nzdCalendar.set( Calendar.SECOND, nzdRollTime[2] );
            nzdCalendar.setTimeZone( TimeZone.getTimeZone( timeZoneStr ) );

            Long nzdCurrentDateRollTime = nzdCalendar.getTimeInMillis();


            if ( nzdCurrentDateRollTime <= currentTime )
            {
                tradeDate = tradeDate.addDays( 1 );

            }
        }

        return tradeDate;
    }

    public void _testNZDRollTimeEODServerDelay()
    {

        try
        {
            boolean passed = true;
            passed = checkDelay( "18:00:00", "06:00:00" ) && passed;
            passed = checkDelay( "19:00:00", "07:00:00" ) && passed;
            passed = checkDelay( "18:30:00", "06:30:00" ) && passed;
            passed = checkDelay( "18:30:30", "06:30:30" ) && passed;
            passed = checkDelay( "20:00:00", "08:00:00" ) && passed;
            passed = checkDelay( "21:00:00", "09:00:00" ) && passed;
            passed = checkDelay( "22:00:00", "10:00:00" ) && passed;
            passed = checkDelay( "23:00:00", "11:00:00" ) && passed;
            passed = checkDelay( "00:00:00", "12:00:00" ) && passed;
            passed = checkDelay( "01:00:00", "13:00:00" ) && passed;
            passed = checkDelay( "02:00:00", "14:00:00" ) && passed;
            passed = checkDelay( "03:00:00", "15:00:00" ) && passed;
            passed = checkDelay( "04:00:00", "16:00:00" ) && passed;
            passed = checkDelay( "05:00:00", "17:00:00" ) && passed;
            passed = checkDelay( "06:00:00", "18:00:00" ) && passed;
            passed = checkDelay( "07:00:00", "19:00:00" ) && passed;
            passed = checkDelay( "08:00:00", "20:00:00" ) && passed;
            passed = checkDelay( "09:00:00", "21:00:00" ) && passed;
            passed = checkDelay( "10:00:00", "22:00:00" ) && passed;
            passed = checkDelay( "11:00:00", "23:00:00" ) && passed;
            passed = checkDelay( "12:00:00", "00:00:00" ) && passed;
            passed = checkDelay( "13:00:00", "01:00:00" ) && passed;
            passed = checkDelay( "14:00:00", "02:00:00" ) && passed;
            passed = checkDelay( "15:00:00", "03:00:00" ) && passed;
            passed = checkDelay( "16:00:00", "04:00:00" ) && passed;
            passed = checkDelay( "17:00:00", "05:00:00" ) && passed;
            passed = checkDelay( "18:00:00", "06:00:00" ) && passed;
            assertEquals( passed, true );
        }
        catch ( Exception e )
        {
            log.error( "testNZDRollTime", e );
        }
    }

    private boolean checkDelay( String gmtTime, String nstTime )
    {
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_GMT, gmtTime, ConfigurationProperty.DYNAMIC_SCOPE );
        long delayFromGMT = getDelayFromMockUpdateNZDRollTime();
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_GMT, null, ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( financeMBean.NZD_ROLLTIME_IN_NST, nstTime, ConfigurationProperty.DYNAMIC_SCOPE );

        long delayFromNST = getDelayFromMockUpdateNZDRollTime();

        if ( delayFromGMT != delayFromNST )
        {
            log.error( "FAILED. Delay from GMT : " + delayFromGMT + " Delay from NST : " + delayFromNST );
            return false;
        }
        else
        {
            log.info( "PASSED For GMTTime : " + gmtTime + " NSTTime : " + nstTime );
            return true;
        }
    }


    public long getDelayFromMockUpdateNZDRollTime()
    {
        TimeZone timeZone;
        int[] NZDRollTimeArr;
        String GMTTimeZoneId = "GMT";
        String NSTTimeZoneId = "NST";

        timeZone = TimeZone.getTimeZone( GMTTimeZoneId );

        if ( financeMBean.getNZDRollTimeInNST() == null )
        {
            NZDRollTimeArr = financeMBean.getNZDRollTimeInGMTIntValue();
        }
        else
        {
            NZDRollTimeArr = financeMBean.getNZDRollTimeFromNSTInGMTIntValue();
        }

        IdcDate currentDate = DateTimeFactory.newDate();
        long NZDRollTime = NZDRollTimeArr[0] * 3600 + NZDRollTimeArr[1] * 60 + NZDRollTimeArr[2];
        Time nzdCurrentDateRollTime = new Time( NZDRollTime * 1000 );

        IdcDateTime currentNZDRollTime = DateTimeFactory.newDateTime( currentDate, nzdCurrentDateRollTime, timeZone );
        long delay = currentNZDRollTime.asTimestamp().getTime() - DateTimeFactory.newDateTime( new Date(), timeZone ).asTimestamp().getTime();
        long originalDelay = delay;
        if ( delay <= 0 )
        {
            log.warn( "EndOfDayServerC.updateNZDRollTime : Recalculating delay. Original delay " + delay );
            currentNZDRollTime = DateTimeFactory.newDateTime( currentDate.addDays( 1 ), nzdCurrentDateRollTime, timeZone );
            delay = currentNZDRollTime.asTimestamp().getTime() - DateTimeFactory.newDateTime( new Date(), timeZone ).asTimestamp().getTime();
        }
        log.warn( new StringBuilder( 200 ).append( "EndOfDayServerC.updateNZDRollTime.INFO : Scheduling the next NZD rolltime invocation after " )
                .append( delay ).append( ",currentRollTime=" ).append( currentNZDRollTime ).append( ",originalDelay=" )
                .append( originalDelay ).toString() );

        if ( delay > 0 )
        {
        }
        else
        {
            log.error( "EndOfDayServerC.updateNZDRollTime.ERROR : Unable to schedule NZDEndOfDayTimerTaskC." );
        }

        return delay;
    }

    public void testForwardRatePrecisionBasedOnConvention()
    {
        try
        {
            FXRateConvention conv1 = FXFactory.newFXRateConvention();
            conv1.setShortName( "Conv1" );

            FXRateConvention conv2 = FXFactory.newFXRateConvention();
            conv2.setShortName( "Conv2" );


            assertFalse( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( null ) );
            assertFalse( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv1 ) );
            assertFalse( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv2 ) );

            // now set the global property
            financeMBean.setProperty( FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv1 ) );
            assertTrue( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv2 ) );
            assertTrue( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( null ) );

            financeMBean.setProperty( FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv1 ) );
            assertFalse( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv2 ) );
            assertFalse( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( null ) );


            financeMBean.setProperty( FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED_PREFIX + "Conv1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED_PREFIX + "Conv2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv1 ) );
            assertFalse( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv2 ) );

            financeMBean.setProperty( FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv1 ) );
            assertFalse( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv2 ) );
            assertTrue( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( null ) );

            financeMBean.setProperty( FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED_PREFIX + "Conv1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED_PREFIX + "Conv2", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv1 ) );
            assertTrue( financeMBean.isForwardRatePrecisionCalculationUsingPipsFactorEnabled( conv2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testForwardRatePrecisionBasedOnConvention" );
        }
    }

    public void testForceQuoteConventionCurrencyPairDeliverableSetting()
    {
        try
        {
            FXRateConvention conv1 = FXFactory.newFXRateConvention();
            conv1.setShortName( "Conv1" );

            FXRateConvention conv2 = FXFactory.newFXRateConvention();
            conv2.setShortName( "Conv2" );

            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting ( null, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,"EUR/USD" ) );

            // now set the global property
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting ( null, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,"EUR/USD" ) );


            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv1", "EUR/USD", ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv2", "USD/JPY,USD/INR", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, "EUR/USD" ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, "USD/JPY" ) );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "USD/INR" ) );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "USD/JPY" ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "EUR/USD" ) );

            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv1", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv2", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting ( null, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,"EUR/USD" ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testForceQuoteConventionCurrencyPairDeliverableSetting" );
        }
        finally
        {
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING, ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv1", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv2", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING, ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv1", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv2", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testForceQuoteConventionCurrencyPairGroupDeliverableSetting()
    {
        try
        {
            FXRateConvention conv1 = FXFactory.newFXRateConvention();
            conv1.setShortName( "Conv1" );

            FXRateConvention conv2 = FXFactory.newFXRateConvention();
            conv2.setShortName( "Conv2" );

            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting ( null, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,"EUR/USD" ) );

            // now set the global property
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting ( null, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,"EUR/USD" ) );


            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv1", "G7@MAIN", ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv2", "EUR/All@MAIN,AUD/All@MAIN", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, "EUR/USD" ) );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, "EUR/USD" ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "USD/INR" ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "USD/JPY" ) );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "AUD/USD" ) );

            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv1", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv2", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting ( null, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,null ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2,"EUR/USD" ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testForceQuoteConventionCurrencyPairGroupDeliverableSetting" );
        }
        finally
        {
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING, ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv1", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv2", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING, ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv1", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv2", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }
    public void testForceQuoteConventionCurrencyPairDeliverableSettingPrecedence()
    {
        try
        {
            FXRateConvention conv1 = FXFactory.newFXRateConvention();
            conv1.setShortName( "Conv1" );

            FXRateConvention conv2 = FXFactory.newFXRateConvention();
            conv2.setShortName( "Conv2" );

            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv1", "EUR/USD", ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv2", "USD/JPY,USD/INR", ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv1", "G7@MAIN", ConfigurationProperty.DYNAMIC_SCOPE, null );
            financeMBean.setProperty( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv2", "CHF/All@MAIN,NOK/All@MAIN", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, "EUR/USD" ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv1, "USD/INR" ) );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "NOK/JPY" ) );
            assertTrue( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "USD/JPY" ) );
            assertFalse( financeMBean.isForceQuoteConventionCurrencyPairDeliverableSetting( conv2, "EUR/USD" ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testForceQuoteConventionCurrencyPairDeliverableSettingPrecedence" );
        }
        finally
        {
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING, ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv1", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + "Conv2", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING, ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv1", ConfigurationProperty.DYNAMIC_SCOPE );
            financeMBean.removeProperty ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_GROUP_DELIVERABLE_SETTING_PREFIX + "Conv2", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }
}
