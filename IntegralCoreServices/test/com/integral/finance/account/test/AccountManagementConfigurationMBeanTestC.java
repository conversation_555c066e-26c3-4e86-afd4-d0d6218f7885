package com.integral.finance.account.test;

// Copyright (c) 2018 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.account.configuration.AccountManagementConfiguration;
import com.integral.finance.account.configuration.AccountManagementConfigurationFactory;
import com.integral.finance.account.configuration.AccountManagementConfigurationMBean;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

/**
 * <AUTHOR> Development Corporation.
 */
public class AccountManagementConfigurationMBeanTestC extends MBeanTestCaseC
{
    AccountManagementConfiguration accountManagementConfig = ( AccountManagementConfiguration ) AccountManagementConfigurationFactory.getAccountManagementConfigurationMBean ();

    public AccountManagementConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( accountManagementConfig, "cryptoExchangeNames", AccountManagementConfigurationMBean.CRYPTO_EXCHANGE_NAMES, MBeanTestCaseC.COLLECTION_STRING );
        testProperty( accountManagementConfig, "eventNotesMaxLength", AccountManagementConfigurationMBean.EVENT_NOTES_MAX_LENGTH, MBeanTestCaseC.INTEGER );
        testProperty( accountManagementConfig, "eventTicketIdMaxLength", AccountManagementConfigurationMBean.EVENT_TICKETID_MAX_LENGTH, MBeanTestCaseC.INTEGER );
        testProperty( accountManagementConfig, "eventExternalTransactionIdMaxLength", AccountManagementConfigurationMBean.EVENT_EXTERNAL_TRANSACTIONID_MAX_LENGTH, MBeanTestCaseC.INTEGER );
    }

    public void testAccountEnabled()
    {
        try
        {
            assertFalse( accountManagementConfig.isAccountEnabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse( accountManagementConfig.isAccountEnabled ( org1 ) );
            assertFalse( accountManagementConfig.isAccountEnabled( org2 ) );

            // now set the global property
            accountManagementConfig.setProperty( AccountManagementConfigurationMBean.ACCOUNT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( accountManagementConfig.isAccountEnabled(org1) );
            assertTrue( accountManagementConfig.isAccountEnabled(org2) );
            assertFalse( accountManagementConfig.isAccountEnabled( null ) );

            accountManagementConfig.setProperty( AccountManagementConfigurationMBean.ACCOUNT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( accountManagementConfig.isAccountEnabled(org1) );
            assertFalse( accountManagementConfig.isAccountEnabled(org2) );
            assertFalse( accountManagementConfig.isAccountEnabled(null) );


            accountManagementConfig.setProperty( AccountManagementConfigurationMBean.ACCOUNT_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            accountManagementConfig.setProperty( AccountManagementConfigurationMBean.ACCOUNT_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( accountManagementConfig.isAccountEnabled(org1) );
            assertFalse( accountManagementConfig.isAccountEnabled(org2) );

            accountManagementConfig.setProperty( AccountManagementConfigurationMBean.ACCOUNT_ENABLED_PREFIX, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( accountManagementConfig.isAccountEnabled(org1) );
            assertFalse( accountManagementConfig.isAccountEnabled(org2) );
            assertFalse(accountManagementConfig.isAccountEnabled(null));
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testAccountEnabled" );
        }
    }
}
