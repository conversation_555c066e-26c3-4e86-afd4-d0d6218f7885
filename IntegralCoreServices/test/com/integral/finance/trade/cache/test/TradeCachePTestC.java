package com.integral.finance.trade.cache.test;

// Copyright (c) 1999-2006 Integral Development Corp.  All rights reserved.

import com.integral.cache.ExpirableCache;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.cache.TradeCacheC;
import com.integral.test.DealingPTestCaseC;
import org.eclipse.persistence.sessions.UnitOfWork;


/**
 * TradeCachePTestC
 *
 * <AUTHOR> Development Corp.
 */

public class TradeCachePTestC extends DealingPTestCaseC
{
    public TradeCachePTestC( String aName )
    {
        super( aName );
    }

    public void testTradeCache()
    {
        try
        {
            Trade trade = prepareSingleLegTrade( 100, true, true, "EUR/USD", makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trade.setEntryUser( takerUser );
            TradeCacheC.getInstance().add( trade );
            Trade cachedTakerTrade = TradeCacheC.getInstance().get( trade.getTransactionID(), takerUser );
            assertNotNull( cachedTakerTrade );
            assertEquals( cachedTakerTrade, trade );

            Trade noUserTrade = TradeCacheC.getInstance().get( trade.getTransactionID(), null );
            assertNotNull( noUserTrade );

            // now get a trade from the data base
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            Trade newTrade = prepareSingleLegTrade( 100, true, true, "EUR/USD", makerOrg, makerTpForTaker, bidRates[0], spotDate );
            Trade regTrade = ( Trade ) uow.registerObject( newTrade );
            regTrade.setEntryUser( takerUser );
            uow.commit();

            TradeCacheC.getInstance().add( newTrade );

            sleepFor( ExpirableCache.DEFAULT_CONTAINER * ExpirableCache.DEFAULT_ROTATION_PERIOD + 1000 );
            cachedTakerTrade = TradeCacheC.getInstance().get( trade.getTransactionID(), takerUser );
            assertNull( cachedTakerTrade );

            noUserTrade = TradeCacheC.getInstance().get( trade.getTransactionID(), null );
            assertNull( noUserTrade );

            Trade invalidUserTrade = TradeCacheC.getInstance().get( trade.getTransactionID(), makerUser );
            assertNull( invalidUserTrade );

            Trade cachedNewTakerTrade = TradeCacheC.getInstance().get( newTrade.getTransactionID(), takerUser );
            assertNotNull( cachedNewTakerTrade );

        }
        catch ( Exception e )
        {
            fail( "testTradeCache", e );
        }
    }
}
