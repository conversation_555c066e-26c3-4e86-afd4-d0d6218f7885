package com.integral.finance.trade.calculator.test;

import java.io.StringReader;

import javax.xml.XMLConstants;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;

import org.eclipse.persistence.sessions.UnitOfWork;

import com.integral.SEF.SEFConstants;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.calculator.TradeDownloadFPMLMessageBuilderC;
import com.integral.finance.trade.calculator.fpml.FPMLConstants;
import com.integral.finance.trade.calculator.fpml.FPMLUtilsC;
import com.integral.finance.trade.calculator.fpml.schema.Acknowledgement;
import com.integral.finance.trade.calculator.fpml.schema.NonpublicExecutionReport;
import com.integral.finance.trade.calculator.fpml.schema.NonpublicExecutionReportRetracted;
import com.integral.finance.trade.calculator.fpml.schema.Party;
import com.integral.finance.trade.calculator.fpml.schema.PartyId;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemC;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.ExternalSystemIdC;
import com.integral.session.IdcTransaction;
import com.integral.test.DealingPTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;

public class TradeDownloadFPMLMessageBuilderTestC extends DealingPTestCaseC
{

	Unmarshaller unmarshaller;
	JAXBContext jaxbContext;
	public TradeDownloadFPMLMessageBuilderTestC( String name )
	{
		super(name);
		try
		{
			initJAXB();
		}
		catch ( Exception e )
		{
			fail("Error in initiating JAXB");
		}
	}
	public static Counterparty getClearingMember( Counterparty cptyA, Counterparty cptyB )
	{
		LegalEntity legalEntity = CounterpartyUtilC.getLegalEntity(cptyA);
		LegalEntity otherCptyLe = CounterpartyUtilC.getLegalEntity(cptyB);
		TradingParty tp = CounterpartyUtilC.getTradingParty(otherCptyLe, legalEntity.getOrganization());
		return tp.getPrimeBrokerTradingParty().getLegalEntity();

	}
	
	private void initJAXB() throws Exception
	{
		jaxbContext = JAXBContext.newInstance(NonpublicExecutionReport.class, NonpublicExecutionReportRetracted.class, Acknowledgement.class, com.integral.finance.trade.calculator.fpml.schema.Exception.class);
		unmarshaller = jaxbContext.createUnmarshaller();
		SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
		Schema schema = schemaFactory.newSchema(NonpublicExecutionReport.class.getResource("/schema/fpml-main.xsd"));
		unmarshaller.setSchema(schema);
	
	}
    public void testFPMLDownloadMessage()
	{
		try
		{
			cptyName = "FI2";
			cptyLeName = "FI2-le1";
			fiName = "FI1";
			fiLeName = "FI1-le1";
			String INFXName = "INFXSEF";
			String ICEName = "ICE";
	        TradingParty fiTpforCpty = ( TradingParty ) namedEntityReader.execute( TradingPartyC.class, cptyLeName, fiName );
	     
	        Organization INFXOrg = ( Organization ) namedEntityReader.execute( Organization.class, INFXName );
	        Organization ICEOrg = ( Organization ) namedEntityReader.execute( Organization.class, ICEName );
		      
	        
			double tradeAmt = 1000;
			IdcTransaction tx = initTransaction();
			UnitOfWork uow = tx.getUOW();
			uow.addReadOnlyClasses(getDealingTransactionReadOnlyClasses());
			FXSingleLeg trade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate);
			TradeCloneServiceC.createCptyTrades(trade);
			prepareSingleLegRequest(trade);
			Organization organization = trade.getCounterpartyB().getOrganization();			
			trade.setCounterpartyC(getClearingMember(trade.getCounterpartyA(), trade.getCounterpartyB()));
			trade.setCounterpartyD(getClearingMember(trade.getCounterpartyB(), trade.getCounterpartyA()));
			trade.setClearingHouse(ICEOrg);
			trade.setSEFOrg(INFXOrg);
			trade.setUPI("USD_INR_3M");
			WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
			wfm.setProperty("organization", organization);
			wfm.setEventName(TradeService.VERIFY_EVENT);
			wfm.setObject(trade);
			wfm.setParameterValue(TradeServiceConstants.DOWNLOAD_FORMAT_KEY, TradeServiceConstants.FPML53_FORMAT);
			TradeDownloadFPMLMessageBuilderC builder = new TradeDownloadFPMLMessageBuilderC();

			String downloadMessage = builder.buildDownloadMessage(wfm, null);
			wfm.setParameterValue(TradeServiceConstants.DOWNLOAD_FORMAT_KEY, TradeServiceConstants.FPML53_FORMAT);
			log.info(downloadMessage);	
			tx.release();
		}
		catch ( Exception e )
		{
			fail("testFPMLDownloadMessage()", e);
		}
	}

    public void testCH_AccountIDFPMLDownloadMessage()
	{
		try
		{
			
			cptyName = "FI2";
			cptyLeName = "FI2-le1";
			fiName = "FI1";
			fiLeName = "FI1-le1";
			String INFXName = "INFXSEF";
			String ICEName = "ICE";
			fiTpforCpty = (TradingParty) namedEntityReader.execute(TradingPartyC.class, cptyLeName, fiName);
			cptyTpForFI = (TradingParty) namedEntityReader.execute(TradingPartyC.class, fiLeName, cptyName);
			cptyLe = (LegalEntity) namedEntityReader.execute(LegalEntity.class, cptyLeName);
			fiLe = (LegalEntity) namedEntityReader.execute(LegalEntity.class, fiLeName);
			Organization INFXOrg = (Organization) namedEntityReader.execute(Organization.class, INFXName);
			Organization ICEOrg = (Organization) namedEntityReader.execute(Organization.class, ICEName);

			ExternalSystem es = new ExternalSystemC(SEFConstants.ICE_CHAccontIdExtSys);
			ExternalSystemId exId = new ExternalSystemIdC();
			exId.setExternalSystem(es);
			exId.setSystemId("FI2_ICE01");
			cptyLe.addExternalSystemId(exId);			


			ExternalSystemId exId2 = new ExternalSystemIdC();
			exId2.setExternalSystem(es);
			exId2.setSystemId("FI1_ICE01");
			fiLe.addExternalSystemId(exId2);
			
			ExternalSystem est = new ExternalSystemC(SEFConstants.DTCC_SDRAccontIdExtSys);
			ExternalSystemId exIdd = new ExternalSystemIdC();
			exIdd.setExternalSystem(est);
			exIdd.setSystemId("FI2_DTCC01");
			cptyLe.addExternalSystemId(exIdd);
			
			ExternalSystemId exIdd2 = new ExternalSystemIdC();
			exIdd2.setExternalSystem(est);
			exIdd2.setSystemId("FI1_DTCC01");
			fiLe.addExternalSystemId(exIdd2);

			double tradeAmt = 1000;
			IdcTransaction tx = initTransaction();
			UnitOfWork uow = tx.getUOW();
			uow.addReadOnlyClasses(getDealingTransactionReadOnlyClasses());
			FXSingleLeg trade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate);
			TradeCloneServiceC.createCptyTrades(trade);
			prepareSingleLegRequest(trade);
			Organization organization = trade.getCounterpartyB().getOrganization();
			trade.setCounterpartyC(getClearingMember(trade.getCounterpartyA(), trade.getCounterpartyB()));
			trade.setCounterpartyD(getClearingMember(trade.getCounterpartyB(), trade.getCounterpartyA()));
			trade.setClearingHouse(ICEOrg);
			trade.setSEFOrg(INFXOrg);
			trade.setUPI("USD_INR_3M");
			
			WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
			wfm.setProperty("organization", organization);
			wfm.setEventName(TradeService.VERIFY_EVENT);
			wfm.setObject(trade);
			wfm.setParameterValue(TradeServiceConstants.DOWNLOAD_FORMAT_KEY, TradeServiceConstants.FPML53_FORMAT);
			TradeDownloadFPMLMessageBuilderC builder = new TradeDownloadFPMLMessageBuilderC();

			String downloadMessage = builder.buildDownloadMessage(wfm, null);
			NonpublicExecutionReport report = null;
			try
			{
				Object object = unmarshaller.unmarshal(new StreamSource(new StringReader(downloadMessage)));
				report = (NonpublicExecutionReport) ((JAXBElement) object).getValue();
			}
			catch ( Exception e )
			{
				fail("Could not validate downloaded xml");
			}
			boolean isFi1ValueSet = false;
			boolean isFi2ValueSet = false;
			for ( Party party : report.getParty() )
			{

				if ( party.getId().equals(FPMLUtilsC.replaceSpecialChars("ORG_"+fiLe.getOrganization().getShortName() + "_" + fiLe.getShortName())) )
				{
					for ( PartyId partyId : party.getPartyId() )
					{
						if ( partyId.getPartyIdScheme().equals(FPMLConstants.clearingHousePartyIdScheme) )
						{
							assertEquals("FI1_ICE01", partyId.getValue());
							isFi1ValueSet = true;
						}
					}

				}
				if ( party.getId().equals(FPMLUtilsC.replaceSpecialChars("ORG_"+cptyLe.getOrganization().getShortName() + "_" + cptyLe.getShortName())) )
				{
					for ( PartyId partyId : party.getPartyId() )
					{
						if ( partyId.getPartyIdScheme().equals(FPMLConstants.clearingHousePartyIdScheme) )
						{
							assertEquals("FI2_ICE01", partyId.getValue());
							isFi2ValueSet = true;
						}
					}

				}

			}
			assertTrue(isFi1ValueSet && isFi2ValueSet);
			log.info(downloadMessage);
			tx.release();
		}
		catch ( Exception e )
		{
			fail("testFPMLDownloadMessage()", e);
		}
		finally
		{
			IdcUtilC.refreshObject(fiLe);
			IdcUtilC.refreshObject(cptyLe);
		}
	}
    public void testFXSwapFPMLDownloadMessage()
	{
		try
		{
			cptyName = "FI2";
			cptyLeName = "FI2-le1";
			fiName = "FI1";
			fiLeName = "FI1-le1";
			String INFXName = "INFXSEF";
	        Organization INFXOrg = ( Organization ) namedEntityReader.execute( Organization.class, INFXName );
		        
	        
			IdcTransaction tx = initTransaction();
			UnitOfWork uow = tx.getUOW();
			uow.addReadOnlyClasses(getDealingTransactionReadOnlyClasses());
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            TradeCloneServiceC.createCptyTrades( swapTrade );
			prepareSwapRequest(swapTrade);
			Organization organization = swapTrade.getCounterpartyB().getOrganization();			
			swapTrade.setSEFOrg(INFXOrg);
			swapTrade.setUPI("USD_INR_3M");
			WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
			wfm.setProperty("organization", organization);
			wfm.setEventName(TradeService.VERIFY_EVENT);
			wfm.setObject(swapTrade);
			wfm.setParameterValue(TradeServiceConstants.DOWNLOAD_FORMAT_KEY, TradeServiceConstants.FPML53_FORMAT);
			TradeDownloadFPMLMessageBuilderC builder = new TradeDownloadFPMLMessageBuilderC();

			String downloadMessage = builder.buildDownloadMessage(wfm, null);
			wfm.setParameterValue(TradeServiceConstants.DOWNLOAD_FORMAT_KEY, TradeServiceConstants.FPML53_FORMAT);
			log.info(downloadMessage);	
			tx.release();
		}
		catch ( Exception e )
		{
			fail("testFXSwapFPMLDownloadMessage()", e);
		}

	}            
    private static String getTagValue( String downloadMessage, String tagName )
    {
        int tagIndex = downloadMessage.indexOf( tagName ) + tagName.length() + 1;
        return downloadMessage.substring( tagIndex, downloadMessage.indexOf( "\n", tagIndex + 1 ) );
    }
    
	public void testNDFFPML()
	{
		try
		{
			double tradeAmt = 1000;
			double spread = 0.05;
			IdcTransaction tx;
			UnitOfWork uow;
			try
			{
				tx = initTransaction();
				uow = tx.getUOW();
				uow.addReadOnlyClasses(getDealingTransactionReadOnlyClasses());
			}
			catch ( Exception e )
			{
				e.printStackTrace();
				return;
			}
			String INFXName = "INFXSEF";
			String ICEName = "ICE";
			cptyName = "FI2";
			cptyLeName = "FI2-le1";
			fiName = "FI1";
			fiLeName = "FI1-le1";
	        cptyOrg = ( Organization ) namedEntityReader.execute( Organization.class, cptyName );
	        fiOrg = ( Organization ) namedEntityReader.execute( Organization.class, fiName );
	        cptyLe = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, cptyLeName );
	        fiLe = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, fiLeName );
	        cptyTpForFI = ( TradingParty ) namedEntityReader.execute( TradingPartyC.class, fiLeName, cptyName );
	        fiTpforCpty = ( TradingParty ) namedEntityReader.execute( TradingPartyC.class, cptyLeName, fiName );
	        Organization INFXOrg = ( Organization ) namedEntityReader.execute( Organization.class, INFXName );
	        Organization ICEOrg = ( Organization ) namedEntityReader.execute( Organization.class, ICEName );
		 
			IdcDate fixingDate = DateTimeFactory.newDate().addDays(2);
			FXSingleLeg trade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate, fixingDate, Tenor.SPOT_TENOR);
			TradeCloneServiceC.createCptyTrades(trade);
			prepareSingleLegRequest(trade);
			trade.setCounterpartyC(getClearingMember(trade.getCounterpartyA(), trade.getCounterpartyB()));
			trade.setCounterpartyD(getClearingMember(trade.getCounterpartyB(), trade.getCounterpartyA()));
			trade.setClearingHouse(ICEOrg);
			trade.setSEFOrg(INFXOrg);
			trade.setUPI("USD_INR_3M");
			WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
			wfm.setProperty("organization", trade.getCounterpartyB().getOrganization());
			wfm.setEventName(TradeService.VERIFY_EVENT);
			wfm.setObject(trade);

			TradeDownloadFPMLMessageBuilderC builder = new TradeDownloadFPMLMessageBuilderC();
			String downloadMessage = "";
			try
			{
				downloadMessage = builder.buildDownloadMessage(wfm, null);
				assertEquals(true,true);
				log(downloadMessage);
			}
			catch ( Exception exc )
			{
				fail("Error while building a download message.", exc);
			}
			log(downloadMessage);
			tx.release();
		}
		catch ( Exception e )
		{
			fail("testNDFFPML", e);
		}
	}
	//In Fpml both First Name & Last name are required fields .As a result when only one of them is set in admin we get a validation exception.
	// We will set only first name in this case. With the new fix this should pass.
	public void testBug54373()
	{
		try
		{
			double tradeAmt = 1000;
			double spread = 0.05;
			IdcTransaction tx;
			UnitOfWork uow;
			try
			{
				tx = initTransaction();
				uow = tx.getUOW();
				uow.addReadOnlyClasses(getDealingTransactionReadOnlyClasses());
			}
			catch ( Exception e )
			{
				e.printStackTrace();
				return;
			}
			String INFXName = "INFXSEF";
			String ICEName = "ICE";
			cptyName = "FI2";
			cptyLeName = "FI2-le1";
			fiName = "FI1";
			takerUserName = "fi1mm1";
			fiLeName = "FI1-le1";
	        cptyOrg = ( Organization ) namedEntityReader.execute( Organization.class, cptyName );
	        fiOrg = ( Organization ) namedEntityReader.execute( Organization.class, fiName );
	        cptyLe = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, cptyLeName );
	        fiLe = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, fiLeName );
	        cptyTpForFI = ( TradingParty ) namedEntityReader.execute( TradingPartyC.class, fiLeName, cptyName );
	        fiTpforCpty = ( TradingParty ) namedEntityReader.execute( TradingPartyC.class, cptyLeName, fiName );
	        Organization INFXOrg = ( Organization ) namedEntityReader.execute( Organization.class, INFXName );
	        Organization ICEOrg = ( Organization ) namedEntityReader.execute( Organization.class, ICEName );
	        User takerUser = ( User ) namedEntityReader.execute( User.class, takerUserName );
			takerUser.getContact().setFirstName("fi1mm1");
			//takerUser.getContact().setLastName("lastName");
			
			
			
			IdcDate fixingDate = DateTimeFactory.newDate().addDays(2);
			FXSingleLeg trade = prepareSingleLegTrade(tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate, fixingDate, Tenor.SPOT_TENOR);
			TradeCloneServiceC.createCptyTrades(trade);
			prepareSingleLegRequest(trade);
			trade.setCounterpartyC(getClearingMember(trade.getCounterpartyA(), trade.getCounterpartyB()));
			trade.setCounterpartyD(getClearingMember(trade.getCounterpartyB(), trade.getCounterpartyA()));
			trade.setClearingHouse(ICEOrg);
			trade.setSEFOrg(INFXOrg);
			trade.setUPI("USD_INR_3M");
			
			WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
			wfm.setProperty("organization", trade.getCounterpartyB().getOrganization());
			wfm.setEventName(TradeService.VERIFY_EVENT);
			wfm.setObject(trade);

			TradeDownloadFPMLMessageBuilderC builder = new TradeDownloadFPMLMessageBuilderC();
			String downloadMessage = "";
			try
			{
				downloadMessage = builder.buildDownloadMessage(wfm, null);
				assertEquals(true,true);
				log(downloadMessage);
			}
			catch ( Exception exc )
			{
				fail("Error while building a download message.", exc);
			}
			log(downloadMessage);
			tx.release();
		}
		catch ( Exception e )
		{
			fail("testNDFFPML", e);
		}
		finally
		{
			IdcUtilC.refreshObject(takerUser);
		}
	}
}
