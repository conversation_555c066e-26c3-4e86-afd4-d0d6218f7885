package com.integral.finance.trade.calculator.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilder;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

import electric.xml.Document;
import electric.xml.Element;
import electric.xml.XPath;

// Copyright (c) 1999-2006 Integral Development Corp.  All rights reserved.

/**
 * TradeDownloadMessageBuilderPTestC
 *
 * <AUTHOR> Development Corp.
 */

public class TradeDownloadMessageBuilderPTestC extends PTestCaseC
{
    protected Organization org1 = null;
    protected Organization org2 = null;

    protected TradingParty tp1 = null;
    protected TradingParty tp2 = null;

    protected LegalEntity le1 = null;
    protected LegalEntity le2 = null;

    static final String EURUSD = "EUR/USD";

    public TradeDownloadMessageBuilderPTestC( String aName )
    {
        super( aName );
        org1 = new OrganizationC();
        org1.setShortName( "TestOrg1" );

        le1 = new LegalEntityC();
        le1.setShortName( "TestLe1" );
        le1.setOrganization( org1 );

        tp1 = new TradingPartyC();
        tp1.setShortName( "TestTp1" );
        tp1.setOrganization( org1 );
        tp1.setLegalEntity( le1 );
    }

    protected FXSingleLeg prepareTrade( double amt, boolean isBid, String ccyPair, Organization org, TradingParty tp )
    {
        FXSingleLeg singleLeg = FXFactory.newFXSingleLeg();
        singleLeg.setOrganization( org );
        singleLeg.setCounterpartyB( tp );
        singleLeg.setCounterpartyA( tp );
        FXLeg fxLeg = singleLeg.getFXLeg();
        FXPaymentParameters fxPmt = FXFactory.newFXPaymentParameters();
        fxPmt.setCurrency1Amount( amt );
        fxPmt.setBuyingCurrency1( !isBid );
        fxLeg.setFXPayment( fxPmt );
        fxPmt.setFXRate( prepareFXRate( ccyPair ) );
        fxPmt.setCurrency1( fxPmt.getFXRate().getBaseCurrency() );
        fxPmt.setCurrency2( fxPmt.getFXRate().getVariableCurrency() );
        return singleLeg;
    }

    protected FXRate prepareFXRate( String ccyPair )
    {
        FXRate fxRate = FXFactory.newFXRate();
        Currency baseCcy = CurrencyFactory.getCurrency( ccyPair.substring( 0, ccyPair.indexOf( '/' ) ) );
        Currency varCcy = CurrencyFactory.getCurrency( ccyPair.substring( ccyPair.indexOf( '/' ) + 1 ) );
        CurrencyPair currencyPair = CurrencyFactory.newCurrenyPair( baseCcy, varCcy );
        fxRate.setCurrencyPair( currencyPair );
        return fxRate;
    }

    public void testTradeDownloadMessageBuilder()
    {
        Trade trade = prepareTrade( 1000000, true, EURUSD, org1, tp1 );
        WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
        wfm.setObject( trade );

        TradeDownloadMessageBuilder builder = new TradeDownloadMessageBuilderC();
        wfm.setProperty( "organization", trade.getCounterpartyA().getOrganization() );
        String downloadMessage = "";
        try
        {
            downloadMessage = builder.buildDownloadMessage( wfm, null );
            // test null fixing date for NDF
            Document xmlDoc = new Document( downloadMessage );
            Element fixingDateElement = xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fixingDate" ) );
            assertNull( fixingDateElement );
            Element fixingTenorElement = xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fixingTenor" ) );
            assertNull( fixingTenorElement );
        }
        catch ( Exception exc )
        {
            fail( "Error while building a download message." + exc );
        }
        log( downloadMessage );
    }
}
