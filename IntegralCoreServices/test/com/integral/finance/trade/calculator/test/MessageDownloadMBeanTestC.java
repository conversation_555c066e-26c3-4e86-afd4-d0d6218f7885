package com.integral.finance.trade.calculator.test;

import com.integral.finance.trade.calculator.download.configuration.MessageDownloadFactory;
import com.integral.finance.trade.calculator.download.configuration.MessageDownloadMBean;
import com.integral.finance.trade.calculator.download.configuration.MessageDownloadMBeanC;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class MessageDownloadMBeanTestC extends MBeanTestCaseC
{
    MessageDownloadMBeanC msgDownloadMBean = ( MessageDownloadMBeanC ) MessageDownloadFactory.getMessageDownloadMBean();

    public MessageDownloadMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( msgDownloadMBean, "offlineDealCapture", MessageDownloadMBean.OFFLINE_DEAL_CAPTURE_KEY, MBeanTestCaseC.STRING, false, true );
    }
}
