package com.integral.finance.trade.calculator.test;

import com.integral.finance.counterparty.CounterpartyExternalSystemIdC;
import com.integral.finance.fx.FXCoverRate;
import com.integral.finance.fx.FXCoverRateC;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.calculator.TradeDownloadKondorMessageBuilderC;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.message.EntityReference;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.ExternalSystemClassification;
import com.integral.persistence.Namespace;
import com.integral.persistence.ExternalSystemC;
import com.integral.session.IdcTransaction;
import com.integral.test.DealingPTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.text.SimpleDateFormat;

// Copyright (c) 1999-2006 Integral Development Corp.  All rights reserved.

/**
 * TradeDownloadKondorMessageBuilderPTestC
 *
 * <AUTHOR> Development Corp.
 */

public class TradeDownloadKondorMessageBuilderPTestC extends DealingPTestCaseC
{

    public TradeDownloadKondorMessageBuilderPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest(new TradeDownloadKondorMessageBuilderPTestC("testKondorV20FormatExternalSystem"));
        suite.addTest(new TradeDownloadKondorMessageBuilderPTestC("testKondorV20Format"));
        suite.addTest(new TradeDownloadKondorMessageBuilderPTestC("testKondorDateFormatExternalId"));
        suite.addTest(new TradeDownloadKondorMessageBuilderPTestC("testTradeDownloadKondorMessageBuilder"));
        suite.addTest(new TradeDownloadKondorMessageBuilderPTestC("testSwapTradeDownloadKondorMessageBuilder"));
        suite.addTest(new TradeDownloadKondorMessageBuilderPTestC("testPreSpotSwapTradeDownloadKondorMessage"));
        suite.addTest(new TradeDownloadKondorMessageBuilderPTestC("testNDFTradeDownloadKondorMessageBuilder"));
        return suite;
    }

    public void testKondorV20FormatExternalSystem()
    {
        try
        {
            double tradeAmt = 1000;
            IdcTransaction tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            FXSingleLeg trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            TradeCloneServiceC.createCptyTrades( trade );
            prepareSingleLegRequest( trade );


            WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
            wfm.setProperty( "organization", trade.getCounterpartyB().getOrganization() );
            wfm.setEventName( TradeService.VERIFY_EVENT );
            wfm.setObject( trade );

            TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();

            EntityReference ef = MessageFactory.newEntityReference();
            ef.setClassName( ExternalSystem.class.getName() );
            ef.setShortName( "KONDOR_V20" );
            ExternalSystem extSys = ( ExternalSystem ) ef.getEntity();
            if ( extSys == null )
            {
                ef.setClassName( ExternalSystemClassification.class.getName() );
                ef.setShortName( "CounterpartyId" );
                ExternalSystemClassification extSysClsf = ( ExternalSystemClassification ) ef.getEntity();
                ef.setClassName( Namespace.class.getName() );
                ef.setShortName( "MAIN" );
                Namespace main = ( Namespace ) ef.getEntity();
                extSys = ( ExternalSystem ) uow.registerObject( new ExternalSystemC() );
                extSys.setShortName( "KONDOR_V20" );
                extSys.setClassification( extSysClsf );
                extSys.setNamespace( main );
            }

            ExternalSystemId extSysId1 = new CounterpartyExternalSystemIdC();
            extSysId1.setExternalSystem( extSys );
            trade.getCounterpartyB().getOrganization().setExternalSystemId( "KONDOR_V20", extSysId1 );
            trade.getCounterpartyB().getOrganization().getExternalSystemId( "KONDOR_V20" ).setSystemId( "true" );
            String downloadMessage = builder.buildDownloadMessage( wfm, null );
            doV20Validations( downloadMessage, trade );
            trade.getCounterpartyB().getOrganization().getExternalSystemId( "KONDOR_V20" ).setSystemId( "false" );
            downloadMessage = builder.buildDownloadMessage( wfm, null );
            assertEquals( downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.CLIENT_DEAL_ID ) == -1, true );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testKondorV20FormatExternalSystem", e );
        }
    }

    public void testKondorV20Format()
    {
        try
        {
            double tradeAmt = 1000;
            IdcTransaction tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            FXSingleLeg trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            TradeCloneServiceC.createCptyTrades( trade );
            prepareSingleLegRequest( trade );

            Organization organization = trade.getCounterpartyB().getOrganization();
            WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
            wfm.setProperty( "organization", organization );
            wfm.setEventName( TradeService.VERIFY_EVENT );
            wfm.setObject( trade );
            wfm.setParameterValue( TradeServiceConstants.DOWNLOAD_FORMAT_KEY, TradeServiceConstants.KONDOR_V20_FORMAT );
            TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();

//            ( ( Entity ) organization.getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat" , TradeServiceConstants.KONDOR_V20_FORMAT );
            String downloadMessage = builder.buildDownloadMessage( wfm, null );
            doV20Validations( downloadMessage, trade );
            wfm.setParameterValue( TradeServiceConstants.DOWNLOAD_FORMAT_KEY, "Kondor+" );
            //          ( ( Entity ) organization.getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat" , "Kondor+" );
            downloadMessage = builder.buildDownloadMessage( wfm, null );
            assertEquals( downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.CLIENT_DEAL_ID ) == -1, true );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testKondorV20Format", e );
        }
    }

    private void doV20Validations( String downloadMessage, Trade trade )
    {
        int cptyRefIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.CLIENT_DEAL_ID );
        assertEquals( cptyRefIndex >= 0, true );
        String cptyRef = downloadMessage.substring( cptyRefIndex + TradeDownloadKondorMessageBuilderC.CLIENT_DEAL_ID.length() + 2, downloadMessage.indexOf( '\n', cptyRefIndex ) - 1 );
        assertEquals( cptyRef, trade.getTransactionID() );


    }

    public void testKondorDateFormatExternalId()
    {
        try
        {
            double tradeAmt = 1000;
            IdcTransaction tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            FXSingleLeg trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            TradeCloneServiceC.createCptyTrades( trade );
            prepareSingleLegRequest( trade );


            WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
            wfm.setProperty( "organization", trade.getCounterpartyB().getOrganization() );
            wfm.setEventName( TradeService.VERIFY_EVENT );
            wfm.setObject( trade );

            TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();

            EntityReference ef = MessageFactory.newEntityReference();
            ef.setClassName( ExternalSystem.class.getName() );
            ef.setShortName( "KONDOR_DateFormat" );
            ExternalSystem extSys = ( ExternalSystem ) ef.getEntity();
            if ( extSys == null )
            {
                ef.setClassName( ExternalSystemClassification.class.getName() );
                ef.setShortName( "CounterpartyId" );
                ExternalSystemClassification extSysClsf = ( ExternalSystemClassification ) ef.getEntity();
                ef.setClassName( Namespace.class.getName() );
                ef.setShortName( "MAIN" );
                Namespace main = ( Namespace ) ef.getEntity();
                extSys = ( ExternalSystem ) uow.registerObject( new ExternalSystemC() );
                extSys.setShortName( "KONDOR_DateFormat" );
                extSys.setClassification( extSysClsf );
                extSys.setNamespace( main );
            }

            ExternalSystemId extSysId1= new CounterpartyExternalSystemIdC();
            extSysId1.setExternalSystem( extSys );
            trade.getCounterpartyB().getOrganization().setExternalSystemId( "SettlementCode", extSysId1 );
            trade.getCounterpartyB().getOrganization().getExternalSystemId( "KONDOR_DateFormat" ).setSystemId( "eeeeeeee" ); // wrong format should return default value
            String downloadMessage = builder.buildDownloadMessage( wfm, null );
            int tradeDateIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.TRD_DATE );
            String tradeDeate = downloadMessage.substring( tradeDateIndex + TradeDownloadKondorMessageBuilderC.TRD_DATE.length() + 1, downloadMessage.indexOf( '\n', tradeDateIndex ) );
            assertEquals( tradeDeate, new SimpleDateFormat( "dd/MM/yyyy" ).format( trade.getTradeDate().asJdkDate() ) );

            trade.getCounterpartyB().getOrganization().getExternalSystemId( "KONDOR_DateFormat" ).setSystemId( "yyyy/MMM/dd" ); // right format as per Java; but not supported as per bug; so return default
            downloadMessage = builder.buildDownloadMessage( wfm, null );
            tradeDateIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.TRD_DATE );
            tradeDeate = downloadMessage.substring( tradeDateIndex + TradeDownloadKondorMessageBuilderC.TRD_DATE.length() + 1, downloadMessage.indexOf( '\n', tradeDateIndex ) );
            assertEquals( tradeDeate, new SimpleDateFormat( "dd/MM/yyyy" ).format( trade.getTradeDate().asJdkDate() ) );

            helperKondorDateFormat("yyyy/MM/dd", trade, wfm);
            helperKondorDateFormat("MM/dd/yy", trade, wfm);
            helperKondorDateFormat("MM/dd/yyyy", trade, wfm);
            helperKondorDateFormat("yy/MM/dd", trade, wfm);
            helperKondorDateFormat("dd/MM/yy", trade, wfm);
            helperKondorDateFormat("MM-dd-yy", trade, wfm);
            helperKondorDateFormat("yyyy-MM-dd", trade, wfm);
            helperKondorDateFormat("dd/MM/yyyy", trade, wfm);
            helperKondorDateFormat("MM-dd-yyyy", trade, wfm);
            helperKondorDateFormat("dd-MMM-yy", trade, wfm);
            helperKondorDateFormat("yy-MM-dd", trade, wfm);
            helperKondorDateFormat("dd-MM-yy", trade, wfm);
            helperKondorDateFormat("dd-MM-yyyy", trade, wfm);
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testKondorDateFormatExternalId", e );
        }
    }

    private void helperKondorDateFormat( String dateFormat, FXSingleLeg trade , WorkflowMessage wfm ) throws Exception
    {
        trade.getCounterpartyB().getOrganization().getExternalSystemId( "KONDOR_DateFormat" ).setSystemId( dateFormat ); // right format as per Java; but not supported as per bug; so return default
        TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();
        String downloadMessage = builder.buildDownloadMessage( wfm, null );
        int tradeDateIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.TRD_DATE );
        String tradeDeate = downloadMessage.substring( tradeDateIndex + TradeDownloadKondorMessageBuilderC.TRD_DATE.length() + 1, downloadMessage.indexOf( '\n', tradeDateIndex ) );
        assertEquals( tradeDeate, new SimpleDateFormat( dateFormat ).format( trade.getTradeDate().asJdkDate() ) );
    }

    public void testTradeDownloadKondorMessageBuilder()
    {
        try
        {
            double tradeAmt = 1000;
            double spread = 0.05;
            IdcTransaction tx;
            UnitOfWork uow;
            try
            {
                tx = initTransaction();
                uow = tx.getUOW();
                uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            }
            catch ( Exception e )
            {
                e.printStackTrace();
                return;
            }

            FXSingleLeg trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate );
            TradeCloneServiceC.createCptyTrades( trade );
            prepareSingleLegRequest( trade );

            FXCoverRate coverRate = new FXCoverRateC();
            FXRate acceptedFXRate = trade.getFXLeg().getFXPayment().getFXRate();
            coverRate.setNamespace( trade.getCounterpartyB().getNamespace() );
            coverRate.getFXRate().setSpotRate( acceptedFXRate.getSpotRate() );
            coverRate.getFXRate().setForwardPoints( 10 );
            coverRate.getFXRate().setRate( acceptedFXRate.getSpotRate() + spread );
            trade.getFXLeg().getFXPayment().setFXCoverRate( coverRate );

            WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
            wfm.setProperty( "organization", trade.getCounterpartyB().getOrganization() );
            trade.getCounterpartyB().getOrganization().getExternalSystemId( "KONDOR_DateFormat" ).setSystemId( "" );
            wfm.setEventName( TradeService.VERIFY_EVENT );
            wfm.setObject( trade );

            TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();
            String downloadMessage = "";
            try
            {
                // do the download on behalf of counterpartyB
                //trade.setCptyTrade( trade.getCptyTrade( 'B' ) );
                downloadMessage = builder.buildDownloadMessage( wfm, null );
                String subStr = downloadMessage.substring( downloadMessage.indexOf( '\n' + TradeDownloadKondorMessageBuilderC.SPOT_RATE ) );
                double spotRate = Double.valueOf( subStr.substring( TradeDownloadKondorMessageBuilderC.SPOT_RATE.length() + 2, subStr.indexOf( '\n', TradeDownloadKondorMessageBuilderC.SPOT_RATE.length() + 2 ) ) );
                assertEquals( "The spot rate obtained in msg should match the one in trade. ", spotRate == trade.getFXLeg().getFXPayment().getFXRate().getSpotRate(), true );

                subStr = downloadMessage.substring( downloadMessage.indexOf( '\n' + TradeDownloadKondorMessageBuilderC.COVER_SPOT_RATE ) );
                double coverSpotRate = Double.valueOf( subStr.substring( TradeDownloadKondorMessageBuilderC.COVER_SPOT_RATE.length() + 2, subStr.indexOf( '\n', TradeDownloadKondorMessageBuilderC.COVER_SPOT_RATE.length() + 2 ) ) );
                assertEquals( "The cover spot rate obtained in msg should match the one in trade. ", coverSpotRate == trade.getFXLeg().getFXPayment().getFXCoverRate().getFXRate().getSpotRate(), true );

                int cptyRefIndex = downloadMessage.indexOf( "Cpty.Ref" );
                String cptyRef = downloadMessage.substring( cptyRefIndex + "Cpty.Ref".length() + 2, downloadMessage.indexOf( '\n', cptyRefIndex ) - 1 );
                assertEquals( cptyRef, trade.getCounterpartyA().getShortName() );

                int tradeDateIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.TRD_DATE );
                String tradeDeate = downloadMessage.substring( tradeDateIndex + TradeDownloadKondorMessageBuilderC.TRD_DATE.length() + 1, downloadMessage.indexOf( '\n', tradeDateIndex ) );
                assertEquals( tradeDeate, new SimpleDateFormat( "dd/MM/yyyy" ).format( trade.getTradeDate().asJdkDate() ) );

                int amt1StrIndex = downloadMessage.indexOf( "Amount1" );
                String amt1Str = downloadMessage.substring( amt1StrIndex + "Amount1".length() + 1, downloadMessage.indexOf( '\n', amt1StrIndex ) );
                log( "amount1 value=" + amt1Str );
                assertEquals( amt1Str.indexOf( "-" ) != -1, true ); // cptyB is selling amount1
                int amt2StrIndex = downloadMessage.indexOf( "Amount2" );
                String amt2Str = downloadMessage.substring( amt2StrIndex + "Amount2".length() + 1, downloadMessage.indexOf( '\n', amt2StrIndex ) );
                log( "amount2 value=" + amt2Str );
                assertEquals( amt2Str.indexOf( "-" ) != -1, false ); // cptyB is buying amount1

                // download for cptyA
                wfm.setProperty( "organization", trade.getCounterpartyA().getOrganization() );
                //trade.setCptyTrade( trade.getCptyTrade( 'A' ) );
                downloadMessage = builder.buildDownloadMessage( wfm, null );
                cptyRefIndex = downloadMessage.indexOf( "Cpty.Ref" );
                cptyRef = downloadMessage.substring( cptyRefIndex + "Cpty.Ref".length() + 2, downloadMessage.indexOf( '\n', cptyRefIndex ) - 1 );
                assertEquals( cptyRef, trade.getCounterpartyB().getShortName() );
                int amt1StrIndexCptyA = downloadMessage.indexOf( "Amount1" );
                String amt1StrCptyA = downloadMessage.substring( amt1StrIndexCptyA + "Amount1".length() + 1, downloadMessage.indexOf( '\n', amt1StrIndexCptyA ) );
                log( "amount1 value=" + amt1StrCptyA );
                assertEquals( amt1StrCptyA.indexOf( "-" ) != -1, false ); // cptyA is buying amount1
                int amt2StrIndexCptyA = downloadMessage.indexOf( "Amount2" );
                String amt2StrCptyA = downloadMessage.substring( amt2StrIndexCptyA + "Amount2".length() + 1, downloadMessage.indexOf( '\n', amt2StrIndexCptyA ) );
                log( "amount2 value=" + amt2StrCptyA );
                assertEquals( amt2StrCptyA.indexOf( "-" ) != -1, true ); // cptyA is selling amount1
                int isClsIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.IS_CLS ) + TradeDownloadKondorMessageBuilderC.IS_CLS.length() + 1;
                assertEquals( downloadMessage.substring( isClsIndex, downloadMessage.indexOf( "\n", isClsIndex + 1 ) ).equals( TradeDownloadKondorMessageBuilderC.IS_CLS_DEFAULT_V ), true );
                
                int forwardTypeIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.FORWARD_TYPE );
                assertEquals( forwardTypeIndex, -1 );
                
                int liquidationDateIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.LIQUIDATION_DATE );
                assertEquals( liquidationDateIndex , -1 );
                
                int NDFFixingTypeIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.NDF_FIXING_TYPE );
                assertEquals( NDFFixingTypeIndex, -1 );
                
                log( downloadMessage );
            }
            catch ( Exception exc )
            {
                fail( "Error while building a download message.", exc );
            }
            log( downloadMessage );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testTradeDownloadKondorMessageBuilder", e );
        }
    }

    public void testSwapTradeDownloadKondorMessageBuilder() throws Exception
    {
        try
        {
            IdcTransaction tx = initTransaction();
            uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            TradeCloneServiceC.createCptyTrades( swapTrade );


            double spread = 0.05;

            FXCoverRate nearCoverRate = new FXCoverRateC();
            FXRate acceptedFXRate = swapTrade.getNearLeg().getFXPayment().getFXRate();
            nearCoverRate.setNamespace( swapTrade.getCounterpartyB().getNamespace() );
            nearCoverRate.getFXRate().setSpotRate( acceptedFXRate.getSpotRate() );
            nearCoverRate.getFXRate().setRate( acceptedFXRate.getSpotRate() + spread );
            swapTrade.getNearLeg().getFXPayment().setFXCoverRate( nearCoverRate );

            FXCoverRate farCoverRate = new FXCoverRateC();
            acceptedFXRate = swapTrade.getFarLeg().getFXPayment().getFXRate();
            farCoverRate.setNamespace( swapTrade.getCounterpartyB().getNamespace() );
            farCoverRate.getFXRate().setSpotRate( acceptedFXRate.getSpotRate() );
            farCoverRate.getFXRate().setForwardPoints( 20 );
            farCoverRate.getFXRate().setRate( acceptedFXRate.getSpotRate() + spread + 0.01 );
            swapTrade.getFarLeg().getFXPayment().setFXCoverRate( farCoverRate );

            WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
            wfm.setEventName( TradeService.VERIFY_EVENT );
            wfm.setProperty( "organization", swapTrade.getCounterpartyB().getOrganization() );
            wfm.setObject( swapTrade );
            //swapTrade.setCptyTrade( swapTrade.getCptyTrade( 'B' ) );
            EntityReference ef = MessageFactory.newEntityReference();
            ef.setClassName( ExternalSystem.class.getName() );
            ef.setShortName( "CLSCounterparty" );
            ExternalSystem extSys = ( ExternalSystem ) ef.getEntity();

            CounterpartyExternalSystemIdC extSysId1 = new CounterpartyExternalSystemIdC();
            extSysId1.setExternalSystem( extSys );
            extSysId1.setName( "Y" );

            swapTrade.getCptyTrade( 'B' ).getTradingParty().setExternalSystemId( "CLSCounterparty", extSysId1 );

            TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();
            String downloadMessage = builder.buildDownloadMessage( wfm, null );

            String subStr = downloadMessage.substring( downloadMessage.indexOf( '\n' + TradeDownloadKondorMessageBuilderC.SPOT_RATE ) );
            double spotRate = Double.valueOf( subStr.substring( TradeDownloadKondorMessageBuilderC.SPOT_RATE.length() + 2, subStr.indexOf( '\n', TradeDownloadKondorMessageBuilderC.SPOT_RATE.length() + 2 ) ) );
            assertEquals( "The spot rate obtained in msg should match the one in trade. ", spotRate == swapTrade.getFarLeg().getFXPayment().getFXRate().getSpotRate(), true );

            subStr = downloadMessage.substring( downloadMessage.indexOf( '\n' + TradeDownloadKondorMessageBuilderC.COVER_SPOT_RATE ) );
            double coverSpotRate = Double.valueOf( subStr.substring( TradeDownloadKondorMessageBuilderC.COVER_SPOT_RATE.length() + 2, subStr.indexOf( '\n', TradeDownloadKondorMessageBuilderC.COVER_SPOT_RATE.length() + 2 ) ) );
            assertEquals( "The spot rate obtained in msg should match the one in trade. ", coverSpotRate == swapTrade.getFarLeg().getFXPayment().getFXCoverRate().getFXRate().getSpotRate(), true );

            int isClsIndex = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.IS_CLS ) + TradeDownloadKondorMessageBuilderC.IS_CLS.length() + 1;
            assertEquals( downloadMessage.substring( isClsIndex, downloadMessage.indexOf( "\n", isClsIndex + 1 ) ).equals( "Y \"Yes\"" ), true );

            log( downloadMessage );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSwapTradeDownloadKondorMessageBuilder", e );
        }
    }

    public void testPreSpotSwapTradeDownloadKondorMessage() throws Exception
    {
        try
        {
            IdcTransaction tx = initTransaction();
            uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            FXSwap preSpotSwapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.subtractDays( 1 ), spotDate.addDays( 2 ) );
            TradeCloneServiceC.createCptyTrades( preSpotSwapTrade );

            WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
            wfm.setEventName( TradeService.VERIFY_EVENT );
            wfm.setProperty( "organization", preSpotSwapTrade.getCounterpartyB().getOrganization() );
            wfm.setObject( preSpotSwapTrade );
            //preSpotSwapTrade.setCptyTrade( preSpotSwapTrade.getCptyTrade( 'B' ) );

            TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();
            String downloadMessage = builder.buildDownloadMessage( wfm, null );
            log( downloadMessage );
            assertEquals( "It should be a round swap", downloadMessage.indexOf( "Round Swap" ) != -1, true );

            // now do a spotSwap trade.
            FXSwap spotSwapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            TradeCloneServiceC.createCptyTrades( spotSwapTrade );

            WorkflowMessage wfm1 = MessageFactory.newWorkflowMessage();
            wfm1.setEventName( TradeService.VERIFY_EVENT );
            wfm1.setProperty( "organization", spotSwapTrade.getCounterpartyB().getOrganization() );
            wfm1.setObject( spotSwapTrade );
            //spotSwapTrade.setCptyTrade( spotSwapTrade.getCptyTrade( 'B' ) );

            String downloadMessage1 = builder.buildDownloadMessage( wfm1, null );
            log( downloadMessage1 );
            assertEquals( "It should be a round swap", downloadMessage1.indexOf( "Round Swap" ) != -1, true );

            // now do a post spot swap trade.
            FXSwap postSpotSwapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays( 1 ), spotDate.addDays( 2 ) );
            TradeCloneServiceC.createCptyTrades( postSpotSwapTrade );

            WorkflowMessage wfm2 = MessageFactory.newWorkflowMessage();
            wfm2.setEventName( TradeService.VERIFY_EVENT );
            wfm2.setProperty( "organization", postSpotSwapTrade.getCounterpartyB().getOrganization() );
            wfm2.setObject( postSpotSwapTrade );
            //postSpotSwapTrade.setCptyTrade( postSpotSwapTrade.getCptyTrade( 'B' ) );

            String downloadMessage2 = builder.buildDownloadMessage( wfm2, null );
            log( downloadMessage2 );
            assertEquals( "It should be a round swap", downloadMessage2.indexOf( "Round Swap" ) != -1, false );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testPreSpotSwapTradeDownloadKondorMessage", e );
        }
    }
    
    public void testNDFTradeDownloadKondorMessageBuilder()
    {
        try
        {
            double tradeAmt = 1000;
            double spread = 0.05;
            IdcTransaction tx;
            UnitOfWork uow;
            try
            {
                tx = initTransaction();
                uow = tx.getUOW();
                uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            }
            catch ( Exception e )
            {
                e.printStackTrace();
                return;
            }

            IdcDate fixingDate = DateTimeFactory.newDate().addDays( 2 );
            FXSingleLeg trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpforCpty, bidRates[0], spotDate, fixingDate, Tenor.SPOT_TENOR );
            TradeCloneServiceC.createCptyTrades( trade );
            prepareSingleLegRequest( trade );


            WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
            wfm.setProperty( "organization", trade.getCounterpartyB().getOrganization() );
            trade.getCounterpartyB().getOrganization().getExternalSystemId( "KONDOR_DateFormat" ).setSystemId( "" );
            wfm.setEventName( TradeService.VERIFY_EVENT );
            wfm.setObject( trade );

            TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();
            String downloadMessage = "";
            try
            {
                downloadMessage = builder.buildDownloadMessage( wfm, null );

                checkTagValue( downloadMessage, TradeDownloadKondorMessageBuilderC.FORWARD_TYPE, TradeDownloadKondorMessageBuilderC.NDF_FORWARD_TYPE_VALUE );
                
                String liquidationDateStr = getTagValue( downloadMessage, TradeDownloadKondorMessageBuilderC.LIQUIDATION_DATE );
                assertEquals( new SimpleDateFormat( "dd/MM/yyyy" ).format( fixingDate.asJdkDate() ), liquidationDateStr );
                
                checkTagValue( downloadMessage, TradeDownloadKondorMessageBuilderC.NDF_FIXING_TYPE, TradeDownloadKondorMessageBuilderC.NDF_FIXING_TYPE_VALUE );
                
                log( downloadMessage );
            }
            catch ( Exception exc )
            {
                fail( "Error while building a download message.", exc );
            }
            log( downloadMessage );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testNDFTradeDownloadKondorMessageBuilder", e );
        }
    }
    
    private static void checkTagValue( String downloadMessage, String tagName, String tagValue )
    {
        String value = getTagValue( downloadMessage, tagName ); 
        assertEquals( tagValue, value );
    }
                
    private static String getTagValue( String downloadMessage, String tagName )
    {
        int tagIndex = downloadMessage.indexOf( tagName ) + tagName.length() + 1;
        return downloadMessage.substring( tagIndex, downloadMessage.indexOf( "\n", tagIndex + 1 ) );
    }
}
