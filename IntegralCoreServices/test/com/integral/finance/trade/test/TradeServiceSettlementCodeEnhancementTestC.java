package com.integral.finance.trade.test;

import com.integral.finance.counterparty.CounterpartyExternalSystemIdC;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.calculator.TradeDownloadFIXMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadKondorMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.message.EntityReference;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemC;
import com.integral.persistence.ExternalSystemClassification;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.Namespace;
import com.integral.session.IdcTransaction;
import com.integral.user.Organization;
import electric.xml.Document;
import electric.xml.XPath;
import org.eclipse.persistence.sessions.UnitOfWork;

public class TradeServiceSettlementCodeEnhancementTestC extends TradeServiceTestC
{
    WorkflowMessage wmTaker;
    WorkflowMessage wmMaker;
    WorkflowMessage wmTakerPBOrg;
    WorkflowMessage wmMakerPBOrg;
    Trade trd;
    ExternalSystem extSys;
    ExternalSystemId extSysId1;
    ExternalSystemId extSysId2;
    ExternalSystemId extSysId3;
    ExternalSystemId extSysId4;
    ExternalSystemId extSysId5;
    ExternalSystemId extSysId6;
    ExternalSystemId extSysId7;
    ExternalSystemId extSysId8;

    public TradeServiceSettlementCodeEnhancementTestC( String name ) throws Exception
    {
        super( name );
        IdcTransaction tx;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        EntityReference ef = MessageFactory.newEntityReference();
        ef.setClassName( ExternalSystem.class.getName() );
        ef.setShortName( "SettlementCode" );
        extSys = ( ExternalSystem ) ef.getEntity();
        if ( extSys == null )
        {
            ef.setClassName( ExternalSystemClassification.class.getName() );
            ef.setShortName( "CounterpartyId" );
            ExternalSystemClassification extSysClsf = ( ExternalSystemClassification ) ef.getEntity();
            ef.setClassName( Namespace.class.getName() );
            ef.setShortName( "MAIN" );
            Namespace main = ( Namespace ) ef.getEntity();
            extSys = ( ExternalSystem ) uow.registerObject( new ExternalSystemC() );
            extSys.setShortName( "SettlementCode" );
            extSys.setClassification( extSysClsf );
            extSys.setNamespace( main );
        }
        extSysId1 = new CounterpartyExternalSystemIdC();
        extSysId1.setExternalSystem( extSys );
        extSysId2 = new CounterpartyExternalSystemIdC();
        extSysId2.setExternalSystem( extSys );
        extSysId3 = new CounterpartyExternalSystemIdC();
        extSysId3.setExternalSystem( extSys );
        extSysId4 = new CounterpartyExternalSystemIdC();
        extSysId4.setExternalSystem( extSys );
        extSysId5 = new CounterpartyExternalSystemIdC();
        extSysId5.setExternalSystem( extSys );
        extSysId6 = new CounterpartyExternalSystemIdC();
        extSysId6.setExternalSystem( extSys );
        extSysId7 = new CounterpartyExternalSystemIdC();
        extSysId7.setExternalSystem( extSys );
        extSysId8 = new CounterpartyExternalSystemIdC();
        extSysId8.setExternalSystem( extSys );

        tx.commit();
        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        tx.release();

        wmTaker = MessageFactory.newWorkflowMessage();
        wmTaker.setObject( trd );
        wmTaker.setEventName( TradeService.VERIFY_EVENT );
        wmTaker.setProperty( "organization", trd.getCounterpartyA().getOrganization() );

        wmMaker = MessageFactory.newWorkflowMessage();
        wmMaker.setObject( trd );
        wmMaker.setProperty( "organization", ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntityOrganization() );
        wmMaker.setEventName( TradeService.VERIFY_EVENT );

        wmTakerPBOrg = MessageFactory.newWorkflowMessage();
        wmTakerPBOrg.setObject( trd );
        wmTakerPBOrg.setEventName( TradeService.VERIFY_EVENT );
        wmTakerPBOrg.setProperty( "organization", trd.getCptyTrade( 'C' ).getNamespace().getOrganizations().iterator().next() );

        wmMakerPBOrg = MessageFactory.newWorkflowMessage();
        wmMakerPBOrg.setObject( trd );
        wmMakerPBOrg.setEventName( TradeService.VERIFY_EVENT );
        wmMakerPBOrg.setProperty( "organization", trd.getCptyTrade( 'D' ).getNamespace().getOrganizations().iterator().next() );


    }

    private void setSettlementCode()
    {
        trd.getCounterpartyA().setExternalSystemId( "SettlementCode", extSysId1 );
        trd.getCounterpartyA().getExternalSystemId( "SettlementCode" ).setSystemId( "TestCptyATaker" );

        CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), trd.getCounterpartyA().getOrganization() ).setExternalSystemId( "SettlementCode", extSysId2 );
        CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), trd.getCounterpartyA().getOrganization() ).getExternalSystemId( "SettlementCode" ).setSystemId( "TestCptyBTaker" );

        trd.getCounterpartyB().setExternalSystemId( "SettlementCode", extSysId3 );
        trd.getCounterpartyB().getExternalSystemId( "SettlementCode" ).setSystemId( "TestCptyBMaker" );

        CounterpartyUtilC.getTradingParty( trd.getCounterpartyA(), trd.getCounterpartyB().getOrganization() ).setExternalSystemId( "SettlementCode", extSysId4 );
        CounterpartyUtilC.getTradingParty( trd.getCounterpartyA(), trd.getCounterpartyB().getOrganization() ).getExternalSystemId( "SettlementCode" ).setSystemId( "TestCptyAMaker" );


        CounterpartyUtilC.getTradingParty( trd.getCounterpartyA(), ( Organization ) trd.getCptyTrade( 'C' ).getNamespace().getOrganizations().iterator().next() ).setExternalSystemId( "SettlementCode", extSysId5 );
        CounterpartyUtilC.getTradingParty( trd.getCounterpartyA(), ( Organization ) trd.getCptyTrade( 'C' ).getNamespace().getOrganizations().iterator().next() ).getExternalSystemId( "SettlementCode" ).setSystemId( "TestCptyATakerPB" );

        CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), ( Organization ) trd.getCptyTrade( 'C' ).getNamespace().getOrganizations().iterator().next() ).setExternalSystemId( "SettlementCode", extSysId6 );
        CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), ( Organization ) trd.getCptyTrade( 'C' ).getNamespace().getOrganizations().iterator().next() ).getExternalSystemId( "SettlementCode" ).setSystemId( "TestCptyBTakerPB" );

        CounterpartyUtilC.getTradingParty( trd.getCounterpartyA(), ( Organization ) trd.getCptyTrade( 'D' ).getNamespace().getOrganizations().iterator().next() ).setExternalSystemId( "SettlementCode", extSysId7 );
        CounterpartyUtilC.getTradingParty( trd.getCounterpartyA(), ( Organization ) trd.getCptyTrade( 'D' ).getNamespace().getOrganizations().iterator().next() ).getExternalSystemId( "SettlementCode" ).setSystemId( "TestCptyAMakerPB" );

        CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), ( Organization ) trd.getCptyTrade( 'D' ).getNamespace().getOrganizations().iterator().next() ).setExternalSystemId( "SettlementCode", extSysId8 );
        CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), ( Organization ) trd.getCptyTrade( 'D' ).getNamespace().getOrganizations().iterator().next() ).getExternalSystemId( "SettlementCode" ).setSystemId( "TestCptyBMakerPB" );


    }

    private void clearSettlementCode()
    {
        if ( trd.getCounterpartyA().getExternalSystemId( "SettlementCode" ) == null )
        {
            return;
        }
        trd.getCounterpartyA().getExternalSystemId( "SettlementCode" ).setSystemId( "" );
        CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), trd.getCounterpartyA().getOrganization() ).getExternalSystemId( "SettlementCode" ).setSystemId( "" );
        trd.getCounterpartyB().getExternalSystemId( "SettlementCode" ).setSystemId( "" );
        CounterpartyUtilC.getTradingParty( trd.getCounterpartyA(), trd.getCounterpartyB().getOrganization() ).getExternalSystemId( "SettlementCode" ).setSystemId( "" );

    }

    public void testFinXMLDownload() throws Exception
    {
        setSettlementCode();
        //trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xmlTaker = new TradeDownloadMessageBuilderC().buildDownloadMessage( wmTaker, null );
        xmlTaker = applyStleSheetOnXML( xmlTaker, "FinXML51v20" );
        Document xmlDoc = new Document( xmlTaker );
        String cptyTradeLegalEntity = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_LEGAL_ENTITY ) ).getTextString();
        String cptyTradeTradingParty = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_TRADING_PARTY ) ).getTextString();
        assertEquals( cptyTradeLegalEntity, "TestCptyATaker" );
        assertEquals( cptyTradeTradingParty, "TestCptyBTaker" );

        //trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        String xmlMaker = new TradeDownloadMessageBuilderC().buildDownloadMessage( wmMaker, null );
        xmlMaker = applyStleSheetOnXML( xmlMaker, "FinXML51v20" );
        xmlDoc = new Document( xmlMaker );
        cptyTradeLegalEntity = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_LEGAL_ENTITY ) ).getTextString();
        cptyTradeTradingParty = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_TRADING_PARTY ) ).getTextString();
        assertEquals( cptyTradeLegalEntity, "TestCptyBMaker" );
        assertEquals( cptyTradeTradingParty, "TestCptyAMaker" );
        clearSettlementCode();
    }

    public void testKondorDownload() throws Exception
    {
        setSettlementCode();
        //trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xmlTaker = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( wmTaker, null );
        int index1 = xmlTaker.indexOf( "Cpty.Ref" );
        assertEquals( xmlTaker.substring( xmlTaker.indexOf( '\"', index1 ), xmlTaker.indexOf( '\n', index1 ) ), "\"TestCptyBTaker\"" );

        //trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        String xmlMaker = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( wmMaker, null );
        index1 = xmlMaker.indexOf( "Cpty.Ref" );
        assertEquals( xmlMaker.substring( xmlMaker.indexOf( '\"', index1 ), xmlMaker.indexOf( '\n', index1 ) ), "\"TestCptyAMaker\"" );
        clearSettlementCode();
    }

    public void testFIXDownload() throws Exception
    {
        setSettlementCode();
        //trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xmlTaker = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmTaker, null );
        int index1 = xmlTaker.indexOf( "50=" ) + 3;
        String out1 = xmlTaker.substring( index1, xmlTaker.indexOf( '\u0001', index1 ) );
        assertEquals( out1, "TestCptyATaker" );
        index1 = xmlTaker.indexOf( "57=" ) + 3;
        assertEquals( xmlTaker.substring( index1, xmlTaker.indexOf( '\u0001', index1 ) ), "TestCptyBTaker" );

        //trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        String xmlMaker = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmMaker, null );
        index1 = xmlMaker.indexOf( "50=" ) + 3;
        out1 = xmlMaker.substring( index1, xmlMaker.indexOf( '\u0001', index1 ) );
        assertEquals( out1, "TestCptyAMaker" );
        index1 = xmlMaker.indexOf( "57=" ) + 3;
        assertEquals( xmlMaker.substring( index1, xmlMaker.indexOf( '\u0001', index1 ) ), "TestCptyBMaker" );


        //trd.setCptyTrade( trd.getCptyTrade( 'C' ) );
        String xmlTakerPBC = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmTakerPBOrg, null );
        index1 = xmlTakerPBC.indexOf( "50=" ) + 3;
        out1 = xmlTakerPBC.substring( index1, xmlTakerPBC.indexOf( '\u0001', index1 ) );
        assertEquals( out1, "TestCptyATakerPB" );
        index1 = xmlTakerPBC.indexOf( "57=" ) + 3;
        assertEquals( xmlTakerPBC.substring( index1, xmlTakerPBC.indexOf( '\u0001', index1 ) ), "TestCptyBTakerPB" );

        //trd.setCptyTrade( trd.getCptyTrade( 'D' ) );
        String xmlMakerPBD = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmMakerPBOrg, null );
        index1 = xmlMakerPBD.indexOf( "50=" ) + 3;
        out1 = xmlMakerPBD.substring( index1, xmlMakerPBD.indexOf( '\u0001', index1 ) );
        assertEquals( out1, "TestCptyAMakerPB" );
        index1 = xmlMakerPBD.indexOf( "57=" ) + 3;
        assertEquals( xmlMakerPBD.substring( index1, xmlMakerPBD.indexOf( '\u0001', index1 ) ), "TestCptyBMakerPB" );

        clearSettlementCode();
    }

    public void testFIXDownloadTrader() throws Exception
    {
        setSettlementCode();
        IdcTransaction tx;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        trd.setCounterpartyB( CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), trd.getCounterpartyA().getOrganization() ) );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();
        //trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xmlTaker = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmTaker, null );
        int index1 = xmlTaker.indexOf( "50=" ) + 3;
        String out1 = xmlTaker.substring( index1, xmlTaker.indexOf( '\u0001', index1 ) );
        assertEquals( out1, "TestCptyATaker" );
        index1 = xmlTaker.indexOf( "57=" ) + 3;
        assertEquals( xmlTaker.substring( index1, xmlTaker.indexOf( '\u0001', index1 ) ), "TestCptyBTaker" );

        //trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        String xmlMaker = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmMaker, null );
        index1 = xmlMaker.indexOf( "50=" ) + 3;
        out1 = xmlMaker.substring( index1, xmlMaker.indexOf( '\u0001', index1 ) );
        assertEquals( out1, "TestCptyAMaker" );
        index1 = xmlMaker.indexOf( "57=" ) + 3;
        assertEquals( xmlMaker.substring( index1, xmlMaker.indexOf( '\u0001', index1 ) ), "TestCptyBMaker" );
        clearSettlementCode();
    }

    public void testFinXMLDownloadShowingShortName() throws Exception
    {
        clearSettlementCode();
        //trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xmlTaker = new TradeDownloadMessageBuilderC().buildDownloadMessage( wmTaker, null );
        xmlTaker = applyStleSheetOnXML( xmlTaker, "FinXML51v20" );
        Document xmlDoc = new Document( xmlTaker );
        String cptyTradeLegalEntity = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_LEGAL_ENTITY ) ).getTextString();
        String cptyTradeTradingParty = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_TRADING_PARTY ) ).getTextString();
        assertEquals( cptyTradeLegalEntity, trd.getCounterpartyA().getShortName() );
        assertEquals( cptyTradeTradingParty, trd.getCounterpartyB().getShortName() );

        //trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        String xmlMaker = new TradeDownloadMessageBuilderC().buildDownloadMessage( wmMaker, null );
        xmlMaker = applyStleSheetOnXML( xmlMaker, "FinXML51v20" );
        xmlDoc = new Document( xmlMaker );
        cptyTradeLegalEntity = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_LEGAL_ENTITY ) ).getTextString();
        cptyTradeTradingParty = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_TRADING_PARTY ) ).getTextString();
        assertEquals( cptyTradeLegalEntity, trd.getCounterpartyB().getShortName() );
        assertEquals( cptyTradeTradingParty, trd.getCounterpartyA().getShortName() );
    }

    public void testKondorDownloadShowingShortName() throws Exception
    {
        clearSettlementCode();
        //trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xmlTaker = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( wmTaker, null );
        int index1 = xmlTaker.indexOf( "Cpty.Ref" );
        assertEquals( xmlTaker.substring( xmlTaker.indexOf( '\"', index1 ), xmlTaker.indexOf( '\n', index1 ) ), "\"FI2-le1\"" );

        //trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        String xmlMaker = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( wmMaker, null );
        index1 = xmlMaker.indexOf( "Cpty.Ref" );
        assertEquals( xmlMaker.substring( xmlMaker.indexOf( '\"', index1 ), xmlMaker.indexOf( '\n', index1 ) ), "\"FI1-le1\"" );
    }

    public void testFIXDownloadShowingShortName() throws Exception
    {
        clearSettlementCode();
        //trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xmlTaker = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmTaker, null );
        int index1 = xmlTaker.indexOf( "50=" ) + 3;
        String out1 = xmlTaker.substring( index1, xmlTaker.indexOf( '\u0001', index1 ) );
        assertEquals( out1, trd.getCounterpartyA().getShortName() );
        index1 = xmlTaker.indexOf( "57=" ) + 3;
        assertEquals( xmlTaker.substring( index1, xmlTaker.indexOf( '\u0001', index1 ) ), trd.getCounterpartyB().getShortName() );

        //trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        String xmlMaker = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmMaker, null );
        index1 = xmlMaker.indexOf( "50=" ) + 3;
        out1 = xmlMaker.substring( index1, xmlMaker.indexOf( '\u0001', index1 ) );
        assertEquals( out1, trd.getCounterpartyA().getShortName() );
        index1 = xmlMaker.indexOf( "57=" ) + 3;
        assertEquals( xmlMaker.substring( index1, xmlMaker.indexOf( '\u0001', index1 ) ), trd.getCounterpartyB().getShortName() );
    }

    public void testFIXDownloadShowingShortNameForTrader() throws Exception
    {
        clearSettlementCode();
        IdcTransaction tx;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        trd.setCounterpartyB( CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), trd.getCounterpartyA().getOrganization() ) );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();
        //trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xmlTaker = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmTaker, null );
        int index1 = xmlTaker.indexOf( "50=" ) + 3;
        String out1 = xmlTaker.substring( index1, xmlTaker.indexOf( '\u0001', index1 ) );
        assertEquals( out1, trd.getCounterpartyA().getShortName() );
        index1 = xmlTaker.indexOf( "57=" ) + 3;
        assertEquals( xmlTaker.substring( index1, xmlTaker.indexOf( '\u0001', index1 ) ), trd.getCounterpartyB().getShortName() );

        //trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        String xmlMaker = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wmMaker, null );
        index1 = xmlMaker.indexOf( "50=" ) + 3;
        out1 = xmlMaker.substring( index1, xmlMaker.indexOf( '\u0001', index1 ) );
        assertEquals( out1, trd.getCounterpartyA().getShortName() );
        index1 = xmlMaker.indexOf( "57=" ) + 3;
        assertEquals( xmlMaker.substring( index1, xmlMaker.indexOf( '\u0001', index1 ) ), trd.getCounterpartyB().getShortName() );
    }

    /* not needed because TOF does not have any Legal Entity printed
    public void testTOFDownload() throws Exception
    {
    }*/
}
