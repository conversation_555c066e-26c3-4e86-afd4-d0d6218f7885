package com.integral.finance.trade.test;


import com.integral.SEF.SEFUtilC;
import com.integral.deposits.DepositsParam;
import com.integral.finance.counterparty.CounterpartyC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.dealing.FeatureFlags;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.financialEvent.FinancialEvent;
import com.integral.finance.financialEventBuilder.FinancialEventBuilder;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.finance.trade.TradeExecutionFlags;
import com.integral.finance.trade.TradeFactory;
import com.integral.finance.trade.TradeLeg;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.facade.TradeStateFacadeC;
import com.integral.is.ISCommonConstants;
import com.integral.persistence.Entity;
import com.integral.persistence.EntityFactory;
import com.integral.persistence.NamespaceGroupC;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.workflow.State;
import com.integral.workflow.StateC;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Timestamp;
import java.util.*;


/**
 * Unit tests for TradeC
 */
public class TradePTestC
        extends PTestCaseC
{
    static String name = "Trade Test";
    Vector users = null;
    static String marketSnapShot = null;


    public TradePTestC( String name )
    {
        super( name );
    }


    public void testLookup()
    {
        log( "lookupTest" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( Trade.class, new ExpressionBuilder().get( Entity.ObjectID ).lessThan( 100 ) );
            for ( int i = 0; i < objs.size(); i++ )
            {
                Trade trade = ( Trade ) objs.elementAt( i );
                printTrade( trade, i );
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            fail( "lookupTest", e );
        }
    }

    private String getSnapShot( int lenth )
    {
        StringBuilder builder = new StringBuilder( lenth );
        for ( int i = 0; i < lenth; i++ )
        {
            builder.append( "A" );
        }
        return builder.toString();
    }

    public void testInsert()
    {
        log( "insertTest" );
        try
        {
            Trade trade = new FXSingleLegC();
            printTrade( trade, 0 );
            //long now = System.currentTimeMillis();
            Timestamp now = new Timestamp( System.currentTimeMillis() );

            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.addReadOnlyClass( UserC.class );
            uow1.addReadOnlyClass( CounterpartyC.class );
            Trade trade1 = ( Trade ) uow1.registerObject( trade );
            trade1.setEtlTimestamp( now );
            long now2 = System.currentTimeMillis();
            trade1.setAcptSentByUser( new Timestamp( now2 + 31 ) );
            trade1.setAcptRecvdByApp( new Timestamp( now2 + 41 ) );
            trade1.setAcptSentByApp( new Timestamp( now2 + 51 ) );
            trade1.setAcptRecvdByAdpt( new Timestamp( now2 + 61 ) );
            trade1.setAcptSentByAdpt( new Timestamp( now2 + 71 ) );
            trade1.setRjctRecvdFrmPrvdr( new Timestamp( now2 + 81 ) );
            trade1.setVrfyRecvdFrmPrvdr( new Timestamp( now2 + 101 ) );
            trade1.setCnfrmByUser( new Timestamp( now2 + 111 ) );

            trade1.setRjctRecvdByApp( new Timestamp( now2 + 10 ) );
            trade1.setVrfyRecvdByApp( new Timestamp( now2 + 20 ) );
            trade1.setVrfySentByApp( new Timestamp( now2 + 30 ) );
            trade1.setRjctSentByApp( new Timestamp( now2 + 40 ) );
            trade1.setUserLatency( 67 );
            trade1.setNoOfAttemptsByClient( 89 );
            trade1.setClientCPUUsage( 99 );
            trade1.setClientMemoryUsage( 56 );
            trade1.setClientPollLatency( 44 );
            trade1.setJMSProxyWaitTime( new Timestamp( now2 + 80 ) );
            trade1.setOAOrderReceivedByAdaptor( new Timestamp( now2 + 50 ) );
            trade1.setOAOrderPublishedByAdaptor( new Timestamp( now2 + 60 ) );
            trade1.setOAOrderMatchedByAdaptor( new Timestamp( now2 + 70 ) );
            trade1.setBusinessExecutionDate( new Timestamp( now2 + 83 ) );

            trade1.setRateEffective( new Timestamp( now2 + 112 ) );
            trade1.setRateRecvdByAdpt( new Timestamp( now2 + 113 ) );
            trade1.setRateSentByAdpt( new Timestamp( now2 + 114 ) );
            trade1.setRateRecvdByApp( new Timestamp( now2 + 115 ) );
            trade1.setRateRecvdByUser( new Timestamp( now2 + 116 ) );
            trade1.setRateAcptByUser( new Timestamp( now2 + 117 ) );

            trade1.setQuoteCreatedByApp( new Timestamp( now2 + 10 ) );
            trade1.setRateSentByApp( new Timestamp( now2 + 20 ) );
            trade1.setRateRecvdByPrxy( new Timestamp( now2 + 30 ) );
            trade1.setRateQueriedByUser( new Timestamp( now2 + 40 ) );
            trade1.setRateSentByPrxy( new Timestamp( now2 + 50 ) );
            trade1.setRateDsplyByUser( new Timestamp( now2 + 60 ) );
            trade1.setNxtRateRecvdByApp( new Timestamp( now2 + 70 ) );
            trade1.setRateAggrtByApp( new Timestamp( now2 + 57 ) );
            trade1.setOrdRecvdByApp( new Timestamp( now2 + 58 ) );
            trade1.setOrdMtchdByApp( new Timestamp( now2 + 59 ) );

            trade1.setRFSQuoteStateChangeSnapshot( "TEST" );
            trade1.setPriceRegenerationKey( "PriceRegenTest" );
            trade1.setPriceRegenerationState( 78 );
            trade1.setTriggeredBy( ( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() ).getOrganization() );
            trade1.setTriggerReachedAt( new Timestamp( now2 + 89 ) );
            trade1.setCptyReqIdExtSys( "1" );
            trade1.setCptyTradeIdExtSys( "2" );
            trade1.setISExtSys( "3" );
            trade1.setCoveredTradeExtSys( "4" );
            trade1.setDirectFXTraderExtSys( "5" );
            trade1.setFIX43ExtSys( "6" );
            trade1.setRequestIdExtSys( "7" );
            trade1.setTraderESPExtSys( "8" );
            trade1.setProviderExtSys( "9" );
            trade1.setCSDKExtSys( "0" );
            trade1.setMaskedLP( "MaskedLP" );
            trade1.setMakerMarketSnapshot( getSnapShot( 4100 ) );
            LegalEntity cpty = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntity.class, "FI1-le1" );
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );

            trade1.setCoveredTradeCounterparty( cpty );
            trade1.setCoveredTradeUser( user );
            trade1.setUSI("USITEST");
            trade1.setSDRAckId("SDRACKID");
			trade1.setClearingHouseAckId("CHAckId");
            trade1.setSDRTradeStatus("SDRState");
			trade1.setClearingHouseTradeStatus("CHState");
			trade1.setCreditLimitHubId("CRLMTHubIdTest");
			trade1.setCreditLimitHubStatus("CRLMTHubStatus");
            trade1.setSpaces(true);
            trade1.setUSILinkId( "TESTUSILINKID" );
            trade1.setUTILinkId("UTILINKID_TEST");
            trade1.setSyntheticCross(true);
            trade1.setVehicleCCYAmount(*********.356544);
            trade1.setVehicleCCY(1234);
            TradeC backRef = new TradeC();
			trade1.setSyntheticCrossBackRef(backRef);
            backRef.setSyntheticCrossComponent(ISCommonConstants.SYNTHETICCROSSCOMPONENT_PRIMARY);
            trade1.setMandatorilyClearable(false);
            // Add entry user and entry user account for manual trade
            User entryUser = ( User ) new ReadNamedEntityC().execute( User.class, "testEntryUser" );
            trade1.setEntryUser(entryUser);
            trade1.setEntryUserLE("testEntryUserAccount");
            trade1.setWorkflowCode(4);
            trade1.setWorkflowCodeArgument("TestArgument");
            trade1.setAlgoType("TWAP");
            trade1.setPricingType("Auto");
            trade1.setFeatureFlags(trade1.getFeatureFlags() | FeatureFlags.FULLAMOUNT);
            DepositsParam depositsParam = new DepositsParam();
            depositsParam.setNumberOfDays(1);
            depositsParam.setAnnualInterestRate(5.5D);
            depositsParam.setActualInterestRate(3.1);
            depositsParam.setInterest(37.3);
            trade1.setDepositsParam(depositsParam);
            printTrade( trade1, 1 );
            uow1.commit();

            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            Trade trade2 = ( Trade ) uow2.registerObject( trade );
            DatePeriod dp = DateTimeFactory.newDatePeriod ("3D" );
            trade2.setSettlementDateTerm( dp );
            printTrade( trade2, 2 );

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "objectID" ).equal( trade.getObjectID() );
            Trade sharedTrade = ( Trade ) getPersistenceSession().readObject( Trade.class, expr );
            assertEquals( sharedTrade.getEtlTimestamp(), now );
            
            assertEquals( sharedTrade.getCoveredTradeCounterparty(), cpty );
            assertEquals( sharedTrade.getCoveredTradeUser(), user );
            assertEquals( sharedTrade.getAcptSentByUser().getTime(), now2 + 31 );
            assertEquals( sharedTrade.getAcptRecvdByApp().getTime(), now2 + 41 );
            assertEquals( sharedTrade.getAcptSentByApp().getTime(), now2 + 51 );
            assertEquals( sharedTrade.getAcptRecvdByAdpt().getTime(), now2 + 61 );
            assertEquals( sharedTrade.getAcptSentByAdpt().getTime(), now2 + 71 );
            assertEquals( sharedTrade.getRjctRecvdFrmPrvdr().getTime(), now2 + 81 );
            assertEquals( sharedTrade.getVrfyRecvdFrmPrvdr().getTime(), now2 + 101 );
            assertEquals( sharedTrade.getCnfrmByUser().getTime(), now2 + 111 );
            assertEquals( sharedTrade.getTriggeredBy(), ( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() ).getOrganization() );
            assertEquals( sharedTrade.getTriggerReachedAt().getTime(), now2 + 89 );


            assertEquals( sharedTrade.getUserLatency(), 67 );
            assertEquals( sharedTrade.getNoOfAttemptsByClient(), 89 );
            assertEquals( sharedTrade.getClientCPUUsage(), 99 );
            assertEquals( sharedTrade.getClientMemoryUsage(), 56 );
            assertEquals( sharedTrade.getClientPollLatency(), 44 );

            assertEquals( sharedTrade.getRjctRecvdByApp().getTime(), now2 + 10 );
            assertEquals( sharedTrade.getVrfyRecvdByApp().getTime(), now2 + 20 );
            assertEquals( sharedTrade.getVrfySentByApp().getTime(), now2 + 30 );
            assertEquals( sharedTrade.getRjctSentByApp().getTime(), now2 + 40 );
            assertEquals( sharedTrade.getJMSProxyWaitTime().getTime(), now2 + 80 );
            assertEquals( sharedTrade.getOAOrderReceivedByAdaptor().getTime(), now2 + 50 );
            assertEquals( sharedTrade.getOAOrderPublishedByAdaptor().getTime(), now2 + 60 );
            assertEquals( sharedTrade.getOAOrderMatchedByAdaptor().getTime(), now2 + 70 );
            assertEquals( sharedTrade.getBusinessExecutionDate().getTime(), now2 + 83 );


            assertEquals( sharedTrade.getRateEffective().getTime(), now2 + 112 );
            assertEquals( sharedTrade.getRateRecvdByAdpt().getTime(), now2 + 113 );
            assertEquals( sharedTrade.getRateSentByAdpt().getTime(), now2 + 114 );
            assertEquals( sharedTrade.getRateRecvdByApp().getTime(), now2 + 115 );
            assertEquals( sharedTrade.getRateRecvdByUser().getTime(), now2 + 116 );
            assertEquals( sharedTrade.getRateAcptByUser().getTime(), now2 + 117 );


            assertEquals( sharedTrade.getQuoteCreatedByApp().getTime(), now2 + 10 );
            assertEquals( sharedTrade.getRateSentByApp().getTime(), now2 + 20 );
            assertEquals( sharedTrade.getRateRecvdByPrxy().getTime(), now2 + 30 );
            assertEquals( sharedTrade.getRateQueriedByUser().getTime(), now2 + 40 );
            assertEquals( sharedTrade.getRateSentByPrxy().getTime(), now2 + 50 );
            assertEquals( sharedTrade.getRateDsplyByUser().getTime(), now2 + 60 );
            assertEquals( sharedTrade.getNxtRateRecvdByApp().getTime(), now2 + 70 );

            assertEquals( sharedTrade.getRateAggrtByApp().getTime(), now2 + 57 );
            assertEquals( sharedTrade.getOrdRecvdByApp().getTime(), now2 + 58 );
            assertEquals( sharedTrade.getOrdMtchdByApp().getTime(), now2 + 59 );

            assertEquals( sharedTrade.getRFSQuoteStateChangeSnapshot(), "TEST" );
            assertEquals( sharedTrade.getPriceRegenerationKey(), "PriceRegenTest" );
            assertEquals( sharedTrade.getPriceRegenerationState(), 78 );
            assertEquals( sharedTrade.getCptyReqIdExtSys(), "1" );
            assertEquals( sharedTrade.getCptyTradeIdExtSys(), "2" );
            assertEquals( sharedTrade.getISExtSys(), "3" );
            assertEquals( sharedTrade.getCoveredTradeExtSys(), "4" );
            assertEquals( sharedTrade.getDirectFXTraderExtSys(), "5" );
            assertEquals( sharedTrade.getFIX43ExtSys(), "6" );
            assertEquals( sharedTrade.getRequestIdExtSys(), "7" );
            assertEquals( sharedTrade.getTraderESPExtSys(), "8" );
            assertEquals( sharedTrade.getProviderExtSys(), "9" );
            assertEquals( sharedTrade.getCSDKExtSys(), "0" );
            assertEquals( sharedTrade.getMaskedLP(), "MaskedLP" );
            assertEquals( sharedTrade.getMakerMarketSnapshot(), getSnapShot( 4000 ) );
            assertEquals( sharedTrade.getUSI(), "USITEST" );
            assertEquals( sharedTrade.getSDRAckId(), "SDRACKID" );
            assertEquals( sharedTrade.getSDRTradeStatus(), "SDRState");
            assertEquals( sharedTrade.getClearingHouseAckId(), "CHAckId" );
            assertEquals( sharedTrade.getClearingHouseTradeStatus(), "CHState");
            assertEquals( sharedTrade.getCreditLimitHubId(), "CRLMTHubIdTest" );
            assertEquals( sharedTrade.getCreditLimitHubStatus(), "CRLMTHubStatus");
            assertTrue( sharedTrade.isSpaces());
            assertEquals( sharedTrade.getUSILinkId(), "TESTUSILINKID" );
            assertEquals( sharedTrade.getUTILinkId(), "UTILINKID_TEST" );
            assertEquals( sharedTrade.getEntryUser(), entryUser );
            assertEquals( sharedTrade.getEntryUserLE(), "testEntryUserAccount" );
            assertEquals(sharedTrade.isSyntheticCross(), true);
            assertEquals(sharedTrade.getVehicleCCY(), 1234);
            assertEquals(sharedTrade.getVehicleCCYAmount(), *********.356544D);
            assertEquals(backRef.getObjectID(), sharedTrade.getSyntheticCrossBackRef().getObjectID());
            assertEquals(backRef.getSyntheticCrossComponent(),ISCommonConstants.SYNTHETICCROSSCOMPONENT_PRIMARY);
            assertFalse(sharedTrade.isMandatorilyClearable());
            assertEquals(sharedTrade.getWorkflowCode().intValue(), 4);
            assertEquals(sharedTrade.getWorkflowCodeArgument(), "TestArgument");
            assertEquals(sharedTrade.getAlgoType(), "TWAP");
            assertEquals(sharedTrade.getPricingType(), "Auto");
            assertTrue((sharedTrade.getFeatureFlags() & FeatureFlags.FULLAMOUNT) == FeatureFlags.FULLAMOUNT);
            DepositsParam dep2 = sharedTrade.getDepositsParam();
            assertNotNull ( dep2.getNumberOfDays ()  );
            int days = dep2.getNumberOfDays ();
            assertEquals ( 1, days );
            assertEquals(dep2.getActualInterestRate(), 3.1);
            assertEquals(dep2.getAnnualInterestRate(), 5.5);
            assertEquals(dep2.getInterest(), 37.3);
            log( "shared   date period: " + sharedTrade.getSettlementDateTerm() );
            log( "original date period: " + trade.getSettlementDateTerm() );
            log( "new      date period: " + trade2.getSettlementDateTerm() );

            uow2.commit();

            log( "insertTest" );
        }
        catch ( Exception e )
        {
            fail( "insertTest", e );
        }
    }

    public void testBuildEvents()
    {
        log( "buildEvents" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "objectID" ).equal( 2001 );
            Vector objs = getPersistenceSession().readAllObjects( Trade.class, expr );
            for ( int i = 0; i < objs.size(); i++ )
            {
                Trade trade = ( Trade ) objs.elementAt( i );

                log( "\tBEFORE BUILDING EVENTS" );
                printTrade( trade, i );

                // patch object model to get financial event building to work
                //
                log( "\tpatching object model to get financial event building to work..." );

                Iterator legs = trade.getTradeLegs().iterator();
                while ( legs.hasNext() )
                {
                    TradeLeg leg = ( TradeLeg ) legs.next();
                    log( "\t setting event builder on leg " + leg );
                }

                // building events
                log( "\tBuilding events now..." );
                trade.buildFinancialEvents();
                log( "\tFinished building events" );

                log( "\tAFTER BUILDING EVENTS" );
                printTrade( trade, i );
            }
            log( "buildEvents" );
        }
        catch ( Exception e )
        {
            fail( "buildEvents", e );
        }
    }

    /**
     * Tests behaviour of cptyTrades on trade when we clone it.
     */
    public void testCloneTradeOnCptyTrades()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow1 = session.acquireUnitOfWork();
            Vector<Class> vect = new Vector<Class>( 1 );
            vect.addElement( UserC.class );
            vect.addElement( com.integral.workflow.StateC.class );
            vect.addElement( NamespaceGroupC.class );
            uow1.addReadOnlyClasses( vect );

            Trade trade1 = ( Trade ) uow1.registerObject( FXFactory.newFXSingleLeg() );
            CptyTrade cptyTrade1 = ( CptyTrade ) uow1.registerObject( TradeFactory.newCptyTrade() );
            CptyTrade cptyTrade2 = ( CptyTrade ) uow1.registerObject( TradeFactory.newCptyTrade() );
            Collection<CptyTrade> cptyTrades = new ArrayList<CptyTrade>( 2 );
            cptyTrades.add( cptyTrade1 );
            cptyTrades.add( cptyTrade2 );
            trade1.setCptyTrades( cptyTrades );
            uow1.commit();

            uow1 = session.acquireUnitOfWork();
            trade1 = ( Trade ) session.refreshObject( trade1 );
            log.info( "Trade1 id is " + trade1.getObjectID() );
            for ( CptyTrade cptyT : trade1.getCptyTrades() )
            {
                log.info( String.valueOf( cptyT.getObjectID() ) );
            }
            int sumOfCptyTradesId = 0;
            for ( CptyTrade cptyT : trade1.getCptyTrades() )
            {
                sumOfCptyTradesId += cptyT.getObjectID();
            }
            trade1.setCptyTrades( null );
            Trade clonedTrade = ( Trade ) EntityFactory.newEntity( session, trade1, false );
            clonedTrade = ( Trade ) uow1.registerObject( clonedTrade );


            for ( CptyTrade cptyT : clonedTrade.getCptyTrades() )
            {
                log.info( "Cloned Trade " + cptyT.getObjectID() + ", before setting to null; so should be same as original trade" );
            }
            clonedTrade.setCptyTrades( new ArrayList<CptyTrade>() );
            CptyTrade cptyTrade3 = ( CptyTrade ) uow1.registerObject( TradeFactory.newCptyTrade() );
            CptyTrade cptyTrade4 = ( CptyTrade ) uow1.registerObject( TradeFactory.newCptyTrade() );

            Collection<CptyTrade> cptyTrades2 = new ArrayList<CptyTrade>( 2 );
            cptyTrades2.add( cptyTrade3 );
            cptyTrades2.add( cptyTrade4 );

            clonedTrade.setCptyTrades( cptyTrades2 );
            uow1.commit();
            trade1 = ( Trade ) session.refreshObject( trade1 );
            clonedTrade = ( Trade ) session.refreshObject( clonedTrade );
            log.info( "Trade1 id is " + trade1.getObjectID() );
            int sumOfCptyTradesIdFinal = 0;
            for ( CptyTrade cptyT : trade1.getCptyTrades() )
            {
                log.info( "Orig Trade " + cptyT.getObjectID() );
                sumOfCptyTradesIdFinal += cptyT.getObjectID();
            }

            log.info( "clonedTrade id is " + clonedTrade.getObjectID() );
            for ( CptyTrade cptyT : clonedTrade.getCptyTrades() )
            {
                log.info( "Cloned Trade " + cptyT.getObjectID() );
            }
            assertEquals( "Original Trade with id " + trade1.getObjectID() + " should have 2 cpty trades", trade1.getCptyTrades().size(), 2 );
            assertEquals( "Cloned Trade with id " + clonedTrade.getObjectID() + " should have 2 cpty trades", clonedTrade.getCptyTrades().size(), 2 );
            assertEquals( "Original trade cpty trade ids sum should be same ", sumOfCptyTradesId, sumOfCptyTradesIdFinal );

        }
        catch ( Exception e )
        {
            fail( "insertTest", e );
        }
    }


    private void printTrade( Trade trade, int i )
    {
        log( "trade #" + i );
        log( "\t objectID = " + trade.getObjectID() );

        // get financial event builder
        FinancialEventBuilder builder = trade.getFinancialEventBuilder();
        log( "\t trade builder " + builder );

        // get financial event builder
        DatePeriod dp = trade.getSettlementDateTerm();
        log( "\t settlement date term " + dp );

        Iterator events = trade.getFinancialEvents().iterator();
        int e = 0;
        while ( events.hasNext() )
        {
            FinancialEvent event = ( FinancialEvent ) events.next();
            log( "\t trade event #" + e + ": " + event );
            e++;
        }

        Iterator legs = trade.getTradeLegs().iterator();
        int l = 0;
        while ( legs.hasNext() )
        {
            TradeLeg leg = ( TradeLeg ) legs.next();
            log( "\t trade leg #" + l + ": " + leg );

            Iterator leg_events = leg.getFinancialEvents().iterator();
            int le = 0;
            while ( leg_events.hasNext() )
            {
                FinancialEvent leg_event = ( FinancialEvent ) leg_events.next();
                log( "\t\t trade leg event #" + e + ": " + leg_event + ",le=" + le );
                le++;
            }

            l++;
        }
    }

    /*
    * This test case checks that confirmed/verified date gets set when setVerified or setConfirmed are called.
    */
    public void testConfirmExecutionDate()
    {
        IdcTransaction tx = null;
        try
        {
            //TODO: it would be better to read the last 10 records
            Vector objs = getPersistenceSession().readAllObjects( Trade.class, new ExpressionBuilder().get(Entity.ObjectID).lessThan(10) );
            Enumeration enum1 = objs.elements();
            tx = IdcSessionManager.getInstance().newTransaction();
            Vector vect = new Vector( 2 );
            vect.addElement( UserC.class );
            vect.addElement( com.integral.workflow.StateC.class );
            tx.getUOW().addReadOnlyClasses( vect );


            while ( enum1.hasMoreElements() )
            {
                Trade trd = ( Trade ) enum1.nextElement();
                trd = ( Trade ) trd.getRegisteredObject();
                TradeStateFacade tradeFacade = new TradeStateFacadeC();
                tradeFacade.setTrade( trd );
                // check that date changes only for verified and not for confirmed.
                //tradeFacade.setConfirmed();
                tradeFacade.setVerified();
            }
            tx.commit();
        }
        catch ( Exception e )
        {
            fail( "Exception in testConfirmExecutionDate : ", e );
        }
        finally
        {
            if ( tx != null )
            {
                tx.release();
                IdcSessionManager.getInstance().setTransaction( null );
            }
        }
    }

    public void testMakerRequest()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            Trade testTrade = new TradeC();
            Trade registeredTrade = ( Trade ) uow.registerObject( testTrade );
            Request makerRequest = new RequestC();
            Request registeredMakerRequest = ( Request ) uow.registerObject( makerRequest );
            registeredMakerRequest.setTransactionID( "Test" + System.nanoTime() );
            registeredTrade.setMakerRequest( registeredMakerRequest );
            uow.commit();

            testTrade.setMakerRequest( null );
            // refresh the trade and retrieve the maker request.
            testTrade = ( Trade ) session.refreshObject( testTrade );
            log( "testTrade=" + testTrade + ",testTrade.makerRequest=" + testTrade.getMakerRequest() );
            assertEquals( "maker request should not be null.", testTrade.getMakerRequest() != null, true );
        }
        catch ( Exception e )
        {
            fail( "Exception in testMakerRequest", e );
        }
    }

    public void testMakerOrder()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            Trade testTrade = new TradeC();
            Trade registeredTrade = ( Trade ) uow.registerObject( testTrade );
            String makerOrderId = String.valueOf( System.nanoTime() );
            registeredTrade.setMakerOrderId( makerOrderId );
            uow.commit();

            testTrade.setMakerOrderId( null );
            // refresh the trade and retrieve the maker order id string.
            testTrade = ( Trade ) session.refreshObject( testTrade );
            log( "testTrade=" + testTrade + ",testTrade.makerOrder=" + testTrade.getMakerOrderId() );
            assertEquals( "maker order should not be null.", testTrade.getMakerOrderId(), makerOrderId );
        }
        catch ( Exception e )
        {
            fail( "Exception in testMakerOrder :", e );
        }
    }

    public void testOriginatingCptyUser()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            Trade testTrade = new TradeC();
            Trade registeredTrade = ( Trade ) uow.registerObject( testTrade );
            assertNull( registeredTrade.getOriginatingCptyId() );
            assertNull( registeredTrade.getOriginatingUserId() );
            assertNull( registeredTrade.getOriginatingCounterparty() );
            assertNull( registeredTrade.getOriginatingUser() );
            LegalEntity cpty = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntity.class, "FI1-le1" );
            long origCptyId = cpty.getObjectID();
            registeredTrade.setOriginatingCptyId( origCptyId );
            User user = ( User ) new ReadNamedEntityC().execute( User.class, "fi1mm1" );
            long origUserId = user.getObjectID();
            registeredTrade.setOriginatingUserId( origUserId );
            uow.commit();

            testTrade.setOriginatingCptyId( null );
            testTrade.setOriginatingUserId( null );
            // refresh the trade and retrieve the maker request.
            testTrade = ( Trade ) session.refreshObject( testTrade );
            log( "testTrade=" + testTrade + ",testTrade.origCptyId=" + testTrade.getOriginatingCptyId() + ",testTrade.origCpty=" + testTrade.getOriginatingCounterparty() );
            assertEquals( "orig cpty id should not be null.", testTrade.getOriginatingCptyId().longValue(), origCptyId );
            assertEquals( "orig cpty should not be null.", testTrade.getOriginatingCounterparty(), cpty );

            log( "testTrade=" + testTrade + ",testTrade.origUserId=" + testTrade.getOriginatingUserId() + ",testTrade.origUser=" + testTrade.getOriginatingUser() );
            assertEquals( "orig user id should not be null.", testTrade.getOriginatingUserId().longValue(), origUserId );
            assertEquals( "orig user should not be null.", testTrade.getOriginatingUser(), user );
        }
        catch ( Exception e )
        {
            fail( "Exception in testOriginatingCptyUser : ", e );
        }
    }


    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( UserC.class );
            uow.addReadOnlyClass( OrganizationC.class );
            LegalEntity le1 = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "FI1-le1" );
            User user1 = ( User ) new ReadNamedEntityC().execute( UserC.class, "fi1mm1" );
            LegalEntity le2 = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "FI1-le2" );
            User user2 = ( User ) new ReadNamedEntityC().execute( UserC.class, "fi1mm2" );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "CITI" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "DB" );
            Trade testTrade = new TradeC();
            assertEquals( testTrade.getExecutionFlags(), 0 );
            assertFalse( testTrade.isRiskPosition() );
            assertFalse( testTrade.isSEF() );
            assertFalse( testTrade.isManualTrade() );
            Trade registeredTrade = ( Trade ) uow.registerObject( testTrade );
            assertFalse( registeredTrade.isRiskPosition() );
            assertEquals( registeredTrade.getExecutionFlags(), 0 );
            assertNull( registeredTrade.getStream() );
            assertNull( registeredTrade.getVirtualServer() );
            assertFalse( registeredTrade.isSEF() );
            assertFalse( registeredTrade.isManualTrade() );
            registeredTrade.setStream( "TestStream" );
            registeredTrade.setVirtualServer( "TestServer" );
            String makerMktSnapshot = "sdfs";
            registeredTrade.setMakerMarketSnapshot( makerMktSnapshot );
            State verifiedState = ( State ) new ReadNamedEntityC().execute( StateC.class, "TSVERIFIED" );
            registeredTrade.setState( verifiedState );
            registeredTrade.setExecutionFlags( ( short ) TradeExecutionFlags.FLASH );
            registeredTrade.setSalesDealerCounterparty( le1 );
            registeredTrade.setSalesDealerUser( user1 );
            registeredTrade.setMakerSalesDealerCounterparty( le2 );
            registeredTrade.setMakerSalesDealerUser( user2 );
            registeredTrade.setRiskPosition( true );
            registeredTrade.setCoverTradeTxIds( "FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135" );
            String upi = "TestUPI";
            registeredTrade.setUPI( upi );
            registeredTrade.setClientTag( "testClientTag" );
            registeredTrade.setCounterpartyALEI( "testCptyALEI" );
            registeredTrade.setCounterpartyBLEI( "testCptyBLEI" );
            registeredTrade.setSEF( true );
            registeredTrade.setSEFOrg( org1 );
            registeredTrade.setClearingHouse( org2 );
            String coverMakerRefIds = "TestCoverMakerRefIds";
            registeredTrade.setCoverTrdMkrIds( coverMakerRefIds );
            registeredTrade.setReportingParty( le1 );
            registeredTrade.setSubTradeType ( "TestSubTradeType" );

            String contractId = "EUR_USD_130611";
            registeredTrade.setContractId( contractId );
            registeredTrade.setAllocation(true);
            registeredTrade.setExternalRequestId("externalReqId");
            registeredTrade.setManualTrade( true );
            registeredTrade.setAcceptedPriceSkewed(true);
            registeredTrade.setSkewSpread(5.0);
            registeredTrade.setSyntheticCrossComponent( ISCommonConstants.SYNTHETICCROSSCOMPONENT_PRIMARY );
            registeredTrade.setAlgoType("TWAP");
            registeredTrade.setPricingType("Auto");
            registeredTrade.setFeatureFlags(registeredTrade.getFeatureFlags() | FeatureFlags.FULLAMOUNT);
            SEFUtilC.setRegulatoryField(registeredTrade, "NewKey", "NewValue");

            registeredTrade.addCustomParameter ( "custodian", "testCustodian" );
            registeredTrade.addCustomParameter ( "purpose", "testPurpose" );
            registeredTrade.addCustomParameter ( "subPurpose", "testSubPurpose" );
            registeredTrade.removeCustomParameter ( "custodian" );
            String customParams = registeredTrade.getCustomParameters ();
            DepositsParam depositsParam = new DepositsParam();
            depositsParam.setInterest(37.3);
            depositsParam.setNumberOfDays(1);
            depositsParam.setActualInterestRate(3.1);
            depositsParam.setAnnualInterestRate(5.5);
            registeredTrade.setDepositsParam(depositsParam);

            uow.commit();

            testTrade.setVirtualServer( null );
            testTrade.setStream( null );
            testTrade.setState( null );
            testTrade.setMakerMarketSnapshot( null );
            testTrade.setSalesDealerCounterparty( null );
            testTrade.setSalesDealerUser( null );
            testTrade.setMakerSalesDealerUser( null );
            testTrade.setMakerSalesDealerUser( null );
            
            // refresh the trade and retrieve the maker request.
            testTrade = ( Trade ) session.refreshObject( testTrade );
            log( "testTrade=" + testTrade + ",testTrade.stream=" + testTrade.getStream() + ",testTrade.virtualServer=" + testTrade.getVirtualServer() );
            assertEquals( "Stream should not be null.", testTrade.getStream(), "TestStream" );
            assertEquals( "Virtual Server should not be null.", testTrade.getVirtualServer(), "TestServer" );
            assertEquals( "State should be same set", testTrade.getState(), verifiedState );
            assertEquals( "Maker market snapshot should be set as " + testTrade.getMakerMarketSnapshot(), testTrade.getMakerMarketSnapshot(), makerMktSnapshot );
            assertEquals( "Execution flags set as as " + testTrade.getExecutionFlags(), testTrade.getExecutionFlags(), TradeExecutionFlags.FLASH );
            assertEquals( "SD cpty set as " + testTrade.getSalesDealerCounterparty(), testTrade.getSalesDealerCounterparty(), le1 );
            assertEquals( "SD user set as " + testTrade.getSalesDealerUser(), testTrade.getSalesDealerUser(), user1 );
            assertEquals( "Maker SD cpty set as " + testTrade.getMakerSalesDealerCounterparty(), testTrade.getMakerSalesDealerCounterparty(), le2 );
            assertEquals( "Maker SD user set as " + testTrade.getMakerSalesDealerUser(), testTrade.getMakerSalesDealerUser(), user2 );
            assertTrue( testTrade.isRiskPosition() );
            assertEquals( testTrade.getCoverTradeTxIds(), "FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135" );
            assertEquals( testTrade.getUPI(), upi );
            assertEquals( testTrade.getClientTag(), "testClientTag" );
            assertEquals( "testCptyALEI", testTrade.getCounterpartyALEI() );
            assertEquals( "testCptyBLEI", testTrade.getCounterpartyBLEI() );
            assertTrue( testTrade.isSEF() );
            assertTrue( org1.isSameAs( testTrade.getSEFOrg() ) );
            assertTrue( org2.isSameAs( testTrade.getClearingHouse() ) );
            assertEquals( testTrade.getCoverTrdMkrIds(), coverMakerRefIds );
            assertTrue( le1.isSameAs( testTrade.getReportingParty() ) );
            assertEquals( contractId, testTrade.getContractId() );
			assertTrue(testTrade.isAllocation());
			assertEquals("externalReqId", registeredTrade.getExternalRequestId());
            assertTrue( testTrade.isManualTrade() );
            assertTrue( testTrade.isAcceptedPriceSkewed() );
            assertEquals("Skew spread incorrect", 5.0, testTrade.getSkewSpread(), 0.1);
            assertEquals(testTrade.isSyntheticCross(), false);
            assertEquals(testTrade.getSyntheticCrossComponent(), ISCommonConstants.SYNTHETICCROSSCOMPONENT_PRIMARY);
            assertEquals(testTrade.getVehicleCCY(), 0);
            assertEquals(testTrade.getVehicleCCYAmount(), 0.0D);
            assertEquals(testTrade.getSyntheticCrossBackRef(), null);
            assertEquals(SEFUtilC.getRegulatoryField(testTrade, "NewKey" ),"NewValue");
            assertEquals(testTrade.getAlgoType(), "TWAP");
            assertEquals(testTrade.getPricingType(), "Auto");
            assertTrue((testTrade.getFeatureFlags() & FeatureFlags.FULLAMOUNT) == FeatureFlags.FULLAMOUNT);
            assertEquals ( "subTradeType", "TestSubTradeType", testTrade.getSubTradeType ()  );
            assertEquals ( customParams, testTrade.getCustomParameters () );
            Map<String, String> customParamsMap = testTrade.getCustomParametersMap ();
            assertNotNull ( customParamsMap );
            assertFalse( customParamsMap.isEmpty () );
            assertEquals ( 2, customParamsMap.size () );
            assertEquals ( "testPurpose", customParamsMap.get ( "purpose" ) );
            assertEquals ( "testSubPurpose", customParamsMap.get ( "subPurpose" ) );
            assertNull ( customParamsMap.get ( "custodian" ) );
            DepositsParam dep = testTrade.getDepositsParam();
            assertEquals (dep.getInterest(), 37.3 );
            assertEquals ( dep.getActualInterestRate(), 3.1 );
            assertEquals ( dep.getAnnualInterestRate(), 5.5 );
            assertNotNull ( dep.getNumberOfDays () );
            int days = dep.getNumberOfDays ();
            assertEquals ( 1, days );
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence : ", e );
        }
    }

    public void testFieldsPersistence1()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( UserC.class );
            LegalEntity le1 = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "FI1-le1" );
            User user1 = ( User ) new ReadNamedEntityC().execute( UserC.class, "fi1mm1" );
            LegalEntity le2 = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "FI1-le2" );
            User user2 = ( User ) new ReadNamedEntityC().execute( UserC.class, "fi1mm2" );
            Trade testTrade = new TradeC();
            assertEquals( testTrade.getExecutionFlags(), 0 );
            assertFalse( testTrade.isRiskPosition() );
            Trade registeredTrade = ( Trade ) uow.registerObject( testTrade );
            assertFalse( registeredTrade.isRiskPosition() );
            assertEquals( registeredTrade.getExecutionFlags(), 0 );
            assertNull( registeredTrade.getStream() );
            assertNull( registeredTrade.getVirtualServer() );
            registeredTrade.setStream( "TestStream" );
            registeredTrade.setVirtualServer( "TestServer" );
            String makerMktSnapshot = "sdfs";
            registeredTrade.setMakerMarketSnapshot( makerMktSnapshot );
            State verifiedState = ( State ) new ReadNamedEntityC().execute( StateC.class, "TSVERIFIED" );
            registeredTrade.setState( verifiedState );
            registeredTrade.setExecutionFlags( ( short ) TradeExecutionFlags.FLASH );
            registeredTrade.setSalesDealerCounterparty( le1 );
            registeredTrade.setSalesDealerUser( user1 );
            registeredTrade.setMakerSalesDealerCounterparty( le2 );
            registeredTrade.setMakerSalesDealerUser( user2 );
            registeredTrade.setRiskPosition( true );
            registeredTrade.setCoverTradeTxIds( "FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135-FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135-FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135" );
            registeredTrade.setSymbol( "EUR/USD" );
            registeredTrade.setSyntheticCross( true );
            registeredTrade.setVehicleCCY( 1234 );
            registeredTrade.setVehicleCCYAmount(123456789.1234567);

            Trade backRef = new TradeC();
            Trade registeredBackRef = ( Trade ) uow.registerObject( backRef );
            registeredTrade.setSyntheticCrossBackRef(registeredBackRef);

            uow.commit();

            testTrade.setVirtualServer( null );
            testTrade.setStream( null );
            testTrade.setState( null );
            testTrade.setMakerMarketSnapshot( null );
            testTrade.setSalesDealerCounterparty( null );
            testTrade.setSalesDealerUser( null );
            testTrade.setMakerSalesDealerUser( null );
            testTrade.setMakerSalesDealerUser( null );

            // refresh the trade and retrieve the maker request.
            testTrade = ( Trade ) session.refreshObject( testTrade );
            log( "testTrade=" + testTrade + ",testTrade.stream=" + testTrade.getStream() + ",testTrade.virtualServer=" + testTrade.getVirtualServer() );
            assertEquals( "Stream should not be null.", testTrade.getStream(), "TestStream" );
            assertEquals( "Virtual Server should not be null.", testTrade.getVirtualServer(), "TestServer" );
            assertEquals( "State should be same set", testTrade.getState(), verifiedState );
            assertEquals( "Maker market snapshot should be set as " + testTrade.getMakerMarketSnapshot(), testTrade.getMakerMarketSnapshot(), makerMktSnapshot );
            assertEquals( "Execution flags set as as " + testTrade.getExecutionFlags(), testTrade.getExecutionFlags(), TradeExecutionFlags.FLASH );
            assertEquals( "SD cpty set as " + testTrade.getSalesDealerCounterparty(), testTrade.getSalesDealerCounterparty(), le1 );
            assertEquals( "SD user set as " + testTrade.getSalesDealerUser(), testTrade.getSalesDealerUser(), user1 );
            assertEquals( "Maker SD cpty set as " + testTrade.getMakerSalesDealerCounterparty(), testTrade.getMakerSalesDealerCounterparty(), le2 );
            assertEquals( "Maker SD user set as " + testTrade.getMakerSalesDealerUser(), testTrade.getMakerSalesDealerUser(), user2 );
            assertTrue( testTrade.isRiskPosition() );
            assertEquals( testTrade.getCoverTradeTxIds(), "FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135-FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139-FXI140690162-FXI140690128-FXI140690130-FXI140690170-FXI140690111-FXI140690105-FXI140690124-FXI140690119-FXI140690015-FXI140690118-FXI140690125-FXI140690057-FXI140690180-FXI140690157-FXI140690051-FXI140690095-FXI140689917-FXI140689971-FXI140690138-FXI140690096-FXI140689920-FXI140690082-FXI140690185-FXI140690152-FXI140690104-FXI140690042-FXI140690131-FXI140690123-FXI140690069-FXI140690164-FXI140690083-FXI140690064-FXI140690094-FXI140690151-FXI140690098-FXI140690165-FXI140690097-FXI140690179-FXI140690112-FXI140690102-FXI140690137-FXI140690136-FXI140690122-FXI140690061-FXI140690089-FXI140690158-FXI140690063-FXI140690172-FXI140690144-FXI140689924-FXI140690121-FXI140690143-FXI140690088-FXI140690173-FXI140690167-FXI140690062-FXI140690049-FXI140690182-FXI140690117-FXI140690114-FXI140690060-FXI140690109-FXI140690066-FXI140690142-FXI140690034-FXI140690129-FXI140690171-FXI140690145-FXI140690181-FXI140690184-FXI140690081-FXI140690150-FXI140690067-FXI140690018-FXI140690068-FXI140690166-FXI140690091-FXI140690092-FXI140690108-FXI140690045-FXI140690146-FXI140690169-FXI140690135-FXI140690175-FXI140690147-FXI140690076-FXI140690090-FXI140689985-FXI140690093-FXI140690087-FXI140690115-FXI140690038-FXI140690176-FXI140690059-FXI140690161-FXI140690120-FXI140690183-FXI140690134-FXI140690086-FXI140690132-FXI140690154-FXI140690126-FXI140690058-FXI140690133-FXI140690140-FXI140690168-FXI140690149-FXI140689982-FXI140689918-FXI140690178-FXI140690099-FXI140690044-FXI140690113-FXI140690039-FXI140690085-FXI140690107-FXI140690163-FXI140690127-FXI140690037-FXI140690084-FXI140690153-FXI140690148-FXI140690156-FXI140690141-FXI140689981-FXI140689919-FXI140690041-FXI140690106-FXI140690000-FXI140690139" );
            assertEquals( testTrade.getSymbol(), "EUR/USD" );
            assertEquals(testTrade.isSyntheticCross(), true);
            assertEquals(testTrade.getSyntheticCrossComponent(),ISCommonConstants.SYNTHETICCROSSCOMPONENT_CROSS);
            assertEquals(testTrade.getVehicleCCY(), 1234);
            assertEquals(testTrade.getVehicleCCYAmount(), 123456789.1234567);
            assertEquals(backRef.getObjectID(), testTrade.getSyntheticCrossBackRef().getObjectID());

        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence1 : ", e );
        }
    }

    public void testFieldsPersistence2()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( UserC.class );
            LegalEntity le1 = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "FI1-le1" );
            User user1 = ( User ) new ReadNamedEntityC().execute( UserC.class, "fi1mm1" );
            LegalEntity le2 = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "FI1-le2" );
            User user2 = ( User ) new ReadNamedEntityC().execute( UserC.class, "fi1mm2" );
            Trade testTrade = new TradeC();
            assertEquals( testTrade.getExecutionFlags(), 0 );
            assertFalse(testTrade.isRiskPosition());
            Trade registeredTrade = ( Trade ) uow.registerObject( testTrade );
            assertFalse(registeredTrade.isRiskPosition());
            assertEquals(registeredTrade.getExecutionFlags(), 0);
            assertNull(registeredTrade.getStream());
            assertNull(registeredTrade.getVirtualServer());
            registeredTrade.setStream("TestStream");
            registeredTrade.setVirtualServer("TestServer");
            String makerMktSnapshot = "sdfs";
            registeredTrade.setMakerMarketSnapshot(makerMktSnapshot);
            State verifiedState = ( State ) new ReadNamedEntityC().execute( StateC.class, "TSVERIFIED" );
            registeredTrade.setState(verifiedState);
            registeredTrade.setExecutionFlags((short) TradeExecutionFlags.FLASH);
            registeredTrade.setSalesDealerCounterparty(le1);
            registeredTrade.setSalesDealerUser(user1);
            registeredTrade.setMakerSalesDealerCounterparty(le2);
            registeredTrade.setMakerSalesDealerUser(user2);
            registeredTrade.setRiskPosition(true);
            registeredTrade.setCoverTradeTxIds("FXI140690175");
            registeredTrade.setMakerOrderUserChannel("BA/ESP");
            registeredTrade.setMakerOrderDealtCurrency("USD");
            String orderId = "OID " + System.nanoTime();
            registeredTrade.setOrderId(orderId);
            registeredTrade.setOriginatingPortfolioId("1234567890");
            registeredTrade.setPrimaryDealtCcyId(123456);
            registeredTrade.setSecondaryDealtCcyId(345678);
            registeredTrade.setMidSpotRate( 1.1 );
            registeredTrade.setMidBenchmarkRate1( 1.2 );
            registeredTrade.setMidBenchmarkRate2( 1.3 );
            registeredTrade.setMidBenchmarkFwdPoints( 0.0014 );
            registeredTrade.setCreditValuationAdjustment( 1.234 );
            registeredTrade.setClientRequestId( "1234567890" );
            registeredTrade.setPriceModificationCount( 3 );
            registeredTrade.setPnlCurrency("MXN");
            registeredTrade.setHomeCurrencySpotRate( 1.2345 );
            registeredTrade.setPnlAmount( 1234567.89 );
            registeredTrade.setBaseRate( 3.3456 );
            registeredTrade.setBaseSwapPoints( 0.01234 );
            registeredTrade.setParentTradeCreatedDateTime( new Timestamp( System.currentTimeMillis() ) );

            uow.commit();

            testTrade.setVirtualServer(null);
            testTrade.setStream(null);
            testTrade.setState(null);
            testTrade.setMakerMarketSnapshot(null);
            testTrade.setSalesDealerCounterparty(null);
            testTrade.setSalesDealerUser(null);
            testTrade.setMakerSalesDealerUser( null );
            testTrade.setMakerSalesDealerUser( null );

            // refresh the trade and retrieve the maker request.
            testTrade = ( Trade ) session.refreshObject( testTrade );
            log( "testTrade=" + testTrade + ",testTrade.stream=" + testTrade.getStream() + ",testTrade.virtualServer=" + testTrade.getVirtualServer() );
            assertEquals("Stream should not be null.", testTrade.getStream(), "TestStream");
            assertEquals( "Virtual Server should not be null.", testTrade.getVirtualServer(), "TestServer" );
            assertEquals( "State should be same set", testTrade.getState(), verifiedState );
            assertEquals( "Maker market snapshot should be set as " + testTrade.getMakerMarketSnapshot(), testTrade.getMakerMarketSnapshot(), makerMktSnapshot );
            assertEquals( "Execution flags set as as " + testTrade.getExecutionFlags(), testTrade.getExecutionFlags(), TradeExecutionFlags.FLASH );
            assertEquals( "SD cpty set as " + testTrade.getSalesDealerCounterparty(), testTrade.getSalesDealerCounterparty(), le1 );
            assertEquals( "SD user set as " + testTrade.getSalesDealerUser(), testTrade.getSalesDealerUser(), user1 );
            assertEquals( "Maker SD cpty set as " + testTrade.getMakerSalesDealerCounterparty(), testTrade.getMakerSalesDealerCounterparty(), le2 );
            assertEquals( "Maker SD user set as " + testTrade.getMakerSalesDealerUser(), testTrade.getMakerSalesDealerUser(), user2 );
            assertTrue(testTrade.isRiskPosition());
            assertEquals(testTrade.getCoverTradeTxIds(), "FXI140690175");
            assertEquals( "BA/ESP", testTrade.getMakerOrderUserChannel() );
            assertEquals( "USD", testTrade.getMakerOrderDealtCurrency() );
            assertEquals( orderId, testTrade.getOrderId() );
            assertEquals("Originating Portfolio ID failed to get persisted; id=" + testTrade.getObjectId(), "1234567890", testTrade.getOriginatingPortfolioId());
            assertEquals("Primary Dealt Ccy Id failed to persist", 123456, testTrade.getPrimaryDealtCcyId());
            assertEquals("Secondary Dealt Ccy Id failed to persisted", 345678, testTrade.getSecondaryDealtCcyId());

            assertEquals("Mid spot rate failed to persist", 1.1, testTrade.getMidSpotRate());
            assertEquals("Mid Benchmark rate1 failed to persist", 1.2, testTrade.getMidBenchmarkRate1());
            assertEquals("Mid Benchmark rate2 failed to persist", 1.3, testTrade.getMidBenchmarkRate2());
            assertEquals("Mid Benchmark FwdPoints failed to persist", 0.0014, testTrade.getMidBenchmarkFwdPoints());
            assertEquals("CreditValuationAdjustment failed to persist", 1.234, testTrade.getCreditValuationAdjustment());
            assertEquals("ClientRequestId failed to persist", "1234567890", testTrade.getClientRequestId());
            assertEquals("PriceModificationCount failed to persist", 3, testTrade.getPriceModificationCount());
            assertNotNull("ParentTradeCreatedDateTime failed to persist", testTrade.getParentTradeCreatedDateTime());
            assertEquals("HomeCurrency failed to persist", "MXN", testTrade.getPnlCurrency());
            assertEquals("HomeCurrencySpotRate failed to persist", 1.2345, testTrade.getHomeCurrencySpotRate());
            assertEquals("HomeCurrencyPnlAmt failed to persist", 1234567.89, testTrade.getPnlAmount());
            assertEquals("BaseRate failed to persist", 3.3456, testTrade.getBaseRate());
            assertEquals("BaseSwapPoints failed to persist", 0.01234, testTrade.getBaseSwapPoints());
        }
        catch ( Exception e )
        {
            fail( "Exception in testFieldsPersistence2 : ", e );
        }
    }

    public void testMiFIDFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( LegalEntityC.class );
            uow.addReadOnlyClass( UserC.class );
            Organization mtfOrg = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "INTEGRALMTF" );
            Trade testTrade = new TradeC();
            assertEquals( testTrade.getExecutionFlags(), 0 );
            assertFalse(testTrade.isRiskPosition());
            Trade registeredTrade = ( Trade ) uow.registerObject( testTrade );
            registeredTrade.setExecutionVenue ( mtfOrg );

            uow.commit();

            testTrade.setExecutionVenue(null);

            // refresh the trade and retrieve the maker request.
            testTrade = ( Trade ) session.refreshObject( testTrade );
            log( "testTrade=" + testTrade + ",testTrade.executionVenue=" + testTrade.getExecutionVenue () + ",testTrade.MTFVenueName=" + testTrade.getMTFVenueName () );
            assertEquals("executionVenue should not be null.", mtfOrg, testTrade.getExecutionVenue () );
            assertEquals( "MTFVenueName should not be null.", mtfOrg.getShortName (), testTrade.getMTFVenueName () );
        }
        catch ( Exception e )
        {
            fail( "Exception in testMiFIDFieldsPersistence : ", e );
        }
    }
}
