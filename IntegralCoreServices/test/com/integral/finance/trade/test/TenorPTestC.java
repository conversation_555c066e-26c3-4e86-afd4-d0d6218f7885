package com.integral.finance.trade.test;

import com.integral.exception.IdcIllegalArgumentException;
import com.integral.finance.config.FinanceMBean;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateBasisC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXSettlementDateRule;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TenorFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;

import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;

public class TenorPTestC
        extends PTestCaseC
{
    static String name = "Tenor Test";
    private final static String[] MONTHS = {"JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"};
    private static final String[] MONTH_TENORS = {"1M", "2M", "3M", "4M", "5M", "6M", "7M", "8M", "9M", "10M", "11M", "12M"};
    private static final String[] YEAR_TENORS = {"1Y", "2Y", "3Y", "4Y", "5Y"};
    private static final String[] SPECIAL_TENORS = {Tenor.Overnight, Tenor.SP, Tenor.SPOT, Tenor.SPOT_NEXT, Tenor.Today,
    Tenor.Today_0, Tenor.Tomorrow, Tenor.Tomorrow_0, Tenor.Tomorrow_1, Tenor.Tomorrow_2};

    public TenorPTestC( String name )
    {
        super( name );
    }

    public void testTenor0()
    {
        try
        {
            log( "Begin Tenor test0" );
            Tenor t = new Tenor( "IMM" );
            assertNotNull( t );
            log( "Finish Tenor test0" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor1()
    {
        try
        {
            log( "Begin Tenor test1" );
            Tenor t = new Tenor( "1IMM" );
            assertNotNull( t );
            log( "Finish Tenor test1" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor2()
    {
        try
        {
            log( "Begin Tenor test2" );
            Tenor t = new Tenor( "2IMM" );
            assertNotNull( t );
            log( "Finish Tenor test2" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor3()
    {
        try
        {
            log( "Begin Tenor test3" );
            Tenor t = new Tenor( "5IMM" );
            assertNotNull( t );
            log( "Finish Tenor test3" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor4()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test4" );
            t = new Tenor( "-1IMM" );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test4" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor5()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test5" );
            t = new Tenor( "0IMM" );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test5" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor6()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test6" );
            t = new Tenor( "XIMM" );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test6" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor7()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test7" );
            t = new Tenor( "IMMIMM" );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test7" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor8()
    {
        try
        {
            log( "Begin Tenor test8" );
            Tenor t = new Tenor( "SP" );
            assertNotNull( t );
            log( "Finish Tenor test8" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor9()
    {
        try
        {
            log( "Begin Tenor test9" );
            Tenor t = new Tenor( "ON" );
            assertNotNull( t );
            log( "Finish Tenor test9" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor10()
    {
        try
        {
            log( "Begin Tenor test10" );
            Tenor t = new Tenor( "TM" );
            assertNotNull( t );
            log( "Finish Tenor test10" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor11()
    {
        try
        {
            log( "Begin Tenor test11" );
            Tenor t = new Tenor( "SN" );
            assertNotNull( t );
            log( "Finish Tenor test11" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor12()
    {
        try
        {
            log( "Begin Tenor test12" );
            Tenor t1 = new Tenor( "ON" );
            Tenor t2 = new Tenor( "IMM" );

            t1.compareTo( t2 );
            log( "Finish Tenor test12" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "test Tenor comparator" );
        }
    }

    public void testTenorComparePreSpotTenors()
    {
        try
        {
            log( "Begin tenor pre spot tenors comparison test" );

            // compare -1 day and spot
            Tenor t1 = new Tenor( "-1D" );
            Tenor t2 = new Tenor( "SPOT" );
            int result1 = t1.compareTo( t2 );
            int result2 = t2.compareTo( t1 );
            log( "Compared -1 Day with Spot and the result=" + result1 );
            log( "Compared Spot with -1 day the result=" + result2 );
            assertEquals( "-1 day compared to Spot. Result should be -1", result1 == -1, true );
            assertEquals( "Spot compared to -1 day. Result should be 1", result2 == 1, true );

            Tenor t3 = new Tenor( "ON" );
            Tenor t4 = new Tenor( "TN" );
            int result3 = t3.compareTo( t4 );
            int result4 = t4.compareTo( t3 );
            log( "Compared ON with TN and the result=" + result3 );
            log( "Compared TN with ON the result=" + result4 );
            assertEquals( "ON compared to TN. Result should be -1", result3 == -1, true );
            assertEquals( "TN compared to ON. Result should be 1", result4 == 1, true );

            Tenor t5 = new Tenor( "SN" );
            Tenor t6 = new Tenor( "SPOT" );
            int result5 = t5.compareTo( t6 );
            int result6 = t6.compareTo( t5 );
            log( "Compared SN with SPOT and the result=" + result5 );
            log( "Compared SPOT with SN the result=" + result6 );
            assertEquals( "SN compared to SPOT. Result should be 1", result5 == 1, true );
            assertEquals( "SPOT compared to SN. Result should be -1", result6 == -1, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "test Tenor comparator" );
        }
    }


    public void testTenor13()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test13" );
            t = new Tenor( "M" );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test13" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor14()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test14" );
            t = new Tenor( "" );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test14" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor15()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test15" );
            t = new Tenor( "  " );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test15" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor16()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test16" );
            t = new Tenor( ( String ) null );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test16" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor17()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test17" );
            t = new Tenor( null );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test17" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor18()
    {
        Tenor t = null;
        try
        {
            log( "Begin Tenor test18" );
            t = new Tenor( "MM" );
            fail( "create Tenor using illegal value" );
            log( "Finish Tenor test18" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor19()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test19: value date for IMM" );
            t = new Tenor( "IMM" );
            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }
            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate nextIMM = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** nextIMM=" + nextIMM );
            log( "Finish Tenor test19" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor190()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test190: value date for 3SIMM" );
            t = new Tenor( "3SIMM" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }
            IdcDate nextIMM = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** 3SIMM=" + nextIMM );
            log( "Finish Tenor test190" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor20()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test20: value date for 2IMM" );
            t = new Tenor( "2IMM" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate nextIMM = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** next2IMM=" + nextIMM );
            log( "Finish Tenor test20" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor21()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test20: value date for 3IMM" );
            t = new Tenor( "3IMM" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate nextIMM = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** next3IMM=" + nextIMM );
            log( "Finish Tenor test21" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor22()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test22: value date for 4IMM" );
            t = new Tenor( "4IMM" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate nextIMM = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** next4IMM=" + nextIMM );
            log( "Finish Tenor test22" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor23()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test23: value date for 1M" );
            t = new Tenor( "1M" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate result = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** 1mFWDDate=" + result );
            log( "Finish Tenor test23" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor24()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test24: value date for 3M" );
            t = new Tenor( "3M" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate result = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** 3mFWDDate=" + result );
            log( "Finish Tenor test24" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor25()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test25: value date for 1y" );
            t = new Tenor( "1Y" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate result = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** 1yFWDDate=" + result );
            log( "Finish Tenor test25" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor26()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test26: value date for T5M" );
            t = new Tenor( "T5M" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate result = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** T5M FWDDate=" + result );
            log( "Finish Tenor test26" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor27()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test27: value date for 5M" );
            t = new Tenor( "5M" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate result = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** 5mFWDDate=" + result );
            log( "Finish Tenor test27" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor28()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test28: value date for S5M" );
            t = new Tenor( "S5M" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate result = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** S5M FWDDate=" + result );
            log( "Finish Tenor test28" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor29()
    {
        Tenor t = null;
        FXSettlementDateRule testRule = null;
        FXRateBasis testCcypair = null;

        try
        {
            log( "Begin Tenor test29: value date for 3MM/3MM" );
            t = new Tenor( "3MM/3MM" );

            Vector objs = getPersistenceSession().readAllObjects( FXSettlementDateRule.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXSettlementDateRule r = ( FXSettlementDateRule ) objs.elementAt( i );
                if ( ( r.getShortName() != null ) && ( r.getShortName().equals( "FWD" ) ) )
                {
                    testRule = r;
                    break;
                }
            }

            objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                {
                    testCcypair = b;
                    break;
                }
            }

            IdcDate result = testRule.getValueDate( testCcypair, DateTimeFactory.timeNow(), t );

            log( "*** today=" + DateTimeFactory.timeNow() );
            log( "*** 3MM/3MM FWDDate=" + result );
            log( "Finish Tenor test29" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTenor30()
    {
        try
        {
            log( "Begin Tenor test30" );
            Tenor t = new Tenor( "IMM1" );
            assertNotNull( t );
            log( "Finish Tenor test 30" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor31()
    {
        try
        {
            log( "Begin Tenor test31" );
            Tenor t = new Tenor( "IMM2" );
            assertNotNull( t );
            log( "Finish Tenor test 31" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "create Tenor" );
        }
    }

    public void testTenor32()
    {
        Tenor t = null;
        FXRateBasis testCcypair = null;
        String[] MONTHS = {"JAN", "feb", "MAR", "apr", "MAY", "jun", "JUL", "aug", "SEP", "oct", "NOV", "dec"};
        try
        {
            log( "Begin Tenor test32: value date for month end tenors" );

            for ( String month : MONTHS )
            {
                t = new Tenor( month );
                Vector objs = getPersistenceSession().readAllObjects( FXRateBasis.class );
                for ( int i = 0; i < objs.size(); i++ )
                {
                    FXRateBasis b = ( FXRateBasis ) objs.elementAt( i );
                    if ( b.getCurrencyPairString().equals( "EUR/USD" ) )
                    {
                        testCcypair = b;
                        break;
                    }
                }

                IdcDate endOfMonthValueDate = null;

                if ( null != testCcypair )
                {
                    endOfMonthValueDate = testCcypair.getValueDate( DateTimeFactory.timeNow(), t );
                }
                else
                {
                    log( "No FXRateBasis for EUR/USD is defined." );
                }

                log.warn( "*** today= " + DateTimeFactory.timeNow() );
                log.warn( "*** Tenor= " + month );
                log.warn( "*** Value Date = " + endOfMonthValueDate );
            }
            log( "Finish Tenor test32" );
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );

        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }
    }

    public void testTTT()
    {
        Tenor t = null;
        try
        {
            t = new Tenor( "2007/11/09" );
            t.getDatePeriod();
        }
        catch ( IdcIllegalArgumentException e )
        {
            assertNull( t );
        }
        catch ( Exception e )
        {
            fail( "*** TenorPTestC: catch exception:" + e );
        }

    }

    public void testEndOfMonthTenors()
    {
        try
        {
            FXRateConvention stdQotConv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );
            FXRateBasis eurUsdFXRateBasis = stdQotConv.getFXRateBasis( "EUR", "USD" );
            IdcDate currentDate = DateTimeFactory.newDate();

            for ( int index = 0; index < MONTHS.length; index++ )
            {
                IdcDate valueDate = eurUsdFXRateBasis.getValueDate( currentDate, new Tenor( MONTHS[index] ) );
                log( "value date=" + valueDate + " for tenor=" + MONTHS[index] );
                assertEquals( valueDate.asJdkDate().getMonth(), index );
            }
        }
        catch ( Exception e )
        {
            fail( "TenorPTestC.testEndOfMonthTenors", e );
        }
    }

    public void testMonthTenors()
    {
        try
        {
            FXRateConvention stdQotConv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );
            FXRateBasis eurUsdFXRateBasis = stdQotConv.getFXRateBasis( "EUR", "USD" );
            IdcDate currentDate = DateTimeFactory.newDate();
            IdcDate spotDate = eurUsdFXRateBasis.getSpotDate( currentDate );
            int currentMonth = spotDate.getMonth();

            for ( int index = 0; index < MONTH_TENORS.length; index++ )
            {
                IdcDate valueDate = eurUsdFXRateBasis.getValueDate( currentDate, new Tenor( MONTH_TENORS[index] ) );
                log( "value date=" + valueDate + " for tenor=" + MONTH_TENORS[index] );
                assertEquals( valueDate.asJdkDate().getMonth(), ( index + currentMonth ) % 12 );
            }
        }
        catch ( Exception e )
        {
            fail( "TenorPTestC.testMonthTenors", e );
        }
    }

    public void testYearTenors()
    {
        try
        {
            FXRateConvention stdQotConv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );
            FXRateBasis eurUsdFXRateBasis = stdQotConv.getFXRateBasis( "EUR", "USD" );
            IdcDate currentDate = DateTimeFactory.newDate();
            IdcDate spotDate = eurUsdFXRateBasis.getSpotDate( currentDate );
            int currentMonth = spotDate.asJdkDate().getMonth();

            for ( int index = 0; index < YEAR_TENORS.length; index++ )
            {
                IdcDate valueDate = eurUsdFXRateBasis.getValueDate( currentDate, new Tenor( YEAR_TENORS[index] ) );
                log( "value date=" + valueDate + " for tenor=" + YEAR_TENORS[index] );
                assertEquals( valueDate.asJdkDate().getMonth(), currentMonth );
            }
        }
        catch ( Exception e )
        {
            fail( "TenorPTestC.testYearTenors", e );
        }
    }

    public void testTenorDays()
    {
        try
        {
            for ( String tenorStr: SPECIAL_TENORS )
            {
                Tenor tenor = new Tenor ( tenorStr );
                assertFalse( tenor.isDays() );
            }

            for ( String tenorStr: MONTH_TENORS )
            {
                Tenor tenor = new Tenor ( tenorStr );
                assertFalse( tenor.isDays() );
            }

            for ( String tenorStr: YEAR_TENORS )
            {
                Tenor tenor = new Tenor ( tenorStr );
                assertFalse( tenor.isDays() );
            }

            for ( int i=1; i < 100; i++ )
            {
                String tenorStr = String.valueOf( i ) + "D";
                Tenor tenor = new Tenor ( tenorStr );
                assertTrue( tenor.isDays() );

                String tenorStr1 = String.valueOf( i ) + "d";
                Tenor tenor1 = new Tenor ( tenorStr1 );
                assertTrue( tenor1.isDays() );
            }

            // special handling of "0d"
            Tenor tenor = new Tenor ( "0d" );
            assertFalse( tenor.isDays() );

        }
        catch ( Exception e )
        {
            fail( "testTenorDays", e );
        }
    }

    public void testTenorMap()
    {
        Map<Tenor, String> map = new HashMap<Tenor, String> (  );
        map.put ( new Tenor ( "1M" ), "testIm" );
        assertNull ( map.get ( new Tenor ( "1M" ) ) );

        Map<Tenor, String> map1 = new ConcurrentSkipListMap<Tenor, String> (  );
        map1.put ( new Tenor ( "1M" ), "testIm" );
        assertNotNull ( map1.get ( new Tenor ( "1M" ) ) );
        assertNotNull ( map1.get ( new Tenor ( "1m" ) ) );

        Map<Tenor, String> map2 = new ConcurrentSkipListMap<Tenor, String> (  );
        map2.put ( new Tenor ( "TOD" ), "testIm" );
        assertNotNull ( map2.get ( new Tenor ( Tenor.Today_0 ) ) );
        assertNotNull ( map2.get ( new Tenor ( Tenor.Today ) ) );
    }

    public void testTenorFactory()
    {
        try
        {
            Map<String, Tenor> tenorMap = TenorFactory.getTenorMap( );
            assertNotNull ( tenorMap );
            assertTrue ( tenorMap.size () > 0 );
            for ( String tenorStr: tenorMap.keySet () )
            {
                Tenor newTenor = new Tenor( tenorStr );
                Tenor cachedTenor = tenorMap.get ( tenorStr );
                assertTrue ( newTenor.equals ( cachedTenor ) );
            }
        }
        catch ( Exception e )
        {
            fail( "testTenorFactory", e );
        }
    }

    public void testVirtualCurrencyIndexUniqueness()
    {
        try
        {
            Collection<Tenor> tenors = TenorFactory.getTenorMap ().values ();
            TreeMap<Integer, String> map = new TreeMap<Integer, String> ();
            for ( int i=1; i < 5000; i++ )
            {
                Map<Integer, Tenor> tenorIndexMap = new HashMap<Integer, Tenor> ();
                for ( Tenor tenor: tenors )
                {
                    if ( tenorIndexMap.containsKey ( tenor.getIndex () ) )
                    {
                        continue;
                    }
                    tenorIndexMap.put ( tenor.getIndex (), tenor );
                    int ccyIndex = getSimulatedCurrencyIndex ( i, tenor, null );
                    assertFalse ( "i=" + i + ",ccyIndex=" + ccyIndex + ",map.get=" + map.get ( ccyIndex ) + ",tenor=" + tenor, map.containsKey ( ccyIndex ) );
                    map.put ( ccyIndex, i + "_" + tenor );
                }

                IdcDate date = DateTimeFactory.newDate ();
                for ( int j = 0; j < 5000; j++ )
                {
                    int ccyIndex = getSimulatedCurrencyIndex ( i, null, date );
                    assertFalse ( "i=" + i + ",ccyIndex=" + ccyIndex + ",map.get=" + map.get ( ccyIndex ) + ",date=" + date, map.containsKey ( ccyIndex ) );
                    map.put ( ccyIndex, i + "_" + date.getFormattedDate ( IdcDate.DD_MMM_YYYY_FORWARD_SLASH ) );
                    date = date.addDays ( 1 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testVirtualCurrencyIndexUniqueness", e );
        }
    }

    private int getSimulatedCurrencyIndex( int ccyIndex, Tenor tenor, IdcDate brokenDate )
    {
        int idx = ccyIndex;
        idx <<= 16;
        if ( tenor != null )
        {
            if ( tenor.getIndex () != -1 )
            {
                idx |= (tenor.getIndex () & 0xFFFF);
            }
            else
            {
                log.info ( "invalid index for settlement tenor for currency=" + this + ",ccyIndex=" + ccyIndex + ",tenor=" + tenor + ",brokenDate=" + brokenDate );
            }
        }
        else if ( brokenDate != null )
        {
            idx |= ( ( brokenDate.asJdkDate ().getTime() / IdcDate.MILLISECONDS_PER_DAY ) & 0xFFFF);
        }
        return idx;
    }

    public void testIMMDateCalculationLogicChange()
    {
        try
        {
            TimeZone.setDefault( TimeZone.getTimeZone( "GMT" ));
            WatchPropertyC.update ( FinanceMBean.FIRST_IMM_DATE_AFTER_SPOT_DATE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );

            FXRateBasis rateBasis = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ().getFXRateBasis ( "EUR", "USD" );
            Tenor imm1Tenor = new Tenor( "1IMM" );
            Tenor imm2Tenor = new Tenor( "2IMM" );
            Tenor imm3Tenor = new Tenor( "3IMM" );
            Tenor imm4Tenor = new Tenor( "4IMM" );

            IdcDate tradeDate = DateTimeFactory.newDate ( TimeZone.getDefault () );
            IdcDate date = tradeDate;
            for ( int i=0; i < 500; i++ )
            {
                IdcDate spotDate = rateBasis.getValueDate ( date, Tenor.SPOT_TENOR );
                IdcDate imm1Date = rateBasis.getValueDate ( date, imm1Tenor );
                IdcDate imm2Date = rateBasis.getValueDate ( date, imm2Tenor );
                IdcDate imm3Date = rateBasis.getValueDate ( date, imm3Tenor );
                IdcDate imm4Date = rateBasis.getValueDate ( date, imm4Tenor );
                int dp1 = imm2Date.asDays () - imm1Date.asDays ();
                int dp2 = imm3Date.asDays () - imm2Date.asDays ();
                int dp3 = imm4Date.asDays () - imm3Date.asDays ();
                assertTrue ( imm1Date.isLaterThanOrEqualTo ( spotDate ) );
                assertTrue ( dp1 > 85 && dp1 < 95 );
                assertTrue ( dp2 > 85 && dp2 < 95 );
                assertTrue ( dp3 > 85 && dp3 < 95 );

                assertEquals ( IdcDate.WEDNESDAY, imm1Date.getDayOfWeek () );
                assertEquals ( IdcDate.WEDNESDAY, imm2Date.getDayOfWeek () );
                assertEquals ( IdcDate.WEDNESDAY, imm3Date.getDayOfWeek () );
                assertEquals ( IdcDate.WEDNESDAY, imm4Date.getDayOfWeek () );

                date = date.addDays ( 1 );
            }

            ( ( FXRateBasisC ) rateBasis).resetTransients ();

            WatchPropertyC.update ( FinanceMBean.FIRST_IMM_DATE_AFTER_SPOT_DATE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            date = tradeDate;
            for ( int i=0; i < 500; i++ )
            {
                IdcDate spotDate = rateBasis.getValueDate ( date, Tenor.SPOT_TENOR );
                IdcDate imm1Date = rateBasis.getValueDate ( date, imm1Tenor );
                IdcDate imm2Date = rateBasis.getValueDate ( date, imm2Tenor );
                IdcDate imm3Date = rateBasis.getValueDate ( date, imm3Tenor );
                IdcDate imm4Date = rateBasis.getValueDate ( date, imm4Tenor );
                int dp1 = imm2Date.asDays () - imm1Date.asDays ();
                int dp2 = imm3Date.asDays () - imm2Date.asDays ();
                int dp3 = imm4Date.asDays () - imm3Date.asDays ();
                assertTrue ( "imm1Date=" + imm1Date + ",spotDate=" + spotDate + ",date=" + date, imm1Date.isLaterThan ( spotDate ) );
                assertTrue ( dp1 > 85 && dp1 < 95 );
                assertTrue ( dp2 > 85 && dp2 < 95 );
                assertTrue ( dp3 > 85 && dp3 < 95 );

                assertEquals ( IdcDate.WEDNESDAY, imm1Date.getDayOfWeek () );
                assertEquals ( IdcDate.WEDNESDAY, imm2Date.getDayOfWeek () );
                assertEquals ( IdcDate.WEDNESDAY, imm3Date.getDayOfWeek () );
                assertEquals ( IdcDate.WEDNESDAY, imm4Date.getDayOfWeek () );

                date = date.addDays ( 1 );
            }
        }
        catch ( Exception e )
        {
            fail ( "testIMMDateCalculationLogicChange", e );
        }
    }
}

