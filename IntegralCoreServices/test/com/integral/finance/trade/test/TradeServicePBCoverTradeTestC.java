package com.integral.finance.trade.test;

import com.integral.facade.FacadeFactory;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.Quote;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.calculator.TradeDownloadKondorMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadTOFMessageBuilderC;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.message.EntityReference;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemC;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.util.DealingTestUtilC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.workflow.RequestWorkflowStateMapDependentC;
import electric.xml.Document;
import electric.xml.XPath;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

public class TradeServicePBCoverTradeTestC extends TradeServiceTestC
{
    public static final String singleLeg = "SingleLeg";
    public static final String swap = "Swap";

    public TradeServicePBCoverTradeTestC( String name ) throws Exception
    {
        super( name );
        ExternalSystem extSys = ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( takerPbName, ExternalSystemC.class, null, Entity.ACTIVE_STATUS );
        if ( extSys == null )
        {
            init( adminUser );
            IdcTransaction tx = initTransaction();
            ExternalSystem extSysNew = ( ExternalSystem ) new ExternalSystemC().getRegisteredObject();
            extSysNew.setShortName( takerPbName );
            tx.commit();
        }
    }

    // created to test 41943. and also to make sure that my changes do not result in any recursive loops. I had manually put System.out.Println in executePrimeWorkflow Method to test
    // that that method gets called 9 times for this test case          System.out.println( "TradeServiceC.executePrimeBrokerCoverTradeWorkflow" +  sourceWfMsg.getEventName() );
    public void testPrimeWorkflowGettingExecuted() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        FXSingleLeg trade = prepareSingleLegTrade( 100.00, true, true, "EUR/USD", makerOrg, makerTpForTaker, 1.2334, spotDate );
        prepareSingleLegRequest( trade );
        TradeCloneServiceC.createCptyTrades( trade );

        WorkflowMessage msg2 = MessageFactory.newWorkflowMessage();
        msg2.setEventName( TradeService.VERIFY_EVENT );
        msg2.setTopic( TradeService.MESSAGE_TOPIC );
        msg2.setProperty( "WORKFLOW_CODE", "2" );
        msg2.setObject( trade );
        msg2.setProperty( "WORKFLOW_CODE_ARG", "TEST" );
        ts.verifyTrade( trade, msg2 );
        ts.process( msg2 );
        ts.verifyTrade( trade, null );


        msg2.setEventName( TradeService.FAIL_EVENT );
        ts.failTrade( trade, 0, "TEST", msg2 );
        msg2.setProperty( "WORKFLOW_CODE", "2" );// needed because failTrade above sets the value as Integer 0 on msg2; hence ClassCastException happens in ts.process below
        ts.process( msg2 );
        ts.failTrade( trade, 0, "TEST", null );

        msg2.setEventName( TradeService.REJECT_EVENT );
        ts.rejectTrade( trade, 0, "", msg2 );
        msg2.setProperty( "WORKFLOW_CODE", "2" );
        ts.process( msg2 );
        ts.rejectTrade( trade, 0, "", null );
        tx.release();

    }

    private Quote createQuote(Trade trade)
    {
        Quote emptyQuote = DealingFactory.newQuote();
        assertNull( emptyQuote.getBidQuotePrices() );
        assertNull( emptyQuote.getOfferQuotePrices() );

        Currency ccy1 = CurrencyFactory.getCurrency( "EUR" );
        Currency ccy2 = CurrencyFactory.getCurrency( "USD" );
        double[] bidRates = new double[]{1.2345, 1.2346, 1.2347};
        double[] offerRates = new double[]{1.2355, 1.2356};
        double[] bidLimits = new double[]{1000, 2000, 3000};
        double[] offerLimits = new double[]{1000, 2000};
        Quote quote = DealingTestUtilC.createTestMultipleTierSpotQuote( null, ccy1, ccy2, bidRates, offerRates, bidLimits, offerLimits, false );
        quote.setExternalTradeId( trade.getCounterpartyB().getShortName()+trade.getTransactionID() );

        return quote;
    }

    private WorkflowMessage execute( String event, String type, Trade trdArgument, String testCaseName )
    {
        TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow.FICpty12", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        Trade coverTrade;
        WorkflowMessage msg = null;
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        try
        {
            tx = initTransaction();
            uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            return null;
        }

        if ( type.equals( singleLeg ) )
        {
            trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            if ( trdArgument == null )
            {
                prepareSingleLegRequest( ( FXSingleLeg ) trd );
            }
        }
        else
        {
            trd = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            if ( trdArgument == null )
            {
                prepareSwapRequest( ( FXSwap ) trd );
            }
        }
        if ( trdArgument != null )
        {
            trdArgument = ( Trade ) ( ( Trade ) uow.refreshObject( trdArgument ) ).getRegisteredObject();
            trd.setRequest( trdArgument.getRequest() );
            trd.getRequest().setTrade( trd );
        }
        trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        Counterparty cptyA = trd.getCounterpartyA();
        Counterparty cptyB = trd.getCounterpartyB();
        String origNamespace = trd.getNamespace().getShortName();
        if ( testCaseName.equals( "testVerifyEventSingleLegWarmupTrade" ) )
        {
            IdcUtilC.setAsWarmUpObject( trd );
            IdcUtilC.setAsWarmUpObject( trd.getRequest() );
        }
        ts.newTrade( trd, null );  // so as to set cptyc on trade and create cpty trades
        TradingParty cptyC = ( TradingParty ) trd.getCounterpartyC();//initialize these only after newtrade is called
        trd.getRequest().setPrimeBrokerTradingParty( cptyC );
        User defaultPBUser = cptyC.getLegalEntityOrganization().getDefaultDealingUser();
        User originalTakerUser = trd.getEntryUser();
        User originalMakerUser = trd.getMakerUser();
        if ( event.equals( TradeService.VERIFY_EVENT ) )
        {
            msg = ts.verifyTrade( trd, null );
        }
        else if ( event.equals( TradeService.REJECT_EVENT ) )
        {
            msg = ts.rejectTrade( trd, 1, "TEST", null );
        }
        else if ( event.equals( TradeService.FAIL_EVENT ) )
        {
            msg = ts.failTrade( trd, 1, "TEST", null );
        }
        coverTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        tx.commitAndResume();
        coverTrade = ( Trade ) uow.refreshObject( coverTrade );
        trd = ( Trade ) uow.refreshObject( trd );
        tx.release();    //release the tx before; so that if any of assertEquals fails; this transaction is not passed to any  other test case.
        assertEquals( trd.getCounterpartyA().getObjectID(), cptyA.getObjectID() );
        assertEquals( trd.getCounterpartyB().getObjectID(), CounterpartyUtilC.getLegalEntity( cptyC ).getObjectID() );
        assertEquals( trd.getCounterpartyC(), null );
        assertEquals( trd.getCounterpartyD(), null );
        assertEquals( CounterpartyUtilC.getLegalEntity( trd.getCounterpartyA() ), trd.getCptyTrade( 'A' ).getLegalEntity() );
        assertEquals( CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ), trd.getCptyTrade( 'B' ).getLegalEntity() );
        assertEquals( CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ), CounterpartyUtilC.getLegalEntity( trd.getCounterpartyA() ).getOrganization() ), trd.getCptyTrade( 'A' ).getTradingParty() );
        assertEquals( CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( trd.getCounterpartyA() ), CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ).getOrganization() ), trd.getCptyTrade( 'B' ).getTradingParty() );

        //All users on trd, cptyTrades
        assertEquals( trd.getMakerUser(), defaultPBUser );
        assertEquals( trd.getEntryUser(), originalTakerUser );
        assertEquals( trd.getCptyTrade( 'A' ).getUser(), originalTakerUser );
        assertEquals( trd.getCptyTrade( 'B' ).getUser(), defaultPBUser );
        assertEquals( trd.getCptyTrade( 'A' ).getTradingPartyUser(), defaultPBUser );
        assertEquals( trd.getCptyTrade( 'B' ).getTradingPartyUser(), originalTakerUser );
        assertEquals( coverTrade.getMakerUser(), originalMakerUser );
        assertEquals( coverTrade.getEntryUser(), defaultPBUser );
        assertEquals( coverTrade.getCptyTrade( 'A' ).getUser(), defaultPBUser );
        assertEquals( coverTrade.getCptyTrade( 'B' ).getUser(), originalMakerUser );
        assertEquals( coverTrade.getCptyTrade( 'A' ).getTradingPartyUser(), originalMakerUser );
        assertEquals( coverTrade.getCptyTrade( 'B' ).getTradingPartyUser(), defaultPBUser );


        assertEquals( coverTrade.getCounterpartyA(), CounterpartyUtilC.getLegalEntity( cptyC ) );
        assertEquals( coverTrade.getCounterpartyC(), null );
        assertEquals( coverTrade.getCounterpartyD(), null );
        assertEquals( CounterpartyUtilC.getLegalEntity( coverTrade.getCounterpartyA() ), coverTrade.getCptyTrade( 'A' ).getLegalEntity() );
        assertEquals( CounterpartyUtilC.getLegalEntity( coverTrade.getCounterpartyB() ), coverTrade.getCptyTrade( 'B' ).getLegalEntity() );
        assertEquals( CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( coverTrade.getCounterpartyB() ), CounterpartyUtilC.getLegalEntity( coverTrade.getCounterpartyA() ).getOrganization() ), coverTrade.getCptyTrade( 'A' ).getTradingParty() );
        assertEquals( CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( coverTrade.getCounterpartyA() ), CounterpartyUtilC.getLegalEntity( coverTrade.getCounterpartyB() ).getOrganization() ), coverTrade.getCptyTrade( 'B' ).getTradingParty() );


        assertEquals( trd.getCptyTrade( 'A' ).getNamespace().getShortName(), CounterpartyUtilC.getLegalEntity( cptyA ).getNamespace().getShortName() );
        assertEquals( trd.getCptyTrade( 'B' ).getNamespace().getShortName(), cptyC.getLegalEntity().getNamespace().getShortName() );
        assertEquals( coverTrade.getCptyTrade( 'A' ).getNamespace().getShortName(), cptyC.getLegalEntity().getNamespace().getShortName() );

        if ( !testCaseName.equals( "testVerifyEventAnnonymousLP" ) )
        {
            assertEquals( coverTrade.getCptyTrade( 'B' ).getNamespace().getShortName(), CounterpartyUtilC.getLegalEntity( cptyB ).getNamespace().getShortName() );
            assertEquals( coverTrade.getMakerCounterparty(), CounterpartyUtilC.getTradingParty( coverTrade.getCounterpartyB(), CounterpartyUtilC.getLegalEntity( coverTrade.getCounterpartyA() ).getOrganization() ) );
            assertEquals( coverTrade.getCounterpartyB().getObjectID(), cptyB.getObjectID() ); // cptyB of original trade should be same as cptyB of coverTrade
        }
        if ( !testCaseName.equals( "testBackToBackPB" ) )
        {
            assertTrue( coverTrade.getCptyTrades().size() > 0 );  // can be 2 or 4 depending on the configuration. Before we were not creating cptytrades for backto back pb; but in between we had an enhacnement to do so
            assertTrue( trd.getCptyTrades().size() > 0 );
        }

        assertEquals( trd.getTransactionID() + 'C', coverTrade.getTransactionID() );
        if ( trdArgument == null ) // if not multi-fill
        {
            assertEquals( trd.getRequest().getTransactionID() + 'C', coverTrade.getRequest().getTransactionID() );
        }

        assertEquals( trd.getNamespace().getShortName(), origNamespace );
        assertEquals( coverTrade.getNamespace().getShortName(), cptyC.getLegalEntity().getNamespace().getShortName() );
        assertEquals( coverTrade.getRequest().getNamespace().getShortName(), cptyC.getLegalEntity().getNamespace().getShortName() );

        assertEquals( trd.getTakerCounterparty(), trd.getCounterpartyA() );
        assertEquals( trd.getMakerCounterparty(), CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), CounterpartyUtilC.getLegalEntity( trd.getCounterpartyA() ).getOrganization() ) );
        assertEquals( coverTrade.getTakerCounterparty(), coverTrade.getCounterpartyA() );
        assertEquals( coverTrade.getRequest().getWorkflowStateMap() instanceof RequestWorkflowStateMapDependentC, true );//38961
        return msg;
    }

    //38671
    public void testSTPFinXMLDownloads() throws Exception
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testSTPFinXMLDownloads" );
        Trade origTrade = ( Trade ) msg.getObject();

        msg.setProperty( "organization", origTrade.getCounterpartyA().getOrganization() );

        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        msg.setEntity( "request", origTrade.getRequest() );
        EntityReference entRef = msg.getEntityReference( "request" );
        entRef.setReferenceOnly( false );

        log( "********************** Original trade *************" );
        String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );
        Document xmlDoc = new Document( xml );
        String requestStateOrig = xmlDoc.getElement( new XPath( "/workflowMessage/entityProperty[@key='request']/request/workflowStateMap/state" ) ).getTextString();

        log( "********************** Cover trade created*************" );
        msg.setObject( newTrade );
        newTrade.setCptyTrade( newTrade.getCptyTrade( 'A' ) );
        msg.setEntity( "request", newTrade.getRequest() );
        xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );


        xmlDoc = new Document( xml );
        String requestStateNew = xmlDoc.getElement( new XPath( "/workflowMessage/entityProperty[@key='request']/request/workflowStateMap/state" ) ).getTextString();
        String settlementDateRule = xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/settlementDateRule" ) ).getTextString();
        assertEquals( requestStateOrig, requestStateNew );
        assertEquals( settlementDateRule, origTrade.getSettlementDateRule().getShortName() );

    }

    public void testSTPKondorDownloadsSingleLeg() throws Exception
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testSTPKondorDownloadsSingleLeg" );
        Trade origTrade = ( Trade ) msg.getObject();
        msg.setProperty( "organization", origTrade.getCounterpartyA().getOrganization() );
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        newTrade.setCptyTrade( newTrade.getCptyTrade( 'A' ) );
        origTrade.setCptyTrade( origTrade.getCptyTrade( 'A' ) );
        msg.setEntity( "request", origTrade.getRequest() );
        EntityReference entRef = msg.getEntityReference( "request" );
        entRef.setReferenceOnly( false );

        String xml = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( msg, null );
        log( "Original trade in kondor format is " + xml );
        msg.setObject( newTrade );
        msg.setEntity( "request", newTrade.getRequest() );
        xml = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( msg, null );
        log( "Cover trade in kondor format is " + xml );
    }

    public void testSTPKondorDownloadsSwap() throws Exception
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, swap, null, "testSTPKondorDownloadsSwap" );
        Trade origTrade = ( Trade ) msg.getObject();
        msg.setProperty( "organization", origTrade.getCounterpartyA().getOrganization() );
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        newTrade.setCptyTrade( newTrade.getCptyTrade( 'A' ) );
        origTrade.setCptyTrade( origTrade.getCptyTrade( 'A' ) );
        msg.setEntity( "request", origTrade.getRequest() );
        EntityReference entRef = msg.getEntityReference( "request" );
        entRef.setReferenceOnly( false );

        String xml = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( msg, null );
        log( "Original trade in kondor format is " + xml );
        msg.setObject( newTrade );
        msg.setEntity( "request", newTrade.getRequest() );
        xml = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( msg, null );
        log( "Cover trade in kondor format is " + xml );
    }

    public void testSTPTOFDownloadsSingleLeg() throws Exception
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testSTPTOFDownloadsSingleLeg" );
        Trade origTrade = ( Trade ) msg.getObject();

        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        msg.setEntity( "request", origTrade.getRequest() );
        EntityReference entRef = msg.getEntityReference( "request" );
        entRef.setReferenceOnly( false );
        newTrade.setCptyTrade( newTrade.getCptyTrade( 'A' ) );
        origTrade.setCptyTrade( origTrade.getCptyTrade( 'A' ) );
        msg.setProperty( "organization", origTrade.getCounterpartyA().getOrganization() );

        log( "********************** Original trade *************" );
        String xml = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );

        log( "********************** Cover trade created*************" );
        msg.setObject( newTrade );
        msg.setEntity( "request", newTrade.getRequest() );
        newTrade.setEntryUser( takerUser );
        xml = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );
    }

    public void testSTPTOFDownloadsSwap() throws Exception
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, swap, null, "testSTPTOFDownloadsSwap" );
        Trade origTrade = ( Trade ) msg.getObject();

        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        msg.setEntity( "request", origTrade.getRequest() );
        EntityReference entRef = msg.getEntityReference( "request" );
        entRef.setReferenceOnly( false );
        newTrade.setCptyTrade( newTrade.getCptyTrade( 'A' ) );
        origTrade.setCptyTrade( origTrade.getCptyTrade( 'A' ) );
        msg.setProperty( "organization", origTrade.getCounterpartyA().getOrganization() );

        log( "********************** Original trade *************" );
        String xml = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );

        log( "********************** Cover trade created*************" );
        msg.setObject( newTrade );
        msg.setEntity( "request", newTrade.getRequest() );
        newTrade.setEntryUser( takerUser );
        xml = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );
    }


    public void testVerifyEventMultiFill()
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testVerifyEventMultiFill" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        msg = execute( TradeService.VERIFY_EVENT, singleLeg, origTrd, "testVerifyEventMultiFill" );
        Trade origTrd2 = ( Trade ) msg.getObject();
        Trade newTrade2 = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        assertEquals( origTrd.getRequest().getOrganization(), origTrd2.getRequest().getOrganization() );
        assertEquals( newTrade.getRequest().getOrganization(), newTrade2.getRequest().getOrganization() );
        assertEquals( origTrd.getRequest().getObjectID(), origTrd2.getRequest().getObjectID() );
        assertEquals( newTrade.getRequest().getObjectID(), newTrade2.getRequest().getObjectID() );
    }

    public void testBackToBackPB()
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testBackToBackPB" );
        Trade origTrade = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        assertTrue( origTrade.getCptyTrades().size() > 0 ); // can be 2 or 4 depending on the configuration. Before we were not creating cptytrades for backto back pb; but in between we had an enhacnement to do so
        assertTrue( newTrade.getCptyTrades().size() > 0 );

        TradingParty tpA = CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( origTrade.getCounterpartyB() ), CounterpartyUtilC.getLegalEntity( origTrade.getCounterpartyA() ).getOrganization() );
        TradingParty tpB = CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( origTrade.getCounterpartyA() ), CounterpartyUtilC.getLegalEntity( origTrade.getCounterpartyB() ).getOrganization() );

        TradingParty newTradeTpA = CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( newTrade.getCounterpartyB() ), CounterpartyUtilC.getLegalEntity( newTrade.getCounterpartyA() ).getOrganization() );
        TradingParty newTradeTpB = CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( newTrade.getCounterpartyA() ), CounterpartyUtilC.getLegalEntity( newTrade.getCounterpartyB() ).getOrganization() );
        try
        {
            tpA.setPrimeBrokerOrganization( cptyOrg );
            tpB.setPrimeBrokerOrganization( ddOrg );
            newTradeTpA.setPrimeBrokerOrganization( cptyOrg );
            newTradeTpB.setPrimeBrokerOrganization( ddOrg );
            msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testBackToBackPB" );
            origTrade = ( Trade ) msg.getObject();
            newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
            assertTrue( origTrade.getCptyTrades().size() > 0 );
            assertTrue( newTrade.getCptyTrades().size() > 0 );
        }
        finally
        {
            tpA.setPrimeBrokerOrganization( null );
            tpB.setPrimeBrokerOrganization( null );
            newTradeTpA.setPrimeBrokerOrganization( null );
            newTradeTpB.setPrimeBrokerOrganization( null );
        }


    }


    public void testVerifyEventBrokerCoverTrades()
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testVerifyEventBrokerCoverTrades" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
    }

    public void testConfirmEventSingleLeg() throws Exception
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testConfirmEventSingleLeg" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        origTrd = ( Trade ) tx.getUOW().refreshObject( origTrd );
        newTrade = ( Trade ) tx.getUOW().refreshObject( newTrade );
        ts.confirmTrade( origTrd, null );
        tx.commit();
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, origTrd );
        TradeStateFacade tradeFacade2 = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, newTrade );
        assertEquals( tradeFacade.isConfirmed(), true );
        assertEquals( tradeFacade2.isConfirmed(), true );
    }

    public void testVerifyEventAnnonymousLP() throws Exception
    {
        try
        {
            // set the masked org configuration
            makerOrg.setRealLP( ddOrg );
            TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( "IDC.LP.DD1.Masked", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( "IDC.LP.DD1.Masked.Name", "FI2", ConfigurationProperty.DYNAMIC_SCOPE, null );

            WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testVerifyEventAnnonymousLP" );
            Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( com.integral.user.Organization.class );
            Expression expr = new ExpressionBuilder().get( NamedEntity.ShortName ).equal( "DD1" );
            raq.setSelectionCriteria( expr );
            Organization obj = ( Organization ) ( ( Vector ) PersistenceFactory.newSession().executeQuery( raq ) ).get( 0 );
            TradeConfigurationFactory.getTradeConfigurationMBean().removeProperty( "IDC.LP.DD1.Masked", ConfigurationProperty.DYNAMIC_SCOPE );
            TradeConfigurationFactory.getTradeConfigurationMBean().removeProperty( "IDC.LP.DD1.Masked.Name", ConfigurationProperty.DYNAMIC_SCOPE );
            assertEquals( newTrade.getCounterpartyB(), obj.getCustomFieldValue( "DirectFX_DefaultDealingEntity" ) );
            assertEquals( newTrade.getCptyTrade( 'B' ).getNamespace().getShortName(), "DD1" );
        }
        finally
        {
            makerOrg.setRealLP( null );
        }
    }

    public void testVerifyEventSingleLegWarmupTrade()
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testVerifyEventSingleLegWarmupTrade" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, origTrd );
        assertEquals( tradeFacade.isVerified(), true );
        tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, newTrade );
        assertEquals( tradeFacade.isVerified(), true );
    }

    public void testVerifyEventSingleLeg()
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, singleLeg, null, "testVerifyEventSingleLeg" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, origTrd );
        assertEquals( tradeFacade.isVerified(), true );
        tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, newTrade );
        assertEquals( origTrd.getMakerReferenceId(), newTrade.getTransactionID() );
        //assertEquals( newTrade.getTakerReferenceId(), origTrd.getTransactionID() ); we no longer set it ; changes were done for Traiana ReportingParty tag
        assertEquals( origTrd.getTakerReferenceId(), null );
        assertEquals( newTrade.getMakerReferenceId(), null );
        assertEquals( origTrd.getExternalSystemId( CounterpartyUtilC.getLegalEntity( origTrd.getCounterpartyB() ).getOrganization().getShortName() ).getSystemId(), newTrade.getTransactionID() );
        assertEquals( newTrade.getExternalSystemId( TradeService.COVEREDTRADE_KEY ).getSystemId(), origTrd.getTransactionID() );
        assertEquals( tradeFacade.isVerified(), true );
    }

    public void testVerifyEventSwap()
    {
        WorkflowMessage msg = execute( TradeService.VERIFY_EVENT, swap, null, "testVerifyEventSwap" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, origTrd );
        assertEquals( tradeFacade.isVerified(), true );
        tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, newTrade );
        assertEquals( tradeFacade.isVerified(), true );
    }

    public void testRejectEventSingleLeg()
    {
        WorkflowMessage msg = execute( TradeService.REJECT_EVENT, singleLeg, null, "testRejectEventSingleLeg" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, origTrd );
        assertEquals( tradeFacade.isRejected(), true );
        tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, newTrade );
        assertEquals( tradeFacade.isRejected(), true );
    }

    public void testRejectEventSwap()
    {
        WorkflowMessage msg = execute( TradeService.REJECT_EVENT, swap, null, "testRejectEventSwap" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, origTrd );
        assertEquals( tradeFacade.isRejected(), true );
        tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, newTrade );
        assertEquals( tradeFacade.isRejected(), true );
    }

    public void testFailEventSingleLeg()
    {
        WorkflowMessage msg = execute( TradeService.FAIL_EVENT, singleLeg, null, "testFailEventSingleLeg" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, origTrd );
        assertEquals( tradeFacade.isFailed(), true );
        tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, newTrade );
        assertEquals( tradeFacade.isFailed(), true );
    }

    public void testFailEventSwap()
    {
        WorkflowMessage msg = execute( TradeService.FAIL_EVENT, swap, null, "testFailEventSwap" );
        Trade origTrd = ( Trade ) msg.getObject();
        Trade newTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, origTrd );
        assertEquals( tradeFacade.isFailed(), true );
        tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, newTrade );
        assertEquals( tradeFacade.isFailed(), true );
    }

    public void testPBTakerMakerReferenceID ()
    {
        TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow.FICpty12", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( "Idc.PB.CoverTrade.Maker.Reference.Id.Enabled", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
        verifyPBMakerRefid(true);

        TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( "Idc.PB.CoverTrade.Maker.Reference.Id.Enabled", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        verifyPBMakerRefid(false);
    }

    private void verifyPBMakerRefid(boolean testEqual)
    {
        Trade coverTrade;
        WorkflowMessage msg = null;
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        try
        {
            tx = initTransaction();
            uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );

            trd.setCptyTrade( trd.getCptyTrade( 'A' ) );

            trd.getRequest().setAcceptedQuote( createQuote( trd ) );
            String orderId = "123456";
            trd.getRequest().setOrderId(orderId  );
            ts.newTrade( trd, null );  // so as to set cptyc on trade and create cpty trades
            TradingParty cptyC = ( TradingParty ) trd.getCounterpartyC();//initialize these only after newtrade is called
            trd.getRequest().setPrimeBrokerTradingParty( cptyC );
            String makerReferenceId = trd.getRequest().getAcceptedQuote().getExternalTradeId();
            msg = ts.verifyTrade( trd, null );
            coverTrade = ( Trade ) msg.getParameterValue( TradeService.COVER_TRADE_KEY );
            tx.commitAndResume();
            coverTrade = ( Trade ) uow.refreshObject( coverTrade );
            trd = ( Trade ) uow.refreshObject( trd );
            tx.release();
            assertNotNull(trd.getMakerReferenceId());
            assertNotNull( coverTrade.getMakerReferenceId() );
            assertNotNull( trd.getTakerReferenceId() );
            assertNotNull( coverTrade.getTakerReferenceId() );
            if (testEqual)
            {
                assertEquals( trd.getMakerReferenceId(), coverTrade.getTransactionID() );
                assertEquals( coverTrade.getMakerReferenceId(),coverTrade.getTransactionID() );
                assertEquals( trd.getTakerReferenceId(),orderId );
                assertEquals( coverTrade.getTakerReferenceId(),orderId );
            }
            else
            {
                assertEquals(trd.getMakerReferenceId(),coverTrade.getTransactionID());
                assertEquals( coverTrade.getMakerReferenceId(),makerReferenceId );
            }
        }
        catch ( Exception e )
        {
            fail("testPBTakerMakerReferenceID",e) ;

        }
    }
}
