package com.integral.finance.trade.test;

import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeDownloadServiceConfiguration;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.test.MBeanTestCaseC;
import com.integral.util.Triplet;

/**
 * Created by IntelliJ IDEA.
 * User: pandit
 * Date: 12/5/11
 * Time: 5:44 PM
 * To change this template use File | Settings | File Templates.
 */
public class TradeDownloadServiceConfigurationTestC extends MBeanTestCaseC
{
    private IdcMBeanC config = ( IdcMBeanC ) TradeConfigurationFactory.getTradeDownloadServiceConfigurationMBean();

    public TradeDownloadServiceConfigurationTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( config, "retryCountsOnFailure", TradeServiceConstants.STP_MAX_RETRY_COUNTS_KEY, MBeanTestCaseC.INTEGER );
        testProperty( config, "stpFailureRetryErrorCodePattern", TradeServiceConstants.STP_JMSMESSAGE_FAILURE_ERRORCODEPATTERN_KEY, MBeanTestCaseC.STRING );
        testProperty( config, "stpFailureRetryReasonCodePattern", TradeServiceConstants.STP_JMSMESSAGE_FAILURE_REASONCODEPATTERN_KEY, MBeanTestCaseC.STRING );
    }

    public void testRetryCalls()
    {
        WatchPropertyC.update( TradeServiceConstants.STP_JMSMESSAGE_FAILURE_ERRORCODEPATTERN_KEY, "MQJMS2007", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( TradeServiceConstants.STP_JMSMESSAGE_FAILURE_REASONCODEPATTERN_KEY, "2009", ConfigurationProperty.DYNAMIC_SCOPE );
        checkRetryCall( "MQJMS2007", 2009, true );
        checkRetryCall( "MQJMS2008", 2009, false );
        checkRetryCall( "MQJMS2007", 2010, false );
        checkRetryCall( null, 2010, false );
        checkRetryCall( null, 2009, false );

        WatchPropertyC.update( TradeServiceConstants.STP_JMSMESSAGE_FAILURE_ERRORCODEPATTERN_KEY, "MQJMS2007|MQJMS2008", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( TradeServiceConstants.STP_JMSMESSAGE_FAILURE_REASONCODEPATTERN_KEY, "2009", ConfigurationProperty.DYNAMIC_SCOPE );
        checkRetryCall( "MQJMS2007", 2009, true );
        checkRetryCall( "MQJMS2008", 2009, true );
        checkRetryCall( "MQJMS2007", 2010, false );
        checkRetryCall( "MQJMS2010", 2009, false );
        checkRetryCall( null, 2010, false );
        checkRetryCall( null, null, false );


        WatchPropertyC.update( TradeServiceConstants.STP_JMSMESSAGE_FAILURE_ERRORCODEPATTERN_KEY, "MQJMS2007|MQJMS2008", ConfigurationProperty.DYNAMIC_SCOPE );
        WatchPropertyC.update( TradeServiceConstants.STP_JMSMESSAGE_FAILURE_REASONCODEPATTERN_KEY, "2009|2016", ConfigurationProperty.DYNAMIC_SCOPE );
        checkRetryCall( "MQJMS2007", 2009, true );
        checkRetryCall( "MQJMS2008", 2009, true );
        checkRetryCall( "MQJMS2007", 2010, false );
        checkRetryCall( "MQJMS2010", 2009, false );
        checkRetryCall( null, 2010, false );
        checkRetryCall( null, null, false );
        checkRetryCall( "MQJMS2016", 2009, false );
        checkRetryCall( "MQJMS2008", 2016, true );
        checkRetryCall( "MQJMS2007", 2010, false );
        checkRetryCall( "MQJMS2010", 2016, false );
        checkRetryCall( null, 2010, false );
        checkRetryCall( null, null, false );

    }

    private void checkRetryCall( String errorcode, Integer reasonCode, boolean isTrue )
    {
        Triplet<String, String, Integer> reasonCodes = new Triplet<String, String, Integer>();
        TradeDownloadServiceConfiguration configuration = ( TradeDownloadServiceConfiguration ) config;
        reasonCodes.first = errorcode;
        reasonCodes.third = reasonCode;

        if ( isTrue )
        {
            assertTrue( reasonCodes != null && reasonCodes.first != null && reasonCodes.third != null && reasonCodes.first.matches( configuration.getStpFailureRetryErrorCodePattern() ) && String.valueOf( reasonCodes.third ).matches( configuration.getStpFailureRetryReasonCodePattern() ) );
        }
        else
        {
            assertFalse( reasonCodes != null && reasonCodes.first != null && reasonCodes.third != null && reasonCodes.first.matches( configuration.getStpFailureRetryErrorCodePattern() ) && String.valueOf( reasonCodes.third ).matches( configuration.getStpFailureRetryReasonCodePattern() ) );
        }
    }
}