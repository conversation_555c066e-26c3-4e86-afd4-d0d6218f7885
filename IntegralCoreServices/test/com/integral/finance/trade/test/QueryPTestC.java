package com.integral.finance.trade.test;


import com.integral.finance.financialEvent.FinancialEvent;
import com.integral.finance.fx.FXTradeC;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.persistence.Entity;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Iterator;

public class QueryPTestC extends PTestCaseC
{
    static String name = "Trade Query Test";

    public QueryPTestC( String name )
    {
        super( name );
    }

    public void testPerform_1()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            log( " searching for all fx trades - read all query" );
            java.util.Vector vec = uow.readAllObjects( TradeC.class, new ExpressionBuilder().get( Entity.ObjectID ).lessThan( 1000 ) );
            for ( int i = 0; i < vec.size(); i++ )
            {
                Object obj = vec.elementAt( i );
                Entity ent = ( Entity ) obj;
                log( "  trade #" + i + " , oid = <" + ent.getObjectID() + ">, "
                        + " java class = <" + obj.getClass().getName() + '>' );
            }

            uow.release();
            log( "query trades. size=" + vec.size() );
        }
        catch ( Exception e )
        {
            fail( "query trades", e );
        }
    }

    public void testPerform_2()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            log( " searching for all fx trades - cursored query" );

            ReadAllQuery query = new ReadAllQuery();
            Expression expr = new ExpressionBuilder().get( Entity.ObjectID ).lessThan( 1000 );
            query.setReferenceClass( FXTradeC.class );
            query.setSelectionCriteria( expr );
            query.useCursoredStream( 100, 100 );

            CursoredStream stream = ( CursoredStream ) uow.executeQuery( query );
            int i = 1;
            while ( !stream.atEnd() )
            {
                Object obj = stream.read();
                Entity ent = ( Entity ) obj;
                log( "  trade #" + ( i - 1 ) + " , oid = <" + ent.getObjectID() + ">, "
                        + " class = <" + obj.getClass().getName() + '>' );

                if ( i % 100 == 0 )
                {
                    uow.commitAndResume();
                    stream.releasePrevious();
                }
                i++;
            }

            uow.commit();
            log( "query trades" );
        }
        catch ( Exception e )
        {
            fail( "query trades", e );
        }
    }

    public void testPerform_3()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            log( " searching for all fx trades - cursored query" );

            ReadAllQuery query = new ReadAllQuery();
            Expression expr = new ExpressionBuilder().get( Entity.ObjectID ).lessThan( 1000 );
            query.setReferenceClass( FXTradeC.class );
            query.setSelectionCriteria( expr );
            query.addBatchReadAttribute( "financialEvents" );
            query.useCursoredStream( 50, 50 );

            CursoredStream stream = ( CursoredStream ) uow.executeQuery( query );
            int i = 1;
            while ( !stream.atEnd() )
            {
                Object obj = stream.read();
                Trade ent = ( Trade ) obj;
                log( "  trade #" + ( i - 1 ) + " , OID = <" + ent.getObjectID() + ">, "
                        + " CLASS = <" + obj.getClass().getName() + '>' );


                Iterator events = ent.getFinancialEvents().iterator();
                int j = 0;
                while ( events.hasNext() )
                {
                    j++;
                    FinancialEvent event = ( FinancialEvent ) events.next();
                    log( "\tEVENT-" + j + " = <" + event.getObjectID() + ">," );
                }


                if ( i % 3 == 0 )
                {
                    uow.commitAndResume();
                    stream.releasePrevious();
                }
                i++;
            }

            uow.commit();
            log( "query trades" );
        }
        catch ( Exception e )
        {
            fail( "query trades", e );
        }
    }
}
