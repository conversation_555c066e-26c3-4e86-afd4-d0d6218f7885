package com.integral.finance.trade.test;

import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.calculator.TradeDownloadKondorMessageBuilderC;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.user.Organization;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;

public class IntrafloorTradeDownloadServiceTestC extends TradeServiceTestC
{

    public IntrafloorTradeDownloadServiceTestC( String name ) throws Exception
    {
        super( name );
    }

    public void testIntraFloorFIXSTPDownload() throws Exception
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setIncludeOrderIdInSTP( true );
            takerOrg.setIntraFloorSTPFlag( Organization.INTRAFLOOR_TRADE_BOTH_DOWNLOAD );
            Trade trd = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setCounterpartyB( ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le2", LegalEntityC.class, null, null ) );
            trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            trd.getRequest().setOrderId( "1000" );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );
            log( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testIntraFloorFIXSTPDownload", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
        }
    }

    public void testIntraFloorTOFSTPDownload() throws Exception
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.TOF_FORMAT );
            takerOrg.setIntraFloorSTPFlag( Organization.INTRAFLOOR_TRADE_BOTH_DOWNLOAD );
            Trade trd = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setCounterpartyB( ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le2", LegalEntityC.class, null, null ) );
            trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );
            log( "displayFlipNeeded=" + CounterpartyUtilC.isDisplayOrderFlipNeeded( trd ) );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.TOF_FORMAT + " : " + takerSTPMessage );
            log( "makerSTPMessage in format=" + TradeServiceConstants.TOF_FORMAT + " : " + makerSTPMessage );

            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testIntraFloorTOFSTPDownload", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
        }
    }

    public void testIntraFloorKondorSTPDownload() throws Exception
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.KONDOR_FORMAT );
            takerOrg.setIntraFloorSTPFlag( Organization.INTRAFLOOR_TRADE_BOTH_DOWNLOAD );
            Trade trd = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setCounterpartyB( ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le2", LegalEntityC.class, null, null ) );
            trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );
            log( "displayFlipNeeded=" + CounterpartyUtilC.isDisplayOrderFlipNeeded( trd ) );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.KONDOR_FORMAT + " : " + takerSTPMessage );
            log( "makerSTPMessage in format=" + TradeServiceConstants.KONDOR_FORMAT + " : " + makerSTPMessage );

            String cptyRefValueForTaker = getKondorValue( takerSTPMessage, TradeDownloadKondorMessageBuilderC.CPTY_REF );
            log( "cptyRefValueForTaker" + cptyRefValueForTaker );
            assertEquals( "FI1-le2", cptyRefValueForTaker );

            String cptyRefValueForMaker = getKondorValue( makerSTPMessage, TradeDownloadKondorMessageBuilderC.CPTY_REF );
            log( "cptyRefValueForMaker" + cptyRefValueForMaker );
            assertEquals( "FI1-le1", cptyRefValueForMaker );

            //make sure that cptyName is not there in Kondor format as it is only available in Kondor+v20 format.
            String cptyNameValueForTaker = getKondorValue( takerSTPMessage, TradeDownloadKondorMessageBuilderC.CPTY_NAME );
            log( "cptyNameValueForTaker" + cptyNameValueForTaker );
            assertEquals( "", cptyNameValueForTaker );

            String cptyNameValueForMaker = getKondorValue( makerSTPMessage, TradeDownloadKondorMessageBuilderC.CPTY_NAME );
            log( "cptyNameValueForMaker" + cptyNameValueForMaker );
            assertEquals( "", cptyNameValueForMaker );

            FXLeg fxLeg = ( FXLeg ) trd.getTradeLeg( "fxLeg" );
            String ccy1AmtStr = fxLeg.getFXPayment().getCurrency1().getDecimalFormat( TradeDownloadKondorMessageBuilderC.AMOUNT_FORMAT_STRING ).format( fxLeg.getFXPayment().getCurrency1Amount() );
            String ccy2AmtStr = fxLeg.getFXPayment().getCurrency1().getDecimalFormat( TradeDownloadKondorMessageBuilderC.AMOUNT_FORMAT_STRING ).format( fxLeg.getFXPayment().getCurrency2Amount() );
            String amt1ForTaker = getKondorValue( takerSTPMessage, TradeDownloadKondorMessageBuilderC.AMT_1 );
            String amt2ForTaker = getKondorValue( takerSTPMessage, TradeDownloadKondorMessageBuilderC.AMT_2 );
            String amt1ForMaker = getKondorValue( makerSTPMessage, TradeDownloadKondorMessageBuilderC.AMT_1 );
            String amt2ForMaker = getKondorValue( makerSTPMessage, TradeDownloadKondorMessageBuilderC.AMT_2 );
            log( "amt1ForTaker=" + amt1ForTaker );
            log( "amt2ForTaker=" + amt2ForTaker );
            log( "amt1ForMaker=" + amt1ForMaker );
            log( "amt2ForMaker=" + amt2ForMaker );

            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testIntraFloorKondorSTPDownload", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
        }
    }

    public void testIntraFloorKondorV20STPDownload() throws Exception
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.KONDOR_V20_FORMAT );
            takerOrg.setIntraFloorSTPFlag( Organization.INTRAFLOOR_TRADE_BOTH_DOWNLOAD );
            Trade trd = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setCounterpartyB( ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le2", LegalEntityC.class, null, null ) );
            trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );
            log( "displayFlipNeeded=" + CounterpartyUtilC.isDisplayOrderFlipNeeded( trd ) );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.KONDOR_V20_FORMAT + " : " + takerSTPMessage );
            log( "makerSTPMessage in format=" + TradeServiceConstants.KONDOR_V20_FORMAT + " : " + makerSTPMessage );
            String cptyRefValueForTaker = getKondorValue( takerSTPMessage, TradeDownloadKondorMessageBuilderC.CPTY_REF );
            log( "cptyRefValueForTaker" + cptyRefValueForTaker );
            assertEquals( "FI1-le2", cptyRefValueForTaker );

            String cptyRefValueForMaker = getKondorValue( makerSTPMessage, TradeDownloadKondorMessageBuilderC.CPTY_REF );
            log( "cptyRefValueForMaker" + cptyRefValueForMaker );
            assertEquals( "FI1-le1", cptyRefValueForMaker );

            //make sure that cptyName is not there in Kondor format as it is only available in Kondor+v20 format.
            String cptyNameValueForTaker = getKondorValue( takerSTPMessage, TradeDownloadKondorMessageBuilderC.CPTY_NAME );
            log( "cptyNameValueForTaker" + cptyNameValueForTaker );
            assertEquals( "FI1-le2", cptyNameValueForTaker );

            String cptyNameValueForMaker = getKondorValue( makerSTPMessage, TradeDownloadKondorMessageBuilderC.CPTY_NAME );
            log( "cptyNameValueForMaker" + cptyNameValueForMaker );
            assertEquals( "FI1-le1", cptyNameValueForMaker );

            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testIntraFloorKondorV20STPDownload", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
        }
    }
}
  
