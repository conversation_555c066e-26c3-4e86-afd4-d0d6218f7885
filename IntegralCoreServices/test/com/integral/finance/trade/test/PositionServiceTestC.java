package com.integral.finance.trade.test;

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.trade.TradeLegC;
import com.integral.finance.trade.facade.TradeStateFacadeC;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.DealingPTestCaseC;
import com.integral.time.IdcDate;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;

import java.util.ArrayList;
import java.util.List;

public class PositionServiceTestC extends DealingPTestCaseC
{
    public PositionServiceTestC( String name )
    {
        super( name );
    }

    public void testTradesLookupPerformance()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            IdcDate currentTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();

            ReportQuery reportQuery = new ReportQuery( eb );
            reportQuery.setReferenceClass( TradeLegC.class );
            reportQuery.useCollectionClass( ArrayList.class );

            reportQuery.addItem( "buyingcrnc1", eb.getField( "IDCTRDLEG.BUYINGCRNC1" ) );
            reportQuery.addItem( "sumCrnc1Amt", eb.getField( "IDCTRDLEG.CRNC1AMT" ) );
            reportQuery.addItem( "sumCrnc2Amt", eb.getField( "IDCTRDLEG.CRNC2AMT" ) );
            reportQuery.addItem( "currency1", eb.getField( "IDCTRDLEG.CRNC1ID" ) );
            reportQuery.addItem( "currency2", eb.getField( "IDCTRDLEG.CRNC2ID" ) );
            reportQuery.addItem( "counterpartyA", eb.get( "containingTrade" ).get( "counterpartyA" ).get( NamedEntity.ShortName ) );
            reportQuery.addItem( "counterpartyB", eb.get( "containingTrade" ).get( "counterpartyB" ).get( NamedEntity.ShortName ) );
            reportQuery.addItem( "makerUser", eb.get( "containingTrade" ).get( "makerUser" ).get( NamedEntity.ShortName ) );
            reportQuery.addItem( "entryUser", eb.get( "containingTrade" ).get( "entryUser" ).get( NamedEntity.ShortName ) );
            reportQuery.addItem( "valueDate", eb.getField( "IDCTRDLEG.VALUEDATE" ) );

            Expression activeExp = eb.get( "containingTrade" ).get( "status" ).equal( Entity.ACTIVE_STATUS );
            Expression tradeDateExp = eb.getField( "IDCTRDLEG.VALUEDATE" ).greaterThanEqual( currentTradeDate.asSqlDate() );

            Expression confirmedTrdExp = eb.get( "containingTrade" ).get( "state" ).equal( TradeStateFacadeC.CONFIRMED_STATE ).
                    or( eb.get( "containingTrade" ).get( "state" ).equal( TradeStateFacadeC.VERIFIED_STATE ) ).
                    or( eb.get( "containingTrade" ).get( "state" ).equal( TradeStateFacadeC.NET_STATE ) ).
                    or( eb.get( "containingTrade" ).get( "state" ).equal( TradeStateFacadeC.MANUAL_STATE ) );


            Expression finalExpr = activeExp.and( tradeDateExp ).and( confirmedTrdExp );
            reportQuery.setSelectionCriteria( finalExpr );

            long t0 = System.currentTimeMillis();
            List<ReportQueryResult> results = ( List ) PersistenceFactory.newSession().executeQuery( reportQuery );
            long t1 = System.currentTimeMillis();
            log( "Count is " + results.size() + ",timeTaken=" + ( t1 - t0 ) );
            assertEquals( "Should not take more than 2000 ms=", ( t1 - t0 ) < 2000, true );
        }
        catch ( Exception e )
        {
            fail( "testTradesLookupPerformance", e );
        }
    }
}
