package com.integral.finance.trade.test;

import com.integral.finance.counterparty.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.dealing.ExecutionType;
import com.integral.finance.dealing.TimeInForce;
import com.integral.finance.dealing.test.util.DealingTestUtil;
import com.integral.finance.fx.FXBusinessCalendar;
import com.integral.finance.fx.FXBusinessCalendarC;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.instrument.InstrumentClassificationC;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.calculator.TradeDownloadFIXMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadKondorMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadTOFMessageBuilderC;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.finance.trade.TradeDownloadServiceC;
import com.integral.finance.trade.TradeService;
import com.integral.message.EntityReference;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.UserC;
import com.integral.util.IdcUtilC;
import com.integral.xml.binding.JavaXMLBinderFactory;
import electric.xml.Document;
import electric.xml.Element;
import electric.xml.Elements;
import electric.xml.XPath;
import org.eclipse.persistence.sessions.UnitOfWork;
import quickfix.Field;
import quickfix.Message;
import quickfix.field.BodyLength;
import quickfix.field.CheckSum;
import quickfix.field.MaturityDate;
import quickfix.field.MsgSeqNum;
import quickfix.field.SendingTime;
import quickfix.field.TradeReportID;
import quickfix.field.TransactTime;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class TradeDownloadServiceTestC extends TradeServiceTestC
{

    public TradeDownloadServiceTestC( String name ) throws Exception
    {
        super( name );
    }

    public void testCurrencySwap() throws Exception
    {
        Currency eur = CurrencyFactory.getCurrency( "EUR" );
        Currency usd = CurrencyFactory.getCurrency( "USD" );
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
            msg.setObject( spotTrade );
            eur.setAlias( "ABC" );
            usd.setAlias( "XYZ" );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_BASE_CCY ) ).getTextString(), "ABC" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_VAR_CCY ) ).getTextString(), "XYZ" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_DEALT_CURRENCY ) ).getTextString(), "ABC" );

            eur.setAlias( "DEF" );
            usd.setAlias( "GBI" );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_BASE_CCY ) ).getTextString(), "DEF" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_VAR_CCY ) ).getTextString(), "GBI" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_DEALT_CURRENCY ) ).getTextString(), "DEF" );
        }
        finally
        {
            IdcUtilC.refreshObject( eur );
            IdcUtilC.refreshObject( usd );
        }
    }

    public void testDownloadSpotTradeCounterpartyASellsBaseFinXMLFormatCurrencySwap()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", true, TimeInForce.DAY, ExecutionType.CROSS, 100, 1000 );
    }

    public void testRequestDownload() throws Exception
    {
        StringWriter writer = new StringWriter( 1024 );
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        FXSingleLeg trade = prepareSingleLegTrade( 100.00, true, true, "EUR/USD", makerOrg, makerTpForTaker, 1.2334, spotDate );
        prepareSingleLegRequest( trade );
        TradeCloneServiceC.createCptyTrades( trade );

        WorkflowMessage msg2 = MessageFactory.newWorkflowMessage();
        msg2.setEventName( TradeService.VERIFY_EVENT );
        msg2.setTopic( TradeService.MESSAGE_TOPIC );
        msg2.setProperty( "WORKFLOW_CODE", "2" );
        msg2.setObject( trade );
        msg2.setProperty( "WORKFLOW_CODE_ARG", "TEST" );
        ts.verifyTrade( trade, msg2 );

        WorkflowMessage msg3 = MessageFactory.newWorkflowMessage();
        msg3.setObject( trade.getRequest() );
        EntityReference objRef = ( EntityReference ) msg3.getObjectReference();
        objRef.setReferenceOnly( false );

        JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( writer, msg3, "IntegrationServer" );
        log( "************$$$$$$$$$$$$$$$ " + writer );
        tx.release();

    }

    public void testDownloaSpotTradeCounterpartyABuysBaseFinXMLFormat()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000 );
    }

    public void testDownloadSpotTradeCounterpartyABuysTermFinXMLFormat()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "USD", "EUR", true, TimeInForce.GTC, ExecutionType.LIFT, 10000, 1000000 );
    }

    public void testDownloadSpotTradeCounterpartyASellsTermFinXMLFormat()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "USD", "EUR", false, TimeInForce.GTD, ExecutionType.CROSS, 100000, 10000000 );
    }

    public void testDownloadSpotTradeCounterpartyABuysBaseFinXMLFormatNDF()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000, true );
    }

    public void testDownloadSpotTradeCounterpartyASellsTermFIXFormat() throws Exception
    {
        downloadSpotTradeFIXFormat( EURUSD, "USD", false, false );
    }

    public void testDownloadSpotTradeCounterpartyABuysBaseFinXMLFormatWithMaskLP()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000, false, true );
    }

    //window forward tests
    public void testDownloadWindowFowardTradeCounterpartyABuysBaseFinXMLFormat()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000, false, false, true );
    }

    public void testDownloadWindowFowardTradeCounterpartyABuysTermFinXMLFormat()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "USD", "EUR", true, TimeInForce.GTC, ExecutionType.LIFT, 10000, 1000000, false, false, true );
    }

    public void testDownloadWindowFowardTradeCounterpartyASellsTermFinXMLFormat()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "USD", "EUR", false, TimeInForce.GTD, ExecutionType.CROSS, 100000, 10000000, false, false, true );
    }

    public void testDownloadWindowFowardTradeCounterpartyABuysBaseFinXMLFormatWithMaskLP()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000, false, true, true );
    }

    //bug 45298
    public void testFinXMLReplaceCptyShortnameWithSettlementCode() throws Exception
    {
        TradingParty tpAInBNS = null;
        try
        {
            Trade trade = downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", true, TimeInForce.DAY, ExecutionType.CROSS, 100, 1000 );
            ( TradeConfigurationFactory.getTradeConfigurationMBean() ).setProperty( "IDC.STP.FinXMLV20.UseSettlementCode." +
                    trade.getCounterpartyB().getOrganization().getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

            log( "&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&& STARTS NOW ******** " );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage();
            wm.setObject( trade );
            wm.setProperty( "organization", trade.getCounterpartyB().getOrganization() );
            trade.setCptyTrade( trade.getCptyTrade( 'B' ) );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm, null );
            xml = applyStleSheetOnXML( xml, "FinXML51v20" );
            Document xmlDoc = new Document( xml );
            tpAInBNS = CounterpartyUtilC.getTradingParty( trade.getCounterpartyA(), trade.getCounterpartyB().getOrganization() );
            log( "ABCD are " + trade.getCounterpartyA().getObjectID() + "," + trade.getCounterpartyB().getObjectID()
                    + "," + trade.getCounterpartyC().getObjectID() + "," + trade.getCounterpartyD().getObjectID() );

            // counterparties should CHANGE ONLY IF Settlement code is also defined.. just setting property is not enough
            assertEquals( xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/counterpartyB/referenceId" ) ).getLong(), trade.getCounterpartyB().getObjectID() );
            //assertNotSame(  xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/counterpartyA/referenceId" ) ).getLong(), trade.getCounterpartyA().getObjectID());
            assertEquals( xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/counterpartyA/referenceId" ) ).getLong(), trade.getCounterpartyA().getObjectID() );
            assertEquals( xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/counterpartyC/referenceId" ) ).getLong(), trade.getCounterpartyC().getObjectID() );
            assertEquals( xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/counterpartyD/referenceId" ) ).getLong(), trade.getCounterpartyD().getObjectID() );

            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( "deepaksinghal" );
            tpAInBNS.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm, null );
            xml = applyStleSheetOnXML( xml, "FinXML51v20" );
            xmlDoc = new Document( xml );
            extId.setName( "" );

            assertEquals( xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/counterpartyA/shortName" ) ).getTextString(), "deepaksinghal" );
        }
        catch ( Exception e )
        {
            fail( "testFinXMLReplaceCptyShortnameWithSettlementCode", e );
        }
        finally
        {
            if ( tpAInBNS != null )
            {
                IdcUtilC.refreshObject( tpAInBNS );
            }
        }
    }


    public void testFXRateBasisXMLMapping()
    {
        FXRateBasis fxRateBasis = stdQuoteConv.getFXRateBasis( "EUR/USD" );
        try
        {
            fxRateBasis.setNDF( true );
            InstrumentClassification clsf = new InstrumentClassificationC();
            clsf.setShortName( "METAL" );
            clsf.setLongName( "Metals" );
            FXBusinessCalendar busCal = new FXBusinessCalendarC();
            busCal.setLag( 2 );
            busCal.getHolidayCalendars().add( namedEntityReader.execute( HolidayCalendar.class, "NYC" ) );
            busCal.getHolidayCalendars().add( namedEntityReader.execute( HolidayCalendar.class, "TAR" ) );
            fxRateBasis.getBaseCurrency().setInstrumentClassification( clsf );
            fxRateBasis.setFixingBusinessCalendar( busCal );
            String convertedXML = convertToXML( fxRateBasis, "TradeDownload" );
            log.debug( "convertedXML=" + convertedXML );
            convertedXML = applyStleSheetOnXML( convertedXML, "FinXML51v20" );
            Document xmlDoc = new Document( convertedXML );
            Element element = xmlDoc.getElement( new XPath( "fxRateBasis/ndf" ) );
            boolean ndf = Boolean.valueOf( element.getTextString() );
            String type = xmlDoc.getElement( new XPath( "fxRateBasis/type" ) ).getTextString();
            String fixBusCal = xmlDoc.getElement( new XPath( "fxRateBasis/fixingBusinessCalendar/holidayCalendar" ) ).getTextString();
            log.debug( "ndf=" + ndf + ",type=" + type + ",fixBusCal=" + fixBusCal );
            assertEquals( "ndf should be true", ndf, true );
            assertEquals( "type should be metal", "METAL", type );
            assertEquals( "holiday calendar should be NYC", "NYC", fixBusCal );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            IdcUtilC.refreshObject( stdQuoteConv );
            IdcUtilC.refreshObject( fxRateBasis );
        }
    }

    //if custom fields present on FXSwap; they should not appear in STP Download
    public void test37985() throws Exception
    {
        Trade trade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
        trade.putCustomField( "TEST1", "VALUE1" );
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setObject( trade );
        wm.setProperty( "organization", trade.getCounterpartyA().getOrganization() );
        String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm, null );
        log.debug( "xml message is=" + xml );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );
        Document xmlDoc = new Document( xml );
        assertEquals( "Custom field should not be present in STP Download", xmlDoc.getElement( new XPath( "/workflowMessage/fxSwap/customField[@key='TEST1']" ) ), null );
    }

    public void test42171() throws Exception
    {
        try
        {
            Trade trade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage();
            wm.setObject( trade );
            wm.setProperty( "organization", trade.getCounterpartyA().getOrganization() );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm, null );
            xml = applyStleSheetOnXML( xml, "FinXML51v20" );
            Document xmlDoc = new Document( xml );
            assertEquals( "note should not be present", xmlDoc.getElement( new XPath( "/workflowMessage/fxSwap/note" ) ), null );
            Elements coverRateSpreads = xmlDoc.getElements( new XPath( XPATH_SINGLE_LEG_COVER_RATE_SPREADS ) );
            while ( coverRateSpreads.hasMoreElements() )
            {
                Element spread = ( Element ) coverRateSpreads.nextElement();
                String nameAttribute = spread.getAttribute( "name" );
                String value = spread.getTextString();
                log.debug( "spreadName=" + nameAttribute + ",value=" + value );
            }

            trade.setNote( "TEST" );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm, null );
            xml = applyStleSheetOnXML( xml, "FinXML51v20" );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( "/workflowMessage/fxSwap/note" ) ).getTextString(), "TEST" );
        }
        catch ( Exception e )
        {
            fail( "test42171", e );
        }
    }

    public void testTradingPartyDownloadFormatOverride()
    {
        IdcTransaction tx = null;
        TradingParty tp = null;
        try
        {
            tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, lpOrg, lpTpForMaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            ts.newTrade( spotTrade, null );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setObject( spotTrade );
            msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
            msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
            msg.setTopic( TradeService.MESSAGE_TOPIC );

            tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
            tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
            tp.setStpDownloadFormat( TradeService.DOWNLOAD_FORMAT_FIX );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FinXML51v20_Complex_New" );
            WorkflowMessage out = ts.process( msg );
            String stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "8=FIX" ) >= 0, true ); // if stp format is fix in tp level, then it should be fix.

            tp.setSTPEnabled( Counterparty.YES_STP_DOWNLOAD_ENABLED );
            tp.setStpDownloadFormat( TradeService.DOWNLOAD_FORMAT_FIX );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FinXML51v20_Complex_New" );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.startsWith( "8=FIX" ), true ); // if stp enabled is YES; should take format from TP  and not from org

            tp.setSTPEnabled( Counterparty.YES_STP_DOWNLOAD_ENABLED );
            tp.setStpDownloadFormat( "" );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FinXML51v20_Complex_New" );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "<workflowMessage>" ) >= 0, true ); // if stp enabled is YES; should take format from org if value is not specified on tp level

            tp.setSTPEnabled( Counterparty.YES_STP_DOWNLOAD_ENABLED );
            tp.setStpDownloadFormat( "" );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "" );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage, null ); // if format is null at both org and tp level; value should be null
        }
        catch ( Exception e )
        {
            fail();
        }
        finally
        {
            tx.release(); // most important release the transaction because we do not want changes on TP above to go into DB
            if ( tp != null )
            {
                IdcUtilC.refreshObject( tp );
            }
        }
    }

    public void testOriginatingDetailsOverrideAtTradingParty() throws Exception
    {
        TradingParty tp = null;
        TradingParty origCpty = null;
        Organization orgA = null;
        try
        {
            IdcTransaction tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            tx.commit();
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setObject( spotTrade );
            spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
            orgA = spotTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", orgA );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FinXML51v20_Complex_New" );
            msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
            msg.setTopic( TradeService.MESSAGE_TOPIC );

            tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
            tp.setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

            origCpty = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
            spotTrade.setOriginatingCptyId( origCpty.getObjectID() );
            spotTrade.setOriginatingUserId( spotTrade.getEntryUser().getObjectID() );

            //*********************************************** YES AT TP . so use originating details specified at TradingParty ONLY ******************* //
            tp.setShowOrigCptyUserInSTP( Counterparty.YES_SHOW_ORIG );

            tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            WorkflowMessage out = ts.process( msg );
            String stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + origCpty.getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + origCpty.getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + origCpty.getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyA().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyA().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyA().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );
            /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyB().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getMakerUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyB().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getMakerUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyB().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getMakerUser().getShortName() + "</parameter>" ) > 0, true );

            //************* YES AT TP ENDS *****************************

            //*********************************************** YES AT TP . so use originating details specified at TradingParty ONLY ******************* //
            //*********************************************** DEFAULT AT TP . so use originating details specified at ORG ONLY ******************* //
            tp.setShowOrigCptyUserInSTP( Counterparty.DEFAULT_SHOW_ORIG );

            tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + origCpty.getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyA().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyB().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getMakerUser().getShortName() + "</parameter>" ) > 0, true );

            ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + origCpty.getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyA().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyB().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getMakerUser().getShortName() + "</parameter>" ) > 0, true );
            /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + origCpty.getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );
            tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyA().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getEntryUser().getShortName() + "</parameter>" ) > 0, true );

            tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( stpMessage.indexOf( "key=\"originatingCounterparty\">" + spotTrade.getCounterpartyB().getShortName() + "</parameter>" ) > 0 && stpMessage.indexOf( "key=\"originatingUser\">" + spotTrade.getMakerUser().getShortName() + "</parameter>" ) > 0, true );
            //*********************************************** DEFAULT AT TP  ENDS. so use originating details specified at ORG ONLY ******************* //
        }
        finally
        {
            if ( orgA != null )
            {
                IdcUtilC.refreshObject( orgA );
            }
            if ( tp != null )
            {
                IdcUtilC.refreshObject( tp );
            }
            if ( origCpty != null )
            {
                IdcUtilC.refreshObject( origCpty );
            }
        }
    }


    public void testTradingPartyDownloadFormatQueueOverride()
    {
        IdcTransaction tx = null;
        TradingParty tp = null;
        try
        {
            tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, lpOrg, lpTpForMaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            ts.newTrade( spotTrade, null );
            spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );

            //test cases for queue. as we cannot ascertain using the test cases which queue it gets downloaded ; creating test cases for method only.
            TradeDownloadServiceC trdDownload = new TradeDownloadServiceC();


            tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
            tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
            tp.setStpDownloadQueue( "TradingPartyQueue" );
            ( ( ExternalSystem ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).setInDestination( "OrganizationQueue" );
            String finalQueue = trdDownload.getDownloadDestination( spotTrade.getCounterpartyA().getOrganization(), spotTrade );
            assertEquals( finalQueue, "TradingPartyQueue" ); // if stp enabled is default; should take format from org and not from TP

            tp.setSTPEnabled( Counterparty.YES_STP_DOWNLOAD_ENABLED );
            tp.setStpDownloadQueue( "TradingPartyQueue" );
            ( ( ExternalSystem ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).setInDestination( "OrganizationQueue" );
            finalQueue = trdDownload.getDownloadDestination( spotTrade.getCounterpartyA().getOrganization(), spotTrade );
            assertEquals( finalQueue, "TradingPartyQueue" ); // if stp enabled is default; should take format from org and not from TP

            tp.setSTPEnabled( Counterparty.YES_STP_DOWNLOAD_ENABLED );
            tp.setStpDownloadQueue( "" );
            ( ( ExternalSystem ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).setInDestination( "OrganizationQueue" );
            finalQueue = trdDownload.getDownloadDestination( spotTrade.getCounterpartyA().getOrganization(), spotTrade );
            assertEquals( finalQueue, "OrganizationQueue" ); // if stp enabled is default; should take format from org and not from TP
        }
        catch ( Exception e )
        {
            fail();
        }
        finally
        {
            tx.release(); // most important release the transaction because we do not want changes on TP above to go into DB
            if ( tp != null )
            {
                IdcUtilC.refreshObject( tp );
            }
        }
    }


    public void testTradingPartySTPEnabledOverride()
    {
        IdcTransaction tx = null;
        TradingParty tp = null;
        try
        {
            tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, lpOrg, lpTpForMaker, bidRates[0], spotDate.addDays( 2 ) );
            LegalEntity legalEntity = CounterpartyUtilC.getLegalEntity( spotTrade.getCounterpartyA() );
            prepareSingleLegRequest( spotTrade );
            ts.newTrade( spotTrade, null );
            spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );


            tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );

            tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadMode", "ii" );
            assertEquals( ts.doDownload( spotTrade.getCounterpartyA().getOrganization(), spotTrade, tp, legalEntity, false ), Boolean.FALSE );

            tp.setSTPEnabled( Counterparty.YES_STP_DOWNLOAD_ENABLED );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadMode", "ii" );
            assertEquals( ts.doDownload( spotTrade.getCounterpartyA().getOrganization(), spotTrade, tp, legalEntity, false ), Boolean.FALSE );

            tp.setSTPEnabled( Counterparty.NO_STP_DOWNLOAD_ENABLED );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadMode", "ii" );
            assertEquals( ts.doDownload( spotTrade.getCounterpartyA().getOrganization(), spotTrade, tp, legalEntity, false ), Boolean.FALSE );

            tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadMode", "Automatic" );
            assertEquals( ts.doDownload( spotTrade.getCounterpartyA().getOrganization(), spotTrade, tp, legalEntity, false ), Boolean.TRUE );

            tp.setSTPEnabled( Counterparty.NO_STP_DOWNLOAD_ENABLED );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadMode", "Automatic" );
            assertEquals( ts.doDownload( spotTrade.getCounterpartyA().getOrganization(), spotTrade, tp, legalEntity, false ), Boolean.FALSE );

            tp.setSTPEnabled( Counterparty.YES_STP_DOWNLOAD_ENABLED );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadMode", "Automatic" );
            assertEquals( ts.doDownload( spotTrade.getCounterpartyA().getOrganization(), spotTrade, tp, legalEntity, false ), Boolean.TRUE );

        }
        catch ( Exception e )
        {
            fail();
        }
        finally
        {
            tx.release(); // most important release the transaction because we do not want changes on TP above to go into DB
            if ( tp != null )
            {
                IdcUtilC.refreshObject( tp );
            }
        }
    }

    public void downloadSpotTradeFIXFormat( String ccyPair, String takerDltCcy, boolean isBid, boolean ndf ) throws Exception
    {
        init( fiUser );
        IdcTransaction tx = initTransaction();
        tx.getUOW().addReadOnlyClass( UserC.class );
        double amt = 1000;

        // do a trade where CptyA sells base currency.
        String baseCcy = DealingTestUtilC.getBaseCurrency( ccyPair );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, isBid, true, baseCcy.equals( takerDltCcy ), ccyPair, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ),
                ndf ? spotDate.addDays( 2 ) : null, ndf ? Tenor.SPOT_TENOR : null );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        msg.setObject( spotTrade );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        String output = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( msg, null );
        log.debug( output );
        tx.commit();
        if ( ndf )
        {
            List<String> fixingDateList = getFIXValue( output, Integer.toString( MaturityDate.FIELD ) );
            assertEquals( fixingDateList.get( 0 ), spotDate.addDays( 2 ).getFormattedDate( IdcDate.YYYY_MM_DD ) );
        }
        else
        {
            List<String> fixingDateList = getFIXValue( output, Integer.toString( MaturityDate.FIELD ) );
            assertTrue( fixingDateList.isEmpty() );
        }
    }

    public void testSalesDealerDownload() throws Exception
    {
        init( fiUser );
        IdcTransaction tx = initTransaction();
        tx.getUOW().addReadOnlyClass( UserC.class );
        double amt = 1000;

        // do a trade where CptyA sells base currency.
        String baseCcy = DealingTestUtilC.getBaseCurrency( EURUSD );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, false, true, baseCcy.equals( "EUR" ), EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ),
                false ? spotDate.addDays( 2 ) : null, false ? Tenor.SPOT_TENOR : null );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        spotTrade.setSalesDealerCounterparty( spotTrade.getCounterpartyB() );
        Organization downloadOrg = spotTrade.getCounterpartyB().getOrganization();
        downloadOrg.setIncludeSDInfo( true );
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setProperty( "organization", downloadOrg );
        msg.setObject( spotTrade );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );
        Document xmlDoc = new Document( xml );
        String sdShortName = xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/salesDealer/shortName" ) ).getTextString();
        assert ( sdShortName != null );
        downloadOrg.setIncludeSDInfo( false );
        xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );
        xmlDoc = new Document( xml );
        sdShortName = null;
        if ( xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/salesDealer/shortName" ) ) != null )
        {
            sdShortName = xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/salesDealer/shortName" ) ).getTextString();
        }
        assertNull( sdShortName );
        tx.commit();
    }

    public void testIntraFloorTrade39186() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();
        deleteAllDealingData( uow );
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        trd.setCounterpartyB( trd.getCounterpartyA() );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        //((LegalEntity)trd.getCounterpartyA()).getOrganization().setIntraFloorTradeSingleDownload( false);
        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );

        tx.commit();
    }

    public void testIntraFloorAllSTPFormats() throws Exception
    {
        try
        {
            IdcTransaction tx;
            Trade trd;
            UnitOfWork uow;
            tx = initTransaction();
            uow = tx.getUOW();
            deleteAllDealingData( uow );
            tx = initTransaction();
            uow = tx.getUOW();

            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setCounterpartyB( trd.getCounterpartyA() );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setProperty( "organization", trd.getCounterpartyA().getOrganization() );
            msg.setObject( trd );
            msg.setEventName( TradeService.VERIFY_EVENT );
            trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
            String output = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( msg, null );
            log.debug( output );
            output = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log.debug( output );
            output = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );
            log.debug( output );
            output = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( msg, null );
            log.debug( output );

            // specifically test v25 format.
            // enable stp download for taker org.
            ExternalSystem es = new ExternalSystemC();
            es.setInDestination( "TestQueue" );
            es.putCustomField( CounterpartyUtilC.DOWNLOAD_FORMAT, TradeServiceConstants.FinXML51v25_FORMAT );
            es.putCustomField( CounterpartyUtilC.DOWNLOAD_MODE, CounterpartyUtilC.AUTO_DOWNLOAD );
            takerOrg.putCustomField( CounterpartyUtilC.DOWNLOAD_EXTERNAL_SYSTEM, es );
            output = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( msg, null );
            log.debug( output );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testIntraFloorAllSTPFormats", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
        }
    }

    public void testIntraFloorSTPFlags() throws Exception
    {
        try
        {
            IdcTransaction tx;
            UnitOfWork uow;
            tx = initTransaction();
            uow = tx.getUOW();
            deleteAllDealingData( uow );
            tx = initTransaction();
            uow = tx.getUOW();

            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            // enable stp download for taker org.
            ExternalSystem es = new ExternalSystemC();
            es.setInDestination( "TestQueue" );
            es.putCustomField( CounterpartyUtilC.DOWNLOAD_FORMAT, TradeServiceConstants.FinXML51v25_FORMAT );
            es.putCustomField( CounterpartyUtilC.DOWNLOAD_MODE, CounterpartyUtilC.AUTO_DOWNLOAD );
            takerOrg.putCustomField( CounterpartyUtilC.DOWNLOAD_EXTERNAL_SYSTEM, es );

            // set the intrafloor trade stp flag to no download
            takerOrg.setIntraFloorSTPFlag( Organization.INTRAFLOOR_TRADE_NO_DOWNLOAD );
            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setCounterpartyB( trd1.getCounterpartyA() );
            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );

            // set the intrafloor stp flag to both download
            takerOrg.setIntraFloorSTPFlag( Organization.INTRAFLOOR_TRADE_BOTH_DOWNLOAD );
            Trade trd2 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd2.setCounterpartyB( trd2.getCounterpartyA() );
            prepareSingleLegRequest( ( FXSingleLeg ) trd2 );
            ts.newTrade( trd2, null );
            ts.verifyTrade( trd2, null );

            // set the intrafloor stp flag to taker download
            takerOrg.setIntraFloorSTPFlag( Organization.INTRAFLOOR_TRADE_TAKER_DOWNLOAD );
            Trade trd3 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd3.setCounterpartyB( trd3.getCounterpartyA() );
            prepareSingleLegRequest( ( FXSingleLeg ) trd3 );
            ts.newTrade( trd3, null );
            ts.verifyTrade( trd3, null );


            // set the intrafloor stp flag to maker download
            takerOrg.setIntraFloorSTPFlag( Organization.INTRAFLOOR_TRADE_MAKER_DOWNLOAD );
            Trade trd4 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd4.setCounterpartyB( trd4.getCounterpartyA() );
            prepareSingleLegRequest( ( FXSingleLeg ) trd4 );
            ts.newTrade( trd4, null );
            ts.verifyTrade( trd4, null );
            tx.release();

            //todo add mechanism to
        }
        catch ( Exception e )
        {
            fail( "testIntraFloorSTPFlags", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
        }
    }


    public void testTOF() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();
        deleteAllDealingData( uow );
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setProperty( "organization", trd.getCounterpartyA().getOrganization() );
        msg.setObject( trd );
        msg.setEventName( TradeService.VERIFY_EVENT );
        trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String output = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );

        assertEquals( getTOFValue( output, "508" ), trd.getCounterpartyB().getOrganization().getShortName() );
        assertEquals( getTOFValue( output, "509" ), trd.getCounterpartyB().getOrganization().getLongName() );
        assertEquals( getTOFValue( output, "514" ), "2" );
        assertEquals( getTOFValue( output, "504" ), trd.getEntryUser().getShortName() );

        log.debug( output );
        msg.setProperty( "organization", trd.getCounterpartyB().getOrganization() );
        trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        output = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );
        assertEquals( getTOFValue( output, "508" ), trd.getCounterpartyA().getOrganization().getShortName() );
        assertEquals( getTOFValue( output, "509" ), trd.getCounterpartyA().getOrganization().getLongName() );
        assertEquals( getTOFValue( output, "514" ), "1" );
        assertEquals( getTOFValue( output, "504" ), trd.getMakerUser().getShortName() );

        // Check SPOT classification
        assertEquals( getTOFValue( output, "569" ), "2" );
        // Check NDF fields are absent.
        assertEquals( getTOFValue( output, "554" ), "" );

        log.debug( output );

        tx.release();
    }

    public void testTOFTradeNDF() throws Exception
    {
        try
        {
            IdcTransaction tx;
            Trade trd;
            UnitOfWork uow;
            tx = initTransaction();
            uow = tx.getUOW();
            deleteAllDealingData( uow );
            tx = initTransaction();
            uow = tx.getUOW();

            makerOrg.setSendSpreadsInSTP( true );

            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            IdcDate fixingDate = spotDate.addDays( 2 );
            String fixingDateStr = fixingDate.getFormattedDate( IdcDate.DD_MMM_YYYY_SPACE ).toUpperCase();
            trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate, fixingDate, Tenor.SPOT_TENOR );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setProperty( "organization", trd.getCounterpartyB().getOrganization() );
            msg.setObject( trd );
            msg.setEventName( TradeService.VERIFY_EVENT );
            trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
            String output = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );

            log.debug( output );
            assertEquals( getTOFValue( output, "554" ), fixingDateStr );
            assertEquals( getTOFValue( output, "569" ), "128" );

            // check the spread values for the maker org.
            assertEquals( getTOFValue( output, "561" ), TradeDownloadTOFMessageBuilderC.Base_Rate );
            assertTrue( output.indexOf( "566" ) != -1 );

            tx.release();
        }
        finally
        {
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testDownloadNDFTradeCounterpartyABuysBaseFinXMLFormat()
    {
        downloadNDFTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000 );
    }

    public void testDownloadNDFTradeCounterpartyABuysTermFinXMLFormat()
    {
        downloadNDFTradeFinXmlFormat( EURUSD, "USD", "EUR", true, TimeInForce.GTC, ExecutionType.LIFT, 10000, 1000000 );
    }

    public void testDownloadNDFTradeCounterpartyASellsTermFinXMLFormat()
    {
        downloadNDFTradeFinXmlFormat( EURUSD, "USD", "EUR", false, TimeInForce.GTD, ExecutionType.CROSS, 100000, 10000000 );
    }

    public void testDownloadNDFTradeCounterpartyABuysBaseFinXMLFormatNDF()
    {
        downloadNDFTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000 );
    }

    public void testDownloadNDFTradeCounterpartyASellsTermFIXFormat() throws Exception
    {
        downloadSpotTradeFIXFormat( EURUSD, "USD", false, true );
    }

    public void testFIXMessageDownloads() throws Exception
    {
        doFIXMessageDownloads( EURUSD, "USD", false );
    }

    public void doFIXMessageDownloads( String ccyPair, String takerDltCcy, boolean isBid ) throws Exception
    {
        init( fiUser );
        double amt = 1000;

        // do a trade where CptyA sells base currency.
        String baseCcy = DealingTestUtilC.getBaseCurrency( ccyPair );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, isBid, true, baseCcy.equals( takerDltCcy ), ccyPair, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ),
                null, null );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        WorkflowMessage msg2 = MessageFactory.newWorkflowMessage();
        Organization org = spotTrade.getCounterpartyA().getOrganization();
        enableFIXDownload( org );
        msg.setProperty( "organization", org );
        msg2.setProperty( "organization", org );
        msg.setObject( spotTrade );
        msg2.setObject( spotTrade );

        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        String output = getSTPResendMessage( spotTrade, msg );

        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        String outputGenerated = getGeneratedMessage( spotTrade, msg2, org );

        log.debug( "downloaded message: " + output );
        log.debug( "generated message:  " + outputGenerated );
        assertTrue( output != null && outputGenerated != null );
        Message resendMessage = new Message( output );
        Message generatedMessage = new Message( outputGenerated );

        assertTrue( compareFIXMessages( resendMessage, generatedMessage ) );
        assertTrue( compareFIXMessages( generatedMessage, resendMessage ) );

        for ( CptyTrade cptyTrade : spotTrade.getCptyTrades() )
        {
            cptyTrade.setDownloadedMessage( null );
        }

        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        String outputGenerated2 = getGeneratedMessage( spotTrade, msg2, org );

        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        String output2 = getSTPResendMessage( spotTrade, msg );
        assertTrue( output2 != null && outputGenerated2 != null );

        resendMessage = new Message( output );
        generatedMessage = new Message( outputGenerated );
        assertTrue( compareFIXMessages( resendMessage, generatedMessage ) );
        assertTrue( compareFIXMessages( generatedMessage, resendMessage ) );
    }

    public void testCurrencyPairAlias() throws Exception
    {
        try
        {
            FXSingleLeg spotTrade = DealingTestUtil.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
            msg.setObject( spotTrade );

            WatchPropertyC.update(TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + takerOrg.getShortName(), "EUR/USD~ABC/DEF", ConfigurationProperty.DYNAMIC_SCOPE );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log.info( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_NAME ) ).getTextString(), "ABC/DEF" );

            WatchPropertyC.update(TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + takerOrg.getShortName(), "USD/JPY~sss;EUR/USD~GER30", ConfigurationProperty.DYNAMIC_SCOPE );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log.info( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_NAME ) ).getTextString(), "GER30" );

            WatchPropertyC.update(TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + takerOrg.getShortName(), "", ConfigurationProperty.DYNAMIC_SCOPE );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log.info( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_NAME ) ).getTextString(), "EUR/USD" );
        }
        finally
        {
            WatchPropertyC.update(TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + takerOrg.getShortName(), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public boolean compareFIXMessages( Message resendMessage, Message generatedMessage )
    {
        Iterator<Field<?>> iterator = resendMessage.iterator();
        Map<Integer, Object> fieldValues = new HashMap<Integer, Object>();
        while ( iterator.hasNext() )
        {
            Field field = iterator.next();
            log.debug( "adding field " + field.getTag() + ", value " + field.getObject() );
            fieldValues.put( field.getTag(), field.getObject() );
        }

        Iterator<Field<?>> generatedIterator = generatedMessage.iterator();
        while ( generatedIterator.hasNext() )
        {
            Field generatedField = generatedIterator.next();
            switch ( generatedField.getTag() )
            {
                case TransactTime.FIELD:
                case BodyLength.FIELD:
                case MsgSeqNum.FIELD:
                case SendingTime.FIELD:
                case CheckSum.FIELD:
                case TradeReportID.FIELD:
                    log.debug( "not checking field " + generatedField.getTag() );
                    break;
                default:
                    log.debug( "testing field " + generatedField.getTag() + ", value " + generatedField.getObject() + ",resendMessageValue=" + fieldValues.get( generatedField.getTag() ) );
                    assertTrue( fieldValues.get( generatedField.getTag() ).equals( generatedField.getObject() ) );
            }
        }
        return true;
    }

    public String getSTPResendMessage( FXSingleLeg trade, WorkflowMessage msg )
    {
        msg.setStatus( MessageStatus.SUCCESS );
        msg.setEvent( TradeServiceConstants.MSG_EVT_RESEND_EVENT );
        msg.setTopic( TradeServiceConstants.TOPIC_TRADE );
        msg.setSender( fiUser );
        msg.setObject( trade );
        msg.setParameterValue( TradeServiceConstants.FIX_TRADE_REQUEST_ID, "test_trade_request_id" );

        WorkflowMessage output = ts.process( msg );
        for ( CptyTrade cptyTrade : trade.getCptyTrades() )
        {
            if ( cptyTrade.getDownloadedMessage() != null )
            {
                return cptyTrade.getDownloadedMessage();
            }
        }
        return null;
    }

    public String getGeneratedMessage( FXSingleLeg trd, WorkflowMessage workflowMessage, Organization org )
    {
        workflowMessage.setStatus( MessageStatus.SUCCESS );
        workflowMessage.setTopic( TradeService.MESSAGE_TOPIC );
        workflowMessage.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
        workflowMessage.setParameterValue( TradeServiceConstants.DOWNLOAD_FORMAT_KEY, "FIX" );
        workflowMessage.setSender( fiUser );
        //workflowMessage.setProperty(FixConstants.WF_PARAM_ORGNAIZATION, workflowMessage.getSender().getOrganization());
        workflowMessage.setProperty( TradeServiceConstants.RESEND_ORGANIZATION_TO_KEY, org );
        workflowMessage.setParameterValue( TradeServiceConstants.FIX_TRADE_REQUEST_ID, "test_trade_request_id" );

        WorkflowMessage output = ts.process( workflowMessage );
        return ( String ) output.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
    }

}
  
