package com.integral.finance.trade.test;

import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventFacadeC;
import com.integral.facade.FacadeFactory;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.calculator.CalculatorFactory;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitWorkflowState;
import com.integral.finance.creditLimit.CreditLimitWorkflowStateFacadeC;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditEventFacade;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditEventFacadeC;
import com.integral.finance.dealing.ExecutionType;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.TimeInForce;
import com.integral.finance.dealing.facade.DealStateFacade;
import com.integral.finance.dealing.facade.DealStateFacadeC;
import com.integral.finance.dealing.facade.OrderStateFacadeC;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXSingleLegDealC;
import com.integral.finance.dealing.fx.FXSingleLegOrderC;
import com.integral.finance.dealing.fx.FXSwapDealC;
import com.integral.finance.dealing.fx.FXSwapOrderC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.finance.trade.TradeServiceC;
import com.integral.finance.trade.calculator.TradeDownloadFIXMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadKondorMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadTOFMessageBuilderC;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.configuration.TradeDownloadServiceConfiguration;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.facade.TradeStateFacadeC;
import com.integral.is.ISCommonConstants;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemC;
import com.integral.persistence.ExternalSystemFactory;
import com.integral.persistence.Namespace;
import com.integral.persistence.TransactionIdFacadeC;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.DealingPTestCaseC;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import electric.xml.Document;
import electric.xml.Element;
import electric.xml.Elements;
import electric.xml.XPath;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


public class TradeServiceTestC extends DealingPTestCaseC
{
    public TradeServiceC ts = new TradeServiceC();
    //IdcTransaction transaction;
    protected final static String TRADE_DOWNLOAD_MAPPING = "TradeDownload";

    //XPaths for various trade download information.
    protected final static String XPATH_SINGLE_LEG = "/workflowMessage/fxSingleLeg";
    protected final static String XPATH_SINGLE_LEG_LEGAL_ENTITY_BUYS_BASE = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/legalEntityBuysBase";
    protected final static String XPATH_SINGLE_LEG_CPTY_DEALT_CURRENCY = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/dealtCurrency";
    protected final static String XPATH_SINGLE_LEG_BASE_CURRENCY_AMOUNT = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/baseCurrencyAmount";
    protected final static String XPATH_SINGLE_LEG_TERM_CURRENCY_AMOUNT = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/termCurrencyAmount";
    protected final static String XPATH_SINGLE_LEG_CPTY_TRADE_LEGAL_ENTITY = "/workflowMessage/fxSingleLeg/cptyTrade/legalEntity";
    protected final static String XPATH_SINGLE_LEG_CPTY_TRADE_TRADING_PARTY = "/workflowMessage/fxSingleLeg/cptyTrade/tradingParty";
    protected final static String XPATH_SINGLE_LEG_TRADE_CLASSIFICATION = "/workflowMessage/fxSingleLeg/tradeClassification";
    protected final static String XPATH_FXSWAP_TRADE_CLASSIFICATION = "/workflowMessage/fxSwap/tradeClassification";
    protected final static String XPATH_TRADE_ORIGINATING_COUNTERPARTY = "/workflowMessage/parameter[@key='originatingCounterparty']";
    protected final static String XPATH_TRADE_ORIGINATING_USER = "/workflowMessage/parameter[@key='originatingUser']";
    protected final static String XPATH_TRADE_MAKER_USER = "/workflowMessage/fxSingleLeg/makerUser/shortName";
    protected final static String XPATH_SINGLE_LEG_CPTY_TRADE_LEGAL_ENTITY_USER = "/workflowMessage/fxSingleLeg/cptyTrade/legalEntityUser";
    protected final static String XPATH_SINGLE_LEG_CPTY_TRADE_TRADING_PARTY_USER = "/workflowMessage/fxSingleLeg/cptyTrade/tradingPartyUser";
    protected final static String XPATH_SINGLE_LEG_REQUEST_TIMEINFORCE = "/workflowMessage/entityProperty[@key='request']/request/timeInForce";
    protected final static String XPATH_SINGLE_LEG_REQUEST_EXECUTION_TYPE = "/workflowMessage/entityProperty[@key='request']/request/fxLegPrice/executionType";
    protected final static String XPATH_SINGLE_LEG_REQUEST_MIN_DEALT_AMOUNT = "/workflowMessage/entityProperty[@key='request']/request/fxLegPrice/minDealtAmount";
    protected final static String XPATH_SINGLE_LEG_REQUEST_MAX_SHOW_AMOUNT = "/workflowMessage/entityProperty[@key='request']/request/fxLegPrice/maxShowAmount";
    protected final static String XPATH_SINGLE_LEG_RATEBASIS_BASE_CCY = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fxRate/fxRateBasis/baseCurrency";
    protected final static String XPATH_SINGLE_LEG_BLOCK_TRADE = "/workflowMessage/fxSingleLeg";
    protected final static String XPATH_SINGLE_LEG_RATEBASIS_VAR_CCY = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fxRate/fxRateBasis/variableCurrency";
    protected final static String XPATH_SINGLE_LEG_RATEBASIS_CROSS_CCY = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fxRate/fxRateBasis/crossCurrency";
    protected final static String XPATH_SINGLE_LEG_COVER_RATE_SPREADS = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fxCoverRate/spread";
    protected final static String XPATH_SINGLE_LEG_FIXING_DATE = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fixingDate";
    protected final static String XPATH_SINGLE_LEG_FIXING_TENOR = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fixingTenor";
    protected final static String XPATH_SINGLE_LEG_RATEBASIS_NONDELIVERABLE = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fxRate/fxRateBasis/ndf";
    protected final static String XPATH_SINGLE_LEG_CPTYTRADE_STREAM_LP = "/workflowMessage/fxSingleLeg/cptyTrade/streamLP";
    protected final static String XPATH_SINGLE_LEG_COUNTERPARTYP_STREAM_LP = "/workflowMessage/fxSingleLeg/counterpartyB/streamLP";
    protected final static String XPATH_MESSAGE_ID = "/workflowMessage/messageId";
    protected final static String XPATH_SINGLE_LEG_USI = "/workflowMessage/fxSingleLeg/fxLeg/USI";
    protected final static String XPATH_SINGLE_LEG_EXECUTION_DATE_TIME = "/workflowMessage/fxSingleLeg/executionDateTime";
    protected final static String XPATH_SINGLE_LEG_RATEBASIS_NAME = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fxRate/fxRateBasis/name";
    protected final static String XPATH_SINGLE_LEG_START_DATE = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/startDate";
    protected final static String XPATH_SINGLE_LEG_START_TENOR = "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/startTenor";
    protected final static String XPATH_SINGLE_LEG_SUB_TRADE_TYPE = "/workflowMessage/fxSingleLeg/subTradeType";

    protected final static String IS_BLOCK_TRADE_KEY = "isBlockTrade";

    private static final String tradeDownloadKeySimpleOld = "TradeDownloadMessageBuilder_Simple_Old";
    private static final String tradeDownloadKeySimpleNew = "TradeDownloadMessageBuilder_Simple_New";
    private static final String tradeDownloadKeyComplexOld = "TradeDownloadMessageBuilder_Complex_Old";
    private static final String tradeDownloadKeyComplexNew = "TradeDownloadMessageBuilder_Complex_New";
    private static final String tradeTofDownloadKey = "SDKTOFTradeDownloadMessageBuilder";
    private static final String tradeKondorDownloadKey = "SDKKONDORTradeDownloadMessageBuilder";
    private static final String tradeFIXDownloadKey = "TradeDownloadMessageBuilder_FIX";
    protected static TradeConfigurationMBean tradeConfig = TradeConfigurationFactory.getTradeConfigurationMBean();
    public static final String RATE_FORMAT_STRING = "#.########";

    public TradeServiceTestC( String name )
    {
        super( name );
        try
        {
            tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            ts.setSendDownloadMessageEnabled( true );
            FacadeFactory.setFacade( TradeStateFacade.TRADE_STATE_FACADE, Trade.class, TradeStateFacadeC.class );
            FacadeFactory.setFacade( "fx_audit_FXTradeAuditEventFacade", FXSingleLegC.class, AuditEventFacadeC.class );
            FacadeFactory.setFacade( TransactionIdFacadeC.FACADE_NAME, TradeC.class, TransactionIdFacadeMock.class );
            FacadeFactory.setFacade( TransactionIdFacadeC.FACADE_NAME, com.integral.finance.dealing.RequestC.class, TransactionIdFacadeMock.class );
            FacadeFactory.setFacade( OrderStateFacadeC.FACADE_NAME, FXSingleLegOrderC.class, OrderStateFacadeC.class );
            FacadeFactory.setFacade( OrderStateFacadeC.FACADE_NAME, FXSwapOrderC.class, OrderStateFacadeC.class );
            FacadeFactory.setFacade( DealStateFacade.FACADE_NAME, FXSingleLegDealC.class, DealStateFacadeC.class );
            FacadeFactory.setFacade( DealStateFacade.FACADE_NAME, FXSwapDealC.class, DealStateFacadeC.class );
            FacadeFactory.setFacade( CreditLimitWorkflowStateFacadeC.class.getName(), CreditLimitWorkflowState.class, CreditLimitWorkflowStateFacadeC.class );
            FacadeFactory.setFacade( CreditLimitAdminAuditEventFacade.FACADE_NAME, AuditEvent.class, CreditLimitAdminAuditEventFacadeC.class );

            // register trade download calcs.

            //FinXML51v25
            CalculatorFactory.putCalculator( TradeServiceConstants.FinXML51v25_FORMAT, com.integral.finance.fx.FXSingleLegC.class, TradeDownloadMessageBuilderC.class );
            CalculatorFactory.putCalculator( TradeServiceConstants.FinXML51v25_FORMAT, com.integral.finance.fx.FXSwapC.class, TradeDownloadMessageBuilderC.class );

            //FinXML51v21
            CalculatorFactory.putCalculator( TradeServiceConstants.FinXML51v21_FORMAT, com.integral.finance.fx.FXSingleLegC.class, TradeDownloadMessageBuilderC.class );
            CalculatorFactory.putCalculator( TradeServiceConstants.FinXML51v21_FORMAT, com.integral.finance.fx.FXSwapC.class, TradeDownloadMessageBuilderC.class );

            //FinXML51
            CalculatorFactory.putCalculator( tradeDownloadKeyComplexOld, com.integral.finance.fx.FXSingleLegC.class, TradeDownloadMessageBuilderC.class );
            CalculatorFactory.putCalculator( tradeDownloadKeyComplexOld, com.integral.finance.fx.FXSwapC.class, TradeDownloadMessageBuilderC.class );
            //FinXML51V20
            CalculatorFactory.putCalculator( tradeDownloadKeyComplexNew, com.integral.finance.fx.FXSingleLegC.class, TradeDownloadMessageBuilderC.class );
            CalculatorFactory.putCalculator( tradeDownloadKeyComplexNew, com.integral.finance.fx.FXSwapC.class, TradeDownloadMessageBuilderC.class );
            //TOF
            CalculatorFactory.putCalculator( tradeTofDownloadKey, com.integral.finance.fx.FXSingleLegC.class, TradeDownloadTOFMessageBuilderC.class );
            CalculatorFactory.putCalculator( tradeTofDownloadKey, com.integral.finance.fx.FXSwapC.class, TradeDownloadTOFMessageBuilderC.class );
            //KONDOR+
            CalculatorFactory.putCalculator( tradeKondorDownloadKey, com.integral.finance.fx.FXSingleLegC.class, TradeDownloadKondorMessageBuilderC.class );
            CalculatorFactory.putCalculator( tradeKondorDownloadKey, com.integral.finance.fx.FXSwapC.class, TradeDownloadKondorMessageBuilderC.class );
            //KONDOR v20
            CalculatorFactory.putCalculator( TradeServiceConstants.KONDOR_V20_FORMAT, com.integral.finance.fx.FXSingleLegC.class, TradeDownloadKondorMessageBuilderC.class );
            CalculatorFactory.putCalculator( TradeServiceConstants.KONDOR_V20_FORMAT, com.integral.finance.fx.FXSwapC.class, TradeDownloadKondorMessageBuilderC.class );

            //FIX
            CalculatorFactory.putCalculator( tradeFIXDownloadKey, com.integral.finance.fx.FXSingleLegC.class, TradeDownloadFIXMessageBuilderC.class );
            CalculatorFactory.putCalculator( tradeFIXDownloadKey, com.integral.finance.fx.FXSwapC.class, TradeDownloadFIXMessageBuilderC.class );

            init( takerUser );
            //ts.getConfig().setEnableCredit( false );

            //disable stp download logging
            WatchPropertyC.update( TradeDownloadServiceConfiguration.STP_MESSAGES_LOGGING_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    protected String getExecutionTypeStr( int execType )
    {
        switch ( execType )
        {
            case ExecutionType.CROSS:
            {
                return "CROSS";
            }
            case ExecutionType.MATCH:
            {
                return "MATCH";
            }
            case ExecutionType.LIFT:
            {
                return "LIFT";
            }
        }
        return null;
    }

    //created otherwise while running test cases; it fails on this class saying that none test cases found
    public void testEmpty()
    {

    }

    protected String getTimeInForceStr( int timeInForce )
    {
        switch ( timeInForce )
        {
            case TimeInForce.DAY:
            {
                return "DAY";
            }
            case TimeInForce.GTC:
            {
                return "GTC";
            }
            case TimeInForce.OPG:
            {
                return "OPG";
            }
            case TimeInForce.IOC:
            {
                return "IOC";
            }
            case TimeInForce.FOK:
            {
                return "FOK";
            }
            case TimeInForce.GTX:
            {
                return "GTX";
            }
            case TimeInForce.GTD:
            {
                return "GTD";
            }
            case TimeInForce.GFS:
            {
                return "GFS";
            }
            case TimeInForce.ATC:
            {
                return "ATC";
            }
        }
        return null;
    }

    public void testIsBlockTradeWithNDF()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000, true );
    }

    public void testIsBlockTradeWithOutNDF()
    {
        downloadSpotTradeFinXmlFormat( EURUSD, "EUR", "USD", false, TimeInForce.FOK, ExecutionType.MATCH, 1000, 10000, false );
    }


    public void testTradeCounterparties()
    {
        Trade trade = prepareSingleLegTrade( 100.00, true, true, "EUR/USD", makerOrg, makerTpForTaker, 1.2334, spotDate );
        trade.setCounterpartyA( takerLe );
        trade.setCounterpartyB( makerLe );
        setCpty( trade );
        Counterparty cptyC = trade.getCounterpartyC();
        Counterparty cptyD = trade.getCounterpartyD();
        assertEquals( "cptyC", cptyC, takerTpForTakerPb );
        assertEquals( "cptyD", cptyD, makerTpForMakerPb );

        trade.setCounterpartyA( takerLe );
        trade.setCounterpartyB( takerTpForMaker );
        setCpty( trade );
        cptyC = trade.getCounterpartyC();
        cptyD = trade.getCounterpartyD();
        assertEquals( "cptyC", cptyC, takerTpForTakerPb );
        assertEquals( "cptyD", cptyD, makerTpForMakerPb );

        trade.setCounterpartyA( makerTpForTaker );
        trade.setCounterpartyB( takerTpForMaker );
        setCpty( trade );
        cptyC = trade.getCounterpartyC();
        cptyD = trade.getCounterpartyD();
        assertEquals( "cptyC", cptyC, takerTpForTakerPb );
        assertEquals( "cptyD", cptyD, makerTpForMakerPb );
    }

    public void testTakerMakerCpty()
    {
        Trade trade = prepareSingleLegTrade( 100.00, true, true, "EUR/USD", makerOrg, makerTpForTaker, 1.2334, spotDate );
        prepareSingleLegRequest( ( FXSingleLeg ) trade );

        // 38770: ideally cptyA and request.getCpty should be same. previously we were setting takerCpty from request.getCpty not from trd.getcptya hence the bug.
        // so now set them different; and check if takercpty is same as cptya
        trade.getRequest().setCounterparty( lpLe ); // set cpty on request something different than cptyA on trade
        ts.updateCounterparties( trade );
        assertEquals( CounterpartyUtilC.getLegalEntity( trade.getCounterpartyA() ), trade.getTakerCounterparty() );
        assertEquals( CounterpartyUtilC.getTradingParty( trade.getCounterpartyB(), CounterpartyUtilC.getLegalEntity( trade.getCounterpartyA() ).getOrganization() ), trade.getMakerCounterparty() );


    }

    private void setCpty( Trade trd )
    {
        TradingParty tpA = CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ), CounterpartyUtilC.getLegalEntity( trd.getCounterpartyA() ).getOrganization() );
        TradingParty tpB = CounterpartyUtilC.getTradingParty( CounterpartyUtilC.getLegalEntity( trd.getCounterpartyA() ), CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ).getOrganization() );
        trd.setCounterpartyC( tpA != null ? tpA.getPrimeBrokerTradingParty() : null );
        trd.setCounterpartyD( tpB != null ? tpB.getPrimeBrokerTradingParty() : null );
    }

    protected String getTOFValue( final String incomingMessage, String tag )
    {
        int index1 = incomingMessage.indexOf( TradeDownloadTOFMessageBuilderC.RS + tag + TradeDownloadTOFMessageBuilderC.US );
        if ( index1 != -1 )
        {
            return incomingMessage.substring( index1 + tag.length() + 1, incomingMessage.indexOf( TradeDownloadTOFMessageBuilderC.RS, index1 + tag.length() + 1 ) ).trim();

        }
        return "";
    }

    protected String getKondorValue( final String incomingMessage, String tag )
    {
        int index1 = incomingMessage.indexOf( tag );
        if ( index1 != -1 )
        {
            String str = incomingMessage.substring( index1 + tag.length() + 1 );
            return str.substring( 1, str.indexOf( TradeDownloadKondorMessageBuilderC.NEW_LINE ) - 1 );

        }
        return "";
    }


    protected List<String> getFIXValue( final String incomingMessage, String tag )
    {
        int index1 = incomingMessage.indexOf( '\u0001' + tag + "=" );
        List<String> out = new ArrayList<String>( 3 );
        while ( index1 != -1 )
        {
            out.add( incomingMessage.substring( index1 + tag.length() + 2, incomingMessage.indexOf( '\u0001', index1 + tag.length() + 2 ) ) );
            index1 = incomingMessage.indexOf( '\u0001' + tag + "=", index1 + 2 );
        }
        return out;
    }

    protected void setSTPFormat( Organization org, String format )
    {
        // enable stp download for taker org.
        ExternalSystem es = new ExternalSystemC();
        es.setInDestination( "TestQueue" );
        es.putCustomField( CounterpartyUtilC.DOWNLOAD_FORMAT, format );
        es.putCustomField( CounterpartyUtilC.DOWNLOAD_MODE, CounterpartyUtilC.AUTO_DOWNLOAD );
        org.putCustomField( CounterpartyUtilC.DOWNLOAD_EXTERNAL_SYSTEM, es );
    }

    protected void setSTPFormat( Counterparty cpty, String format, String destination )
    {
        cpty.setStpDownloadFormat ( format );
        cpty.setSTPEnabled ( Counterparty.YES_STP_DOWNLOAD_ENABLED );
        cpty.setStpDownloadQueue ( destination );
    }


    protected Collection<String> getAllDownloadFormats()
    {
        Collection<String> formats = new ArrayList<String>( 6 );
        formats.add( TradeServiceConstants.FinXML51v25_FORMAT );  // finxmlv25
        formats.add( TradeServiceConstants.FINXML51_COMPLEX_OLD );
        formats.add( TradeServiceConstants.FINXML51V20_COMPLEX_NEW );
        formats.add( TradeServiceConstants.FinXML51v21_FORMAT );
        formats.add( TradeServiceConstants.FINXML51 );
        formats.add( TradeServiceConstants.KONDOR_FORMAT );
        formats.add( TradeServiceConstants.KONDOR_V20_FORMAT );
        formats.add( TradeServiceConstants.FIX_FORMAT );
        formats.add( TradeServiceConstants.TOF_FORMAT );
        return formats;
    }

    protected Collection<String> getAllFinxmlDownloadFormats()
    {
        Collection<String> formats = new ArrayList<String>( 6 );
        formats.add( TradeServiceConstants.FinXML51v25_FORMAT );  // finxmlv25
        formats.add( TradeServiceConstants.FINXML51_COMPLEX_OLD );
        formats.add( TradeServiceConstants.FINXML51V20_COMPLEX_NEW );
        formats.add( TradeServiceConstants.FinXML51v21_FORMAT );
        formats.add( TradeServiceConstants.FINXML51 );
        return formats;
    }

    protected Trade downloadSpotTradeFinXmlFormat( String ccyPair, String takerDltCcy, String makerDltCcy, boolean isBid, int timeInForce, int execType, double minDealtAmt, double maxShowAmt )
    {
        return downloadSpotTradeFinXmlFormat( ccyPair, takerDltCcy, makerDltCcy, isBid, timeInForce, execType, minDealtAmt, maxShowAmt, false );
    }

    protected Trade downloadNDFTradeFinXmlFormat( String ccyPair, String takerDltCcy, String makerDltCcy, boolean isBid, int timeInForce, int execType, double minDealtAmt, double maxShowAmt )
    {
        return downloadSpotTradeFinXmlFormat( ccyPair, takerDltCcy, makerDltCcy, isBid, timeInForce, execType, minDealtAmt, maxShowAmt, true );
    }

    protected Trade downloadSpotTradeFinXmlFormat( String ccyPair, String takerDltCcy, String makerDltCcy, boolean isBid, int timeInForce, int execType, double minDealtAmt, double maxShowAmt, boolean isNDFTrade )
    {
        return downloadSpotTradeFinXmlFormat( ccyPair, takerDltCcy, makerDltCcy, isBid, timeInForce, execType, minDealtAmt, maxShowAmt, isNDFTrade, false );
    }

    protected Trade downloadWindowForwardTradeFinXmlFormat( String ccyPair, String takerDltCcy, String makerDltCcy, boolean isBid, int timeInForce, int execType, double minDealtAmt, double maxShowAmt, boolean isNDFTrade, boolean isShowMaskLP )
    {
        return downloadSpotTradeFinXmlFormat( ccyPair, takerDltCcy, makerDltCcy, isBid, timeInForce, execType, minDealtAmt, maxShowAmt, isNDFTrade, isShowMaskLP, false );
    }

    protected Trade downloadSpotTradeFinXmlFormat( String ccyPair, String takerDltCcy, String makerDltCcy, boolean isBid, int timeInForce, int execType, double minDealtAmt, double maxShowAmt, boolean isNDFTrade, boolean isShowMaskLP )
    {
        return downloadSpotTradeFinXmlFormat( ccyPair, takerDltCcy, makerDltCcy, isBid, timeInForce, execType, minDealtAmt, maxShowAmt, isNDFTrade, isShowMaskLP, false );
    }

    protected Trade downloadSpotTradeFinXmlFormat( String ccyPair, String takerDltCcy, String makerDltCcy, boolean isBid, int timeInForce, int execType, double minDealtAmt, double maxShowAmt, boolean isNDFTrade, boolean isShowMaskLP, boolean isWindowForward )
    {
        IdcTransaction tx = null;
        FXSingleLeg spotTrade = null;
        try
        {
            init( fiUser );
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            double amt = 1000;

            // do a trade where CptyA sells base currency.
            String baseCcy = DealingTestUtilC.getBaseCurrency( ccyPair );
            String termCcy = DealingTestUtilC.getTermCurrency( ccyPair );

            spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, isBid, true, baseCcy.equals( takerDltCcy ), ccyPair, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ),
                    isNDFTrade ? spotDate.addDays( 2 ) : null, isNDFTrade ? Tenor.SPOT_TENOR : null );
            spotTrade.getFXLeg().getFXPayment().getFXRate().setFXRateConvention( stdQuoteConv );
            spotTrade.setEntryUser( takerUser );
            spotTrade.setMakerUser( makerUser );

            // set maker order details.
            spotTrade.setMakerOrderDealtCurrency( makerDltCcy );

            // set masked LP
            if ( isShowMaskLP )
            {
                spotTrade.setMaskedLP( makerOrg.getShortName() );
            }

            //Set isSEF flag for NDF trades
            spotTrade.setSEF( isNDFTrade ? true : false );

            if ( isWindowForward )
            {
                spotTrade.setSubTradeType ( ISCommonConstants.WINDOW_FORWARD );
                spotTrade.getFXLeg ().getFXPayment ().setVariableTenor ( new Tenor ( "1M" ) );
                spotTrade.getFXLeg ().getFXPayment ().setVariableValueDate ( spotDate.addMonths ( 1 ) );
            }

            Request spotRequest = prepareSingleLegRequest( spotTrade );
            FXLegDealingPrice dp1 = ( FXLegDealingPrice ) spotRequest.getRequestPrice( FXLegDealingPrice.SINGLE_LEG );
            dp1.setExecutionType( execType );
            dp1.setMinDealtAmount( minDealtAmt );
            dp1.setMaxShowAmount( maxShowAmt );
            spotTrade.getFXLeg().getFXPayment().getFXRate().setFXRateBasis( stdQuoteConv.getFXRateBasis( ccyPair ) );
            spotTrade.setRequest( spotRequest );
            spotRequest.setCounterparty( takerLe );
            spotRequest.setUser( takerUser );
            spotRequest.setTimeInForce( timeInForce );
            spotRequest.setOriginatingCptyId( takerLe.getObjectId() );
            spotRequest.setOriginatingUserId( takerUser.getObjectID() );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage();
            wm.setObject( spotTrade );
            ts.newTrade( spotTrade, wm );
            commitTransaction( tx );

            // temporarily enable maker org for stp spreads
            makerOrg.setSendSpreadsInSTP( true );
            WatchPropertyC.update (TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP_PREFIX + makerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            // verify the trade.
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            wm = ts.verifyTrade( spotTrade, wm );
            ( ( FXSingleLeg ) wm.getObject() ).getFXLeg().getFXPayment().getFXRate().setFXRateConvention( stdQuoteConv );
            wm.setParameterValue( "tradeClassification", spotTrade.getTradeClassification().getShortName() );
            wm.setParameterValue( "originatingCounterparty", spotTrade.getOriginatingCounterparty().getShortName() );
            wm.setParameterValue( "originatingUser", spotTrade.getOriginatingUser().getShortName() );
            Collection<CptyTrade> cptyTrades = spotTrade.getCptyTrades();
            Trade trade = ( Trade ) wm.getRegisteredObject();
            LegalEntity cptyALe = ( ( TradingParty ) spotTrade.getCounterpartyA() ).getLegalEntity();
            LegalEntity cptyBLe = ( ( TradingParty ) spotTrade.getCounterpartyB() ).getLegalEntity();
            LegalEntity cptyCLe = ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntity();
            LegalEntity cptyDLe = ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntity();
            log.info( "cptyCle=" + cptyCLe + ",cptyDle=" + cptyDLe );
            int spreadedCptyTradeCounter = 0;
            for ( CptyTrade cptyTrade : cptyTrades )
            {
                log.info( "cptyTrade=" + cptyTrade );
                trade.setCptyTrade( cptyTrade );
                Organization downloadOrg = cptyTrade.getLegalEntity().getOrganization();
                wm.setProperty( "organization", downloadOrg );
                boolean isOrgShowMaskLP = downloadOrg.isShowMaskLP();
                if ( isShowMaskLP )
                {
                    downloadOrg.setShowMaskLP( true );
                }
                String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm, null );
                if ( isShowMaskLP )
                {
                    downloadOrg.setShowMaskLP( isOrgShowMaskLP );
                }
                log.debug( "xml message for cptyTrade=" + cptyTrade + ",xml=" + xml );

                xml = applyStleSheetOnXML( xml, "FinXML51v20" );
                Document xmlDoc = new Document( xml );
                Element element = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_LEGAL_ENTITY_BUYS_BASE ) );
                boolean legalEntityBuysBase = Boolean.valueOf( element.getTextString() );
                String cptyTradeLegalEntity = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_LEGAL_ENTITY ) ).getTextString();
                String cptyTradeTradingParty = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_TRADING_PARTY ) ).getTextString();
                String trdClsf = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_TRADE_CLASSIFICATION ) ).getTextString();
                String dealtCcy = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_DEALT_CURRENCY ) ).getTextString();
                String makerUser = xmlDoc.getElement( new XPath( XPATH_TRADE_MAKER_USER ) ).getTextString();
                String leUser = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_LEGAL_ENTITY_USER ) ).getTextString();
                String tpUser = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTY_TRADE_TRADING_PARTY_USER ) ).getTextString();
                String timeInForceStr = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_REQUEST_TIMEINFORCE ) ).getTextString();
                String execTypeStr = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_REQUEST_EXECUTION_TYPE ) ).getTextString();
                String minDealtAmtStr = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_REQUEST_MIN_DEALT_AMOUNT ) ).getTextString();
                String maxShowAmtStr = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_REQUEST_MAX_SHOW_AMOUNT ) ).getTextString();
                String origCptyName = xmlDoc.getElement( new XPath( XPATH_TRADE_ORIGINATING_COUNTERPARTY ) ).getTextString();
                String origUserName = xmlDoc.getElement( new XPath( XPATH_TRADE_ORIGINATING_USER ) ).getTextString();
                String baseCcyFXRateBasis = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_BASE_CCY ) ).getTextString();
                String varCcyFXRateBasis = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_VAR_CCY ) ).getTextString();
                String crossCcyFXRateBasis = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_CROSS_CCY ) ).getTextString();
                Elements coverRateSpreads = xmlDoc.getElements( new XPath( XPATH_SINGLE_LEG_COVER_RATE_SPREADS ) );
                String nonDeliverableFXRateBasis = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_NONDELIVERABLE ) ).getTextString();
                String isBlockTrade = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_BLOCK_TRADE ) ).getAttribute( IS_BLOCK_TRADE_KEY );


                while ( coverRateSpreads.hasMoreElements() )
                {
                    Element spread = ( Element ) coverRateSpreads.nextElement();
                    String nameAttribute = spread.getAttribute( "name" );
                    String value = spread.getTextString();
                    log.info( "spreadName=" + nameAttribute + ",value=" + value );
                    spreadedCptyTradeCounter++;
                }

                Element fixingDateElem = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_FIXING_DATE ) );
                String fixingDate = fixingDateElem == null ? null : fixingDateElem.getTextString();
                Element fixingTenorElem = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_FIXING_TENOR ) );
                String fixingTenor = fixingTenorElem == null ? null : fixingTenorElem.getTextString();

                Element startDateElem = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_START_DATE ) );
                String startDate = startDateElem == null ? null : startDateElem.getTextString();
                Element startTenorElem = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_START_TENOR ) );
                String startTenor = startTenorElem == null ? null : startTenorElem.getTextString();
                Element subTradeTypeElem = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_SUB_TRADE_TYPE ) );
                String subTradeType = subTradeTypeElem == null ? null : subTradeTypeElem.getTextString ();


                log.info( "cptyTradeLegalEntity=" + cptyTradeLegalEntity + ",cptyTradeTradingParty=" + cptyTradeTradingParty
                        + ",legalEntityBuysBase=" + legalEntityBuysBase + ",trdClsf=" + trdClsf + ",dltCcy="
                        + dealtCcy + ",makerUser=" + makerUser + ",leUser=" + leUser + ",tpUser=" + tpUser
                        + ",timeInForce=" + timeInForceStr + ",execType=" + execTypeStr + ",minDealtAmt="
                        + minDealtAmtStr + ",maxShowAmt=" + maxShowAmtStr + ",origCpty=" + origCptyName + ",origUser=" + origUserName );

                if ( isNDFTrade )
                {
                    assertEquals( "trade type should be spot", ISCommonConstants.TRD_CLSF_FXNDF, trdClsf );
                }
                else
                {
                    assertEquals( "trade type should be spot", ISCommonConstants.TRD_CLSF_SP, trdClsf );
                }
                assertEquals( "originating cpty should be " + takerLe.getShortName(), takerLe.getShortName(), origCptyName );
                assertEquals( "originating user should be " + takerUser.getShortName(), takerUser.getShortName(), origUserName );

                assertEquals( "Base ccy on fxrate basis should be " + baseCcy, baseCcy, baseCcyFXRateBasis );
                assertEquals( "Term ccy on fxrate basis should be " + termCcy, termCcy, varCcyFXRateBasis );
                assertEquals( "Cross ccy on fxrate basis should be " + termCcy, termCcy, crossCcyFXRateBasis );
                //assertEquals( "Classification attribute of Base ccy on fxrate basis should be " + CurrencyFactory.getCurrency( baseCcy ).getInstrumentClassification().getShortName(),
                //CurrencyFactory.getCurrency( baseCcy ).getInstrumentClassification().getShortName(), xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_BASE_CCY ) ).getAttribute( "classification" ) );
                //assertEquals( "Classification attribute of term ccy on fxrate basis should be " + CurrencyFactory.getCurrency( termCcy ).getInstrumentClassification().getShortName(),
                //CurrencyFactory.getCurrency( termCcy ).getInstrumentClassification().getShortName(), xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_RATEBASIS_VAR_CCY ) ).getAttribute( "classification" ) );

                if ( cptyALe.isSameAs( cptyTrade.getLegalEntity() ) )
                {
                    assertEquals( "cptyA should be selling the base.", legalEntityBuysBase, !isBid );
                    assertEquals( "cptyTrade should contain the legal entity of cptyA", cptyTradeLegalEntity.equals( cptyALe.getShortName() ), true );
                    assertEquals( "cptyTrade should contain the tradingparty representing cptyB", cptyTradeTradingParty.equals( cptyBLe.getShortName() ), true );
                    assertEquals( "cptyA dealt currency should be taker dealt ccy", dealtCcy, takerDltCcy );
                    assertEquals( "legal entity user should be taker side", leUser, takerUserName );
                    assertEquals( "trading party user should be maker side", tpUser, makerUserName );
                    assertEquals( "fxcoverRate should not be present", xml.contains( "fxCoverRate" ), false );
                }
                else
                {
                    assertEquals( "cptyB should be buying the base.", legalEntityBuysBase, isBid );
                    assertEquals( "cptyTrade should contain the legal entity of cptyB", cptyTradeLegalEntity.equals( cptyBLe.getShortName() ), true );
                    assertEquals( "cptyTrade should contain the tradingparty representing cptyA", cptyTradeTradingParty.equals( cptyALe.getShortName() ), true );
                    assertEquals( "cptyB dealt currency should be maker dealt ccy", dealtCcy, makerDltCcy );
                    assertEquals( "legal entity user should be maker side", leUser, makerUserName );
                    assertEquals( "trading party user should be taker side", tpUser, takerUserName );
                    if ( cptyTrade.getOwningCptyRef() == 'B' )
                    {
                        assertEquals( "fxcoverRate should be present", xml.contains( "fxCoverRate" ), true );
                    }
                }
                log.debug( "workflowMessage legalentitybuys= " + element.getTextString() );
                assertEquals( "minDealtAmt should be the same", Double.valueOf( minDealtAmtStr ), minDealtAmt );
                assertEquals( "maxShowAmt should be the same", Double.valueOf( maxShowAmtStr ), maxShowAmt );
                assertEquals( "execution type should match", getExecutionTypeStr( execType ), execTypeStr );
                assertEquals( "timeInForce should match", getTimeInForceStr( timeInForce ), timeInForceStr );
                if ( isNDFTrade )
                {
                    String expectedDate = spotDate.addDays( 2 ).getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN );
                    assertEquals( "fixingDate should match", expectedDate, fixingDate );
                    String expectedTenor = Tenor.SPOT_TENOR.getName();
                    assertEquals( "fixingTenor should match", expectedTenor, fixingTenor );
                    assertNotNull( isBlockTrade );
                }
                if ( !isNDFTrade )
                {
                    assertEquals( "FXRateBasis should be deliverable", "false", nonDeliverableFXRateBasis );
                    assertNull( isBlockTrade );
                }

                if ( isShowMaskLP )
                {
                    String streamLP1 = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_CPTYTRADE_STREAM_LP ) ).getTextString();
                    assertEquals( "Mask LP not found in cptyTrade", makerOrg.getShortName(), streamLP1 );
                    String streamLP2 = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_COUNTERPARTYP_STREAM_LP ) ).getTextString();
                    assertEquals( "Mask LP not found in counterpartyB", makerOrg.getShortName(), streamLP2 );
                }

                if ( isWindowForward )
                {
                    if (TradeConfigurationFactory.getTradeConfigurationMBean ().isWindowForwardParametersSTPEnabled ( downloadOrg ) )
                    {
                        String expectedDate = spotDate.addMonths ( 1 ).getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN );
                        assertEquals( "startDate should match", expectedDate, startDate );
                        String expectedTenor = new Tenor( "1M" ).getName ();
                        assertEquals( "startTenor should match", expectedTenor, startTenor );
                        assertEquals ( ISCommonConstants.WINDOW_FORWARD, subTradeType );
                    }
                    else
                    {
                        assertNull ( startDateElem );
                        assertNull ( startTenorElem );
                        assertNull ( subTradeTypeElem );
                    }
                }
                else
                {
                    assertNull ( startDateElem );
                    assertNull ( startTenorElem );
                    assertNull ( subTradeTypeElem );
                }
            }
            assertNotSame( "Counter should not be zero - means there should have been some spreads", spreadedCptyTradeCounter, 0 );
            commitTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "Exception in downloadSpotTradeFinXmlFormat. " + e );
            e.printStackTrace();
            fail();
        }
        finally
        {
            if ( tx != null )
            {
                tx.release();
            }

            // reset send spreads flag
            makerOrg.setSendSpreadsInSTP( false );
            WatchPropertyC.update (TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP_PREFIX + makerOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
        return spotTrade;
    }

    protected String getWorkflowParameterStringInFinxml( String key )
    {
        return "key=" + "\"" + key + "\"";
    }

    protected void enableFIXDownload( Organization org )
    {
        ExternalSystem extSys = ExternalSystemFactory.newExternalSystem( "DOWNLOAD" );
        Namespace namespace = org.getNamespace();
        extSys.setNamespace( namespace );
        extSys.putCustomField( "DirectFX_TradeDownloadMode", "Automatic" );
        extSys.putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );// Changed for FXI where in StyleSheet was required.
        extSys.putCustomField( "DirectFX_BrokerReference", "" );
        extSys.putCustomField( "DirectFX_Vendor", "" );
        org.putCustomField( "DirectFX_DownloadExtSys", extSys );
        if ( !org.containsCustomFieldKey( "DirectFX_DownloadWithoutCustomer" ) )
        {
            org.putCustomField( "DirectFX_DownloadWithoutCustomer", new Boolean( true ) );
        }
        org.setShowOrigCptyUserInSTP( false );
        org.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
    }
}
  
