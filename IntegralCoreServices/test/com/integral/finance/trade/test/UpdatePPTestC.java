package com.integral.finance.trade.test;


import com.integral.finance.fx.FXTradeC;
import com.integral.finance.trade.TradeC;
import com.integral.persistence.Entity;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

public class UpdatePPTestC extends PTestCaseC
{
    static String name = "Trade Query Test";

    public UpdatePPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        try
        {
            UpdatePPTestC test = new UpdatePPTestC( name );
            test.run();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void testPerform1()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            log( " searching for all fx trades - read all query" );
            java.util.Vector vec = uow.readAllObjects( TradeC.class );
            for ( int i = 0; i < vec.size(); i++ )
            {
                Object obj = vec.elementAt( i );
                Entity ent = ( Entity ) obj;
                log( "  trade #" + i + " , oid = <" + ent.getObjectID() + ">, "
                        + " java class = <" + obj.getClass().getName() + '>' );
            }

            uow.release();
            log( "query trades" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "query trades" );
        }
    }

    public void testPerform2()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            log( " searching for all fx trades - cursored query" );

            ReadAllQuery query = new ReadAllQuery();
            query.setReferenceClass( FXTradeC.class );
            query.useCursoredStream( 100, 100 );

            CursoredStream stream = ( CursoredStream ) uow.executeQuery( query );
            int i = 1;
            while ( !stream.atEnd() )
            {
                Object obj = stream.read();
                Entity ent = ( Entity ) obj;

                log( "  trade #" + ( i - 1 ) + " , oid = <" + ent.getObjectID() + ">, "
                        + " class = <" + obj.getClass().getName() + '>' );

                if ( name == null )
                {
                    name = "A";
                }
                else
                {
                    if ( name.length() >= 10 )
                    {
                        name = "A";
                    }
                    else
                    {
                        name += "X";
                    }
                }

                if ( i % 100 == 0 )
                {
                    uow.commitAndResume();
                    stream.releasePrevious();
                }
                i++;
            }

            uow.commit();
            log( "query trades" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "query trades" );
        }
    }
}
