package com.integral.finance.trade.test;

import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestClassificationC;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXMidRateC;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeClassification;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.TradeLeg;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.configuration.TradeDownloadServiceConfiguration;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.is.ISCommonConstants;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.user.Organization;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;
import electric.xml.Document;
import electric.xml.Element;
import electric.xml.XPath;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.HashSet;

public class TradeDownloadService2TestC extends TradeServiceTestC
{
    public TradeDownloadService2TestC( String name ) throws Exception
    {
        super( name );
    }

    public void testFinxmlSTPDownloadsForMessageIdGeneration() throws Exception
    {
        try
        {
            IdcTransaction tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            for ( int i = 0; i < 3; i++ )
            {
                tradeConfig.setProperty( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE, String.valueOf( i ), ConfigurationProperty.DYNAMIC_SCOPE, null );

                // go through all the finxml stp download formats.
                for ( String format : getAllFinxmlDownloadFormats() )
                {
                    setSTPFormat( takerOrg, format );
                    setSTPFormat( makerOrg, format );
                    Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                    prepareSingleLegRequest( ( FXSingleLeg ) trd );
                    trd.setCoveredTradeTxId( "FXI100" );
                    trd.setCoverTradeTxIds( "FXI101-FXI102" );
                    trd.setMakerReferenceId( "FXI200" );
                    ts.newTrade( trd, null );
                    WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                    msg.setParameterValue( TradeServiceConstants.COVER_TRADE_MKR_REF_IDS, "FXI200" );
                    ts.verifyTrade( trd, msg );

                    String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
                    String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
                    log.debug( "takerSTPMessage in format=" + format + " : " + takerSTPMessage );
                    log.debug( "makerSTPMessage in format=" + format + " : " + makerSTPMessage );
                    Document xmlDoc1 = new Document( takerSTPMessage );
                    String messageId1 = xmlDoc1.getElement( new XPath( XPATH_MESSAGE_ID ) ).getTextString();
                    Document xmlDoc2 = new Document( makerSTPMessage );
                    String messageId2 = xmlDoc2.getElement( new XPath( XPATH_MESSAGE_ID ) ).getTextString();
                    log.debug( "messageId of takerSTP=" + messageId1 );
                    log.debug( "messageId of takerSTP=" + messageId2 );
                    switch ( i )
                    {
                        case TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_GUID:
                        {
                            assertTrue( messageId1.contains( "G" ) );
                            assertTrue( messageId2.contains( "G" ) );
                            break;
                        }
                        case TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS:
                        {
                            long t0 = System.currentTimeMillis();
                            long t1 = Long.parseLong( messageId1 );
                            long t2 = Long.parseLong( messageId2 );
                            assertTrue( t0 - t1 < 10000 );
                            assertTrue( t0 - t2 < 10000 );
                            break;
                        }
                        case TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_NANOSECONDS:
                        {
                            long t0 = System.nanoTime();
                            long t1 = Long.parseLong( messageId1 );
                            long t2 = Long.parseLong( messageId2 );
                            assertTrue( t0 - t1 < 1000000000 );
                            assertTrue( t0 - t2 < 1000000000 );
                            break;
                        }
                    }
                    sleepFor( 100 );
                }
            }
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testFinxmlSTPDownloadsForMessageIdGeneration", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            tradeConfig.setProperty( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE, null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }
    public void testFinxmlSTPDownloadsForAccountIdNull() throws Exception {
        IdcTransaction tx = initTransaction();

        try {
            tx.getUOW().addReadOnlyClasses(getDealingTransactionReadOnlyClasses());
            WatchPropertyC.update( TradeConfigurationMBean.ACCOUNT_SUPPORT_STP, "false", ConfigurationProperty.DYNAMIC_SCOPE );

            // go through all the finxml stp download formats.
            for (String format : getAllFinxmlDownloadFormats()) {
                setSTPFormat(takerOrg, format);
                setSTPFormat(makerOrg, format);

                // For Taker Org, enable Show SEF Identifiers. For Maker Org, don't enable Show SEF Identifiers.
                takerOrg.setDownloadEnabledForOutright(true);
                makerOrg.setDownloadEnabledForOutright(true);

                Trade trd = prepareSingleLegTrade(1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate);
                trd.setTradeClassification((TradeClassification) namedEntityReader.execute(TradeClassification.class, ISCommonConstants.TRD_CLSF_OR));
                prepareSingleLegRequest((FXSingleLeg) trd);
                Request request = trd.getRequest();
                request.setRequestClassification((RequestClassificationC) namedEntityReader.execute(RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE));
                trd.setCoveredTradeTxId("FXI100");
                trd.setCoverTradeTxIds("FXI101-FXI102");
                trd.setMakerReferenceId("FXI200");
                trd.setTransactionID("FXI300");
                TradeLeg leg = trd.getTradeLeg("fxLeg");
                FXRate midRate = new FXMidRateC();
                midRate.setSpotRate(1.001);
                midRate.setForwardPoints(0.0001);
                midRate.setRate(1.0011);
                ((FXLeg) leg).getFXPayment().setFXMidRate(midRate);
                trd.setReportingParty(takerLe);

                ts.newTrade(trd, null);
                WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                msg.setParameterValue(TradeServiceConstants.COVER_TRADE_MKR_REF_IDS, "FXI200");
                ts.verifyTrade(trd, msg);

                String STPMessage = trd.getCptyTrade(Trade.CPTYB).getDownloadedMessage();
                Document xmlDoc = new Document(STPMessage);

                //validation for USI

                Element fxSingleLegEle2 = xmlDoc.getElement(new XPath(XPATH_SINGLE_LEG));
                assertNull("AccountId Attribute should  be present if Show Idc.RFS.Account.Support is true",
                        fxSingleLegEle2.getAttribute("accountId"));

            }

        } catch (Exception e) {
            fail("testFinxmlSTPDownloadsForSEFIdentifiers", e);
        } finally {
            WatchPropertyC.update( TradeConfigurationMBean.ACCOUNT_SUPPORT_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            tx.release();
            IdcUtilC.refreshObject(takerOrg);
            IdcUtilC.refreshObject(makerOrg);
        }
    }
    public void testFinxmlSTPDownloadsForAccountId() throws Exception {
        IdcTransaction tx = initTransaction();

        try {
            tx.getUOW().addReadOnlyClasses(getDealingTransactionReadOnlyClasses());
            WatchPropertyC.update( TradeConfigurationMBean.ACCOUNT_SUPPORT_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE );

            // go through all the finxml stp download formats.
            for (String format : getAllFinxmlDownloadFormats()) {
                setSTPFormat(takerOrg, format);
                setSTPFormat(makerOrg, format);

                // For Taker Org, enable Show SEF Identifiers. For Maker Org, don't enable Show SEF Identifiers.
                takerOrg.setDownloadEnabledForOutright(true);
                makerOrg.setDownloadEnabledForOutright(true);

                Trade trd = prepareSingleLegTrade(1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate);
                trd.setTradeClassification((TradeClassification) namedEntityReader.execute(TradeClassification.class, ISCommonConstants.TRD_CLSF_OR));
                prepareSingleLegRequest((FXSingleLeg) trd);
                Request request = trd.getRequest();
                request.setRequestClassification((RequestClassificationC) namedEntityReader.execute(RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE));
                trd.setCoveredTradeTxId("FXI100");
                trd.setCoverTradeTxIds("FXI101-FXI102");
                trd.setMakerReferenceId("FXI200");
                trd.setTransactionID("FXI300");
                TradeLeg leg = trd.getTradeLeg("fxLeg");
                FXRate midRate = new FXMidRateC();
                midRate.setSpotRate(1.001);
                midRate.setForwardPoints(0.0001);
                midRate.setRate(1.0011);
                ((FXLeg) leg).getFXPayment().setFXMidRate(midRate);
                trd.setReportingParty(takerLe);

                ts.newTrade(trd, null);
                WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                msg.setParameterValue(TradeServiceConstants.COVER_TRADE_MKR_REF_IDS, "FXI200");
                ts.verifyTrade(trd, msg);

                String STPMessage = trd.getCptyTrade(Trade.CPTYB).getDownloadedMessage();
                Document xmlDoc = new Document(STPMessage);

                //validation for USI

                Element fxSingleLegEle2 = xmlDoc.getElement(new XPath(XPATH_SINGLE_LEG));
                assertNotNull("AccountId Attribute should  be present if Show Idc.RFS.Account.Support is true",
                        fxSingleLegEle2.getAttribute("accountId"));

            }

        } catch (Exception e) {
            fail("testFinxmlSTPDownloadsForSEFIdentifiers", e);
        } finally {
            WatchPropertyC.update( TradeConfigurationMBean.ACCOUNT_SUPPORT_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            tx.release();
            IdcUtilC.refreshObject(takerOrg);
            IdcUtilC.refreshObject(makerOrg);
        }
    }

    public void testFinxmlSTPDownloadsForSEFIdentifiers() throws Exception
    {
        IdcTransaction tx = initTransaction();

        try
        {
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            // go through all the finxml stp download formats.
            for ( String format : getAllFinxmlDownloadFormats() )
            {
                setSTPFormat( takerOrg, format );
                setSTPFormat( makerOrg, format );

                // For Taker Org, enable Show SEF Identifiers. For Maker Org, don't enable Show SEF Identifiers.
                takerOrg.setShowSEF( true );
                makerOrg.setShowSEF( false );
                takerOrg.setDownloadEnabledForOutright( true );
                makerOrg.setDownloadEnabledForOutright( true );

                Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                trd.setSDR( ConfigurationFactory.getSefMBean().getSDR() );
                trd.setSEFOrg( ConfigurationFactory.getSefMBean().getINFXSEF() );
                trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );
                prepareSingleLegRequest( ( FXSingleLeg ) trd );
                Request request = trd.getRequest();
                request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
                trd.setCoveredTradeTxId( "FXI100" );
                trd.setCoverTradeTxIds( "FXI101-FXI102" );
                trd.setMakerReferenceId( "FXI200" );
                trd.setTransactionID( "FXI300" );
                TradeLeg leg = trd.getTradeLeg( "fxLeg" );
                leg.setUSI( "TESTUSI12345" );
                leg.setUSINamespace( "TESTUSI" );
                leg.setUSIIdentifier( "12345" );
                FXRate midRate = new FXMidRateC();
                midRate.setSpotRate( 1.001 );
                midRate.setForwardPoints( 0.0001 );
                midRate.setRate( 1.0011 );
                ( ( FXLeg ) leg ).getFXPayment().setFXMidRate( midRate );
                trd.setReportingParty( takerLe );

                ts.newTrade( trd, null );
                WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                msg.setParameterValue( TradeServiceConstants.COVER_TRADE_MKR_REF_IDS, "FXI200" );
                ts.verifyTrade( trd, msg );

                String STPMessageSEF = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
                String STPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
                log.debug( "Taker's STPMessage with Show SEF Identifiers enabled in  " + format + " format : " + STPMessageSEF );
                log.debug( "Maker's STPMessage without Show SEF Identifiers disabled in " + format + " format : " + STPMessage );
                Document xmlDocSEF = new Document( STPMessageSEF );
                Document xmlDoc = new Document( STPMessage );

                //validation for USI
                Element USIEle = xmlDocSEF.getElement( new XPath( XPATH_SINGLE_LEG_USI ) );
                assertTrue( "USI Tag should be present if Show SEF Identifiers is enabled", ( ( USIEle != null ) && ( USIEle.getTextString() != null ) ) );
                assertEquals( "USI Tag should NOT be present if Show SEF Identifiers is NOT enabled", xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_USI ) ), null );

                //validation for UPI
                Element fxSingleLegEle1 = xmlDocSEF.getElement( new XPath( XPATH_SINGLE_LEG ) );
                assertNotNull( "UPI Attribute should be present if Show SEF Identifiers is enabled", fxSingleLegEle1.getAttribute( "UPI" ) );

                Element fxSingleLegEle2 = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG ) );
                assertNull( "UPI Attribute should NOT be present if Show SEF Identifiers is NOT enabled", fxSingleLegEle2.getAttribute( "UPI" ) );

                //validation for SDR Org
                Element shortNameEle = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/SDR/shortName" ) );
                assertTrue( "SDR Org Tags should be present if Show SEF Identifiers is enabled", ( ( shortNameEle != null ) && ( shortNameEle.getTextString() != null ) ) );
                assertEquals( "SDR Org Tags should NOT be present if Show SEF Identifiers is NOT enabled", xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/SDR/shortName" ) ), null );

                //validation for INFXSEF Org
                Element shortNameEle1 = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/SEF/shortName" ) );
                assertTrue( "INFXSEF Org Tags should be present if Show SEF Identifiers is enabled", ( ( shortNameEle1 != null ) && ( shortNameEle1.getTextString() != null ) ) );
                assertEquals( "INFXSEF Org Tags should NOT be present if Show SEF Identifiers is NOT enabled", xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/SEF/shortName" ) ), null );

                Element reportingPartyElement = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/reportingParty/shortName" ) );
                assertTrue( "Reporting party tag should be present if Show SEF Identifiers is enabled", ( ( reportingPartyElement != null ) && ( reportingPartyElement.getTextString() != null ) ) );
                assertEquals( "Reporting party tag should NOT be present if Show SEF Identifiers is NOT enabled", xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/reportingParty/shortName" ) ), null );

                Element USIElement = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/USI" ) );
                assertTrue( "USI tag should be present if Show SEF Identifiers is enabled", ( ( USIElement != null ) && ( USIElement.getTextString() != null ) ) );
                assertEquals( "USI tag should NOT be present if Show SEF Identifiers is NOT enabled", xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/USI" ) ), null );

                Element USINamespaceElement = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/USINamespace" ) );
                assertTrue( "USI namespace tag should be present if Show SEF Identifiers is enabled", ( ( USINamespaceElement != null ) && ( USINamespaceElement.getTextString() != null ) ) );
                assertEquals( "USI namespace tag should NOT be present if Show SEF Identifiers is NOT enabled", xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/USINamespace" ) ), null );

                Element USIIdentifierElement = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/USIIdentifier" ) );
                assertTrue( "USI identifier tag should be present if Show SEF Identifiers is enabled", ( ( USIIdentifierElement != null ) && ( USIIdentifierElement.getTextString() != null ) ) );
                assertEquals( "USI identifier tag should NOT be present if Show SEF Identifiers is NOT enabled", xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/USIIdentifier" ) ), null );

                Element midRateElement = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/midRate" ) );
                Element midRateValueElement = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/midRate/rate" ) );
                Element midRateSpotRateElement = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/midRate/spotRate" ) );
                Element midRateForwardPointsElement = xmlDocSEF.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/midRate/forwardPoints" ) );
                assertTrue( "Midrate tag should be present if Show SEF Identifiers is enabled", ( ( midRateElement != null ) ) );
                assertTrue( "Midrate rate tag should be present if Show SEF Identifiers is enabled", ( ( midRateValueElement != null ) && ( midRateValueElement.getTextString() != null ) ) );
                assertTrue( "Midrate spotRate tag should be present if Show SEF Identifiers is enabled", ( ( midRateSpotRateElement != null ) && ( midRateSpotRateElement.getTextString() != null ) ) );
                assertTrue( "Midrate forwardPoints tag should be present if Show SEF Identifiers is enabled", ( ( midRateForwardPointsElement != null ) && ( midRateForwardPointsElement.getTextString() != null ) ) );

                assertEquals( "Midrate tag should NOT be present if Show SEF Identifiers is NOT enabled", xmlDoc.getElement( new XPath( "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/midRate" ) ), null );
            }

        }
        catch ( Exception e )
        {
            fail( "testFinxmlSTPDownloadsForSEFIdentifiers", e );
        }
        finally
        {
            tx.release();
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testAllMonthTenorsSTPDownloads() throws Exception
    {
        try
        {
            IdcTransaction tx;
            UnitOfWork uow;
            tx = initTransaction();
            uow = tx.getUOW();
            deleteAllDealingData( uow );
            tx = initTransaction();
            uow = tx.getUOW();

            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXRateBasis rb = stdQuoteConv.getFXRateBasis( "EUR", "USD" );
            Collection<Tenor> tenorList = new HashSet<Tenor>();
            for ( String tnr : monthTenors )
            {
                tenorList.add( new Tenor( tnr ) );
            }
            for ( String format : getAllDownloadFormats() )
            {
                setSTPFormat( takerOrg, format );
                setSTPFormat( makerOrg, format );
                for ( Tenor tenor : tenorList )
                {
                    FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], rb.getValueDate( tradeDate, tenor ) );
                    trd.getFXLeg().getFXPayment().setTenor( tenor );
                    trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
                    prepareSingleLegRequest( trd );
                    ts.newTrade( trd, null );
                    ts.verifyTrade( trd, null );

                    String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
                    String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
                    log.debug( "takerSTPMessage in format=" + format + " : " + takerSTPMessage + " : " + tenor );
                    log.debug( "makerSTPMessage in format=" + format + " : " + makerSTPMessage + " : " + tenor );
                    assertNotNull( takerSTPMessage );
                    assertNotNull( makerSTPMessage );
                    tx.release();
                    sleepFor( 100 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testAllMonthTenorsSTPDownloads", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testAllSpecialTenorsSTPDownloads() throws Exception
    {
        try
        {
            IdcTransaction tx;
            UnitOfWork uow;
            tx = initTransaction();
            uow = tx.getUOW();
            deleteAllDealingData( uow );
            tx = initTransaction();
            uow = tx.getUOW();

            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXRateBasis rb = stdQuoteConv.getFXRateBasis( "EUR", "USD" );
            Collection<Tenor> tenorList = new HashSet<Tenor>();
            for ( String tnr : specialTenors )
            {
                tenorList.add( new Tenor( tnr ) );
            }
            for ( String format : getAllDownloadFormats() )
            {
                setSTPFormat( takerOrg, format );
                setSTPFormat( makerOrg, format );
                for ( Tenor tenor : tenorList )
                {
                    FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], rb.getValueDate( tradeDate, tenor ) );
                    trd.getFXLeg().getFXPayment().setTenor( tenor );
                    trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
                    prepareSingleLegRequest( trd );
                    ts.newTrade( trd, null );
                    ts.verifyTrade( trd, null );

                    String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
                    String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
                    log.debug( "takerSTPMessage in format=" + format + " : " + takerSTPMessage + " : " + tenor );
                    log.debug( "makerSTPMessage in format=" + format + " : " + makerSTPMessage + " : " + tenor );
                    assertNotNull( takerSTPMessage );
                    assertNotNull( makerSTPMessage );
                    tx.release();
                    sleepFor( 100 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testAllSpecialTenorsSTPDownloads", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testAllDatePeriodTenorsSTPDownloads() throws Exception
    {
        try
        {
            IdcTransaction tx;
            UnitOfWork uow;
            tx = initTransaction();
            uow = tx.getUOW();
            deleteAllDealingData( uow );
            tx = initTransaction();
            uow = tx.getUOW();

            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXRateBasis rb = stdQuoteConv.getFXRateBasis( "EUR", "USD" );
            Collection<String> tenorStrList = new HashSet<String>();
            for ( int i = 0; i < 10; i++ )
            {
                tenorStrList.add( i + "d" );
                tenorStrList.add( i + "D" );
            }

            for ( int i = 0; i < 36; i++ )
            {
                tenorStrList.add( i + "m" );
                tenorStrList.add( i + "M" );
            }

            for ( int i = 0; i < 3; i++ )
            {
                tenorStrList.add( i + "y" );
                tenorStrList.add( i + "Y" );
            }
            Collection<Tenor> tenorList = new HashSet<Tenor>();
            for ( String tnr : tenorStrList )
            {
                tenorList.add( new Tenor( tnr ) );
            }
            for ( String format : getAllDownloadFormats() )
            {
                setSTPFormat( takerOrg, format );
                setSTPFormat( makerOrg, format );
                for ( Tenor tenor : tenorList )
                {
                    FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], rb.getValueDate( tradeDate, tenor ) );
                    trd.getFXLeg().getFXPayment().setTenor( tenor );
                    trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
                    prepareSingleLegRequest( trd );
                    ts.newTrade( trd, null );
                    ts.verifyTrade( trd, null );

                    String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
                    String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
                    log.debug( "takerSTPMessage in format=" + format + " : " + takerSTPMessage + " : " + tenor );
                    log.debug( "makerSTPMessage in format=" + format + " : " + makerSTPMessage + " : " + tenor );
                    assertNotNull( takerSTPMessage );
                    assertNotNull( makerSTPMessage );
                    tx.release();
                    sleepFor( 100 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testAllDaysTenorsSTPDownloads", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testCoverTradeCounterpartySetting() throws Exception
    {
        try
        {
            IdcTransaction tx;
            UnitOfWork uow;
            tx = initTransaction();
            uow = tx.getUOW();
            deleteAllDealingData( uow );
            tx = initTransaction();
            uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            for ( String format : getAllDownloadFormats() )
            {
                setSTPFormat( takerOrg, format );
                setSTPFormat( makerOrg, format );
                FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
                prepareSingleLegRequest( trd );
                trd.getRequest().getRequestAttributes().setCoveredTradeLE( lpLe.getShortName() );
                trd.getRequest().getRequestAttributes().setCoveredTradeUser( lpUser.getShortName() );
                trd.getRequest().getRequestAttributes().setCoveredTradeOrganization( lpOrg.getShortName() );
                ts.newTrade( trd, null );
                ts.verifyTrade( trd, null );

                String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
                String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
                log.debug( "takerSTPMessage in format=" + format + " : " + takerSTPMessage );
                log.debug( "makerSTPMessage in format=" + format + " : " + makerSTPMessage );
                assertNotNull( takerSTPMessage );
                assertNotNull( makerSTPMessage );
                assertEquals( trd.getCoveredTradeCounterparty(), lpLe );
                assertEquals( trd.getCoveredTradeUser(), lpUser );
                tx.release();
                sleepFor( 100 );
            }
        }
        catch ( Exception e )
        {
            fail( "testCoverTradeCounterpartySetting", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testTradeLEIS() throws Exception
    {
        try
        {
            String takerLEI = "takerLEI";
            String makerLEI = "makerLEI";
            takerLe.setLEI( takerLEI );
            makerLe.setLEI( makerLEI );
            IdcTransaction tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXSingleLeg trade = prepareSingleLegTrade( 100.00, true, true, "EUR/USD", makerOrg, makerTpForTaker, 1.2334, spotDate );
            prepareSingleLegRequest( trade );
            TradeCloneServiceC.createCptyTrades( trade );

            WorkflowMessage msg2 = MessageFactory.newWorkflowMessage();
            msg2.setEventName( TradeService.VERIFY_EVENT );
            msg2.setTopic( TradeService.MESSAGE_TOPIC );
            msg2.setObject( trade );
            ts.verifyTrade( trade, msg2 );

            tx.release();

            assertEquals( trade.getCounterpartyALEI(), takerLEI );
            assertEquals( trade.getCounterpartyBLEI(), makerLEI );
        }
        catch ( Exception e )
        {
            fail( "testTradeLEIS", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerLe );
            IdcUtilC.refreshObject( makerLe );
        }
    }

    public void testTradeUPI() throws Exception
    {
        String orgName = makerTpForTaker.getLegalEntityOrganization().getShortName();
        try
        {
            TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + "." + orgName,
                    "FXSpot~SPOTPRODUCT,FXOutright~OUTRIGHT,FXNDF~NDF,FXSpotFwd~SWAP,FXFwdFwd~SWAP", ConfigurationProperty.DYNAMIC_SCOPE, null );
            init( fiUser );
            IdcTransaction tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXSingleLeg trade = prepareSingleLegTrade( 100.00, true, true, "EUR/USD", makerOrg, makerTpForTaker, 1.2334, spotDate );
            prepareSingleLegRequest( trade );
            TradeCloneServiceC.createCptyTrades( trade );

            WorkflowMessage msg2 = MessageFactory.newWorkflowMessage();
            msg2.setEventName( TradeService.VERIFY_EVENT );
            msg2.setTopic( TradeService.MESSAGE_TOPIC );
            msg2.setObject( trade );
            ts.verifyTrade( trade, msg2 );

            tx.release();
            assertEquals( "EUR_USD_SPOTPRODUCT", trade.getUPI() );
        }
        catch ( Exception ex )
        {
            fail( "testTradeUPI ", ex );
        }
        finally
        {
            TradeConfigurationFactory.getTradeConfigurationMBean().removeProperty( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + "." + orgName, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testIntraFloorSTPDownloads() throws Exception
    {
        try
        {
            IdcTransaction tx;
            UnitOfWork uow;
            tx = initTransaction();
            uow = tx.getUOW();
            deleteAllDealingData( uow );
            tx = initTransaction();
            uow = tx.getUOW();

            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            // go through all the stp download flag settings.
            for ( int i = 0; i < 4; i++ )
            {
                takerOrg.setIntraFloorSTPFlag( i );

                for ( int j = 0; j < 4; j++ )
                {
                    takerOrg.setSTPOriginatingDetails( j );
                    for ( String format : getAllDownloadFormats() )
                    {
                        setSTPFormat( takerOrg, format );
                        Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                        trd.setCounterpartyB( trd.getCounterpartyA() );
                        trd.setMakerUser( UserFactory.getUser( "fi1mm2@FI1" ) );
                        prepareSingleLegRequest( ( FXSingleLeg ) trd );
                        trd.setCoveredTradeTxId( "FXI100" );
                        trd.setCoverTradeTxIds( "FXI101-FXI102" );
                        ts.newTrade( trd, null );
                        ts.verifyTrade( trd, null );

                        String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
                        String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
                        log.debug( "takerSTPMessage in format=" + format + " : " + takerSTPMessage );
                        log.debug( "makerSTPMessage in format=" + format + " : " + makerSTPMessage );
                        int intrafloorFlags = takerOrg.getIntraFloorSTPFlag();
                        switch ( intrafloorFlags )
                        {
                            case Organization.INTRAFLOOR_TRADE_NO_DOWNLOAD:
                            {
                                assertNull( takerSTPMessage );
                                assertNull( makerSTPMessage );
                                break;
                            }
                            case Organization.INTRAFLOOR_TRADE_BOTH_DOWNLOAD:
                            {
                                assertNotNull( takerSTPMessage );
                                assertNotNull( makerSTPMessage );
                                break;
                            }
                            case Organization.INTRAFLOOR_TRADE_TAKER_DOWNLOAD:
                            {
                                assertNotNull( takerSTPMessage );
                                assertNull( makerSTPMessage );
                                break;
                            }
                            case Organization.INTRAFLOOR_TRADE_MAKER_DOWNLOAD:
                            {
                                assertNull( takerSTPMessage );
                                assertNotNull( makerSTPMessage );
                                break;
                            }
                        }
                        sleepFor( 100 );
                    }
                }
            }

            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testIntraFloorSTPDownloads", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
        }
    }

    public void testFinxmlSTPDownloadsForWorkflowMessageParametersOrdering() throws Exception
    {
        try
        {
            IdcTransaction tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            // go through all the finxml stp download formats.
            for ( String format : getAllFinxmlDownloadFormats() )
            {
                setSTPFormat( takerOrg, format );
                setSTPFormat( makerOrg, format );
                Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
                prepareSingleLegRequest( ( FXSingleLeg ) trd );
                trd.setCoveredTradeTxId( "FXI100" );
                trd.setCoverTradeTxIds( "FXI101-FXI102" );
                trd.setMakerReferenceId( "FXI200" );
                ts.newTrade( trd, null );
                WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                msg.setParameterValue( TradeServiceConstants.COVER_TRADE_MKR_REF_IDS, "FXI200" );
                ts.verifyTrade( trd, msg );

                String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
                String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
                log.debug( "takerSTPMessage in format=" + format + " : " + takerSTPMessage );
                log.debug( "makerSTPMessage in format=" + format + " : " + makerSTPMessage );

                int takerMsgPos1 = takerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.ORIGINATING_COUNTERPARTY_PARAM ) );
                int takerMsgPos2 = takerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.ORIGINATING_USER_PARAM ) );
                int takerMsgPos3 = takerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.DOWNLOAD_FORMAT_KEY ) );
                int takerMsgPos4 = takerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.TRADE_CLASSIFICATION_PARAM ) );
                int takerMsgPos5 = takerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.COVER_TRADE_MKR_REF_IDS ) );
                log.debug( "takerMsgPos1=" + takerMsgPos1 );
                log.debug( "takerMsgPos2=" + takerMsgPos2 );
                log.debug( "takerMsgPos3=" + takerMsgPos3 );
                log.debug( "takerMsgPos4=" + takerMsgPos4 );
                log.debug( "takerMsgPos5=" + takerMsgPos5 );

                int makerMsgPos1 = makerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.ORIGINATING_COUNTERPARTY_PARAM ) );
                int makerMsgPos2 = makerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.ORIGINATING_USER_PARAM ) );
                int makerMsgPos3 = makerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.DOWNLOAD_FORMAT_KEY ) );
                int makerMsgPos4 = makerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.TRADE_CLASSIFICATION_PARAM ) );
                int makerMsgPos5 = makerSTPMessage.indexOf( getWorkflowParameterStringInFinxml( TradeServiceConstants.COVER_TRADE_MKR_REF_IDS ) );
                log.debug( "makerMsgPos1=" + makerMsgPos1 );
                log.debug( "makerMsgPos2=" + makerMsgPos2 );
                log.debug( "makerMsgPos3=" + makerMsgPos3 );
                log.debug( "makerMsgPos4=" + makerMsgPos4 );
                log.debug( "makerMsgPos5=" + makerMsgPos5 );

                assertTrue( takerMsgPos4 < takerMsgPos1 );
                assertTrue( takerMsgPos1 < takerMsgPos2 );
                assertTrue( takerMsgPos2 < takerMsgPos3 );
                assertTrue( takerMsgPos3 < takerMsgPos5 );

                assertTrue( makerMsgPos4 < makerMsgPos1 );
                assertTrue( makerMsgPos1 < makerMsgPos2 );
                assertTrue( makerMsgPos2 < makerMsgPos3 );
                assertTrue( makerMsgPos3 < makerMsgPos5 );
                sleepFor( 100 );
            }

            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testFinxmlSTPDownloadsForWorkflowMessageParametersOrdering", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testLegalEntityOverride() throws Exception
    {
        try
        {
            IdcTransaction tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            takerOrg.setSTPOverrideOnLE ( true );
            makerOrg.setSTPOverrideOnLE ( true );

            // org level setting
            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );

            setSTPFormat ( makerLe, TradeServiceConstants.TOF_FORMAT, "TestQueue" );
            setSTPFormat ( takerLe, TradeServiceConstants.FinXML51v21_FORMAT, "TestQueue" );



            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            trd.setMakerReferenceId( "FXI200" );
            ts.newTrade( trd, null );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setParameterValue( TradeServiceConstants.COVER_TRADE_MKR_REF_IDS, "FXI200" );
            ts.verifyTrade( trd, msg );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
            log.debug( "takerSTPMessage in format=" + TradeServiceConstants.TOF_FORMAT + " : " + takerSTPMessage );
            log.debug( "makerSTPMessage in format=" + TradeServiceConstants.TOF_FORMAT + " : " + makerSTPMessage );
            Document xmlDoc1 = new Document( takerSTPMessage );
            String messageId1 = xmlDoc1.getElement( new XPath( XPATH_MESSAGE_ID ) ).getTextString();
            assertNotNull ( messageId1 );

            assertTrue ( makerSTPMessage.indexOf ( "501" ) == 1 );
            sleepFor( 100 );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testLegalEntityOverride", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject( takerLe );
            IdcUtilC.refreshObject( makerLe );
        }
    }

    public void testLegalEntityOverrideForCounterpartyCD() throws Exception
    {
        try
        {
            IdcTransaction tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            takerPbOrg.setSTPOverrideOnLE ( true );
            makerPbOrg.setSTPOverrideOnLE ( true );

            takerTpForMaker.setPrimeBrokerOrganization ( takerPbOrg );
            takerTpForMaker.setPrimeBrokerTradingParty ( takerTpForTakerPb );
            takerTpForMaker.setPrimeBrokerSTPEnabled ( true );

            makerTpForTaker.setPrimeBrokerOrganization ( makerPbOrg );
            makerTpForTaker.setPrimeBrokerTradingParty ( makerTpForMakerPb );
            makerTpForTaker.setPrimeBrokerSTPEnabled ( true );

            // org level setting
            setSTPFormat( takerPbOrg, TradeServiceConstants.FIX_FORMAT );
            setSTPFormat( makerPbOrg, TradeServiceConstants.FIX_FORMAT );

            setSTPFormat ( makerPbLe, TradeServiceConstants.TOF_FORMAT, "TestQueue" );
            setSTPFormat ( takerPbLe, TradeServiceConstants.FinXML51v21_FORMAT, "TestQueue" );



            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            trd.setMakerReferenceId( "FXI200" );
            ts.newTrade( trd, null );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setParameterValue( TradeServiceConstants.COVER_TRADE_MKR_REF_IDS, "FXI200" );
            ts.verifyTrade( trd, msg );

            String takerPBSTPMessage = trd.getCptyTrade( Trade.CPTYC ).getDownloadedMessage();
            String makerPBSTPMessage = trd.getCptyTrade( Trade.CPTYD ).getDownloadedMessage();
            log.debug( "takerSTPMessage in format=" + TradeServiceConstants.TOF_FORMAT + " : " + takerPBSTPMessage );
            log.debug( "makerSTPMessage in format=" + TradeServiceConstants.TOF_FORMAT + " : " + makerPBSTPMessage );
            Document xmlDoc1 = new Document( takerPBSTPMessage );
            String messageId1 = xmlDoc1.getElement( new XPath( XPATH_MESSAGE_ID ) ).getTextString();
            assertNotNull ( messageId1 );

            assertTrue ( makerPBSTPMessage.indexOf ( "501" ) == 1 );
            sleepFor( 100 );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testLegalEntityOverrideForCounterpartyCD", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerPbOrg );
            IdcUtilC.refreshObject( makerPbOrg );
            IdcUtilC.refreshObject( takerPbLe );
            IdcUtilC.refreshObject( makerPbLe );
        }
    }

    public void testTradeExecutionTimeInMillisInFinxmlDisabled() throws Exception
    {
        try
        {
            WatchPropertyC.update( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS_PREFIX + takerOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcTransaction tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            // org level setting
            setSTPFormat( takerOrg, TradeServiceConstants.FinXML51v21_FORMAT );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            ts.newTrade( trd, null );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            ts.verifyTrade( trd, msg );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            log.debug( "takerSTPMessage in format=" + TradeServiceConstants.FinXML51v21_FORMAT + " : " + takerSTPMessage );
            Document xmlDoc1 = new Document( takerSTPMessage );
            Object element = xmlDoc1.getElement( new XPath( XPATH_SINGLE_LEG_EXECUTION_DATE_TIME ) );
            assertNull ( element );

            sleepFor( 100 );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testTradeExecutionTimeInMillisInFinxmlDisabled", e );
        }
        finally
        {
            WatchPropertyC.update( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS_PREFIX + takerOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcUtilC.refreshObject( takerOrg );
        }
    }

    public void testTradeExecutionTimeInMillisInFinxmlEnabled() throws Exception
    {
        try
        {
            WatchPropertyC.update( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcTransaction tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            // org level setting
            setSTPFormat( takerOrg, TradeServiceConstants.FinXML51v21_FORMAT );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            ts.newTrade( trd, null );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            ts.verifyTrade( trd, msg );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            log.debug( "takerSTPMessage in format=" + TradeServiceConstants.FinXML51v21_FORMAT + " : " + takerSTPMessage );
            Document xmlDoc1 = new Document( takerSTPMessage );
            String messageId1 = xmlDoc1.getElement( new XPath( XPATH_SINGLE_LEG_EXECUTION_DATE_TIME ) ).getTextString();
            assertNotNull ( messageId1 );

            sleepFor( 100 );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testTradeExecutionTimeInMillisInFinxmlEnabled", e );
        }
        finally
        {
            WatchPropertyC.update( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS_PREFIX + takerOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcUtilC.refreshObject( takerOrg );
        }
    }
}
  
