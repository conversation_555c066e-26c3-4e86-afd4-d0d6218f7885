package com.integral.finance.trade.test;

import com.integral.finance.counterparty.*;
import com.integral.finance.dealing.mifid.MiFIDTradeParams;
import com.integral.finance.dealing.mifid.MiFIDUtils;
import com.integral.finance.fx.*;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.mifid.MiFIDMBean;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.util.DealingTestUtilC;
import com.integral.user.ExecutingUserType;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import electric.xml.Document;
import electric.xml.XPath;
import java.util.*;

public class MiFIDTradeDownloadServiceTestC extends TradeServiceTestC
{
    protected final static String XPATH_SINGLELEG_ISIN = "/workflowMessage/fxSingleLeg/fxLeg/ISIN";
    protected final static String XPATH_SWAP_NEARLEG_ISIN = "/workflowMessage/fxSwap/nearLeg/ISIN";
    protected final static String XPATH_SWAP_FARLEG_ISIN = "/workflowMessage/fxSwap/farLeg/ISIN";
    protected final static String XPATH_SINGLELEG_COUNTERPARTYA_MIFID_INVESTMENT_FIRM = "/workflowMessage/fxSingleLeg/counterpartyA/MiFIDInvestmentFirm";
    protected final static String XPATH_SWAP_COUNTERPARTYA_MIFID_INVESTMENT_FIRM = "/workflowMessage/fxSwap/counterpartyA/MiFIDInvestmentFirm";
    protected final static String XPATH_SINGLELEG_MIFID2_MTF = "/workflowMessage/fxSingleLeg/MiFID2/MTF/shortName";
    protected final static String XPATH_SWAP_MIFID2_MTF = "/workflowMessage/fxSwap/MiFID2/MTF/shortName";
    protected final static String XPATH_SINGLELEG_MIFID2_MTF_MICCODE = "/workflowMessage/fxSingleLeg/MiFID2/MTF/MICCode";
    protected final static String XPATH_SINGLELEG_MIFID2_MTF_LEI = "/workflowMessage/fxSingleLeg/MiFID2/MTF/LEI";
    protected final static String XPATH_SWAP_MIFID2_MTF_MICCODE = "/workflowMessage/fxSwap/MiFID2/MTF/MICCode";
    protected final static String XPATH_SWAP_MIFID2_MTF_LEI = "/workflowMessage/fxSwap/MiFID2/MTF/LEI";
    protected final static String XPATH_SINGLELEG_TOORG_MICCODE = "/workflowMessage/to/MICCode";
    protected final static String XPATH_SINGLELEG_TOORG_LEI = "/workflowMessage/to/LEI";
    protected final static String XPATH_SWAP_TOORG_MICCODE = "/workflowMessage/to/MICCode";
    protected final static String XPATH_SWAP_TOORG_LEI = "/workflowMessage/to/LEI";
    protected final static String XPATH_SINGLELEG_MIFID2_TRADING_CAPACITY = "/workflowMessage/fxSingleLeg/MiFID2/tradingCapacity";
    protected final static String XPATH_SWAP_MIFID2_TRADING_CAPACITY = "/workflowMessage/fxSwap/MiFID2/tradingCapacity";
    protected final static String XPATH_SINGLELEG_MIFID2_TRADING_IDM = "/workflowMessage/fxSingleLeg/MiFID2/investmentDecisionMaker";
    protected final static String XPATH_SWAP_MIFID2_TRADING_IDM = "/workflowMessage/fxSwap/MiFID2/investmentDecisionMaker";
    protected final static String XPATH_SINGLELEG_ISIN_LINK_ID = "/workflowMessage/fxSingleLeg/ISINLinkId";
    protected final static String XPATH_SWAP_ISIN_LINK_ID = "/workflowMessage/fxSwap/ISINLinkId";
    protected final static String XPATH_SINGLELEG_MIFID2_BUYER = "/workflowMessage/fxSingleLeg/MiFID2/buyer";
    protected final static String XPATH_SWAP_MIFID2_BUYER = "/workflowMessage/fxSwap/MiFID2/buyer";
    protected final static String XPATH_SINGLELEG_MIFID2_SELLER = "/workflowMessage/fxSingleLeg/MiFID2/seller";
    protected final static String XPATH_SWAP_MIFID2_SELLER = "/workflowMessage/fxSwap/MiFID2/seller";
    protected final static String XPATH_SINGLELEG_MIFID2_CUSTOMER_COUNTRY_CODE = "/workflowMessage/fxSingleLeg/MiFID2/customerCountry";
    protected final static String XPATH_SWAP_MIFID2_CUSTOMER_COUNTRY_CODE = "/workflowMessage/fxSwap/MiFID2/customerCountry";
    protected final static String XPATH_SINGLELEG_MIFID2_TAKER_COUNTRY_CODE = "/workflowMessage/fxSingleLeg/MiFID2/takerCountry";
    protected final static String XPATH_SWAP_MIFID2_TAKER_COUNTRY_CODE = "/workflowMessage/fxSwap/MiFID2/takerCountry";
    protected final static String XPATH_SINGLELEG_MIFID2_MAKER_COUNTRY_CODE = "/workflowMessage/fxSingleLeg/MiFID2/makerCountry";
    protected final static String XPATH_SWAP_MIFID2_MAKER_COUNTRY_CODE = "/workflowMessage/fxSwap/MiFID2/makerCountry";
    protected final static String XPATH_SINGLELEG_BENCHMARK_MID = "/workflowMessage/fxSingleLeg/benchmarkMid";
    protected final static String XPATH_SWAP_BENCHMARK_MID = "/workflowMessage/fxSwap/benchmarkMid";
    protected final static String XPATH_SINGLELEG_UTI = "/workflowMessage/fxSingleLeg/fxLeg/UTI";
    protected final static String XPATH_SWAP_NEARLEG_UTI = "/workflowMessage/fxSwap/nearLeg/UTI";
    protected final static String XPATH_SWAP_FARLEG_UTI = "/workflowMessage/fxSwap/farLeg/UTI";
    protected final static String XPATH_SINGLELEG_UTI_NAMESPACE = "/workflowMessage/fxSingleLeg/fxLeg/UTINamespace";
    protected final static String XPATH_SWAP_NEARLEG_UTI_NAMESPACE = "/workflowMessage/fxSwap/nearLeg/UTINamespace";
    protected final static String XPATH_SWAP_FARLEG_UTI_NAMESPACE = "/workflowMessage/fxSwap/farLeg/UTINamespace";
    protected final static String XPATH_SINGLELEG_MIFID2_VENUE = "/workflowMessage/fxSingleLeg/MiFID2/venue";
    protected final static String XPATH_SWAP_MIFID2_VENUE = "/workflowMessage/fxSwap/MiFID2/venue";

    public MiFIDTradeDownloadServiceTestC( String name ) throws Exception
    {
        super( name );
    }

    public void testSingleLegISIN() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            String ISIN = "testISIN";
            spotTrade.getFXLeg ().setISIN ( ISIN );
            downloadingOrg.setShowMiFID ( true  );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_ISIN ) ).getTextString(), ISIN );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_ISIN ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegISIN", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSwapISIN() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            String ISIN1 = "testISINNear";
            String ISIN2 = "testISINFar";
            swapTrade.getNearLeg ().setISIN ( ISIN1 );
            swapTrade.getFarLeg ().setISIN ( ISIN2 );
            downloadingOrg.setShowMiFID ( true  );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_NEARLEG_ISIN ) ).getTextString(), ISIN1 );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_FARLEG_ISIN ) ).getTextString(), ISIN2 );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_NEARLEG_ISIN ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_FARLEG_ISIN ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapISIN", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSingleLegCounterpartyFields() throws Exception
    {
        Organization downloadingOrg = null;
        LegalEntity legalEntity = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            downloadingOrg.setShowMiFID ( true  );
            legalEntity = ( LegalEntity ) spotTrade.getCounterpartyA ();
            legalEntity.setJurisdiction ( CounterpartyUtilC.JURISDICTION_MIFID );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_COUNTERPARTYA_MIFID_INVESTMENT_FIRM ) ).getTextString(), "true" );

            legalEntity.setJurisdiction ( CounterpartyUtilC.JURISDICTION_EMIR );
            String xml1 = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml1 );
            Document xmlDoc1 = new Document( xml1 );
            assertEquals( xmlDoc1.getElement( new XPath( XPATH_SINGLELEG_COUNTERPARTYA_MIFID_INVESTMENT_FIRM ) ).getTextString(), "false" );


            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_COUNTERPARTYA_MIFID_INVESTMENT_FIRM ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegCounterpartyFields", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
            IdcUtilC.refreshObject ( legalEntity );
        }
    }

    public void testSwapCounterpartyFields() throws Exception
    {
        Organization downloadingOrg = null;
        LegalEntity legalEntity = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            downloadingOrg.setShowMiFID ( true  );
            legalEntity = ( LegalEntity ) swapTrade.getCounterpartyA ();
            legalEntity.setJurisdiction ( CounterpartyUtilC.JURISDICTION_MIFID );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_COUNTERPARTYA_MIFID_INVESTMENT_FIRM ) ).getTextString(), "true" );

            legalEntity.setJurisdiction ( CounterpartyUtilC.JURISDICTION_EMIR );
            String xml1 = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml1 );
            Document xmlDoc1 = new Document( xml1 );
            assertEquals( xmlDoc1.getElement( new XPath( XPATH_SWAP_COUNTERPARTYA_MIFID_INVESTMENT_FIRM ) ).getTextString(), "false" );


            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_COUNTERPARTYA_MIFID_INVESTMENT_FIRM ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapCounterpartyFields", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
            IdcUtilC.refreshObject ( legalEntity );
        }
    }

    public void testSingleLegMTFOrg() throws Exception
    {
        Organization downloadingOrg = null;
        Organization MTFOrg = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            downloadingOrg.setShowMiFID ( true  );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            MTFOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            MTFOrg.setMICCode ( "testMICCode" );
            MTFOrg.setLEI ( "testLEI" );
            MTFOrg.setMTFVenue ( true );
            downloadingOrg.setMICCode ( "testMICCode1" );
            downloadingOrg.setLEI ( "testLEI" );
            spotTrade.setExecutionVenue ( MTFOrg );
            spotTrade.setMiFIDTradeParams ( params );
            spotTrade.setMTFTrade ( true );

            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_MTF ) ).getTextString(), "Broker1" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_MTF_MICCODE ) ).getTextString(), "testMICCode" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_MTF_LEI ) ).getTextString(), "testLEI" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_TOORG_MICCODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_TOORG_LEI ) ), null );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_MTF ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_MTF_MICCODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_MTF_LEI ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_TOORG_MICCODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_TOORG_LEI ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegMTFOrg", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
            IdcUtilC.refreshObject ( MTFOrg );
        }
    }

    public void testSwapMTFOrg() throws Exception
    {
        Organization downloadingOrg = null;
        Organization MTFOrg = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            downloadingOrg.setShowMiFID ( true  );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            MTFOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            MTFOrg.setMICCode ( "testMICCode" );
            MTFOrg.setLEI ( "testLEI" );
            MTFOrg.setMTFVenue ( true );
            downloadingOrg.setMICCode ( "testMICCode1" );
            downloadingOrg.setLEI ( "testLEI" );
            swapTrade.setExecutionVenue ( MTFOrg );
            swapTrade.setMiFIDTradeParams ( params );
            swapTrade.setMTFTrade ( true );

            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_MTF ) ).getTextString(), "Broker1" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_MTF_MICCODE ) ).getTextString(), "testMICCode" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_MTF_LEI ) ).getTextString(), "testLEI" );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_TOORG_MICCODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_TOORG_LEI ) ), null );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_MTF ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_MTF_MICCODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_MTF_LEI ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_TOORG_MICCODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_TOORG_LEI ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapMTFOrg", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
            IdcUtilC.refreshObject ( MTFOrg );
        }
    }

    public void testSingleLegISINLinkId() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            String ISINLinkId = "testISINLinkId";
            spotTrade.setISINLinkId ( ISINLinkId );
            downloadingOrg.setShowMiFID ( true  );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_ISIN_LINK_ID ) ), null );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_ISIN_LINK_ID ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegISINLinkId", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSwapISINLinkId() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            String ISINLinkId = "testISINLinkId";
            swapTrade.setISINLinkId ( ISINLinkId );
            downloadingOrg.setShowMiFID ( true  );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_ISIN_LINK_ID ) ).getTextString(), ISINLinkId );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_ISIN_LINK_ID ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapISINLinkId", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSingleLegBuyerSellerFields() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            downloadingOrg.setShowMiFID ( true  );
            String buyer = "BuyerLEI";
            String seller = "SellerLEI";

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            params.setBuyerLEI ( buyer );
            params.setSellerLEI ( seller );
            spotTrade.setMiFIDTradeParams ( params );
            spotTrade.setMTFTrade ( true );


            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_BUYER ) ).getTextString(), buyer );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_SELLER ) ).getTextString(), seller );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_BUYER ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_SELLER ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegBuyerSellerFields", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSwapBuyerSellerFields() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            downloadingOrg.setShowMiFID ( true  );
            String buyer = "BuyerLEI";
            String seller = "SellerLEI";

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            params.setBuyerLEI ( buyer );
            params.setSellerLEI ( seller );
            swapTrade.setMiFIDTradeParams ( params );
            swapTrade.setMTFTrade ( true );

            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_BUYER ) ).getTextString(), buyer );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_SELLER ) ).getTextString(), seller );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_BUYER ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_SELLER ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapBuyerSellerFields", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSingleLegCountryFields() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            downloadingOrg.setShowMiFID ( true  );
            String customerCountry = "BE";
            String takerCountry = "FR";
            String makerCountry = "US";

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            params.setCustomerCountryCode ( customerCountry );
            params.setTakerCountryCode ( takerCountry );
            params.setMakerCountryCode ( makerCountry );
            spotTrade.setMiFIDTradeParams ( params );
            spotTrade.setMTFTrade ( true );


            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( customerCountry, xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_CUSTOMER_COUNTRY_CODE ) ).getTextString() );
            assertEquals( takerCountry, xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_TAKER_COUNTRY_CODE ) ).getTextString() );
            assertEquals( makerCountry, xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_MAKER_COUNTRY_CODE ) ).getTextString() );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );

            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_CUSTOMER_COUNTRY_CODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_TAKER_COUNTRY_CODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_MAKER_COUNTRY_CODE ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegCountryFields", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSwapCountryFields() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            downloadingOrg.setShowMiFID ( true  );
            String customerCountry = "BE";
            String takerCountry = "FR";
            String makerCountry = "US";

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            params.setCustomerCountryCode ( customerCountry );
            params.setTakerCountryCode ( takerCountry );
            params.setMakerCountryCode ( makerCountry );
            swapTrade.setMiFIDTradeParams ( params );
            swapTrade.setMTFTrade ( true );

            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_CUSTOMER_COUNTRY_CODE ) ).getTextString(), customerCountry );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_TAKER_COUNTRY_CODE) ).getTextString(), takerCountry );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_MAKER_COUNTRY_CODE) ).getTextString(), makerCountry );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_CUSTOMER_COUNTRY_CODE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_TAKER_COUNTRY_CODE) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_MAKER_COUNTRY_CODE) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapCountryFields", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSingleLegBenchmarkMid() throws Exception
    {
        Organization downloadingOrg = null;
        Organization cptyAOrg = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyB().getOrganization();
            cptyAOrg = spotTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            downloadingOrg.setShowMiFID ( true  );
            double benchmarkMid = 1.2345;

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            spotTrade.setMiFIDTradeParams ( params );
            spotTrade.setMTFTrade ( true );
            spotTrade.setMidBenchmarkRate2 ( benchmarkMid );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            cptyAOrg.setBrokerOrganization( downloadingOrg );
            MiFIDUtils.setMidBenchmarkAllinRate( spotTrade );

            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_BENCHMARK_MID) ).getTextString(), String.valueOf ( benchmarkMid ) );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );

            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_BENCHMARK_MID ) ).getTextString(), String.valueOf ( benchmarkMid ) );
            downloadingOrg.setShowMiFID ( true );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );

            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_BENCHMARK_MID ) ), null );


            downloadingOrg.setShowMiFID ( true );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            spotTrade.setMidBenchmarkAllInRate ( null );
            MiFIDUtils.setMidBenchmarkAllinRate( spotTrade );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );

            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_BENCHMARK_MID ) ), null );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );

            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_BENCHMARK_MID ) ), null );

            cptyAOrg.setBrokerOrganization( null );
            spotTrade.setMidBenchmarkAllInRate ( null );
            MiFIDUtils.setMidBenchmarkAllinRate( spotTrade );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );

            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_BENCHMARK_MID ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegBenchmarkMid", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            if ( cptyAOrg != null )
            {
                IdcUtilC.refreshObject( cptyAOrg );
            }
        }
    }

    public void testSwapBenchmarkMid() throws Exception
    {
        Organization downloadingOrg = null;
        Organization cptyAOrg = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyB().getOrganization();
            cptyAOrg = swapTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            downloadingOrg.setShowMiFID ( true  );
            double benchmarkMid = 1.2345;

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            swapTrade.setMiFIDTradeParams ( params );
            swapTrade.setMTFTrade ( true );
            swapTrade.setMidBenchmarkFwdPoints ( benchmarkMid );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            cptyAOrg.setBrokerOrganization( downloadingOrg );
            MiFIDUtils.setMidBenchmarkAllinRate( swapTrade );

            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_BENCHMARK_MID ) ).getTextString(), String.valueOf ( benchmarkMid ) );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_BENCHMARK_MID ) ).getTextString(), String.valueOf ( benchmarkMid ) );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            downloadingOrg.setShowMiFID ( true );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_BENCHMARK_MID ) ), null );


            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_BENCHMARK_MID ) ), null );


            downloadingOrg.setShowMiFID ( true );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            swapTrade.setMidBenchmarkAllInRate ( null );
            MiFIDUtils.setMidBenchmarkAllinRate( swapTrade );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );

            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_BENCHMARK_MID ) ), null );

            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            cptyAOrg.setBrokerOrganization( null );
            swapTrade.setMidBenchmarkAllInRate ( null );
            MiFIDUtils.setMidBenchmarkAllinRate( swapTrade );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );

            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_BENCHMARK_MID ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapBenchmarkMid", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_CAPTUREBENCHMARKMID, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            if ( cptyAOrg != null )
            {
                IdcUtilC.refreshObject( cptyAOrg );
            }
        }
    }


    public void testSingleLegUTIWithoutNamespace() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            String utiId = "testUTI";
            String utiNS = "testUTINamespace";
            String uti = utiNS + utiId;
            spotTrade.getFXLeg ().setUTI ( uti );
            spotTrade.getFXLeg ().setUTINamespace ( utiNS );
            downloadingOrg.setShowUTI ( true  );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_UTI ) ).getTextString(), uti );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_UTI_NAMESPACE) ).getTextString(), utiNS );

            WatchPropertyC.update ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED_PREFIX + downloadingOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_UTI ) ).getTextString(), utiId );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_UTI_NAMESPACE) ).getTextString(), utiNS );

            downloadingOrg.setShowUTI ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_UTI ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_UTI_NAMESPACE ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegUTIWithoutNamespace", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED_PREFIX + downloadingOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSwapUTIWithoutNamespace() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyA().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            String nearUtiId = "testUTI1";
            String nearUtiNS = "testUTI1Namespace";
            String nearUti = nearUtiNS + nearUtiId;
            swapTrade.getNearLeg ().setUTI( nearUti );
            swapTrade.getNearLeg ().setUTINamespace ( nearUtiNS );

            String farUtiId = "testUTI2";
            String farUtiNS = "testUTI2Namespace";
            String farUti = farUtiNS + farUtiId;
            swapTrade.getFarLeg ().setUTI( farUti );
            swapTrade.getFarLeg ().setUTINamespace ( farUtiNS );
            downloadingOrg.setShowUTI ( true  );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_NEARLEG_UTI ) ).getTextString(), nearUti );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_FARLEG_UTI ) ).getTextString(), farUti );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_NEARLEG_UTI_NAMESPACE ) ).getTextString(), nearUtiNS );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_FARLEG_UTI_NAMESPACE ) ).getTextString(), farUtiNS );

            WatchPropertyC.update ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED_PREFIX + downloadingOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_NEARLEG_UTI ) ).getTextString(), nearUtiId );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_FARLEG_UTI ) ).getTextString(), farUtiId );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_NEARLEG_UTI_NAMESPACE ) ).getTextString(), nearUtiNS );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_FARLEG_UTI_NAMESPACE ) ).getTextString(), farUtiNS );

            downloadingOrg.setShowUTI ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_NEARLEG_ISIN ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_FARLEG_ISIN ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_NEARLEG_UTI_NAMESPACE ) ), null );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_FARLEG_UTI_NAMESPACE ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapUTIWithoutNamespace", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED_PREFIX + downloadingOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSingleLegMakerVenue() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            TradeCloneServiceC.createCptyTrades( spotTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = spotTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( spotTrade );
            downloadingOrg.setShowMiFID ( true  );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            spotTrade.setMiFIDTradeParams ( params );
            spotTrade.setMTFTrade ( true );


            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_VENUE ) ).getTextString(), MiFIDUtils.MTF );

            spotTrade.setMTFTrade ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SINGLELEG_MIFID2_VENUE ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSingleLegMakerVenue", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }

    public void testSwapMakerVenue() throws Exception
    {
        Organization downloadingOrg = null;
        try
        {
            FXSwap swapTrade = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( swapTrade );
            TradeCloneServiceC.createCptyTrades( swapTrade );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            downloadingOrg = swapTrade.getCounterpartyB().getOrganization();
            msg.setProperty( "organization", downloadingOrg );
            msg.setObject( swapTrade );
            downloadingOrg.setShowMiFID ( true  );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            swapTrade.setMiFIDTradeParams ( params );
            swapTrade.setMTFTrade ( true );

            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            Document xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_VENUE) ).getTextString(), MiFIDUtils.MTF );

            downloadingOrg.setShowMiFID ( false );
            xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( msg, null );
            log( xml );
            xmlDoc = new Document( xml );
            assertEquals( xmlDoc.getElement( new XPath( XPATH_SWAP_MIFID2_VENUE ) ), null );
        }
        catch ( Exception e )
        {
            fail ( "testSwapMakerVenue", e );
        }
        finally
        {
            IdcUtilC.refreshObject( downloadingOrg );
        }
    }
}

