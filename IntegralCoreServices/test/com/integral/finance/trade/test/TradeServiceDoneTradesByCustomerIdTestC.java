package com.integral.finance.trade.test;

import com.integral.client.ClientServiceC;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeServiceC;
import com.integral.finance.trade.TradeService;
import com.integral.message.query.NamedCriteria;
import com.integral.message.query.QueryCriteria;
import com.integral.message.query.QueryMessage;
import com.integral.message.query.QueryMessageFactory;
import com.integral.message.query.QueryParameter;
import com.integral.message.query.QueryResult;
import com.integral.message.query.ResultItem;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;

import java.util.ArrayList;
import java.util.Collection;

// Copyright (c) 1999-2006 Integral Development Corp.  All rights reserved.

/**
 * TradeServiceDoneTradesByCustomerIdTestC
 *
 * <AUTHOR> Development Corp.
 */

public class TradeServiceDoneTradesByCustomerIdTestC extends TradeServiceDoneTradesTestC
{
    public TradeServiceDoneTradesByCustomerIdTestC( String name )
    {
        super( name );
    }

    public void testGetDoneTradesInDifferentNamespace()
    {
        log( "Test# 0 --- Test Done Trades with creating trade in FI namespace and then querying using LP namespace" );
        IdcSessionContext fiSessContext = IdcSessionManager.getInstance().getSessionContext( fiUser );
        IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        String cid = "CID" + System.nanoTime();
        String txID = createTrade( 1, null, null, cid );         // created trade in FI namespace

        fiSessContext = IdcSessionManager.getInstance().getSessionContext( lpUser );
        IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        QueryResult result = callTradeService( cid );
        assertEquals( "Valid TxID result count:", 0, result.getCount() );
        // now set sessioncontext back to fi and then test; now it should give u the record
        fiSessContext = IdcSessionManager.getInstance().getSessionContext( fiUser );
        IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        result = callTradeService( cid );
        assertEquals( "Valid TxID result count:", 1, result.getCount() );
        Trade trade = ( Trade ) ( ( ResultItem ) result.getResultItems().get( 0 ) ).getResult();
        assertEquals( "Valid TxID:", txID, trade.getTransactionID() );
    }

    public void testGetDoneTradesInDifferentNamespaceCallClientService()
    {
        log( "Test# 0 --- Test Done Trades with creating trade in FI namespace and then querying using LP namespace" );
        IdcSessionContext fiSessContext = IdcSessionManager.getInstance().getSessionContext( fiUser );
        IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        String cid = "CID" + System.nanoTime();
        String txID = createTrade( 1, null, null, cid );         // created trade in FI namespace
        String cid2 = "CID" + System.nanoTime() + 100;
        String txID2 = createTrade( 1, null, null, cid2 );         // created trade in FI namespace

        QueryResult result = callClientService( cid ); // call normal workflow in same namespace
        assertEquals( "Valid TxID result count:", 1, result.getCount() );
        Trade trade = ( Trade ) ( ( ResultItem ) result.getResultItems().get( 0 ) ).getResult();
        assertEquals( "Valid TxID:", txID, trade.getTransactionID() );

       // fiSessContext = IdcSessionManager.getInstance().getSessionContext( lpUser );   // now clientService has been modified such that we should return only throse trades which belong to a user.
        //IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        result = callClientService( cid );
        assertEquals( "Valid TxID result count:", 1, result.getCount() ); // CITI and FI1 has LP_to_FI has relationship set

        fiSessContext = IdcSessionManager.getInstance().getSessionContext( makerUser ); //fi2mm1
        IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        result = callClientService( cid );
        assertEquals( "Valid TxID result count:", 0, result.getCount() ); // FI2 and FI1 DOES NOT have LP_to_FI has relationship set

        //repeat the process with multiple trades
        fiSessContext = IdcSessionManager.getInstance().getSessionContext( fiUser );
        IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        cid += ',' + cid2;
        result = callClientService( cid ); // call normal workflow in same namespace
        assertEquals( "Valid TxID result count:", 2, result.getCount() );
        trade = ( Trade ) ( ( ResultItem ) result.getResultItems().get( 0 ) ).getResult();
        assertEquals( "Valid TxID:", txID, trade.getTransactionID() );
        trade = ( Trade ) ( ( ResultItem ) result.getResultItems().get( 1 ) ).getResult();
        assertEquals( "Valid TxID:", txID2, trade.getTransactionID() );

    //    fiSessContext = IdcSessionManager.getInstance().getSessionContext( lpUser ); // now clientService has been modified such that we should return only throse trades which belong to a user.
     //   IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        result = callClientService( cid );
        assertEquals( "Valid TxID result count:", 2, result.getCount() ); // CITI and FI1 has LP_to_FI has relationship set

        fiSessContext = IdcSessionManager.getInstance().getSessionContext( makerUser ); //fi2mm1
        IdcSessionManager.getInstance().setSessionContext( fiSessContext );

        result = callClientService( cid );
        assertEquals( "Valid TxID result count:", 0, result.getCount() ); // FI2 and FI1 DOES NOT have LP_to_FI has relationship set
    }

    private QueryResult callClientService( String cid )
    {
        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_CUSTOMERID_PARAM );
        queryParam.setValue( cid );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_CUSTOMERID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        ClientServiceC cs = new ClientServiceC();
        QueryMessage resultMessage = cs.invokeTradeServiceDoneTrades( service, queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        return result;

    }

    private QueryResult callTradeService( String cid )
    {
        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_CUSTOMERID_PARAM );
        queryParam.setValue( cid );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_CUSTOMERID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        return result;

    }

    public void testDoneTradesBySingleValidCustomerID()
    {
        log( "Test# 1 --- Test Done Trades with Single Valid Customer ID" );

        String cid = "CID" + System.nanoTime();
        String txID = createTrade( 1, null, null, cid );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_CUSTOMERID_PARAM );
        queryParam.setValue( cid );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_CUSTOMERID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );

        assertEquals( "Valid TxID result count:", 1, result.getCount() );

        Trade trade = ( Trade ) ( ( ResultItem ) result.getResultItems().get( 0 ) ).getResult();
        assertEquals( "Valid TxID:", txID, trade.getTransactionID() );

    }

    public void testDoneTradesBySingleInvalidCustomerID()
    {

        log( "Test# 2 --- Test Done Trades with Single Invalid customer ID" );

        String txID = "TX" + System.currentTimeMillis();

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_CUSTOMERID_PARAM );
        queryParam.setValue( txID );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_CUSTOMERID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 2 );
        assertEquals( "Inalid TxID result count:", 0, result.getCount() );
    }

    public void testDoneTradesByMultipleValidCustomerID()
    {
        log( "Test# 3 --- Test Done Trades with Multiple Valid customer ID" );

        long time = System.nanoTime();
        String cid = "CID" + time;
        String cid1 = "CID" + ( time + 1000 );
        String cid2 = "CID" + ( time + 10000 );

        String txID = createTrade( 1, null, null, cid );
        String txID1 = createTrade( 2, null, null, cid1 );
        String txID2 = createTrade( 3, null, null, cid2 );

        String txIDs = txID + ',' + txID1 + ',' + txID2;
        String cids = cid + ',' + cid1 + ',' + cid2;

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_CUSTOMERID_PARAM );
        queryParam.setValue( cids );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_CUSTOMERID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        assertEquals( "Valid TxID result count:", 3, result.getCount() );

        String retrievedIDs = getTxIDString( result );

        assertEquals( "Valid TxIDs:", txIDs, retrievedIDs );

    }

    public void testDoneTradesByMultipleInvalidProviderID()
    {
        log( "Test# 4 --- Test Done Trades with Multiple Invalid customer ID" );

        String cid = "TX" + System.currentTimeMillis();
        String cid1 = "TX" + System.currentTimeMillis();
        String cid2 = "TX" + System.currentTimeMillis();

        String cids = cid + ',' + cid1 + ',' + cid2;

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_CUSTOMERID_PARAM );
        queryParam.setValue( cids );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_CUSTOMERID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        assertEquals( "Valid TxID result count:", 0, result.getCount() );
    }

    public void testDoneTradesBySomeInvalidCustomerID()
    {
        log( "Test# 5 --- Test Done Trades with Some Invalid customer ID" );

        String cid = "CID" + System.nanoTime();

        String txID = "TX" + System.currentTimeMillis();
        String txID1 = createTrade( 1, null, null, cid );
        String txID2 = "TX" + ( System.currentTimeMillis() + 1000 );

        String pids = txID + ',' + cid + ',' + txID1 + ',' + txID2;

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_CUSTOMERID_PARAM );
        queryParam.setValue( pids );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_CUSTOMERID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        assertEquals( "Valid TxID result count:", 1, result.getCount() );

        Trade trade = ( Trade ) ( ( ResultItem ) result.getResultItems().get( 0 ) ).getResult();
        assertEquals( "Valid TxID:", txID1, trade.getTransactionID() );
    }

    public void testDoneTradesByNoCustomerID()
    {
        log( "Test# 6 --- Test Done Trades with No customer ID" );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_CUSTOMERID_PARAM );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_CUSTOMERID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        assertEquals( "Valid error count:", false, resultMessage.getErrors().isEmpty() );
        assertEquals( "Valid result count:", null, result );
    }
}
