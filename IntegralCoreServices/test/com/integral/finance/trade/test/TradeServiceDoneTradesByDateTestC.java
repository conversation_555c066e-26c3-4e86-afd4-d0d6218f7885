package com.integral.finance.trade.test;

import com.integral.client.ClientServiceC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.TradeServiceC;
import com.integral.message.ErrorMessageC;
import com.integral.message.MessageStatus;
import com.integral.message.query.NamedCriteria;
import com.integral.message.query.QueryCriteria;
import com.integral.message.query.QueryMessage;
import com.integral.message.query.QueryMessageFactory;
import com.integral.message.query.QueryParameter;
import com.integral.message.query.QueryResult;
import com.integral.message.query.ResultItem;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.util.DealingTestUtilC;
import com.integral.user.User;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

// Copyright (c) 1999-2006 Integral Development Corp.  All rights reserved.

/**
 * TradeServiceDoneTradesByDateTestC
 *
 * <AUTHOR> Development Corp.
 */

public class TradeServiceDoneTradesByDateTestC extends TradeServiceDoneTradesTestC
{
    public static final SimpleDateFormat DONE_TRADES_DATE_DATEFORMAT = new SimpleDateFormat( TradeService.DONE_TRADES_DATE_DATEFORMAT_STRING );

    public TradeServiceDoneTradesByDateTestC( String name )
    {
        super( name );
    }

    public void testCallingClientService() throws Exception
    {
        QueryMessage result = callClientService( "2008-06-06", "2008-08-08" );
        //assertEquals( "Valid TxID result count:", 1, result.getCount() );
        System.out.println( result.getQueryResult() );

    }

    public void testCallingClientService39242() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        deleteAllDealingData( tx.getUOW() );
        for ( int i = 0; i < 110; i++ ) // by default the query returns 100 records; so create 110 records
        {
            createTrade( 1, ( new Date() ) );
        }

        QueryMessage result = callClientService( "2008-06-06", DONE_TRADES_DATE_DATEFORMAT.format( new Date() ) );
        assertEquals( result.getQueryResult().getResultItems().size(), 0 );
        assertEquals( result.getStatus().toString(), MessageStatus.FAILURE.toString() );
        assertEquals( result.getErrors().size(), 1 );
        assertEquals( ( ( ErrorMessageC ) result.getErrors().iterator().next() ).getCode().indexOf( "Large number of records " ) >= 0, true );
        tx.release();
    }

    public void testCallingClientServiceSequencing39254() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        deleteAllDealingData( tx.getUOW() );
        IdcSessionManager.getInstance().setSessionContext( IdcSessionManager.getInstance().getSessionContext( takerUser.getFullyQualifiedName() ) );
        for ( int i = 0; i < 5; i++ )
        {
            DealingTestUtilC.createTestSingleLegTrade( 1000, true, false, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            DealingTestUtilC.createTestSingleLegTrade( 1000, true, false, true, EURUSD, takerOrg, takerTpForMaker, bidRates[0], spotDate );
        }
        uow.commitAndResume();

        QueryMessage result = callClientService( "2008-06-06", DONE_TRADES_DATE_DATEFORMAT.format( new Date() ) );
        assertEquals( result.getQueryResult().getResultItems().size(), 5 );
        int i = 1;
        /*for ( Object obj : result.getQueryResult().getResultItems() ) // this cannot be done because in ClientService we remove some trades from the result set and those removed can be from between.. so sequencing can be improper
        {
            ResultItem item = ( ResultItem ) obj;
            assertEquals( item.getNum(), i++ );
        }*/
        tx.release();
    }

    private QueryMessage callClientService( String fromDate, String toDate )
    {
        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
        queryParam.setValue( fromDate );
        params.add( queryParam );
        queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
        queryParam.setValue( toDate );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_DATE );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        com.integral.finance.trade.TradeServiceC service = new com.integral.finance.trade.TradeServiceC();
        ClientServiceC cs = new ClientServiceC();
        QueryMessage resultMessage = cs.invokeTradeServiceDoneTrades( service, queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        return resultMessage;

    }

    public void testDoneTradesBySingleValidDate()
    {
        log( "Test# 1 --- Test Done Trades with valid Date Range" );

        Date tradeDate = null;
        try
        {
            tradeDate = DONE_TRADES_DATE_DATEFORMAT.parse( "2006-07-01" );
        }
        catch ( ParseException exc )
        {
            log.error( "Error occurred during parsing", exc );
        }

        String txID = createTrade( 1, tradeDate );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
        queryParam.setValue( "2006-07-01" );
        params.add( queryParam );
        queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
        queryParam.setValue( "2006-07-01" );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_DATE );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );

        String recievedTxID = "";

        for ( Object o : result.getResultItems() )
        {
            ResultItem item = ( ResultItem ) o;
            Trade trade = ( Trade ) item.getResult();
            if ( trade.getTransactionID().equals( txID ) )
            {
                recievedTxID = trade.getTransactionID();
            }
        }
        assertEquals( "Valid TxID:", txID, recievedTxID );

    }

    public void testDoneTradesBySingleValidDateExecDate()
    {
        int count = TradeConfigurationFactory.getTradeConfigurationMBean().getMaxNumDoneTrades();
        try
        {
            WatchPropertyC.update(TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, "1000000", ConfigurationProperty.DYNAMIC_SCOPE);
            helpDoneTradesBySingleValidDateExecDate(false);
        }
        finally
        {
            WatchPropertyC.update(TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, String.valueOf( count ), ConfigurationProperty.DYNAMIC_SCOPE);
        }
    }

    public void testDoneCptyTradesBySingleValidDateExecDate()
    {
        helpDoneTradesBySingleValidDateExecDate( true );
    }

    public void helpDoneTradesBySingleValidDateExecDate( boolean cptyTradesTest )
    {
        log( "Test# 1 --- Test Done Trades with valid Exec Date Range" );
        try
        {
            IdcTransaction tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXSingleLeg trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            trd1.setEntryUser( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() );
            prepareSingleLegRequest( trd1 );
            TradeCloneServiceC.createCptyTrades( trd1 );
            
            String txID = trd1.getTransactionID();
            uow.commit();
            tx.release();
            Date fromDate = new Date();
            fromDate.setHours( fromDate.getHours() - 1 );
            Date toDate = new Date();
            toDate.setMinutes(toDate.getMinutes() + 5);
            Collection<QueryParameter> params = new ArrayList<QueryParameter>();
            QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
            queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
            queryParam.setValue( new SimpleDateFormat( TradeServiceConstants.DONE_TRADES_EXEC_DATE_DATE_TIME_FORMAT_STR ).format( fromDate ) );
            params.add( queryParam );
            queryParam = QueryMessageFactory.newQueryParameter();
            queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
            queryParam.setValue( new SimpleDateFormat( TradeServiceConstants.DONE_TRADES_EXEC_DATE_DATE_TIME_FORMAT_STR ).format( toDate ) );
            params.add( queryParam );

            QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
            QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
            NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
            namedCriteria.setQueryName( TradeServiceConstants.DONE_TRADES_EXEC_DATE );
            namedCriteria.setQueryParameters( params );
            queryCriteria.setClassName( Trade.class.toString() );
            queryCriteria.setNamedCriteria( namedCriteria );
            queryMessage.setQueryCriteria( queryCriteria );

            TradeServiceC service = new TradeServiceC();
            if (cptyTradesTest)
            {
                queryMessage.setProperty( com.integral.finance.trade.TradeService.GET_DONE_CPTY_TRADES , "true" );
            }
            //PersistenceFactory.getPersistenceMBean().setDatabaseLogSQL(true);
            //PersistenceFactory.getPersistenceMBean().setToplinkLogDefaultDebuglevel(true);
            QueryMessage resultMessage = service.getDoneTrades( queryMessage );
            QueryResult result = resultMessage.getQueryResult();

            printResult( result, 1 );

            String recievedTxID = "";

            for ( Object o : result.getResultItems() )
            {
                ResultItem item = ( ResultItem ) o;
                Trade trade = ( Trade ) item.getResult();
                if ( trade.getTransactionID().equals( txID ) )
                {
                    recievedTxID = trade.getTransactionID();
                }
            }
            assertEquals( "Valid TxID:", txID, recievedTxID );
        }
        catch ( Exception ex )
        {
            fail();
        }


    }

    public void testDoneTradesByInvalidDate()
    {
        log( "Test# 2 --- Test Done Trades with invalid Date Range" );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
        queryParam.setValue( "1997-07-01" );
        params.add( queryParam );
        queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
        queryParam.setValue( "1998-07-01" );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_DATE );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );

        assertEquals( "Valid Date result count:", 0, result.getCount() );

    }

    public void testDoneTradesByNoFromDate()
    {
        log( "Test# 3 --- Test Done Trades with no fromDate" );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
        queryParam.setValue( "1998-07-01" );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_DATE );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        assertEquals( "Valid error count:", false, resultMessage.getErrors().isEmpty() );
        assertEquals( "Valid result count:", null, result );
    }

    public void testDoneTradesByNoToDate()
    {
        log( "Test# 4 --- Test Done Trades with no toDate" );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
        queryParam.setValue( "1998-07-01" );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_DATE );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        assertEquals( "Valid error count:", false, resultMessage.getErrors().isEmpty() );
        assertEquals( "Valid result count:", null, result );
    }

    public void testDoneTradesByInvalidFromDateFormat()
    {
        log( "Test# 5 --- Test Done Trades with invalid fromDate format" );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
        queryParam.setValue( "19970701" );
        params.add( queryParam );
        queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
        queryParam.setValue( "1998-07-01" );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_DATE );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        assertEquals( "Valid error count:", false, resultMessage.getErrors().isEmpty() );
        assertEquals( "Valid result count:", null, result );
    }

    public void testDoneTradesByInvalidToDateFormat()
    {
        log( "Test# 3 --- Test Done Trades with invalid toDate format" );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
        queryParam.setValue( "1997-07-01" );
        params.add( queryParam );
        queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
        queryParam.setValue( "19980701" );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_DATE );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        assertEquals( "Valid error count:", false, resultMessage.getErrors().isEmpty() );
        assertEquals( "Valid result count:", null, result );
    }

    public void testDoneTradesBySingleValidDateBusinessExecDate()
    {
        int count = TradeConfigurationFactory.getTradeConfigurationMBean().getMaxNumDoneTrades();
        try
        {
            WatchPropertyC.update(TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, "1000000", ConfigurationProperty.DYNAMIC_SCOPE);
            helpDoneTradesBySingleValidDateBusinessExecDate(false);
        }
        finally
        {
            WatchPropertyC.update(TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, String.valueOf( count ), ConfigurationProperty.DYNAMIC_SCOPE);
        }
    }

    public void testDoneCptyTradesBySingleValidDateBusinessExecDate()
    {
        helpDoneTradesBySingleValidDateBusinessExecDate( true );
    }


    public void helpDoneTradesBySingleValidDateBusinessExecDate( boolean cptyTradesTest )
    {
        log( "Test# 1 --- Test Done Trades with valid business Exec Date Range" );
        try
        {
            IdcTransaction tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXSingleLeg trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            trd1.setEntryUser( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() );
            prepareSingleLegRequest( trd1 );
            TradeCloneServiceC.createCptyTrades( trd1 );
            long now = System.currentTimeMillis();
            trd1.setExecutionDateTime( new Date( now ) );
            trd1.setBusinessExecutionDate( new Timestamp( now - 20000 ) );

            String txID1 = trd1.getTransactionID();

            FXSingleLeg trd2 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
            trd2.setEntryUser( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() );
            prepareSingleLegRequest( trd2 );
            TradeCloneServiceC.createCptyTrades( trd2 );

            String txID2 = trd2.getTransactionID();
            trd2.setExecutionDateTime( new Date( now - 20000 ) );
            trd2.setBusinessExecutionDate( new Timestamp( now ) );

            uow.commit();
            tx.release();
            Date fromDate = new Date( now - 10000 );
            Date toDate = new Date( now + 10000 );
            Collection<QueryParameter> params = new ArrayList<QueryParameter>();
            QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
            queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
            queryParam.setValue( new SimpleDateFormat( TradeServiceConstants.DONE_TRADES_EXEC_DATE_DATE_TIME_FORMAT_STR ).format( fromDate ) );
            params.add( queryParam );
            queryParam = QueryMessageFactory.newQueryParameter();
            queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
            queryParam.setValue( new SimpleDateFormat( TradeServiceConstants.DONE_TRADES_EXEC_DATE_DATE_TIME_FORMAT_STR ).format( toDate ) );
            params.add( queryParam );

            QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
            QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
            NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
            namedCriteria.setQueryName( TradeServiceConstants.DONE_TRADES_EXEC_DATE );
            namedCriteria.setQueryParameters( params );
            queryCriteria.setClassName( Trade.class.toString() );
            queryCriteria.setNamedCriteria( namedCriteria );
            queryMessage.setQueryCriteria( queryCriteria );

            TradeServiceC service = new TradeServiceC();
            if (cptyTradesTest)
            {
                queryMessage.setProperty( com.integral.finance.trade.TradeService.GET_DONE_CPTY_TRADES , "true" );
            }
            //PersistenceFactory.getPersistenceMBean().setDatabaseLogSQL(true);
            //PersistenceFactory.getPersistenceMBean().setToplinkLogDefaultDebuglevel(true);
            WatchPropertyC.update( TradeConfigurationMBean.DONE_TRADES_QUERY_USE_BUSINESS_EXECDATE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            QueryMessage resultMessage = service.getDoneTrades( queryMessage );
            QueryResult result = resultMessage.getQueryResult();

            printResult( result, 1 );

            String recievedTxID = "";

            for ( Object o : result.getResultItems() )
            {
                ResultItem item = ( ResultItem ) o;
                Trade trade = ( Trade ) item.getResult();
                if ( trade.getTransactionID().equals( txID1 ) )
                {
                    recievedTxID = trade.getTransactionID();
                }
            }
            assertEquals( "Valid TxID:", txID1, recievedTxID );

            //

            WatchPropertyC.update( TradeConfigurationMBean.DONE_TRADES_QUERY_USE_BUSINESS_EXECDATE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            QueryMessage resultMessage2 = service.getDoneTrades( queryMessage );
            QueryResult result2 = resultMessage2.getQueryResult();

            printResult( result2, 1 );

            String recievedTxID2 = "";

            for ( Object o : result2.getResultItems() )
            {
                ResultItem item = ( ResultItem ) o;
                Trade trade = ( Trade ) item.getResult();
                if ( trade.getTransactionID().equals( txID2 ) )
                {
                    recievedTxID2 = trade.getTransactionID();
                }
            }
            assertEquals( "Valid TxID:", txID2, recievedTxID2 );

        }
        catch ( Exception ex )
        {
            fail();
        }
        finally
        {
            WatchPropertyC.update( TradeConfigurationMBean.DONE_TRADES_QUERY_USE_BUSINESS_EXECDATE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }

    }
}
