package com.integral.finance.trade.test;

import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditLimitWorkflowState;
import com.integral.finance.creditLimit.CreditLimitWorkflowStateUtilC;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.admin.CreditLimitAdminService;
import com.integral.finance.creditLimit.admin.CreditLimitAdminServiceFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.DealC;
import com.integral.finance.fx.FXCoverRate;
import com.integral.finance.fx.FXCoverRateC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.*;
import com.integral.finance.trade.calculator.TradeDownloadKondorMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadTOFMessageBuilderC;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.is.ISCommonConstants;
import com.integral.message.ErrorMessage;
import com.integral.message.MessageEvent;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;
import com.integral.workflow.NamedWorkflowState;
import com.integral.workflow.WorkflowStateMap;
import electric.xml.Document;
import electric.xml.XPath;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.StringTokenizer;

public class TradeServiceManualTradesTest extends TradeServiceTestC
{
    public static final String headerSpot = "TradeDate(MM/dd/yyyy),ValueDate(MM/dd/yyyy),CurrencyPair,TradeType,OrganizationBuySell,BaseAmount,TermAmount,Rate,TakerOrganization,CounterpartyOrganization,OrganizationAccount,CounterpartyAccount,EnableSTPDownload,OrganizationReferenceID,CounterpartyReferenceID,OrganizationUser,CounterpartyUser,DealtCurrency,CoverTradeIds,CoveredTradeId";
    public static final String headerOutright = "TradeDate(MM/dd/yyyy),ValueDate(MM/dd/yyyy),CurrencyPair,TradeType,OrganizationBuySell,BaseAmount,TermAmount,ForwardPoints,SpotRate,Rate,TakerOrganization,CounterpartyOrganization,OrganizationAccount,CounterpartyAccount,EnableSTPDownload,OrganizationReferenceID,CounterpartyReferenceID,OrganizationUser,CounterpartyUser,DealtCurrency,CoverTradeIds,CoveredTradeId";

    public static final String headerSwap = "TradeDate(MM/dd/yyyy),ValueDate(MM/dd/yyyy),FarValueDate(MM/dd/yyyy),CurrencyPair,TradeType,OrganizationBuySell,FarOrganizationBuySell,BaseAmount,FarBaseAmount,TermAmount,FarTermAmount,ForwardPoints,FarForwardPoints,SpotRate,FarSpotRate,Rate,TakerOrganization,CounterpartyOrganization,OrganizationAccount,CounterpartyAccount,EnableSTPDownload,OrganizationReferenceID,CounterpartyReferenceID,OrganizationUser,CounterpartyUser,DealtCurrency,CoverTradeIds,CoveredTradeId";
    //private static final String ManualTradeNotificationFunctorC = "com.integral.is.functor.ManualTradeNotificationFunctorC";
    //private static final String ManualTradeLocalNotificationFunctorC = "com.integral.is.common.admin.ManualTradeLocalNotificationFunctorC";
    TradeLoaderC loader = new TradeLoaderC();

    public TradeServiceManualTradesTest( String name ) throws Exception
    {
        super( name );
        loader.setHttpRequestParameters( new HashMap() );
        loader.setRunEmbedded( false );
        loader.trdServiceJavaClass = ts;

        //TradeRemoteNotificationFunctorServerC.getInstance().addTradeRemoteNotificationFunctor( TradeService.MANUAL_TRADE_EVENT, ManualTradeNotificationFunctorC );
        //TradeRemoteNotificationFunctorServerC.getInstance().addTradeLocalNotificationFunctor( TradeService.MANUAL_TRADE_EVENT, ManualTradeLocalNotificationFunctorC );

    }

    private Trade execute( String data, String trdType ) throws Exception
    {
        init( takerUser );
        HashMap map = getMap( data, trdType );
        Trade trd = null;
        boolean isSwap = TradeLoaderC.SPOT_SWAP_TRADE_TYPE.equals( trdType ) || TradeLoaderC.FWD_SWAP_TRADE_TYPE.equals( trdType );
        trd = loader.loadTrade( map );
        if ( trd != null )
        {
            trd = ( Trade ) IdcUtilC.refreshObject( trd );
            FXPaymentParameters params = null;
            if ( isSwap )
            {
                params = ( ( FXSwap ) trd ).getNearLeg().getFXPayment();
            }
            else
            {
                params = ( ( FXSingleLeg ) trd ).getFXLeg().getFXPayment();
            }

            assertEquals( trd.getCounterpartyA(), takerLe );
            assertEquals( CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ), lpLe );
            assertEquals( params.getCurrency1().getShortName() + '/' + params.getCurrency2().getShortName(), EURUSD );
            assertEquals( params.isBuyingCurrency1(), false );
            //assertEquals( trd.getCptyTrades().size(), 2 );
            assertEquals( params.isDealtCurrency1(), false );
            //all deal related test cases now
            assertNotNull( trd.getMaskedLP() );

            if ( !tradeConfig.isDealOrderOraclePersistenceDisabled( trd.getCounterpartyA().getOrganization().getShortName() ) )
            {
                Expression expr = new ExpressionBuilder().get( "transactionId" ).equal( trd.getTransactionID() );
                Collection<Deal> deals = ( Collection<Deal> ) getPersistenceSession().readAllObjects( DealC.class, expr );
                assertNotNull( deals );
                assertTrue( deals.size() > 0 );
                Deal deal = deals.iterator().next();
                assertNotNull( deal );
                assertEquals( deal.getMaskedLP(), trd.getMaskedLP() );
            }
            return trd;
        }
        return null;
    }

    public void testSpot() throws Exception
    {
        Trade refreshedCreatedTrade = execute( "01/03/2013,01/06/2013," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.SPOT_TRADE_TYPE );
        FXPaymentParameters params = ( ( FXSingleLeg ) refreshedCreatedTrade ).getFXLeg().getFXPayment();
        //assertEquals( params.getValueDate(), new IdcDateC(4,23,2008) );
        assertEquals( params.getCurrency1Amount(), 200000d );
        assertEquals( params.getCurrency2Amount(), 296000d );
        assertEquals( params.getFXRate().getRate(), 1.48 );
        assertEquals( refreshedCreatedTrade.getTradeClassification().getShortName(), TradeLoaderC.SPOT_TRADE_TYPE );
        assertEquals( refreshedCreatedTrade.getTradeDate(), DateTimeFactory.newDate( 1, 3, 2013 ) );

        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setProperty( "organization", refreshedCreatedTrade.getCounterpartyA().getOrganization() );
        wm.setObject( refreshedCreatedTrade );
        String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm, null );
        log( "xml message is=" + xml );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );
        Document xmlDoc = new Document( xml );

        //bug id 39312
        assertEquals( xmlDoc.getElement( new electric.xml.XPath( XPATH_SINGLE_LEG_CPTY_DEALT_CURRENCY ) ).getTextString(), "USD" );
        assertEquals( xmlDoc.getElement( new electric.xml.XPath( "/workflowMessage/fxSingleLeg/maturityDate" ) ).getTextString(), "2013-01-06" );
        assertEquals( xmlDoc.getElement( new electric.xml.XPath( "/workflowMessage/fxSingleLeg/settlementDate" ) ).getTextString(), "2013-01-06" );
        assertEquals( xmlDoc.getElement( new electric.xml.XPath( "/workflowMessage/fxSingleLeg/settlementDateRule" ) ).getTextString(), "SPOT" );
        assertEquals( xmlDoc.getElement( new electric.xml.XPath( "/workflowMessage/fxSingleLeg/fxLeg/fxPayment/fxSettlementDateRule" ) ).getTextString(), "SPOT" );


    }

    public void testDifferentDownloadFormats() throws Exception
    {
        Trade refreshedCreatedTrade = execute( "04/23/2008,04/25/2008," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.SPOT_TRADE_TYPE );
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setProperty( "organization", refreshedCreatedTrade.getCounterpartyA().getOrganization() );
        msg.setEvent( MessageEvent.CREATE );
        msg.setObject( refreshedCreatedTrade );
        refreshedCreatedTrade.setCptyTrade( refreshedCreatedTrade.getCptyTrade( 'A' ) );
        String xml = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( msg, null );
        log( "Original trade in kondor format is " + xml );
        xml = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );
        log( "Original trade in TOF format is " + xml );

        refreshedCreatedTrade = execute( "04/23/2008,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.OUTRIGHT_TRADE_TYPE );
        msg = MessageFactory.newWorkflowMessage();
        msg.setEvent( MessageEvent.CREATE );
        msg.setProperty( "organization", refreshedCreatedTrade.getCounterpartyA().getOrganization() );
        msg.setObject( refreshedCreatedTrade );
        refreshedCreatedTrade.setCptyTrade( refreshedCreatedTrade.getCptyTrade( 'A' ) );
        xml = new TradeDownloadKondorMessageBuilderC().buildDownloadMessage( msg, null );
        log( "Original trade in kondor format is " + xml );
        xml = new TradeDownloadTOFMessageBuilderC().buildDownloadMessage( msg, null );
        log( "Original trade in TOF format is " + xml );

    }

    //38618
    public void testFILPRelationship() throws Exception
    {
        String fiName = "DD1";
        String fiLeName = "DD1-le1";
        String fiUserName = "DD1DirectFXTrader1";
        User fiUser = UserFactory.getUser( fiUserName + '@' + fiName );
        String data = "04/23/2008,04/25/2008," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + fiName + ',' + makerName + ',' + fiLeName + ',' + makerLeName + ",F,TestOrgRefId,TestCptyRefId," + fiUserName + ',' + makerUserName + ",USD";
        init( fiUser );
        HashMap map = getMap( data, TradeLoaderC.SPOT_TRADE_TYPE );
        Trade trd = loader.loadTrade( map );
        assertNull( trd ); // trade should not get created as there is no relationship between fi1 and fi2
        assertEquals( ( ( String ) loader.getExceptions().keySet().iterator().next() ).indexOf( " Reason: Trading relationship is not set between" ) >= 0, true );
    }

    public void testOutright() throws Exception
    {

        Trade refreshedCreatedTrade = execute( "04/23/2008,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.OUTRIGHT_TRADE_TYPE );
        FXPaymentParameters params = ( ( FXSingleLeg ) refreshedCreatedTrade ).getFXLeg().getFXPayment();
        assertEquals( params.getCurrency1Amount(), 200000d );
        assertEquals( params.getCurrency2Amount(), 296200d );
        assertEquals( params.getFXRate().getRate(), 1.481 );
        assertEquals( params.getFXRate().getSpotRate(), 1.48 );
        assertEquals( params.getFXRate().getForwardPoints(), 0.001 );
        assertEquals( params.getTenor().getName(), "1W" );
        assertEquals( params.getValueDate(), DateTimeFactory.newDate( 5, 2, 2008 ) );
        assertEquals( refreshedCreatedTrade.getTradeDate(), DateTimeFactory.newDate( 4, 23, 2008 ) );

        assertEquals( refreshedCreatedTrade.getTradeClassification().getShortName(), "FXOutright" );
    }

    public void testCoveredAndCoverTradeValidations()
    {
        try
        {
            Trade spotTrade0 = execute( "04/23/2012,04/25/2012," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.SPOT_TRADE_TYPE );
            Trade outrightTrade0 = execute( "04/23/2012,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.OUTRIGHT_TRADE_TYPE );
            Trade spotTrade1 = execute( "04/23/2012,04/25/2012," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.SPOT_TRADE_TYPE );
            Trade outrightTrade1 = execute( "04/23/2012,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.OUTRIGHT_TRADE_TYPE );

            // set the property to  be true.
            tradeConfig.setProperty( TradeConfigurationMBean.MANUAL_TRADE_MISMATCHED_COVER_ALLOWED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );


            // upload a new manual trade with outright trade against the spot trade.
            Trade outrightTrade2 = execute( "04/23/2012,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD" + "," + spotTrade1.getTransactionID() + "," + spotTrade0.getTransactionID(), TradeLoaderC.OUTRIGHT_TRADE_TYPE );
            Trade spotTrade2 = execute( "04/23/2012,04/25/2012," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD" + "," + outrightTrade1.getTransactionID() + "," + outrightTrade0.getTransactionID(), TradeLoaderC.SPOT_TRADE_TYPE );
            assertNotNull( outrightTrade2 );
            assertNotNull( spotTrade2 );

            tradeConfig.setProperty( TradeConfigurationMBean.MANUAL_TRADE_MISMATCHED_COVER_ALLOWED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            // upload a new manual trade with outright trade against the spot trade.
            Trade outrightTrade3 = execute( "04/23/2012,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD" + "," + spotTrade1.getTransactionID() + "," + spotTrade0.getTransactionID(), TradeLoaderC.OUTRIGHT_TRADE_TYPE );
            Trade spotTrade3 = execute( "04/23/2012,04/25/2012," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD" + "," + outrightTrade1.getTransactionID() + "," + outrightTrade0.getTransactionID(), TradeLoaderC.SPOT_TRADE_TYPE );
            assertNull( spotTrade3 );
            assertNull( outrightTrade3 );

            // it should not be possible to do have different trade dates in either combinations.
            Trade outrightTrade4 = execute( "04/24/2012,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD" + "," + spotTrade1.getTransactionID() + "," + spotTrade0.getTransactionID(), TradeLoaderC.OUTRIGHT_TRADE_TYPE );
            Trade spotTrade4 = execute( "04/24/2012,04/25/2012," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD" + "," + outrightTrade1.getTransactionID() + "," + outrightTrade0.getTransactionID(), TradeLoaderC.SPOT_TRADE_TYPE );
            assertNull( spotTrade4 );
            assertNull( outrightTrade4 );

            tradeConfig.setProperty( TradeConfigurationMBean.MANUAL_TRADE_MISMATCHED_COVER_ALLOWED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            Trade outrightTrade5 = execute( "04/24/2012,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD" + "," + spotTrade1.getTransactionID() + "," + spotTrade0.getTransactionID(), TradeLoaderC.OUTRIGHT_TRADE_TYPE );
            Trade spotTrade5 = execute( "04/24/2012,04/25/2012," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD" + "," + outrightTrade1.getTransactionID() + "," + outrightTrade0.getTransactionID(), TradeLoaderC.SPOT_TRADE_TYPE );
            assertNull( spotTrade5 );
            assertNull( outrightTrade5 );
        }
        catch ( Exception e )
        {
            fail( "testCoveredAndCoverTradeValidations", e );
        }
        finally
        {
            tradeConfig.setProperty( TradeConfigurationMBean.MANUAL_TRADE_MISMATCHED_COVER_ALLOWED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }

    public void testTradeLEIS() throws Exception
    {
        try
        {
            String takerLEI = "takerLEI";
            String lpLEI = "lpLEI";
            takerLe.setLEI( takerLEI );
            lpLe.setLEI( lpLEI );

            Trade spotTrade = execute( "04/23/2012,04/25/2012," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.SPOT_TRADE_TYPE );
            Trade outrightTrade = execute( "04/23/2012,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.OUTRIGHT_TRADE_TYPE );

            assertEquals( spotTrade.getCounterpartyALEI(), takerLEI );
            assertEquals( spotTrade.getCounterpartyBLEI(), lpLEI );
            assertEquals( outrightTrade.getCounterpartyALEI(), takerLEI );
            assertEquals( outrightTrade.getCounterpartyBLEI(), lpLEI );
        }
        catch ( Exception e )
        {
            fail( "testTradeLEIS", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerLe );
            IdcUtilC.refreshObject( lpLe );
        }
    }

    public void testTradeUPI() throws Exception
    {
        tradeConfig.setProperty( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + "." + takerName,
                "FXSpot~TESTSPOT,FXOutright~TESTOUTRIGHT,FXNDF~NDF,FXSpotFwd~SWAP,FXFwdFwd~SWAP", ConfigurationProperty.DYNAMIC_SCOPE, null );
        try
        {
            Trade spotTrade = execute( "04/23/2012,04/25/2012," + EURUSD + ',' + TradeLoaderC.SPOT_TRADE_TYPE + ",S,200000,296000,1.48," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.SPOT_TRADE_TYPE );
            Trade outrightTrade = execute( "04/23/2012,1W," + EURUSD + ',' + TradeLoaderC.OUTRIGHT_TRADE_TYPE + ",S,200000,296200,0.001,1.48,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.OUTRIGHT_TRADE_TYPE );
            assertEquals( "EUR_USD_TESTSPOT", spotTrade.getUPI() );
            assertEquals( "EUR_USD_TESTOUTRIGHT", outrightTrade.getUPI() );
        }
        catch ( Exception e )
        {
            fail( "testTradeUPI", e );
        }
        finally
        {
            tradeConfig.removeProperty( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + "." + takerName, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testManualTradeAPI()
    {
        try
        {
            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setTradeType( "FXSpot" );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setDealtCurrency( wm.getBaseCurrency() );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.SUCCESS );

            // do a sanity check on the trade.
            FXSingleLeg trade = ( FXSingleLeg ) responseMsg.getObject();
            assertEquals( trade.getCounterpartyA(), takerLe );
            assertEquals( trade.getCounterpartyB(), lpLe );
            assertEquals( trade.getEntryUser(), takerUser );
            assertEquals( trade.getMakerUser(), lpUser );
            assertEquals( trade.getFXRateBasis().getCurrencyPair(), CurrencyFactory.getCurrencyPair( wm.getBaseCurrency(), wm.getVariableCurrency() ) );
        }
        catch ( Exception e )
        {
            fail( "testManualTradeAPI", e );
        }
    }

    public void testManualTradeAPIBypassTenorRestrictions()
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setTradeType( "FXSpot" );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setDealtCurrency( wm.getBaseCurrency() );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            wm.setCreditWorkflowMode ( CreditWorkflowMode.EXCESS_UTILIZATION_ALLOWED );
            wm.setBypassCreditTenorRestrictions ( true );

            _creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForTaker, new Tenor ( "1W" ) );
            _creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForTaker, new Tenor ( "2W" ) );


            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.SUCCESS );

            // do a sanity check on the trade.
            FXSingleLeg trade = ( FXSingleLeg ) responseMsg.getObject();
            assertEquals( trade.getCounterpartyA(), takerLe );
            assertEquals( trade.getCounterpartyB(), lpLe );
            assertEquals( trade.getEntryUser(), takerUser );
            assertEquals( trade.getMakerUser(), lpUser );
            assertEquals( trade.getFXRateBasis().getCurrencyPair(), CurrencyFactory.getCurrencyPair( wm.getBaseCurrency(), wm.getVariableCurrency() ) );
            WorkflowStateMap wsm = trade.getWorkflowStateMap ();
            assertNotNull ( wsm );
            Collection<NamedWorkflowState> namedWorkflowStates= wsm.getWorkflowStates ();
            assertTrue ( namedWorkflowStates.size () > 0  );
            for ( NamedWorkflowState namedWorkflowState: namedWorkflowStates )
            {
                if ( namedWorkflowState instanceof CreditLimitWorkflowState )
                {
                    assertTrue ( CreditLimitWorkflowStateUtilC.CREDIT_USED.isSameAs ( namedWorkflowState.getState () ) );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testManualTradeAPIBypassTenorRestrictions", e );
        }
        finally
        {
            _creditAdminSvc.setMinimumTenor ( takerOrg, takerTpForLp, null );
            _creditAdminSvc.setMaximumTenor ( takerOrg, takerTpForLp, null );
            _creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForTaker, null );
            _creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForTaker, null );
        }
    }

    public void testManualTradeAPIWithTenorRestrictions()
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setTradeType( "FXSpot" );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setDealtCurrency( wm.getBaseCurrency() );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            wm.setCreditWorkflowMode ( CreditWorkflowMode.DEFAULT );
            wm.setBypassCreditTenorRestrictions ( false );

            _creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForTaker, new Tenor ( "1W" ) );
            _creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForTaker, new Tenor ( "2W" ) );

            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.FAILURE );

        }
        catch ( Exception e )
        {
            fail( "testManualTradeAPIWithTenorRestrictions", e );
        }
        finally
        {
            _creditAdminSvc.setMinimumTenor ( takerOrg, takerTpForLp, null );
            _creditAdminSvc.setMaximumTenor ( takerOrg, takerTpForLp, null );
            _creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForTaker, null );
            _creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForTaker, null );
        }
    }

    public void testManualTradeAPICreditWorkflowMode_Default()
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setTradeType( "FXSpot" );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setDealtCurrency( wm.getBaseCurrency() );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            wm.setCreditWorkflowMode ( CreditWorkflowMode.DEFAULT );

            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0, CreditUtilC.getLimitCurrency( lpOrg ) );


            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            fail( "testManualTradeAPICreditWorkflowMode_Default", e );
        }
        finally
        {
            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1000000, CreditUtilC.getLimitCurrency( lpOrg ) );
        }
    }

    public void testManualTradeAPICreditWorkflowMode_Overshoot()
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setTradeType( "FXSpot" );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setDealtCurrency( wm.getBaseCurrency() );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            wm.setCreditWorkflowMode ( CreditWorkflowMode.EXCESS_UTILIZATION_ALLOWED );

            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0, CreditUtilC.getLimitCurrency( lpOrg ) );


            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.SUCCESS );

            // do a sanity check on the trade.
            FXSingleLeg trade = ( FXSingleLeg ) responseMsg.getObject();
            assertEquals( trade.getCounterpartyA(), takerLe );
            assertEquals( trade.getCounterpartyB(), lpLe );
            assertEquals( trade.getEntryUser(), takerUser );
            assertEquals( trade.getMakerUser(), lpUser );
            assertEquals( trade.getFXRateBasis().getCurrencyPair(), CurrencyFactory.getCurrencyPair( wm.getBaseCurrency(), wm.getVariableCurrency() ) );
            WorkflowStateMap wsm = trade.getWorkflowStateMap ();
            assertNotNull ( wsm );
            Collection<NamedWorkflowState> namedWorkflowStates= wsm.getWorkflowStates ();
            assertTrue ( namedWorkflowStates.size () > 0  );
            for ( NamedWorkflowState namedWorkflowState: namedWorkflowStates )
            {
                if ( namedWorkflowState instanceof CreditLimitWorkflowState )
                {
                    assertTrue ( CreditLimitWorkflowStateUtilC.CREDIT_USED.isSameAs ( namedWorkflowState.getState () ) );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testManualTradeAPICreditWorkflowMode_Overshoot", e );
        }
        finally
        {
            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1000000, CreditUtilC.getLimitCurrency( lpOrg ) );
        }
    }

    public void testManualTradeAPICreditWorkflowMode_Ignore()
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setTradeType( "FXSpot" );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setDealtCurrency( wm.getBaseCurrency() );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            wm.setCreditWorkflowMode ( CreditWorkflowMode.NO_CHECK );

            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0, CreditUtilC.getLimitCurrency( lpOrg ) );


            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.SUCCESS );

            // do a sanity check on the trade.
            FXSingleLeg trade = ( FXSingleLeg ) responseMsg.getObject();
            assertEquals( trade.getCounterpartyA(), takerLe );
            assertEquals( trade.getCounterpartyB(), lpLe );
            assertEquals( trade.getEntryUser(), takerUser );
            assertEquals( trade.getMakerUser(), lpUser );
            assertEquals( trade.getFXRateBasis().getCurrencyPair(), CurrencyFactory.getCurrencyPair( wm.getBaseCurrency(), wm.getVariableCurrency() ) );
        }
        catch ( Exception e )
        {
            fail( "testManualTradeAPICreditWorkflowMode_Ignore", e );
        }
        finally
        {
            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1000000, CreditUtilC.getLimitCurrency( lpOrg ) );
        }
    }

    public void testManualTradeAPITradeTypeValidation()
    {
        try
        {
            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setDealtCurrency( wm.getBaseCurrency() );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "wfm.object=" + responseMsg.getObject() );
            assertNull( responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            fail( "testManualTradeAPITradeTypeValidation", e );
        }
    }

    public void testManualTradeAPIDealtCurrencyValidation()
    {
        try
        {
            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setTradeType( "FXSpot" );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "wfm.object=" + responseMsg.getObject() );
            assertNull( responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.FAILURE );
        }
        catch ( Exception e )
        {
            fail( "testManualTradeAPITradeTypeValidation", e );
        }
    }


    public void _testSwapFwdFwdCSV() throws Exception
    {
        Trade refreshedCreatedTrade = execute( "01/04/2013,1W,2W," + EURUSD + ',' + TradeLoaderC.FWD_SWAP_TRADE_TYPE + ",S,B,200000,200000,296200,296200,0.001,0.002,1.48,1.479,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.FWD_SWAP_TRADE_TYPE );
        FXPaymentParameters params = ( ( FXSwap ) refreshedCreatedTrade ).getNearLeg().getFXPayment();
        FXPaymentParameters farParams = ( ( FXSwap ) refreshedCreatedTrade ).getFarLeg().getFXPayment();

        assertEquals( refreshedCreatedTrade.getTradeDate(), DateTimeFactory.newDate( 1, 4, 2013 ) );

        assertEquals( params.getCurrency1Amount(), 200000d );
        assertEquals( params.getCurrency2Amount(), 296200d );
        assertEquals( params.getFXRate().getRate(), 1.481 );
        assertEquals( params.getFXRate().getSpotRate(), 1.48 );
        assertEquals( params.getFXRate().getForwardPoints(), 0.001 );
        assertEquals( params.getTenor().getName(), "1W" );
        assertEquals( params.getValueDate(), DateTimeFactory.newDate( 1, 15, 2013 ) );

        assertEquals( farParams.getCurrency1Amount(), 200000d );
        assertEquals( farParams.getCurrency2Amount(), 296200d );
        assertEquals( farParams.getFXRate().getRate(), 1.481 );
        assertEquals( farParams.getFXRate().getSpotRate(), 1.479 );
        assertEquals( farParams.getFXRate().getForwardPoints(), 0.002 );
        assertEquals( farParams.getTenor().getName(), "2W" );
        assertEquals( farParams.getValueDate(), DateTimeFactory.newDate( 1, 22, 2013 ) );

        assertEquals( refreshedCreatedTrade.getTradeClassification().getShortName(), TradeLoaderC.FWD_SWAP_TRADE_TYPE );
    }

    public void _testSwapSpotFwdCSV() throws Exception
    {
        Trade refreshedCreatedTrade = execute( "01/01/2013,01/04/2013,2W," + EURUSD + ',' + TradeLoaderC.SPOT_SWAP_TRADE_TYPE + ",S,B,200000,200000,296200,296200,0.001,0.002,1.48,1.479,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.SPOT_SWAP_TRADE_TYPE );
        FXPaymentParameters params = ( ( FXSwap ) refreshedCreatedTrade ).getNearLeg().getFXPayment();
        FXPaymentParameters farParams = ( ( FXSwap ) refreshedCreatedTrade ).getFarLeg().getFXPayment();

        assertEquals( refreshedCreatedTrade.getTradeDate(), DateTimeFactory.newDate( 1, 1, 2013 ) );

        assertEquals( params.getCurrency1Amount(), 200000d );
        assertEquals( params.getCurrency2Amount(), 296200d );
        assertEquals( params.getFXRate().getRate(), 1.481 );
        assertEquals( params.getFXRate().getSpotRate(), 1.48 );
        assertEquals( params.getFXRate().getForwardPoints(), 0.001 );
        assertNull( params.getTenor() );
        assertEquals( params.getValueDate(), DateTimeFactory.newDate( 1, 4, 2013 ) );

        assertEquals( farParams.getCurrency1Amount(), 200000d );
        assertEquals( farParams.getCurrency2Amount(), 296200d );
        assertEquals( farParams.getFXRate().getRate(), 1.481 );
        assertEquals( farParams.getFXRate().getSpotRate(), 1.479 );
        assertEquals( farParams.getFXRate().getForwardPoints(), 0.002 );
        assertEquals( farParams.getTenor().getName(), "2W" );
        assertEquals( farParams.getValueDate(), DateTimeFactory.newDate( 1, 18, 2013 ) );

        assertEquals( refreshedCreatedTrade.getTradeClassification().getShortName(), TradeLoaderC.SPOT_SWAP_TRADE_TYPE );
    }

    public void testFailureSwapSpotFwdCSV() throws Exception
    {
        Trade refreshedCreatedTrade = execute( "01/04/2013,1W,2W," + EURUSD + ',' + TradeLoaderC.SPOT_SWAP_TRADE_TYPE + ",S,B,200000,200000,296200,296200,0.001,0.002,1.48,1.479,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.SPOT_SWAP_TRADE_TYPE );
        assertNull( refreshedCreatedTrade );
    }

    public void testFailureNearFarSameBuySellSwapCSV() throws Exception
    {
        Trade refreshedCreatedTrade = execute( "01/04/2013,1W,2W," + EURUSD + ',' + TradeLoaderC.FWD_SWAP_TRADE_TYPE + ",S,S,200000,200000,296200,296200,0.001,0.002,1.48,1.479,1.481," + takerName + ',' + lpName + ',' + takerLeName + ',' + lpLeName + ",F,TestOrgRefId,TestCptyRefId," + takerUserName + ',' + lpUserName + ",USD", TradeLoaderC.FWD_SWAP_TRADE_TYPE );
        assertNull( refreshedCreatedTrade );
    }


    public void testSwapManualTradeWorkflow() throws Exception
    {
        init( takerUser );
        ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC();

        mtm.setTradeDate( DateTimeFactory.newDate( 1, 4, 2013 ) );
        mtm.setValueDate( DateTimeFactory.newDate( 1, 15, 2013 ) );
        mtm.setFarValueDate( DateTimeFactory.newDate( 1, 22, 2013 ) );
        mtm.setTradeType( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
        mtm.setBaseAmount( 200000 );
        mtm.setFarBaseAmount( 200000 );
        mtm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );

        mtm.setVariableAmount( 296200 );
        mtm.setFarVariableAmount( 296200 );
        mtm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
        mtm.setSpotRate( 1.48 );
        mtm.setForwardPoints( 0.001 );
        mtm.setRate( 1.481 );

        mtm.setFarSpotRate( 1.479 );
        mtm.setFarForwardPoints( 0.002 );
        mtm.setFarRate( 1.481 );

        mtm.setPortfolioId( new Date().getTime() + "" );
        mtm.setTakerOrganization( takerOrg );
        mtm.setMakerOrganization( lpOrg );
        mtm.setTakerLegalEntity( takerLe );
        mtm.setMakerLegalEntity( lpLe );
        mtm.setTakerRefId( "TestOrgRefId" );
        mtm.setMakerRefId( "TestOrgRefId" );

        mtm.setTakerUser( takerUser );
        mtm.setMakerUser( lpUser );
        mtm.setDealtCurrency( mtm.getVariableCurrency() );
        WorkflowMessage responseMsg = ts.createManualTrade( mtm );

        log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
        log( "responseMsg.object=" + responseMsg.getObject() );
        assertTrue( responseMsg.getStatus() == MessageStatus.SUCCESS );
    }

    public void testSwapFailureFWDWithSameSpotDateManualTradeWorkflow() throws Exception
    {
        init( takerUser );
        ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC();

        mtm.setTradeDate( DateTimeFactory.newDate( 1, 4, 2013 ) );
        mtm.setValueDate( DateTimeFactory.newDate( 1, 8, 2013 ) );
        mtm.setFarValueDate( DateTimeFactory.newDate( 1, 22, 2013 ) );
        mtm.setTradeType( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
        mtm.setBaseAmount( 200000 );
        mtm.setFarBaseAmount( 200000 );
        mtm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );

        mtm.setVariableAmount( 296200 );
        mtm.setFarVariableAmount( 296200 );
        mtm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );

        mtm.setSpotRate( 1.48 );
        mtm.setForwardPoints( 0.001 );
        mtm.setRate( 1.481 );

        mtm.setFarSpotRate( 1.479 );
        mtm.setFarForwardPoints( 0.002 );
        mtm.setFarRate( 1.481 );

        mtm.setTakerOrganization( takerOrg );
        mtm.setMakerOrganization( lpOrg );
        mtm.setTakerLegalEntity( takerLe );
        mtm.setMakerLegalEntity( lpLe );
        mtm.setTakerRefId( "TestOrgRefId" );
        mtm.setMakerRefId( "TestOrgRefId" );

        mtm.setTakerUser( takerUser );
        mtm.setMakerUser( lpUser );
        mtm.setDealtCurrency( mtm.getVariableCurrency() );
        WorkflowMessage responseMsg = ts.createManualTrade( mtm );

        log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
        log( "responseMsg.object=" + responseMsg.getObject() );
        assertTrue( responseMsg.getStatus() == MessageStatus.FAILURE );
    }

    public void testSingleLegManualTradeWorkflowWithFXCoverRate()
    {
        try
        {
            init( takerUser );
            Currency baseCcy = CurrencyFactory.getCurrency( "EUR" );
            Currency varCcy = CurrencyFactory.getCurrency( "USD" );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC();

            mtm.setTradeDate( DateTimeFactory.newDate( 1, 4, 2013 ) );
            mtm.setValueDate( DateTimeFactory.newDate( 1, 15, 2013 ) );
            mtm.setTradeType( TradeLoaderC.OUTRIGHT_TRADE_TYPE );
            mtm.setBaseAmount( 200000 );
            mtm.setBaseCurrency( baseCcy );

            mtm.setVariableAmount( 296200 );
            mtm.setVariableCurrency( varCcy );
            mtm.setSpotRate( 1.48 );
            mtm.setForwardPoints( 0.001 );
            mtm.setRate( 1.481 );

            // set fx cover rates.
            FXCoverRate fxCoverRate = new FXCoverRateC();
            fxCoverRate.setNamespace( lpOrg.getNamespace() );
            FXRate fxRate1 = FXFactory.newFXRate( baseCcy, varCcy, stdQuoteConv );
            fxRate1.setSpotRate( 1.1111 );
            fxRate1.setForwardPoints( 0.0005 );
            fxCoverRate.setFXRate( fxRate1 );
            fxCoverRate.addSpread( FXCoverRate.PP_SPOT_SPREAD, 0.0002 );
            fxCoverRate.addSpread( FXCoverRate.PP_FWD_SPREAD, 0.0003 );
            mtm.setFXCoverRate( fxCoverRate );

            mtm.setPortfolioId( new Date().getTime() + "" );
            mtm.setTakerOrganization( takerOrg );
            mtm.setMakerOrganization( lpOrg );
            mtm.setTakerLegalEntity( takerLe );
            mtm.setMakerLegalEntity( lpLe );
            mtm.setTakerRefId( "TestOrgRefId" );
            mtm.setMakerRefId( "TestOrgRefId" );

            mtm.setTakerUser( takerUser );
            mtm.setMakerUser( lpUser );
            mtm.setDealtCurrency( mtm.getVariableCurrency() );
            WorkflowMessage responseMsg = ts.createManualTrade( mtm );
            FXSingleLeg singleLegTrade = ( FXSingleLeg ) responseMsg.getObject();
            assertNotNull( singleLegTrade );

            FXCoverRate fxCvrRate = singleLegTrade.getFXLeg().getFXPayment().getFXCoverRate();
            assertNotNull( fxCvrRate );
            assertEquals( fxCvrRate.getFXRate().getSpotRate(), 1.1111 );
            assertEquals( fxCvrRate.getSpread( FXCoverRate.PP_SPOT_SPREAD ), 0.0002 );
            assertEquals( fxCvrRate.getSpread( FXCoverRate.PP_FWD_SPREAD ), 0.0003 );
            assertEquals( fxCvrRate.getNamespace(), lpOrg.getNamespace() );

            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.SUCCESS );
        }
        catch ( Exception e )
        {
            fail( "testSingleLegManualTradeWorkflowWithFXCoverRate", e );
        }
    }


    public void testSwapManualTradeWorkflowWithFXCoverRate()
    {
        try
        {
            init( takerUser );
            Currency baseCcy = CurrencyFactory.getCurrency( "EUR" );
            Currency varCcy = CurrencyFactory.getCurrency( "USD" );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC();

            mtm.setTradeDate( DateTimeFactory.newDate( 1, 4, 2013 ) );
            mtm.setValueDate( DateTimeFactory.newDate( 1, 15, 2013 ) );
            mtm.setFarValueDate( DateTimeFactory.newDate( 1, 22, 2013 ) );
            mtm.setTradeType( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount( 200000 );
            mtm.setFarBaseAmount( 200000 );
            mtm.setBaseCurrency( baseCcy );

            mtm.setVariableAmount( 296200 );
            mtm.setFarVariableAmount( 296200 );
            mtm.setVariableCurrency( varCcy );
            mtm.setSpotRate( 1.48 );
            mtm.setForwardPoints( 0.001 );
            mtm.setRate( 1.481 );

            mtm.setFarSpotRate( 1.479 );
            mtm.setFarForwardPoints( 0.002 );
            mtm.setFarRate( 1.481 );

            // set fx cover rates.
            FXCoverRate nearFXCoverRate = new FXCoverRateC();
            nearFXCoverRate.setNamespace( lpOrg.getNamespace() );
            FXRate fxRate1 = FXFactory.newFXRate( baseCcy, varCcy, stdQuoteConv );
            fxRate1.setSpotRate( 1.1111 );
            fxRate1.setForwardPoints( 0.0005 );
            nearFXCoverRate.setFXRate( fxRate1 );
            nearFXCoverRate.addSpread( FXCoverRate.PP_SPOT_SPREAD, 0.0002 );
            mtm.setFXCoverRate( nearFXCoverRate );

            FXCoverRate farFXCoverRate = new FXCoverRateC();
            farFXCoverRate.setNamespace( lpOrg.getNamespace() );
            FXRate fxRate2 = FXFactory.newFXRate( baseCcy, varCcy, stdQuoteConv );
            fxRate2.setSpotRate( 1.1111 );
            fxRate2.setForwardPoints( 0.0007 );
            farFXCoverRate.setFXRate( fxRate1 );
            farFXCoverRate.addSpread( FXCoverRate.PP_SPOT_SPREAD, 0.0003 );
            farFXCoverRate.addSpread( FXCoverRate.PP_FWD_SPREAD, 0.0004 );
            mtm.setFarFXCoverRate( farFXCoverRate );

            mtm.setPortfolioId( new Date().getTime() + "" );
            mtm.setTakerOrganization( takerOrg );
            mtm.setMakerOrganization( lpOrg );
            mtm.setTakerLegalEntity( takerLe );
            mtm.setMakerLegalEntity( lpLe );
            mtm.setTakerRefId( "TestOrgRefId" );
            mtm.setMakerRefId( "TestOrgRefId" );

            mtm.setTakerUser( takerUser );
            mtm.setMakerUser( lpUser );
            mtm.setDealtCurrency( mtm.getVariableCurrency() );
            WorkflowMessage responseMsg = ts.createManualTrade( mtm );
            FXSwap swapTrade = ( FXSwap ) responseMsg.getObject();
            assertNotNull( swapTrade );

            FXCoverRate fxCoverRateNear = swapTrade.getNearLeg().getFXPayment().getFXCoverRate();
            assertNotNull( fxCoverRateNear );
            assertEquals( fxCoverRateNear.getFXRate().getSpotRate(), 1.1111 );
            assertEquals( fxCoverRateNear.getSpread( FXCoverRate.PP_SPOT_SPREAD ), 0.0002 );
            assertEquals( fxCoverRateNear.getNamespace(), lpOrg.getNamespace() );

            FXCoverRate fxCoverRateFar = swapTrade.getFarLeg().getFXPayment().getFXCoverRate();
            assertNotNull( fxCoverRateFar );
            assertEquals( fxCoverRateFar.getFXRate().getSpotRate(), 1.1111 );
            assertEquals( fxCoverRateFar.getSpread( FXCoverRate.PP_SPOT_SPREAD ), 0.0003 );
            assertEquals( fxCoverRateFar.getSpread( FXCoverRate.PP_FWD_SPREAD ), 0.0004 );
            assertEquals( fxCoverRateFar.getNamespace(), lpOrg.getNamespace() );

            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertTrue( responseMsg.getStatus() == MessageStatus.SUCCESS );
        }
        catch ( Exception e )
        {
            fail( "testSwapManualTradeWorkflowWithFXCoverRate", e );
        }
    }

    public void testSwapManualTradeWorkflowBypassTenorRestrictions() throws Exception
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init ( takerUser );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC ();

            IdcDate tradeDate = DateTimeFactory.newDate ();
            mtm.setTradeDate ( tradeDate );
            mtm.setValueDate ( tradeDate.addDays ( 5 ) );
            mtm.setFarValueDate ( tradeDate.addDays ( 100 ) );
            mtm.setTradeType ( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount ( 200000 );
            mtm.setFarBaseAmount ( 200000 );
            mtm.setBaseCurrency ( CurrencyFactory.getCurrency ( "EUR" ) );

            mtm.setVariableAmount ( 296200 );
            mtm.setFarVariableAmount ( 296200 );
            mtm.setVariableCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
            mtm.setSpotRate ( 1.48 );
            mtm.setForwardPoints ( 0.001 );
            mtm.setRate ( 1.481 );

            mtm.setFarSpotRate ( 1.479 );
            mtm.setFarForwardPoints ( 0.002 );
            mtm.setFarRate ( 1.481 );

            mtm.setPortfolioId ( new Date ().getTime () + "" );
            mtm.setTakerOrganization ( takerOrg );
            mtm.setMakerOrganization ( lpOrg );
            mtm.setTakerLegalEntity ( takerLe );
            mtm.setMakerLegalEntity ( lpLe );
            mtm.setTakerRefId ( "TestOrgRefId" );
            mtm.setMakerRefId ( "TestOrgRefId" );

            mtm.setTakerUser ( takerUser );
            mtm.setMakerUser ( lpUser );
            mtm.setDealtCurrency ( mtm.getVariableCurrency () );

            mtm.setCreditWorkflowMode ( CreditWorkflowMode.EXCESS_UTILIZATION_ALLOWED );
            mtm.setBypassCreditTenorRestrictions ( true );

            _creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForTaker, new Tenor ( "1W" ) );
            _creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForTaker, new Tenor ( "2W" ) );

            WorkflowMessage responseMsg = ts.createManualTrade ( mtm );

            log ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ) + ",status=" + responseMsg.getStatus () );
            log ( "responseMsg.object=" + responseMsg.getObject () );
            assertTrue ( responseMsg.getStatus () == MessageStatus.SUCCESS );

            Trade trade = ( Trade ) responseMsg.getObject ();
            WorkflowStateMap wsm = trade.getWorkflowStateMap ();
            assertNotNull ( wsm );
            Collection<NamedWorkflowState> namedWorkflowStates= wsm.getWorkflowStates ();
            assertTrue ( namedWorkflowStates.size () > 0  );
            for ( NamedWorkflowState namedWorkflowState: namedWorkflowStates )
            {
                if ( namedWorkflowState instanceof CreditLimitWorkflowState )
                {
                    assertTrue ( CreditLimitWorkflowStateUtilC.CREDIT_USED.isSameAs ( namedWorkflowState.getState () ) );
                }
            }
        }
        finally
        {
            _creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForTaker, null );
            _creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForTaker, null);
        }
    }

    public void testSwapManualTradeWorkflowWithTenorRestrictions() throws Exception
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init ( takerUser );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC ();

            IdcDate tradeDate = DateTimeFactory.newDate ();
            mtm.setTradeDate ( tradeDate );
            mtm.setValueDate ( tradeDate.addDays ( 5 ) );
            mtm.setFarValueDate ( tradeDate.addDays ( 100 ) );
            mtm.setTradeType ( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount ( 200000 );
            mtm.setFarBaseAmount ( 200000 );
            mtm.setBaseCurrency ( CurrencyFactory.getCurrency ( "EUR" ) );

            mtm.setVariableAmount ( 296200 );
            mtm.setFarVariableAmount ( 296200 );
            mtm.setVariableCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
            mtm.setSpotRate ( 1.48 );
            mtm.setForwardPoints ( 0.001 );
            mtm.setRate ( 1.481 );

            mtm.setFarSpotRate ( 1.479 );
            mtm.setFarForwardPoints ( 0.002 );
            mtm.setFarRate ( 1.481 );

            mtm.setPortfolioId ( new Date ().getTime () + "" );
            mtm.setTakerOrganization ( takerOrg );
            mtm.setMakerOrganization ( lpOrg );
            mtm.setTakerLegalEntity ( takerLe );
            mtm.setMakerLegalEntity ( lpLe );
            mtm.setTakerRefId ( "TestOrgRefId" );
            mtm.setMakerRefId ( "TestOrgRefId" );

            mtm.setTakerUser ( takerUser );
            mtm.setMakerUser ( lpUser );
            mtm.setDealtCurrency ( mtm.getVariableCurrency () );

            mtm.setCreditWorkflowMode ( CreditWorkflowMode.DEFAULT );
            mtm.setBypassCreditTenorRestrictions ( false );

            _creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForTaker, new Tenor ( "1W" ) );
            _creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForTaker, new Tenor ( "2W" ) );

            WorkflowMessage responseMsg = ts.createManualTrade ( mtm );

            log ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ) + ",status=" + responseMsg.getStatus () );
            log ( "responseMsg.object=" + responseMsg.getObject () );
            assertTrue ( responseMsg.getStatus () == MessageStatus.FAILURE );
        }
        finally
        {
            _creditAdminSvc.setMinimumTenor ( lpOrg, lpTpForTaker, null );
            _creditAdminSvc.setMaximumTenor ( lpOrg, lpTpForTaker, null);
        }
    }

    public void testSwapManualTradeWorkflowCreditWorkflowMode_Default() throws Exception
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init ( takerUser );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC ();

            IdcDate tradeDate = DateTimeFactory.newDate ();
            mtm.setTradeDate ( tradeDate );
            mtm.setValueDate ( tradeDate.addDays ( 5 ) );
            mtm.setFarValueDate ( tradeDate.addDays ( 100 ) );
            mtm.setTradeType ( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount ( 200000 );
            mtm.setFarBaseAmount ( 200000 );
            mtm.setBaseCurrency ( CurrencyFactory.getCurrency ( "EUR" ) );

            mtm.setVariableAmount ( 296200 );
            mtm.setFarVariableAmount ( 296200 );
            mtm.setVariableCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
            mtm.setSpotRate ( 1.48 );
            mtm.setForwardPoints ( 0.001 );
            mtm.setRate ( 1.481 );

            mtm.setFarSpotRate ( 1.479 );
            mtm.setFarForwardPoints ( 0.002 );
            mtm.setFarRate ( 1.481 );

            mtm.setPortfolioId ( new Date ().getTime () + "" );
            mtm.setTakerOrganization ( takerOrg );
            mtm.setMakerOrganization ( lpOrg );
            mtm.setTakerLegalEntity ( takerLe );
            mtm.setMakerLegalEntity ( lpLe );
            mtm.setTakerRefId ( "TestOrgRefId" );
            mtm.setMakerRefId ( "TestOrgRefId" );

            mtm.setTakerUser ( takerUser );
            mtm.setMakerUser ( lpUser );
            mtm.setDealtCurrency ( mtm.getVariableCurrency () );

            mtm.setCreditWorkflowMode ( CreditWorkflowMode.DEFAULT );

            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0, CreditUtilC.getLimitCurrency ( lpOrg ) );

            WorkflowMessage responseMsg = ts.createManualTrade ( mtm );

            log ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ) + ",status=" + responseMsg.getStatus () );
            log ( "responseMsg.object=" + responseMsg.getObject () );
            assertTrue ( responseMsg.getStatus () == MessageStatus.FAILURE );
        }
        finally
        {
            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1000000.00, CreditUtilC.getLimitCurrency ( lpOrg ) );
        }
    }

    public void testSwapManualTradeWorkflowCreditWorkflowMode_Overshoot() throws Exception
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init ( takerUser );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC ();

            IdcDate tradeDate = DateTimeFactory.newDate ();
            mtm.setTradeDate ( tradeDate );
            mtm.setValueDate ( tradeDate.addDays ( 5 ) );
            mtm.setFarValueDate ( tradeDate.addDays ( 100 ) );
            mtm.setTradeType ( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount ( 200000 );
            mtm.setFarBaseAmount ( 200000 );
            mtm.setBaseCurrency ( CurrencyFactory.getCurrency ( "EUR" ) );

            mtm.setVariableAmount ( 296200 );
            mtm.setFarVariableAmount ( 296200 );
            mtm.setVariableCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
            mtm.setSpotRate ( 1.48 );
            mtm.setForwardPoints ( 0.001 );
            mtm.setRate ( 1.481 );

            mtm.setFarSpotRate ( 1.479 );
            mtm.setFarForwardPoints ( 0.002 );
            mtm.setFarRate ( 1.481 );

            mtm.setPortfolioId ( new Date ().getTime () + "" );
            mtm.setTakerOrganization ( takerOrg );
            mtm.setMakerOrganization ( lpOrg );
            mtm.setTakerLegalEntity ( takerLe );
            mtm.setMakerLegalEntity ( lpLe );
            mtm.setTakerRefId ( "TestOrgRefId" );
            mtm.setMakerRefId ( "TestOrgRefId" );

            mtm.setTakerUser ( takerUser );
            mtm.setMakerUser ( lpUser );
            mtm.setDealtCurrency ( mtm.getVariableCurrency () );

            mtm.setCreditWorkflowMode ( CreditWorkflowMode.EXCESS_UTILIZATION_ALLOWED );

            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0, CreditUtilC.getLimitCurrency ( lpOrg ) );

            WorkflowMessage responseMsg = ts.createManualTrade ( mtm );

            log ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ) + ",status=" + responseMsg.getStatus () );
            log ( "responseMsg.object=" + responseMsg.getObject () );
            assertTrue ( responseMsg.getStatus () == MessageStatus.SUCCESS );

            Trade trade = ( Trade ) responseMsg.getObject ();
            WorkflowStateMap wsm = trade.getWorkflowStateMap ();
            assertNotNull ( wsm );
            Collection<NamedWorkflowState> namedWorkflowStates= wsm.getWorkflowStates ();
            assertTrue ( namedWorkflowStates.size () > 0  );
            for ( NamedWorkflowState namedWorkflowState: namedWorkflowStates )
            {
                if ( namedWorkflowState instanceof CreditLimitWorkflowState )
                {
                    assertTrue ( CreditLimitWorkflowStateUtilC.CREDIT_USED.isSameAs ( namedWorkflowState.getState () ) );
                }
            }
        }
        finally
        {
            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1000000.00, CreditUtilC.getLimitCurrency ( lpOrg ) );
        }
    }

    public void testSwapManualTradeWorkflowCreditWorkflowMode_Ignore() throws Exception
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init ( takerUser );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC ();

            IdcDate tradeDate = DateTimeFactory.newDate ();
            mtm.setTradeDate ( tradeDate );
            mtm.setValueDate ( tradeDate.addDays ( 5 ) );
            mtm.setFarValueDate ( tradeDate.addDays ( 100 ) );
            mtm.setTradeType ( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount ( 200000 );
            mtm.setFarBaseAmount ( 200000 );
            mtm.setBaseCurrency ( CurrencyFactory.getCurrency ( "EUR" ) );

            mtm.setVariableAmount ( 296200 );
            mtm.setFarVariableAmount ( 296200 );
            mtm.setVariableCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
            mtm.setSpotRate ( 1.48 );
            mtm.setForwardPoints ( 0.001 );
            mtm.setRate ( 1.481 );

            mtm.setFarSpotRate ( 1.479 );
            mtm.setFarForwardPoints ( 0.002 );
            mtm.setFarRate ( 1.481 );

            mtm.setPortfolioId ( new Date ().getTime () + "" );
            mtm.setTakerOrganization ( takerOrg );
            mtm.setMakerOrganization ( lpOrg );
            mtm.setTakerLegalEntity ( takerLe );
            mtm.setMakerLegalEntity ( lpLe );
            mtm.setTakerRefId ( "TestOrgRefId" );
            mtm.setMakerRefId ( "TestOrgRefId" );

            mtm.setTakerUser ( takerUser );
            mtm.setMakerUser ( lpUser );
            mtm.setDealtCurrency ( mtm.getVariableCurrency () );

            mtm.setCreditWorkflowMode ( CreditWorkflowMode.NO_CHECK );

            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0, CreditUtilC.getLimitCurrency ( lpOrg ) );

            WorkflowMessage responseMsg = ts.createManualTrade ( mtm );

            log ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ) + ",status=" + responseMsg.getStatus () );
            log ( "responseMsg.object=" + responseMsg.getObject () );
            assertTrue ( responseMsg.getStatus () == MessageStatus.SUCCESS );
        }
        finally
        {
            _creditAdminSvc.setCreditLimit ( lpOrg, lpTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1000000.00, CreditUtilC.getLimitCurrency ( lpOrg ) );
        }
    }

    public void testSwapManualTradeWorkflowCreditWorkflowMode_SkipByProperty() throws Exception
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init ( takerUser );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC ();

            IdcDate tradeDate = DateTimeFactory.newDate ();
            mtm.setTradeDate ( tradeDate );
            mtm.setValueDate ( tradeDate.addDays ( 15 ) );
            mtm.setFarValueDate ( tradeDate.addDays ( 100 ) );
            mtm.setTradeType ( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount ( 200000 );
            mtm.setFarBaseAmount ( 200000 );
            mtm.setBaseCurrency ( CurrencyFactory.getCurrency ( "EUR" ) );

            mtm.setVariableAmount ( 296200 );
            mtm.setFarVariableAmount ( 296200 );
            mtm.setVariableCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
            mtm.setSpotRate ( 1.48 );
            mtm.setForwardPoints ( 0.001 );
            mtm.setRate ( 1.481 );

            mtm.setFarSpotRate ( 1.479 );
            mtm.setFarForwardPoints ( 0.002 );
            mtm.setFarRate ( 1.481 );

            mtm.setPortfolioId ( new Date ().getTime () + "" );
            mtm.setTakerOrganization ( takerOrg );
            mtm.setMakerOrganization ( makerOrg );
            mtm.setTakerLegalEntity ( takerLe );
            mtm.setMakerLegalEntity ( makerLe );
            mtm.setTakerRefId ( "TestOrgRefId" );
            mtm.setMakerRefId ( "TestOrgRefId" );

            mtm.setTakerUser ( takerUser );
            mtm.setMakerUser ( makerUser );
            mtm.setDealtCurrency ( mtm.getVariableCurrency () );

            mtm.setCreditWorkflowMode ( CreditWorkflowMode.DEFAULT );

            _creditAdminSvc.setCreditLimit ( makerOrg, makerTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0, CreditUtilC.getLimitCurrency ( makerOrg ) );
            tradeConfig.setProperty( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + takerOrg.getShortName(), TradeLoaderC.FWD_SWAP_TRADE_TYPE, ConfigurationProperty.DYNAMIC_SCOPE, null );

            WorkflowMessage responseMsg = ts.createManualTrade ( mtm );

            log ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ) + ",status=" + responseMsg.getStatus () );
            log ( "responseMsg.object=" + responseMsg.getObject () );
            assertTrue ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ), responseMsg.getStatus () == MessageStatus.SUCCESS );

            Trade trade = ( Trade ) responseMsg.getObject ();
            WorkflowStateMap wsm = trade.getWorkflowStateMap ();
            assertNotNull ( wsm );
            Collection<NamedWorkflowState> namedWorkflowStates= wsm.getWorkflowStates ();
            assertTrue ( namedWorkflowStates.size () > 0  );
            for ( NamedWorkflowState namedWorkflowState: namedWorkflowStates )
            {
                if ( namedWorkflowState instanceof CreditLimitWorkflowState )
                {
                    assertTrue ( CreditLimitWorkflowStateUtilC.CREDIT_USED.isSameAs ( namedWorkflowState.getState () ) );
                }
            }
        }
        finally
        {
            _creditAdminSvc.setCreditLimit ( makerOrg, makerTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1000000.00, CreditUtilC.getLimitCurrency ( makerOrg ) );
            tradeConfig.removeProperty( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + takerOrg.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSwapManualTradeWorkflowCreditWorkflowMode_SkipByOrgLevelProperty() throws Exception
    {
        CreditLimitAdminService _creditAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService ();
        try
        {
            init ( takerUser );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC ();

            IdcDate tradeDate = DateTimeFactory.newDate ();
            mtm.setTradeDate ( tradeDate );
            mtm.setValueDate ( tradeDate.addDays ( 15 ) );
            mtm.setFarValueDate ( tradeDate.addDays ( 100 ) );
            mtm.setTradeType ( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount ( 200000 );
            mtm.setFarBaseAmount ( 200000 );
            mtm.setBaseCurrency ( CurrencyFactory.getCurrency ( "EUR" ) );

            mtm.setVariableAmount ( 296200 );
            mtm.setFarVariableAmount ( 296200 );
            mtm.setVariableCurrency ( CurrencyFactory.getCurrency ( "USD" ) );
            mtm.setSpotRate ( 1.48 );
            mtm.setForwardPoints ( 0.001 );
            mtm.setRate ( 1.481 );

            mtm.setFarSpotRate ( 1.479 );
            mtm.setFarForwardPoints ( 0.002 );
            mtm.setFarRate ( 1.481 );

            mtm.setPortfolioId ( new Date ().getTime () + "" );
            mtm.setTakerOrganization ( takerOrg );
            mtm.setMakerOrganization ( makerOrg );
            mtm.setTakerLegalEntity ( takerLe );
            mtm.setMakerLegalEntity ( makerLe );
            mtm.setTakerRefId ( "TestOrgRefId" );
            mtm.setMakerRefId ( "TestOrgRefId" );

            mtm.setTakerUser ( takerUser );
            mtm.setMakerUser ( makerUser );
            mtm.setDealtCurrency ( mtm.getVariableCurrency () );

            mtm.setCreditWorkflowMode ( CreditWorkflowMode.DEFAULT );

            _creditAdminSvc.setCreditLimit ( makerOrg, makerTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0, CreditUtilC.getLimitCurrency ( makerOrg ) );
            tradeConfig.setProperty( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + takerOrg.getShortName(), TradeLoaderC.FWD_SWAP_TRADE_TYPE, ConfigurationProperty.DYNAMIC_SCOPE, null );

            WorkflowMessage responseMsg = ts.createManualTrade ( mtm );

            log ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ) + ",status=" + responseMsg.getStatus () );
            log ( "responseMsg.object=" + responseMsg.getObject () );
            assertTrue ( "responseMsg.errors=" + printWorkflowErrors ( responseMsg ), responseMsg.getStatus () == MessageStatus.SUCCESS );

            Trade trade = ( Trade ) responseMsg.getObject ();
            WorkflowStateMap wsm = trade.getWorkflowStateMap ();
            assertNotNull ( wsm );
            Collection<NamedWorkflowState> namedWorkflowStates= wsm.getWorkflowStates ();
            assertTrue ( namedWorkflowStates.size () > 0  );
            for ( NamedWorkflowState namedWorkflowState: namedWorkflowStates )
            {
                if ( namedWorkflowState instanceof CreditLimitWorkflowState )
                {
                    assertTrue ( CreditLimitWorkflowStateUtilC.CREDIT_USED.isSameAs ( namedWorkflowState.getState () ) );
                }
            }
        }
        finally
        {
            _creditAdminSvc.setCreditLimit ( makerOrg, makerTpForTaker, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1000000.00, CreditUtilC.getLimitCurrency ( makerOrg ) );
            tradeConfig.removeProperty( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + takerOrg.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSpotManualTradeAPI_ignoreSTPChannelCheck()
    {
        // set the property to be true for the makerOrg
        tradeConfig.setProperty( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED + makerOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        try
        {
            // org level setting stp settings
            setSTPFormat( takerOrg, TradeServiceConstants.FINXML51V20_COMPLEX_NEW );
            setSTPFormat( makerOrg, TradeServiceConstants.FINXML51V20_COMPLEX_NEW );

            init( takerUser );
            ManualTradeWorkflowMessage wm = new ManualTradeWorkflowMessageC();
            wm.setTradeDate( DateTimeFactory.newDate() );
            wm.setTradeType( "FXSpot" );
            wm.setBaseAmount( 1000.00 );
            wm.setBaseCurrency( CurrencyFactory.getCurrency( "EUR" ) );
            wm.setDealtCurrency( wm.getBaseCurrency() );
            wm.setVariableAmount( 1000.00 );
            wm.setVariableCurrency( CurrencyFactory.getCurrency( "USD" ) );
            wm.setRate( 1.0 );
            wm.setTakerLegalEntity( takerLe );
            wm.setTakerOrganization( takerOrg );
            wm.setTakerUser( takerUser );
            wm.setMakerLegalEntity( lpLe );
            wm.setMakerOrganization( lpOrg );
            wm.setMakerUser( lpUser );
            wm.setChannel("OCX/DT/BD/SPOT");

            WorkflowMessage responseMsg = ts.createManualTrade( wm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertSame(responseMsg.getStatus(), MessageStatus.SUCCESS);
            boolean isSkipSTPChannelCheckForManualTrade = tradeConfig.isCopyParentChannel(makerOrg);
            if ( isSkipSTPChannelCheckForManualTrade ){
                responseMsg.setProperty( TradeLoaderC.AMEND_ALLOCATION_ROLL, new TradeServiceConfiguration ( false, true, true, CreditWorkflowMode.EXCESS_UTILIZATION_ALLOWED, false, false, true ) );
            }

            // do a sanity check on the trade.
            FXSingleLeg trade = ( FXSingleLeg ) responseMsg.getObject();
            assertEquals( trade.getCounterpartyA(), takerLe );
            assertEquals( trade.getCounterpartyB(), lpLe );
            assertEquals( trade.getEntryUser(), takerUser );
            assertEquals( trade.getMakerUser(), lpUser );
            assertEquals( trade.getChannel(), "OCX/DT/BD/SPOT" );
            assertEquals( trade.getFXRateBasis().getCurrencyPair(), CurrencyFactory.getCurrencyPair( wm.getBaseCurrency(), wm.getVariableCurrency() ) );
            responseMsg.setProperty( "organization", trade.getCounterpartyA().getOrganization() );

            log.warn("Response msg: " + responseMsg);
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( responseMsg, null );
            log.warn( "xml message for cptyTrade=" + trade + ",xml=" + xml );
            xml = applyStleSheetOnXML( xml, "FinXML51v20" );
            log.warn( "applyStleSheetOnXML: " + xml );
            Document xmlDoc = new Document( xml );
            log.warn( "xml Document: " + xmlDoc );
            String trdClsf = xmlDoc.getElement( new XPath( XPATH_SINGLE_LEG_TRADE_CLASSIFICATION ) ).getTextString();
            assertEquals( "trade type should be spot", ISCommonConstants.TRD_CLSF_SP, trdClsf );

        }
        catch ( Exception e )
        {
            fail( "testSpotManualTradeAPI_ignoreSTPChannelCheck", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testSwapManualTradeAPI_ignoreSTPChannelCheck()
    {
        // set the property to  be true.
        tradeConfig.setProperty( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED + makerOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );

        try
        {
            // org level setting stp settings
            setSTPFormat( takerOrg, TradeServiceConstants.FINXML51V20_COMPLEX_NEW );
            setSTPFormat( makerOrg, TradeServiceConstants.FINXML51V20_COMPLEX_NEW);

            init( takerUser );
            Currency baseCcy = CurrencyFactory.getCurrency( "EUR" );
            Currency varCcy = CurrencyFactory.getCurrency( "USD" );
            ManualTradeWorkflowMessage mtm = new ManualTradeWorkflowMessageC();

            mtm.setTradeDate( DateTimeFactory.newDate( 1, 4, 2023 ) );
            mtm.setValueDate( DateTimeFactory.newDate( 1, 15, 2023 ) );
            mtm.setFarValueDate( DateTimeFactory.newDate( 1, 22, 2023 ) );
            mtm.setTradeType( TradeLoaderC.FWD_SWAP_TRADE_TYPE );
            mtm.setBaseAmount( 200000 );
            mtm.setFarBaseAmount( 200000 );
            mtm.setBaseCurrency( baseCcy );

            mtm.setVariableAmount( 296200 );
            mtm.setFarVariableAmount( 296200 );
            mtm.setVariableCurrency( varCcy );
            mtm.setSpotRate( 1.48 );
            mtm.setForwardPoints( 0.001 );
            mtm.setRate( 1.481 );

            mtm.setFarSpotRate( 1.479 );
            mtm.setFarForwardPoints( 0.002 );
            mtm.setFarRate( 1.481 );

            // set fx cover rates.
            FXCoverRate nearFXCoverRate = new FXCoverRateC();
            nearFXCoverRate.setNamespace( lpOrg.getNamespace() );
            FXRate fxRate1 = FXFactory.newFXRate( baseCcy, varCcy, stdQuoteConv );
            fxRate1.setSpotRate( 1.1111 );
            fxRate1.setForwardPoints( 0.0005 );
            nearFXCoverRate.setFXRate( fxRate1 );
            nearFXCoverRate.addSpread( FXCoverRate.PP_SPOT_SPREAD, 0.0002 );
            mtm.setFXCoverRate( nearFXCoverRate );

            FXCoverRate farFXCoverRate = new FXCoverRateC();
            farFXCoverRate.setNamespace( lpOrg.getNamespace() );
            FXRate fxRate2 = FXFactory.newFXRate( baseCcy, varCcy, stdQuoteConv );
            fxRate2.setSpotRate( 1.1111 );
            fxRate2.setForwardPoints( 0.0007 );
            farFXCoverRate.setFXRate( fxRate1 );
            farFXCoverRate.addSpread( FXCoverRate.PP_SPOT_SPREAD, 0.0003 );
            farFXCoverRate.addSpread( FXCoverRate.PP_FWD_SPREAD, 0.0004 );
            mtm.setFarFXCoverRate( farFXCoverRate );

            mtm.setPortfolioId( new Date().getTime() + "" );
            mtm.setTakerOrganization( takerOrg );
            mtm.setMakerOrganization( lpOrg );
            mtm.setTakerLegalEntity( takerLe );
            mtm.setMakerLegalEntity( lpLe );
            mtm.setTakerRefId( "TestOrgRefId" );
            mtm.setMakerRefId( "TestOrgRefId" );
            mtm.setTakerUser( takerUser );
            mtm.setMakerUser( lpUser );
            mtm.setDealtCurrency( mtm.getVariableCurrency() );
            mtm.setChannel("OCX/DT/RFS/SWAP");

            WorkflowMessage responseMsg = ts.createManualTrade( mtm );
            log( "responseMsg.errors=" + printWorkflowErrors( responseMsg ) + ",status=" + responseMsg.getStatus() );
            log( "responseMsg.object=" + responseMsg.getObject() );
            assertSame(responseMsg.getStatus(), MessageStatus.SUCCESS);
            boolean isSkipSTPChannelCheckForManualTrade = tradeConfig.isCopyParentChannel(makerOrg);
            if ( isSkipSTPChannelCheckForManualTrade ){
                responseMsg.setProperty( TradeLoaderC.AMEND_ALLOCATION_ROLL, new TradeServiceConfiguration ( false, true, true, CreditWorkflowMode.EXCESS_UTILIZATION_ALLOWED, false, false, true ) );
            }

            FXSwap swapTrade = ( FXSwap ) responseMsg.getObject();
            assertNotNull( swapTrade );
            assertEquals( swapTrade.getChannel(), "OCX/DT/RFS/SWAP" );

            FXCoverRate fxCoverRateNear = swapTrade.getNearLeg().getFXPayment().getFXCoverRate();
            assertNotNull( fxCoverRateNear );
            assertEquals( fxCoverRateNear.getFXRate().getSpotRate(), 1.1111 );
            assertEquals( fxCoverRateNear.getSpread( FXCoverRate.PP_SPOT_SPREAD ), 0.0002 );
            assertEquals( fxCoverRateNear.getNamespace(), lpOrg.getNamespace() );

            FXCoverRate fxCoverRateFar = swapTrade.getFarLeg().getFXPayment().getFXCoverRate();
            assertNotNull( fxCoverRateFar );
            assertEquals( fxCoverRateFar.getFXRate().getSpotRate(), 1.1111 );
            assertEquals( fxCoverRateFar.getSpread( FXCoverRate.PP_SPOT_SPREAD ), 0.0003 );
            assertEquals( fxCoverRateFar.getSpread( FXCoverRate.PP_FWD_SPREAD ), 0.0004 );
            assertEquals( fxCoverRateFar.getNamespace(), lpOrg.getNamespace() );
            responseMsg.setProperty( "organization", swapTrade.getCounterpartyA().getOrganization() );
            log.warn("Response msg: " + responseMsg);

            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( responseMsg, null );
            log.warn( "xml message for cptyTrade=" + swapTrade + ",xml=" + xml );
            xml = applyStleSheetOnXML( xml, "FinXML51v20" );
            log.warn( "applyStleSheetOnXML: " + xml );
            Document xmlDoc = new Document( xml );
            log.warn( "xml Document: " + xmlDoc );
            String trdClsf = xmlDoc.getElement( new XPath( XPATH_FXSWAP_TRADE_CLASSIFICATION ) ).getTextString();
            assertEquals( "trade type should be spot", ISCommonConstants.TRD_CLSF_FWD, trdClsf );
        }
        catch ( Exception e )
        {
            fail( "testSwapManualTradeAPI_ignoreSTPChannelCheck", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    private HashMap getMap( String csv, String tradeType )
    {
        StringTokenizer tok = new StringTokenizer( csv, "," );
        StringTokenizer headerTok = new StringTokenizer( getHeader( tradeType ), "," );
        HashMap<String, String> out = new HashMap<String, String>( 15 );
        while ( tok.hasMoreTokens() )
        {
            out.put( headerTok.nextToken(), tok.nextToken() );
        }
        return out;

    }

    private String getHeader( String tradeType )
    {
        if ( tradeType.equals( TradeLoaderC.SPOT_TRADE_TYPE ) )
        {
            return headerSpot;
        }
        else if ( tradeType.equals( TradeLoaderC.OUTRIGHT_TRADE_TYPE ) )
        {
            return headerOutright;
        }
        else
        {
            return headerSwap;
        }
    }

    private String printWorkflowErrors( WorkflowMessage wm )
    {
        StringBuilder sb = new StringBuilder();
        if ( wm.getErrors() != null )
        {
            for ( ErrorMessage em : ( Collection<ErrorMessage> ) wm.getErrors() )
            {
                sb.append( "Error code=" + em.getCode() ).append( "," );
            }
        }
        return sb.toString();
    }
}

