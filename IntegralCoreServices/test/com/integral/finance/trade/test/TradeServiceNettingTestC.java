package com.integral.finance.trade.test;

import com.integral.SEF.SEFUtilC;
import com.integral.audit.AuditEventFacadeC;
import com.integral.facade.FacadeFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.RequestClassification;
import com.integral.finance.dealing.RequestClassificationC;
import com.integral.finance.dealing.facade.DealStateFacade;
import com.integral.finance.dealing.facade.DealingFacadeFactory;
import com.integral.finance.dealing.fx.*;
import com.integral.finance.dealing.mifid.MiFIDUtils;
import com.integral.finance.fx.*;
import com.integral.finance.positions.NettingPosition;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeClassification;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.TradeFactory;
import com.integral.finance.trade.TradeNettingServiceC;
import com.integral.finance.trade.TradeServiceFactory;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.facade.TradeStateFacadeC;
import com.integral.finance.trade.functor.AutoTradeNettingFunctor;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.DealQueryService;
import com.integral.scheduler.ScheduleFunctor;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.spaces.SpaceIterator;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.util.StringUtilC;
import com.integral.xml.binding.JavaXMLBinderFactory;
import com.integral.xml.mapping.XMLMappingLoaderC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.io.StringReader;
import java.io.StringWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;

public class TradeServiceNettingTestC extends TradeServiceTestC
{
    TradeNettingServiceC ns = new TradeNettingServiceC();
    RequestClassification quotedReqClsf = ( RequestClassification ) ReferenceDataCacheC.getInstance().getEntityByShortName( ISCommonConstants.QTQ_ACCEPT_TYPE, RequestClassificationC.class, null, null );
    RequestClassification rfqReqClsf = ( RequestClassification ) ReferenceDataCacheC.getInstance().getEntityByShortName( ISCommonConstants.RFQ_CREATE_TYPE, RequestClassificationC.class, null, null );

    public TradeServiceNettingTestC( String name ) throws Exception
    {
        super( name );
        FacadeFactory.setFacade( TradeStateFacade.TRADE_STATE_FACADE, Trade.class, TradeStateFacadeC.class );
        FacadeFactory.setFacade( "fx_audit_FXTradeAuditEventFacade", FXSingleLegC.class, AuditEventFacadeC.class );
        TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( "Idc.Netting.DoneDeals.DataWarehouse.Time", "0", ConfigurationProperty.DYNAMIC_SCOPE, null );

    }

    protected void setUp() throws Exception
    {
        super.setUp();
    }

    //testing of normal orders
    public void testFirstPageGroupingNoDisplay() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        deleteAllDealingData( tx.getUOW() );
        cancelAllSpacesDeals ();

        List<Trade> tradesList = new ArrayList<Trade> (  );
        FXSingleLeg trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        IdcSessionManager.getInstance().setSessionContext( IdcSessionManager.getInstance().getSessionContext( trd1.getEntryUser().getFullyQualifiedName() ) );
        FXSingleLeg trd2 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( trd1 );
        TradeCloneServiceC.createCptyTrades( trd1 );
        prepareSingleLegRequest( trd2 );
        TradeCloneServiceC.createCptyTrades( trd2 );
        FXSingleLeg trd3 = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        FXSingleLeg trd4 = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( trd3 );
        TradeCloneServiceC.createCptyTrades( trd3 );
        prepareSingleLegRequest( trd4 );
        TradeCloneServiceC.createCptyTrades( trd4 );

        tradesList.add ( trd1 );
        tradesList.add ( trd2 );
        tradesList.add ( trd3 );
        tradesList.add ( trd4 );

        uow.commit();
        persistDeals ( tradesList );
        tx.release();

        sleepFor ( 3000 );

        TradeNettingServiceC tradeService = TradeServiceFactory.getTradeNettingService();
        List<NettingPosition> positions = tradeService.getAllTradeNettedGroups( trd1.getTradeDate(), trd1.getEntryUser() );
        assertEquals( positions.size(), 2 );// as both
        assertEquals( positions.get( 0 ).getNoOfTrades(), 2 );
        assertEquals( positions.get( 1 ).getNoOfTrades(), 2 );
    }

    //one buy and one sell so zero should be the output
    public void testFirstPageGroupingNoDisplayNegative() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        deleteAllDealingData( tx.getUOW() );

        FXSingleLeg trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        IdcSessionManager.getInstance().setSessionContext( IdcSessionManager.getInstance().getSessionContext( trd1.getEntryUser().getFullyQualifiedName() ) );
        FXSingleLeg trd2 = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( trd1 );
        TradeCloneServiceC.createCptyTrades( trd1 );
        prepareSingleLegRequest( trd2 );
        TradeCloneServiceC.createCptyTrades( trd2 );

        uow.commit();
        tx.release();
        TradeNettingServiceC tradeService = TradeServiceFactory.getTradeNettingService();
        List<NettingPosition> positions = tradeService.getAllTradeNettedGroups( trd1.getTradeDate(), trd1.getEntryUser() );
        assertEquals( positions.size(), 0 );// as both
    }

    //testing of display orders
    public void testFirstPageGroupingDisplay() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        deleteAllDealingData( tx.getUOW() );

        FXSingleLeg trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        IdcSessionManager.getInstance().setSessionContext( IdcSessionManager.getInstance().getSessionContext( trd1.getEntryUser().getFullyQualifiedName() ) );
        FXSingleLeg trd2 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        trd2.setCounterpartyA( trd1.getCounterpartyB() );
        trd2.setCounterpartyB( trd1.getCounterpartyA() );
        trd2.setNamespace( trd1.getCounterpartyB().getOrganization().getNamespace() );
        FXPaymentParameters params1 = trd1.getFXLeg().getFXPayment();
        FXPaymentParameters params2 = trd2.getFXLeg().getFXPayment();
        params2.setBuyingCurrency1( !params1.isBuyingCurrency1() );
        prepareSingleLegRequest( trd1 );
        TradeCloneServiceC.createCptyTrades( trd1 );
        prepareSingleLegRequest( trd2 );
        TradeCloneServiceC.createCptyTrades( trd2 );
        uow.commit();
        tx.release();
        TradeNettingServiceC tradeService = TradeServiceFactory.getTradeNettingService();
        List<NettingPosition> positions = tradeService.getAllTradeNettedGroups( trd1.getTradeDate(), trd1.getEntryUser() );
        //assertEquals( positions.size(), 1 );// as both
        //assertEquals( positions.get( 0 ).getNoOfTrades(), 2 );
    }

    public void testGetAllTradeNettedGroups() throws Exception
    {
        //initialize();
        IdcDate date = DateTimeFactory.newDate();
        Collection result = ns.getAllTradeNettedGroups( date, ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() );
        System.out.println( result.size() );
    }

    public void testNetTrades() throws Exception
    {
        // initialize();
        /**
         * update the LEI for the default legal entities. The LEI should be visible on the netted trade.
         */
        UnitOfWork uowle = PersistenceFactory.getActiveServerSession().acquireUnitOfWork();
        LegalEntity le1 = ( LegalEntity ) uowle.registerObject( makerOrg.getDefaultDealingEntity() );
        LegalEntity le2 = ( LegalEntity ) uowle.registerObject( makerTpForTaker.getLegalEntity() );
        le1.setLEI( "LE 1" );
        le2.setLEI( "LE 2" );
        uowle.commit();
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        deleteAllDealingData( tx.getUOW() );

        TradeClassification spotClsf = ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_SP );
        FXSingleLeg trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        trd1.setTradeClassification( spotClsf );
        trd1.getFXLeg().getFXPayment().setTenor( Tenor.SPOT_TENOR );
        IdcSessionManager.getInstance().setSessionContext( IdcSessionManager.getInstance().getSessionContext( trd1.getEntryUser().getFullyQualifiedName() ) );
        FXSingleLeg trd2 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        trd2.setTradeClassification( spotClsf );
        trd2.getFXLeg().getFXPayment().setTenor( Tenor.SPOT_TENOR );
        prepareSingleLegRequest( trd1 );
        TradeCloneServiceC.createCptyTrades( trd1 );
        prepareSingleLegRequest( trd2 );
        TradeCloneServiceC.createCptyTrades( trd2 );
        FXSingleLeg trd3 = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        trd3.setTradeClassification( spotClsf );
        trd3.getFXLeg().getFXPayment().setTenor( Tenor.SPOT_TENOR );
        FXSingleLeg trd4 = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        trd4.setTradeClassification( spotClsf );
        trd4.getFXLeg().getFXPayment().setTenor( Tenor.SPOT_TENOR );
        prepareSingleLegRequest( trd3 );
        TradeCloneServiceC.createCptyTrades( trd3 );
        prepareSingleLegRequest( trd4 );
        TradeCloneServiceC.createCptyTrades( trd4 );
        trd1.setBusinessExecutionDate( new Timestamp( trd1.getExecutionDateTime().getTime() ) );
        trd2.setBusinessExecutionDate( new Timestamp( trd1.getExecutionDateTime().getTime() ) );
        trd3.setBusinessExecutionDate( new Timestamp( trd1.getExecutionDateTime().getTime() ) );
        trd4.setBusinessExecutionDate( new Timestamp( trd1.getExecutionDateTime().getTime() ) );
        uow.commit();
        tx.release();
        List trades = new ArrayList( 4 );

        trades.add( trd1 );
        trades.add( trd2 );
        trades.add( trd3 );
        trades.add( trd4 );
        WorkflowMessage workflowMessage = MessageFactory.newWorkflowMessage();
        workflowMessage.setObject( trades );
        ns.netTrades( workflowMessage, null, null, ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() );
        /*String[] tokens = ( ( String ) ( workflowMessage.getMap().get( "DetailedStatus" ) ) ).split( ";" );
        String[] tokens1 = tokens[0].split( "\\b" );
        Expression exp = new ExpressionBuilder().get( "transactionID" ).equal( tokens1[13] );
        ReadObjectQuery objQuery = new ReadObjectQuery( FXTrade.class );
        objQuery.setSelectionCriteria( exp );
        Trade trd = ( Trade ) PersistenceFactory.getActiveServerSession().executeQuery( objQuery );
        assertNotNull( trd );
        assertNotNull( trd.getUPI() );
        assertNotNull( trd.getCounterpartyALEI() );
        assertNotNull( trd.getCounterpartyBLEI() );
        */
    }

    public void testPrivateMethods() throws Exception
    {
        //  initialize();
        //    int result = ns.getTodayTradesCount();
        //  System.out.println( result );
    }

    public void testNettingRelationship()
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            org.eclipse.persistence.sessions.UnitOfWork uow = session.acquireUnitOfWork();
            Trade origTrade1 = FXFactory.newFXSingleLeg();
            Trade registeredOrigTrade1 = ( Trade ) uow.registerObject( origTrade1 );
            registeredOrigTrade1.setTransactionID( "1FXI" + String.valueOf( System.currentTimeMillis() ) );
            Trade origTrade2 = FXFactory.newFXSingleLeg();
            Trade registeredOrigTrade2 = ( Trade ) uow.registerObject( origTrade2 );
            registeredOrigTrade2.setTransactionID( "2FXI" + String.valueOf( System.currentTimeMillis() ) );
            Trade netTrade = FXFactory.newFXSingleLeg();
            Trade registeredNetTrade = ( Trade ) uow.registerObject( netTrade );
            registeredNetTrade.setTransactionID( "3FXI" + String.valueOf( System.currentTimeMillis() ) );
            registeredOrigTrade1.setNetTrade( registeredNetTrade );
            registeredOrigTrade2.setNetTrade( registeredNetTrade );
            registeredNetTrade.getNettedTrades().add( registeredOrigTrade1 );
            registeredNetTrade.getNettedTrades().add( registeredOrigTrade2 );
            uow.commit();

            // refresh the orginal trade and net trade.
            Trade refreshedOrigTrade1 = ( Trade ) session.refreshObject( origTrade1 );
            Trade refreshedOrigTrade2 = ( Trade ) session.refreshObject( origTrade2 );
            Trade refreshedNetTrade = ( Trade ) session.refreshObject( netTrade );

            log( "refreshedOrigTrade1=" + refreshedOrigTrade1 );
            log( "refreshedOrigTrade2=" + refreshedOrigTrade2 );
            log( "refreshedNetTrade=" + refreshedNetTrade );
            log( "refreshedOrigTrade1.getnetTrade()=" + refreshedOrigTrade1.getNetTrade() );
            log( "refreshedOrigTrade2.getnetTrade()=" + refreshedOrigTrade2.getNetTrade() );
            log( "refreshedNetTrade.getNettedTrades()=" + refreshedNetTrade.getNettedTrades() );
            assertEquals( "original trades1.netTrade should be net trade.", refreshedNetTrade.isSameAs( refreshedOrigTrade1.getNetTrade() ), true );
            assertEquals( "original trades2.netTrade should be net trade.", refreshedNetTrade.isSameAs( refreshedOrigTrade2.getNetTrade() ), true );
            Collection<Trade> nettedTrades = ( Collection<Trade> ) refreshedNetTrade.getNettedTrades();
            for ( Trade trade : nettedTrades )
            {
                log( "netted trades list trade=" + trade );
                assertEquals( "nettedTrade should have the correct net trade.", trade.getNetTrade().isSameAs( refreshedNetTrade ), true );
            }

            log( "containing  trades." + refreshedNetTrade.getContainedTrades().size() );
            log( "contained trade1=" + refreshedOrigTrade1.getContainingTrade() );
            log( "contained trade2=" + refreshedOrigTrade2.getContainingTrade() );

            // containing trade relationship should be null.
            assertEquals( "containing  trades should be empty", refreshedNetTrade.getContainedTrades().size(), 0 );
            assertEquals( "contained trade1 is null.", refreshedOrigTrade1.getContainingTrade(), null );
            assertEquals( "contained trade2 is null.", refreshedOrigTrade2.getContainingTrade(), null );


            XMLMappingLoaderC mappingLoader = new XMLMappingLoaderC();
            mappingLoader.preload( this.getClass().getClassLoader() );

            StringWriter sw1 = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw1, refreshedOrigTrade1, "TradeDownload" );
            log( "xml output for orig trade1=" + sw1.toString() );

            StringReader reader1 = new StringReader( sw1.toString() );
            Trade unmarshalledOrigTrade1 = ( Trade ) JavaXMLBinderFactory.newJavaXMLBinder().convertFromXML( reader1, "TradeDownload" );
            log( "converted origTrade1=" + unmarshalledOrigTrade1 );
            assertEquals( "converted trade should not be null", unmarshalledOrigTrade1 != null, true );
            assertEquals( "converted trade should have net trade info", unmarshalledOrigTrade1.getNetTrade() != null, true );

            StringWriter sw2 = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw2, refreshedOrigTrade2, "TradeDownload" );
            log( "xml output for orig trade2=" + sw1.toString() );

            StringReader reader2 = new StringReader( sw2.toString() );
            Trade unmarshalledOrigTrade2 = ( Trade ) JavaXMLBinderFactory.newJavaXMLBinder().convertFromXML( reader2, "TradeDownload" );
            log( "converted origTrade2=" + unmarshalledOrigTrade2 );
            assertEquals( "converted trade should not be null", unmarshalledOrigTrade2 != null, true );
            assertEquals( "converted trade should have net trade info", unmarshalledOrigTrade2.getNetTrade() != null, true );

            StringWriter sw3 = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw3, refreshedNetTrade, "TradeDownload" );
            log( "xml output for net trade=" + sw3.toString() );

            StringReader reader3 = new StringReader( sw3.toString() );
            Trade unmarshalledNetTrade = ( Trade ) JavaXMLBinderFactory.newJavaXMLBinder().convertFromXML( reader3, "TradeDownload" );
            log( "converted unmarshalledNetTrade=" + unmarshalledNetTrade );
            assertEquals( "converted trade should not be null", unmarshalledNetTrade != null, true );
            assertEquals( "converted trade should have net trade info", unmarshalledNetTrade.getNettedTrades().size() == 2, true );

            // add a cpty trade and do the xml conversion.
            CptyTrade cptyTrade = TradeFactory.newCptyTrade();
            org.eclipse.persistence.sessions.UnitOfWork uow3 = session.acquireUnitOfWork();
            CptyTrade regCptyTrade = ( CptyTrade ) uow3.registerObject( cptyTrade );
            regCptyTrade.setCoveredTradeTxId( "FXI1000000" );
            regCptyTrade.setCoverTradeTxIds( "FXI1000001,FXI1000002" );
            uow3.commit();

            CptyTrade refCptyTrade = ( CptyTrade ) session.refreshObject( cptyTrade );
            StringWriter sw4 = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw4, refCptyTrade, "TradeDownload" );
            log( "xml output for cpty trade=" + sw4.toString() );


        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNettingPositionsFromDeals()
    {
        Organization fiOrg = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
        Organization lpOrg = ReferenceDataCacheC.getInstance().getOrganization( "CITI" );
        try
        {
            LegalEntity fiLe1 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le1", LegalEntityC.class, fiOrg.getNamespace(), null );
            LegalEntity fiLe2 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le2", LegalEntityC.class, fiOrg.getNamespace(), null );
            LegalEntity fiLe3 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le3", LegalEntityC.class, fiOrg.getNamespace(), null );
            Collection<LegalEntity> fiLeList = new HashSet<LegalEntity>();
            fiLeList.add( fiLe1 );
            fiLeList.add( fiLe2 );
            fiLeList.add( fiLe3 );

            LegalEntity lpLe1 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "CITI-le1", LegalEntityC.class, lpOrg.getNamespace(), null );
            LegalEntity lpLe2 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "CITI-le2", LegalEntityC.class, lpOrg.getNamespace(), null );
            Collection<LegalEntity> lpLeList = new HashSet<LegalEntity>();
            lpLeList.add( lpLe1 );
            lpLeList.add( lpLe2 );

            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair( "EUR", "USD" );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair( "USD", "JPY" );
            List<Deal> deals = new ArrayList<Deal>();

            for ( LegalEntity fiLe : fiLeList )
            {
                for ( LegalEntity lpLe : lpLeList )
                {
                    FXSingleLegDeal dealEurUsdBuy1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    FXSingleLegDeal dealEurUsdBuy2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );

                    FXSingleLegDeal dealUsdJpyBuy1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    FXSingleLegDeal dealUsdJpyBuy2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );

                    FXSingleLegDeal dealEurUsdSell1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    FXSingleLegDeal dealEurUsdSell2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );

                    FXSingleLegDeal dealUsdJpySell1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    FXSingleLegDeal dealUsdJpySell2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );

                    deals.add( dealEurUsdBuy1 );
                    deals.add( dealEurUsdBuy2 );
                    deals.add( dealUsdJpyBuy1 );
                    deals.add( dealUsdJpyBuy2 );

                    deals.add( dealEurUsdSell1 );
                    deals.add( dealEurUsdSell2 );
                    deals.add( dealUsdJpySell1 );
                    deals.add( dealUsdJpySell2 );
                }
            }

            fiOrg.setNettingGroupByLE( false );
            fiOrg.setNettingGroupByTP( false );
            List<NettingPosition> nps = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, false );
            assertNotNull( nps );
            assertTrue( nps.size() == 4 );
            for ( NettingPosition np : nps )
            {
                assertTrue( np.getAmount1() == 12000.0 );
                assertTrue( np.getNoOfTrades() == 12 );
                assertTrue( np.getAmount2() == 24000.0 );
                assertTrue( np.getRate() == 2.0 );
                assertTrue( np.getTidList().size() == 12 );
            }

            fiOrg.setNettingGroupByLE( true );
            fiOrg.setNettingGroupByTP( false );
            List<NettingPosition> nps1 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, false );
            assertNotNull( nps1 );
            assertTrue( nps1.size() == 12 );
            for ( NettingPosition np : nps1 )
            {
                assertTrue( np.getAmount1() == 4000.0 );
                assertTrue( np.getNoOfTrades() == 4 );
                assertTrue( np.getAmount2() == 8000.0 );
                assertTrue( np.getRate() == 2.0 );
                assertTrue( np.getTidList().size() == 4 );
            }

            fiOrg.setNettingGroupByLE( false );
            fiOrg.setNettingGroupByTP( true );
            List<NettingPosition> nps2 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, false );
            assertNotNull( nps2 );
            assertTrue( nps2.size() == 8 );
            for ( NettingPosition np : nps2 )
            {
                assertTrue( np.getAmount1() == 6000.0 );
                assertTrue( np.getNoOfTrades() == 6 );
                assertTrue( np.getAmount2() == 12000.0 );
                assertTrue( np.getRate() == 2.0 );
                assertTrue( np.getTidList().size() == 6 );
            }

            fiOrg.setNettingGroupByLE( true );
            fiOrg.setNettingGroupByTP( true );
            List<NettingPosition> nps3 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, false );
            assertNotNull( nps3 );
            assertTrue( nps3.size() == 24 );
            for ( NettingPosition np : nps3 )
            {
                assertTrue( np.getAmount1() == 2000.0 );
                assertTrue( np.getNoOfTrades() == 2 );
                assertTrue( np.getAmount2() == 4000.0 );
                assertTrue( np.getRate() == 2.0 );
                assertTrue( np.getTidList().size() == 2 );
            }

        }
        catch ( Exception e )
        {
            fail( "testNettingPositionsFromDeals", e );
        }
        finally
        {
            IdcUtilC.refreshObject( fiOrg );
            IdcUtilC.refreshObject( lpOrg );
        }
    }

    public void testNettingPositionsFromDealsWithMultipleCounterpartyOrgs()
    {
        Organization fiOrg = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
        Organization lpOrg1 = ReferenceDataCacheC.getInstance().getOrganization( "CITI" );
        Organization lpOrg2 = ReferenceDataCacheC.getInstance().getOrganization( "DBNB" );
        try
        {
            LegalEntity fiLe1 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le1", LegalEntityC.class, fiOrg.getNamespace(), null );
            LegalEntity fiLe2 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le2", LegalEntityC.class, fiOrg.getNamespace(), null );
            LegalEntity fiLe3 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le3", LegalEntityC.class, fiOrg.getNamespace(), null );
            Collection<LegalEntity> fiLeList = new HashSet<LegalEntity>();
            fiLeList.add( fiLe1 );
            fiLeList.add( fiLe2 );
            fiLeList.add( fiLe3 );

            LegalEntity lp1Le1 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "CITI-le1", LegalEntityC.class, lpOrg1.getNamespace(), null );
            LegalEntity lp1Le2 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "CITI-le2", LegalEntityC.class, lpOrg1.getNamespace(), null );
            Collection<LegalEntity> lpLeList = new HashSet<LegalEntity>();
            lpLeList.add( lp1Le1 );
            lpLeList.add( lp1Le2 );

            LegalEntity lp2Le1 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "DBNB-le1", LegalEntityC.class, lpOrg2.getNamespace(), null );
            lpLeList.add( lp2Le1 );

            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair( "EUR", "USD" );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair( "USD", "JPY" );
            List<Deal> deals = new ArrayList<Deal>();

            for ( LegalEntity fiLe : fiLeList )
            {
                for ( LegalEntity lpLe : lpLeList )
                {
                    FXSingleLegDeal dealEurUsdBuy1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    FXSingleLegDeal dealEurUsdBuy2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );

                    FXSingleLegDeal dealUsdJpyBuy1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    FXSingleLegDeal dealUsdJpyBuy2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );

                    FXSingleLegDeal dealEurUsdSell1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    FXSingleLegDeal dealEurUsdSell2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );

                    FXSingleLegDeal dealUsdJpySell1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    FXSingleLegDeal dealUsdJpySell2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );

                    deals.add( dealEurUsdBuy1 );
                    deals.add( dealEurUsdBuy2 );
                    deals.add( dealUsdJpyBuy1 );
                    deals.add( dealUsdJpyBuy2 );

                    deals.add( dealEurUsdSell1 );
                    deals.add( dealEurUsdSell2 );
                    deals.add( dealUsdJpySell1 );
                    deals.add( dealUsdJpySell2 );
                }
            }

            fiOrg.setNettingGroupByLE( false );
            fiOrg.setNettingGroupByTP( false );
            List<NettingPosition> nps = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, false );
            assertNotNull( nps );
            assertTrue( nps.size() == 8 );
            for ( NettingPosition np : nps )
            {
                if ( np.getNettingSignature().getTpOrg().isSameAs( lpOrg2 ) )
                {
                    assertTrue( np.getAmount1() == 6000.0 );
                    assertTrue( np.getNoOfTrades() == 6 );
                    assertTrue( np.getAmount2() == 12000.00 );
                    assertTrue( np.getRate() == 2.0 );
                    assertTrue( np.getTidList().size() == 6 );
                }
                else
                {
                    assertTrue( np.getAmount1() == 12000.0 );
                    assertTrue( np.getNoOfTrades() == 12 );
                    assertTrue( np.getAmount2() == 24000.00 );
                    assertTrue( np.getRate() == 2.0 );
                    assertTrue( np.getTidList().size() == 12 );
                }
            }

            fiOrg.setNettingGroupByLE( true );
            fiOrg.setNettingGroupByTP( false );
            List<NettingPosition> nps1 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, false );
            assertNotNull( nps1 );
            assertTrue( nps1.size() == 24 );
            for ( NettingPosition np : nps1 )
            {
                if ( np.getNettingSignature().getTpOrg().isSameAs( lpOrg2 ) )
                {
                    assertTrue( np.getAmount1() == 2000.0 );
                    assertTrue( np.getNoOfTrades() == 2 );
                    assertTrue( np.getAmount2() == 4000.00 );
                    assertTrue( np.getRate() == 2.0 );
                    assertTrue( np.getTidList().size() == 2 );
                }
                else
                {
                    assertTrue( np.getAmount1() == 4000.0 );
                    assertTrue( np.getNoOfTrades() == 4 );
                    assertTrue( np.getAmount2() == 8000.00 );
                    assertTrue( np.getRate() == 2.0 );
                    assertTrue( np.getTidList().size() == 4 );
                }
            }

            fiOrg.setNettingGroupByLE( false );
            fiOrg.setNettingGroupByTP( true );
            List<NettingPosition> nps2 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, false );
            assertNotNull( nps2 );
            assertTrue( nps2.size() == 12 );
            for ( NettingPosition np : nps2 )
            {
                assertTrue( np.getAmount1() == 6000.0 );
                assertTrue( np.getNoOfTrades() == 6 );
                assertTrue( np.getAmount2() == 12000.0 );
                assertTrue( np.getRate() == 2.0 );
                assertTrue( np.getTidList().size() == 6 );
            }

            fiOrg.setNettingGroupByLE( true );
            fiOrg.setNettingGroupByTP( true );
            List<NettingPosition> nps3 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, false );
            assertNotNull( nps3 );
            assertTrue( nps3.size() == 36 );
            for ( NettingPosition np : nps3 )
            {
                assertTrue( np.getAmount1() == 2000.0 );
                assertTrue( np.getNoOfTrades() == 2 );
                assertTrue( np.getAmount2() == 4000.0 );
                assertTrue( np.getRate() == 2.0 );
                assertTrue( np.getTidList().size() == 2 );
            }
        }
        catch ( Exception e )
        {
            fail( "testNettingPositionsFromDealsWithMultipleCounterpartyOrgs", e );
        }
        finally
        {
            IdcUtilC.refreshObject( fiOrg );
            IdcUtilC.refreshObject( lpOrg );
        }
    }

    public void testNettingPositionsFromDealsGroupByOrderId()
    {
        Organization fiOrg = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
        Organization lpOrg1 = ReferenceDataCacheC.getInstance().getOrganization( "CITI" );
        Organization lpOrg2 = ReferenceDataCacheC.getInstance().getOrganization( "DBNB" );
        try
        {
            LegalEntity fiLe1 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le1", LegalEntityC.class, fiOrg.getNamespace(), null );
            LegalEntity fiLe2 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le2", LegalEntityC.class, fiOrg.getNamespace(), null );
            LegalEntity fiLe3 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "FI1-le3", LegalEntityC.class, fiOrg.getNamespace(), null );
            Collection<LegalEntity> fiLeList = new HashSet<LegalEntity>();
            fiLeList.add( fiLe1 );
            fiLeList.add( fiLe2 );
            fiLeList.add( fiLe3 );

            LegalEntity lp1Le1 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "CITI-le1", LegalEntityC.class, lpOrg1.getNamespace(), null );
            LegalEntity lp1Le2 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "CITI-le2", LegalEntityC.class, lpOrg1.getNamespace(), null );
            Collection<LegalEntity> lpLeList = new HashSet<LegalEntity>();
            lpLeList.add( lp1Le1 );
            lpLeList.add( lp1Le2 );

            LegalEntity lp2Le1 = ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByShortName( "DBNB-le1", LegalEntityC.class, lpOrg2.getNamespace(), null );
            lpLeList.add( lp2Le1 );

            CurrencyPair eurUsd = CurrencyFactory.getCurrencyPair( "EUR", "USD" );
            CurrencyPair usdJpy = CurrencyFactory.getCurrencyPair( "USD", "JPY" );
            List<Deal> deals = new ArrayList<Deal>();

            for ( LegalEntity fiLe : fiLeList )
            {
                for ( LegalEntity lpLe : lpLeList )
                {
                    FXSingleLegDeal dealEurUsdBuy1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    dealEurUsdBuy1.setOrderId( fiLe.getObjectID() + "10" );
                    FXSingleLegDeal dealEurUsdBuy2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    dealEurUsdBuy2.setOrderId( fiLe.getObjectID() + "10" );

                    FXSingleLegDeal dealUsdJpyBuy1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    dealUsdJpyBuy1.setOrderId( fiLe.getObjectID() + "20" );
                    FXSingleLegDeal dealUsdJpyBuy2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, true, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    dealUsdJpyBuy2.setOrderId( fiLe.getObjectID() + "20" );

                    FXSingleLegDeal dealEurUsdSell1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    dealEurUsdSell1.setOrderId( fiLe.getObjectID() + "11" );
                    FXSingleLegDeal dealEurUsdSell2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, eurUsd, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    dealEurUsdSell2.setOrderId( fiLe.getObjectID() + "11" );

                    FXSingleLegDeal dealUsdJpySell1 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    dealUsdJpySell1.setOrderId( fiLe.getObjectID() + "21" );
                    FXSingleLegDeal dealUsdJpySell2 = DealingTestUtilC.createTestSingleLegDeal( 1000.0, false, true, true, usdJpy, fiLe, lpLe, 2.0, 0.0, 2.0, spotDate, Tenor.SPOT_TENOR, null, null, true );
                    dealUsdJpySell2.setOrderId( fiLe.getObjectID() + "21" );

                    deals.add( dealEurUsdBuy1 );
                    deals.add( dealEurUsdBuy2 );
                    deals.add( dealUsdJpyBuy1 );
                    deals.add( dealUsdJpyBuy2 );

                    deals.add( dealEurUsdSell1 );
                    deals.add( dealEurUsdSell2 );
                    deals.add( dealUsdJpySell1 );
                    deals.add( dealUsdJpySell2 );
                }
            }

            fiOrg.setNettingGroupByLE( false );
            fiOrg.setNettingGroupByTP( false );
            List<NettingPosition> nps = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, true );
            assertNotNull( nps );
            assertTrue( nps.size() == 24 );
            for ( NettingPosition np : nps )
            {
                if ( np.getNettingSignature().getTpOrg().isSameAs( lpOrg2 ) )
                {
                    assertTrue( np.getAmount1() == 2000.0 );
                    assertTrue( np.getNoOfTrades() == 2 );
                    assertTrue( np.getAmount2() == 4000.00 );
                    assertTrue( np.getRate() == 2.0 );
                    assertTrue( np.getTidList().size() == 2 );
                }
                else
                {
                    assertTrue( np.getAmount1() == 4000.0 );
                    assertTrue( np.getNoOfTrades() == 4 );
                    assertTrue( np.getAmount2() == 8000.00 );
                    assertTrue( np.getRate() == 2.0 );
                    assertTrue( np.getTidList().size() == 4 );
                }
            }

            fiOrg.setNettingGroupByLE( true );
            fiOrg.setNettingGroupByTP( false );
            List<NettingPosition> nps1 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, true );
            assertNotNull( nps1 );
            assertTrue( nps1.size() == 24 );
            for ( NettingPosition np : nps1 )
            {
                if ( np.getNettingSignature().getTpOrg().isSameAs( lpOrg2 ) )
                {
                    assertTrue( np.getAmount1() == 2000.0 );
                    assertTrue( np.getNoOfTrades() == 2 );
                    assertTrue( np.getAmount2() == 4000.00 );
                    assertTrue( np.getRate() == 2.0 );
                    assertTrue( np.getTidList().size() == 2 );
                }
                else
                {
                    assertTrue( np.getAmount1() == 4000.0 );
                    assertTrue( np.getNoOfTrades() == 4 );
                    assertTrue( np.getAmount2() == 8000.00 );
                    assertTrue( np.getRate() == 2.0 );
                    assertTrue( np.getTidList().size() == 4 );
                }
            }

            fiOrg.setNettingGroupByLE( false );
            fiOrg.setNettingGroupByTP( true );
            List<NettingPosition> nps2 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, true );
            assertNotNull( nps2 );
            assertTrue( nps2.size() == 36 );
            for ( NettingPosition np : nps2 )
            {
                assertTrue( np.getAmount1() == 2000.0 );
                assertTrue( np.getNoOfTrades() == 2 );
                assertTrue( np.getAmount2() == 4000.0 );
                assertTrue( np.getRate() == 2.0 );
                assertTrue( np.getTidList().size() == 2 );
            }

            fiOrg.setNettingGroupByLE( true );
            fiOrg.setNettingGroupByTP( true );
            List<NettingPosition> nps3 = ns.getNettingPositionsFromDeals( fiOrg.getDefaultDealingUser(), deals, true );
            assertNotNull( nps3 );
            assertTrue( nps3.size() == 36 );
            for ( NettingPosition np : nps3 )
            {
                assertTrue( np.getAmount1() == 2000.0 );
                assertTrue( np.getNoOfTrades() == 2 );
                assertTrue( np.getAmount2() == 4000.0 );
                assertTrue( np.getRate() == 2.0 );
                assertTrue( np.getTidList().size() == 2 );
            }
        }
        catch ( Exception e )
        {
            fail( "testNettingPositionsFromDealsGroupByOrderId", e );
        }
        finally
        {
            IdcUtilC.refreshObject( fiOrg );
            IdcUtilC.refreshObject( lpOrg );
        }
    }

    public void testAutoNettingFunctorValidation()
    {
        try
        {
            AutoTradeNettingFunctor functor = new AutoTradeNettingFunctor();
            functor.setNettingOrg ( "CITI" );
            functor.setNettingCptyOrgs ( "all" );
            functor.setNettingCurrencyPairs ( "all" );
            assertEquals ( ScheduleFunctor.SUCCESS, functor.validate () );

            functor.setNettingOrg ( "xxx" );
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) );

            functor.setNettingOrg ( "CITI" );
            functor.setNettingCptyOrgs ( "xx" );
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) );

            functor.setNettingCptyOrgs ( "all" );
            functor.setNettingCurrencyPairs ( "CITI" );
            assertFalse ( ScheduleFunctor.SUCCESS.equals ( functor.validate () ) );
        }
        catch ( Exception e )
        {
            fail("testAutoNettingFunctorValidation", e );
        }
    }

    private void persistDeals ( List<Trade> tradeList ) throws Exception
    {
        List<ApplicationSpaceEvent> applicationSpaceEvents = new ArrayList<ApplicationSpaceEvent>();
        for ( Trade trade: tradeList )
        {
            applicationSpaceEvents.add ( dealCreate ( trade, TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_A, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT ) );
            applicationSpaceEvents.add ( dealCreate ( trade, TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_B, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT ) );
        }
        PersistenceServiceFactory.getCDQPersistenceService().persist( applicationSpaceEvents, tradeList.get(0).getTransactionID(), false, "ManualTrade" );
    }

    private ApplicationSpaceEvent dealCreate( Trade trade, char owningCptyRef, ApplicationEventCodes applicationEventCode ) throws Exception
    {
        boolean takerNamespace = TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_A == owningCptyRef || TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_SALES_DEALER == owningCptyRef;
        FXLeg tradeLeg = ( ( FXSingleLeg ) trade ).getFXLeg();
        FXPaymentParameters fxPayment = tradeLeg.getFXPayment();
        FXSingleLegDeal deal = FXDealingFactory.newFXSingleLegDeal();
        FXSingleLegDealRequest dealRequest = FXDealingFactory.newFXSingleLegDealRequest();
        Organization dealOrg = getOwningCptyOrg( trade, owningCptyRef );

        deal.setCoveredDealId( trade.getCoveredTradeTxId() );

        deal.setTradeDate( trade.getTradeDate() );
        deal.setCounterpartyA( takerNamespace ? ( LegalEntity ) trade.getCounterpartyA() : ( LegalEntity ) trade.getCounterpartyB() );
        deal.setCounterpartyATradeId( takerNamespace ? trade.getTakerReferenceId() : trade.getMakerReferenceId() );
        deal.setCounterpartyALEI( takerNamespace ? trade.getCounterpartyALEI() : trade.getCounterpartyBLEI() );
        deal.setCounterpartyB( takerNamespace ? ( LegalEntity ) trade.getCounterpartyB() : ( LegalEntity ) trade.getCounterpartyA() );
        deal.setCounterpartyBLEI( takerNamespace ? trade.getCounterpartyBLEI() : trade.getCounterpartyALEI() );
        deal.setTradeClassification( trade.getTradeClassification() );
        deal.setTaker( takerNamespace );
        deal.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
        deal.setUser( takerNamespace ? trade.getEntryUser() : trade.getMakerUser() );
        deal.setExecutionDate( trade.getModifiedDate() );
        deal.setExchange( trade.getCounterpartyB().getOrganization() );
        deal.setMaskedLP( trade.getMaskedLP() );
        deal.setUPI( trade.getUPI() );
        deal.setSEF( trade.isSEF() );
        deal.setExternalRequestId( trade.getExternalRequestId() );
        ( ( DealStateFacade ) deal.getFacade( DealStateFacade.FACADE_NAME ) ).setVerified();
        deal.setTransactionId( trade.getTransactionID() );
        deal.setPortfolioId( trade.getPortfolioRefId() );
        deal.setAllocation( trade.isAllocation() );

        FXDealLeg dealLeg = FXDealingFactory.newFXDealLeg();
        int acceptedBidOfferMode = tradeLeg.getFXPayment().isBuyingCurrency1() ? FXLegDealingPrice.BID : FXLegDealingPrice.OFFER;
        dealLeg.setAcceptedBidOfferMode( acceptedBidOfferMode == FXLegDealingPrice.BID ? takerNamespace ? FXLegDealingPrice.OFFER : FXLegDealingPrice.BID :
                takerNamespace ? FXLegDealingPrice.BID : FXLegDealingPrice.OFFER );
        dealLeg.setCurrencyPair( fxPayment.getFXRate().getCurrencyPair().toString() );
        dealLeg.setCurrency1( fxPayment.getCurrency1() );
        dealLeg.setCurrency2( fxPayment.getCurrency2() );
        dealLeg.setBuyingCurrency1( takerNamespace == fxPayment.isBuyingCurrency1 () );
        dealLeg.setDealtCurrency1( fxPayment.isDealtCurrency1() );
        dealLeg.setValueDate( fxPayment.getValueDate() );
        dealLeg.setTenor( fxPayment.getTenor() == null ? null : fxPayment.getTenor().toString() );
        dealLeg.setSpotRate( fxPayment.getFXRate().getSpotRate() );
        dealLeg.setForwardPoints( fxPayment.getFXRate().getForwardPoints() );
        dealLeg.setRate( fxPayment.getFXRate().getRate() );
        dealLeg.setFxRateConvention( tradeLeg.getFXPayment().getFXRate().getFXRateConvention() );
        dealLeg.setLegClassification( tradeLeg.getTradeLegClassification() );
        dealLeg.setFixingDate( fxPayment.getFixingDate() );
        dealLeg.setFixingTenor( fxPayment.getFixingTenor() );
        dealLeg.setUSI( SEFUtilC.getUSI( trade ) );
        dealLeg.setUTI( SEFUtilC.getUTI( trade ) );
        dealLeg.setISIN( tradeLeg.getISIN() );

        deal.setFXDealLeg( dealLeg );
        dealRequest.setTradeId( trade.getGUID() );
        dealRequest.setExternalRequestId( trade.getExternalRequestId() );
        dealRequest.setMessageId( StringUtilC.isNullOrEmpty( trade.getExternalRequestId() ) ? ( System.currentTimeMillis() + "" ) : trade.getExternalRequestId() );
        dealRequest.setCustomerLimit(fxPayment.isDealtCurrency1() ? fxPayment.getCurrency1Amount() : fxPayment.getCurrency2Amount());

        if ( dealLeg.getTenor() != null && new Tenor ( dealLeg.getTenor() ).isSpot () )
        {
            dealRequest.setRequestClassification( quotedReqClsf );
        }
        else
        {
            dealRequest.setRequestClassification(rfqReqClsf);
        }
        dealRequest.setDeal(deal);
        dealLeg.setCurrency1Amount(fxPayment.getCurrency1Amount());
        dealLeg.setCurrency2Amount(fxPayment.getCurrency2Amount());

        FXQuoteLeg quoteLeg = FXDealingFactory.newFXQuoteLeg();
        quoteLeg.setBaseRate(fxPayment.getFXRate().getRate());
        quoteLeg.setSpotSpread(0);
        quoteLeg.setQuoteLimit(dealRequest.getCustomerLimit());
        dealRequest.setFXQuoteLeg(quoteLeg);

        deal.setDealRequest(dealRequest);

        deal.setNamespace(dealOrg.getNamespace());
        dealRequest.setNamespace(dealOrg.getNamespace());
        // deal changes
        deal.setCounterpartyATradeId(trade.getTransactionID());
        deal.setCounterpartyBTradeId(trade.getCounterpartyB().getOrganization().getShortName() + trade.getTransactionID());
        deal.setDealConfirmed(true);
        deal.setAcceptedDate(trade.getExecutionDateTime());
        deal.setOrderId(trade.getOrderId());
        StringBuilder sb = new StringBuilder( trade.getTransactionID() ).append( "_" ).append( dealOrg.getShortName() );
        deal.set_id(sb.toString());
        deal.setNamespace(dealOrg.getNamespace());
        deal.setNetSpotAmount(trade.getNetSpotAmount());
        deal.setNetSpotAmountTerm(trade.getNetSpotAmountTerm());
        deal.setSpotValueDate(trade.getSpotValueDate());
        deal.setBenchmarkRate( ( ( FXSingleLeg ) trade ).getMidBenchmarkAllinRateStr() );
        deal.setRegulatory( MiFIDUtils.getRegulatoryDetailsFromMiFIDParams( trade ) );
        if(takerNamespace){
            deal.setNetBuyingBase(trade.isNetBuyingBase());
        }
        else{
            deal.setNetBuyingBase(!trade.isNetBuyingBase());
        }

        deal.setExecutionTimestamp(new Timestamp(trade.getExecutionDateTime().getTime()));
        if (deal.getNamespace().equals(trade.getCounterpartyA().getNamespace())) {
            deal.setBookName(trade.getCptyABookName());
        } else if (deal.getNamespace().isSameAs(trade.getCounterpartyB().getNamespace())) {
            deal.setBookName(trade.getCptyBBookName());
        }

        deal.setPricingType(trade.getPricingType());
        deal.setNote(trade.getNote());

        // persist the registered object instead of the original deal bug#59010
        return PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, applicationEventCode );
    }

    private Organization getOwningCptyOrg( Trade trade, char owningCptyRef )
    {
        Organization owningCptyOrg = null;
        if ( TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_A == owningCptyRef )
        {
            owningCptyOrg = trade.getCounterpartyA().getOrganization();
        }
        else if (TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_B == owningCptyRef )
        {
            owningCptyOrg = trade.getCounterpartyB().getOrganization();
        }
        else if (TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_SALES_DEALER == owningCptyRef && trade.getSalesDealerUser() != null)
        {
            owningCptyOrg = trade.getSalesDealerUser().getOrganization();
        }
        else if (TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_MAKER_SALES_DEALER == owningCptyRef && trade.getMakerSalesDealerUser() != null )
        {
            owningCptyOrg = trade.getMakerSalesDealerUser().getOrganization();
        }
        return owningCptyOrg;
    }

    private void cancelAllSpacesDeals()
    {
        try
        {
            List<Deal> deals = new ArrayList<Deal> (  );
            deals.addAll ( getDeals ( makerOrg, makerUser, tradeDate ) );
            deals.addAll ( getDeals ( makerOrg, makerUser, tradeDate.subtractDays ( 1 ) ) );
            deals.addAll ( getDeals ( takerOrg, takerUser, tradeDate ) );
            deals.addAll ( getDeals ( takerOrg, takerUser, tradeDate.subtractDays ( 1 ) ) );
            for ( Deal deal: deals )
            {
                deal.setState ( DealingFacadeFactory.getDealStateMBean().getCancelledState () );
                PersistenceServiceFactory.getCDQPersistenceService().persist( deal, ApplicationEventCodes.EVENT_CD_TRADE_CANCEL );
            }
        }
        catch ( Exception e )
        {
            log.error ( "Exception while cancelAllSpacesDeals", e );
        }
    }

    private List<Deal> getDeals( Organization org, User user, IdcDate tradeDate )
    {
        SpacesQueryService.QueryResult<SpaceIterator<Deal>> queryIterator = DealQueryService.queryForDealByOrganization( org, false, tradeDate.subtractDays ( 100 ).asJdkDate (), tradeDate.asJdkDate(), true );
        SpaceIterator<Deal> resultIterator = queryIterator.getResult();
        List<Deal> dealList = new ArrayList<Deal>();
        while ( resultIterator.hasNext() )
        {
            Deal deal = resultIterator.next();
            dealList.add( deal );
        }
        return dealList;
    }
}
