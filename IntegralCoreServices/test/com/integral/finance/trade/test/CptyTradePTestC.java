package com.integral.finance.trade.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.CptyTradeC;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.User;
import com.integral.user.UserC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.TreeSet;


/**
 * Unit tests for CptyTradeC
 */
public class CptyTradePTestC extends PTestCaseC
{
    static String name = "Cpty Trade Test";

    public CptyTradePTestC( String name )
    {
        super( name );
    }

    public void testCptyTradeCoverTradeTransactionIds()
    {
        try
        {
            CptyTrade cptyTrade = new CptyTradeC();
            String testTxId1 = "FXI1000-FXI1001";
            cptyTrade.setCoverTradeTxIds( testTxId1 );
            log( "collection of coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() );
            assertEquals( "size", 2, cptyTrade.getCoverTradeTxIdCollection().size() );
            cptyTrade.setCoverTradeTxIdCollection( new TreeSet<String>( cptyTrade.getCoverTradeTxIdCollection() ) );
            log( "after setting collection, coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() + ",test=" + testTxId1 );
            assertEquals( "testTxId1 should be sames as", testTxId1, cptyTrade.getCoverTradeTxIds() );

            String testTxId2 = "FXI1000-FXI1001-";
            cptyTrade.setCoverTradeTxIds( testTxId2 );
            log( "collection of coverTradeIds2=" + cptyTrade.getCoverTradeTxIdCollection() );
            assertEquals( "size", 2, cptyTrade.getCoverTradeTxIdCollection().size() );
            cptyTrade.setCoverTradeTxIdCollection( new TreeSet<String>( cptyTrade.getCoverTradeTxIdCollection() ) );
            log( "after setting collection, coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() + ",test=" + testTxId2 );
            assertEquals( "testTxId2 should be sames as", testTxId1, cptyTrade.getCoverTradeTxIds() );

            String testTxId3 = "FXI1000";
            cptyTrade.setCoverTradeTxIds( testTxId3 );
            log( "collection of coverTradeIds3=" + cptyTrade.getCoverTradeTxIdCollection() );
            assertEquals( "size", 1, cptyTrade.getCoverTradeTxIdCollection().size() );
            cptyTrade.setCoverTradeTxIdCollection( new TreeSet<String>( cptyTrade.getCoverTradeTxIdCollection() ) );
            log( "after setting collection, coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() + ",test=" + testTxId3 );
            assertEquals( "testTxId3 should be sames as", testTxId3, cptyTrade.getCoverTradeTxIds() );

            String testTxId4 = "FXI1000-FXI1001-FXI1002-FXI1003,";
            cptyTrade.setCoverTradeTxIds( testTxId4 );
            log( "collection of coverTradeIds4=" + cptyTrade.getCoverTradeTxIdCollection() );
            assertEquals( "size", 4, cptyTrade.getCoverTradeTxIdCollection().size() );
            cptyTrade.setCoverTradeTxIdCollection( new TreeSet<String>( cptyTrade.getCoverTradeTxIdCollection() ) );
            log( "after setting collection, coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() + ",test=" + testTxId4 );
            assertEquals( "testTxId4 should be sames as", testTxId4, cptyTrade.getCoverTradeTxIds() );

            String testTxId5 = "-FXI1000-FXI1001-FXI1002-FXI1003,";
            cptyTrade.setCoverTradeTxIds( testTxId5 );
            log( "collection of coverTradeIds5=" + cptyTrade.getCoverTradeTxIdCollection() );
            assertEquals( "size", 4, cptyTrade.getCoverTradeTxIdCollection().size() );
            cptyTrade.setCoverTradeTxIdCollection( new TreeSet<String>( cptyTrade.getCoverTradeTxIdCollection() ) );
            log( "after setting collection, coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() + ",test=" + testTxId5 );
            assertEquals( "testTxId5 should be sames as", "FXI1000-FXI1001-FXI1002-FXI1003,", cptyTrade.getCoverTradeTxIds() );

            String testTxId6 = "-";
            cptyTrade.setCoverTradeTxIds( testTxId6 );
            log( "collection of coverTradeIds5=" + cptyTrade.getCoverTradeTxIdCollection() );
            assertEquals( "size", 0, cptyTrade.getCoverTradeTxIdCollection().size() );
            cptyTrade.setCoverTradeTxIdCollection( new TreeSet<String>( cptyTrade.getCoverTradeTxIdCollection() ) );
            log( "after setting collection, coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() + ",test=" + testTxId5 );
            assertEquals( "testTxId5 should be sames as", null, cptyTrade.getCoverTradeTxIds() );

            String testTxId7 = "FXI1000-FXI1001-FXI1002-FXI1003";
            cptyTrade.setCoverTradeTxIds( testTxId7 );
            log( "collection of coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() );
            assertEquals( "size", 4, cptyTrade.getCoverTradeTxIdCollection().size() );
            cptyTrade.setCoverTradeTxIdCollection( new TreeSet<String>( cptyTrade.getCoverTradeTxIdCollection() ) );
            log( "after setting collection, coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() + ",test=" + testTxId1 );
            assertEquals( "testTxId7 should be sames as", testTxId7, cptyTrade.getCoverTradeTxIds() );

            String testTxId8 = " FXI1000 - FXI1001 - FXI1002 - FXI1003 ";
            cptyTrade.setCoverTradeTxIds( testTxId8 );
            log( "collection of coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() );
            assertEquals( "size", 4, cptyTrade.getCoverTradeTxIdCollection().size() );
            cptyTrade.setCoverTradeTxIdCollection( new TreeSet<String>( cptyTrade.getCoverTradeTxIdCollection() ) );
            log( "after setting collection, coverTradeIds=" + cptyTrade.getCoverTradeTxIdCollection() + ",test=" + testTxId1 );
            assertEquals( "testTxId7 should be sames as", testTxId7, cptyTrade.getCoverTradeTxIds() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testTradingPartyUser()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( UserC.class );
            CptyTrade testCptyTrade = new CptyTradeC();
            CptyTrade registeredCptyTrade = ( CptyTrade ) uow.registerObject( testCptyTrade );
            registeredCptyTrade.setTradingPartyUser( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() );
            uow.commit();

            testCptyTrade.setTradingPartyUser( null );
            // refresh the trade and retrieve the maker request.
            testCptyTrade = ( CptyTrade ) session.refreshObject( testCptyTrade );
            log( "testCptyTrade=" + testCptyTrade + ",testTrade.tradingPartyUser=" + testCptyTrade.getTradingPartyUser() );
            assertEquals( "trading party user should not be null.", testCptyTrade.getTradingPartyUser() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testManyToManyMappingsSettingOnlyOneSide() throws Exception
    {
        IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
        IdcSessionManager.getInstance().setSessionContext( sessContext );
        Session session = PersistenceFactory.newSession();
        UnitOfWork uow = session.acquireUnitOfWork();
        uow.addReadOnlyClass( UserC.class );
        CptyTrade registeredCptyTrade1 = ( CptyTrade ) uow.registerObject( new CptyTradeC() );
        CptyTrade registeredCptyTrade2 = ( CptyTrade ) uow.registerObject( new CptyTradeC() );
        CptyTrade registeredCptyTrade3 = ( CptyTrade ) uow.registerObject( new CptyTradeC() );
        uow.commit();
        registeredCptyTrade1 = ( CptyTrade ) session.refreshObject( registeredCptyTrade1 );
        registeredCptyTrade2 = ( CptyTrade ) session.refreshObject( registeredCptyTrade2 );
        registeredCptyTrade3 = ( CptyTrade ) session.refreshObject( registeredCptyTrade3 );
    }


    public void testFieldsPersistence()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClass( UserC.class );
            CptyTrade testCptyTrade = new CptyTradeC();
            CptyTrade registeredCptyTrade = ( CptyTrade ) uow.registerObject( testCptyTrade );
            CptyTrade coveredCptyTrade = new CptyTradeC();
            Trade trade = new TradeC();
            Trade registeredTrade = ( Trade ) uow.registerObject( trade );
            coveredCptyTrade.setTrade( registeredTrade );
            CptyTrade registeredCoveredCptyTrade = ( CptyTrade ) uow.registerObject( coveredCptyTrade );

            registeredCptyTrade.setCoveredCptyTrade( registeredCoveredCptyTrade );
            LegalEntity le = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "FI1-le1", null, 'A' );
            registeredCptyTrade.setLegalEntity( le );
            registeredCptyTrade.setTradingPartyLegalEntity( le );
            registeredCptyTrade.setTradingPartyOrganization( le.getOrganization() );

            uow.commit();

            testCptyTrade.setCoveredCptyTrade( null );
            // refresh the trade and retrieve the maker request.
            testCptyTrade = ( CptyTrade ) session.refreshObject( testCptyTrade );
            log( "testCptyTrade=" + testCptyTrade + ",testTrade.coveredCptyTrade=" + testCptyTrade.getCoveredCptyTrade() );

            UnitOfWork uow1 = session.acquireUnitOfWork();
            CptyTrade registeredCptyTrade1 = ( CptyTrade ) uow1.registerObject( testCptyTrade );
            registeredCptyTrade1.setCoveredCptyTrade( null );
            registeredCptyTrade1.setSynthetic(true);
            CptyTradeC backRef = new CptyTradeC();
            registeredCptyTrade1.setSyntheticCrossBackRef(backRef);
            uow1.commit();

            // refresh the trade and retrieve the maker request.
            testCptyTrade = ( CptyTrade ) session.refreshObject( testCptyTrade );
            log( "testCptyTrade=" + testCptyTrade + ",testTrade.coveredCptyTrade=" + testCptyTrade.getCoveredCptyTrade() );
            assertEquals( "coveredCptyTrade should be null.", testCptyTrade.getCoveredCptyTrade() == null, true );
            assertEquals( testCptyTrade.getTradingPartyLegalEntity(), le );
            assertEquals( testCptyTrade.getTradingPartyOrganization(), le.getOrganization() );
            assertEquals(true, testCptyTrade.isSynthetic());
            assertEquals(backRef.getObjectID(), testCptyTrade.getSyntheticCrossBackRef().getObjectID());

            // now query back the synthetic components.
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.getField( "IDCCPTYTRD.SYNXBACKREF").equal( backRef.getObjectID() );
            Collection<CptyTrade> cptyTrades = PersistenceFactory.newSession().readAllObjects( CptyTradeC.class, expr  );
            log( "component cpty trades=" + cptyTrades );
            CptyTrade aCptyTrade = (CptyTrade) cptyTrades.toArray()[0];
            assertNotNull( aCptyTrade );
            assertEquals( aCptyTrade.getObjectID(), testCptyTrade.getObjectID() );

        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }

    public void testTradingPartyLegalEntityAndOrganization()
    {
        try
        {
            LegalEntity le = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "FI1-le1", null, 'A' );
            LegalEntity lpLe = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "CITI-le1", null, 'A' );
            TradingParty tp = lpLe.getTradingParty( le.getOrganization() );
            CptyTrade ct = new CptyTradeC();
            ct.setTradingParty( tp );
            assertTrue( ct.getTradingPartyLegalEntity().isSameAs( tp.getLegalEntity() ) );
            assertTrue( ct.getTradingPartyOrganization().isSameAs( tp.getLegalEntityOrganization() ) );
        }
        catch ( Exception e )
        {
            fail( "testTradingPartyLegalEntityAndOrganization", e );
        }
    }
}
