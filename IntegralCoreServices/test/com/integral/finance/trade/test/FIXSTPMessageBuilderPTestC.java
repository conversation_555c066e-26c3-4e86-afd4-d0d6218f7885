package com.integral.finance.trade.test;

import com.integral.SEF.SEFMBean;
import com.integral.SEF.SEFUtilC;
import com.integral.USI.USIUtilC;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyExternalSystemIdC;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestClassificationC;
import com.integral.finance.dealing.mifid.MiFIDTradeParams;
import com.integral.finance.fx.*;
import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.marketData.configuration.MarketDataConfigurationFactory;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataSetC;
import com.integral.finance.trade.*;
import com.integral.finance.trade.calculator.TradeDownloadFIXMessageBuilderC;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.is.ISCommonConstants;
import com.integral.message.EntityReference;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.mifid.MiFIDMBean;
import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemC;
import com.integral.persistence.ExternalSystemClassification;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.ExecutingUserType;
import com.integral.user.Organization;
import com.integral.user.UserC;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.UnitOfWork;
import quickfix.DataDictionary;
import quickfix.Field;
import quickfix.FieldMap;
import quickfix.FieldNotFound;
import quickfix.FieldType;
import quickfix.Group;
import quickfix.Message;
import quickfix.field.MaturityDate;
import quickfix.field.MsgType;
import quickfix.field.OrderID;

import java.io.FileWriter;
import java.util.*;

// Copyright (c) 1999-2010 Integral Development Corp. All rights reserved.

public class FIXSTPMessageBuilderPTestC extends TradeServiceTestC
{
    TradeServiceC ts = new TradeServiceC();
    TradeDownloadFIXMessageBuilderC builder = new TradeDownloadFIXMessageBuilderC();
    ExternalSystem _settlementCode_ExtSys = ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null );
    public FIXSTPMessageBuilderPTestC( String aName ) throws Exception
    {
        super( aName );
        //fw = new FileWriter( "c://FIXOutput.log", true );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        try
        {
            suite.addTest(new FIXSTPMessageBuilderPTestC("testMiFIDSingleLegTradingCapacity"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testMiFIDSwapTradingCapacity"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testDownloadSpotTradeCounterpartyASellsTermFIXFormat"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testFlippingPrimeBrokerOnCptyAOnly"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testShowOriginatingCptyUserOverrideAtTradingParty"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testFlippingPrimeBrokerOnCptyBOnly"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testFlippingNormalPrimeBrokerOnBothCptys"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testFlippingCommonPrimeBroker"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testOriginatingCptyUserHide"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("test42003"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testOriginatingDetailsOverrideAtTradingParty"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testOriginatingCptyOriginatingCodeDownloadingToA"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testOriginatingCptyShowTPOrigCpty"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testOriginatingCptyShowOrganizationOrigCpty"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testOriginatingCptyUserShow"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testFlippingNormalFILPTrade"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testFIXSTPDownloadOrderIdWithOriginatingDetailsEnabled"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testFIXSTPDownloadOrderIdWithOriginatingDetailsDisabled"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSEFIdentifiers"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testMiFIDVenueTimings"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testMiFIDSingleLegISIN"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testMiFIDSwapISIN"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testNetTradeRefIdEnabled"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testNetTradeRefIdDisabled"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBSettlementCodeOnTakerDownload"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBSettlementCodeOnMakerDownload"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBSettlementCodeOnTakerDownloadWithOnlyCptyD"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBSettlementCodeOnMakerDownloadWithOnlyCptyC"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBExtSysSettlementCodeOnTakerDownload"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBExtSysSettlementCodeOnMakerDownload"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBExtSysSettlementCodeOnTakerDownloadWithOnlyCptyD"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBExtSysSettlementCodeOnMakerDownloadWithOnlyCptyC"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBSettlementCodeOnCptyCDownloadWithOnlyCptyC"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBSettlementCodeOnCptyDDownloadWithOnlyCptyD"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBSettlementCodeOnCptyCDownloadWithBothCAndD"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBSettlementCodeOnCptyDDownloadWithBothCAndD"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBExtSysSettlementCodeOnCptyCDownloadWithOnlyCptyC"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBExtSysSettlementCodeOnCptyDDownloadWithOnlyCptyD"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBExtSysSettlementCodeOnCptyCDownloadWithBothCAndD"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testUsePBExtSysSettlementCodeOnCptyDDownloadWithBothCAndD"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testCurrencyPairAlias"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityType"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithTradeDetail_FXSpot"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithTradeDetail_FXOutright"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithTradeDetail_FXNDF"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithTradeDetail_FXSwap"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithInstrumentDetail_Currency"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithInstrumentDetail_Energy"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithInstrumentDetail_Index"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithInstrumentDetail_Metal"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithInstrumentDetail_Crypto"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithInstrumentDetail_CurrencyAndTradeDetail_FXOutright"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithInstrumentDetail_IndexAndTradeDetail_FXOutright"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testSecurityTypeWithInstrumentDetail_Metal_NoMapping"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testPricingTypeCustomTagField"));
            suite.addTest(new FIXSTPMessageBuilderPTestC("testProrataSchedule"));
        }
        catch ( Exception e )
        {
            log.error( "FIXSTPMessageBuilderPTestC : error", e );
        }
        return suite;
    }


    public void testDownloadSpotTradeCounterpartyASellsTermFIXFormat() throws Exception
    {
        init( fiUser );
        IdcTransaction tx = initTransaction();
        tx.getUOW().addReadOnlyClass( UserC.class );
        double amt = 1000;

        // do a trade where CptyA sells base currency.
        String baseCcy = DealingTestUtilC.getBaseCurrency( EURUSD );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, false, true, baseCcy.equals( "USD" ), EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        msg.setObject( spotTrade );
        String output = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( msg, null );
        List<String> outputList = getValue( output, "58" );
        assertEquals( outputList.size(), 0 );

        spotTrade.setNote( "TEST" );
        output = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( msg, null );
        outputList = getValue( output, "58" );

        assertEquals( outputList.get( 0 ), "TEST" );

        List<String> fixingDateList = getValue( output, Integer.toString( MaturityDate.FIELD ) );
        assertEquals( fixingDateList.size(), 0 );

        testCommissionTags( spotTrade );

        spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, false, true, true, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        testCommissionTags( spotTrade );
        spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, false, true, false, EURGBP, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        testCommissionTags( spotTrade );
        spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, false, true, false, "EUR/JPY", makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        testCommissionTags( spotTrade );
        spotTrade = DealingTestUtilC.createTestSingleLegTrade( amt, false, true, true, "EUR/JPY", makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        testCommissionTags( spotTrade );

        tx.commit();
    }

    com.integral.finance.currency.Currency refCurrency = CurrencyFactory.getCurrency( "USD" );

    private void testCommissionTags( FXSingleLeg spotTrade ) throws Exception
    {
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );


        msg.setObject( spotTrade );
        TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( TradeConfigurationMBean.STP_FIX_EOD_RATE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        String output = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( msg, null );

        Currency dealtCcy = TradeUtilsC.getFXPayment( spotTrade, true ).isDealtCurrency1() ? TradeUtilsC.getFXPayment( spotTrade, true ).getCurrency1() :
                TradeUtilsC.getFXPayment( spotTrade, true ).getCurrency2();
        System.out.println( "***********************************  " + output + ", dealt ccy is " + dealtCcy );
        assertEquals( getValue( output, "13" ).get( 0 ), "1" );
        assertEquals( getValue( output, "479" ).get( 0 ), dealtCcy.getName() ); // dealt ccy
        IdcDate baseDate = spotTrade.getTradeDate().previousDate();
        FXMarketDataSet mds = ReferenceDataCacheC.getInstance().getEODMarketDataSet( baseDate );
        if ( mds == null )
        {
            mds = ( FXMarketDataSet ) ReferenceDataCacheC.getInstance().getEntityByShortName( MarketDataConfigurationFactory.getMarketDataConfigurationMBean().getStaticMarketDataSet(), FXMarketDataSetC.class, null, null );
        }
        FXMarketDataElement rate = mds.findSpotConversionMarketDataElement( dealtCcy, refCurrency, true );
        double eodRate = 1.0;
        if ( !TradeUtilsC.getFXPayment( spotTrade, true ).getCurrency1().isSameAs( refCurrency ) && !TradeUtilsC.getFXPayment( spotTrade, true ).getCurrency2().isSameAs( refCurrency ) )
        {
            eodRate = rate.getFXRate().getSpotRate();
            if ( !rate.getCurrencyPair().getBaseCurrency().isSameAs( dealtCcy ) ) // returned is inverted currency pair
            {
                eodRate = rate.getFXPrice().getInverted().getComputedMidFXRate().getSpotRate();
            }
        }

        assertEquals( Double.parseDouble( ( String ) getValue( output, "12" ).get( 0 ) ), eodRate ); // eod rate
        TradeConfigurationFactory.getTradeConfigurationMBean().setProperty( TradeConfigurationMBean.STP_FIX_EOD_RATE, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
    }


    private String getPerspectiveName( int i )
    {
        switch ( i )
        {
            case 0:
                return "Default";
            case 1:
                return "Maker";
            case 2:
                return "Taker";
        }
        return null;
    }


    public void testFlippingPrimeBrokerOnCptyAOnly() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, lpOrg, lpTpForMaker, bidRates[0], spotDate.addDays( 2 ) );
        Organization initialPBOrg = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).getPrimeBrokerOrganization();
        TradingParty intialPBTP = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).getPrimeBrokerTradingParty();
        try
        {
            CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setPrimeBrokerOrganization( null );
            CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setPrimeBrokerTradingParty( null );
            prepareSingleLegRequest( spotTrade );
            ts.newTrade( spotTrade, null );
            tx.release(); // most important release the transaction because we do not want changes on TP above to go into DB
        }
        finally
        {
            CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setPrimeBrokerOrganization( initialPBOrg );
            CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setPrimeBrokerTradingParty( intialPBTP );
        }


        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setObject( spotTrade );

        //System.out.println( "************************************************************************************* \n testFlippingPrimeBrokerOnCptyAOnly\n **************************************************" );
        //System.out.println( "Trade between " + spotTrade.getCounterpartyA().getOrganization().getShortName() + " and " + spotTrade.getCounterpartyB().getOrganization().getShortName() );
        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        String output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        msg.setProperty( "organization", spotTrade.getCounterpartyB().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'B' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        assertEquals( spotTrade.getCounterpartyD(), null );

        msg.setProperty( "organization", ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'C' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyAOnly", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
    }

    public void testShowOriginatingCptyUserOverrideAtTradingParty()
    {
        IdcTransaction tx = null;
        try
        {
            tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, lpOrg, lpTpForMaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );
            ts.newTrade( spotTrade, null );


            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setObject( spotTrade );
            msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
            spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
            msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
            msg.setTopic( TradeService.MESSAGE_TOPIC );
            TradingParty tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
            spotTrade.setOriginatingCptyId( tp.getObjectID() );
            spotTrade.setOriginatingUserId( spotTrade.getEntryUser().getObjectID() );
            Organization orgA = spotTrade.getCounterpartyA().getOrganization();
            tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );


            tp.setShowOrigCptyUserInSTP( Counterparty.DEFAULT_SHOW_ORIG );
            orgA.setShowOrigCptyUserInSTP( true );
            WorkflowMessage out = ts.process( msg );
            String stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( getValue( stpMessage, "448" ).size(), 3 );
            tp.setShowOrigCptyUserInSTP( Counterparty.DEFAULT_SHOW_ORIG );
            orgA.setShowOrigCptyUserInSTP( false );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( getValue( stpMessage, "448" ).size(), 1 );

            tp.setShowOrigCptyUserInSTP( Counterparty.YES_SHOW_ORIG );
            orgA.setShowOrigCptyUserInSTP( true );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( getValue( stpMessage, "448" ).size(), 3 );

            tp.setShowOrigCptyUserInSTP( Counterparty.YES_SHOW_ORIG );
            orgA.setShowOrigCptyUserInSTP( false );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( getValue( stpMessage, "448" ).size(), 3 );

            tp.setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
            orgA.setShowOrigCptyUserInSTP( true );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( getValue( stpMessage, "448" ).size(), 1 );

            tp.setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
            orgA.setShowOrigCptyUserInSTP( false );
            out = ts.process( msg );
            stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( getValue( stpMessage, "448" ).size(), 1 );
        }
        catch ( Exception e )
        {
            log.error( "TradeDownloadFIXMessageBuilderPTestC.testShowOriginatingCptyUserOverrideAtTradingParty FAILED", e );
            fail();
        }
        finally
        {
            tx.release(); // most important release the transaction because we do not want changes on TP above to go into DB
        }
    }


    public void testFlippingPrimeBrokerOnCptyBOnly() throws Exception
    {
        //System.out.println( "************************************************************************************* \n testFlippingPrimeBrokerOnCptyBOnly (Fi2 - CITI ) \n **************************************************" );
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, lpOrg, lpTpForMaker, bidRates[0], spotDate.addDays( 2 ) );
        //System.out.println( "Trade between " + spotTrade.getCounterpartyA().getOrganization().getShortName() + " and " + spotTrade.getCounterpartyB().getOrganization().getShortName() );
        Organization initialPBOrg = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).getPrimeBrokerOrganization();
        TradingParty intialPBTP = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).getPrimeBrokerTradingParty();
        try
        {
            CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setPrimeBrokerOrganization( null );
            CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setPrimeBrokerTradingParty( null );
            prepareSingleLegRequest( spotTrade );
            ts.newTrade( spotTrade, null );
            tx.release(); // most important release the transaction because we do not want changes on TP above to go into DB
        }
        finally
        {
            CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setPrimeBrokerOrganization( initialPBOrg );
            CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setPrimeBrokerTradingParty( intialPBTP );
        }


        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setObject( spotTrade );

        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        String output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        msg.setProperty( "organization", spotTrade.getCounterpartyB().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'B' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        assertEquals( spotTrade.getCounterpartyC(), null );

        msg.setProperty( "organization", ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'D' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingPrimeBrokerOnCptyBOnly", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
    }

    public void testFlippingNormalPrimeBrokerOnBothCptys() throws Exception
    {
        //System.out.println( "************************************************************************************* \n testFlippingNormalPrimeBrokerOnBothCptys ( FI1, FI2 ) - \n **************************************************" );
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        //System.out.println( "Trade between " + spotTrade.getCounterpartyA().getOrganization().getShortName() + " and " + spotTrade.getCounterpartyB().getOrganization().getShortName() );
        prepareSingleLegRequest( spotTrade );
        ts.newTrade( spotTrade, null );
        tx.commit();

        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setObject( spotTrade );

        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        String output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        msg.setProperty( "organization", spotTrade.getCounterpartyB().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'B' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );


        msg.setProperty( "organization", ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'D' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        msg.setProperty( "organization", ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'C' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalPrimeBrokerOnBothCptys", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );


    }

    public void testFlippingCommonPrimeBroker() throws Exception
    {
        //System.out.println( "************************************************************************************* \n testFlippingCommonPrimeBroker\n **************************************************" );
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, lpOrg, lpTpForMaker, bidRates[0], spotDate.addDays( 2 ) );
        //System.out.println( "Trade between " + spotTrade.getCounterpartyA().getOrganization().getShortName() + " and " + spotTrade.getCounterpartyB().getOrganization().getShortName() );

        prepareSingleLegRequest( spotTrade );
        ts.newTrade( spotTrade, null );
        tx.commit();

        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setObject( spotTrade );

        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        String output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        msg.setProperty( "organization", spotTrade.getCounterpartyB().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'B' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        assertEquals( ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization().getObjectID(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization().getObjectID() );

        msg.setProperty( "organization", ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() );   // it should always take configuration from TP set on cptyA in PB namespace ( not of cptyB in pb namespace )
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'D' ) );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), ( ( TradingParty ) spotTrade.getCounterpartyC() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), ( ( TradingParty ) spotTrade.getCounterpartyD() ).getLegalEntityOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingCommonPrimeBroker", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
    }

    public void testOriginatingCptyUserHide() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );


        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        tx.commit();
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setObject( spotTrade );

        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setShowOrigCptyUserInSTP( 1 );
        String output = builder.buildDownloadMessage( msg, null );

        System.out.println( output );
        assertEquals( getValue( output, "453" ).size(), 1 );
        assertEquals( getValue( output, "447" ).size(), 1 );
        assertEquals( getValue( output, "452" ).size(), 1 );
        assertEquals( getValue( output, "448" ).size(), 1 );

        assertEquals( getValue( output, "453" ).get( 0 ), "1" );
        assertEquals( getValue( output, "447" ).get( 0 ), "D" );
        assertEquals( getValue( output, "452" ).get( 0 ), "3" );
        assertEquals( getValue( output, "448" ).get( 0 ), spotTrade.getMakerUser().getShortName() );
    }

    private List getValue( String incomingMessage, String tag )
    {
        int index1 = incomingMessage.indexOf( tag + "=" );
        List<String> out = new ArrayList<String>( 3 );
        while ( index1 != -1 )
        {
            out.add( incomingMessage.substring( index1 + tag.length() + 1, incomingMessage.indexOf( '\u0001', index1 + tag.length() + 1 ) ) );
            index1 = incomingMessage.indexOf( tag + "=", index1 + 1 );
        }
        return out;
    }

    private void createExternalSystem( Counterparty tp, String name, UnitOfWork uow )
    {
        ExternalSystemId extSysId = tp.getExternalSystemId( name );
        if ( extSysId == null )
        {
            extSysId = new CounterpartyExternalSystemIdC();
            EntityReference ef = MessageFactory.newEntityReference();
            ef.setClassName( ExternalSystem.class.getName() );
            ef.setShortName( name );
            ExternalSystem extSys = ( ExternalSystem ) ef.getEntity();
            if ( extSys == null )
            {
                ef = MessageFactory.newEntityReference();
                ef.setClassName( ExternalSystemClassification.class.getName() );
                ef.setShortName( "CounterpartyId" );
                ExternalSystemClassification extSysClsf = ( ExternalSystemClassification ) ef.getEntity();
                ef.setClassName( Namespace.class.getName() );
                ef.setShortName( "MAIN" );
                Namespace main = ( Namespace ) ef.getEntity();
                extSys = ( ExternalSystem ) uow.registerObject( new ExternalSystemC() );
                extSys.setShortName( name );
                extSys.setClassification( extSysClsf );
                extSys.setNamespace( main );
            }
            extSysId.setExternalSystem( extSys );
            tp.setExternalSystemId( name, extSysId );
        }

    }

    public void test42003()
    {
        IdcTransaction tx = null;
        FXSingleLeg spotTrade = null;
        TradingParty tp = null;
        try
        {
            tx = initTransaction();
            UnitOfWork uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, lpOrg, lpTpForMaker, bidRates[0], spotDate.addDays( 2 ) );
            prepareSingleLegRequest( spotTrade );

            ts.newTrade( spotTrade, null );
            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setObject( spotTrade );
            msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
            spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
            msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
            msg.setTopic( TradeService.MESSAGE_TOPIC );
            spotTrade.setOriginatingCptyId( spotTrade.getCounterpartyA().getObjectID() );
            spotTrade.setOriginatingUserId( spotTrade.getEntryUser().getObjectID() );
            tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );

            createExternalSystem( spotTrade.getOriginatingCounterparty(), "OriginatingCode", uow );
            spotTrade.getOriginatingCounterparty().getExternalSystemId( "OriginatingCode" ).setSystemId( "OriginatingCodeOnOriginatingCpty" );

            createExternalSystem( tp, "OriginatingCode", uow );
            tp.getExternalSystemId( "OriginatingCode" ).setSystemId( "OriginatingCodeOnTradingParty" );

            spotTrade.getCounterpartyA().setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
            spotTrade.getCounterpartyB().setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
            tp.setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
            tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
            tp.setShowOrigCptyUserInSTP( Counterparty.YES_SHOW_ORIG );
            spotTrade.getCounterpartyA().getOrganization().setShowOrigCptyUserInSTP( true );
            ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );
            WorkflowMessage out = ts.process( msg );
            String stpMessage = ( String ) out.getProperty( TradeService.GENERATE_DOWNLOAD_XML );
            assertEquals( getValue( stpMessage, "448" ).contains( "OriginatingCodeOnOriginatingCpty" ), true );
        }
        catch ( Exception e )
        {
            fail();
        }
        finally
        {
            tx.release(); // most important release the transaction because we do not want changes on TP above to go into DB
            tp.removeExternalSystemId( tp.getExternalSystemId( "OriginatingCode" ) );
            spotTrade.getOriginatingCounterparty().removeExternalSystemId( spotTrade.getOriginatingCounterparty().getExternalSystemId( "OriginatingCode" ) );
        }
    }

    public void testOriginatingDetailsOverrideAtTradingParty() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        tx.commit();
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
        msg.setTopic( TradeService.MESSAGE_TOPIC );
        msg.setObject( spotTrade );
        Organization orgA = spotTrade.getCounterpartyA().getOrganization();
        msg.setProperty( "organization", orgA );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        TradingParty tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
        tp.setShowOrigCptyUserInSTP( 2 );
        tp.setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );
        ( ( Entity ) spotTrade.getCounterpartyB().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );

        TradingParty origCpty = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setOriginatingCptyId( origCpty.getObjectID() );
        spotTrade.setOriginatingUserId( spotTrade.getEntryUser().getObjectID() );

        //*********************************************** YES AT TP . so use originating details specified at TradingParty ONLY ******************* //
        tp.setShowOrigCptyUserInSTP( Counterparty.YES_SHOW_ORIG );


        tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        String output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                origCpty.getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                origCpty.getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                origCpty.getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyA().getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyA().getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyA().getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );

        /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyB().getShortName(),
                spotTrade.getMakerUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyB().getShortName(),
                spotTrade.getMakerUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );

        tp.setSTPOriginatingDetails( Counterparty.COVERED_TRADE_COUNTERPARTY_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.COVERED_TRADE_COUNTERPARTY_DETAILS );
        spotTrade.setCoveredTradeCounterparty( spotTrade.getCounterpartyA() );
        spotTrade.setCoveredTradeUser( spotTrade.getEntryUser() );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        System.out.println( "TradeDownloadFIXMessageBuilderPTestC.testOriginatingDetailsOverrideAtTradingParty COVERED_TRADE_LE = " + output );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getEntryUser().getShortName(),
                spotTrade.getCounterpartyA().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyB().getShortName(),
                spotTrade.getMakerUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );

        //************* YES AT TP ENDS *****************************

        //*********************************************** YES AT TP . so use originating details specified at TradingParty ONLY ******************* //
        //*********************************************** DEFAULT AT TP . so use originating details specified at ORG ONLY ******************* //
        tp.setShowOrigCptyUserInSTP( Counterparty.DEFAULT_SHOW_ORIG );

        tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                origCpty.getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyA().getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyB().getShortName(),
                spotTrade.getMakerUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                origCpty.getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyA().getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyB().getShortName(),
                spotTrade.getMakerUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );

        /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                origCpty.getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyA().getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );


        tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        orgA.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyB().getShortName(),
                spotTrade.getMakerUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true );

        //*********************************************** DEFAULT AT TP  ENDS. so use originating details specified at ORG ONLY ******************* //

    }

    public void testOriginatingCptyOriginatingCodeDownloadingToA() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        tx.commit();
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
        msg.setTopic( TradeService.MESSAGE_TOPIC );
        msg.setObject( spotTrade );


        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        TradingParty tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
        tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
        ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );

        tp.setShowOrigCptyUserInSTP( 2 );
        spotTrade.getCounterpartyA().getOrganization().setShowOrigCptyUserInSTP( true );
        tp.setSTPOriginatingDetails( 0 );
        spotTrade.getCounterpartyA().getOrganization().setSTPOriginatingDetails( 0 );

        spotTrade.setOriginatingCptyId( spotTrade.getCounterpartyA().getObjectID() );
        spotTrade.setOriginatingUserId( spotTrade.getEntryUser().getObjectID() );
        createExternalSystem( spotTrade.getOriginatingCounterparty(), "OriginatingCode", uow );
        createExternalSystem( tp, "OriginatingCode", uow );
        spotTrade.getOriginatingCounterparty().getExternalSystemId( "OriginatingCode" ).setSystemId( "OriginatingCodeTestOriginatingCpty" );
        tp.getExternalSystemId( "OriginatingCode" ).setSystemId( "OriginatingCodeTestTradingParty" );


        String output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        tp.getExternalSystemId( "OriginatingCode" ).setSystemId( "" );
        spotTrade.getOriginatingCounterparty().getExternalSystemId( "OriginatingCode" ).setSystemId( "" );

        assertEquals( getValue( output, "453" ).size(), 1 );
        assertEquals( getValue( output, "447" ).size(), 3 );
        assertEquals( getValue( output, "452" ).size(), 3 );
        assertEquals( getValue( output, "448" ).size(), 3 );

        assertEquals( getValue( output, "453" ).get( 0 ), "3" );
        assertEquals( getValue( output, "447" ).toString(), "[D, D, D]" );
        assertEquals( getValue( output, "452" ).containsAll( Arrays.asList( new String[]{"13", "11", "3"} ) ), true );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                "OriginatingCodeTestOriginatingCpty",
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true ); // because flippnig flag is true
    }


    public void testOriginatingCptyShowTPOrigCpty() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        tx.commit();
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
        msg.setTopic( TradeService.MESSAGE_TOPIC );
        msg.setObject( spotTrade );

        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        TradingParty tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
        tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
        ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );

        tp.setShowOrigCptyUserInSTP( 2 );
        tp.setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        tp.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
        spotTrade.setOriginatingUserId( spotTrade.getEntryUser().getObjectID() );
        TradingParty origCpty = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setOriginatingCptyId( origCpty.getObjectID() );
        String output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        System.out.println( output );
        //doBasicValidation ( output);
        assertEquals( getValue( output, "453" ).size(), 1 );
        assertEquals( getValue( output, "447" ).size(), 3 );
        assertEquals( getValue( output, "452" ).size(), 3 );
        assertEquals( getValue( output, "448" ).size(), 3 );

        assertEquals( getValue( output, "453" ).get( 0 ), "3" );
        assertEquals( getValue( output, "447" ).toString(), "[D, D, D]" );
        assertEquals( getValue( output, "452" ).containsAll( Arrays.asList( new String[]{"13", "11", "3"} ) ), true );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyB().getShortName(),
                spotTrade.getMakerUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true ); // because flippnig flag is true
    }

    public void testOriginatingCptyShowOrganizationOrigCpty() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        tx.commit();
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
        msg.setTopic( TradeService.MESSAGE_TOPIC );
        msg.setObject( spotTrade );

        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        TradingParty tp = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
        tp.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
        ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );

        tp.setShowOrigCptyUserInSTP( 2 );
        tp.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
        spotTrade.setOriginatingUserId( spotTrade.getEntryUser().getObjectID() );
        TradingParty origCpty = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setOriginatingCptyId( origCpty.getObjectID() );
        String output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        tp.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
        System.out.println( output );
        //doBasicValidation ( output);
        assertEquals( getValue( output, "453" ).size(), 1 );
        assertEquals( getValue( output, "447" ).size(), 3 );
        assertEquals( getValue( output, "452" ).size(), 3 );
        assertEquals( getValue( output, "448" ).size(), 3 );

        assertEquals( getValue( output, "453" ).get( 0 ), "3" );
        assertEquals( getValue( output, "447" ).toString(), "[D, D, D]" );
        assertEquals( getValue( output, "452" ).containsAll( Arrays.asList( new String[]{"13", "11", "3"} ) ), true );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                spotTrade.getCounterpartyA().getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true ); // because flippnig flag is true
    }

    public void testOriginatingCptyUserShow() throws Exception
    {
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        tx.commit();
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
        msg.setTopic( TradeService.MESSAGE_TOPIC );
        msg.setObject( spotTrade );

        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setShowOrigCptyUserInSTP( 2 );
        spotTrade.setOriginatingUserId( spotTrade.getEntryUser().getObjectID() );

        TradingParty origCpty = CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setOriginatingCptyId( origCpty.getObjectID() );
        origCpty.setSTPEnabled( Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED );
        ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );

        String output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        System.out.println( output );
        //doBasicValidation ( output);
        assertEquals( getValue( output, "453" ).size(), 1 );
        assertEquals( getValue( output, "447" ).size(), 3 );
        assertEquals( getValue( output, "452" ).size(), 3 );
        assertEquals( getValue( output, "448" ).size(), 3 );

        assertEquals( getValue( output, "453" ).get( 0 ), "3" );
        assertEquals( getValue( output, "447" ).toString(), "[D, D, D]" );
        assertEquals( getValue( output, "452" ).containsAll( Arrays.asList( new String[]{"13", "11", "3"} ) ), true );
        assertEquals( getValue( output, "448" ).containsAll( Arrays.asList( new String[]{
                origCpty.getShortName(),
                spotTrade.getEntryUser().getShortName(), spotTrade.getEntryUser().getShortName()} ) ), true ); // because flippnig flag is true
    }

    public void print( DataDictionary dd, Message message ) throws FieldNotFound
    {
        String msgType = message.getHeader().getString( MsgType.FIELD );
        printFieldMap( "", dd, msgType, message.getHeader() );
        printFieldMap( "", dd, msgType, message );
        printFieldMap( "", dd, msgType, message.getTrailer() );
    }

    private void printFieldMap( String prefix, DataDictionary dd, String msgType, FieldMap fieldMap )
            throws FieldNotFound
    {

        Iterator fieldIterator = fieldMap.iterator();
        while ( fieldIterator.hasNext() )
        {
            Field field = ( Field ) fieldIterator.next();
            if ( !isGroupCountField( dd, field ) )
            {
                String value = fieldMap.getString( field.getTag() );
                if ( dd.hasFieldValue( field.getTag() ) )
                {
                    value = dd.getValueName( field.getTag(), fieldMap.getString( field.getTag() ) ) + " (" + value + ")";
                }
                System.out.println( prefix + dd.getFieldName( field.getTag() ) + ": " + value );
            }
        }

        Iterator groupsKeys = fieldMap.groupKeyIterator();
        while ( groupsKeys.hasNext() )
        {
            int groupCountTag = ( ( Integer ) groupsKeys.next() ).intValue();
            System.out.println( prefix + dd.getFieldName( groupCountTag ) + ": count = "
                    + fieldMap.getInt( groupCountTag ) );
            Group g = new Group( groupCountTag, 0 );
            int i = 1;
            while ( fieldMap.hasGroup( i, groupCountTag ) )
            {
                if ( i > 1 )
                {
                    System.out.println( prefix + "  ----" );
                }
                fieldMap.getGroup( i, g );
                printFieldMap( prefix + "  ", dd, msgType, g );
                i++;
            }
        }
    }

    private boolean isGroupCountField( DataDictionary dd, Field field )
    {
        return dd.getFieldTypeEnum( field.getTag() ) == FieldType.NumInGroup;
    }

    public void testFlippingNormalFILPTrade() throws Exception
    {
        //System.out.println( "************************************************************************************* \n testFlippingNormalFILPTrade (FI1 - FI2) \n **************************************************" );
        IdcTransaction tx = initTransaction();
        UnitOfWork uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        FXSingleLeg spotTrade = DealingTestUtilC.createTestSingleLegTrade( 1000, false, true, true, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays( 2 ) );
        //System.out.println( "Trade between " + spotTrade.getCounterpartyA().getOrganization().getShortName() + " and " + spotTrade.getCounterpartyB().getOrganization().getShortName() );

        prepareSingleLegRequest( spotTrade );
        TradeCloneServiceC.createCptyTrades( spotTrade );
        tx.commit();

        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setEventName( TradeService.GENERATE_DOWNLOAD_XML );
        msg.setTopic( TradeService.MESSAGE_TOPIC );
        msg.setObject( spotTrade );
        ( ( Entity ) spotTrade.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FIX" );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );


        msg.setProperty( "organization", spotTrade.getCounterpartyA().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'A' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        String output = ( String ) ts.process( msg ).getProperty( TradeService.GENERATE_DOWNLOAD_XML );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        System.out.println( "original message " + output );
        noFlipping( output, spotTrade, "testFlippingNormalFILPTrade", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalFILPTrade", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyB(), spotTrade.getCounterpartyA().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingNormalFILPTrade", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );

        msg.setProperty( "organization", spotTrade.getCounterpartyB().getOrganization() );
        spotTrade.setCptyTrade( spotTrade.getCptyTrade( 'B' ) );
        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalFILPTrade", msg, Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        flipping( output, spotTrade, "testFlippingNormalFILPTrade", msg, Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );

        CounterpartyUtilC.getTradingParty( spotTrade.getCounterpartyA(), spotTrade.getCounterpartyB().getOrganization() ).setSTPDownloadPerspective( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
        output = builder.buildDownloadMessage( msg, null );
        //System.out.println( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE ) + "\n" + output );
        noFlipping( output, spotTrade, "testFlippingNormalFILPTrade", msg, Counterparty.TAKER_STP_DOWNLOAD_PERSPECTIVE );
    }

    private void noFlipping( String output, FXSingleLeg spotTrade, String methodName, WorkflowMessage msg, int perspective ) throws Exception
    {
        FileWriter fw = new FileWriter( "FIXOutput.log", true );
        fw.write( "Method name :: " + methodName + " \n Trade between " + spotTrade.getCounterpartyA().getOrganization().getShortName() + " and " + spotTrade.getCounterpartyB().getOrganization().getShortName() );
        fw.write( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( perspective ) + "\n" + output );
        fw.write( output + "\n" );
        fw.close();

        int index1 = output.indexOf( "50=" ) + 3;
        String out1 = output.substring( index1, output.indexOf( '\u0001', index1 ) );
        assertEquals( out1, spotTrade.getCounterpartyA().getShortName() );
        index1 = output.indexOf( "57=" ) + 3;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), spotTrade.getCounterpartyB().getShortName() );
        index1 = output.indexOf( "128=" ) + 4;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), spotTrade.getCounterpartyA().getOrganization().getShortName() );
        index1 = output.indexOf( "115=" ) + 4;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), spotTrade.getCounterpartyB().getOrganization().getShortName() );
        index1 = output.indexOf( "448=" ) + 4;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), spotTrade.getEntryUser().getShortName() );
        index1 = output.indexOf( "54=" ) + 3;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), "1" );
    }

    private void flipping( String output, FXSingleLeg spotTrade, String methodName, WorkflowMessage msg, int perspective ) throws Exception
    {
        FileWriter fw = new FileWriter( "FIXOutput.log", true );
        fw.write( "Method name :: " + methodName + " \n Trade between " + spotTrade.getCounterpartyA().getOrganization().getShortName() + " and " + spotTrade.getCounterpartyB().getOrganization().getShortName() );
        fw.write( "Downloading to Org " + ( ( Organization ) msg.getProperty( "organization" ) ).getShortName() + ", Perspective is " + getPerspectiveName( perspective ) + "\n" + output );
        fw.write( output + "\n" );
        fw.close();

        int index1 = output.indexOf( "50=" ) + 3;
        String out1 = output.substring( index1, output.indexOf( '\u0001', index1 ) );
        assertEquals( out1, spotTrade.getCounterpartyB().getShortName() );
        index1 = output.indexOf( "57=" ) + 3;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), spotTrade.getCounterpartyA().getShortName() );
        index1 = output.indexOf( "128=" ) + 4;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), spotTrade.getCounterpartyB().getOrganization().getShortName() );
        index1 = output.indexOf( "115=" ) + 4;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), spotTrade.getCounterpartyA().getOrganization().getShortName() );
        index1 = output.indexOf( "448=" ) + 4;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), spotTrade.getMakerUser().getShortName() );
        index1 = output.indexOf( "54=" ) + 3;
        assertEquals( output.substring( index1, output.indexOf( '\u0001', index1 ) ), "2" );
    }

    public void testFIXSTPDownloadOrderIdWithOriginatingDetailsEnabled() throws Exception
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            // set the both orgs to FIX format
            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setIncludeOrderIdInSTP( false );
            takerOrg.setShowOrigCptyUserInSTP( true );
            takerOrg.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            takerTpForMaker.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            takerTpForMaker.setShowOrigCptyUserInSTP( Counterparty.YES_SHOW_ORIG );

            makerOrg.setIncludeOrderIdInSTP( true );
            makerOrg.setShowOrigCptyUserInSTP( false );
            makerOrg.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            makerTpForTaker.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            makerTpForTaker.setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
            Trade trd = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );
            log( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

            List<String> oid1 = getFIXValue( takerSTPMessage, Integer.toString( OrderID.FIELD ) );
            List<String> oid2 = getFIXValue( makerSTPMessage, Integer.toString( OrderID.FIELD ) );
            log( "oid1=" + oid1 );
            log( "oid2=" + oid2 );
            assertTrue( oid1.get( 0 ).equals( orderId ) );
            assertTrue( oid2.get( 0 ).equals( orderId ) );
            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testFIXSTPDownloadOrderIdWithOriginatingDetailsEnabled", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testFIXSTPDownloadOrderIdWithOriginatingDetailsDisabled() throws Exception
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            // set the both orgs to FIX format
            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setIncludeOrderIdInSTP( false );
            takerOrg.setShowOrigCptyUserInSTP( false );
            takerOrg.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            takerTpForMaker.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            takerTpForMaker.setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );

            makerOrg.setIncludeOrderIdInSTP( true );
            makerOrg.setShowOrigCptyUserInSTP( false );
            makerOrg.setSTPOriginatingDetails( Counterparty.DEFAULT_ORIGINATING_DETAILS );
            makerTpForTaker.setSTPOriginatingDetails( Counterparty.ORGANIZATIION_ORIGINATING_DETAILS );
            makerTpForTaker.setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );
            Trade trd = prepareSingleLegTrade( 1000, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = trd.getCptyTrade( Trade.CPTYA ).getDownloadedMessage();
            String makerSTPMessage = trd.getCptyTrade( Trade.CPTYB ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );
            log( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

            List<String> oid1 = getFIXValue( takerSTPMessage, Integer.toString( OrderID.FIELD ) );
            List<String> oid2 = getFIXValue( makerSTPMessage, Integer.toString( OrderID.FIELD ) );
            log( "oid1=" + oid1 );
            log( "oid2=" + oid2 );
            assertTrue( oid1.isEmpty() );
            assertTrue( oid2.get( 0 ).equals( orderId ) );
            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testFIXSTPDownloadOrderIdWithOriginatingDetailsDisabled", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }
    
    public void testSEFIdentifiers()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setIncludeOrderIdInSTP( false );
            takerOrg.setShowOrigCptyUserInSTP( false );
            takerOrg.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            takerOrg.setShowSEF(true);
            takerTpForMaker.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            takerTpForMaker.setShowOrigCptyUserInSTP( Counterparty.NO_SHOW_ORIG );            
            takerTpForMaker.setReportingParty( 2 );
            takerTpForMaker.setGenerateUSI( true );
            takerTpForMaker.setUSIGenerator( SEFUtilC.TP_USE_GENERATED_USI );
            takerOrg.setDownloadEnabledForOutright( true );

            SEFMBean SEFmbean = ConfigurationFactory.getSefMBean();
            String LEPropertySuffix = takerOrg.getShortName() + "." + takerLe.getShortName();
            WatchPropertyC.update( "Idc.MidRate.Send.Customer.Fix.Enabled." + LEPropertySuffix, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( "Idc.SEF.FIX.IncludeUSI." + LEPropertySuffix, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            
            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setSDR( ConfigurationFactory.getSefMBean().getSDR() );
            trd.setSEFOrg( ConfigurationFactory.getSefMBean().getINFXSEF() );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setCoveredTradeTxId( "FXI100" );
            trd.setCoverTradeTxIds( "FXI101-FXI102" );
            trd.setMakerReferenceId( "FXI200" );
            trd.setTransactionID( "FXI300" );
            TradeLeg leg = trd.getTradeLeg( "fxLeg" );
            leg.setUSI("FXI300");
            FXRate midRate = new FXMidRateC();
            midRate.setSpotRate( 1.001 );
            midRate.setForwardPoints( 0.0001 );
            midRate.setRate( 1.0011 );
            ( ( FXLeg ) leg ).getFXPayment().setFXMidRate( midRate );
            takerTpForMaker.setUSIGenerator( SEFUtilC.TP_NO_USI );
            
            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List< String > USIs = getFIXValue( takerSTPMessage, Integer.toString( USIUtilC.FIX_FIELD_USI ) );
            assertTrue( USIs.get( 0 ).equals( "FXI300" ) );

            List< String > midRates = getFIXValue( takerSTPMessage, Integer.toString( SEFUtilC.FIX_FIELD_MIDRATE ) );
            assertTrue( midRates.get( 0 ).equals( "1.0011" ) );

            tx.release();

        }
        catch ( Exception e )
        {
            fail( "testSEFIdentifiers", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject ( takerTpForMaker );
        }
    }

    public void testMiFIDVenueTimings()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            Organization MTFOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            MTFOrg.setMICCode ( "testMICCode" );
            MTFOrg.setMTFVenue ( true );

            long orderSubmissionTime = System.currentTimeMillis ();
            long execTime = orderSubmissionTime + 31;
            long millis1 = orderSubmissionTime % 1000 ;
            long millis2 = execTime % 1000;
            params.setMiFIDOrderSubmissionTime( orderSubmissionTime );
            params.setMiFIDTradeExecutionTime( execTime );
            trd.setExecutionVenue ( MTFOrg );
            trd.setMiFIDTradeParams ( params );
            trd.setMTFTrade ( true );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List< String > timestamps = getFIXValue( takerSTPMessage, Integer.toString( 769 ) );
            assertTrue( timestamps.get( 0 ).endsWith ( "." + millis1 ) || timestamps.get( 0 ).endsWith ( "." + millis2 ) );

            List< String > timestampTypes = getFIXValue( takerSTPMessage, Integer.toString( 770 ) );
            assertTrue( timestampTypes.get( 0 ).equals( "1" ) ||  timestampTypes.get( 0 ).equals( "10" ) );

            // now set the show MiFID flag to false.
            sleepFor ( 500 );
            takerOrg.setShowMiFID ( false );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            timestamps = getFIXValue( takerSTPMessage, Integer.toString( 769 ) );
            assertTrue( timestamps.isEmpty () );

            timestampTypes = getFIXValue( takerSTPMessage, Integer.toString( 770 ) );
            assertTrue( timestampTypes.isEmpty () );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testMiFIDVenueTimings", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testMiFIDSingleLegISIN()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            Organization MTFOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            MTFOrg.setMICCode ( "testMICCode" );
            MTFOrg.setMTFVenue ( true );

            String ISIN = "testISIN";
            ( ( FXSingleLeg ) trd ).getFXLeg ().setISIN ( ISIN );
            trd.setExecutionVenue ( MTFOrg );
            trd.setMiFIDTradeParams ( params );
            trd.setMTFTrade ( true );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List< String > ISINConstant = getFIXValue( takerSTPMessage, Integer.toString( 22 ) );
            assertTrue( ISINConstant.get( 0 ).equals ( "4" ));

            List< String > ISINValue = getFIXValue( takerSTPMessage, Integer.toString( 48 ) );
            assertTrue( ISINValue.get( 0 ).equals( ISIN ) );

            // now set the MiFID flag to false
            sleepFor ( 500 );
            takerOrg.setShowMiFID ( false );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            ISINConstant = getFIXValue( takerSTPMessage, Integer.toString( 22 ) );
            assertTrue( ISINConstant.isEmpty () );

            ISINValue = getFIXValue( takerSTPMessage, Integer.toString( 48 ) );
            assertTrue( ISINValue.isEmpty () );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testMiFIDSingleLegISIN", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testMiFIDSwapISIN()
    {
        try
        {
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForSwap ( true );

            FXSwap trd = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( trd );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_SWAP ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setTakerTradingCapacity ( "TakerTrdCapacity" );
            params.setMakerTradingCapacity ( "MakerTrdCapacity" );
            params.setMiFIDTradeExecutionTime( new Date().getTime() );
            params.setMiFIDOrderSubmissionTime( new Date().getTime() );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            Organization MTFOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            MTFOrg.setMICCode ( "testMICCode" );
            MTFOrg.setMTFVenue ( true );

            String ISIN1 = "testISINNear";
            String ISIN2 = "testISINFar";
            trd.getNearLeg ().setISIN ( ISIN1 );
            trd.getFarLeg ().setISIN ( ISIN2 );
            request.getRequestPrice ( FXSwap.NEAR_LEG ).setISIN ( ISIN1 );
            request.getRequestPrice ( FXSwap.FAR_LEG ).setISIN ( ISIN2 );

            trd.setExecutionVenue ( MTFOrg );
            trd.setMiFIDTradeParams ( params );
            trd.setMTFTrade ( true );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List< String > ISINConstant = getFIXValue( takerSTPMessage, Integer.toString( 22 ) );
            assertTrue( ISINConstant.get( 0 ).equals ( "4" ));

            List< String > ISINValue = getFIXValue( takerSTPMessage, Integer.toString( 48 ) );
            assertTrue( ISINValue.get( 0 ).equals( ISIN1 + "," + ISIN2 )  );

            // set the show MiFID flag to false
            sleepFor ( 500 );
            takerOrg.setShowMiFID ( false );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            ISINConstant = getFIXValue( takerSTPMessage, Integer.toString( 22 ) );
            assertTrue( ISINConstant.isEmpty () );

            ISINValue = getFIXValue( takerSTPMessage, Integer.toString( 48 ) );
            assertTrue( ISINValue.isEmpty () );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testMiFIDSwapISIN", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMiFIDSingleLegTradingCapacity()
    {
        try
        {
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            makerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );
            makerOrg.setDownloadEnabledForOutright ( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays ( 7 ) );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setMiFIDTradeExecutionTime( new Date ().getTime () );
            params.setMiFIDOrderSubmissionTime ( new Date().getTime () );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            Organization MTFOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            MTFOrg.setMICCode ( "testMICCode" );
            MTFOrg.setMTFVenue ( true );

            String ISIN = "testISIN";
            ( ( FXSingleLeg ) trd ).getFXLeg ().setISIN ( ISIN );
            trd.setExecutionVenue ( MTFOrg );
            trd.setMiFIDTradeParams ( params );
            trd.setMTFTrade ( true );

            String orderId = "1000";
            trd.getRequest ().setOrderId ( orderId );

            String[] takerTCValues = new String[] { "AOTC", "MTCH" ,"DEAL" } ;
            String[] takerTCCodes = new String[] { "1", "3" ,"4" } ;

            String[] makerTCValues = new String[] { "MTCH" ,"DEAL", "AOTC" } ;
            String[] makerTCCodes = new String[] { "3" ,"4", "1" } ;

            for ( int i=0; i < 3; i++ )
            {
                params.setTakerTradingCapacity ( takerTCValues[i] );
                params.setMakerTradingCapacity ( makerTCValues[i] );

                ts.newTrade ( trd, null );
                ts.verifyTrade ( trd, null );

                String takerSTPMessage = getTakerCptyTrade ( trd, takerLe ).getDownloadedMessage ();
                log ( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

                List<String> takerTradingCapacity = getFIXValue ( takerSTPMessage, Integer.toString ( 29 ) );
                assertTrue ( takerTradingCapacity.get ( 0 ).equals ( takerTCCodes[i] ) );

                String makerSTPMessage = getTakerCptyTrade ( trd, makerLe ).getDownloadedMessage ();
                log ( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

                List<String> makerTradingCapacity = getFIXValue ( makerSTPMessage, Integer.toString ( 29 ) );
                assertTrue ( makerTradingCapacity.get ( 0 ).equals ( makerTCCodes[i] ) );
            }

            takerOrg.setShowMiFID ( false );
            makerOrg.setShowMiFID ( false );

            for ( int i=0; i < 3; i++ )
            {
                params.setTakerTradingCapacity ( takerTCValues[i] );
                params.setMakerTradingCapacity ( makerTCValues[i] );

                ts.newTrade ( trd, null );
                ts.verifyTrade ( trd, null );

                String takerSTPMessage = getTakerCptyTrade ( trd, takerLe ).getDownloadedMessage ();
                log ( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

                List<String> takerTradingCapacity = getFIXValue ( takerSTPMessage, Integer.toString ( 29 ) );
                assertTrue ( takerTradingCapacity.isEmpty () );

                String makerSTPMessage = getTakerCptyTrade ( trd, makerLe ).getDownloadedMessage ();
                log ( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

                List<String> makerTradingCapacity = getFIXValue ( makerSTPMessage, Integer.toString ( 29 ) );
                assertTrue ( makerTradingCapacity.isEmpty () );
            }


            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testMiFIDSingleLegTradingCapacity", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testMiFIDSwapTradingCapacity()
    {
        try
        {
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            makerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForSwap ( true );
            makerOrg.setDownloadEnabledForSwap ( true );

            FXSwap trd = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            prepareSwapRequest ( trd );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_SWAP ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSwapRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            MiFIDTradeParams params = new MiFIDTradeParams ();
            params.setMiFIDTradeExecutionTime ( new Date ().getTime () );
            params.setMiFIDOrderSubmissionTime ( new Date().getTime () );
            params.setMakerExecutingUser ( "makerExecUsr" );
            params.setTakerExecutingUser ( "takerExecUsr" );
            params.setTakerExecutingFirm ( "takerExecFirm" );
            params.setMakerExecutingFirm ( "makerExecFirm" );
            params.setTakerExecutingUserCodeSource ( "takerExecUsrCS" );
            params.setTakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setMakerExecutingUserCodeSource ( "makerExecUsrCS" );
            params.setMakerExecutingUserType ( ExecutingUserType.ALGO );
            params.setTakerInvestmentDecisionMaker ( "takerIDM" );
            params.setTakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setTakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerInvestmentDecisionMaker ( "makerIDM" );
            params.setMakerInvestmentDecisionMakerCodeSource ( "takerIDMCS" );
            params.setMakerInvestmentDecisionMakerType ( ExecutingUserType.NATURAL );
            params.setMakerVenueCode ( "IMTF" );
            params.setSIExecution ( true  );
            params.setNPFT ( true );
            params.setCustomerAccount ( "FI1-le1" );
            params.setSecuritiesFinancingTransaction ( true );
            params.setPostTradeDeferralIndicator ( "IQLD" );
            Organization MTFOrg = ReferenceDataCacheC.getInstance ().getOrganization ( "Broker1" );
            MTFOrg.setMICCode ( "testMICCode" );
            MTFOrg.setMTFVenue ( true );

            String ISIN1 = "testISIN1";
            String ISIN2 = "testISIN2";
            trd.getNearLeg ().setISIN ( ISIN1 );
            trd.getFarLeg ().setISIN ( ISIN2 );
            trd.setExecutionVenue ( MTFOrg );
            trd.setMiFIDTradeParams ( params );
            trd.setMTFTrade ( true );

            String orderId = "1000";
            trd.getRequest ().setOrderId ( orderId );

            String[] takerTCValues = new String[] { "AOTC", "MTCH" ,"DEAL" } ;
            String[] takerTCCodes = new String[] { "1", "3" ,"4" } ;

            String[] makerTCValues = new String[] { "MTCH" ,"DEAL", "AOTC" } ;
            String[] makerTCCodes = new String[] { "3" ,"4", "1" } ;

            for ( int i=0; i < 3; i++ )
            {
                params.setTakerTradingCapacity ( takerTCValues[i] );
                params.setMakerTradingCapacity ( makerTCValues[i] );

                ts.newTrade ( trd, null );
                ts.verifyTrade ( trd, null );

                String takerSTPMessage = getTakerCptyTrade ( trd, takerLe ).getDownloadedMessage ();
                log ( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

                List<String> takerTradingCapacity = getFIXValue ( takerSTPMessage, Integer.toString ( 29 ) );
                assertTrue ( takerTradingCapacity.get ( 0 ).equals ( takerTCCodes[i] ) );

                String makerSTPMessage = getTakerCptyTrade ( trd, makerLe ).getDownloadedMessage ();
                log ( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

                List<String> makerTradingCapacity = getFIXValue ( makerSTPMessage, Integer.toString ( 29 ) );
                assertTrue ( makerTradingCapacity.get ( 0 ).equals ( makerTCCodes[i] ) );
            }

            takerOrg.setShowMiFID ( false );
            makerOrg.setShowMiFID ( false );

            for ( int i=0; i < 3; i++ )
            {
                params.setTakerTradingCapacity ( takerTCValues[i] );
                params.setMakerTradingCapacity ( makerTCValues[i] );

                ts.newTrade ( trd, null );
                ts.verifyTrade ( trd, null );

                String takerSTPMessage = getTakerCptyTrade ( trd, takerLe ).getDownloadedMessage ();
                log ( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

                List<String> takerTradingCapacity = getFIXValue ( takerSTPMessage, Integer.toString ( 29 ) );
                assertTrue ( takerTradingCapacity.isEmpty () );

                String makerSTPMessage = getTakerCptyTrade ( trd, makerLe ).getDownloadedMessage ();
                log ( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

                List<String> makerTradingCapacity = getFIXValue ( makerSTPMessage, Integer.toString ( 29 ) );
                assertTrue ( makerTradingCapacity.isEmpty () );
            }


            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testMiFIDSingleLegTradingCapacity", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( MiFIDMBean.IDC_MIFID_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }


    public void testNetTradeRefIdEnabled()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INCLUDE_TRADE_REPORT_REFID, "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade netTrade = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            netTrade.setTransactionID ( "FXI301" );
            trd.setNetTrade ( netTrade );
            netTrade.getNettedTrades ().add ( trd );
            ts.netTrade ( trd, TradeService.NETTED_EVENT );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> netTradeId  = getFIXValue( takerSTPMessage, Integer.toString( 572 ) );
            assertFalse( netTradeId.isEmpty () );
            assertEquals( "FXI301", netTradeId.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testNetTradeRefIdEnabled", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INCLUDE_TRADE_REPORT_REFID, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testNetTradeRefIdDisabled()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INCLUDE_TRADE_REPORT_REFID, "false", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade netTrade = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            netTrade.setTransactionID ( "FXI301" );
            trd.setNetTrade ( netTrade );
            netTrade.getNettedTrades ().add ( trd );
            ts.netTrade ( trd, TradeService.NETTED_EVENT );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> netTradeId  = getFIXValue( takerSTPMessage, Integer.toString( 572 ) );
            assertTrue( netTradeId.isEmpty () );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testNetTradeRefIdDisabled", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testUsePBSettlementCodeOnTakerDownload()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );


            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> cptyA  = getFIXValue( takerSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( takerSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String takerSTPMessage1 = getTakerCptyTrade( trd1, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( takerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( takerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", takerPbLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBSettlementCodeOnTakerDownload", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBSettlementCodeOnMakerDownload()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );
            makerOrg.setShowMiFID ( true );
            makerOrg.setDownloadEnabledForOutright( true );
            makerOrg.setDownloadEnabledForNetting ( true );
            makerOrg.setDownloadEnabledForCancelTrade ( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );


            String makerSTPMessage = getTakerCptyTrade( trd, makerLe ).getDownloadedMessage();
            log( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

            List<String> cptyA  = getFIXValue( makerSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( makerSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String makerSTPMessage1 = getTakerCptyTrade( trd1, makerLe ).getDownloadedMessage();
            log( "makerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", makerPbLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBSettlementCodeOnMakerDownload", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBSettlementCodeOnTakerDownloadWithOnlyCptyD()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            takerTpForMaker.setPrimeBrokerOrganization ( null );
            takerTpForMaker.setPrimeBrokerTradingParty ( null );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );


            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> cptyA  = getFIXValue( takerSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( takerSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );

            String takerSTPMessage1 = getTakerCptyTrade( trd1, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( takerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( takerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerPbLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBSettlementCodeOnTakerDownloadWithOnlyCptyD", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBSettlementCodeOnMakerDownloadWithOnlyCptyC()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );
            makerOrg.setShowMiFID ( true );
            makerOrg.setDownloadEnabledForOutright( true );
            makerOrg.setDownloadEnabledForNetting ( true );
            makerOrg.setDownloadEnabledForCancelTrade ( true );
            makerTpForTaker.setPrimeBrokerOrganization ( null );
            makerTpForTaker.setPrimeBrokerTradingParty ( null );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );


            String makerSTPMessage = getTakerCptyTrade( trd, makerLe ).getDownloadedMessage();
            log( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

            List<String> cptyA  = getFIXValue( makerSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( makerSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String makerSTPMessage1 = getTakerCptyTrade( trd1, makerLe ).getDownloadedMessage();
            log( "makerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerPbLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBSettlementCodeOnMakerDownloadWithOnlyCptyC", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBExtSysSettlementCodeOnTakerDownload()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );

            String testSettlmentCode = "taker->takerPb";
            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( testSettlmentCode );
            takerTpForTakerPb.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );


            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> cptyA  = getFIXValue( takerSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( takerSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String takerSTPMessage1 = getTakerCptyTrade( trd1, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( takerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( takerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", testSettlmentCode, cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBExtSysSettlementCodeOnTakerDownload", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBExtSysSettlementCodeOnMakerDownload()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );
            makerOrg.setShowMiFID ( true );
            makerOrg.setDownloadEnabledForOutright( true );
            makerOrg.setDownloadEnabledForNetting ( true );
            makerOrg.setDownloadEnabledForCancelTrade ( true );

            String testSettlmentCode = "maker->makerPb";
            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( testSettlmentCode );
            makerTpForMakerPb.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String makerSTPMessage = getTakerCptyTrade( trd, makerLe ).getDownloadedMessage();
            log( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

            List<String> cptyA  = getFIXValue( makerSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( makerSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );

            String makerSTPMessage1 = getTakerCptyTrade( trd1, makerLe ).getDownloadedMessage();
            log( "makerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", testSettlmentCode, cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBExtSysSettlementCodeOnMakerDownload", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBExtSysSettlementCodeOnTakerDownloadWithOnlyCptyD()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            takerTpForMaker.setPrimeBrokerOrganization ( null );
            takerTpForMaker.setPrimeBrokerTradingParty ( null );

            String testSettlmentCode = "taker->makerPb";
            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( testSettlmentCode );
            takerTpForMakerPb.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> cptyB  = getFIXValue( takerSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String takerSTPMessage1 = getTakerCptyTrade( trd1, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( takerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( takerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", testSettlmentCode, cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBExtSysSettlementCodeOnTakerDownloadWithOnlyCptyD", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBExtSysSettlementCodeOnMakerDownloadWithOnlyCptyC()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( makerOrg, TradeServiceConstants.FIX_FORMAT );
            makerOrg.setShowMiFID ( true );
            makerOrg.setDownloadEnabledForOutright( true );
            makerOrg.setDownloadEnabledForNetting ( true );
            makerOrg.setDownloadEnabledForCancelTrade ( true );
            makerTpForTaker.setPrimeBrokerOrganization ( null );
            makerTpForTaker.setPrimeBrokerTradingParty ( null );

            String testSettlmentCode = "maker->takerPb";
            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( testSettlmentCode );
            makerTpForTakerPb.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String makerSTPMessage = getTakerCptyTrade( trd, makerLe ).getDownloadedMessage();
            log( "makerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage );

            List<String> cptyA  = getFIXValue( makerSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( makerSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String makerSTPMessage1 = getTakerCptyTrade( trd1, makerLe ).getDownloadedMessage();
            log( "makerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", testSettlmentCode, cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBExtSysSettlementCodeOnMakerDownloadWithOnlyCptyC", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBSettlementCodeOnCptyCDownloadWithOnlyCptyC()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerPbOrg, TradeServiceConstants.FIX_FORMAT );
            takerPbOrg.setShowMiFID ( true );
            takerPbOrg.setDownloadEnabledForOutright( true );
            takerPbOrg.setDownloadEnabledForNetting ( true );
            takerPbOrg.setDownloadEnabledForCancelTrade ( true );
            makerTpForTaker.setPrimeBrokerOrganization ( null );
            makerTpForTaker.setPrimeBrokerTradingParty ( null );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerPBSTPMessage = trd.getCptyTrade ( Trade.CPTYC ).getDownloadedMessage();
            log( "takerPBSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerPBSTPMessage );

            List<String> cptyA  = getFIXValue( takerPBSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( takerPBSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );

            String takerPBSTPMessage1 = trd1.getCptyTrade ( Trade.CPTYC ).getDownloadedMessage();
            log( "takerPBSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerPBSTPMessage1 );

            List<String> cptyA1  = getFIXValue( takerPBSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( takerPBSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBSettlementCodeOnCptyCDownloadWithOnlyCptyC", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBSettlementCodeOnCptyDDownloadWithOnlyCptyD()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( makerPbOrg, TradeServiceConstants.FIX_FORMAT );
            makerPbOrg.setShowMiFID ( true );
            makerPbOrg.setDownloadEnabledForOutright( true );
            makerPbOrg.setDownloadEnabledForNetting ( true );
            makerPbOrg.setDownloadEnabledForCancelTrade ( true );
            takerTpForMaker.setPrimeBrokerOrganization ( null );
            takerTpForMaker.setPrimeBrokerTradingParty ( null );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );


            String makerPBSTPMessage = trd.getCptyTrade ( Trade.CPTYD ).getDownloadedMessage();
            log( "makerPBSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerPBSTPMessage );

            List<String> cptyA  = getFIXValue( makerPBSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( makerPBSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyD ( makerTpForMakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );

            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );

            String makerPBSTPMessage1 = trd1.getCptyTrade ( Trade.CPTYD ).getDownloadedMessage();
            log( "makerPBSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerPBSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerPBSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerPBSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBSettlementCodeOnCptyCDownloadWithOnlyCptyC", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBSettlementCodeOnCptyCDownloadWithBothCAndD()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerPbOrg, TradeServiceConstants.FIX_FORMAT );
            takerPbOrg.setShowMiFID ( true );
            takerPbOrg.setDownloadEnabledForOutright( true );
            takerPbOrg.setDownloadEnabledForNetting ( true );
            takerPbOrg.setDownloadEnabledForCancelTrade ( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerPBSTPMessage = trd.getCptyTrade ( Trade.CPTYC ).getDownloadedMessage();
            log( "takerPBSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerPBSTPMessage );

            List<String> cptyA  = getFIXValue( takerPBSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( takerPBSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String takerPBSTPMessage1 = trd1.getCptyTrade ( Trade.CPTYC ).getDownloadedMessage();
            log( "takerPBSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerPBSTPMessage1 );

            List<String> cptyA1  = getFIXValue( takerPBSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( takerPBSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerPbLe.getShortName (), cptyB1.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBSettlementCodeOnCptyCDownloadWithBothCAndD", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBSettlementCodeOnCptyDDownloadWithBothCAndD()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( makerPbOrg, TradeServiceConstants.FIX_FORMAT );
            makerPbOrg.setShowMiFID ( true );
            makerPbOrg.setDownloadEnabledForOutright( true );
            makerPbOrg.setDownloadEnabledForNetting ( true );
            makerPbOrg.setDownloadEnabledForCancelTrade ( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );


            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );


            String makerPBSTPMessage = trd.getCptyTrade ( Trade.CPTYD ).getDownloadedMessage();
            log( "takerPBSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerPBSTPMessage );

            List<String> cptyA  = getFIXValue( makerPBSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( makerPBSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String makerPBSTPMessage1 = trd1.getCptyTrade ( Trade.CPTYD ).getDownloadedMessage();
            log( "makerPBSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerPBSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerPBSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerPbLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerPBSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB1.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBSettlementCodeOnCptyDDownloadWithBothCAndD", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBExtSysSettlementCodeOnCptyCDownloadWithOnlyCptyC()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerPbOrg, TradeServiceConstants.FIX_FORMAT );
            takerPbOrg.setShowMiFID ( true );
            takerPbOrg.setDownloadEnabledForOutright( true );
            takerPbOrg.setDownloadEnabledForNetting ( true );
            takerPbOrg.setDownloadEnabledForCancelTrade ( true );
            makerTpForTaker.setPrimeBrokerOrganization ( null );
            makerTpForTaker.setPrimeBrokerTradingParty ( null );

            String testSettlmentCode = "takerPb->taker";
            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( testSettlmentCode );
            takerPbTpForTaker.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerPbSTPMessage = trd.getCptyTrade ( Trade.CPTYC ).getDownloadedMessage();
            log( "takerPbSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerPbSTPMessage );

            List<String> cptyA  = getFIXValue( takerPbSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", testSettlmentCode, cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( takerPbSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String makerSTPMessage1 = trd.getCptyTrade ( Trade.CPTYC ).getDownloadedMessage();
            log( "makerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", testSettlmentCode, cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBExtSysSettlementCodeOnMakerDownloadWithOnlyCptyC", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBExtSysSettlementCodeOnCptyDDownloadWithOnlyCptyD()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( makerPbOrg, TradeServiceConstants.FIX_FORMAT );
            makerPbOrg.setShowMiFID ( true );
            makerPbOrg.setDownloadEnabledForOutright( true );
            makerPbOrg.setDownloadEnabledForNetting ( true );
            makerPbOrg.setDownloadEnabledForCancelTrade ( true );
            takerTpForMaker.setPrimeBrokerOrganization ( null );
            takerTpForMaker.setPrimeBrokerTradingParty ( null );

            String testSettlmentCode = "makerPb->maker";
            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( testSettlmentCode );
            makerPbTpForMaker.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String makerPbSTPMessage = trd.getCptyTrade ( Trade.CPTYD ).getDownloadedMessage();
            log( "takerPbSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerPbSTPMessage );

            List<String> cptyA  = getFIXValue( makerPbSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( makerPbSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", testSettlmentCode, cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd1.setCounterpartyC ( takerTpForTakerPb );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String makerPbSTPMessage1 = trd.getCptyTrade ( Trade.CPTYD ).getDownloadedMessage();
            log( "makerSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerPbSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerPbSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerPbSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", testSettlmentCode, cptyB1.get ( 0 ) );
            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBExtSysSettlementCodeOnCptyDDownloadWithOnlyCptyD", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBExtSysSettlementCodeOnCptyCDownloadWithBothCAndD()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerPbOrg, TradeServiceConstants.FIX_FORMAT );
            takerPbOrg.setShowMiFID ( true );
            takerPbOrg.setDownloadEnabledForOutright( true );
            takerPbOrg.setDownloadEnabledForNetting ( true );
            takerPbOrg.setDownloadEnabledForCancelTrade ( true );

            String testSettlmentCodeFromTakerPBToTaker = "takerPb->taker";
            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( testSettlmentCodeFromTakerPBToTaker );
            takerPbTpForTaker.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );

            String testSettlmentCodeFromMakerPBToMaker = "makerPb->maker";
            CounterpartyExternalSystemIdC extId1 = new CounterpartyExternalSystemIdC();
            extId1.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId1.setName( testSettlmentCodeFromMakerPBToMaker );
            makerPbTpForMaker.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId1 );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerPBSTPMessage = trd.getCptyTrade ( Trade.CPTYC ).getDownloadedMessage();
            log( "takerPBSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerPBSTPMessage );

            List<String> cptyA  = getFIXValue( takerPBSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", testSettlmentCodeFromTakerPBToTaker, cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( takerPBSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", makerLe.getShortName (), cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String takerPBSTPMessage1 = trd1.getCptyTrade ( Trade.CPTYC ).getDownloadedMessage();
            log( "takerPBSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerPBSTPMessage1 );

            List<String> cptyA1  = getFIXValue( takerPBSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", testSettlmentCodeFromTakerPBToTaker, cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( takerPBSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", makerPbLe.getShortName (), cptyB1.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBExtSysSettlementCodeOnCptyCDownloadWithBothCAndD", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + takerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testUsePBExtSysSettlementCodeOnCptyDDownloadWithBothCAndD()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( makerPbOrg, TradeServiceConstants.FIX_FORMAT );
            makerPbOrg.setShowMiFID ( true );
            makerPbOrg.setDownloadEnabledForOutright( true );
            makerPbOrg.setDownloadEnabledForNetting ( true );
            makerPbOrg.setDownloadEnabledForCancelTrade ( true );

            String testSettlmentCodeFromTakerPBToTaker = "takerPb->taker";
            CounterpartyExternalSystemIdC extId = new CounterpartyExternalSystemIdC();
            extId.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId.setName( testSettlmentCodeFromTakerPBToTaker );
            takerPbTpForTaker.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId );

            String testSettlmentCodeFromMakerPBToMaker = "makerPb->maker";
            CounterpartyExternalSystemIdC extId1 = new CounterpartyExternalSystemIdC();
            extId1.setExternalSystem( ( ExternalSystem ) ReferenceDataCacheC.getInstance().getEntityByShortName( "SettlementCode", ExternalSystemC.class, null, null ) );
            extId1.setName( testSettlmentCodeFromMakerPBToMaker );
            makerPbTpForMaker.setExternalSystemId( TradeServiceConstants.SETTLEMENT_CODE, extId1 );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String makerPBSTPMessage = trd.getCptyTrade ( Trade.CPTYD ).getDownloadedMessage();
            log( "takerPBSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerPBSTPMessage );

            List<String> cptyA  = getFIXValue( makerPBSTPMessage, Integer.toString( 50 ) );
            assertTrue( !cptyA.isEmpty () );
            assertEquals ( "taker le", takerLe.getShortName (), cptyA.get ( 0 ) );

            List<String> cptyB  = getFIXValue( makerPBSTPMessage, Integer.toString( 57 ) );
            assertTrue( !cptyB.isEmpty () );
            assertEquals ( "maker le", testSettlmentCodeFromMakerPBToMaker, cptyB.get ( 0 ) );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            Trade trd1 = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd1.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd1.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd1 );
            Request request1 = trd1.getRequest();
            request1.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd1.setTransactionID( "FXI301" );


            String orderId1 = "1001";
            trd1.getRequest().setOrderId( orderId1 );
            ts.newTrade( trd1, null );
            ts.verifyTrade( trd1, null );


            String makerPBSTPMessage1 = trd1.getCptyTrade ( Trade.CPTYD ).getDownloadedMessage();
            log( "takerPBSTPMessage1 in format=" + TradeServiceConstants.FIX_FORMAT + " : " + makerPBSTPMessage1 );

            List<String> cptyA1  = getFIXValue( makerPBSTPMessage1, Integer.toString( 50 ) );
            assertTrue( !cptyA1.isEmpty () );
            assertEquals ( "taker le", takerPbLe.getShortName (), cptyA1.get ( 0 ) );

            List<String> cptyB1  = getFIXValue( makerPBSTPMessage1, Integer.toString( 57 ) );
            assertTrue( !cptyB1.isEmpty () );
            assertEquals ( "maker le", testSettlmentCodeFromMakerPBToMaker, cptyB1.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testUsePBExtSysSettlementCodeOnCptyDDownloadWithBothCAndD", e );
        }
        finally
        {
            refreshOrgsAndTradingParties();
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + makerPbOrg.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testCurrencyPairAlias()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setShowMiFID ( true );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );

            Trade trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + takerOrg.getShortName (), "EUR/USD~DEF/XYZ", ConfigurationProperty.DYNAMIC_SCOPE );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> ccyPair  = getFIXValue( takerSTPMessage, Integer.toString( 55 ) );
            assertTrue( !ccyPair.isEmpty () );
            assertEquals ( "maker le", "DEF/XYZ", ccyPair.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairAlias", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityType()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "FOR", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityType", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    public void testSecurityTypeWithTradeDetail_FXSpot()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_SP ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "FXSPOT", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithTradeDetail_FXSpot", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithTradeDetail_FXOutright()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "FXFWD", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithTradeDetail_FXOutright", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithTradeDetail_FXNDF()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNDF ( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.addDays ( 15 ) );
            trd.getFXLeg ().getFXPayment ().setFixingDate ( spotDate.addDays ( 10 ) );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_FXNDF ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "FXNDF", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithTradeDetail_FXNDF", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithTradeDetail_FXSwap()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForSwap ( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSwap trd = prepareSwapTrade( 1000, 1000, true, false, false,false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], bidRates[0], spotDate, spotDate.addDays ( 10 ) );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_SWAP ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSwapRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "FXSWAP", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithTradeDetail_FXSwap", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithInstrumentDetail_Currency()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "CURRENCY~FOR1;METAL~ETC;INDEX~CFD;ENERGY~ETC;CRYPTO~CRYP", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "FOR1", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithInstrumentDetail_Currency", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithInstrumentDetail_Index()
    {
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            usd.setInstrumentClassification ( ( InstrumentClassification ) namedEntityReader.execute( InstrumentClassification.class, InstrumentClassification.INDEX_CLSF ) );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "CURRENCY~FOR1;METAL~ETC;INDEX~CFD;ENERGY~ETC;CRYPTO~CRYP", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "CFD", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithInstrumentDetail_Index", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject ( usd );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithInstrumentDetail_Energy()
    {
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            usd.setInstrumentClassification ( ( InstrumentClassification ) namedEntityReader.execute( InstrumentClassification.class, InstrumentClassification.INDEX_CLSF ) );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "CURRENCY~FOR1;METAL~ETC;INDEX~CFD;ENERGY~ETC;CRYPTO~CRYP", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "CFD", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithInstrumentDetail_Index", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject ( usd );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithInstrumentDetail_Metal()
    {
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            usd.setInstrumentClassification ( ( InstrumentClassification ) namedEntityReader.execute( InstrumentClassification.class, InstrumentClassification.METAL_CLSF ) );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "CURRENCY~FOR1;METAL~ETC1;INDEX~CFD;ENERGY~ETC;CRYPTO~CRYP", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "ETC1", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithInstrumentDetail_Metal", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject ( usd );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithInstrumentDetail_Crypto()
    {
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            usd.setInstrumentClassification ( ( InstrumentClassification ) namedEntityReader.execute( InstrumentClassification.class, InstrumentClassification.CRYPTO_CLSF ) );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "CURRENCY~FOR1;METAL~ETC1;INDEX~CFD;ENERGY~ETC;CRYPTO~CRYP", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "CRYP", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithInstrumentDetail_Crypto", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject ( usd );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithInstrumentDetail_CurrencyAndTradeDetail_FXOutright()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "CURRENCY~FOR;METAL~ETC;INDEX~CFD;ENERGY~ETC;CRYPTO~CRYP", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "FXFWD", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithInstrumentDetail_CurrencyAndTradeDetail_FXOutright", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithInstrumentDetail_IndexAndTradeDetail_FXOutright()
    {
        Currency eur = CurrencyFactory.getCurrency ( "EUR" );
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            eur.setInstrumentClassification ( ( InstrumentClassification ) namedEntityReader.execute( InstrumentClassification.class, InstrumentClassification.INDEX_CLSF ) );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "CURRENCY~FOR1;METAL~ETC;INDEX~CFD;ENERGY~ETC;CRYPTO~CRYP", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "CFD", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithInstrumentDetail_CurrencyAndTradeDetail_FXOutright", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject ( eur );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_TRADETYPES + "." + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSecurityTypeWithInstrumentDetail_Metal_NoMapping()
    {
        Currency usd = CurrencyFactory.getCurrency ( "USD" );
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );

            usd.setInstrumentClassification ( ( InstrumentClassification ) namedEntityReader.execute( InstrumentClassification.class, InstrumentClassification.METAL_CLSF ) );

            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + takerOrg.getShortName (), "CURRENCY~FOR1;INDEX~CFD;ENERGY~ETC;CRYPTO~CRYP", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> securityType  = getFIXValue( takerSTPMessage, Integer.toString( 167 ) );
            assertFalse ( securityType.isEmpty () );
            assertEquals ( "securityType", "FOR", securityType.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testSecurityTypeWithInstrumentDetail_Metal_NoMapping", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            IdcUtilC.refreshObject ( usd );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testPricingTypeCustomTagField()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );


            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED_PREFIX + takerOrg.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );
            trd.setPricingType( "RFS" );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> customTagCountStr  = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.CUSTOM_TAG_COUNT_FIELD) );
            assertFalse ( customTagCountStr.isEmpty () );
            assertEquals ( "customTagsCount", "1", customTagCountStr.get ( 0 ) );

            List<String> pricingTypeName  = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.CUSTOM_TAG_NAME_FIELD ) );
            assertFalse ( pricingTypeName.isEmpty () );
            assertEquals ( "pricingTypeName", TradeDownloadFIXMessageBuilderC.CUSTOM_TAG_NAME_PRICING_TYPE, pricingTypeName.get ( 0 ) );

            List<String> pricingTypeValue  = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.CUSTOM_TAG_VALUE_FIELD ) );
            assertFalse ( pricingTypeValue.isEmpty () );
            assertEquals ( "pricingTypeValue", "RFS", pricingTypeValue.get ( 0 ) );

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testPricingTypeCustomTagField", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
            WatchPropertyC.update ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED_PREFIX + takerOrg.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testProrataSchedule()
    {
        try
        {
            IdcTransaction tx;
            tx = initTransaction();
            tx.getUOW().addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );


            setSTPFormat( takerOrg, TradeServiceConstants.FIX_FORMAT );
            takerOrg.setDownloadEnabledForOutright( true );
            takerOrg.setDownloadEnabledForNetting ( true );
            takerOrg.setDownloadEnabledForCancelTrade ( true );

            FXSingleLeg trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
            trd.setTradeClassification( ( TradeClassification ) namedEntityReader.execute( TradeClassification.class, ISCommonConstants.TRD_CLSF_OR ) );

            trd.setMakerUser( UserFactory.getUser( "fi2mm2@FI2" ) );

            ProrataForwardSchedule schedule = new ProrataForwardSchedule();
            List<Tenor> tenors = tradeConfig.getProrataForwardTenors( makerOrg );
            IdcDate date = DateTimeFactory.newDate();
            double marketStartRate = 1.1;
            double marketPoints = 0.001;
            double customerStartRate = 1.2;
            double customerPoints = 0.002;
            for ( int i = 1; i <= tenors.size(); i++ )
            {
                Tenor tenor = tenors.get( i - 1 );
                ProrataForwardTenor prorataForwardTenor = new ProrataForwardTenor();
                prorataForwardTenor.setTenor( tenor.getName() );
                prorataForwardTenor.setStartDate( date.addMonths( tenor.getDatePeriod().getLength() -1  ).getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN ) );
                prorataForwardTenor.setEndDate( date.addMonths( tenor.getDatePeriod().getLength()  ).getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN) );
                prorataForwardTenor.setMarketStartRate( i * marketStartRate );
                prorataForwardTenor.setMarketSwapPoints( i * marketPoints);
                prorataForwardTenor.setCustomerStartRate( i * customerStartRate );
                prorataForwardTenor.setCustomerSwapPoints( i * customerPoints );
                schedule.addProrataTenor( prorataForwardTenor );
            }

            trd.setSubTradeType( ISCommonConstants.PRORATA_FORWARD );
            trd.setProrataForwardSchedule( IdcUtilC.serializeToJSON( schedule ) );

            prepareSingleLegRequest( trd );
            Request request = trd.getRequest();
            request.setRequestClassification( ( RequestClassificationC ) namedEntityReader.execute( RequestClassificationC.class, ISCommonConstants.RFQ_CREATE_TYPE ) );
            trd.setTransactionID( "FXI300" );

            String orderId = "1000";
            trd.getRequest().setOrderId( orderId );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );

            String takerSTPMessage = getTakerCptyTrade( trd, takerLe ).getDownloadedMessage();
            log( "takerSTPMessage in format=" + TradeServiceConstants.FIX_FORMAT + " : " + takerSTPMessage );

            List<String> prorateTenorsCountStr  = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.PRORATA_FORWARD_TENORS_COUNT_FIELD ) );
            assertFalse ( prorateTenorsCountStr.isEmpty () );
            assertEquals ( "prorateTenorsCountStr", tenors.size() + "", prorateTenorsCountStr.get ( 0 ) );

            FXLeg fxLeg = ( FXLeg ) trd.getTradeLeg ( FXSingleLeg.SINGLE_LEG );
            FXRate fxRate = fxLeg.getFXPayment ().getFXRate ();
            for ( int i = 1; i <= tenors.size(); i++ )
            {
                Tenor tenor = tenors.get( i - 1 );

                List<String> startDates = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.PRORATA_FORWARD_TENORS_START_DATE_FIELD ) );
                assertFalse( startDates.isEmpty() );
                assertEquals( "startDates", date.addMonths( tenor.getDatePeriod().getLength() -1 ).getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN ), startDates.get( i - 1));

                List<String> endDates = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.PRORATA_FORWARD_TENORS_END_DATE_FIELD ) );
                assertFalse( endDates.isEmpty() );
                assertEquals( "endDates", date.addMonths( tenor.getDatePeriod().getLength() ).getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN ), endDates.get( i - 1 ) );

                List<String> marketStartRates = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.PRORATA_FORWARD_TENORS_MARKET_START_RATE_FIELD ) );
                assertFalse( marketStartRates.isEmpty() );
                assertEquals( "marketStartRates", fxRate.getRateFormat ( RATE_FORMAT_STRING ).format (  i * marketStartRate ), marketStartRates.get( i - 1 ) );

                List<String> marketSwapPoints = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.PRORATA_FORWARD_TENORS_MARKET_SWAP_POINTS_FIELD ) );
                assertFalse( marketSwapPoints.isEmpty() );
                assertEquals( "marketSwapPoints", fxRate.getForwardPointsFormat ( RATE_FORMAT_STRING ).format ( i * marketPoints ), marketSwapPoints.get( i - 1 ) );

                List<String> customerStartRates = getFIXValue( takerSTPMessage, Integer.toString( TradeDownloadFIXMessageBuilderC.PRORATA_FORWARD_TENORS_CUSTOMER_START_RATE_FIELD ) );
                assertFalse( customerStartRates.isEmpty() );
                assertEquals( "customerStartRates", fxRate.getRateFormat ( RATE_FORMAT_STRING ).format (   i * customerStartRate ), customerStartRates.get( i - 1 ) );

                List<String> customerSwapPoints = getFIXValue( takerSTPMessage, Integer.toString(TradeDownloadFIXMessageBuilderC.PRORATA_FORWARD_TENORS_CUSTOMER_SWAP_POINTS_FIELD) );
                assertFalse( customerSwapPoints.isEmpty() );
                assertEquals( "customerSwapPoints", fxRate.getForwardPointsFormat ( RATE_FORMAT_STRING ).format ( i * customerPoints ), customerSwapPoints.get( i - 1 ) );
            }

            tx.release();
        }
        catch ( Exception e )
        {
            fail( "testProrataSchedule", e );
        }
        finally
        {
            IdcUtilC.refreshObject( takerOrg );
            IdcUtilC.refreshObject( makerOrg );
        }
    }

    private static CptyTrade getTakerCptyTrade( Trade trade, LegalEntity takerLe )
    {
        Collection< CptyTrade > cptyTrades = trade.getCptyTrades();
        for( CptyTrade cptyTrade : cptyTrades )
        {
            if( takerLe.isSameAs( cptyTrade.getLegalEntity() ) )
            {
                return cptyTrade;
            }
        }
        
        return null;
    }

    private void refreshOrgsAndTradingParties()
    {
        IdcUtilC.refreshObject ( takerOrg );
        IdcUtilC.refreshObject ( makerOrg );
        IdcUtilC.refreshObject( takerPbOrg );
        IdcUtilC.refreshObject( makerPbOrg );
        IdcUtilC.refreshObject ( takerTpForMaker );
        IdcUtilC.refreshObject ( takerTpForTakerPb );
        IdcUtilC.refreshObject ( takerTpForMakerPb );
        IdcUtilC.refreshObject ( makerTpForTaker );
        IdcUtilC.refreshObject ( makerTpForTakerPb );
        IdcUtilC.refreshObject ( makerTpForMakerPb );
        IdcUtilC.refreshObject ( takerPbTpForTaker );
        IdcUtilC.refreshObject ( takerPbTpForMaker );
        IdcUtilC.refreshObject ( takerPbTpForMakerPb );
        IdcUtilC.refreshObject ( makerPbTpForTaker );
        IdcUtilC.refreshObject ( makerPbTpForMaker );
        IdcUtilC.refreshObject ( makerPbTpForTakerPb );
    }
}
