package com.integral.finance.trade.test;

import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.TradeServiceC;
import com.integral.message.query.NamedCriteria;
import com.integral.message.query.QueryCriteria;
import com.integral.message.query.QueryMessage;
import com.integral.message.query.QueryMessageFactory;
import com.integral.message.query.QueryParameter;
import com.integral.message.query.QueryResult;
import com.integral.message.query.ResultItem;

import java.util.ArrayList;
import java.util.Collection;

// Copyright (c) 1999-2006 Integral Development Corp.  All rights reserved.

/**
 * TradeServiceDoneTradesByIdTestC
 *
 * <AUTHOR> Development Corp.
 */

public class TradeServiceDoneTradesByIdTestC extends TradeServiceDoneTradesTestC
{
    public TradeServiceDoneTradesByIdTestC( String name )
    {
        super( name );
    }

    public void testDoneTradesBySingleValidTxID()
    {
        log( "Test# 1 --- Test Done Trades with Single Valid Tx ID" );
        String txID = createTrade( 1 );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TRANSACTIONID_PARAM );
        queryParam.setValue( txID );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_ID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );

        assertEquals( "Valid TxID result count:", 1, result.getCount() );

        Trade trade = ( Trade ) ( ( ResultItem ) result.getResultItems().get( 0 ) ).getResult();
        assertEquals( "Valid TxID:", txID, trade.getTransactionID() );

    }

    public void testDoneTradesBySingleInvalidTxID()
    {

        log( "Test# 2 --- Test Done Trades with Single Invalid Tx ID" );

        String txID = "TX" + System.currentTimeMillis();

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TRANSACTIONID_PARAM );
        queryParam.setValue( txID );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_ID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 2 );
        assertEquals( "Inalid TxID result count:", 0, result.getCount() );
    }

    public void testDoneTradesByMultipleValidTxID()
    {
        log( "Test# 3 --- Test Done Trades with Multiple Valid Tx ID" );

        String txID = createTrade( 1 );
        String txID1 = createTrade( 2 );
        String txID2 = createTrade( 3 );

        String txIDs = txID + ',' + txID1 + ',' + txID2;

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TRANSACTIONID_PARAM );
        queryParam.setValue( txIDs );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_ID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        assertEquals( "Valid TxID result count:", 3, result.getCount() );

        String retrievedIDs = getTxIDString( result );

        assertEquals( "Valid TxIDs:", txIDs, retrievedIDs );

    }

    public void testDoneTradesByMultipleInvalidTxID()
    {
        log( "Test# 4 --- Test Done Trades with Multiple Invalid Tx ID" );

        String txID = "TX" + System.currentTimeMillis();
        String txID1 = "TX" + System.currentTimeMillis();
        String txID2 = "TX" + System.currentTimeMillis();

        String txIDs = txID + ',' + txID1 + ',' + txID2;

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TRANSACTIONID_PARAM );
        queryParam.setValue( txIDs );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_ID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        assertEquals( "Valid TxID result count:", 0, result.getCount() );
    }

    public void testDoneTradesBySomeInvalidTxID()
    {
        log( "Test# 5 --- Test Done Trades with Some Invalid Tx ID" );

        String txID = "TX" + System.currentTimeMillis();
        String txID1 = createTrade( 1 );
        String txID2 = "TX" + System.currentTimeMillis();

        String txIDs = txID + ',' + txID1 + ',' + txID2;

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TRANSACTIONID_PARAM );
        queryParam.setValue( txIDs );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_ID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        printResult( result, 1 );
        assertEquals( "Valid TxID result count:", 1, result.getCount() );

        Trade trade = ( Trade ) ( ( ResultItem ) result.getResultItems().get( 0 ) ).getResult();
        assertEquals( "Valid TxID:", txID1, trade.getTransactionID() );
    }

    public void testDoneTradesByNoTxID()
    {
        log( "Test# 6 --- Test Done Trades with No Tx ID" );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TRANSACTIONID_PARAM );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( TradeService.DONE_TRADES_ID );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        assertEquals( "Valid error count:", false, resultMessage.getErrors().isEmpty() );
        assertEquals( "Valid result count:", null, result );
    }
}
