package com.integral.finance.trade.test;

import org.eclipse.persistence.sessions.UnitOfWork;

import com.integral.SEF.SEFUtilC;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.dealing.Request;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TradeService;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.session.IdcTransaction;
import com.integral.time.IdcDate;

public class TakerUSITestC extends TradeServiceTestC
{

	public TakerUSITestC( String name )
	{
		super(name);
	}
	
	public void testTakerUSI()
	{
		IdcTransaction tx = null;
		try
		{
	        tx = initTransaction();
	        UnitOfWork uow = tx.getUOW();
	        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
	        IdcDate fixingDate = spotDate.addDays( 2 );
	        FXSingleLeg trade = prepareSingleLegTrade( 100.00, true, true, "EUR/USD", makerOrg, makerTpForTaker, 1.2334, spotDate, fixingDate, Tenor.SPOT_TENOR );
	        prepareSingleLegRequest( trade );
	        //TradeCloneServiceC.createCptyTrades( trade );
	
	        WorkflowMessage msg2 = MessageFactory.newWorkflowMessage();
	        msg2.setEventName( TradeService.NEW_EVENT );
	        msg2.setTopic( TradeService.MESSAGE_TOPIC );
	        msg2.setProperty( "WORKFLOW_CODE", "2" );
	        msg2.setObject( trade );
	        msg2.setProperty( "WORKFLOW_CODE_ARG", "TEST" );
	        
	        LegalEntity counterpartyB = ( LegalEntity ) trade.getCounterpartyB();
	        TradingParty tp = ( TradingParty ) CounterpartyUtilC.getTradingParty( counterpartyB, trade.getOrganization() );
	        tp.setUSIGenerator( SEFUtilC.TP_USE_TAKER_USI );
	        final String TESTUSI = "FXTAKERUSI12345";
	        msg2.setParameterValue( SEFUtilC.WF_FIX_USI_PARAM, TESTUSI );
	        Request request = trade.getRequest();
	        request.setTakerUSI( TESTUSI );
	        
	        ts.newTrade( trade, msg2 );
	        ts.process( msg2 );
	        
	        String USI = SEFUtilC.getUSI( trade );
	        assertTrue( TESTUSI.equals( USI ) );
		}
		catch( Exception ex )
		{
			fail( "testTakerUSI failed ", ex );
		}
		finally
		{
	        tx.release();			
		}
	}
}
