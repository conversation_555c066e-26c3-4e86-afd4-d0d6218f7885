package com.integral.finance.trade.test;


import com.integral.finance.fx.FXSwapC;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeFolder;
import com.integral.finance.trade.TradeFolderC;
import com.integral.finance.trade.query.tradeFolder.AllTradesQueryC;
import com.integral.finance.trade.query.tradeFolder.TradesQueryC;
import com.integral.query.QueryCriteria;
import com.integral.query.QueryFactory;
import com.integral.query.QueryResult;
import com.integral.query.QueryService;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDateTime;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Iterator;
import java.util.Vector;


/**
 * Unit tests for trade folders
 */
public class TradeFolderPTestC
        extends PTestCaseC
{
    static String name = "Trade Folder Test";
    Vector users = null;

    public TradeFolderPTestC( String name )
    {
        super( name );
        QueryFactory.setNamedQuery( TradesQueryC.QUERY_NAME, TradesQueryC.class );
        QueryFactory.setNamedQuery( AllTradesQueryC.QUERY_NAME, AllTradesQueryC.class );
    }

    public void testInsert()
    {
        log( "starting insertTest" );
        try
        {

            IdcDateTime date = DateTimeFactory.newDateTime();

            Trade trade = new FXSwapC();

            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            trade = ( Trade ) uow.registerObject( trade );

            TradeFolder parent = new TradeFolderC();
            parent = ( TradeFolder ) uow.registerObject( parent );
            parent.setShortName( "Parent-" + date.getSecondOfDay() );

            TradeFolder child = new TradeFolderC();
            child = ( TradeFolder ) uow.registerObject( child );
            child.setShortName( "Child-" + date.getSecondOfDay() );
            child.add( trade );
            parent.getChildren().add( child );
            child.setParent( parent );

            TradeFolder second = new TradeFolderC();
            second = ( TradeFolder ) uow.registerObject( second );
            second.setShortName( "Second-" + date.getSecondOfDay() );
            second.add( trade );

            uow.commit();

            printTradeFolder( child.getParent(), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
        log( "done with insertTest" );
    }

    public void testLookup()
    {
        log( "starting lookupTest" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( TradeFolder.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                TradeFolder folder = ( TradeFolder ) objs.elementAt( i );
                printTradeFolder( folder, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTest" );
        }
        log( "done with lookupTest" );
    }

    private void printTrade( Trade trade, int i )
    {
        log( "trade #" + i );
        log( "\t\t objectID = " + trade.getObjectID() );
        log( "\t\t\t contained in " + trade.getTradeFolders() );
    }

    private void printTradeFolder( TradeFolder folder, int i )
    {
        log( "trade folder #" + i );
        log( "\t objectID = " + folder.getObjectID() );
        log( "\t shortname = " + folder.getShortName() );

        if ( folder.getParent() != null )
        {
            log( "\t parent object ID = " + folder.getParent().getObjectID() );
        }

        if ( folder.getChildren() != null )
        {
            Iterator iter = folder.getChildren().iterator();
            while ( iter.hasNext() )
            {
                TradeFolder child = ( TradeFolder ) iter.next();
                log( "\t childs object ID = " + child.getObjectID() );
            }
        }
    }

    public void testQuery()
    {
        log( "starting queryTest" );
        try
        {
            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );
            IdcSessionManager.getInstance().setSessionContext( ctxt );

            QueryService qs = QueryFactory.getQueryService();
            Collection objs = qs.findAll( TradeFolder.class );
            Iterator iterator = objs.iterator();
            int i = 0;
            while ( iterator.hasNext() )
            {
                TradeFolder folder = ( TradeFolder ) iterator.next();
                printTradeFolder( folder, i );

                TradesQueryC query = ( TradesQueryC ) QueryFactory.newNamedQuery( TradesQueryC.QUERY_NAME );
                assertNotNull( query );
                query.setTradeFolder( folder );
                Collection result = qs.findAll( query );
                Iterator iter = result.iterator();

                while ( iter.hasNext() )
                {
                    Trade trade = ( Trade ) iter.next();
                    printTrade( trade, i );
                }
                log( "------------------------------------" );
                iter = folder.getTrades( null, null );
                while ( iter.hasNext() )
                {
                    Trade trade = ( Trade ) iter.next();
                    printTrade( trade, i );
                }

                i++;
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "queryTest" );
        }
        log( "done with queryTest" );
    }

    // binding parameters does not work with in clause. Hence removing the test case from the test suite.
    public void queryAll()
    {
        log( "starting queryAllTest" );
        try
        {
            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );
            IdcSessionManager.getInstance().setSessionContext( ctxt );

            QueryService qs = QueryFactory.getQueryService();
            Collection objs = qs.findAll( TradeFolder.class );
            Iterator iterator = objs.iterator();
            int i = 0;
            while ( iterator.hasNext() )
            {
                TradeFolder folder = ( TradeFolder ) iterator.next();
                printTradeFolder( folder, i );

                AllTradesQueryC query = ( AllTradesQueryC ) QueryFactory.newNamedQuery( AllTradesQueryC.QUERY_NAME );
                assertNotNull( query );
                query.setTradeFolders( folder.getChildren() );
                if ( folder.getChildren().size() != 0 )
                {
                    Collection result = qs.findAll( query );
                    Iterator iter = result.iterator();

                    while ( iter.hasNext() )
                    {
                        Trade trade = ( Trade ) iter.next();
                        printTrade( trade, i );
                    }
                }
                log( "------------------------------------" );
                Iterator iter = folder.getAllTrades( null, null );
                while ( iter.hasNext() )
                {
                    Trade trade = ( Trade ) iter.next();
                    printTrade( trade, i );
                }
                i++;
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "queryAllTest" );
        }
        log( "done with queryAllTest" );
    }

    /**
     * Tests retrieving a few trade folders based on object IDs using a straight expression
     */
    public void testInQueryUsingExpression()
    {
        log( "starting testQueryAllUsingExpression" );
        try
        {
            long[] oids = {1, 2, 3};

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "objectID" ).in( oids );

            Vector folders = getPersistenceSession().readAllObjects( TradeFolder.class, expr );
            Iterator iterator = folders.iterator();
            int i = 0;
            while ( iterator.hasNext() )
            {
                TradeFolder folder = ( TradeFolder ) iterator.next();
                printTradeFolder( folder, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "queryAllTest" );
        }
        log( "done with queryAllTest" );
    }

    /**
     * Tests retrieving a few trade folders based on object IDs using a parmeterized query expression
     */
    public void testInQueryUsingParameterizedQuery()
    {
        log( "starting testInQueryUsingParameterizedQuery" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "objectID" ).in( eb.getParameter( "OIDS" ) );

            ReadAllQuery query = new ReadAllQuery();
            query.setReferenceClass( TradeFolder.class );
            query.setSelectionCriteria( expr );
            query.addArgument( "OIDS" );
            query.dontBindAllParameters();


            Vector oids = new Vector();
            oids.addElement( ( long ) 1 );
            oids.addElement( ( long ) 2 );
            oids.addElement( ( long ) 3 );

            Vector args = new Vector();
            args.add( oids );

            Vector folders = ( Vector ) getPersistenceSession().executeQuery( query, args );
            Iterator iterator = folders.iterator();
            int i = 0;
            while ( iterator.hasNext() )
            {
                TradeFolder folder = ( TradeFolder ) iterator.next();
                printTradeFolder( folder, i );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "queryAllTest" );
        }
        log( "done with queryAllTest" );
    }

    public void testPagedQuery()
    {
        log( "starting pagedQueryTest" );
        try
        {
            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );
            IdcSessionManager.getInstance().setSessionContext( ctxt );


            Vector objs = getPersistenceSession().readAllObjects( TradeFolder.class );
            QueryCriteria criteria = QueryFactory.newQueryCriteriaBuilder().get( "status" ).equal( "A" );
            for ( int i = 0; i < objs.size(); i++ )
            {
                TradeFolder folder = ( TradeFolder ) objs.elementAt( i );
                printTradeFolder( folder, i );

                QueryResult result = folder.getTrades( criteria, null, 1, 2, true );
                if ( result != null )
                {
                    log( " total results = " + result.getTotalCount() );
                    Iterator iter = result.getResultCollection().iterator();
                    while ( iter.hasNext() )
                    {
                        Trade trade = ( Trade ) iter.next();
                        printTrade( trade, i );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "pagedQueryTest" );
        }
        log( "done with pagedQueryTest" );
    }

    public void testPagesQueryAll()
    {
        log( "starting pagedQueryAllTest" );

        try
        {
            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext( "fi1mm1@FI1" );
            IdcSessionManager.getInstance().setSessionContext( ctxt );

            Vector objs = getPersistenceSession().readAllObjects( TradeFolder.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                TradeFolder folder = ( TradeFolder ) objs.elementAt( i );
                printTradeFolder( folder, i );

                QueryResult result = folder.getAllTrades( null, null, 1, 2, true );
                if ( result != null )
                {
                    log( " total results = " + result.getTotalCount() );
                    Iterator iter = result.getResultCollection().iterator();
                    while ( iter.hasNext() )
                    {
                        Trade trade = ( Trade ) iter.next();
                        printTrade( trade, i );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "pagedQueryAllTest" );
        }
        log( "done with pagedQueryAllTest" );
    }
}
