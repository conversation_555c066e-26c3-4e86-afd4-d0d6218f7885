package com.integral.finance.trade.test;

import com.integral.facade.FacadeFactory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitRuleService;
import com.integral.finance.creditLimit.CreditLimitRuleServiceFactory;
import com.integral.finance.creditLimit.CreditWorkflowRidersDefault;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.calculator.TradeDownloadFIXMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadKondorMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.functor.TradeRemoteNotificationFunctorServerC;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.Entity;
import com.integral.persistence.test.TestRemoteNotificationFunctor;
import com.integral.session.IdcTransaction;
import com.integral.user.Organization;
import org.eclipse.persistence.sessions.UnitOfWork;

public class TradeServiceCancelTradeEnhancementTestC extends TradeServiceTestC
{
    CreditLimitRuleService creditLimitSvc = CreditLimitRuleServiceFactory.getCreditLimitRuleService();

    public TradeServiceCancelTradeEnhancementTestC( String name ) throws Exception
    {
        super( name );
        TradeRemoteNotificationFunctorServerC.getInstance().addTradeRemoteNotificationFunctor( TradeService.AMEND_EVENT, TestRemoteNotificationFunctor.class);
    }

    public void testCancelTradeFinXMLTradeDownload() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );
        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setObject( trd );
        wm.setProperty( "organization", trd.getCounterpartyA().getOrganization() );
        trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm, null );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );
        assertEquals( xml.indexOf( "<state>TSCANCELLED</state>" ) >= 0, true );
        tx.commit();
    }

    public void testCancelTradeFIXTradeDownload() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );
        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setObject( trd );
        wm.setEventName( TradeService.CANCEL_EVENT );
        wm.setProperty( "organization", trd.getCounterpartyA().getOrganization() );
        trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xml = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wm, null );
        char stae = xml.charAt( xml.indexOf( "487=" ) + 4 );
        assertEquals( stae, 'C' );
        tx.commit();
    }

    public void testCancelTradeKondorTradeDownload() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        TradeDownloadKondorMessageBuilderC builder = new TradeDownloadKondorMessageBuilderC();
        WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
        trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
        wfm.setProperty( "organization", trd.getCounterpartyB().getOrganization() );
        wfm.setObject( trd );

        String downloadMessage = builder.buildDownloadMessage( wfm, null );
        System.out.println( downloadMessage );
        int index1 = downloadMessage.indexOf( TradeDownloadKondorMessageBuilderC.ACTION ) + TradeDownloadKondorMessageBuilderC.ACTION.length();
        String subStr = downloadMessage.substring( index1, downloadMessage.indexOf( '\n', index1 ) ).trim();//.replaceAll( "\"", "");
        assertEquals( subStr, TradeDownloadKondorMessageBuilderC.ACTION_DEL_DEFAULT_V );
        tx.commit();
    }


    public void testCancelTradeSuccess() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        assertEquals( trd.getStatus(), Entity.ACTIVE_STATUS );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isCancelled(), true );
        assertNotNull( trd.getEtlTimestamp() );
        tx.commit();
    }

    public void testCancelTradeValidateState() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.rejectTrade( trd, 1, "TEST", null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.FAILURE );
        assertNotNull( msg.getError( "INVALID_TRADE_STATE" ) );
        tx.release();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        assertEquals( trd.getStatus(), Entity.ACTIVE_STATUS );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isRejected(), true );
        tx.commit();

    }

    public void testCancelTradeValidateSettledTradeSingleLegFail() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.subtractDays( 5 ) );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.FAILURE );
        assertNotNull( msg.getError( "SETTLED_TRADE_NOT_CANCELLED" ) );
        tx.release();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        assertEquals( trd.getStatus(), Entity.ACTIVE_STATUS );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isVerified(), true );
        tx.commit();

    }

    public void testCancelTradeValidateSettledTradeSingleLegPass() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, makerOrg, makerTpForTaker, bidRates[0], spotDate.subtractDays( 5 ) );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );
        ( ( Organization ) CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ).getOrganization().getRegisteredObject() ).setAllowCancelOfSettledTrades( true );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        ( ( Organization ) CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ).getOrganization().getRegisteredObject() ).setAllowCancelOfSettledTrades( false );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        assertEquals( trd.getStatus(), Entity.ACTIVE_STATUS );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isCancelled(), true );
        tx.commit();
    }


    // a plain test case for swap cancellation
    public void testCancelTradeValidateSettledTradeSwapSuccess() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays( 2 ), spotDate.addDays( 5 ) );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSwapRequest( ( FXSwap ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        assertEquals( trd.getStatus(), Entity.ACTIVE_STATUS );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isCancelled(), true );
        tx.commit();

    }

    public void testCancelTradeValidateSettledTradeSwapFail() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.subtractDays( 6 ), spotDate.subtractDays( 4 ) );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSwapRequest( ( FXSwap ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        Organization makerOrg = CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ).getOrganization();
        makerOrg.setAllowCancelOfSettledTrades( false );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.FAILURE );
        assertNotNull( msg.getError( "SETTLED_TRADE_NOT_CANCELLED" ) );
        tx.release();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        assertEquals( trd.getStatus(), Entity.ACTIVE_STATUS );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isVerified(), true );
        assertNull( trd.getEtlTimestamp() );
        tx.commit();

    }


    public void testCancelTradeValidateSettledTradeSwapSettingFieldPass() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSwapTrade( 1000, 2000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.subtractDays( 6 ), spotDate.addDays( 2 ) );
        creditLimitSvc.takeBilateralCredit( ( LegalEntity ) trd.getCounterpartyA(), ( ( TradingParty ) trd.getCounterpartyB() ).getLegalEntity(), trd, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        prepareSwapRequest( ( FXSwap ) trd );
        TradeCloneServiceC.createCptyTrades( trd );
        ( ( Organization ) CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ).getOrganization().getRegisteredObject() ).setAllowCancelOfSettledTrades( true );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage msg = ts.cancelTrade( trd, null );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        ( ( Organization ) CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ).getOrganization().getRegisteredObject() ).setAllowCancelOfSettledTrades( false );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        assertEquals( trd.getStatus(), Entity.ACTIVE_STATUS );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isCancelled(), true );
        tx.commit();
    }
}
