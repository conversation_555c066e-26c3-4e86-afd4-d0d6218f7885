package com.integral.finance.trade.test;

import com.integral.finance.trade.configuration.TradeConfiguration;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.DealingPTestCaseC;

public class TradeConfigurationTestC extends DealingPTestCaseC
{
    TradeConfiguration mbean = ( TradeConfiguration ) TradeConfigurationFactory.getTradeConfigurationMBean();

    public TradeConfigurationTestC( String name )
    {
        super( name );
    }

    public static final String testingOrgName = "JUnitOrg";

    {
        mbean.setProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow.JUnitOrg", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        mbean.setProperty( "IDC.LP.JUnitOrg.Masked.Name", "JUnitOrgAA,JUnitOrgBB", ConfigurationProperty.DYNAMIC_SCOPE, null );
    }

    public void testReverseLookUpMaskOnRemovalProperty()
    {
        /*assertEquals( mbean.getOrigNameForMaskedLPOrg( "JUnitRemovalAA" ), null );
        mbean.setProperty( "IDC.LP.JUnitRemoval.Masked.Name", "JUnitRemovalAA", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( mbean.getOrigNameForMaskedLPOrg( "JUnitRemovalAA" ), "JUnitRemoval" );
        mbean.removeProperty( "IDC.LP.JUnitRemoval.Masked.Name", ConfigurationProperty.DYNAMIC_SCOPE );
        assertEquals( mbean.getOrigNameForMaskedLPOrg( "JUnitRemovalAA" ), null );*/
    }

    public void testReverseLPMaskedName()
    {
        /*assertEquals( mbean.getOrigNameForMaskedLPOrg( testingOrgName + "AA" ), testingOrgName );
        assertEquals( mbean.getOrigNameForMaskedLPOrg( testingOrgName + "BB" ), testingOrgName );
        assertEquals( mbean.getOrigNameForMaskedLPOrg( testingOrgName + "AA" + "PP" ), null );

        // to test if someone modifies the property from config webapp
        mbean.setProperty( "IDC.LP." + testingOrgName + ".Masked.Name", testingOrgName + "MODIFIED", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( mbean.getOrigNameForMaskedLPOrg( testingOrgName + "MODIFIED" ), testingOrgName );

        assertEquals( mbean.getOrigNameForMaskedLPOrg( testingOrgName + "New" ), null );
        mbean.setProperty( "IDC.LP." + testingOrgName + "New.Masked.Name", testingOrgName + "NewAA", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( mbean.getOrigNameForMaskedLPOrg( testingOrgName + "NewAA" ), testingOrgName + "New" );*/
    }

    public void testPBCoverTradeEnabled()
    {
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( testingOrgName ), true );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( testingOrgName + "PP" ), false );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( "JUNIT2" ), false );
//        mbean.setProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow.JUNIT2", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( "JUNIT2" ), true ); //commented because its failing as of now because in IdcMbeanC. while settingProperty it do getProperties().set(); while retrieveing it says getProperties().getProperty()
//
//
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( lpOrg, makerOrg, takerOrg ), false );
//
//        mbean.setProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow." + lpOrg.getShortName() + '.' + makerOrg.getShortName() + '.' + takerOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( lpOrg, makerOrg, takerOrg ), true );
//        mbean.removeProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow." + lpOrg.getShortName() + '.' + makerOrg.getShortName() + '.' + takerOrg.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( lpOrg, makerOrg, takerOrg ), false );
//
//        mbean.setProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow." + lpOrg.getShortName() + '.' + makerOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( lpOrg, makerOrg, takerOrg ), true );
//        mbean.removeProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow." + lpOrg.getShortName() + '.' + makerOrg.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( lpOrg, makerOrg, takerOrg ), false );
//
//        mbean.setProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow." + lpOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( lpOrg, makerOrg, takerOrg ), true );
//        mbean.removeProperty( "Idc.PrimeBroker.EnableCoverTradeWorkflow." + lpOrg.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE );
//        assertEquals( mbean.isPrimeBrokerCoverTradeEnabled( lpOrg, makerOrg, takerOrg ), false );
    }
}
