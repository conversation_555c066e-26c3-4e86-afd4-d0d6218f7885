package com.integral.finance.trade.test;

import com.integral.facade.FacadeFactory;
import com.integral.finance.financialEvent.FinancialEvent;
import com.integral.finance.financialEventBuilder.FinancialEventBuilder;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeLeg;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.TradeServiceC;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.facade.TradeStateFacadeC;
import com.integral.message.query.NamedCriteria;
import com.integral.message.query.QueryCriteria;
import com.integral.message.query.QueryMessage;
import com.integral.message.query.QueryMessageFactory;
import com.integral.message.query.QueryParameter;
import com.integral.message.query.QueryResult;
import com.integral.message.query.ResultItem;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.ExternalSystemC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DatePeriod;
import com.integral.time.DatePeriodC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.User;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

// Copyright (c) 1999-2006 Integral Development Corp.  All rights reserved.

/**
 * TradeServiceDoneTradesTestC
 *
 * <AUTHOR> Development Corp.
 */

public class TradeServiceDoneTradesTestC extends TradeServiceTestC
{

    private ExternalSystem providerExtSys = ( ExternalSystem ) new ReadNamedEntityC().execute( ExternalSystem.class, "RequestId" );
    private ExternalSystem customerExtSys = ( ExternalSystem ) new ReadNamedEntityC().execute( ExternalSystem.class, "CPTYRequestId" );

    public TradeServiceDoneTradesTestC( String name )
    {
        super( name );
        FacadeFactory.setFacade( TradeStateFacade.TRADE_STATE_FACADE, Trade.class, TradeStateFacadeC.class );
        if ( providerExtSys == null )
        {
            providerExtSys = createNewExternalSystem( "RequestId" );
        }

        if ( customerExtSys == null )
        {
            customerExtSys = createNewExternalSystem( "CPTYRequestId" );
        }
    }

    protected String createTrade( Object... args )
    {

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        Trade trade = new FXSingleLegC();

        trade = ( Trade ) uow.registerNewObject( trade );
        trade.buildFinancialEvents();

        DatePeriod dp = DateTimeFactory.newDatePeriod ( 0, 0, 3 );

        trade.setSettlementDateTerm( dp );
        trade.setTransactionID( "TX" + System.currentTimeMillis() );
        if ( args.length > 1 && args[1] != null )
        {
            IdcDate tradeDate = DateTimeFactory.newDate( ( Date ) args[1] );
            trade.setTradeDate( tradeDate );
        }
        if ( args.length > 2 && args[2] != null )
        {
            String providerId = ( String ) args[2];
            providerExtSys = ( ExternalSystem ) uow.registerObject( providerExtSys );
            trade.addExternalSystemId( providerExtSys, providerId );
        }
        if ( args.length > 3 && args[3] != null )
        {
            String customerId = ( String ) args[3];
            customerExtSys = ( ExternalSystem ) uow.registerObject( customerExtSys );
            trade.addExternalSystemId( customerExtSys, customerId );
        }
        trade.setCounterpartyA( takerLe );
        trade.setEntryUser( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( TradeStateFacade.TRADE_STATE_FACADE, trade );
        tradeFacade.setVerified();

        uow.commit();

        printTrade( trade, ( Integer ) args[0] );

        return trade.getTransactionID();
    }

    protected void printResult( QueryResult result, int i )
    {
        log( "result #" + i );
        log( "\t count = " + result.getCount() );

        for ( Object o : result.getResultItems() )
        {
            ResultItem resultItem = ( ResultItem ) o;

            Trade trade = ( Trade ) resultItem.getResult();
            printTrade( trade, resultItem.getNum() );
        }
    }

    protected String getTxIDString( QueryResult result )
    {

        String txIDs = "";

        for ( Object o : result.getResultItems() )
        {
            ResultItem resultItem = ( ResultItem ) o;

            Trade trade = ( Trade ) resultItem.getResult();
            if ( !txIDs.equals( "" ) )
            {
                txIDs = txIDs + ',';
            }
            txIDs = txIDs + trade.getTransactionID();
        }
        return txIDs;
    }

    protected void printTrade( Trade trade, int i )
    {
        log( "trade #" + i );
        log( "\t objectID = " + trade.getObjectID() );

        log( "\t txID = " + trade.getTransactionID() );

        // get financial event builder
        FinancialEventBuilder builder = trade.getFinancialEventBuilder();
        log( "\t trade builder " + builder );

        // get settlement date
        DatePeriod dp = trade.getSettlementDateTerm();
        log( "\t settlement date term " + dp );

        //get value date
        IdcDate tradeDate = trade.getTradeDate();
        log( "\t trade date " + tradeDate );

        Iterator events = trade.getFinancialEvents().iterator();
        int e = 0;
        while ( events.hasNext() )
        {
            FinancialEvent event = ( FinancialEvent ) events.next();
            log( "\t trade event #" + e + ": " + event );
            e++;
        }

        Iterator legs = trade.getTradeLegs().iterator();
        int l = 0;
        while ( legs.hasNext() )
        {
            TradeLeg leg = ( TradeLeg ) legs.next();
            log( "\t trade leg #" + l + ": " + leg );

            Iterator leg_events = leg.getFinancialEvents().iterator();
            int le = 0;
            while ( leg_events.hasNext() )
            {
                FinancialEvent leg_event = ( FinancialEvent ) leg_events.next();
                log( "\t\t trade leg event #" + le + ": " + leg_event );
                le++;
            }
            l++;
        }
    }

    public void testDoneTradesInvalidQueryName()
    {
        log( "Test# 1 --- Test Done Trades with invalid query name" );

        Collection<QueryParameter> params = new ArrayList<QueryParameter>();
        QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
        queryParam.setKey( TradeService.DONE_TRADES_TRANSACTIONID_PARAM );
        params.add( queryParam );

        QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
        QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
        NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
        namedCriteria.setQueryName( "INVALID_QUERY_NAME" );
        namedCriteria.setQueryParameters( params );
        queryCriteria.setClassName( Trade.class.toString() );
        queryCriteria.setNamedCriteria( namedCriteria );
        queryMessage.setQueryCriteria( queryCriteria );

        TradeServiceC service = new TradeServiceC();
        QueryMessage resultMessage = service.getDoneTrades( queryMessage );
        QueryResult result = resultMessage.getQueryResult();

        assertEquals( "Valid error count:", false, resultMessage.getErrors().isEmpty() );
        assertEquals( "Valid result count:", null, result );
    }

    public void testGetDoneTradesUnlimited()
    {
        int maxCount = TradeConfigurationFactory.getTradeConfigurationMBean().getMaxNumDoneTrades();
        try
        {
            tradeConfig.setProperty( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT_PREFIX + takerOrg.getShortName(), "0", ConfigurationProperty.DYNAMIC_SCOPE, null );
            log( "Test# 0 --- Test Done Trades with creating trade in FI namespace and then querying using LP namespace" );
            IdcSessionContext fiSessContext = IdcSessionManager.getInstance().getSessionContext( takerUser );
            IdcSessionManager.getInstance().setSessionContext( fiSessContext );

            int count = 200;
            for ( int i = 0; i < count; i++ )
            {
                String cid = "CID" + System.nanoTime();
                String txID = createTrade( 1, new Date(), null, cid );
            }
            Collection<QueryParameter> params = new ArrayList<QueryParameter>();
            QueryParameter queryParam = QueryMessageFactory.newQueryParameter();
            queryParam.setKey( TradeService.DONE_TRADES_FROMDATE_PARAM );
            queryParam.setValue( DateTimeFactory.newDate().previousDate().getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN ) );
            params.add( queryParam );
            queryParam = QueryMessageFactory.newQueryParameter();
            queryParam.setKey( TradeService.DONE_TRADES_TODATE_PARAM );
            queryParam.setValue( DateTimeFactory.newDate().nextDate().getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN ) );
            params.add( queryParam );

            QueryMessage queryMessage = QueryMessageFactory.newQueryMessage();
            QueryCriteria queryCriteria = QueryMessageFactory.newQueryCriteria();
            NamedCriteria namedCriteria = QueryMessageFactory.newNamedCriteria();
            namedCriteria.setQueryName( TradeService.DONE_TRADES_DATE );
            namedCriteria.setQueryParameters( params );
            queryCriteria.setClassName( Trade.class.toString() );
            queryCriteria.setNamedCriteria( namedCriteria );
            queryMessage.setQueryCriteria( queryCriteria );

            queryMessage = ts.getDoneTrades( queryMessage );
            QueryResult result = queryMessage.getQueryResult();
            Collection coll = result.getResultItems();
            log( "coll.size=" + ( coll != null ? coll.size() : -1 ) );
            assertTrue( coll.size() >= count );

            tradeConfig.setProperty( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT_PREFIX + takerOrg.getShortName(), "100", ConfigurationProperty.DYNAMIC_SCOPE, null );
            QueryMessage queryMessage1 = QueryMessageFactory.newQueryMessage();
            QueryCriteria queryCriteria1 = QueryMessageFactory.newQueryCriteria();
            NamedCriteria namedCriteria1 = QueryMessageFactory.newNamedCriteria();
            namedCriteria1.setQueryName( TradeService.DONE_TRADES_DATE );
            namedCriteria1.setQueryParameters( params );
            queryCriteria1.setClassName( Trade.class.toString() );
            queryCriteria1.setNamedCriteria( namedCriteria1 );
            queryMessage1.setQueryCriteria( queryCriteria1 );

            queryMessage1 = ts.getDoneTrades( queryMessage1 );
            QueryResult result1 = queryMessage1.getQueryResult();
            Collection coll1 = result1.getResultItems();
            log( "coll1.size=" + ( coll1 != null ? coll1.size() : -1 ) );
            assertTrue( coll1.size() == 0 );

            // remove the org level property and make the global property value to zero.
            tradeConfig.setProperty( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT_PREFIX + takerOrg.getShortName(), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeConfig.setProperty( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, "0", ConfigurationProperty.DYNAMIC_SCOPE, null );
            QueryMessage queryMessage2 = QueryMessageFactory.newQueryMessage();
            QueryCriteria queryCriteria2 = QueryMessageFactory.newQueryCriteria();
            NamedCriteria namedCriteria2 = QueryMessageFactory.newNamedCriteria();
            namedCriteria2.setQueryName( TradeService.DONE_TRADES_DATE );
            namedCriteria2.setQueryParameters( params );
            queryCriteria2.setClassName( Trade.class.toString() );
            queryCriteria2.setNamedCriteria( namedCriteria2 );
            queryMessage2.setQueryCriteria( queryCriteria2 );

            queryMessage2 = ts.getDoneTrades( queryMessage2 );
            QueryResult result2 = queryMessage2.getQueryResult();
            Collection coll2 = result2.getResultItems();
            log( "coll2.size=" + ( coll2 != null ? coll2.size() : -1 ) );
            assertTrue( coll2.size() >= count );
        }
        finally
        {
            tradeConfig.setProperty( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT_PREFIX + takerOrg.getShortName(), null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeConfig.setProperty( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, String.valueOf( maxCount ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }


    private ExternalSystem createNewExternalSystem( String name )
    {
        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        ExternalSystem extSys = new ExternalSystemC( name );
        extSys = ( ExternalSystem ) uow.registerNewObject( extSys );

        uow.commit();

        return extSys;
    }
}
