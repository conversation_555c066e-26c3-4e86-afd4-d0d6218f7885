package com.integral.finance.trade.test;

import com.integral.facade.FacadeFactory;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.calculator.TradeDownloadFIXMessageBuilderC;
import com.integral.finance.trade.calculator.TradeDownloadMessageBuilderC;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.functor.TradeRemoteNotificationFunctorServerC;
import com.integral.message.EntityReference;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.test.TestRemoteNotificationFunctor;
import com.integral.session.IdcTransaction;
import com.integral.user.Organization;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;


public class TradeServiceAmendTradeEnhancementTestC extends TradeServiceTestC
{
    public TradeServiceAmendTradeEnhancementTestC( String name ) throws Exception
    {
        super( name );
        TradeRemoteNotificationFunctorServerC.getInstance().addTradeRemoteNotificationFunctor( TradeService.AMEND_EVENT, TestRemoteNotificationFunctor.class);
        init( takerUser );
    }


    public void testFixSTPMessage() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        init( takerUser );
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        LegalEntity le = getLegalEntityWithRelationship( ( LegalEntity ) trd.getCounterpartyA(), ( LegalEntity ) trd.getCounterpartyB() );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        trd = ( Trade ) trd.getRegisteredObject();
        le = ( LegalEntity ) uow.refreshObject( le );
        trd.setCounterpartyA( le );
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setObject( trd );
        wm.setEventName( TradeService.AMEND_EVENT );
        wm.setTopic( TradeService.MESSAGE_TOPIC );
        wm.setParameterValue( TradeService.ORIG_CPTY, trd.getCounterpartyA() );
        wm.setParameterValue( TradeService.CPTY_A_AMENDED, true );
        WorkflowMessage msg = ts.process( wm );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        WorkflowMessage wm2 = MessageFactory.newWorkflowMessage();
        wm2.setObject( trd );
        wm2.setProperty( "organization", trd.getCounterpartyA().getOrganization() );
        trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        wm2.setEventName( TradeService.AMEND_EVENT );
        String xml = new TradeDownloadFIXMessageBuilderC().buildDownloadMessage( wm2, null );
        assertEquals( xml.indexOf( "150=5" ) >= 0, true );
        assertEquals( xml.indexOf( "570=Y" ) >= 0, true );
        assertEquals( xml.indexOf( "50=" + le.getShortName() ) >= 0, true );
        System.out.println( xml );
        tx.release();
    }

    public void setValidRelationship( LegalEntity cptyA, LegalEntity cptyB )
    {
        cptyA = ( LegalEntity ) cptyA.getRegisteredObject();
        cptyB = ( LegalEntity ) cptyB.getRegisteredObject();
        cptyA.setStatus( Entity.ACTIVE_STATUS );
        cptyB.setStatus( Entity.ACTIVE_STATUS );
        setOneSideValidRelationship( cptyA, cptyB );
        setOneSideValidRelationship( cptyB, cptyA );
    }

    private void setOneSideValidRelationship( LegalEntity le1, LegalEntity le2 )
    {
        TradingParty tp = le1.getTradingParty( le2.getOrganization() );
        if ( tp == null )
        {
            log( "setOneSideValidRelationship. No tp in le2Org=" + le2.getOrganization() + " for le1=" + le1 );
            return;
        }
        tp = ( TradingParty ) tp.getRegisteredObject();
        LegalEntity defaultLE = ( LegalEntity ) tp.getCustomFieldValue( "DirectFX_TradingPartyDefaultDealingEntity" );
        if ( defaultLE == null )
        {
            Iterator les = le2.getOrganization().getLegalEntities().iterator();
            defaultLE = ( LegalEntity ) les.next();
            if ( defaultLE.getObjectID() == le2.getObjectID() )
            {
                defaultLE = ( LegalEntity ) les.next();
            }
            tp.getRegisteredObject().putCustomField( "DirectFX_TradingPartyDefaultDealingEntity", defaultLE );
        }

        TradingParty tpLe1 = ( TradingParty ) defaultLE.getTradingParty( le1.getOrganization() ).getRegisteredObject();
        tpLe1.setStatus( Entity.ACTIVE_STATUS );
        List relatedLEs = ( List ) tpLe1.getCustomFieldValue( "DirectFX_RelatedLegalEntity" );
        if ( relatedLEs == null )
        {
            relatedLEs = new ArrayList();
            relatedLEs.add( le1.getShortName() );
            tpLe1.putCustomField( "DirectFX_RelatedLegalEntity", relatedLEs );
            tpLe1.getCustomField( "DirectFX_RelatedLegalEntity" ).setValue( relatedLEs );
        }
        relatedLEs.add( le1.getShortName() );

    }


    public void testFinXMLDownloadTakerModifying() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        init( takerUser );
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, fiOrg, fiTpForLp, bidRates[0], spotDate );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );
        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        Iterator les = trd.getCounterpartyA().getOrganization().getLegalEntities().iterator();
        LegalEntity le = ( LegalEntity ) les.next();
        if ( le.getObjectID() == trd.getCounterpartyA().getObjectID() )
        {
            le = ( LegalEntity ) les.next();
        }

        if ( !CounterpartyUtilC.isValidRelationShip( le, CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ) ) )
        {
            setValidRelationship( le, CounterpartyUtilC.getLegalEntity( trd.getCounterpartyB() ) );
        }

        tx.commit();
        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        trd = ( Trade ) trd.getRegisteredObject();
        le = ( LegalEntity ) uow.refreshObject( le );
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        Counterparty oldCpty = trd.getCounterpartyA();
        wm.setObject( trd );
        wm.setEventName( TradeService.AMEND_EVENT );
        wm.setTopic( TradeService.MESSAGE_TOPIC );
        wm.setParameterValue( TradeService.ORIG_CPTY, trd.getCounterpartyA() );
        wm.setParameterValue( TradeService.CPTY_A_AMENDED, true );
        trd.setCounterpartyA( le );
        /*  ( ( Entity ) trd.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadMode", "Automatic" );
( ( Entity ) trd.getCounterpartyA().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FinXML51v20_Complex_New" );
( ( Entity ) trd.getCounterpartyB().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadMode", "Automatic" );
( ( Entity ) trd.getCounterpartyB().getOrganization().getCustomFieldValue( "DirectFX_DownloadExtSys" ) ).putCustomField( "DirectFX_TradeDownloadFormat", "FinXML51v20_Complex_New" );*/
        WorkflowMessage msg = ts.process( wm );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        TradingParty tp = CounterpartyUtilC.getTradingParty( trd.getCounterpartyA(), trd.getCounterpartyB().getOrganization() );
        assertEquals( trd.getCptyTrade( 'A' ).getLegalEntity().getObjectID(), le.getObjectID() );
        assertEquals( trd.getCptyTrade( 'C' ).getLegalEntity().getObjectID(), le.getObjectID() );
        assertEquals( trd.getCptyTrade( 'B' ).getTradingParty(), tp );
        assertEquals( trd.getCptyTrade( 'D' ).getTradingParty(), tp );
        assertEquals( trd.getCounterpartyA().getObjectID(), le.getObjectID() );
        assertEquals( trd.getTakerCounterparty().getObjectID(), trd.getCounterpartyA().getObjectID() );
        assertEquals( trd.getMakerCounterparty().getObjectID(), CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), trd.getCounterpartyA().getOrganization() ).getObjectID() );
        WorkflowMessage wm2 = MessageFactory.newWorkflowMessage();
        wm2.setObject( trd );
        wm2.setProperty( "organization", trd.getCounterpartyA().getOrganization() );
        trd.setCptyTrade( trd.getCptyTrade( 'A' ) );
        String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm2, null );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );
        //these wont work because then it should be invoked via TradeService where all these parameters are set on TradeService.And if we invoke via tradeService; we cannot get this xml
        /*int leIndex = xml.indexOf( TradeService.CPTYTRADE_LE);
        String leStr = xml.substring( xml.indexOf( ">" , leIndex) , xml.indexOf( "<" , leIndex) );
        int tpIndex = xml.indexOf( TradeService.CPTYTRADE_TP);
        int cptyAIndex = xml.indexOf( TradeService.CPTY_A);
        String cptyAStr = xml.substring( xml.indexOf( ">" , cptyAIndex) , xml.indexOf( "<" , cptyAIndex) );
        int cptyBIndex = xml.indexOf( TradeService.CPTY_B);
        assertEquals( leIndex > 0, true );
        assertEquals( tpIndex == -1 , true);
        assertEquals( cptyAIndex > 0, true );
        assertEquals( cptyBIndex == -1 , true);
        assertEquals( leStr, le.getShortName());
        assertEquals( cptyAStr, oldCpty.getShortName());
        wm2.setProperty( "organization", trd.getCounterpartyB().getOrganization() );
        trd.setCptyTrade( trd.getCptyTrade( 'B'));
        xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm2, null );
        xml = applyStleSheetOnXML( xml, "FinXML51v20" );
*/

        tx.release();
    }

    private void setOnOrg( Organization org, String key, String value )
    {

        Entity extSys = ( Entity ) org.getCustomFieldValue( "DirectFX_DownloadExtSys" );
        if ( extSys == null )
        {
            EntityReference ef = MessageFactory.newEntityReference();
            ef.setClassName( ExternalSystem.class.getName() );
            ef.setShortName( "DOWNLOAD" );
            ef.setNamespace( org.getNamespace() );
            extSys = ef.getEntity();
        }
        org.putCustomField( "DirectFX_DownloadExtSys", extSys );
        extSys.putCustomField( key, value );
    }

    public void testFinXMLDownloadMakerModifying() throws Exception
    {
        IdcTransaction tx = null;
        try
        {
            Trade trd;
            UnitOfWork uow;
            init( takerUser );
            tx = initTransaction();
            uow = tx.getUOW();

            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            trd = prepareSingleLegTrade( 1000, true, false, EURUSD, fiOrg, fiTpForLp, bidRates[0], spotDate );
            prepareSingleLegRequest( ( FXSingleLeg ) trd );
            TradeCloneServiceC.createCptyTrades( trd );
            ts.newTrade( trd, null );
            ts.verifyTrade( trd, null );
            Iterator les = trd.getCounterpartyB().getOrganization().getLegalEntities().iterator();
            LegalEntity le = ( LegalEntity ) les.next();
            if ( le.getObjectID() == trd.getCounterpartyB().getObjectID() )
            {
                le = ( LegalEntity ) les.next();
            }

            if ( !CounterpartyUtilC.isValidRelationShip( le, CounterpartyUtilC.getLegalEntity( trd.getCounterpartyA() ) ) )
            {
                setValidRelationship( le, CounterpartyUtilC.getLegalEntity( trd.getCounterpartyA() ) );
            }

            tx.commit();
            tx = initTransaction();
            uow = tx.getUOW();
            uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
            trd = ( Trade ) uow.refreshObject( trd );
            trd = ( Trade ) trd.getRegisteredObject();
            le = ( LegalEntity ) uow.refreshObject( le );
            WorkflowMessage wm = MessageFactory.newWorkflowMessage();
            Counterparty oldCpty = trd.getCounterpartyB();
            wm.setObject( trd );
            wm.setEventName( TradeService.AMEND_EVENT );
            wm.setTopic( TradeService.MESSAGE_TOPIC );
            wm.setParameterValue( TradeService.ORIG_CPTY, trd.getCounterpartyB() );
            wm.setParameterValue( TradeService.CPTY_A_AMENDED, false );
            trd.setCounterpartyB( le );
            /*    setOnOrg( trd.getCounterpartyA().getOrganization(), "DirectFX_TradeDownloadMode", "Automatic" );
                        setOnOrg( trd.getCounterpartyA().getOrganization(), "DirectFX_TradeDownloadFormat", "FinXML51v20_Complex_New" );
                        setOnOrg( trd.getCounterpartyB().getOrganization(), "DirectFX_TradeDownloadMode", "Automatic" );
                        setOnOrg( trd.getCounterpartyB().getOrganization(), "DirectFX_TradeDownloadFormat", "FinXML51v20_Complex_New" );
            */
            WorkflowMessage msg = ts.process( wm );
            assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
            tx.commit();

            tx = initTransaction();
            uow = tx.getUOW();
            trd = ( Trade ) uow.refreshObject( trd );
            TradingParty tp = CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), trd.getCounterpartyA().getOrganization() );
            assertEquals( trd.getCptyTrade( 'B' ).getLegalEntity().getObjectID(), le.getObjectID() );
            assertEquals( trd.getCptyTrade( 'D' ).getLegalEntity().getObjectID(), le.getObjectID() );
            assertEquals( trd.getCptyTrade( 'A' ).getTradingParty(), tp );
            assertEquals( trd.getCptyTrade( 'C' ).getTradingParty(), tp );
            assertEquals( trd.getCounterpartyB().getObjectID(), le.getObjectID() );
            assertEquals( trd.getTakerCounterparty().getObjectID(), trd.getCounterpartyA().getObjectID() );
            assertEquals( trd.getMakerCounterparty(), CounterpartyUtilC.getTradingParty( trd.getCounterpartyB(), trd.getCounterpartyA().getOrganization() ) );
            WorkflowMessage wm2 = MessageFactory.newWorkflowMessage();
            wm2.setObject( trd );
            wm2.setProperty( "organization", trd.getCounterpartyB().getOrganization() );
            trd.setCptyTrade( trd.getCptyTrade( 'B' ) );
            String xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm2, null );
            xml = applyStleSheetOnXML( xml, "FinXML51v20" );
            //these wont work because then it should be invoked via TradeService where all these parameters are set on TradeService.And if we invoke via tradeService; we cannot get this xml
            /*int leIndex = xml.indexOf( TradeService.CPTYTRADE_LE);
                    String leStr = xml.substring( xml.indexOf( ">" , leIndex) , xml.indexOf( "<" , leIndex) );
                    int tpIndex = xml.indexOf( TradeService.CPTYTRADE_TP);
                    int cptyAIndex = xml.indexOf( TradeService.CPTY_A);
                    String cptyAStr = xml.substring( xml.indexOf( ">" , cptyAIndex) , xml.indexOf( "<" , cptyAIndex) );
                    int cptyBIndex = xml.indexOf( TradeService.CPTY_B);
                    assertEquals( leIndex > 0, true );
                    assertEquals( tpIndex == -1 , true);
                    assertEquals( cptyAIndex > 0, true );
                    assertEquals( cptyBIndex == -1 , true);
                    assertEquals( leStr, le.getShortName());
                    assertEquals( cptyAStr, oldCpty.getShortName());
                    wm2.setProperty( "organization", trd.getCounterpartyB().getOrganization() );
                    trd.setCptyTrade( trd.getCptyTrade( 'B'));
                    xml = new TradeDownloadMessageBuilderC().buildDownloadMessage( wm2, null );
                    xml = applyStleSheetOnXML( xml, "FinXML51v20" );
            */
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {

            tx.release();
        }
    }

    public void testSuccess() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        init( takerUser );
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        LegalEntity le = getLegalEntityWithRelationship( ( LegalEntity ) trd.getCounterpartyA(), ( LegalEntity ) trd.getCounterpartyB() );


        tx.commit();
        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        trd = ( Trade ) trd.getRegisteredObject();
        le = ( LegalEntity ) uow.refreshObject( le );
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setObject( trd );
        wm.setEventName( TradeService.AMEND_EVENT );
        wm.setTopic( TradeService.MESSAGE_TOPIC );
        wm.setParameterValue( TradeService.ORIG_CPTY, trd.getCounterpartyA() );
        wm.setParameterValue( TradeService.CPTY_A_AMENDED, true );
        trd.setCounterpartyA( le );

        WorkflowMessage msg = ts.process( wm );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        le = ( LegalEntity ) uow.refreshObject( le );
        assertEquals( trd.getCounterpartyA().getObjectID(), le.getObjectID() );//todo ask Polson why is that trd.getCA != le; thats why i had to do do equals on object id . isnt that toplink should have taken care of it.
        assertEquals( trd.getCptyTrade( 'A' ).getLegalEntity().getObjectID(), le.getObjectID() );
        assertNotNull( trd.getEtlTimestamp() );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isConfirmed(), true );

        tx.release();
    }

    public void testSuccessConfirmState() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        init( takerUser );
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.verifyTrade( trd, null );
        ts.confirmTrade( trd, null );
        LegalEntity le = getLegalEntityWithRelationship( ( LegalEntity ) trd.getCounterpartyA(), ( LegalEntity ) trd.getCounterpartyB() );

        tx.commit();
        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        trd = ( Trade ) trd.getRegisteredObject();
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setObject( trd );
        wm.setEventName( TradeService.AMEND_EVENT );
        wm.setTopic( TradeService.MESSAGE_TOPIC );
        wm.setParameterValue( TradeService.ORIG_CPTY, trd.getCounterpartyA() );
        wm.setParameterValue( TradeService.CPTY_A_AMENDED, true );
        trd.setCounterpartyA( le );

        WorkflowMessage msg = ts.process( wm );
        assertEquals( msg.getStatus(), MessageStatus.SUCCESS );
        tx.commit();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );

        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isConfirmed(), true );

        tx.release();
    }

    public void testTradeStateFailure() throws Exception
    {
        IdcTransaction tx;
        Trade trd;
        UnitOfWork uow;
        init( takerUser );
        tx = initTransaction();
        uow = tx.getUOW();

        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = prepareSingleLegTrade( 1000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
        prepareSingleLegRequest( ( FXSingleLeg ) trd );
        TradeCloneServiceC.createCptyTrades( trd );

        ts.newTrade( trd, null );
        ts.rejectTrade( trd, 1, "TEST", null );
        LegalEntity le = getLegalEntityWithRelationship( ( LegalEntity ) trd.getCounterpartyA(), ( LegalEntity ) trd.getCounterpartyB() );

        tx.commit();
        tx = initTransaction();
        uow = tx.getUOW();
        uow.addReadOnlyClasses( getDealingTransactionReadOnlyClasses() );
        trd = ( Trade ) uow.refreshObject( trd );
        trd = ( Trade ) trd.getRegisteredObject();
        le = ( LegalEntity ) uow.refreshObject( le );
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setObject( trd );
        wm.setEventName( TradeService.AMEND_EVENT );
        wm.setTopic( TradeService.MESSAGE_TOPIC );
        Counterparty originalCpty = trd.getCounterpartyA();
        wm.setParameterValue( TradeService.ORIG_CPTY, originalCpty );
        wm.setParameterValue( TradeService.CPTY_A_AMENDED, true );
        trd.setCounterpartyA( le );

        WorkflowMessage msg = ts.process( wm );
        assertEquals( msg.getStatus(), MessageStatus.FAILURE );
        assertNotNull( msg.getError( "INVALID_TRADE_STATE" ) );
        tx.release();

        tx = initTransaction();
        uow = tx.getUOW();
        trd = ( Trade ) uow.refreshObject( trd );
        assertEquals( trd.getCounterpartyA().getObjectID(), originalCpty.getObjectID() );
        assertEquals( trd.getCptyTrade( 'A' ).getLegalEntity().getObjectID(), originalCpty.getObjectID() );
        assertNull( trd.getEtlTimestamp() );
        TradeStateFacade tradeFacade = ( TradeStateFacade ) FacadeFactory.newEntityFacade( ts.getConfig().getTradeStateFacade(), trd );
        assertEquals( tradeFacade.isRejected(), true );

        tx.release();
    }

    private LegalEntity getLegalEntityWithRelationship( LegalEntity le1, LegalEntity le2 )
    {
        Organization org1 = le1.getOrganization();
        Collection<LegalEntity> org1Les = org1.getLegalEntities();
        for ( LegalEntity org1Le : org1Les )
        {
            if ( org1Le.isSameAs( le1 ) )
            {
                continue;
            }
            if ( CounterpartyUtilC.isValidRelationShip( org1Le, le2 ) )
            {
                return org1Le;
            }
        }
        return null;
    }

    protected void setUp() throws Exception
    {
        super.setUp();

        try
        {
            init( adminUser );
            IdcTransaction tx = initTransaction();
            uow = tx.getUOW();
            uow.removeAllReadOnlyClasses();
            Collection<LegalEntity> lpLes = lpOrg.getLegalEntities();
            Collection<LegalEntity> fiLes = fiOrg.getLegalEntities();
            for ( LegalEntity lpLe : lpLes )
            {
                for ( LegalEntity fiLe : fiLes )
                {
                    if ( !CounterpartyUtilC.isValidRelationShip( fiLe, lpLe ) )
                    {
                        setValidRelationship( fiLe, lpLe );
                    }
                }
            }
            tx.commit();
        }
        catch ( Exception e )
        {
            log.error( "setup exception", e );
        }
    }
}
