package com.integral.finance.trade.test;

import com.integral.finance.trade.TradeChannel;
import com.integral.finance.trade.TradeChannelC;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.user.UserFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;
import org.junit.Test;

/**
 * <AUTHOR> jessy
 *         To change this template use File | Settings | File Templates.
 */
public class TradeChannelPTestC extends PTestCaseC {
    public TradeChannelPTestC(final String aName) {
        super(aName);
    }

    @Test
    public void testCreateAndModifyTradeChannel() {
        log.info("testCreateTradeChannel");
        try {
            PersistenceFactory.getPersistenceMBean().setDatabaseLogSQL(true);
            PersistenceFactory.getPersistenceMBean().setToplinkLogDefaultDebuglevel(true);

            Session session1 = (org.eclipse.persistence.sessions.Session) getPersistenceSession();
            UnitOfWork uow = session1.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass(NamespaceC.class);
            ExpressionBuilder eb = new ExpressionBuilder();

            Expression expr = eb.get("shortName").equal("FXIAPI_FXIPrime");
            TradeChannel tradeChannelC = (TradeChannel) uow.readObject(TradeChannelC.class, expr);
            if (tradeChannelC == null){ // for creation
                TradeChannel tradeChannelNew = new TradeChannelC();
                tradeChannelC = (TradeChannelC) uow.registerObject(tradeChannelNew);
            }
            tradeChannelC.setChannel("FXIAPI_FXIPrime");
            tradeChannelC.setClientApp("API");
            tradeChannelC.setAppServerType("FXI");
            tradeChannelC.setWorkflowType("ESP");
            tradeChannelC.setUiComponent("APIWeb");
            tradeChannelC.setDisplay("FXI Prime HTML");
            tradeChannelC.setNamespace(UserFactory.getNamespace("MAIN"));
            tradeChannelC.setProduct("Product");
            uow.commit();


            TradeChannel tc = (TradeChannel) uow.readObject(TradeChannelC.class, expr);
            assertNotNull(tc);
            assertNotNull("Channel not null", tc.getChannel());

            assertEquals("FXIAPI_FXIPrime", tc.getChannel());
            assertEquals("API", tc.getClientApp());
            assertEquals("FXI", tc.getAppServerType());
            assertEquals("ESP", tc.getWorkflowType());
            assertEquals("APIWeb", tc.getUiComponent());
            assertEquals("FXI Prime HTML", tc.getDisplay());
            assertEquals("Product", tc.getProduct());

        } catch (Exception e) {
            e.printStackTrace();
            fail("testCreateTradeChannel");
        }

    }

//    @Test
//    public void testCreateTradeChannel() {
//        log.info("testCreateTradeChannel");
//        try {
//            PersistenceFactory.getPersistenceMBean().setDatabaseLogSQL(true);
//            PersistenceFactory.getPersistenceMBean().setToplinkLogDefaultDebuglevel(true);
//
//            Session session1 = (Session) getPersistenceSession();
//            UnitOfWork uow1 = session1.acquireUnitOfWork();
//            uow1.removeAllReadOnlyClasses();
//
//            //OrganizationC org = (OrganizationC)UserFactory.newOrganization();
//            ExpressionBuilder eb = new ExpressionBuilder();
//            Expression expr = eb.get("shortName").equal("fi2mm1");
//            Expression expr1 = eb.get("namespace").get("shortName").equal("FI2");
////            Expression expr2 = eb.get("shortName").equal("FI2");
////            Expression expr3 = eb.get("shortName").equal(standardQuoteConvName);
////            Expression expr5 = eb.get("shortName").equal("FI2-le1");
//
//            UserC userC = ( UserC ) uow1.readObject( UserC.class, expr.and( expr1 ) );
//
//            TradeChannelC tradeChannelC = new TradeChannelC();
//            long objectID = tradeChannelC.getObjectID();
//            tradeChannelC.setObjectId(objectID);
//            tradeChannelC.setChannel("FXIAPI_FXIPrime");
//            tradeChannelC.setClientApp("API");
//            tradeChannelC.setAppServerType("FXI");
//            tradeChannelC.setWorkflowType("ESP");
//            tradeChannelC.setUiComponent("APIWeb");
//            tradeChannelC.setDisplay("FXI Prime HTML");
//            tradeChannelC.setNamespace(userC.getNamespace());
//            tradeChannelC.setShortName(tradeChannelC.getChannel());
//            tradeChannelC.setGUID("test");
//            uow1.commitAndResume();
//
//
//            Expression expr4 = eb.get("objectID").equal(objectID);
//            TradeChannelC tc = (TradeChannelC) session1.readObject(TradeChannelC.class, expr4);
//            assertNotNull(tc);
//            assertNotNull("Channel not null", tc.getChannel());
//
//            assertEquals("FXIAPI_FXIPrime",tc.getChannel());
//            assertEquals("API",tc.getClientApp());
//            assertEquals("FXI",tc.getAppServerType());
//            assertEquals("ESP",tc.getWorkflowType());
//            assertEquals("APIWeb",tc.getUiComponent());
//            assertEquals("FXI Prime HTML",tc.getDisplay());
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            fail("testCreateNettingTradeRequest");
//        }
//
//    }
}
