package com.integral.finance.trade.test;

import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TenorBand;
import com.integral.finance.trade.TradeClassification;
import com.integral.finance.trade.TradeClassificationC;
import com.integral.finance.trade.configuration.TradeConfiguration;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.is.ISCommonConstants;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.util.IdcUtilC;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR> Development Corporation.
 */
public class TradeConfigurationMBeanTestC extends MBeanTestCaseC
{
    TradeConfiguration tradeMBean = ( TradeConfiguration ) TradeConfigurationFactory.getTradeConfigurationMBean ();

    public TradeConfigurationMBeanTestC ( String aName )
    {
        super ( aName );
    }

    public void testProperties ( )
    {
        testProperty ( tradeMBean, "STPDownloadEnabledForManualTrade", "Idc.ManualTrade.STPDownload.Enabled", MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "nettingInterTradeMSPause", "Idc.Netting.InterTrade.MSPause", MBeanTestCaseC.INTEGER );
        testProperty ( tradeMBean, "nettingTxBatchSize", "Idc.Netting.Transaction.CommitSize", MBeanTestCaseC.INTEGER );
        testProperty ( tradeMBean, "netingProgressRefreshTime", "Idc.Netting.Progress.RefreshTime", MBeanTestCaseC.INTEGER );
        testProperty ( tradeMBean, "nettingVerifiedDealsTimeInterval", "Idc.Netting.DoneDeals.DataWarehouse.Time", MBeanTestCaseC.INTEGER );
        testProperty ( tradeMBean, "NZDRollTimeRollTradeDateEnabled", TradeConfigurationMBean.NZD_ROLLTIME_ROLLTRADEDATE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "NDFSupported", TradeConfigurationMBean.NDF_SUPPORT_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "FSRSupported", TradeConfigurationMBean.FSR_SUPPORT_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "SSPSupported", TradeConfigurationMBean.SSP_SUPPORT_ENABLED, MBeanTestCaseC.BOOLEAN );

        testProperty ( tradeMBean, "useInternalQueueForFIXSTP", TradeConfigurationMBean.Key_Use_InternalQueue_For_FIX_STP, MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "maxNumDoneTrades", TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, MBeanTestCaseC.INTEGER );
        testProperty ( tradeMBean, "manualTradeMismatchedCoverAllowed", TradeConfigurationMBean.MANUAL_TRADE_MISMATCHED_COVER_ALLOWED, MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "PBCoverTradeMakerRefEnabled", TradeConfigurationMBean.PB_COVERTRADE_MAKERREF_ID_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "priceSkewEnabled", TradeConfigurationMBean.IDC_SKEW_SERVICE_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "stpSenderOverFlowPoolSize", TradeConfigurationMBean.STP_SENDER_OVERFLOW_POOL_SIZE, MBeanTestCaseC.INTEGER );
        testProperty ( tradeMBean, "useCreateEventSTPForPortfolioTradeSTP", TradeConfigurationMBean.IDC_USE_CREATE_EVENT_PORTFOLIO_TRADE_STP, MBeanTestCaseC.BOOLEAN );
        testProperty ( tradeMBean, "asyncPersistenceForManualTrade", TradeConfigurationMBean.ASYNC_PERSISTENCE_MANUAL_TRADE, MBeanTestCaseC.BOOLEAN );
    }

    public void testUserMappingForFIXTags ( )
    {
        try
        {
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_REPLACE_USER_NAMES + "FI1", "fi1mm1|Aliasfi1mm1,fi1mm2|Aliasfi1mm2,fi1mm3|Aliasfi1mm3", ConfigurationProperty.DYNAMIC_SCOPE, null );

            assertEquals ( "Aliasfi1mm1", tradeMBean.getUserMappingForFIXPartyID ( "FI1", "fi1mm1" ) );
            assertEquals ( "Aliasfi1mm2", tradeMBean.getUserMappingForFIXPartyID ( "FI1", "fi1mm2" ) );
            assertEquals ( "Aliasfi1mm3", tradeMBean.getUserMappingForFIXPartyID ( "FI1", "fi1mm3" ) );
        }
        catch ( Exception e )
        {

            e.printStackTrace ();
            fail ( "testUserMappingForFIXTags" );

        }
    }

    public void testShowAllSpreadsConfig ( )
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isShowAllSpreadsInSTP ( org1 ) );
            assertFalse ( tradeMBean.isShowAllSpreadsInSTP ( org2 ) );

            // now set the org1 as show all spreads enabled.
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SPREADS_SHOWALL, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isShowAllSpreadsInSTP ( org1 ) );
            assertTrue ( tradeMBean.isShowAllSpreadsInSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SPREADS_SHOWALL_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SPREADS_SHOWALL_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isShowAllSpreadsInSTP ( org1 ) );
            assertFalse ( tradeMBean.isShowAllSpreadsInSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SPREADS_SHOWALL, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isShowAllSpreadsInSTP ( org1 ) );
            assertFalse ( tradeMBean.isShowAllSpreadsInSTP ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testShowAllSpreadsConfig" );
        }
    }

    public void testSupportedSpreadsForTradeType ( )
    {
        try
        {
            ReadNamedEntityC namedEntityReader = new ReadNamedEntityC ();
            TradeClassification spot = ( TradeClassification ) namedEntityReader.execute ( TradeClassificationC.class, "FXSpot" );
            TradeClassification outright = ( TradeClassification ) namedEntityReader.execute ( TradeClassificationC.class, "FXOutright" );
            TradeClassification spotFwd = ( TradeClassification ) namedEntityReader.execute ( TradeClassificationC.class, "FXSpotFwd" );
            TradeClassification fwdFwd = ( TradeClassification ) namedEntityReader.execute ( TradeClassificationC.class, "FXFwdFwd" );
            Collection<String> spotSpreads = tradeMBean.getSTPSpreads ( spot );
            assertNotNull ( spotSpreads );
            assertTrue ( spotSpreads.size () > 0 );

            Collection<String> outrightSpreads = tradeMBean.getSTPSpreads ( outright );
            assertNotNull ( outrightSpreads );
            assertTrue ( outrightSpreads.size () > 0 );

            Collection<String> spotFwdSpreads = tradeMBean.getSTPSpreads ( spotFwd );
            assertNotNull ( spotFwdSpreads );
            assertTrue ( spotFwdSpreads.size () > 0 );

            Collection<String> fwdFwdSpreads = tradeMBean.getSTPSpreads ( fwdFwd );
            assertNotNull ( fwdFwdSpreads );
            assertTrue ( fwdFwdSpreads.size () > 0 );

            // now set a single spread only for spot type.
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SPREADS_PREFIX + "FXSpot", "PMMinSprd", ConfigurationProperty.DYNAMIC_SCOPE, null );
            spotSpreads = tradeMBean.getSTPSpreads ( spot );
            assertEquals ( spotSpreads.size (), 1 );

            // sent three different spread types for spot.
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SPREADS_PREFIX + "FXSpot", "PMMinSprd,PPCustSprd,PPSpotSprd", ConfigurationProperty.DYNAMIC_SCOPE, null );
            spotSpreads = tradeMBean.getSTPSpreads ( spot );
            assertEquals ( spotSpreads.size (), 3 );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSupportedSpreadsForTradeType" );
        }
    }

    public void testShowEndOfDayRateConfig ( )
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isShowEndOfDayRateInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isShowEndOfDayRateInFIXSTP ( org2 ) );

            // now set the org1 as show all spreads enabled.
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_FIX_EOD_RATE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isShowEndOfDayRateInFIXSTP ( org1 ) );
            assertTrue ( tradeMBean.isShowEndOfDayRateInFIXSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_FIX_EOD_RATE_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_FIX_EOD_RATE_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isShowEndOfDayRateInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isShowEndOfDayRateInFIXSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_FIX_EOD_RATE, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isShowEndOfDayRateInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isShowEndOfDayRateInFIXSTP ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testShowEndOfDayRateConfig" );
        }
    }

    public void testShowChannelInFIXSTPConfig ( )
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertTrue ( tradeMBean.isShowChannelInFIXSTPMap ( org1 ) );
            assertTrue ( tradeMBean.isShowChannelInFIXSTPMap ( org2 ) );

            // now set the global property as show channel disabled
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_FIX_INCLUDE_CHANNEL, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isShowChannelInFIXSTPMap ( org1 ) );
            assertFalse ( tradeMBean.isShowChannelInFIXSTPMap ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_FIX_INCLUDE_CHANNEL_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_FIX_INCLUDE_CHANNEL_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isShowChannelInFIXSTPMap ( org1 ) );
            assertFalse ( tradeMBean.isShowChannelInFIXSTPMap ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_FIX_INCLUDE_CHANNEL, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isShowChannelInFIXSTPMap ( org1 ) );
            assertFalse ( tradeMBean.isShowChannelInFIXSTPMap ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testShowChannelInFIXSTPConfig" );
        }
    }

    public void testPriceSkewServiceEnabled ( )
    {
        try
        {
            assertFalse ( tradeMBean.isPriceSkewEnabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "CITI" );
            assertFalse ( tradeMBean.isPriceSkewEnabled ( org1 ) );
            assertFalse ( tradeMBean.isPriceSkewEnabled ( org2 ) );

            // now set the global property as show channel disabled
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_SKEW_SERVICE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isPriceSkewEnabled ( org1 ) );
            assertTrue ( tradeMBean.isPriceSkewEnabled ( org2 ) );
            assertTrue ( tradeMBean.isPriceSkewEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_SKEW_SERVICE_ENABLED_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_SKEW_SERVICE_ENABLED_PREFIX + "CITI", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isPriceSkewEnabled ( org1 ) );
            assertFalse ( tradeMBean.isPriceSkewEnabled ( org2 ) );

            // test the non broker for customer, skew settings
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_SKEW_SERVICE_ENABLED_ALL_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_SKEW_SERVICE_ENABLED_ALL_PREFIX + "CITI", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isPriceSkewEnabled ( org1 ) );
            assertFalse ( tradeMBean.isPriceSkewEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_SKEW_SERVICE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isPriceSkewEnabled ( org1 ) );
            assertFalse ( tradeMBean.isPriceSkewEnabled ( org2 ) );
            assertFalse ( tradeMBean.isPriceSkewEnabled ( null ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPriceSkewServiceEnabled" );
        }
    }

    public void testPricingConfiguration ( )
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            if ( null != org1 )
            {
                tradeMBean.setProperty ( TradeConfigurationMBean.IDC_SWAP_PRICING_MID_NEAR_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
                assertTrue ( tradeMBean.isSwapFwdPointsAsMid ( org1 ) );
            }
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            assertFalse ( tradeMBean.isSwapFwdPointsAsMid ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPricingConfiguration" );
        }
    }

    public void testSendTradeCaptureReportWithoutSTP ( )
    {
        try
        {
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org1 ) );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org2 ) );

            // now set the global property as show channel disabled
            tradeMBean.setProperty ( TradeConfigurationMBean.KEY_SendTradeCaptureReportWithoutSTP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org1 ) );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org2 ) );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.KEY_SendTradeCaptureReportWithoutSTP, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org1 ) );
            assertFalse ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org2 ) );
            assertFalse ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.KEY_SendTradeCaptureReportWithoutSTP_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.KEY_SendTradeCaptureReportWithoutSTP_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org1 ) );
            assertFalse ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.KEY_SendTradeCaptureReportWithoutSTP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org1 ) );
            assertFalse ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org2 ) );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.KEY_SendTradeCaptureReportWithoutSTP_PREFIX + "FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.KEY_SendTradeCaptureReportWithoutSTP_PREFIX + "FI2", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org1 ) );
            assertTrue ( tradeMBean.isSendTradeCaptureReportWithoutSTP ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSendTradeCaptureReportWithoutSTP" );
        }
    }

    public void testDoneTradesQueryMaxCountConfig ( )
    {
        try
        {
            int defaultMaxCount = tradeMBean.getMaxNumDoneTrades ();
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org1 ), defaultMaxCount );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org2 ), defaultMaxCount );

            // now set the org1 with a different value
            int newDefaultMaxCount = 500;
            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, "500", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org1 ), newDefaultMaxCount );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org2 ), newDefaultMaxCount );

            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT_PREFIX + "FI1", "1000", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT_PREFIX + "FI2", "3000", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org1 ), 1000 );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org2 ), 3000 );

            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT, "50", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org1 ), 1000 );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org2 ), 3000 );

            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_MAX_COUNT_PREFIX + "FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org1 ), 50 );
            assertEquals ( tradeMBean.getMaxNumDoneTrades ( org2 ), 3000 );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testDoneTradesQueryMaxCountConfig" );
        }
    }

    public void testUPIProductMapping ( )
    {
        Organization organization = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        try
        {
            //FXSpot~SPOT,FXOutright~OUTRIGHT,FXNDF~NDF,FXSpotFwd~SWAP,FXFwdFwd~SWAP
            assertEquals ( "SPOT", tradeMBean.getUPIProduct ( organization, "FXSpot" ) );
            assertEquals ( "OUTRIGHT", tradeMBean.getUPIProduct ( organization, "FXOutright" ) );
            assertEquals ( "NDF", tradeMBean.getUPIProduct ( organization, "FXNDF" ) );
            assertEquals ( "SWAP", tradeMBean.getUPIProduct ( organization, "FXSpotFwd" ) );
            assertEquals ( "SWAP", tradeMBean.getUPIProduct ( organization, "FXFwdFwd" ) );

            // test customer property
            tradeMBean.setProperty ( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + ".FI1",
                    "FXSpot~SPOT1,FXOutright~OUTRIGHT,FXNDF~NDF,FXSpotFwd~SWAP,FXFwdFwd~SWAP", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( "SPOT1", tradeMBean.getUPIProduct ( organization, "FXSpot" ) );

            // test broker property
            Organization broker = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            organization.setBrokerOrganization ( broker );
            tradeMBean.setProperty ( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + ".BrokerCustomers." + "Broker1",
                    "FXSpot~SPOT2,FXOutright~OUTRIGHT,FXNDF~NDF,FXSpotFwd~SWAP,FXFwdFwd~SWAP", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.removeProperty ( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + ".FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            assertEquals ( "SPOT2", tradeMBean.getUPIProduct ( organization, "FXSpot" ) );

            // test global property
            tradeMBean.setProperty ( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING,
                    "FXSpot~SPOT3,FXOutright~OUTRIGHT,FXNDF~NDF,FXSpotFwd~SWAP,FXFwdFwd~SWAP", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.removeProperty ( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + ".BrokerCustomers." + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            assertEquals ( "SPOT3", tradeMBean.getUPIProduct ( organization, "FXSpot" ) );

            assertEquals ( tradeMBean.getUPIProduct ( organization, "FXSpot1" ), "FXSpot1" );

            // add partial property
            tradeMBean.setProperty ( TradeConfigurationMBean.UPI_CLSF_PRODUCT_MAPPING + ".FI1",
                    "FXOutright~OUTRIGHT,FXNDF~NDF,FXSpotFwd~SWAP,FXFwdFwd~SWAP", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getUPIProduct ( organization, "FXSpot" ), "FXSpot" );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUPIProductMapping" );
        }
        finally
        {
            IdcUtilC.refreshObject ( organization );
        }
    }

    public void testUseBusinessDateForTransactTime ( )
    {
        try
        {
            assertFalse ( tradeMBean.isUseBusinessExecDateForTransactTime ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isUseBusinessExecDateForTransactTime ( org1 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateForTransactTime ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.TRANSACTTIME_USE_BUSINESS_EXECDATE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseBusinessExecDateForTransactTime ( org1 ) );
            assertTrue ( tradeMBean.isUseBusinessExecDateForTransactTime ( org2 ) );
            assertTrue ( tradeMBean.isUseBusinessExecDateForTransactTime ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.TRANSACTTIME_USE_BUSINESS_EXECDATE, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseBusinessExecDateForTransactTime ( org1 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateForTransactTime ( org2 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateForTransactTime ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.TRANSACTTIME_USE_BUSINESS_EXECDATE_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.TRANSACTTIME_USE_BUSINESS_EXECDATE_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseBusinessExecDateForTransactTime ( org1 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateForTransactTime ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.TRANSACTTIME_USE_BUSINESS_EXECDATE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseBusinessExecDateForTransactTime ( org1 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateForTransactTime ( org2 ) );
            assertTrue ( tradeMBean.isUseBusinessExecDateForTransactTime ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.TRANSACTTIME_USE_BUSINESS_EXECDATE_PREFIX + "FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.TRANSACTTIME_USE_BUSINESS_EXECDATE_PREFIX + "FI2", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseBusinessExecDateForTransactTime ( org1 ) );
            assertTrue ( tradeMBean.isUseBusinessExecDateForTransactTime ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseBusinessDateForTransactTime" );
        }
    }

    public void testSTPMessageIdGenerationSource ( )
    {
        try
        {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org1 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_GUID );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org2 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_GUID );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE, "1", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org1 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_NANOSECONDS );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org2 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_NANOSECONDS );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( null ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_NANOSECONDS );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE, "0", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org1 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org2 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( null ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS );


            tradeMBean.setProperty ( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_PREFIX + "FI1", "1", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_PREFIX + "FI2", "2", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org1 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_NANOSECONDS );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org2 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_GUID );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( null ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE, "0", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org1 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_NANOSECONDS );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org2 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_GUID );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( null ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_PREFIX + "FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_PREFIX + "FI2", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org1 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( org2 ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS );
            assertEquals ( tradeMBean.getSTPMessageIdGenerationSource ( null ), TradeConfigurationMBean.STP_MESSAGEID_GENERATION_SOURCE_MILLISECONDS );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSTPMessageIdGenerationSource" );
        }
    }

    public void testBatchTradeSupported ( )
    {
        try
        {
            assertFalse ( tradeMBean.isBatchTradeSupported ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isBatchTradeSupported ( org1 ) );
            assertFalse ( tradeMBean.isBatchTradeSupported ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_SUPPORTED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isBatchTradeSupported ( org1 ) );
            assertTrue ( tradeMBean.isBatchTradeSupported ( org2 ) );
            assertTrue ( tradeMBean.isBatchTradeSupported ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_SUPPORTED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isBatchTradeSupported ( org1 ) );
            assertFalse ( tradeMBean.isBatchTradeSupported ( org2 ) );
            assertFalse ( tradeMBean.isBatchTradeSupported ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_SUPPORTED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_SUPPORTED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isBatchTradeSupported ( org1 ) );
            assertFalse ( tradeMBean.isBatchTradeSupported ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_SUPPORTED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isBatchTradeSupported ( org1 ) );
            assertFalse ( tradeMBean.isBatchTradeSupported ( org2 ) );
            assertTrue ( tradeMBean.isBatchTradeSupported ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_SUPPORTED_PREFIX + "FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_SUPPORTED_PREFIX + "FI2", "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isBatchTradeSupported ( org1 ) );
            assertTrue ( tradeMBean.isBatchTradeSupported ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_BATCH_SPREAD_NAME_SWAP_TO_OUTRIGHT, "PPFarSprd:PPFwdSprd,PPNearSprd:PPFwdSprd", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getSpreadTagModifiedName ( "PPFarSprd" ), "PPFwdSprd" );
            assertEquals ( tradeMBean.getSpreadTagModifiedName ( "PPNearSprd" ), "PPFwdSprd" );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testBatchTradeSupported" );
        }
    }

    public void testIncludeOrderNotesInSTP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isIncludeOrderNotesInSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isIncludeOrderNotesInSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludeOrderNotesInSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_ORDER_NOTES_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeOrderNotesInSTP ( org1 ) );
            assertTrue ( tradeMBean.isIncludeOrderNotesInSTP ( org2 ) );
            assertTrue ( tradeMBean.isIncludeOrderNotesInSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_ORDER_NOTES_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isIncludeOrderNotesInSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludeOrderNotesInSTP ( org2 ) );
            assertFalse ( tradeMBean.isIncludeOrderNotesInSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_ORDER_NOTES_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_ORDER_NOTES_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeOrderNotesInSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludeOrderNotesInSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_ORDER_NOTES_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeOrderNotesInSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludeOrderNotesInSTP ( org2 ) );
            assertTrue ( tradeMBean.isIncludeOrderNotesInSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testIncludeOrderNotesInSTP" );
        }
    }

    public void testIncludePortfolioRefIdInSTP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isIncludePortfolioRefIdInSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isIncludePortfolioRefIdInSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludePortfolioRefIdInSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_PORTFOLIOREFID_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludePortfolioRefIdInSTP ( org1 ) );
            assertTrue ( tradeMBean.isIncludePortfolioRefIdInSTP ( org2 ) );
            assertTrue ( tradeMBean.isIncludePortfolioRefIdInSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_PORTFOLIOREFID_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isIncludePortfolioRefIdInSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludePortfolioRefIdInSTP ( org2 ) );
            assertFalse ( tradeMBean.isIncludePortfolioRefIdInSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_PORTFOLIOREFID_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_PORTFOLIOREFID_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludePortfolioRefIdInSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludePortfolioRefIdInSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_INCLUDE_PORTFOLIOREFID_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludePortfolioRefIdInSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludePortfolioRefIdInSTP ( org2 ) );
            assertTrue ( tradeMBean.isIncludePortfolioRefIdInSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testIncludePortfolioRefIdInSTP" );
        }
    }

    public void testIncludeCoverTradeIdsInSTPForBatchTrade ( )
    {
        assertTrue ( "Default Value for Idc.Trade.Batch.STP.Include.CoverTrade.Ids.Value for null", tradeMBean.isCoverTradeIncludedInStpForBatchTrade ( null ) );
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        assertTrue ( "Default Value for Idc.Trade.Batch.STP.Include.CoverTrade.Ids.Value for Broker1", tradeMBean.isCoverTradeIncludedInStpForBatchTrade ( org1 ) );
        tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_BATCH_STP_INCLUDE_COVERTRADE_IDS, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertFalse ( "Global Value for Idc.Trade.Batch.STP.Include.CoverTrade.Ids.Value for Broker1", tradeMBean.isCoverTradeIncludedInStpForBatchTrade ( org1 ) );
        tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_BATCH_STP_INCLUDE_COVERTRADE_IDS_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertTrue ( "Global Value for Idc.Trade.Batch.STP.Include.CoverTrade.Ids.Value for Broker1", tradeMBean.isCoverTradeIncludedInStpForBatchTrade ( org1 ) );
    }

    public void testSTPSenderAuxPoolEnabled ( )
    {
        try
        {
            assertFalse ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org1 ) );
            assertFalse ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SENDER_AUX_POOL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org1 ) );
            assertTrue ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org2 ) );
            assertTrue ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SENDER_AUX_POOL_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org1 ) );
            assertFalse ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org2 ) );
            assertFalse ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SENDER_AUX_POOL_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SENDER_AUX_POOL_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org1 ) );
            assertFalse ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.STP_SENDER_AUX_POOL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org1 ) );
            assertFalse ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( org2 ) );
            assertTrue ( tradeMBean.isStpSenderAuxiliaryPoolEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSTPSenderAuxPoolEnabled" );
        }
    }

    public void testUseCreateEventForPortfolioTradeSTP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_USE_CREATE_EVENT_PORTFOLIO_TRADE_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org1 ) );
            assertTrue ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org2 ) );
            assertTrue ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_USE_CREATE_EVENT_PORTFOLIO_TRADE_STP, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org2 ) );
            assertFalse ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_USE_CREATE_EVENT_PORTFOLIO_TRADE_STP_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_USE_CREATE_EVENT_PORTFOLIO_TRADE_STP_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_USE_CREATE_EVENT_PORTFOLIO_TRADE_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( org2 ) );
            assertTrue ( tradeMBean.isUseCreateEventSTPForPortfolioTradeSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseCreateEventForPortfolioTradeSTP" );
        }
    }

    public void testAutoRollTradeChannelEnabled ( )
    {
        try
        {
            assertFalse ( tradeMBean.isAutoRollTradeChannelEnabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isAutoRollTradeChannelEnabled ( org1 ) );
            assertFalse ( tradeMBean.isAutoRollTradeChannelEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_AUTO_ROLL_TRADE_CHANNEL_ENABLE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isAutoRollTradeChannelEnabled ( org1 ) );
            assertTrue ( tradeMBean.isAutoRollTradeChannelEnabled ( org2 ) );
            assertTrue ( tradeMBean.isAutoRollTradeChannelEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_AUTO_ROLL_TRADE_CHANNEL_ENABLE, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isAutoRollTradeChannelEnabled ( org1 ) );
            assertFalse ( tradeMBean.isAutoRollTradeChannelEnabled ( org2 ) );
            assertFalse ( tradeMBean.isAutoRollTradeChannelEnabled ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_AUTO_ROLL_TRADE_CHANNEL_ENABLE_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_AUTO_ROLL_TRADE_CHANNEL_ENABLE_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isAutoRollTradeChannelEnabled ( org1 ) );
            assertFalse ( tradeMBean.isAutoRollTradeChannelEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_AUTO_ROLL_TRADE_CHANNEL_ENABLE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isAutoRollTradeChannelEnabled ( org1 ) );
            assertFalse ( tradeMBean.isAutoRollTradeChannelEnabled ( org2 ) );
            assertTrue ( tradeMBean.isAutoRollTradeChannelEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testAutoRollTradeChannelEnabled" );
        }
    }

    public void testUseBusinessExecutionDateInDoneTradesQueryEnabled ( )
    {
        try
        {
            assertFalse ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org1 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_QUERY_USE_BUSINESS_EXECDATE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org1 ) );
            assertTrue ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org2 ) );
            assertTrue ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_QUERY_USE_BUSINESS_EXECDATE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org1 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org2 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_QUERY_USE_BUSINESS_EXECDATE_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_QUERY_USE_BUSINESS_EXECDATE_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org1 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.DONE_TRADES_QUERY_USE_BUSINESS_EXECDATE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org1 ) );
            assertFalse ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( org2 ) );
            assertTrue ( tradeMBean.isUseBusinessExecDateInDoneTradesQuery ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseBusinessExecutionDateInDoneTradesQueryEnabled" );
        }
    }

    public void testTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org1 ) );
            assertFalse ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_TWO_LEGS_ZERO_DEALT_AMOUNT_AS_SSP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org1 ) );
            assertTrue ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org2 ) );
            assertTrue ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_TWO_LEGS_ZERO_DEALT_AMOUNT_AS_SSP, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org1 ) );
            assertFalse ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org2 ) );
            assertFalse ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_TWO_LEGS_ZERO_DEALT_AMOUNT_AS_SSP_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_TWO_LEGS_ZERO_DEALT_AMOUNT_AS_SSP_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org1 ) );
            assertFalse ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.BATCH_TRADE_TWO_LEGS_ZERO_DEALT_AMOUNT_AS_SSP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org1 ) );
            assertFalse ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( org2 ) );
            assertTrue ( tradeMBean.isTwoLegsBatchTradeWithZeroDealtAmountAsSSP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTwoLegsBatchTradeWithZeroDealtAmountAsSSP" );
        }
    }

    public void testSendUTIWithoutNamespaceEnabled ( )
    {
        try
        {
            assertFalse ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org1 ) );
            assertFalse ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org1 ) );
            assertTrue ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org2 ) );
            assertTrue ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org1 ) );
            assertFalse ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org2 ) );
            assertFalse ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org1 ) );
            assertFalse ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.SEND_UTI_WITHOUT_NAMESPACE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org1 ) );
            assertFalse ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( org2 ) );
            assertTrue ( tradeMBean.isSendUTIWithoutNamespaceEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSendUTIWithoutNamespaceEnabled" );
        }
    }

    public void testMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org1 ) );
            assertFalse ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_MISMATCH_SWAP_SUPPRESS_NEGATIVE_SPREADS_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org1 ) );
            assertTrue ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org2 ) );
            assertTrue ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_MISMATCH_SWAP_SUPPRESS_NEGATIVE_SPREADS_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org1 ) );
            assertFalse ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org2 ) );
            assertFalse ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_MISMATCH_SWAP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_MISMATCH_SWAP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org1 ) );
            assertFalse ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_MISMATCH_SWAP_SUPPRESS_NEGATIVE_SPREADS_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org1 ) );
            assertFalse ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( org2 ) );
            assertTrue ( tradeMBean.isMismatchSwapsNegativeSpreadsSuppressEnabledForSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testMismatchSwapsNegativeSpreadsSuppressEnabledForSTP" );
        }
    }

    public void testIncludeTradeReportRefIdInFIXSTP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INCLUDE_TRADE_REPORT_REFID, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org1 ) );
            assertTrue ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org2 ) );
            assertTrue ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INCLUDE_TRADE_REPORT_REFID, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org2 ) );
            assertFalse ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INCLUDE_TRADE_REPORT_REFID_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INCLUDE_TRADE_REPORT_REFID_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INCLUDE_TRADE_REPORT_REFID, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( org2 ) );
            assertTrue ( tradeMBean.isIncludeTradeReportRefIdInFIXSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testIncludeTradeReportRefIdInFIXSTP" );
        }
    }

    public void testUsePBSettlementCodeInFIXSTP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org1 ) );
            assertTrue ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org2 ) );
            assertTrue ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org2 ) );
            assertFalse ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_PB_SETTLEMENT_CODE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( org2 ) );
            assertTrue ( tradeMBean.isUsePBSettlementCodeInFIXSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUsePBSettlementCodeInFIXSTP" );
        }
    }

    public void testIncludeTradeExecutionTimeInMillis ( )
    {
        try
        {
            assertFalse ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org1 ) );
            assertFalse ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org1 ) );
            assertTrue ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org2 ) );
            assertTrue ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org1 ) );
            assertFalse ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org2 ) );
            assertFalse ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org1 ) );
            assertFalse ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXML_INCLUDE_TRADE_EXECUTION_TIME_IN_MILLIS, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org1 ) );
            assertFalse ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( org2 ) );
            assertTrue ( tradeMBean.isIncludeTradeExecutionTimeInMillis ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testIncludeTradeExecutionTimeInMillis" );
        }
    }

    public void testWindowForwardSupportEnabled ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker1 );
        org2.setBrokerOrganization ( broker1 );
        try
        {
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( null ) );
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_SUPPORT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isWindowForwardSupportEnabled ( org1 ) );
            assertTrue ( tradeMBean.isWindowForwardSupportEnabled ( org2 ) );
            assertTrue ( tradeMBean.isWindowForwardSupportEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_SUPPORT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( org2 ) );
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_SUPPORT_ENABLED_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isWindowForwardSupportEnabled ( org1 ) );
            assertTrue ( tradeMBean.isWindowForwardSupportEnabled ( org2 ) );
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_SUPPORT_ENABLED_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_SUPPORT_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_SUPPORT_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isWindowForwardSupportEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_SUPPORT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isWindowForwardSupportEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardSupportEnabled ( org2 ) );
            assertTrue ( tradeMBean.isWindowForwardSupportEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testWindowForwardSupportEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker1 );
        }
    }

    public void testWindowForwardParametersSTPEnabled ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker1 );
        org2.setBrokerOrganization ( broker1 );
        try
        {
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( null ) );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isWindowForwardParametersSTPEnabled ( org1 ) );
            assertTrue ( tradeMBean.isWindowForwardParametersSTPEnabled ( org2 ) );
            assertTrue ( tradeMBean.isWindowForwardParametersSTPEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( org2 ) );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( org2 ) );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isWindowForwardParametersSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOWFORWARD_PARAMETERS_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isWindowForwardParametersSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isWindowForwardParametersSTPEnabled ( org2 ) );
            assertTrue ( tradeMBean.isWindowForwardParametersSTPEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testWindowForwardParametersSTPEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker1 );
        }
    }

    public void testWindowForwardCustomerSpreadMultiplier ( )
    {
        try
        {
            assertEquals ( 1.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertEquals ( 1.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org1 ) );
            assertEquals ( 1.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOW_FORWARD_CUSTOMER_SPREAD_MULTIPLIER, "2.0", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( 2.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( null ) );
            assertEquals ( 2.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org1 ) );
            assertEquals ( 2.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOW_FORWARD_CUSTOMER_SPREAD_MULTIPLIER, "3.0", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( 3.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( null ) );
            assertEquals ( 3.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org1 ) );
            assertEquals ( 3.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org2 ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOW_FORWARD_CUSTOMER_SPREAD_MULTIPLIER_PREFIX + "FI1", "4.5", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOW_FORWARD_CUSTOMER_SPREAD_MULTIPLIER_PREFIX + "FI2", "5.5", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( 3.0, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( null ) );
            assertEquals ( 4.5, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org1 ) );
            assertEquals ( 5.5, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_WINDOW_FORWARD_CUSTOMER_SPREAD_MULTIPLIER, "2.5", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( 2.5, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( null ) );
            assertEquals ( 4.5, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org1 ) );
            assertEquals ( 5.5, tradeMBean.getWindowForwardCustomerSpreadMultiplier ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testWindowForwardCustomerSpreadMultiplier" );
        }
    }

    public void testCoverTradeTagsOnPBDownloadDisabled ( )
    {
        try
        {
            assertFalse ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org1 ) );
            assertFalse ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_COVERTRADETAGS_ON_PB_DOWNLOAD_DISABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org1 ) );
            assertTrue ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org2 ) );
            assertTrue ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_COVERTRADETAGS_ON_PB_DOWNLOAD_DISABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org1 ) );
            assertFalse ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org2 ) );
            assertFalse ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_COVERTRADETAGS_ON_PB_DOWNLOAD_DISABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_COVERTRADETAGS_ON_PB_DOWNLOAD_DISABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org1 ) );
            assertFalse ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_COVERTRADETAGS_ON_PB_DOWNLOAD_DISABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org1 ) );
            assertFalse ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( org2 ) );
            assertTrue ( tradeMBean.isCoverTradeTagsOnPBDownloadDisabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testCoverTradeTagsOnPBDownloadDisabled" );
        }
    }

    public void testUseCurrencyPairAliasInSTP ( )
    {
        try
        {
            String ccyPair = "EUR/USD";
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( null, ccyPair ) );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( org1, ccyPair ) );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( org2, ccyPair ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS, "USD/JPY~PPP/QQQ;EUR/USD~XXX/YYY", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( null, ccyPair ) );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( org1, ccyPair ) );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( org2, ccyPair ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( null, ccyPair ) );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( org1, ccyPair ) );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( org2, ccyPair ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + "FI1", "USD/JPY~PPP/QQQ;EUR/USD~YYY/ZZZ", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + "FI2", "USD/JPY~PPP/QQQ;EUR/USD~AAA/BBB", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( null, ccyPair ) );
            assertEquals ( "YYY/ZZZ", tradeMBean.getCurrencyPairAliasInSTP ( org1, ccyPair ) );
            assertEquals ( "AAA/BBB", tradeMBean.getCurrencyPairAliasInSTP ( org2, ccyPair ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + "FI1", "EUR/USD~YYY/ZZZ", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCYPAIR_ALIAS_PREFIX + "FI2", "EUR/USD~AAA/BBB", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getCurrencyPairAliasInSTP ( null, ccyPair ) );
            assertEquals ( "YYY/ZZZ", tradeMBean.getCurrencyPairAliasInSTP ( org1, ccyPair ) );
            assertEquals ( "AAA/BBB", tradeMBean.getCurrencyPairAliasInSTP ( org2, ccyPair ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseCurrencyPairAliasInSTP" );
        }
    }

    public void testUseCurrencyAliasInSTP ( )
    {
        try
        {
            String ccy = "EUR";
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( null, ccy ) );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( org1, ccy ) );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( org2, ccy ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCY_ALIAS, "USD~PPP;EUR~XXX", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( null, ccy ) );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( org1, ccy ) );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( org2, ccy ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCY_ALIAS, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( null, ccy ) );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( org1, ccy ) );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( org2, ccy ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCY_ALIAS_PREFIX + "FI1", "USD~PPP;EUR~YYY", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCY_ALIAS_PREFIX + "FI2", "USD~PPP;EUR~AAA", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( null, ccy ) );
            assertEquals ( "YYY", tradeMBean.getCurrencyAliasInSTP ( org1, ccy ) );
            assertEquals ( "AAA", tradeMBean.getCurrencyAliasInSTP ( org2, ccy ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCY_ALIAS_PREFIX + "FI1", "EUR~YYY", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_USE_CURRENCY_ALIAS_PREFIX + "FI2", "EUR~AAA", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getCurrencyAliasInSTP ( null, ccy ) );
            assertEquals ( "YYY", tradeMBean.getCurrencyAliasInSTP ( org1, ccy ) );
            assertEquals ( "AAA", tradeMBean.getCurrencyAliasInSTP ( org2, ccy ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseCurrencyAliasInSTP" );
        }
    }

    public void testUseDetailInstrumentTypesEnabledInFIXSTP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org1 ) );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org2 ) );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org2 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_USE_DETAIL_INSTRUMENT_TYPES, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( org2 ) );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFIXSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseDetailInstrumentTypesEnabledInFIXSTP" );
        }
    }

    public void testUseDetailInstrumentTypesEnabledInFinXMLV25STP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_INSTRUMENT_TYPES, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_INSTRUMENT_TYPES, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_INSTRUMENT_TYPES_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_INSTRUMENT_TYPES, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );
            assertTrue ( tradeMBean.isUseDetailInstrumentTypesEnabledInFinXMLV25STP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseDetailInstrumentTypesEnabledInFinXMLV25STP" );
        }
    }

    public void testInstrumentTypesEnabledInFinXMLV25STP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertFalse ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPES_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertTrue ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );
            assertTrue ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPES_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertFalse ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );
            assertFalse ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPES_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPES_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertFalse ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPES_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org1 ) );
            assertFalse ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( org2 ) );
            assertTrue ( tradeMBean.isInstrumentTypesEnabledInFinXMLV25STP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseDetailInstrumentTypesEnabledInFinXMLV25STP" );
        }
    }

    public void testInstrumentMappingInFIXSTP ( )
    {
        try
        {
            String ccyClsf = "FOR";
            String metalClsf = "ETC";
            String energyClsf = "ETC";
            String cryptoClsf = "CRYP";
            String indexClsf = "CFD";
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CRYPTO_CLSF ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING, "CURRENCY~FOR1;METAL~ETC1;INDEX~CFD1;ENERGY~ETC1;CRYPTO~CRYP1", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ccyClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf + "1", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CRYPTO_CLSF ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CRYPTO_CLSF ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + "FI1", "CURRENCY~FOR2;METAL~ETC2;INDEX~CFD2;ENERGY~ETC2;CRYPTO~CRYP2", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + "FI2", "CURRENCY~FOR3;METAL~ETC3;INDEX~CFD3;ENERGY~ETC3;CRYPTO~CRYP3", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFIXSTP ( null, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf + "2", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf + "2", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "2", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "2", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf + "2", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf + "3", tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf + "3", tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "3", tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "3", tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf + "3", tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.CRYPTO_CLSF ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + "FI1", "METAL~ETC4;INDEX~CFD4;", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_INSTRUMENT_TYPE_MAPPING_PREFIX + "FI2", "METAL~ETC5;INDEX~CFD5;", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "4", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "4", tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFIXSTP ( org1, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "5", tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "5", tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFIXSTP ( org2, InstrumentClassification.CRYPTO_CLSF ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testInstrumentMappingInFIXSTP" );
        }
    }

    public void testInstrumentMappingInFinXMLV25STP ( )
    {
        try
        {
            String ccyClsf = "FOR";
            String metalClsf = "ETC";
            String energyClsf = "ETC";
            String cryptoClsf = "CRYP";
            String indexClsf = "CFD";
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CRYPTO_CLSF ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPE_MAPPING, "CURRENCY~FOR1;METAL~ETC1;INDEX~CFD1;ENERGY~ETC1;CRYPTO~CRYP1", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ccyClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf + "1", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CRYPTO_CLSF ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPE_MAPPING, "", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CRYPTO_CLSF ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPE_MAPPING_PREFIX + "FI1", "CURRENCY~FOR2;METAL~ETC2;INDEX~CFD2;ENERGY~ETC2;CRYPTO~CRYP2", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPE_MAPPING_PREFIX + "FI2", "CURRENCY~FOR3;METAL~ETC3;INDEX~CFD3;ENERGY~ETC3;CRYPTO~CRYP3", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( ccyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf, tradeMBean.getInstrumentMappingInFinXMLV25STP ( null, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf + "2", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf + "2", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "2", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "2", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf + "2", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( ccyClsf + "3", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( energyClsf + "3", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "3", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "3", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( cryptoClsf + "3", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.CRYPTO_CLSF ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPE_MAPPING_PREFIX + "FI1", "METAL~ETC4;INDEX~CFD4;", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_INSTRUMENT_TYPE_MAPPING_PREFIX + "FI2", "METAL~ETC5;INDEX~CFD5;", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "4", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "4", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org1, InstrumentClassification.CRYPTO_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.CURRENCY_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.ENERGY_CLSF ) );
            assertEquals ( indexClsf + "5", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.INDEX_CLSF ) );
            assertEquals ( metalClsf + "5", tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.METAL_CLSF ) );
            assertEquals ( null, tradeMBean.getInstrumentMappingInFinXMLV25STP ( org2, InstrumentClassification.CRYPTO_CLSF ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testInstrumentMappingInFinXMLV25STP" );
        }
    }

    public void testUseDetailTradeTypesEnabledForFinXMLV25SStp ( )
    {
        try
        {
            assertFalse ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_TRADETYPES, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org1 ) );
            assertTrue ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org2 ) );
            assertTrue ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_TRADETYPES, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org2 ) );
            assertFalse ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_TRADETYPES_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_TRADETYPES_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FINXMLV25_USE_DETAIL_TRADETYPES, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org1 ) );
            assertFalse ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( org2 ) );
            assertTrue ( tradeMBean.isUseDetailTradeTypesEnabledForFinXMLV25Stp ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseDetailTradeTypesEnabledForFinXMLV25SStp" );
        }
    }

    public void testSkipRFSCreditCheckTradeTypes ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        try
        {
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, "FXSpot,FXOutright", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_OR ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SWAP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, "FXOutright", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + "FI1", "FXSpot", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + "FI2", "FXOutright", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_OR ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, "FXNDF", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSkipRFSCreditCheckTradeTypes" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + org1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + org2.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testSkipRFSCreditCheckTradeTypesAtBrokerLevel ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker1 );
        try
        {
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, "FXSpot,FXOutright", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, "FXOutright", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_BROKER_CUSTOMERS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + "Broker1", "FXSpot,FXNDF", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_RFS_BROKER_CUSTOMERS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, "FXOutright", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_OR ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + "FI1", "FXNDF", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + "FI2", "FXSpot", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_SP ) );
            assertTrue ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, "FXOutright", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isRFSCreditCheckSkipEnabled ( org1, ISCommonConstants.TRD_CLSF_FXNDF ) );
            assertTrue ( tradeMBean.isRFSCreditCheckSkipEnabled ( org2, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isRFSCreditCheckSkipEnabled ( null, ISCommonConstants.TRD_CLSF_SP ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSkipRFSCreditCheckTradeTypesAtBrokerLevel" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker1 );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + org1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_RFS_BROKER_CUSTOMERS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_RFS_SKIP_CREDIT_CHECK_TRADE_TYPES_LIST_PREFIX + org2.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testStpSuppressNegativeSpreadsEnabledTradeTypesAtBrokerLevel ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization broker2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker2" );
        try
        {
            //Without setting the property checking
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( null, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, null ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SP ) );

            //Now set the property at broker1
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TRADE_TYPES_PREFIX + "Broker1", "FXSpot", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_OR ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SWAP ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_FWD ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TRADE_TYPES_PREFIX + "Broker1", "FXSpotFwd", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SWAP ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_OR ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_FXNDF ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_FWD ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TRADE_TYPES_PREFIX + "Broker1", "FXSpot,FXOutright,FXSpotFwd,FXNDF,FXFwdFwd", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SP ) );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_OR ) );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SWAP ) );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_FWD ) );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_FXNDF ) );

            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker2, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker2, ISCommonConstants.TRD_CLSF_SWAP ) );

            //Set property to golbal and see it should not take effect
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TRADE_TYPES_PREFIX + "Broker1", "FXSpot,FXOutright", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( "IDC.STP.Suppress.Negative.Spreads.Enabled.Trade.Types.List", "FXSpotFwd,FXNDF,FXFwdFwd", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SP ) );
            assertTrue ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_OR ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SWAP ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_FWD ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_FXNDF ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TRADE_TYPES_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( "IDC.STP.Suppress.Negative.Spreads.Enabled.Trade.Types.List", ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SP ) );
            assertFalse ( tradeMBean.isSTPSuppressNegativeSpreadsEnabledForTradeType ( broker1, ISCommonConstants.TRD_CLSF_SWAP ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testStpSuppressNegativeSpreadsEnabledTradeTypesAtBrokerLevel" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( broker2 );
            IdcUtilC.refreshObject ( broker1 );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TRADE_TYPES_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( "IDC.STP.Suppress.Negative.Spreads.Enabled.Trade.Types.List", ConfigurationProperty.DYNAMIC_SCOPE );

        }
    }


    public void testStpSuppressNegativeSpreadsEnabledTagsListAtBrokerLevel ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization broker2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker2" );
        try
        {
            //Without setting the property checking
            assertNull ( null, tradeMBean.getSTPSpreadTags ( null ) );
            assertNull ( null, tradeMBean.getSTPSpreadTags ( broker1 ) );

            //Set the property value at BA level
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TAGS_LIST_PREFIX + "Broker1", "PMMinSprd,PMMaxSprd", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TAGS_LIST, "PPSpotSprd,PPCustSprd", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( "[PMMinSprd, PMMaxSprd]", tradeMBean.getSTPSpreadTags ( broker1 ).toString () );
            assertNull ( null, tradeMBean.getSTPSpreadTags ( broker2 ) );
            assertNull ( null, tradeMBean.getSTPSpreadTags ( null ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testStpSuppressNegativeSpreadsEnabledTagsListAtBrokerLevel" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_SUPPRESS_NEGATIVE_SPREADS_ENABLED_TAGS_LIST_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( "IDC.STP.Suppress.Negative.Spreads.Enabled.Tags.List", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testParentTradeTxIdSTPEnabled ( )
    {
        try
        {
            assertFalse ( tradeMBean.isParentTradeTxIdSTPEnabled ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isParentTradeTxIdSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isParentTradeTxIdSTPEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PARENT_TRADETXID_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isParentTradeTxIdSTPEnabled ( org1 ) );
            assertTrue ( tradeMBean.isParentTradeTxIdSTPEnabled ( org2 ) );
            assertTrue ( tradeMBean.isParentTradeTxIdSTPEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PARENT_TRADETXID_STP, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isParentTradeTxIdSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isParentTradeTxIdSTPEnabled ( org2 ) );
            assertFalse ( tradeMBean.isParentTradeTxIdSTPEnabled ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PARENT_TRADETXID_STP_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PARENT_TRADETXID_STP_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isParentTradeTxIdSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isParentTradeTxIdSTPEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PARENT_TRADETXID_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isParentTradeTxIdSTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isParentTradeTxIdSTPEnabled ( org2 ) );
            assertTrue ( tradeMBean.isParentTradeTxIdSTPEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testParentTradeTxIdSTPEnabled" );
        }
    }

    public void testUseMT300Field72inJSONSTP ( )
    {
        try
        {
            assertFalse ( tradeMBean.isUseMT300Field72inJSONSTP ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertFalse ( tradeMBean.isUseMT300Field72inJSONSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseMT300Field72inJSONSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_JSON_USE_TAG_MT300FIELD72, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseMT300Field72inJSONSTP ( org1 ) );
            assertTrue ( tradeMBean.isUseMT300Field72inJSONSTP ( org2 ) );
            assertTrue ( tradeMBean.isUseMT300Field72inJSONSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_JSON_USE_TAG_MT300FIELD72, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseMT300Field72inJSONSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseMT300Field72inJSONSTP ( org2 ) );
            assertFalse ( tradeMBean.isUseMT300Field72inJSONSTP ( null ) );


            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_JSON_USE_TAG_MT300FIELD72_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_JSON_USE_TAG_MT300FIELD72_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseMT300Field72inJSONSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseMT300Field72inJSONSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_JSON_USE_TAG_MT300FIELD72, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseMT300Field72inJSONSTP ( org1 ) );
            assertFalse ( tradeMBean.isUseMT300Field72inJSONSTP ( org2 ) );
            assertTrue ( tradeMBean.isUseMT300Field72inJSONSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUseMT300Field72inJSONSTP" );
        }
    }

    public void testIsCopyParentChannel ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        try
        {
            assertFalse ( tradeMBean.isCopyParentChannel ( null ) );
            assertFalse ( tradeMBean.isCopyParentChannel ( org1 ) );
            assertFalse ( tradeMBean.isCopyParentChannel ( broker1 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isCopyParentChannel ( org1 ) );
            assertTrue ( tradeMBean.isCopyParentChannel ( broker1 ) );
            assertTrue ( tradeMBean.isCopyParentChannel ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isCopyParentChannel ( org1 ) );
            assertFalse ( tradeMBean.isCopyParentChannel ( broker1 ) );
            assertFalse ( tradeMBean.isCopyParentChannel ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isCopyParentChannel ( org1 ) );
            assertTrue ( tradeMBean.isCopyParentChannel ( broker1 ) );
            assertFalse ( tradeMBean.isCopyParentChannel ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED_PREFIX + "Broker1", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isCopyParentChannel ( org1 ) );
            assertFalse ( tradeMBean.isCopyParentChannel ( broker1 ) );
            assertTrue ( tradeMBean.isCopyParentChannel ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( tradeMBean.isCopyParentChannel ( org1 ) );
            assertTrue ( tradeMBean.isCopyParentChannel ( broker1 ) );
            assertTrue ( tradeMBean.isCopyParentChannel ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_IS_TRADE_BATCH_AMEND_ALLOCATE_ROLL_COPY_PARENT_CHANNEL_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse ( tradeMBean.isCopyParentChannel ( org1 ) );
            assertFalse ( tradeMBean.isCopyParentChannel ( broker1 ) );
            assertFalse ( tradeMBean.isCopyParentChannel ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testIsCopyParentChannel" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( broker1 );
        }
    }

    public void testTradeCustomParametersEnabled ( )
    {
        try
        {
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            // now set the global property. it should not have any impact.
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST, "PURPOSE,SUB_PURPOSE", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST, ConfigurationProperty.DYNAMIC_SCOPE );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "FI1", "PURPOSE,SUB_PURPOSE,Custodian", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            assertNotNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertEquals ( 3, tradeMBean.getTradeCustomParameters ( org1 ).size () );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeCustomParametersEnabled" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testTradeCustomParametersEnabledAtBrokerLevel ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        Organization broker = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        org1.setBrokerOrganization ( broker );
        try
        {
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "Broker1", "PURPOSE,SUB_PURPOSE,Custodian", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            assertNotNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertEquals ( 3, tradeMBean.getTradeCustomParameters ( org1 ).size () );
            assertNotNull ( tradeMBean.getTradeCustomParameters ( broker ) );
            assertEquals ( 3, tradeMBean.getTradeCustomParameters ( broker ).size () );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeCustomParametersEnabledAtBrokerLevel" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testTradeCustomParametersInputDetailProperty ( )
    {
        try
        {
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "FI1", "PURPOSE,SUB_PURPOSE,Custodian", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            assertNotNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertEquals ( 3, tradeMBean.getTradeCustomParameters ( org1 ).size () );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            String propVal = "Default=Payment,Display=Purpose,ListOfValues=[100~Hundred;200~TwoHundred],Mandatory=No,Symbols=MYR";
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETER_INPUT_DETAIL_PREFIX + org1.getShortName () + ".PURPOSE", propVal, ConfigurationProperty.DYNAMIC_SCOPE, null );
            Map<String, String> param1Map = tradeMBean.getTradeCustomParameterInputDetails ( org1, "PURPOSE" );
            assertNotNull ( param1Map );
            assertFalse ( param1Map.isEmpty () );
            assertEquals ( "Payment", param1Map.get ( TradeConfigurationMBean.CustomParameterInputAspect.Default.toString () ) );
            assertEquals ( "Purpose", param1Map.get ( TradeConfigurationMBean.CustomParameterInputAspect.Display.toString () ) );
            assertEquals ( "No", param1Map.get ( TradeConfigurationMBean.CustomParameterInputAspect.Mandatory.toString () ) );
            assertEquals ( "MYR", param1Map.get ( TradeConfigurationMBean.CustomParameterInputAspect.Symbols.toString () ) );

            Map<String, String> param2Map = tradeMBean.getTradeCustomParameterInputDetails ( org2, "PURPOSE" );
            assertNull ( param2Map );
            Map<String, String> param3Map = tradeMBean.getTradeCustomParameterInputDetails ( org1, "SUB_PURPOSE" );
            assertNull ( param3Map );

            assertEquals ( "Payment", tradeMBean.getTradeCustomParameterInputValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterInputAspect.Default ) );
            assertEquals ( "Purpose", tradeMBean.getTradeCustomParameterInputValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterInputAspect.Display ) );
            assertEquals ( "No", tradeMBean.getTradeCustomParameterInputValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterInputAspect.Mandatory ) );
            assertEquals ( "MYR", tradeMBean.getTradeCustomParameterInputValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterInputAspect.Symbols ) );

            String inputPropParam1 = tradeMBean.getTradeCustomParameterInputDetailsProperty ( org1, "PURPOSE" );
            assertEquals ( propVal, inputPropParam1 );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeCustomParametersEnabled" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETER_INPUT_DETAIL_PREFIX + "FI1" + ".PURPOSE", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testTradeCustomParametersInputDetailPropertyAtBrokerLevel ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        Organization broker = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        try
        {
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            org1.setBrokerOrganization ( broker );
            assertNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + broker.getShortName (), "PURPOSE,SUB_PURPOSE,Custodian", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            assertNotNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertEquals ( 3, tradeMBean.getTradeCustomParameters ( org1 ).size () );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            String propVal = "Default=Payment,Display=Purpose,ListOfValues=[100~Hundred;200~TwoHundred],Mandatory=No,Symbols=MYR";
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETER_INPUT_DETAIL_PREFIX + broker.getShortName () + ".PURPOSE", propVal, ConfigurationProperty.DYNAMIC_SCOPE, null );
            Map<String, String> param1Map = tradeMBean.getTradeCustomParameterInputDetails ( org1, "PURPOSE" );
            assertNotNull ( param1Map );
            assertFalse ( param1Map.isEmpty () );
            assertEquals ( "Payment", param1Map.get ( TradeConfigurationMBean.CustomParameterInputAspect.Default.toString () ) );
            assertEquals ( "Purpose", param1Map.get ( TradeConfigurationMBean.CustomParameterInputAspect.Display.toString () ) );
            assertEquals ( "No", param1Map.get ( TradeConfigurationMBean.CustomParameterInputAspect.Mandatory.toString () ) );
            assertEquals ( "MYR", param1Map.get ( TradeConfigurationMBean.CustomParameterInputAspect.Symbols.toString () ) );

            Map<String, String> param2Map = tradeMBean.getTradeCustomParameterInputDetails ( org2, "PURPOSE" );
            assertNull ( param2Map );
            Map<String, String> param3Map = tradeMBean.getTradeCustomParameterInputDetails ( org1, "SUB_PURPOSE" );
            assertNull ( param3Map );

            assertEquals ( "Payment", tradeMBean.getTradeCustomParameterInputValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterInputAspect.Default ) );
            assertEquals ( "Purpose", tradeMBean.getTradeCustomParameterInputValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterInputAspect.Display ) );
            assertEquals ( "No", tradeMBean.getTradeCustomParameterInputValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterInputAspect.Mandatory ) );
            assertEquals ( "MYR", tradeMBean.getTradeCustomParameterInputValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterInputAspect.Symbols ) );

            String inputPropParam1 = tradeMBean.getTradeCustomParameterInputDetailsProperty ( org1, "PURPOSE" );
            assertEquals ( propVal, inputPropParam1 );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeCustomParametersInputDetailPropertyAtBrokerLevel" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETER_INPUT_DETAIL_PREFIX + "Broker1" + ".PURPOSE", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testTradeCustomParametersWorkflowDetailProperty ( )
    {
        try
        {
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
            assertNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + org1.getShortName (), "PURPOSE,SUB_PURPOSE,Custodian", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            assertNotNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertEquals ( 3, tradeMBean.getTradeCustomParameters ( org1 ).size () );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            String propVal = "STP_FIX=No,STP_FINXML=Yes,GM=No,CDQ=No";
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETER_WORKFLOW_DETAIL_PREFIX + org1.getShortName () + ".PURPOSE", propVal, ConfigurationProperty.DYNAMIC_SCOPE, null );
            Map<String, String> param1Map = tradeMBean.getTradeCustomParameterWorkflowDetails ( org1, "PURPOSE" );
            assertNotNull ( param1Map );
            assertFalse ( param1Map.isEmpty () );
            assertEquals ( "No", param1Map.get ( TradeConfigurationMBean.CustomParameterWorkflowAspect.STP_FIX.toString () ) );
            assertEquals ( "Yes", param1Map.get ( TradeConfigurationMBean.CustomParameterWorkflowAspect.STP_FINXML.toString () ) );
            assertEquals ( "No", param1Map.get ( TradeConfigurationMBean.CustomParameterWorkflowAspect.GM.toString () ) );
            assertEquals ( "No", param1Map.get ( TradeConfigurationMBean.CustomParameterWorkflowAspect.CDQ.toString () ) );

            Map<String, String> param2Map = tradeMBean.getTradeCustomParameterWorkflowDetails ( org2, "PURPOSE" );
            assertNull ( param2Map );
            Map<String, String> param3Map = tradeMBean.getTradeCustomParameterWorkflowDetails ( org1, "SUB_PURPOSE" );
            assertNull ( param3Map );

            assertEquals ( "No", tradeMBean.getTradeCustomParameterWorkflowValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterWorkflowAspect.STP_FIX ) );
            assertEquals ( "Yes", tradeMBean.getTradeCustomParameterWorkflowValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterWorkflowAspect.STP_FINXML ) );
            assertEquals ( "No", tradeMBean.getTradeCustomParameterWorkflowValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterWorkflowAspect.GM ) );
            assertEquals ( "No", tradeMBean.getTradeCustomParameterWorkflowValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterWorkflowAspect.CDQ ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeCustomParametersWorkflowDetailProperty" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETER_WORKFLOW_DETAIL_PREFIX + "FI1" + ".PURPOSE", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testTradeCustomParametersWorkflowDetailPropertyAtBrokerLevel ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        Organization broker = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        try
        {
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            org1.setBrokerOrganization ( broker );
            assertNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + broker.getShortName (), "PURPOSE,SUB_PURPOSE,Custodian", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getTradeCustomParameters ( null ) );
            assertNotNull ( tradeMBean.getTradeCustomParameters ( org1 ) );
            assertEquals ( 3, tradeMBean.getTradeCustomParameters ( org1 ).size () );
            assertNull ( tradeMBean.getTradeCustomParameters ( org2 ) );

            String propVal = "STP_FIX=Yes,STP_FINXML=No,GM=Yes,CDQ=Yes";
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETER_WORKFLOW_DETAIL_PREFIX + broker.getShortName () + ".PURPOSE", propVal, ConfigurationProperty.DYNAMIC_SCOPE, null );
            Map<String, String> param1Map = tradeMBean.getTradeCustomParameterWorkflowDetails ( org1, "PURPOSE" );
            assertNotNull ( param1Map );
            assertFalse ( param1Map.isEmpty () );
            assertEquals ( "Yes", param1Map.get ( TradeConfigurationMBean.CustomParameterWorkflowAspect.STP_FIX.toString () ) );
            assertEquals ( "No", param1Map.get ( TradeConfigurationMBean.CustomParameterWorkflowAspect.STP_FINXML.toString () ) );
            assertEquals ( "Yes", param1Map.get ( TradeConfigurationMBean.CustomParameterWorkflowAspect.GM.toString () ) );
            assertEquals ( "Yes", param1Map.get ( TradeConfigurationMBean.CustomParameterWorkflowAspect.CDQ.toString () ) );

            Map<String, String> param2Map = tradeMBean.getTradeCustomParameterWorkflowDetails ( org2, "PURPOSE" );
            assertNull ( param2Map );
            Map<String, String> param3Map = tradeMBean.getTradeCustomParameterWorkflowDetails ( org1, "SUB_PURPOSE" );
            assertNull ( param3Map );

            assertEquals ( "Yes", tradeMBean.getTradeCustomParameterWorkflowValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterWorkflowAspect.STP_FIX ) );
            assertEquals ( "No", tradeMBean.getTradeCustomParameterWorkflowValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterWorkflowAspect.STP_FINXML ) );
            assertEquals ( "Yes", tradeMBean.getTradeCustomParameterWorkflowValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterWorkflowAspect.GM ) );
            assertEquals ( "Yes", tradeMBean.getTradeCustomParameterWorkflowValue ( org1, "PURPOSE", TradeConfigurationMBean.CustomParameterWorkflowAspect.CDQ ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testTradeCustomParametersWorkflowDetailPropertyAtBrokerLevel" );
        }
        finally
        {
            IdcUtilC.refreshObject ( org1 );
            IdcUtilC.refreshObject ( org2 );
            IdcUtilC.refreshObject ( broker );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETERS_LIST_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_CUSTOM_PARAMETER_WORKFLOW_DETAIL_PREFIX + "Broker1" + ".PURPOSE", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }


    public void testIsSTPNoteForCancelEnabled ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        try
        {
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( null ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( broker1 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_STP_NOTE_WORKFLOW_TRIGGERED_CANCEL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_STP_NOTE_WORKFLOW_TRIGGERED_CANCEL_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_STP_NOTE_WORKFLOW_TRIGGERED_CANCEL_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_STP_NOTE_WORKFLOW_TRIGGERED_CANCEL_ENABLED_PREFIX + "Broker1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSTPNoteForCancelEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( fi1 ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_STP_NOTE_WORKFLOW_TRIGGERED_CANCEL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_IS_STP_NOTE_WORKFLOW_TRIGGERED_CANCEL_ENABLED_PREFIX + "Broker1", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( fi1 ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_IS_STP_NOTE_WORKFLOW_TRIGGERED_CANCEL_ENABLED_PREFIX + "Broker1", ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_IS_STP_NOTE_WORKFLOW_TRIGGERED_CANCEL_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isSTPNoteForCancelEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testIsSTPNoteForCancelEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject ( fi1 );
            IdcUtilC.refreshObject ( broker1 );
        }
    }

    public void testNetTradeDealtCurrencyProperty ( )
    {
        Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization fi2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        try
        {
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( null ) );
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( fi1 ) );
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( fi2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_NETTING_NETTRADE_DEALT_CURRENCY, "USD", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( null ) );
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( fi1 ) );
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( fi2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_NETTING_NETTRADE_DEALT_CURRENCY_PREFIX + fi1.getShortName (), "EUR", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( null ) );
            assertEquals ( "EUR", tradeMBean.getNetTradeDealtCurrency ( fi1 ) );
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( fi2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_NETTING_NETTRADE_DEALT_CURRENCY_PREFIX + fi2.getShortName (), "GBP", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertNull ( tradeMBean.getNetTradeDealtCurrency ( null ) );
            assertEquals ( "EUR", tradeMBean.getNetTradeDealtCurrency ( fi1 ) );
            assertEquals ( "GBP", tradeMBean.getNetTradeDealtCurrency ( fi2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testNetTradeDealtCurrencyProperty" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_NETTING_NETTRADE_DEALT_CURRENCY, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_NETTING_NETTRADE_DEALT_CURRENCY_PREFIX + fi1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_NETTING_NETTRADE_DEALT_CURRENCY_PREFIX + fi2.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testVerificationSTPSkipEnabled ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        try
        {
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( null ) );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( org1 ) );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_VERIFICATION_STP_SKIP_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( null ) );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( org1 ) );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_VERIFICATION_STP_SKIP_ENABLED_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( null ) );
            assertTrue ( tradeMBean.isVerificationSTPSkipEnabled ( org1 ) );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_VERIFICATION_STP_SKIP_ENABLED_PREFIX + org1.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_VERIFICATION_STP_SKIP_ENABLED_PREFIX + org2.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( null ) );
            assertFalse ( tradeMBean.isVerificationSTPSkipEnabled ( org1 ) );
            assertTrue ( tradeMBean.isVerificationSTPSkipEnabled ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testVerificationSTPSkipEnabled" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_VERIFICATION_STP_SKIP_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_VERIFICATION_STP_SKIP_ENABLED_PREFIX + org1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_VERIFICATION_STP_SKIP_ENABLED_PREFIX + org2.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testProrataForwardSupportEnabled ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        try
        {
            fi1.setBrokerOrganization ( broker1 );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( null ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( fi1 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( broker1 ) );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( fi1 ) );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( fi1 ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED_PREFIX + broker1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( broker1 ) );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( fi1 ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED_PREFIX + broker1.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( fi1 ) );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( broker1 ) );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( fi1 ) );
            assertTrue ( tradeMBean.isProrataForwardSupportEnabled ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( fi1 ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED_PREFIX + fi1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( broker1 ) );
            assertTrue( tradeMBean.isProrataForwardSupportEnabled ( fi1 ) );
            assertFalse ( tradeMBean.isProrataForwardSupportEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testProrataForwardSupportEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject ( fi1 );
            IdcUtilC.refreshObject ( broker1 );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED_PREFIX + fi1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_SUPPORT_ENABLED_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testProrataForwardTenorList ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        try
        {
            Collection<String> tenors1 = tradeMBean.getProrataForwardTenorsList ( broker1 );
            assertNotNull ( tenors1 );
            assertTrue ( tenors1.size () > 0 );

            Collection<String> tenors2 = tradeMBean.getProrataForwardTenorsList ( fi1 );
            assertNotNull ( tenors2 );
            assertTrue ( tenors2.size () > 0 );

            Collection<Tenor> tenors3 = tradeMBean.getProrataForwardTenors ( broker1 );
            assertNotNull ( tenors3 );
            assertTrue ( tenors3.size () > 0 );
            assertEquals ( tenors1.size (), tenors3.size () );

            Map<String, TenorBand> tenorMap1 = tradeMBean.getProrataForwardTenorBands ( tradeMBean.getProrataForwardTenors ( broker1 ) );
            assertNotNull ( tenorMap1 );
            assertTrue ( tenorMap1.size () > 0 );
            assertEquals ( 12, tenorMap1.size () );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_TENORS_LIST_PREFIX + broker1.getShortName (), "1D,2D,3D,1M", ConfigurationProperty.DYNAMIC_SCOPE, null );

            Collection<String> tenors4 = tradeMBean.getProrataForwardTenorsList ( broker1 );
            assertNotNull ( tenors4 );
            assertEquals ( 4, tenors4.size () );

            Collection<Tenor> tenors5 = tradeMBean.getProrataForwardTenors ( broker1 );
            assertNotNull ( tenors5 );
            assertEquals ( 4, tenors5.size () );

            Map<String, TenorBand> tenorMap2 = tradeMBean.getProrataForwardTenorBands ( tradeMBean.getProrataForwardTenors ( broker1 ) );
            assertNotNull ( tenorMap2 );
            assertEquals ( tenors4.size (), tenorMap2.size () );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testProrataForwardTenorList" );
        }
    }

    public void testProrataForwardCurrencyPairList ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        try
        {
            Collection<String> ccyPairs1 = tradeMBean.getProrataForwardCurrencyPairsList ( broker1 );
            assertNotNull ( ccyPairs1 );
            assertTrue ( ccyPairs1.isEmpty () );

            Collection<String> ccyPairs2 = tradeMBean.getProrataForwardCurrencyPairsList ( fi1 );
            assertNotNull ( ccyPairs2 );
            assertTrue ( ccyPairs2.isEmpty () );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_CURRENCYPAIRS_LIST, "EUR/USD,USD/CHF", ConfigurationProperty.DYNAMIC_SCOPE, null );

            Collection<String> ccyPairs3 = tradeMBean.getProrataForwardCurrencyPairsList ( broker1 );
            assertNotNull ( ccyPairs3 );
            assertEquals ( 2, ccyPairs3.size () );

            Collection<String> ccyPairs4 = tradeMBean.getProrataForwardCurrencyPairsList ( fi1 );
            assertNotNull ( ccyPairs4 );
            assertEquals ( 2, ccyPairs4.size () );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_CURRENCYPAIRS_LIST_PREFIX + broker1.getShortName (), "EUR/USD,USD/CHF,USD/JPY", ConfigurationProperty.DYNAMIC_SCOPE, null );

            Collection<String> ccyPairs5 = tradeMBean.getProrataForwardCurrencyPairsList ( broker1 );
            assertNotNull ( ccyPairs5 );
            assertEquals ( 3, ccyPairs5.size () );

            Collection<String> ccyPairs6 = tradeMBean.getProrataForwardCurrencyPairsList ( fi1 );
            assertNotNull ( ccyPairs6 );
            assertEquals ( 2, ccyPairs6.size () );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testProrataForwardCurrencyPairList" );
        }
    }

    public void testProrataForwardResidualDays ( )
    {
        try
        {
            Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            assertEquals ( tradeMBean.getProrataForwardResidualDays ( broker1 ), 7 );
            assertEquals ( tradeMBean.getProrataForwardResidualDays ( fi1 ), 7 );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_RESIDUAL_DAYS, "9", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getProrataForwardResidualDays ( broker1 ), 9 );
            assertEquals ( tradeMBean.getProrataForwardResidualDays ( fi1 ), 9 );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_RESIDUAL_DAYS_PREFIX + broker1.getShortName (), "3", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getProrataForwardResidualDays ( broker1 ), 3 );
            assertEquals ( tradeMBean.getProrataForwardResidualDays ( fi1 ), 9 );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testProrataForwardResidualDays" );
        }
    }

    public void testProrataForwardMaxTenor ( )
    {
        try
        {
            Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            assertEquals ( tradeMBean.getProrataForwardMaxTenor ( broker1 ), TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_MAX_TENOR_DEFAULT );
            assertEquals ( tradeMBean.getProrataForwardMaxTenor ( fi1 ), TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_MAX_TENOR_DEFAULT );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_MAX_TENOR, "9M", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getProrataForwardMaxTenor ( broker1 ), "9M" );
            assertEquals ( tradeMBean.getProrataForwardMaxTenor ( fi1 ), "9M" );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_MAX_TENOR_PREFIX + broker1.getShortName (), "3M", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getProrataForwardMaxTenor ( broker1 ), "3M" );
            assertEquals ( tradeMBean.getProrataForwardMaxTenor ( fi1 ), "9M" );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testProrataForwardMaxTenor" );
        }
    }

    public void testProrataForwardMinTenor ( )
    {
        try
        {
            Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            assertEquals ( tradeMBean.getProrataForwardMinTenor ( broker1 ), TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_MIN_TENOR_DEFAULT );
            assertEquals ( tradeMBean.getProrataForwardMinTenor ( fi1 ), TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_MIN_TENOR_DEFAULT );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_MIN_TENOR, "1M", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getProrataForwardMinTenor ( broker1 ), "1M" );
            assertEquals ( tradeMBean.getProrataForwardMinTenor ( fi1 ), "1M" );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATAFORWARD_MIN_TENOR_PREFIX + broker1.getShortName (), "3M", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getProrataForwardMinTenor ( broker1 ), "3M" );
            assertEquals ( tradeMBean.getProrataForwardMinTenor ( fi1 ), "1M" );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testProrataForwardMinTenor" );
        }
    }

    public void testShowPricingTypeInFIXSTP ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        try
        {
            assertFalse ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( null ) );
            assertFalse ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( org1 ) );
            assertFalse ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( null ) );
            assertTrue ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( org1 ) );
            assertTrue ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( null ) );
            assertTrue ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( org1 ) );
            assertTrue ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED_PREFIX + org1.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED_PREFIX + org2.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( null ) );
            assertFalse ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( org1 ) );
            assertTrue ( tradeMBean.isShowPricingTypeEnabledInFIXSTP ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testShowPricingTypeInFIXSTP" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED_PREFIX + org1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_FIX_PRICING_TYPE_ENABLED_PREFIX + org2.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testShowRTNUPIInFIXSTP ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        try
        {
            assertFalse ( tradeMBean.isRTNAndUPISTPEnabled ( null ) );
            assertFalse ( tradeMBean.isRTNAndUPISTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isRTNAndUPISTPEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_RTN_UPI_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isRTNAndUPISTPEnabled ( null ) );
            assertTrue ( tradeMBean.isRTNAndUPISTPEnabled ( org1 ) );
            assertTrue ( tradeMBean.isRTNAndUPISTPEnabled ( org2 ) );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_RTN_UPI_STP, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_RTN_UPI_STP_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isRTNAndUPISTPEnabled ( null ) );
            assertTrue ( tradeMBean.isRTNAndUPISTPEnabled ( org1 ) );
            assertFalse ( tradeMBean.isRTNAndUPISTPEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_RTN_UPI_STP_PREFIX + org1.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_RTN_UPI_STP_PREFIX + org2.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isRTNAndUPISTPEnabled ( null ) );
            assertFalse ( tradeMBean.isRTNAndUPISTPEnabled ( org1 ) );
            assertTrue ( tradeMBean.isRTNAndUPISTPEnabled ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testShowRTNUPIInFIXSTP" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_RTN_UPI_STP, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_RTN_UPI_STP_PREFIX + org1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_RTN_UPI_STP_PREFIX + org2.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testShowOldUTIPrefixInFIXSTP ( )
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI2" );
        try
        {
            assertFalse ( tradeMBean.isOldUTIEnabled ( null ) );
            assertFalse ( tradeMBean.isOldUTIEnabled ( org1 ) );
            assertFalse ( tradeMBean.isOldUTIEnabled ( org2 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_OLD_UTI_STP, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isOldUTIEnabled ( null ) );
            assertTrue ( tradeMBean.isOldUTIEnabled ( org1 ) );
            assertTrue ( tradeMBean.isOldUTIEnabled ( org2 ) );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_OLD_UTI_STP, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_OLD_UTI_STP_PREFIX + org1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isOldUTIEnabled ( null ) );
            assertTrue ( tradeMBean.isOldUTIEnabled ( org1 ) );
            assertFalse ( tradeMBean.isOldUTIEnabled ( org2 ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_OLD_UTI_STP_PREFIX + org1.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_OLD_UTI_STP_PREFIX + org2.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isOldUTIEnabled ( null ) );
            assertFalse ( tradeMBean.isOldUTIEnabled ( org1 ) );
            assertTrue ( tradeMBean.isOldUTIEnabled ( org2 ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testShowOldUTIPrefixInFIXSTP" );
        }
        finally
        {
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_OLD_UTI_STP, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_OLD_UTI_STP_PREFIX + org1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_OLD_UTI_STP_PREFIX + org2.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testProrataForwardSpecialClientsMaxTenor ( )
    {
        try
        {
            Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
            assertEquals ( tradeMBean.getProrataForwardSpecialClientsMaxTenor ( broker1 ), TradeConfigurationMBean.IDC_TRADE_PRORATA_FORWARD_SPECIAL_CLIENTS_MAX_TENOR_DEFAULT );
            assertEquals ( tradeMBean.getProrataForwardSpecialClientsMaxTenor ( fi1 ), TradeConfigurationMBean.IDC_TRADE_PRORATA_FORWARD_SPECIAL_CLIENTS_MAX_TENOR_DEFAULT );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATA_FORWARD_SPECIAL_CLIENTS_MAX_TENOR, "9M", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getProrataForwardSpecialClientsMaxTenor ( broker1 ), "9M" );
            assertEquals ( tradeMBean.getProrataForwardSpecialClientsMaxTenor ( fi1 ), "9M" );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATA_FORWARD_SPECIAL_CLIENTS_MAX_TENOR_PREFIX + broker1.getShortName (), "3M", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertEquals ( tradeMBean.getProrataForwardSpecialClientsMaxTenor ( broker1 ), "3M" );
            assertEquals ( tradeMBean.getProrataForwardSpecialClientsMaxTenor ( fi1 ), "9M" );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testProrataForwardSpecialClientsMaxTenor" );
        }
    }

    public void testProrataForwardSpecialClientsTenorList ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        try
        {
            Collection<String> tenors1 = tradeMBean.getProrataForwardSpecialClientsTenorsList ( broker1 );
            assertNotNull ( tenors1 );
            assertTrue ( tenors1.size () > 0 );

            Collection<String> tenors2 = tradeMBean.getProrataForwardSpecialClientsTenorsList ( fi1 );
            assertNotNull ( tenors2 );
            assertTrue ( tenors2.size () > 0 );

            Collection<Tenor> tenors3 = tradeMBean.getProrataForwardSpecialClientsTenors ( broker1 );
            assertNotNull ( tenors3 );
            assertTrue ( tenors3.size () > 0 );
            assertEquals ( tenors1.size (), tenors3.size () );

            Map<String, TenorBand> tenorMap1 = tradeMBean.getProrataForwardTenorBands ( tradeMBean.getProrataForwardSpecialClientsTenors ( broker1 ) );
            assertNotNull ( tenorMap1 );
            assertTrue ( tenorMap1.size () > 0 );
            assertEquals ( 4, tenorMap1.size () );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_PRORATA_FORWARD_SPECIAL_CLIENTS_TENORS_LIST_PREFIX + broker1.getShortName (), "1W,2W,3W,4W", ConfigurationProperty.DYNAMIC_SCOPE, null );

            Collection<String> tenors4 = tradeMBean.getProrataForwardSpecialClientsTenorsList ( broker1 );
            assertNotNull ( tenors4 );
            assertEquals ( 4, tenors4.size () );

            Collection<Tenor> tenors5 = tradeMBean.getProrataForwardSpecialClientsTenors ( broker1 );
            assertNotNull ( tenors5 );
            assertEquals ( 4, tenors5.size () );

            Map<String, TenorBand> tenorMap2 = tradeMBean.getProrataForwardTenorBands ( tradeMBean.getProrataForwardSpecialClientsTenors ( broker1 ) );
            assertNotNull ( tenorMap2 );
            assertEquals ( tenors4.size (), tenorMap2.size () );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testProrataForwardSpecialClientsTenorList" );
        }
    }


    public void testIsRFQSupported() {
        try {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker2" );
            assertFalse(tradeMBean.isRFQSupported(org1));
            assertFalse(tradeMBean.isRFQSupported(org2));

            // now set the global property
            tradeMBean.setProperty(TradeConfigurationMBean.RFQ_SUPPORT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertTrue(tradeMBean.isRFQSupported(org1));
            assertTrue(tradeMBean.isRFQSupported(org2));

            tradeMBean.setProperty(TradeConfigurationMBean.RFQ_SUPPORT_ENABLED_PREFIX + org1.getShortName(), "false", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertFalse(tradeMBean.isRFQSupported(org1));
            assertTrue(tradeMBean.isRFQSupported(org2));

            //remove org and ccy pair property
            tradeMBean.removeProperty(TradeConfigurationMBean.RFQ_SUPPORT_ENABLED_PREFIX + org1.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE);
            assertTrue(tradeMBean.isRFQSupported(org1));
            assertTrue(tradeMBean.isRFQSupported(org2));
            //remove ccy pair property
            tradeMBean.removeProperty(TradeConfigurationMBean.RFQ_SUPPORT_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE);
            assertFalse(tradeMBean.isRFQSupported(org1));
            assertFalse(tradeMBean.isRFQSupported(org2));

        } catch (Exception e) {
            e.printStackTrace();
            fail("testIsRFQSupported");
        }
    }

    public void testIsRoundUsingBigDecimal() {
        try {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker2" );
            assertFalse(tradeMBean.isRoundUsingBigDecimal(org1));
            assertFalse(tradeMBean.isRoundUsingBigDecimal(org2));

            // now set the global property
            tradeMBean.setProperty(TradeConfigurationMBean.PRECISION_USE_BIG_DECIMAL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertTrue(tradeMBean.isRoundUsingBigDecimal(org1));
            assertTrue(tradeMBean.isRoundUsingBigDecimal(org2));

            tradeMBean.setProperty(TradeConfigurationMBean.PRECISION_USE_BIG_DECIMAL_ENABLED_PREFIX + org1.getShortName(), "false", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertFalse(tradeMBean.isRoundUsingBigDecimal(org1));
            assertTrue(tradeMBean.isRoundUsingBigDecimal(org2));

            //remove org and ccy pair property
            tradeMBean.removeProperty(TradeConfigurationMBean.PRECISION_USE_BIG_DECIMAL_ENABLED_PREFIX + org1.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE);
            assertTrue(tradeMBean.isRoundUsingBigDecimal(org1));
            assertTrue(tradeMBean.isRoundUsingBigDecimal(org2));
            //remove ccy pair property
            tradeMBean.removeProperty(TradeConfigurationMBean.PRECISION_USE_BIG_DECIMAL_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE);
            assertFalse(tradeMBean.isRoundUsingBigDecimal(org1));
            assertFalse(tradeMBean.isRoundUsingBigDecimal(org2));

        } catch (Exception e) {
            e.printStackTrace();
            fail("testIsRoundUsingBigDecimal");
        }
    }

    public void testIsSingleQuoteRFSSupported() {
        try {
            Organization org1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker2" );
            assertFalse(tradeMBean.isSingleQuoteRFSSupported(org1));
            assertFalse(tradeMBean.isSingleQuoteRFSSupported(org2));

            // now set the global property
            tradeMBean.setProperty(TradeConfigurationMBean.SINGLE_QUOTE_RFS_SUPPORT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertTrue(tradeMBean.isSingleQuoteRFSSupported(org1));
            assertTrue(tradeMBean.isSingleQuoteRFSSupported(org2));

            tradeMBean.setProperty(TradeConfigurationMBean.SINGLE_QUOTE_RFS_SUPPORT_ENABLED_PREFIX + org1.getShortName(), "false", ConfigurationProperty.DYNAMIC_SCOPE, null);
            assertFalse(tradeMBean.isSingleQuoteRFSSupported(org1));
            assertTrue(tradeMBean.isSingleQuoteRFSSupported(org2));

            //remove org and ccy pair property
            tradeMBean.removeProperty(TradeConfigurationMBean.SINGLE_QUOTE_RFS_SUPPORT_ENABLED_PREFIX + org1.getShortName(), ConfigurationProperty.DYNAMIC_SCOPE);
            assertTrue(tradeMBean.isSingleQuoteRFSSupported(org1));
            assertTrue(tradeMBean.isSingleQuoteRFSSupported(org2));
            //remove ccy pair property
            tradeMBean.removeProperty(TradeConfigurationMBean.SINGLE_QUOTE_RFS_SUPPORT_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE);
            assertFalse(tradeMBean.isSingleQuoteRFSSupported(org1));
            assertFalse(tradeMBean.isSingleQuoteRFSSupported(org2));

        } catch (Exception e) {
            e.printStackTrace();
            fail("testIsSingleQuoteRFSSupported");
        }
    }

    public void testSendSalesDealerDetailsInFIXSTPEnabled ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        try
        {
            fi1.setBrokerOrganization ( broker1 );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( null ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( broker1 ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( fi1 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( broker1 ) );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( fi1 ) );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( broker1 ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( fi1 ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS_PREFIX + broker1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( broker1 ) );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( fi1 ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS_PREFIX + broker1.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( broker1 ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( fi1 ) );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( broker1 ) );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( fi1 ) );
            assertTrue ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS, ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( broker1 ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( fi1 ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS_PREFIX + fi1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( broker1 ) );
            assertTrue( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( fi1 ) );
            assertFalse ( tradeMBean.isSendSalesDealerDetailsEnabledInFIXSTP ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testSendSalesDealerDetailsInFIXSTPEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject ( fi1 );
            IdcUtilC.refreshObject ( broker1 );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS_PREFIX + fi1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_STP_FIX_SEND_SALES_DEALER_DETAILS_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }


    public void testUserMakerToTakerQuoteConventionForManualTradeProperty ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        Organization fi1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "FI1" );
        try
        {
            fi1.setBrokerOrganization ( broker1 );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( null ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( broker1 ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( fi1 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( broker1 ) );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( fi1 ) );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( broker1 ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( fi1 ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE_PREFIX + broker1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( broker1 ) );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( fi1 ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE_PREFIX + broker1.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( broker1 ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( fi1 ) );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( broker1 ) );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( fi1 ) );
            assertTrue ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE, ConfigurationProperty.DYNAMIC_SCOPE );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( broker1 ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( fi1 ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE_PREFIX + fi1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( broker1 ) );
            assertTrue( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( fi1 ) );
            assertFalse ( tradeMBean.isUseMakerToTakerQuoteConventionForManualTrade ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testUserMakerToTakerQuoteConventionForManualTradeProperty" );
        }
        finally
        {
            IdcUtilC.refreshObject ( fi1 );
            IdcUtilC.refreshObject ( broker1 );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE_PREFIX + fi1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.IDC_TRADE_MAKER_TO_TAKER_QUOTE_CONVENTION_FOR_MANUAL_TRADE_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testPreTradeAllocationSpreadingModelEnabledProperty ( )
    {
        Organization broker1 = ( Organization ) new ReadNamedEntityC ().execute ( OrganizationC.class, "Broker1" );
        try
        {
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( null ) );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( broker1 ) );

            // now set the global property
            tradeMBean.setProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( broker1 ) );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED_PREFIX + broker1.getShortName (), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( broker1 ) );
            assertFalse ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( null ) );

            tradeMBean.setProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tradeMBean.setProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED_PREFIX + broker1.getShortName (), "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( broker1 ) );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( broker1 ) );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( null ) );

            tradeMBean.removeProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( broker1 ) );
            assertTrue ( tradeMBean.isPreTradeAllocationSpreadingModelEnabled ( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ( "testPreTradeAllocationSpreadingModelEnabledProperty" );
        }
        finally
        {
            IdcUtilC.refreshObject ( broker1 );
            tradeMBean.removeProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            tradeMBean.removeProperty ( TradeConfigurationMBean.PRE_TRADE_ALLOCATION_SPREADING_MODEL_ENABLED_PREFIX + broker1.getShortName (), ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }
}
