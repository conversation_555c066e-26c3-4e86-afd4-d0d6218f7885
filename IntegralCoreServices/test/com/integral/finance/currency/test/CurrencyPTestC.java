package com.integral.finance.currency.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyRemoteTransactionFunctor;
import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.instrument.InstrumentClassificationC;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntime;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.*;

public class CurrencyPTestC
        extends PTestCaseC
{
    static String name = "Currency Test";
    private String CCY_SHORT_NAME = "XXX";

    public CurrencyPTestC( String name )
    {
        super( name );
    }

    public void testCurrencyDeliverable()
    {
        Currency rubalCurrency = CurrencyFactory.newCurrency();

        // Inserting the new currency to DB.
        try
        {
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.removeAllReadOnlyClasses ();
                rubalCurrency = ( Currency ) this.uow.registerNewObject ( rubalCurrency );
                rubalCurrency.setDeliverable ( false );
                rubalCurrency.setShortName ( CCY_SHORT_NAME );
                rubalCurrency.setLongName ( CCY_SHORT_NAME );
                rubalCurrency.setDescription ( "Rubal" );
                rubalCurrency.setAlias ( "RU0" );
                rubalCurrency.setStatus ( 'T' );
                uow.commit ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencyDeliverable() Exception 1 - " + exc );
            }

            // Reloading the currency from DB.
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.refreshObject ( rubalCurrency );
                assertEquals ( rubalCurrency.isDeliverable (), false );
                assertEquals ( rubalCurrency.getAlias (), "RU0" );
                assertEquals ( rubalCurrency.getDisplayName (), "RU0" );
                uow.release ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencyDeliverable() Exception 2 - " + exc );
            }

        }
        finally
        {
            deleteTestCurrency ();
        }
    }

    public void testCurrencyIndex()
    {
        try
        {
            int index1 = createCurrency().getIndex();
            Thread.sleep( 5 );
            int index2 = createCurrency().getIndex();
            log( "ccy11Index=" + index1 + ",ccy2Index=" + index2 );
            assertEquals( "index should be incremented by one.", index2 - index1, 1 );
            assertEquals( "index should be greater than or equal to 1", index1 >= 1, true );
            assertEquals( "index should be greater than or equal to 1", index2 >= 1, true );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyIndex", e );
        }
    }

    public void testCurrencyIndexUniqueness()
    {
        try
        {
            Session session = PersistenceFactory.newSession();

            Currency ccy1 = createCurrency();
            Thread.sleep( 5 );
            Currency ccy2 = createCurrency();
            int index1 = ccy1.getIndex();
            long objectId = ccy2.getObjectID();
            log( "ccy11Index=" + index1 + ",ccy2Index=" + ccy2.getObjectID() );

            boolean exceptionThrown = false;
            try
            {
                session.executeSQL( "update idcinstr set idx = " + index1 + " where id = " + objectId );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyIndexUniqueness", e );
        }
    }

    protected Currency createCurrency()
    {
        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        Currency ccy = new CurrencyC();
        ccy = ( Currency ) uow.registerObject( ccy );
        ccy.setShortName( "CCYTEST-" + System.currentTimeMillis() );
        ccy.setStatus( 'T' );
        uow.commit();
        return ( Currency ) getPersistenceSession().refreshObject( ccy );
    }

    public void testCurrencyPair()
    {
        try
        {
            Expression expr = new ExpressionBuilder().get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
            Collection<Currency> ccys = PersistenceFactory.newSession().readAllObjects( CurrencyC.class, expr );
            log( "ccys=" + ccys );
            for ( Currency ccy : ccys )
            {
                //check the currency pair object for each other currencies.
                Set<Currency> ccySet = new HashSet<Currency>();
                ccySet.addAll( ccys );
                for ( Currency other : ccySet )
                {
                    CurrencyPair cp = CurrencyFactory.getCurrencyPair( ccy, other );
                    assertNotNull( cp );
                    assertEquals( cp.getBaseCurrency(), ccy );
                    assertEquals( cp.getVariableCurrency(), other );
                    CurrencyPair cpFromString = CurrencyFactory.getCurrencyPair( ccy.getShortName(), other.getShortName() );
                    assertNotNull( cpFromString );
                    assertEquals( cpFromString.getBaseCurrency(), ccy );
                    assertEquals( cpFromString.getVariableCurrency(), other );

                    CurrencyPair cp1 = CurrencyFactory.getCurrencyPair( other, ccy );
                    assertNotNull( cp1 );
                    assertEquals( cp1.getBaseCurrency(), other );
                    assertEquals( cp1.getVariableCurrency(), ccy );

                    CurrencyPair cp1FromString = CurrencyFactory.getCurrencyPair( other.getShortName(), ccy.getShortName() );
                    assertNotNull( cp1FromString );
                    assertEquals( cp1FromString.getBaseCurrency(), other );
                    assertEquals( cp1FromString.getVariableCurrency(), ccy );

                    // check the currency pair names
                    String ccyPairName1 = CurrencyFactory.getCurrencyPairName( ccy, other );
                    String ccyPairName2 = ccy.getShortName() + "/" + other.getShortName();
                    assertEquals( ccyPairName1, ccyPairName2 );

                    String ccyPairName3 = CurrencyFactory.getCurrencyPairName( other, ccy );
                    String ccyPairName4 = other.getShortName() + "/" + ccy.getShortName();
                    assertEquals( ccyPairName3, ccyPairName4 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPair", e );
        }
    }

    public void testCurrencyAlias()
    {
        try
        {
            CurrencyRemoteTransactionFunctor remoteFunctor = new CurrencyRemoteTransactionFunctor();
            Expression expr = new ExpressionBuilder().get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
            Collection<Currency> ccys = PersistenceFactory.newSession().readAllObjects( CurrencyC.class, expr );
            log( "ccys=" + ccys );
            for ( Currency ccy : ccys )
            {
                //check the currency pair object for each other currencies.
                Set<Currency> ccySet = new HashSet<Currency>();
                ccySet.addAll( ccys );
                for ( Currency other : ccySet )
                {
                    CurrencyPair cp1 = CurrencyFactory.getCurrencyPair( ccy, other );
                    CurrencyPair cp2 = CurrencyFactory.getCurrencyPair( other, ccy );
                    String ccyPairName1 = CurrencyFactory.getCurrencyPairName( ccy, other );
                    String ccyPairName2 = CurrencyFactory.getCurrencyPairName( other, ccy );
                    String ccyPairDisplayName1 = CurrencyFactory.getCurrencyPairDisplayName( ccy, other );
                    String ccyPairDisplayName2 = CurrencyFactory.getCurrencyPairDisplayName( other, ccy );

                    // set an alias on the currency.
                    String newAlias = ccy.getShortName() + String.valueOf( System.nanoTime() );
                    ccy.setAlias( newAlias );
                    String newDisplayName1 = ccy.getDisplayName() + "/" + other.getDisplayName();
                    String newDisplayName2 = other.getDisplayName() + "/" + ccy.getDisplayName();

                    // invoke the functor for notification.
                    HashMap map = new HashMap();
                    map.put( CurrencyRemoteTransactionFunctor.CCY_GUID, ccy.getGUID() );
                    remoteFunctor.onCommit( map );

                    CurrencyPair cp3 = CurrencyFactory.getCurrencyPair( ccy, other );
                    CurrencyPair cp4 = CurrencyFactory.getCurrencyPair( other, ccy );
                    String ccyPairName3 = CurrencyFactory.getCurrencyPairName( ccy, other );
                    String ccyPairName4 = CurrencyFactory.getCurrencyPairName( other, ccy );
                    String ccyPairDisplayName3 = CurrencyFactory.getCurrencyPairDisplayName( ccy, other );
                    String ccyPairDisplayName4 = CurrencyFactory.getCurrencyPairDisplayName( other, ccy );
                    assertTrue( cp1.equals( cp3 ) );
                    assertTrue( cp2.equals( cp4 ) );
                    assertTrue( ccyPairName1.equals( ccyPairName3 ) );
                    assertTrue( ccyPairName2.equals( ccyPairName4 ) );
                    assertEquals( newDisplayName1, ccyPairDisplayName3 );
                    assertEquals( newDisplayName2, ccyPairDisplayName4 );


                    IdcUtilC.refreshObject( ccy );

                    HashMap map1 = new HashMap();
                    map1.put( CurrencyRemoteTransactionFunctor.CCY_GUIDS, new String[]{ccy.getGUID()} );
                    remoteFunctor.onCommit( map );
                    CurrencyPair cp5 = CurrencyFactory.getCurrencyPair( ccy, other );
                    CurrencyPair cp6 = CurrencyFactory.getCurrencyPair( other, ccy );
                    String ccyPairName5 = CurrencyFactory.getCurrencyPairName( ccy, other );
                    String ccyPairName6 = CurrencyFactory.getCurrencyPairName( other, ccy );
                    String ccyPairDisplayName5 = CurrencyFactory.getCurrencyPairDisplayName( ccy, other );
                    String ccyPairDisplayName6 = CurrencyFactory.getCurrencyPairDisplayName( other, ccy );
                    assertTrue( cp1.equals( cp5 ) );
                    assertTrue( cp2.equals( cp6 ) );
                    assertTrue( ccyPairName1.equals( ccyPairName5 ) );
                    assertTrue( ccyPairName2.equals( ccyPairName6 ) );
                    assertEquals( ccyPairDisplayName5, ccyPairDisplayName1 );
                    assertEquals( ccyPairDisplayName6, ccyPairDisplayName2 );
                    break;
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCurrencyAlias", e );
        }
    }

    public void testCurrencyEnergyClassification()
    {
        try
        {
            InstrumentClassification energyClsf = ( InstrumentClassification ) namedEntityReader.execute ( InstrumentClassification.class, "ENERGY", null );
            assertNotNull ( energyClsf );
            Currency ccy = CurrencyFactory.newCurrency ();

            // Inserting the new currency to DB.
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.removeAllReadOnlyClasses ();
                uow.addReadOnlyClass ( InstrumentClassificationC.class );
                ccy = ( Currency ) this.uow.registerNewObject ( ccy );
                ccy.setDeliverable ( false );
                ccy.setShortName ( CCY_SHORT_NAME );
                ccy.setLongName ( CCY_SHORT_NAME );
                ccy.setDescription ( "Energy" );
                ccy.setStatus ( 'T' );
                ccy.setInstrumentClassification ( energyClsf );
                uow.commit ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencyEnergyClassification() Exception 1 - " + exc );
            }

            // Reloading the currency from DB.
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.refreshObject ( ccy );
                assertTrue ( ccy.getInstrumentClassification ().isSameAs ( energyClsf ) );
                uow.release ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencyEnergyClassification() Exception 2 - " + exc );
            }

            Expression engergyExpr = new ExpressionBuilder ().get ( "instrumentClassification" ).equal ( energyClsf );
            Collection<Currency> currencies = ( Collection<Currency> ) getPersistenceSession ().readAllObjects ( CurrencyC.class, engergyExpr );
            assertNotNull ( currencies );
            assertTrue ( ! currencies.isEmpty () );
            boolean result = false;
            for ( Currency currency : currencies )
            {
                assertTrue ( currency.getInstrumentClassification ().isSameAs ( energyClsf ) );
                result = true;
            }
            assertTrue ( result );
        }
        finally
        {
            deleteTestCurrency ();
        }
    }

    public void testCurrencyTickValue()
    {
        String AMOUNT_FORMAT_STRING = "###,###,###,###.########";
        Currency trl = CurrencyFactory.getCurrency( "TRL" );
        try
        {
            assertNotNull ( trl );
            trl.setTickValue ( 0.0000000001 );//0.000000007

            double amt = 0.0000006978;
            String formattedAmt = trl.getDecimalFormat ( AMOUNT_FORMAT_STRING ).format ( amt );
            System.out.println ( "formatted amount=" + formattedAmt );
        }
        catch ( Exception e )
        {
            fail ( "testCurrencyTickValue", e );
        }
        finally
        {
            IdcUtilC.refreshObject ( trl );
        }
    }

    public void testCurrencyCryptoClassification()
    {
        try
        {
            InstrumentClassification cryptoClsf = ( InstrumentClassification ) namedEntityReader.execute ( InstrumentClassification.class, "CRYPTO", null );
            assertNotNull ( cryptoClsf );
            Currency ccy = CurrencyFactory.newCurrency ();

            // Inserting the new currency to DB.
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.removeAllReadOnlyClasses ();
                uow.addReadOnlyClass ( InstrumentClassificationC.class );
                ccy = ( Currency ) this.uow.registerNewObject ( ccy );
                ccy.setDeliverable ( false );
                ccy.setShortName ( CCY_SHORT_NAME );
                ccy.setLongName ( CCY_SHORT_NAME );
                ccy.setDescription ( "Crypto" );
                ccy.setStatus ( 'T' );
                ccy.setInstrumentClassification ( cryptoClsf );
                uow.commit ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencyCryptoClassification() Exception 1 - " + exc );
            }

            // Reloading the currency from DB.
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.refreshObject ( ccy );
                assertTrue ( ccy.getInstrumentClassification ().isSameAs ( cryptoClsf ) );
                uow.release ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencyEnergyClassification() Exception 2 - " + exc );
            }

            Expression cryptoExpr = new ExpressionBuilder ().get ( "instrumentClassification" ).equal ( cryptoClsf );
            Collection<Currency> currencies = ( Collection<Currency> ) getPersistenceSession ().readAllObjects ( CurrencyC.class, cryptoExpr );
            assertNotNull ( currencies );
            assertTrue ( ! currencies.isEmpty () );
            boolean result = false;
            for ( Currency currency : currencies )
            {
                assertTrue ( currency.getInstrumentClassification ().isSameAs ( cryptoClsf ) );
                result = true;
            }
            assertTrue ( result );
        }
        finally
        {
            deleteTestCurrency ();
        }
    }
    
    public void testCurrencySettlementType()
    {  
        Currency ccy = CurrencyFactory.newCurrency();

        // Inserting the new currency to DB.
        try
        {
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.removeAllReadOnlyClasses ();
                uow.addReadOnlyClass ( InstrumentClassificationC.class );
                ccy = ( Currency ) this.uow.registerNewObject ( ccy );
                ccy.setDeliverable ( false );
                ccy.setShortName ( CCY_SHORT_NAME );
                ccy.setLongName ( CCY_SHORT_NAME );
                ccy.setDescription ( "ForwardInstrument" );
                ccy.setStatus ( 'T' );
                ccy.setSettlementTenor ( new Tenor ( "2M" ) );
                Currency aud = CurrencyFactory.getCurrency ( "AUD" );
                ccy.setUnderlyingCurrency ( ( Currency ) uow.registerObject ( aud ) );
                ccy.setSettlementType ( Currency.SettlementType.FORWARD );
                uow.commit ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencySettlementType() Exception 1 - " + exc );
            }

            // Reloading the currency from DB.
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.refreshObject ( ccy );
                assertTrue ( Currency.SettlementType.FORWARD == ccy.getSettlementType () );
                assertTrue ( ccy.getSettlementTenor ().equals ( new Tenor ( "2M" ) ) );
                assertTrue ( ccy.getUnderlyingCurrency ().isSameAs ( CurrencyFactory.getCurrency ( "AUD" ) ) );
                uow.release ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencySettlementType() Exception 2 - " + exc );
            }
        }
        finally
        {
            deleteTestCurrency ();
        }
    }

    public void testForwardCurrencyPairLookup()
    {
        Currency ccy = CurrencyFactory.newCurrency();

        // Inserting the new currency to DB.
        try
        {
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.removeAllReadOnlyClasses ();
                uow.addReadOnlyClass ( InstrumentClassificationC.class );
                ccy = ( Currency ) this.uow.registerNewObject ( ccy );
                ccy.setDeliverable ( false );
                ccy.setShortName ( CCY_SHORT_NAME );
                ccy.setLongName ( CCY_SHORT_NAME );
                ccy.setDescription ( "ForwardInstrument" );
                ccy.setStatus ( 'A' );
                ccy.setSettlementTenor ( new Tenor ( "4M" ) );
                Currency chf = CurrencyFactory.getCurrency ( "CHF" );
                ccy.setUnderlyingCurrency ( ( Currency ) uow.registerObject ( chf ) );
                ccy.setSettlementType ( Currency.SettlementType.FORWARD );
                uow.commit ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencySettlementType() Exception 1 - " + exc );
            }

            // Reloading the currency from DB.
            try
            {
                CurrencyFactory.init ();
                ccy = CurrencyFactory.getCurrency ( CCY_SHORT_NAME );
                Currency eur = CurrencyFactory.getCurrency ( "EUR" );
                Currency chf = CurrencyFactory.getCurrency ( "CHF" );
                assertTrue ( CurrencyFactory.getOriginalCurrency ( eur ).isSameAs ( eur ) );
                assertTrue ( CurrencyFactory.getOriginalCurrency ( eur ).isSameAs ( eur ) );
                assertTrue ( CurrencyFactory.getOriginalCurrency ( ccy ).isSameAs ( chf ) );
                assertTrue ( CurrencyFactory.getOriginalCurrencyPair ( eur, chf ).isMatch ( CurrencyFactory.getCurrencyPair ( eur, chf ) ) );
                CurrencyFactory.init ();
                CurrencyPair cp = CurrencyFactory.getCurrencyPair ( eur, chf, new Tenor( "4M"), Currency.SettlementType.FORWARD );
                assertNotNull ( cp );
                assertNotNull ( cp.getBaseCurrency () );
                assertNotNull ( cp.getVariableCurrency () );
                assertTrue ( new Tenor ( "4m" ).equals ( cp.getVariableCurrency ().getSettlementTenor () ) );

                CurrencyPair cp1 = CurrencyFactory.getCurrencyPair ( eur, chf, new Tenor( "4m"), Currency.SettlementType.FORWARD );
                assertNotNull ( cp1 );
                assertNotNull ( cp1.getBaseCurrency () );
                assertNotNull ( cp1.getVariableCurrency () );
                assertTrue ( new Tenor ( "4m" ).equals ( cp.getVariableCurrency ().getSettlementTenor () ) );

                CurrencyPair cp2 = CurrencyFactory.getCurrencyPair ( eur, chf, new Tenor( "2m"), Currency.SettlementType.FORWARD );
                assertNull ( cp2 );
                CurrencyPair cp3 = CurrencyFactory.getCurrencyPair ( eur, chf, new Tenor( "4M"), Currency.SettlementType.SWAP );
                assertNull ( cp3 );
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testForwardCurrencyPairLookup() Exception 2 - " + exc );
            }
        }
        finally
        {
            deleteTestCurrency ();
        }
    }

    public void testSwapCurrencyPairLookup()
    {
        Currency ccy = CurrencyFactory.newCurrency();

        // Inserting the new currency to DB.
        try
        {
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.removeAllReadOnlyClasses ();
                uow.addReadOnlyClass ( InstrumentClassificationC.class );
                ccy = ( Currency ) this.uow.registerNewObject ( ccy );
                ccy.setDeliverable ( false );
                ccy.setShortName ( CCY_SHORT_NAME );
                ccy.setLongName ( CCY_SHORT_NAME );
                ccy.setDescription ( "ForwardInstrument" );
                ccy.setStatus ( 'A' );
                ccy.setSettlementTenor ( new Tenor ( "5M" ) );
                Currency cad = CurrencyFactory.getCurrency ( "CAD" );
                ccy.setUnderlyingCurrency ( ( Currency ) uow.registerObject ( cad ) );
                ccy.setSettlementType ( Currency.SettlementType.SWAP );
                uow.commit ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testCurrencySettlementType() Exception 1 - " + exc );
            }

            // Reloading the currency from DB.
            try
            {
                CurrencyFactory.init ();
                ccy = CurrencyFactory.getCurrency ( CCY_SHORT_NAME );
                Currency eur = CurrencyFactory.getCurrency ( "EUR" );
                Currency cad = CurrencyFactory.getCurrency ( "CAD" );
                assertTrue ( CurrencyFactory.getOriginalCurrency ( eur ).isSameAs ( eur ) );
                assertTrue ( CurrencyFactory.getOriginalCurrency ( eur ).isSameAs ( eur ) );
                assertTrue ( CurrencyFactory.getOriginalCurrency ( ccy ).isSameAs ( cad ) );
                assertTrue ( CurrencyFactory.getOriginalCurrencyPair ( eur, cad ).isMatch ( CurrencyFactory.getCurrencyPair ( eur, cad ) ) );
                CurrencyFactory.init ();
                CurrencyPair cp = CurrencyFactory.getCurrencyPair ( eur, cad, new Tenor( "5M"), Currency.SettlementType.SWAP );
                assertNotNull ( cp );
                assertNotNull ( cp.getBaseCurrency () );
                assertNotNull ( cp.getVariableCurrency () );
                assertTrue ( new Tenor ( "5m" ).equals ( cp.getVariableCurrency ().getSettlementTenor () ) );

                CurrencyPair cp1 = CurrencyFactory.getCurrencyPair ( eur, cad, new Tenor( "5m"), Currency.SettlementType.SWAP );
                assertNotNull ( cp1 );
                assertNotNull ( cp1.getBaseCurrency () );
                assertNotNull ( cp1.getVariableCurrency () );
                assertTrue ( new Tenor ( "5m" ).equals ( cp.getVariableCurrency ().getSettlementTenor () ) );

                CurrencyPair cp2 = CurrencyFactory.getCurrencyPair ( eur, cad, new Tenor( "4m"), Currency.SettlementType.SWAP );
                assertNull ( cp2 );
                CurrencyPair cp3 = CurrencyFactory.getCurrencyPair ( eur, cad, new Tenor( "5M"), Currency.SettlementType.FORWARD );
                assertNull ( cp3 );
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testSwapCurrencyPairLookup() Exception 2 - " + exc );
            }
        }
        finally
        {
            deleteTestCurrency ();
        }
    }

    public void testForwardCurrencyLookup()
    {
        Currency ccy = CurrencyFactory.newCurrency();

        // Inserting the new currency to DB.
        try
        {
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.removeAllReadOnlyClasses ();
                uow.addReadOnlyClass ( InstrumentClassificationC.class );
                ccy = ( Currency ) this.uow.registerNewObject ( ccy );
                ccy.setDeliverable ( false );
                ccy.setShortName ( CCY_SHORT_NAME );
                ccy.setLongName ( CCY_SHORT_NAME );
                ccy.setDescription ( "ForwardInstrument" );
                ccy.setStatus ( 'A' );
                ccy.setSettlementTenor ( new Tenor ( "7M" ) );
                Currency nzd = CurrencyFactory.getCurrency ( "NZD" );
                ccy.setUnderlyingCurrency ( ( Currency ) uow.registerObject ( nzd ) );
                ccy.setSettlementType ( Currency.SettlementType.FORWARD );
                uow.commit ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testForwardCurrencyLookup() Exception 1 - " + exc );
            }

            // Reloading the currency from DB.
            try
            {
                CurrencyFactory.init ();
                ccy = CurrencyFactory.getCurrency ( CCY_SHORT_NAME );
                assertNotNull ( ccy );
                Currency nzd = CurrencyFactory.getCurrency ( "NZD" );
                Currency fwdCcy = CurrencyFactory.getCurrency ( nzd, new Tenor("7M"), Currency.SettlementType.FORWARD );
                assertNotNull ( fwdCcy );
                assertTrue( fwdCcy.isSameAs ( ccy ) );

                Currency fwdCcy1 = CurrencyFactory.getCurrency ( nzd, new Tenor("7m"), Currency.SettlementType.FORWARD );
                assertNotNull ( fwdCcy1 );
                assertTrue( fwdCcy1.isSameAs ( ccy ) );

                Currency fwdCcy2 = CurrencyFactory.getCurrency ( ccy, Tenor.SPOT_TENOR, Currency.SettlementType.FORWARD );
                assertNotNull ( fwdCcy2 );
                assertTrue( fwdCcy2.isSameAs ( nzd ) );

                Currency fwdCcy3 = CurrencyFactory.getCurrency ( ccy, new Tenor("7m"), Currency.SettlementType.SPOT );
                assertNotNull ( fwdCcy3 );
                assertTrue( fwdCcy3.isSameAs ( nzd ) );
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testForwardCurrencyLookup() Exception 2 - " + exc );
            }
        }
        finally
        {
            deleteTestCurrency ();
        }
    }

    public void testSwapCurrencyLookup()
    {
        Currency ccy = CurrencyFactory.newCurrency();

        // Inserting the new currency to DB.
        try
        {
            try
            {
                uow = this.getPersistenceSession ().acquireUnitOfWork ();
                uow.removeAllReadOnlyClasses ();
                uow.addReadOnlyClass ( InstrumentClassificationC.class );
                ccy = ( Currency ) this.uow.registerNewObject ( ccy );
                ccy.setDeliverable ( false );
                ccy.setShortName ( CCY_SHORT_NAME );
                ccy.setLongName ( CCY_SHORT_NAME );
                ccy.setDescription ( "SwapInstrument" );
                ccy.setStatus ( 'A' );
                ccy.setSettlementTenor ( new Tenor ( "8M" ) );
                Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
                ccy.setUnderlyingCurrency ( ( Currency ) uow.registerObject ( gbp ) );
                ccy.setSettlementType ( Currency.SettlementType.SWAP );
                uow.commit ();
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testSwapCurrencyLookup() Exception 1 - " + exc );
            }

            // Reloading the currency from DB.
            try
            {
                CurrencyFactory.init ();
                ccy = CurrencyFactory.getCurrency ( CCY_SHORT_NAME );
                assertNotNull ( ccy );
                Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
                Currency swapCcy = CurrencyFactory.getCurrency ( gbp, new Tenor("8M"), Currency.SettlementType.SWAP );
                assertNotNull ( swapCcy );
                assertTrue( swapCcy.isSameAs ( ccy ) );

                Currency swapCcy1 = CurrencyFactory.getCurrency ( gbp, new Tenor("8m"), Currency.SettlementType.SWAP );
                assertNotNull ( swapCcy1 );
                assertTrue( swapCcy1.isSameAs ( ccy ) );

                Currency swapCcy2 = CurrencyFactory.getCurrency ( ccy, Tenor.SPOT_TENOR, Currency.SettlementType.FORWARD );
                assertNotNull ( swapCcy2 );
                assertTrue( swapCcy2.isSameAs ( gbp ) );

                Currency swapCcy3 = CurrencyFactory.getCurrency ( ccy, new Tenor("8m"), Currency.SettlementType.SPOT );
                assertNotNull ( swapCcy3 );
                assertTrue( swapCcy3.isSameAs ( gbp ) );
            }
            catch ( Exception exc )
            {
                log.error ( "CurrencyPTestC.testSwapCurrencyLookup() Exception 2 - " + exc );
            }
        }
        finally
        {
            deleteTestCurrency ();
        }
    }

    public void testVirtualCurrencyPairLookupByName()
    {
        ServerRuntime runTimeMBean = ( ServerRuntime ) RuntimeFactory.getServerRuntimeMBean();
        try
        {
            runTimeMBean.setProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_IN_MEMORY_CURRENCYPAIR_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Tenor tenor1 = new Tenor( "7W" );
            Tenor tenor2 = new Tenor( "9W" );
            IdcDate brokenDate1 = DateTimeFactory.newDate ().addDays ( 30 );
            IdcDate brokenDate2 = brokenDate1.addDays ( 7 );

            CurrencyPair vCcyPair_Tenor1 = CurrencyFactory.getVirtualNonSpotCurrencyPair ( eur, usd, tenor1, null, Currency.SettlementType.FORWARD );
            assertNotNull ( vCcyPair_Tenor1 );
            String ccyPair_Tenor2 = vCcyPair_Tenor1.getName ().replace ( tenor1.getName (), tenor2.getName () );
            CurrencyPair vCcyPair_Tenor2 = CurrencyFactory.getVirtualNonSpotCurrencyPair (  ccyPair_Tenor2 );
            assertNotNull ( vCcyPair_Tenor2 );
            assertEquals ( tenor2.getName (), vCcyPair_Tenor2.getVariableCurrency ().getSettlementTenor ().getName () );


            CurrencyPair vCcyPair_BrokenDate1 = CurrencyFactory.getVirtualNonSpotCurrencyPair ( eur, usd, null, brokenDate1, Currency.SettlementType.FORWARD );
            assertNotNull ( vCcyPair_BrokenDate1 );
            String ccyPair_BrokenDate2 = vCcyPair_BrokenDate1.getName ().replace ( brokenDate1.getFormattedDate ( IdcDate.YYYY_MM_DD_HYPHEN ), brokenDate2.getFormattedDate ( IdcDate.YYYY_MM_DD_HYPHEN ) );
            CurrencyPair vCcyPair_BrokenDate2 = CurrencyFactory.getVirtualNonSpotCurrencyPair (  ccyPair_BrokenDate2 );
            assertNotNull ( vCcyPair_BrokenDate2 );
            assertEquals ( brokenDate2.getFormattedDate ( IdcDate.YYYY_MM_DD_HYPHEN ), vCcyPair_BrokenDate2.getVariableCurrency ().getBrokenDate ().getFormattedDate ( IdcDate.YYYY_MM_DD_HYPHEN ) );
        }
        catch ( Exception e )
        {
            fail ( "testVirtualCurrencyPairLookupByName", e );
        }
        finally
        {
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_IN_MEMORY_CURRENCYPAIR_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    private void deleteTestCurrency()
    {
        try
        {
            uow = this.getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder builder = new ExpressionBuilder();
            Expression expr = builder.get( "shortName" ).equal( CCY_SHORT_NAME );
            Currency ccy = ( Currency ) uow.readObject( com.integral.finance.currency.Currency.class, expr );
            if ( ccy != null )
            {
                Currency refreshedCcy = ( Currency ) uow.refreshObject( ccy );
                if ( refreshedCcy != null )
                {
                    Currency registeredCcy = ( Currency ) uow.registerObject ( refreshedCcy );
                    uow.removeAllReadOnlyClasses();
                    uow.deleteObject( registeredCcy );
                    uow.commit();
                    CurrencyFactory.init ();
                    uow.release();
                    log.warn ( "deleted the test currency=" + CCY_SHORT_NAME );

                    uow = this.getPersistenceSession().acquireUnitOfWork();
                    ExpressionBuilder builder1 = new ExpressionBuilder();
                    Expression expr1 = builder1.get( "shortName" ).equal( CCY_SHORT_NAME );
                    Currency ccy1 = ( Currency ) uow.readObject( com.integral.finance.currency.Currency.class, expr1 );
                    assertEquals( ccy1, null );
                }
            }
        }
        catch ( Exception exc )
        {
            log.error( "CurrencyPTestC.deleteTestCurrency() Exception 3 - " + exc );
        }
    }

    protected void setUp() throws Exception
    {
        super.setUp ();
        deleteTestCurrency ();
    }

    protected void tearDown()
    {
        super.tearDown ();
        deleteTestCurrency ();
    }

    public void testCurrencyFactoryNullHandling()
    {
        try
        {
            // Test null handling in getCurrencyPair
            Currency ccy1 = null;
            Currency ccy2 = null;
            CurrencyPair cp1 = CurrencyFactory.getCurrencyPair(ccy1, ccy2);
            assertNull(cp1);
            
            Currency usd = CurrencyFactory.getCurrency("USD");
            CurrencyPair cp2 = CurrencyFactory.getCurrencyPair(usd, null);
            assertNull(cp2);
            
            CurrencyPair cp3 = CurrencyFactory.getCurrencyPair(null, usd);
            assertNull(cp3);
            
            // Test null handling in getCurrencyPairName
            String name1 = CurrencyFactory.getCurrencyPairName(ccy1, ccy2);
            assertNull(name1);
            
            String name2 = CurrencyFactory.getCurrencyPairName(usd, null);
            assertNull(name2);
            
            String name3 = CurrencyFactory.getCurrencyPairName(null, usd);
            assertNull(name3);
            
            // Test null handling in string-based methods
            CurrencyPair cp4 = CurrencyFactory.getCurrencyPair(null, "USD");
            assertNull(cp4);
            
            CurrencyPair cp5 = CurrencyFactory.getCurrencyPair("USD", null);
            assertNull(cp5);
        }
        catch (Exception e)
        {
            fail("testCurrencyFactoryNullHandling failed", e);
        }
    }

    public void testCurrencyPairDisplayName()
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency("USD");
            Currency eur = CurrencyFactory.getCurrency("EUR");
            assertNotNull(usd);
            assertNotNull(eur);
            
            String displayName = CurrencyFactory.getCurrencyPairDisplayName(usd, eur);
            assertNotNull(displayName);
            assertTrue(displayName.contains("/"));
            
            // Test null handling
            String nullDisplay = CurrencyFactory.getCurrencyPairDisplayName(null, eur);
            assertNull(nullDisplay);
            
            String nullDisplay2 = CurrencyFactory.getCurrencyPairDisplayName(usd, null);
            assertNull(nullDisplay2);
        }
        catch (Exception e)
        {
            fail("testCurrencyPairDisplayName failed", e);
        }
    }

    public void testCurrencyPairWithCustomDelimiter()
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency("USD");
            Currency eur = CurrencyFactory.getCurrency("EUR");
            assertNotNull(usd);
            assertNotNull(eur);
            
            // Test with different delimiters
            String name1 = CurrencyFactory.getCurrencyPairName(usd, eur, "-");
            assertEquals("USD-EUR", name1);
            
            String name2 = CurrencyFactory.getCurrencyPairName("USD", "EUR", "_");
            assertEquals("USD_EUR", name2);
            
            String name3 = CurrencyFactory.getCurrencyPairName(usd, eur, "");
            assertEquals("USDEUR", name3);
        }
        catch (Exception e)
        {
            fail("testCurrencyPairWithCustomDelimiter failed", e);
        }
    }

    public void testGetBaseCurrencyAndTermCurrency()
    {
        try
        {
            String ccyPairStr = "EUR/USD";
            
            String baseCcy = CurrencyFactory.getBaseCurrency(ccyPairStr);
            assertEquals("EUR", baseCcy);
            
            String termCcy = CurrencyFactory.getTermCurrency(ccyPairStr);
            assertEquals("USD", termCcy);
            
        }
        catch (Exception e)
        {
            fail("testGetBaseCurrencyAndTermCurrency failed", e);
        }
    }

    public void testCurrencyArrayMethods()
    {
        try
        {
            Currency[] allCurrencies = CurrencyFactory.getCurrencies();
            assertNotNull(allCurrencies);
            assertTrue(allCurrencies.length > 0);
            
            Currency[] spotCurrencies = CurrencyFactory.getSpotCurrencies();
            assertNotNull(spotCurrencies);
            
            Currency[] sortedSpotCurrencies = CurrencyFactory.getSortedSpotCurrencies();
            assertNotNull(sortedSpotCurrencies);
            
            Currency[] nonSpotCurrencies = CurrencyFactory.getNonSpotCurrencies();
            assertNotNull(nonSpotCurrencies);
            
            // Verify spot currencies are actually spot
            for (Currency ccy : spotCurrencies)
            {
                if (ccy != null)
                {
                    assertTrue(ccy.isSpotSettlementType());
                }
            }
        }
        catch (Exception e)
        {
            fail("testCurrencyArrayMethods failed", e);
        }
    }

    public void testCurrencyCollectionMethods()
    {
        try
        {
            Collection<Currency> sortedSpotList = CurrencyFactory.getSortedSpotCurrencyList();
            assertNotNull(sortedSpotList);
            
            Collection<Currency> spotListByName = CurrencyFactory.getSpotCurrencyListSortedByName();
            assertNotNull(spotListByName);
            
            // Verify collections are not empty
            assertFalse(sortedSpotList.isEmpty());
            assertFalse(spotListByName.isEmpty());
        }
        catch (Exception e)
        {
            fail("testCurrencyCollectionMethods failed", e);
        }
    }

    public void testInstrumentId()
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency("USD");
            Currency eur = CurrencyFactory.getCurrency("EUR");
            assertNotNull(usd);
            assertNotNull(eur);
            
            int instrumentId = CurrencyFactory.getInstrumentId(usd, eur);
            assertTrue(instrumentId > 0);
            
            // Test with null currencies
            int nullId = CurrencyFactory.getInstrumentId(null, eur);
            assertEquals(0, nullId);
            
            int nullId2 = CurrencyFactory.getInstrumentId(usd, null);
            assertEquals(0, nullId2);
        }
        catch (Exception e)
        {
            fail("testInstrumentId failed", e);
        }
    }

    public void testCurrencyPairFromString()
    {
        try
        {
            CurrencyPair cp1 = CurrencyFactory.getCurrencyPairFromString("EUR/USD");
            assertNotNull(cp1);
            assertEquals("EUR", cp1.getBaseCurrency().getShortName());
            assertEquals("USD", cp1.getVariableCurrency().getShortName());
            
            // Test with invalid format
            CurrencyPair cp2 = CurrencyFactory.getCurrencyPairFromString("INVALID");
            assertNull(cp2);
            
            // Test with null
            CurrencyPair cp3 = CurrencyFactory.getCurrencyPairFromString(null);
            assertNull(cp3);
            
            // Test with empty string
            CurrencyPair cp4 = CurrencyFactory.getCurrencyPairFromString("");
            assertNull(cp4);
        }
        catch (Exception e)
        {
            fail("testCurrencyPairFromString failed", e);
        }
    }

    public void testNewCurrencyMethods()
    {
        try
        {
            // Test newCurrency()
            Currency newCcy1 = CurrencyFactory.newCurrency();
            assertNotNull(newCcy1);
            
            // Test newCurrency(String)
            Currency newCcy2 = CurrencyFactory.newCurrency("TEST");
            assertNotNull(newCcy2);
            assertEquals("TEST", newCcy2.getISOName());
            
            // Test newCurrencyPair methods
            Currency usd = CurrencyFactory.getCurrency("USD");
            Currency eur = CurrencyFactory.getCurrency("EUR");
            
            CurrencyPair newCp1 = CurrencyFactory.newCurrencyPair(usd, eur);
            assertNotNull(newCp1);
            
            CurrencyPair newCp2 = CurrencyFactory.newCurrencyPair("USD", "EUR");
            assertNotNull(newCp2);
        }
        catch (Exception e)
        {
            fail("testNewCurrencyMethods failed", e);
        }
    }

    public void testCurrencyIndexMethods()
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency("USD");
            Currency eur = CurrencyFactory.getCurrency("EUR");
            assertNotNull(usd);
            assertNotNull(eur);
            
            int instrumentId = CurrencyFactory.getInstrumentId(usd, eur);
            
            int baseIndex = CurrencyFactory.getBaseCurrencyIndex(instrumentId);
            int varIndex = CurrencyFactory.getVariableCurrencyIndex(instrumentId);
            
            assertEquals(usd.getIndex(), baseIndex);
            assertEquals(eur.getIndex(), varIndex);
        }
        catch (Exception e)
        {
            fail("testCurrencyIndexMethods failed", e);
        }
    }

    public void testVirtualNonSpotCurrency()
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency("USD");
            assertNotNull(usd);
            
            Tenor tenor = new Tenor("1M");
            Currency virtualCcy = CurrencyFactory.getVirtualNonSpotCurrency(
                usd, tenor, null, Currency.SettlementType.FORWARD);
            
            // May be null if virtual currencies are not enabled
            if (virtualCcy != null)
            {
                assertTrue(virtualCcy.isNonSpotSettlementType());
            }
            
            // Test string-based lookup
            Currency virtualFromString = CurrencyFactory.getVirtualNonSpotCurrency("USD_1M");
            // May be null if not found
        }
        catch (Exception e)
        {
            fail("testVirtualNonSpotCurrency failed", e);
        }
    }

    public void testCurrencyFactoryMaps()
    {
        try
        {
            // Test access to internal maps (for monitoring/debugging)
            Map<String, CurrencyPair> cpMap = CurrencyFactory.getCurrencyPairMap();
            assertNotNull(cpMap);

            Map forwardCcyMap = CurrencyFactory.getForwardCurrencyMap();
            assertNotNull(forwardCcyMap);

            Map swapCcyMap = CurrencyFactory.getSwapCurrencyMap();
            assertNotNull(swapCcyMap);

            Map forwardCpMap = CurrencyFactory.getForwardCurrencyPairMap();
            assertNotNull(forwardCpMap);

            Map swapCpMap = CurrencyFactory.getSwapCurrencyPairMap();
            assertNotNull(swapCpMap);
        }
        catch (Exception e)
        {
            fail("testCurrencyFactoryMaps failed", e);
        }
    }

    public void testCurrencyWithTenorAndSettlementType()
    {
        try
        {
            Currency usd = CurrencyFactory.getCurrency("USD");
            assertNotNull(usd);
            
            // Test spot tenor returns original currency
            Currency spotCcy = CurrencyFactory.getCurrency(usd, Tenor.SPOT_TENOR, Currency.SettlementType.SPOT);
            assertNotNull(spotCcy);
            assertTrue(spotCcy.isSameAs(usd) || spotCcy.equals(usd));
            
            // Test forward currency lookup
            Tenor forwardTenor = new Tenor("1M");
            Currency forwardCcy = CurrencyFactory.getCurrency(usd, forwardTenor, Currency.SettlementType.FORWARD);
            // May be null if forward currency doesn't exist
            
            // Test swap currency lookup
            Currency swapCcy = CurrencyFactory.getCurrency(usd, forwardTenor, Currency.SettlementType.SWAP);
            // May be null if swap currency doesn't exist
        }
        catch (Exception e)
        {
            fail("testCurrencyWithTenorAndSettlementType failed", e);
        }
    }
}
