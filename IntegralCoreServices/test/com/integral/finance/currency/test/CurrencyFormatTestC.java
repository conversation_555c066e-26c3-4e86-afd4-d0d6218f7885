package com.integral.finance.currency.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.test.TestCaseC;

import java.text.DecimalFormat;

public class CurrencyFormatTestC
        extends TestCaseC
{
    public CurrencyFormatTestC( String name )
    {
        super( name );
    }

    public static String[] patterns = {
            "\u00A4 #,##;(#,##)",
            "\u00A4 #,##;-#,##",
            "\u00A4 #,##.##; -#,##.##",
            "\u00A4 #,##.00; -#,##.00",
            "#,##;(#,##) \u00A4\u00A4",
            "#,##;-#,## \u00A4\u00A4",
            "#,##.00; -#,##.00 \u00A4\u00A4"};

    public void testCurrencyFormatter()
    {
        Currency eur = CurrencyFactory.getCurrency( "EUR" );
        DecimalFormat df = eur.getDecimalFormat( "#,##" );
        log( "EUR=" + eur.getTickValue() + " format=" + df.toPattern() );
        DecimalFormat eurDF = new DecimalFormat( "#,##.00" );
        assertEquals( "EUR decinal format should contain 2 decimal palces precision.", eurDF.toPattern().equals( df.toPattern() ), true );


        Currency jpy = CurrencyFactory.getCurrency( "JPY" );
        df = jpy.getDecimalFormat( "#,##" );
        log( "JPY=" + jpy.getTickValue() + " format=" + df.toPattern() );
        DecimalFormat jpyDF = new DecimalFormat( "#,##" );
        assertEquals( "JPY decinal format should contain 0 decimal palces precision.", jpyDF.toPattern().equals( df.toPattern() ), true );
    }

    public void testFormatter()
    {
        Currency ccy = new CurrencyC();
        ccy.setShortName( "ABC" );
        ccy.setPrefixCharacter( '@' );
        ccy.setTickValue( 0.1 );

        log( "Currency object shortName: " + ccy.getShortName() );
        log( "Currency object prefix char: " + ccy.getPrefixCharacter() );
        log( "Currency object tickValue: " + ccy.getTickValue() );

        DecimalFormat format;
        String pattern;

        for ( int i = 0; i < patterns.length; i++ )
        {
            format = new DecimalFormat( patterns[i] );
            format = ccy.getDecimalFormat( format );
            format.applyPattern( patterns[i] );
            log( "Test format orig pattern: " + patterns[i] );
            log( "\tTest format modified pattern: " + format.toPattern() );
            log( "\tTest format modified (localized) pattern: " + format.toLocalizedPattern() );
            log( "\tNumber\t\t " + new Double( 100.45 ) );
            log( "\tPositive\t\t" + format.format( new Double( 100.45 ) ) );
            log( "\tNegative\t\t" + format.format( new Double( -100.45 ) ) );
        }


        for ( int i = 0; i < patterns.length; i++ )
        {
            format = ccy.getDecimalFormat( patterns[i] );
            log( "Test format orig pattern: " + patterns[i] );
            log( "\tTest format modified pattern: " + format.toPattern() );
            log( "\tTest format modified (localized) pattern: " + format.toLocalizedPattern() );
            log( "\tNumber\t\t " + new Double( 100.45 ) );
            log( "\tPositive\t\t" + format.format( new Double( 100.45 ) ) );
            log( "\tNegative\t\t" + format.format( new Double( -100.45 ) ) );
        }

        log( "Currency Format Test done" );

    }
}