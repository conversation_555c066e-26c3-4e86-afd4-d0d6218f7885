package com.integral.finance.currency.test;

import com.integral.finance.country.CountryFactory;
import com.integral.finance.currency.CurrencyC;
import com.integral.test.TestCaseC;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

public class CurrencyTestC
        extends TestCaseC
{
    static String name = "Currency Test";

    public CurrencyTestC( String name )
    {
        super( name );
    }

    public void testCountries()
    {
        log( "testCountries" );
        try
        {
            // get collection
            Collection countries = CountryFactory.getISOCountries();
            Iterator it = countries.iterator();
            while ( it.hasNext() )
            {
                Object country = it.next();
                log( "adding country " + country + " of class " + country.getClass() );
            }
            int num = countries.size() * 2 + ( countries.size() - 1 );
            log( "country string size = " + num );

            // create new currency
            CurrencyC crnc = new CurrencyC();
            assertEquals( 0, crnc.getCountries().size() );

            // add countries
            crnc.setCountries( countries );
            assertEquals( countries.size(), crnc.getCountries().size() );

            // reset transient member
            crnc.resetTransients();
            assertEquals( countries.size(), crnc.getCountries().size() );

            // clear countries
            crnc.setCountries( new ArrayList() );
            assertEquals( 0, crnc.getCountries().size() );

            // clear countries
            crnc.setCountries( null );
            assertEquals( 0, crnc.getCountries().size() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCountries" );
        }
    }
}
