package com.integral.finance.currency.test;

import com.integral.finance.currency.*;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateBasisC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR> jessy
 *         To change this template use File | Settings | File Templates.
 */
public class SyntheticCurrencyPairGroupPTestC extends PTestCaseC
{
    public void testInsertSyntheticCrossCurrencyPairGroup()
    {
        try
        {
            Session session = getPersistenceSession();
            long time = System.currentTimeMillis();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass(CurrencyC.class);
            uow.addReadOnlyClass(FXRateBasisC.class);
            SyntheticCurrencyPairGroup synGroup = new SyntheticCurrencyPairGroupC();
            SyntheticCurrencyPairGroup regSynGroup = (SyntheticCurrencyPairGroup) uow.registerObject( synGroup );
            regSynGroup.setName("Test" +time);
            regSynGroup.setVehicleCurrency(CurrencyFactory.getCurrency("USD"));
            ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();
            FXRateConvention fxRateConvention =(FXRateConvention) namedEntityReader.execute(FXRateConventionC.class, "STDQOTCNV");
            FXRateBasis one = getFXRateBasis( fxRateConvention, "USD/JPY" );
            CurrencyPairGroup basegroup = regSynGroup.getBaseCurrencyPairGroup();
            basegroup.getFXRateBasisReferences().add(one);
            CurrencyPairGroup termgroup = regSynGroup.getVariableCurrencyPairGroup();
            FXRateBasis two = getFXRateBasis( fxRateConvention, "EUR/USD" );
            termgroup.getFXRateBasisReferences().add(two);

            uow.commit();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( NamedEntity.ShortName ).equal("Test" +time);
            SyntheticCurrencyPairGroup strFromDB = (SyntheticCurrencyPairGroup) getPersistenceSession().readObject( SyntheticCurrencyPairGroup.class, expr );
            assertNotNull( strFromDB );
            log.info( "SyntheticCrossCurrencyPairGroup from DB=" + strFromDB );
            assertEquals(strFromDB.getVehicleCurrency(),regSynGroup.getVehicleCurrency());
            assertEquals(strFromDB.getVariableCurrencyPairGroup().getCurrencyPairs().size(),1);
            Collection lists = strFromDB.getVariableCurrencyPairGroup().getCurrencyPairs();
            assertEquals(lists.toArray()[0],two.getCurrencyPair());
            System.out.println(strFromDB+" "+lists.toString());
            assertEquals(strFromDB.getBaseCurrencyPairGroup().getCurrencyPairs().size(),1);
            Collection lists2 = strFromDB.getBaseCurrencyPairGroup().getCurrencyPairs();
            assertEquals(lists2.toArray()[0],one.getCurrencyPair());
            System.out.println(strFromDB+" "+lists2.toString());
        }
        catch ( Exception e )
        {
            fail( "testInsertSyntheticCrossCurrencyPairGroup", e );
        }
    }


    public void testFieldsPersistence()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            long time = System.currentTimeMillis();
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass(CurrencyC.class);
            uow.addReadOnlyClass(FXRateBasisC.class);


            SyntheticCurrencyPairGroup regSynGroup = (SyntheticCurrencyPairGroup) uow.registerObject( new SyntheticCurrencyPairGroupC() );
            regSynGroup.setName("Test" +time);
            regSynGroup.setVehicleCurrency(CurrencyFactory.getCurrency("USD"));
            ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();
            FXRateConvention fxRateConvention =(FXRateConvention) namedEntityReader.execute(FXRateConventionC.class, "STDQOTCNV");
            FXRateBasis one = getFXRateBasis( fxRateConvention, "USD/JPY" );
            CurrencyPairGroup basegroup = regSynGroup.getBaseCurrencyPairGroup();
            basegroup.getFXRateBasisReferences().add(one);
            CurrencyPairGroup termgroup = regSynGroup.getVariableCurrencyPairGroup();
            FXRateBasis two = getFXRateBasis( fxRateConvention, "EUR/USD" );
            FXRateBasis three = getFXRateBasis( fxRateConvention, "GBP/USD" );
            termgroup.getFXRateBasisReferences().add(two);
            termgroup.getFXRateBasisReferences().add(three);
            uow.commit();


            regSynGroup = (SyntheticCurrencyPairGroup) session.refreshObject( regSynGroup );
//            log( regSynGroup.getToString() );
            assertEquals(regSynGroup.getName(), "Test" +time);
            assertEquals(regSynGroup.getVehicleCurrency(),CurrencyFactory.getCurrency("USD"));
            assertEquals(regSynGroup.getBaseCurrencyPairGroup().getCurrencyPairs().size(),1);
            Collection<FXRateBasis> lists = regSynGroup.getBaseCurrencyPairGroup().getFXRateBasisReferences();
            assertEquals(lists.toArray()[0],one);
            assertEquals(regSynGroup.getVariableCurrencyPairGroup().getCurrencyPairs().size(),2);
            Collection<FXRateBasis> lists2 = regSynGroup.getVariableCurrencyPairGroup().getFXRateBasisReferences();
            System.out.println(regSynGroup+" "+lists2.toString());

            //update the synthetic cross
            uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass(CurrencyC.class);
            uow.addReadOnlyClass(FXRateBasisC.class);

            SyntheticCurrencyPairGroup synone =(SyntheticCurrencyPairGroup) namedEntityReader.execute(SyntheticCurrencyPairGroup.class, "Test" +time);
            synone =  (SyntheticCurrencyPairGroup) uow.registerObject(synone);
            synone.getBaseCurrencyPairGroup().getFXRateBasisReferences().add(three);
            synone.getVariableCurrencyPairGroup().getFXRateBasisReferences().remove(two);
            uow.commit();

            regSynGroup = (SyntheticCurrencyPairGroup) session.refreshObject( regSynGroup );
//            log( regSynGroup.getToString() );

            assertEquals(regSynGroup.getBaseCurrencyPairGroup().getCurrencyPairs().size(),2);
            Collection<FXRateBasis> lists3 = regSynGroup.getBaseCurrencyPairGroup().getFXRateBasisReferences();
            assertEquals(regSynGroup.getVariableCurrencyPairGroup().getFXRateBasisReferences().size(),1);
            Collection<FXRateBasis> lists4 =regSynGroup.getVariableCurrencyPairGroup().getFXRateBasisReferences();
            assertEquals(lists4.toArray()[0],three);
            System.out.println(regSynGroup+" "+lists3.toString()+" "+lists4.toString());

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCrossCurrencyPair()
    {
        FXRateConvention fxRateConvention =(FXRateConvention) namedEntityReader.execute(FXRateConventionC.class, "STDQOTCNV");
        FXRateBasis one = getFXRateBasis( fxRateConvention, "USD/JPY" );
        FXRateBasis two = getFXRateBasis( fxRateConvention, "EUR/USD" );
        FXRateBasis three = getFXRateBasis( fxRateConvention, "GBP/USD" );
        FXRateBasis four = getFXRateBasis( fxRateConvention, "USD/AED" );
        FXRateBasis five = getFXRateBasis( fxRateConvention, "USD/CHF" );
        SyntheticCurrencyPairGroup SynGroup = new SyntheticCurrencyPairGroupC();
        SynGroup.setFXRateConvention(fxRateConvention);
        SynGroup.setVehicleCurrency(CurrencyFactory.getCurrency("USD"));
        SynGroup.getBaseCurrencyPairGroup().getFXRateBasisReferences().add(one);
        SynGroup.getBaseCurrencyPairGroup().getFXRateBasisReferences().add(two);
        SynGroup.getBaseCurrencyPairGroup().getFXRateBasisReferences().add(three);
        SynGroup.getVariableCurrencyPairGroup().getFXRateBasisReferences().add(four);
        SynGroup.getVariableCurrencyPairGroup().getFXRateBasisReferences().add(five);
        Collection<CurrencyPair> ccyPairs = SynGroup.getCurrencyPairs();
        System.out.println(ccyPairs);
        assertEquals(ccyPairs.size(),6);
        FXRateBasis excluded = getFXRateBasis(fxRateConvention, "EUR/CHF");//TODO this has to exsit
        Collection<FXRateBasis>  excludedList = new ArrayList<FXRateBasis>();
        excludedList.add(excluded);
        SynGroup.setExcludedFXRateBasis(excludedList);
        ccyPairs = SynGroup.getCurrencyPairs();
        System.out.println(SynGroup.getExcludedFXRateBasis());
        System.out.println(ccyPairs);
        assertEquals(ccyPairs.size(),5);
    }

    private FXRateBasis getFXRateBasis( FXRateConvention fxRateConvention, String ccyPairName )
    {
        Collection<FXRateBasis> col = fxRateConvention.getFXRateBasis();
        for ( FXRateBasis fxRateBasis : col )
        {
            if ( fxRateBasis.getName().equals( ccyPairName ) )
            {
                return fxRateBasis;
            }
        }
        return null;
    }
}
