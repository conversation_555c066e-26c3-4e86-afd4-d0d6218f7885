package com.integral.finance.currency.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyGroup;
import com.integral.finance.currency.CurrencyGroupC;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateBasisC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.persistence.Namespace;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationPropertyC;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

public class CurrencyPairGroupTestC extends PTestCaseC
{
    static String name = "CurrencyPairGroup Test";

    public CurrencyPairGroupTestC( String aName )
    {
        super( aName );
    }

    public void testCurrencyPairGroups()
    {
        try
        {
            CurrencyGroup baseCcyGroup;
            CurrencyGroup varCcyGroup;
            CurrencyPairGroup ccyPairGroup1, ccyPairGroup2;
            CurrencyPairGroup ccyPairGroup;

            ccyPairGroup = new CurrencyPairGroupC();
            baseCcyGroup = new CurrencyGroupC();
            varCcyGroup = new CurrencyGroupC();

            Currency usd, eur, jpy;

            usd = new CurrencyC();
            usd.setShortName( "USD" );

            eur = new CurrencyC();
            eur.setShortName( "EUR" );

            jpy = new CurrencyC();
            jpy.setShortName( "JPY" );

            baseCcyGroup.getIncludedCurrencies().add( usd );
            baseCcyGroup.getIncludedCurrencies().add( eur );
            varCcyGroup.getIncludedCurrencies().add( jpy );
            varCcyGroup.getIncludedCurrencies().add( eur );

            ccyPairGroup1 = new CurrencyPairGroupC();
            ccyPairGroup1.setBaseCurrencyGroup( baseCcyGroup );
            ccyPairGroup1.setVariableCurrencyGroup( varCcyGroup );
            print( ccyPairGroup1.getCurrencyPairs() );


            ccyPairGroup = new CurrencyPairGroupC();
            ccyPairGroup.getCurrencyPairGroups().add( ccyPairGroup1 );
            print( ccyPairGroup.getCurrencyPairs() );

            ccyPairGroup2 = new CurrencyPairGroupC();
            ccyPairGroup2.getCurrencyPairGroups().add( ccyPairGroup1 );
            ccyPairGroup2.setBaseCurrencyGroup( baseCcyGroup );
            ccyPairGroup2.setVariableCurrencyGroup( varCcyGroup );
            print( ccyPairGroup2.getCurrencyPairs() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }

    }

    public static void print( Collection c )
    {
        System.out.println( "*****************************" );
        Iterator it = c.iterator();
        while ( it.hasNext() )
        {
            System.out.println( it.next() );
        }
        System.out.println( "*****************************" );
    }

    public void testCurrencyPairGroupFieldsPersistence()
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            CurrencyPairGroup ccyPairGrp = ( CurrencyPairGroup ) new ReadNamedEntityC().execute( CurrencyPairGroup.class, "USD/All" );

            uow.removeAllReadOnlyClasses();
            ccyPairGrp = ( CurrencyPairGroup ) uow.registerObject( ccyPairGrp );
            int sort = ccyPairGrp.getSortOrder() + ( Math.random() < 0.5 ? -10 : 10 );
            ccyPairGrp.setSortOrder( sort );
            uow.commit();

            ccyPairGrp = ( CurrencyPairGroup ) PersistenceFactory.newSession().refreshObject( ccyPairGrp );
            assertEquals( ccyPairGrp.getSortOrder(), sort );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCurrencyPairGroupFieldsPersistence" );
        }
    }

    public void testCurrencyPairGroupExcluded()
    {
        try
        {
            String newCurrencyPairGroup = "NEWCCYGRP";
            ReadNamedEntityC entityC = new ReadNamedEntityC();
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            Namespace mainNS = ( Namespace ) entityC.execute( Namespace.class, "MAIN" );
            FXRateConvention fxRateConvention = ( FXRateConvention ) entityC.execute( com.integral.finance.fx.FXRateConventionC.class, "STDQOTCNV" );
            FXRateBasis excluded = getFXRateBasis( fxRateConvention, "USD/JPY" );

            ArrayList<CurrencyPairGroup> lstGroup = new ArrayList<CurrencyPairGroup>();
            CurrencyPairGroup ccyPairGrp = ( CurrencyPairGroup ) entityC.execute( CurrencyPairGroup.class, "USD/All" );
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( FXRateBasisC.class );
            uow.addReadOnlyClass( NamespaceC.class );

            CurrencyPairGroup newccyPairGrp = ( CurrencyPairGroup ) entityC.execute( CurrencyPairGroup.class, newCurrencyPairGroup );
            if ( null == newccyPairGrp )
            {
                newccyPairGrp = new CurrencyPairGroupC();
            }
            newccyPairGrp = ( CurrencyPairGroup ) uow.registerObject( newccyPairGrp );

            newccyPairGrp.setName( newCurrencyPairGroup );
            newccyPairGrp.setLongName( newCurrencyPairGroup );
            newccyPairGrp.setNamespace( mainNS );
            lstGroup.add( ( CurrencyPairGroup ) uow.registerObject( ccyPairGrp ) );
            newccyPairGrp.setCurrencyPairGroups( lstGroup );
            ArrayList<FXRateBasis> excludedFXRateBasis = new ArrayList<FXRateBasis>();
            excludedFXRateBasis.add( excluded );

            newccyPairGrp.setExcludedFXRateBasis( excludedFXRateBasis );
            uow.commit();

            newccyPairGrp = ( CurrencyPairGroup ) PersistenceFactory.newSession().refreshObject( newccyPairGrp );
            boolean isUSDJPYExist = newccyPairGrp.getCurrencyPairs().contains( excluded.getCurrencyPair() );
            assertFalse( "USD/JPY not exist in Currency Pair Group" + newCurrencyPairGroup, isUSDJPYExist );

            // test the direct query of excluded currency pairs.
            newccyPairGrp = ( CurrencyPairGroup ) IdcUtilC.refreshObject( newccyPairGrp );
            WatchPropertyC.update( ServerRuntimeMBean.CCYPAIRGROUP_EXCLUDED_CCYPAIRS_DIRECT_QUERY_ENABLED, "true", ConfigurationPropertyC.DYNAMIC_SCOPE );
            Collection<FXRateBasis> exclusionList = newccyPairGrp.getExcludedFXRateBasis();
            assertNotNull( exclusionList );
            assertTrue( exclusionList.contains( excluded ) );

            newccyPairGrp = ( CurrencyPairGroup ) IdcUtilC.refreshObject( newccyPairGrp );
            WatchPropertyC.update( ServerRuntimeMBean.CCYPAIRGROUP_EXCLUDED_CCYPAIRS_DIRECT_QUERY_ENABLED, "false", ConfigurationPropertyC.DYNAMIC_SCOPE );
            Collection<FXRateBasis> exclusionList1 = newccyPairGrp.getExcludedFXRateBasis();
            assertNotNull( exclusionList1 );
            assertTrue( exclusionList1.contains( excluded ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCurrencyPairGroupIncludedAndExcluded" );
        }
    }

    public void testCurrencyPairGroupIncluded()
    {
        try
        {
            String newCurrencyPairGroup = "NEWCCYGRP";
            ReadNamedEntityC entityC = new ReadNamedEntityC();
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            Namespace mainNS = ( Namespace ) entityC.execute( Namespace.class, "MAIN" );
            FXRateConvention fxRateConvention = ( FXRateConvention ) entityC.execute( com.integral.finance.fx.FXRateConventionC.class, "STDQOTCNV" );
            FXRateBasis includecluded = getFXRateBasis( fxRateConvention, "EUR/USD" );

            ArrayList<CurrencyPairGroup> lstGroup = new ArrayList<CurrencyPairGroup>();
            CurrencyPairGroup ccyPairGrp = ( CurrencyPairGroup ) entityC.execute( CurrencyPairGroup.class, "USD/All" );
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( FXRateBasisC.class );
            uow.addReadOnlyClass( NamespaceC.class );

            CurrencyPairGroup newccyPairGrp = ( CurrencyPairGroup ) entityC.execute( CurrencyPairGroup.class, newCurrencyPairGroup );
            if ( null == newccyPairGrp )
            {
                newccyPairGrp = new CurrencyPairGroupC();
            }
            newccyPairGrp = ( CurrencyPairGroup ) uow.registerObject( newccyPairGrp );

            newccyPairGrp.setName( newCurrencyPairGroup );
            newccyPairGrp.setLongName( newCurrencyPairGroup );
            newccyPairGrp.setNamespace( mainNS );
            lstGroup.add( ( CurrencyPairGroup ) uow.registerObject( ccyPairGrp ) );
            newccyPairGrp.setCurrencyPairGroups( lstGroup );

            ArrayList<FXRateBasis> includedFXRateBasis = new ArrayList<FXRateBasis>();
            includedFXRateBasis.add( includecluded );

            newccyPairGrp.setIncludedFXRateBasis( includedFXRateBasis );
            uow.commit();

            newccyPairGrp = ( CurrencyPairGroup ) PersistenceFactory.newSession().refreshObject( newccyPairGrp );
            boolean isEURUSDExist = newccyPairGrp.getCurrencyPairs().contains( includecluded.getCurrencyPair() );
            assertTrue( "EUR/USD  exist in Currency Pair Group " + newCurrencyPairGroup, isEURUSDExist );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testCurrencyPairGroupIncludedAndExcluded" );
        }
    }

    private FXRateBasis getFXRateBasis( FXRateConvention fxRateConvention, String ccyPairName )
    {
        Collection<FXRateBasis> col = fxRateConvention.getFXRateBasis();
        for ( FXRateBasis fxRateBasis : col )
        {
            if ( fxRateBasis.getName().equals( ccyPairName ) )
            {
                return fxRateBasis;
            }
        }
        return null;
    }

    public void testContains()
    {
        try
        {
            Collection<CurrencyPairGroup> cpgs = ( Collection<CurrencyPairGroup> ) PersistenceFactory.newSession().readAllObjects( CurrencyPairGroupC.class );
            for ( CurrencyPairGroup cpg : cpgs )
            {
                Collection<CurrencyPair> ccyPairs = cpg.getCurrencyPairs();
                for ( CurrencyPair cp : ccyPairs )
                {
                    assertTrue( cpg.contains( cp ) );
                }

                UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
                uow.removeAllReadOnlyClasses();
                CurrencyPairGroup registeredCpg = ( CurrencyPairGroup ) uow.registerObject( cpg );
                for ( CurrencyPair cp : ccyPairs )
                {
                    assertTrue( registeredCpg.contains( cp ) );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testContains", e );
        }
    }

    public void testPrivatelyOwnedCurrencyPairGroupLookup()
    {
        try
        {
            String newCurrencyPairGroup = "CAD/All";
            ReadNamedEntityC entityC = new ReadNamedEntityC();
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            FXRateConvention fxRateConvention = ( FXRateConvention ) entityC.execute( com.integral.finance.fx.FXRateConventionC.class, "STDQOTCNV" );
            FXRateBasis cadJpy = getFXRateBasis( fxRateConvention, "CAD/JPY" );
            FXRateBasis cadUsd = getFXRateBasis( fxRateConvention, "USD/CAD" );
            Collection included = new ArrayList();
            included.add( cadJpy );
            included.add( cadUsd );

            Organization[] orgs = ReferenceDataCacheC.getInstance().getOrgs();
            Namespace ns = null;
            for ( Organization org: orgs )
            {
                if ( org == null || org.getNamespace() == null || IdcUtilC.MAIN_NAMESPACE.equals( org.getNamespace().getShortName() ))
                {
                    continue;
                }
                CurrencyPairGroup aCpg = (CurrencyPairGroup) ReferenceDataCacheC.getInstance().getEntityByShortName( newCurrencyPairGroup, CurrencyPairGroup.class, org.getNamespace(), null );
                if ( aCpg == null || IdcUtilC.MAIN_NAMESPACE.equals( aCpg.getNamespace().getShortName() ))
                {
                    ns = org.getNamespace();
                }
            }

            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( FXRateBasisC.class );
            uow.addReadOnlyClass( NamespaceC.class );


            CurrencyPairGroup cpg = new CurrencyPairGroupC();
            CurrencyPairGroup registeredCpg = ( CurrencyPairGroup ) uow.registerObject( cpg );

            registeredCpg.setName( newCurrencyPairGroup );
            registeredCpg.setLongName( newCurrencyPairGroup );
            registeredCpg.setNamespace( ns );
            registeredCpg.setPrivatelyOwned( true );
            registeredCpg.setIncludedFXRateBasis( included );
            uow.commit();

            cpg = ( CurrencyPairGroup ) PersistenceFactory.newSession().refreshObject( cpg );
            assertNotNull( cpg );

            CurrencyPairGroup queriedCpg = (CurrencyPairGroup) ReferenceDataCacheC.getInstance().getEntityByShortName( newCurrencyPairGroup, CurrencyPairGroup.class, ns, null );
            if ( queriedCpg != null )
            {
                assertEquals(  IdcUtilC.MAIN_NAMESPACE, queriedCpg.getNamespace().getShortName());
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testPrivatelyOwnedCurrencyPairGroupLookup" );
        }
    }

}

		