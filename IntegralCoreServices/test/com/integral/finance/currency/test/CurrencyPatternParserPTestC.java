package com.integral.finance.currency.test;


import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPatternParserC;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.util.PatternParser;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.text.ParseException;


public class CurrencyPatternParserPTestC
        extends PTestCaseC
{
    static String name = "Currency Pattern Parser Test";

    public CurrencyPatternParserPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new CurrencyPatternParserPTestC( "testFormat" ) );
        suite.addTest( new CurrencyPatternParserPTestC( "testParse" ) );
        return suite;
    }


    public void testFormat()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "USD" );
            Currency usd = ( Currency ) PersistenceFactory.newSession().readObject( Currency.class, expr );

            String pattern = "%id %sn %iso %dn %pre %sym %pf";
            PatternParser epp = new CurrencyPatternParserC( pattern );
            try
            {
                System.out.println( "PATTERN[" + pattern + "] ==> <" + epp.format( usd ) + '>' );
            }
            catch ( ParseException pe )
            {
            }
        }
        catch ( Exception e )
        {
            fail( "Exception " + e );
        }
    }

    public void testParse()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "USD" );
            Currency usd = ( Currency ) PersistenceFactory.newSession().readObject( Currency.class, expr );

            // lookup by short name
            //
            String pattern = "%sn";
            String text = "USD";
            PatternParser epp = new CurrencyPatternParserC( pattern );
            try
            {
                Object obj = epp.parseObject( Currency.class, text );
                System.out.println( "PATTERN[" + pattern + "] ==> <" + obj + '>' );
            }
            catch ( ParseException pe )
            {
            }
            catch ( IdcNoSuchObjectException nsoe )
            {
            }

            // lookup by display name
            //
            pattern = "%dn";
            text = "USD";
            epp = new CurrencyPatternParserC( pattern );
            try
            {
                Object obj = epp.parseObject( Currency.class, text );
                System.out.println( "PATTERN[" + pattern + "] ==> <" + obj + '>' );
            }
            catch ( ParseException pe )
            {
            }
            catch ( IdcNoSuchObjectException nsoe )
            {
            }

            // lookup by iso code
            //
            pattern = "%iso";
            text = "USD";
            epp = new CurrencyPatternParserC( pattern );
            try
            {
                Object obj = epp.parseObject( Currency.class, text );
                System.out.println( "PATTERN[" + pattern + "] ==> <" + obj + '>' );
            }
            catch ( ParseException pe )
            {
            }
            catch ( IdcNoSuchObjectException nsoe )
            {
            }
        }
        catch ( Exception e )
        {
            fail( "Exception " + e );
        }
    }

}
