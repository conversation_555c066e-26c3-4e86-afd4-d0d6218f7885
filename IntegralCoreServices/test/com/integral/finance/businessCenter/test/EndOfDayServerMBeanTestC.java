package com.integral.finance.businessCenter.test;

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.businessCenter.configuration.EndOfDayServiceMBean;
import com.integral.finance.businessCenter.configuration.EndOfDayServiceMBeanC;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class EndOfDayServerMBeanTestC extends MBeanTestCaseC
{
    EndOfDayServiceMBeanC endOfDayServiceMBean = ( EndOfDayServiceMBeanC ) EndOfDayServiceFactory.getEndOfDayServiceMBean();

    public EndOfDayServerMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( endOfDayServiceMBean, "endOfDayServiceSchedulerEnabled", EndOfDayServiceMBean.ENDOFDAY_SCHEDULER_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( endOfDayServiceMBean, "preHandlersTriggerOffset", EndOfDayServiceMBean.ENDOFDAY_PRE_HANDLERS_TRIGGER_OFFSET, MBeanTestCaseC.LONG );
    }
}
