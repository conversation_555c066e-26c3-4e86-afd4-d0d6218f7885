package com.integral.finance.businessCenter.test;

import com.integral.exception.IdcException;
import com.integral.finance.businessCenter.EndOfDayServerC;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.businessCenter.NZDEndOfDaySchedulerHandlerC;
import com.integral.finance.config.Finance;
import com.integral.finance.config.FinanceConfigurationFactory;
import com.integral.finance.config.FinanceMBean;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.system.configuration.ConfigurationProperty;
import junit.framework.TestCase;

import java.util.Calendar;
import java.util.TimeZone;


/**
 * <AUTHOR> Development Corporation.
 */
public class EndOfDayServerTestC extends TestCase
{

    public void testNZDEndOfDayRollTime()
    {
        try
        {
            EndOfDayServerC endOfDayServer = EndOfDayServiceFactory.getEndOfDayService().getEndOfDayServer();
            
            FinanceMBean financeMBean = FinanceConfigurationFactory.getFinanceMBean();
            Finance.setNZDRollTimeSchedulerHandler( new NZDEndOfDaySchedulerHandlerC() );

            TestNZDRollTimeHandler handler = new TestNZDRollTimeHandler();
            endOfDayServer.addNZDEndOfDayHandler( handler );

            // check the handler invoke is false
            assertEquals( handler.isInvoked(), false );

            // get the current time and add 30 second delay
            Calendar cal = Calendar.getInstance();
            cal.setTimeZone( TimeZone.getTimeZone( "GMT" ) );
            cal.add( Calendar.SECOND, 30 );
            String hour = String.valueOf( cal.get( Calendar.HOUR_OF_DAY ) );
            String min = String.valueOf( cal.get( Calendar.MINUTE ) );
            String sec = String.valueOf( cal.get( Calendar.SECOND ) );
            String rollTime = hour + ":" + min + ":" + sec;

            // set the property with 30 seconds + the current time
            ( ( Finance ) financeMBean ).setProperty( "IDC.ROLLTIME.NZD.CHECK.ENABLED", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            ( ( Finance ) financeMBean ).setProperty( "IDC.ROLLTIME.NZD.IN.GMT", rollTime, ConfigurationProperty.DYNAMIC_SCOPE, null );

            // add sleep time of 35 seconds
            Thread.sleep( 35000 );

            // check that the handler should be invoked
            assertEquals( handler.isInvoked(), true );

            //reset the handler to false
            handler.setInvoked( false );
            assertEquals( handler.isInvoked(), false );

            //disable the property and set invoked to false
            Calendar cal2 = Calendar.getInstance();
            cal2.setTimeZone( TimeZone.getTimeZone( "GMT" ) );
            cal2.add( Calendar.SECOND, 30 );
            hour = String.valueOf( cal.get( Calendar.HOUR_OF_DAY ) );
            min = String.valueOf( cal.get( Calendar.MINUTE ) );
            sec = String.valueOf( cal.get( Calendar.SECOND ) );
            rollTime = hour + ":" + min + ":" + sec;
            ( ( Finance ) financeMBean ).setProperty( "IDC.ROLLTIME.NZD.CHECK.ENABLED", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            ( ( Finance ) financeMBean ).setProperty( "IDC.ROLLTIME.NZD.IN.GMT", rollTime, ConfigurationProperty.DYNAMIC_SCOPE, null );

            Thread.sleep( 35000 );
            System.out.println( "sleep is done" );
            // even after the time elapse the invoked should be false
            assertEquals( handler.isInvoked(), false );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }

    }

    /**
     * Test NZD Eod update rolltime handler class.
     */
    protected class TestNZDRollTimeHandler implements MessageHandler
    {
        boolean invoked = false;

        public Message handle( Message message ) throws IdcException
        {
            setInvoked( true );
            return null;
        }

        public boolean isInvoked()
        {
            return invoked;
        }

        public void setInvoked( boolean invoked )
        {
            this.invoked = invoked;
        }
    }
}
