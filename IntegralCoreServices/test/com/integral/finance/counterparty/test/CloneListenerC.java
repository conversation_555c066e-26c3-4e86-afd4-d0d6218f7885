package com.integral.finance.counterparty.test;

import com.integral.finance.counterparty.CounterpartyContact;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.CloneListener;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;

import java.util.Vector;

class CloneListenerC implements CloneListener
{
    Session session;
    Namespace ns;
    private Log log = LogFactory.getLog( getClass() );

    CloneListenerC( Session aSession )
    {
        session = aSession;

        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).equal( "MAIN" );

        ReadAllQuery query = new ReadAllQuery();
        query.setReferenceClass( Namespace.class );
        query.setSelectionCriteria( expr );
        Vector nss = ( Vector ) session.executeQuery( query );
        ns = ( Namespace ) nss.elementAt( 0 );

    }

    public void postClone( Object theOriginal, Object theClone )
    {
        Entity entity = ( Entity ) theClone;
        log.info( "@@@@@@@@@ postClone " + theClone + '#' + entity.getObjectID()
                + '/' + theClone.getClass() );
        if ( theClone instanceof CounterpartyContact )
        {
            CounterpartyContact contact = ( CounterpartyContact ) theOriginal;
            if ( contact.getCounterparty() != null )
            {
                log.info( "orig contact parent: " + contact.getCounterparty().getObjectID() );
            }
            else
            {
                log.info( "orig contact parent: NULL" );
            }

            CounterpartyContact contactc = ( CounterpartyContact ) theClone;
            if ( contactc.getCounterparty() != null )
            {
                log.info( "clone contact parent: " + contactc.getCounterparty().getObjectID() );
            }
            else
            {
                log.info( "clone contact parent: NULL" );
            }
        }
        entity.setNamespace( ns );
        entity.setStatus( 'X' );
    }
}
