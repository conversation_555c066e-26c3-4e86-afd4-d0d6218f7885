package com.integral.finance.counterparty.test;

import com.integral.SEF.SEFUtilC;
import com.integral.finance.counterparty.CounterpartyC;
import com.integral.finance.counterparty.CounterpartyContact;
import com.integral.finance.counterparty.CounterpartyContactC;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.dealing.TradeChannelSupport;
import com.integral.finance.dealing.TradeChannelSupportC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Contact;
import com.integral.user.ContactC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class LegalEntityPTestC extends PTestCaseC
{
    public static final String name = "LegalEntityPTestC";

    public LegalEntityPTestC( String name )
    {
        super( name );
    }

    public void testTradingPartyLookup()
    {
        try
        {
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( LegalEntityC.class );
            LegalEntity testLe = ( LegalEntity ) new ReadNamedEntityC().execute( LegalEntityC.class, "CITI-le1" );
            Organization fi = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            TradingParty tp = testLe.getTradingParty( fi );
            log( "tp=" + tp );
            assertNotNull( tp );
            TradingParty registeredLe = ( TradingParty ) uow.registerObject( testLe );
            registeredLe.update();
            uow.commit();

            tp = testLe.getTradingParty( fi );
            assertNotNull( tp );

            // refresh the trade and retrieve the maker request.
            testLe = ( LegalEntity ) session.refreshObject( testLe );
            tp = testLe.getTradingParty( fi );
            log( "testLe=" + testLe + ",testLe.tp=" + tp );
            assertNotNull( tp );
            Collection<TradingParty> tps = testLe.getTradingParties();
            for ( TradingParty trdParty : tps )
            {
                TradingParty cpty = testLe.getTradingParty( trdParty.getOrganization() );
                assertNotNull( cpty );
                assertEquals( "tp should be the same. trdParty=" + trdParty, cpty, trdParty );
            }
        }
        catch ( Exception e )
        {
            fail( "Exception in trading party lookup", e );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( LegalEntityC.class );
            uow.removeReadOnlyClass( ContactC.class );
            uow.removeReadOnlyClass( CounterpartyContactC.class );
            
            LegalEntity test1 = ( LegalEntity ) uow.registerObject( new LegalEntityC() );
            LegalEntity test2 = ( LegalEntity ) uow.registerObject( new LegalEntityC() );
            test1.setShortName( "test1" + System.currentTimeMillis() );
            test2.setShortName( "test2" + System.currentTimeMillis() );

            // set trade channel support;
            TradeChannelSupport trdChnlSup1 = new TradeChannelSupportC();
            trdChnlSup1.setRFSSpotSupported( true );
            trdChnlSup1.setESPSwapSupported( true );
            test1.setTradeChannelSupport( trdChnlSup1 );

            TradeChannelSupport trdChnlSup2 = new TradeChannelSupportC();
            trdChnlSup2.setESPOutrightSupported( true );
            trdChnlSup2.setNDFSupported( true );
            test2.setTradeChannelSupport( trdChnlSup2 );

            // SEF fields.
            int SSN = *********;
            String regType = "MSP";
            test1.setTaxId( SSN );
            test1.setRegType( regType );
            Timestamp startDate = new Timestamp( System.currentTimeMillis() );
            test1.setStartDate( startDate );
            Contact contact = ( Contact ) uow.registerObject( new ContactC() );
            contact.setShortName( "test" + System.currentTimeMillis() );
            test1.setDefaultContact( contact );
            CounterpartyContact counterpartyContact = new CounterpartyContactC();
            counterpartyContact.setShortName( contact.getShortName() + "0" );
            counterpartyContact.setContactFunction( SEFUtilC.LE_CONTACT_TYPE_OTHR );
            counterpartyContact.setContactFunctionDesc( "TEST" );
            ( ( CounterpartyC ) test1 ).addContact( counterpartyContact );
            String executingFirmId = "TESTEXECFIRMID";
            test1.setExecutingFirmId(executingFirmId);
            String lei = "TESTLEI";
            test1.setLEI( lei );
            test1.setInterimLEI( true );
            String USJurisdiction = "US";
            test1.setJurisdiction( USJurisdiction );
            test1.setSDRDownload( true );
            test1.setEndUserException( true );
            test1.setSEFType( CounterpartyUtilC.SEFTYPE_SEFMEMBER );

            //MiFID Fields
            User user = (User) uow.registerObject(new UserC());
            user.setShortName ( "Test" + System.currentTimeMillis () );
            test1.setMiFIDInvDecisionMaker(true);
            test1.setMiFIDExecFirm( true );
            test1.setMiFIDShortCode( "ShortCode" );
            test1.setMiFIDExecutingUser( user );
            
            uow.commit();
            test1 = ( LegalEntity ) session.refreshObject( test1 );
            test2 = ( LegalEntity ) session.refreshObject( test2 );

            // test trade channel support.
            assertNotNull( test1.getTradeChannelSupport() );
            assertNotNull( test2.getTradeChannelSupport() );

            assertTrue( test1.getTradeChannelSupport().isESPSpotSupported() );
            assertFalse( test1.getTradeChannelSupport().isESPOutrightSupported() );
            assertTrue( test1.getTradeChannelSupport().isESPSwapSupported() );
            assertTrue( test1.getTradeChannelSupport().isRFSSpotSupported() );
            assertFalse( test1.getTradeChannelSupport().isRFSOutrightSupported() );
            assertFalse( test1.getTradeChannelSupport().isRFSSwapSupported() );
            assertFalse( test1.getTradeChannelSupport().isNDFSupported() );

            assertTrue( test2.getTradeChannelSupport().isESPSpotSupported() );
            assertFalse( test2.getTradeChannelSupport().isESPSwapSupported() );
            assertTrue( test2.getTradeChannelSupport().isESPOutrightSupported() );
            assertFalse( test2.getTradeChannelSupport().isRFSSpotSupported() );
            assertFalse( test2.getTradeChannelSupport().isRFSOutrightSupported() );
            assertFalse( test2.getTradeChannelSupport().isRFSSwapSupported() );
            assertTrue( test2.getTradeChannelSupport().isNDFSupported() );
            
            assertEquals( SSN, test1.getTaxId() );
            assertEquals( regType, test1.getRegType() );
            assertEquals( startDate, test1.getStartDate() );
            assertTrue( contact.isSameAs( test1.getDefaultContact() ) );
            assertEquals( executingFirmId, test1.getExecutingFirmId() );
            assertEquals( lei, test1.getLEI() );
            assertTrue( test1.isInterimLEI() );
            assertEquals( USJurisdiction, test1.getJurisdiction() );
            assertTrue( test1.isSDRDownload() );
            assertTrue( test1.isEndUserException() );
            assertEquals( CounterpartyUtilC.SEFTYPE_SEFMEMBER, test1.getSEFType() );
            assertTrue( test1.isSEFMember() );
            //assertFalse( test1.isPermissiveSEFMember() );
            Collection contacts = test1.getContacts();
            boolean hasCounterpartyContact = false;
            for( Object contact1 : contacts )
            {
                if( contact1 instanceof CounterpartyContactC )
                {
                    assertTrue( SEFUtilC.LE_CONTACT_TYPE_OTHR.equals( ( ( CounterpartyContact ) contact1 ).getContactFunction() ) );
                    assertTrue( "TEST".equals( ( ( CounterpartyContact ) contact1 ).getContactFunctionDesc() ) );
                    hasCounterpartyContact = true;
                    break;
                }
            }
            assertTrue( hasCounterpartyContact );
            assertTrue( test1.getCptyIndex() > 0 );
            assertTrue( test1.getIndex() > 0 );
            assertTrue( test1.isMiFIDExecFirm());
            assertTrue( test1.isMiFIDInvDecisionMaker());
            assertEquals( test1.getMiFIDShortCode(), "ShortCode");
            assertTrue( user.isSameAs( test1.getMiFIDExecutingUser()) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testLegalEntityIndices()
    {
        try
        {
            Collection<LegalEntity> les = getPersistenceSession().readAllObjects( LegalEntityC.class );
            assertTrue( les != null && !les.isEmpty() );
            List<Integer> leIndices = new ArrayList<Integer>();
            List<Integer> cptyIndices = new ArrayList<Integer>();

            for ( LegalEntity le: les )
            {
                assertFalse( leIndices.contains(le.getIndex()));
                leIndices.add(le.getIndex());

                assertFalse( cptyIndices.contains( le.getCptyIndex() ));
                cptyIndices.add( le.getCptyIndex() );
            }
        }
        catch ( Exception e )
        {
            fail ( "testLegalEntityIndices", e );
        }
    }


}

