package com.integral.finance.counterparty.test;

import java.util.Collection;

import com.integral.finance.counterparty.*;
import com.integral.finance.dealing.TradeChannelSupport;
import com.integral.finance.dealing.TradeChannelSupportC;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCache;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.internal.oxm.Reference;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

public class CounterpartyPTestC extends PTestCaseC
{
    public static final String name = "CounterpartyPTestC";

    public CounterpartyPTestC( String name )
    {
        super( name );

    }

    public static void main( String[] args )
    {
        try
        {
            CounterpartyPTestC test = new CounterpartyPTestC( name );
            test.testFieldsPersistence();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );

            TradingParty test1 = ( TradingParty ) uow.registerObject( new TradingPartyC() );
            TradingParty test2 = ( TradingParty ) uow.registerObject( new TradingPartyC() );
            test1.setShortName( "test1" + System.currentTimeMillis() );
            test2.setShortName( "test2" + System.currentTimeMillis() );
            test1.setAnonymous( true );
            test2.setSTPEnabled( Counterparty.NO_STP_DOWNLOAD_ENABLED );
            test2.setStpDownloadQueue( "TESTQUEUE" );
            test2.setStpDownloadFormat( "TESTFORMAT" );
            test2.setShowOrigCptyUserInSTP( Counterparty.YES_SHOW_ORIG );
            test2.setSTPDownloadPerspective( Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
            test2.setSTPOriginatingDetails( Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            assertTrue( test1.isPrimeBrokerSTPEnabled() );
            assertTrue( test2.isPrimeBrokerSTPEnabled() );
            test1.setPrimeBrokerSTPEnabled( true );
            test2.setPrimeBrokerSTPEnabled( false );
            test2.setSendSpreadsInSTP( true );
            test1.setSTPMappingExternalName( "BANX" );
            test1.setSTPMappingExternalFullName( "BANK_OF_AMERICA_CORPORATION" );
            test1.setFIXSTPSession( "TestFIXSession1" );
            test2.setFIXSTPSession( "TestFIXSession2" );
            String lei1 = "testLEI1";
            test2.setLEI( lei1 );
            String lei2 = "testLEI2";
            test2.setLEI( lei2 );
            test1.setInterimLEI( true );
            test1.setAlias("testAlias");

            // set trade channel support;
            TradeChannelSupport trdChnlSup1 = new TradeChannelSupportC();
            trdChnlSup1.setRFSSpotSupported( true );
            trdChnlSup1.setESPSwapSupported( true );
            test1.setTradeChannelSupport( trdChnlSup1 );

            TradeChannelSupport trdChnlSup2 = new TradeChannelSupportC();
            trdChnlSup2.setESPOutrightSupported( true );
            trdChnlSup2.setNDFSupported( true );
            test2.setTradeChannelSupport( trdChnlSup2 );

            CounterpartyParentRelationship parentRelationship = new CounterpartyParentRelationshipC();
            parentRelationship = ( CounterpartyParentRelationship ) uow.registerNewObject( parentRelationship );
            parentRelationship.setChild( test1 );
            parentRelationship.setParent( test2 );
            
            uow.commit();
            test1 = ( TradingParty ) session.refreshObject( test1 );
            test2 = ( TradingParty ) session.refreshObject( test2 );
            log( "TradingParty=" + test1 + ",test1.isAnonymous=" + test1.isAnonymous() );
            log( "TradingParty=" + test2 + ",test2.isAnonymous=" + test2.isAnonymous() );
            assertEquals( "testTradingPartyIsAnonymous should be true .", test1.isAnonymous(), true );
            assertEquals( "testTradingPartyIsAnonymous should be false .", test2.isAnonymous(), false );
            assertEquals( test1.getSTPEnabled(), Counterparty.DEFAULT_STP_DOWNLOAD_ENABLED ); // testing for default behaviour
            assertEquals( test1.getSTPDownloadPerspective(), Counterparty.DEFAULT_STP_DOWNLOAD_PERSPECTIVE ); // testing for default behaviour
            assertEquals( test1.getSTPOriginatingDetails(), Counterparty.DEFAULT_ORIGINATING_DETAILS ); // testing for default behaviour
            assertEquals( test2.getSTPEnabled(), Counterparty.NO_STP_DOWNLOAD_ENABLED );
            assertEquals( test2.getSTPDownloadPerspective(), Counterparty.MAKER_STP_DOWNLOAD_PERSPECTIVE );
            assertEquals( test2.getSTPOriginatingDetails(), Counterparty.TRADING_PARTY_ORIGINATING_DETAILS );
            assertEquals( test1.getShowOrigCptyUserInSTP(), Counterparty.DEFAULT_SHOW_ORIG ); // testing for default behaviour
            assertEquals( test2.getShowOrigCptyUserInSTP(), Counterparty.YES_SHOW_ORIG );
            assertEquals( test2.getStpDownloadFormat(), "TESTFORMAT" );
            assertEquals( test2.getStpDownloadQueue(), "TESTQUEUE" );
            assertEquals( test2.isSendSpreadsInSTP(), true );
            assertEquals( test1.isSendSpreadsInSTP(), false );
            assertTrue( test1.isPrimeBrokerSTPEnabled() );
            assertFalse( test2.isPrimeBrokerSTPEnabled() );
            assertEquals( test1.getSTPMappingExternalName(), "BANX" );
            assertEquals( test1.getSTPMappingExternalFullName(), "BANK_OF_AMERICA_CORPORATION" );
            assertEquals( test1.getFIXSTPSession(), "TestFIXSession1" );
            assertEquals( test2.getFIXSTPSession(), "TestFIXSession2" );
            assertEquals( test1.getLEI(), test1.getLegalEntity() != null ? test1.getLegalEntity().getLEI() : null );
            assertEquals( test2.getLEI(), test1.getLegalEntity() != null ? test1.getLegalEntity().getLEI() : null );
            assertTrue( test1.isInterimLEI() );
            assertEquals(test1.getAlias(), "testAlias");
            
            // test trade channel support.
            assertNotNull( test1.getTradeChannelSupport() );
            assertNotNull( test2.getTradeChannelSupport() );

            assertTrue( test1.getTradeChannelSupport().isESPSpotSupported() );
            assertFalse( test1.getTradeChannelSupport().isESPOutrightSupported() );
            assertTrue( test1.getTradeChannelSupport().isESPSwapSupported() );
            assertTrue( test1.getTradeChannelSupport().isRFSSpotSupported() );
            assertFalse( test1.getTradeChannelSupport().isRFSOutrightSupported() );
            assertFalse( test1.getTradeChannelSupport().isRFSSwapSupported() );
            assertFalse( test1.getTradeChannelSupport().isNDFSupported() );

            assertTrue( test2.getTradeChannelSupport().isESPSpotSupported() );
            assertFalse( test2.getTradeChannelSupport().isESPSwapSupported() );
            assertTrue( test2.getTradeChannelSupport().isESPOutrightSupported() );
            assertFalse( test2.getTradeChannelSupport().isRFSSpotSupported() );
            assertFalse( test2.getTradeChannelSupport().isRFSOutrightSupported() );
            assertFalse( test2.getTradeChannelSupport().isRFSSwapSupported() );
            assertTrue( test2.getTradeChannelSupport().isNDFSupported() );
            
            Collection< CounterpartyParentRelationship > parentRelationships = test1.getParentRelationships();
            assertTrue( parentRelationships.size() == 1 );
            for( CounterpartyParentRelationship relationship : parentRelationships )
            {
                assertTrue( test2.isSameAs( relationship.getParent() ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testNewTradingPartyLookupByIndex()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );

            TradingParty test1 = ( TradingParty ) uow.registerObject( new TradingPartyC() );
            TradingParty test2 = ( TradingParty ) uow.registerObject( new TradingPartyC() );
            test1.setShortName( "test1" + System.currentTimeMillis() );
            test2.setShortName( "test2" + System.currentTimeMillis() );

            uow.commit();
            test1 = ( TradingParty ) session.refreshObject( test1 );
            test2 = ( TradingParty ) session.refreshObject( test2 );

            ReferenceDataCacheC referenceDataCacheC = ReferenceDataCacheC.getInstance ();
            referenceDataCacheC.resetCounterpartiesArray ();

            assertEquals ( test1, ReferenceDataCacheC.getInstance ().getTradingParty ( test1.getIndex () ) );
            assertEquals ( test1, ReferenceDataCacheC.getInstance ().getCounterparty ( test1.getCptyIndex () ) );

            assertEquals ( test2, ReferenceDataCacheC.getInstance ().getTradingParty ( test2.getIndex () ) );
            assertEquals ( test2, ReferenceDataCacheC.getInstance ().getCounterparty ( test2.getCptyIndex () ) );
        }
        catch ( Exception e )
        {
            fail ( "testNewTradingPartyLookupByIndex", e );
        }
    }

    public void testNewLegalEntityLookupByIndex()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( LegalEntityC.class );

            LegalEntity test1 = ( LegalEntity ) uow.registerObject( new LegalEntityC() );
            LegalEntity test2 = ( LegalEntity ) uow.registerObject( new LegalEntityC() );
            test1.setShortName( "test1" + System.currentTimeMillis() );
            test2.setShortName( "test2" + System.currentTimeMillis() );

            uow.commit();
            test1 = ( LegalEntity ) session.refreshObject( test1 );
            test2 = ( LegalEntity ) session.refreshObject( test2 );

            ReferenceDataCacheC referenceDataCacheC = ReferenceDataCacheC.getInstance ();
            referenceDataCacheC.resetCounterpartiesArray ();

            assertEquals ( test1, ReferenceDataCacheC.getInstance ().getLegalEntity ( test1.getIndex () ) );
            assertEquals ( test1, ReferenceDataCacheC.getInstance ().getCounterparty ( test1.getCptyIndex () ) );

            assertEquals ( test2, ReferenceDataCacheC.getInstance ().getLegalEntity ( test2.getIndex () ) );
            assertEquals ( test2, ReferenceDataCacheC.getInstance ().getCounterparty ( test2.getCptyIndex () ) );
        }
        catch ( Exception e )
        {
            fail ( "testNewLegalEntityLookupByIndex", e );
        }
    }
}

