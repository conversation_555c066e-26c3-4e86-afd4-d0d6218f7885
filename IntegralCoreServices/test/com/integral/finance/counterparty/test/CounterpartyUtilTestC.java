package com.integral.finance.counterparty.test;

import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.OrganizationRelationship;
import com.integral.user.OrganizationRelationshipC;
import com.integral.user.OrganizationRelationshipClassification;
import com.integral.util.IdcUtilC;

import java.util.Collection;

public class CounterpartyUtilTestC extends PTestCaseC
{
    static String name = "Counterparty Util Test";
    protected ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();

    public CounterpartyUtilTestC( String name )
    {
        super( name );
    }

    public void testGetTradingParties()
    {
        Organization citi = ( Organization ) namedEntityReader.execute( Organization.class, "CITI" );
        Collection tps = citi.getTradingParties( "LP_to_FI" );
        if ( tps.size() == 0 )
        {
            fail( "CITI should have more than zero trading parties" );
        }

        tps = citi.getTradingParties( "FI_to_LP" );
        if ( tps.size() != 0 )
        {
            fail( "CITI cannot have more than 0 FI_to_LP relationship" );
        }
    }

    public void testClearingHouse()
    {
        LegalEntity FILE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "SEFFI1" );
        LegalEntity LPLE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "SEFLP1" );
        LegalEntity CM1LE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "SEFCM1" );
        LegalEntity CM2LE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "SEFCM2" );
        LegalEntity CHLE = ( LegalEntity ) namedEntityReader.execute( LegalEntity.class, "SEFCH1" );

        if ( FILE == null || LPLE == null || CM1LE == null || CM2LE == null || CHLE == null )
        {
            return;
        }


        try
        {
            assertNull( CounterpartyUtilC.getClearingHouse( FILE, LPLE ) );

            FILE.setLEI( "TESTLEI" );
            LPLE.setLEI( "TESTLEI" );
            
            CM1LE.setLEI( "TESTLEI" );
            CM1LE.setSEFType(CounterpartyUtilC.SEFTYPE_CLEARINGMEMBER);
            CM2LE.setLEI( "TESTLEI" );
            CM2LE.setSEFType(CounterpartyUtilC.SEFTYPE_CLEARINGMEMBER);
            
            CHLE.setLEI( "TESTLEI" );
            CHLE.getOrganization().setClearingHouse( true );

            Organization CH = CounterpartyUtilC.getClearingHouse( FILE, LPLE );
            assertTrue( CHLE.getOrganization().isSameAs( CH ) );
        }
        finally
        {
            FILE.setLEI( null );
            LPLE.setLEI( null );
            CM1LE.setLEI( null );
            CM2LE.setLEI( null );
            CHLE.setLEI( null );
            CHLE.getOrganization().setClearingHouse( false );
        }
    }

    public void testTakerOrgGroup()
    {
        Organization fi = null;
        Organization lp = null;
        try
        {
            Organization[] allOrgs = ReferenceDataCacheC.getInstance().getOrgs();
            for ( Organization org : allOrgs )
            {
                Collection<Organization> orgGrp = CounterpartyUtilC.getTakerOrgGroup( org, null );
                System.out.println( "org=" + org + ",orgGroup=" + orgGrp );
            }

            // now do a specific test to see if 

            fi = ReferenceDataCacheC.getInstance().getOrganization( "FI1" );
            lp = ReferenceDataCacheC.getInstance().getOrganization( "DBNB" );
            addOrgRelationship( fi, lp, CounterpartyUtilC.LP_FI_RELATIONSHIP );

            Collection<Organization> orgGrp1 = CounterpartyUtilC.getTakerOrgGroup( fi, null );
            System.out.println( "fi=" + fi + ",orgGroup1=" + orgGrp1 );
            Collection<Organization> orgGrp2 = CounterpartyUtilC.getTakerOrgGroup( lp, null );
            System.out.println( "lp=" + lp + ",orgGrp2=" + orgGrp2 );
        }
        catch ( Exception e )
        {
            fail( "testTakerOrgGroup", e );
        }
        finally
        {
            IdcUtilC.refreshObject( fi );
            IdcUtilC.refreshObject( lp );
        }
    }
    
    private void addOrgRelationship( Organization org, Organization relatedOrg, String clsf )
    {
        if ( !org.getRelatedOrganizations( clsf ).contains( relatedOrg ))
        {
            OrganizationRelationship orgRel = new OrganizationRelationshipC();
            orgRel.setOwner( org );
            orgRel.setRelatedOrganization( relatedOrg );
            orgRel.setClassification( (OrganizationRelationshipClassification) ReferenceDataCacheC.getInstance().getEntityByShortName( clsf, OrganizationRelationshipClassification.class, null, null ) );
            org.getOrganizationRelationships().add( orgRel );
            (( OrganizationC ) org).resetTransients();
        }
    }
}
