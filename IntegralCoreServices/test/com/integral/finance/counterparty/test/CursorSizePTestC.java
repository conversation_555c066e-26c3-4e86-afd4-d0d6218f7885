package com.integral.finance.counterparty.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.persistence.NamedEntity;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Hashtable;

public class CursorSizePTestC
        extends PTestCaseC
{
    static String name = "Counterparty Cursor Size Test";

    private Hashtable objectMap;

    public CursorSizePTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        try
        {
            CursorSizePTestC test = new CursorSizePTestC( name );
            test.run();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void testPerform()
    {
        queryCpty();
    }

    public void queryCpty()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            log( " searching for all legal entities - cursored query" );

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( "A" );

            ReadAllQuery query = new ReadAllQuery();
            query.setReferenceClass( LegalEntity.class );
            query.useCursoredStream( 10, 10 );
            query.setSelectionCriteria( expr );

            CursoredStream stream = ( CursoredStream ) uow.executeQuery( query );
            log( "  getting estimated number of legal entities" );
            int sizeEstimated = stream.size();
            log( "# legal entities estimated: " + sizeEstimated );
            int i = 1;
            while ( !stream.atEnd() )
            {
                Object obj = stream.read();
                NamedEntity ent = ( NamedEntity ) obj;
                log( "  trade #" + ( i - 1 ) + " , oid = <" + ent.getObjectID() + ">, "
                        + " SN=<" + ent.getShortName() + ">, "
                        + " LN=<" + ent.getLongName() + ">, "
                        + " class = <" + obj.getClass().getName() + '>' );

                if ( i % 100 == 0 )
                {
                    uow.commitAndResume();
                    stream.releasePrevious();
                }
                i++;
            }
            log( "# legal entities estimated: " + sizeEstimated );
            log( "# legal entities retrieved: " + ( i - 1 ) );

            uow.commit();
            log( "query legal entities" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "query legal entities" );
        }
    }
}
