package com.integral.finance.counterparty.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.persistence.Namespace;
import com.integral.test.PTestCaseC;
import com.integral.user.DisplayPreference;
import com.integral.user.Organization;
import com.integral.user.User;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.HashMap;
import java.util.Vector;

public class SetDefaultLegalEntityCPTestC
        extends PTestCaseC
{
    static String name = "SetDefaultLegalEntityCPTestC Test";

    public SetDefaultLegalEntityCPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        try
        {
            SetDefaultLegalEntityCPTestC test = new SetDefaultLegalEntityCPTestC( name );
            test.run();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void testDefaultLegalEntity()
    {
        preloadReferenceData();
        setLE();
    }

    public void preloadReferenceData()
    {
        getPersistenceSession().readAllObjects( Namespace.class );
        getPersistenceSession().readAllObjects( Organization.class );
        getPersistenceSession().readAllObjects( User.class );
    }

    public void setLE()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            // workaround for caching problem
            uow.readAllObjects( Organization.class );
            uow.readAllObjects( User.class );

            // read all LE
            log( " searching for LE" );
            HashMap<Namespace, LegalEntity> cptyMap = new HashMap<Namespace, LegalEntity>();
            Vector cpties = uow.readAllObjects( LegalEntity.class );
            if ( cpties != null )
            {
                for ( int j = 0; j < cpties.size(); j++ )
                {
                    LegalEntity le = ( LegalEntity ) cpties.elementAt( j );


                    if ( le.getNamespace().getShortName().startsWith( "NS_PMEM" ) )
                    {
                        log( "\tle name = " + le.getShortName()
                                + "\t ns = " + le.getNamespace().getShortName() );

                        LegalEntity c2 = cptyMap.get( le.getNamespace() );
                        if ( c2 != null )
                        {
                            log( "\t########## have already LE for namespace" );
                        }
                        cptyMap.put( le.getNamespace(), le );
                    }
                }
            }

            log( " searching for user prefs" );
            Vector prefs = uow.readAllObjects( User.class );

            if ( prefs != null )
            {
                for ( int i = 0; i < prefs.size(); i++ )
                {
                    User user = ( User ) prefs.elementAt( i );
                    if ( user.getOrganization() != null && user.getOrganization().getNamespace() != null && user.getOrganization().getNamespace().getShortName().startsWith( "NS_PMEM" ) )
                    {
                        DisplayPreference pref = user.getDisplayPreference();
                        log( "\tpref oid = " + pref.getObjectID()
                                + "\t ns  = " + user.getOrganization().getNamespace().getShortName() );

                        LegalEntity le = cptyMap.get( user.getOrganization().getNamespace() );
                        if ( le == null )
                        {
                            log( "\t########## cannot found LE for user" );
                        }
                        else
                        {
                        }
                    }
                }
            }

            // cleanup
            uow.commit();
            log( "update prefs" );
        }
        catch ( Exception e )
        {
            fail( "update prefs", e );
        }
    }
}
