package com.integral.finance.counterparty.test;

import com.integral.SEF.SEFUtilC;
import com.integral.broker.model.ExecutionRule;
import com.integral.broker.model.ExecutionRuleC;
import com.integral.broker.model.Stream;
import com.integral.broker.model.StreamC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.dealing.TradeChannelSupport;
import com.integral.finance.dealing.TradeChannelSupportC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.Session;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class TradingPartyPTestC extends PTestCaseC
{
    public static final String name = "TradingPartyPTestC";

    public TradingPartyPTestC( String name )
    {
        super( name );

    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            org.eclipse.persistence.sessions.UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );
            TradingParty testTp = new TradingPartyC();
            testTp.setShortName( "TestTp" + System.nanoTime() );
            testTp.setStatus( 'T' );
            TradingParty registeredTp = ( TradingParty ) uow.registerObject( testTp );
            registeredTp.setAnonymous( true );
            boolean flag = registeredTp.isAnonymous();
            ExecutionRule rule = ( ExecutionRule ) uow.registerObject( new ExecutionRuleC() );
            rule.setShortName( System.currentTimeMillis() + "" );
            registeredTp.setBrokerExecutionRule( rule );
            Stream stream = ( Stream ) uow.registerObject( new StreamC() );
            stream.setShortName( System.currentTimeMillis() + "" );

            registeredTp.setBrokerStream( stream );
            log( "anonymous=" + flag );

            final boolean sendSpreads = registeredTp.isSendSpreadsInSTP();
            final boolean newSendSpreads = !sendSpreads;
            registeredTp.setSendSpreadsInSTP( newSendSpreads );

            final boolean isCoverTradeEnabled = registeredTp.isPrimeBrokerCoverTradeEnabled();
            final boolean newCoverTradeEnabled = !isCoverTradeEnabled;
            registeredTp.setPrimeBrokerCoverTradeEnabled( newCoverTradeEnabled );

            // set trade channel support;
            TradeChannelSupport trdChnlSup1 = new TradeChannelSupportC();
            trdChnlSup1.setRFSSpotSupported( true );
            trdChnlSup1.setESPSwapSupported( true );
            registeredTp.setTradeChannelSupport( trdChnlSup1 );

            registeredTp.setReportingParty( 2 );
            registeredTp.setGenerateUSI( true );
            registeredTp.setUSIGenerator( SEFUtilC.TP_USE_GENERATED_USI );
            uow.commit();

            // refresh the trade and retrieve the maker request.
            testTp = ( TradingParty ) session.refreshObject( testTp );
            log( "testTp=" + testTp + ",testTp.anonymous=" + testTp.isAnonymous() );
            assertEquals( "anonymous flag should be true.", testTp.isAnonymous(), true );
            assertNotNull( testTp.getBrokerStream() );
            assertNotNull( testTp.getBrokerExecutionRule() );
            assertEquals( testTp.isSendSpreadsInSTP(), newSendSpreads );
            assertEquals( testTp.isPrimeBrokerCoverTradeEnabled(), newCoverTradeEnabled );
            assertNotNull( testTp.getTradeChannelSupport() );
            assertTrue( testTp.getReportingParty() == 2);
            assertTrue( testTp.isGenerateUSI() );
            assertEquals( SEFUtilC.TP_USE_GENERATED_USI, testTp.getUSIGenerator() );
            // check default flags
            TradingParty newTP = new TradingPartyC();
            assertFalse( newTP.isSendSpreadsInSTP() );
            assertFalse( newTP.isPrimeBrokerageCreditUsed() );
            assertFalse( newTP.isAnonymous() );
            assertFalse( newTP.isPrimeBrokerCoverTradeEnabled() );


            assertTrue( testTp.getTradeChannelSupport().isESPSpotSupported() );
            assertFalse( testTp.getTradeChannelSupport().isESPOutrightSupported() );
            assertTrue( testTp.getTradeChannelSupport().isESPSwapSupported() );
            assertTrue( testTp.getTradeChannelSupport().isRFSSpotSupported() );
            assertFalse( testTp.getTradeChannelSupport().isRFSOutrightSupported() );
            assertFalse( testTp.getTradeChannelSupport().isRFSSwapSupported() );
            assertFalse( testTp.getTradeChannelSupport().isNDFSupported() );
            assertTrue( testTp.getCptyIndex() > 0 );
            assertTrue( testTp.getIndex() > 0 );
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }

    public void testTradingPartyIndices()
    {
        try
        {
            Collection<TradingParty> tps = getPersistenceSession().readAllObjects( TradingPartyC.class );
            assertTrue( tps != null && !tps.isEmpty() );
            List<Integer> tpIndices = new ArrayList<Integer>();
            List<Integer> cptyIndices = new ArrayList<Integer>();

            for ( TradingParty tp: tps )
            {
                assertFalse( tpIndices.contains( tp.getIndex() ));
                tpIndices.add( tp.getIndex() );

                assertFalse( cptyIndices.contains( tp.getCptyIndex() ));
                cptyIndices.add( tp.getCptyIndex() );
            }
        }
        catch ( Exception e )
        {
            fail ( "testTradingPartyIndices", e );
        }
    }
}

