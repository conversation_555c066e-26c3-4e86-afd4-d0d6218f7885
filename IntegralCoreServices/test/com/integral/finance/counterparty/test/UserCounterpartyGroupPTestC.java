package com.integral.finance.counterparty.test;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.UserCounterpartyGroup;
import com.integral.finance.counterparty.UserCounterpartyGroupC;
import com.integral.finance.counterparty.UserCounterpartyGroupUtils;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.User;
import com.integral.user.UserC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Vector;


public class UserCounterpartyGroupPTestC
        extends PTestCaseC
{
    static String name = "Deal Test";

    public UserCounterpartyGroupPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new UserCounterpartyGroupPTestC( "testSalesDealerGroupsForTradingParty" ) );
        // suite.addTest(new OrganizationPTestC("testSelectOrgRel"));
        return suite;
    }

    public void testSalesDealerGroupsForTradingParty()
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();

            //Query for users and set them in our old counterparty group - get fiproxy1 and fiproxy2 users
            ReadAllQuery raqLe1 = new ReadAllQuery( LegalEntityC.class, ( eb.get( "shortName" ).equal( "FI1-le1" ) ) );
            Vector vect1 = ( Vector ) session.executeQuery( raqLe1 );
            LegalEntity lecpty = ( LegalEntity ) vect1.elementAt( 0 );

            ReadAllQuery raqOrg = new ReadAllQuery( OrganizationC.class, eb.get( "shortName" ).equal( "FI1" ) );
            Vector vect2 = ( Vector ) session.executeQuery( raqOrg );
            Organization org1 = ( Organization ) vect2.elementAt( 0 );

            Collection<UserCounterpartyGroup> salesDealerColl = UserCounterpartyGroupUtils.getSalesDealerGroupsForTradingParty( lecpty, org1 );

            for ( UserCounterpartyGroup sdg : salesDealerColl )
            {
                System.out.println( "sdg.getName()" + sdg.getName() );
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }

    }

    /**
     * This test requires a UserCounterpartyGroup in DB and two users atleast - change value 502 and fi1proxy1 and fi1proxy2 users in program.
     * This is test for bugs - > 32512, 32517, 32518, 32564
     */
    public void testSetUsers()
    {
        try
        {
            System.out.println( "about to run usercounterparty set-users test" );
            Session session = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();

            //Query for users and set them in our old counterparty group - get fiproxy1 and fiproxy2 users
            ReadAllQuery raqUser1 = new ReadAllQuery( UserC.class, ( eb.get( "shortName" ).equal( "fi1proxy1" ) ) );
            Vector vect1 = ( Vector ) session.executeQuery( raqUser1 );

            ReadAllQuery raqUser2 = new ReadAllQuery( UserC.class, eb.get( "shortName" ).equal( "fi1proxy2" ) );
            Vector vect2 = ( Vector ) session.executeQuery( raqUser2 );

            // Query for UserCounterparty group
            ReadAllQuery query = new ReadAllQuery( eb );
            query.setReferenceClass( UserCounterpartyGroupC.class );
            query.useCollectionClass( ArrayList.class );
            query.setSelectionCriteria( eb.get( "objectID" ).equal( 502 ) );
            ArrayList result = ( ArrayList ) session.executeQuery( query );

            System.out.println( "Query completed, Numner of User counterparty groups obtained iss - " + result.size() );
            UserCounterpartyGroupC ucg = null;

            System.out.println( "############# Sales deal group - BEFORE ##################" );
            // print out existing user counterparty group details (User counterparty group name and associated users) for reference
            for ( int i = 0; i < result.size(); i++ )
            {
                UserCounterpartyGroupC row = ( UserCounterpartyGroupC ) result.get( i );
                Collection coll = row.getUsers();
                ucg = row;
                System.out.println( "row #" + i + ":\t" + row + " ~~ workgroup name iss ~ " + row.getShortName() );
                System.out.println( "Number of users associated to workgroup = " + coll.size() );
                Iterator iter1 = coll.iterator();
                while ( iter1.hasNext() )
                {
                    User usr = ( User ) iter1.next();
                    System.out.println( "user name iss " + usr.getShortName() );
                }
            }
            System.out.println( "############# Users - BEFORE ##################" );

            // print out user counterparty groups associated to the user -->
            System.out.println( "About to search for fiproxy1 and fi1proxy2 users" );
            ReadAllQuery raqUser = new ReadAllQuery( UserC.class, ( eb.get( "shortName" ).equal( "fi1proxy1" ) ).or( eb.get( "shortName" ).equal( "fi1proxy2" ) ) );
            Vector resultUser = ( Vector ) session.executeQuery( raqUser );
            System.out.println( "users found- " + resultUser.size() );
            for ( int i = 0; i < resultUser.size(); i++ )
            {
                UserC row1 = ( UserC ) resultUser.get( i );
                Collection coll = row1.getCurrentWorkGroups();
                Iterator iter1 = coll.iterator();
                System.out.println( "Users List #" + i + ":\t" + row1 );
                while ( iter1.hasNext() )
                {
                    UserCounterpartyGroupC usrGrp = ( UserCounterpartyGroupC ) iter1.next();
                    System.out.println( "user counterparty groups Associated to user are " + usrGrp.getShortName() );
                }
            }
            System.out.println( "############# BEFORE ##################" );

//############################### Main method for data manipulation #######################
            UserC user2 = ( UserC ) vect2.elementAt( 0 );
            UserC user1 = ( UserC ) vect1.elementAt( 0 );
            UnitOfWork uow = session.acquireUnitOfWork();
            UserCounterpartyGroupC ucg1 = ( UserCounterpartyGroupC ) uow.registerObject( ucg );
            ArrayList al = new ArrayList( 2 );
            // ADD or REMOVE comment in front of following 2 lines to add / remove users from the collection.
            // This collection will be sent to setUsers method.

            al.add( uow.registerObject( user2 ) );
            al.add( uow.registerObject( user1 ) );

            System.out.println( " ADDing users " + al + "to set Users()" );
            ucg1.setUsers( al );
            uow.commit();
//###############################                                   #######################


            System.out.println( "\n\n\n############# Sales dealer group - AFTER ##################" );
            // print out user counterparty group details again to see that user changes have gotten reflected
            // The user counterparty group should now show the 'newly added users' to the group - REmoved users should have got removed
            ArrayList result1 = ( ArrayList ) session.executeQuery( query );
            System.out.println( "AFTER UPDATE - Workgroup data " + result1.size() );
            for ( int i = 0; i < result1.size(); i++ )
            {
                UserCounterpartyGroupC row1 = ( UserCounterpartyGroupC ) result1.get( i );
                Collection coll = row1.getUsers();
                Iterator iter1 = coll.iterator();
                System.out.println( "WorkGroup is #" + i + ":\t" + row1 );
                while ( iter1.hasNext() )
                {
                    User usr = ( User ) iter1.next();
                    System.out.println( "Associated users to the workgroup are " + usr.getShortName() );
                }
            }

            System.out.println( "############# Users - AFTER ##################" );
            // print out user counterparty groups associated to the user -->
            resultUser = ( Vector ) session.executeQuery( raqUser );
            System.out.println( "AFTER UPDATE - About to print counterparty groups associated to user -> " );
            for ( int i = 0; i < resultUser.size(); i++ )
            {
                UserC row1 = ( UserC ) resultUser.get( i );
                Collection coll = row1.getCurrentWorkGroups();
                Iterator iter1 = coll.iterator();
                System.out.println( "Users List #" + i + ":\t" + row1 );
                while ( iter1.hasNext() )
                {
                    UserCounterpartyGroupC usrGrp = ( UserCounterpartyGroupC ) iter1.next();
                    System.out.println( "user counterparty groups Associated to user are " + usrGrp.getShortName() );
                }
            }
            System.out.println( "############# AFTER ##################" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }
}
