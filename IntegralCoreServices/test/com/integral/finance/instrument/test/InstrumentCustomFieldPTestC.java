package com.integral.finance.instrument.test;


import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyC;
import com.integral.finance.creditLimit.CurrencyPositionCollectionC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.instrument.Instrument;
import com.integral.finance.instrument.InstrumentFactory;
import com.integral.persistence.CustomField;
import com.integral.persistence.Entity;
import com.integral.test.PTestCaseC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.user.User;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Timestamp;
import java.util.Iterator;
import java.util.Vector;

public class InstrumentCustomFieldPTestC
        extends PTestCaseC
{
    public InstrumentCustomFieldPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new InstrumentCustomFieldPTestC( "createSuccess" ) );
        suite.addTest( new InstrumentCustomFieldPTestC( "createFailure" ) );
        suite.addTest( new InstrumentCustomFieldPTestC( "createPersistence" ) );
        suite.addTest( new InstrumentCustomFieldPTestC( "testEntity" ) );
        suite.addTest( new InstrumentCustomFieldPTestC( "testInstrument" ) );
        return suite;
    }

    public void createSuccess()
    {
        try
        {
            //
            // value test
            //

            // null
            CustomField o0 = InstrumentFactory.newInstrumentCustomField();
            o0.setKey( "key0" );
            assertNull( o0.getValue() );

            // int
            CustomField o1 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key1" );
            Integer v1 = 1;
            o1.setInteger( v1 );
            assertEquals( o1.getInteger(), v1 );

            // long
            CustomField o2 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key2" );
            Long v2 = ( long ) 2;
            o2.setLong( v2 );
            assertEquals( o2.getLong(), v2 );

            // double
            CustomField o3 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key3" );
            Double v3 = 3.3;
            o3.setDouble( v3 );
            assertEquals( o3.getDouble(), 3.3, 0 );

            // char
            CustomField o4 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key4" );
            Character v4 = 'a';
            o4.setChar( v4 );
            assertEquals( o4.getChar(), v4 );

            // boolean
            CustomField o5 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key5" );
            Boolean v5 = Boolean.FALSE;
            o5.setBoolean( v5 );
            assertEquals( o5.getBoolean(), v5 );

            CustomField o6 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key6" );
            Boolean v6 = Boolean.FALSE;
            o6.setBoolean( v6 );
            assertEquals( o6.getBoolean(), v6 );

            // String
            CustomField o7 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key7" );
            String v7 = "abc";
            o7.setValue( v7 );
            assertEquals( o7.getValue(), v7 );

            // IdcDate & IdcDateTime
            CustomField o8 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key7b" );
            IdcDate v8 = DateTimeFactory.timeNow();
            o8.setValue( v8 );
            assertEquals( o8.getValue(), v8 );


            CustomField o8b = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key8" );
            IdcDateTime v8b = DateTimeFactory.timeNowTime();
            o8b.setValue( v8b );
            assertEquals( o8b.getValue(), v8b );

            // Timestamp
            CustomField o9 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key9" );
            Timestamp v9 = new Timestamp( 0 );
            o9.setValue( v9 );
            assertEquals( o9.getValue(), v9 );

            // Entity
            CustomField o11 = InstrumentFactory.newInstrumentCustomField();
            o1.setKey( "key11" );
            Entity v11 = CurrencyFactory.newCurrency( "BB" );
            o11.setValue( v11 );
            assertEquals( o11.getValue(), v11 );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "positive creation" );
        }

    }

    public void createFailure()
    {
        try
        {
            try
            {
                // illegal object
                CustomField o1 = InstrumentFactory.newInstrumentCustomField();
                o1.setValue( new CurrencyPositionCollectionC() );
                fail( "set custom field to exception object" );
            }
            catch ( Exception e )
            {
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "negative creation" );
        }

    }

    public void createPersistence()
    {
        try
        {
            CustomField objs[] = new CustomField[100];
            int i = 0;

            // UOW
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            // null
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertNull( objs[i].getValue() );
            i++;

            // int
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            Integer v1 = new Integer( 1 );
            objs[i].setInteger( v1 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getInteger(), v1 );
            i++;

            // long
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            Long v2 = new Long( 2 );
            objs[i].setLong( v2 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getLong(), v2 );
            i++;

            // double
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            Double v3 = 3.3;
            objs[i].setDouble( v3 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getDouble(), 3.3, 0 );
            i++;

            // char
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            Character v4 = 'a';
            objs[i].setChar( v4 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getChar(), v4 );
            i++;

            // boolean
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            Boolean v5 = Boolean.FALSE;
            objs[i].setBoolean( v5 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getBoolean(), v5 );
            i++;

            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            Boolean v6 = Boolean.TRUE;
            objs[i].setBoolean( v6 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getBoolean(), v6 );

            i++;
            // String
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            String v7 = "abc";
            objs[i].setValue( v7 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getValue(), v7 );
            i++;

            // IdcDate
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            IdcDate v8 = DateTimeFactory.timeNow();
            objs[i].setValue( v8 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getValue(), v8 );
            i++;

            // IdcDateTime
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            IdcDateTime v8b = DateTimeFactory.timeNowTime();
            objs[i].setValue( v8b );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getValue(), v8b );
            i++;

            // Timestamp
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            Timestamp v9 = new Timestamp( 0 );
            objs[i].setValue( v9 );
            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            assertEquals( objs[i].getValue(), v9 );
            i++;

            // Entity
            objs[i] = InstrumentFactory.newInstrumentCustomField();
            objs[i].setKey( "key" + i );
            Entity v11 = ( Entity ) uow.readObject( CurrencyC.class );
            objs[i].setValue( v11 );

            objs[i] = ( CustomField ) uow.registerObject( objs[i] );
            if ( objs[i].getValue() instanceof Entity )
            {
                Entity v11_p = ( Entity ) objs[i].getValue();
                assertTrue( ( ( v11_p instanceof Currency )
                        && ( v11.getClass() == v11_p.getClass() )
                        && ( v11.getObjectID() == v11_p.getObjectID() ) ) );
            }
            else
            {
                fail( "Entity" );
            }
            i++;

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "positive persistence creation" );
        }

    }


    public void testEntity()
    {
        try
        {
            try
            {
                // UnsupportedOperationException
                CurrencyC crnc = new CurrencyC();
                crnc.putCustomField( "try", "abc" );
            }
            catch ( Exception e )
            {
                e.printStackTrace();
                fail();
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Instrument test" );
        }

    }

    public void testInstrument()
    {
        try
        {
            String BACKOFFICEID_KEY = "backOfficeId";
            String RELATEDCRNC_KEY = "relatedCrnc";

            int i = 0;
            String shortName = "abc";

            //
            // Phase 1: add objects and save them to DB
            //

            // UOW
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();

            // retrieve Counterparty

            Counterparty cpty = ( Counterparty ) uow1.readObject( CounterpartyC.class );
            // create Instrument
            Instrument instrument = CurrencyFactory.newCurrency();
            instrument = ( Instrument ) uow1.registerObject( instrument );
            instrument.setShortName( shortName );

            // add two custom fields
            instrument.putCustomField( BACKOFFICEID_KEY, "abc123" );
            instrument.putCustomField( RELATEDCRNC_KEY, cpty );

            // commit
            uow1.commit();

            //
            // Phase 2: fetch Instrument and display custom fields
            //

            // UOW
            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();

            // query Instrument
            ReadAllQuery query2 = new ReadAllQuery();
            query2.setReferenceClass( Instrument.class );
            query2.setSelectionCriteria( new ExpressionBuilder().get( "shortName" ).equal( shortName ) );
            query2.addJoinedAttribute( query2.getExpressionBuilder().anyOf( "customFields" ) );
            Vector instrs2 = ( Vector ) uow2.executeQuery( query2 );

            // display custom fields
            if ( instrs2 != null && instrs2.size() != 0 )
            {
                for ( i = 0; i < instrs2.size(); i++ )
                {
                    Instrument instrument2 = ( Instrument ) instrs2.elementAt( i );

                    // display all custom fields
                    log( "\tall custom fields" );
                    Iterator it = instrument2.customFieldKeySet().iterator();
                    while ( it.hasNext() )
                    {
                        String key = ( String ) it.next();
                        Object value = instrument2.getCustomField( key );
                        log( "\t\tkey = " + key + ", value = " + value );
                    }

                    // display specific custom fields
                    log( "\tspecific custom fields" );
                    if ( instrument2.containsCustomFieldKey( BACKOFFICEID_KEY ) )
                    {
                        String backOfficeId = ( String ) instrument2.getCustomFieldValue( BACKOFFICEID_KEY );
                        log( "\t\tback office ID = " + backOfficeId );
                    }
                    if ( instrument2.containsCustomFieldKey( RELATEDCRNC_KEY ) )
                    {
                        Entity relatedCrnc = ( Entity ) instrument2.getCustomFieldValue( RELATEDCRNC_KEY );
                        log( "\t\trelated Currency = #" + relatedCrnc.getObjectID() );
                    }

                }
            }

            // release
            uow2.release();

            //
            // Phase 3: fetch instrument and update custom fields
            //

            // UOW
            UnitOfWork uow3 = getPersistenceSession().acquireUnitOfWork();

            // retrieve user
            User user = ( User ) uow3.readObject( User.class );

            // query Instrument
            ReadAllQuery query3 = new ReadAllQuery();
            query3.setReferenceClass( Instrument.class );
            query3.setSelectionCriteria( new ExpressionBuilder().get( "shortName" ).equal( shortName ) );
            query3.addJoinedAttribute( query3.getExpressionBuilder().anyOf( "customFields" ) );
            Vector instrs3 = ( Vector ) uow3.executeQuery( query3 );

            if ( instrs3 != null && instrs3.size() != 0 )
            {
                for ( i = 0; i < instrs3.size(); i++ )
                {
                    Instrument instrument3 = ( Instrument ) instrs3.elementAt( i );

                    // add two custom fields
                    instrument3.putCustomField( BACKOFFICEID_KEY, "XYZ987" );
                    instrument3.putCustomField( RELATEDCRNC_KEY, user );
                }
            }
            // commit
            uow3.commit();

            //
            // Phase 4: fetch instrument and display custom fields
            //

            // UOW
            UnitOfWork uow4 = getPersistenceSession().acquireUnitOfWork();

            // query instrument
            ReadAllQuery query4 = new ReadAllQuery();
            query4.setReferenceClass( Instrument.class );
            query4.setSelectionCriteria( new ExpressionBuilder().get( "shortName" ).equal( shortName ) );
            query4.addJoinedAttribute( query4.getExpressionBuilder().anyOf( "customFields" ) );
            Vector instrs4 = ( Vector ) uow4.executeQuery( query4 );

            // display custom fields
            if ( instrs4 != null && instrs4.size() != 0 )
            {
                for ( i = 0; i < instrs4.size(); i++ )
                {
                    Instrument instrument4 = ( Instrument ) instrs4.elementAt( i );

                    // display all custom fields
                    log( "\tAll custom fields" );
                    Iterator it = instrument4.customFieldKeySet().iterator();
                    while ( it.hasNext() )
                    {
                        String key = ( String ) it.next();
                        Object value = instrument4.getCustomField( key );
                        log( "\t\tkey = " + key + ", value = " + value );
                    }

                    // display specific custom fields
                    log( "\tSpecific custom fields" );
                    if ( instrument4.containsCustomFieldKey( BACKOFFICEID_KEY ) )
                    {
                        String backOfficeId = ( String ) instrument4.getCustomFieldValue( BACKOFFICEID_KEY );
                        log( "\t\tback office ID = " + backOfficeId );
                    }
                    if ( instrument4.containsCustomFieldKey( RELATEDCRNC_KEY ) )
                    {
                        Entity relatedUser = ( Entity ) instrument4.getCustomFieldValue( RELATEDCRNC_KEY );
                        log( "\t\trelated user = #" + relatedUser.getObjectID() );
                    }

                }
            }

            // release
            uow4.release();

            //
            // Phase 5: fetch instrument based on custom fields
            //

            // UOW
            UnitOfWork uow5 = getPersistenceSession().acquireUnitOfWork();

            ReadAllQuery query5 = new ReadAllQuery();
            query5.setReferenceClass( Instrument.class );
            ExpressionBuilder exb = query5.getExpressionBuilder();
            Expression batchExpression = exb.anyOf( "customFields" );
            Expression expression = exb.get( "shortName" ).equal( shortName );
            expression = expression.and( batchExpression.get( "stringValue" ).equal
                    ( "XYZ987" ) );
            query5.setSelectionCriteria( expression );
            query5.addBatchReadAttribute( batchExpression );

            Vector instrs5 = ( Vector ) uow5.executeQuery( query5 );

            // display custom fields
            if ( instrs5 != null && instrs5.size() != 0 )
            {
                for ( i = 0; i < instrs5.size(); i++ )
                {
                    Instrument instrument5 = ( Instrument ) instrs5.elementAt( i );

                    // display all custom fields
                    log( "\tall custom fields" );
                    Iterator it = instrument5.customFieldKeySet().iterator();
                    while ( it.hasNext() )
                    {
                        String key = ( String ) it.next();
                        Object value = instrument5.getCustomField( key );
                        log( "\t\tkey = " + key + ", value = " + value );
                    }

                    // display specific custom fields
                    log( "\tspecific custom fields" );
                    if ( instrument5.containsCustomFieldKey( BACKOFFICEID_KEY ) )
                    {
                        String backOfficeId = ( String ) instrument5.getCustomFieldValue( BACKOFFICEID_KEY );
                        log( "\t\tback office ID = " + backOfficeId );
                    }
                    if ( instrument5.containsCustomFieldKey( RELATEDCRNC_KEY ) )
                    {
                        Entity relatedUser = ( Entity ) instrument5.getCustomFieldValue( RELATEDCRNC_KEY );
                        log( "\t\trelated user = #" + relatedUser.getObjectID() );
                    }

                }
            }

            // release
            uow5.release();

            //Phase 6 Fetch user and delete the custom fields

            UnitOfWork uow6 = getPersistenceSession().acquireUnitOfWork();

            ReadAllQuery query6 = new ReadAllQuery();
            query6.setReferenceClass( Instrument.class );
            query6.setSelectionCriteria( new ExpressionBuilder().get( "shortName" ).equal( shortName ) );
            query6.addJoinedAttribute( query6.getExpressionBuilder().anyOf( "customFields" ) );
            Vector instrs6 = ( Vector ) uow6.executeQuery( query6 );
            if ( instrs6 != null && instrs6.size() != 0 )
            {
                for ( i = 0; i < instrs6.size(); i++ )
                {
                    Instrument instrument6 = ( Instrument ) instrs6.elementAt( i );
                    Iterator it = instrument6.customFieldKeySet().iterator();
                    while ( it.hasNext() )
                    {
                        String key = ( String ) it.next();
                        if ( key.equals( BACKOFFICEID_KEY ) )
                        {
                            instrument6.removeCustomField( BACKOFFICEID_KEY );
                        }
                    }
                }
            }
            uow6.commit();

            //	Phase 7: fetch Instrument based on custom fields and check if the entry has been deleted

            UnitOfWork uow7 = getPersistenceSession().acquireUnitOfWork();

            // query Instrument

            ReadAllQuery query7 = new ReadAllQuery();
            query7.setReferenceClass( Instrument.class );
            ExpressionBuilder exb1 = query5.getExpressionBuilder();
            Expression batchExpression1 = exb1.anyOf( "customFields" );
            Expression expression1 = exb.get( "shortName" ).equal( shortName );
            expression = expression1.and( batchExpression1.get( "stringValue" ).equal
                    ( "XYZ987" ) );
            query5.setSelectionCriteria( expression );
            query5.addBatchReadAttribute( batchExpression );

            /*ReadAllQuery query7 = new ReadAllQuery();

query7.setReferenceClass( Instrument.class );
query7.setSelectionCriteria( new ExpressionBuilder().get( "shortName" ).equal( shortName ) );
query7.addJoinedAttribute( query7.getExpressionBuilder().anyOf( "customFields" )
       .get( "stringValue" ).equal( "XYZ987" ) );*/
            Vector instrs7 = ( Vector ) uow7.executeQuery( query7 );

            // display custom fields
            if ( instrs7 != null && instrs7.size() != 0 )
            {
                for ( i = 0; i < instrs7.size(); i++ )
                {
                    Instrument instrument7 = ( Instrument ) instrs7.elementAt( i );
                    log( "\tDisplay all the custom fields" );
                    Iterator it = instrument7.customFieldKeySet().iterator();
                    while ( it.hasNext() )
                    {
                        String key = ( String ) it.next();
                        Object value = instrument7.getCustomField( key );
                        log( "\t\tkey = " + key + ", value = " + value );
                    }
                    log( "\tspecific custom field test" );
                    if ( instrument7.containsCustomFieldKey( BACKOFFICEID_KEY ) )
                    {
                        String backOfficeId = ( String ) instrument7.getCustomFieldValue( BACKOFFICEID_KEY );
                        log( "\t\tback office ID = " + backOfficeId );
                    }
                    else
                    {
                        log( "\tThe required customfield has been deleted successfully" );
                    }
                    if ( instrument7.containsCustomFieldKey( RELATEDCRNC_KEY ) )
                    {
                        Entity relatedUser = ( Entity ) instrument7.getCustomFieldValue( RELATEDCRNC_KEY );
                        log( "\t\trelated user = #" + relatedUser.getObjectID() );
                    }
                }
            }
            uow7.release();

            //Phase 8 Fetch a Instrument and delete all the customFields

            UnitOfWork uow8 = getPersistenceSession().acquireUnitOfWork();
            ExpressionBuilder eb8 = new ExpressionBuilder();
            Expression expr8 = eb8.get( "shortName" ).equal( shortName );

            ReadAllQuery query8 = new ReadAllQuery();
            query8.setReferenceClass( Instrument.class );
            query8.setSelectionCriteria( expr8 );
            query8.addJoinedAttribute( query8.getExpressionBuilder().anyOf( "customFields" ) );
            Vector instrs8 = ( Vector ) uow8.executeQuery( query8 );
            if ( instrs8 != null )
            {
                for ( i = 0; i < instrs8.size(); i++ )
                {
                    Instrument instrument8 = ( Instrument ) instrs8.elementAt( i );
                    instrument8.clearCustomFields();
                }
            }
            uow8.commit();

            //Phase 9 Fetch a Instrument and display the customfields.
            UnitOfWork uow9 = getPersistenceSession().acquireUnitOfWork();

            // query Instrument
            ReadAllQuery query9 = new ReadAllQuery();
            query9.setReferenceClass( Instrument.class );
            query9.setSelectionCriteria( new ExpressionBuilder().get( "shortName" ).equal( shortName ) );
            query9.addJoinedAttribute( query9.getExpressionBuilder().anyOf( "customFields" ) );
            Vector instrs9 = ( Vector ) uow9.executeQuery( query9 );

            // display custom fields
            if ( instrs9 != null && instrs9.size() != 0 )
            {
                for ( i = 0; i < instrs9.size(); i++ )
                {
                    Instrument instrument9 = ( Instrument ) instrs9.elementAt( i );
                    log( "\tDisplay all the custom fields" );
                    Iterator it = instrument9.customFieldKeySet().iterator();
                    while ( it.hasNext() )
                    {
                        String key = ( String ) it.next();
                        Object value = instrument9.getCustomField( key );
                        log( "\t\tkey = " + key + ", value = " + value );
                    }
                }
            }
            uow9.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Instrument test" );
        }
    }
}
