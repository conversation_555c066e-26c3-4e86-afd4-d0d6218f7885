package com.integral.finance.fx.test;

import com.integral.finance.businessCalendar.BusinessCalendarFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dateGeneration.PeriodicDateRule;
import com.integral.finance.financialEvent.FinancialEventC;
import com.integral.finance.financialEvent.FinancialEventClassificationC;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.fx.FXSettlementDateRule;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.persistence.Entity;
import com.integral.persistence.EntityObserver;
import com.integral.persistence.ExternalSystemFactory;
import com.integral.persistence.NamedEntity;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.workflow.WorkflowFactory;
import com.integral.xml.binding.JavaXMLBinderFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

/**
 *
 */
public class FXLegPTestC extends PTestCaseC
{
    static String standardFXConventionName = "STDQOTCNV";
    public FXRateConvention aTestConvention = null;
    public Currency usd, jpy, cad, eur;
    User lpUser;
    private static int FXPAYMENTPARAM_FROM_FACTORY = 0;
    private static int FXPAYMENTPARAM_FROM_FXLEG = 1;
    private static int REGISTER_FXPAYMENTPARAM_FROM_FACTORY = 2;

    public FXLegPTestC( String name )
    {
        super( name );
        eur = CurrencyFactory.getCurrency( "EUR" );
        usd = CurrencyFactory.getCurrency( "USD" );
        cad = CurrencyFactory.getCurrency( "CAD" );
        jpy = CurrencyFactory.getCurrency( "JPY" );
        lpUser = UserFactory.getUser( "Integral1" );
    }

    public void testFXLegInsert()
    {
        log( "testFXLegInsert" );
        try
        {
            persistFXLeg( FXPAYMENTPARAM_FROM_FACTORY );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXLegInsert" );
        }
    }

    public void testFXLegInsert1()
    {
        log( "testFXLegInsert1" );
        try
        {
            persistFXLeg( FXPAYMENTPARAM_FROM_FXLEG );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXLegInsert1" );
        }
    }

    public void testFXLegInsert2()
    {
        log( "testFXLegInsert2" );
        try
        {
            persistFXLeg( REGISTER_FXPAYMENTPARAM_FROM_FACTORY );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXLegInsert2" );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            Currency aud = CurrencyFactory.getCurrency( "AUD" );
            FXRateConvention conv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );

            uow = this.getPersistenceSession().acquireUnitOfWork();

            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            FXLeg cfLeg = FXFactory.newFXLeg();
            cfLeg = ( FXLeg ) uow.registerObject( cfLeg );
            FXPaymentParameters fxPmt = cfLeg.getFXPayment();
            fxPmt.setCurrency1( eur );
            fxPmt.setCurrency2( usd );
            fxPmt.setAcceptedProviderPrice( 2.2222 );
            fxPmt.setAcceptedForwardPoints( 0.1111 );
            FXRate fxRate = fxPmt.getFXRate();
            fxRate.setBaseCurrency( eur );
            fxRate.setVariableCurrency( usd );
            fxRate.setFXRateConvention( conv );
            IdcDate date = DateTimeFactory.newDate();
            fxPmt.setFixingDate( date );
            fxPmt.setFixingTenor( Tenor.SPOT_TENOR );
            cfLeg.setUSI( "TESTUSI" );
            cfLeg.setUSINamespace( "NS1234ABCD" );
            cfLeg.setUSIIdentifier( "12345" );
            cfLeg.setUTI( "TESTUTI" );
            cfLeg.setUTINamespace( "TESTUTINAM" );
            cfLeg.setUTIIdentifier( "UTIIDENTIFIERTEST" );
            cfLeg.setMakerUnfilledAmount( 1000.50 );
            uow.commit();
            assertNotNull( cfLeg );
            assertEquals( cfLeg.getFXPayment().getCurrency1(), eur );
            assertEquals( cfLeg.getFXPayment().getCurrency2(), usd );
            assertEquals( cfLeg.getFXPayment().getFXRate().getBaseCurrency(), eur );
            assertEquals( cfLeg.getFXPayment().getFXRate().getVariableCurrency(), usd );
            assertEquals( cfLeg.getFXPayment().getFXRate().getCurrencyPair().getBaseCurrency(), eur );
            assertEquals( cfLeg.getFXPayment().getFXRate().getCurrencyPair().getVariableCurrency(), usd );
            assertEquals( cfLeg.getFXPayment().getAcceptedProviderPrice(), 2.2222 );
            assertEquals( cfLeg.getFXPayment().getAcceptedForwardPoints(), 0.1111 );
            assertEquals( cfLeg.getFXPayment().getFixingDate(), date );
            assertEquals( cfLeg.getFXPayment().getFixingTenor(), Tenor.SPOT_TENOR );
            assertEquals( "TESTUSI", cfLeg.getUSI() );
            assertEquals( "NS1234ABCD", cfLeg.getUSINamespace() );
            assertEquals( "12345", cfLeg.getUSIIdentifier() );
            assertEquals( 1000.50, cfLeg.getMakerUnfilledAmount() );

            // refresh the object and check again.
            // refresh all the objects and check again.
            cfLeg = ( FXLeg ) getPersistenceSession().refreshObject( cfLeg );
            assertEquals( cfLeg.getFXPayment().getCurrency1(), eur );
            assertEquals( cfLeg.getFXPayment().getCurrency2(), usd );
            assertEquals( cfLeg.getFXPayment().getFXRate().getBaseCurrency(), eur );
            assertEquals( cfLeg.getFXPayment().getFXRate().getVariableCurrency(), usd );
            assertEquals( cfLeg.getFXPayment().getFXRate().getCurrencyPair().getBaseCurrency(), eur );
            assertEquals( cfLeg.getFXPayment().getFXRate().getCurrencyPair().getVariableCurrency(), usd );
            assertEquals( cfLeg.getFXPayment().getAcceptedProviderPrice(), 2.2222 );
            assertEquals( cfLeg.getFXPayment().getAcceptedForwardPoints(), 0.1111 );
            assertEquals( cfLeg.getFXPayment().getFixingDate(), date );
            assertEquals( cfLeg.getFXPayment().getFixingTenor(), Tenor.SPOT_TENOR );
            assertEquals( "TESTUSI", cfLeg.getUSI() );
            assertEquals( "NS1234ABCD", cfLeg.getUSINamespace() );
            assertEquals( "12345", cfLeg.getUSIIdentifier() );
            assertEquals( 1000.50, cfLeg.getMakerUnfilledAmount() );
            assertEquals( "TESTUTI", cfLeg.getUTI() );
            assertEquals( "TESTUTINAM", cfLeg.getUTINamespace() );
            assertEquals( "UTIIDENTIFIERTEST", cfLeg.getUTIIdentifier() );

            // now update the currencies to another.
            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            uow.addReadOnlyClass( CurrencyC.class );
            uow.addReadOnlyClass( FXRateConventionC.class );
            cfLeg = ( FXLeg ) uow.registerObject( cfLeg );
            FXPaymentParameters fxPmt1 = cfLeg.getFXPayment();
            fxPmt1.setCurrency1( gbp );
            fxPmt1.setCurrency2( aud );
            FXRate fxRate1 = fxPmt1.getFXRate();
            fxRate1.setBaseCurrency( gbp );
            fxRate1.setVariableCurrency( aud );
            fxRate1.setFXRateConvention( conv );
            IdcDate variableValueDate = DateTimeFactory.newDate ().addDays ( 5 );
            cfLeg.getFXPayment ().setVariableValueDate ( variableValueDate );
            cfLeg.getFXPayment ().setVariableTenor ( new Tenor ( "5D" ) );

            uow.commit();
            assertNotNull( cfLeg );

            assertEquals( cfLeg.getFXPayment().getCurrency1(), gbp );
            assertEquals( cfLeg.getFXPayment().getCurrency2(), aud );
            assertEquals( cfLeg.getFXPayment().getFXRate().getBaseCurrency(), gbp );
            assertEquals( cfLeg.getFXPayment().getFXRate().getVariableCurrency(), aud );
            assertEquals( cfLeg.getFXPayment().getFXRate().getCurrencyPair().getBaseCurrency(), gbp );
            assertEquals( cfLeg.getFXPayment().getFXRate().getCurrencyPair().getVariableCurrency(), aud );
            assertTrue ( "variable tenor", new Tenor( "5D" ).equals ( cfLeg.getFXPayment ().getVariableTenor () ) );
            assertTrue ( "variable value date", variableValueDate.isSameAs ( cfLeg.getFXPayment ().getVariableValueDate () ) );

            // refresh all the objects and check again.
            cfLeg = ( FXLeg ) getPersistenceSession().refreshObject( cfLeg );
            assertEquals( cfLeg.getFXPayment().getCurrency1(), gbp );
            assertEquals( cfLeg.getFXPayment().getCurrency2(), aud );
            assertEquals( cfLeg.getFXPayment().getFXRate().getBaseCurrency(), gbp );
            assertEquals( cfLeg.getFXPayment().getFXRate().getVariableCurrency(), aud );
            assertEquals( cfLeg.getFXPayment().getFXRate().getCurrencyPair().getBaseCurrency(), gbp );
            assertEquals( cfLeg.getFXPayment().getFXRate().getCurrencyPair().getVariableCurrency(), aud );
            assertTrue ( "variable tenor", new Tenor( "5D" ).equals ( cfLeg.getFXPayment ().getVariableTenor () ) );
            assertTrue ( "variable value date", variableValueDate.isSameAs ( cfLeg.getFXPayment ().getVariableValueDate () ) );
        }
        catch ( Exception e )
        {
            fail( "testFieldsPersistence", e );
        }
    }


    private void persistFXLeg( int paymentParamType )
    {
        UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();

        FXLeg singleLeg = FXFactory.newFXLeg();
        Trade trade = ( Trade ) getTrade( Trade.class, 1 );

        singleLeg = ( FXLeg ) uow.registerObject( singleLeg );
        trade = ( Trade ) uow.registerObject( trade );

        singleLeg.setNamespace( lpUser.getNamespace() );
        singleLeg.setName( "testLeg" );
        singleLeg.setTrade( trade );

        FXPaymentParameters fxPayment = null;

        if ( paymentParamType == REGISTER_FXPAYMENTPARAM_FROM_FACTORY )
        {
            fxPayment = FXFactory.newFXPaymentParameters();
            fxPayment = ( FXPaymentParameters ) fxPayment.getRegisteredObject();
        }
        else if ( paymentParamType == FXPAYMENTPARAM_FROM_FXLEG )
        {
            fxPayment = singleLeg.getFXPayment();
        }
        else if ( paymentParamType == FXPAYMENTPARAM_FROM_FACTORY )
        {
            fxPayment = FXFactory.newFXPaymentParameters();
        }

        populateFXPaymentParameters( singleLeg, fxPayment, lpUser );

        FXSettlementDateRule dateRule = ( FXSettlementDateRule ) getNamedEntity( FXSettlementDateRule.class, "SPOT" );
        dateRule = ( FXSettlementDateRule ) uow.registerObject( dateRule );

        fxPayment.setFXSettlementDateRule( dateRule );
        fxPayment.setContainingTrade( trade );


        singleLeg.setFXPayment( fxPayment );
        uow.commit();
        printFXLegPaymentsDetails( "Persisting to DB", singleLeg );

        uow = this.getPersistenceSession().acquireUnitOfWork();
        uow.refreshObject( singleLeg );
        printFXLegPaymentsDetails( "Reading from DB", singleLeg );
        checkXMLMapping( singleLeg );
    }

    private void populateFXPaymentParameters( FXLeg fxLeg, FXPaymentParameters fxPayment, User user )
    {
        fxPayment.setFXRate( getFXRate( usd, cad, 2.0, 2.0 ) );
        fxPayment.setCurrency1( usd );
        fxPayment.setCurrency2( cad );
        fxPayment.setCurrency1Amount( 10000 );
        fxPayment.setCurrency2Amount( 70000 );
        fxPayment.setCurrency2ValueDate( IdcDateC.newDate( new Date() ) );
        fxPayment.setCurrency1ValueDate( IdcDateC.newDate( new Date() ) );
        fxPayment.setDealtCurrency1( Boolean.TRUE );
        fxPayment.setBuyingCurrency1( Boolean.FALSE );
        fxPayment.setSpotDate( IdcDateC.newDate( new Date() ) );
        fxPayment.setTenor( Tenor.SPOT_TENOR );
        fxPayment.setValueDate( IdcDateC.newDate( new Date() ).addDays( 2 ) );

        try
        {
            fxPayment.setActive( Boolean.TRUE );
        }
        catch ( Exception exc )
        {
            log( "Exception.setActive - " + exc );
        }

        try
        {
            fxPayment.setAdjustPeriodDates( Boolean.TRUE );
        }
        catch ( Exception exc )
        {
            log( "Exception.setAdjustPeriodDates - " + exc );
        }

        try
        {
            fxPayment.setBeginStubLength( 'A' );
        }
        catch ( Exception exc )
        {
            log( "Exception.setBeginStubLength - " + exc );
        }

        try
        {
            fxPayment.setBusinessCalendar( BusinessCalendarFactory.newBusinessCalendar() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setBusinessCalendar - " + exc );
        }

        try
        {
            fxPayment.setContainingFinancialEventParameters( FXFactory.newFXPaymentParameters() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setContainingFinancialEventParameters - " + exc );
        }

        try
        {
            fxPayment.setContainingInstrument( usd );
        }
        catch ( Exception exc )
        {
            log( "Exception.setContainingInstrument - " + exc );
        }

        try
        {
            fxPayment.setContainingTradeLeg( fxLeg );
        }
        catch ( Exception exc )
        {
            log( "Exception.setContainingTradeLeg - " + exc );
        }

        try
        {
            fxPayment.setCustomFields( new HashMap() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setCustomFields - " + exc );
        }

        try
        {
            fxPayment.setDateGenerationDirection( 'D' );
        }
        catch ( Exception exc )
        {
            log( "Exception.setDateGenerationDirection - " + exc );
        }

        try
        {
            PeriodicDateRule dateRule = ( PeriodicDateRule ) getNamedEntity( PeriodicDateRule.class, "MON" );
            fxPayment.setDateRule( dateRule );
        }
        catch ( Exception exc )
        {
            log( "Exception.setDateRule - " + exc );
        }

        try
        {
            fxPayment.setDisplayKey( "test" );
        }
        catch ( Exception exc )
        {
            log( "Exception.setDisplayKey - " + exc );
        }

        try
        {
            fxPayment.setEndStubLength( 'Z' );
        }
        catch ( Exception exc )
        {
            log( "Exception - " + exc );
        }

        try
        {
            fxPayment.setExternalSystemId( "test", null );
        }
        catch ( Exception exc )
        {
            log( "Exception.setEndStubLength - " + exc );
        }

        try
        {
            fxPayment.setExternalSystemIds( new ArrayList() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setExternalSystemIds - " + exc );
        }

        try
        {
            fxPayment.setFinancialEventClassification( new FinancialEventClassificationC() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setFinancialEventClassification - " + exc );
        }

        try
        {
            fxPayment.setFinancialEventValues( new FinancialEventC() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setFinancialEventValues - " + exc );
        }

        try
        {
            fxPayment.setGUID( "dasdsadsad" );
        }
        catch ( Exception exc )
        {
            log( "Exception.setGUID - " + exc );
        }

        try
        {
            fxPayment.setLastModifiedBy( user );
        }
        catch ( Exception exc )
        {
            log( "Exception.setLastModifiedBy - " + exc );
        }

        try
        {
            fxPayment.setNamespace( user.getNamespace() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setNamespace - " + exc );
        }

        try
        {
            fxPayment.setObjectID( user.getObjectID() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setObjectID - " + exc );
        }

        try
        {
            fxPayment.setObjectId( user.getObjectID() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setObjectId - " + exc );
        }

        try
        {
            fxPayment.setOwner( fxLeg );
        }
        catch ( Exception exc )
        {
            log( "Exception.setOwner - " + exc );
        }

        try
        {
            fxPayment.setStatus( 'A' );
        }
        catch ( Exception exc )
        {
            log( "Exception.setStatus - " + exc );
        }

        try
        {
            fxPayment.setWorkflowState( "test", WorkflowFactory.newNamedWorkflowState() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setWorkflowState - " + exc );
        }

        try
        {
            fxPayment.setWorkflowStateMap( WorkflowFactory.newWorkflowStateMap() );
        }
        catch ( Exception exc )
        {
            log( "Exception.setWorkflowStateMap - " + exc );
        }

        try
        {
            fxPayment.addExternalSystemId( ExternalSystemFactory.newExternalSystemId() );
        }
        catch ( Exception exc )
        {
            log( "Exception.addExternalSystemId - " + exc );
        }

        try
        {
            fxPayment.addExternalSystemId( ExternalSystemFactory.newExternalSystem(), "test" );
        }
        catch ( Exception exc )
        {
            log( "Exception.addExternalSystemId - " + exc );
        }

        try
        {
            fxPayment.addUpdateObserver( new EntityObserver()
            {
                public void onUpdate( Entity anEntity )
                {
                }
            } );
        }
        catch ( Exception exc )
        {
            log( "Exception.addUpdateObserver - " + exc );
        }

        try
        {
            fxPayment.createFinancialEvents();
        }
        catch ( Exception exc )
        {
            log( "Exception.createFinancialEvents - " + exc );
        }

        try
        {
            fxPayment.newCustomField( "testUser", user );
        }
        catch ( Exception exc )
        {
            log( "Exception.newCustomField - " + exc );
        }

        try
        {
            fxPayment.putCustomField( "testUser", user );
        }
        catch ( Exception exc )
        {
            log( "Exception.putCustomField - " + exc );
        }


        try
        {
            fxPayment.clearCustomFields();
        }
        catch ( Exception exc )
        {
            log( "Exception.clearCustomFields - " + exc );
        }

        try
        {
            fxPayment.clearExternalSystemIds();
        }
        catch ( Exception exc )
        {
            log( "Exception.clearExternalSystemIds - " + exc );
        }

        try
        {
            fxPayment.resetGUID();
        }
        catch ( Exception exc )
        {
            log( "Exception.resetGUID - " + exc );
        }

    }

    private void printFXLegPaymentsDetails( String comment, FXLeg fxLeg )
    {
        log( "\t ********************** " + comment + " ****************************" );
        log( "\t fxLeg   = " + fxLeg );
        log( "\t fxLeg.name  = " + fxLeg.getName() );
        log( "\t fxLeg.objectID = " + fxLeg.getObjectID() );
        log( "\t fxLeg.namespace = " + fxLeg.getNamespace() );
        log( "\t fxLeg.trade = " + fxLeg.getTrade() );

        FXPaymentParameters payment = fxLeg.getFXPayment();

        log( "\t FXPaymentParameters.getCurrency1 = " + payment.getCurrency1() );
        log( "\t FXPaymentParameters.getCurrency2 = " + payment.getCurrency2() );
        log( "\t FXPaymentParameters.getCurrency1Amount = " + payment.getCurrency1Amount() );
        log( "\t FXPaymentParameters.getCurrency2Amount = " + payment.getCurrency2Amount() );
        log( "\t FXPaymentParameters.getCurrency1ValueDate = " + payment.getCurrency1ValueDate() );
        log( "\t FXPaymentParameters.getCurrency2ValueDate = " + payment.getCurrency2ValueDate() );
        log( "\t FXPaymentParameters.isDealtCurrency1 = " + payment.isDealtCurrency1() );
        log( "\t FXPaymentParameters.getFXRate.getRate = " + payment.getFXRate().getRate() );
        log( "\t FXPaymentParameters.getFXRate.getForwardPoints = " + payment.getFXRate().getForwardPoints() );
        log( "\t FXPaymentParameters.tradeLeg = " + payment.getContainingTradeLeg().getObjectID() );
        log( "\t FXPaymentParameters.trade = " + payment.getContainingTrade() );

        log( "\t FXPaymentParameters.isBuyingCurrency1 = " + payment.isBuyingCurrency1() );
        log( "\t FXPaymentParameters.getFXCoverRate = " + payment.getFXCoverRate() );
        log( "\t FXPaymentParameters.getFXSettlementDateRule = " + payment.getFXSettlementDateRule() );
        log( "\t FXPaymentParameters.getSpotDate = " + payment.getSpotDate() );
        log( "\t FXPaymentParameters.getValueDate = " + payment.getValueDate() );
        log( "\t FXPaymentParameters.getTenor = " + payment.getTenor() );


        log( "\t FXPaymentParameters.getBaseCurrencyAmount = " + payment.getBaseCurrencyAmount() );
        log( "\t FXPaymentParameters.getBeginStubLength = " + payment.getBeginStubLength() );
        log( "\t FXPaymentParameters.getBusinessCalendar = " + payment.getBusinessCalendar() );
        log( "\t FXPaymentParameters.getBuyingAmount = " + payment.getBuyingAmount( "test" ) );
        log( "\t FXPaymentParameters.getBuyingCurrency = " + payment.getBuyingCurrency( "test" ) );
        log( "\t FXPaymentParameters.getContainingFinancialEventParameters = " + payment.getContainingFinancialEventParameters() );
        log( "\t FXPaymentParameters.getContainingInstrument = " + payment.getContainingInstrument() );
        log( "\t FXPaymentParameters.getContainingTrade = " + payment.getContainingTrade() );
        log( "\t FXPaymentParameters.getContainingTradeLeg = " + payment.getContainingTradeLeg() );
        log( "\t FXPaymentParameters.getCreatedDate = " + payment.getCreatedDate() );
        log( "\t FXPaymentParameters.getCreatedDateTime = " + payment.getCreatedDateTime() );
        //log("\t FXPaymentParameters.getCustomField = " + payment.getCustomField("test"));
        //log("\t FXPaymentParameters.getCustomFields = " + payment.getCustomFields());
        //log("\t FXPaymentParameters.getTenor = " + payment.getCustomFieldValue("test"));
        log( "\t FXPaymentParameters.getCustomFieldValue = " + payment.getDateGenerationDirection() );
        log( "\t FXPaymentParameters.getDateRule = " + payment.getDateRule() );
        log( "\t FXPaymentParameters.getDisplayKey = " + payment.getDisplayKey() );
        log( "\t FXPaymentParameters.getEncryptedObjectID = " + payment.getEncryptedObjectID() );
        log( "\t FXPaymentParameters.getEncryptedObjectId = " + payment.getEncryptedObjectId() );
        log( "\t FXPaymentParameters.getEndStubLength = " + payment.getEndStubLength() );
        log( "\t FXPaymentParameters.getEntityKey = " + payment.getEntityKey() );
        log( "\t FXPaymentParameters.getEventTimeCategoryName = " + payment.getEventTimeCategoryName() );
        log( "\t FXPaymentParameters.getExternalSystemId = " + payment.getExternalSystemId( "test" ) );
        log( "\t FXPaymentParameters.getExternalSystemIds = " + payment.getExternalSystemIds() );
        log( "\t FXPaymentParameters.getFacade = " + payment.getFacade( "test" ) );
        log( "\t FXPaymentParameters.getFinancialEventBuilder = " + payment.getFinancialEventBuilder() );
        log( "\t FXPaymentParameters.getFinancialEventClassification = " + payment.getFinancialEventClassification() );
        log( "\t FXPaymentParameters.getGUID = " + payment.getGUID() );
        log( "\t FXPaymentParameters.getLastModifiedBy = " + payment.getLastModifiedBy() );
        log( "\t FXPaymentParameters.getModifiedDate = " + payment.getModifiedDate() );
        log( "\t FXPaymentParameters.getModifiedDateTime = " + payment.getModifiedDateTime() );
        log( "\t FXPaymentParameters.getNamespace = " + payment.getNamespace() );
        log( "\t FXPaymentParameters.getObjectID = " + payment.getObjectID() );
        log( "\t FXPaymentParameters.getObjectId = " + payment.getObjectId() );
        log( "\t FXPaymentParameters.getOwner = " + payment.getOwner() );
        log( "\t FXPaymentParameters.getReferenceId = " + payment.getReferenceId() );
        log( "\t FXPaymentParameters.getRegisteredObject = " + payment.getRegisteredObject() );
        log( "\t FXPaymentParameters.getSellingAmount = " + payment.getSellingAmount( "test" ) );
        log( "\t FXPaymentParameters.getSellingCurrency = " + payment.getSellingCurrency( "test" ) );
        //log("\t FXPaymentParameters.getService = " + payment.getService("test"));
        log( "\t FXPaymentParameters.getSellingAmount = " + payment.getSellingAmount( "test" ) );
        log( "\t FXPaymentParameters.getSpotDate = " + payment.getSpotDate() );
        log( "\t FXPaymentParameters.getStatus = " + payment.getStatus() );
        log( "\t FXPaymentParameters.getStatusLastDateModified = " + payment.getStatusLastDateModified() );
        log( "\t FXPaymentParameters.getStatusLastDateTimeModified = " + payment.getStatusLastDateTimeModified() );
        log( "\t FXPaymentParameters.getVersion = " + payment.getVersion() );
        log( "\t FXPaymentParameters.getValueDatePeriod = " + payment.getValueDatePeriod() );
        log( "\t FXPaymentParameters.getVariableCurrencyAmount = " + payment.getVariableCurrencyAmount() );
        log( "\t FXPaymentParameters.getWorkflowState = " + payment.getWorkflowState( "test" ) );
        log( "\t FXPaymentParameters.getWorkflowStateMap = " + payment.getWorkflowStateMap() );
        //log("\t FXPaymentParameters.containsCustomFieldKey = " + payment.containsCustomFieldKey("test"));
        //log("\t FXPaymentParameters.customFieldKeySet = " + payment.customFieldKeySet());
        //log("\t FXPaymentParameters.customFieldValues = " + payment.customFieldValues());
        log( "\t FXPaymentParameters.isActive = " + payment.isActive() );
        log( "\t FXPaymentParameters.isAdjustPeriodDates = " + payment.isAdjustPeriodDates() );
        //log("\t FXPaymentParameters.isOwnedBy = " + payment.isOwnedBy(fxLeg));
        log( "\t FXPaymentParameters.isPassive = " + payment.isPassive() );
        //log("\t FXPaymentParameters.newExternalSystemId = " + payment.newExternalSystemId());
        //log("\t FXPaymentParameters.removeCustomField = " + payment.removeCustomField("test"));
        log( "\t FXPaymentParameters.toString = " + payment.toString() );
        log( "\t ******************************************************************* " );
    }

    private void checkXMLMapping( FXLeg leg )
    {
        try
        {
            StringWriter sw = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw, leg, "Integral" );

            log( "******************* FXLeg to XML ************************" );
            log( sw.toString() );

            StringReader sr = new StringReader( sw.toString() );
            Object fxleg = JavaXMLBinderFactory.newJavaXMLBinder().convertFromXML( sr, "Integral" );

            printFXLegPaymentsDetails( "Reading from XML ", ( FXLeg ) fxleg );
        }
        catch ( Exception exc )
        {
            log( "Exception.checkXMLMapping() - " + exc );
        }
    }


    private FXRate getFXRate( Currency base, Currency var, double spotRate, double fwdPoints )
    {
        aTestConvention = getFXRateConvention( standardFXConventionName );
        FXRate fxRate = new FXRateC();
        fxRate.setFXRateConvention( aTestConvention );
        FXRateBasis basis = aTestConvention.getFXRateBasis( base, var );

        if ( basis.getBaseCurrency().getShortName().equals( base.getShortName() ) )
        {
            fxRate.setBaseCurrency( basis.getBaseCurrency() );
            fxRate.setVariableCurrency( basis.getVariableCurrency() );
        }
        else
        {
            fxRate.setBaseCurrency( basis.getVariableCurrency() );
            fxRate.setVariableCurrency( basis.getBaseCurrency() );
        }
        fxRate.setSpotRate( spotRate );
        fxRate.setForwardPoints( fwdPoints );
        fxRate.setFXRateBasis( basis );
        return fxRate;
    }

    protected FXRateConvention getFXRateConvention( String shortName )
    {
        return ( FXRateConvention ) this.getNamedEntity(
                FXRateConvention.class,
                shortName );
    }

    protected NamedEntity getNamedEntity( Class aClass, String shortName )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).equal( shortName );
        Object obj = null;
        if ( this.uow == null )
        {
            obj = this.getPersistenceSession().readObject( aClass, expr );
        }
        else
        {
            obj = this.uow.readObject( aClass, expr );
        }
        return ( NamedEntity ) obj;
    }

    protected Entity getTrade( Class aClass, long id )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "objectID" ).equal( id );
        Object obj = null;
        if ( this.uow == null )
        {
            obj = this.getPersistenceSession().readObject( aClass, expr );
        }
        else
        {
            obj = this.uow.readObject( aClass, expr );
        }
        return ( Entity ) obj;
    }
}
