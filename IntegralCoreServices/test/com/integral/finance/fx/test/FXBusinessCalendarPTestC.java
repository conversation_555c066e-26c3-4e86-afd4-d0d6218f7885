package com.integral.finance.fx.test;


import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.fx.FXBusinessCalendar;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateBasisC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Vector;

/**
 * Unit test for FXBusinessCalendar
 */
public class FXBusinessCalendarPTestC
        extends PTestCaseC
{
    public FXBusinessCalendarPTestC( String name )
    {
        super( name );
    }

    public void testLookup()
    {
        log( "testLookup" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( FXRateBasisC.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXRateBasis rb = ( FXRateBasis ) objs.elementAt( i );
                log.info( "FXRateBasis: " + rb );
                FXBusinessCalendar pp = rb.getFXBusinessCalendar();
                log.info( "  FXBusinessCalendar: " + pp );

                if ( ( pp != null ) && ( pp.getHolidayCalendars() != null ) )
                {
                    int j = 0;
                    Iterator it = pp.getHolidayCalendars().iterator();
                    while ( it.hasNext() )
                    {
                        HolidayCalendar hc = ( HolidayCalendar ) it.next();
                        log.info( "\t hol cal #" + j + "       : " + hc );
                        j++;
                    }
                }
                if ( ( pp != null ) && ( pp.getNoLagHolidayCalendars() != null ) )
                {
                    int j = 0;
                    Iterator it = pp.getNoLagHolidayCalendars().iterator();
                    while ( it.hasNext() )
                    {
                        HolidayCalendar hc = ( HolidayCalendar ) it.next();
                        log.info( "\t no lag hol cal #" + j + ": " + hc );
                        j++;
                    }
                }
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testLookup" );
        }
    }

    /**
     *
     */
    public void testUpdateNoLag()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();

            // get calendar
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "currencyPairString" ).equal( "USD/CAD" );
            FXRateBasis rb = ( FXRateBasis ) uow.readObject( FXRateBasis.class, expr );
            FXBusinessCalendar bc = rb.getFXBusinessCalendar();

            // get holiday calendars
            ExpressionBuilder eb1 = new ExpressionBuilder();
            Expression expr1 = eb1.get( "shortName" ).equal( "NYC" );
            HolidayCalendar nyc = ( HolidayCalendar ) uow.readObject( HolidayCalendar.class, expr1 );

            ExpressionBuilder eb2 = new ExpressionBuilder();
            Expression expr2 = eb2.get( "shortName" ).equal( "TOR" );
            HolidayCalendar tor = ( HolidayCalendar ) uow.readObject( HolidayCalendar.class, expr2 );

            // first try calculation with NYC only
            List holcals1 = new ArrayList();
            holcals1.add( nyc );
            bc.setNoLagHolidayCalendars( holcals1 );

            // then add TOR and try calc again
            List holcals2 = new ArrayList();
            holcals2.add( nyc );
            holcals2.add( tor );
            bc.setNoLagHolidayCalendars( holcals2 );

            uow.commit();

            log( "insertTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }

}
