package com.integral.finance.fx.test;


import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * Tests the functionalities of FXRate
 */
public class FXRatePTestC extends PTestCaseC
{
    static String name = "FXRate Test";

    public FXRatePTestC( String name )
    {
        super( name );
    }

    public void testFXRateAmountCalculation()
    {
        try
        {
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            jpy.setRoundingType( BigDecimal.ROUND_DOWN );
            FXRate fxRate = FXFactory.newFXRate();
            fxRate.setBaseCurrency( usd );
            fxRate.setVariableCurrency( jpy );
            fxRate.setFXRateConvention( ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" ) );
            double rate = 103.96;
            fxRate.setRate( rate );
            double amt = 300000.00;
            double result = amt * rate;
            double settledAmt = fxRate.getAmountOfVariable( amt );
            log( "settled amt=" + settledAmt + ",calc amt=" + result + ",formatAmt=" + jpy.getDecimalFormat( "#.#" ).format( settledAmt ) );
            assertEquals( "settledamt", settledAmt, 31188000.00 );
            assertEquals( "settled amt=" + settledAmt, jpy.getDecimalFormat( "#.#" ).format( settledAmt ), "31188000" );

            double dealtAmt = fxRate.getAmountOfBase( settledAmt );
            double calcDealtAmt = result / rate;
            log( "dealt amt=" + dealtAmt + ",calcDealtAmt=" + calcDealtAmt );
            assertEquals( "dealtAmt", dealtAmt, amt );
            assertEquals( "dealt amt=" + dealtAmt + ",formattedAmt=" + usd.getDecimalFormat( "#.#" ).format( dealtAmt ), usd.getDecimalFormat( "#.#" ).format( dealtAmt ), "300000.00" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXRateAmountCalculation", e );
        }
    }

    public void testFXRateAmountCalculation1()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            FXRate fxRate = FXFactory.newFXRate();
            fxRate.setBaseCurrency( eur );
            fxRate.setVariableCurrency( usd );
            fxRate.setFXRateConvention( ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" ) );
            double rate = 1.56648;
            fxRate.setRate( rate );
            double amt = 10000.00;
            double result = amt * rate;
            double settledAmt = fxRate.getAmountOfVariable( amt );
            log( "settled amt=" + settledAmt + ",calc amt=" + result + ",formatAmt=" + usd.getDecimalFormat( "#.#" ).format( settledAmt ) );
            assertEquals( "settledamt", settledAmt, 15664.80 );
            assertEquals( "settled amt=" + settledAmt, usd.getDecimalFormat( "#.#" ).format( settledAmt ), "15664.80" );

            double dealtAmt = fxRate.getAmountOfBase( settledAmt );
            double calcDealtAmt = result / rate;
            log( "dealt amt=" + dealtAmt + ",calcDealtAmt=" + calcDealtAmt );
            assertEquals( "dealtAmt", dealtAmt, amt );
            assertEquals( "dealt amt=" + dealtAmt + ",formattedAmt=" + usd.getDecimalFormat( "#.#" ).format( dealtAmt ), usd.getDecimalFormat( "#.#" ).format( dealtAmt ), "10000.00" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXRateAmountCalculation1", e );
        }
    }

    public void testFXRateBasis()
    {
        try
        {
            FXRateConvention stdQuoteConv = ( FXRateConvention ) namedEntityReader.execute( FXRateConvention.class, "STDQOTCNV" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            FXRate fxRate = FXFactory.newFXRate();
            fxRate.setBaseCurrency( usd );
            fxRate.setVariableCurrency( jpy );
            fxRate.setFXRateConvention( stdQuoteConv );
            FXRateBasis usdJpy = fxRate.getFXRateBasis();
            log( "usdJpy=" + usdJpy );
            assertNotNull( usdJpy );
            assertEquals( usdJpy.getBaseCurrency(), usd );
            assertEquals( usdJpy.getVariableCurrency(), jpy );

            // reset the currency pairs.
            fxRate.setBaseCurrency( eur );
            fxRate.setVariableCurrency( gbp );
            FXRateBasis eurGbp = fxRate.getFXRateBasis();
            log( "eurGbp=" + eurGbp );
            assertNotNull( eurGbp );
            assertEquals( eurGbp.getBaseCurrency(), eur );
            assertEquals( eurGbp.getVariableCurrency(), gbp );

            // now set a direct fxrate basis without fx rate convention
            FXRate fxRate1 = FXFactory.newFXRate();
            fxRate1.setBaseCurrency( usd );
            fxRate1.setVariableCurrency( jpy );
            fxRate1.setFXRateBasis( usdJpy );
            FXRateBasis usdJpy1 = fxRate1.getFXRateBasis();
            log( "usdJpy1=" + usdJpy1 );
            assertNotNull( usdJpy1 );
            assertEquals( usdJpy1.getBaseCurrency(), usd );
            assertEquals( usdJpy1.getVariableCurrency(), jpy );

            // now fx rate without any convention and fx rate basis
            FXRate fxRate2 = FXFactory.newFXRate();
            fxRate2.setBaseCurrency( usd );
            fxRate2.setVariableCurrency( jpy );
            FXRateBasis noVal = fxRate2.getFXRateBasis();
            log( "noVal=" + noVal );
            assertNull( noVal );

            // now set a direct fxrate basis without fx rate convention
            FXRate fxRate3 = FXFactory.newFXRate();
            fxRate3.setBaseCurrency( usd );
            fxRate3.setVariableCurrency( jpy );
            fxRate3.setFXRateBasis( eurGbp );
            FXRateBasis eurGbp1 = fxRate3.getFXRateBasis();
            log( "eurGBP=" + eurGbp1 );
            assertNotNull( eurGbp1 );
            assertEquals( eurGbp1.getBaseCurrency(), eur );
            assertEquals( eurGbp1.getVariableCurrency(), gbp );
        }
        catch ( Exception e )
        {
            fail( "testFXRateBasis", e );
        }
    }

    public void testCloneFXRate()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );

            FXRate orig = FXFactory.newFXRate();
            orig.setBaseCurrency( eur );
            orig.setVariableCurrency( usd );
            orig.setSpotRate( 1.1111 );
            orig.setForwardPoints( 0.11 );
            double rate = orig.getRate();

            // now clone the fx rate and set the variable currency.
            FXRate clone = ( FXRate ) orig.clone();
            clone.setBaseCurrency( usd );
            clone.setVariableCurrency( gbp );
            assertEquals( clone.getBaseCurrency(), usd );
            assertEquals( clone.getVariableCurrency(), gbp );
            assertEquals( orig.getBaseCurrency(), eur );
            assertEquals( orig.getVariableCurrency(), usd );
            assertEquals( orig.getSpotRate(), 1.1111 );
            assertEquals( orig.getForwardPoints(), 0.11 );

            assertEquals( orig.getRate(), rate );
            assertEquals( clone.getSpotRate(), 1.1111 );
            assertEquals( clone.getForwardPoints(), 0.11 );
            assertEquals( clone.getRate(), rate );
        }
        catch ( Exception e )
        {
            fail( "testCloneFXRate", e );
        }
    }
    
    private static double getFractionalRate( int precision )
    {
        return 1 / Math.pow( 10.0, precision );
    }
    
    public void testFXMidRatePrecision()
    {
        try
        {
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            jpy.setRoundingType( BigDecimal.ROUND_DOWN );
            FXRate fxRate = FXFactory.newFXRate();
            fxRate.setBaseCurrency( usd );
            fxRate.setVariableCurrency( jpy );
            fxRate.setFXRateConvention( ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" ) );
            
            FXRateBasis rb = fxRate.getFXRateBasis();
            int spotPrecision = rb.getSpotPrecision();
            fxRate.setSpotRate( getFractionalRate( spotPrecision + 1 ) );
            assertTrue( fxRate.getSpotRate() == 0 || fxRate.getSpotRate() != Double.NaN );
            fxRate.setRate( getFractionalRate( spotPrecision ) );
            assertTrue( fxRate.getSpotRate() > 0 );

            int rbPrecision = rb.getForwardRatePrecision();
            fxRate.setRate( getFractionalRate( rbPrecision + 1 ) );
            assertTrue( fxRate.getRate() == 0 );
            fxRate.setRate( getFractionalRate( rbPrecision ) );
            assertTrue( fxRate.getRate() > 0 );
            
            FXRate midRate = FXFactory.newFXMidRate();
            midRate.setBaseCurrency( usd );
            midRate.setVariableCurrency( jpy );
            midRate.setFXRateConvention( ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" ) );
            midRate.setRate( fxRate.getRate() );
            midRate.setSpotRate( getFractionalRate( spotPrecision ) );
            assertTrue( midRate.getRate() > 0 );
            assertTrue( midRate.getSpotRate() > 0 );
            midRate.setRate( fxRate.getRate() / 2 );
            double midSpotRate = midRate.getSpotRate() / 2;
            midRate.setSpotRate( midSpotRate );
            assertTrue( midRate.getRate() > 0 && midRate.getRate() == fxRate.getRate() / 2 );
            assertTrue( midRate.getSpotRate() > 0 && midRate.getSpotRate() == midSpotRate );
            
            DecimalFormat rateFormat = midRate.getRateFormat( null );
            String rateStr = rateFormat.format( midRate.getRate() );
            assertTrue( "0.0000005".equals( rateStr ) );
            
            DecimalFormat spotRateFormat = midRate.getSpotRateFormat( null );
            String spotRateStr = spotRateFormat.format( midRate.getSpotRate() );
            assertTrue( "0.0005".equals( spotRateStr ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testFXRateAmountCalculation", e );
        }
    }
}
