package com.integral.finance.fx.test;


import com.integral.finance.trade.Trade;
import com.integral.test.PTestCaseC;
import com.integral.workflow.NamedWorkflowState;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.util.Iterator;
import java.util.Vector;

/**
 * Tests persistence of FX trades and related objects
 */
public class FXPersistencePTestC
        extends PTestCaseC
{
    static String name = "FX Persistence Test";

    public FXPersistencePTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new FXPersistencePTestC( "testNamedWorkflowQueryRestriction" ) );
        return suite;
    }

    /**
     * Selects all trades that have a workflow state DOWNLOAD
     * that is in state TSDLINIT
     * <BR>
     * It is important to first get a common root expression to get the
     * named workflow state and add the name and state restriction on
     * this common expression.
     */
    public void testNamedWorkflowQueryRestriction()
    {
        String workflowStateName = "DOWNLOAD";
        String workflowStateState = "TSDLINIT";

        log( "testNamedWorkflowQueryRestriction" );
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression nwfsm = eb.get( "workflowStateMap" ).anyOf( "workflowStateMap" );
            Expression e1 = nwfsm.get( "name" ).equal( workflowStateName );
            Expression e2 = nwfsm.get( "state" ).get( "shortName" ).equal( workflowStateState );
            Expression expr = e1.and( e2 );

            Vector objs = getPersistenceSession().readAllObjects( Trade.class, expr );
            for ( int i = 0; i < objs.size(); i++ )
            {
                Trade trade = ( Trade ) objs.elementAt( i );
                log( "trade #" + i + ": " + trade );
                if ( ( trade.getWorkflowStateMap() != null )
                        && ( trade.getWorkflowStateMap().getMap() != null ) )
                {
                    Iterator it = trade.getWorkflowStateMap().getMap().keySet().iterator();
                    while ( it.hasNext() )
                    {
                        String key = ( String ) it.next();
                        NamedWorkflowState value = trade.getWorkflowState( key );
                        String state = value.getState().getShortName();
                        log( "\tworkflow state map entry:" + key + " --> " + state );
                    }
                }

            }
            log( "end testNamedWorkflowQueryRestriction" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testNamedWorkflowQueryRestriction" );
        }
    }
}
