package com.integral.finance.fx.test;


import com.integral.finance.currency.CurrencyC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.time.DateTimeFactory;
import com.integral.xml.binding.JavaXMLBinder;
import com.integral.xml.binding.JavaXMLBinderFactory;
import com.integral.xml.mapping.XMLMappingLoaderC;
import junit.framework.TestCase;

import java.io.OutputStreamWriter;

public class FXEventBuildingTestC
        extends TestCase
{
    public FXEventBuildingTestC( String aName )
    {
        super( aName );
    }

    protected void setUp()
    {
        try
        {
            super.setUp();

            XMLMappingLoaderC mappingLoader = new XMLMappingLoaderC();
            mappingLoader.preload( this.getClass().getClassLoader() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "couldn't load xml mapping file" + e );
        }
    }

    private FXSingleLeg newFXSpotTrade() throws Exception
    {
        FXSingleLeg trade = new FXSingleLegC();
        CurrencyC usd = new CurrencyC();
        usd.setShortName( "USD" );
        CurrencyC eur = new CurrencyC();
        usd.setShortName( "EUR" );

        trade.getFXLeg().getFXPayment().setCurrency1( usd );
        trade.getFXLeg().getFXPayment().setCurrency2( eur );

        trade.getFXLeg().getFXPayment().setCurrency1Amount( 11111.11 );
        trade.getFXLeg().getFXPayment().setCurrency2Amount( 22222.22 );

        trade.getFXLeg().setReceivingCounterpartyReference( "counterpartyC" );
        trade.getFXLeg().setPayingCounterpartyReference( "counterpartyD" );

        trade.getFXLeg().getFXPayment().setValueDate( DateTimeFactory.newDate( "2003-03-15" ) );

        return trade;
    }

    public void testSpot()
    {
        JavaXMLBinder xmlReader = JavaXMLBinderFactory.newJavaXMLBinder();
        FXSingleLeg fxspot;

        try
        {
            //fxspot = (FXSingleLeg)
            //	xmlReader.convertFromXMLFile ( "com/integral/finance/fx/test/fxspot.xml", "Integral" );
            fxspot = newFXSpotTrade();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            throw new RuntimeException( e.getMessage() );
        }

        try
        {
            xmlReader.convertToXML( new OutputStreamWriter( System.out ), fxspot, "Integral" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            throw new RuntimeException( e.getMessage() );
        }
    }

    public void spotTiming()
    {
        JavaXMLBinder xmlReader = JavaXMLBinderFactory.newJavaXMLBinder();
        FXSingleLeg fxspot;

        try
        {
            //fxspot = (FXSingleLeg)
            //	xmlReader.convertFromXMLFile ( "com/integral/finance/fx/test/fxspot.xml", "Integral" );
            fxspot = newFXSpotTrade();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            throw new RuntimeException( e.getMessage() );
        }

        long start = System.currentTimeMillis();
        for ( int i = 0; i < 100; i++ )
        {
            fxspot.buildAllFinancialEvents();
        }
        long end = System.currentTimeMillis();

        System.out.println( "100 FX build events time (ms): " + ( end - start ) );
    }


}
