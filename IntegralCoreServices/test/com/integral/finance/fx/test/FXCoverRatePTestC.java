package com.integral.finance.fx.test;

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.fx.FXCoverRate;
import com.integral.finance.fx.FXCoverRateC;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.STPFXCoverRate;
import com.integral.finance.fx.SpreadElement;
import com.integral.finance.fx.SpreadElementC;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.CptyTradeC;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryFactory;
import com.integral.test.DealingPTestCaseC;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.xml.binding.JavaXMLBinderFactory;
import com.integral.xml.mapping.startup.MappingStartupC;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;

/**
 * Test case for FXCoverRateEnhancement http://wiki/wiki/index.php/Product_Management/Save_Spreads_in_Quote_%26_Trade_and_Send_in_STP
 */
public class FXCoverRatePTestC extends DealingPTestCaseC
{

    static String name = "FX CoverRate Test";
    Vector users = null;

    public FXCoverRatePTestC( String name )
    {
        super( name );
        fiOrg = ( Organization ) namedEntityReader.execute( Organization.class, fiName );
        fiTpForLp = ( TradingParty ) namedEntityReader.execute( TradingPartyC.class, lpLeName, fiName );
    }

    public static void main( String args[] )
    {
    }

    static double spread1 = -1.343455;
    static double spread2 = 2.0;
    static double spread3 = 0.899922;
    static double spread4 = 2.0E-4;
    static double spread5 = 99.999;

    static double marketFXRateRate = 1.368;
    static double marketFXRateFwdPoints = 0.004;
    static double marketFXSpotRate = 1.364;
    double rateOnCoverRate = 1.1111;
    double fwdPointsOnCoverRate = 0.444444;
    static double spotRateOnCoverRate = 0.666656;


    static HashMap<Double, Double> doubleValues = new HashMap<Double, Double>();

    static
    {
        doubleValues.put( spread1, spread1 );
        doubleValues.put( spread2, spread2 );
        doubleValues.put( spread3, spread3 );
        doubleValues.put( spread4, spread4 );
        doubleValues.put( spread5, spread5 );
    }


    public void testSpreadsOnFXCoverRate()
    {

        try
        {

            init( fiUser );
            // Create a trade leg so we have a cover rate to play around
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            IdcDate spotDate = DateTimeFactory.newDate().addDays( 2 );
            FXSingleLeg singleLeg = DealingTestUtilC.createTestSingleLegTrade( 1000, true, false, true, "EUR/USD", fiOrg, fiTpForLp, 1.2365, spotDate );

            singleLeg = ( FXSingleLeg ) uow.registerObject( singleLeg );

            CptyTrade registeredCptyTrade = ( CptyTrade ) uow.registerObject( new CptyTradeC() );
            registeredCptyTrade.setNamespace( lpOrg.getNamespace() );
            registeredCptyTrade.setLegalEntity( lpOrg.getDefaultDealingEntity() );
            registeredCptyTrade.setTrade( singleLeg );
            registeredCptyTrade.setOwningCptyRef( 'B' );
            CptyTrade registeredCoveredCptyTrade = ( CptyTrade ) uow.registerObject( new CptyTradeC() );
            registeredCoveredCptyTrade.setNamespace( fiOrg.getNamespace() );
            registeredCoveredCptyTrade.setLegalEntity( fiOrg.getDefaultDealingEntity() );
            registeredCoveredCptyTrade.setTrade( singleLeg );
            registeredCoveredCptyTrade.setOwningCptyRef( 'A' );
            registeredCptyTrade.setCoveredCptyTrade( registeredCoveredCptyTrade );
            List<CptyTrade> cptyTrades = new ArrayList<CptyTrade>();
            cptyTrades.add( registeredCoveredCptyTrade );
            cptyTrades.add( registeredCptyTrade );
            singleLeg.setCptyTrades( cptyTrades );

            //singleLeg = ( FXSingleLeg ) uow.registerObject( singleLeg );
            log( "single leg fxLeg=" + singleLeg.getFXLeg() );
            assertEquals( "leg should not be null.", singleLeg.getFXLeg() != null, true );
            uow.commit();

            uow = PersistenceFactory.newSession().acquireUnitOfWork();
            singleLeg = ( FXSingleLeg ) uow.registerObject( singleLeg );
            FXCoverRate coverRate = singleLeg.getFXLeg().getFXPayment().getFXCoverRate();
            String ccyPair = coverRate.getFXRate().getCurrencyPair().getName();
            coverRate = ( FXCoverRate ) uow.registerObject( coverRate );
            coverRate.setNamespace( lpOrg.getNamespace() );
            coverRate.addSpread( FXCoverRate.PM_MIN_SPREAD, spread1 );
            coverRate.addSpread( FXCoverRate.PM_MAX_SPREAD, spread2 );
            coverRate.addSpread( FXCoverRate.PP_SPOT_SPREAD, spread3 );

            // Add MarketFXRate and forward points also since these are also to be shown in trade download.
            FXRate marketFXRate = DealingTestUtilC.createTestFXRate( ccyPair, marketFXRateRate );
            marketFXRate.setForwardPoints( marketFXRateFwdPoints );
            //marketFXRate = (FXRate)uow.registerObject(marketFXRate);
            coverRate.setMarketFxRate( marketFXRate );
            uow.commit();

            coverRate = ( FXCoverRateC ) PersistenceFactory.newSession().refreshObject( coverRate );
            Double spreadFromDB = coverRate.getSpread( FXCoverRate.PM_MAX_SPREAD );
            assertEquals( "Spread2 should be same as in DB", spreadFromDB, 2.0 );
            assertEquals( "Spread1 should be same as in DB", coverRate.getSpread( FXCoverRate.PM_MIN_SPREAD ), spread1 );
            assertEquals( "Spread3 should be same as in DB", coverRate.getSpread( FXCoverRate.PP_SPOT_SPREAD ), spread3 );

            Map allSpreads = coverRate.getAllSpreads();
            assertEquals( "size should be 3", allSpreads.size(), 3 );

            // Remove one and add another one..
            uow = PersistenceFactory.newSession().acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            coverRate = ( FXCoverRateC ) uow.registerObject( coverRate );
            coverRate.removeSpread( FXCoverRate.PM_MAX_SPREAD );
            // REmove NON EXISTENT SPREAD
            coverRate.removeSpread( "TEST" );
            coverRate.addSpread( FXCoverRate.PP_SPOT_SPREAD, spread4 );
            coverRate.addSpread( "NON_STANDARD_SPREAD", spread5 );
            uow.commit();

            // Test for correct values now..
            coverRate = ( FXCoverRateC ) PersistenceFactory.newSession().refreshObject( coverRate );
            assertEquals( "Spread2 should be null now - hence 0", coverRate.getSpread( FXCoverRate.PM_MAX_SPREAD ), 0.0 );
            assertEquals( "Spread4 should be same as in DB", coverRate.getSpread( FXCoverRate.PP_SPOT_SPREAD ), spread4 );
            assertEquals( "Spread1 should be same as in DB", coverRate.getSpread( FXCoverRate.PM_MIN_SPREAD ), spread1 );

            allSpreads = coverRate.getAllSpreads();
            assertEquals( "size should be 3 now", allSpreads.size(), 3 );
            String transactionID = singleLeg.getTransactionID();

            //Now let us test if trade download has these spread elements..
            ExpressionBuilder eb = new ExpressionBuilder();
            ReadAllQuery raq = new ReadAllQuery( TradeC.class, eb.get( "transactionID" ).equal( transactionID ) );
            Vector tradeVect = ( Vector ) QueryFactory.getQueryService().find( lpOrg.getNamespace(), raq );

            Trade trade = ( Trade ) tradeVect.get( 0 );
            FXSingleLeg singleLegTrade = ( FXSingleLeg ) trade;
            singleLegTrade.getCptyTrade( 'A' );
            singleLegTrade.getCptyTrade( 'B' );
            trade.setCptyTrade( singleLegTrade.getCptyTrade( 'B' ) );

            FXSingleLeg trade1 = ( FXSingleLeg ) singleLegTrade.getFXLeg().getFXPayment().getContainingTrade();
            trade1.getCptyTrade( 'A' );
            trade1.getCptyTrade( 'B' );
            trade1.setCptyTrade( trade1.getCptyTrade( 'B' ) );
            singleLegTrade.setCptyTrade( trade1.getCptyTrade( 'B' ) );

            //jut to initialze the objects
            FXCoverRate rate = singleLegTrade.getFXLeg().getFXPayment().getFXCoverRate();
            System.out.println( " RATE IS " + rate.getAllSpreads() );
            // initialize cover rate also..
            STPFXCoverRate rateSpread = singleLegTrade.getFXLeg().getFXPayment().getCptyFXCoverRate();
            System.out.println( " Rate Spread " + rateSpread );

            MappingStartupC startup = new MappingStartupC();
            startup.startup( null, null );

            //----------------- CHECKS FOR SPREADS ------------------------//

            // org.sendSpreadsInSTP is not set on lpOrg -  default behaviour - so num spreads should be 0
            lpOrg.setSendSpreadsInSTP( false );
            Document doc = getXMLDocument( trade );
            int ctr = getNumSpreadElementsInFXCoverRate( doc );
            System.out.println( " spreads size " + ctr );
            assertNotSame( "All double values not present in collection", -999, ctr );
            assertEquals( "Counter should be 0", 0, ctr );
            // baseRate and baseForwardPoint Tags should not be present
            assertEquals( "BaseRate and fws points should not be present", false, isExistRateAndForwardPoints( doc ) );

            // Now let us enabble LPORG for spreads - 3 spreads should get downloaded now..
            lpOrg.setSendSpreadsInSTP( true );
            doc = getXMLDocument( trade );
            System.out.println( " doc iss " + doc );
            ctr = getNumSpreadElementsInFXCoverRate( doc );
            System.out.println( " spreads size " + ctr );
            assertNotSame( "All double values not present in collection", -999, ctr );
            assertEquals( "Counter should be 3", 3, ctr );
            assertEquals( "BaseRate and fws points should be present", true, isExistRateAndForwardPoints( doc ) );

            //Spreads (infact fxCoverRate) should not get downloaded for counterparty A regardless of spreads Enabled on lpOrg and / or fiOrg
            trade.setCptyTrade( singleLegTrade.getCptyTrade( 'A' ) );
            fiOrg.setSendSpreadsInSTP( true );
            doc = getXMLDocument( trade );
            assertEquals( "FXCover Rate should not exist ", isFXCoverRateExists( doc ), false );
            ctr = getNumSpreadElementsInFXCoverRate( doc );
            System.out.println( " spreads size " + ctr );
            assertNotSame( "All double values not present in collection", -999, ctr );
            assertEquals( "Counter should be 0", 0, ctr );
            //Reset back to cptyTrade ( B )
            trade.setCptyTrade( singleLegTrade.getCptyTrade( 'B' ) );

            // --------------------- Check for base rate and forward points gettting picked up either from market Rate or cover Rate's Rate ---------------------- //
            //background work
            rate = singleLegTrade.getFXLeg().getFXPayment().getFXCoverRate();
            rate.getFXRate().setRate( rateOnCoverRate );
            rate.getFXRate().setForwardPoints( fwdPointsOnCoverRate );

            // When Market Rate is set :
            doc = getXMLDocument( trade );
            boolean result = testRateAndForwardPoints( doc, marketFXRateRate, marketFXRateFwdPoints );
            assertEquals( "Rate on Market FX Cover Rate Test ", true, result );

            // when market fx rate is not set : Set it to null in memory since it is already present
            singleLegTrade.getFXLeg().getFXPayment().getFXCoverRate().setMarketFxRate( null );
            doc = getXMLDocument( trade );
            result = testRateAndForwardPoints( doc, rateOnCoverRate, fwdPointsOnCoverRate );
            assertEquals( "Rate on Cover Rate Test ", true, result );


        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            lpOrg.setSendSpreadsInSTP( false );
        }

    }

    private boolean testRateAndForwardPoints( Document doc, double rate, double fwdPoints )
    {
        NodeList nodes = doc.getElementsByTagName( "fxCoverRate" ).item( 0 ).getChildNodes();
        int nodesSize = nodes.getLength();
        boolean rateAndFwdTest = true;
        double rateInXML = 0d;
        double fwdPointsInXML = 0d;
        double spotRateInXML = 0d;
        for ( int i = 0; i < nodesSize; i++ )
        {
            Node node = nodes.item( i );
            if ( node.getNodeName().equals( "baseRate" ) )
            {
                rateInXML = Double.valueOf( node.getChildNodes().item( 0 ).getNodeValue() );
            }
            if ( node.getNodeName().equals( "baseForwardPoints" ) )
            {
                fwdPointsInXML = Double.valueOf( node.getChildNodes().item( 0 ).getNodeValue() );
            }
            if ( node.getNodeName().equals( "baseSpotRate" ) )
            {
                spotRateInXML = Double.valueOf( node.getChildNodes().item( 0 ).getNodeValue() );
            }


        }

        if ( !( rate == ( rateInXML - fwdPointsInXML ) ) )
        {
            rateAndFwdTest = false;
            log.error( "FXCoverRatePTestC. testRateAndForwardPoints Fail base rate mismatch  wanted " + rate + " actual: " + rateInXML );
        }
        if ( !( fwdPoints == fwdPointsInXML ) )
        {
            rateAndFwdTest = false;
            log.error( "FXCoverRatePTestC. testRateAndForwardPoints Fail base forward points mismatch  wanted " + fwdPoints + " actual:  " + fwdPointsInXML );
        }
        if ( !( rate == ( spotRateInXML ) ) )
        {
            rateAndFwdTest = false;
            log.error( "FXCoverRatePTestC. testRateAndForwardPoints Fail base spot rate mismatch  wanted " + rate + " actual: " + spotRateInXML );
        }

        return rateAndFwdTest;
    }

    private boolean isExistRateAndForwardPoints( Document doc )
    {
        NodeList nodes = doc.getElementsByTagName( "fxCoverRate" );
        boolean baseRateTag = false;
        boolean fwdPointTag = false;

        if ( nodes != null )
        {
            nodes = nodes.item( 0 ).getChildNodes();
            int nodesSize = nodes.getLength();

            for ( int i = 0; i < nodesSize; i++ )
            {
                Node node = nodes.item( i );
                if ( node.getNodeName().equals( "baseRate" ) )
                {
                    baseRateTag = true;
                }
                if ( node.getNodeName().equals( "baseForwardPoints" ) )
                {
                    fwdPointTag = true;
                }
            }
        }
        return ( baseRateTag & fwdPointTag );
    }

    private Document getXMLDocument( Trade trade )
    {
        Document doc = null;
        StringWriter writer = new StringWriter( 1024 );
        try
        {
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( writer, trade, "TradeDownload" );
            System.out.println( "" + writer.toString() );

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            doc = builder.parse( new InputSource( new StringReader( writer.toString() ) ) );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
        return doc;

    }

    private boolean isFXCoverRateExists( Document doc )
    {
        NodeList coverRateNodes = doc.getElementsByTagName( "fxCoverRate" );
        return coverRateNodes != null && coverRateNodes.getLength() != 0;
    }


    private int getNumSpreadElementsInFXCoverRate( Document doc )
    {
        int ctr = 0;
        NodeList coverRateNodes = doc.getElementsByTagName( "fxCoverRate" );
        if ( coverRateNodes != null && coverRateNodes.getLength() != 0 )
        {
            NodeList nodes = coverRateNodes.item( 0 ).getChildNodes();
            int nodesSize = nodes.getLength();
            boolean spreadTest = true;
            for ( int i = 0; i < nodesSize; i++ )
            {
                Node node = nodes.item( i );
                if ( node.getNodeName().equals( "spread" ) )
                {
                    ctr++;
                    if ( !doubleValues.containsKey( Double.parseDouble( node.getChildNodes().item( 0 ).getNodeValue() ) ) )
                    {
                        spreadTest = false;
                    }
                }
            }
            if ( !spreadTest )
            {
                return -999;
            }
            else
            {
                return ctr;
            }
        }
        return ctr;

    }

    public void testSpreadElementSerialization()
    {
        List<SpreadElement> list = new ArrayList<SpreadElement>();
        list.add( new SpreadElementC( "AAA", 1.000 ) );
        list.add( new SpreadElementC( "BBB", 2.000 ) );

        // This spread should get skipped since spread size is becoming greater than 1000
        StringBuilder sb = new StringBuilder( 1000 );
        for ( int i = 0; i < 333; i++ )
        {
            sb.append( "RRR" );
        }
        list.add( new SpreadElementC( sb.toString(), 9.999999 ) );
        //Add one more spread.. the one above should get skipped since it is causing characters to go more than 1000
        list.add( new SpreadElementC( "PSP", 3.333 ) );

        //Make a map for testing later on..
        Map<String, SpreadElement> spreadMap = new HashMap<String, SpreadElement>();
        for ( SpreadElement sprdEle : list )
        {
            spreadMap.put( sprdEle.getSpreadName(), sprdEle );
        }


        try
        {
            // --------------SERIALIZE-------------------------
            String serializedObj = FXCoverRateC.serializeSpreadElements( list );
            System.out.println( " Serialized SE " + serializedObj );

            // ------------------DE-SERIALIZE------------------
            List<SpreadElement> deSerializedObj = FXCoverRateC.deSerializeSpreadElements( serializedObj );
            System.out.println( " objList " + deSerializedObj );
            for ( SpreadElement sprd : deSerializedObj )
            {
                System.out.println( " SPRD " + sprd );
            }
            // Now Test for adding this to FXCoverRate..
            FXCoverRateC coverRate = new FXCoverRateC();
            coverRate.addSpreadElements( deSerializedObj );
            Map<String, SpreadElement> spreadColl = coverRate.getAllSpreads();
            Set<String> keys = spreadColl.keySet();
            assertEquals( "Size should be 3 (not 4 - large one should get skipped)", spreadColl.size(), 3 );
            for ( String eleName : keys )
            {
                SpreadElement sprdEle = spreadMap.get( eleName );
                SpreadElement fromCoverRate = spreadColl.get( eleName );
                assertEquals( "NAme", sprdEle.getSpreadName(), fromCoverRate.getSpreadName() );
                System.out.println( "Spread NAme " + sprdEle.getSpreadName() );
                //Check for auto setting of readings name even if none supplied
                if ( fromCoverRate.getSpreadName().equals( "PPSpotSprd" ) )
                {
                    assertEquals( "Readings name should have got set automatically ", fromCoverRate.getSpreadName(), "PPSpotSprd" );
                }
                assertEquals( "Value ", sprdEle.getSpreadValue(), fromCoverRate.getSpreadValue() );
            }


        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }

    }

}
