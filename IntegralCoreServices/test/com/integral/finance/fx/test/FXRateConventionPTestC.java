package com.integral.finance.fx.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.*;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.util.IdcUtilC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Iterator;
import java.util.StringTokenizer;
import java.util.Vector;

public class FXRateConventionPTestC extends PTestCaseC
{

    static String name = "FXRateConvention Tests";

    static String standardFXConventionName = "STDQOTCNV";

    static String testFXConventionName = "UNITTESTCONV";

    static String[] standardCurrencyPairs = {"GBP/AUD", "AUD/USD"};

    //These must be inverted.
    static String[] customCurrencyPairs = {"USD/EUR"};

    static String[] updateCustomCurrencyPairs = {"JPY/EUR"};

    static String[] parentPairLookup = {"AUD/HKD"};

    private boolean parentPairLookupSupported = true;

    static String grandChildFXConventionName = "GRANDCHILDUNITTESTCONV";

    static String childFXConventionName = "CHILDUNITTESTCONV";

    static String parentFXConventionName = "PARENTUNITTESTCONV";

    static String[] grandChildCurrencyPairs = {"JPY/EUR", "USD/CHF"};
    static String[] childCurrencyPairs = {"JPY/EUR", "AUD/USD"};
    static String[] parentCurrencyPairs = {"USD/EUR", "JPY/EUR"};

    public FXRateConventionPTestC ( String aName )
    {
        super ( aName );
    }

    public static void main ( String args[] )
    {
        junit.textui.TestRunner.run ( suite () );
    }

    public static Test suite ( )
    {
        TestSuite suite = new TestSuite ();
        suite.addTest ( new FXRateConventionPTestC ( "testInsert" ) );
        suite.addTest ( new FXRateConventionPTestC ( "testLoad" ) );
        suite.addTest ( new FXRateConventionPTestC ( "testUpdate" ) );
        suite.addTest ( new FXRateConventionPTestC ( "testFxRateConventionBusinessLogic" ) );
        suite.addTest ( new FXRateConventionPTestC ( "testCustomBusinessLogic" ) );
        suite.addTest ( new FXRateConventionPTestC ( "testDisplayRateBasis" ) );
        suite.addTest ( new FXRateConventionPTestC ( "testFXRateBasisLookup" ) );
        suite.addTest ( new FXRateConventionPTestC ( "testEffectiveFXRateBasis" ) );
        suite.addTest ( new FXRateConventionPTestC ( "testDelete" ) );
        return suite;
    }

    public void testLoad ( )
    {
        try
        {
            this.log ( "Start FX RateConvention Load Test" );
            FXRateConvention obj =
                    this.getFXRateConvention ( standardFXConventionName );
            if ( obj != null )
            {
                this.log ( "Test Passed Loaded FXRateConvention" );
            }
            else
            {
                fail ( "Failed Test unable to located Standard FXRateConvention" );
                this.log ( "End FX Rate Convention Load Test" );
            }
        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "loadTest" );
        }

    }

    public void testInsert ( )
    {
        try
        {
            FXRateConvention aTestConvention = this.getFXRateConvention ( testFXConventionName );
            if ( aTestConvention != null )
            {
                return;
            }
            this.log ( "Start FX RateConvention insert Test" );
            this.uow = this.getPersistenceSession ().acquireUnitOfWork ();
            this.uow.removeAllReadOnlyClasses ();
            this.uow.addReadOnlyClass ( CurrencyC.class );
            FXRateConvention aConvention = FXFactory.newFXRateConvention ();
            FXRateConvention standardConvention = this.getFXRateConvention ( standardFXConventionName );
            if ( standardConvention == null )
            {
                throw new Exception ( "TEST STANDARD CONVENTION NOT FOUND " + standardFXConventionName );
            }
            aConvention.setShortName ( testFXConventionName );
            aConvention = ( FXRateConvention ) this.uow.registerObject ( aConvention );
            aConvention.setParent ( ( FXRateConvention ) uow.registerObject ( standardConvention ) );
            FXRateBasis aBasis;
            StringTokenizer pairSplitter;
            this.log ( "Adding New FXRateBasis Objects" );
            for ( int i = 0; i < standardCurrencyPairs.length; i++ )
            {
                aBasis = ( FXRateBasis ) this.uow.registerNewObject ( FXFactory.newFXRateBasis () );
                pairSplitter =
                        new StringTokenizer ( standardCurrencyPairs[i], "/" );
                aBasis.setBaseCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aBasis.setVariableCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aBasis.setOwner ( aConvention );
                aConvention.addFXRateBasis ( aBasis );
            }
            CustomFXRateBasis aCustomBasis;
            FXRateBasis parentBasis;
            this.log ( "Adding New CustomFXRateBasis Objects" );
            for ( int i = 0; i < customCurrencyPairs.length; i++ )
            {
                aCustomBasis = ( CustomFXRateBasis ) this.uow.registerNewObject ( FXFactory.newCustomFXRateBasis () );
                pairSplitter =
                        new StringTokenizer ( customCurrencyPairs[i], "/" );
                aCustomBasis.setBaseCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aCustomBasis.setVariableCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                parentBasis =
                        standardConvention.getFXRateBasis ( customCurrencyPairs[i] );
                aCustomBasis.setParent (
                        ( FXRateBasis ) this.uow.registerObject ( parentBasis ) );
                aCustomBasis.setOwner ( aConvention );
                aConvention.addFXRateBasis ( aCustomBasis );
            }
            this.log ( "Committing transaction" );
            this.uow.commit ();
            this.log ( "End FX Rate Convention insert Test" );
        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "insertTest" );
        }
    }

    public void testUpdate ( )
    {

        try
        {
            this.log ( "Start FX RateConvention update Test" );
            this.uow = this.getPersistenceSession ().acquireUnitOfWork ();
            this.uow.removeAllReadOnlyClasses ();
            FXRateConvention aTestConvention = this.getFXRateConvention ( testFXConventionName );
            aTestConvention = ( FXRateConvention ) this.uow.registerObject ( aTestConvention );
            if ( aTestConvention == null )
            {
                throw new Exception ( "TEST CONVENTION NOT FOUND " + testFXConventionName );
            }
            FXRateConvention standardConvention =
                    this.getFXRateConvention ( standardFXConventionName );
            if ( standardConvention == null )
            {
                throw new Exception ( "TEST STANDARD CONVENTION NOT FOUND " + standardFXConventionName );
            }
            StringTokenizer pairSplitter;
            CustomFXRateBasis aCustomBasis;
            FXRateBasis parentBasis;
            this.log ( "Adding New CustomFXRateBasis Objects" );
            for ( int i = 0; i < updateCustomCurrencyPairs.length; i++ )
            {
                parentBasis = null;
                aCustomBasis = ( CustomFXRateBasis ) this.uow.registerNewObject ( FXFactory.newCustomFXRateBasis () );
                pairSplitter =
                        new StringTokenizer ( updateCustomCurrencyPairs[i], "/" );
                aCustomBasis.setBaseCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aCustomBasis.setVariableCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                parentBasis =
                        standardConvention.getFXRateBasis ( updateCustomCurrencyPairs[i] );
                if ( parentBasis == null )
                {
                    throw new Exception ( "Parent Basis " + updateCustomCurrencyPairs[i] + " NOT FOUND" );
                }
                aCustomBasis.setParent (
                        ( FXRateBasis ) this.uow.registerObject ( parentBasis ) );
                aCustomBasis.setOwner ( aTestConvention );
                aTestConvention.addFXRateBasis ( aCustomBasis );
            }
            this.log ( "Committing transaction" );
            this.uow.commit ();
            this.log ( "End FX Rate Convention update Test" );
        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "updateTest" );
        }
    }

    protected Currency getCurrency ( String shortName )
    {
        return ( Currency ) this.getNamedEntity ( Currency.class, shortName );
    }

    protected FXRateConvention getFXRateConvention ( String shortName )
    {
        return ( FXRateConvention ) this.getNamedEntity (
                FXRateConvention.class,
                shortName );
    }

    protected NamedEntity getNamedEntity ( Class aClass, String shortName )
    {
        ExpressionBuilder eb = new ExpressionBuilder ();
        Expression expr = eb.get ( "shortName" ).equal ( shortName );
        Object obj = null;
        if ( this.uow == null )
        {
            obj = this.getPersistenceSession ().readObject ( aClass, expr );
        }
        else
        {
            obj = this.uow.readObject ( aClass, expr );
        }
        return ( NamedEntity ) obj;
    }

    public void testFxRateConventionBusinessLogic ( )
    {
        try
        {
            this.log ( "Start FX RateConvention business logic Test" );
            FXRateConvention aTestConvention = this.getFXRateConvention ( testFXConventionName );
            if ( aTestConvention == null )
            {
                throw new Exception ( "TEST CONVENTION NOT FOUND " + testFXConventionName );
            }
            for ( int i = 0; i < customCurrencyPairs.length; i++ )
            {
                FXRateBasis customLookup = aTestConvention.getFXRateBasis ( customCurrencyPairs[i] );
                if ( !(customLookup instanceof CustomFXRateBasis) )
                {
                    throw new Exception ( "FXRateBasis lookup failed for custom pair " + customCurrencyPairs[i] );
                }
            }
            this.log ( "Passed custom lookup" );
            if ( parentPairLookupSupported )
            {
                for ( int i = 0; i < parentPairLookup.length; i++ )
                {
                    String cp = parentPairLookup[i];
                    Currency baseCcy = CurrencyFactory.getCurrency ( cp.substring ( 0, cp.indexOf ( '/' ) ) );
                    Currency varCcy = CurrencyFactory.getCurrency ( cp.substring ( cp.indexOf ( '/' ) + 1 ) );

                    FXRateBasis customLookup1 = aTestConvention.getFXRateBasis ( baseCcy, varCcy );
                    assertNotNull ( customLookup1 );
                    FXRateBasis customLookup2 = aTestConvention.getFXRateBasis ( baseCcy.getShortName (), varCcy.getShortName () );
                    assertNotNull ( customLookup2 );
                    FXRateBasis customLookup3 = aTestConvention.getFXRateBasis ( cp );
                    assertNotNull ( customLookup3 );
                    assertTrue ( customLookup1.isSameAs ( customLookup2 ) );
                    assertTrue ( customLookup2.isSameAs ( customLookup3 ) );

                    FXRateBasis rb1 = aTestConvention.getFXRateBasis ( baseCcy, varCcy, false );
                    assertNull ( rb1 );
                }
                this.log ( "Passed custom lookup" );
            }
            this.log ( "End FX RateConvention business logic Test" );
        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "fxRateConventionBusinessLogicTest" );
        }

    }


    public void testCustomBusinessLogic ( )
    {
        try
        {
            this.log ( "Start FX RateConvention custom business logic Test" );
            FXRateConvention aTestConvention = this.getFXRateConvention ( testFXConventionName );
            if ( aTestConvention == null )
            {
                throw new Exception ( "TEST CONVENTION NOT FOUND " + testFXConventionName );
            }
            for ( int i = 0; i < customCurrencyPairs.length; i++ )
            {
                CustomFXRateBasis customFXRateBasis = ( CustomFXRateBasis ) aTestConvention.getFXRateBasis ( customCurrencyPairs[i] );
                if ( !(customFXRateBasis instanceof CustomFXRateBasis) )
                {
                    throw new Exception ( "FXRateBasis lookup failed for custom pair " + customCurrencyPairs[i] );
                }
                FXRateBasis parentBasis = aTestConvention.getParent ().getFXRateBasis ( customCurrencyPairs[i] );
                this.validate ( customFXRateBasis, parentBasis );
            }
            this.log ( "Passed" );
            this.log ( "End FX RateConvention business logic Test" );
        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "fxRateConventionBusinessLogicTest" );
        }

    }

    public void testDelete ( )
    {
        try
        {
            this.log ( "Start FX RateConvention Delete Test" );
            FXRateConvention aTestConvention = this.getFXRateConvention ( testFXConventionName );
            if ( aTestConvention == null )
            {
                throw new Exception ( "TEST CONVENTION NOT FOUND " + testFXConventionName );
            }
            this.uow = this.getPersistenceSession ().acquireUnitOfWork ();
            this.uow.removeAllReadOnlyClasses ();
            this.uow.deleteObject ( aTestConvention );
            this.log ( "Deleting object " + testFXConventionName );
            uow.commit ();
            this.log ( "End FX RateConvention business logic Test" );
        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "deleteTest" );
        }

    }

    public void testDisplayRateBasis ( )
    {
        try
        {
            Session session = PersistenceFactory.newSession ();
            Vector rateConventions = session.readAllObjects ( FXRateConvention.class );
            Iterator rcIt = rateConventions.iterator ();
            while ( rcIt.hasNext () )
            {
                FXRateConvention aTestConvention = ( FXRateConvention ) rcIt.next ();
                log ( "testing deletion of fx rate basis for quote convention " + aTestConvention );
                Iterator it = aTestConvention.getFXRateBasis ().iterator ();
                while ( it.hasNext () )
                {
                    FXRateBasis basis = ( FXRateBasis ) it.next ();
                    log ( "\trate basis: " + basis );
                }
            }
        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "testDeleteRateBasis" );
        }

    }

    public void testDeleteRateBasis ( )
    {
        try
        {
            String FXRATEBASISNAME = "KWD/SAR";
            Currency kwd = CurrencyFactory.getCurrency ( "KWD" );
            Currency sar = CurrencyFactory.getCurrency ( "SAR" );
            FXRateBasis kwdsar = null;

            Session session = PersistenceFactory.newSession ();
            FXRateConvention aTestConvention = this.getFXRateConvention ( standardFXConventionName );
            log ( "testing deletion of fx rate basis for quote convention " + aTestConvention );

            // check for rate basis

            kwdsar = aTestConvention.getFXRateBasis ( kwd, sar );
            if ( kwdsar == null )
            {
                UnitOfWork uow1 = session.acquireUnitOfWork ();
                kwdsar = ( FXRateBasis ) uow1.registerObject ( FXFactory.newFXRateBasis () );
                kwdsar.setBaseCurrency ( CurrencyFactory.getCurrency ( "KWD" ) );
                kwdsar.setVariableCurrency ( CurrencyFactory.getCurrency ( "SAR" ) );
                kwdsar.setFXRateConvention ( ( FXRateConvention ) uow1.registerObject ( aTestConvention ) );
                uow1.commit ();
            }

            Iterator it = aTestConvention.getFXRateBasis ().iterator ();
            while ( it.hasNext () )
            {
                FXRateBasis basis = ( FXRateBasis ) it.next ();
                log ( "\trate basis: " + basis );
                if ( FXRATEBASISNAME.equals ( basis.getCurrencyPairString () ) )
                {
                    kwdsar = basis;
                }
            }
            assertNotNull ( kwdsar );

            // delete rate basis
            UnitOfWork uow = session.acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            FXRateConvention cloneRC = ( FXRateConvention ) uow.registerObject ( aTestConvention );
            FXRateBasis cloneRB = ( FXRateBasis ) uow.registerObject ( kwdsar );
            cloneRC.getFXRateBasis ().remove ( cloneRB );
            boolean found = false;
            it = cloneRC.getFXRateBasis ().iterator ();
            while ( it.hasNext () )
            {
                FXRateBasis basis = ( FXRateBasis ) it.next ();
                if ( FXRATEBASISNAME.equals ( basis.getCurrencyPairString () ) )
                {
                    found = true;
                }
            }
            assertEquals ( false, found );
            uow.commit ();

            // check now shared copy
            found = false;
            it = aTestConvention.getFXRateBasis ().iterator ();
            while ( it.hasNext () )
            {
                FXRateBasis basis = ( FXRateBasis ) it.next ();
                if ( "KWD/SAR".equals ( basis.getCurrencyPairString () ) )
                {
                    found = true;
                }
            }
            assertEquals ( false, found );
        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "testDeleteRateBasis" );
        }

    }

    public void validate ( CustomFXRateBasis customBasis, FXRateBasis parentBasis ) throws Exception
    {

        if ( customBasis.getForwardPointsPrecision () != parentBasis.getInverseForwardPointsPrecision () )
        {
            throw new Exception ( "Failed Vaildation custom getForwardPointsPrecision != parent getInverseForwardPointsPrecision" );
        }
        if ( customBasis.getSpotPointsPrecision () != parentBasis.getInverseSpotPointsPrecision () )
        {
            throw new Exception ( "Failed Vaildation custom getSpotPointsPrecision != parent getInverseSpotPointsPrecision" );
        }
        if ( customBasis.getSpotPrecision () != parentBasis.getInverseSpotPrecision () )
        {
            throw new Exception ( "Failed Vaildation custom getSpotPrecision != parent getInverseSpotPrecision" );
        }
        if ( customBasis.getForwardRatePrecision () != parentBasis.getInverseForwardRatePrecision () )
        {
            throw new Exception ( "Failed Vaildation custom getForwardRatePrecision != parent getInverseForwardRatePrecision" );
        }
        if ( customBasis.getPipsFactor () != parentBasis.getInversePipsFactor () )
        {
            throw new Exception ( "Failed Vaildation custom getPipsFactor != parent getInversePipsFactor" );
        }

    }

    public void testFXRateBasisLookup ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" );
            this.uow = this.getPersistenceSession ().acquireUnitOfWork ();
            this.uow.removeAllReadOnlyClasses ();
            this.uow.addReadOnlyClass ( CurrencyC.class );
            FXRateConvention testConv = FXFactory.newFXRateConvention ();
            String convName = "T" + System.currentTimeMillis ();
            testConv.setShortName ( convName );
            testConv = ( FXRateConvention ) this.uow.registerObject ( testConv );
            FXRateBasis eurUsd = FXFactory.newFXRateBasis ();
            eurUsd = ( FXRateBasis ) uow.registerObject ( eurUsd );
            eurUsd.setBaseCurrency ( eur );
            eurUsd.setVariableCurrency ( usd );
            testConv.addFXRateBasis ( eurUsd );
            assertNotNull ( testConv.getFXRateBasis ( eur, usd ) );
            assertNotNull ( testConv.getFXRateBasis ( usd, eur ) );
            uow.commit ();

            testConv = ( FXRateConvention ) getPersistenceSession ().refreshObject ( testConv );
            assertNotNull ( testConv.getFXRateBasis ( eur, usd ) );
            assertNotNull ( testConv.getFXRateBasis ( usd, eur ) );
            assertNull ( testConv.getFXRateBasis ( eur, gbp ) );
            assertNull ( testConv.getFXRateBasis ( gbp, eur ) );

            FXRate fxRate = FXFactory.newFXRate ( eur, usd, testConv );
            FXRateBasis fxRateBasis = fxRate.getFXRateBasis ();
            log ( "fxratebasis=" + fxRateBasis );
            assertNotNull ( fxRateBasis );

        }
        catch ( Exception e )
        {
            fail ( "testFXRateBasisLookup", e );
        }
    }

    public void testEffectiveFXRateBasis ( )
    {
        FXRateConvention grandChildConvention = this.getFXRateConvention ( grandChildFXConventionName );
        FXRateConvention childConvention = this.getFXRateConvention ( childFXConventionName );
        FXRateConvention parentConvention = this.getFXRateConvention ( parentFXConventionName );
        try
        {
            FXRateConvention std = this.getFXRateConvention ( standardFXConventionName );
            Collection<FXRateBasis> stdBases = std.getFXRateBasis ();
            Collection<FXRateBasis> effStdBases = std.getEffectiveFXRateBasis ();
            assertTrue ( stdBases.size () == effStdBases.size () );
            if ( childConvention != null || parentConvention != null || grandChildConvention != null )
            {
                return;
            }
            log ( "Start EffectiveFXRateBasis Test" );
            this.uow = this.getPersistenceSession ().acquireUnitOfWork ();
            this.uow.removeAllReadOnlyClasses ();

            parentConvention = FXFactory.newFXRateConvention ();
            FXRateConvention registeredParentConvention = ( FXRateConvention ) this.uow.registerObject ( parentConvention );
            registeredParentConvention.setShortName ( parentFXConventionName );
            FXRateBasis aBasis;
            StringTokenizer pairSplitter;
            StringBuilder parentSB = new StringBuilder ();
            for ( int i = 0; i < parentCurrencyPairs.length; i++ )
            {
                aBasis = ( FXRateBasis ) this.uow.registerNewObject ( FXFactory.newFXRateBasis () );
                pairSplitter =
                        new StringTokenizer ( parentCurrencyPairs[i], "/" );
                aBasis.setBaseCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aBasis.setVariableCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aBasis.setOwner ( registeredParentConvention );
                registeredParentConvention.addFXRateBasis ( aBasis );
                if ( i != 0 )
                {
                    parentSB.append ( ", " );
                }
                parentSB.append ( aBasis );
            }
            log ( "Adding Parent FXRateBasis Objects: " + parentSB );

            childConvention = FXFactory.newFXRateConvention ();
            FXRateConvention registeredChildConvention = ( FXRateConvention ) this.uow.registerObject ( childConvention );
            registeredChildConvention.setShortName ( childFXConventionName );
            StringBuilder childSB = new StringBuilder ();

            for ( int i = 0; i < childCurrencyPairs.length; i++ )
            {
                aBasis = ( FXRateBasis ) this.uow.registerNewObject ( FXFactory.newFXRateBasis () );
                pairSplitter =
                        new StringTokenizer ( childCurrencyPairs[i], "/" );
                aBasis.setBaseCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aBasis.setVariableCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aBasis.setOwner ( registeredChildConvention );
                registeredChildConvention.addFXRateBasis ( aBasis );
                if ( i != 0 )
                {
                    childSB.append ( ", " );
                }
                childSB.append ( aBasis );
            }
            log ( "Adding Child FXRateBasis Objects: " + childSB );
            registeredChildConvention.setParent ( registeredParentConvention );

            grandChildConvention = FXFactory.newFXRateConvention ();
            FXRateConvention registeredGrandChildConvention = ( FXRateConvention ) this.uow.registerObject ( grandChildConvention );
            registeredGrandChildConvention.setShortName ( grandChildFXConventionName );
            StringBuilder grandChildSB = new StringBuilder ();

            for ( int i = 0; i < grandChildCurrencyPairs.length; i++ )
            {
                aBasis = ( FXRateBasis ) this.uow.registerNewObject ( FXFactory.newFXRateBasis () );
                pairSplitter =
                        new StringTokenizer ( grandChildCurrencyPairs[i], "/" );
                aBasis.setBaseCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aBasis.setVariableCurrency (
                        this.getCurrency ( pairSplitter.nextToken () ) );
                aBasis.setOwner ( registeredGrandChildConvention );
                registeredGrandChildConvention.addFXRateBasis ( aBasis );
                if ( i != 0 )
                {
                    grandChildSB.append ( ", " );
                }
                grandChildSB.append ( aBasis );
            }
            log ( "Adding Child FXRateBasis Objects: " + grandChildSB );
            registeredGrandChildConvention.setParent ( registeredChildConvention );

            Collection<FXRateBasis> effectiveRateBasisGrandChild = registeredGrandChildConvention.getEffectiveFXRateBasis ();
            log ( "Displaying Grand Child FX Rate Basis" );
            for ( FXRateBasis basis : effectiveRateBasisGrandChild )
            {
                log ( "FXRateBasis: " + basis + " - Owner FXRateConvention: " + basis.getFXRateConvention ().getShortName () );
            }
            assertTrue ( effectiveRateBasisGrandChild.size () == 4 );
            FXRateBasis rb1 = registeredGrandChildConvention.getFXRateBasis ( "JPY/EUR" );
            assertEquals ( rb1.getOwner (), registeredGrandChildConvention );
            FXRateBasis rb2 = registeredGrandChildConvention.getFXRateBasis ( "AUD/USD" );
            assertEquals ( rb2.getOwner (), registeredChildConvention );

            Collection<FXRateBasis> effectiveRateBasisChild = registeredChildConvention.getEffectiveFXRateBasis ();
            log ( "Displaying Child FX Rate Basis" );
            for ( FXRateBasis basis : effectiveRateBasisChild )
            {
                log ( "FXRateBasis: " + basis + " - Owner FXRateConvention: " + basis.getFXRateConvention ().getShortName () );
            }
            assertTrue ( effectiveRateBasisChild.size () == 3 );
            FXRateBasis rb3 = registeredChildConvention.getFXRateBasis ( "JPY/EUR" );
            assertEquals ( rb3.getOwner (), registeredChildConvention );
            FXRateBasis rb4 = registeredChildConvention.getFXRateBasis ( "USD/EUR" );
            assertEquals ( rb4.getOwner (), registeredParentConvention );

            Collection<FXRateBasis> effectiveRateBasisParent = registeredParentConvention.getEffectiveFXRateBasis ();
            log ( "Displaying Parent FX Rate Basis" );
            for ( FXRateBasis basis : effectiveRateBasisParent )
            {
                log ( "FXRateBasis: " + basis + " - Owner FXRateConvention: " + basis.getFXRateConvention ().getShortName () );
            }
            assertTrue ( effectiveRateBasisParent.size () == 2 );
            FXRateBasis rb5 = registeredParentConvention.getFXRateBasis ( "JPY/EUR" );
            assertEquals ( rb5.getOwner (), registeredParentConvention );

            this.uow.commit ();

            parentConvention = ( FXRateConvention ) IdcUtilC.refreshObject ( parentConvention );
            childConvention = ( FXRateConvention ) IdcUtilC.refreshObject ( childConvention );
            grandChildConvention = ( FXRateConvention ) IdcUtilC.refreshObject ( grandChildConvention );

            // check the hierarchical lookup again.
            effectiveRateBasisGrandChild = grandChildConvention.getEffectiveFXRateBasis ();
            effectiveRateBasisChild = childConvention.getEffectiveFXRateBasis ();
            effectiveRateBasisParent = parentConvention.getEffectiveFXRateBasis ();
            assertTrue ( effectiveRateBasisGrandChild.size () == 4 );
            FXRateBasis rb6 = grandChildConvention.getFXRateBasis ( "JPY/EUR" );
            assertEquals ( rb6.getOwner (), grandChildConvention );
            FXRateBasis rb7 = grandChildConvention.getFXRateBasis ( "AUD/USD" );
            assertEquals ( rb7.getOwner (), childConvention );
            assertTrue ( effectiveRateBasisChild.size () == 3 );
            FXRateBasis rb8 = childConvention.getFXRateBasis ( "JPY/EUR" );
            assertEquals ( rb8.getOwner (), childConvention );
            FXRateBasis rb9 = grandChildConvention.getFXRateBasis ( "USD/EUR" );
            assertEquals ( rb9.getOwner (), parentConvention );
            assertTrue ( effectiveRateBasisParent.size () == 2 );
            FXRateBasis rb10 = parentConvention.getFXRateBasis ( "JPY/EUR" );
            assertEquals ( rb10.getOwner (), parentConvention );

        }
        catch ( Exception exc )
        {
            exc.printStackTrace ();
            fail ( "testEffectiveFXRateBasis" );
        }
        finally
        {
            this.uow = getPersistenceSession ().acquireUnitOfWork ();
            this.uow.removeAllReadOnlyClasses ();
            if ( grandChildConvention != null )
            {
                FXRateConvention registeredGrandChildConvention = ( FXRateConvention ) this.uow.registerObject ( grandChildConvention );
                this.uow.deleteObject ( registeredGrandChildConvention );
                log ( "Deleting object " + grandChildFXConventionName );
            }

            if ( childConvention != null )
            {
                FXRateConvention registeredChildConvention = ( FXRateConvention ) this.uow.registerObject ( childConvention );
                this.uow.deleteObject ( registeredChildConvention );
                log ( "Deleting object " + childFXConventionName );
            }

            if ( parentConvention != null )
            {
                FXRateConvention registeredParentConvention = ( FXRateConvention ) this.uow.registerObject ( parentConvention );
                this.uow.deleteObject ( registeredParentConvention );
                log ( "Deleting object " + parentFXConventionName );
            }

            log ( "Committing transaction" );
            this.uow.commit ();
            log ( "End EffectiveFXRateBasis Test" );
        }
    }

    protected void tearDown ( )
    {
        super.tearDown ();
        deleteTestFXRateConventions ();
    }

    private void deleteTestFXRateConventions ( )
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder ();
            Expression expr = eb.get ( Entity.Status ).equal ( ENTITY_STATUS_TEST );
            Collection<FXRateConvention> testConventions = getPersistenceSession ().readAllObjects ( FXRateConventionC.class, expr );
            if ( testConventions != null && !testConventions.isEmpty () )
            {
                this.uow = this.getPersistenceSession ().acquireUnitOfWork ();
                this.uow.removeAllReadOnlyClasses ();
                for ( FXRateConvention conv : testConventions )
                {
                    this.uow.deleteObject ( conv );
                }
                uow.commit ();
            }
        }
        catch ( Exception e )
        {
            log.error ( "deleteTestFXRateConventions", e );
        }
    }
}