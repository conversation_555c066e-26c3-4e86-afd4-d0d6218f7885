package com.integral.finance.fx.test;

import com.integral.finance.businessCalendar.BusinessCalendar;
import com.integral.finance.config.FinanceMBean;
import com.integral.finance.currency.*;
import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.dateGeneration.HolidayCalendarC;
import com.integral.finance.fx.*;
import com.integral.finance.instrument.InstrumentClassificationC;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateConstants;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;

public class FXRateBasisPTestC
        extends PTestCaseC
{
    static String name = "FXRateBasis Test";
    private String BASE_CCY_SHORT_NAME = "EUR";
    private String VARIABLE_CCY_SHORT_NAME = "RUR";
    private String FWD_CCY_SHORT_NAME = "YYY";

    public FXRateBasisPTestC ( String name )
    {
        super ( name );
    }

    public void testFixingDate ( )
    {
        FXRateBasis rateBasis = null;
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
            uow.removeReadOnlyClass ( FXRateBasisC.class );
            ExpressionBuilder builder = new ExpressionBuilder ();
            Expression expr = builder.get ( "shortName" ).equal ( "STDQOTCNV" );

            FXRateConvention convention = ( FXRateConvention ) uow.readObject ( com.integral.finance.fx.FXRateConventionC.class, expr );
            rateBasis = convention.getFXRateBasis ( "EUR/USD" );

            IdcDate valueDate = DateTimeFactory.newDate ();

            for ( int i = 0; i < 400; i++ )
            {
                valueDate = getBusinessDate ( rateBasis, valueDate.addDays ( 1 ) );
                IdcDate fixingDate = rateBasis.getFixingDate ( valueDate );
                assertTrue ( rateBasis.isFixingBusinessDate ( fixingDate ) );
                IdcDate valueDate1 = rateBasis.getValueDate ( fixingDate, Tenor.SPOT_TENOR );

                if ( valueDate.compare ( valueDate1 ) != 0 )
                {
                    log.warn ( "Value Date - " + valueDate );
                    log.warn ( "Fixing Date for above value date - " + fixingDate );
                    log.warn ( "Spot value Date calculated from the fixing date - " + valueDate1 );
                    log.warn ( "Spot value date of fixing date is not same as Value date " + valueDate.compare ( valueDate1 ) );
                    log.warn ( "*****************************************************************************" );
                }
            }

            // now modify the fxRateBasis and commit
            FXRateBasis regFXRateBasis = ( FXRateBasis ) uow.registerObject ( rateBasis );
            regFXRateBasis.setDisplayKey ( String.valueOf ( System.currentTimeMillis () ) );
            uow.commit ();

            // now calculate the fixing date again.
            valueDate = DateTimeFactory.newDate ();
            for ( int i = 0; i < 400; i++ )
            {
                valueDate = getBusinessDate ( rateBasis, valueDate.addDays ( 1 ) );
                IdcDate fixingDate = rateBasis.getFixingDate ( valueDate );
                assertTrue ( rateBasis.isFixingBusinessDate ( fixingDate ) );
                IdcDate valueDate1 = rateBasis.getValueDate ( fixingDate, Tenor.SPOT_TENOR );

                if ( valueDate.compare ( valueDate1 ) != 0 )
                {
                    log.warn ( "Value Date - " + valueDate );
                    log.warn ( "Fixing Date for above value date - " + fixingDate );
                    log.warn ( "Spot value Date calculated from the fixing date - " + valueDate1 );
                    log.warn ( "Spot value date of fixing date is not same as Value date " + valueDate.compare ( valueDate1 ) );
                    log.warn ( "*****************************************************************************" );
                }
            }
        }
        catch ( Exception e )
        {
            fail ( "testFixingDate exception", e );
        }
        finally
        {
            if ( rateBasis != null )
            {
                IdcUtilC.refreshObject ( rateBasis );
                (( FXRateBasisC ) rateBasis).resetTransients ();
            }
        }
    }

    private IdcDate getBusinessDate ( FXRateBasis rateBasis, IdcDate date )
    {
        IdcDate finalDate = null;
        Collection<BusinessCalendar> businessCals = new ArrayList<BusinessCalendar> ();

        if ( null != rateBasis.getFixingBusinessCalendar () )
        {
            businessCals.add ( rateBasis.getFixingBusinessCalendar () );
        }
        else
        {
            businessCals.addAll ( rateBasis.getBusinessCalendars () );
        }

        for ( BusinessCalendar cal : businessCals )
        {
            IdcDate bDate = cal.businessDateFor ( date );

            if ( null == finalDate || bDate.isLaterThan ( finalDate ) )
            {
                finalDate = bDate;
            }
        }

        /*
         * Iterate through all business calendars and find the soonest
         * common business date.
         */
        IdcDate fDate = null;
        while ( finalDate != fDate )
        {
            for ( BusinessCalendar cal : businessCals )
            {
                if ( cal.isBusinessDate ( finalDate ) )
                {
                    fDate = finalDate;        // termination
                }
                else
                {
                    finalDate = cal.getNextBusinessDate ( finalDate );
                    fDate = null;
                    break;        // check all calendars over again
                }
            }
        }

        return finalDate;
    }

    public void testFXRateBasisDeliverable ( )
    {
        FXRateBasis NDFFXRateBasis = FXFactory.newFXRateBasis ();

        // Inserting the new currency to DB.
        try
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            NDFFXRateBasis = ( FXRateBasis ) this.uow.registerObject ( NDFFXRateBasis );
            NDFFXRateBasis.setNDF ( true );
            NDFFXRateBasis.setFixingBusinessCalendar ( getFixingCalendar () );
            NDFFXRateBasis.setBaseCurrency ( getCurrency ( BASE_CCY_SHORT_NAME ) );
            NDFFXRateBasis.setVariableCurrency ( getCurrency ( VARIABLE_CCY_SHORT_NAME ) );
            NDFFXRateBasis.setStatus ( 'T' );
            uow.commit ();
        }
        catch ( Exception exc )
        {
            log.error ( "CurrencyPTestC.testCurrencyDeliverable() Exception 1 - " + exc );
        }

        // Reloading the currency from DB.
        try
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            uow.refreshObject ( NDFFXRateBasis );
            assertEquals ( NDFFXRateBasis.isNDF (), true );
            assertEquals ( NDFFXRateBasis.getFixingBusinessCalendar ().getNoLagHolidayCalendar1 ().getShortName (), "NYC" );
            assertEquals ( NDFFXRateBasis.getFixingBusinessCalendar ().getNoLagHolidayCalendar2 ().getShortName (), "IST" );
            assertEquals ( NDFFXRateBasis.getFixingBusinessCalendar ().getNoLagHolidayCalendar3 ().getShortName (), "HKG" );
            assertEquals ( NDFFXRateBasis.getFixingBusinessCalendar ().getNoLagHolidayCalendar4 ().getShortName (), "SHG" );

            assertEquals ( NDFFXRateBasis.getFixingBusinessCalendar ().getHolidayCalendars ().size (), 4 );
            assertEquals ( NDFFXRateBasis.getFixingBusinessCalendar ().getLag (), 2 );
            uow.release ();
        }
        catch ( Exception exc )
        {
            log.error ( "CurrencyPTestC.testCurrencyDeliverable() Exception 2 - " + exc );
        }

        long objectId = NDFFXRateBasis.getObjectID ();

        // Deleteing the entry.
        try
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.deleteObject ( NDFFXRateBasis );
            uow.commit ();
            uow.release ();
        }
        catch ( Exception exc )
        {
            log.error ( "CurrencyPTestC.testCurrencyDeliverable() Exception 3 - " + exc );
        }

        // Retrieve the object again to make sure object is deleted.
        try
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
            ExpressionBuilder builder = new ExpressionBuilder ();
            Expression expr = builder.get ( "objectID" ).equal ( objectId );
            FXRateBasis ccy = ( FXRateBasis ) uow.readObject ( com.integral.finance.currency.Currency.class, expr );
            assertEquals ( ccy, null );
        }
        catch ( Exception exc )
        {
            log.error ( "CurrencyPTestC.testCurrencyDeliverable() Exception 4 - " + exc );
        }
    }

    protected Currency getCurrency ( String shortName )
    {
        return ( Currency ) this.getNamedEntity ( Currency.class, shortName );
    }

    protected NamedEntity getNamedEntity ( Class aClass, String shortName )
    {
        ExpressionBuilder eb = new ExpressionBuilder ();
        Expression expr = eb.get ( "shortName" ).equal ( shortName );
        Object obj = null;
        if ( this.uow == null )
        {
            obj = this.getPersistenceSession ().readObject ( aClass, expr );
        }
        else
        {
            obj = this.uow.readObject ( aClass, expr );
        }
        return ( NamedEntity ) obj;
    }

    private FXBusinessCalendar getFixingCalendar ( )
    {
        FXBusinessCalendar fixingBusinessCalendar = FXFactory.newFXBusinessCalendar ();

        fixingBusinessCalendar.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
        fixingBusinessCalendar.setLag ( 2 );
        fixingBusinessCalendar.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );
        fixingBusinessCalendar.setNoLagHolidayCalendar2 ( getHolidayCalendar ( "IST" ) );
        fixingBusinessCalendar.setNoLagHolidayCalendar3 ( getHolidayCalendar ( "HKG" ) );
        fixingBusinessCalendar.setNoLagHolidayCalendar4 ( getHolidayCalendar ( "SHG" ) );

        Collection holidayCalc = new ArrayList ();
        holidayCalc.add ( getHolidayCalendar ( "NYC" ) );
        holidayCalc.add ( getHolidayCalendar ( "IST" ) );
        holidayCalc.add ( getHolidayCalendar ( "TOK" ) );
        holidayCalc.add ( getHolidayCalendar ( "SHG" ) );
        fixingBusinessCalendar.setHolidayCalendars ( holidayCalc );

        return fixingBusinessCalendar;
    }

    private HolidayCalendar getHolidayCalendar ( String shortName )
    {
        if ( uow == null )
        {
            uow = this.getPersistenceSession ().acquireUnitOfWork ();
        }
        ExpressionBuilder eb1 = new ExpressionBuilder ();
        Expression expr1 = eb1.get ( "shortName" ).equal ( shortName );
        HolidayCalendar holdiayCalc = ( HolidayCalendar ) uow.readObject ( HolidayCalendar.class, expr1 );
        return holdiayCalc;
    }

    public void testFXRateBasisName ( )
    {
        FXRateBasis testFXRateBasis = null;
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance ().getSessionContext ( "Integral" );
            IdcSessionManager.getInstance ().setSessionContext ( sessContext );
            Session session = PersistenceFactory.newSession ();
            UnitOfWork uow = session.acquireUnitOfWork ();
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            uow.removeReadOnlyClass ( FXRateBasisC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            testFXRateBasis = fxConv.getFXRateBasis ( "EUR/USD" );
            FXRateBasis registeredFXRateBasis = ( FXRateBasis ) uow.registerObject ( testFXRateBasis );
            String name = "TestName" + System.currentTimeMillis ();
            testFXRateBasis.setName ( null );
            String existingName = testFXRateBasis.getName ();
            assertEquals ( "Should be same as currency pair name", testFXRateBasis.getCurrencyPairString (), testFXRateBasis.getName () );
            registeredFXRateBasis.setName ( name );
            uow.commit ();

            testFXRateBasis.setName ( null );
            // refresh the trade and retrieve the maker request.
            testFXRateBasis = ( FXRateBasis ) session.refreshObject ( testFXRateBasis );
            log ( "testFXRateBasis=" + testFXRateBasis + ",testFXRateBasis.name=" + testFXRateBasis.getName () );
            assertEquals ( "name should not be null.", testFXRateBasis.getName (), name );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ();
        }
        finally
        {
            try
            {
                UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
                uow.removeReadOnlyClass ( FXRateBasisC.class );
                FXRateBasis registeredFXRateBasis = ( FXRateBasis ) uow.registerObject ( testFXRateBasis );
                registeredFXRateBasis.setName ( testFXRateBasis.getCurrencyPairString () );
                uow.commit ();
            }
            catch ( Exception e )
            {
                fail ( "testFXRateBasisName", e );
            }
        }
    }

    public void testAllowInverseFlag ( )
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance ().getSessionContext ( "Integral" );
            IdcSessionManager.getInstance ().setSessionContext ( sessContext );
            Session session = PersistenceFactory.newSession ();
            org.eclipse.persistence.sessions.UnitOfWork uow = session.acquireUnitOfWork ();
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            uow.removeReadOnlyClass ( FXRateBasisC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            FXRateBasis testFXRateBasis = fxConv.getFXRateBasis ( "EUR/USD" );
            FXRateBasis registeredFXRateBasis = ( FXRateBasis ) uow.registerObject ( testFXRateBasis );
            boolean allowInverse = registeredFXRateBasis.isAllowInverse ();
            log ( "existing value of allow inverse=" + allowInverse );
            boolean newAllowInverse = !allowInverse;
            registeredFXRateBasis.setAllowInverse ( newAllowInverse );
            uow.commit ();

            // refresh the fxrate basis and check allow inverse flag
            testFXRateBasis = ( FXRateBasis ) session.refreshObject ( testFXRateBasis );
            log ( "testFXRateBasis=" + testFXRateBasis + ",testFXRateBasis.allowInverse=" + testFXRateBasis.isAllowInverse () );
            assertEquals ( "allow inverse should be new value.", testFXRateBasis.isAllowInverse (), newAllowInverse );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ();
        }
    }

    public void testNZDValueDate ( )
    {
        FXRateBasis eurUsdRb = null;
        try
        {
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            FXRateBasis nzdUsdRb = fxConv.getFXRateBasis ( "NZD/USD" );
            eurUsdRb = fxConv.getFXRateBasis ( "EUR/USD" );
            assertNotNull ( nzdUsdRb );
            assertNotNull ( eurUsdRb );

            IdcDate tradeDate = DateTimeFactory.newDate ();
            IdcDate nzdTradeDate = nzdUsdRb.getNZDTradeDate ( tradeDate );
            IdcDate nzdValDate1 = nzdUsdRb.getValueDate ( tradeDate, Tenor.SPOT_TENOR );
            IdcDate nzdValDate2 = nzdUsdRb.getValueDate ( tradeDate, Tenor.SPOT_TENOR, true );
            IdcDate eurValDate1 = eurUsdRb.getValueDate ( tradeDate, Tenor.SPOT_TENOR );
            IdcDate eurValDate2 = eurUsdRb.getValueDate ( tradeDate, Tenor.SPOT_TENOR, true );
            log ( "tradeDate=" + tradeDate.getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            log ( "nzdTradeDate=" + nzdTradeDate.getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            log ( "nzdValDate1=" + nzdValDate1.getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            log ( "nzdValDate2=" + nzdValDate2.getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            log ( "eurValDate1=" + eurValDate1.getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            log ( "eurValDate2=" + eurValDate2.getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            if ( nzdTradeDate.isSameAs ( tradeDate ) )
            {
                assertEquals ( "EUR/USD value date should be the same", nzdValDate1, nzdValDate2 );
            }
            else
            {
                IdcDate d1 = nzdUsdRb.getValueDate ( nzdTradeDate, Tenor.SPOT_TENOR, true );
                IdcDate d2 = nzdUsdRb.getValueDate ( nzdTradeDate, Tenor.SPOT_TENOR, false );
                assertEquals ( "for nzd trade date, value dates on both API should be the same", d1, d2 );
            }

            // make sure that the ignore current time does not make any difference to the EUR/USD
            assertEquals ( "EUR/USD value date should be the same", eurValDate1, eurValDate2 );
        }
        catch ( Exception e )
        {
            fail ( "testNZDValueDate", e );
        }
        finally
        {
            if ( eurUsdRb != null )
            {
                IdcUtilC.refreshObject ( eurUsdRb );
                (( FXRateBasisC ) eurUsdRb).resetTransients ();
            }
        }
    }

    public void testNoLagCalendarAndRegularCalendarSame ( )
    {
        FXRateBasis rb = null;
        try
        {
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            rb = fxConv.getFXRateBasis ( "EUR", "USD" );

            // set a new fx business calendar with same calendar as regular calendar as well as no lag calendar.
            FXBusinessCalendar fxBusCal = FXFactory.newFXBusinessCalendar ();

            fxBusCal.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fxBusCal.setLag ( 2 );
            fxBusCal.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );

            Collection holidayCalc = new ArrayList ();
            holidayCalc.add ( getHolidayCalendar ( "NYC" ) );
            fxBusCal.setHolidayCalendars ( holidayCalc );

            rb.setFXBusinessCalendar ( fxBusCal );
            IdcDate trdDate = DateTimeFactory.newDate ();
            IdcDate valDate = rb.getValueDate ( trdDate, Tenor.SPOT_TENOR );
            log ( "TradeDate=" + trdDate + ",valDate=" + valDate );
            assertTrue ( valDate.asDays () - trdDate.asDays () >= 2 );
            (( FXRateBasisC ) rb).resetTransients ();

            // set a new fx business calendar with more no lag calendars.
            FXBusinessCalendar fxBusCal1 = FXFactory.newFXBusinessCalendar ();

            fxBusCal1.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fxBusCal1.setLag ( 2 );
            fxBusCal1.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );
            fxBusCal1.setNoLagHolidayCalendar2 ( getHolidayCalendar ( "TAR" ) );

            Collection holidayCalc1 = new ArrayList ();
            holidayCalc1.add ( getHolidayCalendar ( "NYC" ) );
            fxBusCal1.setHolidayCalendars ( holidayCalc1 );

            rb.setFXBusinessCalendar ( fxBusCal1 );
            IdcDate trdDate1 = DateTimeFactory.newDate ();
            IdcDate valDate1 = rb.getValueDate ( trdDate1, Tenor.SPOT_TENOR );
            log ( "TradeDate1=" + trdDate1 + ",valDate1=" + valDate1 );
            assertTrue ( valDate1.asDays () - trdDate1.asDays () >= 2 );
            (( FXRateBasisC ) rb).resetTransients ();

            // set a new fx business calendar without any no lag holiday calendars
            FXBusinessCalendar fxBusCal2 = FXFactory.newFXBusinessCalendar ();

            fxBusCal2.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fxBusCal2.setLag ( 2 );

            Collection holidayCalc2 = new ArrayList ();
            holidayCalc2.add ( getHolidayCalendar ( "NYC" ) );
            fxBusCal2.setHolidayCalendars ( holidayCalc2 );

            rb.setFXBusinessCalendar ( fxBusCal2 );
            IdcDate trdDate2 = DateTimeFactory.newDate ();
            IdcDate valDate2 = rb.getValueDate ( trdDate2, Tenor.SPOT_TENOR );
            log ( "TradeDate2=" + trdDate2 + ",valDate2=" + valDate2 );
            assertTrue ( valDate2.asDays () - trdDate2.asDays () >= 2 );

            (( FXRateBasisC ) rb).resetTransients ();

            // set a new fx business calendar without any no lag holiday calendars
            FXBusinessCalendar fxBusCal3 = FXFactory.newFXBusinessCalendar ();

            fxBusCal3.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fxBusCal3.setLag ( 2 );
            fxBusCal3.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );
            fxBusCal3.setNoLagHolidayCalendar2 ( getHolidayCalendar ( "TAR" ) );

            Collection holidayCalc3 = new ArrayList ();
            holidayCalc3.add ( getHolidayCalendar ( "NYC" ) );
            holidayCalc3.add ( getHolidayCalendar ( "TAR" ) );
            fxBusCal3.setHolidayCalendars ( holidayCalc3 );

            rb.setFXBusinessCalendar ( fxBusCal3 );
            IdcDate trdDate3 = DateTimeFactory.newDate ();
            IdcDate valDate3 = rb.getValueDate ( trdDate3, Tenor.SPOT_TENOR );
            log ( "TradeDate3=" + trdDate3 + ",valDate3=" + valDate3 );
            assertTrue ( valDate3.asDays () - trdDate3.asDays () >= 2 );
        }
        catch ( Exception e )
        {
            fail ( "testNoLagCalendarAndRegularCalendarSame", e );
        }
        finally
        {
            if ( rb != null )
            {
                IdcUtilC.refreshObject ( rb );
                (( FXRateBasisC ) rb).resetTransients ();
            }
        }
    }

    public void testFixingBusinessDateCheck ( )
    {
        FXRateBasis rb = null;
        try
        {
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            rb = fxConv.getFXRateBasis ( "EUR", "USD" );

            // set both the fxbusiness calendar and fixing business calendar to null.
            rb.setFXBusinessCalendar ( null );
            rb.setFixingBusinessCalendar ( null );
            IdcDate date = DateTimeFactory.newDate ();
            for ( int i = 0; i < 4000; i++ )
            {
                date = date.addDays ( 1 );
                boolean businessDate = rb.isBusinessDate ( date );
                boolean fixingBusinessDate = rb.isFixingBusinessDate ( date );
                log ( "date=" + date + ",businessDate=" + businessDate + ",fixingBusinessDate=" + fixingBusinessDate );
                assertTrue ( businessDate == fixingBusinessDate );

                // calculate spot tenor dates for settlement as well as fixing.
                IdcDate spotValDate = rb.getValueDate ( date, Tenor.SPOT_TENOR );
                IdcDate fixingDate = rb.getFixingDate ( date, Tenor.SPOT_TENOR );
                log ( "spotValDate=" + spotValDate + ",fixingDate=" + fixingDate );
                assertEquals ( spotValDate, fixingDate );
            }

            // set a new fx business calendar
            FXBusinessCalendar fxBusCal = FXFactory.newFXBusinessCalendar ();

            fxBusCal.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fxBusCal.setLag ( 2 );
            fxBusCal.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );

            Collection holidayCalc = new ArrayList ();
            holidayCalc.add ( getHolidayCalendar ( "NYC" ) );
            holidayCalc.add ( getHolidayCalendar ( "TAR" ) );
            fxBusCal.setHolidayCalendars ( holidayCalc );

            rb.setFXBusinessCalendar ( fxBusCal );
            rb.setFixingBusinessCalendar ( null );

            date = DateTimeFactory.newDate ();
            for ( int i = 0; i < 4000; i++ )
            {
                date = date.addDays ( 1 );
                boolean businessDate = rb.isBusinessDate ( date );
                boolean fixingBusinessDate = rb.isFixingBusinessDate ( date );
                log ( "date=" + date + ",businessDate=" + businessDate + ",fixingBusinessDate=" + fixingBusinessDate );
                assertTrue ( businessDate == fixingBusinessDate );

                // calculate spot tenor dates for settlement as well as fixing.
                IdcDate spotValDate = rb.getValueDate ( date, Tenor.SPOT_TENOR );
                IdcDate fixingDate = rb.getFixingDate ( date, Tenor.SPOT_TENOR );
                log ( "spotValDate=" + spotValDate + ",fixingDate=" + fixingDate );
                assertEquals ( spotValDate, fixingDate );
            }

            // set a new fixing business calendar with the same set of calendars.
            FXBusinessCalendar fixingBusCal = FXFactory.newFXBusinessCalendar ();

            fixingBusCal.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fixingBusCal.setLag ( 2 );
            fixingBusCal.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );

            Collection fixingCalcs = new ArrayList ();
            fixingCalcs.add ( getHolidayCalendar ( "NYC" ) );
            fixingCalcs.add ( getHolidayCalendar ( "TAR" ) );
            fixingBusCal.setHolidayCalendars ( holidayCalc );

            rb.setFixingBusinessCalendar ( fixingBusCal );


            date = DateTimeFactory.newDate ();
            for ( int i = 0; i < 4000; i++ )
            {
                date = date.addDays ( 1 );
                boolean businessDate = rb.isBusinessDate ( date );
                boolean fixingBusinessDate = rb.isFixingBusinessDate ( date );
                log ( "date=" + date + ",businessDate=" + businessDate + ",fixingBusinessDate=" + fixingBusinessDate );
                assertTrue ( businessDate == fixingBusinessDate );

                // calculate spot tenor dates for settlement as well as fixing.
                IdcDate spotValDate = rb.getValueDate ( date, Tenor.SPOT_TENOR );
                IdcDate fixingDate = rb.getFixingDate ( date, Tenor.SPOT_TENOR );
                log ( "spotValDate=" + spotValDate + ",fixingDate=" + fixingDate );
                assertEquals ( spotValDate, fixingDate );
            }
        }
        catch ( Exception e )
        {
            fail ( "testFixingBusinessDateCheck", e );
        }
        finally
        {
            if ( rb != null )
            {
                IdcUtilC.refreshObject ( rb );
                (( FXRateBasisC ) rb).resetTransients ();
            }
        }
    }

    public void testFixingDateWithNoHolidayCalendarsInFixingBusinessCalendar ( )
    {
        FXRateBasis rb = null;
        try
        {
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            rb = fxConv.getFXRateBasis ( "EUR", "USD" );
            (( FXRateBasisC ) rb).resetTransients ();

            // set a new fx business calendar with same calendar as regular calendar as well as no lag calendar.
            FXBusinessCalendar fixingBusCal = FXFactory.newFXBusinessCalendar ();

            fixingBusCal.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fixingBusCal.setLag ( 2 );

            rb.setFixingBusinessCalendar ( fixingBusCal );
            IdcDate trdDate = DateTimeFactory.newDate ();
            IdcDate valDate = rb.getValueDate ( trdDate, Tenor.SPOT_TENOR );
            IdcDate fixingDateFromTradeDate = rb.getFixingDate ( trdDate, Tenor.SPOT_TENOR );
            log ( "TradeDate=" + trdDate + ",valDate=" + valDate + ",fixingDateFromTradeDate=" + fixingDateFromTradeDate );
            assertTrue ( valDate.asDays () - trdDate.asDays () >= 2 );
            assertTrue ( fixingDateFromTradeDate.asDays () - trdDate.asDays () >= 2 );
            assertEquals ( valDate, fixingDateFromTradeDate );

            // now calculate the fixing date for the value date.
            IdcDate fixingDateFromValueDate = rb.getFixingDate ( valDate );
            log ( "fixingDateFromValueDate=" + fixingDateFromValueDate + ",valDate=" + valDate );
            assertTrue ( valDate.asDays () - fixingDateFromValueDate.asDays () >= 2 );
            log ( "isTradeDateBusinessDate?" + rb.isBusinessDate ( trdDate ) );
            if ( rb.isBusinessDate ( trdDate ) )
            {
                assertEquals ( fixingDateFromValueDate, trdDate );
            }
            else
            {
                assertTrue ( fixingDateFromValueDate.isEarlierThanOrEqualTo ( trdDate ) );
            }

            (( FXRateBasisC ) rb).resetTransients ();
        }
        catch ( Exception e )
        {
            fail ( "testFixingDateWithNoHolidayCalendarsInFixingBusinessCalendar", e );
        }
        finally
        {
            if ( rb != null )
            {
                IdcUtilC.refreshObject ( rb );
                (( FXRateBasisC ) rb).resetTransients ();
            }
        }
    }


    public void testFixingDateWithNoFixingBusinessCalendar ( )
    {
        FXRateBasis rb = null;
        try
        {
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            rb = fxConv.getFXRateBasis ( "EUR", "USD" );
            (( FXRateBasisC ) rb).resetTransients ();

            rb.setFixingBusinessCalendar ( null );
            IdcDate trdDate = DateTimeFactory.newDate ();
            IdcDate valDate = rb.getValueDate ( trdDate, Tenor.SPOT_TENOR );
            IdcDate fixingDateFromTradeDate = rb.getFixingDate ( trdDate, Tenor.SPOT_TENOR );
            log ( "TradeDate=" + trdDate + ",valDate=" + valDate + ",fixingDateFromTradeDate=" + fixingDateFromTradeDate );
            assertTrue ( valDate.asDays () - trdDate.asDays () >= 2 );
            assertTrue ( fixingDateFromTradeDate.asDays () - trdDate.asDays () >= 2 );
            assertEquals ( valDate, fixingDateFromTradeDate );

            // now calculate the fixing date for the value date.
            IdcDate fixingDateFromValueDate = rb.getFixingDate ( valDate );
            log ( "fixingDateFromValueDate=" + fixingDateFromValueDate + ",valDate=" + valDate );
            assertTrue ( valDate.asDays () - fixingDateFromValueDate.asDays () >= 2 );
            log ( "isTradeDateBusinessDate?" + rb.isBusinessDate ( trdDate ) );
            if ( rb.isBusinessDate ( trdDate ) )
            {
                assertEquals ( fixingDateFromValueDate, trdDate );
            }
            else
            {
                assertTrue ( fixingDateFromValueDate.isEarlierThanOrEqualTo ( trdDate ) );
            }

            (( FXRateBasisC ) rb).resetTransients ();
        }
        catch ( Exception e )
        {
            fail ( "testFixingDateWithNoFixingBusinessCalendar", e );
        }
        finally
        {
            if ( rb != null )
            {
                IdcUtilC.refreshObject ( rb );
                (( FXRateBasisC ) rb).resetTransients ();
            }
        }
    }

    public void testFixingDateWithNoFixingBusinessCalendarAndNoFXBusinessCalendar ( )
    {
        FXRateBasis rb = null;
        try
        {
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            rb = fxConv.getFXRateBasis ( "EUR", "USD" );
            (( FXRateBasisC ) rb).resetTransients ();

            rb.setFixingBusinessCalendar ( null );
            rb.setFXBusinessCalendar ( null );
            IdcDate trdDate = DateTimeFactory.newDate ();
            IdcDate valDate = rb.getValueDate ( trdDate, Tenor.SPOT_TENOR );
            IdcDate fixingDateFromTradeDate = rb.getFixingDate ( trdDate, Tenor.SPOT_TENOR );
            log ( "TradeDate=" + trdDate + ",valDate=" + valDate + ",fixingDateFromTradeDate=" + fixingDateFromTradeDate );
            assertTrue ( valDate.asDays () - trdDate.asDays () >= 2 );
            assertTrue ( fixingDateFromTradeDate.asDays () - trdDate.asDays () >= 2 );
            assertEquals ( valDate, fixingDateFromTradeDate );

            // now calculate the fixing date for the value date.
            IdcDate fixingDateFromValueDate = rb.getFixingDate ( valDate );
            log ( "fixingDateFromValueDate=" + fixingDateFromValueDate + ",valDate=" + valDate );
            assertTrue ( valDate.asDays () - fixingDateFromValueDate.asDays () >= 2 );
            log ( "isTradeDateBusinessDate?" + rb.isBusinessDate ( trdDate ) );
            if ( rb.isBusinessDate ( trdDate ) )
            {
                assertEquals ( fixingDateFromValueDate, trdDate );
            }
            else
            {
                assertTrue ( fixingDateFromValueDate.isEarlierThanOrEqualTo ( trdDate ) );
            }

            (( FXRateBasisC ) rb).resetTransients ();
        }
        catch ( Exception e )
        {
            fail ( "testFixingDateWithNoFixingBusinessCalendarAndNoFXBusinessCalendar", e );
        }
        finally
        {
            if ( rb != null )
            {
                IdcUtilC.refreshObject ( rb );
                (( FXRateBasisC ) rb).resetTransients ();
            }
        }
    }

    /**
     * In this test case, have a fixing lag greater than settlement lag while keeping the holiday calendars same.
     * The verification point is that fixing settlement dates on average later than regular settlement dates.
     */
    public void testMismatchedFixingAndSettlementLags ( )
    {
        FXRateBasis rb = null;
        try
        {
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            rb = fxConv.getFXRateBasis ( "EUR", "USD" );
            (( FXRateBasisC ) rb).resetTransients ();
            rb.resetTradeDateCache ();

            // set a new fx business calendar
            FXBusinessCalendar fxBusCal = FXFactory.newFXBusinessCalendar ();

            fxBusCal.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fxBusCal.setLag ( 2 );
            fxBusCal.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );

            Collection holidayCalc = new ArrayList ();
            holidayCalc.add ( getHolidayCalendar ( "NYC" ) );
            holidayCalc.add ( getHolidayCalendar ( "TAR" ) );
            fxBusCal.setHolidayCalendars ( holidayCalc );

            rb.setFXBusinessCalendar ( fxBusCal );

            // set a new fixing business calendar with the same set of calendars with 3 as lag
            FXBusinessCalendar fixingBusCal = FXFactory.newFXBusinessCalendar ();

            fixingBusCal.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fixingBusCal.setLag ( 3 );
            fixingBusCal.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );

            Collection fixingCalcs = new ArrayList ();
            fixingCalcs.add ( getHolidayCalendar ( "NYC" ) );
            fixingCalcs.add ( getHolidayCalendar ( "TAR" ) );
            fixingBusCal.setHolidayCalendars ( fixingCalcs );

            rb.setFixingBusinessCalendar ( fixingBusCal );


            IdcDate date = DateTimeFactory.newDate ();
            int matchCount = 0;
            int mismatchCount = 0;
            for ( int i = 0; i < 4000; i++ )
            {
                date = date.addDays ( 1 );

                // calculate spot tenor dates for settlement as well as fixing.
                IdcDate spotValDate = rb.getValueDate ( date, Tenor.SPOT_TENOR );
                IdcDate fixingSpotDate = rb.getFixingDate ( date, Tenor.SPOT_TENOR );
                log ( "spotValDate=" + spotValDate + ",fixingSpotDate=" + fixingSpotDate );
                IdcDate valDate1W = rb.getValueDate ( date, new Tenor ( "1W" ) );
                IdcDate fixingDate1W = rb.getFixingDate ( date, new Tenor ( "1W" ) );
                log ( "valDate1W=" + valDate1W + ",fixingDate1W=" + fixingDate1W );
                assertTrue ( fixingSpotDate.isLaterThanOrEqualTo ( spotValDate ) );
                assertTrue ( fixingDate1W.isLaterThanOrEqualTo ( valDate1W ) );
                if ( spotValDate.isSameAs ( fixingSpotDate ) )
                {
                    matchCount++;
                }
                else
                {
                    mismatchCount++;
                }
                log ( "matchCount=" + matchCount + ",mismatchCount=" + mismatchCount );
            }

            // conservatively mismatch count will be at least 10 times that of match count.
            if ( matchCount != 0 )
            {
                assertTrue ( mismatchCount / matchCount > 10 );
            }
        }
        catch ( Exception e )
        {
            fail ( "testMismatchedFixingAndSettlementLags", e );
        }
        finally
        {
            if ( rb != null )
            {
                IdcUtilC.refreshObject ( rb );
                (( FXRateBasisC ) rb).resetTransients ();
            }
        }
    }

    /**
     * In this test case, have a fixing lag greater than settlement lag while keeping the holiday calendars same.
     * The verification point is that fixing settlement dates on average later than regular settlement dates.
     */
    public void testMismatchedFixingAndSettlementHolidayCalendars ( )
    {
        FXRateBasis rb = null;
        HolidayCalendar syd = getHolidayCalendar ( "SYD" );

        // simulate a mismatch by adding Friday as a non-business day.
        Collection nonBusinessDaysOfWeek = new ArrayList ();
        nonBusinessDaysOfWeek.add ( new Integer ( IdcDate.SUNDAY ) );
        nonBusinessDaysOfWeek.add ( new Integer ( IdcDate.FRIDAY ) );
        nonBusinessDaysOfWeek.add ( new Integer ( IdcDate.SATURDAY ) );
        syd.setNonBusinessDaysOfWeek ( nonBusinessDaysOfWeek );

        try
        {
            FXRateConvention fxConv = ( FXRateConvention ) new ReadNamedEntityC ().execute ( FXRateConvention.class, "STDQOTCNV" );
            rb = fxConv.getFXRateBasis ( "EUR", "USD" );

            // set a new fx business calendar
            FXBusinessCalendar fxBusCal = FXFactory.newFXBusinessCalendar ();

            fxBusCal.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fxBusCal.setLag ( 2 );
            fxBusCal.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );

            Collection holidayCalc = new ArrayList ();
            holidayCalc.add ( getHolidayCalendar ( "NYC" ) );
            holidayCalc.add ( getHolidayCalendar ( "TAR" ) );
            fxBusCal.setHolidayCalendars ( holidayCalc );

            rb.setFXBusinessCalendar ( fxBusCal );

            // set a new fixing business calendar with the same set of calendars with 3 as lag
            FXBusinessCalendar fixingBusCal = FXFactory.newFXBusinessCalendar ();

            fixingBusCal.setLagType ( FXBusinessCalendar.LAG_TYPE_BUSINESS );
            fixingBusCal.setLag ( 2 );
            fixingBusCal.setNoLagHolidayCalendar1 ( getHolidayCalendar ( "NYC" ) );

            Collection fixingCalcs = new ArrayList ();
            fixingCalcs.add ( getHolidayCalendar ( "NYC" ) );
            fixingCalcs.add ( getHolidayCalendar ( "TAR" ) );
            fixingCalcs.add ( getHolidayCalendar ( "SYD" ) );
            fixingBusCal.setHolidayCalendars ( fixingCalcs );

            rb.setFixingBusinessCalendar ( fixingBusCal );


            IdcDate date = DateTimeFactory.newDate ();
            int matchCount = 0;
            int mismatchCount = 0;
            for ( int i = 0; i < 4000; i++ )
            {
                date = date.addDays ( 1 );

                // calculate spot tenor dates for settlement as well as fixing.
                IdcDate spotValDate = rb.getValueDate ( date, Tenor.SPOT_TENOR );
                IdcDate fixingSpotDate = rb.getFixingDate ( date, Tenor.SPOT_TENOR );
                log ( "tradeDate=" + date + ",spotValDate=" + spotValDate + ",fixingSpotDate=" + fixingSpotDate );
                IdcDate valDate1W = rb.getValueDate ( date, new Tenor ( "1W" ) );
                IdcDate fixingDate1W = rb.getFixingDate ( date, new Tenor ( "1W" ) );
                log ( "tradeDate=" + date + ",valDate1W=" + valDate1W + ",fixingDate1W=" + fixingDate1W );
                assertTrue ( fixingSpotDate.isLaterThanOrEqualTo ( spotValDate ) );
                assertTrue ( fixingDate1W.isLaterThanOrEqualTo ( valDate1W ) );
                if ( spotValDate.isSameAs ( fixingSpotDate ) )
                {
                    matchCount++;
                }
                else
                {
                    mismatchCount++;
                }
                log ( "matchCount=" + matchCount + ",mismatchCount=" + mismatchCount );
            }

            // there should be some match counts as well as mismatch counts.
            assertTrue ( mismatchCount > 0 );
            assertTrue ( matchCount > 0 );
        }
        catch ( Exception e )
        {
            fail ( "testMismatchedFixingAndSettlementHolidayCalendars", e );
        }
        finally
        {
            if ( syd != null )
            {
                IdcUtilC.refreshObject ( syd );
                (( HolidayCalendarC ) syd).resetTransients ();
            }
            if ( rb != null )
            {
                IdcUtilC.refreshObject ( rb );
                (( FXRateBasisC ) rb).resetTransients ();
            }
        }
    }

    public void testPipsFactorBasedForwardRateCalculation ( )
    {
        FXRateBasis rateBasis = null;
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
            uow.removeReadOnlyClass ( FXRateBasisC.class );
            ExpressionBuilder builder = new ExpressionBuilder ();
            Expression expr = builder.get ( "shortName" ).equal ( "STDQOTCNV" );

            FXRateConvention convention = ( FXRateConvention ) uow.readObject ( com.integral.finance.fx.FXRateConventionC.class, expr );
            rateBasis = convention.getFXRateBasis ( "EUR/USD" );

            rateBasis.setSpotPrecision ( 5 );
            rateBasis.setInverseSpotPrecision ( 5 );
            rateBasis.setForwardPointsPrecision ( 2 );
            rateBasis.setInverseForwardPointsPrecision ( 2 );
            rateBasis.setPipsFactor ( 10000 );
            rateBasis.setInversePipsFactor ( 10000 );
            int ratePrecision = rateBasis.getForwardRatePrecision ();
            log ( "ratePrecision=" + ratePrecision );
            assertEquals ( ratePrecision, 7 );

            int inverseRatePrecision = rateBasis.getInverseForwardRatePrecision ();
            log ( "inverseRatePrecision=" + inverseRatePrecision );
            assertTrue ( inverseRatePrecision == rateBasis.getInverseSpotPrecision () + rateBasis.getInverseForwardPointsPrecision () );

            WatchPropertyC.update ( FinanceMBean.FORWARD_RATE_PRECISION_CALCULATION_USING_PIPSFACTOR_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );

            int ratePrecision1 = rateBasis.getForwardRatePrecision ();
            log ( "ratePrecision1=" + ratePrecision1 );
            assertEquals ( ratePrecision1, 6 );

            int inverseRatePrecision1 = rateBasis.getInverseForwardRatePrecision ();
            log ( "inverseRatePrecision1=" + inverseRatePrecision1 );
            assertEquals ( inverseRatePrecision1, rateBasis.getLog10InversePipFactor () + rateBasis.getInverseForwardPointsPrecision () );
        }
        catch ( Exception e )
        {
            fail ( "testFixingDate exception", e );
        }
        finally
        {
            if ( rateBasis != null )
            {
                IdcUtilC.refreshObject ( rateBasis );
                (( FXRateBasisC ) rateBasis).resetTransients ();
            }
        }
    }

    public void testValueDateWithLagInBusinessDays ( )
    {
        try
        {
            FXRateConvention convention = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();
            FXRateBasis rateBasis = convention.getFXRateBasis ( "EUR/USD" );

            IdcDate tradeDate = DateTimeFactory.newDate ();

            for ( int i = 1; i < 400; i++ )
            {
                String tenorString = String.valueOf ( i ) + "d";
                Tenor tenor = new Tenor ( tenorString );
                IdcDate valueDateWithBusinessLag = rateBasis.getValueDateWithBusinessDaysLag ( tradeDate, tenor, true );
                IdcDate valueDate = rateBasis.getValueDate ( tradeDate, tenor, true );
                assertTrue ( rateBasis.isBusinessDate ( valueDateWithBusinessLag ) );
                assertTrue ( valueDate.isEarlierThanOrEqualTo ( valueDateWithBusinessLag ) );
            }

            for ( int i = 1; i < 40; i++ )
            {
                String tenorString = String.valueOf ( i ) + "w";
                Tenor tenor = new Tenor ( tenorString );
                IdcDate valueDateWithBusinessLag = rateBasis.getValueDateWithBusinessDaysLag ( tradeDate, tenor, true );
                IdcDate valueDate = rateBasis.getValueDate ( tradeDate, tenor, true );
                assertTrue ( rateBasis.isBusinessDate ( valueDateWithBusinessLag ) );
                assertTrue ( valueDate.isSameAs ( valueDateWithBusinessLag ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testValueDateWithLagInBusinessDays", e );
        }
    }

    public void testForwardCurrencyValueDateCalculation ( )
    {
        try
        {
            IdcDate tradeDate = DateTimeFactory.newDate ();
            Tenor tenor = new Tenor ( "9M" );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            FXRateConvention conv = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();
            FXRateBasis rb = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ().getFXRateBasis ( eur, usd );
            Currency testFwdCcy = createTestForwardCurrency ( FWD_CCY_SHORT_NAME, tenor, Currency.SettlementType.FORWARD, usd );
            assertNotNull ( testFwdCcy );
            FXRateBasisC rb1 = ( FXRateBasisC ) FXFactory.newFXRateBasis ();
            rb1.setFXRateConvention ( conv );
            rb1.setBaseCurrency ( eur );
            rb1.setVariableCurrency ( testFwdCcy );
            rb1.setFXBusinessCalendar ( rb.getFXBusinessCalendar () );
            rb1.setFXSettlementDateRuleSet ( rb.getFXSettlementDateRuleSet () );
            for ( Object bc : rb.getBusinessCalendars () )
            {
                if ( bc != null )
                {
                    rb1.getBusinessCalendars ().add ( bc );
                }
            }

            IdcDate eurUsdImValueDate = rb.getValueDate ( tradeDate, tenor );
            IdcDate rb1SpotDate = rb1.getSpotDate ( tradeDate );
            assertTrue ( eurUsdImValueDate.isSameAs ( rb1SpotDate ) );

            // check cache
            IdcDate rb1SpotDate1 = rb1.getSpotDate ( tradeDate );
            assertTrue ( eurUsdImValueDate.isSameAs ( rb1SpotDate1 ) );

            Collection<Tenor> stdTenors = RuntimeFactory.getServerRuntimeMBean ().getForwardCurrencySupportedTenors ();
            for ( Tenor stdTenor : stdTenors )
            {
                rb1.resetTradeDateCache ();
                rb1.resetTransients ();
                testFwdCcy.setSettlementTenor ( stdTenor );
                IdcDate eurUsdValDate = rb.getValueDate ( tradeDate, stdTenor );
                IdcDate fwdCcyPairValDate = rb1.getSpotDate ( tradeDate );
                assertTrue ( "stdTenor=" + stdTenor + ",eurUsdValDate=" + eurUsdValDate + ",fwdCcyPairValDate=" + fwdCcyPairValDate, eurUsdValDate.isSameAs ( fwdCcyPairValDate ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testForwardCurrencyValueDateCalculation", e );
        }
        finally
        {
            deleteTestCurrency ( FWD_CCY_SHORT_NAME );
        }
    }

    public void testForwardCurrencyFixingDateCalculation ( )
    {
        try
        {
            IdcDate tradeDate = DateTimeFactory.newDate ();
            Tenor tenor = new Tenor ( "13M" );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            FXRateConvention conv = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();
            FXRateBasis rb = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ().getFXRateBasis ( eur, usd );
            Currency testFwdCcy = createTestForwardCurrency ( FWD_CCY_SHORT_NAME, tenor, Currency.SettlementType.FORWARD, usd );
            assertNotNull ( testFwdCcy );
            FXRateBasisC rb1 = ( FXRateBasisC ) FXFactory.newFXRateBasis ();
            rb1.setFXRateConvention ( conv );
            rb1.setBaseCurrency ( eur );
            rb1.setVariableCurrency ( testFwdCcy );
            rb1.setFXBusinessCalendar ( rb.getFXBusinessCalendar () );
            for ( Object bc : rb.getBusinessCalendars () )
            {
                if ( bc != null )
                {
                    rb1.getBusinessCalendars ().add ( bc );
                }
            }
            rb1.setFXSettlementDateRuleSet ( rb.getFXSettlementDateRuleSet () );

            IdcDate eurUsdImValueDate = rb.getValueDate ( tradeDate, tenor );
            IdcDate rb1SpotDate = rb1.getSpotDate ( tradeDate );
            assertTrue ( eurUsdImValueDate.isSameAs ( rb1SpotDate ) );

            // check cache
            IdcDate rb1SpotDate1 = rb1.getSpotDate ( tradeDate );
            assertTrue ( eurUsdImValueDate.isSameAs ( rb1SpotDate1 ) );

            Collection<Tenor> stdTenors = RuntimeFactory.getServerRuntimeMBean ().getForwardCurrencySupportedTenors ();
            for ( Tenor stdTenor : stdTenors )
            {
                rb1.resetTradeDateCache ();
                rb1.resetTransients ();
                testFwdCcy.setSettlementTenor ( stdTenor );
                IdcDate eurUsdValDate = rb.getValueDate ( tradeDate, stdTenor );
                IdcDate eurUsdSpotDate = rb.getSpotDate ( tradeDate );
                if ( eurUsdValDate.isEarlierThan ( rb.getSpotDate ( eurUsdSpotDate ) ) )
                {
                    continue;
                }
                IdcDate eurUsdFixingDate = rb.getFixingDate ( eurUsdValDate );
                IdcDate fwdCcyPairFixingDate = rb1.getFixingDate ( eurUsdValDate );
                assertTrue ( "stdTenor=" + stdTenor + ",eurUsdValDate=" + eurUsdValDate + ",fwdCcyPairFixingDate=" + fwdCcyPairFixingDate, eurUsdFixingDate.isSameAs ( fwdCcyPairFixingDate ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testForwardCurrencyFixingDateCalculation", e );
        }
        finally
        {
            deleteTestCurrency ( FWD_CCY_SHORT_NAME );
        }
    }

    public void testDeliverableCheck ( )
    {
        FXRateConvention conv = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();
        Currency rub = CurrencyFactory.getCurrency ( "RUB" );
        FXRateBasis rb = null;
        try
        {
            assertNotNull ( conv );
            rb = conv.getFXRateBasis ( "USD", "RUB" );
            assertNotNull ( rb );
            rb.setNDF ( true );
            rub.setDeliverable ( false );
            assertTrue ( rb.isNonDeliverable () );

            rb.setNDF ( false );
            rub.setDeliverable ( false );
            assertTrue ( rb.isNonDeliverable () );

            rb.setNDF ( false );
            rub.setDeliverable ( true );
            assertFalse ( rb.isNonDeliverable () );

            WatchPropertyC.update ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + conv.getShortName (), "USD/RUB", ConfigurationProperty.DYNAMIC_SCOPE );
            rub.setDeliverable ( false );
            rb.setNDF ( false );
            assertFalse ( rb.isNonDeliverable () );

            rub.setDeliverable ( false );
            rb.setNDF ( true );
            assertTrue ( rb.isNonDeliverable () );

            rub.setDeliverable ( true );
            rb.setNDF ( false );
            assertFalse ( rb.isNonDeliverable () );

            rub.setDeliverable ( true );
            rb.setNDF ( true );
            assertTrue ( rb.isNonDeliverable () );
        }
        catch ( Exception e )
        {
            fail ( "testDeliverableCheck", e );
        }
        finally
        {
            WatchPropertyC.update ( FinanceMBean.FORCE_QUOTE_CONVENTION_CURRENCY_PAIR_DELIVERABLE_SETTING_PREFIX + conv.getShortName (), "", ConfigurationProperty.DYNAMIC_SCOPE );
            IdcUtilC.refreshObject ( rub );
            if ( rb != null )
            {
                IdcUtilC.refreshObject ( rb );
            }
        }
    }

    public void testWeekendDate ( )
    {
        try
        {
            FXRateConvention stdConv = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();
            FXRateBasis eurUsdRb = stdConv.getFXRateBasis ( "EUR/USD" );
            IdcDate date = DateTimeFactory.newDate ();
            for ( int i = 0; i < IdcDateConstants.DAYS_PER_YEAR; i++ )
            {
                boolean weekendDate = eurUsdRb.isWeekendDate ( date );
                if ( date.getDayOfWeek () == IdcDate.SUNDAY || date.getDayOfWeek () == IdcDate.SATURDAY )
                {
                    assertTrue ( weekendDate );
                }
                else
                {
                    assertFalse ( weekendDate );
                }
                date = date.addDays ( 1 );
            }

            FXRateBasis aedUsdRb = stdConv.getFXRateBasis ( "AED/USD" );
            date = DateTimeFactory.newDate ();
            for ( int i = 0; i < IdcDateConstants.DAYS_PER_YEAR; i++ )
            {
                boolean weekendDate = aedUsdRb.isWeekendDate ( date );
                if ( date.getDayOfWeek () == IdcDate.FRIDAY || date.getDayOfWeek () == IdcDate.SATURDAY || date.getDayOfWeek () == IdcDate.SUNDAY )
                {
                    assertTrue ( "date=" + date, weekendDate );
                }
                else
                {
                    assertFalse ( weekendDate );
                }
                date = date.addDays ( 1 );
            }
        }
        catch ( Exception e )
        {
            fail ( "testWeekendDate", e );
        }
    }

    private Currency createTestForwardCurrency ( String name, Tenor tenor, Currency.SettlementType settlementType, Currency underlyingCcy )
    {
        Currency currency = null;
        try
        {
            UnitOfWork unitOfWork = this.getPersistenceSession ().acquireUnitOfWork ();
            unitOfWork.removeAllReadOnlyClasses ();
            unitOfWork.addReadOnlyClass ( InstrumentClassificationC.class );
            Currency ccy = CurrencyFactory.newCurrency ();
            ccy = ( Currency ) unitOfWork.registerNewObject ( ccy );
            ccy.setDeliverable ( false );
            ccy.setShortName ( name );
            ccy.setLongName ( name );
            ccy.setDescription ( "ForwardInstrument" );
            ccy.setStatus ( 'T' );
            ccy.setSettlementTenor ( tenor );
            ccy.setUnderlyingCurrency ( ( Currency ) unitOfWork.registerObject ( underlyingCcy ) );
            ccy.setSettlementType ( settlementType );
            unitOfWork.commit ();
            currency = ( Currency ) IdcUtilC.refreshObject ( ccy );
            CurrencyFactory.init ();
        }
        catch ( Exception e )
        {
            log.error ( "createTestForwardCurrency : Exception", e );
        }
        return currency;
    }

    private void deleteTestCurrency ( String ccyName )
    {
        try
        {
            UnitOfWork unitOfWork = this.getPersistenceSession ().acquireUnitOfWork ();
            ExpressionBuilder builder = new ExpressionBuilder ();
            Expression expr = builder.get ( "shortName" ).equal ( ccyName );
            Currency ccy = ( Currency ) getPersistenceSession ().readObject ( com.integral.finance.currency.Currency.class, expr );
            if ( ccy != null )
            {
                Currency refreshedCcy = ( Currency ) unitOfWork.refreshObject ( ccy );
                Currency registeredCcy = ( Currency ) unitOfWork.registerObject ( refreshedCcy );
                unitOfWork.removeAllReadOnlyClasses ();
                unitOfWork.deleteObject ( registeredCcy );
                unitOfWork.commit ();
                unitOfWork.release ();
                log.warn ( "deleted the test currency=" + ccyName );
            }
        }
        catch ( Exception exc )
        {
            log.error ( "deleteTestCurrency() Exception 3 - " + exc );
        }
    }

    public void testFieldPersistence ( )
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency usd = CurrencyFactory.getCurrency ( "USD" );
            this.uow = this.getPersistenceSession ().acquireUnitOfWork ();
            this.uow.removeAllReadOnlyClasses ();
            this.uow.addReadOnlyClass ( CurrencyC.class );
            String testFXConventionName = "Test" + System.currentTimeMillis ();
            FXRateConvention registeredConv = ( FXRateConvention ) this.uow.registerNewObject ( FXFactory.newFXRateConvention () );
            registeredConv.setShortName ( testFXConventionName );
            registeredConv.setStatus ( ENTITY_STATUS_TEST );
            FXRateBasis rb = FXFactory.newFXRateBasis ();
            assertTrue ( rb.isMarketConvention () );
            FXRateBasis registeredRateBasis = ( FXRateBasis ) this.uow.registerNewObject ( rb );
            assertTrue ( registeredRateBasis.isMarketConvention () );
            registeredRateBasis.setBaseCurrency ( usd );
            registeredRateBasis.setVariableCurrency ( eur );
            registeredRateBasis.setOwner ( registeredConv );
            registeredConv.addFXRateBasis ( registeredRateBasis );
            registeredRateBasis.setMarketConvention ( false );
            this.uow.commit ();

            rb = ( FXRateBasis ) IdcUtilC.refreshObject ( rb );
            assertNotNull ( rb );
            assertFalse ( rb.isMarketConvention () );
        }
        catch ( Exception e )
        {
            fail ( "testFieldPersistence", e );
        }
    }

    protected void setUp ( ) throws Exception
    {
        super.setUp ();
        deleteTestCurrency ( FWD_CCY_SHORT_NAME );
    }

    protected void tearDown ( )
    {
        super.tearDown ();
        deleteTestCurrency ( FWD_CCY_SHORT_NAME );
        deleteTestFXRateConventions ();
    }

    private void deleteTestFXRateConventions ( )
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder ();
            Expression expr = eb.get ( Entity.Status ).equal ( ENTITY_STATUS_TEST );
            Collection<FXRateConvention> testConventions = getPersistenceSession ().readAllObjects ( FXRateConventionC.class, expr );
            if ( testConventions != null && !testConventions.isEmpty () )
            {
                this.uow = this.getPersistenceSession ().acquireUnitOfWork ();
                this.uow.removeAllReadOnlyClasses ();
                for ( FXRateConvention conv : testConventions )
                {
                    this.uow.deleteObject ( conv );
                }
                uow.commit ();
            }
        }
        catch ( Exception e )
        {
            log.error ( "deleteTestFXRateConventions", e );
        }
    }

    public void testConvCurrencyPairEquals()
    {
        FXRateConvention stdConv = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();
        assertNotNull ( stdConv );
        Collection<FXRateBasis> rateBases = stdConv.getFXRateBasis ();
        for ( FXRateBasis rb: rateBases )
        {
            for ( FXRateBasis otherRb : rateBases )
            {
                if ( rb.isSameAs ( otherRb ))
                {
                    continue;
                }
                assertFalse ( rb.getCurrencyPair ().equals( otherRb.getCurrencyPair () ) );
            }
        }
    }

    public void testCurrencyPairEquals()
    {
        try
        {
            Currency esh = new CurrencyC();
            esh.setShortName ( "ESH" );
            Currency eur = CurrencyFactory.getCurrency ( "EUR" );
            Currency f4h = new CurrencyC();
            f4h.setShortName ( "F4H" );
            CurrencyPair ccyPair = new CurrencyPairC ( );
            ccyPair.setBaseCurrency ( esh );
            ccyPair.setVariableCurrency ( eur );

            CurrencyPair ccyPair1 = new CurrencyPairC ( );
            ccyPair.setBaseCurrency ( f4h );
            ccyPair.setVariableCurrency ( eur );

            assertFalse ( ccyPair.equals ( ccyPair1 ) );
        }
        catch ( Exception e )
        {
            fail( "testCurrencyPairEquals", e );
        }
    }

    public void testCurrencyFactoryCurrencyPairsForEquals()
    {
        try
        {
            Collection<CurrencyPair> ccyPairList = new ArrayList<CurrencyPair> ( CurrencyFactory.getCurrencyPairMap ().values () );
            for ( CurrencyPair cp: ccyPairList )
            {
                for ( CurrencyPair otherCp : ccyPairList )
                {
                    if ( cp.getBaseCurrency ().isSameAs ( otherCp.getBaseCurrency () ) && cp.getVariableCurrency ().isSameAs ( otherCp.getVariableCurrency () ) )
                    {
                        continue;
                    }
                    assertFalse ( cp.equals( otherCp ) );
                }
            }
        }
        catch ( Exception e )
        {
            fail ("testCurrencyFactoryCurrencyPairsForEquals", e);
        }
    }

    public void testEndOfMonthTenors()
    {
        try
        {
            String[] months = new String[]{"JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"};
            FXRateConvention stdConv = ReferenceDataCacheC.getInstance ().getStandardFXRateConvention ();
            assertNotNull ( stdConv );
            FXRateBasis rateBasis = stdConv.getFXRateBasis ( "EUR/USD" );
            assertNotNull ( rateBasis );

            IdcDate tradeDate = DateTimeFactory.newDate ().subtractDays ( 100 );
            for ( int i = 0; i < IdcDate.DAYS_IN_NORMAL_YEAR * 25 ; i++ )
            {
                for ( int j = 1; j <= months.length; j++ )
                {
                    String monthName = months[j-1];
                    IdcDate valueDate = rateBasis.getValueDate ( tradeDate, new Tenor ( monthName ) );
                    assertEquals ( "TradeDate=" + tradeDate + ",valueDate=" + valueDate + ",month=" + monthName + ",monthNum=" + j, j, valueDate.getMonth () );
                }
                tradeDate = tradeDate.addDays ( 1 );
            }
        }
        catch ( Exception e )
        {
            fail ("testEndOfMonthTenors", e);
        }
    }
}
