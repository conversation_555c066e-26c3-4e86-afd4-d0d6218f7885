package com.integral.finance.fx.test;


import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyPairC;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.dealing.Quote;
import com.integral.finance.financialEvent.FinancialEvent;
import com.integral.finance.financialEventBuilder.FinancialEventBuilder;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.fx.FXSSPTrade;
import com.integral.finance.fx.FXSSPTradeC;
import com.integral.finance.fx.FXSettlementDateRuleC;
import com.integral.finance.fx.FXSideRates;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.fx.FXTradeC;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.CptyTradeC;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.finance.trade.TradeClassificationC;
import com.integral.finance.trade.TradeCloneServiceC;
import com.integral.finance.trade.TradeLeg;
import com.integral.persistence.Entity;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserC;
import com.integral.util.IdcUtilC;
import com.integral.workflow.StateC;
import com.integral.workflow.WorkflowStateMapC;

import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Iterator;
import java.util.Vector;


public class FXTradePTestC
        extends PTestCaseC
{
    static String name = "FX Trade Test";
    Vector users = null;
    protected String fiName = "FI1";
    protected String fiLeName = "FI1-le1";
    protected String lpName = "CITI";
    protected String lpLeName = "CITI-le1";
    protected Organization fiOrg;
    protected TradingParty fiTpForLp;
    protected ReadNamedEntityC namedEntityReader = new ReadNamedEntityC();

    public FXTradePTestC( String name )
    {
        super( name );
        fiOrg = ( Organization ) namedEntityReader.execute( Organization.class, fiName );
        fiTpForLp = ( TradingParty ) namedEntityReader.execute( TradingPartyC.class, lpLeName, fiName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        // suite.addTest(new FXTradePTestC("insertTest"));
        // suite.addTest(new FXTradePTestC("lookupTest"));
        suite.addTest( new FXTradePTestC( "modTest" ) );
        return suite;
    }

    public void testTradeLegs()
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            IdcDate spotDate = DateTimeFactory.newDate().addDays( 2 );
            FXSingleLeg singleLeg = DealingTestUtilC.createTestSingleLegTrade( 1000, true, false, true, "EUR/USD", fiOrg, fiTpForLp, 1.2365, spotDate );
            singleLeg = ( FXSingleLeg ) uow.registerObject( singleLeg );
            singleLeg.setPortfolioRefId("TESTREF");
            log( "single leg fxLeg=" + singleLeg.getFXLeg() );
            assertEquals( "leg should not be null.", singleLeg.getFXLeg() != null, true );
            uow.commit();
            singleLeg = ( FXSingleLeg ) PersistenceFactory.newSession().refreshObject( singleLeg );
            log( "After refreshing single leg fxLeg=" + singleLeg.getFXLeg() );
            assertEquals( "After refreshing leg should not be null.", singleLeg.getFXLeg() != null, true );
            assertEquals(singleLeg, "TESTREF");
            
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "fxLeg" ).get( Entity.ObjectID ).equal( singleLeg.getObjectID() );
            FXSingleLeg trade = ( FXSingleLeg ) PersistenceFactory.newSession().readObject( FXTrade.class, expr );
            log( "trade=" + trade );
            assertEquals( "queried trade should not be null.", trade != null, true );

            UnitOfWork uow1 = PersistenceFactory.newSession().acquireUnitOfWork();
            FXSwap swap = DealingTestUtilC.createTestSwapTrade( 1000, 1000, true, false, false, false, true, true, "EUR/USD", fiOrg, fiTpForLp, 1.4545, 1.4565, spotDate, spotDate.addDays( 2 ) );
            swap = ( FXSwap ) uow1.registerObject( swap );
            log( "swap near leg fxLeg=" + swap.getNearLeg() );
            log( "swap far leg fxLeg=" + swap.getFarLeg() );
            assertEquals( "near leg should not be null.", swap.getNearLeg() != null, true );
            assertEquals( "far leg should not be null.", swap.getFarLeg() != null, true );
            swap.setPortfolioRefId("SWAPPORTREF");
            uow1.commit();

            swap = ( FXSwap ) PersistenceFactory.newSession().refreshObject( swap );
            log( "After refreshing swap leg near=" + swap.getNearLeg() + ",farLeg=" + swap.getFarLeg() );
            assertEquals( "After refreshing near leg should not be null.", swap.getNearLeg() != null, true );
            assertEquals( "After refreshing far leg should not be null.", swap.getFarLeg() != null, true );
            assertEquals( "Verifying Portfolio Reference ID", swap.getPortfolioRefId() ,"SWAPPORTREF" );
            ExpressionBuilder eb1 = new ExpressionBuilder();
            Expression expr1 = eb1.get( "nearLeg" ).get( Entity.ObjectID ).equal( swap.getNearLeg().getObjectID() );
            Expression expr2 = eb1.get( "farLeg" ).get( Entity.ObjectID ).equal( swap.getFarLeg().getObjectID() );
            FXSwap swapTrade = ( FXSwap ) PersistenceFactory.newSession().readObject( FXTrade.class, expr1.and( expr2 ) );
            log( "FXSwap=" + swapTrade );
            assertEquals( "queried swap trade should not be null.", swapTrade != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testInsertSwap()
    {
        log( "testInsertSwap" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            FXTrade trade = FXFactory.newFXSwap();
            uow.registerObject( trade );

            uow.commit();
            log( "testInsertSwap" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsertSwap" );
        }
    }

    public void testTradeLookup()
    {
        log( "testTradeLookup" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( FXTrade.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXTrade trade = ( FXTrade ) objs.elementAt( i );
                printTrade( trade, i );
            }
            log( "testTradeLookup" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testTradeLookup" );
        }
    }

    public void modTest()
    {
        log( "modTest" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            Vector objs = uow.readAllObjects( Quote.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                Quote quote = ( Quote ) objs.elementAt( i );
                try
                {
                    FXPrice prc = ( FXPrice ) quote.getQuotePrice( "singleLeg" ).getPriceElement().getPrice();
                    FXSideRates sr = prc.getBidFXRate().getSideRates();

                    prc.getBidFXRate().setSpotRate( 2.3 );

                    log.info( "changed quote " + quote );
                    log.info( "  with side rates " + sr );
                }
                catch ( Exception e2 )
                {
                    log.warn( "error changing quote " + quote );
                }
            }
            uow.commit();
            log( "lmodest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "modTest" );
        }
    }

    public void testInsertFXSSPTrade()
    {
        log( "testInsertFXSSPTrade" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            FXTrade trade = FXFactory.newFXSSPTrade();
            uow.registerObject( trade );

            uow.commit();
            log( "testInsertFXSSPTrade" );

            FXSSPTrade refreshedTrade = ( FXSSPTrade ) IdcUtilC.refreshObject( trade );
            assertNotNull( refreshedTrade );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsertFXSSPTrade" );
        }
    }

    public void testInsertFXSSPTradeWithMultipleLegs()
    {
        log( "testInsertFXSSPTradeWithMultipleLegs" );
        try
        {
            //UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            IdcTransaction tx = IdcSessionManager.getInstance().newTransaction();
            tx.getUOW().removeReadOnlyClass(CptyTradeC.class);
            tx.getUOW().removeReadOnlyClass(FXTradeC.class);
            tx.getUOW().removeReadOnlyClass(FXSSPTradeC.class);
            tx.getUOW().removeReadOnlyClass(WorkflowStateMapC.class);
            tx.getUOW().addReadOnlyClass(LegalEntityC.class);
            tx.getUOW().addReadOnlyClass(NamespaceC.class);
            tx.getUOW().addReadOnlyClass(UserC.class);
            tx.getUOW().addReadOnlyClass(OrganizationC.class);
            tx.getUOW().addReadOnlyClass(FXSettlementDateRuleC.class);
            tx.getUOW().addReadOnlyClass(FXRateConventionC.class);
            tx.getUOW().addReadOnlyClass(TradeClassificationC.class);
            tx.getUOW().addReadOnlyClass(StateC.class);
            tx.getUOW().addReadOnlyClass(TradingPartyC.class);
            tx.getUOW().addReadOnlyClass(CurrencyC.class);
            tx.getUOW().addReadOnlyClass(CurrencyPairC.class);
            tx.getUOW().addReadOnlyClass(CurrencyPairGroupC.class);
            double[] amts = new double[]{1000.00, 2000.00, 3000.00, 4000.0};
            boolean[] isBid = new boolean[]{true, false, true, true};
            boolean[] isBaseAmt = new boolean[]{true, false, true, true};
            boolean[] isDealtAmt = new boolean[]{true, false, true, true};
            double[] fwdPoints = new double[]{0.0010, 0.003, -0.0045, -0.0044};
            IdcDate[] valueDates = new IdcDate[4];
            valueDates[0] = DateTimeFactory.newDate().addDays( 2 );
            valueDates[1] = DateTimeFactory.newDate().addMonths( 1 );
            valueDates[2] = DateTimeFactory.newDate().addMonths( 2 );
            valueDates[3] = DateTimeFactory.newDate().addMonths( 3 );

            Tenor[] tenors = new Tenor[4];
            tenors[0] = Tenor.SPOT_TENOR;
            tenors[1] = new Tenor( "1m" );
            tenors[2] = new Tenor( "2m" );
            tenors[3] = new Tenor( "3m" );

            Organization taker = ( Organization ) namedEntityReader.execute( Organization.class, "FI1" );
            Organization maker = ( Organization ) namedEntityReader.execute( Organization.class, "CITI" );
            TradingParty makerTpForTaker = taker.getDefaultDealingEntity().getTradingParty( maker );
            FXTrade trade = DealingTestUtilC.createTestFXSSPTrade( amts, isBid, isBaseAmt, isDealtAmt, "EUR/USD", maker, makerTpForTaker, 1.2334, fwdPoints, valueDates, tenors, null, null );
            trade = (FXTrade)tx.getRegisteredObject(trade);

            TradeCloneServiceC.createCptyTrades(trade);
            //uow.registerObject( trade );

            tx.commit();
            log( "testInsertFXSSPTradeWithMultipleLegs" );

            FXSSPTrade refreshedTrade = ( FXSSPTrade ) IdcUtilC.refreshObject( trade );
            assertNotNull( refreshedTrade );
            assertTrue( refreshedTrade.getTradeLegs().size() == 4 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsertFXSSPTradeWithMultipleLegs" );
        }
    }

    private void printTrade( Trade trade, int i )
    {
        log( "fx trade #" + i );
        log( "\t objectID = " + trade.getObjectID() );

        // get financial event builder
        FinancialEventBuilder builder = trade.getFinancialEventBuilder();
        log( "\t trade builder " + builder );

        Iterator events = trade.getFinancialEvents().iterator();
        int e = 0;
        while ( events.hasNext() )
        {
            FinancialEvent event = ( FinancialEvent ) events.next();
            log( "\t trade event #" + e + ": " + event );
            e++;
        }

        Iterator legs = trade.getTradeLegs().iterator();
        int l = 0;
        while ( legs.hasNext() )
        {
            TradeLeg leg = ( TradeLeg ) legs.next();
            log( "\t trade leg #" + l + ": " + leg );

            Iterator leg_events = leg.getFinancialEvents().iterator();
            int le = 0;
            while ( leg_events.hasNext() )
            {
                FinancialEvent leg_event = ( FinancialEvent ) leg_events.next();
                log( "\t\t trade leg event #" + e + ": " + leg_event );
                le++;
            }

            l++;
        }
    }
}
