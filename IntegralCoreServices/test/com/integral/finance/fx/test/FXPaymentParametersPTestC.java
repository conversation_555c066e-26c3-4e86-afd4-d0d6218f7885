package com.integral.finance.fx.test;


import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXPaymentParametersC;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateC;
import com.integral.finance.fx.FXSideRates;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

/**
 * Unit test for fx payment paramters
 */
public class FXPaymentParametersPTestC
        extends PTestCaseC
{
    static String name = "FXPaymentParameters Test";
    Vector users = null;

    public FXPaymentParametersPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        // suite.addTest(new FXPaymentParametersPTestC("insertTest"));
        suite.addTest( new FXPaymentParametersPTestC( "modTestFail" ) );
        // suite.addTest(new FXPaymentParametersPTestC("modTest"));
        // suite.addTest(new FXPaymentParametersPTestC("lookupTest"));
        return suite;
    }

    /**
     * Inserts a new fx payment parameter with a fx rate pointing to a fx side rates
     */
    public void insertTest()
    {
        log( "insertTest" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            FXRate rate = new FXRateC();

            FXSideRates newSideRate = FXFactory.newFXSideRates();
            rate.setSideRates( newSideRate );

            FXPaymentParameters pp = new FXPaymentParametersC();
            pp.setFXRate( rate );
            uow.registerObject( pp );

            uow.commit();
            log( "insertTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }

    public void lookupTest()
    {
        log( "lookupTest" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( FXPaymentParameters.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXPaymentParameters pp = ( FXPaymentParameters ) objs.elementAt( i );
                printFXPaymentParameters( pp, i );
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTest" );
        }
    }

    public void modTestNew()
    {
        log( "modTest" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( FXPaymentParameters.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXPaymentParameters pp = ( FXPaymentParameters ) objs.elementAt( i );
                if ( ( pp.getFXRate() != null ) && ( pp.getFXRate().getSideRates() != null ) )
                {
                    log( "\tcloning " + pp );

                    UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

                    FXPaymentParameters ppNEW = new FXPaymentParametersC();

                    FXRate rate = ( FXRate ) pp.getFXRate().clone();
                    ppNEW.setFXRate( rate );

                    uow.registerObject( ppNEW );

                    uow.commit();
                }
            }
            log( "modTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "modTest" );
        }
    }

    public void modTestFail()
    {
        log( "modTestFail" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( FXPaymentParameters.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                FXPaymentParameters pp = ( FXPaymentParameters ) objs.elementAt( i );
                if ( ( pp.getFXRate() != null ) && ( pp.getFXRate().getSideRates() != null ) )
                {
                    log( "nor sr  = " + pp.getFXRate().getSideRates() );

                    UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
                    FXPaymentParameters pp1 = ( FXPaymentParameters ) uow1.registerObject( pp );

                    // pp1.getFXRate().getSideRates();
                    // FXRate rate = (FXRate)pp1.getFXRate().clone();
                    // pp.setFXRate(rate);

                    FXSideRates sr = ( FXSideRates ) uow1.registerObject( pp.getFXRate().getSideRates() );
                    // pp.getFXRate().setSideRates(pp1.getFXRate().getSideRates());
                    pp.getFXRate().setSideRates( sr );
                    log( "uow sr 1= " + sr );
                    log( "reg sr 1= " + pp1.getFXRate().getSideRates() );

                    log( "\tcorrupting " + pp );
                    UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
                    pp = ( FXPaymentParameters ) uow.registerObject( pp );

                    log( "reg sr 2= " + pp.getFXRate().getSideRates() );
                    // pp.getFXRate().setSideRates(pp.getFXRate().getSideRates());
                    pp.getFXRate().setRate( 1.222 );
                    pp.setStatus( 'A' );

                    uow.commit();
                }
            }
            log( "modTestFail" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "modTestFail" );
        }
    }

    private void printFXPaymentParameters( FXPaymentParameters pp, int i )
    {
        log( "FXPaymentParameters #" + i );
        log( "\t objectID = " + pp.getObjectID() );
        log( "\t fx rate  = " + pp.getFXRate() );
        if ( pp.getFXRate() != null )
        {
            log( "\t fx rate side rates  = " + pp.getFXRate().getSideRates() );
        }
    }
}
