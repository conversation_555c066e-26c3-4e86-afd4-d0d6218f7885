package com.integral.finance.fx;

import java.io.StringWriter;

import com.integral.time.DateTimeFactory;
import org.codehaus.jackson.JsonFactory;
import org.codehaus.jackson.JsonGenerator;
import org.codehaus.jackson.JsonParser;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.trade.Tenor;
import com.integral.serialize.SerializerMap;
import com.integral.serialize.TradeSerializer;
import com.integral.serialize.TradeSerializer.FXTradeType;
import com.integral.test.PTestCaseC;
import com.integral.test.TestUtilsConstant;
import com.integral.test.util.DealingTestUtilC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

public class FXTradeSerializerTestC extends PTestCaseC {
	
	public FXTradeSerializerTestC(String name) {
		super(name);
	}
	
	public void testSSPSerializationWithoutCompaction() throws Exception
    {
		sSPSerializationWithoutCompaction(0);
    }
	
	public void testSSPSerializationWithCompaction() throws Exception
    {
		sSPSerializationWithoutCompaction(1);
    }
	
	private void sSPSerializationWithoutCompaction(int compaction) throws Exception
    {
	    CurrencyPair eurUsd =TestUtilsConstant.EURUSD;
	    String takerLE = "FI1-le1";
	    String taker = "FI1", maker = "DBNA";
    	try
    	{
            ExpressionBuilder eb = new ExpressionBuilder();

            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            SerializerMap serializerMap = new SerializerMap();
            TradeSerializer mrs = new TradeSerializer(new String[]{"fxSSP", "ssp"}, null, serializerMap, FXTradeType.SSP);
            JsonFactory f = new JsonFactory();
            StringWriter wri = new StringWriter();
            boolean[] isBid = {false,false,true,false};
            boolean[] isBaseAmt = {false,false,true,false};
            boolean[] isDealtAmt = {false,false,true,false};
            double [] amt = {10000.00,20000.00,30000.00,40000.00};
            Tenor[] tenor = {new Tenor("1W"),new Tenor("2W"),new Tenor("1M"), new Tenor("2M")},fixingTenor = null;
            Organization makerOrg =(Organization) session1.readObject( OrganizationC.class, eb.get( "shortName" ).equal(maker) );
            Expression tpExp = eb.get("shortName").equal(takerLE).and(eb.get( "namespace").get("shortName").equal(makerOrg.getNamespace().getShortName()));
            tpExp = tpExp.and(eb.get( "legalEntity").get("organization").get("shortName").equal("FI1"));
            
            TradingParty makerTpForTaker = (TradingParty)session1.readObject( TradingPartyC.class,tpExp);
            System.out.println("Trading Party " + makerTpForTaker);
            double spotRate = 1.2345;
            double fwdPoints[] = {2,4,6,8};
            IdcDate[] valueDate = new IdcDate[4], fixingDate = null;
            
            FXSSPTrade sspTrasde = DealingTestUtilC.createTestFXSSPTrade(amt,isBid,isBaseAmt,isDealtAmt,eurUsd.getName(),
            															makerOrg,makerTpForTaker,spotRate,fwdPoints,valueDate,
            															tenor,fixingDate,fixingTenor);
            
            
            JsonGenerator g = f.createJsonGenerator( wri );
            long start = System.nanoTime();
            g.writeStartObject();
            mrs.serializeEntity( sspTrasde, g, compaction);
            g.writeEndObject();

            g.flush();
            long end = System.nanoTime();

            System.out.println( ">>>JSON: " + wri.getBuffer().toString() );
            System.out.println( ">>>Nanos: " + (end - start) );

            JsonParser parser = f.createJsonParser( wri.getBuffer().toString() );
            parser.nextToken();
            FXSSPTrade trdDeSerialize = new FXSSPTradeC();
            mrs.advanceToObjectBoundary( parser, compaction );
            try
            {
                mrs.deserializeToObject( parser, trdDeSerialize, compaction );
                for(int i=0;i<4;i++)
                {
                	boolean isBase = isBaseAmt[i];
                	FXLeg leg = ((FXLeg)trdDeSerialize.getTradeLeg("FXLeg" + i ));
                	FXPaymentParameters fxPaymentParameters = leg.getFXPayment();
	                assertEquals(isBase?fxPaymentParameters.getCurrency1Amount():fxPaymentParameters.getCurrency2Amount(), 
	                				amt[i]);
	                assertEquals(trdDeSerialize.getFXRateBasis().getCurrencyPairString(),eurUsd.getName()); 
	                assertEquals(trdDeSerialize.getCounterpartyA().getOrganization().getShortName(),taker);
	                assertEquals(trdDeSerialize.getCounterpartyB().getOrganization().getShortName(),maker);
	                assertEquals(trdDeSerialize.getCounterpartyA().getShortName(),takerLE);
	                assertEquals(fxPaymentParameters.getFXRate().getSpotRate(),spotRate);
	                assertEquals(fxPaymentParameters.getFXRate().getForwardPoints(),fwdPoints[i]);
	                assertEquals(fxPaymentParameters.getTenor(),tenor[i]);

                }
            }
            catch ( Exception e )
            {
                log.error( "Exception e : ", e );
                fail("Failed to validate FXSSP Serialization");
            }
          
    	} 
    	catch ( NullPointerException e) 
    	{
    		log.error("NullPointer Exception due to invalid request object Ignoring." + e);
            fail("Failed to validate FXSSP Serialization");
    	}
    }

    public void testTradeSerialization() throws Exception
    {
        CurrencyPair eurUsd =TestUtilsConstant.EURUSD;
        String takerLE = "FI1-le1";
        String taker = "FI1", maker = "DBNA";
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();

            Session session1 = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            SerializerMap serializerMap = new SerializerMap();
            TradeSerializer mrs = new TradeSerializer(new String[]{"fxSingleLeg", "fxSL"}, null, serializerMap, FXTradeType.SingleLeg);
            JsonFactory f = new JsonFactory();
            boolean isBid = false;
            boolean isBaseAmt = true;
            boolean isDealtAmt = true;
            double amt = 1000;
            Organization makerOrg =(Organization) session1.readObject( OrganizationC.class, eb.get( "shortName" ).equal(maker) );
            Expression tpExp = eb.get("shortName").equal(takerLE).and(eb.get( "namespace").get("shortName").equal(makerOrg.getNamespace().getShortName()));
            tpExp = tpExp.and(eb.get( "legalEntity").get("organization").get("shortName").equal("FI1"));

            TradingParty makerTpForTaker = (TradingParty)session1.readObject( TradingPartyC.class,tpExp);
            System.out.println("Trading Party " + makerTpForTaker);
            double spotRate = 1.2345;
            IdcDate valueDate = DateTimeFactory.newDate().addDays(2);
            FXSingleLeg singleLegTrade = DealingTestUtilC.createTestSingleLegTrade(amt, isBid, isBaseAmt, isDealtAmt, "EUR/USD", makerOrg, makerTpForTaker, spotRate, valueDate);
            singleLegTrade.setOriginatingPortfolioId("1234567890");

            // Serializaton with Compaction
            StringWriter wri = new StringWriter();
            JsonGenerator g = f.createJsonGenerator( wri );
            long start = System.nanoTime();
            g.writeStartObject();
            mrs.serializeEntity( singleLegTrade, g, 1);
            g.writeEndObject();
            g.flush();
            long end = System.nanoTime();
            System.out.println( ">>>JSON: " + wri.getBuffer().toString() );
            System.out.println( ">>>Nanos: " + (end - start) );
            JsonParser parser = f.createJsonParser( wri.getBuffer().toString() );
            parser.nextToken();
            FXSingleLeg deSerializedTrade = new FXSingleLegC();
            mrs.advanceToObjectBoundary( parser, 1 );
            try
            {
                mrs.deserializeToObject( parser, deSerializedTrade, 1 );
                assertEquals("OriginatingPortfolioId serialization failed", "1234567890", deSerializedTrade.getOriginatingPortfolioId());
            }
            catch ( Exception e )
            {
                log.error( "Exception e : ", e );
                fail("Failed to validate FXSSP Serialization");
            }

            // Serializaton without Compaction
            wri = new StringWriter();
            g = f.createJsonGenerator( wri );
            start = System.nanoTime();
            g.writeStartObject();
            mrs.serializeEntity( singleLegTrade, g, 0);
            g.writeEndObject();
            g.flush();
            end = System.nanoTime();
            System.out.println( ">>>JSON: " + wri.getBuffer().toString() );
            System.out.println( ">>>Nanos: " + (end - start) );
            parser = f.createJsonParser( wri.getBuffer().toString() );
            parser.nextToken();
            deSerializedTrade = new FXSingleLegC();
            mrs.advanceToObjectBoundary( parser, 0 );
            try
            {
                mrs.deserializeToObject( parser, deSerializedTrade, 0 );
                assertEquals("OriginatingPortfolioId serialization failed", "1234567890", deSerializedTrade.getOriginatingPortfolioId());
            }
            catch ( Exception e )
            {
                log.error( "Exception e : ", e );
                fail("Failed to validate FXSSP Serialization");
            }
        }
        catch ( NullPointerException e)
        {
            log.error("NullPointer Exception due to invalid request object Ignoring." + e);
            fail("Failed to validate FXSSP Serialization");
        }
    }
}
