package com.integral.finance.price.fx.test;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.price.DoublePrice;
import com.integral.finance.price.DoublePriceC;
import com.integral.finance.price.fx.FXForwardPointsSpreadPriceCalculatorC;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceC;
import com.integral.finance.price.fx.FXSpreadPriceCalculatorC;
import com.integral.finance.price.fx.PercentageOfMidFXSpreadC;
import com.integral.finance.price.fx.PercentageOfSpreadFXSpreadC;
import com.integral.persistence.NamedEntity;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

public class FXSpreadPriceTestC extends PTestCaseC
{
    public FXPrice base, target, fxSpreadPrice;
    public DoublePrice dblSpreadPrice;
    static String standardFXConventionName = "STDQOTCNV";
    private FXRateConvention convention = null;

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new FXSpreadPriceTestC( "pctOfMid" ) );
        suite.addTest( new FXSpreadPriceTestC( "pctOfSpread" ) );
        suite.addTest( new FXSpreadPriceTestC( "pointSpread" ) );
        return suite;
    }

    public FXSpreadPriceTestC( String name )
    {
        super( name );
        initBasePrice();
    }

    private void initBasePrice()
    {
        login();
        convention = this.getFXRateConvention();

        base = new FXPriceC();
        base.setBid( fxRate( 1000.0, 10.0 ) );
        base.setOffer( fxRate( 2000.0, 20.0 ) );
        base.setMid( fxRate( 1500.0, 15.0 ) );
        target = new FXPriceC();
        target.setBid( fxRate( 0, 0 ) );
        target.setOffer( fxRate( 0, 0 ) );
        target.setMid( fxRate( 0, 0 ) );
    }

    protected FXRateConvention getFXRateConvention()
    {
        return ( FXRateConvention ) this.getNamedEntity(
                FXRateConvention.class,
                standardFXConventionName );
    }

    protected NamedEntity getNamedEntity( Class aClass, String shortName )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).equal( shortName );
        Object obj = null;
        if ( this.uow == null )
        {
            obj = this.getPersistenceSession().readObject( aClass, expr );
        }
        else
        {
            obj = this.uow.readObject( aClass, expr );
        }
        return ( NamedEntity ) obj;
    }

    private FXRate fxRate( double spotRate, double fwdPoints )
    {
        FXRate fxRate = new FXRateC();
        fxRate.setCurrencyPair( CurrencyFactory.newCurrencyPair( "EUR", "USD" ) );
        fxRate.setSpotRate( spotRate );
        fxRate.setForwardPoints( fwdPoints );
        fxRate.setFXRateBasis( convention.getFXRateBasis( "EUR", "USD" ) );
        return fxRate;
    }

    public void pointSpread()
    {
        fxSpreadPrice = new FXPriceC();
        fxSpreadPrice.setBid( fxRate( 2500, 0 ) );
        fxSpreadPrice.setOffer( fxRate( 2500, 0 ) );
        fxSpreadPrice.setMid( fxRate( 2500, 0 ) );

        FXSpreadPriceCalculatorC calc = new FXForwardPointsSpreadPriceCalculatorC();
        ( ( FXPriceC ) fxSpreadPrice ).setPriceTypeCalculator( calc );

        initBasePrice();
        fxSpreadPrice.add( target, fxSpreadPrice );
        System.out.println( "pointSpread w/  pointSpread" );
        printFXPrice( "Spread", fxSpreadPrice );
        printFXPrice( "Target", target );


        initBasePrice();
        base.add( target, fxSpreadPrice );
        System.out.println( "pointSpread w/ FXPrice" );
        printFXPrice( "Base", base );
        printFXPrice( "Spread", fxSpreadPrice );
        printFXPrice( "Target", target );


        initBasePrice();
        base.setMid( null );
        base.add( target, fxSpreadPrice );
        System.out.println( "pointSpread w/ FXPrice, no mid" );
        printFXPrice( "Base", base );
        printFXPrice( "Spread", fxSpreadPrice );
        printFXPrice( "Target", target );

        dblSpreadPrice = new DoublePriceC();
        dblSpreadPrice.setBid( new Double( 0.1 ) );
        dblSpreadPrice.setMid( new Double( 0.15 ) );
        dblSpreadPrice.setOffer( new Double( 0.2 ) );
        ( ( DoublePriceC ) dblSpreadPrice ).setPriceTypeCalculator( calc );

        initBasePrice();
        base.add( target, dblSpreadPrice );
        System.out.println( "pointSpread w/ DoublePrice" );
        printFXPrice( "Base", base );
        printDoublePrice( "Spread", dblSpreadPrice );
        printFXPrice( "Target", target );

        initBasePrice();
        base.setMid( null );
        base.add( target, dblSpreadPrice );
        System.out.println( "pointSpread w/ DoublePrice, no mid" );
        printFXPrice( "Base", base );
        printDoublePrice( "Spread", dblSpreadPrice );
        printFXPrice( "Target", target );

    }

    public void pctOfMid()
    {
        fxSpreadPrice = new FXPriceC();
        fxSpreadPrice.setBid( fxRate( 0.1, 0.1 ) );
        fxSpreadPrice.setOffer( fxRate( 0.2, 0.2 ) );
        fxSpreadPrice.setMid( fxRate( 0.15, 0.15 ) );

        FXSpreadPriceCalculatorC calc = new PercentageOfMidFXSpreadC();
        ( ( FXPriceC ) fxSpreadPrice ).setPriceTypeCalculator( calc );

        initBasePrice();
        fxSpreadPrice.add( target, fxSpreadPrice );
        System.out.println( "PctOfMid w/  PctOfMid" );
        printFXPrice( "Spread", fxSpreadPrice );
        printFXPrice( "Target", target );


        initBasePrice();
        base.add( target, fxSpreadPrice );
        System.out.println( "PctOfMid w/ FXPrice" );
        printFXPrice( "Base", base );
        printFXPrice( "Spread", fxSpreadPrice );
        printFXPrice( "Target", target );


        initBasePrice();
        base.setMid( null );
        base.add( target, fxSpreadPrice );
        System.out.println( "PctOfMid w/ FXPrice, no mid" );
        printFXPrice( "Base", base );
        printFXPrice( "Spread", fxSpreadPrice );
        printFXPrice( "Target", target );

        dblSpreadPrice = new DoublePriceC();
        dblSpreadPrice.setBid( new Double( 0.1 ) );
        dblSpreadPrice.setMid( new Double( 0.15 ) );
        dblSpreadPrice.setOffer( new Double( 0.2 ) );
        ( ( DoublePriceC ) dblSpreadPrice ).setPriceTypeCalculator( calc );

        initBasePrice();
        base.add( target, dblSpreadPrice );
        System.out.println( "PctOfMid w/ DoublePrice" );
        printFXPrice( "Base", base );
        printDoublePrice( "Spread", dblSpreadPrice );
        printFXPrice( "Target", target );

        initBasePrice();
        base.setMid( null );
        base.add( target, dblSpreadPrice );
        System.out.println( "PctOfMid w/ DoublePrice, no mid" );
        printFXPrice( "Base", base );
        printDoublePrice( "Spread", dblSpreadPrice );
        printFXPrice( "Target", target );

    }


    public void pctOfSpread()
    {
        fxSpreadPrice = new FXPriceC();
        fxSpreadPrice.setBid( fxRate( 0.1, 0.1 ) );
        fxSpreadPrice.setOffer( fxRate( 0.2, 0.2 ) );
        fxSpreadPrice.setMid( fxRate( 0.15, 0.15 ) );

        FXSpreadPriceCalculatorC calc = new PercentageOfSpreadFXSpreadC();
        ( ( FXPriceC ) fxSpreadPrice ).setPriceTypeCalculator( calc );


        initBasePrice();
        fxSpreadPrice.add( target, fxSpreadPrice );
        System.out.println( "PctOfSpread w/  PctOfSpread" );
        printFXPrice( "Spread", fxSpreadPrice );
        printFXPrice( "Target", target );


        initBasePrice();
        base.add( target, fxSpreadPrice );
        System.out.println( "PctOfSpread w/ FXPrice" );
        printFXPrice( "Base", base );
        printFXPrice( "Spread", fxSpreadPrice );
        printFXPrice( "Target", target );


        dblSpreadPrice = new DoublePriceC();
        dblSpreadPrice.setBid( new Double( 0.1 ) );
        dblSpreadPrice.setMid( new Double( 0.15 ) );
        dblSpreadPrice.setOffer( new Double( 0.2 ) );
        ( ( DoublePriceC ) dblSpreadPrice ).setPriceTypeCalculator( calc );

        initBasePrice();
        base.add( target, dblSpreadPrice );
        System.out.println( "PctOfSpread w/ DoublePrice" );
        printFXPrice( "Base", base );
        printDoublePrice( "Spread", dblSpreadPrice );
        printFXPrice( "Target", target );

    }


    private void printFXPrice( String name, FXPrice fxPrice )
    {
        System.out.println( "FXPrice: " + name );

        System.out.println( "\tBid: Rate: " + fxPrice.getBidFXRate().getRate() +
                " Spot: " + fxPrice.getBidFXRate().getSpotRate() +
                " Points: " + fxPrice.getBidFXRate().getForwardPoints() );

        if ( fxPrice.getMid() != null )
        {
            System.out.println( "\tMid: Rate: " + fxPrice.getMidFXRate().getRate() +
                    " Spot: " + fxPrice.getMidFXRate().getSpotRate() +
                    " Points: " + fxPrice.getMidFXRate().getForwardPoints() );
        }
        else
        {
            System.out.println( "\tComputed Mid: Rate: " + ( ( FXRate ) fxPrice.getComputedMid() ).getRate() +
                    " Spot: " + ( ( FXRate ) fxPrice.getComputedMid() ).getSpotRate() +
                    " Points: " + ( ( FXRate ) fxPrice.getComputedMid() ).getForwardPoints() );
        }


        System.out.println( "\tOffer: Rate: " + fxPrice.getOfferFXRate().getRate() +
                " Spot: " + fxPrice.getOfferFXRate().getSpotRate() +
                " Points: " + fxPrice.getOfferFXRate().getForwardPoints() );

        System.out.println( "\tSpread: Rate: " + ( fxPrice.getOfferFXRate().getRate() - fxPrice.getBidFXRate().getRate() ) +
                " Spot: " + ( fxPrice.getOfferFXRate().getSpotRate() - fxPrice.getBidFXRate().getSpotRate() ) +
                " Points: " + ( fxPrice.getOfferFXRate().getForwardPoints() - fxPrice.getBidFXRate().getForwardPoints() ) );

        System.out.println();
    }

    private void printDoublePrice( String name, DoublePrice price )
    {
        System.out.println( "DoublePrice: " + name );

        System.out.println( "\tBid: " + price.getBidDouble() );
        System.out.println( "\tMid: " + price.getMidDouble() );
        System.out.println( "\tOffer: " + price.getOfferDouble() );
    }
}