package com.integral.finance.price.fx.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.price.DoublePrice;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceC;
import com.integral.persistence.NamedEntity;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.text.DecimalFormat;

public class FXPriceTestC extends PTestCaseC
{
    public FXPrice usd_cad, usd_jpy, cad_jpy, jpy_cad;
    public Currency usd, jpy, cad, eur;

    static String standardFXConventionName = "STDQOTCNV";
    public DoublePrice dblSpreadPrice;
    public FXRateConvention aTestConvention = null;

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new FXPriceTestC( "sideRates" ) );
        suite.addTest( new FXPriceTestC( "invertRates" ) );
        suite.addTest( new FXPriceTestC( "scaledForwardPointsFormat" ) );
        return suite;
    }

    public void scaledForwardPointsFormat()
    {
        aTestConvention = getFXRateConvention( standardFXConventionName );
        FXRate rate = getFXRate( usd, cad, 2.0, 2.0 );
        User user = UserFactory.getUser( "Integral3" );

        DecimalFormat decimalFormat = user.getDisplayPreference().getDecimalFormat();
        DecimalFormat forwardPoints = rate.getScaledForwardPointsFormat( decimalFormat );
        printScalePoints( rate, forwardPoints.getMaximumFractionDigits() );

        rate = getFXRate( cad, usd, 2.0, 2.0 );
        forwardPoints = rate.getScaledForwardPointsFormat( decimalFormat );
        printScalePoints( rate, forwardPoints.getMaximumFractionDigits() );

        rate = getFXRate( eur, usd, 2.0, 2.0 );
        forwardPoints = rate.getScaledForwardPointsFormat( decimalFormat );
        printScalePoints( rate, forwardPoints.getMaximumFractionDigits() );

        rate = getFXRate( usd, eur, 2.0, 2.0 );
        forwardPoints = rate.getScaledForwardPointsFormat( decimalFormat );
        printScalePoints( rate, forwardPoints.getMaximumFractionDigits() );

        rate = getFXRate( usd, jpy, 2.0, 2.0 );
        forwardPoints = rate.getScaledForwardPointsFormat( decimalFormat );
        forwardPoints = rate.getScaledForwardPointsFormat( decimalFormat );
        printScalePoints( rate, forwardPoints.getMaximumFractionDigits() );

        rate = getFXRate( jpy, usd, 2.0, 2.0 );
        forwardPoints = rate.getScaledForwardPointsFormat( decimalFormat );
        printScalePoints( rate, forwardPoints.getMaximumFractionDigits() );
    }

    public FXPriceTestC( String name )
    {
        super( name );

        eur = CurrencyFactory.newCurrency( "EUR" );
        usd = CurrencyFactory.newCurrency( "USD" );
        cad = CurrencyFactory.newCurrency( "CAD" );
        jpy = CurrencyFactory.newCurrency( "JPY" );
    }


    private FXRate fxRate( Currency base, Currency var, double spotRate, double fwdPoints )
    {
        FXRate fxRate = new FXRateC();
        fxRate.setBaseCurrency( base );
        fxRate.setVariableCurrency( var );
        fxRate.setSpotRate( spotRate );
        fxRate.setForwardPoints( fwdPoints );
        return fxRate;
    }

    public void sideRates()
    {
        usd_cad = new FXPriceC();
        usd_cad.setBid( fxRate( usd, cad, 2.0, 2.0 ) );
        usd_cad.setOffer( fxRate( usd, cad, 3.0, 3.0 ) );
        usd_cad.setMid( fxRate( usd, cad, 2.5, 2.5 ) );

        usd_jpy = new FXPriceC();
        usd_jpy.setBid( fxRate( usd, jpy, 100.0, 100.0 ) );
        usd_jpy.setOffer( fxRate( usd, jpy, 200.0, 200.0 ) );
        usd_jpy.setMid( fxRate( usd, jpy, 150.0, 150.0 ) );


        cad_jpy = new FXPriceC();
        cad_jpy.setBid( fxRate( cad, jpy, 33.33, 0.0 ) );
        cad_jpy.setOffer( fxRate( cad, jpy, 100.0, 0.0 ) );
        cad_jpy.setMid( fxRate( cad, jpy, 60.0, 0 ) );

        printFXPrice( "usd_jpy", usd_jpy );
        printFXPrice( "usd_cad", usd_cad );
        printFXPrice( "cad_jpy", cad_jpy );

        cad_jpy.setSideRates( usd_cad, usd_jpy );
        printFXPrice( "cad_jpy", cad_jpy );

        cad_jpy.setSideRates( usd_cad.getInverted(), usd_jpy );
        printFXPrice( "cad_usd", usd_cad.getInverted() );
        printFXPrice( "cad_jpy (inverted cad)", cad_jpy );

        cad_jpy.setSideRates( usd_cad, usd_jpy.getInverted() );
        printFXPrice( "jpy_usd", usd_jpy.getInverted() );
        printFXPrice( "cad_jpy (inverted jpy)", cad_jpy );

    }

    public void invertRates()
    {
        aTestConvention = getFXRateConvention( standardFXConventionName );
        usd_cad = new FXPriceC();
        usd_cad.setBid( getFXRate( usd, cad, 1.0274, -0.000023 ) );
        usd_cad.setOffer( getFXRate( usd, cad, 1.0278, -0.000003 ) );
        usd_cad.setMid( getFXRate( usd, cad, 1.0276, -0.000013 ) );

        printFXPrice( "usd_cad Direct FXRate ", usd_cad );

        printFXPrice( "cad_usd Inverted FXRate ", usd_cad.getInverted() );
    }

    private FXRate getFXRate( Currency base, Currency var, double spotRate, double fwdPoints )
    {
        FXRate fxRate = new FXRateC();
        fxRate.setFXRateConvention( aTestConvention );
        FXRateBasis basis = aTestConvention.getFXRateBasis( base, var );

        if ( basis.getBaseCurrency().getShortName().equals( base.getShortName() ) )
        {
            fxRate.setBaseCurrency( basis.getBaseCurrency() );
            fxRate.setVariableCurrency( basis.getVariableCurrency() );
        }
        else
        {
            fxRate.setBaseCurrency( basis.getVariableCurrency() );
            fxRate.setVariableCurrency( basis.getBaseCurrency() );
        }
        fxRate.setSpotRate( spotRate );
        fxRate.setForwardPoints( fwdPoints );
        return fxRate;
    }

    private void printScalePoints( FXRate rate, int points )
    {
        System.out.println( rate.getCurrencyPair().getName() + " scaled forward poitns - " + points );
    }

    private void printFXPrice( String name, FXPrice fxPrice )
    {
        System.out.println( "FXPrice: " + name );

        System.out.println( "\tBid: Rate: " + fxPrice.getBidFXRate().getRate() +
                " Spot: " + fxPrice.getBidFXRate().getSpotRate() +
                " Points: " + fxPrice.getBidFXRate().getForwardPoints() );

        if ( fxPrice.getMid() != null )
        {
            System.out.println( "\tMid: Rate: " + fxPrice.getMidFXRate().getRate() +
                    " Spot: " + fxPrice.getMidFXRate().getSpotRate() +
                    " Points: " + fxPrice.getMidFXRate().getForwardPoints() );
        }
        else
        {
            System.out.println( "\tComputed Mid: Rate: " + ( ( FXRate ) fxPrice.getComputedMid() ).getRate() +
                    " Spot: " + ( ( FXRate ) fxPrice.getComputedMid() ).getSpotRate() +
                    " Points: " + ( ( FXRate ) fxPrice.getComputedMid() ).getForwardPoints() );
        }


        System.out.println( "\tOffer: Rate: " + fxPrice.getOfferFXRate().getRate() +
                " Spot: " + fxPrice.getOfferFXRate().getSpotRate() +
                " Points: " + fxPrice.getOfferFXRate().getForwardPoints() );

        System.out.println();
    }

    protected FXRateConvention getFXRateConvention( String shortName )
    {
        return ( FXRateConvention ) this.getNamedEntity(
                FXRateConvention.class,
                shortName );
    }

    protected NamedEntity getNamedEntity( Class aClass, String shortName )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).equal( shortName );
        Object obj = null;
        if ( this.uow == null )
        {
            obj = this.getPersistenceSession().readObject( aClass, expr );
        }
        else
        {
            obj = this.uow.readObject( aClass, expr );
        }
        return ( NamedEntity ) obj;
    }
}
