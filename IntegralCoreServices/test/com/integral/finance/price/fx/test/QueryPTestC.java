package com.integral.finance.price.fx.test;


import com.integral.finance.calculator.Calculator;
import com.integral.finance.price.PriceTypeCalculator;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

public class QueryPTestC
        extends PTestCaseC
{
    public QueryPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new QueryPTestC( "listCalcs" ) );
        return suite;
    }

    public void listCalcs()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "shortName" ).equal( "Points" );

            Calculator calc = ( Calculator ) getPersistenceSession().readObject( PriceTypeCalculator.class, expr );
            // Calculator calc = (Calculator)getPersistenceSession().readObject(com.integral.finance.price.fx.FXSpreadPriceCalculatorC.class,expr);
            // Calculator calc = (Calculator)getPersistenceSession().readObject(com.integral.finance.price.fx.FXPriceTypeCalculator.class,expr);
            if ( calc != null )
            {
                log( "calc: " + calc.getShortName() );
            }
            else
            {
                log( "calc: NULL" );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "Exception in listCalcs: " + e );
        }

    }


}
