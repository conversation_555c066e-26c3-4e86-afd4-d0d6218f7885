package com.integral.finance.country.test;


import com.integral.finance.country.CountryFactory;
import com.integral.test.TestCaseC;

import java.util.Iterator;
import java.util.Locale;


public class CountryTestC
        extends TestCaseC
{
    static String name = "Country Test";

    public CountryTestC( String name )
    {
        super( name );
    }

    public void testJavaCountry()
    {
        log( "lookupTestJavaCountry" );
        try
        {
            String[] countries = Locale.getISOCountries();
            for ( int i = 0; i < countries.length; i++ )
            {
                String country = countries[i];
                log( "ISO 2-letter code   : " + country );

                Locale loc = new Locale( "US", country );
                log( "\tUS ISO 3-letter code: " + loc.getISO3Country() );
                log( "\tUS Display name     : " + loc.getDisplayCountry() );

                log( "\tFR ISO 3-letter code: " + loc.getISO3Country() );
                log( "\tFR Display name     : " + loc.getDisplayCountry( Locale.FRANCE ) );

                log( "\tGE ISO 3-letter code: " + loc.getISO3Country() );
                log( "\tGE Display name     : " + loc.getDisplayCountry( Locale.GERMAN ) );
            }

            log( "lookupTestJavaCountry" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTestJavaCountry" );
        }
    }

    public void testCountryFactory()
    {
        log( "lookupTestCountryFactory" );
        try
        {
            Iterator countries = CountryFactory.getISOCountries().iterator();
            while ( countries.hasNext() )
            {
                String country = ( String ) countries.next();
                log( "ISO 2-letter code       : " + country );
                log( " ISO 3-letter code      : " + CountryFactory.getCountryISO3Code( country ) );
                log( " display name (default) : " + CountryFactory.getCountryDisplayName( country ) );
                log( " display name (GERMAN)  : " + CountryFactory.getCountryDisplayName( country, Locale.GERMAN ) );
            }

            log( "lookupTestCountryFactory" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTestCountryFactory" );
        }
    }
}
