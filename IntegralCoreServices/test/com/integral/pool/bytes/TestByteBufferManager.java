package com.integral.pool.bytes;

import junit.framework.TestCase;

/**
 * Author: inder
 */
public class TestByteBufferManager extends TestCase {
    public TestByteBufferManager(String aName) {
        super(aName);
    }

    static int bufferSize = 8;
    static int numBuffers = 100;

    public void testAllocateBuffer() {
        ByteBufferMemoryManager bbMgr = new ByteBufferMemoryManager(9999, bufferSize, numBuffers);
        byte[] memoryBank = bbMgr.memoryBank;

        for (int i = 0; i < numBuffers; i++) {
            ByteBufferMemoryManager.ManagedByteBuffer nextBuff = bbMgr.getNextBuffer();
            assertEquals(1, memoryBank[i * bufferSize]);
            nextBuff.returnBuffer();
            assertEquals(0, memoryBank[i * bufferSize]);
        }

    }

    public void testAllocateBufferWrapAround() {
        ByteBufferMemoryManager bbMgr = new ByteBufferMemoryManager(9999, bufferSize, numBuffers);
        byte[] memoryBank = bbMgr.memoryBank;

        for (int i = 0; i < numBuffers; i++) {
            ByteBufferMemoryManager.ManagedByteBuffer nextBuff = bbMgr.getNextBuffer();
            assertEquals(1, memoryBank[i * bufferSize]);
            nextBuff.returnBuffer();
            assertEquals(0, memoryBank[i * bufferSize]);
        }

        //This should be an unmanaged buffer.
        ByteBufferMemoryManager.ManagedByteBuffer nextBuff = bbMgr.getNextBuffer();
        assertEquals(-1, nextBuff.memoryBankOffset);

        for (int i = 0; i < numBuffers; i++) {
            nextBuff = bbMgr.getNextBuffer();
            assertEquals(1, memoryBank[i * bufferSize]);
            nextBuff.returnBuffer();
            assertEquals(0, memoryBank[i * bufferSize]);
        }

        System.out.println("WrapAround success!!");

    }

    public void testAssertion() {
        ByteBufferMemoryManager bbMgr = new ByteBufferMemoryManager(9999, bufferSize, numBuffers);
        byte[] memoryBank = bbMgr.memoryBank;

        ByteBufferMemoryManager.ManagedByteBuffer nextBuff = bbMgr.getNextBuffer();
        nextBuff.returnBuffer();

        try {
            nextBuff.returnBuffer();
        }
        catch (RuntimeException e) {
            return;
        }

        assertTrue("Expected exception not caught!!", false);


    }

    public static void main(String[] args) {
        ByteBufferMemoryManager bbMgr = new ByteBufferMemoryManager(9999, 1024, 10000);

        long start = System.currentTimeMillis();
        for (int i=0; i < ********; i++) {
              ByteBufferMemoryManager.ManagedByteBuffer nextBuff = bbMgr.getNextBuffer();
            nextBuff.returnBuffer();
        }
        System.out.println(System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        for (int i=0; i < ********; i++) {
            byte[] b = new byte[1024];
            b = null;
        }
        System.out.println(System.currentTimeMillis() - start);
    }

    /**
     * Numbers are ******** executions:
     * 668
     * 5202
     */
}
