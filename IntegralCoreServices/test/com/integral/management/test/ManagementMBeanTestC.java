package com.integral.management.test;

import com.integral.management.util.ManagementMBeanC;
import com.integral.management.util.ManagementUtil;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class ManagementMBeanTestC extends MBeanTestCaseC
{
    ManagementMBeanC mgmtMBean = ( ManagementMBeanC ) ManagementUtil.getManagementMBean();

    public ManagementMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( mgmtMBean, "mgmtMonitoringEnabled", "IDC.management.monitoring.isEnabled", MBeanTestCaseC.BOOLEAN, true, false );
        testProperty( mgmtMBean, "fromAddress", "IDC.management.monitoring.Email.fromAddress", MBeanTestCaseC.STRING, true, false );
        testProperty( mgmtMBean, "toAddress", "IDC.management.monitoring.Email.ToAddress", MBeanTestCaseC.STRING, true, false );
        testProperty( mgmtMBean, "CCAddress", "IDC.management.monitoring.Email.CCAddress", MBeanTestCaseC.STRING, true, false );
        testProperty( mgmtMBean, "subject", "IDC.management.monitoring.Email.subject", MBeanTestCaseC.STRING, true, false );
    }
}
