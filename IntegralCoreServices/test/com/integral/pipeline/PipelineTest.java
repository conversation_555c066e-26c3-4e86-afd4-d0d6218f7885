package com.integral.pipeline;

import com.integral.pipeline.metrics.Metric;
import junit.framework.TestCase;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Jan 3, 2011
 * Time: 12:08:09 PM
 */
public class PipelineTest extends TestCase {

    public void testProcessesBlocked() throws Exception {
        int alertDurationSec = 1;

        final TestPipeline pipeline = new TestPipeline("testPipeline", new ProcessorDescriptor("testProcessDescriptor", new Processor() {
            @Override
            public PipelineMessage process(PipelineMessage msg) {
                try {
                    Thread.sleep(3000); //block for 3 secs.
                } catch (InterruptedException e) {
                    throw new PipelineException(e);
                }
                return msg;
            }
        }), null, alertDurationSec);

        Thread processThread = new Thread(new Runnable() {
            public void run() {
                pipeline.process(new PipelineMessage());
            }
        });
        processThread.start();
        Thread.sleep(3000);
        ArrayList<Metric> metrics = new ArrayList<Metric>();
        pipeline.collect(metrics);
        assertTrue("Pipeline not notified the blocked thread after metrics collection.", pipeline.isNotified());
        processThread.join();

        pipeline.reset();
        pipeline.collect(metrics);
        assertFalse("Pipeline should not be notified as no process was processed since last collection.", pipeline.isNotified());

        final TestPipeline pipeline2 = new TestPipeline("testPipeline2", new ProcessorDescriptor("testProcessDescriptor2", new Processor() {
            @Override
            public PipelineMessage process(PipelineMessage msg) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    throw new PipelineException(e);
                }
                return msg;
            }
        }), null, alertDurationSec);

        processThread = new Thread(new Runnable() {
            public void run() {
                pipeline2.process(new PipelineMessage());
            }
        });
        processThread.start();
        Thread.sleep(200);
        pipeline.collect(metrics);
        assertFalse("Pipeline notified for the non-blocked thread after metrics collection.", pipeline2.isNotified());
        processThread.join();
    }

    private class TestPipeline extends Pipeline {

        AtomicBoolean notified = new AtomicBoolean();

        /**
         * @param name
         */
        public TestPipeline(String name, ProcessorDescriptor head, ProcessorDescriptor errorHandler, int alertDuration) {
            super(name, head, errorHandler, alertDuration);
        }

        @Override
        protected void notifyProcessBlocked(long diffSeconds) {
            System.out.println("Pipeline notified. Name:"+this.getName()+", diffSeconds:"+diffSeconds);
            notified.set(true);
        }

        public boolean isNotified() {
            return notified.get();
        }

        public void reset() {
            notified.set(false);
        }
    }
}
