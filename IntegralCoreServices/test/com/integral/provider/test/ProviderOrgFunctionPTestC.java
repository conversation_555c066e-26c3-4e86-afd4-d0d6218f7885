package com.integral.provider.test;

import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.dealing.TradeChannelSupport;
import com.integral.finance.dealing.TradeChannelSupportC;
import com.integral.persistence.PersistenceFactory;
import com.integral.provider.ProviderOrgFunction;
import com.integral.provider.ProviderOrgFunctionC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

public class ProviderOrgFunctionPTestC extends PTestCaseC
{
    public static final String name = "ProviderOrgFunctionPTestC";

    public ProviderOrgFunctionPTestC( String name )
    {
        super( name );

    }

    public static void main( String[] args )
    {
        try
        {
            ProviderOrgFunctionPTestC test = new ProviderOrgFunctionPTestC( name );
            test.testBestQuoteStrategy();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void testBestQuoteStrategy()
    {
        try
        {
            IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            IdcSessionManager.getInstance().setSessionContext( sessContext );
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( TradingPartyC.class );

            ProviderOrgFunction test1 = ( ProviderOrgFunction ) uow.registerObject( new ProviderOrgFunctionC() );
            ProviderOrgFunction test2 = ( ProviderOrgFunction ) uow.registerObject( new ProviderOrgFunctionC() );
            ProviderOrgFunction test3 = ( ProviderOrgFunction ) uow.registerObject( new ProviderOrgFunctionC() );
            test1.setBestQuoteStrategy( ProviderOrgFunction.NEW_QUOTE_BETTER_PRICE );
            test2.setBestQuoteStrategy( ProviderOrgFunction.SAME_QUOTE );
            uow.commit();
            test1 = ( ProviderOrgFunction ) session.refreshObject( test1 );
            test2 = ( ProviderOrgFunction ) session.refreshObject( test2 );
            test3 = ( ProviderOrgFunction ) session.refreshObject( test3 );
            log( "ProviderOrgFunction=" + test1 + ",test1.isAnonymous=" + test1.getBestQuoteStrategy() );
            log( "ProviderOrgFunction=" + test2 + ",test1.isAnonymous=" + test2.getBestQuoteStrategy() );
            assertEquals( "testBestQuoteStrategy should be 2 .", test1.getBestQuoteStrategy(), ProviderOrgFunction.NEW_QUOTE_BETTER_PRICE );
            assertEquals( "testBestQuoteStrategy should be 0 .", test2.getBestQuoteStrategy(), ProviderOrgFunction.SAME_QUOTE );
            assertEquals( "Default value", test3.getBestQuoteStrategy(), ProviderOrgFunction.NEW_QUOTE_SAME_PRICE );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testTradeChannelSupportFlags()
    {
        try
        {
            TradeChannelSupport tcs = new TradeChannelSupportC();


            assertTrue( tcs.isESPSpotSupported() );
            assertFalse( tcs.isESPOutrightSupported() );
            assertFalse( tcs.isESPSwapSupported() );
            assertFalse( tcs.isRFSSpotSupported() );
            assertFalse( tcs.isRFSOutrightSupported() );
            assertFalse( tcs.isRFSSwapSupported() );
            assertFalse( tcs.isNDFSupported() );
            assertFalse( tcs.isFSRSupported() );
            assertFalse( tcs.isSSPSupported() );


            tcs.setESPSpotSupported( true );
            log( "tradeChannelSupport=" + tcs );

            // check the support flag is set correctly
            assertTrue( tcs.isESPSpotSupported() );
            assertFalse( tcs.isESPOutrightSupported() );
            assertFalse( tcs.isESPSwapSupported() );
            assertFalse( tcs.isRFSSpotSupported() );
            assertFalse( tcs.isRFSOutrightSupported() );
            assertFalse( tcs.isRFSSwapSupported() );
            assertFalse( tcs.isNDFSupported() );
            assertFalse( tcs.isFSRSupported() );
            assertFalse( tcs.isSSPSupported() );

            // now check double setting.
            tcs.setESPSpotSupported( true );
            assertTrue( tcs.isESPSpotSupported() );
            assertFalse( tcs.isESPOutrightSupported() );
            assertFalse( tcs.isESPSwapSupported() );
            assertFalse( tcs.isRFSSpotSupported() );
            assertFalse( tcs.isRFSOutrightSupported() );
            assertFalse( tcs.isRFSSwapSupported() );
            assertFalse( tcs.isNDFSupported() );
            assertFalse( tcs.isFSRSupported() );
            assertFalse( tcs.isSSPSupported() );

            // now set the value as false
            tcs.setESPSpotSupported( false );
            assertFalse( tcs.isESPSpotSupported() );
            assertFalse( tcs.isESPOutrightSupported() );
            assertFalse( tcs.isESPSwapSupported() );
            assertFalse( tcs.isRFSSpotSupported() );
            assertFalse( tcs.isRFSOutrightSupported() );
            assertFalse( tcs.isRFSSwapSupported() );
            assertFalse( tcs.isNDFSupported() );
            assertFalse( tcs.isFSRSupported() );
            assertFalse( tcs.isSSPSupported() );

            // now set the values of false again.
            tcs.setESPSpotSupported( false );
            assertFalse( tcs.isESPSpotSupported() );
            assertFalse( tcs.isESPOutrightSupported() );
            assertFalse( tcs.isESPSwapSupported() );
            assertFalse( tcs.isRFSSpotSupported() );
            assertFalse( tcs.isRFSOutrightSupported() );
            assertFalse( tcs.isRFSSwapSupported() );
            assertFalse( tcs.isNDFSupported() );
            assertFalse( tcs.isFSRSupported() );
            assertFalse( tcs.isSSPSupported() );

            // now set all the values to true
            tcs.setESPSpotSupported( true );
            tcs.setESPOutrightSupported( true );
            tcs.setESPSwapSupported( true );
            tcs.setRFSSpotSupported( true );
            tcs.setRFSOutrightSupported( true );
            tcs.setRFSSwapSupported( true );
            tcs.setRFSSpotSupported( true );
            tcs.setNDFSupported( true );
            tcs.setFSRSupported( true );
            tcs.setSSPSupported( true );
            assertTrue( tcs.isESPSpotSupported() );
            assertTrue( tcs.isESPOutrightSupported() );
            assertTrue( tcs.isESPSwapSupported() );
            assertTrue( tcs.isRFSSpotSupported() );
            assertTrue( tcs.isRFSOutrightSupported() );
            assertTrue( tcs.isRFSSwapSupported() );
            assertTrue( tcs.isNDFSupported() );
            assertTrue( tcs.isFSRSupported() );
            assertTrue( tcs.isSSPSupported() );

            // do a mix of true and false
            tcs.setESPSpotSupported( true );
            tcs.setESPOutrightSupported( false );
            tcs.setESPSwapSupported( true );
            tcs.setRFSSpotSupported( false );
            tcs.setRFSOutrightSupported( true );
            tcs.setRFSSwapSupported( false );
            tcs.setNDFSupported( true );
            tcs.setFSRSupported( false );
            tcs.setSSPSupported( true );
            assertTrue( tcs.isESPSpotSupported() );
            assertFalse( tcs.isESPOutrightSupported() );
            assertTrue( tcs.isESPSwapSupported() );
            assertFalse( tcs.isRFSSpotSupported() );
            assertTrue( tcs.isRFSOutrightSupported() );
            assertFalse( tcs.isRFSSwapSupported() );
            assertTrue( tcs.isNDFSupported() );
            assertFalse( tcs.isFSRSupported() );
            assertTrue( tcs.isSSPSupported() );
        }
        catch ( Exception e )
        {
            fail( "testTradeChannelSupportFlags.exception", e );
        }
    }

    public void testFieldsPersistence()
    {
        try
        {
            Session session = getPersistenceSession();
            org.eclipse.persistence.sessions.UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeReadOnlyClass( ProviderOrgFunctionC.class );

            ProviderOrgFunction testPof = new ProviderOrgFunctionC();
            ProviderOrgFunction regTestPof = ( ProviderOrgFunction ) uow.registerObject( testPof );
            TradeChannelSupport trdChnlSup = new TradeChannelSupportC();
            trdChnlSup.setRFSSpotSupported( true );
            trdChnlSup.setESPSwapSupported( true );
            trdChnlSup.setNDFSupported( true );
            trdChnlSup.setFSRSupported( true );
            trdChnlSup.setSSPSupported( true );

            regTestPof.setTradeChannelSupport( trdChnlSup );
            CurrencyPairGroup oneClickCcyPairGrp = CurrencyFactory.newCurrencyPairGroup();
            CurrencyPairGroup regOneClickCcyPairGrp = ( CurrencyPairGroup ) uow.registerObject( oneClickCcyPairGrp );
            regOneClickCcyPairGrp.setShortName( "TESTOneCcy" + System.currentTimeMillis() );
            regTestPof.setOneClickCurrencyPairGroup( regOneClickCcyPairGrp );
            CurrencyPairGroup rfsCcyPairGrp = CurrencyFactory.newCurrencyPairGroup();
            CurrencyPairGroup regRfsCcyPairGrp = ( CurrencyPairGroup ) uow.registerObject( rfsCcyPairGrp );
            regRfsCcyPairGrp.setShortName( "TESTRFS" + System.currentTimeMillis() );
            regTestPof.setRFSCurrencyPairGroup( regRfsCcyPairGrp );
            uow.commit();

            testPof.setTradeChannelSupport( null );
            // refresh the trade and retrieve the maker request.
            testPof = ( ProviderOrgFunction ) session.refreshObject( testPof );
            log( "testPof=" + testPof + ",testOrgRel.tradeChannelSupport=" + testPof.getTradeChannelSupport() + ",oneClickCcyPairGrp=" + testPof.getOneClickCurrencyPairGroup() + ",rfsCcyPairGrp=" + testPof.getRFSCurrencyPairGroup() );
            assertEquals( "orig trade channel support should not be null.", testPof.getTradeChannelSupport() != null, true );
            TradeChannelSupport refreshedTrdChnlSupport = testPof.getTradeChannelSupport();
            assertTrue( refreshedTrdChnlSupport.isESPSpotSupported() );
            assertFalse( refreshedTrdChnlSupport.isESPOutrightSupported() );
            assertTrue( refreshedTrdChnlSupport.isESPSwapSupported() );
            assertTrue(refreshedTrdChnlSupport.isESPSupported());
            assertTrue( refreshedTrdChnlSupport.isRFSSpotSupported() );
            assertFalse( refreshedTrdChnlSupport.isRFSOutrightSupported() );
            assertFalse( refreshedTrdChnlSupport.isRFSSwapSupported() );
            assertTrue( refreshedTrdChnlSupport.isNDFSupported() );
            assertTrue( refreshedTrdChnlSupport.isFSRSupported() );
            assertTrue( refreshedTrdChnlSupport.isSSPSupported() );
            assertTrue(refreshedTrdChnlSupport.isRFSSupported());
            assertEquals( "one click ccy pair group should not be null.", testPof.getOneClickCurrencyPairGroup() != null, true );
        }
        catch ( Exception e )
        {
            fail( "Exception in fields persistence", e );
        }
    }
}

