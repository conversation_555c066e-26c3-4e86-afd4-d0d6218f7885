package com.integral.is.common.mbean;

import com.integral.system.configuration.ConfigurationProperty;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

public class QuoteConventionMBeanPTestC extends MBeanTestCaseC
{
    QuoteConventionMBeanC quoteConvMBean = ( QuoteConventionMBeanC ) QuoteConventionMBeanC.getInstance();

    public QuoteConventionMBeanPTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( quoteConvMBean, "defaultQuoteConvention", QuoteConventionMBean.IDC_IS_DEFAULT_QUOTE_CONVENTION, MBeanTestCaseC.STRING );
        testProperty( quoteConvMBean, "useBrokerForCustomerQuoteConvention", QuoteConventionMBean.IDC_IS_USE_BROKERFORCUSTOMER_QUOTE_CONVENTION, MBeanTestCaseC.BOOLEAN );
    }

    public void testUseBrokerForCustomerQuoteConvention()
    {
        try
        {
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( null ) );
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org1 ) );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org2 ) );

            // now set the global property
            quoteConvMBean.setProperty( QuoteConventionMBean.IDC_IS_USE_BROKERFORCUSTOMER_QUOTE_CONVENTION, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( quoteConvMBean.isUseBrokerForCustomerQuoteConvention() );
            assertTrue( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org1 ) );
            assertTrue( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org2 ) );
            assertTrue( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( null ) );

            quoteConvMBean.setProperty( QuoteConventionMBean.IDC_IS_USE_BROKERFORCUSTOMER_QUOTE_CONVENTION, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention() );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org1 ) );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org2 ) );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( null ) );


            quoteConvMBean.setProperty( QuoteConventionMBean.IDC_IS_USE_BROKERFORCUSTOMER_QUOTE_CONVENTION_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            quoteConvMBean.setProperty( QuoteConventionMBean.IDC_IS_USE_BROKERFORCUSTOMER_QUOTE_CONVENTION_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention() );
            assertTrue( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org1 ) );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org2 ) );

            quoteConvMBean.setProperty( QuoteConventionMBean.IDC_IS_USE_BROKERFORCUSTOMER_QUOTE_CONVENTION, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( quoteConvMBean.isUseBrokerForCustomerQuoteConvention() );
            assertTrue( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org1 ) );
            assertFalse( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( org2 ) );
            assertTrue( quoteConvMBean.isUseBrokerForCustomerQuoteConvention( null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testUseBrokerForCustomerQuoteConvention" );
        }
    }

}
