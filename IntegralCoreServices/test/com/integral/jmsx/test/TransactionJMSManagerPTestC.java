package com.integral.jmsx.test;


import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.jmsx.IJMSManager;
import com.integral.jmsx.JMSManagerFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import javax.jms.TextMessage;


/**
 * Tests the transactional jms manager framework.
 */
public class TransactionJMSManagerPTestC
        extends PTestCaseC
{
    static String name = "TransactionalJMSManager Test";

    public TransactionJMSManagerPTestC( String name )
    {
        super( name );
    }

    /**
     * Tests the commit feature of the transactional jms manager
     * This test creates an audit event.
     */
    public void testCommit()
    {
        // create audit event
        AuditEvent event = new AuditEventC( "TEST", "TransactionalJMSManagerTest" );

        // start a transaction
        IdcSessionContext ctx = null;
        IdcTransaction xact = null;
        try
        {
            ctx = IdcSessionManager.getInstance().getSessionContext( "Integral" );
            xact = IdcSessionManager.getInstance().newTransaction( ctx );
        }
        catch ( Exception e )
        {
            fail( "error starting transaction: " + e );
        }

        // create a transactional JMS message
        IJMSManager mgr = null;
        try
        {
            mgr = JMSManagerFactory.getJMSManager();
            TextMessage msg = mgr.createTextMessage( "TOPIC.AUDIT" );
            msg.setText( "TEST: TransactionalJMSManagerTest" );
            mgr.sendMessage( "TOPIC.AUDIT", msg );
        }
        catch ( Exception e )
        {
            fail( "Error creating jms message: ", e );
        }

        // register entity and commit new entity
        try
        {
            log.info( "start commiting new audit object" + " with hashCode " + event.hashCode() );
            Session session = getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.removeAllReadOnlyClasses();
            AuditEvent event1 = ( AuditEvent ) uow.registerObject( event );
            uow.commit();
            log.info( "  completed commiting new audit object" + " with hashCode " + event.hashCode() );
            // TBD: add auto check of jms message here
        }
        catch ( Exception e )
        {
            fail( "Error commiting AuditEvent instance: ", e );
        }

        // commit transaction
        try
        {
            xact.commit();
        }
        catch ( Exception e )
        {
            fail( "Error committing transaction: ", e );
        }

    }

}
