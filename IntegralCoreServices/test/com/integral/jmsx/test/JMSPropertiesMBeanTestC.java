package com.integral.jmsx.test;

import com.integral.jmsx.JMSPropertiesMBean;
import com.integral.jmsx.JMSPropertiesMBeanC;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class JMSPropertiesMBeanTestC extends MBeanTestCaseC
{
    JMSPropertiesMBeanC JMSMBean = ( JMSPropertiesMBeanC ) JMSPropertiesMBeanC.getInstance();

    public JMSPropertiesMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( JMSMBean, "defaultBrokerName", JMSPropertiesMBean.DEFAULT_BROKERID_KEY, MBeanTestCaseC.STRING );
        testProperty( JMSMBean, "defaultTopicSeperator", JMSPropertiesMBean.DEFAULT_BROKER_TOPICSEPERATOR_KEY, MBeanTestCaseC.STRING );
        testProperty( JMSMBean, "defaultTopicWildCard", JMSPropertiesMBean.DEFAULT_BROKER_TOPICWILDCARD_KEY, MBeanTestCaseC.STRING );
        testProperty( J<PERSON><PERSON><PERSON>, "monitorInteval", JMSPropertiesMBean.DEFAULT_MONITOR_INTERVAL_KEY, MBeanTestCaseC.INTEGER, false, true );
        testProperty( JMSMBean, "systemProperties", JMSPropertiesMBean.SYSTEM_PROPERTIES_KEY, MBeanTestCaseC.STRING, true, false );
        testProperty( JMSMBean, "providerURL", JMSPropertiesMBean.PROVIDER_URL_KEY, MBeanTestCaseC.STRING );
        testProperty( JMSMBean, "initialContextFactory", JMSPropertiesMBean.INITIALCONTEXTFACTORY_KEY, MBeanTestCaseC.STRING );
    }
}
