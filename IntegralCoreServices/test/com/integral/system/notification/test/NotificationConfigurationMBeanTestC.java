package com.integral.system.notification.test;

import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.notification.configuration.NotificationConfiguration;
import com.integral.system.notification.configuration.NotificationConfigurationFactory;
import com.integral.system.notification.configuration.NotificationConfigurationMBean;
import com.integral.test.MBeanTestCaseC;

import java.util.Collection;

/**
 * <AUTHOR> Development Corporation.
 */
public class NotificationConfigurationMBeanTestC extends MBeanTestCaseC
{
    NotificationConfiguration notificationMBean = ( NotificationConfiguration ) NotificationConfigurationFactory.getNotificationConfigurationMBean();

    public NotificationConfigurationMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        try
        {
            testProperty( notificationMBean, "notificationServiceEnabled", NotificationConfigurationMBean.NOTIFICATION_SERVICE_ENABLED, MBeanTestCaseC.BOOLEAN );
            testProperty( notificationMBean, "notificationSenderThreadPoolMaxCount", NotificationConfigurationMBean.NOTIFICATION_SENDER_THREAD_POOL_MAX_COUNT, MBeanTestCaseC.INTEGER );
            testProperty( notificationMBean, "notificationSenderThreadPoolMinCount", NotificationConfigurationMBean.NOTIFICATION_SENDER_THREAD_POOL_MIN_COUNT, MBeanTestCaseC.INTEGER );
            testProperty( notificationMBean, "configNotificationEnabled", NotificationConfigurationMBean.CONFIG_NOTIFICATION_ENABLED, MBeanTestCaseC.BOOLEAN );
            testProperty( notificationMBean, "JMSNotificationSenderThreadPoolMaxCount", NotificationConfigurationMBean.JMS_NOTIFICATION_SENDER_THREAD_POOL_MAX_COUNT, MBeanTestCaseC.INTEGER );
            testProperty( notificationMBean, "JMSNotificationSenderThreadPoolMinCount", NotificationConfigurationMBean.JMS_NOTIFICATION_SENDER_THREAD_POOL_MIN_COUNT, MBeanTestCaseC.INTEGER );
            testProperty( notificationMBean, "JMSNotificationSenderThreadPoolQueueSize", NotificationConfigurationMBean.JMS_NOTIFICATION_SENDER_THREAD_POOL_QUEUE_SIZE, MBeanTestCaseC.INTEGER );
            testProperty( notificationMBean, "remoteJMSNotificationEnabled", NotificationConfigurationMBean.REMOTE_JMS_NOTIFICATION_SERVICE_ENABLED, MBeanTestCaseC.BOOLEAN );
        }
        catch ( Exception e )
        {
            fail( "testProperties" );
        }
    }
}
