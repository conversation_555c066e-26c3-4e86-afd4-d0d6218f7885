package com.integral.system.timer.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.util.TimerTask;

class MyTimerTask extends TimerTask
{
    protected Log log = LogFactory.getLog( this.getClass() );
    private String msg;

    public MyTimerTask( String msg )
    {
        this.msg = msg;
    }

    public void run()
    {
        log.debug( "got util event #" + msg );
    }
}

