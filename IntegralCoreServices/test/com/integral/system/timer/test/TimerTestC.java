package com.integral.system.timer.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import junit.framework.TestCase;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Observable;
import java.util.Observer;
import java.util.TimerTask;

public class TimerTestC extends TestCase
        implements Observer, ActionListener
{
    static String name = "IDC Timer Test";
    private int num;
    private String mode;

    protected Log log = LogFactory.getLog( this.getClass() );

    public TimerTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        try
        {
            int num = 0;
            String mode = "all";
            for ( int i = 0; i < args.length; i++ )
            {
                if ( args[i].equalsIgnoreCase( "-num" ) )
                {
                    i++;
                    num = new Integer( args[i] );
                }
                else if ( args[i].equalsIgnoreCase( "-mode" ) )
                {
                    i++;
                    mode = args[i];
                }
            }

            TimerTestC test = new TimerTestC( name );
            test.setNum( num );
            test.setMode( mode );
            test.testPerform();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void setNum( int aNum )
    {
        this.num = aNum;
    }

    public void setMode( String aMode )
    {
        this.mode = aMode;
    }

    public void testPerform()
    {
        if ( "idc".equalsIgnoreCase( mode ) )
        {
            runIdcTimerTest();
        }
        else if ( "swing".equalsIgnoreCase( mode ) )
        {
            runSwingTimerTest();
        }
        else if ( "util".equalsIgnoreCase( mode ) )
        {
            runUtilTimerTest();
        }
        else
        {
            runIdcTimerTest();
            runSwingTimerTest();
            runUtilTimerTest();
        }

        try
        {
            Thread.sleep( 30000 );
        }
        catch ( Exception e )
        {
            log.debug( "sleep interrupted" );
        }
    }

    public void runIdcTimerTest()
    {
        log.debug( "IDC Timer: com.integral.system.timer.Timer" );
        long[] startTimes = {1000, 500, 750, 250};
        long t1 = System.currentTimeMillis();

        com.integral.system.timer.Timer timer = com.integral.system.timer.Timer.getInstance();
        for ( int i = 0; i < num; i++ )
        {
            long time = ( i < startTimes.length ) ? startTimes[i] : i * 2000;
            String msg = "Test-" + i + " Start time " + time;

            /*
            if ((i % 1000) == 0)
            {
                t2 = System.currentTimeMillis() - t2;
                log.debug("\ttime to insert #" + 1000 + " timers (total=" + i + "): " + t2 + "msec");
                t2 = System.currentTimeMillis();
            }
            */

            timer.addRequest( this, time, msg );
        }
        t1 = System.currentTimeMillis() - t1;
        log.debug( "\ttime to insert #" + num + " timers: " + t1 + "msec" );
    }

    public void runSwingTimerTest()
    {
        log.debug( "JDK Timer: javax.swing.Timer" );
        long t1 = System.currentTimeMillis();
        int t = 10000;
        for ( int i = 0; i < num; i++ )
        {
            t++;
            javax.swing.Timer timer = new javax.swing.Timer( t, this );
            timer.start();
        }
        t1 = System.currentTimeMillis() - t1;
        log.debug( "\ttime to insert #" + num + " timers: " + t1 + "msec" );
    }

    public void runUtilTimerTest()
    {
        log.debug( "JDK Timer: java.util.Timer" );

        // timer setup
        java.util.Timer timer = new java.util.Timer( true );

        long t1 = System.currentTimeMillis();
        int t = 10000;
        for ( int i = 0; i < num; i++ )
        {
            t++;
            String msg = "Test-" + i;

            /*
            if ((i % 1000) == 0)
            {
                t2 = System.currentTimeMillis() - t2;
                log.debug("\ttime to insert #" + 1000 + " timers (total=" + i + "): " + t2 + "msec");
                t2 = System.currentTimeMillis();
            }
            */

            TimerTask tt = new MyTimerTask( msg );
            timer.schedule( tt, t );

            if ( i % 2 == 0 )
            {
                tt.cancel();
            }
        }
        t1 = System.currentTimeMillis() - t1;
        log.debug( "\ttime to insert #" + num + " timers: " + t1 + "msec" );
    }

    public void update( Observable o, Object arg )
    {
        if ( arg instanceof String )
        {
            log.debug( "got IDC Event #" + arg );
        }
    }

    public void actionPerformed( ActionEvent e )
    {
        log.debug( "got Swing Event #" + e );
    }

}
