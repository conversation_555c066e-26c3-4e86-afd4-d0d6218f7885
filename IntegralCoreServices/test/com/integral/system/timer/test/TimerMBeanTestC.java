package com.integral.system.timer.test;

import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.timer.config.Timer;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class TimerMBeanTestC extends MBeanTestCaseC
{
    Timer timerMBean = ( Timer ) ConfigurationFactory.getTimerMBean();

    public TimerMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( timerMBean, "scheduleWithinSeconds", "Scheduler.ScheduleWithin.Seconds", MBeanTestCaseC.INTEGER );
        testProperty( timerMBean, "scheduleEventLoaderName", "Scheduler.LoadScheduleEvents.Name", MBeanTestCaseC.STRING );
        testProperty( timerMBean, "extraSchedulerModuleNames", "Scheduler.Extra.Module.Names", MBeanTestCaseC.STRING );
        testProperty( timerMBean, "defaultSchedulerModuleName", "Scheduler.Default.Module.Name", MBeanTestCaseC.STRING );
        testProperty( timerMBean, "persistedTimeZoneEnabled", "Scheduler.UsePersistedTimeZone", MBeanTestCaseC.BOOLEAN );
        testProperty( timerMBean, "futureEventUpdateEnabled", "Scheduler.UpdateFutureEvent", MBeanTestCaseC.BOOLEAN );
        testProperty( timerMBean, "futureEventUpdateTolerance", "Scheduler.FutureEventTolerance", MBeanTestCaseC.INTEGER );
    }
}
