package com.integral.system.mail.test;

import com.integral.alert.AlertLoggerFactory;
import com.integral.is.log.MessageLogger;
import com.integral.system.mail.SendEmailC;
import com.integral.test.TestCaseC;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.internet.MimeBodyPart;

/**
 * Unit tests for sending email
 */
public class MailTestC extends TestCaseC
{
    static String name = "Mail Test";

    public MailTestC( String aName )
    {
        super( aName );
        AlertLoggerFactory.setLogger(MessageLogger.getInstance());
    }

    /**
     * Test sending emails to valid email addresses
     */
    public void testSendValidMail()
    {
        // to, subject, body
        try
        {
            SendEmailC.sendEmail( "<EMAIL>",
                    "Test1",
                    "Line1\nLine2\nto,subject,body" );
        }
        catch ( Exception e )
        {
            fail( "sending email 1" );
        }

        // to, cc, subject, body
        try
        {
            SendEmailC.sendEmail( "<EMAIL>", "<EMAIL>",null,
                    "Test2",
                    "Line1\nLine2\nto,cc,subject,body" );
        }
        catch ( Exception e )
        {
            fail( "sending email 2" );
        }

        // from, to, cc, subject, body
        try
        {
            SendEmailC.sendEmail( "<EMAIL>",
                    "<EMAIL>", "<EMAIL>",
                    "Test3",
                    "Line1\nLine2\nfrom,to,cc,subject,body" );
        }
        catch ( Exception e )
        {
            fail( "sending email 3" );
        }
    }

    /**
     * Test sending emails to invalid email addresses
     * Commented out since  SendEmailC doesnt throw exception for SMTP connection exception
     */
    /* public void testSendInvalidMail()
    {
        // to, subject, body
        try
        {
            SendEmailC.sendEmail( "<EMAIL>",
                    "Test1",
                    "Line1\nLine2\nto,subject,body" );
            fail( "sending invalid email 1" );
        }
        catch ( Exception e )
        {
        }
    }*/

    /**
     * Test sending multi-part emails to valid email addresses
     */
    public void testSendMultiPartMail()
    {
        try
        {
            String body = "Multi-Part Test Email";

            BodyPart bodyPart1 = new MimeBodyPart();
            bodyPart1.setContent( body, "text/html" );

            BodyPart bodyPart2 = new MimeBodyPart();
            DataSource source2 = new FileDataSource( "IntegralCoreDomain/test/resources/logo.png" );
            bodyPart2.setDataHandler( new DataHandler( source2 ) );
            bodyPart2.setHeader( "Content-ID", "logo" );

            BodyPart bodyPart3 = new MimeBodyPart();
            DataSource source3 = new FileDataSource( "IntegralCoreDomain/test/resources/logo.png" );
            bodyPart3.setDataHandler( new DataHandler( source3 ) );
            bodyPart3.setHeader( "Content-ID", "fxi" );

            BodyPart[] bodyParts = new BodyPart[3];
            bodyParts[0] = bodyPart1;
            bodyParts[1] = bodyPart2;
            bodyParts[2] = bodyPart3;

            SendEmailC.sendEmail( true,null,"<EMAIL>",null,
                    "Multi-Part-Test1",
                    bodyParts,null );
        }
        catch ( Exception e )
        {
            fail( "sending email 1" );
        }
    }


}
