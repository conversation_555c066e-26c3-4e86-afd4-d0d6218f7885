package com.integral.system.mail.test;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.startup.WatchPropertyC;
import com.integral.system.mail.SendEmailC;
import com.integral.test.TestCaseC;

public class SendEmailTestC extends TestCaseC
{
    static String name = "SendEmailPTestC Test";
    static int number = 5;
    static long delay = 10;
    static long shutdownDelay = 0;

    static Log log = LogFactory.getLog( SendEmailTestC.class );

    public SendEmailTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {

        WatchPropertyC watch = new WatchPropertyC();
        watch.startup( null, null );

        SendEmailTestC test = new SendEmailTestC( name );
        if ( args.length > 0 )
        {
            number = Integer.parseInt( args[0] );
            delay = Integer.parseInt( args[3] );
            shutdownDelay = Integer.parseInt( args[4] );
        }
        test.sendTestEmail( args[1], args[2] );
    }

    public void testEmpty()
    {

    }

    public void sendTestEmail( String fromEmailAddress, String toEmailAddress )
    {
        try
        {
            log.warn( "Total email number to be sent =" + number );
            long start = System.currentTimeMillis();

            for ( int i = 0; i < number; i++ )
            {
                String subject = "Test mail number: " + i;
                String body = subject;
                Thread.sleep( delay );
                SendEmailC.sendEmail( fromEmailAddress, toEmailAddress, null, subject, body );
                log.warn( "Finish sending email number:" + i );
            }
            long end1 = System.currentTimeMillis();

            System.out.println( "Finished calling SendEmailC.sendEmail in ms=" + ( end1 - start ) );

            SendEmailC.getEventsPool().shutdown();

            /*   long end = System.currentTimeMillis();

             long min = Long.MAX_VALUE;
             long max = Long.MIN_VALUE;
             double avg = 0;

             for(Long l : SendEmailThread.getStatistics()){
                 if(l > max)
                     max = l;
                 if(l < min)
                     min = l;
                 avg += l;
             }

             avg = avg/SendEmailThread.getStatistics().size();

             log.warn( "Min time in ms= " +  min*1.0/(1000*1000));
             log.warn( "Max time in ms= " +  max*1.0/(1000*1000));
             log.warn( "Avg time in ms= " +  avg/(1000*1000));
             log.warn( "Total time elapsed in ms= " +  (end-start));
            */
            Thread.sleep( shutdownDelay );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }
}
