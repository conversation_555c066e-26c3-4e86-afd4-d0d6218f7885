package com.integral.system.data.test;


import com.integral.persistence.Entity;
import com.integral.system.data.DataSetC;
import com.integral.system.data.DataSetObject;
import com.integral.system.data.DataSetObjectC;
import com.integral.test.PTestCaseC;
import com.integral.user.DisplayPreferenceC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.Iterator;
import java.util.Vector;


public class DataSetPTestC
        extends PTestCaseC
{
    static String name = "DataSetC Test";
    Vector users = null;

    public DataSetPTestC( String name )
    {
        super( name );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
//		suite.addTest(new DataSetPTestC("insertTest"));
        suite.addTest( new DataSetPTestC( "lookupTest" ) );
        return suite;
    }

    public void lookupTest()
    {
        log( "lookupTest" );
        try
        {
            Vector objs = ( Vector ) getPersistenceSession().readAllObjects( DataSetC.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                DataSetC ds = ( DataSetC ) objs.elementAt( i );
                log( "\t Data set object id is - " + ds.getObjectID() +
                        " short name is - " + ds.getShortName() );


                Collection col = ds.getObjects();
                Iterator iter = col.iterator();

                while ( iter.hasNext() )
                {
                    DataSetObject dso = ( DataSetObject ) iter.next();
                    Object obj = dso.getObject();
                    log( "\t\t Object is of type " +
                            ( ( null == obj ) ?
                                    "null" :
                                    ( obj.getClass().getName() ) + " id is " + ( ( Entity ) obj ).getObjectId() ) );
                }
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTest" );
        }
    }

    public void insertTest()
    {
        log( "insertTest" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            DataSetC ds = new DataSetC();
            ds.setShortName( "test1" );
            ds.setLongName( "test1" );
            uow.registerNewObject( ds );

            DisplayPreferenceC up = new DisplayPreferenceC();
            up.setDateFormatPattern( "dd-MMM-yy" );
            up.setDecimalAmountSeparator( "," );
            uow.registerNewObject( up );

            DataSetObjectC dso = new DataSetObjectC();
            dso.setObject( up );

            ds.addObject( dso );

            uow.commit();

            log( "ds param objectID = " + ds.getObjectID() );

            log( "insertTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }
}
