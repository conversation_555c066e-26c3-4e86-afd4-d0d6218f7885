package com.integral.system.security.test;

import com.integral.test.TestCaseC;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Provider;
import java.security.Security;
import java.util.Enumeration;


/**
 * Created by IntelliJ IDEA.
 * User: pandit
 * Date: Mar 26, 2008
 * Time: 4:03:35 PM
 * To change this template use File | Settings | File Templates.
 */
public class CryptoTestC extends TestCaseC
{

    public CryptoTestC( String name )
    {
        super( name );
    }


    public void testAlgorithm()
    {
        String password = "abcd1234efgh5678";
        String msg = "This is Test Message.";
        try
        {
            String encString = encrypt( msg, password );
            System.out.println( "encString = " + encString );
            String decrypted = decrypt( encString, password );
            if ( !msg.equals( decrypted ) )
            {
                fail( "Issues with decryption. Unable to decrypt the message properly." );
            }
        }
        catch ( Exception e )
        {
            fail( "Encryption / Decryption failed." );
        }
    }

    public void testAlgorithm1()
    {
        String password = "abcd";
        String msg = "This is Test Message.";
        try
        {
            String decrypted = decrypt( "PstO3jh+XaJawM42YM4+LESsK+r5KU/6q4dhnLIrlmM=", password );
            if ( msg.equals( decrypted ) )
            {
                //message encrypted using password abcd1234efgh5678 can be descrypted using password abcd. Hence failing
                fail( "message encrypted using password abcd1234efgh5678 can be descrypted using password abcd. Hence failing" );
            }
        }
        catch ( Exception e )
        {
        }
    }

    public static String encrypt( String text, String password ) throws Exception
    {
        Cipher cipher = Cipher.getInstance( "AES/CBC/PKCS5Padding" );

        //setup key
        byte[] keyBytes = new byte[16];
        byte[] b = password.getBytes( "UTF-8" );
        int len = b.length;
        if ( len > keyBytes.length )
        {
            len = keyBytes.length;
        }
        System.arraycopy( b, 0, keyBytes, 0, len );

        SecretKeySpec keySpec = new SecretKeySpec( keyBytes, "AES" );

        //the below may make this less secure, hard code byte array the IV in both java and .net clients
        IvParameterSpec ivSpec = new IvParameterSpec( keyBytes );

        cipher.init( Cipher.ENCRYPT_MODE, keySpec, ivSpec );
        byte[] results = cipher.doFinal( text.getBytes( "UTF-8" ) );
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode( results );
    }

    public static String decrypt( String text, String password ) throws Exception
    {
        Cipher cipher = Cipher.getInstance( "AES/CBC/PKCS5Padding" );

        //setup key
        byte[] keyBytes = new byte[16];
        byte[] b = password.getBytes( "UTF-8" );
        int len = b.length;
        if ( len > keyBytes.length )
        {
            len = keyBytes.length;
        }
        System.arraycopy( b, 0, keyBytes, 0, len );
        SecretKeySpec keySpec = new SecretKeySpec( keyBytes, "AES" );
        IvParameterSpec ivSpec = new IvParameterSpec( keyBytes );
        cipher.init( Cipher.DECRYPT_MODE, keySpec, ivSpec );

        BASE64Decoder decoder = new BASE64Decoder();
        byte[] results = cipher.doFinal( decoder.decodeBuffer( text ) );
        return new String( results, "UTF-8" );


    }


    public void testAlgorithms()
    {
        try
        {
            Provider p[] = Security.getProviders();
            for ( int i = 0; i < p.length; i++ )
            {
                System.out.println( p[i] );
                for ( Enumeration e = p[i].keys(); e.hasMoreElements(); )
                {
                    System.out.println( "\t" + e.nextElement() );
                }
            }
        }
        catch ( Exception e )
        {
            System.out.println( e );
        }
    }


}

