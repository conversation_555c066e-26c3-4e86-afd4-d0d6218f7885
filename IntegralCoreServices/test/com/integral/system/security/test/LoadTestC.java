package com.integral.system.security.test;

import com.integral.test.TestCaseC;

import java.security.Security;

public class LoadTestC extends TestCaseC
{
    static String name = "Load Test";

    public LoadTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        try
        {
            LoadTestC test = new LoadTestC( name );
            test.testLoad();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public void testLoad()
    {
        try
        {
            String cn = "com.sun.crypto.provider.SunJCE";
            log.info( "fetching class " + cn );
            Class cls = Class.forName( cn );
            log.info( "about to register security provider " + cls.getName() );
            Security.addProvider( ( java.security.Provider ) cls.newInstance() );
            log.info( "finished registering security provider " + cls.getName() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
