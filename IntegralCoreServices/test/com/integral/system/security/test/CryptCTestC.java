package com.integral.system.security.test;

import com.integral.security.CryptC;
import com.integral.test.TestCaseC;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * Created by IntelliJ IDEA.
 * User: pandit
 * Date: Mar 26, 2008
 * Time: 4:03:35 PM
 * To change this template use File | Settings | File Templates.
 */
public class CryptCTestC extends TestCaseC
{

    private final static ExecutorService executor = Executors.newCachedThreadPool();
    public static final int noOfRequests = 500;

    public CryptCTestC( String name )
    {
        super( name );
    }

    public void testCryptMD5()
    {
        Future<Boolean>[] futureTasks = new Future[noOfRequests];
        for ( int i = 0; i < noOfRequests; i++ )
        {
            CryptCTestCRunner runner = new CryptCTestCRunner();
            futureTasks[i] = executor.submit( runner );
        }
        for ( int i = 0; i < noOfRequests; i++ )
        {
            try
            {
                Boolean isTrue = futureTasks[i].get();
                assertTrue( isTrue );
            }
            catch ( InterruptedException e )
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            catch ( ExecutionException e )
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    private class CryptCTestCRunner implements Callable<Boolean>
    {
        public Boolean call()
        {
            return CryptC.validateMD5( "25aa2761448ea05599418ee93dd1d5c5", "adAZd7rZ3BHeGvmyEwPoSQ==" );
        }
    }

}