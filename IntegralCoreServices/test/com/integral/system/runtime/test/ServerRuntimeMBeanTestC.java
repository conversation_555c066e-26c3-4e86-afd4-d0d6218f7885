package com.integral.system.runtime.test;

// Copyright (c) 2001-2009 Integral Development Corp. All rights reserved.

import com.integral.finance.trade.Tenor;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.runtime.AuxillaryPersistanceQueryType;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntime;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.test.MBeanTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.util.IdcUtilC;

import javax.management.AttributeChangeNotification;
import javax.management.AttributeChangeNotificationFilter;
import javax.management.ListenerNotFoundException;
import javax.management.Notification;
import javax.management.NotificationListener;
import java.util.Collection;
import java.util.HashMap;

public class ServerRuntimeMBeanTestC extends MBeanTestCaseC
{
    ServerRuntime runTimeMBean = ( ServerRuntime ) RuntimeFactory.getServerRuntimeMBean();
    boolean isLoginsPropertyModified = false;
    boolean isRatesPropertyModified = false;
    boolean isTradesPropertyModified = false;

    boolean isLoginsEnabled = false;
    boolean isRatesEnabled = false;
    boolean isTradesEnabled = false;


    public ServerRuntimeMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( runTimeMBean, "ratesEnabled", ServerRuntimeMBean.SERVER_RATESENABLED, MBeanTestCaseC.BOOLEAN, true, true );
        testProperty( runTimeMBean, "loginsEnabled", ServerRuntimeMBean.SERVER_LOGINSENABLED, MBeanTestCaseC.BOOLEAN, true, true );
        testProperty( runTimeMBean, "tradesEnabled", ServerRuntimeMBean.SERVER_TRADESENABLED, MBeanTestCaseC.BOOLEAN, true, true );
        testProperty( runTimeMBean, "applicationShortName", ServerRuntimeMBean.APPLICATION_SHORTNAME, MBeanTestCaseC.STRING, true, true );
        testProperty( runTimeMBean, "JMSBrokerMonitorPingInterval", ServerRuntimeMBean.JMS_BROKER_MONITOR_PING_INTERVAL, MBeanTestCaseC.LONG, true, true );
        testProperty( runTimeMBean, "JMSBrokerMonitorMessagesThreshold", ServerRuntimeMBean.JMS_BROKER_MONITOR_MESSAGES_THRESHOLD, MBeanTestCaseC.INTEGER, true, true );
        testProperty( runTimeMBean, "auxiliaryDatabaseQueryEnabled", ServerRuntimeMBean.AUXILIARY_DATABASE_QUERY_ENABLED, MBeanTestCaseC.BOOLEAN, true, true );
        testProperty( runTimeMBean, "currencyPairGroupExcludedCurrencyPairsDirectQueryEnabled", ServerRuntimeMBean.CCYPAIRGROUP_EXCLUDED_CCYPAIRS_DIRECT_QUERY_ENABLED, MBeanTestCaseC.BOOLEAN, true, true );
        testProperty( runTimeMBean, "JMSBrokerMonitorResetListenersOnError", ServerRuntimeMBean.JMS_BROKER_MONITOR_RESET_LISTENERS, MBeanTestCaseC.BOOLEAN, true, true );
        testProperty( runTimeMBean, "JMSBrokerMonitorRetryFailedListenersReset", ServerRuntimeMBean.JMS_BROKER_MONITOR_RetryFailedListenersReset, MBeanTestCaseC.BOOLEAN, true, true );
        testProperty( runTimeMBean, "asyncRemoteFunctorExecutionEnabledOnTransactionCommit", ServerRuntimeMBean.ASYNC_REMOTE_FUNCTOR_EXECUTION_ENABLED_ON_TRANSACTION_COMMIT, MBeanTestCaseC.BOOLEAN, true, true );
        testProperty( runTimeMBean, "OCXDeploymentEnabled", ServerRuntimeMBean.IDC_IS_OCX_OA_DEPLOYMENT, MBeanTestCaseC.BOOLEAN );
        testProperty( runTimeMBean, "NSCreditProcessingDelay", ServerRuntimeMBean.IDC_NS_CREDIT_PROCESSING_DELAY, MBeanTestCaseC.LONG );
        testProperty( runTimeMBean, "quickCreditCheckEnabled", ServerRuntimeMBean.IDC_QUICK_CREDIT_CHECK_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( runTimeMBean, "quickCreditCheckReloadTaskDelayInMills", ServerRuntimeMBean.IDC_QUICK_CREDIT_RELOAD_DELAY_MILLS, MBeanTestCaseC.LONG );
        testProperty( runTimeMBean, "quickCreditCheckLimitsRecalcTaskDelayInMills", ServerRuntimeMBean.IDC_QUICK_CREDIT_LIMIT_RECALC_DELAY_MILLS, MBeanTestCaseC.LONG );
        testProperty( runTimeMBean, "provisionedOrgsDataPreloadEnabled", ServerRuntimeMBean.PROVISIONED_ORGS_DATA_PRELOAD_ENABLED, MBeanTestCaseC.BOOLEAN );
        testProperty( runTimeMBean, "referenceDataUpdateNotificationClasses", ServerRuntimeMBean.PROVISIONED_ORGS_DATA_PRELOAD_EXEMPT_CLASSES, MBeanTestCaseC.COLLECTION_CLASS );
        testProperty( runTimeMBean, "postCreditProcessorCacheSize", ServerRuntimeMBean.IDC_NS_POST_CREDIT_PROCESSOR_CACHE_SIZE, MBeanTestCaseC.INTEGER );
        testProperty( runTimeMBean, "postCreditProcessorCacheExpTimeMills", ServerRuntimeMBean.IDC_NS_POST_CREDIT_PROCESSOR_CACHE_EXP_TIME_MILLS, MBeanTestCaseC.LONG );
       //testProperty( runTimeMBean, "streamingNonSpotInMemoryCurrencyPairEnabled", ServerRuntimeMBean.STREAMING_NON_SPOT_IN_MEMORY_CURRENCYPAIR_ENABLED, MBeanTestCaseC.BOOLEAN );
    }

    public void testPrefixedProperties()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        String defaultAppName = runTimeMBean.getApplicationShortName();
        runTimeMBean.setProperty( ServerRuntimeMBean.APPLICATION_SHORTNAME_PREFIX + "FI1", "FI1WL", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( runTimeMBean.getApplicationShortName( org1 ), "FI1WL" );
        assertEquals( runTimeMBean.getApplicationShortName( org2 ), defaultAppName );
        runTimeMBean.setProperty( ServerRuntimeMBean.APPLICATION_SHORTNAME_PREFIX + "FI2", "FI2WL", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( runTimeMBean.getApplicationShortName( org1 ), "FI1WL" );
        assertEquals( runTimeMBean.getApplicationShortName( org2 ), "FI2WL" );
        assertEquals( runTimeMBean.getApplicationShortName(), defaultAppName );
        runTimeMBean.setProperty( ServerRuntimeMBean.APPLICATION_SHORTNAME_PREFIX + "FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        runTimeMBean.setProperty( ServerRuntimeMBean.APPLICATION_SHORTNAME_PREFIX + "FI2", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( runTimeMBean.getApplicationShortName( org1 ), defaultAppName );
        assertEquals( runTimeMBean.getApplicationShortName( org2 ), defaultAppName );
        assertEquals( runTimeMBean.getApplicationShortName(), defaultAppName );
        runTimeMBean.setProperty( ServerRuntimeMBean.AUXILIARY_DATABASE_QUERY_ENABLED_PREFIX + AuxillaryPersistanceQueryType.ADMINAUDIT_QUERY, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals("Failed to verify " + ServerRuntimeMBean.AUXILIARY_DATABASE_QUERY_ENABLED_PREFIX + AuxillaryPersistanceQueryType.ADMINAUDIT_QUERY, runTimeMBean.isAuxiliaryDatabaseQueryEnabled(AuxillaryPersistanceQueryType.ADMINAUDIT_QUERY),true);

        boolean loadAllLinesDefault = runTimeMBean.isQuickCreditCheckLoadLinesForAllLEs(null);
        runTimeMBean.setProperty( ServerRuntimeMBean.IDC_QUICK_CREDIT_LOAD_ALL_LES_ON_SUBSCRIPTION_PREFIX + "FI1", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( runTimeMBean.isQuickCreditCheckLoadLinesForAllLEs( org1 ), false );
        assertEquals( runTimeMBean.isQuickCreditCheckLoadLinesForAllLEs( org2 ), loadAllLinesDefault );
        runTimeMBean.setProperty( ServerRuntimeMBean.IDC_QUICK_CREDIT_LOAD_ALL_LES_ON_SUBSCRIPTION_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( runTimeMBean.isQuickCreditCheckLoadLinesForAllLEs( org1 ), false );
        assertEquals( runTimeMBean.isQuickCreditCheckLoadLinesForAllLEs( org2 ), false );        
        runTimeMBean.setProperty( ServerRuntimeMBean.IDC_QUICK_CREDIT_LOAD_ALL_LES_ON_SUBSCRIPTION_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        runTimeMBean.setProperty( ServerRuntimeMBean.IDC_QUICK_CREDIT_LOAD_ALL_LES_ON_SUBSCRIPTION_PREFIX + "FI2", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( runTimeMBean.isQuickCreditCheckLoadLinesForAllLEs( org1 ), true );
        assertEquals( runTimeMBean.isQuickCreditCheckLoadLinesForAllLEs( org2 ), true );
        assertEquals( runTimeMBean.isQuickCreditCheckLoadLinesForAllLEs(null), loadAllLinesDefault );
    }

    private AttributeChangeNotificationFilter getAttributeChangeNotificationFilter()
    {
        AttributeChangeNotificationFilter filter = new AttributeChangeNotificationFilter();
        filter.enableAttribute( "ratesEnabled" );
        filter.enableAttribute( "loginsEnabled" );
        filter.enableAttribute( "tradesEnabled" );
        return filter;
    }

    public void testServerRuntimeNotificationListenerForLoginsEnabledProperty()
    {
        ServerRuntimeNotificationListenerC listener = new ServerRuntimeNotificationListenerC();
        runTimeMBean.addNotificationListener( listener, getAttributeChangeNotificationFilter(), null );
        runTimeMBean.setLoginsEnabled( !runTimeMBean.isLoginsEnabled(), false, new HashMap() );
        try
        {
            runTimeMBean.removeNotificationListener( listener );
        }
        catch ( ListenerNotFoundException e )
        {
            fail( "Unable to remove listener. Listener " + listener + " not found in IdcMbean listeners list. Problem with IdcMBean listener handling mechanism." );
        }

        assertEquals( "Notification not received for : loginsEnabled ", isLoginsPropertyModified, true );
        assertEquals( "property mismtach for : loginsEnabled ", isLoginsEnabled, runTimeMBean.isLoginsEnabled() );
    }

    public void testServerRuntimeNotificationListenerForRatesEnabledProperty()
    {
        ServerRuntimeNotificationListenerC listener = new ServerRuntimeNotificationListenerC();
        runTimeMBean.addNotificationListener( listener, getAttributeChangeNotificationFilter(), null );
        runTimeMBean.setRatesEnabled( !runTimeMBean.isRatesEnabled(), false, new HashMap() );
        try
        {
            runTimeMBean.removeNotificationListener( listener );
        }
        catch ( ListenerNotFoundException e )
        {
            fail( "Unable to remove listener. Listener " + listener + " not found in IdcMbean listeners list. Problem with IdcMBean listener handling mechanism." );
        }

        assertEquals( "Notification not received for : ratesEnabled ", isRatesPropertyModified, true );
        assertEquals( "property mismtach for : ratesEnabled ", isRatesEnabled, runTimeMBean.isRatesEnabled() );
    }

    public void testServerRuntimeNotificationListenerForTradesEnabledProperty()
    {
        ServerRuntimeNotificationListenerC listener = new ServerRuntimeNotificationListenerC();
        runTimeMBean.addNotificationListener( listener, getAttributeChangeNotificationFilter(), null );
        runTimeMBean.setTradesEnabled( !runTimeMBean.isTradesEnabled(), false, new HashMap() );
        try
        {
            runTimeMBean.removeNotificationListener( listener );
        }
        catch ( ListenerNotFoundException e )
        {
            fail( "Unable to remove listener. Listener " + listener + " not found in IdcMbean listeners list. Problem with IdcMBean listener handling mechanism." );
        }

        assertEquals( "Notification not received for : TradesEnabled ", isTradesPropertyModified, true );
        assertEquals( "property mismtach for : TradesEnabled ", isTradesEnabled, runTimeMBean.isTradesEnabled() );
    }

    public void testServerRuntimeNotificationListenerForAllProperties()
    {
        ServerRuntimeNotificationListenerC listener = new ServerRuntimeNotificationListenerC();
        runTimeMBean.addNotificationListener( listener, getAttributeChangeNotificationFilter(), null );
        runTimeMBean.setTradesEnabled( !runTimeMBean.isTradesEnabled(), false, new HashMap() );
        runTimeMBean.setLoginsEnabled( !runTimeMBean.isLoginsEnabled(), false, new HashMap() );
        runTimeMBean.setRatesEnabled( !runTimeMBean.isRatesEnabled(), false, new HashMap() );
        try
        {
            runTimeMBean.removeNotificationListener( listener );
        }
        catch ( ListenerNotFoundException e )
        {
            fail( "Unable to remove listener. Listener " + listener + " not found in IdcMbean listeners list. Problem with IdcMBean listener handling mechanism." );
        }

        assertEquals( "Notification not received for : ratesEnabled ", isRatesPropertyModified, true );
        assertEquals( "Notification not received for : loginsEnabled ", isLoginsPropertyModified, true );
        assertEquals( "Notification not received for : TradesEnabled ", isTradesPropertyModified, true );

        assertEquals( "property mismtach for : ratesEnabled ", isRatesEnabled, runTimeMBean.isRatesEnabled() );
        assertEquals( "property mismtach for : loginsEnabled ", isLoginsEnabled, runTimeMBean.isLoginsEnabled() );
        assertEquals( "property mismtach for : TradesEnabled ", isTradesEnabled, runTimeMBean.isTradesEnabled() );
    }

    public void testForwardCurrencySupportedTenors()
    {
        try
        {
            Collection<Tenor> tenorList = runTimeMBean.getForwardCurrencySupportedTenors ();
            assertNotNull ( tenorList );
            assertTrue ( tenorList.size () > 0  );

            runTimeMBean.setProperty ( ServerRuntimeMBean.FORWARD_CURRENCY_SUPPORTED_TENORS, "TOD", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tenorList = runTimeMBean.getForwardCurrencySupportedTenors ();
            assertNotNull ( tenorList );
            assertTrue ( tenorList.size () == 1  );
            assertTrue ( tenorList.toArray ()[0].equals ( Tenor.TODAY_TENOR ) );

            runTimeMBean.setProperty ( ServerRuntimeMBean.FORWARD_CURRENCY_SUPPORTED_TENORS, "TOD,TOM", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tenorList = runTimeMBean.getForwardCurrencySupportedTenors ();
            assertNotNull ( tenorList );
            assertTrue ( tenorList.size () == 2  );

            //add an invalid tenor
            runTimeMBean.setProperty ( ServerRuntimeMBean.FORWARD_CURRENCY_SUPPORTED_TENORS, "TOD,XX,TOM", ConfigurationProperty.DYNAMIC_SCOPE, null );
            tenorList = runTimeMBean.getForwardCurrencySupportedTenors ();
            assertNotNull ( tenorList );
            assertTrue ( tenorList.size () == 2  );
        }
        catch ( Exception e )
        {
            e.printStackTrace ();
            fail ();
        }
        finally
        {
            runTimeMBean.removeProperty ( ServerRuntimeMBean.FORWARD_CURRENCY_SUPPORTED_TENORS, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testStreamingNonSpotEnabled()
    {
        try
        {
            assertFalse(runTimeMBean.isStreamingNonSpotEnabled (null));
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse(runTimeMBean.isStreamingNonSpotEnabled(org1));
            assertFalse(runTimeMBean.isStreamingNonSpotEnabled(org2));

            // now set the global property
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled(org1) );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled(org2) );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled(null) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( runTimeMBean.isStreamingNonSpotEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotEnabled(org2) );
            assertFalse( runTimeMBean.isStreamingNonSpotEnabled(null) );


            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotEnabled(org2) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotEnabled(org2) );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled (null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testStreamingNonSpotEnabled" );
        }
        finally
        {
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED_PREFIX + "FI2", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testStreamingNonSpotEnabledBrokerForCustomerCase()
    {
        Organization fi = null;
        Organization brokerOrg = null;
        try
        {
            assertFalse(runTimeMBean.isStreamingNonSpotEnabled (null));
            fi = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            brokerOrg = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            fi.setBrokerOrganization ( brokerOrg );
            assertFalse(runTimeMBean.isStreamingNonSpotEnabled( fi ));
            assertFalse(runTimeMBean.isStreamingNonSpotEnabled( brokerOrg ));

            // now set the global property
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled( fi ) );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled( brokerOrg ) );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled( null ) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( runTimeMBean.isStreamingNonSpotEnabled( fi ) );
            assertFalse( runTimeMBean.isStreamingNonSpotEnabled( brokerOrg ) );
            assertFalse( runTimeMBean.isStreamingNonSpotEnabled( null ) );


            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED_PREFIX + "FI2", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled( fi ) );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled( brokerOrg ) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED_PREFIX + "FI1", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( runTimeMBean.isStreamingNonSpotEnabled( fi ) );
            assertTrue( runTimeMBean.isStreamingNonSpotEnabled( brokerOrg ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testStreamingNonSpotEnabledBrokerForCustomerCase" );
        }
        finally
        {
            IdcUtilC.refreshObject ( fi );
            IdcUtilC.refreshObject ( brokerOrg );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED_PREFIX + "FI2", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testStreamingSwapTradingEnabled()
    {
        try
        {
            assertFalse(runTimeMBean.isStreamingSwapTradingEnabled (null));
            Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            assertFalse(runTimeMBean.isStreamingSwapTradingEnabled(org1));
            assertFalse(runTimeMBean.isStreamingSwapTradingEnabled(org2));

            // now set the global property
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled(org1) );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled(org2) );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled(null) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( runTimeMBean.isStreamingSwapTradingEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingSwapTradingEnabled(org2) );
            assertFalse( runTimeMBean.isStreamingSwapTradingEnabled(null) );


            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingSwapTradingEnabled(org2) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingSwapTradingEnabled(org2) );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled (null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testStreamingSwapTradingEnabled" );
        }
        finally
        {
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED_PREFIX + "FI2", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testStreamingSwapTradingEnabledBrokerForCustomerCase()
    {
        Organization fi = null;
        Organization brokerOrg = null;
        try
        {
            assertFalse(runTimeMBean.isStreamingSwapTradingEnabled (null));
            fi = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
            brokerOrg = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
            fi.setBrokerOrganization ( brokerOrg );
            assertFalse(runTimeMBean.isStreamingSwapTradingEnabled( fi ));
            assertFalse(runTimeMBean.isStreamingSwapTradingEnabled( brokerOrg ));

            // now set the global property
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled( fi ) );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled( brokerOrg ) );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled( null ) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( runTimeMBean.isStreamingSwapTradingEnabled( fi ) );
            assertFalse( runTimeMBean.isStreamingSwapTradingEnabled( brokerOrg ) );
            assertFalse( runTimeMBean.isStreamingSwapTradingEnabled( null ) );


            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED_PREFIX + "FI2", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled( fi ) );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled( brokerOrg ) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED_PREFIX + "FI1", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse ( runTimeMBean.isStreamingSwapTradingEnabled( fi ) );
            assertTrue( runTimeMBean.isStreamingSwapTradingEnabled( brokerOrg ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testStreamingSwapTradingEnabledBrokerForCustomerCase" );
        }
        finally
        {
            IdcUtilC.refreshObject ( fi );
            IdcUtilC.refreshObject ( brokerOrg );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED_PREFIX + "FI2", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_SWAP_TRADING_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testStreamingNonSpotBrokenDateEnabled()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        Organization brokerOrg = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "Broker1" );

        try
        {
            org1.setBrokerOrganization( brokerOrg );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled ( null ) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled( org1 ) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled( org2 ) );

            // now set the global property
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org1) );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org2) );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(null) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org2) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(null) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED_PREFIX + brokerOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org2) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(null) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org2) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateEnabled(org2) );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateEnabled (null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testStreamingNonSpotBrokenDateEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject( org1 );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED_PREFIX + "FI2", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testStreamingNonSpotBrokenDateExecutionEnabled()
    {
        Organization org1 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI1" );
        Organization org2 = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "FI2" );
        Organization brokerOrg = ( Organization ) new ReadNamedEntityC().execute( OrganizationC.class, "Broker1" );

        try
        {
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_IN_MEMORY_CURRENCYPAIR_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            org1.setBrokerOrganization( brokerOrg );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled ( null ) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled( org1 ) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled( org2 ) );

            // now set the global property
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org1) );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org2) );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(null) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org2) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(null) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED_PREFIX + brokerOrg.getShortName(), "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org2) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(null) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED_PREFIX + "FI1", "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED_PREFIX + "FI2", "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org2) );

            runTimeMBean.setProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org1) );
            assertFalse( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled(org2) );
            assertTrue( runTimeMBean.isStreamingNonSpotBrokenDateExecutionEnabled (null ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testStreamingNonSpotBrokenDateExecutionEnabled" );
        }
        finally
        {
            IdcUtilC.refreshObject( org1 );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED_PREFIX + "FI1", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED_PREFIX + "FI2", ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty ( ServerRuntimeMBean.STREAMING_NON_SPOT_BROKEN_DATE_EXECUTION_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
            runTimeMBean.removeProperty( ServerRuntimeMBean.STREAMING_NON_SPOT_IN_MEMORY_CURRENCYPAIR_ENABLED, ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }
    private class ServerRuntimeNotificationListenerC implements NotificationListener
    {
        public void handleNotification( Notification notification, Object o )
        {
            if ( notification instanceof AttributeChangeNotification )
            {
                AttributeChangeNotification acn = ( AttributeChangeNotification ) notification;
                if ( "ServerRuntime Property Change".equals( acn.getMessage() ) )
                {
                    if ( "ratesEnabled".equals( acn.getAttributeName() ) )
                    {
                        isRatesPropertyModified = true;
                        isRatesEnabled = ( Boolean ) acn.getNewValue();
                    }
                    else if ( "loginsEnabled".equals( acn.getAttributeName() ) )
                    {
                        isLoginsPropertyModified = true;
                        isLoginsEnabled = ( Boolean ) acn.getNewValue();
                    }
                    else if ( "tradesEnabled".equals( acn.getAttributeName() ) )
                    {
                        isTradesPropertyModified = true;
                        isTradesEnabled = ( Boolean ) acn.getNewValue();
                    }
                }
                else
                {
                    log.error( "This listener only handles ServerRuntime Porperty change messages." );
                }
            }
        }
    }
}
