package com.integral.system.io.test;

import com.integral.system.io.CSVLoaderC;
import com.integral.test.TestCaseC;

import java.io.File;
import java.net.URL;
import java.util.List;
import java.util.HashMap;

// Copyright (c) 2001-2009 Integral Development Corp. All rights reserved.

public class CSVLoaderTestC extends TestCaseC
{
    private static int cols = 6;

    public CSVLoaderTestC( String name )
    {
        super( name );
    }

    public CSVLoaderTestC()
    {
        super( "CSVLoaderTestC" );
    }

    public void testCSVLoad()
    {
        try
        {
            String csvFile = "CSVLoaderTest.csv";
            URL url = this.getClass().getClassLoader().getResource( csvFile );
            assertNotNull( url );
            File fileToUpload = new File( url.toURI() );
            assertNotNull( fileToUpload );

            CSVLoaderC loader = new CSVLoaderC();
            loader.setVerboseMode( true );
            List propSheet = loader.getPropertySheet( fileToUpload.getPath() );
            for ( int i = 0; i < propSheet.size(); i++ )
            {
                HashMap<String,String> tbl = ( HashMap<String,String> ) propSheet.get( i );
                String shortname = tbl.get( "ShortName");
                if(shortname.indexOf( "#") == 0){
                    //ignore comments
                    continue;
                }
                else{
                    if ( tbl.size() == cols )
                    {
                        int col1len = tbl.get( "ShortName").length();
                    int col2len = tbl.get( "LongName").length();
                    int col3len = tbl.get( "UserPermissions").length();
                    int col4len = Integer.parseInt( tbl.get( "C1Size"));
                    int col5len = Integer.parseInt( tbl.get( "C2Size"));
                    int col6len = Integer.parseInt( tbl.get( "C3Size"));
                    if(col1len != col4len ||  col2len != col5len || col3len != col6len){
                        throw new RuntimeException("Cols doesn't match with required cols");
                    }
                }
                else{
                    throw new RuntimeException("Cols doesn't match with required cols - Current : " + tbl.size() + " Expected : " + cols);
                }
                }
            }

        }
        catch ( Exception e )
        {
            log.error( "Exception : ", e );
            fail( "Failed to run test case : testCSVLoad " );
        }
    }
}
