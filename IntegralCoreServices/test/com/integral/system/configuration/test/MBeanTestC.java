package com.integral.system.configuration.test;

import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.PersistenceMBean;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.test.TestCaseC;

import javax.naming.Context;
import javax.naming.InitialContext;
import java.util.Properties;


/**
 * Tests mbeans registered in the app server
 */
public class MBeanTestC extends TestCaseC
{
    static String name = "MBean Test";

    public MBeanTestC( String aName )
    {
        super( aName );
    }

    /**
     * Lookup mbean server via straight JNDI
     */
    public void testLookupMBeanServer()
    {
        try
        {
            String username = "Integral@MAIN";
            String password = "integral";

            ServerMBean mbean = ConfigurationFactory.getServerMBean();
            String appServerURL = mbean.getApplicationServerURL();
            String serverContextClass = mbean.getInitialContextFactoryClassForServer();
            String clientContextClass = serverContextClass;
            String mbeanHomeName = "XXXX";

            log.info( "connecting to " + appServerURL
                    + " using username/password " + username + '/' + password );

            Properties prop = new Properties();
            prop.put( Context.INITIAL_CONTEXT_FACTORY, clientContextClass );
            prop.put( Context.PROVIDER_URL, appServerURL );
            prop.put( Context.SECURITY_PRINCIPAL, username );
            prop.put( Context.SECURITY_CREDENTIALS, password );
            InitialContext ctx = new InitialContext( prop );
            ctx.bind ( mbeanHomeName, new Object() );

            Object serverR = ctx.lookup( mbeanHomeName );
            log.info( "MBean server remote object = " + serverR );
            /*
            MBeanServer server = (MBeanServer)PortableRemoteObject.narrow (serverR,
                                                                       MBeanServer.class);
            log.info("MBean server = " + server);
            */

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "error looking up MBean server with JNDI" );
        }
    }

    /**
     * Tests register a mbean in a standalone program. In order to run this,
     * you must
     * <UL>
     * <LI>set the property IDC.Server.Type=standalone
     * <LI>have both mx4j jars loaded before the weblogic jar
     * </UL>
     */
    public void testRunStandalone()
    {
        ServerMBean mbean1 = ConfigurationFactory.getServerMBean();
        ConfigurationFactory.registerMBean( mbean1 );

        PersistenceMBean mbean2 = PersistenceFactory.getPersistenceMBean();
        ConfigurationFactory.registerMBean( mbean2 );

        try
        {
            Thread.sleep( 10000 );
        }
        catch ( Exception e )
        {
        }
    }
}
