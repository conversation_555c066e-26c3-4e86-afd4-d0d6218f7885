package com.integral.system.configuration.test;

import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.configuration.Server;
import com.integral.system.configuration.ServerMBean;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class ServerMBeanTestC extends MBeanTestCaseC
{
    Server serverMBean = ( Server ) ConfigurationFactory.getServerMBean();

    public ServerMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {
        testProperty( serverMBean, "systemLocaleLanguage", "SystemLocale.Language", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "systemLocaleCountry", "SystemLocale.Country", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "serverType", "IDC.Server.Type", MBeanTestCaseC.STRING );
        //todo: below methods uses init methods
        testProperty( serverMBean, "serverRootDirectory", "IDC.Server.Root.Directory", MBeanTestCaseC.STRING );

        testProperty( serverMBean, "applicationServerURL", "AppServerURL", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "domainName", "idc.domain.name", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "earName", "idc.ear.name", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "systemUserName", "IDC.System.User.Name", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "JMSManagerType", "jmsmanager.type", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "applicationLongName", "IDC.Application.LongName", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "virtualServerName", "IDC.VirtualServer.Name", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "virtualServerGroupName", "IDC.VirtualServer.Group", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "virtualServerType", "IDC.VirtualServer.Type", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "isReloadJNDI", "IDC.JMSMBean.xml.Reload.TO.DB", MBeanTestCaseC.STRING, false, true );
        testProperty( serverMBean, "isReloadJNDIConfirm", "IDC.JMSMBean.xml.Reload.TO.DB.Confirm", MBeanTestCaseC.STRING, false, true );
        testProperty( serverMBean, "isVirtualServerIPAddressPortUpdate", "IDC.VirtualServer.IPAddressPort.Update", MBeanTestCaseC.STRING, false, true );
        testProperty( serverMBean, "heartbeatPublishInterval", "Idc.VirtualServer.Heartbeat.publish.interval", MBeanTestCaseC.INTEGER );
        testProperty( serverMBean, "heartbeatPublishTopicJNDI", "Idc.VirtualServer.Heartbeat.publish.Topic.JNDI.Name", MBeanTestCaseC.STRING );
        testProperty( serverMBean, "streamingMode", "IDC.Streaming.Mode", MBeanTestCaseC.INTEGER, false, true );
        testProperty( serverMBean, "productionInitial", ServerMBean.IDC_PRODUCTION_SERVER_INITIAL, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "stagingInitial", ServerMBean.IDC_STAGING_SERVER_INITIAL, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "betaInitial", ServerMBean.IDC_BETA_SERVER_INITIAL, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "demoInitial", ServerMBean.IDC_DEMO_SERVER_INITIAL, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "productionEnvName", ServerMBean.IDC_PRODUCTION_SERVER_ENV_NAME, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "demoEnvName", ServerMBean.IDC_DEMO_SERVER_ENV_NAME, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "stagingEnvName", ServerMBean.IDC_STAGING_SERVER_ENV_NAME, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "betaEnvName", ServerMBean.IDC_BETA_SERVER_ENV_NAME, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "serverEnvName", ServerMBean.IDC_SERVER_ENV_NAME, MBeanTestCaseC.STRING );
        testProperty( serverMBean, "JMSProxySessionTimeOut", ServerMBean.IDC_JMSPROXY_SESSION_TIMEOUT, MBeanTestCaseC.INTEGER, false, true );
        testProperty( serverMBean, "JMSProxyServerCharset", ServerMBean.IDC_JMSPROXY_SERVER_CHARSET, MBeanTestCaseC.STRING, false, true );
        testProperty( serverMBean, "maxForwardYearsForIdcDate", ServerMBean.IDCDATE_MAX_FORWARD_YEARS, MBeanTestCaseC.INTEGER );

        testProperty( serverMBean, "webAppSessionTimeout", ServerMBean.SESSION_TIMEOUT, MBeanTestCaseC.INTEGER, false, true );
        testProperty( serverMBean, "dealingWorkflowDBQueryStackTraceEnabled", ServerMBean.DEALING_WORKFLOW_DB_QUERY_STACK_TRACE_ENABED, MBeanTestCaseC.BOOLEAN );
        testProperty( serverMBean, "allWorkflowsDBQueryStackTraceEnabled", ServerMBean.ALL_WORKFLOWS_DB_QUERY_STACK_TRACE_ENABED, MBeanTestCaseC.BOOLEAN );
    }

    //testdifferent webappsessiontimeout properties
    public void testWebAppSessionTimeoutProperties()
    {

        //test global property

        assertEquals( ServerMBean.DEAFULT_SESSION_TIMEOUT, serverMBean.getWebAppSessionTimeout() );

        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT, Integer.toString( 5 ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".admin", Integer.toString( 5 ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".config", Integer.toString( 5 ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".is", Integer.toString( 5 ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".is.FI1", Integer.toString( 5 ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".admin.FI1", Integer.toString( 5 ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".admin.MAIN", Integer.toString( 5 ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        assertEquals( 5, serverMBean.getWebAppSessionTimeout() );

        //global is set to 5 - we should get all 5

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "admin" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "config" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "is" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "is", "FI1" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "admin", "MAIN" ) );

        //setting admin webapp to 60
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".admin", Integer.toString( 60 ), ConfigurationProperty.DYNAMIC_SCOPE, null );
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".admin.MAIN", null, ConfigurationProperty.DYNAMIC_SCOPE, null );
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".admin.FI1", null, ConfigurationProperty.DYNAMIC_SCOPE, null );

        assertEquals( 60, serverMBean.getWebAppSessionTimeout( "admin" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "config" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "is" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "is", "FI1" ) );

        assertEquals( 60, serverMBean.getWebAppSessionTimeout( "admin", "MAIN" ) );

        assertEquals( 60, serverMBean.getWebAppSessionTimeout( "admin", "FI1" ) );

        //setting config.MAIN to 120 seconds
        serverMBean.setProperty( ServerMBean.SESSION_TIMEOUT + ".config.MAIN", Integer.toString( 120 ), ConfigurationProperty.DYNAMIC_SCOPE, null );

        assertEquals( 60, serverMBean.getWebAppSessionTimeout( "admin" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "config" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "is" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "is", "FI1" ) );

        assertEquals( 60, serverMBean.getWebAppSessionTimeout( "admin", "MAIN" ) );

        assertEquals( 60, serverMBean.getWebAppSessionTimeout( "admin", "FI1" ) );

        assertEquals( 120, serverMBean.getWebAppSessionTimeout( "config", "MAIN" ) );

        assertEquals( 5, serverMBean.getWebAppSessionTimeout( "config", "FI2" ) );

    }

    public void testPostStartupHandlers()
    {
        try
        {
            TestHandler handler1 = new TestHandler();
            TestHandler handler2 = new TestHandler();
            serverMBean.addPostStartupHandler( "Test1", handler1 );
            serverMBean.addPostStartupHandler( "Test2", handler2 );
            serverMBean.setApplicationStarted( true );
            assertEquals( handler1.getCount(), 1 );
            assertEquals( handler2.getCount(), 1 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testPostStartupHandlers" );
        }
    }

    private class TestHandler implements MessageHandler
    {
        private int count;

        public WorkflowMessage handle( Message wm )
        {
            count++;
            return null;
        }

        public int getCount()
        {
            return count;
        }
    }

}
