package com.integral.system.configuration.test;

import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.system.configuration.*;
import com.integral.test.MBeanTestCaseC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;

import java.util.Vector;


/**
 * Created by IntelliJ IDEA.
 * User: pandit
 * Date: Dec 19, 2006
 * Time: 5:29:19 PM
 * To change this template use Options | File Templates.
 */
public class OrganizationMBeanPTestC extends MBeanTestCaseC
{
    OrganizationMBeanC organizationMBean = (OrganizationMBeanC) ConfigurationFactory.getOrganizationMBean();
    static String name = "OrganiztionMBeanC Test";

    public OrganizationMBeanPTestC( String name )
    {
        super( name );
    }



    /**
     * Unit test to create new broker defination and Topic entry
     */
    public void testReadAdminBrandNameProp()
    {

        try
        {
            Session session1 = ( org.eclipse.persistence.sessions.Session ) PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( OrganizationC.class );
            raq.setSelectionCriteria( eb.get( "shortName" ).equal( "FI1" ) );
            Vector vect = ( Vector ) session1.executeQuery( raq );
            Organization org = ( Organization ) vect.elementAt( 0 );
            System.out.println( "organization iss " + org.getShortName() );

            org.getAdminBrandName();

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testREadAdminBrandNameProp" );
        }
    }
    public void testProperties()
    {
        Session session1 = null;
        try {
            session1 = ( Session ) PersistenceFactory.newSession();
        } catch (PersistenceException e) {
            e.printStackTrace();
        }
        ExpressionBuilder eb = new ExpressionBuilder();
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass( OrganizationC.class );
        raq.setSelectionCriteria( eb.get( "shortName" ).equal( "FI1" ) );
        Vector vect = ( Vector ) session1.executeQuery( raq );
        Organization org = ( Organization ) vect.elementAt( 0 );
        System.out.println( "organization iss " + org.getShortName() );

        organizationMBean.setProperty( org.getShortName() + OrganizationMBean.IDC_BROKER_METATRADER_URL, "BRKA", ConfigurationProperty.DYNAMIC_SCOPE, null);
        String orgURL = organizationMBean.getMetaTraderServerForBrokerOrg( org);
        assertEquals("BRKA",orgURL);
        System.out.println("result: "+orgURL);

        String defineOrgs = "DBNA,DBNB";
        String[] defineOrgArray = {"DBNA","DBNB"};
        organizationMBean.setProperty( org.getShortName() + OrganizationMBean.IDC_CUSTOMER_METATRADER_ORGANIZATION, defineOrgs, ConfigurationProperty.DYNAMIC_SCOPE, null);
        String[] orgs = organizationMBean.getMetaTraderCustomerOrgsForBrokerOrg( org);

        System.out.println("result: "+orgs[0]+" "+orgs[1]);
        assertEquals("DBNA",orgs[0]);
        assertEquals("DBNB",orgs[1]);
   }

}
