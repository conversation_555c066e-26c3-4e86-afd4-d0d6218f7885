package com.integral.system.configuration.test;

import com.integral.finance.config.Finance;
import com.integral.finance.config.FinanceConfigurationFactory;
import com.integral.jmsx.JMSConstantsC;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.NamespaceGroupC;
import com.integral.persistence.Persistence;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.startup.WatchPropertyC;
import com.integral.system.configuration.ConfigRemoteNotificationFunctorC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.system.configuration.Server;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.property.PropertyFactory;
import com.integral.system.property.SystemPropertyC;
import com.integral.system.property.SystemPropertyMetaDataC;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;


/**
 * <AUTHOR> Development Corporation.
 */
public class ConfigurationPropertyTestC extends PTestCaseC
{

    VirtualServer globalVS = ( VirtualServer ) new ReadNamedEntityC().execute( VirtualServer.class, "GLOBALVS" );

    public ConfigurationPropertyTestC( String aName )
    {
        super( aName );
    }

    public void testPropertyPrecedence()
    {
        //take a MBean - set the props at different levels and when the one at higher level is added/modifed it should take effect
        Finance financeMBean = ( Finance ) FinanceConfigurationFactory.getFinanceMBean();
        financeMBean.printProperties();

        String oldValue = financeMBean.getMonteCarloServer();
        String newValue = "TESTVALUE" + System.currentTimeMillis();
        addProperty( "IDC.MonteCarlo.Server", newValue, "LOCAL" );

        assertNotSame( oldValue, financeMBean.getMonteCarloServer() );
        assertEquals( "The value should match DB property Value.", newValue, financeMBean.getMonteCarloServer() );

        newValue = "TESNEWTVALUE" + System.currentTimeMillis();
        addProperty( "IDC.MonteCarlo.Server", newValue, "LOCAL" );
        assertEquals( "The value should match DB property Value.", newValue, financeMBean.getMonteCarloServer() );

    }

    public void testPropertyNotReadingDB()
    {
        String newValue = "TESTVALUE" + System.currentTimeMillis();
        addProperty( "IDC.System.User.Name", newValue, "GLOBAL" );

        // call the startup class to initialize MBeans to laod db properties correctly
        WatchPropertyC watch = new WatchPropertyC();
        watch.startup( null, null );

        Server serverMBean = ( Server ) ConfigurationFactory.getServerMBean();
        ConfigurationProperty configProp = IdcMBeanC.getConfigPropertyMap().get( "IDC.System.User.Name" );
        String valueAtPropertyFileScope = configProp.getProperty( 8 );
        assertNotSame( "The value for ServerMBean should be used from database scope", valueAtPropertyFileScope, serverMBean.getSystemUserName() );
    }

    public void testAddUpdDelPropertyPrecedence()
    {
        Finance financeMBean = ( Finance ) FinanceConfigurationFactory.getFinanceMBean();
        financeMBean.printProperties();
        String propertyName = "IDC.MonteCarlo.Server";
        //take a MBean - for a given property if exists remove all the props from diff. scopes
        removePropertyFromAllScopes( propertyName );

        // cache the property set in the properties file as oldValue
        String oldValue = financeMBean.getMonteCarloServer();

        long t0 = System.currentTimeMillis();
        String newGlobalValue = "TESTGLOBAL" + t0;
        String newGroupValue = "TESTGROUP" + t0;
        String newLocalValue = "TESTLOCAL" + t0;

        // add property at global, group and then local level and subsequently that should take effect
        addProperty( propertyName, newGlobalValue, "GLOBAL" );
        assertNotSame( oldValue, financeMBean.getMonteCarloServer() );
        assertEquals( "The value should match DB property Global Value.", newGlobalValue, financeMBean.getMonteCarloServer() );

        addProperty( propertyName, newGroupValue, "GROUP" );
        assertNotSame( "The value should mis-match global Value.", newGlobalValue, financeMBean.getMonteCarloServer() );
        assertEquals( "The value should match DB property Group Value.", newGroupValue, financeMBean.getMonteCarloServer() );

        addProperty( propertyName, newLocalValue, "LOCAL" );
        assertNotSame( "The value should mis-match global Value.", newGlobalValue, financeMBean.getMonteCarloServer() );
        assertNotSame( "The value should mis-match group Value.", newGroupValue, financeMBean.getMonteCarloServer() );
        assertEquals( "The value should match DB property Local Value.", newLocalValue, financeMBean.getMonteCarloServer() );

        long t1 = System.currentTimeMillis();
        String newGlobalValue1 = "TESTGLOBAL" + t1;
        String newGroupValue1 = "TESTGROUP" + t1;
        String newLocalValue1 = "TESTLOCAL" + t1;

        // then change property at global or group level - should be unaffected as the LOCAL level property is set
        addProperty( propertyName, newGlobalValue1, "GLOBAL" );
        assertNotSame( "The value should mis-match new global Value.", newGlobalValue1, financeMBean.getMonteCarloServer() );
        assertEquals( "The value should match DB property Local Value.", newLocalValue, financeMBean.getMonteCarloServer() );
        addProperty( propertyName, newGroupValue1, "GROUP" );
        assertNotSame( "The value should mis-match new group Value.", newGroupValue1, financeMBean.getMonteCarloServer() );
        assertEquals( "The value should match DB property Local Value.", newLocalValue, financeMBean.getMonteCarloServer() );

        // then change proerpty at local level - affected
        addProperty( propertyName, newLocalValue1, "LOCAL" );
        assertNotSame( "The value should mis-match old local Value.", newLocalValue, financeMBean.getMonteCarloServer() );
        assertEquals( "The value should match DB property new Local Value.", newLocalValue1, financeMBean.getMonteCarloServer() );

        // then subsequently remove property from local, group and global level - in that order the next level should take affect
        removeProperty( propertyName, "LOCAL" );
        assertEquals( "The value should match DB property new Group Value.", newGroupValue1, financeMBean.getMonteCarloServer() );

        removeProperty( propertyName, "GROUP" );
        assertEquals( "The value should match DB property new Global Value.", newGlobalValue1, financeMBean.getMonteCarloServer() );

        removeProperty( propertyName, "GLOBAL" );
        assertEquals( "The value should match old cached property value.", oldValue, financeMBean.getMonteCarloServer() );

    }


    public void testDynamicPropertyPrecedence()
    {
        Persistence persitenceMBean = ( Persistence ) PersistenceFactory.getPersistenceMBean();
        String propertyName = "TopLink.ConnectionPool.Read.Max";

        //add property at LOCAL level
        String oldValue = persitenceMBean.getDatabaseReadConnectionPoolMaximum();
        String newLocalValue = "LOCAL_READMAX_" + System.currentTimeMillis();
        addProperty( propertyName, newLocalValue, "LOCAL" );

        assertNotSame( oldValue, persitenceMBean.getDatabaseReadConnectionPoolMaximum() );
        assertEquals( "The value should match DB property Value.", newLocalValue, persitenceMBean.getDatabaseReadConnectionPoolMaximum() );

        //now set the property at dynamic level - this is invoked from jmx-console
        String newDynValue = "DYNAMIC_READMAX_" + System.currentTimeMillis();
        persitenceMBean.setDatabaseReadConnectionPoolMaximum( newDynValue );
        assertEquals( "The value should match dynamic Value.", newDynValue, persitenceMBean.getDatabaseReadConnectionPoolMaximum() );

        //update the local DB value - even then the dynamic value should take effect and not new local value
        newLocalValue = "NEW_LOCAL_READMAX_" + System.currentTimeMillis();
        addProperty( propertyName, newLocalValue, "LOCAL" );
        assertEquals( "The value should match dynamic Value.", newDynValue, persitenceMBean.getDatabaseReadConnectionPoolMaximum() );
        assertNotSame( "The value should mis-match new group Value.", newLocalValue, persitenceMBean.getDatabaseReadConnectionPoolMaximum() );

    }

    public void testParametricProperty()
    {
        /*//obtain a parametric property from tradeConfig
        TradeConfiguration tradeMBean = ( TradeConfiguration ) TradeConfigurationFactory.getTradeConfigurationMBean();
        String parameter = String.valueOf( System.currentTimeMillis() );
        String property = "IDC.LP." + parameter + ".Masked.Name";

        // obtian a property with some paramter not found
        String value = tradeMBean.getOrigNameForMaskedLPOrg( parameter );
        assertEquals( "For new parameter the value should be null.", value, null );

        // set that property
        addProperty( property, parameter, "GLOBAL" );

        // when accessed with that paramter the proeprty should be set
        value = tradeMBean.getOrigNameForMaskedLPOrg( parameter );
        assertEquals( "The value should be set and accesible from tradeConfigMBean", value, parameter );*/
    }

    public void testPropertiesWithPrefix()
    {
        /*//get the count of properties with a prefix
        TradeConfiguration tradeMBean = ( TradeConfiguration ) TradeConfigurationFactory.getTradeConfigurationMBean();
        Iterator keys = tradeMBean.getPropertiesWithPrefix( "IDC.LP" ).keySet().iterator();
        int count = 0;
        while ( keys.hasNext() )
        {
            String key = ( String ) keys.next();
            if ( tradeMBean.getOrigNameForMaskedLPOrg( key.substring( 7, key.indexOf( '.', 7 ) ) ) != null )
            {
                count++;
            }
        }
        System.out.println( "Initial count of props in traeMBean=" + count );

        // add 2 more properties with different parameters
        String parameter = String.valueOf( System.currentTimeMillis() );
        String property = "IDC.LP." + parameter + ".Masked.Name";
        addProperty( property, parameter, "GLOBAL" );
        parameter = String.valueOf( System.currentTimeMillis() );
        property = "IDC.LP." + parameter + ".Masked.Name";
        addProperty( property, parameter, "GLOBAL" );
        keys = tradeMBean.getPropertiesWithPrefix( "IDC.LP" ).keySet().iterator();
        int countAfterAdd = 0;
        while ( keys.hasNext() )
        {
            String key = ( String ) keys.next();
            if ( tradeMBean.getOrigNameForMaskedLPOrg( key.substring( 7, key.indexOf( '.', 7 ) ) ) != null )
            {
                countAfterAdd++;
            }
        }

        // the count should increment by 2
        assertEquals( "Add 2 more proeprties with same prefix.", count + 2, countAfterAdd );

        removeProperty( property, "GLOBAL" );
        keys = tradeMBean.getPropertiesWithPrefix( "IDC.LP" ).keySet().iterator();
        int countAfterRemove = 0;
        while ( keys.hasNext() )
        {
            String key = ( String ) keys.next();
            if ( tradeMBean.getOrigNameForMaskedLPOrg( key.substring( 7, key.indexOf( '.', 7 ) ) ) != null )
            {
                countAfterRemove++;
            }
        }
        assertEquals( "One less property with same prefix.", countAfterAdd - 1, countAfterRemove );*/
    }

    /*public void testRemovePropertyFromAllAScopes()
    {
        //set property at DB level
        Finance financeMBean = ( Finance ) FinanceConfigurationFactory.getFinanceMBean();
        String propertyName = "IDC.MonteCarlo.Server";

        addProperty( propertyName, "LOCAL_VALUE", "LOCAL" );
        assertNotSame( "The property value should match local value.", "LOCAL_VALUE", financeMBean.getMonteCarloServer() );

        // remove proprty from all the scopes - every single one
        removeProperty( propertyName, "GLOBAL" );
        removeProperty( propertyName, "GROUP" );
        removeProperty( propertyName, "LOCAL" );
        financeMBean.removeProperty( propertyName, ConfigurationProperty.DYNAMIC_SCOPE );
        financeMBean.removeProperty( propertyName, ConfigurationProperty.HOSTNAME_LOCAL_SCOPE );
        financeMBean.removeProperty( propertyName, ConfigurationProperty.HOSTNAME_SCOPE );
        financeMBean.removeProperty( propertyName, ConfigurationProperty.MACHINE_NAME_LOCAL_SCOPE );
        financeMBean.removeProperty( propertyName, ConfigurationProperty.MACHINE_NAME_SCOPE );
        financeMBean.removeProperty( propertyName, ConfigurationProperty.PROPERTY_FILE_SCOPE );

        //assert that the property value and the porperty source is set to null
        assertEquals( "The property value should be null", null, financeMBean.getMonteCarloServer() );
        assertEquals( "The property source should be null", null, financeMBean.getMonteCarloServer() );

    }*/


    private void removePropertyFromAllScopes( String propertyName )
    {
        log.info( "ConfigurationPropertyTestC:removePropertyFromAllScopes for property : " + propertyName );

        try
        {

            removeProperty( propertyName, "GLOBAL" );
            removeProperty( propertyName, "GROUP" );
            removeProperty( propertyName, "LOCAL" );

            /*     Session session = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( SystemPropertyC.class );
            ExpressionBuilder aBuilder = new ExpressionBuilder();
            Expression exp = aBuilder.get( "shortName" ).equal( propertyName );
            raq.setSelectionCriteria( exp );
            Collection spColl = ( Collection ) session.executeQuery( raq );
            Iterator iter = spColl.iterator();
            UnitOfWork uow = session.acquireUnitOfWork();
            while ( iter.hasNext() )
            {
                SystemPropertyC spReg = ( SystemPropertyC ) uow.registerObject( ( SystemPropertyC ) iter.next() );
                uow.deleteObject( spReg );
                log.info( "Removed property at scope=" + spReg.getScope() );
            }
            uow.commit();*/
        }
        catch ( Exception e )
        {
            fail( "ConfigurationPropertyTestC:removePropertyFromAllScopes. exception = " + e );
        }
    }

    private void addProperty( String propertyName, String propertyValue, String scope )
    {
        try
        {
            log.info( "ConfigurationPropertyTestC:addProperty : for :" + propertyName );

            IdcSessionManager.getInstance().setTransaction( null );
            IdcTransaction tx = IdcSessionManager.getInstance().newTransaction();
            IdcSessionManager.getInstance().setTransaction( tx );
            tx = IdcSessionManager.getInstance().getTransaction();
            tx.getUOW().addReadOnlyClass( NamespaceGroupC.class );
            tx.getUOW().addReadOnlyClass( NamespaceC.class );
            tx.getUOW().addReadOnlyClass( VirtualServerC.class );

            SystemPropertyC spOld = getSystemPropertyForScope( tx.getUOW(), propertyName, scope );

            ServerMBean smb = ConfigurationFactory.getServerMBean();
            VirtualServer vs = ( VirtualServer ) new ReadNamedEntityC().execute( VirtualServerC.class, smb.getVirtualServerName() );
            if ( spOld == null )
            {
                log( "Property : " + propertyName + " does not exists. Add it." );
                SystemPropertyC sp = ( SystemPropertyC ) PropertyFactory.newProperty();
                sp.setShortName( propertyName );
                sp.setLongName( propertyName );
                sp.setDescription( propertyName + " Test Desc" );
                sp.setValue( propertyValue );
                sp.setScope( scope );
                sp.setVirtualServer( scope.equals( "GLOBAL" ) || scope.equals( "GROUP" ) ? globalVS : vs );
                sp.setServerGroup( scope.equals( "GLOBAL" ) ? "GLOBAL" : smb.getVirtualServerGroupName() );

                SystemPropertyMetaDataC spmd = new SystemPropertyMetaDataC();
                spmd.setShortName( propertyValue );
                spmd.setDescription( propertyName + " SPMD Desc" );
                spmd.setServerType( "WL" );
                spmd.setRequiresRestart( "NO" );
                spmd.setValidationInfo( "XXXXXX" );
                spmd.setValidValues( "No;YES;MAYBE" );
                spmd.setDefaultValue( "DEFAULT_NO_YES" );
                SystemPropertyMetaDataC spmdReg = ( SystemPropertyMetaDataC ) tx.getUOW().registerObject( spmd );
                SystemPropertyC spReg = ( SystemPropertyC ) tx.getUOW().registerObject( sp );
                spReg.setSystemPropertyMetaData( spmdReg );
            }
            else
            {
                log( "Update the existing system property." );
                SystemPropertyC sp = ( SystemPropertyC ) spOld.getRegisteredObject();
                sp.setValue( propertyValue );
            }

            HashMap<String, Object> propertiesMap = new HashMap<String, Object>( 4 );
            propertiesMap.put( JMSConstantsC.EVENT_KEY, JMSConstantsC.SYSTEM_PROP_ADD_OPERATION );
            propertiesMap.put( JMSConstantsC.SHORT_NAME_KEY, propertyName );
            propertiesMap.put( JMSConstantsC.VALUE_KEY, propertyValue );
            propertiesMap.put( JMSConstantsC.SCOPE_KEY, scope );
            propertiesMap.put( JMSConstantsC.SERVER_NAMES_KEY, scope.equals( SystemPropertyC.SCOPE_TYPE_GROUP ) ? vs.getServerGroup() : ( scope.equals( SystemPropertyC.SCOPE_TYPE_LOCAL ) ?
                    vs.getShortName() : SystemPropertyC.SCOPE_TYPE_GLOBAL ) );
            tx.addRemoteFunctor( ConfigRemoteNotificationFunctorC.class.getName(), propertiesMap );

            tx.commit();
            Thread.sleep( 10000 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
        log( "SystemPropertyPTestC:testAddProperty - END" );
    }

    private void removeProperty( String propertyName, String scope )
    {
        log.info( "ConfigurationPropertyTestC:removeProperty property : " + propertyName + " scope : " + scope );

        try
        {
            IdcSessionManager.getInstance().setTransaction( null );
            IdcTransaction tx = IdcSessionManager.getInstance().newTransaction();
            IdcSessionManager.getInstance().setTransaction( tx );
            tx = IdcSessionManager.getInstance().getTransaction();
            tx.getUOW().addReadOnlyClass( NamespaceGroupC.class );
            tx.getUOW().addReadOnlyClass( NamespaceC.class );
            tx.getUOW().addReadOnlyClass( VirtualServerC.class );

            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( SystemPropertyC.class );
            ExpressionBuilder aBuilder = new ExpressionBuilder();
            Expression exp = aBuilder.get( "shortName" ).equal( propertyName ).and( aBuilder.get( "scope" ).equal( scope ) );
            raq.setSelectionCriteria( exp );
            Collection spColl = ( Collection ) tx.getUOW().executeQuery( raq );
            Iterator iter = spColl.iterator();
            ServerMBean smb = ConfigurationFactory.getServerMBean();
            VirtualServer vs = ( VirtualServer ) new ReadNamedEntityC().execute( VirtualServerC.class, smb.getVirtualServerName() );
            while ( iter.hasNext() )
            {
                SystemPropertyC spReg = ( SystemPropertyC ) tx.getUOW().registerObject( ( SystemPropertyC ) iter.next() );
                tx.getUOW().deleteObject( spReg );
                log.info( "Removed property : " + propertyName + " at scope=" + spReg.getScope() );
            }

            HashMap<String, Object> propertiesMap = new HashMap<String, Object>( 4 );
            propertiesMap.put( JMSConstantsC.EVENT_KEY, JMSConstantsC.SYSTEM_PROP_DELETED_KEY );
            propertiesMap.put( JMSConstantsC.SHORT_NAME_KEY, propertyName );
            propertiesMap.put( JMSConstantsC.VALUE_KEY, " " );
            propertiesMap.put( JMSConstantsC.SCOPE_KEY, scope );
            propertiesMap.put( JMSConstantsC.SERVER_NAMES_KEY, scope.equals( SystemPropertyC.SCOPE_TYPE_GROUP ) ? vs.getServerGroup() : ( scope.equals( SystemPropertyC.SCOPE_TYPE_LOCAL ) ?
                    vs.getShortName() : SystemPropertyC.SCOPE_TYPE_GLOBAL ) );
            tx.addRemoteFunctor( ConfigRemoteNotificationFunctorC.class.getName(), propertiesMap );

            tx.commit();
            Thread.sleep( 10000 );
        }
        catch ( Exception e )
        {
            fail( "ConfigurationPropertyTestC:removePropertyFromAllScopes. exception", e );
        }
    }

    private SystemPropertyC getSystemPropertyForScope( UnitOfWork uow, String propertyName, String scope )
    {
        SystemPropertyC sp = null;
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass( SystemPropertyC.class );
        ExpressionBuilder aBuilder = new ExpressionBuilder();
        Expression expr1 = aBuilder.get( "shortName" ).equal( propertyName );
        Expression exp = expr1.and( aBuilder.get( "scope" ).equal( scope ) );
        raq.setSelectionCriteria( exp );
        Collection spColl = ( Collection ) uow.executeQuery( raq );
        Iterator iter = spColl.iterator();
        while ( iter.hasNext() )
        {
            sp = ( SystemPropertyC ) iter.next();
        }
        return sp;
    }


}
