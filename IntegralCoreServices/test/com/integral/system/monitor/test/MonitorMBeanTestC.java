package com.integral.system.monitor.test;

import com.integral.system.monitor.MonitorFactory;
import com.integral.system.monitor.MonitorMBeanC;
import com.integral.test.MBeanTestCaseC;

/**
 * <AUTHOR> Development Corporation.
 */
public class MonitorMBeanTestC extends MBeanTestCaseC
{
    MonitorMBeanC monitorMBean = ( MonitorMBeanC ) MonitorFactory.getMonitorMBean();

    public MonitorMBeanTestC( String aName )
    {
        super( aName );
    }

    public void testProperties()
    {

        testProperty( monitorMBean, "databaseHealthCheckEnabled", "IDC.System.Monitor.Database.HealthCheck", MBeanTestCaseC.BOOLEAN );
        testProperty( monitorMBean, "monitorAdminPermission", "IDC.System.Monitor.Admin.Permission", MBeanTestCaseC.STRING );
        testProperty( monitorMBean, "switchedToAdminOnJMSFailure", "IDC.System.Monitor.JMS.SwitchToAdminOnFailure", MBeanTestCaseC.BOOLEAN );

    }
}
