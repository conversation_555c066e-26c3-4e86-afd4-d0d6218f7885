package com.integral.transport.multicast.test;

import com.integral.broker.model.Stream;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.test.PTestCaseC;
import com.integral.transport.multicast.MulticastAddress;
import com.integral.transport.multicast.MulticastAddressC;
import com.integral.transport.multicast.MulticastDao;
import com.integral.transport.multicast.MulticastGroup;
import com.integral.transport.multicast.MulticastGroupC;
import com.integral.transport.multicast.MulticastPort;
import com.integral.transport.multicast.MulticastPortC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import org.eclipse.persistence.sessions.UnitOfWork;
import org.junit.Assert;

import java.util.Collection;

public class MulticastDaoTestC extends PTestCaseC
{
    private static Log log = LogFactory.getLog( MulticastDaoTestC.class );

    String providerShortName = "DBNA";
    private String recycleProviderName = "DBNB";
    private String noStreamsProviderName = "BNK2";

    public MulticastDaoTestC( String aName )
    {
        super( aName, false );
    }

    /*
    public void testSaveGroupWithNoStreams()
    {
        try
        {
            trace( "Starting testSaveMulticastAddressGroup .. " );

            UnitOfWork uow = getSession().acquireUnitOfWork();
            uow.removeReadOnlyClass( OrganizationC.class );
            uow.removeReadOnlyClass( MulticastGroupC.class );
            Organization org = ReferenceDataCacheC.getInstance().getOrganization( noStreamsProviderName );
            Organization registeredOrg = ( Organization ) uow.registerObject( org );
            registeredOrg.setMulticastGroup( null );
            uow.commit();

            MulticastPort port = MulticastDao.savePort( null, "RecyleTestPort", 12345 );
            assertTrue( "Could not create port", port != null && port.getPort() == 12345 );

            deleteGroup( noStreamsProviderName );

            // Now save the addresses
            MulticastGroup group = MulticastDao.saveMulticastAddressGroup( noStreamsProviderName, port.getLogicalPortName(), true, true );
            assertNotNull( group );
            assertNotNull( group.getOrganization() );
            assertNotNull( group.getLogicalPort() );
            //assertTrue( group.getAddresses().size() == 1 );
            //assertTrue( (( MulticastAddress ) group.getAddresses().get( 0 ) ).getStream() == null );
        }
        catch ( Exception e )
        {
            fail( "testSaveGroupWithNoStreams failed ", e );
        }
    }

    public void testSaveGroups() throws Exception
    {
        trace( "Starting testSaveGroups  .. " );
        this.testSaveMulticastAddressGroup();
    }

    public void testGetGroups() throws Exception
    {
        trace( "Starting testGetGroups .. " );
        this.testGetMulticastAddressGroupByProviderShortName();

    }

    private boolean deleteGroup( String providerShortName ) throws IdcNoSuchObjectException
    {
        UnitOfWork uow = getSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( MulticastAddressC.class );
        uow.removeReadOnlyClass( MulticastGroupC.class );
        return deleteGroup( MulticastDao.getMulticastAddressGroupByProviderShortName( providerShortName ), uow );
    }

    private boolean deleteGroup( MulticastGroup group, UnitOfWork uow )
    {
        if ( group != null )
        {
            for ( Object addrObj : group.getAddresses() )
            {
                MulticastAddress addr = ( MulticastAddress ) addrObj;
                if ( addr.getStream() != null )
                {
                    Stream dbStream = ( Stream ) uow.registerObject( addr.getStream() );
                    dbStream.setMulticastAddress( null );
                }
                uow.deleteObject( uow.registerObject( addr ) );
            }
            Organization org = ( Organization ) uow.registerObject( group.getOrganization() );
            org.setMulticastGroup( null );
            uow.deleteObject( uow.registerObject( group ) );

            uow.commit();
            return true;
        }
        else
        {
            return false;
        }
    }

    public void testSaveMulticastAddressGroup() throws Exception
    {
        trace( "Starting testSaveMulticastAddressGroup .. " );

        UnitOfWork uow = getSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( OrganizationC.class );
        uow.removeReadOnlyClass( MulticastGroupC.class );
        Organization org = ReferenceDataCacheC.getInstance().getOrganization( providerShortName );
        Organization registeredOrg = ( Organization ) uow.registerObject( org );
        registeredOrg.setMulticastGroup( null );
        uow.commit();


        uow = getSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( MulticastAddressC.class );
        uow.removeReadOnlyClass( MulticastGroupC.class );

        MulticastGroup group = MulticastDao.getMulticastAddressGroupByProviderShortName( providerShortName );
        Long groupId = ( group != null ? group.getObjectID() : null );
        trace( "GroupId before the test is " + groupId );

        deleteGroup( group, uow );

        assertTrue( "After deleting the MulticastGroup, still the Organization has an associated MulticastGroup",
                org.getMulticastGroup() == null );

        group = MulticastDao.getMulticastAddressGroupByProviderShortName( providerShortName );
        assertTrue( "After deleting the MulticastGroup, it is still in the DB", group == null );

        if ( groupId != null )
        {
            Collection<MulticastAddress> addresses = MulticastDao.getMulticastAddressesByGroupId( groupId );
            assertTrue( "After deleting the multicastAddresses, some still exist in the DB (" + addresses + ")", addresses.size() == 0 );
        }

        MulticastPort port = MulticastDao.savePort( null, "TESTPORT", 55555 );
        assertTrue( "Could not create port", port != null && port.getPort() == 55555 );

        group = MulticastDao.saveMulticastAddressGroup( providerShortName, port.getLogicalPortName(), true, true );
        assertTrue( "Could not create group", group != null && group.getOrganization() != null && group.getLogicalPort() != null );

        group = MulticastDao.getMulticastAddressGroupByProviderShortName( providerShortName );

        if ( group != null )
        {
            trace( "New group contains " + group.getAddresses().size() + " addresses." );
            for ( Object addressObj : group.getAddresses() )
            {
                MulticastAddress address = ( MulticastAddress ) addressObj;
                Stream stream = address.getStream();
                if ( stream != null )
                {
                    assertTrue("Stream did not get the multicast address correctly", stream.getMulticastAddress() != null);
                }
            }
        }

        assertTrue( "Could not create group addresses", group != null && group.getAddresses().size() > 0 );
    }

    public void testGetMulticastAddressGroupByProviderShortName() throws Exception
    {
        try
        {
            MulticastGroup group = MulticastDao.getMulticastAddressGroupByProviderShortName( providerShortName );
            assertTrue( group != null && group.getAddresses().size() > 0 );
        }
        catch ( Exception ex )
        {
            fail( "testSavePort failed", ex );
        }
    } */

    public void testSavePort() throws Exception
    {
        String portName = "DaoTestPort";
        String updatedPortName = "DaoTestPort-Updated";
        int portNumber = 12345;
        int updatedPortNumber = 12346;
        UnitOfWork uow = getSession().acquireUnitOfWork();
        try
        {
            MulticastPort port = MulticastDao.getLogicalPort( portName );
            if ( port != null )
            {
                trace( "Deleting port .." );
                uow.deleteObject( port );
                uow.commit();
                port = MulticastDao.getLogicalPort( portName );
                Assert.assertTrue( "Why did not TopLink delete the port ? ", port == null );
                uow = getSession().acquireUnitOfWork();
            }

            port = MulticastDao.getLogicalPort( updatedPortName );
            uow.removeReadOnlyClass( MulticastPortC.class );
            if ( port != null )
            {
                trace( "Deleting port " + updatedPortName + "  .." );
                uow.deleteObject( port );
                uow.commit();
                port = MulticastDao.getLogicalPort( updatedPortName );
                Assert.assertTrue( "Why did not TopLink delete the port " + updatedPortName + " ? ", port == null );
                uow = getSession().acquireUnitOfWork();
            }

            trace( "Saving port .." );
            port = MulticastDao.savePort( null, portName, portNumber );

            port = MulticastDao.getLogicalPort( portName );
            Assert.assertTrue( "Why did TopLink not create the port ? ",
                    port != null && port.getLogicalPortName().equals( portName )
                            && port.getPort() == portNumber );

            trace( "Updating port .." );
            port = MulticastDao.savePort( port.getObjectID(), updatedPortName, updatedPortNumber );
            port = MulticastDao.getLogicalPort( updatedPortName );
            Assert.assertTrue( "Why did TopLink not update the port ? ",
                    port != null && port.getLogicalPortName().equals( updatedPortName )
                            && port.getPort() == updatedPortNumber );
            trace( "testSavePort SUCCESS" );

        }
        catch ( Exception ex )
        {
            fail( "testSavePort failed", ex );
        }
    }

    /*
    public void _testRecycleAddresses() throws Exception
    {
        trace( "Starting testSaveMulticastAddressGroup .. " );

        UnitOfWork uow = getSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( OrganizationC.class );
        uow.removeReadOnlyClass( MulticastGroupC.class );
        Organization org = ReferenceDataCacheC.getInstance().getOrganization( recycleProviderName );
        Organization registeredOrg = ( Organization ) uow.registerObject( org );
        registeredOrg.setMulticastGroup( null );
        uow.commit();

        MulticastPort port = MulticastDao.savePort( null, "RecyleTestPort", 12345 );
        assertTrue( "Could not create port", port != null && port.getPort() == 12345 );


        deleteGroup( recycleProviderName );

        // Now save the addresses
        MulticastGroup group = MulticastDao.saveMulticastAddressGroup( recycleProviderName, port.getLogicalPortName(), true, true );
        assertTrue( "Could not create group", group != null && group.getOrganization() != null && group.getLogicalPort() != null );

        group = MulticastDao.getMulticastAddressGroupByProviderShortName( recycleProviderName );

        uow = getSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( MulticastGroupC.class );
        uow.removeReadOnlyClass( MulticastAddressC.class );
        int naturalSize = group.getAddresses().size();
        MulticastDao.MAX_ADDRESSES_PER_GROUP = naturalSize + 10;
        MulticastAddress address;
        for ( int i = 0; i < MulticastDao.MAX_ADDRESSES_PER_GROUP; i++ )
        {
            address = MulticastDao.createMulticastAddress( group, null, uow );
        }
        uow.commit();

        int expectedSize = naturalSize + MulticastDao.MAX_ADDRESSES_PER_GROUP;
        group = MulticastDao.getMulticastAddressGroupByProviderShortName( recycleProviderName );
        assertTrue( " Incorrect address numbers (" + group.getAddresses().size() + ") expected: " + expectedSize,
                group.getAddresses().size() == expectedSize );

        uow = getSession().acquireUnitOfWork();
        uow.removeReadOnlyClass( MulticastGroupC.class );
        uow.removeReadOnlyClass( MulticastAddressC.class );
        int i = 0;
        for (; i * 2 < MulticastDao.MAX_ADDRESSES_PER_GROUP; i++ )
        {
            address = ( MulticastAddress ) uow.registerObject( group.getAddresses().get( i * 2 ) );
            uow.deleteObject( address );
        }
        uow.commit();

        expectedSize -= i - 1;
        group = MulticastDao.getMulticastAddressGroupByProviderShortName( recycleProviderName );
        assertTrue( " Incorrect address numbers (" + group.getAddresses().size() + ") expected: " + expectedSize,
                group.getAddresses().size() == expectedSize );

        int j = 0;
        for (; j < i; j++ )
        {
            address = MulticastDao.createMulticastAddress( group, null, uow );
        }
        uow.commit();

        expectedSize += j;
        group = MulticastDao.getMulticastAddressGroupByProviderShortName( recycleProviderName );
        assertTrue( " Incorrect address numbers (" + group.getAddresses().size() + ") expected: " + expectedSize,
                group.getAddresses().size() == expectedSize );

    }
    */

    public void testComputeAddress()
    {
        // Stream testing  - fourth octet will be sent as offset - use it
        System.out.println(" ######### Stream next address est");
        String nextAddress = MulticastDao.computeAddress( "*********", 4);
        System.out.println("Next address "+ nextAddress );
        nextAddress = MulticastDao.computeAddress( "***********", 5 );
        System.out.println("Next address "+ nextAddress );

        System.out.println(" ######### Third octet roll test. \nCurrent address: ***********" );
        // third octet roll:
        nextAddress = MulticastDao.computeAddress( "***********", 0);
        System.out.println("Next address "+ nextAddress );
        //should get rolled now to - 239.1.0,0
        nextAddress = MulticastDao.computeAddress( nextAddress, 0);
        System.out.println("Next address "+ nextAddress );
        // again for verification - *********
        nextAddress = MulticastDao.computeAddress( nextAddress, 0);
        System.out.println("Next address "+ nextAddress );


        System.out.println(" ######### Second octet roll test, \ncurrent address: *************");
        // second octet roll:
        nextAddress = MulticastDao.computeAddress( "*************", 0);
        System.out.println("Next address "+ nextAddress );
        //*************
        nextAddress = MulticastDao.computeAddress( nextAddress, 0);
        System.out.println("Next address "+ nextAddress );
        //240.0.0.0 - couple of more prints
        for (int i=0; i<5 ; i++)
        {
            nextAddress = MulticastDao.computeAddress( nextAddress, 0);
            System.out.println("Next address "+ nextAddress );
        }

    }

    public static void trace( String msg )
    {
        log.info( msg );
        System.out.println( msg );
    }


}
