package com.integral.jsp.test;


import com.integral.jsp.JSPRuleSetC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;


public class JSPRuleSetPTestC
        extends PTestCaseC
{
    static String name = "JSPRuleSet Test";

    public JSPRuleSetPTestC( String name )
    {
        super( name );
    }

    public void testInsert()
    {
        try
        {
            Session session = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();

            JSPRuleSetC jspRS = new JSPRuleSetC();
            jspRS.setShortName( "rs" + System.currentTimeMillis() );
            uow.registerObject( jspRS );
            uow.commit();

            log( "insert JSPRuleSet " );
        }
        catch ( Exception e )
        {
            fail( "Error in insert JSPRuleSet", e );
        }
    }


}
