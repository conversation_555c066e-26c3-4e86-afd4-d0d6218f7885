package com.integral.jsp.test;


import com.integral.jsp.URIReference;
import com.integral.jsp.URIReferenceC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


public class URIReferencePTestC
        extends PTestCaseC
{
    static String name = "URIReference Test";

    public URIReferencePTestC( String name )
    {
        super( name );
    }

    public void testPerform()
    {
        // insertTest();
        // lookupTest();
        testLookup( "requestPage" );
    }

    public void testInsert()
    {
        log( "insert test" );
        try
        {
            Session session = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();

            URIReference uri = new URIReferenceC();
            uri.setShortName( "uri-1" );
            uow.registerObject( uri );

            uow.commit();

            log( "insert URIReference " );
        }
        catch ( Exception e )
        {
            fail( "Error in insert URIReference", e );
        }
    }


    public void testLookup()
    {
        log( "lookup test" );
        try
        {
            Session session = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();

            Vector uris = session.readAllObjects( URIReference.class );
            for ( int i = 0; i < uris.size(); i++ )
            {
                URIReference uri = ( URIReference ) uris.elementAt( i );
                log( "uri = " + uri );
            }


            log( "lookup URIReference " );
        }
        catch ( Exception e )
        {
            fail( "Error in lookup URIReference", e );
        }
    }


    public void testLookup( String name )
    {
        log( "lookup test for name " + name );
        try
        {
            Session session = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr1 = eb.get( "shortName" ).equal( name );
            Expression expr2 = eb.get( "status" ).equal( 'A' );
            Expression expr = expr1.and( expr2 );
            Vector uris = session.readAllObjects( URIReference.class, expr );
            for ( int i = 0; i < uris.size(); i++ )
            {
                URIReference uri = ( URIReference ) uris.elementAt( i );
                log( "uri = " + uri );
            }


            log( "lookup URIReference " );
        }
        catch ( Exception e )
        {
            fail( "Error in lookup URIReference", e );
        }
    }


}
