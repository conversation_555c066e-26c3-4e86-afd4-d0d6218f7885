package com.integral.jsp.test;


import com.integral.jsp.JSPApplication;
import com.integral.jsp.JSPApplicationC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;


public class JSPApplicationPTestC
        extends PTestCaseC
{
    static String name = "JSP Application Test";

    public JSPApplicationPTestC( String name )
    {
        super( name );
    }

    public void testInsert()
    {
        try
        {
            Session session = ( org.eclipse.persistence.sessions.Session ) getPersistenceSession();
            UnitOfWork uow = session.acquireUnitOfWork();

            JSPApplication jspApp = new JSPApplicationC();
            uow.registerObject( jspApp );

            log( "insert JSPApplication " );
        }
        catch ( Exception e )
        {
            fail( "Error in insert JSPApplication", e );
        }
    }


}
