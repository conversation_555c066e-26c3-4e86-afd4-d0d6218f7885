package com.integral.rule.test;

import com.integral.rule.AttributeConstant;
import com.integral.rule.AttributeConstantC;
import junit.framework.TestCase;


public class AttributeConstantTestC
        extends TestCase
{
    public void testConstant()
    {
        AttributeConstant ac1 = new AttributeConstantC();
        ac1.setValueClassName( "java.lang.Double" );

        AttributeConstant ac2 = new AttributeConstantC();
        ac2.setValueClassName( "java.no.MyClass" );

        AttributeConstant ac3 = new AttributeConstantC();
        ac3.setValueClassName( "com.integral.rule.ExceptionMessages.properties" );
        ac3.setStringValue( "com.integral.rule.RuleExecutionException" );
        System.out.println( ac3.getValue() );

        AttributeConstant ac4 = new AttributeConstantC();
        ac4.setValueClassName( "com.integral.rule.RuleFactory" );
        ac4.setStringValue( "newDateCondition" );
        System.out.println( ac4.getValue() );

        /*
          AttributeConstant ac4 = new AttributeConstantC();
          ac4.setValueClassName ( "com.integral.system.configuration.ConfigurationFactory" );
          ac4.setStringValue("serverMBean.earName");
          System.out.println ( ac4.getValue() );
          */

    }
}
