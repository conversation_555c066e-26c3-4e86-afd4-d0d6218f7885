package com.integral.rule.test;

import com.integral.rule.TimeCondition;
import com.integral.rule.TimeConditionC;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;
import java.util.Vector;


public class TimeConditionPTestC
        extends PTestCaseC
{
    public TimeConditionPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        TimeZone tm = TimeZone.getTimeZone( "GMT" );
        TimeZone.setDefault( tm );

        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        // suite.addTest(new TimeConditionPTestC("condTest"));
        suite.addTest( new TimeConditionPTestC( "insertTest" ) );
        suite.addTest( new TimeConditionPTestC( "lookupTest" ) );
        return suite;
    }

    public void condTest()
    {
        try
        {
            SimpleDateFormat formatter = new SimpleDateFormat( "hh:mm zzz" );
            Date dt1 = formatter.parse( "6:00 PST" );
            Date dt2 = formatter.parse( "18:00 PST" );
            Date dt3 = formatter.parse( "17:50 PST" );

            Time t1 = new Time( dt1.getTime() );
            Time t2 = new Time( dt2.getTime() );
            Time t3 = new Time( dt3.getTime() );

            if ( t1.compareTo( t3 ) == 0 )
            {
                log( " " + t1 + "  equals to " + t3 );
            }
            else if ( t1.compareTo( t3 ) < 0 )
            {
                log( " " + t1 + "  before  " + t3 );
            }
            else if ( t1.compareTo( t3 ) > 0 )
            {
                log( " " + t1 + "  after  " + t3 );
            }
            if ( t3.compareTo( t1 ) == 0 )
            {
                log( " " + t3 + "  equals to " + t1 );
            }
            else if ( t3.compareTo( t1 ) < 0 )
            {
                log( " " + t3 + "  before  " + t1 );
            }
            else if ( t3.compareTo( t1 ) > 0 )
            {
                log( " " + t3 + "  after  " + t1 );
            }

            if ( t2.compareTo( t3 ) == 0 )
            {
                log( " " + t2 + "  equals to " + t3 );
            }
            else if ( t2.compareTo( t3 ) < 0 )
            {
                log( " " + t2 + "  before  " + t3 );
            }
            else if ( t2.compareTo( t3 ) > 0 )
            {
                log( " " + t2 + "  after  " + t3 );
            }

            if ( t3.compareTo( t2 ) == 0 )
            {
                log( " " + t3 + "  equals to " + t2 );
            }
            else if ( t2.compareTo( t2 ) < 0 )
            {
                log( " " + t3 + "  before  " + t2 );
            }
            else if ( t3.compareTo( t2 ) > 0 )
            {
                log( " " + t3 + "  after  " + t2 );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTest" );
        }

    }

    public void lookupTest()
    {
        try
        {
            SimpleDateFormat formatter = new SimpleDateFormat( "hh:mm zzz" );
            Date dt1 = formatter.parse( "6:00 PST" );
            Date dt2 = formatter.parse( "18:00 PST" );
            Time t1 = new Time( dt1.getTime() );

            TimeCondition mCond = new TimeConditionC();
            mCond.setTime1( t1 );
            mCond.setTime2( new Time( dt2.getTime() ) );

            mCond.setPropertyName( "$time.PST" );
            mCond.setOp1( "LE" );
            mCond.setOp2( "GE" );

            // UOW
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            Vector conds = getPersistenceSession().readAllObjects( TimeCondition.class );
            for ( int i = 0; i < conds.size(); i++ )
            {
                TimeCondition cond = ( TimeCondition ) conds.elementAt( i );
                log( "time condition " + cond );
                log( "   time 1: " + cond.getTime1() );
                log( "   time 1 msec: " + cond.getTime1().getTime() / 3600 );
                log( "   time 2: " + cond.getTime2() );
                log( "   time 2 msec: " + cond.getTime2().getTime() / 3600 );

                try
                {
                    boolean isMatch1 = cond.isMatch( mCond );
                    log( "   compare to another TC: " + isMatch1 );

                    boolean isMatch2 = cond.isMatch( cond );
                    log( "   compare to self      : " + isMatch2 );
                }
                catch ( Exception e2 )
                {
                    log( "   error comparing : " + cond );
                }
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTest" );
        }

    }

    public void insertTest()
    {
        try
        {
            // UOW
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            // SimpleDateFormat formatter = new SimpleDateFormat ("hh:mm zzz");
            SimpleDateFormat formatter = new SimpleDateFormat( "HH:mm z" );
            formatter.setTimeZone( TimeZone.getTimeZone( "GMT" ) );

            Date dt1 = formatter.parse( "6:00 PST" );
            Date dt2 = formatter.parse( "18:00 PST" );

            TimeCondition mCond = new TimeConditionC();
            mCond.setTime1( new Time( dt1.getTime() ) );
            mCond.setTime2( new Time( dt2.getTime() ) );

            log( "   ADD time 1 msec: " + mCond.getTime1().getTime() / 3600 );
            log( "   ADD time 2 msec: " + mCond.getTime2().getTime() / 3600 );

            mCond.setPropertyName( "$time.PST" );
            mCond.setOp1( "GE" );
            mCond.setOp2( "LE" );

            uow.registerObject( mCond );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }

    }


}
