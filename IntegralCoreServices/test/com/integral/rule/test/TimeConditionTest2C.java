package com.integral.rule.test;

import com.integral.rule.TimeCondition;
import com.integral.rule.TimeConditionC;

import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.Date;


public class TimeConditionTest2C
{
    public static void main( String args[] )
    {
        try
        {
            SimpleDateFormat formatter = new SimpleDateFormat( "HH:mm zzz" );
            Date dt1 = formatter.parse( "20:00 PST" );
            Date dt2 = formatter.parse( "3:00 PST" );
            Time t1 = new Time( dt1.getTime() );
            Time t2 = new Time( dt2.getTime() );

            TimeCondition mCond = new TimeConditionC();
            mCond.setTime1( t1 );
            mCond.setTime2( t2 );

            System.out.println( "Time1 = " + t1 );
            System.out.println( "Time2 = " + t2 );

            mCond.setPropertyName( "$TIME.PST" );
            mCond.setOp1( "LE" );
            mCond.setOp2( "GE" );

            boolean isMatch1 = mCond.isMatch( null );
            System.out.println( "isMatch " + isMatch1 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

}
