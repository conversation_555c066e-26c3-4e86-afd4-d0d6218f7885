/**
 * Copyright Integral Development Corp.
 *
 * User: inders
 * Date: Jul 17, 2003
 * Time: 2:16:26 PM
 *
 * <AUTHOR> Development Corp.
 */
package com.integral.rule.test;

import com.integral.rule.JavaAction;
import com.integral.rule.JavaActionC;
import com.integral.rule.JavaCondition;
import com.integral.rule.JavaConditionC;
import com.integral.rule.Rule;
import com.integral.rule.RuleC;
import com.integral.rule.RuleExecutionException;
import com.integral.rule.RuleSet;
import com.integral.rule.RuleSetC;

/**
 * <AUTHOR> Development Corp.
 */
public class TestJavaRules
{
    public void test()
    {
        RuleSet rs = new RuleSetC();
        JavaCondition jc = new JavaConditionC();
        jc.setImports( "import java.util.*;" );
        jc.setScript( "System.out.println(\"Hello World from JavaCondition....\");\nreturn new Boolean(true);" );
        JavaAction ja = new JavaActionC();
        ja.setImports( "import java.util.*;" );
        ja.setScript( "System.out.println(\"Hello World from JavaAction...\");" );
        Rule rule = new RuleC();
        rule.setCondition( jc );
        rule.addAction( ja );
        rs.addRule( rule );
        try
        {
            for ( int i = 0; i < 5; i++ )
            {
                long now = System.currentTimeMillis();
                rs.execute( new Object() );
                long then = System.currentTimeMillis();
                System.out.println( ( then - now ) + "ms." );
            }
        }
        catch ( RuleExecutionException e )
        {
            e.printStackTrace();
        }
    }

    public static void main( String[] args )
    {
        TestJavaRules t = new TestJavaRules();
        t.test();
        System.exit( 0 );
    }
}
