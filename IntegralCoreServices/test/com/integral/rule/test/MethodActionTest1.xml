<methodAction>
    <methodParameter>
        <attributeConstant valueClass="java.lang.String">abc</attributeConstant>
    </methodParameter>
    <methodParameter parameterClass="com.integral.workgroup.WorkGroup">
        <attributeConstant valueClass="com.integral.workgroup.WorkGroup">null</attributeConstant>
    </methodParameter>
    <methodName>update</methodName>
    <receiverObject valueClass="com.integral.rule.test.MethodActionTestC">new</receiverObject>
</methodAction>
