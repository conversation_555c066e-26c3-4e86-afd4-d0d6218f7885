package com.integral.rule.test;

import com.integral.persistence.Entity;
import com.integral.rule.AttributeConstant;
import com.integral.rule.AttributeConstantC;
import com.integral.rule.MethodAction;
import com.integral.rule.MethodActionC;
import com.integral.rule.MethodParameter;
import com.integral.rule.MethodParameterC;
import com.integral.rule.Rule;
import com.integral.xml.binding.JavaXMLBinder;
import com.integral.xml.binding.JavaXMLBinderFactory;
import com.integral.xml.mapping.XMLMappingLoaderC;
import junit.framework.TestCase;

import java.io.InputStream;
import java.io.InputStreamReader;


public class MethodActionTestC
        extends TestCase
{
    public MethodActionTestC()
    {

    }

    public void setUp()
            throws Exception
    {
        super.setUp();

        try
        {
            // load all xml mappings
            XMLMappingLoaderC loader1 = new XMLMappingLoaderC();
            loader1.preload();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "couldn't load xml mapping file" + e );
        }
    }


    public void testActionExecution()
    {
        AttributeConstant dblConstant = new AttributeConstantC();
        dblConstant.setValueClassName( "java.lang.Double" );
        dblConstant.setStringValue( "-30.45" );

        AttributeConstant mathClass = new AttributeConstantC();
        mathClass.setValueClassName( "java.lang.Class" );
        mathClass.setStringValue( "java.lang.Math" );

        MethodParameter dparam = new MethodParameterC();
        dparam.setConstant( dblConstant );
        dparam.setParameterClassName( "double" );

        MethodAction action = new MethodActionC();
        action.setMethodName( "abs" );
        action.setReceiverClassName( "java.lang.Math" );
        action.addMethodParameter( dparam );

        MethodParameter mparam = new MethodParameterC();
        mparam.setParameterMethod( action );
        mparam.setParameterClassName( "double" );

        MethodAction action2 = new MethodActionC();
        action2.setMethodName( "getNumberOfPrecision" );
        action2.setReceiverClassName( "com.integral.util.MathUtilC" );
        action2.addMethodParameter( mparam );


        Object result = null;

        try
        {
            result = action2.executeWithReturn( null );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }

        System.out.println( "Math.ceil(abs(-30.45)) is " + result );

    }

    public void update( String arg1, Entity arg2 )
    {
        String arg1X = ( arg1 == null ) ? "EMPTY/NULL" : arg1;
        String arg2X = ( arg2 == null ) ? "EMPTY/NULL" : arg2.toString();

        System.out.println( "MethodActionTestC: update(" + arg1X + ',' + arg2X + ')' );
    }

    public void testMethodActionWithNullArgConstant()
    {
        try
        {
            InputStream xmlStream = this.getClass().getClassLoader().getResourceAsStream( "MethodActionTest1.xml" );
            InputStreamReader xmlReader = new InputStreamReader( xmlStream );

            JavaXMLBinder conv = JavaXMLBinderFactory.newJavaXMLBinder();
            MethodAction action = ( MethodAction ) conv.convertFromXML( xmlReader, "Integral" );
            System.out.println( "method action = " + action );
            // action.execute(this);
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "error invoking method action" );
        }

    }

    /**
     * Negative unit test
     */
    public void testMethodActionWithNoArgConstant()
    {
        try
        {
            InputStream xmlStream = this.getClass().getClassLoader().getResourceAsStream( "MethodActionTest2.xml" );
            InputStreamReader xmlReader = new InputStreamReader( xmlStream );

            JavaXMLBinder conv = JavaXMLBinderFactory.newJavaXMLBinder();
            MethodAction action = ( MethodAction ) conv.convertFromXML( xmlReader, "Integral" );
            action.execute( this );
            fail( "Excepected illegal argument exception" );
        }
        catch ( Exception e )
        {
            // e.printStackTrace();
            //
        }

    }


    public void testJacksXML()
    {
        try
        {
            InputStream xmlStream = this.getClass().getClassLoader().getResourceAsStream( "LiquidityProviderDefaultRuleTemplate.xml" );
            InputStreamReader xmlReader = new InputStreamReader( xmlStream );

            JavaXMLBinder conv = JavaXMLBinderFactory.newJavaXMLBinder();
            Rule rule = ( Rule ) conv.convertFromXML( xmlReader, "Integral" );
            System.out.println( "rule SN = " + rule.getShortName() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "error invoking method action" );
        }

    }

}
