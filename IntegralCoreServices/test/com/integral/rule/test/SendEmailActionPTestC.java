package com.integral.rule.test;


import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.rule.SendEmailAction;
import com.integral.rule.SendEmailActionC;
import com.integral.test.PTestCaseC;
import com.integral.xml.binding.JavaXMLBinder;
import com.integral.xml.binding.JavaXMLBinderFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.io.StringReader;
import java.util.ArrayList;
import java.util.Vector;

/**
 * Tests send email action
 */
public class SendEmailActionPTestC
        extends PTestCaseC
{
    public SendEmailActionPTestC( String aName )
    {
        super( aName );
    }

    public void testCleanup()
    {
        UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "status" ).equal( 'T' );
        Vector delActions = new Vector( 2 );
        Vector actions = this.getPersistenceSession().readAllObjects( SendEmailAction.class, expr );
        for ( int i = 0; i < actions.size(); i++ )
        {
            SendEmailAction action = ( SendEmailAction ) actions.elementAt( i );
            if ( action.getTos() == null || action.getTos().size() == 0 || "".equals( action.getFrom() ) )
            {
                delActions.add( action );
            }
        }
        uow.deleteAllObjects( delActions );
        uow.commit();
    }

    public void testSendEmailAllFields()
    {
        try
        {
            SendEmailAction action = new SendEmailActionC();
            action.setFrom( "<EMAIL>" );
            action.setTo( "<EMAIL>" );
            action.setCC( "<EMAIL>" );
            action.setSubject( "test testSendEmailAllFields" );
            action.setBody( "body testSendEmailAllFields" );
            action.execute( null );
        }
        catch ( Exception e )
        {
            fail( "error sending email", e );
        }
    }

    public void testSendEmailNoFroms()
    {
        try
        {
            SendEmailAction action = new SendEmailActionC();
            action.setTo( "<EMAIL>" );
            action.setCC( "<EMAIL>" );
            action.setSubject( "test testSendEmailNoFroms" );
            action.setBody( "body testSendEmailNoFroms" );
            action.execute( null );
        }
        catch ( Exception e )
        {
            fail( "error sending email", e );
        }
    }

    public void testSendEmailMultipleTos()
    {
        try
        {
            SendEmailAction action = new SendEmailActionC();
            ArrayList tos = new ArrayList( 2 );
            tos.add( "<EMAIL>" );
            tos.add( "<EMAIL>" );
            action.setTos( tos );
            action.setCC( "<EMAIL>" );
            action.setSubject( "test testSendEmailMultipleTos" );
            action.setBody( "body testSendEmailMultipleTos" );
            action.execute( null );
        }
        catch ( Exception e )
        {
            fail( "error sending email", e );
        }
    }

    public void testSendEmailMultipleCCs()
    {
        try
        {
            SendEmailAction action = new SendEmailActionC();
            ArrayList tos = new ArrayList( 2 );
            tos.add( "<EMAIL>" );
            tos.add( "<EMAIL>" );
            action.setTos( tos );
            ArrayList ccs = new ArrayList( 2 );
            ccs.add( "<EMAIL>" );
            ccs.add( "<EMAIL>" );
            action.setCCs( ccs );
            action.setSubject( "test testSendEmailMultipleCCs" );
            action.setBody( "body testSendEmailMultipleCCs" );
            action.execute( null );
        }
        catch ( Exception e )
        {
            fail( "error sending email", e );
        }
    }

    /**
     * Tests sending a html email
     */
    public void testSendHTMLEmail()
    {
        try
        {
            SendEmailAction action = new SendEmailActionC();
            ArrayList tos = new ArrayList( 2 );
            tos.add( "<EMAIL>" );
            action.setTos( tos );
            action.setSubject( "Test HTML Email" );
            action.setBody( "<HTML><HEAD><TITLE>Test HTML Email</TITLE></HEAD><BODY><H1>Section1</H1>Test email body with sections</BODY></HTML>" );
            action.execute( null );
        }
        catch ( Exception e )
        {
            fail( "error sending email", e );
        }
    }

    public void testSendEmailExpansion()
    {
        try
        {
            Request request = new RequestC();
            request.setNotes( "body $GUID status=$status\nmy notes are $MYNOTES.\nThis next prop is unexpanded: $foobar." );

            SendEmailAction action = new SendEmailActionC();
            action.putCustomField( "COMPANY", "integral.com" );
            action.putCustomField( "GUID", "$GUID" );
            action.putCustomField( "status", "$status" );
            action.putCustomField( "MYNOTES", "mynotes" );

            action.setFrom( "test1@$COMPANY" );
            action.setTo( "test@$COMPANY" );
            action.setCC( "test@$COMPANY" );
            action.setSubject( "request $GUID" );
            action.setBody( "$notes" );

            action.execute( request );
        }
        catch ( Exception e )
        {
            fail( "error sending email", e );
        }
    }

    public void testSendSimpleXmlEmailRule()
    {
        try
        {
            String xml = "<emailAction>"
                    + " <emailFrom><EMAIL></emailFrom>"
                    + " <emailTo><EMAIL></emailTo>"
                    + " <emailCC><EMAIL></emailCC>"
                    + " <emailSubject>Test Email</emailSubject>"
                    + "<emailBody><![CDATA[Hello]]></emailBody>"
                    + "<property type=\"java.lang.String\" key=\"COMPANY\">integral.com</property>"
                    + "<property type=\"java.lang.String\" key=\"GUID\">$GUID</property>"
                    + "<property type=\"java.lang.String\" key=\"status\">$status</property>"
                    + "<property type=\"java.lang.String\" key=\"MYNOTES\">my notes</property>"
                    + "</emailAction>";
            StringReader xmlReader = new StringReader( xml );

            Request request = new RequestC();
            request.setNotes( "body $GUID status=$status\nmy notes are $MYNOTES.\nThis next prop is unexpanded: $foobar." );

            JavaXMLBinder conv = JavaXMLBinderFactory.newJavaXMLBinder();
            SendEmailAction action = ( SendEmailAction ) conv.convertFromXML( xmlReader, "Integral" );
            action.execute( request );
        }
        catch ( Exception e )
        {
            fail( "error sending email", e );
        }
    }

    public void testSendExpandedXmlEmailRule()
    {
        try
        {
            String xml = "<emailAction>"
                    + " <emailFrom><EMAIL></emailFrom>"
                    + " <emailTo><EMAIL></emailTo>"
                    + " <emailCC><EMAIL></emailCC>"
                    + " <emailSubject>Test Email for request $GUID</emailSubject>"
                    + " <emailBody><![CDATA[$notes]]></emailBody>"
                    + " <property type=\"java.lang.String\" key=\"COMPANY\">integral.com</property>"
                    + " <property type=\"java.lang.String\" key=\"GUID\">$GUID</property>"
                    + " <property type=\"java.lang.String\" key=\"status\">$status</property>"
                    + " <property type=\"java.lang.Double\" key=\"bignum\">12413455.0123245</property>"
                    + " <property type=\"java.lang.String\" key=\"MYNOTES\">my notes</property>"
                    + " <property type=\"java.lang.String\" key=\"DecimalFormat\">###,###,###.0000</property>"
                    + "</emailAction>";
            StringReader xmlReader = new StringReader( xml );

            Request request = new RequestC();
            request.setNotes( "body $GUID status=$status\nmy notes are $MYNOTES.\na big number: $bignum\nThis next prop is unexpanded: $foobar." );

            JavaXMLBinder conv = JavaXMLBinderFactory.newJavaXMLBinder();
            SendEmailAction action = ( SendEmailAction ) conv.convertFromXML( xmlReader, "Integral" );
            action.execute( request );
        }
        catch ( Exception e )
        {
            fail( "error sending email", e );
        }
    }

    public void testPersistenceInsert()
    {

        try
        {
            String xml = "<emailAction>"
                    + " <emailFrom><EMAIL></emailFrom>"
                    + " <emailTo><EMAIL></emailTo>"
                    + " <emailCC><EMAIL></emailCC>"
                    + " <emailSubject>Test Email for request $GUID</emailSubject>"
                    + " <emailBody><![CDATA[$notes]]></emailBody>"
                    + " <property type=\"java.lang.String\" key=\"COMPANY\">integral.com</property>"
                    + " <property type=\"java.lang.String\" key=\"GUID\">$GUID</property>"
                    + " <property type=\"java.lang.String\" key=\"status\">$status</property>"
                    + " <property type=\"java.lang.String\" key=\"MYNOTES\">my notes</property>"
                    + "</emailAction>";
            StringReader xmlReader = new StringReader( xml );

            Request request = new RequestC();
            request.setNotes( "body $GUID status=$status\nmy notes are $MYNOTES.\nThis next prop is unexpanded: $foobar." );

            JavaXMLBinder conv = JavaXMLBinderFactory.newJavaXMLBinder();
            SendEmailAction action = ( SendEmailAction ) conv.convertFromXML( xmlReader, "Integral" );
            action.setStatus( 'T' );

            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.registerObject( action );
            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "error saving email action", e );
        }
    }

    public void testPersistenceQuery()
    {

        try
        {
            Request request = new RequestC();
            request.setNotes( "body $GUID status=$status\nmy notes are $MYNOTES.\nThis next prop is unexpanded: $foobar." );

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );
            Vector actions = this.getPersistenceSession().readAllObjects( SendEmailAction.class, expr );
            for ( int i = 0; i < actions.size(); i++ )
            {
                SendEmailAction action = ( SendEmailAction ) actions.elementAt( i );
                action.execute( request );
            }
        }
        catch ( Exception e )
        {
            fail( "error querying email action", e );
        }
    }
}
