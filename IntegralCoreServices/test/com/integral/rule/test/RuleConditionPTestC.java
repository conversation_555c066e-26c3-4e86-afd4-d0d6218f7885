package com.integral.rule.test;


import com.integral.persistence.CustomField;
import com.integral.rule.AttributeRuleCondition;
import com.integral.rule.AttributeRuleConditionC;
import com.integral.rule.ConditionAttribute;
import com.integral.rule.ConditionAttributeC;
import com.integral.rule.PredicateCondition;
import com.integral.rule.PredicateConditionC;
import com.integral.rule.RuleConditionCustomFieldC;
import com.integral.test.PTestCaseC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Iterator;
import java.util.Vector;

/**
 * Tests rule conditions
 */
public class RuleConditionPTestC
        extends PTestCaseC
{
    public RuleConditionPTestC( String aName )
    {
        super( aName );
    }


    /**
     * Tests inserting predicate conditions using
     */
    public void testInsertPredicateCondition()
    {
        log.debug( "running testInsertPredicateCondition" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            PredicateCondition cond1 = new PredicateConditionC();
            cond1.setName( "TEST1-" + System.currentTimeMillis() );
            cond1.setStatus( 'T' );
            cond1 = ( PredicateCondition ) uow.registerObject( cond1 );

            PredicateCondition cond2 = new PredicateConditionC();
            cond2.setName( "TEST2-" + System.currentTimeMillis() );
            cond2.setStatus( 'T' );
            cond2 = ( PredicateCondition ) uow.registerObject( cond2 );

            PredicateCondition cond3 = new PredicateConditionC();
            cond3.setName( "TEST3-" + System.currentTimeMillis() );
            cond3.setStatus( 'T' );
            cond3 = ( PredicateCondition ) uow.registerObject( cond3 );

            cond1.setOwnedCondition( "Test2", cond2 );
            cond2.setOwnedCondition( "Test3", cond3 );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testPredicateConditionQuery" );
        }
    }

    /**
     * Tests querying predicate conditions using
     */
    public void testQueryPredicateCondition()
    {
        log.debug( "running testPredicateConditionQuery" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.anyOf( "ownedConditions" ).anyOf( "ownedConditions" ).get( "name" ).like( "TEST3-%" );

            Vector conditions = uow.readAllObjects( com.integral.rule.RuleCondition.class, expr );
            for ( int i = 0; i < conditions.size(); i++ )
            {
                PredicateCondition pc = ( PredicateCondition ) conditions.get( i );
                log.debug( "\t#" + i + ":\t" + pc );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testPredicateConditionQuery" );
        }
    }

    /**
     * Tests inserting attribute rule conditions
     */
    public void testInsertAttributeRuleCondition()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            AttributeRuleCondition cond1 = new AttributeRuleConditionC();
            cond1.setName( "TEST1-" + System.currentTimeMillis() );
            cond1.setStatus( 'T' );
            cond1 = ( AttributeRuleCondition ) uow.registerObject( cond1 );

            cond1.putCustomField( "TEST", "STRING-CF" );

            ConditionAttribute attr1 = new ConditionAttributeC();
            attr1.setStatus( 'T' );
            cond1.getAttributes().add( attr1 );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testInsertAttributeConditionQuery" );
        }
    }

    /**
     * Tests querying attribute rule conditions with status 'T'
     */
    public void testQueryAttributeRuleCondition()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector conditions = uow.readAllObjects( AttributeRuleCondition.class, expr );
            for ( int i = 0; i < conditions.size(); i++ )
            {
                AttributeRuleCondition pc = ( AttributeRuleCondition ) conditions.get( i );
                log.info( "\t#" + i + ":\t" + pc );

                int j = 0;
                Iterator it = pc.getAttributes().iterator();
                while ( it.hasNext() )
                {
                    ConditionAttribute attr1 = ( ConditionAttribute ) it.next();
                    log.info( "\t\tattribute #" + j + ":\t" + attr1 );
                }

                j = 0;
                it = pc.getCustomFields().keySet().iterator();
                while ( it.hasNext() )
                {
                    String key = ( String ) it.next();
                    CustomField cf = ( CustomField ) pc.getCustomField( key );
                    log.info( "\t\tcustom field #" + j + ":\t" + cf );
                }
            }
            uow.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testQueryAttributeConditionQuery" );
        }
    }

    /**
     * Tests deleting attribute rule conditions with status 'T'
     */
    public void testDeleteAttributeRuleCondition()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector conditions = uow.readAllObjects( AttributeRuleCondition.class, expr );
            for ( int i = 0; i < conditions.size(); i++ )
            {
                AttributeRuleCondition pc = ( AttributeRuleCondition ) conditions.get( i );
                log.debug( "\tdeleting #" + i + ":\t" + pc );
                uow.deleteObject( pc );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testDeleteAttributeConditionQuery" );
        }
    }

    /**
     * Tests querying attribute rule condition related classes
     */
    public void testQueryRuleConditionSecondaryClasses()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            Vector result = uow.readAllObjects( ConditionAttribute.class );
            for ( int i = 0; i < result.size(); i++ )
            {
                Object pc = result.get( i );
                log.debug( "\tqueried cond attr #" + i + ":\t" + pc );
            }

            result = uow.readAllObjects( RuleConditionCustomFieldC.class );
            for ( int i = 0; i < result.size(); i++ )
            {
                Object pc = result.get( i );
                log.debug( "\tqueried rule condition cf #" + i + ":\t" + pc );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testQueryRuleConditionSecondaryClasses" );
        }
    }

}
