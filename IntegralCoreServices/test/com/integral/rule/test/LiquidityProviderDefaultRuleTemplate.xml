<rule sortOrder="1000000">
    <shortName>DefaultRule</shortName>
    <longName>DefaultLiquidityRule</longName>
    <description>Default Liquidity Rule.</description>
    <setPropertyAction>
        <name>DefaultActionRadio</name>
        <toProperty>defaultAction</toProperty>
        <attributeConstant valueClass="java.lang.String">Cover</attributeConstant>
    </setPropertyAction>
    <rule>
        <shortName>DefaultSubRule_Cover</shortName>
        <description>This checks for default action string and sets the Quote Provider appropriately</description>
        <attributeCondition>
            <conditionAttribute type="java.lang.String">
                <attributeKey>defaultAction</attributeKey>
                <attributeValue>Cover</attributeValue>
            </conditionAttribute>
        </attributeCondition>
        <setPropertyAction>
            <name>SetDefaultProvider</name>
            <toProperty>QuoteProvider</toProperty>
            <fromProperty>$ruleSet.customFieldValue(defaultLiquidityProvider)</fromProperty>
        </setPropertyAction>
    </rule>
    <rule>
        <shortName>DefaultSubRule_Decline</shortName>
        <description>This checks for default action string and sets the Quote Provider appropriately</description>
        <attributeCondition>
            <conditionAttribute type="java.lang.String">
                <attributeKey>defaultAction</attributeKey>
                <attributeValue>Decline</attributeValue>
            </conditionAttribute>
        </attributeCondition>
        <setPropertyAction>
            <attributeConstant valueClass="com.integral.user.OrganizationC">null</attributeConstant>
            <toProperty>QuoteProvider</toProperty>
        </setPropertyAction>
    </rule>
</rule>