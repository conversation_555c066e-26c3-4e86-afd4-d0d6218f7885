package com.integral.rule.test;


import com.integral.rule.Rule;
import com.integral.rule.RuleCustomFieldC;
import com.integral.rule.RuleFactory;
import com.integral.rule.SetPropertyAction;
import com.integral.test.PTestCaseC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

/**
 * Tests rule custom fields
 */
public class RuleCustomFieldPTestC
        extends PTestCaseC
{
    public RuleCustomFieldPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();

        suite.addTest( new RuleCustomFieldPTestC( "testUpdate" ) );
        return suite;
    }

    public void testUpdate()
    {
        try
        {
            // UOW
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
            Rule rule = ( Rule ) uow.registerObject( RuleFactory.newRule() );
            rule.setShortName( "Test" + System.currentTimeMillis() );
            rule.putCustomField( "MinNotional", 110.0 );
            rule.putCustomField( "MaxNotional", 1000.0 );
            rule.putCustomField( "DirectFX_MinTenor", "1M" );
            rule.putCustomField( "DirectFX_MaxTenor", "1Y" );

            RuleCustomFieldC rcf1 = ( RuleCustomFieldC ) rule.getCustomField( "MinNotional" );
            RuleCustomFieldC rcf2 = ( RuleCustomFieldC ) rule.getCustomField( "MaxNotional" );
            RuleCustomFieldC rcf3 = ( RuleCustomFieldC ) rule.getCustomField( "DirectFX_MinTenor" );
            RuleCustomFieldC rcf4 = ( RuleCustomFieldC ) rule.getCustomField( "DirectFX_MaxTenor" );

            log( "######## BEFORE COMMIT ###########" );
            log( "MinNotional = 		" + rcf1.getDouble() );
            log( "MaxNotional = 		" + rcf2.getDouble() );
            log( "DirectFX_MinTenor =" + rcf3.getValue() );
            log( "DirectFX_MaxTenor =" + rcf4.getValue() );

            Rule ruleObj = ( Rule ) uow.registerObject( rule );
            ruleObj.putCustomField( "MinNotional", new Double( 40 ) );
            ruleObj.putCustomField( "DirectFX_MinTenor", "3M" );
            ruleObj.putCustomField( "DirectFX_MaxTenor", "6M" );

            uow.commit();

            log( "######## AFTER COMMIT ###########" );
            rcf1 = ( RuleCustomFieldC ) rule.getCustomField( "MinNotional" );
            rcf2 = ( RuleCustomFieldC ) rule.getCustomField( "MaxNotional" );
            rcf3 = ( RuleCustomFieldC ) rule.getCustomField( "DirectFX_MinTenor" );
            rcf4 = ( RuleCustomFieldC ) rule.getCustomField( "DirectFX_MaxTenor" );
            log( "MinNotional = 		" + rcf1.getDouble() );
            log( "MaxNotional = 		" + rcf2.getDouble() );
            log( "DirectFX_MinTenor =" + rcf3.getValue() );
            log( "DirectFX_MaxTenor =" + rcf4.getValue() );

            log( "######## BEFORE COMMIT 2 ###########" );
            UnitOfWork uow2 = getPersistenceSession().acquireUnitOfWork();
            ruleObj = ( Rule ) uow2.registerObject( rule );
            ruleObj.putCustomField( "MinNotional", new Double( 41 ) );
            ruleObj.putCustomField( "DirectFX_MinTenor", "4M" );
            ruleObj.putCustomField( "DirectFX_MaxTenor", "7M" );

            uow2.commit();

            log( "######## AFTER COMMIT 2 ###########" );
            rcf1 = ( RuleCustomFieldC ) rule.getCustomField( "MinNotional" );
            rcf2 = ( RuleCustomFieldC ) rule.getCustomField( "MaxNotional" );
            rcf3 = ( RuleCustomFieldC ) rule.getCustomField( "DirectFX_MinTenor" );
            rcf4 = ( RuleCustomFieldC ) rule.getCustomField( "DirectFX_MaxTenor" );
            log( "MinNotional = 		" + rcf1.getDouble() );
            log( "MaxNotional = 		" + rcf2.getDouble() );
            log( "DirectFX_MinTenor =" + rcf3.getValue() );
            log( "DirectFX_MaxTenor =" + rcf4.getValue() );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }

    }

    /**
     * Tests querying rule action custom field by a field mapped in
     * the SetPropertyAction subclass
     */
    public void testQueryRuleActionByConstant()
    {
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder emp = new ExpressionBuilder();
            Expression expr = emp.get( "constant.stringValue" ).notNull();

            Vector ruleActions = uow.readAllObjects( com.integral.rule.RuleActionC.class, expr );
            for ( int i = 0; i < ruleActions.size(); i++ )
            {
                SetPropertyAction spa = ( SetPropertyAction ) ruleActions.get( i );
                log( "\t#" + i + ":\t" + spa );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }

    }

}
