package com.integral.mdg.handler

import com.integral.finance.currency.Currency
import com.integral.finance.currency.CurrencyFactory
import com.integral.provision.CurrencyProvision
import com.integral.provision.CurrencyProvisionQuery
import com.integral.model.OracleEntity
import com.integral.rds.client.ReferenceDataService
import com.integral.rds.exception.ReferenceDataServiceException
import com.integral.testframework.api.*

class CurrencyProvisionHandlerTestC extends BaseHandlerTestC{
	
	public void testCcyProvisionRetrieveAll(){
		String testname = "testCcyProvisionRetrieveAll";
		// create currencies
		def ccy = [
			"EUR",
			"USD",
			"JPY",
			"INR",
			"GBP"
		]
		for(String tmp : ccy){
			Currency c = API.getCcy tmp
			GroovyTestCase.assertTrue(c.getShortName().equals(tmp))
			GroovyTestCase.assertTrue(CurrencyFactory.getCurrency(tmp).getShortName().equals(tmp))
		}

		CurrencyProvisionQuery query = new CurrencyProvisionQuery();
		query.setShortName(testname)
		query.setLimit(6);
		query.setSkip(0);

		List<OracleEntity> ccyList = rds.process(query);
		GroovyTestCase.assertNotNull(ccyList)
		GroovyTestCase.assertEquals(5, ccyList.size())

		List<CurrencyProvision> result = (List<CurrencyProvision>) ccyList;
		for(CurrencyProvision ccyProv: result){
			GroovyTestCase.assertTrue(ccy.contains(ccyProv.getShortName()))
		}
	}

	public void testCcyProvisionRetrieveOneAtATime(){
		String testname = "testCcyProvisionRetrieveOneAtATime";
		// create currencies
		def ccy = [
			"EUR",
			"USD",
			"JPY",
			"INR",
			"GBP"
		]
		for(String tmp : ccy){
			Currency c = API.getCcy tmp
			GroovyTestCase.assertTrue(c.getShortName().equals(tmp))
			GroovyTestCase.assertTrue(CurrencyFactory.getCurrency(tmp).getShortName().equals(tmp))
		}

		List<OracleEntity> ccyList = new ArrayList<OracleEntity>();

		CurrencyProvisionQuery query = new CurrencyProvisionQuery();
		query.setShortName(testname)
		query.setLimit(1);
		int i =0;
		query.setSkip(i)
		
		List<OracleEntity> temp
		try{
			while(!(temp= rds.process(query)).isEmpty()){
				ccyList.addAll(temp)
				i += temp.size()
				query.setSkip(i);
			}
		}catch(ReferenceDataServiceException rdse){
		}

		GroovyTestCase.assertEquals(5, ccyList.size())

		List<CurrencyProvision> result = (List<CurrencyProvision>) ccyList;
		for(CurrencyProvision ccyProv: result){
			GroovyTestCase.assertTrue(ccy.contains(ccyProv.getShortName()))
		}
	}
}