package com.integral.mdg.handler

import com.integral.provision.CurrencyPairProvision
import com.integral.provision.LPProvision
import com.integral.provision.LPProvisionQuery
import com.integral.model.OracleEntity
import com.integral.rds.client.ReferenceDataService
import com.integral.rds.exception.ReferenceDataServiceException
import com.integral.subscription.rules.ordermatching.OrgUtil
import com.integral.testframework.api.*

class LPProvisionHandlerTestC extends BaseHandlerTestC {

	public void testLPProvision(){

		String testName = "testLPProvision";
		def currencyPair = API.getCcyPair "EUR/USD"

		// create org
		LP LP3 = API.createLP testName + "LP";
		LP3.TypeSuperBank true
		LE LP3LE = LP3.legalEntity "LP3LE"
		LP3LE.defaultDealingEntity true

		FI FI2 = API.createFI testName + "FI2";
		LE FI2LE = FI2.legalEntity "FI2LE";
		FI2LE.defaultDealingEntity true

		FI2.addLP LP3
		LP3.addFI FI2
		TP LP3TPFORFI2LE = LP3.createTP FI2LE;
		TP FI2TPFORLP3LE = FI2.createTP LP3LE;

		//add currency pair  and assign stream
		LP3.addCcyPairs "EUR/USD";
		STREAM GROUP1 = LP3.stream "GROUP1";
		GROUP1.defaultStream true
		GROUP1.legalEntity "LP3LE"
		LP3TPFORFI2LE.stream "GROUP1";

		//enable Liquidity provisioning
		String[] ccy = [currencyPair.displayName]
		ORG[] orgs = [LP3]
		OrgUtil.enableLiquidityProvision(FI2, orgs, ccy)

		LQPR LQP = API.createLQPR()

		LQP.addCcyPair "EUR/USD"
		LQP.aggregationProviders LP3
		LQP.executionProviders LP3
		FI2.addLQPR LQP

		LPProvisionQuery query = new LPProvisionQuery(testName + "FI2");

		List<OracleEntity> lpProvList = rds.process(query);

		GroovyTestCase.assertNotNull(lpProvList)
		GroovyTestCase.assertEquals(1, lpProvList.size())
		LPProvision lpProvision = (LPProvision)lpProvList.get(0);

		GroovyTestCase.assertTrue(lpProvision.getShortName().equals(testName + "LP"));
		GroovyTestCase.assertTrue(lpProvision.getRealLP().equals(testName + "LP"));
		GroovyTestCase.assertTrue(lpProvision.getStreamName().equals("GROUP1"));

		List<CurrencyPairProvision> ccyPairs = lpProvision.getCcyPairs();
		GroovyTestCase.assertEquals(1, ccyPairs.size());
		GroovyTestCase.assertTrue(ccyPairs.get(0).getShortName().equals("EUR/USD"));

	}

	public void testLPProvisionMaskedLP(){

		String testName = "testLPProvisionMaskedLP";
		def currencyPair = API.getCcyPair "EUR/USD"

		// create LP
		LP LP3 = API.createLP testName + "LP";
		LP3.TypeSuperBank true
		LE LP3LE = LP3.legalEntity "LP3LE"
		LP3LE.defaultDealingEntity true

		FI FI2 = API.createFI testName + "FI2";
		LE FI2LE = FI2.legalEntity "FI2LE";
		FI2LE.defaultDealingEntity true

		FI2.addLP LP3
		LP3.addFI FI2
		TP LP3TPFORFI2LE = LP3.createTP FI2LE;
		TP FI2TPFORLP3LE = FI2.createTP LP3LE;

		//add currency pair  and assign stream
		LP3.addCcyPairs "EUR/USD";
		STREAM GROUP1 = LP3.stream "GROUP1";
		STREAM GROUP2 = LP3.stream "GROUP2";
		GROUP1.defaultStream true
		GROUP1.legalEntity "LP3LE"
		GROUP2.legalEntity "LP3LE"
		LP3TPFORFI2LE.stream "GROUP1";

		//create a mask lp LP32LP
		LP LP32LP = API.createLP "LP32LP";
		LP32LP.maskOf LP3;
		LE LP32LPLE = LP32LP.legalEntity "LP32LPLE"
		LP32LPLE.defaultDealingEntity true

		//establish trading party relationships
		TP LP32LPTPFORFI2LE = LP32LP.createTP FI2LE;
		TP FI2TPFORLP32LPLE = FI2.createTP LP32LPLE;
		LP32LP.addCcyPairs "EUR/USD";

		FI2.addLP LP32LP
		LP32LP.addFI FI2;
		LP32LPTPFORFI2LE.stream "GROUP2";

		//enable Liquidity provisioning
		String[] ccy = [currencyPair.displayName]
		ORG[] orgs = [LP3, LP32LP]
		OrgUtil.enableLiquidityProvision(FI2, orgs, ccy)

		LQPR LQP = API.createLQPR()

		LQP.addCcyPair "EUR/USD"
		LQP.aggregationProviders LP32LP
		LQP.executionProviders LP32LP
		FI2.addLQPR LQP


		LPProvisionQuery query = new LPProvisionQuery(testName + "FI2");

		List<OracleEntity> lpProvList = rds.process(query);

		// there should be 2 provision objects : 1 for REal lp and another for the masked lp
		GroovyTestCase.assertNotNull(lpProvList)
		GroovyTestCase.assertEquals(2, lpProvList.size())
		LPProvision lpProvision = (LPProvision)lpProvList.get(0);

		GroovyTestCase.assertTrue(lpProvision.getShortName().equals(testName + "LP"));
		GroovyTestCase.assertTrue(lpProvision.getRealLP().equals(testName + "LP"));
		GroovyTestCase.assertTrue(lpProvision.getStreamName().equals("GROUP1"));

		List<CurrencyPairProvision> ccyPairs = lpProvision.getCcyPairs();
		GroovyTestCase.assertEquals(1, ccyPairs.size());
		GroovyTestCase.assertTrue(ccyPairs.get(0).getShortName().equals("EUR/USD"));

		// assert the masked lp provision object too; stream,ccy pair should be that of the real lp
		lpProvision = (LPProvision)lpProvList.get(1);

		GroovyTestCase.assertTrue(lpProvision.getShortName().equals("LP32LP"));
		GroovyTestCase.assertTrue(lpProvision.getRealLP().equals(testName + "LP"));
		GroovyTestCase.assertTrue(lpProvision.getStreamName().equals("GROUP1"));

		ccyPairs = lpProvision.getCcyPairs();
		GroovyTestCase.assertEquals(1, ccyPairs.size());
		GroovyTestCase.assertTrue(ccyPairs.get(0).getShortName().equals("EUR/USD"));
	}

	public void testLPProvisionNoEntity(){

		String testName = "testLPProvisionNoEntity";
		LPProvisionQuery query = new LPProvisionQuery(testName + "FI2");

		try{
			List<OracleEntity> lpProvList = rds.process(query);
		}catch(ReferenceDataServiceException rdse){
			GroovyTestCase.assertTrue(rdse.getMessage().contains("Handler returned null entities"));
		}
	}
}
