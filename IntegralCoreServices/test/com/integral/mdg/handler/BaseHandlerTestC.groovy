package com.integral.mdg.handler

import com.integral.rds.SetupStub
import com.integral.rds.client.ClientFactory
import com.integral.rds.client.ReferenceDataService
import com.integral.rds.config.ServiceConfigMBeanC
import com.integral.rds.registry.InitEntityRegistry
import com.integral.rds.setup.TestServerStartup
import com.integral.system.configuration.IdcMBeanC
import com.integral.testframework.api.*

public class BaseHandlerTestC extends GroovyTestCase{
	protected ReferenceDataService rds;

	@Override
	protected void setUp() throws Exception {
		IdcMBeanC.setShouldReadFromDB(false)
		API.setupInMemoryTestInfrastructure()
		Properties config = SetupStub.initConfig();
		config.put("Idc.RDS.Client.API.Version",ServiceConfigMBeanC.instance.getApiVersion());
		TestServerStartup.startRDSServer(config);

		InitEntityRegistry.init();

		rds = ClientFactory.getFactory().getReferenceDataService()
		GroovyTestCase.assertNotNull(rds)
	}

	@Override
	protected void tearDown() throws Exception {
		TestServerStartup.shutDownRDSServer();
	}
	
	public void testDummy(){
		// To avoid junit throwing "junit.framework.AssertionFailedError: No tests "
	}
}