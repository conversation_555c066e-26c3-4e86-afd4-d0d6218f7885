package com.integral.mdg.handler

import com.integral.provision.OrgProvision
import com.integral.provision.OrgProvisionQuery
import com.integral.model.OracleEntity
import com.integral.rds.client.ReferenceDataService
import com.integral.rds.exception.ReferenceDataServiceException
import com.integral.testframework.api.*

class OrgProvisionQueryHandlerTestC extends BaseHandlerTestC {

	public void testOrgProvision(){
		String testName = "testOrgProvision";
		String le1 = testName + "LE1";
		String le2 = testName + "LE2";
		// create org
		LP LP3 = API.createLP testName;
		LE LP3LE = LP3.legalEntity le1;
		LE LP3LE2 = LP3.legalEntity le2;
		LP3LE.defaultDealingEntity true

		List<String> orgList = new ArrayList<String>(1);
		orgList.add(testName);
		OrgProvisionQuery query = new OrgProvisionQuery(testName, orgList);
		List<OracleEntity> orgProvList = rds.process(query);

		GroovyTestCase.assertNotNull(orgProvList)
		GroovyTestCase.assertEquals(1, orgProvList.size())
		OrgProvision orgProvision = (OrgProvision)orgProvList.get(0);
		GroovyTestCase.assertTrue(orgProvision.getShortName().equals(testName));
		GroovyTestCase.assertTrue(orgProvision.getDefaultLE().equals(le1));

	}

	public void testOrgProvisionNoEntity(){
		String testName = "testOrgProvisionNoEntity";

		List<String> orgList = new ArrayList<String>(1);
		orgList.add(testName);
		OrgProvisionQuery query = new OrgProvisionQuery(testName, orgList);

		try{
			List<OracleEntity> orgProvList = rds.process(query);
			GroovyTestCase.assertTrue(orgProvList.isEmpty());
			//GroovyTestCase.fail("Test expected to throw ReferenceDataServiceException");
		}catch(ReferenceDataServiceException rdse){
			GroovyTestCase.assertTrue(rdse.getMessage().contains("not found in DB"));
		}
	}
}