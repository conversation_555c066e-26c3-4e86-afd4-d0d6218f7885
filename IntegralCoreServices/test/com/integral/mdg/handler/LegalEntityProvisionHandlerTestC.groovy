package com.integral.mdg.handler

import com.integral.provision.LegalEntityProvision
import com.integral.provision.LegalEntityProvisionQuery
import com.integral.provision.OrgProvision
import com.integral.provision.OrgProvisionQuery
import com.integral.model.OracleEntity
import com.integral.rds.client.ReferenceDataService
import com.integral.rds.exception.ReferenceDataServiceException
import com.integral.testframework.api.*


class LegalEntityProvisionHandlerTestC extends BaseHandlerTestC {

	public void testLEProvision(){
		String testName = "testLEProvision";
		String le1 = testName + "LE1";
		String le2 = testName + "LE2";
		// create org
		LP LP3 = API.createLP testName;
		LE LP3LE = LP3.legalEntity le1;
		LE LP3LE2 = LP3.legalEntity le2;

		LegalEntityProvisionQuery query = new LegalEntityProvisionQuery(testName);

		List<OracleEntity> result = rds.process(query);

		GroovyTestCase.assertNotNull(result)
		GroovyTestCase.assertEquals(2, result.size())

		GroovyTestCase.assertTrue(((LegalEntityProvision)result.get(0)).getShortName().equals(le1));
		GroovyTestCase.assertTrue(((LegalEntityProvision)result.get(1)).getShortName().equals(le2));

	}

	public void testLEProvisionNoEntry(){
		String testName = "testLEProvision";
		String le1 = testName + "LE1";
		String le2 = testName + "LE2";
		// create org
		LP LP3 = API.createLP testName;

		LegalEntityProvisionQuery query = new LegalEntityProvisionQuery(testName);
		try{
			List<OracleEntity> result = rds.process(query);
		}catch(ReferenceDataServiceException rdse){
			GroovyTestCase.assertTrue(rdse.getMessage().contains("Handler returned null entities"));
		}


	}

}