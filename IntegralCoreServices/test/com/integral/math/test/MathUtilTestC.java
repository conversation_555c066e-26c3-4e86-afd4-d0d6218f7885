package com.integral.math.test;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.math.MathUtil;
import com.integral.util.MathUtilC;
import junit.framework.TestCase;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * Tests math utilities
 */
public class MathUtilTestC
        extends TestCase
{
    public void testRoundDown(){
        DecimalFormat df = new DecimalFormat("0.0000000000");
        double v = MathUtilC.roundDown(2.3D,100000000L);
        //assert df.format(2.3D).equals(df.format(v)); // CRYP-346 changes

        v = MathUtilC.roundDown(2.300000001D,100000000L);
        assert df.format(2.3D).equals(df.format(v));

        v = MathUtilC.roundDown(2.29999999D,100000000L);
        assert df.format(2.29999999D).equals(df.format(v));

        v = MathUtilC.roundDown(2.299999999999D,100000000L);
        //assert df.format(2.3D).equals(df.format(v));

        v = MathUtilC.roundDown(2.2999999900000134D,100000000L);
        assert df.format(2.29999999D).equals(df.format(v));
    }

    public void testRoundHalfDownUsingMathUtilRound(){
        DecimalFormat df = new DecimalFormat("0.0000000000");
        double v = MathUtil.round(2.3D, 8, BigDecimal.ROUND_HALF_DOWN );
        assert df.format(2.3D).equals(df.format(v));

        v = MathUtil.round(2.300000001D,8, BigDecimal.ROUND_HALF_DOWN );
        assert df.format(2.3D).equals(df.format(v));

        v = MathUtil.round(2.29999999D,8, BigDecimal.ROUND_HALF_DOWN );
        assert df.format(2.29999999D).equals(df.format(v));

        v = MathUtil.round(2.299999999999D,8, BigDecimal.ROUND_HALF_DOWN );
        assert df.format(2.3D).equals(df.format(v));

        v = MathUtil.round(2.2999999900000134D,8, BigDecimal.ROUND_HALF_DOWN );
        assert df.format(2.29999999D).equals(df.format(v));
    }

    public void testRoundDownUsingMathUtilRound(){
        DecimalFormat df = new DecimalFormat("0.0000000000");
        double v = MathUtil.round(2.3D, 8, BigDecimal.ROUND_DOWN );
        //assert df.format(2.3D).equals(df.format(v));// CRYP-346 changes

        v = MathUtil.round(2.300000001D,8, BigDecimal.ROUND_DOWN );
        assert df.format(2.3D).equals(df.format(v));

        v = MathUtil.round(2.29999999D,8, BigDecimal.ROUND_DOWN );
        assert df.format(2.29999999D).equals(df.format(v));

        v = MathUtil.round(2.299999999999D,8, BigDecimal.ROUND_DOWN );
        //assert df.format(2.3D).equals(df.format(v));

        v = MathUtil.round(2.2999999900000134D,8, BigDecimal.ROUND_DOWN );
        assert df.format(2.29999999D).equals(df.format(v));
    }

    /**
     * Tests whether significant decimal places are calculated properly
     */
    public void testSignificantDecimalPlaces()
    {

        int i = MathUtil.significantDecimalPlaces( 1.3456, 4 );
        assertEquals( 4, i );

        i = MathUtil.significantDecimalPlaces( 1.3, 4 );
        assertEquals( 1, i );

        i = MathUtil.significantDecimalPlaces( 1.3996, 4 );
        assertEquals( 4, i );

        i = MathUtil.significantDecimalPlaces( 1.3996, 3 );
        assertEquals( 1, i );

        i = MathUtil.significantDecimalPlaces( 1.3996, 2 );
        assertEquals( 1, i );
    }

    /**
     * Tests whether rounding in big doubles works when using BigDecimal.ROUND_HALF_UP
     */
    public void testRoundingHalfUp()
    {
        Double d;
        int type = BigDecimal.ROUND_HALF_UP;

        double value = 3.333333333333333E29;
        double precision = 0.01;
        Double expected = new Double( 9.223372036854776E16 );
        Double d1 = new Double( ( double ) ( ( long ) ( ( value * 100 ) + ( 0.5 * 1 ) ) ) / 100 );
        assertEquals( expected, d1 );
        Double d2 = new Double( MathUtil.round( value, precision, type ) );
        assertEquals( expected, d2 );

        value = 4000;
        precision = 0.01;
        expected = new Double( 4000.0 );
        d1 = new Double( ( double ) ( ( long ) ( ( value * 100 ) + ( 0.5 * 1 ) ) ) / 100 );
        assertEquals( expected, d1 );
        d2 = new Double( MathUtil.round( value, precision, type ) );
        assertEquals( expected, d2 );

        value = 2.2222222222222222E16;
        precision = 0.01;
        expected = new Double( 2.2222222222222224E16 );
        d1 = new Double( ( double ) ( ( long ) ( ( value * 100 ) + ( 0.5 * 1 ) ) ) / 100 );
        assertEquals( expected, d1 );
        d2 = new Double( MathUtil.round( value, precision, type ) );
        assertEquals( expected, d2 );

        value = 2.22222222222222222E39;
        precision = 0.01;
        expected = new Double( 9.223372036854776E16 );
        d1 = new Double( ( double ) ( ( long ) ( ( value * 100 ) + ( 0.5 * 1 ) ) ) / 100 );
        assertEquals( expected, d1 );
        // without long downcast
        d1 = new Double( ( double ) ( ( value * 100 ) + ( 0.5 * 1 ) ) / 100 );
        assertEquals( new Double( value ), d1 );
        d2 = new Double( MathUtil.round( value, precision, type ) );
        assertEquals( expected, d2 );

        // max long value
        System.out.println( "Long MaxValue=" + Long.MAX_VALUE );
        assertEquals( 9223372036854775807L, Long.MAX_VALUE );

        // d = new Double(Math.pow(2.22222222222222222,39));
        // assertEquals(new Double(2222222222222679000000000000000000000000000000D),d);
        // 3.3474335360048445E13
    }

    /**
     * Add any problematic number combinations here so that this will get tested.
     */
    public void testMathUtilRound()
    {
        try
        {
            double[] val1 = new double[]{5000000.00, 12344565.00, 816500.00};
            double[] val2 = new double[]{111.2345, 1.9898, 1.34491};
            double[] multiplyResults = new double[]{556172500, 24563215.44, 1098119.02};
            double[] divisionResults = new double[]{44950.08, 6203922.50, 607103.82};

            for ( int i = 0; i < val1.length; i++ )
            {
                // check multiplication
                double product = MathUtil.round( val1[i] * val2[i], 2, BigDecimal.ROUND_HALF_UP );
                assertTrue( multiplyResults[i] == product );

                // check division
                double quotient = MathUtil.round( val1[i] / val2[i], 2, BigDecimal.ROUND_HALF_UP );
                assertTrue( divisionResults[i] == quotient );
            }

            // bug no.36265 fix verification
            double[] val3 = new double[]{1.490031, 1.122548, 1.122575, 1.215194, 1.215221, 1.386065, 1.396049, 1.464549, 1.465051, 1.480425, 1.490031};
            int precision = 7;
            for ( int j = 0; j < val3.length; j++ )
            {
                double rounded = MathUtil.round( val3[j], precision, BigDecimal.ROUND_CEILING );
                assertTrue( val3[j] == rounded );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testFloatingPointCorrectionForWholeNumbers( )
    {
        DecimalFormat fmt = new DecimalFormat("0.000000000000");
        for( long i=0; i<100000000; ++i )
        {
            double roundedValue = MathUtilC.correctFloatingPointsCalculationPrecision ( i );
            if ( i % 100000 == 0 )
            {
                System.out.println ( "MathUtilTestC.roundFloatingPointCorrection : finished upto " + i  );
            }
            assertTrue ( "fmt.format( l )=" + fmt.format( i ) + ",fmt.format( roundedValue )=" + fmt.format( roundedValue ) , fmt.format( i ).equals( fmt.format( roundedValue ) ) );
        }
    }


    public void testRoundWholeNumbers()
    {
        roundTest ( 1, BigDecimal.ROUND_HALF_UP );
        roundTest ( 1, BigDecimal.ROUND_HALF_DOWN );
        roundTest ( 2, BigDecimal.ROUND_HALF_UP );
        roundTest ( 2, BigDecimal.ROUND_HALF_DOWN );
        roundTest ( 3, BigDecimal.ROUND_HALF_UP );
        roundTest ( 3, BigDecimal.ROUND_HALF_DOWN );
        roundTest ( 4, BigDecimal.ROUND_HALF_UP );
        roundTest ( 4, BigDecimal.ROUND_HALF_DOWN );
        roundTest ( 5, BigDecimal.ROUND_HALF_UP );
        roundTest ( 5, BigDecimal.ROUND_HALF_DOWN );
        roundTest ( 6, BigDecimal.ROUND_HALF_UP );
        roundTest ( 6, BigDecimal.ROUND_HALF_DOWN );
        roundTest ( 7, BigDecimal.ROUND_HALF_UP );
        roundTest ( 7, BigDecimal.ROUND_HALF_DOWN );
    }

    private void roundTest( int precision, int roundingType )
    {
        Currency ccy = new CurrencyC ();
        ccy.setTickValue( 1.0/Math.pow ( 10, precision ) );
        ccy.setRoundingType( roundingType );
        DecimalFormat fmt = new DecimalFormat("0.000000000000");
        for( long i=0; i<100000000; i = i + 10000 )
        {
            double roundedValue = ccy.round( i );
            if ( i % 100000 == 0 )
            {
                System.out.println ( "MathUtilTestC.roundTest : finished upto " + i + " for precision=" + precision + ",roundingType=" + roundingType );
            }
            assertTrue ( "fmt.format( l )=" + fmt.format( i ) + ",fmt.format( roundedValue )=" + fmt.format( roundedValue ) , fmt.format( i ).equals( fmt.format( roundedValue ) ) );
        }
    }

}

