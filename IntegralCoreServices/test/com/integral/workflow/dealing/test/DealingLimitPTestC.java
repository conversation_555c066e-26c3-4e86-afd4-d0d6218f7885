package com.integral.workflow.dealing.test;

// Copyright (c) 2009 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.test.PTestCaseC;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import com.integral.workflow.dealing.fx.FXDealingLimitCollection;
import com.integral.workflow.dealing.fx.FXWorkflowFactory;

public class DealingLimitPTestC extends PTestCaseC
{
    public DealingLimitPTestC( String name )
    {
        super( name );
    }

    public void testDealingLimitCollectionClone()
    {
        try
        {
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            Currency jpy = CurrencyFactory.getCurrency( "JPY" );
            FXDealingLimitCollection col1 = FXWorkflowFactory.newFXDealingLimitCollection();
            FXDealingLimit dl1 = FXWorkflowFactory.newFXDealingLimit();
            dl1.setBaseCurrency( eur );
            dl1.setVariableCurrency( usd );
            dl1.setBidLimits( new double[2] );
            dl1.setOfferLimits( new double[2] );
            dl1.setBidLimit( 1000, 0 );
            dl1.setBidLimit( 2000, 1 );
            dl1.setOfferLimit( 3000, 0 );
            dl1.setOfferLimit( 4000, 1 );
            col1.addDealingLimit( dl1 );

            FXDealingLimit dl2 = FXWorkflowFactory.newFXDealingLimit();
            dl2.setBaseCurrency( usd );
            dl2.setVariableCurrency( jpy );
            dl2.setBidLimits( new double[2] );
            dl2.setOfferLimits( new double[2] );
            dl2.setBidLimit( 5000, 0 );
            dl2.setBidLimit( 6000, 1 );
            dl2.setOfferLimit( 7000, 0 );
            dl2.setOfferLimit( 8000, 1 );
            col1.addDealingLimit( dl2 );

            // now clone the dealing limit collection
            FXDealingLimitCollection col2 = ( FXDealingLimitCollection ) col1.copy();
            FXDealingLimit dl1Copy = col2.getDealingLimit( eur, usd );
            FXDealingLimit dl2Copy = col2.getDealingLimit( usd, jpy );
            assertNotNull( dl1Copy );
            assertNotNull( dl2Copy );
            assertNotSame( dl1.getBidLimits(), dl1Copy.getBidLimits() );
            assertNotSame( dl2.getBidLimits(), dl2Copy.getBidLimits() );
            assertNotSame( dl1.getOfferLimits(), dl1Copy.getOfferLimits() );
            assertNotSame( dl2.getOfferLimits(), dl2Copy.getOfferLimits() );
            assertEquals( dl1.getBidLimit(), dl1Copy.getBidLimit() );
            assertEquals( dl2.getBidLimit(), dl2Copy.getBidLimit() );
            assertEquals( dl1.getOfferLimit(), dl1Copy.getOfferLimit() );
            assertEquals( dl2.getOfferLimit(), dl2Copy.getOfferLimit() );

            // test the error code.
            FXDealingLimit d1 = FXWorkflowFactory.newFXDealingLimit();
            d1.setErrorCode( "test" );
            FXDealingLimit d2 = FXWorkflowFactory.newFXDealingLimit();
            d1.apply( d2 );
            assertEquals( d1.getErrorCode(), "test" );
            assertNull( d2.getErrorCode() );

            d2.apply( d1 );
            assertEquals( d1.getErrorCode(), "test" );
            assertEquals( d2.getErrorCode(), "test" );
            FXDealingLimit d3 = FXWorkflowFactory.newFXDealingLimit();
            d3.apply( d1 );
            assertEquals( d1.getErrorCode(), "test" );
            assertEquals( d2.getErrorCode(), "test" );
            assertEquals( d3.getErrorCode(), "test" );
        }
        catch ( Exception e )
        {
            fail( "testDealingLimitCollectionClone", e );
        }
    }
}
