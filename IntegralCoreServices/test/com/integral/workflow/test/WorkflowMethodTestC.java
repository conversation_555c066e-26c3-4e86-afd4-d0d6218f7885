package com.integral.workflow.test;

import com.integral.message.TransactionPerformer;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.test.TestCaseC;
import org.apache.commons.beanutils.MethodUtils;

import java.lang.reflect.InvocationTargetException;

public class WorkflowMethodTestC extends TestCaseC implements TransactionPerformer
{
    static String name = "WorkflowMethod Test";

    public WorkflowMethodTestC( String aName )
    {
        super( aName );
    }

    public void testWorkflowMethod()
    {
        try
        {

        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

    public static void println( String message )
    {
        System.out.println( "=====================================" );
        System.out.println( message );
        System.out.println( "=====================================" );
    }

    public static Object invokeExactMethod( Object object, String methodName ) throws Exception
    {
        Object result = null;
        try
        {
            Object[] args = new Object[0];
            result = MethodUtils.invokeExactMethod( object, methodName, args );
        }
        catch ( InvocationTargetException ite )
        {
            Throwable target = ite.getTargetException();
            target.printStackTrace();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
        return result;
    }

    public static Object registerObject( Object object ) throws Exception
    {
        IdcTransaction transaction = IdcSessionManager.getInstance().getTransaction();
        Object result = transaction.getUOW().registerObject( object );
        return result;
    }

    public void perform()
    {
        this.println( "WorkflowMethodTestC.perform()" );
    }
}

