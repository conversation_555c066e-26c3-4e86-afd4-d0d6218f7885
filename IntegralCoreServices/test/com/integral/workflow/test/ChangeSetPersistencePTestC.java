package com.integral.workflow.test;

import com.integral.persistence.CustomField;
import com.integral.persistence.Entity;
import com.integral.test.PTestCaseC;
import com.integral.workflow.ChangeSet;
import com.integral.workflow.ChangeSetC;
import com.integral.workflow.ChangeSetCustomField;
import com.integral.workflow.ChangeSetCustomFieldC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

/**
 * Tests whether ChangeSet classes in the com.integral.workflow package persist properly
 */
public class ChangeSetPersistencePTestC
        extends PTestCaseC
{
    public ChangeSetPersistencePTestC( String aName )
    {
        super( aName );
    }


    /**
     * Tests persistence of com.integral.workflow.ChangeSetC
     */
    public void testChangeSetPersistence()
    {
        Class clz = ChangeSetC.class;

        log( "test persistence of " + clz.getName() );

        // insert
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ChangeSet cs = new ChangeSetC();
            cs.setStatus( 'T' );

            uow.registerObject( cs );

            uow.commit();
            log( "test insert of " + clz.getName() );
        }
        catch ( Exception e )
        {
            fail( "test insert " + clz.getName() );
            e.printStackTrace();
        }

        // query
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = ( Vector ) uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tretrieved " + entity );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            fail( "test query " + clz.getName() );
            e.printStackTrace();
        }

        // delete
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = ( Vector ) uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                Entity entity = ( Entity ) objects.get( i );
                log( "\tdeleting " + entity );
                uow.deleteObject( entity );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "test delete " + clz.getName() );
            e.printStackTrace();
        }
    }

    /**
     * Tests persistence of com.integral.workflow.ChangeSetCustomFieldC
     */
    public void testChangeSetCustomFieldPersistence()
    {
        Class clz = ChangeSetCustomFieldC.class;

        log( "test persistence of " + clz.getName() );

        // insert
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            CustomField cs = new ChangeSetCustomFieldC();
            cs.setStatus( 'T' );

            uow.registerObject( cs );

            uow.commit();
            log( "test insert of " + clz.getName() );
        }
        catch ( Exception e )
        {
            fail( "test insert " + clz.getName() );
            e.printStackTrace();
        }

        // query
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = ( Vector ) uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                ChangeSetCustomField cf = ( ChangeSetCustomField ) objects.get( i );
                log( "\tretrieved     " + cf );
                log( "\t   with value " + cf.getValue() );
            }

            uow.release();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "test query " + clz.getName() );
        }

        // delete
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( "status" ).equal( 'T' );

            Vector objects = ( Vector ) uow.readAllObjects( clz, expr );
            for ( int i = 0; i < objects.size(); i++ )
            {
                CustomField cf = ( CustomField ) objects.get( i );
                log( "\tdeleting " + cf );
                uow.deleteObject( cf );
            }

            uow.commit();
        }
        catch ( Exception e )
        {
            fail( "test delete " + clz.getName() );
            e.printStackTrace();
        }
    }
}
