package com.integral.workflow.test;

import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.QuoteC;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.test.PTestCaseC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.workflow.QuoteWorkflowStateMapDependentC;
import com.integral.workflow.RequestWorkflowStateMapDependentC;
import com.integral.workflow.State;
import com.integral.workflow.WorkflowError;
import com.integral.workflow.WorkflowErrors;
import com.integral.workflow.WorkflowFactory;
import com.integral.workflow.WorkflowStateMap;
import com.integral.xml.binding.JavaXMLBinderFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.io.StringReader;
import java.io.StringWriter;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

/**
 * This class is written for testing the DB operation of RequestWorkflowStateMapDependentC and
 * QuoteWorkflowStateMapDependentC with Request and Quote object respectively.
 * <p/>
 * It tests the following db operations with request and quote objects:
 * a) Persisting the new dependent request/quote workflowState & workflowErrors.
 * -  RequestWorkflowStateMapDependentC reqState = WorkflowFactory.newRequestWorkflowStateMapDependent();
 * -  QuoteWorkflowStateMapDependentC quoteState = WorkflowFactory.newQuoteWorkflowStateMapDependent();
 * b) Persisting the existing workflowState & workflowErrors.
 * -  WorkflowStateMap workflowStateMap = WorkflowFactory.newWorkflowStateMap();
 */

public class WorkflowStateMapDependentPTestC extends PTestCaseC
{

    private String errorParams[] = new String[]{"No DB connection", "No Responce from LP", "Verified", "Rejected"};

    public WorkflowStateMapDependentPTestC( String name )
    {
        super( name );
    }

    public void testRequestInsert1()
    {
        log( "testRequestInsert1" );
        try
        {
            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();

            long st = System.currentTimeMillis();
            Request r = new RequestC();

            RequestWorkflowStateMapDependentC reqState = WorkflowFactory.newRequestWorkflowStateMapDependent();

            User user = UserFactory.getUser( "Integral3" );
            User user1 = UserFactory.getUser( "Integral1" );
            user = ( User ) uow.registerObject( user );
            user1 = ( User ) uow.registerObject( user1 );

            reqState.setFirstActor( user );
            reqState.setLastActor( user );

            reqState.setState( getState( "Approved" ) );

            WorkflowErrors errors = WorkflowFactory.newWorkflowErrors();
            errors.addError( "1234", errorParams );
            reqState.setWorkflowErrors( errors );

            reqState.setAcceptanceRecievedByApplication( new Date() );
            reqState.setAcceptanceRecievedByProviderAdaptor( new Date() );
            reqState.setAcceptanceSentByConsumer( new Date() );
            reqState.setAcceptanceSentBySDK( new Date() );
            reqState.setRejectedByProviderAdaptor( new Date() );

            r = ( Request ) uow.registerObject( r );
            r.setTransactionID( "Test" + System.nanoTime() );
            r.setWorkflowStateMap( reqState );
            r.setUser( user1 );
            r.setNotes( "Test" );

            long now = System.currentTimeMillis();
            reqState.setRejectionReceivedByIS( new Timestamp( now + 10 ) );
            reqState.setVerificationReceivedByIS( new Timestamp( now + 20 ) );
            reqState.setVerificationSentByIS( new Timestamp( now + 30 ) );
            reqState.setRejectionSentByIS( new Timestamp( now + 40 ) );
            r.setMarketSnapshot( "TEST" );
            reqState.setUserLatency( 67 );
            reqState.setNoOfAttemptsByClient( 89 );
            reqState.setClientCPUUsage( 99 );
            reqState.setClientMemoryUsage( 56 );
            reqState.setClientPollLatency( 44 );
            reqState.setJMSProxyWaitTime( new Timestamp( now + 80 ) );
            reqState.setOAOrderReceivedByAdaptor( new Timestamp( now + 50 ) );
            reqState.setOAOrderPublishedByAdaptor( new Timestamp( now + 60 ) );
            reqState.setOAOrderMatchedByAdaptor( new Timestamp( now + 70 ) );


            uow.commit();
            long et = System.currentTimeMillis();
            printRequest( "Persisting Request to DB with RequestWorkflowStateMapDependentC. Time taken - " + ( et - st ), r );

            uow = this.getPersistenceSession().acquireUnitOfWork();
            r = ( Request ) uow.refreshObject( r );
            RequestWorkflowStateMapDependentC wfMap = ( RequestWorkflowStateMapDependentC ) r.getWorkflowStateMap();
            //now we have moved these mappings to Trade. So, even setting those values as above should not get saved in DB and should return null.
            assertEquals( r.getMarketSnapshot(), "TEST" );
            assertEquals( wfMap.getUserLatency(), 0 );
            assertEquals( wfMap.getNoOfAttemptsByClient(), 0 );
            assertEquals( wfMap.getClientCPUUsage(), 0 );
            assertEquals( wfMap.getClientMemoryUsage(), 0 );
            assertEquals( wfMap.getClientPollLatency(), 0 );

            assertNull( wfMap.getRejectionReceivedByIS() );
            assertNull( wfMap.getVerificationReceivedByIS() );
            assertNull( wfMap.getVerificationSentByIS() );
            assertNull( wfMap.getRejectionSentByIS() );
            assertNull( wfMap.getJMSProxyWaitTime() );
            assertNull( wfMap.getOAOrderReceivedByAdaptor() );
            assertNull( wfMap.getOAOrderPublishedByAdaptor() );
            assertNull( wfMap.getOAOrderMatchedByAdaptor() );

            printRequest( "Reading RequestWorkflowStateMapDependentC details of Request from DB ", r );

            checkXMLMapping( r );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testRequestInsert1" );
        }
    }

    public void testRequestInsert2()
    {
        log( "testRequestInsert2" );
        try
        {

            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();
            long st = System.currentTimeMillis();
            Request r = new RequestC();

            RequestWorkflowStateMapDependentC reqState = WorkflowFactory.newRequestWorkflowStateMapDependent();

            User user = UserFactory.getUser( "Integral3" );
            User user1 = UserFactory.getUser( "Integral1" );
            user = ( User ) uow.registerObject( user );
            user1 = ( User ) uow.registerObject( user1 );

            reqState.setFirstActor( user );
            reqState.setLastActor( user );

            reqState.setState( getState( "Modified" ) );

            reqState.getWorkflowErrors().addError( "2351", errorParams );

            reqState.setAcceptanceRecievedByApplication( new Date() );
            reqState.setAcceptanceRecievedByProviderAdaptor( new Date() );
            reqState.setAcceptanceSentByConsumer( new Date() );
            reqState.setAcceptanceSentBySDK( new Date() );
            reqState.setRejectedByProviderAdaptor( new Date() );

            r = ( Request ) uow.registerObject( r );
            r.setTransactionID( "Test" + System.nanoTime() );
            r.setWorkflowStateMap( reqState );
            r.setUser( user1 );
            r.setNotes( "Test" );

            uow.commit();
            long et = System.currentTimeMillis();
            printRequest( "Persisting Request to DB with RequestWorkflowStateMapDependentC. Time taken - " + ( et - st ), r );

            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( r );
            printRequest( "Reading RequestWorkflowStateMapDependentC details of Request from DB ", r );

            checkXMLMapping( r );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testRequestInsert2" );
        }
    }

    public void testRequestInsert3()
    {
        log( "testRequestInsert3" );
        try
        {
            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();

            long st = System.currentTimeMillis();
            Request r = new RequestC();

            User user = UserFactory.getUser( "Integral3" );
            User user1 = UserFactory.getUser( "Integral1" );
            user = ( User ) uow.registerObject( user );
            user1 = ( User ) uow.registerObject( user1 );

            WorkflowStateMap workflowStateMap = WorkflowFactory.newWorkflowStateMap();
            workflowStateMap = ( WorkflowStateMap ) uow.registerObject( workflowStateMap );

            workflowStateMap.setState( getState( "Declined" ) );

            WorkflowErrors errors = WorkflowFactory.newWorkflowErrors();
            errors.addError( "4321", errorParams );
            errors.addError( "8765", errorParams );
            errors.addError( "0000", errorParams );
            workflowStateMap.setWorkflowErrors( errors );

            workflowStateMap.setFirstActor( user );
            workflowStateMap.setLastActor( user );

            r = ( Request ) uow.registerObject( r );
            r.setTransactionID( "Test" + System.nanoTime() );
            r.setWorkflowStateMap( workflowStateMap );
            r.setUser( user1 );
            r.setNotes( "Test" );

            uow.commit();
            long et = System.currentTimeMillis();
            printRequest( "Persisting Request to DB with WorkflowStateMapC . Time taken - " + ( et - st ), r );

            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( r );
            printRequest( "Reading WorkflowStateMapC details of Request from DB ", r );

            checkXMLMapping( r );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testRequestInsert3" );
        }
    }

    public void testQuoteInsert1()
    {
        log( "testQuoteInsert1" );
        try
        {
            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();

            long st = System.currentTimeMillis();
            Quote q = new QuoteC();


            QuoteWorkflowStateMapDependentC quoteState = WorkflowFactory.newQuoteWorkflowStateMapDependent();

            User user = UserFactory.getUser( "Integral3" );
            User user1 = UserFactory.getUser( "Integral1" );
            user = ( User ) uow.registerObject( user );
            user1 = ( User ) uow.registerObject( user1 );

            quoteState.setFirstActor( user );
            quoteState.setLastActor( user );

            quoteState.setState( getState( "Approved" ) );

            WorkflowErrors errors = WorkflowFactory.newWorkflowErrors();
            errors.addError( "1234", errorParams );
            quoteState.setWorkflowErrors( errors );

            quoteState.setAcceptedByConsumer( new Date() );
            quoteState.setCreatedByProvider( new Date() );
            quoteState.setDealtAwayByConsumer( new Date() );
            quoteState.setDeclinedByProviderAdaptor( new Date() );
            quoteState.setRecievedByApplication( new Date() );

            q = ( Quote ) uow.registerObject( q );
            q.setWorkflowStateMap( quoteState );
            q.setUser( user1 );
            q.setNotes( "Test" );
            long now = System.currentTimeMillis();
            quoteState.setQuoteCreated( new Timestamp( now + 10 ) );
            quoteState.setRateSentByIS( new Timestamp( now + 20 ) );
            quoteState.setRateRecievedAtJMSProxy( new Timestamp( now + 30 ) );
            quoteState.setClientQueriedJMSProxy( new Timestamp( now + 40 ) );
            quoteState.setJMSProxySendRate( new Timestamp( now + 50 ) );
            quoteState.setClientRateDisplay( new Timestamp( now + 60 ) );
            quoteState.setNextRateReceived( new Timestamp( now + 70 ) );
            quoteState.setRFSQuoteStateChangeSnapshot( "TEST" );

            uow.commit();
            long et = System.currentTimeMillis();
            printQuote( "Persisting Quote to DB with QuoteWorkflowStateMapDependentC. Time taken - " + ( et - st ), q );

            uow = this.getPersistenceSession().acquireUnitOfWork();
            q = ( Quote ) uow.refreshObject( q );
            quoteState = ( QuoteWorkflowStateMapDependentC ) q.getWorkflowStateMap();
            //now we have moved these mappings to Trade. So, even setting those values as above should not get saved in DB and should return null.
            assertNull( quoteState.getQuoteCreated() );
            assertNull( quoteState.getRateSentByIS() );
            assertNull( quoteState.getRateRecievedAtJMSProxy() );
            assertNull( quoteState.getClientQueriedJMSProxy() );
            assertNull( quoteState.getJMSProxySendRate() );
            assertNull( quoteState.getClientRateDisplay() );
            assertNull( quoteState.getNextRateReceived() );
            assertNull( quoteState.getRFSQuoteStateChangeSnapshot() );


            printQuote( "Reading QuoteWorkflowStateMapDependentC details of Quote from DB ", q );

            checkXMLMapping( q );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testQuoteInsert1" );
        }
    }

    public void testQuoteInsert2()
    {
        log( "testQuoteInsert2" );
        try
        {
            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();

            long st = System.currentTimeMillis();
            Quote q = new QuoteC();


            QuoteWorkflowStateMapDependentC quoteState = WorkflowFactory.newQuoteWorkflowStateMapDependent();

            User user = UserFactory.getUser( "Integral3" );
            User user1 = UserFactory.getUser( "Integral1" );
            user = ( User ) uow.registerObject( user );
            user1 = ( User ) uow.registerObject( user1 );

            quoteState.setFirstActor( user );
            quoteState.setLastActor( user );

            quoteState.setState( getState( "Modified" ) );
            quoteState.getWorkflowErrors().addError( "2351", errorParams );

            quoteState.setAcceptedByConsumer( new Date() );
            quoteState.setCreatedByProvider( new Date() );
            quoteState.setDealtAwayByConsumer( new Date() );
            quoteState.setDeclinedByProviderAdaptor( new Date() );
            quoteState.setRecievedByApplication( new Date() );

            q = ( Quote ) uow.registerObject( q );
            q.setWorkflowStateMap( quoteState );
            q.setUser( user1 );
            q.setNotes( "Test" );

            uow.commit();
            long et = System.currentTimeMillis();
            printQuote( "Persisting Quote to DB with QuoteWorkflowStateMapDependentC. Time taken - " + ( et - st ), q );

            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( q );
            printQuote( "Reading QuoteWorkflowStateMapDependentC details of Quote from DB ", q );

            checkXMLMapping( q );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testQuoteInsert2" );
        }
    }

    public void testQuoteInsert3()
    {
        log( "testQuoteInsert3" );
        try
        {
            UnitOfWork uow = this.getPersistenceSession().acquireUnitOfWork();

            long st = System.currentTimeMillis();
            Quote q = new QuoteC();

            User user = UserFactory.getUser( "Integral3" );
            User user1 = UserFactory.getUser( "Integral1" );
            user = ( User ) uow.registerObject( user );
            user1 = ( User ) uow.registerObject( user1 );

            WorkflowStateMap workflowStateMap = WorkflowFactory.newWorkflowStateMap();
            workflowStateMap = ( WorkflowStateMap ) uow.registerObject( workflowStateMap );

            workflowStateMap.setState( getState( "Declined" ) );

            WorkflowErrors errors = WorkflowFactory.newWorkflowErrors();
            errors.addError( "4321", errorParams );
            errors.addError( "8765", errorParams );
            errors.addError( "0000", errorParams );
            workflowStateMap.setWorkflowErrors( errors );

            workflowStateMap.setFirstActor( user );
            workflowStateMap.setLastActor( user );

            q = ( Quote ) uow.registerObject( q );
            q.setWorkflowStateMap( workflowStateMap );
            q.setUser( user1 );
            q.setNotes( "Test" );

            uow.commit();
            long et = System.currentTimeMillis();
            printQuote( "Persisting Quote to DB with WorkflowStateMapC. Time taken - " + ( et - st ), q );

            uow = this.getPersistenceSession().acquireUnitOfWork();
            uow.refreshObject( q );
            printQuote( "Reading WorkflowStateMapC details of Quote from DB ", q );

            checkXMLMapping( q );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "testQuoteInsert3" );
        }
    }

    private void printQuote( String comment, Quote quote )
    {
        log( "\t ********************** " + comment + " ****************************" );
        log( "\t object   = " + quote );
        log( "\t objectID = " + quote.getObjectID() );
        log( "\t objectID user = " + quote.getUser() );

        log( "\t WorkflowState = " + quote.getWorkflowStateMap().getState().getShortName() );
        log( "\t WorkflowState firstActor = " + quote.getWorkflowStateMap().getFirstActor() );
        log( "\t WorkflowState lastActor = " + quote.getWorkflowStateMap().getLastActor() );

        Collection errors = quote.getWorkflowStateMap().getWorkflowErrors().getErrors();
        Iterator itr = errors.iterator();

        while ( itr.hasNext() )
        {
            WorkflowError wrkError = ( WorkflowError ) itr.next();
            log( "\t     WorkflowError = " + wrkError.getErrorCode() );
            log( "\t     WorkflowError params = " );

            String[] errParams = wrkError.getErrorParameters();
            for ( int i = 0; i < errParams.length; i++ )
            {
                log( "\t         - " + errParams[i] );
            }
        }

        WorkflowStateMap quoteWorkflowStateMap = quote.getWorkflowStateMap();

        if ( null != quoteWorkflowStateMap && quoteWorkflowStateMap instanceof QuoteWorkflowStateMapDependentC )
        {
            QuoteWorkflowStateMapDependentC qWrkStateMapDependent = ( QuoteWorkflowStateMapDependentC ) quoteWorkflowStateMap;
            log( "\t WorkflowState.getAcceptedByConsumer = " + qWrkStateMapDependent.getAcceptedByConsumer() );
            log( "\t WorkflowState.getCreatedByProvider = " + qWrkStateMapDependent.getCreatedByProvider() );
            log( "\t WorkflowState.getDealtAwayByConsumer = " + qWrkStateMapDependent.getDealtAwayByConsumer() );
            log( "\t WorkflowState.getDeclinedByProviderAdaptor = " + qWrkStateMapDependent.getDeclinedByProviderAdaptor() );
            log( "\t WorkflowState.getRecievedByApplication = " + qWrkStateMapDependent.getRecievedByApplication() );
        }

        log( "\t ******************************************************************* " );
    }


    private void printRequest( String comment, Request req )
    {
        log( "\t ********************** " + comment + " ****************************" );
        log( "\t object   = " + req );
        log( "\t objectID = " + req.getObjectID() );
        log( "\t objectID user = " + req.getUser() );

        log( "\t WorkflowState = " + req.getWorkflowStateMap().getState().getShortName() );
        log( "\t WorkflowState firstActor = " + req.getWorkflowStateMap().getFirstActor() );
        log( "\t WorkflowState lastActor = " + req.getWorkflowStateMap().getLastActor() );

        Collection errors = req.getWorkflowStateMap().getWorkflowErrors().getErrors();
        Iterator itr = errors.iterator();

        while ( itr.hasNext() )
        {
            WorkflowError wrkError = ( WorkflowError ) itr.next();
            log( "\t     WorkflowError = " + wrkError.getErrorCode() );
            log( "\t     WorkflowError params = " );

            String[] errParams = wrkError.getErrorParameters();
            for ( int i = 0; i < errParams.length; i++ )
            {
                log( "\t         - " + errParams[i] );
            }
        }

        WorkflowStateMap reqWorkflowStateMap = req.getWorkflowStateMap();

        if ( null != reqWorkflowStateMap && reqWorkflowStateMap instanceof RequestWorkflowStateMapDependentC )
        {
            RequestWorkflowStateMapDependentC reqWrkStateMapDependent = ( RequestWorkflowStateMapDependentC ) reqWorkflowStateMap;
            log( "\t WorkflowState.getAcceptanceRecievedByApplication = " + reqWrkStateMapDependent.getAcceptanceRecievedByApplication() );
            log( "\t WorkflowState.getAcceptanceRecievedByProviderAdaptor = " + reqWrkStateMapDependent.getAcceptanceRecievedByProviderAdaptor() );
            log( "\t WorkflowState.getAcceptanceSentByConsumer = " + reqWrkStateMapDependent.getAcceptanceSentByConsumer() );
            log( "\t WorkflowState.getAcceptanceSentBySDK = " + reqWrkStateMapDependent.getAcceptanceSentBySDK() );
            log( "\t WorkflowState.getRejectedByProviderAdaptor = " + reqWrkStateMapDependent.getRejectedByProviderAdaptor() );
        }

        log( "\t ******************************************************************* " );
    }

    protected State getState( String stateName )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).equal( stateName );

        if ( null == this.uow )
        {
            this.uow = this.getPersistenceSession().acquireUnitOfWork();
        }

        State state = ( State ) this.uow.readObject( State.class, expr );
        return state;
    }

    protected Collection getObjects( Class aClass )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "notes" ).equal( "Test" );
        Collection objects;
        if ( this.uow == null )
        {
            objects = this.getPersistenceSession().readAllObjects( aClass, expr );
        }
        else
        {
            objects = this.uow.readAllObjects( aClass, expr );
        }
        return objects;
    }

    private void checkXMLMapping( Object object )
    {
        try
        {
            StringWriter sw = new StringWriter();
            JavaXMLBinderFactory.newJavaXMLBinder().convertToXML( sw, object, "Integral" );

            if ( object instanceof Request )
            {
                log( "******************* Request to XML ************************" );
                log( sw.toString() );
            }
            else
            {
                log( "******************* Quote to XML ************************" );
                log( sw.toString() );
            }

            StringReader sr = new StringReader( sw.toString() );
            Object dPrice = JavaXMLBinderFactory.newJavaXMLBinder().convertFromXML( sr, "Integral" );

            if ( object instanceof Request )
            {
                printRequest( "Reading from XML ", ( Request ) dPrice );
            }
            else
            {
                printQuote( "Reading from XML ", ( Quote ) dPrice );
            }
        }
        catch ( Exception exc )
        {
            log( "Exception.checkXMLMapping() - " + exc );
        }
    }
}
