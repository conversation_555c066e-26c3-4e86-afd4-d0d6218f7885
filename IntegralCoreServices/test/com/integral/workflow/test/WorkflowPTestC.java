package com.integral.workflow.test;


import com.integral.test.PTestCaseC;
import com.integral.workflow.WorkflowStateMap;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.util.Iterator;
import java.util.Vector;


public class WorkflowPTestC
        extends PTestCaseC
{

    public WorkflowPTestC( String aName )
    {
        super( aName );
    }

    /**
     * Finds all workflow state maps that have a key/value combination
     */
    public void testQueryWorkflowStateMap()
    {
        try
        {
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression nfsExpr = eb.anyOf( "workflowStateMap" );
            Expression expr1 = nfsExpr.get( "name" ).like( "A%" );
            Expression expr2 = nfsExpr.get( "state" ).get( "status" ).equal( 'A' );
            Expression expr = expr1.and( expr2 );

            Vector wsm = ( Vector ) getPersistenceSession().readAllObjects( WorkflowStateMap.class, expr );
            for ( int i = 0; i < wsm.size(); i++ )
            {
                WorkflowStateMap map = ( WorkflowStateMap ) wsm.elementAt( i );
                log.info( "\t#" + i + ":\t" + map.getObjectID() );
                Iterator keys = map.getMap().keySet().iterator();
                while ( keys.hasNext() )
                {
                    Object key = keys.next();
                    Object value = map.getMap().get( key );
                    log.info( "\t\t" + key + " --> " + value );
                }

            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "finding all workflow state maps" );
        }
    }


}
