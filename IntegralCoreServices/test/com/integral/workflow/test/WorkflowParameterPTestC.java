package com.integral.workflow.test;


import com.integral.rule.AttributeConstantC;
import com.integral.test.PTestCaseC;
import com.integral.workflow.WorkflowMethod;
import com.integral.workflow.WorkflowMethodC;
import com.integral.workflow.WorkflowParameter;
import com.integral.workflow.WorkflowParameterC;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;


public class WorkflowParameterPTestC
        extends PTestCaseC
{
    static String name = "WorkflowParameterC Test";
    Vector users = null;

    public WorkflowParameterPTestC( String name )
    {
        super( name );
    }

    public void testQuery()
    {
        log( "lookupTest" );
        try
        {
            Vector objs = getPersistenceSession().readAllObjects( WorkflowParameterC.class );
            for ( int i = 0; i < objs.size(); i++ )
            {
                WorkflowParameterC event = ( WorkflowParameterC ) objs.elementAt( i );
                log( "wf param #" + i );
                log( "\t objectID = " + event.getObjectID() );
                log( "\t object = " + event );
                log( "\t constant: " + event.getConstant() );
            }
            log( "lookupTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "lookupTest" );
        }
    }

    public void testInsert()
    {
        log( "insertTest" );
        try
        {
            UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();

            WorkflowParameter event = new WorkflowParameterC();
            WorkflowParameter regWf = ( WorkflowParameter ) uow.registerObject( event );

            AttributeConstantC constant = new AttributeConstantC();
            regWf.setConstant( constant );

            WorkflowMethod workflowMethod = new WorkflowMethodC();
            WorkflowMethod regWorkflowMethod = ( WorkflowMethod ) uow.registerObject( workflowMethod );
            regWorkflowMethod.setMethodName( "Test" );
            regWf.setWorkflowMethod( regWorkflowMethod );

            uow.commit();

            log( "wf param objectID = " + event.getObjectID() );
            log( "wf param objectID = " + event );
            log( "\tconstant: " + event.getConstant() );

            log( "insertTest" );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "insertTest" );
        }
    }
}
