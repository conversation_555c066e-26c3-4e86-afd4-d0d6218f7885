package com.integral.classification.test;


import com.integral.classification.Classification;
import com.integral.classification.ClassificationFactory;
import com.integral.persistence.CustomField;
import com.integral.persistence.PersistenceFactory;
import com.integral.test.PTestCaseC;
import com.integral.user.UserClassificationC;
import junit.framework.Test;
import junit.framework.TestSuite;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Vector;

public class ClassificationPTestC
        extends PTestCaseC
{
    static int oid_seed = 0;

    public ClassificationPTestC( String aName )
    {
        super( aName );
    }

    public static void main( String args[] )
    {
        junit.textui.TestRunner.run( suite() );
    }

    public static Test suite()
    {
        TestSuite suite = new TestSuite();
        suite.addTest( new ClassificationPTestC( "testPersistence" ) );
        return suite;
    }

    public void setUp() throws Exception
    {
        super.setUp();

        UnitOfWork uow = getPersistenceSession().acquireUnitOfWork();
        uow.removeAllReadOnlyClasses();

        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "shortName" ).like( "TST-%" );
        Vector objs = uow.readAllObjects( Classification.class, expr );
        uow.deleteAllObjects( objs );

        uow.commit();
    }

    public void testPersistence()
    {
        try
        {
            // UOW
            UnitOfWork uow1 = getPersistenceSession().acquireUnitOfWork();
            uow1.removeAllReadOnlyClasses();

            // create classification
            Classification clsf = new UserClassificationC();
            clsf.setShortName( "TST-" + ( oid_seed++ ) );

            // register classification
            clsf = ( Classification ) uow1.registerObject( clsf );

            // commit
            uow1.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "classification test" );
        }

    }

    /**
     * Write a simple clsf CF
     */
    public void testInsert()
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();

            CustomField cf = ClassificationFactory.newClassificationCustomField( "TEST", "TESTVAL" );
            cf.setStatus( 'T' );
            uow.registerObject( cf );

            uow.commit();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
    }

}

