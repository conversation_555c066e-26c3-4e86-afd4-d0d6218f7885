package com.integral.facade.test;

// Copyright (c) 2003-2005 Integral Development Corp. All rights reserved.

import com.integral.facade.EventTimeFacade;
import com.integral.facade.EventTimeFacadeC;
import com.integral.facade.FacadeFactory;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import junit.framework.TestCase;

public class FacadeTestC
        extends TestCase
{
    public FacadeTestC( String aName )
    {
        super( aName );
    }

    public void tesTrade()
    {
        Trade trade = new TradeC();
        FXTrade fxspot = new FXSingleLegC();
        //FXTrade fxswap = new FXSwapC();

        FacadeFactory.setFacade( "test", Trade.class, TradeFacade.class );

        System.out.println( "Facade test for trade is " + FacadeFactory.newEntityFacade( "test", trade ) );
        System.out.println( "Facade test for fxspot is " + FacadeFactory.newEntityFacade( "test", fxspot ) );

        FacadeFactory.setFacade( "test", FXTrade.class, FXTradeFacade.class );

        System.out.println( "Facade test for trade is " + FacadeFactory.newEntityFacade( "test", trade ) );
        System.out.println( "Facade test for fxspot is " + FacadeFactory.newEntityFacade( "test", fxspot ) );
    }

    public void testEventTimeFacade()
    {
        User user = UserFactory.newUser( "joe" );

        EventTimeFacade facade = new EventTimeFacadeC();
        facade.setEntity( user );

        facade.setTime( "event-1", 100000 );
        facade.setTime( "event-2", 200000 );

        facade.log();
    }

    public void testEventTimeFacadeWithEntityInfo()
    {
        User user = UserFactory.newUser( "joe" );

        EventTimeFacade facade = new EventTimeFacadeC();
        facade.setEntity( user );

        facade.setTime( "event-1", 100000 );
        facade.setTime( "event-2", 200000 );

        facade.setEntityInfo( "info-1", new Long( 123 ) );
        facade.setEntityInfo( "info-2", new Double( 123 ) );

        facade.log();
    }
}

	

		