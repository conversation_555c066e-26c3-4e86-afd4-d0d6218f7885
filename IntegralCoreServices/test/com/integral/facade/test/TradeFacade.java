package com.integral.facade.test;

// Copyright (c) 2003-2005 Integral Development Corp. All rights reserved.

import com.integral.facade.EntityFacade;
import com.integral.persistence.Entity;

public class TradeFacade implements EntityFacade
{
    Entity e;

    public TradeFacade()
    {
    }

    public Entity getEntity()
    {
        return e;
    }

    public void setEntity( Entity ee )
    {
        e = ee;
    }

    public void resetTransients()
    {

    }
}

	