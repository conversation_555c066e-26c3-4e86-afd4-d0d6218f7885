package com.integral.facade.test;

import com.integral.facade.EventTimeFacade;
import com.integral.facade.EventTimeFacadeC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import junit.framework.TestCase;

/**
 * Created by IntelliJ IDEA.
 * User: pandit
 * Date: Dec 16, 2008
 * Time: 11:46:58 AM
 * To change this template use File | Settings | File Templates.
 */
public class EventTimeFacadeTestC extends TestCase
{
    public EventTimeFacadeTestC( String aName )
    {
        super( aName );
    }

    public void testEventTimeFacade()
    {
        User user = UserFactory.newUser( "joe" );

        EventTimeFacade facade = new EventTimeFacadeC();
        facade.setEntity( user );

        facade.setTime( "event-1", 100000 );
        facade.setTime( "event-2", 200000 );
        facade.setTime( "event-3", -10000 );

        facade.log();
    }

    public void testEventTimeFacadeWithEntityInfo()
    {
        User user = UserFactory.newUser( "joe" );

        EventTimeFacade facade = new EventTimeFacadeC();
        facade.setEntity( user );

        facade.setTime( "event-1", 100000 );
        facade.setTime( "event-2", 200000 );

        facade.setEntityInfo( "info-1", 123L );
        facade.setEntityInfo( "info-2", 123.0 );
        facade.setEntityInfo( "info-2", user );

        facade.log();
    }

    public void testGetTime()
    {
        User user = UserFactory.newUser( "joe" );

        long startTime = System.nanoTime();
        EventTimeFacade facade = new EventTimeFacadeC();
        facade.setEntity( user );

        facade.setTime( "event-1", 100000L );
        facade.setTime( "event-2", -200000L );
        facade.setTime( "event-3", 0L );

        if ( facade.getTime( "event-1" ) != 100000L )
        {
            fail( "Failed. Set time with Positive Long fails." );
        }

        if ( facade.getTime( "event-2" ) != -200000L )
        {
            fail( "Failed. Set time with Negetive Long fails." );
        }

        if ( facade.getTime( "event-3" ) != 0L )
        {
            fail( "Failed. Set time with zero fails." );
        }

        if ( facade.getTime( "event-4" ) != 0L )
        {
            fail( "Failed. Get time for non-existing event fails." );
        }

        facade.setTime( "event-2", 200000L );

        if ( facade.getTime( "event-2" ) != 200000L )
        {
            fail( "Failed. Set time with Positive Long greater than existing value fails." );
        }

        facade.setTime( "event-2", -1000L );

        if ( facade.getTime( "event-2" ) == -1000L )
        {
            fail( "Failed. Set time with time less than existing value fails." );
        }


        System.out.println( "Time Taken = " + ( System.nanoTime() - startTime ) );
    }

}
