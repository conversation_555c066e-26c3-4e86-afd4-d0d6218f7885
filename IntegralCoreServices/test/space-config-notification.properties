cluster.clusterone.url=mvmongo2:50000
#metaspace.provisoning-integral.reliabilityLevel=PRIMARY_RELIABILITY
#metaspace.TRADING-integral.reliabilityLevel=PRIMARY_RELIABILITY

#metaspace.provisoning-integral.reliabilityLevel=PRIMARY_RELIABILITY
metaspace.TRADING-integral.reliabilityLevel=PRIMARY_RELIABILITY
metaspace.TRADING-integral.concurrencyFactor=1
metaspace.TRADING-integral.shortCircuitFuture=true
metaspace.TRADING-integral.bufferSize=32
metaspace.TRADING-integral.serializationSize=8192
#metaspace.TRADING-integral.dbPerNamespace=true
metaspace.TRADING-integral.cluster=clusterone
metaspace.TRADING-integral.enableNotifications=true
metaspace.TRADING-integral.metaspacePrefix=

#properties for notification
# the message size for notifications should be >= metaspace.<metaspaceName>.serializationSize
#notification.port=8672
#notification.group=192.168.249.34
#notification.driverClass=com.integral.spaces.notification.rabbitmq.RabbitMQNotificationTransport
notification.bufferSize=16
notification.messageSize=8192
notification.messageSerializationType=JSON
notification.port=4544
notification.group=225.4.5.6
notification.driverClass=com.integral.spaces.notification.multicast.MuliticastNotificationTransport
futureTimeoutInMs=60000
