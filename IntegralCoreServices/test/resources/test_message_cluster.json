{"clusters": [{"name": "Test-Cluster", "user": "integral", "password": "integral", "addresses": "mvsonic:5672", "autoReconnectionEnabled": true, "recoveryInterval": 50, "virtualHosts": [{"name": "RDS", "listenerThreadPoolSize": 1, "channelPoolSize": 10, "exchanges": [{"name": "RDSX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 5}, {"name": "MAILX", "type": "direct", "durable": true, "policy": "", "publishRetries": 5}, {"name": "MRFQX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 5}, {"name": "EntityMultiAppX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 5}, {"name": "RefEntityMultiAppX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 5}, {"name": "DISTCACHEX", "type": "topic", "durable": false, "policy": "", "publishRetries": 5}, {"name": "CreditX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 5}, {"name": "RTSTX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 5}, {"name": "NotificationX", "type": "topic", "durable": false, "policy": "", "publishRetries": 5}, {"name": "SessionControlX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 5}, {"name": "ConfigX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 5}, {"name": "STPX", "type": "direct", "durable": true, "policy": "", "publishRetries": 5}, {"name": "YMP2PX", "type": "topic", "durable": false, "policy": "", "publishRetries": 1}, {"name": "TradeUpdateX", "type": "direct", "durable": true, "policy": "", "publishRetries": 5}, {"name": "AlertX", "type": "fanout", "durable": true, "policy": "", "publishRetries": 2}, {"name": "HeartBeatX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 3}, {"name": "PriceProvisionX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 3}, {"name": "YMINTX", "type": "topic", "durable": true, "policy": "", "publishRetries": 1}, {"name": "MakerPortalMRFQX", "type": "fanout", "durable": false, "policy": "", "publishRetries": 1}, {"name": "MTFEventX", "type": "topic", "durable": true, "policy": "", "publishRetries": 5}, {"name": "IWSX", "type": "topic", "durable": true, "policy": "", "publishRetries": 5}, {"name": "GMNotificationX", "type": "topic", "durable": true, "policy": "", "publishRetries": 5}, {"name": "GMInternalX", "type": "direct", "durable": false, "policy": "", "publishRetries": 5}, {"name": "Unity", "type": "direct", "durable": true, "policy": "", "publishRetries": 5}]}]}]}