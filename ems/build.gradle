def repoPath = "$repoSrc"

apply plugin: 'groovy'
apply plugin: "jacoco"

description = "EMS Module"

version = USRV_VERSION

sourceSets{
    test{
        resources{
            srcDir file("${repoPath}/properties")
        }
    }
}

dependencies{
    testCompile project(":notification")
    testCompile project(":fxiapi")
    /*
        Using testframework.jar as using testframework as a project causes test runtime issues due to
        powermock
     */
    //testCompile fileTree(dir: repoPath, include: 'testframework.jar')
    testCompile project(":testframework")
    testCompile group: 'org.mockito', name: 'mockito-core', version: '2.7.20'
    testCompile "com.integral:xems-testframework:3.0.1-jdk8"
    testCompile group: 'org.springframework.boot', name: 'spring-boot', version: '1.4.4.RELEASE'
    testCompile group: 'org.springframework.boot', name: 'spring-boot-test-autoconfigure', version: '1.4.4.RELEASE'
    testCompile group: 'org.springframework', name: 'spring-test', version: '3.1.1.RELEASE'
    testCompile group: 'org.springframework.boot', name: 'spring-boot-starter-parent', version: '1.4.4.RELEASE'
    testCompile group: 'org.springframework.boot', name: 'spring-boot-starter-actuator', version: '1.4.4.RELEASE'
    testCompile group: 'org.springframework.boot', name: 'spring-boot-starter-web', version: '1.4.4.RELEASE'
    testCompile group: 'org.springframework.boot', name: 'spring-boot-starter-test', version: '1.4.4.RELEASE'
    testCompile group: 'org.springframework.boot', name: 'spring-boot-devtools', version: '1.4.4.RELEASE'

    testRuntime group: "com.integral", name: 'yieldmanager', version :DARWIN_YIELDMANAGER_VERSION
    testRuntime group: 'com.integral', name: 'log', version: '1.2'
}

jar {
    jar.archiveName = "ems.jar"
    destinationDir = file("$repoPath")

    includeEmptyDirs = false

    manifest {
        attributes 'Implementation-Title': project.name,
                'Implementation-Version': project.version,
                'Implementation-Description': project.description,
                'Built-By': System.getProperty('user.name'),
                'Built-JDK': System.getProperty('java.version'),
                'Built-Hostname': hostname(),
                'Build-Time': buildTime(),
                'Build-Version': svnversion()
    }
}

uploadArchives{
    enabled = false
}

clean{
    delete "$repoPath/ems.jar"
    delete "$repoPath/ems/tx-spaces"
}

test {
    forkEvery = 1
    jvmArgs = ['-Djava.net.preferIPv4Stack=true']
}
test.onlyIf { project.hasProperty('testsEnabled') }
